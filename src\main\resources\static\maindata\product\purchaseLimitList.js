$(function () {

    $('#X_Tableb').XGrid({
        url:"/proxy-product/product/purchaseLimit/listData",
        colNames: ['ID','单据（记录id）','申请日期', '机构', '申请人ID','申请人', '单据编号', '商品编码', '商品名称', '商品大类','型号/规格', '生产厂家', '包装单位','限销','限采','停用','申请类型','停（启）用原因', '审核状态'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true
            },{
                name: 'recordId',
                index: 'recordId',
                hidden:true
            },{
                name: 'applicationTime',
                index: 'applicationTime',
                formatter:function(value){
                    return new Date(value).Format('yyyy-MM-dd');
                },
                width: 100
            }, {
                name: 'orgCode',
                index: 'orgCode',
                width: 220
            },{
                name: 'applicantId',
                index: 'applicantId',
                hidden:true
            },  {
                name: 'applicantValue',
                index: 'applicantValue',
                width: 80
            }, {
                name: 'applicationCode',
                index: 'applicationCode',
                width: 160
            }, {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'productName',
                index: 'productName',
                width: 200
            }, {
                name: 'largeCategoryVal',
                index: 'largeCategoryVal',
                width: 80
            },{
                name: 'specifications',
                index: 'specifications',
                width: 200
            }, {
                name: 'manufacturerValue',
                index: 'manufacturerValue',
                width: 220
            }, {
                name: 'packingUnitValue',
                index: 'packingUnitValue',
                width: 80
            },{
                name: 'limitedPinState',
                index: 'limitedPinState',
                hidden:true
            },{
                name: 'limitedProductionState',
                index: 'limitedProductionState',
                hidden:true
            },{
                name: 'disableState',
                index: 'disableState',
                hidden:true
            },{
                name: 'disableState',
                index: 'disableState',
                formatter:function (val,rowType,rowData) {
                    var str="";
                    if (rowData.limitedPinState!=undefined) {
                        if (rowData.limitedPinState!=-1){
                            if (rowData.limitedPinState==1) {
                                str=str+"限销"
                            }else {
                                str=str+"取消限销"
                            }
                        }
                    }
                    if (rowData.limitedProductionState!=undefined) {
                        if (rowData.limitedProductionState!=-1){
                            if (rowData.limitedProductionState==1) {
                                str=str+"限采"
                            }else {
                                str=str+"取消限采"
                            }
                        }
                    }
                    if (rowData.disableState!=undefined) {
                        if (rowData.disableState!=-1){
                            if (rowData.disableState==1) {
                                str=str+"停用"
                            }else {
                                str=str+"取消停用"
                            }
                        }
                    }
                    return str;
                },
                width: 200
            }, {
                name: 'disableReason',
                index: 'disableReason'
            },{
                name: 'statues',
                index: 'statues',
                formatter:function(value){
                    if (value==0){
                        return "录入中"
                    }else if (value==1){
                        return "审核中"
                    }else if (value==2){
                        return "审核通过"
                    }else if (value==3){
                        return "审核不通过"
                    }},
                width: 100}
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            var link_url="/proxy-product/product/purchaseLimit/audit/toDetail?businessId="+obj.recordId;
            if (obj.statues=="录入中"){
                var loginUserId = $("#loginUserId").val();
                if(obj.applicantId==loginUserId){
                    link_url="/proxy-product/product/purchaseLimit/editPurchaseLimit?recordId="+obj.recordId;
                    utils.openTabs("productDisableChangeEdit","商品停用申请", link_url);
                }else{
                    utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                }
            }else {
                utils.openTabs("productDisableChangeDetail","商品停用申请详情", link_url);
            }
        }
    });

    $("#SearchBtn").on("click", function () {
        $("#keyword").val($("#keyword").val().trim());
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "orgCode": $("#orgCode").val(),
                "keyword":$("#keyword").val(),
                "statues":$("#statues").val(),
                "largeCategory":$("#largeCategory").val()
            },page:1
        }).trigger('reloadGrid');
    });

    // 删除草稿
    $("#deleteDraftBtn").on("click", function () {
        let selRow = $('#X_Tableb').XGrid('getSeleRow');
        var loginUserId =$("#loginUserId").val();
        var applicantId=selRow[0].applicantId;
        // 表格重新渲染 的参数
        let postData = {
        }
        let data = {
            purchaseLimitDetailId: selRow[0].id,
            applicantId:applicantId,
            loginUserId:loginUserId,
            purchaseLimitId:selRow[0].recordId
        }
        let params = {
            applicantId:applicantId,
            statusVal: selRow[0].statues,
            statusName: '录入中',
            url:'/proxy-product/product/purchaseLimit/delete',
            loginUserId:loginUserId
        }
        utils.deleteDraft('X_Tableb', params, data, postData);
    });

    $("#exportBtn").on("click", function () {
        var body = document.body;
        var form = $(body).find('form#searchForm');
        $(form).attr("action","/proxy-product/product/purchaseLimit/export");
        $(form).submit();

        //noinspection JSAnnotator
        // let html = $('#exportHtml').html();
        // //noinspection JSAnnotator
        // let d = dialog({
        //     title: '提示',
        //     width:400,
        //     content: html,
        //     okValue: '确定',
        //     ok: function () {
        //         this.title('提交中…');
        //         return false;
        //     },
        //     cancelValue: '取消',
        //     cancel: function () { }
        // });
        // d.showModal();
    });

})
var orgCode=$("#loginOrgCode").val();
if(orgCode=='001'){
    $.ajax({
        url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode="+orgCode,
        async : false,
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].orgCode+'">'+arr[i].orgName+'</option>';
                    }
                }
            }
            $("#orgCode").html(html);
        },
        error:function () {
        }
    });
}
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}