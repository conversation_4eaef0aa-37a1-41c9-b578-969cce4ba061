$(function () {
  $('div[fold=head]').fold({
    sub: 'sub'
  });

  function formatter(value) {
    if (value) {
      // 临时规避浮点数陷阱 https://github.com/camsong/blog/issues/9
      return (value * 100).toFixed(2) + "%";
    } else {
      return '0%'
    }
  }

  function isDisabledYnFormatter(value) {
    let result = ""
    switch (value) {
      case 1:
        result = "是"
        break;
      case 0:
        result = "否"
        break;
      default:
        break
    }
    return result
  }

  const colMaps = [
    {colName: '', name: 'id', hidden: true},
    {colName: '机构', name: 'orgName', index: 'orgName', width: 240},
    {colName: '客户总数', name: 'customerCount', index: 'customerCount'},
    {colName: '是否停用', name: 'disabledYn', index: 'disabledYn', formatter: isDisabledYnFormatter},
    {colName: '<i>客户资质总数</i><i class="questa"></i>', name: 'credentialCount', index: 'credentialCount'},
    {colName: '<i>临期客户数</i><i class="questa"></i>', name: 'adventCustomerCount', index: 'adventCustomerCount'},
    {colName: '<i>临期资质总数</i><i class="questa"></i>', name: 'adventCredentialCount', index: 'adventCredentialCount'},
    {colName: '<i>客户临期率</i><i class="questa"></i>', name: 'adventCustomerRatio', index: 'adventCustomerRatio', formatter: formatter},
    {colName: '<i>资质临期率</i><i class="questa"></i>', name: 'adventCredentialRatio', index: 'adventCredentialRatio', formatter: formatter},
    {colName: '<i>临期活跃客户数</i><i class="questa"></i>', name: 'adventActiveCustomerCount', index: 'adventActiveCustomerCount'},
    {colName: '<i>临期活跃客户<br/>资质总数</i><i class="questa"></i>', name: 'adventActiveCredentialCount', index: 'adventActiveCredentialCount', width: 250},
    {colName: '<i>逾期客户数</i><i class="questa"></i>', name: 'overdueCustomerCount', index: 'overdueCustomerCount'},
    {colName: '<i>客户逾期率</i><i class="questa"></i>', name: 'overdueCustomerRatio', index: 'overdueCustomerRatio', formatter: formatter},
    {colName: '<i>逾期资质总数</i><i class="questa"></i>', name: 'overdueCredentialCount', index: 'overdueCredentialCount'},
    {colName: '<i>资质逾期率</i><i class="questa"></i>', name: 'overdueCredentialRatio', index: 'overdueCredentialRatio', formatter: formatter},
  ]

  // 创建表格
  $('#baseTable').XGrid({
    colNames: colMaps.map(item => {
      return item.colName
    }),
    colModel: colMaps.map(item => {
      delete item.colName
      return item
    }),
    // 设置为交替行表格,默认为false
    altRows: true,
    // 设置每页展示行数，-1 表示全部展示
    rowNum: -1,
    // 是否展示序号
    rownumbers: true,
  });


  function fetchData() {
    $.ajax({
        type: 'post',
        data: $("#searchForm").serializeToJSON(),
        url: '/proxy-customer/customer/statistic/summaryList',
        success: (res) => {
          const total = res.result.recordTotal
          if (total) {
            total.orgName = '合计'
            res.result.recordList.push(total)
          }
          $('#baseTable').XGrid('setGridParam', {
            data: res.result.recordList
          }).trigger('reloadGrid')
        }
      }
    )
  }
  /**字段名称解释start*/
  var questaOption
      = [
    {
      name:'credentialCount',
      content:'客户的资质总数，包括客户批准文件，质量保证协议，客户委托书 ，被委托人身份证4个页签下的资质总数。比如客户A有1个营业执照，1个药品经营许可证，2个委托书，2个委托人身份证，1个质保协议，那该客户的资质总数为7。'
    },
    {
      name:'adventCustomerCount',
      content:'当客户存在资质临期【30<= 资质剩余效期>=1 天】，即为临期客户。比如客户A有6个资质，其中有1个资质临期，那此客户即为临期客户。'
    },
    {
      name:'adventCredentialCount',
      content:'客户资质【30<= 资质剩余效期>=1 天】的临期总数，比如客户A有6个资质，其中有3个临期，那此客户的临期资质数是3.'
    },
    {
      name:'adventCustomerRatio',
      content:'客户临期率=临期客户数/客户总数'
    },
    {
      name:'adventCredentialRatio',
      content:'资质临期率=临期资质总数/客户资质总数'
    },
    {
      name:'adventActiveCustomerCount',
      content:'近30天有下单记录的【在神农存在未取消状态的订单】临期客户数'
    },
    {
      name:'adventActiveCredentialCount',
      content:'近30天有下单记录的临期客户对应的临期资质总数'
    },
    {
      name:'overdueCustomerCount',
      content:'当客户存在资质逾期【资质剩余效期<=-1】，即为逾期客户。比如客户A有6个资质，其中有1个资质逾期，那此客户即为逾期客户'
    },
    {
      name:'overdueCustomerRatio',
      content:'客户逾期率=逾期客户数/客户总数'
    },
    {
      name:'overdueCredentialCount',
      content:'客户资质【资质剩余效期<=-1】的逾期总数，比如客户A有6个资质，其中有2个逾期，那此客户的逾期资质数是2'
    },
    {
      name:'overdueCredentialRatio',
      content:'资质逾期率=逾期资质总数/客户资质总数'
    },
  ];

  $("body").on({
    mouseover:function (e) {
      var item = questaOption.find(function (item) {
        return item.name === $(e.target).parent('th').attr('row-describedby');
      });
      $("body").append("<div id='div_toop'><div id='inner'>"+item.content+"</div></div>");
      $("#div_toop")
          .css({
            "top": (e.pageY - 44) + "px",
            "position": "absolute",
            "padding-left": "5px",
            "padding-right": "5px",
            "padding-top": "5px",
            "padding-bottom": "5px",
            "background-color": "lightGray",
            "left": (e.pageX - 130) + "px"
          }).show("fast");
    },
    mouseout:function () {
      $("#div_toop").remove();
    }
  },questaOption.map(function (item) { return "th[row-describedby='"+item.name+"'] .questa" }).join(','));
  /**
   * 1. 设置查询按钮点击事件
   * 2. 立即触发查询按钮的点击事件
   */
  $("#searchBtn").on("click", fetchData).trigger('click');


  /**
   * 设置导出按钮点击事件
   */
  $("#exportBtn").on("click", function () {
    const data = $('#baseTable').XGrid('getGridParam').data
    //无数据提示
    if (data.length==0){
      utils.dialog({"content":"无数据可导出","timeout":2000}).show();
      return
    }
    utils.dialog({
      title: '提示',
      content: "数据导出需要一定时间，请耐心等待。",
      okValue: '开始导出',
      cancelValue: '取消',
      ok: function () {
        // 利用 form 表单完成导出
        const form = $('#searchForm');
        // 导出接口
        $(form).attr("action", "/proxy-customer/customer/statistic/summaryList/export");
        $(form).submit();
      },
      cancel: function () {
      },
    }).showModal();
  });
})
