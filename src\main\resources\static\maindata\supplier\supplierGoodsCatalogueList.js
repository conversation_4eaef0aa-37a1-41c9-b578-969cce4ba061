var urlObjectList = [];
let baseName = ['','采购员', '机构','list'],
    baseModel = [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },
        {
            name: 'productBuyers',
            index: 'productBuyers',
            width: 200
        },
        {
            name: 'orgName',
            index: 'orgName',
            width: 350
        },
        {
            name: 'scopeCodeList',//供应商经营范围
            index: 'scopeCodeList',
            hidden:true
        }
    ];
let supplierName = ['供应商编码', '供应商名称'],
    supplierModel = [
        {
            name: 'supplierCode',
            index: 'supplierCode',
            width: 200

        },
        {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        }
    ];
let productName = ['商品编码', '商品名称', '通用名', '规格/型号','生产厂家'],
    productModel = [
        {
            name: 'productCode',
            index: 'productCode',
            width: 150
        }, {
            name: 'productName',
            index: 'productName',
            width: 150
        }, {
            name: 'commonName',
            index: 'commonName',
            width: 130
        }, {
            name: 'specifications',
            index: 'specifications',
            width: 200
        }, {
            name: 'manufacturerValue',
            index: 'manufacturerValue',
            width: 200
        },
    ];
let defaultName = supplierName.concat(productName),
    defaultModel = supplierModel.concat(productModel);
let colNames = baseName.splice(1,0,defaultName),
    colModel = baseModel.splice(1,0,defaultModel);
colNames = Array.prototype.concat.apply([],baseName);
colModel = Array.prototype.concat.apply([],baseModel);
$('#X_Tableb').XGrid({
    url:"/proxy-supplier/supplier/supplierGoodsCatalogue/ajaxSupplierGoodsCatalogueList",
    colNames: colNames,
    colModel: colModel,
    rowNum: 20,
    rowList:[20,50,100],
    rownumbers:true,
    altRows: true,//设置为交替行表格,默认为false
    multiselect: true,//是否多选
    pager: '#grid-pager',
    onSelectRow: function (id, dom, obj, index, event) {
        console.log(id, dom, obj, index, event)
        setUrlObjectList(dom,id,obj);
    }
});

//查看方式   viewType
$('#viewType').on('change', function () {
    let thisVal = $(this).val();
    baseName = ['','采购员', '机构','list']
    baseModel = [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                hidden:true
            },
            {
                name: 'productBuyers',
                index: 'productBuyers',
                width: 200
            },
            {
                name: 'orgName',
                index: 'orgName',
                width: 350
            },
            {
                name: 'scopeCodeList',//供应商经营范围
                index: 'scopeCodeList',
                hidden:true
            }
        ];
    if (thisVal == '2') {
        defaultName = productName.concat(supplierName);
        defaultModel = productModel.concat(supplierModel);
    }else{
        defaultName = supplierName.concat(productName);
        defaultModel = supplierModel.concat(productModel);
    }
    baseName.splice(1,0,defaultName);
    baseModel.splice(1,0,defaultModel);
    colNames = Array.prototype.concat.apply([],baseName);
    colModel = Array.prototype.concat.apply([],baseModel);
    $('.hidden-x-scroll').html('<table id="X_Tableb"> </table>')
    $('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierGoodsCatalogue/ajaxSupplierGoodsCatalogueList",
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        multiselect: true,//是否多选
        pager: '#grid-pager',
        postData: {
            "viewType": $("#viewType").val(),
            "supplierCodeOrName": $("#supplierCodeOrName").val(),
            "productCode":$("#productCode").val(),
            "productCode1":$("#productCode1").val(),
            "orgCode":$("#orgCode").val()
        },
    });
})

//搜索按钮
$("#searchBtn").on("click", function () {
    urlObjectList=[];
    $('#X_Tableb').XGrid('setGridParam', {
        postData: {
            "viewType": $("#viewType").val(),
            "supplierCodeOrName": $("#supplierCodeOrName").val(),
            "productCode":$("#productCode").val(),
            "productCode1":$("#productCode1").val(),
            "orgCode":$("#orgCode").val(),

        },page:1
    }).trigger('reloadGrid');
});

//新增按钮  supplierGoodsCatalogueVOList
$("#addBtn").on("click", function () {
    utils.dialog({
        url: '/proxy-supplier/supplier/supplierGoodsCatalogue/toInsertList',//弹框页面请求地址
        title: '新增',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        // data: '', // 给modal 要传递的 的数据   后期也不打开
        onclose:function(){
            if(this.returnValue){
              var data=this.returnValue;
              // $.ajax({
              //     url:'/proxy-supplier/supplier/supplierOrganBaseChangeApproval/queryOrganBaseById',
              //     data:{"id":data.id},
              //     type:"post",
              //     dataType:'json',
              //     success:function(data){
              //     },
              //     error:function(){}
              // });
            }
          }
    }).showModal();
});
//删除按钮  supplierGoodsCatalogueVOList
$("#delBtn").on("click", function () {
    let selectRow =  $('#X_Tableb').XGrid('getSeleRow');
    let supplierGoodsCatalogueVOList = [];
    if (selectRow.length == 0) {
        utils.dialog({content: '请选择要删除的行！', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    supplierGoodsCatalogueVOList = selectRow.map(item => {
        return {id: item.id,supplierCode: item.supplierCode,productCode:item.productCode}
    })
    console.log(supplierGoodsCatalogueVOList)

    utils.dialog({
        title: '温馨提示:',
        content: '确定要删除选中的数据吗？',
        width: 300,
        height: 50,
        okValue: '是',
        ok: function () {
            $.ajax({
                url: '/proxy-supplier/supplier/supplierGoodsCatalogue/delSupplierGoodsCatalogue',
                data:JSON.stringify(supplierGoodsCatalogueVOList),
                type:"post",
                dataType:'json',
                contentType: "application/json",
                success: function (data) {
                    if("0"==data.code){
                        utils.dialog({content: "删除成功", quickClose: true, timeout: 2000}).showModal();
                    }else{
                        utils.dialog({content: data.msg, quickClose: true, timeout: 2000}).showModal();
                    }
                    $('#X_Tableb').XGrid({
                        url:"/proxy-supplier/supplier/supplierGoodsCatalogue/ajaxSupplierGoodsCatalogueList",
                        colNames: colNames,
                        colModel: colModel,
                        rowNum: 20,
                        rowList:[20,50,100],
                        rownumbers:true,
                        altRows: true,//设置为交替行表格,默认为false
                        multiselect: true,//是否多选
                        pager: '#grid-pager',
                        postData: {
                            "viewType": $("#viewType").val(),
                            "supplierCodeOrName": $("#supplierCodeOrName").val(),
                            "productCode":$("#productCode").val(),
                            "productCode1":$("#productCode1").val(),
                            "orgCode":$("#orgCode").val()
                        }
                    });
                },
                error: function (err) {
                    console.log(err)
                    utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
                }
            });
        },
        cancelValue: '否',
        cancel: function () {}
    }).showModal();
});





//商品 点击放大镜触发
$(document).on("click", "#searchProductBtn", function (ev) {
    searchPdroduct(ev);
});
// 商品 回车触发
$(document).on("keydown", "#productCode1", function (ev) {
    if(ev.keyCode==13){
        searchPdroduct(ev);
    }
});
// 搜索商品
function searchPdroduct(ev){
    var orgCode = $("#orgCode").val();
    dialog({
        url: '/proxy-supplier/supplier/supplierGoodsCatalogue/toProductCatalogueList?compareFlag=false',//弹框页面请求地址
        title: '搜索商品',
        width: 1000,
        height: 650,
        data: {
            orgCode: orgCode,
            compareFlag:false//是否比较供应商的经营范围和商品的经营范围 false 不比较
        },
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                $('#productCode1').val(data.productCode);
            }
        }

    }).showModal();
    ev.stopPropagation();
}



function setUrlObjectList($tr,id,rowData){
    var a=rowData;
    var fileParam = {};
    if(!id){
        //  点击的是全选按钮。，所以拿全部数据
        urlObjectList = a.map(function (item,index) {
            let _obj = {}
            _obj.id = item.id;
            return _obj;
        })
    }else{
        if($tr.hasClass('selRow') && a.snapImageUrl){
            fileParam.id = a.id;
            urlObjectList.push(fileParam);
        }else if(!$tr.hasClass('selRow')){
            $(urlObjectList).map(function (i,v) {
                if(v.id==a.id){
                    urlObjectList.splice(i,1);
                }
            })
        }
    }
}





