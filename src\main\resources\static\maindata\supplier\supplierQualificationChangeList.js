$(function () {
//创建供应商列表
    $('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierQualificationChangeController/querySupplierQualificationChangeList",
        colNames: ['', '申请日期', '机构', '申请人ID', '申请人', '单据编号', '供应商编码', '供应商名称', '供应商类型', '营业执照号','审核状态','审核id','','审核日期'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden:true
        }, {
            name: 'applicationTime',
            index: 'applicationTime',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'orgCodeName',
            index: 'orgCodeName'
        }, {
            name: 'applicantId',
            index: 'applicantId',
            hidden:true
        }, {
            name: 'applicantName',
            index: 'applicantName'
        }, {
            name: 'applicationCode',
            index: 'applicationCode'
        }, {
            name: 'supplierCode',
            index: 'supplierCode'
        }, {
            name: 'supplierName',
            index: 'supplierName'
        }, {
            name: 'supplierTypeName',
            index: 'supplierTypeName'
        }, {
            name: 'supplierBusinessNum',
            index: 'supplierBusinessNum'
        },
            {
                name: 'auditStatus',
                formatter: function (e) {
                    if (e == '0') {
                        return '录入中'
                    } else if (e == '1') {
                        return '审核中'
                    }else if (e == '2') {
                        return '审核通过'
                    }else if (e == '3') {
                        return '审核不通过'
                    }
                }
            },
            {
                name: 'auditStatus',
                hidden:'true'
            },{
                name: 'applicantId',
                index: 'applicantId',
                hidden:true
            },{
                name: 'auditTime',
                index: 'auditTime',
                formatter:function(value){
                    var date=value;
                    if(!value)return '';
                    date=format(value);
                    return date.split(' ')[0];
                }
            }],
        rowNum: 20,
        rownumbers: true,//是否展示序号
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {
            //录入中的进入可编辑页面
            if("0"==obj.auditStatus){
                var loginUserId = $("#loginUserId").val();
                if(obj.applicantId==loginUserId){
                    var url="/proxy-supplier/supplier/supplierQualificationChangeController/querySupplierQualificationChangeDetail?id="+id+"&auditStatus="+obj.auditStatus;
                    parent.openTabs("supplierQualificationChangeDetail","供应商资质变更详情",url);
                }else{
                    utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                }
            }else{
                var url="/proxy-supplier/supplier/supplierQualificationChangeController/querySupplierQualificationChangeDetail?id="+id+"&auditStatus="+obj.auditStatus;
                parent.openTabs("supplierQualificationChangeDetail","供应商资质变更详情",url);
            }

        },
        onSelectRow: function (id, dom, obj, index, event, obj) {
            console.log('单机行事件', id, dom, obj, index, event, obj);
        },
        pager: '#grid-pager'
    });
    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "orgCode": $('#orgCode option:selected').val().trim(),
                "applicationCode":$("#application").val().trim(),
                "supplierCode":$("#supplier").val().trim(),
                "supplierName":$("#supplier").val().trim(),
                "supplierMnemonicCode":$("#supplier").val().trim(),
                "auditStatus":$("#auditStatus").val().trim(),
                "applicationBegin":$("#applicationBegin").val().trim(),
                "applicationEnd":$("#applicationEnd").val().trim(),
                "auditBegin":$("#auditBegin").val().trim(),
                "auditEnd":$("#auditEnd").val().trim()
            },page:1
        }).trigger('reloadGrid');
    });
})

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }

// 删除草稿
$("#deleteDraftBtn").on("click", function () {
    let selRow = $('#X_Tableb').XGrid('getSeleRow');
    // 表格重新渲染 的参数
    let postData = {
        "orgCode": $('#orgCode option:selected').val().trim(),
        "applicationCode":$("#application").val().trim(),
        "supplierCode":$("#supplier").val().trim(),
        "supplierName":$("#supplier").val().trim(),
        "supplierMnemonicCode":$("#supplier").val().trim(),
        "auditStatus":$("#auditStatus").val().trim(),
        "applicationBegin":$("#applicationBegin").val().trim(),
        "applicationEnd":$("#applicationEnd").val().trim(),
        "auditBegin":$("#auditBegin").val().trim(),
        "auditEnd":$("#auditEnd").val().trim()
    }
    // supplierOrganBaseId：  删除草稿需要传递的参数
    let data = {
        qualificationId: selRow.length != 0 ? selRow[0].id : ''
    }
    let params = {
        statusName: selRow.length != 0 ? selRow[0].auditStatus: '', //  表格中审核状态的字段
        statusVal: '0', // 对应的录入中的状态值
        url: '/proxy-supplier/supplier/supplierQualificationChangeController/deleteDraftsQualification', // 删除草稿的url
        applicantId: selRow.length != 0 ? selRow[0].applicantId : '', //选中行申请人的ID
        loginUserId: $('#loginUserId').val() // 当前登录人 ID
    }
    // 表格id
    utils.deleteDraft('X_Tableb', params, data, postData)
});
