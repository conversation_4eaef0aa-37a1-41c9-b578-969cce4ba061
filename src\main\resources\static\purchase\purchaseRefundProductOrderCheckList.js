﻿  //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    //导出时判断是否有选中项，有的话取值，没有的话拿全部值
    function getExportCheckVal() {
        var check_val = [];
        $('input[name="che"]:checked').each(function () {
            check_val.push($(this).val());
        })
        //console.log(check_val)
        return check_val;
    }



/* 获取出库单选中项,并获取单据数据(组装) */

function seleRow(callback) {
    var ary = [];
    var cs = $("#toggle_wrap .active").attr("id");
    var sele_data =[];
    if(cs == "orderList"){
        sele_data = $("#X_Tablea").XGrid("getSeleRow");
    }else{
        sele_data = $("#X_Tableb").XGrid("getSeleRow");
    }
    if(sele_data.length>0){
        if(!$.isArray(sele_data)){
            ary.push(sele_data.orderRefundProductNo)
        }else {
            sele_data.forEach(function (item,index) {
                ary.push(item.orderRefundProductNo);
            })
            // ary.push(sele_data[0].orderRefundProductNo);
        }
    }else {
        utils.dialog({
            title:'预览',
            content:"无打印数据",
            timeout:2000
        }).show();
        return
    }
    callback(ary);
}


/* 打印预览 */
$("#btn_print_view").on("click",function () {
    seleRow(function (ary) {
        $("#print_box")[0].contentWindow.getData(0,ary);
    })
});

$("#btn_print").on("click",function () {
        utils.dialog({
            content:"正在打印...",
            timeout:1000
        }).showModal();
        seleRow(function (ary) {
            $("#print_box")[0].contentWindow.getData(1,ary);
        })
});