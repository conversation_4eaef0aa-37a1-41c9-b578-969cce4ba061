$('div[fold=head]').fold({
    sub: 'sub'
});
//鼠标在不在原因备注上
var nowIsReasonRemarks = false
var showReasonRemarks;
var channelId = "${purchaseOrderNo.channelId}";
var pageType = $("#pageType").val();
var applyTime = $("#applyTime").val() + " 00:00:00";
applyTime = applyTime.replace(/-/g, '/'); //必须把日期'-'转为'/'
var date1 = new Date(applyTime);
var curMonth = date1.getMonth();
curMonth = (curMonth == 0) ? '12' : curMonth;
var lastMonth = ((curMonth - 1) == 0) ? '12' : (curMonth - 1);

var lastLastMonth = ((lastMonth - 1) == 0) ? '12' : (lastMonth - 1);
var colNames = ['ID', '商品id', '机构编码', '机构', '业务类型', '商品唯一值', '原商品id', '商品编号', '原商品编码', '小包装条码', '商品名称', '通用名', '商品规格', '生产厂家', '单位',
    '采购员', '销售状态', '下架原因', '原因说明', "替换编码", '原因备注', '附件', '商品定位一级', '商品定位一级', '品类策略审核商品定位一级', '商品定位二级', '商品定位二级', '品类策略审核商品定位二级', '集采签约方式', '集采签约方式', '品类策略审核集采签约方式', '销售定位', '品类组审核销售定位', '经营状态隐藏字段', '经营状态', '品类组审核经营状态', '合格品库可用库存数量', '总库存数量',
    '最后含税进价', '最后供应商', 'APP售价', '终端建议零售价', '商品产地', '剂型', '处方分类', '批准文号',
    '一级分类', '二级分类', '三级分类', '四级分类', '应季类型', 'TOP排名', lastLastMonth + '月总销量', lastMonth + '月总销量', curMonth + '月总销量', '月至今netGMV', '当月销量', '上月客户数', '当月客户数', '市占率', '30天内售罄天数', '60天内售罄天数', '是否属于首推高毛专区的商品'
];
var colModel = [{
        name: 'id',
        index: 'id',
        hidden: true,
        hidegrid: true
    }, {
        name: 'productId',
        index: 'productId',
        hidden: true,
        hidegrid: true
    }, {
        name: 'orgCode',
        index: 'orgCode',
        hidden: true,
        hidegrid: true
    }, {
        name: 'orgName',
        index: 'orgName',
        width: 210
    }, {
        name: 'channelId',
        index: 'channelId',
        width: 100
    }, {
        name: 'productIdChannel',
        index: 'productIdChannel',
        hidden: true,
        hidegrid: true
    }, {
        name: 'oldProductId',
        index: 'oldProductId',
        hidden: true,
        hidegrid: true
    }, {
        name: 'productCode',
        index: 'productCode',
        width: 100
    }, {
        name: 'oldProductCode',
        index: 'oldProductCode',
        width: 140
    }, {
        name: 'smallPackageBarCode',
        index: 'smallPackageBarCode',
        width: 140
    }, {
        name: 'productName',
        index: 'productName',
        width: 110
    }, {
        name: 'commonName',
        index: 'commonName',
        width: 110
    }, {
        name: 'specifications',
        index: 'specifications',
        width: 140
    }, {
        name: 'manufacturerName',
        index: 'manufacturerName',
        width: 240
    }, {
        name: 'packingUnitVal',
        index: 'packingUnitVal',
        width: 80
    }, {
        name: 'buyerVal',
        index: 'buyerVal',
        width: 80
    }, {
        name: 'ecStatusVal',
        index: 'ecStatusVal',
        width: 80
    }, {
        name: 'lowerShelfReason',
        index: 'lowerShelfReason',
        rowtype: '#lowerShelfReason',
        width: 200
    }, {
        name: 'remarks',
        index: 'remarks',
        //rowtype: '#remarks',
        width: 250,
        formatter: (val, rowType, rowData) => {
            let htmlArr = [];
            htmlArr.push('<select name="remarks" class="form-control applyUpperShelf">');
            htmlArr.push('<option value="">请选择</option>');

            //根据原因说明拼装select的option
            let remarksOptionElement = [];
            //下架原因select
            if (rowData.lowerShelfReason) {
                const options = remarksOptionsAdapter(rowData.lowerShelfReason)
                options.forEach(optionItem => {
                    remarksOptionElement.push(`<option value="${optionItem.value}" selected="${rowData.remarks === optionItem.value}">${optionItem.label}</option>`)
                });
                //remarks兼容旧的input数据
                if (rowData.remarks) {
                    let remarksOldValueFilter = options.some(optionItem => {
                        return optionItem.value === rowData.remarks
                    })
                    if (!remarksOldValueFilter) {
                        remarksOptionElement.push(`<option value="${rowData.remarks}" selected="true">${rowData.remarks}</option>`)
                    }
                }
            } else {
                //remarks兼容旧的input数据
                if (rowData.remarks) {
                    remarksOptionElement.push(`<option value="${rowData.remarks}" selected="true">${rowData.remarks}</option>`)
                }
            }
            if (remarksOptionElement.length) {
                htmlArr.push(remarksOptionElement.join(''))
            }

            htmlArr.push('</select>')
            return htmlArr.join('')
        }
    }, {
        name: 'replaceEncoding',
        index: 'replaceEncoding',
        hidden: true,
        hidegrid: true,
        width: 200,
        formatter: (a, b, arr) => {
            if (a) { return `<div style='display: flex;justify-content: center;'><input type='text' onclick='selectProduct(this)' value=${a} class="form-control applyUpperShelf" id='replaceEncoding' name='replaceEncoding' maxlength='200'><div style='background-color: black;color: white;border-radius: 50%;width: 20px ;height: 20px;text-align: center;' onmouseenter='replaceEncodingTip(this)'  onmouseleave='questionTip.close().remove()'  specifications='${arr.replaceSpecifications}'    productName='${arr.replaceProductName}'     manufacturerName='${arr.replaceManufacturerName}' >?</div> </div>` } else {
                return `<div style='display: flex;justify-content: center;'><input type='text' onclick='selectProduct(this)'  class="form-control applyUpperShelf" id='replaceEncoding' name='replaceEncoding' maxlength='200'><div style='background-color: black;color: white;border-radius: 50%;width: 20px ;height: 20px;text-align: center;' onmouseenter='replaceEncodingTip(this)'  onmouseleave='questionTip.close().remove()'   specifications=''    productName=''     manufacturerName=''  >?</div> </div>`
            }
        },
    },
    {
        name: 'reasonRemarks',
        index: 'reasonRemarks',
        hidden: false,
        hidegrid: true,
        formatter: (reasonRemarks) => {
            if (reasonRemarks) { return `<input type='text' class="form-control applyUpperShelf"   id='reasonRemarks' name='reasonRemarks' maxlength='200' value=${reasonRemarks} onmouseenter='enterReasonRemarks(this)' onmouseleave='leaveReasonRemarks(this)'>` } else {
                return `<input type='text' class="form-control applyUpperShelf"  id='reasonRemarks' name='reasonRemarks' maxlength='200' onmouseenter='enterReasonRemarks(this)' onmouseleave='leaveReasonRemarks(this)' >`
            }

        },
        width: 200
    }, {
        name: 'enclosureUrl',
        index: 'enclosureUrl',
        formatter: function(val, rowType, rowData) {
            if (val) {
                return `<a href="javascript:;" data-url="${val}" class="file_a">📎</a>`;
            } else if (pageType == 0 || pageType == 1 || pageType == 4) {
                return `<button type="button" class="btn btn-info file_btn">上传</button><input style="display: none" class="file_input" type="file" id="${rowData.productIdChannel}_file"/>`;
            }

        },
        unformat: function(val, rowModel, ele) {
            if ($(ele).find('a.file_a').length > 0) {
                return $(ele).find('a.file_a').attr('data-url');
            } else {
                return '';
            }
        }
    }, {
        name: 'commodityPosition',
        index: 'commodityPosition',
        hidden: true,
        hidegrid: true
    }, {
        name: 'commodityPositionVal',
        index: 'commodityPositionVal',
        width: 150
    }, {
        name: 'auditCommodityPosition',
        index: 'auditCommodityPosition',
        rowtype: '#auditCommodityPosition',
        width: 200
    }, {
        name: 'secondCommodityPosition',
        index: 'secondCommodityPosition',
        hidden: true,
        hidegrid: true
    }, {
        name: 'secondCommodityPositionVal',
        index: 'secondCommodityPositionVal',
        width: 150
    }, {
        name: 'auditSecondCommodityPosition',
        index: 'auditSecondCommodityPosition',
        rowtype: '#auditSecondCommodityPosition',
        width: 200
    }, {
        name: 'purchaseContractMode',
        index: 'purchaseContractMode',
        hidden: true,
        hidegrid: true
    }, {
        name: 'purchaseContractModeVal',
        index: 'purchaseContractModeVal',
        width: 150
    }, {
        name: 'auditPurchaseContractMode',
        index: 'auditPurchaseContractMode',
        rowtype: '#auditPurchaseContractMode',
        width: 200
    }, {
        name: 'salesClassification',
        index: 'salesClassification',
        width: 120
    }, {
        name: 'auditSalesClassification',
        index: 'auditSalesClassification',
        rowtype: '#auditSalesClassification',
        width: 170
    }, {
        name: 'operatingState',
        index: 'operatingState',
        width: 170,
        hidden: true,
    }, {
        name: 'operatingStateVal',
        index: 'operatingStateVal',
        width: 170
    }, {
        name: 'auditOperatingState',
        index: 'auditOperatingState',
        rowtype: "#auditOperatingState",
        width: 170
    }, {
        name: 'inventoryQuantity', //【库存数量】名称改为【合格品库可用库存数量】
        index: 'inventoryQuantity',
        width: 170
    }, {
        name: 'allStockAmount', //总库存数量
        index: 'allStockAmount',
        width: 110
    }, {
        name: 'lastIncludingTaxPurchasePrice',
        index: 'lastIncludingTaxPurchasePrice',
        width: 110
    }, {
        name: 'lastSupplier',
        index: 'lastSupplier',
        width: 220
    }, {
        name: 'appPrice',
        index: 'appPrice',
        formatter: function(e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        width: 110
    }, {
        name: 'terminalPrice',
        index: 'terminalPrice',
        formatter: function(e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        width: 130
    }, {
        name: 'producingArea',
        index: 'producingArea',
        width: 80
    }, {
        name: 'dosageFormVal',
        index: 'dosageFormVal',
        width: 100
    }, {
        name: 'prescriptionClassificationVal',
        index: 'prescriptionClassificationVal',
        width: 100
    }, {
        name: 'approvalNumber',
        index: 'approvalNumber',
        width: 160
    }, {
        name: 'firstCategoryVal',
        index: 'firstCategoryVal',
        width: 100
    }, {
        name: 'secondCategoryVal',
        index: 'secondCategoryVal',
        width: 120
    }, {
        name: 'thirdCategoryVal',
        index: 'thirdCategoryVal',
        width: 120
    }, {
        name: 'fourCategory',
        index: 'fourCategory',
        width: 100
    }, {
        name: 'seasonalVarietiesVal',
        index: 'seasonalVarietiesVal',
        width: 120
    }, {
        name: 'top',
        index: 'top',
        width: 120
    }, {
        name: 'threeMonthSaleVolume',
        index: 'threeMonthSaleVolume',
        width: 120
    }, {
        name: 'twoMonthSaleVolume',
        index: 'twoMonthSaleVolume',
        width: 120
    }, {
        name: 'oneMonthSaleVolume',
        index: 'oneMonthSaleVolume',
        width: 120
    }, {
        name: 'currentNetGMV', //月至今netGMV
        index: 'currentNetGMV',
        width: 170
    }, {
        name: 'currentMonthSaleAmount', //当月销量
        index: 'currentMonthSaleAmount',
        width: 120
    }, {
        name: 'lastMonthCustomerNum', //上月客户数
        index: 'lastMonthCustomerNum',
        width: 120
    }, {
        name: 'currentMonthCustomerNum', //当月客户数
        index: 'currentMonthCustomerNum',
        width: 120
    }, {
        name: 'marketRatio', //市占率
        index: 'marketRatio',
        width: 120
    }, {
        name: 'thirtySaleoutDays',
        index: 'thirtySaleoutDays',
        width: 170
    }, {
        name: 'sixtySaleoutDays',
        index: 'sixtySaleoutDays',
        width: 170
    }, {
        name: 'productFirstGm',
        index: 'productFirstGm',
        hidden: true,
        hidegrid: true
    }
];
var auditEdit = $("#auditEdit").val();
let currentWorkStatus = false
resetDisabled(); // 设置可编辑单元格
if (pageType == '0') { //申请页
    $('#X_Table').XGrid({
        data: [],
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rownumbers: true,
        key: 'productIdChannel',
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function(id, dom, obj, index, event) {
            //console.log('双击行事件', id, dom, obj, index, event);
        },
        onSelectRow: function(id, dom, obj, index, event) {
            //console.log('单机行事件', id, dom, obj, index, event);
        },


    });
} else { //编辑页

    var recordId = $("#recordId").val();
    $('#X_Table').XGrid({
        url: "/proxy-product/product/lowerShelf/querylowerShelfDetail?approvalRecordId=" + recordId,
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rownumbers: true,
        key: 'productIdChannel',
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function(id, dom, obj, index, event) {
            //console.log('双击行事件', id, dom, obj, index, event);
        },
        onSelectRow: function(id, dom, obj, index, event) {
            //console.log('单机行事件', id, dom, obj, index, event);
        },
        gridComplete: function() {
            //删除原生的提示框
            $('#X_Table').find('[row-describedby="replaceEncoding"]').removeAttr("title")
                //处理select
            var idList = []
            const rowData = $('#X_Table').XGrid('getRowData');
            rowData.forEach(rowItem => {
                    if (rowItem.id) {
                        idList.push({ id: rowItem.rowid, productCode: rowItem.productCode, channelId: rowItem.channelId })
                    }
                    const remarksSelect = $('#X_Table #' + rowItem.productIdChannel).find('[row-describedby="remarks"] select')
                    const reasonRemarks = $('#X_Table #' + rowItem.productIdChannel).find('[row-describedby="reasonRemarks"] input')
                    const replaceEncoding = $('#X_Table #' + rowItem.productIdChannel).find('[row-describedby="replaceEncoding"] input')
                    remarksSelect.parent('td').removeAttr('title')
                    remarksSelect.val(remarksSelect.find('[selected="true"]').prop('value')).attr("disabled", true)
                    reasonRemarks.attr("readonly", true)
                    replaceEncoding.attr("disabled", true)
                        //申请：0，编辑：1，驳回：4  设置可编辑单元格
                    if (pageType == '0' || pageType == '1' || pageType == '4') {
                        remarksSelect.attr("disabled", false);
                        reasonRemarks.attr("disabled", false)
                        replaceEncoding.attr("disabled", false)
                    }
                })
                //处理编码(所有編輯狀態下所有选择更换编码的全部清空)
            rowData.forEach((rowTtem, index) => {
                if (rowTtem.lowerShelfReason == 8) {
                    if (pageType == '0' || pageType == '1' || pageType == '4') { $('#X_Table').find('[row-describedby="replaceEncoding"] input').val("") }
                    $('#X_Table').setGridParam({
                        colModel: [{
                            name: 'replaceEncoding',
                            index: 'replaceEncoding',
                            width: 200,
                            hidden: false,
                            formatter: (a, b, arr) => {
                                return "<div style='display: flex;justify-content: center;'><input type='text' onclick='selectProduct(this)' style='width:100px;' id='replaceEncoding' name='replaceEncoding' maxlength='200'><div style='background-color: black;color: white;border-radius: 50%;width: 20px ;height: 20px;text-align: center;'  onmouseenter='enterReasonRemarks(this)' onmouseleave='leaveReasonRemarks(this)'   specifications=''    productName=''     manufacturerName=''  >?</div> </div>"
                            },

                        }]
                    })

                    var $reasonRemarks = $("#X_Table").find(`[row-describedby="reasonRemarks"] #reasonRemarks:eq(${index})`)
                        //当选择编码时，禁用原因备注框
                    $reasonRemarks.attr("disabled", "disabled")
                    $reasonRemarks.val("")

                }

            })



            if (currentWorkStatus) {
                addBackGroundColor();
            }
            if (pageType == '3') {
                idList.forEach(function(idItem) {
                    var tid = null;
                    var idRowDom = $('#' + idItem.id)
                    idRowDom.hover(function() {
                        tid = setTimeout(function() {
                            // console.log('1s后触发')
                            //当触发hover就开始自动在1秒后执行相应代码
                            if (nowIsReasonRemarks == false) {
                                utils.dialog({
                                    url: '/proxy-purchase/purchase/purchaseOrder/toApprovalAssistantList',
                                    title: "审批助手",
                                    content: idItem.productCode,
                                    width: $(window).width() * 0.5 > 750 ? $(window).width() * 0.5 : '830px',
                                    height: '400px',
                                    padding: 0,
                                    quickClose: true,
                                    data: {
                                        channelId: idItem.channelId,
                                        productCode: idItem.productCode,
                                    }, // 给modal 要传递的 的数据
                                }).showModal();
                            }

                        }, 2000);


                    }, function() {
                        clearTimeout(tid); //当在2秒内退出了hover事件就取消计时代码
                    })
                })
            }
        }
    });
}

//下架原因返回原因说明
function remarksOptionsAdapter(reasonValue) {
    const remarksOptions = {
        '6': [ //厂家因素
            { value: '厂家停产(规格、原料)', label: '厂家停产(规格、原料)' },
            { value: '政策原因(带量采购、医保中标等)', label: '政策原因(带量采购、医保中标等)' },
            { value: '供货渠道管控(线上渠道禁销、代理（大包）等)', label: '供货渠道管控(线上渠道禁销、代理（大包）等)' },
            { value: '销售渠道管控（下游客户被限制或禁售）', label: '销售渠道管控（下游客户被限制或禁售）' },
            { value: '厂家批次效期，暂不购进', label: '厂家批次效期，暂不购进' }
        ],
        '7': [ //销售问题
            { value: '连续3个月不满足动销标准', label: '连续3个月不满足动销标准' },
            { value: '连续3个月销售数量为0', label: '连续3个月销售数量为0' },
            { value: '库存清理下架（不良库存，外省调拨）', label: '库存清理下架（不良库存，外省调拨）' },
            { value: '其他', label: '其他' }
        ],
        '8': [ //编码变更
            { value: '同品更换（条码、品牌、其他资质变更等）', label: '同品更换（条码、品牌、其他资质变更等）' },
            { value: '异规更换（规格调整）', label: '异规更换（规格调整）' },
            { value: '采购方式变更（集地采转换）', label: '采购方式变更（集地采转换）' }
        ],
        '9': [ //销售问题
            { value: '上传相关依据附件（邮件或召回函等）', label: '上传相关依据附件（邮件或召回函等）' }

        ]

    }
    return remarksOptions[reasonValue] || []
}

//设置显示列
$("#setRow").click(function() {
    $('#X_Table').XGrid('filterTableHead', 1000);
});
//选择建议下架商品库
$(".selectAdviceProduct").on("click", function() {
    var resultArr = $('#X_Table').getRowData();
    var dataFlag = this.getAttribute("dataFlag");
    dialog({
        url: '/proxy-product/product/lowerShelf/toSearchAdviceProduct?dataFlag=' + dataFlag,
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            "orgCode": $("#recordOrgCode").val(),
            resultArr: resultArr,
            channelId: (resultArr.length != 0) ? resultArr[0].channelId : '',
            channelName: (resultArr.length != 0) ? resultArr[0].channelVal : ''
        }, // 给modal 要传递的 的数据
        onclose: function(data) {
            var data = this.returnValue;
            if (data) {
                var rows = data.resultArr;
                var temp = [];
                $.each(rows, function(i, item) {
                    var orgCode = item.orgCode;
                    temp.push(orgCode)
                });
                $.each(resultArr, function(i, item) {
                    var orgCode = item.orgCode;
                    temp.push(orgCode)
                });
                temp = unique(temp);
                if (temp.length > 1) {
                    utils.dialog({ content: '请选择同一机构商品', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                for (var i = 0; i < rows.length; i++) {
                    var productId = rows[i].productId;
                    if (!findInArr(productId)) {
                        rows[i].id = rows[i]['productId'];
                        $('#X_Table').XGrid('addRowData', rows[i]);
                        addBackGroundColor();
                    }
                }

                let allTrs = $('#X_Table').find('tr').not(':first'),
                    rowData = $('#X_Table').XGrid('getRowData');
                $(rowData).each((index, item) => {
                    $("#X_Table").XGrid('setRowData', item.id, { auditCommodityPosition: item.commodityPosition })
                    $("#X_Table").XGrid('setRowData', item.id, { auditSalesClassification: item.salesClassification })
                })
            }
            setTimeout(function() { //删除原生的提示框
                $('#X_Table').find('[row-describedby="replaceEncoding"]').removeAttr("title")
            }, 2000)
        }
    }).showModal();
});
//数组去重
function unique(arr) {
    var res = [arr[0]];
    for (var i = 1; i < arr.length; i++) {
        var repeat = false;
        for (var j = 0; j < res.length; j++) {
            if (arr[i] === res[j]) {
                repeat = true;
                break;
            }
        }
        if (!repeat) {
            res.push(arr[i]);
        }
    }
    return res;
}
//列表内查询id
function findInArr(productId) {
    var arr = $('#X_Table').getRowData();
    for (var i = 0; i < arr.length; i++) {
        var productID = arr[i].productId;
        if (productID == productId) {
            return true;
        }
    }
    return false;
}
//含商品池的首推高毛专区的商品时整行颜色区分 
function addBackGroundColor() {
    var arr = [];
    arr = $('#X_Table').getRowData();
    console.log(arr, '我在这a ')
    if (arr && arr.length > 0) {
        arr.forEach(ele => {
            if (ele.productFirstGm == '1') {
                console.log('我在这', ele.productIdChannel)
                $("#X_Table  #" + ele.productIdChannel).css('background', 'rgba(254,95,85,1)')
            }
        });
    }
}
//删除行
$("#deleRow").on("click", function() {
    var selectRow = $('#X_Table').XGrid('getSeleRow');
    if (selectRow.length == 0) {
        utils.dialog({ content: '请选择删除行！', quickClose: true, timeout: 2000 }).showModal();
    } else {
        utils.dialog({
            title: "提示",
            width: 300,
            height: 30,
            okValue: '确定',
            content: "确定删除此条记录?",
            ok: function() {
                $('#X_Table').XGrid('delRowData', selectRow[0].id);
                utils.dialog({ content: '删除成功！', quickClose: true, timeout: 2000 }).showModal();
            },
            cancelValue: '取消',
            cancel: function() {}
        }).showModal();
    }
});
//关闭按钮
$("#closePage").on("click", function() {
    var d = dialog({
        title: "提示",
        content: "是否保存草稿？",
        width: 300,
        height: 30,
        okValue: '保存',
        button: [{
            value: '关闭',
            callback: function() {
                utils.closeTab();
            }
        }],
        ok: function() {
            var rowData = $('#X_Table').getRowData();
            if (rowData.length == 0) {
                d.close();
                utils.dialog({ content: '至少添加一种商品', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
            $("#saveCaoGao").click()
            setTimeout(function() {
                utils.closeTab();
            }, 2000)
        }
    }).showModal();
    $(".ui-dialog-close").hide();
});
//提交Ajax请求
function submitFuncAjax(productLowerShelfApprovalRecordVo, status) {
    var data = JSON.stringify(productLowerShelfApprovalRecordVo);
    parent.showLoading();
    // $(".textdisabled").attr("disabled",true);
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/lowerShelf/saveLowerShelfInfo",
        data: data,
        async: false,
        dataType: 'json',
        contentType: "application/json",
        error: function() {
            utils.dialog({ content: "提交失败！", quickClose: true, timeout: 2000 }).showModal();
        },
        success: function(data) {
            if (data.code == 0) {
                var msg = "";
                if (status == 0) {
                    msg = '保存成功';
                } else if (status == 1) {
                    msg = '提交审核成功';
                } else {
                    msg = '操作成功';
                }
                if (data.result.code == 1) {
                    msg = data.result.msg;
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function() {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            } else {
                utils.dialog({
                    title: "提示",
                    content: data.result,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function() {}
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        },
        complete: function() {
            parent.hideLoading();
        }
    });
}
//保存草稿/提交审核
$(".submitAudit").click(function() {
    var status = this.getAttribute("status");
    var formData = $("#applyForm").serializeToJSON();
    var rowData = $('#X_Table').getRowData();
    if (rowData.length == 0) {
        utils.dialog({ content: '至少添加一种商品', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    if (rowData.length > 200) {
        utils.dialog({ content: '最多添加200条商品', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    if (status == 1) {
        if (!checkSubmit()) {
            return false;
        }
    }

    // 补全remarks
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        const $remarksSelect = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="remarks"] select')
        selectRow.remarks = $remarksSelect.val()
            //补全reasonRemarks和编码变更
        selectRow.reasonRemarks = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="reasonRemarks"] input').val()
        selectRow.replaceEncoding = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="replaceEncoding"] input').val()
    }

    //拼接工作流key值,状态
    var productLowerShelfApprovalRecordVo = $.extend(formData, { "workProcessKey": $("#workProcessKey").val() }, { "statues": status });
    productLowerShelfApprovalRecordVo.recordDetailVos = rowData;
    let firstHighWoolAreaStr = '';
    let firstHighWoolAreaList = [];
    rowData.forEach(item => {
        if (item.productFirstGm == '1') {
            firstHighWoolAreaList.push(item.productCode)
        }
    });
    if (firstHighWoolAreaList && firstHighWoolAreaList.length > 0) {
        firstHighWoolAreaStr = firstHighWoolAreaList.join();
        // var d = utils.dialog({
        //     title: "提示",
        //     content: `<div style="word-break: break-all">下架单包含
        //                 <span style="color:red">${firstHighWoolAreaList.length}</span>个首推品种
        //                 <span style="color:red">${firstHighWoolAreaStr}</span>，确认提交吗？
        //               </div>`,
        //     width: 340,
        //     okValue: '确定',
        //     cancelValue: '取消',
        //     cancel: function () {},
        //     ok: function () {
        //         d.close().remove();
        //         submitFuncAjax(productLowerShelfApprovalRecordVo,status);
        //     }
        // }).showModal();
        utils.dialog({
            content: `<div style="word-break: break-all">下架单包含
                        <span style="color:red">${firstHighWoolAreaList.length}</span>个首推品种
                        <span style="color:red">${firstHighWoolAreaStr}</span>，首推高毛专区内商品不允许操作下架！
                      </div>`,
            quickClose: true,
            timeout: 2000
        }).showModal();
        return false
    } else {
        submitFuncAjax(productLowerShelfApprovalRecordVo, status);
    }
});
//申请人驳回后提交审核
$('#auditSubmit').on('click', function() {
    if (!checkSubmit()) {
        return false;
    }
    utils.dialog({
        title: "重新提交",
        content: "确定重新提交申请？",
        width: 300,
        height: 30,
        okValue: '确定',
        ok: function() {
            submitAuditInfo(2, "");
        },
        cancelValue: '取消',
        cancel: function() {}
    }).showModal();
});

/**
 * 必填校验
 * @returns {boolean}
 */
function checkSubmit() {
    var rowData = $('#X_Table').getRowData();
    console.log(rowData)
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        //补全reasonRemarks和编码变更
        selectRow.reasonRemarks = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="reasonRemarks"] input').val()
        selectRow.replaceEncoding = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="replaceEncoding"] input').val()
            // 补全remarks
        const $remarksSelect = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="remarks"] select')
        selectRow.remarks = $remarksSelect.val()

        if (selectRow.lowerShelfReason == "") {
            utils.dialog({ content: '下架原因不能为空', quickClose: true, timeout: 2000 }).showModal();
            return false;
        } else if (selectRow.lowerShelfReason == "6" || selectRow.lowerShelfReason == "7" || selectRow.lowerShelfReason == "8" || selectRow.lowerShelfReason == "9") {
            if (selectRow.remarks == "") {
                utils.dialog({ content: '原因说明不能为空', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
            var content = "";

            // //替换的编码与原编码相同
            // if(selectRow.lowerShelfReason == "2"&&selectRow.remarks!=''&&selectRow.remarks==selectRow.productCode){
            //     content="备注填写的新商品编码不能与商品编码相同，请检查！<br>";
            // }

            //判断是否上传附件
            if (selectRow.lowerShelfReason == "9" && selectRow.enclosureUrl == '') {
                content = '下架原因为质量问题时，必须上传诸如但不限于下列可以证明质量问题的凭证！<br>' +
                    'a、包装问题： 仓库提供凭证采购上传<br>' +
                    'b、厂家召回： 厂家提供凭证采购上传<br>' +
                    'c、质管通知有质量问题 ： 质管提供凭证采购上传';
            }
            //判断选择其它时是否填写备注
            if (selectRow.lowerShelfReason == "7" && selectRow.remarks == "其他" && selectRow.reasonRemarks == "") {
                content = '请填写原因备注'
            }
            //判断选择编码变更是否，填写了编码

            if (selectRow.lowerShelfReason == "8" && selectRow.replaceEncoding == "") {
                content = '请填写替换的编码'
            }
            // if(selectRow.lowerShelfReason == "5"&&selectRow.remarks!=''&&selectRow.remarks.length<5){
            //     content =  '下架原因=“其他”时，备注必填，且必须大于等于5个汉字。';
            // }

            if (content != "") {
                utils.dialog({
                    title: '提示',
                    width: 340,
                    content: content,
                    okValue: '确定',
                    ok: function() {}
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        }
        if (auditEdit == '1') { //品类专员节点
            if (selectRow.auditCommodityPosition == "") {
                utils.dialog({ content: '品类组审核商品定位不能为空', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
            if (selectRow.auditSalesClassification == "") {
                utils.dialog({ content: '品类组审核销售定位不能为空', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
        }
    }
    return true;
}
//审核
$('.audiPass').on('click', function() {
    if (!checkTableItemRequiredFields()) {
        return false
    }
    $('#auditOpinion').val('');
    var status = this.getAttribute("status");
    if (status == 2 && !checkSubmit()) {
        return false;
    }
    var title = "审核通过";
    if (status == 4) {
        title = "审核不通过";
        $('#opinion').show();
    } else if (status == 3) {
        title = "关闭审核";
        $('#opinion').show();
    } else {
        $('#opinion').hide();
    }
    var d = utils.dialog({
        title: title,
        content: $('#container'),
        okValue: '确定',
        ok: function() {
            //审核不通过，意见不能为空
            if (status != 2) {
                if ($("#auditOpinion").val() == "") {
                    utils.dialog({ content: '审批意见不能为空!', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
            }
            d.close();
            submitAuditInfo(status, $("#auditOpinion").val());
        },
        cancelValue: '取消',
        cancel: function() {}
    });
    if (currentWorkStatus && status == 2) {
        var rowData = $('#X_Table').getRowData();
        let firstHighWoolAreaStr = '';
        let firstHighWoolAreaList = [];
        rowData.forEach(item => {
            if (item.productFirstGm == '1') {
                firstHighWoolAreaList.push(item.productCode)
            }
        });
        if (firstHighWoolAreaList && firstHighWoolAreaList.length > 0) {
            firstHighWoolAreaStr = firstHighWoolAreaList.join();
            // var q = utils.dialog({
            //     title: "提示",
            //     content: `<div style="word-break: break-all">下架单包含
            //                 <span style="color:red">${firstHighWoolAreaList.length}</span>个首推品种
            //                 <span style="color:red">${firstHighWoolAreaStr}</span>，确认提交吗？
            //               </div>`,
            //     width: 340,
            //     okValue: '确定',
            //     cancelValue: '取消',
            //     cancel: function () {},
            //     ok: function () {
            //         q.close().remove();
            //         d.showModal();
            //     }
            // })
            // q.showModal();
            utils.dialog({
                content: `<div style="word-break: break-all">下架单包含
                        <span style="color:red">${firstHighWoolAreaList.length}</span>个首推品种
                        <span style="color:red">${firstHighWoolAreaStr}</span>，首推高毛专区内商品不允许操作下架！
                      </div>`,
                quickClose: true,
                timeout: 2000
            }).showModal();
            return false
        } else {
            d.showModal();
        }
    } else {
        d.showModal();
    }



});
/**
 * 审核按钮操作
 */
function submitAuditInfo(auditStatus, auditOpinion) {
    var formData = $("#applyForm").serializeToJSON();
    var applicationCode = $("#applicationCode").val();
    var rowData = $('#X_Table').getRowData();
    //审核通过，判断明细是否加载完成
    if (auditStatus == 2) {
        if (rowData.length == 0) {
            utils.dialog({ content: '请等待商品明细加载完成后，再进行审核', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
    }

    // 补全remarks
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        //补全reasonRemarks和编码变更
        selectRow.reasonRemarks = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="reasonRemarks"] input').val()
        selectRow.replaceEncoding = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="replaceEncoding"] input').val()
        const $remarksSelect = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="remarks"] select')
        selectRow.remarks = $remarksSelect.val()
    }

    var productLowerShelfApprovalRecordVo = $.extend(formData, { "auditStatus": auditStatus }, { "auditOpinion": auditOpinion }, { "applicationCode": applicationCode });
    productLowerShelfApprovalRecordVo.recordDetailVos = rowData;
    if (pageType == '4') { //驳回重新提交
        productLowerShelfApprovalRecordVo.updataStutus = "2";
    } else {
        if (auditEdit == '1') { //品类专员节点
            productLowerShelfApprovalRecordVo.updataStutus = "1";
        } else {
            productLowerShelfApprovalRecordVo.updataStutus = "0";
        }
    }
    productLowerShelfApprovalRecordVo.detailList = rowData;
    var data = JSON.stringify(productLowerShelfApprovalRecordVo);
    console.log(data);
    $.ajax({
        type: "post",
        url: "/proxy-product/product/lowerShelf/auditLowerShelfInfo",
        async: false,
        data: data,
        dataType: "json",
        contentType: "application/json",
        success: function(data) {
            var msg = data.result;
            if (data.result == "sucess") {
                if (pageType == '4') { //驳回重新提交
                    if (auditStatus == 3) {
                        msg = "流程已关闭！";
                    } else {
                        msg = "提交成功！";
                    }
                } else if (auditStatus == 2) {
                    msg = "恭喜审核通过！";
                } else if (auditStatus == 4) {
                    msg = "驳回成功！";
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function() {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            } else {
                utils.dialog({ content: data.result, quickClose: true, timeout: 3000 }).showModal();
                return false;
            }
        },
        error: function() {
            utils.dialog({ content: '审核失败', quickClose: true, timeout: 2000 }).showModal();
        },
        complete: function() {
            parent.hideLoading();
        }
    });
}
//获取审核流程数据
$.ajax({
    type: "POST",
    url: "/proxy-product/product/purchaseLimit/queryTotle?key=" + $("#workProcessKey").val() + "&processInstaId=" + $("#taskId").val(),
    async: true,
    success: function(data) {
        if (data.code == 0 && data.result != null) {
            console.log(data.result);
            $('.flow').html("")
            $('.flow').process(data.result);
            if (pageType !== '0') {
                for (key in data.result) {
                    let currentObj = data.result[key];
                    if (currentObj && currentObj.Taskstatus == 2 && (key == 'node1' || key == 'node2')) {
                        currentWorkStatus = true;
                        addBackGroundColor();
                    }
                }
            }
        }
    },
    error: function() {}
});

/**
 * 设置输入两位小数 并且控制可输入的最大值的位数
 *
 * obj this对象
 * size 最大值位数 可不传
 * */
function setFixedTwo(obj, size) {
    var value = obj.value;
    var n = '';
    if (size) {
        for (var i = 0; i < size; i++) {
            n += '9';
        }
        n += '.99';
        if (Number(value) > Number(n)) {
            value = n;
        }
    }
    obj.value = toDecimal2(value);
}

//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x * 100) / 100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}
//数组去重
function unique(arr) {
    var res = [arr[0]];
    for (var i = 1; i < arr.length; i++) {
        var repeat = false;
        for (var j = 0; j < res.length; j++) {
            if (arr[i] === res[j]) {
                repeat = true;
                break;
            }
        }
        if (!repeat) {
            res.push(arr[i]);
        }
    }
    return res;
}
//时间格式化
function dateFormatter(inputTime) {
    if (inputTime == null || inputTime == "")
        return "";
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    var minute = date.getMinutes();
    var second = date.getSeconds();
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d;
    // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
}
// 设置可编辑单元格
function resetDisabled() {
    //申请：0，编辑：1，驳回：4  设置可编辑单元格
    if (pageType == '0' || pageType == '1' || pageType == '4') {
        $(".applyUpperShelf").attr("disabled", false);
    }
    if (auditEdit == '1') { //品类专员节点
        $(".auit3UpperShelf").attr("disabled", false);
    }
}
$("#X_Table").on('click', '.file_btn', function(e) {
    $(e.target).parents('td').find('.file_input').trigger('click');
});
$("#X_Table").on('change', '.file_input', function(e) {
    sendFile(this.files, $(e.target).parents('tr').attr('id'));
});

function sendFile(files, id) {
    var formData = new FormData();
    for (var i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }
    var d = utils.dialog({
        content: '正在上传..'
    }).showModal();
    $.ajax({
        url: '/proxy-sysmanage/upload/upload',
        data: formData,
        type: 'post',
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(res) {
            d.close().remove();
            if (res.code == 0) {
                utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '上传成功'
                }).showModal();
                $("#X_Table").XGrid('setRowData', id, { enclosureUrl: res.result[0] })
            } else {
                utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '上传失败'
                }).showModal();
            }
        },
        error: function() {
            d.close().remove();
        }
    })
}

$("#X_Table").on('click', '.file_a', function(e) {
    var url = $(e.target).attr('data-url');
    var id = $(e.target).parents('tr').attr('id');
    var content = "";
    if (pageType == 0 || pageType == 1 || pageType == 4) { //申请页，编辑页，驳回重新提交页
        content = `<div class="changeApplyItem" style="line-height: 50px;text-align: center;">
                    <a style="flex: 1;" href="${url}" target="_blank" class="download">查看下载</a>
                    <a style="flex: 1;" href="javascript:;" class="remove_file" data-id="${id}">删除</a>
                </div>`;
    } else {
        content = `<div class="changeApplyItem" style="line-height: 50px;text-align: center;">
                    <a style="flex: 1;" href="${url}" target="_blank" class="download">查看下载</a>
                </div>`;
    }
    utils.dialog({
        align: 'top',
        width: 130,
        height: 50,
        padding: 2,
        content: content,
        quickClose: true
    }).show(this);
});
$("body").on('click', '.remove_file', function(e) {
    $("#X_Table").XGrid('setRowData', $(e.target).attr('data-id'), { enclosureUrl: '' });
    $(e.target).parents('.ui-popup').hide();
    $(".ui-popup-backdrop").hide();
});

//下架原因select的change
$("#X_Table").on('change', '.lowerShelfReason', function(e) {

    const $remarksSelector = $(e.target).parents('tr').find('[row-describedby="remarks"] select');


    //清空remarks的值，移除option
    $remarksSelector.val('').find('option:not(:first)').remove();
    //组装原因说明
    const reasonIndex = $(this).val();
    if (reasonIndex == 8) {
        $('#X_Table').setGridParam({
            colModel: [{
                name: 'replaceEncoding',
                index: 'replaceEncoding',
                width: 200,
                hidden: false,
                formatter: (a, b, arr) => {
                    return "<div style='display: flex;justify-content: center;'><input type='text' onclick='selectProduct(this)' class='form-control applyUpperShelf' id='replaceEncoding' name='replaceEncoding' maxlength='200'><div style='background-color: black;color: white;border-radius: 50%;width: 20px ;height: 20px;text-align: center;' onmouseenter='replaceEncodingTip(this)'  onmouseleave='questionTip.close().remove()'   specifications=''    productName=''     manufacturerName=''  >?</div> </div>"
                },

            }]
        })

        var $reasonRemarks = $(e.target).parents('tr').find('[row-describedby="reasonRemarks"] #reasonRemarks')
            //当选择编码时，禁用原因备注框
        $reasonRemarks.attr("disabled", "disabled")
        $reasonRemarks.val("")

    } else {
        $(e.target).parents('tr').find('[row-describedby="reasonRemarks"] #reasonRemarks').removeAttr('disabled');

        $(e.target).parents('tr').find('[row-describedby="replaceEncoding"] #replaceEncoding').val("")

    }
    let optionElement = [];
    if (reasonIndex) {
        const remarksOption = remarksOptionsAdapter(reasonIndex)
        remarksOption.forEach(optionItem => {
            optionElement.push(`<option value="${optionItem.value}">${optionItem.label}</option>`)
        });
    }
    if (optionElement.length) {
        $remarksSelector.append(optionElement.join(''));
    }
});

function onAuditCommodityPositionChanged(_this) {
    // 加载二级定位列表
    $.ajax({
        type: "get",
        url: "/proxy-product/product/dict/common/queryByParentId",
        async: false,
        data: { "parentId": $(_this).val() },
        dataType: "json",
        success: function(data) {
            let options = ['<option value="" selected="selected">请选择</option>']
            if (data.result) {
                options = options.concat(data.result.map(item => {
                    return "<option value=" + item.id + ">" + item.name + "</option>"
                }))
            }
            // 为当前行的 auditSecondCommodityPosition 列设置 <option> 列表
            $(_this).parents('tr').find("[name=auditSecondCommodityPosition]").html(options.join(""))
                // 为当前行的 auditSecondCommodityPosition 列设置隐藏字段中所选中值
            $(_this).parents('tr').find('[name=auditSecondCommodityPosition]').val('')

            // 修改二级定位、签约方式是否必填
            const isSecondPositionRequired = $(_this).find('option:selected').attr('requiredFlag')
            const isContractRequired = $(_this).find('option:selected').attr('modeRequiredFlag')
            setRequired($(_this).parents('tr').find("[name=auditSecondCommodityPosition]"), isSecondPositionRequired)
            setRequired($(_this).parents('tr').find("[name=auditPurchaseContractMode]"), isContractRequired)
            refreshFgColor($(_this).parents('tr').find("[name=auditSecondCommodityPosition]"))
            refreshFgColor($(_this).parents('tr').find("[name=auditPurchaseContractMode]"))
            resetPurchaseContractMode($(_this).parents('tr').find("[name=auditPurchaseContractMode]"), isContractRequired)
            togglePurchaseContractModeOptionsDisabled($(_this).parents('tr').find("[name=auditPurchaseContractMode]"), isContractRequired)
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

function onAuditSecondCommodityPositionChanged(_this) {
    refreshFgColor(_this)
}

function onAuditPurchaseContractModeChanged(_this) {
    refreshFgColor(_this)
}

/**
 * 若所选商品定位一级非 集采签约 ，则将重置集采签约方式字段，将其置为 "请选择"
 * @param _this 集采签约方式 DOM
 * @param isAudit 是否属于审核字段
 */
function resetPurchaseContractMode(_this, isContractRequired) {
    if (!isContractRequired) {
        $(_this).val("")
    }
}
/**
 * 开关集采签约方式下拉列表的 可用状态
 * @param _this 集采签约方式 DOM
 * @param isContractRequired 集采签约方式是否必填
 */
function togglePurchaseContractModeOptionsDisabled(_this, isContractRequired) {
    if (isContractRequired) {
        $(_this).children().removeAttr("disabled")
    } else {
        $(_this).children().attr("disabled", "disabled")
    }
}

/**
 * 设置 DOM 元素的 required 属性
 * @param _this 将要操作的 DOM 元素
 * @param isRequired
 */
function setRequired(_this, isRequired) {
    if (_this && isRequired) {
        _this.attr('required', true)
        _this.css('color', 'red')
    } else {
        _this.removeAttr('required')
        _this.css('color', '')
    }
}

/**
 * 根据 DOM 元素自身的 required 属性，刷新其颜色
 *      若 DOM 元素含有 required 属性且其 val() 为空，将其设置为红色
 *      否则，将其恢复至普通颜色
 * @param _this 将要操作的 DOM 元素
 */
function refreshFgColor(_this) {
    if ($(_this).attr('required') && !$(_this).val()) {
        $(_this).css('color', 'red')
    } else {
        $(_this).css('color', '')
    }
}

/**
 * 校验 Table 中所有当 required 字段
 * @returns {boolean}
 */
function checkTableItemRequiredFields() {
    let result = 0
    $("#X_Table [required='required']").each((index, item) => {
        if (!$(item).val()) {
            // 刷新文字颜色
            refreshFgColor(item)
            let keyName = $(item).attr('name')
                // 若取得的是 audit 开头的 name，则去除 audit 前缀
            if (keyName.indexOf('audit') != -1) {
                keyName = keyName.substr(5)
                keyName = keyName[0].toLowerCase() + keyName.substr(1)
            }
            // 若为商品二级定位字段，则取其在 X_Table 中的 row-describedby
            if (keyName.indexOf('secondCommodityPositionVal') != -1) {
                keyName = 'secondCommodityPosition'
            }
            const columnName = $("#X_Table").parents('.tableBlock').find('.XGridHead [row-describedby=' + keyName + ']').text()
            utils.dialog({
                content: '请选择 ' + columnName,
                title: '提示',
                cancelValue: '取消',
                cancel: true,
                okValue: '确定',
                ok: function() {

                }
            }).showModal();
            // 当存在非法数据时，中止迭代器
            result++
            return false
        }
    })
    return result == 0
}

//?提示框
var questionTip;

function replaceEncodingTip(e) {
    questionTip = utils.dialog({
        content: $(e).prev().val() + e.getAttribute('productname') + "<br>" + e.getAttribute('specifications') + "<br>" + e.getAttribute('manufacturername'),
    })
    questionTip.show(e)

}
//选择替换编码商品
function selectProduct(e) {
    const $remarksSelector = $(e).parents('tr').find('[row-describedby="lowerShelfReason"] select');
    console.log($remarksSelector.val())
    if ($remarksSelector.val() != 8) {
        var d = utils.dialog({
            content: '仅编码变更可选'
        });
        d.show();
        setTimeout(function() {
            d.close().remove();
        }, 2000);

    } else {
        var resultArr = $('#X_Table').getRowData();
        dialog({
            url: '/proxy-product/product/lowerShelf/toProductCodeQuery',
            title: '商品列表',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.8,
            data: {
                "orgCode": $("#recordOrgCode").val(),
                channelId: (resultArr.length != 0) ? resultArr[0].channelId : '',
                channelName: (resultArr.length != 0) ? resultArr[0].channelVal : ''
            }, // 给modal 要传递的 的数据
            onclose: function(data) {
                console.log(this.returnValue)

                if (this.returnValue.resultArr) {
                    var data = this.returnValue.resultArr[0]

                    /* if (this.returnValue.resultArr.length - 1 >= 0) {
                        var data = this.returnValue.resultArr[this.returnValue.resultArr.length - 1];
                        $(e).val(data.productCode)
                        console.log(data)
                    } */

                    $(e).val(data.productCode)
                    let $EncodingTip = $(e).next()
                    $EncodingTip.attr("productname", data.productName)
                    $EncodingTip.attr("specifications", data.specifications)
                    $EncodingTip.attr("manufacturername", data.manufacturerName)
                }

            }
        }).showModal();
    }
}

function enterReasonRemarks(e) {
    showReasonRemarks = utils.dialog({
            content: $(e).val()
        }).show(e)
        //实时判断鼠标在不在备注原因上
    nowIsReasonRemarks = true

}

function leaveReasonRemarks(e) {
    showReasonRemarks.close().remove()
    nowIsReasonRemarks = false
}