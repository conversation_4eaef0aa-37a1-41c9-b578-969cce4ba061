$(function () {




    var detailNames = ['付款单号','付款金额', '付款申请人', '供应商编号','供应商名称','款项类型','支付方式','银行名称','银行帐号','制单人','制单日期','实际付款日期','备注'];
    var detailModel = [
        {
            name: 'billNo'

        }, {
            name: 'paymentAccount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        },{
             name: 'proposer'
        }, {
            name: 'supplierNo'
        }, {
            name: 'supplierName'
        }, {
            name: 'isPrepayStr'
        }, {
            name: 'paymentTypeStr'
        },  {
            name: 'bankName'
        }, {
            name: 'bankAccount'
        }, {
            name: 'createUser'
        }, {
            name: 'createTime',
            formatter:function (value){
                if(value){
                    return moment(value).format('YYYY-MM-DD');
                }else{
                    return ''
                }
            }
        }, {
            name: 'realPaymentTime',
            formatter:function (value){
                if(value){
                    return moment(value).format('YYYY-MM-DD');
                }else{
                    return ''
                }
            }
        },{
            name: 'remark'
        },{
            name:'id',
            hidden: 'true'
        }
    ];
    var allColModelA = JSON.parse(JSON.stringify(detailModel));
    $('#X_Table1').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/payrequestinfo/getPaymentDetail',
        postData: {
            billNo : $("#billNo").val(),
        },
        // url: 'http://localhost:8080/account/find',
        colNames: detailNames,
        colModel: detailModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers:true
    });



})