if ($("#applicationType").val() == 1) { //集采申请
    $("#centralizedPurchaseType").attr('disabled', 'disabled');
} else {
    $("#purchaseContractMode").attr('disabled', 'disabled');
}
// $('#X_Table').XGrid({
//     data: [],
//     colNames: colNames,
//     colModel: colModel,
//     rowNum: 0,
//     rownumbers: true,
//     key:'productIdChannel',
//     altRows: true, //设置为交替行表格,默认为false
//     ondblClickRow: function (id, dom, obj, index, event) {
//         //console.log('双击行事件', id, dom, obj, index, event);
//     },
//     gridComplete: function () {
//         if($('#taskKey').find('option:selected').attr('processtype') === '1'){
//             var rowData = $('#X_Table').XGrid('getRowData');
//             rowData.forEach(function (item) {
//                 $('#X_Table').XGrid('setRowData',item.productIdChannel,{processFlag: 1,applicationReasonsType:item.specialReason});
//             });
//         }
//     }
// });
if (processTypeFlag != 0) {
    //初始化表格数据
    initTable(newcolNames, newcolModel)
} else {
    initTable(colNames, colModel)
}

// 申请流程切换
function processTypeChange(el) {
    // 特殊调价流程单独逻辑
    var rowData = $('#X_Table').XGrid('getRowData');
    //0:正常调价  1：特殊调价
    // var processType = $(el).find('option:selected').attr('processtype');
    var processType = $(el).find('option:selected').attr('data-val');
    // if(rowData.length > 0) {
    //     if(processType == 1){
    //         rowData.forEach(function (item) {
    //             $('#X_Table').XGrid('setRowData',item.productIdChannel,{processFlag: 1,applicationReasonsType:''});
    //         });
    //     }else {
    //         rowData.forEach(function (item) {
    //             $('#X_Table').XGrid('setRowData',item.productIdChannel,{processFlag: '',applicationReasonsType:''});
    //         });
    //     }
    // }
    if (processType != 0) {
        //去除添加的列
        initTable(newcolNames, newcolModel)
        if (rowData.length > 0) {
            rowData.forEach(function(item) {
                $('#X_Table').XGrid('setRowData', item.productIdChannel, { processFlag: '', applicationReasonsType: '' });
            });
        }
    } else {
        initTable(colNames, colModel)
        if (rowData.length > 0) {
            rowData.forEach(function(item) {
                $('#X_Table').XGrid('setRowData', item.productIdChannel, { processFlag: 1, applicationReasonsType: '' });
            });
        }
    }
    processTypeFlag = processType
    console.log($(el).val());
    $("#range").val($(el).find('option:selected').attr('data-range'));
    $("#money").val($(el).find('option:selected').attr('data-money'));
    $('#orgCategory').val($(el).find('option:selected').attr('data-orgCategory'));
}
let oemType = '';
oemType = $("#oemType").val() || '';
//新增行
$("#addRow").on("click", function() {
    if ($("#centralizedPurchaseType").val() == 1 && !$("#purchaseContractMode").val()) {
        utils.dialog({ content: '请先选择集采签约方式！', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    var resultArr = $('#X_Table').getRowData();
    // 点新增行的时候需要传递当前表格中的业务类型的值
    let channelId = resultArr.length > 0 ? resultArr[0].channelId : '',
        channelName = resultArr.length > 0 ? resultArr[0].channelVal : '',
        upperEcAuditStatus = resultArr.length > 0 ? resultArr[0].upperEcAuditStatus : '',
        upperStatues = resultArr.length > 0 ? resultArr[0].upperStatues : '';
    dialog({
        url: '/proxy-product/product/adjustPrice/searchJicaiProduct',
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            "typeBywindow": 1,
            disableState: 0,
            processType: processTypeFlag,
            resultArr: resultArr,
            channelId: channelId,
            channelName: channelName,
            upperEcAuditStatus: upperEcAuditStatus,
            upperStatues: upperStatues,
            centralizedPurchaseType: $("#centralizedPurchaseType option:selected").val(),
            purchaseContractMode: $("#purchaseContractMode option:selected").val(),
            oemType: oemType || 0,
        }, // 给modal 要传递的 的数据
        onclose: function(data) {
            var data = this.returnValue;
            console.log(data)
            if (data) {
                var rows = data.resultArr;
                for (var i = 0; i < rows.length; i++) {
                    var id = rows[i].productId;
                    let node = rows[i];
                    if (!findInArr(id)) {
                        //申请原因类型判断
                        if ($('#taskKey').find('option:selected').attr('processtype') === '1') {
                            node.processFlag = 1;
                        }
                        node['isPromo'] = "";
                        node['applicationZhiluPrice'] = node['zhiluPrice']
                        node['auditZhiluPrice'] = node['zhiluPrice']
                        node['applicationHeyePrice'] = node['heyePrice']
                        node['auditHeyePrice'] = node['heyePrice']
                        $('#X_Table').XGrid('addRowData', node);
                    }
                }
                let rowData = $('#X_Table').XGrid('getRowData');
                if (rowData.length > 0) {
                    disSeleteHander(true)
                } else {
                    disSeleteHander()
                }
            }
        }
    }).showModal();
});
//关闭按钮
$("#closePage").on("click", function() {
    utils.dialog({
        title: "提示",
        content: "是否关闭集采调价申请页？",
        width: 300,
        height: 30,
        okValue: '确定',
        ok: function() {
            utils.closeTab();
        },
        cancelValue: '取消',
        cancel: function() {}
    }).showModal();
});
//提交校验页面必填项
function submitCheck(rowData) {
    if (rowData.length == 0) {
        utils.dialog({ content: '至少添加一种商品', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    if (rowData.length > 200) {
        utils.dialog({ content: '最多添加200条商品', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    var processType = $('#taskKey').find('option:selected').attr('processtype');
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        // if (selectRow.applicationAppPrice == "" || selectRow.applicationAppPrice <= 0) {
        //     utils.dialog({content: '申请APP售价不能为空或0', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }
        if (processTypeFlag != 0) {
            if (selectRow.applicationAppPrice == "" || selectRow.applicationAppPrice <= 0) {
                utils.dialog({ content: '申请APP售价不能为空或0', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
        } else { //特殊调价校验
            let productChannelAreaList = getProductChannelAreaList(selectRow.productChannelAreaList)
            for (let i = 0; i < productChannelAreaList.length; i++) {
                let item = productChannelAreaList[i]
                if (item.areaCode === "" || item.areaCode == null) {
                    utils.dialog({ content: '区域类型不能为空', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                if (!item.applicationAppPrice || item.applicationAppPrice <= 0) {
                    utils.dialog({ content: '申请APP售价不能为空或0', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }

            }
        }
        // if (selectRow.applicationZhiluPrice == "") {
        //     utils.dialog({content: '申请智鹿总部采购价不能为空', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }
        if (selectRow.applicationChainGuidePrice == "" || selectRow.applicationChainGuidePrice < 0) {
            utils.dialog({ content: '申请连锁APP售价不能为空', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        // if (selectRow.applicationHeyePrice == "") {
        //     utils.dialog({content: '申请荷叶大药房采购价不能为空', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }
        if (selectRow.applicationReasonsType == "") {
            utils.dialog({ content: '请填写申请原因', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        //特殊调价流程
        if (processType == "1" && selectRow.detailRemark == "") {
            utils.dialog({ content: '请填写提交备注', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        //特殊调价流程
        if (processType == "1" && selectRow.treatmentPlan == "") {
            utils.dialog({ content: '请填写客诉处理方案', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        var appPrice = parseFloat(selectRow.appPrice);
        var applicationAppPrice = parseFloat(selectRow.applicationAppPrice);
        var rangeResult = (applicationAppPrice - appPrice) / appPrice;
        //正常调价流程：涨价，降价<50%
        if (processType == "2" && (rangeResult >= 0.5 || rangeResult <= -0.5)) {
            utils.dialog({ content: '商品价格调整范围不满足选定的流程条件，请重新录入或删除商品！', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        //正常调价流程：涨价，降价>=50%
        if (processType == "3" && (rangeResult < 0.5 && rangeResult > -0.5)) {
            utils.dialog({ content: '商品价格调整范围不满足选定的流程条件，请重新录入或删除商品！', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
    }
    return true;
}
/**
 * 校验商品负毛利
 * @param rowData
 * @returns {boolean}
 */
function checkSubmitPrice(rowData, _this) {
    //提交校验
    var message = "";
    var message1 = "";
    var zhilumessage = "";
    var chainGuideMessage = "";
    var heyemessage = "";
    var processType = $('#taskKey').find('option:selected').attr('processtype');
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        //申请连锁APP售价>申请APP售价 拦截
        // if(processTypeFlag!=0){//正常调价流程
        //     if (selectRow.inVogue === '0' && (parseFloat(selectRow.applicationChainGuidePrice) > parseFloat(selectRow.applicationAppPrice)) ){
        //         message = message + selectRow.orgCodeValue + ":" + selectRow.productCode + ":" + selectRow.productName + "</br>";
        //     }
        // }else{//特殊调价流程
        //     let productChannelAreaList=getProductChannelAreaList(selectRow.productChannelAreaList)
        //     for(let j = 0; j < productChannelAreaList.length; j++){
        //         let item=productChannelAreaList[j]
        //         if (parseFloat(selectRow.applicationChainGuidePrice) > parseFloat(item.applicationAppPrice)&&item.areaCode==0){
        //             message = message + selectRow.orgCodeValue + ":" + selectRow.productCode + ":" + selectRow.productName + "</br>";
        //             break;
        //         }
        //     }
        // }

        //申请APP售价< 最后含税入库价  负毛利
        if (parseFloat(selectRow.applicationAppPrice) < parseFloat(selectRow.lastPurchasePrice)) {
            message1 = message1 + selectRow.orgCodeValue + ":" + selectRow.productCode + ":" + selectRow.productName + "</br>";
        }
        //申请连锁APP售价< 底价        负毛利
        console.log("商品数据行", selectRow)
        console.log("申请连锁APP售价< 底价 APP售价< 底价",
            parseFloat(selectRow.applicationChainGuidePrice),
            parseFloat(selectRow.applicationAppPrice),
            parseFloat(selectRow.floorPrice))
        if (parseFloat(selectRow.applicationChainGuidePrice) < parseFloat(selectRow.floorPrice) || parseFloat(selectRow.applicationAppPrice) < parseFloat(selectRow.floorPrice)) {
            chainGuideMessage = chainGuideMessage + selectRow.orgCodeValue + ":" + selectRow.productCode + ":" + selectRow.productName + "</br>";
        }
        //智鹿采购价负毛利 拦截
        // if (parseFloat(selectRow.applicationZhiluPrice) < parseFloat(selectRow.lastPurchasePrice)) {
        //     zhilumessage = zhilumessage + selectRow.orgCodeValue + ":" + selectRow.productCode + ":" + selectRow.productName + "</br>";
        // }
        //荷叶大药房采购价负毛利 拦截
        // if (parseFloat(selectRow.applicationHeyePrice) < parseFloat(selectRow.lastPurchasePrice)) {
        //     heyemessage = heyemessage +  selectRow.orgCodeValue + ":" + selectRow.productCode + ":" + selectRow.productName + "</br>";
        // }
    }
    // if (message != '') {
    //     checkPrice("以下商品申请连锁APP售价＞申请APP售价，请重新录入价格或删除商品！", message);
    //     return false;
    // }
    if (processType != "1" && message1 != '') { //特殊调价流程不校验负毛利
        checkPrice("以下商品价格出现负毛利，请重新录入价格或删除商品！", message1);
        return false;
    }
    // if (processType != "1"  && chainGuideMessage != '') {//特殊调价流程不校验负毛利
    //     checkPrice("以下商品申请连锁APP价格出现负毛利，请重新录入价格或删除商品！", chainGuideMessage);
    //     return false;
    // }
    if (chainGuideMessage != '') { // 所有调价流程校验负毛利
        checkPriceSubmite("以下商品申请连锁APP价格出现负毛利，确认要继续操作调价吗？", chainGuideMessage, _this);
        return false;
    }
    // if (processType != "1"  && zhilumessage != '') {//特殊调价流程不校验负毛利
    //     checkPrice("以下商品申请智鹿总部采购价<最后含税单价，只能走特殊调价流程！", zhilumessage);
    //     return false;
    // }
    // if (processType != "1" && heyemessage != '') {//特殊调价流程不校验负毛利
    //     checkPrice("以下商品申请荷叶大药房采购价<最后含税单价，只能走特殊调价流程！", heyemessage);
    //     return false;
    // }
    return true;
}

function submitAdjust(el) {
    let rowData = $('#X_Table').getRowData();
    //默认设置APP售价和连锁APP售价均继续调价
    for (let i = 0; i < rowData.length; i++) {
        rowData[i]['appControl'] = false; //继续调APP售价
        rowData[i]['chainControl'] = false; //继续调连锁APP售价
    }
    $('#activeGoods_table tr').not(':eq(0)').each((index, item) => {
        let el = $(item).find(' input[type=checkbox]');
        //有活动的商品ID
        var theProductId = $(item).find('[row-describedby="productId"]').text();
        for (let i = 0; i < rowData.length; i++) {
            var channelId = rowData[i]['channelId'];
            var productId = rowData[i]['productId'];
            if (channelId == '1' && productId == theProductId) { //药帮忙业务类型，未选中活动商品设置标识1
                //未选中商品编码 渠道为药帮忙  APP售价和连锁APP售价都不调
                var rowId = rowData[i]["productIdChannel"];
                if (!$(el[0]).prop('checked') && !$(el[1]).prop('checked')) {
                    rowData[i]['isPromo'] = "1";
                    //商品有活动不继续调价删除该商品
                    deleteRow(rowId);
                } else {
                    rowData[i]['appControl'] = !$(el[0]).prop('checked');
                    if (!$(el[0]).prop('checked')) { //APP售价不继续调价
                        // rowData[i]['applicationAppPrice'] =  rowData[i]['appPrice'];
                        // rowData[i]['auditAppPrice'] =  rowData[i]['appPrice'];
                        // $('#X_Table').XGrid('setRowData', rowId, {
                        //     applicationAppPrice: rowData[i]['appPrice'],
                        //     auditAppPrice: rowData[i]['appPrice']
                        // });
                        // appPriceGrossMargin(rowId, rowData[i]['appPrice'])
                        var trId = rowData[i]['id'];
                        let apprice = rowData[i]['appPrice']
                            //特殊调价流程
                        if (processTypeFlag == 0) {
                            let productChannelAreaList = []
                            productChannelAreaList = getProductChannelAreaList(rowData[i].productChannelAreaList);
                            productChannelAreaList.map((item, index) => {
                                // item.appPrice = apprice
                                appPriceGrossMargin(index, trId, apprice);
                            })
                            $(`.applyAppPrice_${rowData[i].productId}`).val(apprice)
                                // $('#X_Table').XGrid('setRowData', rowId, {
                                //     applicationAppPrice: apprice,
                                //     auditAppPrice: apprice,
                                //     appPrice: apprice,
                                //     productChannelAreaList: setProductChannelAreaList(productChannelAreaList)
                                // });

                        } else { //正常调价流程
                            rowData[i]['applicationAppPrice'] = apprice,
                                rowData[i]['auditAppPrice'] = apprice
                            $(`.applyAppPrice_${rowData[i].productId}`).val(apprice)
                            $('#X_Table').XGrid('setRowData', rowId, {
                                applicationAppPrice: apprice,
                                auditAppPrice: apprice
                            });
                            appPriceGrossMargin(i, trId, apprice);
                        }
                    }
                    rowData[i]['chainControl'] = !$(el[1]).prop('checked');
                    if (!$(el[1]).prop('checked')) { //连锁APP售价不继续调价
                        rowData[i]['applicationChainGuidePrice'] = rowData[i]['chainGuidePrice'];
                        rowData[i]['auditChainGuidePrice'] = rowData[i]['chainGuidePrice'];
                        $('#X_Table').XGrid('setRowData', rowId, {
                            applicationChainGuidePrice: rowData[i]['chainGuidePrice'],
                            auditChainGuidePrice: rowData[i]['chainGuidePrice']
                        });
                    }
                }
            }
        }
    });

    let finalRowData = rowData.filter(item => item.isPromo == '') //未参加活动商品集合
    console.log(finalRowData);
    //校验负毛利
    // if(!checkSubmitPrice(finalRowData)){
    //     return false;
    // }
    var taskKey = $("#taskKey").val();
    var dataRange = $('#taskKey').find('option:selected').attr('data-range');
    var dataMoney = $('#taskKey').find('option:selected').attr('data-money');
    var orgCategory = $('#taskKey').find('option:selected').attr('data-orgCategory');
    taskKey = taskKey + orgCategory + dataRange + dataMoney;
    //拼接工作流key值,新加字段“备注”,状态,调价类
    var formData = $("#applyForm").serializeToJSON();
    formData = $.extend(formData, { "remark": $("#remark").val() }, { "statues": 1 }, { "applyType": 1 }, { "taskKey": taskKey }, { "range": dataRange }, { "money": dataMoney }, { "centralizedPurchaseType": $("#centralizedPurchaseType").val() }, { "purchaseContractMode": $("#purchaseContractMode").val() });
    var adjustPriceApprovalRecordVo = { "adjustPriceApprovalRecordVo": formData };
    var adjustPriceApprovalRecordDetailVos = { "adjustPriceApprovalRecordDetailVos": finalRowData.length == 0 ? rowData : finalRowData };
    //处理申请原因字段
    adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos = adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos.map(function(item) {
        if (item.processFlag == 1) {
            item.specialReason = item.applicationReasonsType;
            item.applicationReasonsType = null;
        }
        if (item['promoTypeCode'] != '') {
            item['promoType'] = item['promoTypeCode']
        }
        if (processTypeFlag == 0) {
            item.productChannelAreaList = $.parseJSON(item.productChannelAreaList)
            item.appPrice = null
            item.appPriceIncreaseRise = null
            item.grossMarginStr = null
        } else {
            item.productChannelAreaList = []
        }
        delete item.newAppPrice
        delete item.applyAppPrice //删除html展示列 20210708 by linl
        return item;
    });

    //拼装链接两个json对象
    var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
    parent.showLoading();
    $.ajax({
        type: "POST",
        url: oemType == 1 ? "/proxy-product/product/oemPopAdjustPrice/saveOemPopAdjustPrice" : "/proxy-product/product/adjustPrice/saveJicaiAdjustPrice",
        data: places,
        async: false,
        dataType: 'json',
        contentType: "application/json",
        error: function() {
            utils.dialog({ content: '提交失败', quickClose: true, timeout: 2000 }).showModal();
            return false;
        },
        success: function(data) {
            if (data.code == 0) {
                var msg = '提交审核成功';
                if (data.result.code == 1) {
                    msg = data.result.msg;
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function() {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            } else {
                utils.dialog({
                    title: "提示",
                    content: oemType == 1 ? data.result : data.result.message,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function() {

                    }
                }).showModal();
            }
        },
        complete: function() {
            parent.hideLoading();
        }
    });
}