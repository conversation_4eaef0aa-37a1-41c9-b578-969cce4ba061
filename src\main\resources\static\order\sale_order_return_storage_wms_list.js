$(function () {
    /* 获取dialog上层实例  */
    var dialog = parent.dialog.get(window);
    if (dialog) {
        var dialog_data = dialog.data;
        $('#search_vl').val(dialog_data)
    }

    $('#X_Table').XGrid({
        url:'/proxy-order/order/orderReturnStorage/orderReturnStorageController/findWmsSalesOrderReturnStorageList',
        colNames: ['单据编号', '客户编号', '入库日期', '提货类型','机构编号','行号','商品编号','数量','件数','零散数','批号','生产日期','有效期至','验收评定',
                   '最后更新时间','是否拉取', '自动记账'],
        colModel: [{ name: 'djbh',      index: 'djbh',    },
            {name: 'dwbh',      index: 'dwbh',    },
            {      name: 'rq',      index: 'rq',    },
            {      name: 'thlb',    index: 'thlb',   },
            {    name: 'yzid',     index: 'yzid',   },
            {    name: 'djSort',     index: 'djSort',   },
            {    name: 'spid',     index: 'spid',   },
            {    name: 'sl',     index: 'sl',   },
            {    name: 'js',     index: 'js',   },
            {    name: 'lss',     index: 'lss',   },
            {    name: 'ph',     index: 'ph',   },
            {    name: 'rqSc',     index: 'rqSc',   },
            {    name: 'yxqz',     index: 'yxqz',   },
            {    name: 'yspd',     index: 'yspd',   },
            {    name: 'lastmodifytime',     index: 'lastmodifytime',   },
            {    name: 'isZx',     index: 'isZx',   },
            // {    name: 'BMID',     index: 'BMID',   },
            {    name: 'zidongjizhang',     index: 'zidongjizhang',   }
            ],
        rowNum: 0,
       /* rowList:[20,50,100],*/
        key:'productCode',
        rownumbers: true,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            dialog.close(obj);
        },
       /* pager: '#grid-pager',*/
    });


    /* 查询 */
    $('#search_list').on('click', function () {
        $('#X_Table').setGridParam({
            url: '/proxy-order/order/orderReturnStorage/orderReturnStorageController/findWmsSalesOrderReturnStorageList',
            postData: {
                djbh:$('#search_vl').val()
            }
        }).trigger('reloadGrid');
    });
    /* 生成出库单 */
    $('#gen_input_order').on('click', function () {
        $.ajax({
            url: "/proxy-order/order/orderReturnStorage/orderReturnStorageController/generateSalesOrderReturnStorage",
            type: "get",
            dataType: "json",
            success: function () {
                dialog.close();
            }
        });
    });
})