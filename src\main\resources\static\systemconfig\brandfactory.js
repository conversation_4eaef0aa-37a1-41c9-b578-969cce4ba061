$(function () {

   $('#Brand_Table').XGrid({
        url:'/proxy-sysmanage/sysmanage/dict/querybrandfactory',
        colNames: ['品牌厂家id', '品牌厂家名称', '助记码', '是否停用', '关键词',  '创建人', '创建日期','操作'],
        colModel: [
            {
                name: 'brandfactoryId',
                index: 'brandfactoryId',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'brandfactoryName',
                index: 'brandfactoryName',
                width: 200,//宽度
                editable: true,//是否可编辑
                edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

            }, {
                name: 'brandfactoryNumber',
                index: 'brandfactoryNumber',
                width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'brandfactoryIsstop',
                index: 'brandfactoryIsstop',
                width: 150,
                editable: true,
                formatter:isShop,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'brandfactoryKeyword',
                index: 'brandfactoryKeyword',
                width: 250,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'createUser',
                index: 'createUser',
                width: 250,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'createTime',
                index: 'createTime',
                width: 250,
                sortable: false,
                editable: true,
                formatter:datetimeFormatter,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                sortable: false,
                editable: true,
                formatter:brandOperation
            }
        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        key:'brandfactoryId',
        ondblClickRow: function (id, dom, obj, index, event) {
            //双击事件回调函数
            var el = document.querySelector('#dialog_Block');//html元素
            $('#brandform')[0].reset();
            var selRow = obj;
            if(selRow){
                $("[name='brandfactoryId']").val(selRow.brandfactoryId)
                $("[name='brandfactoryName']").val(selRow.brandfactoryName)
                $("[name='brandfactoryKeyword']").val(selRow.brandfactoryKeyword)
                $("[name='brandfactoryNumber']").val(selRow.brandfactoryNumber)
                var val=$('input[name="brandfactoryKeyword"]').val();
                $('input[data-role="tagsinput"]').tagsinput('removeAll');
                $('input[data-role="tagsinput"]').tagsinput('add',val);
                if(selRow.brandfactoryIsstop=="是"){
                    $(":radio[name='brandfactoryIsstop'][value='1']").prop("checked", "checked");
                }else{
                    $(":radio[name='brandfactoryIsstop'][value='0']").prop("checked", "checked");
                }
                $("[name='brandfactoryName']").attr("disabled",true)
                $(".tagsinput input[type='text']").prop("disabled",true);
                $(":radio[name='brandfactoryIsstop']").attr("disabled",true)

                utils.dialog({
                    title: '查看详情',
                    content: el,
                }).showModal();
                $(".tag").dblclick(function (ev) {
                    return false;
                })
            }else{
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
        }
    });


    //新增一行
    $('#addBrand').on('click', function () {
        var el = document.querySelector('#dialog_Block');//html元素
        $("[name='brandfactoryName']").attr("disabled",false)
        $(".tagsinput input[type='text']").prop("disabled",false);
        $(":radio[name='brandfactoryIsstop']").attr("disabled",false)
        $('#brandform')[0].reset();
        $("[name='brandfactoryId']").val(null)
        $('input[data-role="tagsinput"]').each(function () {
            $(this).tagsinput('removeAll');
        })
        utils.dialog({
            title: '新增',
            content: el,
            width:'630px',
            okValue: '确定',
            ok: function () {
                $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                data= decodeURIComponent($("#brandform").serialize(),true);
                if (validform("brandform").form()) {//验证通过 "myform"为需要验证的form的ID
                    $.ajax({
                    url: "addoreditbrandfactory",
                    type: "post",
                    data: data,
                    success: function (result) {
                        utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                        setTimeout("location.reload();",1000);
                    }
                })
                } else {//验证不通过
                    utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
            },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal();

    })


    //删除选中行
    $('#delBrand').on('click', function () {

        var selRow = $('#Brand_Table').XGrid('getSeleRow');
        if (selRow) {
            $.ajax({
                url:"delatebrandfactory",
                data:{id:selRow.brandfactoryId},
                type:"POST",
                success:function(result){
                    utils.dialog({content:  result.result, quickClose: true, timeout: 2000}).showModal();
                    setTimeout("location.reload();",1000);
                }
            })
        } else {
            utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
        }


    })

    $("#brandBut").click(function () {
        $('#Brand_Table').setGridParam({
            url:"/proxy-sysmanage/sysmanage/dict/querybrandfactory",
            postData:{
                "brandfactoryName":$('#BrandfactoryName').val().replace(/\s+/g,""),
                "isStop":$('#isStop').val()
            }
        }).trigger('reloadGrid');

    });

    function isShop(val){
        if(val==0){
            return "否"
        }else{
            return  "是"
        }
    }

    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })

    function datetimeFormatter(val) {
        if (val != null && val !="") {
            return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
        } else {
            return "";
        }
    };


})
    function  brandOperation(){
        return  "<a href='javascript:;' onclick='editBrand(this)'>编辑</a>"
    }

    function  editBrand(obj){

        $("[name='brandfactoryName']").attr("disabled",false)
        $(".tagsinput input[type='text']").prop("disabled",false);
        $(":radio[name='brandfactoryIsstop']").attr("disabled",false)
        var el = document.querySelector('#dialog_Block');//html元素
        $('#brandform')[0].reset();
        var id=$(obj).parents('tr').attr('id');
        var selRow = $('#Brand_Table').XGrid('getRowData',id);
        if (selRow) {
            $("[name='brandfactoryId']").val(selRow.brandfactoryId)
            $("[name='brandfactoryName']").val(selRow.brandfactoryName)
            $("[name='brandfactoryKeyword']").val(selRow.brandfactoryKeyword)
            $("[name='brandfactoryNumber']").val(selRow.brandfactoryNumber)
            var val=$('input[name="brandfactoryKeyword"]').val();
            $('input[data-role="tagsinput"]').tagsinput('removeAll');
            $('input[data-role="tagsinput"]').tagsinput('add',val);
            if(selRow.brandfactoryIsstop=="是"){
                $(":radio[name='brandfactoryIsstop'][value='1']").prop("checked", "checked");
            }else{
                $(":radio[name='brandfactoryIsstop'][value='0']").prop("checked", "checked");
            }
            utils.dialog({
                title: '编辑',
                content: el,
                width:'630px',
                okValue: '确定',
                ok: function () {
                    $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                    data = decodeURIComponent($("#brandform").serialize(), true);
                    if (validform("brandform").form()) {//验证通过 "myform"为需要验证的form的ID
                        $.ajax({
                            url: "addoreditbrandfactory",
                            type: "post",
                            data: data,
                            success: function (result) {
                                utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                                setTimeout("location.reload();",1000);
                            }
                        })
                    } else {//验证不通过
                        utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                },
                cancelValue: '取消',
                cancel: function () {

                }
            }).showModal();
        }else {
            utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
        }


    }
