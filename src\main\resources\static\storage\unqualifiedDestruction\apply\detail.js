/**
 * Created by 沙漠里的红裤头 on 2019/6/28
 */
/* 审批流 */
/**
 * 流程图显示
 */
    //根据流程实例ID加载流程图
var processInstaId=$("#auditId").val();
var key=$("#processId").val();
var path = '/proxy-storage/storage/unqualifiedDestructionApply/';
initApprovalFlowChart(key,processInstaId);
function  initApprovalFlowChart(key,processInstaId) {
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: "/proxy-storage/storage/lossApply/lossApplyController/queryTotle?key="+key+"&processInstaId="+processInstaId,
        async: false,
        success: function (data) {
            console.log(data);
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}

$(function () {
    var jsonStore = eval('');
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    var colNames = ['销毁原因','销毁地点','销毁方式','报损执行单号','商品编码', '原商品编码','商品大类','商品名称',
                        '商品规格', '商品产地', '生产厂家', '单位',  '库房名称', '业务类型', '批号', '生产日期', '有效期至','销毁数量'
                    ],
        colModel =   [
            {name: 'destructionReason',index: 'destructionReason' },
            {name: 'destructionPlace',index: 'destructionPlace'},
            {name: 'destructionMethod',index: 'destructionMethod'},
            {name: 'storageLossExecutionCode',index: 'storageLossExecutionCode'},
            {name: 'productCode',index: 'productCode' },
            {name: 'oldProductCode',index: 'oldProductCode'},
            {name: 'largeCategory',index: 'largeCategory'},
            {name: 'productName',index: 'productName'},
            {name: 'specifications',index: 'specifications'},
            {name: 'producingArea',index: 'producingArea',},
            {name: 'manufacturerName',index: 'manufacturerName',},
            {name: 'productUnit',index: 'productUnit'},
            {name: 'storeHouseName',index: 'storeHouseName',
                formatter: function (e) {
                    return '不合格库'
                }
            },
            {name: 'channelId', index: 'channelId'},
            {name: 'batch', index: 'batch'},
            {name: 'produceDate',index: 'produceDate' ,
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            },
            {name: 'validDate',index: 'validDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            },
            {name: 'lossNum',index: 'lossNum'}
        ];

    $('#X_Table').XGrid({
        url: path + 'findDetilList?unqualifiedDestructionApplyCode='+$("#unqualifiedDestructionApplyCode").val(),
        colNames: colNames,
        colModel:colModel,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        gridComplete: function () {
            /* 合计写入 */
            var data = $(this).XGrid('getRowData');
            console.log(data);
            data.forEach(function (item, index) {
                if (item.abnormalCause) {
                    $('#X_Table #' + item['id']).addClass('warnning')
                }
            });
        },
        pager: '#grid-pager',
    });
})


// 审核通过
function btn_show_pass() {
    utils.dialog({
        title: '审核通过',
        content: $('#check_pass_msg'),
        okValue: '确定',
        ok: function () {
            // var form_data = $('#form_a').serializeToJSON();
            // var table_data = $('#table_a').XGrid('getRowData');
            // console.log(form_data, table_data);
            if(!$('#remark').val()){
                utils.dialog({content: '请填写通过原因', quickClose: true, timeout: 2000}).show();
                return false;
            }
            $.ajax({
                type: "POST",
                url: path + "apply?&taskId="+$("#taskId").val()+"&comment="+$("#remark").val()+"&unqualifiedDestructionApplyCode="+$("#unqualifiedDestructionApplyCode").val(),
                async: false,
                success: function (data) {
                    console.log("--------审核编码------>"+data.code);
                    if(data.code == 1000000){
                        utils.dialog({
                            title: '温馨提示',
                            content: '审核成功！',
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            },
                        }).show()
                    }else{
                        utils.dialog({
                            title: '温馨提示',
                            content: '审核失败！',
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).show()
                    }
                },
                error: function () {}
            });
        },
        cancelValue: "取消" ,
        cancel: function () {}
    }).showModal()
}

// 审核驳回
function btn_show_unpass() {
    utils.dialog({
        title: '审核驳回',
        content: $('#check_pass_msg'),
        okValue: '确定',
        ok: function () {
            // var form_data = $('#form_a').serializeToJSON();
            // var table_data = $('#table_a').XGrid('getRowData');
            // console.log(form_data, table_data);
            if(!$('#remark').val()){
                utils.dialog({content: '请填写驳回原因', quickClose: true, timeout: 2000}).show();
                return false;
            }
            $.ajax({
                type: "POST",
                url: path + "unapply?&taskId="+$("#taskId").val()+"&comment="+$("#remark").val()+"&unqualifiedDestructionApplyCode="+$("#unqualifiedDestructionApplyCode").val(),
                async: false,
                success: function (data) {
                    console.log("--------审核编码------>"+data.code);
                    if(data.code == 1000000){
                        utils.dialog({
                            title: '温馨提示',
                            content: '驳回成功！',
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            },
                        }).show()
                    }else{
                        utils.dialog({
                            title: '温馨提示',
                            content: '驳回失败！',
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).show()
                    }
                },
                error: function () {}
            });
        },
        cancelValue: "取消" ,
        cancel: function () {}
    }).showModal()
}