$(function () {
    // 生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord"},"manufacturer",{data:"manufactoryId",value:"manufactoryName"});
    $('#X_Tableb').XGrid({
        colNames: ['ID','修改时间', '机构', '申请人', '单据编号', '商品编码', '商品名称', '通用名','商品大类', '生产厂家', '型号/规格', '采购员', '属性', '历史值','当前值'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true
            }, {
                name: 'auditTime',
                index: 'auditTime',
                formatter:function(value){
                    return new Date(value).Format('yyyy-MM-dd hh:mm:ss');
                },
                width: 200
            }, {
                name: 'orgCode',
                index: 'orgCode',
                width: 210
            }, {
                name: 'applicantValue',
                index: 'applicantValue',
                width: 100
            }, {
                name: 'applicationCode',
                index: 'applicationCode',
                width: 160
            }, {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'productName',
                index: 'productName',
                width: 180
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 180
            },  {
                name: 'largeCategoryVal',
                index: 'largeCategoryVal',
                width: 80
            }, {
                name: 'manufacturerName',
                index: 'manufacturerName',
                width: 220
            }, {
                name: 'specifications',
                index: 'specifications',
                width: 200
            }, {
                name: 'buyerValue',
                index: 'buyerValue',
                width: 100
            }, {
                name: 'attributeName',
                index: 'attributeName',
                width: 160
            },{
                name: 'changeBefore',
                index: 'changeBefore',
                width: 100
            },{
                name: 'changeAfter',
                index: 'changeAfter',
                width: 100
            }

        ],
        rownumbers: true//是否展示序号
    });

    $("#SearchBtn").on("click", function () {
        if (!($("#auditTimeStart").val() && $("#auditTimeEnd").val())){
            utils.dialog({content:"请输入起止时间后再进行查询", quickClose: true, timeout: 2000}).showModal();
            return
        }
        let postData = $("#searchForm").serializeToJSON();
        if (postData['attribute']) {
            if (Array.isArray(postData['attribute'])) {
                postData['attribute']= postData['attribute'].join(',')
            }
        }
        $('#X_Tableb').XGrid('setGridParam', {
            url:"/proxy-product/product/orgApproval/getRecordsDetailPageInfo?applyType=4",
            postData: postData,
            page:1,
            rowNum: 20,
            rowList: [20,50,100], //分页条数下拉选择
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true,
            pager: '#grid-pager'
        }).trigger('reloadGrid');
    });
    $("#exportBtn").on("click", function () {
        if (!($("#auditTimeStart").val() && $("#auditTimeEnd").val())){
            utils.dialog({content:"请输入起止时间后再进行导出", quickClose: true, timeout: 2000}).showModal();
            return
        }
        $("#searchForm").attr("action","/proxy-product/product/orgApproval/exportExcel");
        $("#searchForm").submit();
    });
    $('#switchTabBtn').on('click',function () {
        switchTab(0)
    })
    $('#advanceSearchBtn').on('click',function(){
        const isAdvanceSearchGroupHide  = $('.advanceSearchGroup').css('display') === 'none'
        if (isAdvanceSearchGroupHide){
            $('.advanceSearchGroup').css('display','')
            $('.content>div:first-child').css('height','260px')
            $('#advanceSearchBtn span').removeClass('glyphicon-menu-down').addClass('glyphicon-menu-up')
        }else {
            $('.advanceSearchGroup').css('display','none')
            $('.content>div:first-child').css('height','190px')
            $('#advanceSearchBtn span').removeClass('glyphicon-menu-up').addClass('glyphicon-menu-down')
        }
    })
})

var orgCode=$("#loginOrgCode").val();
if(orgCode=='001'){
    $.ajax({
        url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode="+orgCode,
        async : false,
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].orgCode+'">'+arr[i].orgName+'</option>';
                    }
                }
            }
            $("#orgCode").html(html);
        },
        error:function () {
        }
    });
}

/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
        },
        onNoneSelect: function (params, suggestions) {
            //console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    });
}
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

/**
 *
 * @param flag 是否切换到变更详情列表 ： 0 为否， 1为是
 */
function switchTab(flag){
    let result = ''
    if (flag==1){
        result = '/proxy-product/product/orgApproval/approvalDetail'
    } else {
        result = '/proxy-product/product/orgApproval/productManageList'
    }
    window.location.href=result
}

