$(function () {
    var totalTable = z_utils.totalTable;

    var d;
    var colNames = ['ERP发票编码', '供应商发票号', '创建日期', '供应商编号', '供应商名称', '发票不含税金额合计',
        '发票税额合计', '发票价税合计', '汇总不含税金额差额', '含税金额差异',  '发票状态', '创建人', '凭证摘要','是否初始化'], colModel = [
        {
            name: 'invoiceNo'
        },
        {
            name: 'supplierInvoiceNumber',
            formatter: function (val) {
                return '<span class="hover-title" title="' + val + '">' + val + '</span>';
            }
        },
        {
            name: 'invoiceCreateDate',
            formatter: function (value) {
                return moment(value).format("YYYY-MM-DD ");
            }
        },
        {
            name: 'supplierNumber'
        },
        {
            name: 'supplierName'
        },
        {
            name: 'noTotalTaxAmount',
            formatter: function (value) {
                return value.formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        },
        {
            name: 'totalInvoiceValue',
            formatter: function (value) {
                return value.formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        },
        {
            name: 'totalInvoiceTax',
            formatter: function (value) {
                return value.formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        },
        {
            name: 'valueDifference',
            formatter: function (value) {
                return value.formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        },
        {
            name: 'taxAmountDiff',
            formatter: function (value) {
                return value.formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        },
        {
            name: 'invoiceStatus',
            formatter: paytype
        },
        {
            name: 'createUser'
        },
        {
            name: 'certificateRate',
            //title: true,
//				 colMouseenter : function(eType) {
//				 	var content = eType.rowData.certificateRate;
//				 	if (!content)
//				 		return false;
//				 	d = dialog({
//				 		align : 'top',
//				 		padding : 10,
//				 		content : '<textarea style="max-width: 220px;height:100%;resize: none;border: none;outline:none;text-align: center;" rows="1" readonly>'
//				 				+ content + '</textarea>',
//				 		onshow : function() {
//				 			var height = $(this.node).find('textarea')[0].scrollHeight;
//				 			this.height(height);
//				 		}
//				 	})
//				 	d.show(eType.e.target);
//				 },
//				 colMouseout : function(eType) {
//				 	d.close().remove();
//				 },
            formatter: function (val) {
                // val = val.replace(/\"/g, '“').replace(/\'/, '’');
                return transEntity(val, true);
            }
        },
        {
            name: 'isInit',
            hidden: true,
            hidegrid:true,
        }];

    var $X_Table = $('#X_Table');

    var transferiemCheckbox = $("#transferiemCheckbox").prop('checked');
    console.log("transferiemCheckbox="+ transferiemCheckbox);
    var starttime = null;
    var endtime = null;
    var invoiceStatus = null;
    var transferitemStarttime = null;
    var transferitemsEndtime = null;
    if(transferiemCheckbox) {
        transferitemStarttime = $("#transferitemStarttime").val();
        transferitemsEndtime = $("#transferitemsEndtime").val();
    } else {
        starttime = $("#starttime").val();
        endtime = $("#endtime").val();
        invoiceStatus = $("#invoiceStatus").val();
    }
    // 设置table高度
    utils.setTableHeight('X_Table');
    $X_Table.XGrid({
        // data: grid_data,
        url: '/proxy-finance/finance/purchase/invoice/purchaseInvoice',
        postData: {
            "keyWord": $("#keyWord").val(),
            "invoiceNo": $("#invoiceNo").val(),
            "starttime": starttime,
            "endtime": endtime,
            "invoiceStatus": invoiceStatus,
            "supplierInvoiceNumber": $("#supplierInvoiceNumber").val(),
            "invoiceType": $("#invoiceType").val(),
            "certificateRate": $("#certificateRate").val(),
            "invoiceNos" :$("#invoiceNos").val(),
            "transferitemStarttime" : transferitemStarttime,
            "transferitemsEndtime" : transferitemsEndtime
        },
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20, 50, 100],
        rownumbers: true,
        attachRow:true,
        altRows: true, // 设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
            var invoiceNo = a.invoiceNo;
            var isInit = a.isInit;
            if(1==isInit){
                showDetail(invoiceNo,isInit);
            }else{
                if (a.invoiceStatus != '保存') {
                    utils.openTabs('nonSaveStatus','采购发票详情','/proxy-finance/finance/purchase/invoice/toInvoiceInfo?invoiceNo='+ invoiceNo);
                    // window.location.href = "/proxy-finance/finance/purchase/invoice/toInvoiceInfo?invoiceNo="
                    //     + invoiceNo;
                }else{
                    var flag = 2;
                    utils.openTabs('saveStatus','采购发票编辑','/proxy-finance/finance/purchase/invoice/editInvoiceInfo?invoiceNo='+ invoiceNo + '&flag=' + flag);
                    // window.location.href = "/proxy-finance/finance/purchase/invoice/editInvoiceInfo?invoiceNo="
                    //     + invoiceNo + "&flag=" + flag;
                }
            }

        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            var _this = $(this);
            setTimeout(function () {
                if (_this.XGrid('getRowData').length === 0) {
                    utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
                }
            }, 200);
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['noTotalTaxAmount','totalInvoiceValue','totalInvoiceTax','valueDifference', 'taxAmountDiff'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        },
        pager: '#grid-pager'
    });

    //统计
    totalSum();

    // 前台时间转化的
    function datetimeFormatter(val) {
        if (val != null && val != "") {
            return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
        } else {
            return "";
        }
    }

    // 查询数据
    $('#searchBtn').bind('click', function () {
        var transferiemCheckbox = $("#transferiemCheckbox").prop('checked');
        console.log("transferiemCheckbox="+ transferiemCheckbox);
        var starttime = null;
        var endtime = null;
        var invoiceStatus = null;
        var transferitemStarttime = null;
        var transferitemsEndtime = null;
        if(transferiemCheckbox) {
            transferitemStarttime = $("#transferitemStarttime").val();
            transferitemsEndtime = $("#transferitemsEndtime").val();
        } else {
            starttime = $("#starttime").val();
            endtime = $("#endtime").val();
            invoiceStatus = $("#invoiceStatus").val();
        }
        $("#invoiceNos").val("");
        var param = $('#myform').serializeToJSON();
        console.log(param);
        $X_Table.setGridParam({
            url: '/proxy-finance/finance/purchase/invoice/purchaseInvoice',
            postData: {
                "keyWord": $("#keyWord").val(),
                "invoiceNo": $("#invoiceNo").val(),
                "starttime": starttime,
                "endtime": endtime,
                "invoiceStatus": invoiceStatus,
                "supplierInvoiceNumber": $("#supplierInvoiceNumber").val(),
                "invoiceType": $("#invoiceType").val(),
                "certificateRate": $("#certificateRate").val(),
                "transferitemStarttime" : transferitemStarttime,
                "transferitemsEndtime" : transferitemsEndtime
            }
        }).trigger('reloadGrid');
        //统计
        totalSum();
    });

    var allColModelA = JSON.parse(JSON.stringify(colModel));

    // 导出
    $('#exportBtn').bind('click', function () {

        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            var transferiemCheckbox = $("#transferiemCheckbox").prop('checked');
            console.log("transferiemCheckbox="+ transferiemCheckbox);
            var starttime = null;
            var endtime = null;
            var invoiceStatus = null;
            var transferitemStarttime = null;
            var transferitemsEndtime = null;
            if(transferiemCheckbox) {
                transferitemStarttime = $("#transferitemStarttime").val();
                transferitemsEndtime = $("#transferitemsEndtime").val();
            } else {
                starttime = $("#starttime").val();
                endtime = $("#endtime").val();
                invoiceStatus = $("#invoiceStatus").val();
            }
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            //选择导出列
            var nameModel = "";
            addHtmlA(colNames);
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.55,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            if (allColModel[index].name == "sourceDept") {
                                nameModel += allColModel[index].name + "Str:" + $(this).attr('name') + ","
                            } else if (allColModel[index].name == "docAttr") {
                                nameModel += allColModel[index].name + "Str:" + $(this).attr('name') + ","
                            } else {
                                nameModel += allColModel[index].name + ":" + $(this).attr('name') + ","
                            }
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    parent.showLoading({hideTime: 999999999});
                    var obj = {
                        keyWord: $("#keyWord").val(),
                        invoiceNo: $("#invoiceNo").val(),
                        starttime: starttime,
                        endtime: endtime,
                        invoiceStatus: invoiceStatus,
                        supplierInvoiceNumber: $("#supplierInvoiceNumber").val(),
                        invoiceType: $("#invoiceType").val(),
                        certificateRate: $("#certificateRate").val(),
                        "invoiceNos" :$("#invoiceNos").val(),
                        transferitemStarttime : transferitemStarttime,
                        transferitemsEndtime : transferitemsEndtime
                    }
                    obj["nameModel"] = nameModel;
                    httpPost("/proxy-finance/finance/purchase/invoice/exportExcelReceiptList", obj);
                    parent.hideLoading();
                    // parent.showLoading({hideTime: 999999999});
                    // //上传数据
                    // $.ajax({
                    //     url: "/proxy-finance/finance/purchase/invoice/exportExcelReceiptList",
                    //     data: {
                    //         nameModel: nameModel,
                    //         keyWord: $("#keyWord").val(),
                    //         invoiceNo: $("#invoiceNo").val(),
                    //         starttime: starttime,
                    //         endtime: endtime,
                    //         invoiceStatus: invoiceStatus,
                    //         supplierInvoiceNumber: $("#supplierInvoiceNumber").val(),
                    //         invoiceType: $("#invoiceType").val(),
                    //         certificateRate: $("#certificateRate").val(),
                    //         "invoiceNos" :$("#invoiceNos").val(),
                    //         transferitemStarttime : transferitemStarttime,
                    //         transferitemsEndtime : transferitemsEndtime
                    //     },
                    //     type: "post",
                    //     success: function (result) {
                    //         //上传成功，进行导出
                    //         if (result.code == 0) {
                    //             console.log(result.result);
                    //             var url = result.result.filePath;
                    //             var extfilename = result.result.extfilename;
                    //             var parames = [];
                    //             parames.push({ name: "filePath", value: url});
                    //             parames.push({ name: "extfilename", value: extfilename});
                    //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
                    //             parent.hideLoading();
                    //         } else {
                    //             parent.hideLoading();
                    //             utils.dialog({
                    //                 content: result.msg, quickClose: true,
                    //                 timeout: 3000
                    //             }).showModal();
                    //         }
                    //     }
                    // })
                },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })
    });

    // // 导出,不验证
    // $('#exportBtn').bind('click', function () {
    //                 var nameModel = "";
    //                 addHtmlA(colNames);
    //                 dialog({
    //                     content: $("#setCol"),
    //                     title: '筛选列',
    //                     width: $(window).width() * 0.4,
    //                     data: 'val值',
    //                     cancelValue: '取消',
    //                     cancel: true,
    //                     okValue: '导出',
    //                     ok: function () {
    //                         var newColName = [], newColModel = [];
    //                         $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
    //                             if ($(this).is(":checked")) {
    //                                 nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
    //                             }
    //                         });
    //                         var parames = [];
    //                         parames.push({name: "nameModel", value: nameModel});
    //                         parames.push({name: "keyWord", value: $("#keyWord").val()});
    //                         parames.push({name: "invoiceNo", value: $("#invoiceNo").val()});
    //                         parames.push({name: "starttime", value: $("#starttime").val()});
    //                         parames.push({name: "endtime", value: $("#endtime").val()});
    //                         parames.push({name: "invoiceStatus", value: $("#invoiceStatus").val()});
    //                         parames.push({ name: "supplierInvoiceNumber", value: $("#supplierInvoiceNumber").val()});
    //                         parames.push({ name: "invoiceType", value: $("#invoiceType").val()});
    //                         Post("/proxy-finance/finance/purchase/invoice/exportExcelReceiptList", parames);
    //                     }
    //                 }).showModal();
    // });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none; padding-left: 2.4rem;">'
                + '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {
                if(arry[i] == "隐藏列"){
                    continue;
                }
                s += '<div class="col-md-3">'
                    + '            <div class="checkbox">'
                    + '                <label>'
                    + '                    <input style="margin-right: 10px" checked type="checkbox" name="'
                    + arry[i] + '">' + arry[i] + '                </label>'
                    + '            </div>' + '        </div>';

            }
            s += '</div></div>';
            $("body").append(s);
        }

    }
    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    function Post(URL, PARAMTERS) {
        // 创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        // 如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        // 添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        // 提交数据
        temp_form.submit();
    }

    // 查看
    $('#checkBtn').bind('click', function () {

        var selRow = $('#X_Table').XGrid('getSeleRow');
        if (selRow) {
            var invoiceNo = selRow.invoiceNo;
            var flag = 1;
            window.location.href = "/proxy-finance/finance/purchase/invoice/editInvoiceInfo?invoiceNo="
                + invoiceNo + "&flag=" + flag;
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }

    })

    // 新建
    $('#addBtn').bind('click', function () {
        $.ajax({
            url: "/proxy-finance/finance/purchase/invoice/toValidateOrg",
            type: "post",
            success: function (result) {
                if (result.code == 0) {
                    //window.location.href = '/proxy-finance/finance/purchase/invoice/toAdd';
                    utils.openTabs('invoiceToList','新增采购发票','/proxy-finance/finance/purchase/invoice/toAdd');
                } else {
                    utils.dialog({
                        content: "请到供应链平台操作",
                        quickClose: true,
                        timeout: 3000
                    }).showModal();
                }
            }
        })

    });
    // 新建-导入单据
    $('#importBillBtn').bind('click', function () {
        $.ajax({
            url: "/proxy-finance/finance/purchase/invoice/toValidateOrg",
            type: "post",
            success: function (result) {
                if (result.code == 0) {
                    //window.location.href = '/proxy-finance/finance/purchase/invoice/toAdd';
                    utils.openTabs('importCgdj','新建采购发票-导入采购单据','/proxy-finance/finance/purchase/invoice/toImportStoreinDetail');
                } else {
                    utils.dialog({
                        content: "请到供应链平台操作",
                        quickClose: true,
                        timeout: 3000
                    }).showModal();
                }
            }
        })

    });
    // 修改
    $('#editBtn').bind('click', function () {

        var selRow = $('#X_Table').XGrid('getSeleRow');
        if (selRow) {
            var invoiceStatus = selRow.invoiceStatus;
            if (invoiceStatus != '保存') {
                utils.dialog({
                    content: '非保存状态不能修改！',
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                return false;
            }
            var invoiceNo = selRow.invoiceNo;
            var flag = 2;
            $.ajax({
                url: "/proxy-finance/finance/purchase/invoice/toValidateOrg",
                type: "post",
                success: function (result) {
                    if (result.code == 0) {
                        window.location.href = "/proxy-finance/finance/purchase/invoice/editInvoiceInfo?invoiceNo="
                            + invoiceNo + "&flag=" + flag;
                    } else {
                        utils.dialog({
                            content: "请到供应链平台操作",
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                    }
                }
            })

        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }

    })

    // 作废
    $('#zuofeiBtn').bind('click', function () {

        var selRow = $('#X_Table').XGrid('getSeleRow');
        if (selRow.length>0) {
            utils.dialog({
                title: '提示',
                content: '是否确认作废？',
                width: 200,
                okValue: '是',
                cancelValue: '否',
                ok: function () {
                    var invoiceNo = selRow[0].invoiceNo;
                    var invoiceStatus = selRow[0].invoiceStatus;
                    if (invoiceStatus != "保存") {
                        utils.dialog({
                            content: '此发票不是保存状态，不能作废！',
                            quickClose: true,
                            timeout: 2000
                        }).showModal();
                        return false;
                    }
                    $.ajax({
                        url: "/proxy-finance/finance/purchase/invoice/cancellationInvoice",
                        data: {
                            invoiceNo: invoiceNo,
                            invoiceStatus: 3
                        },
                        type: "post",
                        success: function (result) {
                            if (result.code == 0) {
                                utils.dialog({
                                    content: result.result,
                                    quickClose: true,
                                    timeout: 3000
                                }).showModal();
                                $('#X_Table').XGrid('setGridParam', {
                                    url: '/proxy-finance/finance/purchase/invoice/purchaseInvoice',
                                    postData: {
                                        "keyWord": $("#keyWord").val(),
                                        "invoiceNo": $("#invoiceNo").val(),
                                        "starttime": $("#starttime").val(),
                                        "endtime": $("#endtime").val(),
                                        "invoiceStatus": $("#invoiceStatus").val(),
                                        "supplierInvoiceNumber": $("#supplierInvoiceNumber").val(),
                                        "invoiceType": $("#invoiceType").val(),
                                        "certificateRate": $("#certificateRate").val()
                                    },
                                    page: 1
                                }).trigger("reloadGrid");
                            } else {
                                utils.dialog({
                                    content: result.result,
                                    quickClose: true,
                                    timeout: 3000
                                }).showModal();
                            }
                        }
                    })
                },
                cancel: true
            }).showModal();
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }

    })

    // 刪除
    $('#deleBtn').bind('click', function () {
        var selRow = $X_Table.XGrid('getSeleRow');
        if (selRow) {
            $X_Table.XGrid('delRowData', selRow.id);
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }
    })

    // 筛选列，集成到 xgrid.js 里了
    $("#set_tb_rows").click(function () {
        $X_Table.XGrid('filterTableHead');
    })

    // 发票状态的转化
    function paytype(val) {
        if (val == 1) {
            return "保存"
        } else if (val == 2) {
            return "已过账"
        } else if (val == 3) {
            return "作废"
        }else if(val == 4){
            return "部分核销"
        }else if(val == 5){
            return "已核销"
        }
    }

    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/invoice/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            $("#keyWord").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#keyWord").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    // 按照过账日期查询
    $('#transferiemCheckbox').change(function () {
        $('.transferitemsWrap').css('display',$(this).prop('checked')?'block':'none');
        $('#starttime, #endtime, #invoiceStatus').prop('disabled', $(this).prop('checked')?true:false);
    });

    //放大镜查询
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

//供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#keyWord").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();
    }

    //统计方法
    function totalSum() {
        var transferiemCheckbox = $("#transferiemCheckbox").prop('checked');
        console.log("transferiemCheckbox="+ transferiemCheckbox);
        var starttime = null;
        var endtime = null;
        var invoiceStatus = null;
        var transferitemStarttime = null;
        var transferitemsEndtime = null;
        if(transferiemCheckbox) {
            transferitemStarttime = $("#transferitemStarttime").val();
            transferitemsEndtime = $("#transferitemsEndtime").val();
        } else {
            starttime = $("#starttime").val();
            endtime = $("#endtime").val();
            invoiceStatus = $("#invoiceStatus").val();
        }
        $.ajax({
            url:'/proxy-finance/finance/purchase/invoice/totalSumPurchaseInvoiceByParam',
            type:'post',
            data:{
                "keyWord": $("#keyWord").val(),
                "invoiceNo": $("#invoiceNo").val(),
                "starttime": starttime,
                "endtime": endtime,
                "invoiceStatus": invoiceStatus,
                "supplierInvoiceNumber": $("#supplierInvoiceNumber").val(),
                "invoiceType": $("#invoiceType").val(),
                "certificateRate": $("#certificateRate").val(),
                "invoiceNos" :$("#invoiceNos").val(),
                "transferitemStarttime" : transferitemStarttime,
                "transferitemsEndtime" : transferitemsEndtime
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var static = result.result;
                    $("#totalNoTaxAmount").text(parseFloat(static.totalNoTaxAmount).formatMoney('2', '', ',', '.'));
                    $("#totalTax").text(parseFloat(static.totalTax).formatMoney('2', '', ',', '.'));
                    $("#totalAmount").text(parseFloat(static.totalAmount).formatMoney('2', '', ',', '.'));
                    $("#totalTaxDifference").text(parseFloat(static.totalTaxDifference).formatMoney('2', '', ',', '.'));
                    $("#totalTaxAmountDiff").text(parseFloat(static.totalTaxAmountDiff).formatMoney('2', '', ',', '.'));
                }
            }
        })
    }

    function showDetail(invoiceNo,isInit) {
        var title = "";
        var url = "";
        var height =0;
        title = "采购发票详情";
        url = '/proxy-finance/finance/purchase/payrequestinfo/detailInvoiceInfo?invoiceNo=' + invoiceNo+'&isInit='+isInit;
        height = $(window).height() * 0.8;
        utils.dialog({
            title: title,
            url: url,
            width: $(window).width() * 0.8,
            height: height,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                $('iframe').remove();
            }
        }).show();
    }
})
