// 去掉所有input的autocomplete, 显示指定的除外
//获取各种统计总数
function getStatisticsSums() {
    //加载总数量
    $.ajax({
        url: '/proxy-finance/finance/purchase/AccountPayableReports/getStatisticsSums',
        dataType: 'json',
        timeout: 80000, //6000
        data:{
            "keyWord": $("#keyWord").val(),
            "expirationDate": $("#expirationDate").val()
        },
        success: function (data) { 
            if (data.code==0){
                var numResult = data.result;
                if(null != numResult){
                    $("#totalRealPayAmountSum").text(parseFloat(numResult.totalRealPayAmountSum).formatMoney('2', '', ',', '.'));
                    $("#thirtyBalanceSum").text(parseFloat(numResult.thirtyBalanceSum).formatMoney('2', '', ',', '.'));
                    $("#ninetyBalanceSum").text(parseFloat(numResult.ninetyBalanceSum).formatMoney('2', '', ',', '.'));
                    $("#hundredEightyBalanceSum").text(parseFloat(numResult.hundredEightyBalanceSum).formatMoney('2', '', ',', '.'));
                    $("#threeHundredSixtyBalanceSum").text(parseFloat(numResult.threeHundredSixtyBalanceSum).formatMoney('2', '', ',', '.'));
                    $("#moreBalanceSum").text(parseFloat(numResult.moreBalanceSum).formatMoney('2', '', ',', '.'));
                }else {
                    $("#totalRealPayAmountSum").text("0.00");
                    $("#thirtyBalanceSum").text("0.00");
                    $("#ninetyBalanceSum").text("0.00");
                    $("#hundredEightyBalanceSum").text("0.00");
                    $("#threeHundredSixtyBalanceSum").text("0.00");
                    $("#moreBalanceSum").text("0.00");
                }
            }
        },
        error: function () {
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
$(function () {
    getStatisticsSums();
    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    var colNames = ['截止日期', '供应商编码', '供应商名称 ', '未核销发票金额', '0-30天', '31-90天', '91到180天','181天到360天','360天以上'],
        colModel = [{
            name: 'expirationDate'
        }, {
            name: 'supplierNumber'
        }, {
            name: 'supplierName'
        }, {
            name: 'totalBalance',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'oneToThirtyBalance',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'thirtyToNinetyBalance',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'ninetyToHundredEightyBalance',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'hundredEightyToThreeHundredSixtyBalance',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }
        , {
            name: 'moreThanthreeHundredSixtytotalBalance',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }];

    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/purchase/AccountPayableReports/getPayAbleAgeReportData',
        postData: {
            "keyWord": $("#keyWord").val(),
            "expirationDate": $("#expirationDate").val()
        },
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,//是否展示序号
        altRows: true, //设置为交替行表格,默认为false
        mutltiselect: true,
        attachRow:true,
        ondblClickRow: function (rowid, usedata) {
            var rowData = $("#X_Table").XGrid('getRowData', rowid);
            $.extend(rowData, {
                id: rowid
            });
            dialog.close(rowData);
            dialog.remove();
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
        	var data = $(this).XGrid("getRowData");
        	if(data == null || data.length == 0){
        		utils.dialog({
        			content:"查询无数据", 
        			quickClose: true, 
                    timeout: 3000
                    }).showModal();
        	}
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['totalBalance','oneToThirtyBalance','thirtyToNinetyBalance','ninetyToHundredEightyBalance','hundredEightyToThreeHundredSixtyBalance','moreThanthreeHundredSixtytotalBalance'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        },
        pager: '#grid-pager',
    });

    
    
 // 查询数据
    $('#searchBtn').bind('click', function () {
        if ($('#expirationDate').val() == '') {
            utils.dialog({content: '请选择截止日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        $('#X_Table').setGridParam({
            url: '/proxy-finance/finance/purchase/AccountPayableReports/getPayAbleAgeReportData',
            postData: {
            	"keyWord": $("#keyWord").val(),
                "expirationDate": $("#expirationDate").val()
        }
        }).trigger('reloadGrid');
        getStatisticsSums();
    })

      // 导出
    $('#exportBtn').bind('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            if ($('#expirationDate').val() == '') {
            utils.dialog({content: '请选择截止日期！', quickClose: true, timeout: 2000}).show();
            return false;
            }
        parent.showLoading({hideTime: *********});
        var obj = {
            keyWord : $("#keyWord").val(),
            expirationDate : $("#expirationDate").val()
        };
        httpPost("/proxy-finance/finance/purchase/AccountPayableReports/exportPayAbleAgeReport", obj);
        parent.hideLoading();
            // parent.showLoading({hideTime: *********});
            // //验证导出数据
            // $.ajax({
            //     url : "/proxy-finance/finance/purchase/AccountPayableReports/exportPayAbleAgeReport",
            //     data:{ keyWord : $("#keyWord").val(),
            //         expirationDate : $("#expirationDate").val()
            //     },
            //     type: "post",
            //     success:function(result){
            //         //校验成功
            //         if(result.code==0){
            //             console.log(result.result);
            //             var url = result.result.filePath;
            //             var extfilename = result.result.extfilename;
            //             var parames = [];
            //             parames.push({ name: "filePath", value: url});
            //             parames.push({ name: "extfilename", value: extfilename});
            //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
            //             parent.hideLoading();
            //         }else{
            //             parent.hideLoading();
            //             utils.dialog({content:result.msg, quickClose: true,
            //                 timeout: 3000}).showModal();
            //         }
            //     }
            // })
        })
    });
    // //导出，不验证
    // $('#exportBtn').bind('click', function () {
    //                 var parames = [];
    //                 parames.push({ name: "keyWord", value: $("#keyWord").val()});
    //                 parames.push({ name: "expirationDate", value: $("#expirationDate").val()});
    //                 Post("/proxy-finance/finance/purchase/AccountPayableReports/exportPayAbleAgeReport", parames);
    // });
    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }
    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/invoice/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result);
            $("#keyWord").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {

            $("#keyWord").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#keyWord").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
    }





})