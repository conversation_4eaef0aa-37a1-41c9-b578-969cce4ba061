$('div[fold=head]').fold({
    sub: 'sub'
});
//设置剩余可输入字数
$("#text-count").text(100 - $("#remark").val().length);
//是否集采，签约方式控制
$("#centralizedPurchaseType").on("change", function(event) {
    if (event.target.value != 0) {
        disSeleteHander();
    } else {
        $("#purchaseContractMode").val("");
        $("#purchaseContractMode").attr('disabled', 'disabled');
    }
});
let processTypeFlag = $('#taskKey').find('option:selected').attr('data-val');
var colNames = ['', '机构编码', '机构', '业务类型', '商品唯一值', '采购员ID', '采购员', '商品编码', '标准库Id', '商品名', '通用名', '型号/规格',
    '生产厂家', '包装单位', '剂型', 'TOP排名', 'APP售价', '区域类型', '智鹿总部采购价', '连锁APP售价', '荷叶大药房采购价', '<i class="i-red">*</i>申请APP售价', 'APP售价涨幅/涨额',
    '<i class="i-red">*</i>申请智鹿总部采购价', '<i class="i-red">*</i>申请连锁APP售价', '<i class="i-red">*</i>申请荷叶大药房采购价', '<i class="i-red">*</i>申请原因', '特殊调价原因',
    '提交备注', '客诉处理方案', '附件', '在途价', '最后含税进价', '票面毛利率', '票面毛利率', '前30天销量', '库存数量', '底价', '活动类型', '价格对比',
    '市场竞品价', '是否活动商品', '审核连锁APP售价', '审核APP售价', '审核智鹿总部采购价', '审核荷叶大药房采购价', '机构名称', '特殊调价流程标识', '活动类型code', '爆款标识', "商品ec状态", "商品神农审核状态"
];
var colModel = [{
        name: 'productId',
        index: 'productId',
        hidden: true
    }, {
        name: 'orgCode',
        index: 'orgCode',
        hidden: true
    }, {
        name: 'orgCodeValue',
        index: 'orgCodeValue',
    }, {
        name: 'channelId',
        index: 'channelId'
    }, {
        name: 'productIdChannel',
        index: 'productIdChannel',
        hidegrid: true,
        hidden: true
    }, {
        name: 'buyer',
        index: 'buyer',
        hidegrid: true,
        hidden: true
    }, {
        name: 'buyerVal',
        index: 'buyerVal' //采购员
    }, {
        name: 'productCode',
        index: 'productCode'
    }, {
        name: 'standardId',
        index: 'standardId'
    }, {
        name: 'productName',
        index: 'productName'
    }, {
        name: 'commonName',
        index: 'commonName'
    }, {
        name: 'specifications',
        index: 'specifications'
    }, {
        name: 'manufacturerName',
        index: 'manufacturerName'
    }, {
        name: 'packingUnitValue',
        index: 'packingUnitValue'
    }, {
        name: 'dosageFormValue',
        index: 'dosageFormValue'
    }, { //top排名
        name: 'top',
        index: 'top'
    },
    {
        name: 'newAppPrice',
        index: 'newAppPrice',
        formatter: function(val, rowType, rowData) {
            if (processTypeFlag != 0) {
                return rowData.appPrice
            } else {
                let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                let appPriceHtml = ''
                if (areaList && areaList.length > 0) {
                    for (let i = 0; i < areaList.length; i++) {
                        appPriceHtml += `<div class="tdHtml">${areaList[i].appPrice||''}</div>`
                    }
                }
                return appPriceHtml
            }
        }
    }, {
        name: 'areaCode',
        index: 'areaCode',
        formatter: function(val, rowType, rowData) {
            if (processTypeFlag != 0) {
                return val
            } else {
                let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                let areaHtml = `<div id="Area">`
                if (areaList && areaList.length > 0) {
                    for (let i = 0; i < areaList.length; i++) {
                        if (i == 0) {
                            areaHtml += `<div class="tdHtml"><span class="AreaInfo">默认</span><span class="btntext addNewArea">[新增区域]</span><span class="btntext deleteAllArea">[批量删除]</span></div>`
                        } else {
                            let areaNameStr = ''
                            let counts = 0
                            if (areaList[i].areaName) {
                                let resCounts = areaList[i].areaName.match(/,/g);
                                counts = !resCounts ? 0 : resCounts.length;
                                if (counts > 2 && !areaList[i].viewMoreStatus) {
                                    let areaNameArr = areaList[i].areaName.split(',')
                                    areaNameStr = areaNameArr.slice(0, 3).join(',')
                                    areaNameStr += '......'
                                } else {
                                    areaNameStr = areaList[i].areaName
                                }
                            }
                            areaHtml += `<div class="tdHtml"><span class="AreaInfo">${areaNameStr||''}</span><span class="btntext viewMore" index="${i}">${counts>2?areaList[i].viewMoreStatus?'[收起]':'[查看更多]':''}</span><span class="btntext modifyArea" data="${areaList[i].areaCode}" index="${i}">[修改]</span><span class="btntext deleteArea" index="${i}" >[删除]</span></div>`
                        }
                    }
                }
                areaHtml += `</div>`
                return areaHtml
            }
        },
        unformat: function(e) {
            return ''
        }
    },
    {
        name: 'zhiluPrice',
        index: 'zhiluPrice'
    }, {
        name: 'chainGuidePrice',
        index: 'chainGuidePrice'
    }, {
        name: 'heyePrice',
        index: 'heyePrice',
    }, {
        name: 'applyAppPrice',
        index: 'applyAppPrice',
        // rowtype: '#applicationAppPrice'
        formatter: function(val, rowType, rowData) {
            let applyAppPriceClassName = `applyAppPrice_${rowData.productId}`
            if (processTypeFlag != 0) {
                let applicationAppPrice = rowData.applicationAppPrice || ''
                return `<input type="text" name="applyAppPrice" class="form-control applicationAppPrice TwoDecimalHndle ${applyAppPriceClassName}" value="${applicationAppPrice}" autocomplete="off" />`
            } else {
                let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                let AppPriceHtml = ''
                if (areaList && areaList.length > 0) {
                    for (let i = 0; i < areaList.length; i++) {
                        let applicationAppPrice = areaList[i].applicationAppPrice || ''
                        AppPriceHtml += `<div class="tdinput"><input type="text" name="applyAppPrice" index="${i}" value="${applicationAppPrice}" class="form-control applicationAppPrice TwoDecimalHndle ${applyAppPriceClassName}"  autocomplete="off" /></div>`
                    }
                }
                return AppPriceHtml
            }

        }
    }, {
        name: 'appPriceIncreaseRise',
        index: 'appPriceIncreaseRise',
        formatter: function(val, rowType, rowData) {
            if (processTypeFlag != 0) {
                return val || ''
            } else {
                let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                let appPriceIncreaseRiseHtml = ''
                if (areaList && areaList.length > 0) {
                    for (let i = 0; i < areaList.length; i++) {
                        appPriceIncreaseRiseHtml += `<div class="tdHtml">${areaList[i].appPriceIncreaseRise||''}</div>`
                    }
                }
                return appPriceIncreaseRiseHtml
            }
        }
    }, {
        name: 'applicationZhiluPrice',
        index: 'applicationZhiluPrice',
        rowtype: '#applicationZhiluPrice'
    }, {
        name: 'applicationChainGuidePrice',
        index: 'applicationChainGuidePrice',
        rowtype: '#applicationChainGuidePrice'
    }, {
        name: 'applicationHeyePrice',
        index: 'applicationHeyePrice',
        rowtype: '#applicationHeyePrice',
        width: 200,
    }, {
        name: 'applicationReasonsType',
        index: 'applicationReasonsType',
        formatter: function(val, rowType, rowData) {
            if (rowData.processFlag == 1) {
                return { rowtype: '#specialReason', val: val }
            } else {
                return { rowtype: '#applicationReasonsType', val: val }
            }
        },
        unformat: function(val, rowModel, ele) {
            return $(ele).find('input,select').val();
        }
    }, {
        name: 'specialReason',
        index: 'specialReason',
        hidegrid: true,
        hidden: true
    }, {
        name: 'detailRemark',
        index: 'detailRemark',
        rowtype: '#detailRemark'
    }, {
        name: 'treatmentPlan',
        index: 'treatmentPlan',
        rowtype: '#treatmentPlan'
    }, {
        name: 'enclosureUrl',
        index: 'enclosureUrl',
        formatter: function(val, rowType, rowData) {
            if (val) {
                return `<a href="javascript:;" data-url="${val}" class="file_a">📎</a>`;
            } else {
                return `<button type="button" class="btn btn-info file_btn">上传</button><input style="display: none" class="file_input" type="file" id="${rowData.productIdChannel}_file"/>`;
            }

        },
        unformat: function(val, rowModel, ele) {
            if ($(ele).find('a.file_a').length > 0) {
                return $(ele).find('a.file_a').attr('data-url');
            } else {
                return '';
            }
        }
    }, {
        name: 'inTransitPrice',
        index: 'inTransitPrice'
    }, {
        name: 'lastPurchasePrice',
        index: 'lastPurchasePrice'
    }, {
        name: 'grossMargin',
        index: 'grossMargin',
        hidegrid: true,
        hidden: true
    }, {
        name: 'grossMarginStr',
        index: 'grossMarginStr',
        formatter: function(val, rowType, rowData) {
            if (processTypeFlag != 0) {
                return val || ''
            } else {
                let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                let grossMarginStrHtml = ''
                if (areaList && areaList.length > 0) {
                    for (let i = 0; i < areaList.length; i++) {
                        grossMarginStrHtml += `<div class="tdHtml">${areaList[i].grossMarginStr||''}</div>`
                    }
                }
                return grossMarginStrHtml
            }
        }
    }, {
        name: 'salesVolume',
        index: 'salesVolume'
    }, {
        name: 'inventoryQuantity',
        index: 'inventoryQuantity'
    }, {
        name: 'floorPrice',
        index: 'floorPrice'
    }, {
        name: 'promoType',
        index: 'promoType'
    }, {
        name: 'comparison',
        index: 'comparison',
        width: 100,
        formatter: function(e) {
            return `<button type="button" class="btn btn-info comparison">对比</button>`;
        },
        unformat: function(e) {
            return ''
        }
    }, {
        name: 'price_show',
        index: 'price_show',
        formatter: function(val, rowType, rowData) {
            return `<button type="button" class="btn btn-info price_show">查看</button>`;
        }
    }, {
        name: 'isPromo',
        index: 'isPromo',
        hidegrid: true,
        hidden: true
    }, {
        name: 'auditChainGuidePrice',
        index: 'auditChainGuidePrice',
        hidegrid: true,
        hidden: true
    }, {
        name: 'auditAppPrice',
        index: 'auditAppPrice',
        hidegrid: true,
        hidden: true
    }, {
        name: 'auditZhiluPrice',
        index: 'auditZhiluPrice',
        hidegrid: true,
        hidden: true
    }, {
        name: 'auditHeyePrice',
        index: 'auditHeyePrice',
        hidegrid: true,
        hidden: true
    }, {
        name: 'orgCodeValue',
        index: 'orgCodeValue',
        hidegrid: true,
        hidden: true
    }, {
        name: 'processFlag',
        index: 'processFlag',
        hidegrid: true,
        hidden: true
    }, {
        name: 'promoTypeCode',
        index: 'promoTypeCode',
        hidegrid: true,
        hidden: true
    }, {
        name: 'inVogue',
        index: 'inVogue',
        hidegrid: true,
        hidden: true
    }, {
        name: 'oldProductCode',
        index: 'oldProductCode',
        hidegrid: true,
        hidden: true
    }, {
        name: 'applicationAppPrice',
        index: 'applicationAppPrice',
        hidegrid: true,
        hidden: true
    }, {
        name: 'productChannelAreaList',
        index: 'productChannelAreaList',
        hidegrid: true,
        hidden: true,
        formatter: function(val, rowType, rowData) {
            if (!val || typeof val == 'string') {
                return val || []
            } else {
                return JSON.stringify(val)
            }
        },
    }, {
        name: 'appPrice',
        index: 'appPrice',
        hidegrid: true,
        hidden: true
    }, {
        name: 'upperEcAuditStatus',
        index: 'upperEcAuditStatus',
        hidden: true
    }, {
        name: 'upperStatues',
        index: 'upperStatues',
        hidden: true
    }
];
//去除添加的列
var newcolNames = colNames.filter(item => { return item != '区域类型' })
var newcolModel = colModel.filter(item => { return item.name != 'areaCode' })
    /**
     * 获取JSON
     * @param productChannelAreaList
     * @returns {*[]|any}
     */
function getProductChannelAreaList(productChannelAreaList, rowData) {
    // console.log(rowData,'rowata11111')
    if (rowData && !productChannelAreaList) {
        productChannelAreaList = [];
        productChannelAreaList.push({
            areaCode: '0',
            areaName: '默认',
            appPrice: rowData.appPrice,
            grossMargin: '',
            grossMarginStr: '',
            applyAppPrice: '',
            applicationAppPrice: '',
            appPriceIncreaseRise: '',
            viewMoreStatus: false,
        })
        rowData.productChannelAreaList = JSON.stringify(productChannelAreaList)
            // console.log(rowData,'rowata2222')
        return productChannelAreaList
    }
    if (typeof productChannelAreaList == 'string') {
        return $.parseJSON(productChannelAreaList)
    }
    return productChannelAreaList
}
/**
 * 转换JSON为字符串
 * @param productChannelAreaList
 * @returns {*[]|any}
 */
function setProductChannelAreaList(productChannelAreaList) {
    if (typeof productChannelAreaList == 'string') {
        return productChannelAreaList
    } else {
        return JSON.stringify(productChannelAreaList)
    }
}
/**
 * 初始化表格数据及列
 * @param colName
 * @param colModel
 */
function initTable(paramcolName, paramcolModel) {
    $('#X_Table').remove();
    $('.my-list-table').html('<table id="X_Table"></table>');
    $('#X_Table').XGrid({
        url: "/proxy-product/product/adjustPrice/audi/details",
        postData: {
            "recordId": $("#recordId").val()
        },
        colNames: paramcolName,
        colModel: paramcolModel,
        rowNum: 0,
        rownumbers: true,
        key: 'productIdChannel',
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function(id, dom, obj, index, event) {
            //console.log('双击行事件', id, dom, obj, index, event);
        },
        gridComplete: function() {
            if ($('#taskKey').find('option:selected').attr('data-val') === '0') {
                var rowData = $('#X_Table').XGrid('getRowData');
                rowData.forEach(function(item) {
                    $('#X_Table').XGrid('setRowData', item.productIdChannel, { processFlag: 1, applicationReasonsType: item.specialReason });
                });
            }
        }
    });
    initClick()
}

//列表内查询id
function findInArr(id) {
    var arr = $('#X_Table').getRowData();
    for (var i = 0; i < arr.length; i++) {
        var ID = arr[i].productId;
        if (ID == id) {
            return true;
        }
    }
    return false;
}
//删除行
$("#deleRow").on("click", function() {
    var selectRow = $('#X_Table').XGrid('getSeleRow');
    if (!selectRow) {
        utils.dialog({ content: '请选择删除行！', quickClose: true, timeout: 2000 }).showModal();
    } else {
        utils.dialog({
            title: "提示",
            width: 300,
            height: 30,
            okValue: '确定',
            content: "确定删除此条记录?",
            ok: function() {
                deleteRow(selectRow.id);
                utils.dialog({ content: '删除成功！', quickClose: true, timeout: 2000 }).showModal();
            },
            cancelValue: '取消',
            cancel: function() {}
        }).showModal();
    }
});

function deleteRow(id) {
    $('#X_Table').XGrid('delRowData', id);
    var rowData = $('#X_Table').getRowData();
    if (rowData.length <= 0) {
        disSeleteHander(false)
    } else {
        disSeleteHander(true)
    }
}

function checkPrice(content, message) {
    var reg = new RegExp('</br>', "g")
    var copyText = message.replace(reg, '\n');
    var d = utils.dialog({
        title: "提示",
        content: content + "<br/>" + message +
            "<textarea id=\"copyInput\" class=\"btn btn-info\" style=\"float: right;margin-top: -966px;\"  >" + copyText + "</textarea>",
        okValue: '复制信息',
        ok: function() {
            $("#copyInput").select(); // 选择对象
            var flag = document.execCommand("Copy", "false", null); // 执行浏览器复制命令
            if (flag) utils.dialog({ content: '复制成功！', quickClose: true, timeout: 2000 }).show();
            d.close();
        },
        cancelValue: '返回',
        cancel: function() {}
    }).showModal();
}

function checkPriceSubmite(content, message, _this) {
    var reg = new RegExp('</br>', "g")
    var copyText = message.replace(reg, '\n');
    var d = utils.dialog({
        title: "提示",
        content: content + "<br/>" + message +
            "<textarea id=\"copyInput\" class=\"btn btn-info\" style=\"float: right;margin-top: -966px;\"  >" + copyText + "</textarea>",
        okValue: '确认提交',
        ok: function() {
            submitAdjust(_this)
            d.close()
        },
        cancelValue: '返回',
        cancel: function() {}
    }).showModal();
}

//申请APP售价修改，调整幅度，毛利率 更新
function appPriceGrossMargin(selectindex, trId, applicationAppPrice) {
    var selectRow = $('#X_Table').getRowData(trId);
    let productChannelAreaList = []
    if (processTypeFlag == 0) {
        productChannelAreaList = getProductChannelAreaList(selectRow.productChannelAreaList);
    }
    var appPrice = selectRow.appPrice;
    if (processTypeFlag == 0) {
        appPrice = productChannelAreaList.find((item, index) => { return index == selectindex }).appPrice
    }
    var zhange = Number(applicationAppPrice).toFixed(2) - appPrice;
    var zhangfu = "无穷大";
    if (appPrice != 0) {
        zhangfu = zhange / appPrice;
        zhangfu = (Number(zhangfu) * 100).toFixed(2) + "%";
    }
    zhange = Number(zhange).toFixed(2);
    var rise = zhangfu + "/" + zhange;
    //票面毛利率
    var grossMargin = "100";
    var lastPurchasePrice = selectRow.lastPurchasePrice; //最后含税进价
    if (applicationAppPrice != 0) {
        var lastMmount = applicationAppPrice - lastPurchasePrice;
        var grossMargin = lastMmount / applicationAppPrice;
        grossMargin = (Number(grossMargin) * 100).toFixed(2);
    }
    if (processTypeFlag != 0) {
        $('#X_Table').XGrid('setRowData', selectRow.id, { appPriceIncreaseRise: rise });
        $('#X_Table').XGrid('setRowData', selectRow.id, { applicationAppPrice: applicationAppPrice });
        $('#X_Table').XGrid('setRowData', selectRow.id, { auditAppPrice: applicationAppPrice });
        $('#X_Table').XGrid('setRowData', selectRow.id, { grossMargin: grossMargin, grossMarginStr: grossMargin + "%" });
    } else {
        productChannelAreaList.map((item, index) => {
            if (index == selectindex) {
                item.applicationAppPrice = applicationAppPrice
                item.appPriceIncreaseRise = rise
                item.grossMargin = grossMargin
                item.grossMarginStr = grossMargin + "%"
            }

        })
        $('#X_Table').XGrid('setRowData', selectRow.id, { appPriceIncreaseRise: rise, grossMarginStr: grossMargin + "%", productChannelAreaList: productChannelAreaList });
    }
}

//数组去重
function unique(arr) {
    var res = [arr[0]];
    for (var i = 1; i < arr.length; i++) {
        var repeat = false;
        for (var j = 0; j < res.length; j++) {
            if (arr[i] === res[j]) {
                repeat = true;
                break;
            }
        }
        if (!repeat) {
            res.push(arr[i]);
        }
    }
    return res;
}

function sendFile(files, id) {
    var formData = new FormData();
    for (var i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }
    var d = utils.dialog({
        content: '正在上传..'
    }).showModal();
    $.ajax({
        url: '/proxy-sysmanage/upload/upload',
        data: formData,
        type: 'post',
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(res) {
            d.close().remove();
            if (res.code == 0) {
                utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '上传成功'
                }).showModal();
                $("#X_Table").XGrid('setRowData', id, { enclosureUrl: res.result[0] })
            } else {
                utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '上传失败'
                }).showModal();
            }
        },
        error: function() {
            d.close().remove();
        }
    })
}
//获取所有省份信息
function getAllProvince() {
    let areaList = []
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/scm/salesArea/findProvince",
        cache: false,
        async: false,
        dataType: "json",
        contentType: "application/json",
        timeout: 8000,
        success: function(res) {
            areaList = res.result
        }
    })
    return areaList
}

function initClick() {
    $(".submitAudit").click(function() {
        let rowData = $('#X_Table').getRowData();
        //提交前校验必填项
        if (!submitCheck(rowData)) {
            // console.log(!submitCheck(rowData))
            return false;
        }
        let _this = this;
        //参数处理 拼接工作流key值,新加字段“备注”,状态,调价类型
        let formData = $("#applyForm").serializeToJSON();
        formData = $.extend(formData, { "workProcessKey": $("#workProcessKey").val() }, { "remark": $("#remark").val() }, { "statues": status }, { "applyType": 1 }, { "taskKey": "" }, { "range": "" }, { "money": "" }, { "subsidiariesClassification": $("#subsidiariesClassification").val() }, { "centralizedPurchaseType": $("#centralizedPurchaseType").val() }, { "purchaseContractMode": $("#purchaseContractMode").val() });
        let adjustPriceApprovalRecordVo = { "adjustPriceApprovalRecordVo": formData };
        let adjustPriceApprovalRecordDetailVos = { "adjustPriceApprovalRecordDetailVos": rowData };
        //处理申请原因字段
        adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos = adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos.map(function(item) {
            if (item.processFlag == 1) {
                item.specialReason = item.applicationReasonsType;
                item.applicationReasonsType = null;
            }
            if (item['promoTypeCode'] != '') {
                item['promoType'] = item['promoTypeCode']
            }
            if (processTypeFlag == 0) {
                item.productChannelAreaList = $.parseJSON(item.productChannelAreaList)
                item.appPrice = null
                item.appPriceIncreaseRise = null
                item.grossMarginStr = null
            } else {
                item.productChannelAreaList = []
            }
            delete item.newAppPrice
            delete item.applyAppPrice //删除html展示列 20210708 by linl
            return item;
        });
        //拼装链接两个json对象
        let params = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
        // 发起请求 校验负毛利
        $.ajax({
                type: "POST",
                url: "/proxy-product/product/adjustPrice/negativeProfit",
                data: params,
                async: false,
                dataType: 'json',
                contentType: "application/json",
                success: function(data) {
                    if (data.code == 0) {
                        let result = data.result;
                        let title = "以下商品出现负毛利，确认要继续操作调价吗？"
                        let message = ""
                        if (result != null && result.length > 0) {
                            for (let i = 0; i < result.length; i++) {
                                let selectRow = result[i];
                                message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
                            }
                            let d = utils.dialog({
                                title: "提示",
                                content: title + "<br/>" + message,
                                okValue: '确认提交',
                                ok: function() {
                                    checkActiveGoods(rowData, _this)
                                },
                                cancelValue: '返回',
                                cancel: function() {}
                            }).showModal();
                        } else {
                            checkActiveGoods(rowData, _this)
                        }
                    }
                }
            })
            // ------------校验活动商品--------------------------
        function checkActiveGoods(rowData, _this) {
            activeGoodsFun(rowData).then(() => {
                let activeGoods_tableData = $('#activeGoods_table').XGrid('getRowData')
                if (activeGoods_tableData.length > 0) {
                    // 活动商品
                    let ad = utils.dialog({
                        title: '活动商品提示',
                        content: $('#activeGoods_table_div'),
                        height: 500,
                        okValue: '确定',
                        ok: function() {
                            let ary1 = [];

                            $('#activeGoods_table tr').not(':eq(0)').each((index, item) => {
                                let el = $(item).find(' input[type=checkbox]');
                                let checkedStateAry = []
                                $(el).each((ind, ite) => {
                                    checkedStateAry.push($(ite).prop('checked'))
                                })
                                var checkedState = checkedStateAry.some(item => item)
                                if (checkedState) {
                                    ary1.push($(item).find('[row-describedby="productId"]').text())
                                }
                            })
                            if (ary1.length == 0 && $('#activeGoods_table').getRowData().length == $('#X_Table').getRowData().length) {
                                ad.close().remove()
                                utils.dialog({ content: '全部商品均为活动商品且选择不继续调价，本次未生成调价申请单', quickClose: true, timeout: 2000 }).showModal()
                                return false;
                            } else {
                                submitAdjust(_this);
                            }
                        },
                        cancelValue: '取消',
                        cancel: function() {}
                    }).showModal();
                    return false;
                }
                submitAdjust(_this)
            }).catch(err => {
                console.log(err);
            })
        }
        // activeGoodsFun(rowData).then(()=>{
        //     let activeGoods_tableData  = $('#activeGoods_table').XGrid('getRowData');
        //     // console.log(activeGoods_tableData,'activeGoods_tableDataactiveGoods_tableData')
        //     if (activeGoods_tableData.length>0){
        //         // 活动商品
        //         let ad  = utils.dialog({
        //             title: '活动商品提示',
        //             content: $('#activeGoods_table_div'),
        //             height: 500,
        //             okValue: '确定',
        //             ok: function () {
        //                 let ary1 = [];
        //
        //                 $('#activeGoods_table tr').not(':eq(0)').each((index, item) => {
        //                     let el= $(item).find(' input[type=checkbox]');
        //                     let checkedStateAry  = []
        //                     $(el).each((ind,ite) =>{
        //                         checkedStateAry.push($(ite).prop('checked'))
        //                     })
        //                     var checkedState  = checkedStateAry.some(item =>item)
        //                     if(checkedState){
        //                         ary1.push($(item).find('[row-describedby="productId"]').text())
        //                     }
        //                 })
        //                 if (ary1.length == 0 && $('#activeGoods_table').getRowData().length == $('#X_Table').getRowData().length ){
        //                     ad.close().remove()
        //                     utils.dialog({content:  '全部商品均为活动商品且选择不继续调价，本次未生成调价申请单', quickClose:true, timeout:2000}).showModal()
        //                     return false;
        //                 }else{
        //                     submitAdjust(_this);
        //                 }
        //             },
        //             cancelValue: '取消',
        //             cancel: function () {}
        //         }).showModal();
        //         return false;
        //     }
        //     // 点击提交时 商品负毛 校验
        //     if(!checkSubmitPrice(rowData,_this)) {
        //         return false
        //     }
        //     submitAdjust(_this)
        //
        // }).catch(err=>  {
        //     console.log(err);
        // })

        /*-------------RM ----------------*/
    });
    $("#X_Table").on("input", ".applicationAppPrice", function() {
        var trId = $(this).parents('tr').attr('id');
        var applicationAppPrice = this.value;
        let selectindex = $(this).attr("index");
        appPriceGrossMargin(selectindex, trId, applicationAppPrice);
    })
    $("#X_Table").on("input", ".applicationChainGuidePrice", function() {
        var $trId = $(this).parents('tr').attr('id');
        var selectRow = $('#X_Table').getRowData($trId);
        //默认审核连锁APP售价=申请连锁APP售价
        $('#X_Table').XGrid('setRowData', selectRow.id, { auditChainGuidePrice: this.value });
    })

    // //默认审核智鹿总部采购价=申请智鹿总部采购价
    // $("#X_Table").on("input", ".applicationZhiluPrice", function () {
    //     var $trId = $(this).parents('tr').attr('id');
    //     var selectRow = $('#X_Table').getRowData($trId);
    //     $('#X_Table').XGrid('setRowData', selectRow.id, {auditZhiluPrice: this.value});
    // })
    // //默认审核荷叶大药房采购价=申请荷叶大药房采购价
    // $("#X_Table").on("input", ".applicationHeyePrice", function () {
    //     var $trId = $(this).parents('tr').attr('id');
    //     var selectRow = $('#X_Table').getRowData($trId);
    //     $('#X_Table').XGrid('setRowData', selectRow.id, {auditHeyePrice: this.value});
    // })

    /*字数限制（备注）*/
    $("#remark").on("input propertychange", function() {
        var $this = $(this),
            _val = $this.val(),
            count = "";
        if (_val.length > 100) {
            $this.val(_val.substring(0, 100));
        }
        count = 100 - $this.val().length;
        $("#text-count").text(count);
    });


    //查看竞品价格
    $("#X_Table").on('click', '.price_show', function(e) {
        var rowId = $(e.target).parents('tr').attr('id');
        var rowData = $("#X_Table").getRowData(rowId);
        // let productChannelAreaList = []
        // if(processTypeFlag==0){
        //      productChannelAreaList=getProductChannelAreaList(rowData.productChannelAreaList);
        // }
        // let appPrice=rowData.appPrice
        // if(processTypeFlag==0) {
        //     appPrice=productChannelAreaList.find(item=>{return item.areaCode==0}).applicationAppPrice
        // }
        $.ajax({
            type: "POST",
            url: "/proxy-product/product/scm/priceComparison/searchSpiderProduct",
            dataType: "json",
            contentType: "application/json",
            timeout: 8000, //6000
            data: JSON.stringify({
                oldProductCode: rowData.oldProductCode,
                orgCode: rowData.orgCode,
                appPrice: rowData.appPrice

            }),
            beforeSend: function() {
                //parent.showLoading();
            },
            success: function(res) {
                var listData = res.result.list;
                if (!listData || listData.length === 0) {
                    return utils.dialog({
                        title: '提示',
                        width: '200px',
                        content: '抱歉，未查到竞品数据'
                    }).showModal();
                };
                let _html = `
                    <div>
                    <div style="padding: 0 15px">
                        <div class="row">
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">通用名：${rowData.commonName}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">生产厂商：${rowData.manufacturerName}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">规格：${rowData.specifications}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">商品编码：${rowData.productCode}</div>
                        </div>
                        <div class="row" style="margin-top:10px;">
                            <div class="col-xs-5 col-sm-5 col-md-5 col-lg-5">机构：${rowData.orgCodeValue}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">APP售价：${rowData.appPrice}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">连锁APP售价：${rowData.chainGuidePrice}</div>
                        </div>
                        </div>
                 
                    <div class="panel-body" style=" height: 340px; overflow-x: auto;">
                        <table id="comparison_price_table" />
                    </div>
                </div>
                 `
                var comparison_dialog = utils.dialog({
                    title: '市场价格查看',
                    content: _html,
                    width: 800,
                    height: 400,
                    cancelValue: '返回',
                    cancel: function() {}
                }).showModal();
                $('#comparison_price_table').XGrid({
                    data: listData,
                    colNames: ['平台', '店铺/供应商', '链接', '价格类型', '数据来源', '更新时间'],
                    colModel: [{
                            name: 'websiteName',
                            index: 'websiteName',
                        },
                        {
                            name: 'supplier',
                            index: 'supplier',
                        },
                        {
                            name: 'productUrl',
                            index: 'productUrl',
                            formatter: function(val, rowType, rowData) {
                                return `<a href="${rowData.productUrl}" title="${rowData.productUrl}" target="_blank">链接地址</a>`
                            }
                        },
                        {
                            name: 'websiteName',
                            index: 'websiteName',
                            formatter: function(val, rowType, rowData) {
                                return `折前价：${rowData.price}</br>折后价：${rowData.discountPrice}`
                            }
                        },
                        {
                            name: 'supplier',
                            index: 'supplier',
                            formatter: function(val, rowType, rowData) {
                                return `比价系统`
                            }
                        },
                        {
                            name: 'spiderTime',
                            index: 'spiderTime',
                        }
                    ],
                    rowNum: 20,
                    altRows: true, //设置为交替行表格,默认为false
                    rownumber: true
                });
            },
            complete: function() {
                //parent.hideLoading();
            }
        });
    });

    /* 对比 */
    $("#X_Table").on('click', '.comparison', function(e) {
        var rowId = $(e.target).parents('tr').attr('id');
        var rowData = $("#X_Table").getRowData(rowId);
        // let applicationAppPrice=rowData.applicationAppPrice
        // if(processTypeFlag==0) {
        //     applicationAppPrice=getProductChannelAreaList(rowData.productChannelAreaList).find(item=>{return item.areaCode==0}).applicationAppPrice
        // }
        $.ajax({
            type: "get",
            url: "/proxy-product/product/upperShelf/priceCompare",
            data: {
                productCode: rowData.productCode
            },
            beforeSend: function() {
                //parent.showLoading();
            },
            success: function(res) {
                var listData = res.result.list;
                listData.sort(function(a, b) {
                    return a.appPrice - b.appPrice
                });
                let _html = `
                <div id="comparisonDialog_div">
                    <div class="dialog_con">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">当前公司：${rowData.orgCodeValue}</div>
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">当前商品：${rowData.productCode}${rowData.productName} ${rowData.specifications}</div>
                        <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6">APP售价：${rowData.applicationAppPrice}</div>
                        <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6">连锁APP售价：${rowData.applicationChainGuidePrice}</div>
                    </div>
                    <div class="panel-body" style=" height: 340px; overflow-x: auto;">
                        <table id="comparison_table" />
                    </div>
                </div>
                 `
                var comparison_dialog = utils.dialog({
                    title: '价格对比',
                    content: _html,
                    width: 500,
                    height: 400,
                    cancelValue: '返回',
                    cancel: function() {}
                }).showModal();
                $('#comparison_table').XGrid({
                    //url:'/proxy-product/product/upperShelf/priceCompare',
                    data: listData,
                    colNames: ['其他子公司', 'APP售价', '连锁APP售价'],
                    colModel: [{
                            name: 'orgName',
                            index: 'orgName',
                            width: 210
                        },
                        {
                            name: 'appPrice',
                            index: 'appPrice',
                            width: 120
                        },
                        {
                            name: 'chainGuidePrice',
                            index: 'chainGuidePrice',
                            width: 120
                        }
                    ],
                    key: 'channelCode',
                    rowNum: 20,
                    altRows: true, //设置为交替行表格,默认为false
                    rownumber: true
                });
            },
            complete: function() {
                //parent.hideLoading();
            }
        });
    });
    $("#X_Table").on('click', '.file_btn', function(e) {
        $(e.target).parents('td').find('.file_input').trigger('click');
    });
    $("#X_Table").on('change', '.file_input', function(e) {
        sendFile(this.files, $(e.target).parents('tr').attr('id'));
    });

    $("#X_Table").on('click', '.file_a', function(e) {
        var url = $(e.target).attr('data-url');
        var id = $(e.target).parents('tr').attr('id');
        utils.dialog({
            align: 'top',
            width: 130,
            height: 50,
            padding: 2,
            content: `<div class="changeApplyItem" style="line-height: 50px;text-align: center;">
                    <a style="flex: 1;" href="${url}" target="_blank" class="download">查看下载</a>
                    <a style="flex: 1;" href="javascript:;" class="remove_file" data-id="${id}">删除</a>
                </div>`,
            quickClose: true
        }).show(this);
    });
    $("body").on('click', '.remove_file', function(e) {
        $("#X_Table").XGrid('setRowData', $(e.target).attr('data-id'), { enclosureUrl: '' });
        $(e.target).parents('.ui-popup').hide();
        $(".ui-popup-backdrop").hide();
    });
    $('body').on('input', '#X_Table input.TwoDecimalHndle', function() {
        utils.TwoDecimalHndle(this, 12)
    })
    $('body').on('blur', '#X_Table input.TwoDecimalHndle', function() {
            $(this).val(utils.toDecimal2($(this).val()))
        })
        //新增区域
    $("#X_Table").on('click', '.addNewArea', function(e) {
        e.stopPropagation()
        let rowId = $(e.target).parents('tr').attr('id');
        var rowData = $("#X_Table").getRowData(rowId);
        // console.log(rowData,'rowDatarowData')
        let productChannelAreaList = getProductChannelAreaList(rowData.productChannelAreaList);
        productChannelAreaList.push({
            areaCode: '',
            areaName: '',
            appPrice: '',
            grossMargin: '',
            grossMarginStr: '',
            applyAppPrice: '',
            applicationAppPrice: '',
            appPriceIncreaseRise: '',
            viewMoreStatus: false,
        })
        $("#X_Table").XGrid('setRowData', rowId, {...rowData, productChannelAreaList: productChannelAreaList })
    });
    //修改区域
    $("#X_Table").on('click', '.modifyArea', function(e) {
        let rowId = $(e.target).parents('tr').attr('id');
        var rowData = $("#X_Table").getRowData(rowId);
        let productChannelAreaList = getProductChannelAreaList(rowData.productChannelAreaList);
        let selectindex = $(e.target).attr("index");
        let dataArea = ''
        productChannelAreaList.forEach((item, index) => {
            if (index != selectindex) {
                dataArea += item.areaCode
            }
        })
        let currentAreaId = $(e.target).attr("data");
        let areaList = getAllProvince()
        let checkboxhtml = ""
        checkboxhtml += `<label style="margin-right: 10px;"><input type="checkbox" id="all"  autocomplete="off" title="全选"/>全选</label>`
        for (let i = 0; i < areaList.length; i++) {
            if (dataArea.indexOf(areaList[i].areaCode) > -1) { //其他行已选，设置禁用
                checkboxhtml += `<label style="margin-right: 10px;"><input type="checkbox" value="${areaList[i].areaCode}" name="salesArea"  disabled autocomplete="off" title="${areaList[i].areaName}"/>${areaList[i].areaName} </label>`
            } else if (currentAreaId.indexOf(areaList[i].areaCode) > -1) { //本行已选，设置已选
                checkboxhtml += `<label style="margin-right: 10px;"><input type="checkbox" value="${areaList[i].areaCode}" name="salesArea"  checked autocomplete="off" title="${areaList[i].areaName}"/>${areaList[i].areaName}</label>`
            } else { //
                checkboxhtml += `<label style="margin-right: 10px;"><input type="checkbox" value="${areaList[i].areaCode}" name="salesArea"  autocomplete="off" title="${areaList[i].areaName}"/>${areaList[i].areaName}</label>`
            }
        }
        let selectedArrCode = []
        let selectedArrName = []
        var d = dialog({
            title: "修改覆盖省份",
            width: 800,
            // height:30,
            okValue: '确定',
            content: `<div  style="line-height: 50px;text-align: center;">
                 ${checkboxhtml}
                  </div>`,
            ok: function() {
                $("input[name='salesArea']:checked").each(function(i) {
                    selectedArrCode[i] = $(this).val();
                    selectedArrName[i] = $(this).attr("title");
                })
                productChannelAreaList.map((item, index) => {
                    if (item.areaCode == currentAreaId && index == selectindex) {
                        item.areaCode = selectedArrCode.join(',')
                        item.areaName = selectedArrName.join(',')
                    }
                })
                $("#X_Table").XGrid('setRowData', rowId, { areaCode: '', productChannelAreaList: productChannelAreaList })
            },
            cancelValue: '取消',
            cancel: function() {},
        });
        setTimeout(function() {
            var all = $("input[id='all']")
            var allInput = $("input[name='salesArea']")
            var selectAllstatus = false

            function istrue() {
                // 判断是否input的checked 是否全部为true
                for (let i = 0, sum = 0, disabledSum = 0; i < allInput.length; i++) {
                    disabledSum += allInput[i].disabled
                    sum += allInput[i].checked;
                    if (disabledSum + sum === allInput.length) {
                        selectAllstatus = true
                    }
                    if (sum == allInput.length) {
                        all.removeClass('selectAll')
                        all.prop('checked', true)
                    } else if (sum > 0 && sum < allInput.length) {
                        all.prop('checked', false);
                        all.addClass('selectAll')
                    } else if (sum === 0) {
                        all.removeClass('selectAll')
                        selectAllstatus = false
                        all.prop('checked', false);
                    }
                }
            }
            // 全选/全不选
            all.on('click', (event => {
                    // event.stopPropagation()
                    selectAllstatus = !selectAllstatus
                    for (let i = 0, sum = 0; i < allInput.length; i++) {
                        if (!allInput[i].disabled) {
                            allInput[i].checked = selectAllstatus
                        }
                        sum += allInput[i].checked;
                        if (sum == allInput.length) {
                            all.prop('checked', true)
                        } else {
                            all.prop('checked', false);
                        }
                    }
                    istrue()
                }))
                // input 1~10 点击事件
            allInput.each(function(i) {
                $(this).click(function() {
                    istrue();
                })
            })
            istrue()
            d.showModal()
        }, 500)
    });
    //删除区域
    $("#X_Table").on('click', '.deleteArea', function(e) {
        let rowId = $(e.target).parents('tr').attr('id');
        var rowData = $("#X_Table").getRowData(rowId);
        let productChannelAreaList = getProductChannelAreaList(rowData.productChannelAreaList);
        let index = $(e.target).attr("index");
        utils.dialog({
            title: "提示",
            width: 300,
            height: 30,
            okValue: '确定',
            content: "该商品的默认、分省价格将一起删除，请确认是否这样操作?",
            ok: function() {
                productChannelAreaList.splice(index, 1);
                $("#X_Table").XGrid('setRowData', rowId, { areaCode: '', newAppPrice: '', applyAppPrice: '', appPriceIncreaseRise: '', grossMarginStr: '', productChannelAreaList: productChannelAreaList })
                utils.dialog({ content: '删除成功！', quickClose: true, timeout: 2000 }).showModal();
            },
            cancelValue: '取消',
            cancel: function() {}
        }).showModal();
    });

    //批量删除区域
    $("#X_Table").on('click', '.deleteAllArea', function(e) {
        let rowId = $(e.target).parents('tr').attr('id');
        var rowData = $("#X_Table").getRowData(rowId);
        let productChannelAreaList = getProductChannelAreaList(rowData.productChannelAreaList);
        utils.dialog({
            title: "提示",
            width: 300,
            height: 30,
            okValue: '确定',
            content: "确定删除所有区域?",
            ok: function() {
                productChannelAreaList.splice(1);
                $("#X_Table").XGrid('setRowData', rowId, {
                    areaCode: '',
                    newAppPrice: '',
                    applyAppPrice: '',
                    appPriceIncreaseRise: '',
                    grossMarginStr: '',
                    productChannelAreaList: productChannelAreaList
                })
                utils.dialog({ content: '删除成功！', quickClose: true, timeout: 2000 }).showModal();
            },
            cancelValue: '取消',
            cancel: function() {}
        }).showModal();
    });

    //查看更多
    $("#X_Table").on('click', '.viewMore', function(e) {
        e.stopPropagation()
        let rowId = $(e.target).parents('tr').attr('id');
        var rowData = $("#X_Table").getRowData(rowId);
        let productChannelAreaList = getProductChannelAreaList(rowData.productChannelAreaList);
        let indexs = $(e.target).attr("index");
        productChannelAreaList.map((item, index) => {
            if (index == indexs) {
                item.viewMoreStatus = !item.viewMoreStatus
            }

        })
        $("#X_Table").XGrid('setRowData', rowId, {...rowData, productChannelAreaList: productChannelAreaList })
    });
}
//签约方式切换选中内容
function disSeleteHander(boole) {
    if (boole) {
        $("#centralizedPurchaseType").attr('disabled', 'disabled');
        $("#purchaseContractMode").attr('disabled', 'disabled');

    } else {
        $("#centralizedPurchaseType").removeAttr('disabled');
        if ($("#centralizedPurchaseType").val() == '0') {
            $("#purchaseContractMode").val("");
        } else {
            $("#purchaseContractMode").removeAttr('disabled');
        }
    }
}