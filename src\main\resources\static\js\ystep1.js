/*根据处理状态，添加或删除对应的样式名*/
function addClass(elem, className) { //增加类名 
    if (!elem.className) {
        elem.className = className;
        return;
    }
    var clazz = ' ' + elem.className + ' ';
    if (clazz.indexOf(' ' + className + ' ') === -1) {
        elem.className = elem.className + ' ' + className;
    }
}
var step1 = document.getElementById('tab_step1'),
    step2 = document.getElementById('tab_step2'),
    step3 = document.getElementById('tab_step3'),
    step4 = document.getElementById('tab_step4'),
    step5 = document.getElementById('tab_step5');
var status = '<?php echo $status;?>';
switch (status) {
    case '1': //待确认 
        addClass(step2.parentNode, 'active'); //parentNode即为包含step2的外一层标签，此处即为<li>标签 
        break;
    case '2': //待修复 
        addClass(step2.parentNode, 'active');
        addClass(step3.parentNode, 'active');
        break;
    case '3': //已关闭 
        addClass(step2.parentNode, 'active');
        addClass(step3.parentNode, 'active');
        addClass(step4.parentNode, 'active');
        break;
    case '4': //已公开 
        addClass(step2.parentNode, 'active');
        addClass(step3.parentNode, 'active');
        addClass(step4.parentNode, 'active');
        addClass(step5.parentNode, 'end bottom-active');
        break;
}