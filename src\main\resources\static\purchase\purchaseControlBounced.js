$(function () {
    let dialog = parent.dialog.get(window);

    $('#productCodeVal').Autocomplete({
        serviceUrl:'/proxy-product/product/productBase/query', //异步请求
        paramName: 'productCode',
        params: {
            pageNum: 1,
            pageSize: 5
        },
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            if(response.code == 0){
                if(response.result){
                    return {
                        suggestions: $.map(response.result.list, function (dataItem) {
                            return {value: dataItem.productCode+"\xa0\xa0\xa0"+dataItem.productName,productCode:dataItem.productCode,productName:dataItem.productName};
                        })
                    };
                }

            }

        },
        onSelect: function (result) {
            // var str = result.value.split("\xa0\xa0\xa0");
            var productCode = result.productCode;
            var productName = result.productName;
            $("#productNameVal").val(productName);
            $("#productCodeVal").val(productCode);
        },
        onNoneSelect: function (params, suggestions) {
            $("#productNameVal").val("");
            $("#productCodeVal").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    })

    $('#supplierCodeVal').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/supplier/getSuppliserKeyInfo', //异步请求
        paramName: 'keyword',
        params: {
            pageNum: 1,
            pageSize: 5
        },
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        maxWidth: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.supplierCode+"\xa0\xa0\xa0"+  dataItem.supplierName, supplierCode: dataItem.supplierCode,supplierName:dataItem.supplierName};
                })
            };
        },
        onSelect: function (result) {
            var supplierCode = result.supplierCode;
            var supplierName = result.supplierName;
            $("#supplierCodeVal").val(supplierCode);
            $("#supplierNameVal").val(supplierName);
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierCodeVal").val("");
            $("#supplierNameVal").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });

    // 确定
    $('#btn_sub').click(function () {
        var orgCodes = "";
        let formData = {
            org:$("input[name=orgName]:checked").map(function (index,ele) {
                orgCodes = orgCodes + $(ele).val() +",";
               return {
                   orgCode:$(ele).val(),
                   orgName:$(ele).attr('data-name')
               };
            }).toArray(),
            productCode: $("#productCodeVal").val(),
            productName: $("#productNameVal").val(),
            supplierCode: $("#supplierCodeVal").val(),
            supplierName: $("#supplierNameVal").val(),
            remark: $("#remark").val()
        };
        //校验是否为空
        if(orgCodes == "" || $("#productCodeVal").val() == ""){
            utils.dialog({content: '请输入必填项，再点击确认', quickClose: true, timeout: 3000}).show();
            return false;
        }

        var rowData = dialog.data.rowData;
        if(rowData&&rowData.some(function (row,key) {
            return row.productCode === formData.productCode && row.supplierCode === formData.supplierCode && formData.org.some(function (orgItem) {
                return row.orgCode === orgItem.orgCode
            })
        })){
            utils.dialog({content: '数据重复！', quickClose: true, timeout: 3000}).show();
        }else{
            //数据库中校验，是否存在
            $.ajax({
                type: "POST",
                url: "/proxy-purchase/purchase/productSupplier/checkProductSupplier",
                data:{
                    "productCode": $("#productCodeVal").val(),
                    "supplierCode": $("#supplierCodeVal").val(),
                    "orgCodes":orgCodes
                },
                error: function () {},
                success: function (res) {
                    if(res){
                        if(res.code == 1){
                            utils.dialog({content: res.msg, quickClose: true, timeout: 4000}).showModal();
                            return false;
                        }else if(res.code == 0){
                            dialog.close(formData);
                        }
                    }else{
                        utils.dialog({content: "校验错误", quickClose: true, timeout: 4000}).showModal();
                    }
                }
            });
        }
    });
    // 返回
    $('#btn_cancel').click(function () {
        dialog.close();
    })
})
function checkAll(obj) {
    let chks = document.getElementsByName("orgName");
    for (let i = 0; i < chks.length; i++) {
        chks[i].checked = obj.checked;
    }
}
function check(obj){
    let chks = document.getElementsByName("orgName");
    let	chkAll = document.getElementsByName("checkall");
    if(obj.checked==false){
        chkAll[0].checked=false;
    }else{
        let j=1;
        for( let i = 0 ; i < chks.length ; i++ ){
            if(chks[i].checked==false){
                j=2;
                break;
            }
        }
        if (j==1){
            chkAll[0].checked=true;
        }
    }
}


