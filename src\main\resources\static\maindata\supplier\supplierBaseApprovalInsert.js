var supplierBaseId =  $("#supplierBaseId").val(); //基础属性id
var supplierBaseChangeApprovalId =  $("#supplierBaseChangeApprovalId").val(); //运营属性id
var pageType = $("#pageType").val();
var processId = $("#processId").val();
var taskId = $("#taskId").val();
var approvalProcessId = $("#approvalProcessId").val();
//获取仓库地址
function distpickerHTML(n,m) {
    var len = n ? n : 1;
    var html = '';
    //2018.9.4,RL,下面html中新增了非空校验,bug2386
    let radomInit = [];
    for (let i = 0; i < len; i++) {
        let _int = parseInt(Math.random() * 100 + Math.random() * 1000);
        html += `<div class="col-md-6 depotList">
	        <div class="input-group">
	            <div class="input-group-addon require"><i class="text-require">*  </i>仓库地址</div>
	            <div class="form-control form-inline distpicker" id="storageBox_${_int}">
	                <div class="row">
	                    <div class="form-group col-md-2" id="stoProvinceSel_wrap_${_int}">
	                        <select class="form-control repertoryProvinceSelect" name="repertoryProvince_${_int}" id="repertoryProvince_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-2" id="stoCitySel_wrap_${_int}">
	                        <select class="form-control repertoryCitySelect" name="repertoryCity_${_int}" id="repertoryCity_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-2" id="stoDistrictSel_wrap_${_int}">
	                        <select class="form-control repertoryAreaSelect" name="repertoryArea_${_int}" id="repertoryArea_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-5" style="position: initial;">
	                        <input type="text" class="form-control repertoryDetailSelect text-inp Filter_SpaceAndFiveStrLen_Class" name="repertoryDetail"  ${i == 0 && !m? 'changeApplyFlag = "supplierRepertoryAddressVOList"': ''} />
	                    </div>
	                    <div class="form-group btn-box col-md-1">
	                        <button type="button" class="btn ${i == 0 && !m? 'addDepot': 'removeDepot'}">
	                            <span class="glyphicon ${i == 0 && !m ? 'glyphicon-plus': 'glyphicon-minus'}" aria-hidden="true"></span>
	                        </button>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </div>`;
        radomInit.push(_int);
    }
    return {
        html: html,
        radomInit: radomInit
    };
}
function initDistpicker(){
    $('[data-toggle="distpicker"]').each(function(){
        //省
        var val1=$.trim($(this).find("select").eq(0).attr("data-value"));
        if(val1 && val1 != '')
        {
            $(this).find("select").eq(0).val(val1);
            $(this).find("select").eq(0).change();
        }
        //市
        var val2=$.trim($(this).find("select").eq(1).attr("data-value"));
        if(val1 && val1 != '')
        {
            $(this).find("select").eq(1).val(val2);
            $(this).find("select").eq(1).change();
        }
        //区
        var val3=$.trim($(this).find("select").eq(2).attr("data-value"));
        if(val1 && val1 != '')
        {
            $(this).find("select").eq(2).val(val3);
        }
    });
}

// 批准文件
// upLoadFile("#pzwjUpload",'#table3','certificateId');
// //年度报告
// //upLoadFile("#ndbgUpload",'#table4','reportDate');
// ndbgUploadFun();
// //其它附件 批量管理附件按钮
// qtfjUploadFun();



// 设置页面所有属性不可编辑
function disable(){
    $("input[type='text']").attr("disabled","disabled");
    $("input[type='tel']").attr("disabled","disabled");
    $("input[type='number']").attr("disabled","disabled");
    $("input[type='checkbox']").attr("disabled","disabled");
    $("input[type='radio']").attr("disabled","disabled");
    $("textarea").attr("disabled","disabled");
    $("select").attr("disabled","disabled");
    $(".addDepot").hide();
    $(".removeDepot").hide();
    $(".btn_addManufacturer").hide();
    $(".btn_removeManufacturer").hide();
    $(".rowBtn").attr("disabled","disabled");
}
/*$("#changeApplyBtn").on("click",function(){
    $("#addDepot").show();
    $(".removeDepot").show();
});*/
// 设置页面所有属性可读取
function able(){
    $("input[type='text']").removeAttr("disabled");
    $("input[type='tel']").removeAttr("disabled");
    $("input[type='number']").removeAttr("disabled");
    $("input[type='checkbox']").removeAttr("disabled");
    $("input[type='radio']").removeAttr("disabled");
    $("textarea").removeAttr("disabled");
    $("select").removeAttr("disabled");
    $(".addDepot").show();
    $(".removeDepot").show();
    $(".btn_addManufacturer").show();
    $(".btn_removeManufacturer").show();
}
/**
 * 流程图显示
 */
function  initApprovalFlowChart(url) {
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result)
                $('.flow').process(data.result);
            }
        },
        error: function () {
        }
    });
}
disable();
$(function () {
    if (pageType==0){
        $('#supplierName').removeAttr('readonly').removeAttr('disabled');
        $("#changeApplyBtn").hide();
        // 修改字段结构初始化
        $.changApply_insertData({
            name: 'columnValue',
            status: 'changeStatus',
            afterValue: 'valueAfter',
            beforeValue: 'valueBefore'
        });  //按钮id：changeApplyBtn
        var url = "/proxy-product/product/purchaseLimit/queryTotle?key=supplierChange";
        initApprovalFlowChart(url);
    }else {
        //编辑页面
        /*if (pageType==1||pageType==4){
            disable();
            $('#supplierName').removeAttr('readonly').removeAttr('disabled');
        }
        //审核页面
        if (pageType==2||pageType==3){
            disable();
        }*/
        // 审核页和驳回编辑页
        if(pageType==3||pageType==4){
            var url = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+processId;
            initApprovalFlowChart(url);
        }
        // 草稿页
        if(pageType==1){
            var url = "/proxy-product/product/purchaseLimit/queryTotle?key=supplierChange";
            initApprovalFlowChart(url);
        }
        // 详情页
        if(pageType==2){
            var url = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+approvalProcessId;
            initApprovalFlowChart(url);
        }
        $.ajax({
            url:'/proxy-supplier/supplier/supplierBaseChangeApproval/querySupplierBaseAfterDetail',
            data:{"id":supplierBaseChangeApprovalId},
            type:"post",
            success:function(data){
                if(data!=null&&data!=undefined){
                    //字段修改全部
                    var changeObj=data.result.supplierOrganBaseChangeRecordVOList;
                    var obj = {};
                    var objList = {};
                    // 注册地址
                    var flag = 0;
                    var registerList = {
                        "columnValue":"registerList",
                        "valueAfter":{}
                    };
                    $.each(changeObj,function (i, v) {
                        // 判断是否为批准文件
                        if(v.columnValue=="supplierApprovalFileList"){
                            objList.supplierApprovalFileList = {
                                "columnValue":"supplierApprovalFileList",
                                "valueAfter":{},
                                "changeStatus":-1
                            };
                            objList.supplierApprovalFileList.valueAfter = data.result.supplierApprovalFileList;
                        }else if(v.columnValue=="supplierYearReportList"){
                            // 年度报告
                            objList.supplierYearReportList = {
                                "columnValue":"supplierYearReportList",
                                "valueAfter":{},
                                "changeStatus":-1
                            };
                            objList.supplierYearReportList.valueAfter = data.result.supplierYearReportList;
                        }else if(v.columnValue=="supplierOtherFileList"){
                            // 其他附件
                            objList.supplierOtherFileList = {
                                "columnValue":"supplierOtherFileList",
                                "valueAfter":{},
                                "changeStatus":-1
                            };
                            objList.supplierOtherFileList.valueAfter = data.result.supplierOtherFileList;
                        }else if(v.columnValue=="supplierRepertoryAddressVOList"){
                            v.valueAfter = data.result.supplierBaseChangeApprovalRecordVO.supplierRepertoryAddressVOList;
                            v.changeStatus=-1;
                            // 仓库地址
                            obj[v.columnValue]=v;
                        }else if(v.columnValue=="supplierManufactoryVOList"){
                            v.valueAfter = data.result.supplierBaseChangeApprovalRecordVO.supplierManufactoryVOList;
                            v.changeStatus=-1;
                            // 对应生产厂家
                            obj[v.columnValue]=v;
                        }else if(v.columnValue=="registerDetail"){
                            flag = 1;
                            registerList.valueAfter.registerDetail = v.valueAfter;
                        }else if(v.columnValue=="registerProvince"){
                            flag = 1;
                            registerList.valueAfter.registerProvince = v.valueAfter;
                        }else if(v.columnValue=="registerCity"){
                            flag = 1;
                            registerList.valueAfter.registerCity = v.valueAfter;
                        }else if(v.columnValue=="registerArea"){
                            flag = 1;
                            registerList.valueAfter.registerArea = v.valueAfter;
                        }else{
                            v.changeStatus = -1
                            obj[v.columnValue]=v;
                        }
                    });
                    if(flag==1){
                        delete obj.registerProvince;
                        delete obj.registerCity;
                        delete obj.registerArea;
                        delete obj.registerDetail;
                        registerList.changeStatus=-1;
                        obj.registerList=registerList;
                    }
                    //编辑页面
                    if (pageType==1||pageType==4){
                        // 修改字段结构初始化
                        $.changApply_insertData();  //按钮id：changeApplyBtn
                    }
                    //审核页面
                    if (pageType==2||pageType==3){
                        var objView = obj;
                        $.extend(objView,objList);
                        setTimeout(function () {
                            $.changeApply_selectData(obj);
                        });
                    }
                    window.changeApply=obj;
                    window.changeApplyList=objList;
                    window.changeApplyBak=JSON.parse(JSON.stringify(obj));
                    window.changeApplyListBak=JSON.parse(JSON.stringify(objList));
                }
            }
        })
    }

    if(supplierBaseId==null||supplierBaseId==''){
        //批准文件
        xGridTable3({
            id: "",
            certificateId: "",
            certificateNum:"",
            supplierApprovalFileBusinessScopeVOList: [],
            certificationOffice: "",
            certificationDate: "",
            validityDate: "",
            enclosureCount: "",
            enclosureList:[]
        });
        //年度报告
        xGridTable4(xGridData = {
            id: "",
            reportDate: "",
            manageAbnormal: "",
            administrativePenalty: "",
            enclosureCount: "",
            enclosureList:[]
        });
    }else{
        //主数据属性下沉---------开始----------
        //年度报告table初始化
        //---------initTable4($("#supplierBaseId").val(),0);
//初始化批准文件证书类型
        /*localBatchName(function(){
            console.log(1)*/
        //批准文件table初始化
        //---------initTable3($("#supplierBaseId").val(),0);
        //});
        //其他附件
        //---------initOtherFile(supplierBaseId,0)
        //主数据属性下沉---------结束----------
    }

    // 注册地址
    let addressSelIdObj = [
        {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvince',nextNodeId: 'province1'},
        {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCity',nextNodeId: 'registerCity'},
        {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerArea',nextNodeId: 'district1'}
    ];
    let registerPromiseArray = [];
    utils.setAllProDom('#provinceSel_wrap', addressSelIdObj, '#registerBox',true, function () {
        // 注册地址有值回显
        let _registerHiddenVal = eval($('#registerAddressJson').val());
        if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
        _registerHiddenVal.splice(_registerHiddenVal.length - 1);
        $('#' + addressSelIdObj[0]['nextNodeId']).prop('disabled', true);
        $('#' + addressSelIdObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
        for (let i = 1; i < _registerHiddenVal.length; i++) {
            registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
        }
        Promise.all(registerPromiseArray).then(data => {
            console.log(data)
            for (let i = 0; i < data.length; i++) {
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).html(data[i]);
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).prop('disabled', true);
            }
        })
        disable();
    });

    // 仓库地址
    let storgeAddressSelIdObj = [];
    let _storgeHiddenVal = eval($('#repertoryAddressJson').val());
    if (!_storgeHiddenVal) _storgeHiddenVal = [['','','','']];
    let _storgeHiddenValArr =  eval($('#repertoryAddressJson').val());
    if (!_storgeHiddenValArr) _storgeHiddenValArr = [['','','','']];
    $(_storgeHiddenValArr).each((index,item) => {
        item.splice(item.length - 1);
    });
    let obj = distpickerHTML(_storgeHiddenValArr.length);
    $(obj.radomInit).each((index, item) => {
        let _arr = [
            {nextNodeWrap: '#stoProvinceSel_wrap_' + item,nextNodeName: 'repertoryProvince_' + item,nextNodeId: 'repertoryProvince_' + item},
            {nextNodeWrap: '#stoCitySel_wrap_' + item,nextNodeName: 'repertoryCity_' + item,nextNodeId: 'repertoryCity_' + item},
            {nextNodeWrap: '#stoDistrictSel_wrap_' + item,nextNodeName: 'repertoryArea_' + item,nextNodeId: 'repertoryArea_' + item}
        ]
        storgeAddressSelIdObj.push(_arr)
    });
    $('#depotAddress').html(obj.html)
    // $(obj.radomInit).each((index, item) => {
    //     for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
    //         utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[index], storgeAddressSelIdObj[index], '#storageBox', true,function () {
    //             $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(_storgeHiddenValArr[index][0]);
    //             $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[index][3]);
    //             $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
    //             $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).addClass('{validate:{ required :true}}');
    //             $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').prop('disabled', true);
    //             $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').attr('changeApplyFlag', 'supplierRepertoryAddressVOList');
    //             $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('.btn').css('display','none');
    //             disable();
    //             utils.setAddressReturnVal(_storgeHiddenValArr[index][ind-1]).then(str => {
    //                 $('#' + storgeAddressSelIdObj[index][ind]['nextNodeId']).html(str);
    //                 $('#' + storgeAddressSelIdObj[index][ind]['nextNodeId']).val(_storgeHiddenValArr[index][ind]);
    //                 $('#' +  storgeAddressSelIdObj[index][ind]['nextNodeId']).addClass('{validate:{ required :true}}')
    //                 $('#' + storgeAddressSelIdObj[index][ind]['nextNodeId']).prop('disabled', true);
    //             }).catch(err => {
    //                 console.log(err)
    //             })
    //         })
    //     }
    // })
    $(obj.radomInit).each((index, item) => {
        let storagePromiseArray = [];
        utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[index], storgeAddressSelIdObj[index], '#storageBox_' + obj.radomInit[index], true,function () {
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(_storgeHiddenValArr[index][0]);
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[index][3]);
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
            $('#' + storgeAddressSelIdObj[0][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').attr('changeApplyFlag', 'supplierRepertoryAddressVOList');
            for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
                storagePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenValArr[index][ind-1]));
            }
            let allSelArr = storgeAddressSelIdObj[index].flat().map((item, index) => {
                if (index != 0) {
                    return item['nextNodeId']
                }
            }).filter(item => {
                return item
            });
            Promise.all(storagePromiseArray).then(data => {
                for (let i = 0; i < data.length; i++) {
                    $('#' + allSelArr[i]).html(data[i]);
                    $('#' + allSelArr[i]).val(_storgeHiddenValArr[index][i + 1]);
                    $('#' + allSelArr[i]).prop('disabled', true);
                }
            })
            disable();
        })
    })


})




//初始化地址选择
// $('[data-toggle="distpicker"]').distpicker();
// initDistpicker();
//添加仓库
$("#depotAddress").on("click",".addDepot",function () {
    let obj = distpickerHTML(1,1);
    $("#depotAddress").append(obj.html);
    let storgeAddressSelIdObj = [
        {nextNodeWrap: '#stoProvinceSel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryProvince_' + obj.radomInit[0],nextNodeId: 'repertoryProvince_' + obj.radomInit[0]},
        {nextNodeWrap: '#stoCitySel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryCity_' + obj.radomInit[0],nextNodeId: 'repertoryCity_' + obj.radomInit[0]},
        {nextNodeWrap: '#stoDistrictSel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryArea_' + obj.radomInit[0],nextNodeId: 'repertoryArea_' + obj.radomInit[0]}
    ]
    utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[0], storgeAddressSelIdObj, '#storageBox_' + obj.radomInit[0],true, function () {
        $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
        $('#depotAddress .btn').css('display','inline-block');
    })
});
//删除仓库地址
$("#supplierBaseChangeApprovalRecordVO").on("click", ".removeDepot", function () {
    $(this).parents(".depotList").remove();
});

//点击放大镜触发
$(document).on("click", "#supplierBtn", function (ev) {
    search(ev);
});
//回车触发
$(document).on("keydown", "#supplierName", function (ev) {
    if(ev.keyCode==13){
        search(ev);
    }
});
// 模糊搜索供应商名称
function search(ev){
    if(pageType==0||pageType==1){
        var supplierName = $("#supplierName").val();
        dialog({
            url: '/proxy-supplier/supplier/supplierOrganBaseChangeApproval/toSearchList',//弹框页面请求地址
            title: '搜索供应商',
            width: 1000,
            height: 650,
            data: supplierName, // 给modal 要传递的 的数据
            onclose:function(){
                if(this.returnValue)
                {
                    var data=this.returnValue;
                    $.ajax({
                        url:'/proxy-supplier/supplier/supplierOrganBaseChangeApproval/queryOrganBaseById',
                        data:{"id":data.id},
                        type:"post",
                        dataType:'json',
                        success:function(data){
                            // 清空修改的字段
                            window.changeApply = {};//变更obj
                            window.changeApply_ = {};//变更临时obj
                            window.changeApplyList = {};//变更obj-list
                            window.changeApplyList_ = {};//变更临时obj-list
                            var vo = data.result;
                            loadData(vo.supplierBase,$("#base"));
                            $("#supplierBaseId").val(vo.supplierBase.id);
                            // 注册地址
                            let addressSelIdObj = [
                                [
                                    {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvince',nextNodeId: 'province1'},
                                    {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCity',nextNodeId: 'registerCity'},
                                    {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerArea',nextNodeId: 'district1'}
                                ]
                            ];
                            let registerPromiseArray = [];
                            let _registerHiddenVal = [vo.supplierBase.registerProvince, vo.supplierBase.registerCity, vo.supplierBase.registerArea];
                            $('#' + addressSelIdObj[0][0]['nextNodeId']).val(_registerHiddenVal[0]);
                            $('#' + addressSelIdObj[0][0]['nextNodeId']).attr('data-value', _registerHiddenVal[0]);
                            for (let i = 1; i < _registerHiddenVal.length; i++) {
                                registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
                            }
                            Promise.all(registerPromiseArray).then(data => {
                                console.log(data)
                                for (let i = 0; i < data.length; i++) {
                                    $('#' + addressSelIdObj[0][i+1]['nextNodeId']).html(data[i]);
                                    $('#' + addressSelIdObj[0][i+1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                                    $('#' + addressSelIdObj[0][i+1]['nextNodeId']).prop('disabled', true);
                                }
                            })

                            var AddressVOList=vo.supplierBase.supplierRepertoryAddressVOList;
                            if(null !=AddressVOList){
                                var len = AddressVOList.length;
                                $("#depotAddress").find('.depotList').remove();
                                var obj = distpickerHTML(len);
                                let storgeAddressSelIdObj = [];
                                let _storgeHiddenValArr = AddressVOList.map(item => {
                                    return [item.repertoryProvince, item.repertoryCity, item.repertoryArea]
                                });
                                let _storgeHiddenVal = AddressVOList.map(item => {
                                    return [item.repertoryProvince, item.repertoryCity, item.repertoryArea, item.repertoryDetail]
                                });
                                $(obj.radomInit).each((index, item) => {
                                    let _arr = [
                                        {nextNodeWrap: '#stoProvinceSel_wrap_' + item,nextNodeName: 'repertoryProvince_' + item,nextNodeId: 'repertoryProvince_' + item},
                                        {nextNodeWrap: '#stoCitySel_wrap_' + item,nextNodeName: 'repertoryCity_' + item,nextNodeId: 'repertoryCity_' + item},
                                        {nextNodeWrap: '#stoDistrictSel_wrap_' + item,nextNodeName: 'repertoryArea_' + item,nextNodeId: 'repertoryArea_' + item}
                                    ]
                                    storgeAddressSelIdObj.push(_arr)
                                });
                                $('#depotAddress').html(obj.html)
                                $(obj.radomInit).each((index, item) => {
                                    let storagePromiseArray = [];
                                    utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[index], storgeAddressSelIdObj[index], '#storageBox_' + obj.radomInit[index],true, function () {
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(_storgeHiddenValArr[index][0]);
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[index][3]);
                                        $('#' + storgeAddressSelIdObj[0][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').attr('changeApplyFlag', 'supplierRepertoryAddressVOList');
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').prop('disabled', true);
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('.btn').css('display','none');
                                        disable();
                                        for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
                                            storagePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenValArr[index][ind-1]));
                                        }
                                        let allSelArr = storgeAddressSelIdObj[index].flat().map((item, index) => {
                                            if (index % 3 != 0) {
                                                return item['nextNodeId']
                                            }
                                        }).filter(item => {
                                            return item
                                        });
                                        Promise.all(storagePromiseArray).then(data => {
                                            console.log(allSelArr)
                                            console.log(data)
                                            for (let i = 0; i < data.length; i++) {
                                                $('#' + allSelArr[i]).html(data[i]);
                                                $('#' + allSelArr[i]).val(_storgeHiddenValArr[index][i + 1]);
                                                $('#' + allSelArr[i]).prop('disabled', true);
                                            }
                                        })

                                    })
                                })
                            }else{
                                // html=distpickerHTML();
                                let obj = distpickerHTML();
                                $("#depotAddress").html(obj.html);
                                let storgeAddressSelIdObj = [
                                    {nextNodeWrap: '#stoProvinceSel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryProvince_' + obj.radomInit[0],nextNodeId: 'repertoryProvince_' + obj.radomInit[0]},
                                    {nextNodeWrap: '#stoCitySel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryCity_' + obj.radomInit[0],nextNodeId: 'repertoryCity_' + obj.radomInit[0]},
                                    {nextNodeWrap: '#stoDistrictSel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryArea_' + obj.radomInit[0],nextNodeId: 'repertoryArea_' + obj.radomInit[0]}
                                ]
                                utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[0], storgeAddressSelIdObj, '#storageBox_' + obj.radomInit[0],true, function () {
                                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').attr('changeApplyFlag', 'supplierRepertoryAddressVOList');
                                    disable();
                                });
                            }
                            // if(html!=""){
                            //     /**
                            //      * RM 2018-12-04
                            //      * 每次引用的供应商的仓库地址的条数不一样，所以每次进来需要清空一遍，要不会有多余的仓库地址的dom 结构，在回显的时候会报错
                            //      */
                            //     $(".depotList").remove();
                            //     /**
                            //      * RM 2018-11-13
                            //      * 当引用数据回显的时候，如果数据有两条仓库地址。在页面渲染的时候需要做判断
                            //      */
                            //     //仓库地址数据大于一条
                            //     if(AddressVOList.length >1){
                            //         //获取下标：字符串相加后两条地址的dom 结构的首尾标签
                            //         var _ind = html.indexOf('v><d');
                            //         //截取出第一段仓库地址的字符串
                            //         var _html = html.substring(0,_ind+2);
                            //         //页面渲染第一段DOM
                            //         $("#depotAddress").html(_html);
                            //         //给下一行的Dom 追加仓库地址的dom
                            //         //$("#depotAddress").parents('.row').next().append(html.substring(_ind+2));
                            //         $("#depotAddress").parents('.row').append(html.substring(_ind+2));
                            //     }else{
                            //         //只有有一条地址数据时，直接放置dom
                            //         $("#depotAddress").html(html);
                            //     }
                            // }
                            // if(AddressVOList.length > 0) {
                            //     $(".depotList").each(function (index) {
                            //         $(this).find("select[name='repertoryProvince']").attr("data-value", AddressVOList[index].repertoryProvince);
                            //         $(this).find("select[name='repertoryCity']").attr("data-value", AddressVOList[index].repertoryCity);
                            //         $(this).find("select[name='repertoryArea']").attr("data-value", AddressVOList[index].repertoryArea);
                            //         $(this).find("input[name='repertoryDetail']").val(AddressVOList[index].repertoryDetail);
                            //     });
                            // }
                            // $('[data-toggle="distpicker"]').distpicker();
                            // initDistpicker();
                            $('[data-toggle="distpicker"]').distpicker();
                            initDistpicker();

                            // 对应生产厂商
                            let ManufactoryVOList = vo.supplierBase.supplierManufactoryVOList,
                                ManufactoryHtml = '';
                            if(ManufactoryVOList.length != 0){
                                let len = ManufactoryVOList.length;
                                for(let i = 0; i< len; i++){
                                    ManufactoryHtml += `<div class="col-md-6 ManufactoryList">
                                                        <div class="input-group">
                                                            <div class="input-group-addon ">${vo.supplierBase['supplierTypeId'] == "55" ? '<i class="text-require">*  </i>': ''}对应生产厂商</div>
                                                            <div class="form-control form-inline distpicker">
                                                                <div>
                                                                    <div class="form-group col-md-11">
                                                                        <input type="hidden" id="Manufactory${i}" value="${ManufactoryVOList[i]['manufactoryId']}"  name="Manufactory${i}"/>
                                                                        <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class ${vo.supplierBase['supplierTypeId'] == "55" ? '' : ''}" value="${ManufactoryVOList[i]['manufactoryName']}"   id="Manufactory${i}Val" name="Manufactory${i}Val" ${i == 0 ? 'changeApplyFlag="supplierManufactoryVOList"' : ''}  />
                                                                    </div>
                                                                    <div class="form-group btn-box col-md-1">
                                                                        <button type="button" class="btn ${i == 0 ? 'btn_addManufacturer': 'btn_removeManufacturer'} ">
                                                                            <span class="glyphicon ${i == 0 ? 'glyphicon-plus': 'glyphicon-minus'}" aria-hidden="true" ></span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>`;// {validate:{ required :true}}
                                }
                                // $("#manufacturer_row").html(ManufactoryHtml);
                                // ManufactoryVOList.forEach(function (item,index) {
                                //     var _id = 'Manufactory'+index;
                                //     utils.valAutocomplete("/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},_id,
                                //         {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
                                // });
                            }else{
                                // 没有回传值，页面结构不动
                                //结构还原
                                $("#manufacturer_row").html(`<div class="col-md-6 ManufactoryList">
                                    <div class="input-group">
                                        <div class="input-group-addon ">对应生产厂商</div>
                                        <div class="form-control form-inline distpicker">
                                            <div>
                                                <div class="form-group col-md-11">
                                                    <input type="hidden" id="Manufactory" disabled="disabled"  autocomplete="off"  name="Manufactory"/>
                                                    <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class" changeApplyFlag="supplierManufactoryVOList" id="ManufactoryVal" name="ManufactoryVal" disabled="disabled"/>
                                                </div>
                                                <div class="form-group btn-box col-md-1">
                                                    <button type="button" class="btn btn_addManufacturer">
                                                        <span class="glyphicon glyphicon-plus" ></span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>`)
                                // var _id = 'Manufactory';
                                // utils.valAutocomplete("/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},_id,
                                //     {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
                            }


                            //批准文件table初始化
                            initTable3(vo.supplierBase.id,0);
                            //年度报告table初始化
                            initTable4(vo.supplierBase.id,0);
                            // 其他附件
                            initOtherFile(vo.supplierBase.id,0);
                            disable();
                            $("#changeApplyBtn").show();
                            $.clear_changeApply();
                        },
                        error:function(){

                        }
                    });
                }
                disable();
                if(!$("#supplierBaseId").val()){
                    $('#supplierName').removeAttr('readonly').removeAttr('disabled');
                }
            }
        }).showModal();
    }
}

function pushData() {
    //基础属性修改
    var supplierBaseChangeApprovalRecordVO = $("#supplierBaseChangeApprovalRecordVO").serializeToJSON();
    //删除基础属性内的仓库数据，仓库数据单独获取
    delete supplierBaseChangeApprovalRecordVO.repertoryProvince;
    delete supplierBaseChangeApprovalRecordVO.repertoryCity;
    delete supplierBaseChangeApprovalRecordVO.repertoryArea;
    delete supplierBaseChangeApprovalRecordVO.repertoryDetail;
    //主数据id
    supplierBaseChangeApprovalRecordVO.fid = $("#supplierBaseId").val();

    var supplierAllDataVO = {
        supplierBaseChangeApprovalRecordVO: supplierBaseChangeApprovalRecordVO
    };
    //修改后数据拼装
    var changeApplyTemp = JSON.parse(JSON.stringify(window.changeApply));
    var supplierOrganBaseChangeRecordVOList = [];
    $.each(changeApplyTemp,function (c, v) {
        if(c=="supplierRepertoryAddressVOList"){
            // 修改后的仓库集合
            var supplierRepertoryAddressVOList=changeApplyTemp.supplierRepertoryAddressVOList.valueAfter;
            supplierAllDataVO.supplierBaseChangeApprovalRecordVO.supplierRepertoryAddressVOList = supplierRepertoryAddressVOList;
            // 字段变更表删除修改前后字段
            delete changeApplyTemp.supplierRepertoryAddressVOList.valueBefore;
            delete changeApplyTemp.supplierRepertoryAddressVOList.valueAfter;
        }else if(c=="supplierManufactoryVOList"){
            var supplierManufactoryVOList=changeApplyTemp.supplierManufactoryVOList.valueAfter;
            supplierAllDataVO.supplierBaseChangeApprovalRecordVO.supplierManufactoryVOList = supplierManufactoryVOList;
            delete changeApplyTemp.supplierManufactoryVOList.valueBefore;
            delete changeApplyTemp.supplierManufactoryVOList.valueAfter;
        }else if(c=="registerList"){
            // 注册地址修改
            var after = v.valueAfter;
            var before = v.valueBefore;
            var changeStatus = v.changeStatus;
            var registerProvince = {
                "columnValue":"registerProvince",
                "valueAfter":after.registerProvince,
                "changeStatus":changeStatus
            };
            var registerCity = {
                "columnValue":"registerCity",
                "valueAfter":after.registerCity,
                "changeStatus":changeStatus
            };
            var registerArea = {
                "columnValue":"registerArea",
                "valueAfter":after.registerArea,
                "changeStatus":changeStatus
            };
            var registerDetail = {
                "columnValue":"registerDetail",
                "valueAfter":after.registerDetail,
                "changeStatus":changeStatus
            };
            if(before){
                registerProvince.valueBefore=before.registerProvince;
                registerCity.valueBefore=before.registerCity;
                registerArea.valueBefore=before.registerArea;
                registerDetail.valueBefore=before.registerDetail;
            }else{
                registerProvince.valueBefore= $('#province1').val();
                registerCity.valueBefore=$('#registerCity').val();
                registerArea.valueBefore=$('#district1').val();
                registerDetail.valueBefore=$('[name=registerDetail]').val();
            }

            supplierOrganBaseChangeRecordVOList.push(registerProvince);
            supplierOrganBaseChangeRecordVOList.push(registerCity);
            supplierOrganBaseChangeRecordVOList.push(registerArea);
            supplierOrganBaseChangeRecordVOList.push(registerDetail);
            return true;
        }
        supplierOrganBaseChangeRecordVOList.push(v);
    });

    // table等特殊数据
    var changeApplyListTemp = JSON.parse(JSON.stringify(window.changeApplyList));
    // 批准文件 supplierApprovalFileList:table3,
    if(changeApplyListTemp.supplierApprovalFileList){
        var table3 = changeApplyListTemp.supplierApprovalFileList.valueAfter;
        if(changeApplyListTemp.supplierApprovalFileList.changeStatus!=0){
            for(var i=0;i<table3.length;i++){
                delete table3[i].id;
                delete table3[i].scopeofoperationVo;
                //处理经营范围数据
                var scopeList=table3[i].supplierApprovalFileBusinessScopeVOList;
                if(scopeList && scopeList.length > 0){
                    var approvalScopeList=[];
                    var scopeJson={};
                    for(var x=0;x<scopeList.length;x++)
                    {
                        if(scopeList[x].name != '经营范围'){
                            scopeJson={};
                            scopeJson.businessScopeCode=scopeList[x].id;
                            approvalScopeList.push(scopeJson);
                        }
                    }
                    table3[i].supplierApprovalFileBusinessScopeVOList=approvalScopeList;
                }
                if(typeof table3[i].enclosureList == 'string')
                {
                    table3[i].enclosureList=JSON.parse(table3[i].enclosureList);
                }
                for(var j=0;j<table3[i].enclosureList.length;j++)
                {
                    delete table3[i].enclosureList[j].type
                }
            }
            supplierAllDataVO.supplierApprovalFileList=table3;
        }
        var approvalFile = {
            "columnValue":"supplierApprovalFileList",
            "changeStatus":changeApplyListTemp.supplierApprovalFileList.changeStatus
        };
        supplierOrganBaseChangeRecordVOList.push(approvalFile);
    }
    // 年度报告 supplierYearReportList:table4,
    if(changeApplyListTemp.supplierYearReportList){
        var table4 = changeApplyListTemp.supplierYearReportList.valueAfter;
        if(changeApplyListTemp.supplierYearReportList.changeStatus!=0){
            for(var i=0;i<table4.length;i++){
                delete table4[i].id;
                for(var j=0;j<table4[i].enclosureList.length;j++)
                {
                    delete table4[i].enclosureList[j].type
                }
            }
            supplierAllDataVO.supplierYearReportList=table4;
        }
        var yearReport = {
            "columnValue":"supplierYearReportList",
            "changeStatus":changeApplyListTemp.supplierYearReportList.changeStatus
        };
        supplierOrganBaseChangeRecordVOList.push(yearReport);
    }
    // 其他附件
    //supplierOtherFileList:otherFilesArr,
    if(changeApplyListTemp.supplierOtherFileList){
        var supplierOtherFileList = changeApplyListTemp.supplierOtherFileList.valueAfter;
        if(changeApplyListTemp.supplierOtherFileList.changeStatus!=0){
            supplierAllDataVO.supplierOtherFileList=supplierOtherFileList;
        }
        var otherFile = {
            "columnValue":"supplierOtherFileList",
            "changeStatus":changeApplyListTemp.supplierOtherFileList.changeStatus
        };
        supplierOrganBaseChangeRecordVOList.push(otherFile);
    }

    supplierAllDataVO.supplierOrganBaseChangeRecordVOList = supplierOrganBaseChangeRecordVOList;
    console.log(supplierAllDataVO);
    return supplierAllDataVO;

}

//新增的ajax
function insert(supplierAllDataVO){
    var auditStatus = supplierAllDataVO.supplierBaseChangeApprovalRecordVO.auditStatus;
    var successMsg = "保存成功！";
    // 草稿
    if(auditStatus==1){
        successMsg = "恭喜提交审核成功！";
    }
    $.ajax({
        url:'/proxy-supplier/supplier/supplierBaseChangeApproval/baseChangeSave',
        data:JSON.stringify(supplierAllDataVO),
        type:"post",
        dataType:'json',
        contentType: "application/json",

        success:function(data){
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(status!=0){
                if(typeof(taskStatus) != "undefined"){
                    if(taskStatus==true){
                        utils.dialog({content: successMsg, timeout: 2000}).showModal();
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)
                    }else{// 工作流失败
                        utils.dialog({
                            title: "提示",
                            content: data.result.taskMsg + "，已暂时保存为草稿",
                            width: 300,
                            height: 30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                        /*utils.dialog({content: data.result.taskMsg+"，保存为草稿", timeout: 2000}).showModal();*/
                    }
                }else{
                    utils.dialog({content: '保存成功', timeout: 2000}).showModal();
                    setTimeout(function(){
                        utils.closeTab();
                    },2000)
                }
            }else {// 保存失败
                if(data.result.supplierBaseId){
                    checkRepeat(data.result.supplierBaseId,"供应商名称或营业执照已存在，请检查！查看重复供应商/修改？")
                }else{
                    utils.dialog({content: data.result.msg, timeout: 2000}).showModal();
                }
            }
        },
        error:function(){
            utils.dialog({content: '保存失败', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 更新的ajax
function update(supplierAllDataVO,dateFrom){
    var auditStatus = supplierAllDataVO.supplierBaseChangeApprovalRecordVO.auditStatus;
    var successMsg = "保存成功！";
    // 草稿
    if(auditStatus==1){
        successMsg = "恭喜提交审核成功！";
    }
    $.ajax({
        url:'/proxy-supplier/supplier/supplierBaseChangeApproval/updateBaseChangeApproval',
        data:JSON.stringify(supplierAllDataVO),
        type:"post",
        dataType:'json',
        contentType: "application/json",

        success:function(data){
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(status!=0){
                if(typeof(taskStatus) != "undefined"){
                    if(taskStatus==true){
                        utils.dialog({content: successMsg,timeout: 2000}).showModal();
                        /*if(dateFrom=='againAssert'){
                            setTimeout(function(){
                                var  mid = parent.$('#nav-tab li.active a').attr('href').replace('#', '');
                                parent.$('#mainFrameTabs').bTabsClose(mid);
                            },2000)
                        }else{*/
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)

                    }else{// 任务流失败
                        utils.dialog({
                            title: "提示",
                            content: data.result.taskMsg + "，已暂时保存为草稿",
                            width: 300,
                            height: 30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                    }
                }else{
                    utils.dialog({content: '保存成功', timeout: 2000}).showModal();
                    /*if(dateFrom=='againAssert'){
                        setTimeout(function(){
                            var  mid = parent.$('#nav-tab li.active a').attr('href').replace('#', '');
                            parent.$('#mainFrameTabs').bTabsClose(mid);
                        },2000)
                    }else{*/
                    setTimeout(function(){
                        utils.closeTab();
                    },2000)

                }
            }else{// 保存失败
                if(data.result.supplierBaseId){
                    checkRepeat(data.result.supplierBaseId,"供应商名称或营业执照已存在，请检查！查看重复供应商/修改？")
                }else{
                    utils.dialog({content: data.result.msg, timeout: 2000}).showModal();
                }
            }
        },
        error:function(){
            utils.dialog({content: '保存失败', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 通过ajax
function passAjax(){
    var supplierProcessVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":taskId,
        "id":supplierBaseChangeApprovalId
    };
    $.ajax({
        type:"post",
        url: "/proxy-supplier/supplier/supplierBaseChangeApproval/passBaseChangeApproval",
        async : false,
        data:JSON.stringify(supplierProcessVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(taskStatus==true){
                utils.dialog({content: '恭喜审核通过！', timeout: 2000}).showModal();
                setTimeout(function(){
                    utils.closeTab();
                },2000)
            }else {// 任务流失败
                utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '保存失败', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 不通过ajax
function noPassAjax(){
    /*if(!$("#auditOpinion").val()){
        parent.hideLoading();
        utils.dialog({
            content: '审批意见不能为空!',
            quickClose: true,
            timeout: 2000
        }).showModal();
        return false;
    }*/
    var supplierProcessVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":taskId,
        "id":supplierBaseChangeApprovalId
    };
    $.ajax({
        type:"post",
        url: "/proxy-supplier/supplier/supplierBaseChangeApproval/noPassBaseChangeApproval",
        async : false,
        data:JSON.stringify(supplierProcessVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(taskStatus==true){
                utils.dialog({content: '驳回成功！', timeout: 2000}).showModal();
                setTimeout(function(){
                    utils.closeTab();
                },2000)
            }else {// 任务流失败
                utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '驳回失败！', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 撤销
function withdraw(){
    var supplierProcessVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":taskId,
        "id":supplierBaseChangeApprovalId
    };
    $.ajax({
        type:"post",
        url: "/proxy-supplier/supplier/supplierBaseChangeApproval/withdrawBaseChangeApproval",
        async : false,
        data:JSON.stringify(supplierProcessVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(status!=0){
                if(typeof(taskStatus) != "undefined"){
                    if(taskStatus==true){
                        utils.dialog({
                            title: "提示",
                            content: "流程已关闭",
                            width:300,
                            height:30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                    }else{// 任务流启动失败
                        utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
                    }
                }else{
                    utils.dialog({content: '未知错误！', quickClose: true, timeout: 2000}).showModal();
                    setTimeout(function(){
                        utils.closeTab();
                    },2000)
                }
            }else {// 保存失败
                utils.dialog({content: data.result.msg, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '流程关闭失败！', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}

//关闭按钮
$("#closePage").on("click", function () {
    var d =  dialog({
        title: "提示",
        content: "是否保存草稿？",
        width:300,
        height:30,
        okValue: '保存草稿',
        ok: function () {
            $("#saveRowData").click();
            d.close().remove();
            return false;
        },
        button:[
            {
                value:'关闭',
                callback:function(){
                    utils.closeTab();
                }
            }
        ]
    }).showModal();
});
function checkVal() {
    /**
     * RM:
     * 变更前后 对开户银行1 开户名称1 银行账号1 这三项值做判断，只要一项有值就 三项都得有值，如果三项都没值就没值
     * 逻辑：
     * 如果页面加载进来，这三项没有值，在编辑修改后，保存草稿或者提交审核时需要做判断，如果不符合条件弹窗提示
     * 如果页面加载进来，这三项斗殴有值，在编辑修改之后，保存或者提交时，对修改前数据 做有值判断，如果为空提示，
     *                                                                   对修改后数据，做有值判断，如果一个有就都得有
     */
    var flag =0,after_flag,message='';
    var checkVal = ['开户银行1','开户名称1','银行账号1'];
    var bankName1Val = $('input[name=bankName1]').val(),
        accountName1Val = $('input[name=accountName1]').val(),
        bankAccount1Val = $('input[name=bankAccount1]').val();

        if(bankName1Val!='' && accountName1Val!='' && bankAccount1Val!=''){
            flag=1;
        }

        var changAry=[];
        if(changeApply.bankName1){
            if(changeApply.bankName1.valueAfter!=undefined && changeApply.bankName1.valueAfter!=''){
                changAry.push(1);
            }else if(changeApply.bankName1.valueAfter!=undefined && changeApply.bankName1.valueAfter==''){
                changAry.push(0);
            }
        }
        if(changeApply.accountName1){
            if(changeApply.accountName1.valueAfter!=undefined && changeApply.accountName1.valueAfter!=''){
                changAry.push(1);
            }else  if(changeApply.accountName1.valueAfter!=undefined && changeApply.accountName1.valueAfter==''){
                changAry.push(0);
            }
        }
        if(changeApply.bankAccount1){
            if(changeApply.bankAccount1.valueAfter!=undefined && changeApply.bankAccount1.valueAfter!=''){
                changAry.push(1);
            }else if(changeApply.bankAccount1.valueAfter!=undefined && changeApply.bankAccount1.valueAfter==''){
                changAry.push(0);
            }
        }

        for(var i=0;i<changAry.length;i++){
            //判断变更过后本身三个值是否统一
            if(changAry[0]!=changAry[i]){
                checkValdialog('开户银行1、开户户名1、银行账户1，请全部填写或不填写！');
                message='开户银行1、开户户名1、银行账户1，请全部填写或不填写！';
                break;
            }

            //统一内部值
            if(changAry[i]==1){
                after_flag=1;
            }else{
                after_flag=0;
            }
        }

        //判断统一值和变更前统一值，是否一致
        if(after_flag!=undefined && changAry.length<=2 && after_flag!=flag){
            checkValdialog('开户银行1、开户户名1、银行账户1，请全部填写或不填写！');
            message='开户银行1、开户户名1、银行账户1，请全部填写或不填写！';
        }
    return message;
}
//
function checkValdialog(cutInpName) {
    utils.dialog({
        title:'提示',
        content: cutInpName,
        okValue:'确定',
        ok:function () {}
    }).showModal()
}
//保存草稿
$("#saveRowData").on("click",function(){
    // 对开户银行1 开户户名1 银行账号1  做修改后值得判断
    if(checkVal() != ''){
        return false;
    }

    able();
    parent.showLoading({hideTime: 90000});
    if(!$("#supplierBaseId").val()){
        disable();
        utils.dialog({content: '请选择要修改的供应商，才能继续保存', quickClose: true, timeout: 2000}).showModal();
        $('#supplierName').removeAttr('readonly').removeAttr('disabled');
        parent.hideLoading();
    }else{
        // 对应生产厂家   数据提交时拦截
        // if(window.changeApply && window.changeApply['supplierTypeId'] && (window.changeApply['supplierTypeId']['valueAfter'] == '55')){
        //     //  修改后的数据 供应商类别为生产
        //     if(!window.changeApply['supplierManufactoryVOList']){
        //         parent.hideLoading();
        //         disable();
        //         utils.dialog({
        //             title: '提示',
        //             content: '当供应商类别修改为生产时，对应生产厂家必须有值。',
        //             okValue: '确定',
        //             ok: function () {}
        //         }).showModal();
        //         return  false;
        //     }
        // }
        // 状态 审核中
        var supplierAllDataVO = pushData();
        supplierAllDataVO.supplierBaseChangeApprovalRecordVO.auditStatus = 0;
        if(pageType==0){
            // 草稿 新增
            insert(supplierAllDataVO);
        }else if(pageType==1){
            // 草稿再次编辑
            supplierAllDataVO.supplierBaseChangeApprovalRecordVO.id =$("#supplierBaseChangeApprovalId").val();
            supplierAllDataVO.supplierBaseChangeApprovalRecordVO.fid =$("#supplierBaseId").val(); //基础属性id
            update(supplierAllDataVO,"saveRowData");
        }
        disable();
    }
});
//提交审核
$("#submitAssert").on("click",function(){
    // 对开户银行1 开户户名1 银行账号1  做修改后值得判断
    if(checkVal() != ''){
        return false;
    }
    able();
    parent.showLoading({hideTime: 90000});
    if(!$("#supplierBaseId").val()){
        disable();
        utils.dialog({content: '请选择要修改的供应商，才能继续保存', quickClose: true, timeout: 2000}).showModal();
        $('#supplierName').removeAttr('readonly').removeAttr('disabled');
        parent.hideLoading();
    }else{
        // 对应生产厂家   数据提交时拦截
        // if(window.changeApply && window.changeApply['supplierTypeId'] && (window.changeApply['supplierTypeId']['valueAfter'] == '55')){
        //     //  修改后的数据 供应商类别为生产
        //     if(!window.changeApply['supplierManufactoryVOList']){
        //         parent.hideLoading();
        //         disable();
        //         utils.dialog({
        //             title: '提示',
        //             content: '当供应商类别修改为生产时，对应生产厂家必须有值。',
        //             okValue: '确定',
        //             ok: function () {}
        //         }).showModal();
        //         return  false;
        //     }
        // }
        // 状态 审核中
        var supplierAllDataVO = pushData();
        if(supplierAllDataVO.supplierOrganBaseChangeRecordVOList.length<1){
            utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
            disable();
            return false;
        }
        supplierAllDataVO.supplierBaseChangeApprovalRecordVO.auditStatus = 1;
        if(pageType==0) {
            // 草稿 新增
            insert(supplierAllDataVO);
        }else if(pageType==1){
            // 草稿再次编辑
            supplierAllDataVO.supplierBaseChangeApprovalRecordVO.id =$("#supplierBaseChangeApprovalId").val();
            supplierAllDataVO.supplierBaseChangeApprovalRecordVO.fid =$("#supplierBaseId").val(); //基础属性id
            update(supplierAllDataVO,"submitAssert");
        }
        disable();
    }
});
// 审核意见
function dialogData(title,status){
    utils.dialog({
        title:title,
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            parent.showLoading({hideTime: 90000});
            if(status=="pass"){
                passAjax();
            }else if(status=="noPass" ){
                if ($("#auditOpinion").val() == "") {
                    parent.hideLoading();
                    utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                noPassAjax();
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}
// 驳回编辑
function dialogAgain(title,content,status){
    utils.dialog({
        title:title,
        width:300,
        height:30,
        content: content,
        okValue: '确定',
        ok: function () {
            parent.showLoading({hideTime: 90000});
            if(status=="pass"){
                // 对应生产厂商   数据提交时拦截
                if(window.changeApply && window.changeApply['supplierTypeId']){
                    if(window.changeApply['supplierTypeId']['valueAfter'] == '55'){
                        //  修改后的数据 供应商类别为生产
                        // if(!window.changeApply['supplierManufactoryVOList']){
                        //     parent.hideLoading();
                        //     disable();
                        //     utils.dialog({
                        //         title: '提示',
                        //         content: '当供应商类别修改为生产时，对应生产厂商必须有值。',
                        //         okValue: '确定',
                        //         ok: function () {}
                        //     }).showModal();
                        //     return  false;
                        // }
                    }else {
                        //非生产类型，手动清空对应生产厂家
                        var ManufactoryList =  $('#manufacturer_row').find('.ManufactoryList'),ManufactoryListArr=[];
                        if(ManufactoryList.length > 0){
                            $(ManufactoryList).each((index,item) => {
                                let hiddenInpVal = $(item).find("input[name^=Manufactory]").val();
                            let inpVal = $(item).find("input[type=text]").val();
                            ManufactoryListArr[index] = {
                                manufactoryId: hiddenInpVal,
                                manufactoryName: inpVal,
                            };
                        });
                        }
                        window.changeApply['supplierManufactoryVOList'] = {
                            changeStatus: "1",
                            columnValue: "supplierManufactoryVOList",
                            valueAfter: [],
                            valueBefore: ManufactoryListArr
                        };
                    }
                }
                able();
                // 状态 审核中
                var supplierAllDataVO = pushData();
                if(supplierAllDataVO.supplierOrganBaseChangeRecordVOList.length<1){
                    utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
                    parent.hideLoading();
                    disable();
                    return false;
                }
                supplierAllDataVO.supplierBaseChangeApprovalRecordVO.auditStatus = 1;
                supplierAllDataVO.supplierBaseChangeApprovalRecordVO.id =$("#supplierBaseChangeApprovalId").val();
                supplierAllDataVO.supplierBaseChangeApprovalRecordVO.fid =$("#supplierBaseId").val(); //基础属性id
                var supplierProcessVO = {
                    "auditOpinion":$("#auditOpinion").val(),
                    "taskId":taskId,
                    "id":supplierBaseChangeApprovalId
                };
                supplierAllDataVO.supplierProcessVO = supplierProcessVO;
                update(supplierAllDataVO,"againAssert");
            }else if(status=="withdraw"){
                withdraw();
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}

//通过
$("#pass").on("click",function(){
    var title = "审核通过";
    var status = "pass";
    $("#container textarea").removeAttr("disabled");
    $('#opinion').hide();
    // 审批意见弹窗
    dialogData(title,status);
});
//不通过
$("#noPass").on("click",function(){
    var title = "审核不通过";
    var status = "noPass";
    $("#container textarea").removeAttr("disabled");
    $('#opinion').show();
    // 审批意见弹窗
    dialogData(title,status);
});
//再次提交
$("#againAssert").on("click",function(){
    var title = "重新提交";
    var contant = "确定重新提交申请？";
    var status = "pass";
    // 再次提交审核弹窗
    dialogAgain(title,contant,status);
});
// 撤销
$("#withdraw").on("click",function(){
    var title = "关闭审核";
    var contant = "确定关闭审核？";
    var status = "withdraw";
    // 撤销弹窗
    dialogAgain(title,contant,status);
});
// 返回
$("#close").on("click",function(){
    utils.closeTab();
});

// 营业执照号和名称重复弹窗
function checkRepeat(supplierBaseId,alertMsg){
    dialog({
        title: "提示",
        content: alertMsg,
        width:300,
        height:30,
        button:[
            {
                value:'查看',
                callback:function(){
                    var url = '/proxy-supplier/supplier/supplierOrganBase/supplierBase/toDetail?id='+supplierBaseId+"&reapPageFlag=Y";
                    utils.openTabs('supplierBaseToDetailReapPageFlag01', '重复营业执照或供应商名称',url, {reload: false});
//                       utils.openTabs("supplierBaseToDetailReapPageFlag01", "重复营业执照或供应商名称 ", url)
//                       parent.openTabs(windowSupplierPage,'重复营业执照或供应商名称','/supplierOrganBase/supplierBase/toDetail?id='+supplierBaseId+"&reapPageFlag=Y");
//                       window.location.href='/supplierOrganBase/supplierBase/toDetail?id='+supplierBaseId;
                }
            }
        ],
        okValue: '修改',
        ok: function () {
        }
    }).showModal();
}
/*$("#changeApplyBtn").on("click",function(){
    setTimeout(function () {
        if($('[name="threeEvidenceAll"]:checked').val()=='1'){
            $('[name="organizationCode"]').parent().find('i').hide();
            $('[name="taxRegistrationCode"]').parent().find('i').hide();
        }
    },100)
});*/


//添加对应生产厂家
$("body").on("click",'.btn_addManufacturer', function () {
    var obj = ManufactoryHTML();
    $("#manufacturer_row").append(obj.html);
    let _id = 'Manufactory'+obj.radomInt;
    utils.valAutocomplete("/proxy-sysmanage/sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},_id,
        {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
});
//删除对应生产厂家
$('body').on('click','.btn_removeManufacturer',function(){
    $(this).parents(".ManufactoryList").remove();
})

// $(function () {
//     $("#manufacturer_row input[type=hidden]").each(function (index,item) {
//         utils.valAutocomplete("/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},$(item).attr('id'),
//             {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
//     });
// })
function ManufactoryHTML(n) {
    let len = n ? n : 1;
    let html = '', obj = {};
    let radomInt = parseInt((Math.random()*100))+parseInt((Math.random()*10));
    let supplierTypeIdVal = $('#supplierTypeIdVal').val();
    for (let i = 0; i < len; i++) {
        html += `<div class="col-md-6 ManufactoryList">
                    <div class="input-group">
                        <div class="input-group-addon ">${supplierTypeIdVal == "生产" ? '<i class="text-require">*  </i>': ''}对应生产厂家</div>
                        <div class="form-control form-inline distpicker">
                            <div>
                                <div class="form-group col-md-11">
                                    <input type="hidden" id="Manufactory${radomInt}"  name="Manufactory${radomInt}"/>
                                    <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class ${supplierTypeIdVal == "生产" ? '' : ''}"  id="Manufactory${radomInt}Val" name="Manufactory${radomInt}Val"  />
                                </div>
                                <div class="form-group btn-box col-md-1">
                                    <button type="button" class="btn btn_removeManufacturer">
                                        <span class="glyphicon glyphicon-minus" aria-hidden="true" ></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;// {validate:{ required :true}}
    }
    obj = {
        'html':html,
        'radomInt':radomInt
    }
    return obj;
}

//
function checkEdit() {
    let _bool = window.changeApply && window.changeApply['supplierTypeId'];
    _bool = _bool ? (window.changeApply['supplierTypeId']['valueAfter'] == '55') : ($('#supplierTypeId').val() == '55');
    return _bool;
}
