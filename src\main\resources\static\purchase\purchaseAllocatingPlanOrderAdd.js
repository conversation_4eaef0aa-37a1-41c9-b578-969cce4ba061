let orgCode = $("[name='orgCode']").val();
let startDate = '';
let editBoole = false;
const emptyData = {
    productSpecification: '',
    productProduceFactory: '',
    productCount: '',
    productContainTaxPrice: '',
    productContainTaxMoney: '',
    remark: '',
    purchaseUserName: '',
    purchaseUserId: '',
    customerAddress: '',
    customerUser: '',
    customerTel: '',
    centralizedPurchaseName: '',
    centralizedPurchaseType: '',
};
$(function () {
    $('div[fold=head]').fold({ sub: 'sub' });
    // 审批流程
    if ($('#processId').val() != null && $('#processId').val() != '') {
        parent.showLoading();
        $.ajax({
            type: 'POST',
            url: '/proxy-purchase/purchase/purchaseRefundProductOrder/queryTotle?processInstaId=' +
                $('#processId').val(),
            async: false,
            error: function () {
                parent.hideLoading();
                utils
                    .dialog({
                        content: '请求查询顶部审批流程图失败！',
                        quickClose: true,
                        timeout: 2000,
                    })
                    .showModal();
            },
            success: function (data) {
                parent.hideLoading();
                if (data.code == 0) {
                    if (data.result != null) {
                        $('.flow').process(data.result);
                    }
                } else {
                    utils
                        .dialog({ content: '服务器错误', quickClose: true, timeout: 2000 })
                        .showModal();
                }
            },
        });
    }

    //跳转到新建机构间调拨计划单页面的来源
    let fromWhere = $('#fromWhere').val();
    let supplierId = $('#supplierId').val();

    const allotPlanNo = utils.getQueryString('allotPlanNo');
    const oldAllotPlanNo = allotPlanNo;
    $('#editShow_div').css('display', allotPlanNo ? 'block' : 'none');
    $('#addShow_div').css('display', allotPlanNo ? 'none' : 'block');

    let XGridOptions = {
        colNames: [
            '',
            '调出机构',
            '调入机构',
            '商品编码',
            '商品名称',
            '商品审批状态',
            '驳回原因',
            'TOP排名',
            '规格',
            '生产厂家',
            '订单属性',
            '调拨数量',
            '调拨单价',
            '含税总金额',
            '行备注',
            '采购员',
            '采购员id',
            '收货地址',
            '收货人',
            '联系电话',
            '库存数量',
            '是否近效期',
            '生产日期',
            '有效期至',
            '价格可调上限百分比',
            '价格是否可更改',
            '客户编码',
            '客户名称',
            '供应商编码',
            '供应商名称',
            '',
        ],
        colModel: [{
            name: 'id',
            index: 'id', //索引。其和后台交互的参数为sidx
            hidden: true,
            hidegrid: true,
        },
        {
            name: 'callOutOrg', //调出机构
            index: 'callOutOrg',
            rowtype: '#callOut_div',
        },
        {
            name: 'callInOrg',
            index: 'callInOrg', //调入机构
            rowtype: '#callIn_div',
        },
        {
            name: 'productCode',
            index: 'productCode', //商品编码
            rowtype: '#productCode_input',
        },

        {
            name: 'productName',
            index: 'productName', //商品名称
        },
        {
            name: 'orderPlanProductStatusShow', // 商品状态
            index: 'orderPlanProductStatusShow',
            formatter: function (data, a, b) {
                console.log(b)
                return b.orderPlanProductStatus

            }
        },
        {
            name: 'rejectionReason', //bohuiyuanyzhuangtai
            index: 'rejectionReason'
        },
        {
            name: 'top',
            index: 'top', //top排名
            // hidegrid: true,
        },
        {
            name: 'productSpecification', //
            index: 'productSpecification',
        },
        {
            name: 'productProduceFactory', //
            index: 'productProduceFactory',
        },
        {
            name: 'centralizedPurchaseName', //是否集采
            index: 'centralizedPurchaseName',
        },
        {
            name: 'productCount', //调拨数量
            index: 'productCount',
            rowtype: '#allotNum_input',
        },
        {
            name: 'productContainTaxPrice', //调拨单价
            index: 'productContainTaxPrice',
            rowtype: '#allotPrice_input',
        },
        {
            name: 'productContainTaxMoney',
            index: 'productContainTaxMoney',
        },
        {
            name: 'remark',
            index: 'remark',
            rowtype: '#remark_input',
        },
        {
            name: 'purchaseUserName', //采购员
            index: 'purchaseUserName',
        },
        {
            name: 'purchaseUserId',
            index: 'purchaseUserId',
            hidden: true,
        },
        {
            name: 'customerAddress', //收货地址
            index: 'customerAddress',
        },
        {
            name: 'customerUser',
            index: 'customerUser',
        },
        {
            name: 'customerTel',
            index: 'customerTel',
        },
        {
            name: 'productStockCur',
            index: 'productStockCur',
            hidden: true,
        },
        {
            name: 'validityDate', //是否近效期购进
            index: 'validityDate',
            rowtype: '#validityDate_select',
        },
        {
            name: 'productProduceDate', //生产日期
            index: 'productProduceDate',
            rowtype: '#productProduceDate_timePicker',
        },
        {
            name: 'productValidityDate', //有效期至
            index: 'productValidityDate',
            rowtype: '#productValidityDate_timePicker',
        },
        {
            name: 'adjustPercentageLimit',
            index: 'adjustPercentageLimit',
            hidden: true,
        },
        {
            name: 'canAdjustPriceChange',
            index: 'canAdjustPriceChange',
            hidden: true,
        },
        {
            name: 'customerCode',
            index: 'customerCode',
            hidden: true,
        },
        {
            name: 'customerName',
            index: 'customerName',
            hidden: true,
        },
        {
            name: 'supplierCode',
            index: 'supplierCode',
            hidden: true,
        },
        {
            name: 'supplierName',
            index: 'supplierName',
            hidden: true,
        },
        {
            name: 'centralizedPurchaseType', //是否集采
            index: 'centralizedPurchaseType',
            hidden: true,
        },
        ],
        key: 'id',
        rowNum: 9999,
        height: 400,
        maxheight: 400,
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        gridComplete: function () {
            if (!allotPlanNo || editBoole) {
                setTimeout(function () {
                    tableAddRow({ tableId: 'X_Table', rowData: {} });
                }, 200);
            } else {
                setBtnState(true);
            }
            seleteValidityDate();
        },
    };
    if (allotPlanNo) {
        XGridOptions['colNames'].splice(1, 0, '行号');
        XGridOptions['colModel'].splice(1, 0, {
            name: 'linenum',
            index: 'linenum',
        });
    } !allotPlanNo
        ?
        (XGridOptions['data'] = []) :
        (XGridOptions['url'] =
            '/proxy-purchase/purchase/allocatingPlanOrder/product/queryPageInfo');
    XGridOptions['postData'] = { allotPlanNo, oldAllotPlanNo, pageSize: 300 };

    $('#X_Table').XGrid(XGridOptions);

    //新增行，不填写数据
    $('#addRow').click(function () {
        let len = $('#X_Table').XGrid('getRowData');
        if (len == 200) {
            utils
                .dialog({
                    content: '商品行不得超过200行.',
                    quickClose: true,
                    timeout: 2000,
                })
                .showModal();
            return false;
        }
        tableAddRow({ tableId: 'X_Table', rowData: {} });
    });
    /**
     * 删除行
     */
    $('#deleteRow').click(function () {
        let selRow = $('#X_Table').XGrid('getSeleRow');
        if (!selRow.length) {
            utils
                .dialog({
                    content: '没有选中任何行！',
                    quickClose: true,
                    timeout: 3000,
                })
                .showModal();
            return false;
        }
        utils
            .dialog({
                title: '提示',
                content: '是否继续执行删除操作？',
                okValue: '确定',
                ok: function () {
                    deleteRow();
                },
                cancelValue: '取消',
                cancel: function () { },
            })
            .showModal();
    });

    // 导入之前检验
    // $("#export").click(function () {
    //     let disableRows = [];
    //     let ttl = $('#X_Table').XGrid('getRowData');
    //     if (ttl) {
    //         if (ttl.length) {
    //             for (let i = 0; i < ttl.length; i++) {
    //                 disableRows.push(ttl[i].id);
    //             }
    //         } else {
    //             disableRows.push(ttl.id);
    //         }
    //     };
    //     let supplier = $("#find_supplier").val();
    //     let arrivalPeriod = $("#arrivalPeriod").val();
    //     if (supplier == "") {
    //         utils.dialog({content: '供应商不能为空！', quickClose: true, timeout: 4000}).showModal();
    //         return false;
    //     } else if (arrivalPeriod == "") {
    //         utils.dialog({content: '供应商货期不能为空！', quickClose: true, timeout: 4000}).showModal();
    //         return false;
    //     }
    //     if ($("#simpleCode").val() == "") {
    //         utils.dialog({content: '供应商的经营范围为空，无法选择商品！', quickClose: true, timeout: 4000}).showModal();
    //         return false;
    //     }
    //     let channelId = $("#channelId").val();
    //     //大客户导入业务类型默认YBM
    //     if ($("#orderType").val() == '1') {
    //         channelId = '1';
    //     }
    //     utils.dialog({
    //         url: '/proxy-purchase/purchase/purchaseOrder/fileupload?supplyArrivalPeriod=' + $("#arrivalPeriod").val() + '&simpleCode=' + $("#simpleCode").val() + '&supplierId=' + $("#supplierId").val() + "&supplierName=" + "&supplierCode=" + $("#allotPlanNo").val() + "&orderType=" + $("#orderType").val() + "&channelId=" + channelId,
    //         title: '批量导入',
    //         width: 600,
    //         height: 500,
    //         onclose: function () {
    //             let data = this.returnValue;
    //             if (data && data.length > 0) {
    //                 let oldData = [].concat($('#X_Table').XGrid('getRowData'));
    //                 $.each(data, function (index, item) {
    //                     /*let flag = oldData.some(function (val,key) {
    //                             return val.productCode === item.productCode
    //                         });
    //                          if(!flag) $('#X_Table').XGrid('addRowData', item);*/
    //                     $.each(oldData, function (key, val) {
    //                         if (val.productCode === item.productCode) {
    //                             $('#X_Table').XGrid('delRowData', val.sort)
    //                         }
    //                     })
    //                     //$('#X_Table').XGrid('addRowData', item);
    //                     tableAddRow({tableId:'X_Table', rowData:item});
    //                 })
    //             }
    //         }
    //     }).showModal();
    // })

    /**
     * 调拨数量
     */
    $('body').on('input', 'input[name=allotNum]', function (e) {
        let _val = $(this).val();
        _val = _val.replace(/[^\d.]/g, '');
        _val = Number(_val + '') === 0 ? '' : Number(_val + '');

        _val = (_val + '').substring(0, 6);
        // if(Number(_val) === 0) {
        //     _val = 1
        // } else {
        //     if(_val.charAt(0) == 0) {
        //         _val = _val.substring(1)
        //     }
        // }
        // if(val > 100) {
        //     val = 100
        // }
        $(this).val(_val);
        let curRowId = $(this).parents('tr').attr('id');
        let curRowData = $('#X_Table').XGrid('getRowData', curRowId);
        $('#X_Table #' + curRowId)
            .find('[row-describedby="productContainTaxMoney"]')
            .text(
                (Number(curRowData['productContainTaxPrice']) * Number(_val)).toFixed(
                    2,
                ),
            );
    });
    /**
     * 调拨数量放大镜
     */
    $('body').on('click', '.allotNumSearchIcon', function () {
        let rowId = $(this).parents('tr').attr('id');
        let curRowData = $('#X_Table').XGrid('getRowData', rowId);
        $.ajax({
            type: 'POST',
            url: '/proxy-purchase/purchase/allocatingPlanOrder/product/getCallOutOrgProductInfo',
            data: {
                callInOrg: curRowData['callInOrg'],
                callOutOrg: curRowData['callOutOrg'],
                channelCode: $('#channelId').val(),
                productCode: curRowData['productCode'],
            },
            beforeSend: () => {
                parent.showLoading();
            },
            success: function (response) {
                let str = '';
                if (response.result != null) {
                    let callOutOrgName = utils.getOptText(
                        $('#' + rowId).find('[row-describedby="callOutOrg"] select'),
                        $('#' + rowId)
                            .find('[row-describedby="callOutOrg"] select')
                            .val(),
                    );
                    let {
                        productCode,
                        productName,
                        productSpecification,
                        productProduceFactory,
                        purchaseUserName,
                        scatteredYn,
                        mediumPackageNumber,
                    } = response.result;
                    let productStockCur = Number(
                        $('#X_Table #' + rowId)
                            .find('[row-describedby="productStockCur"]')
                            .text(),
                    );
                    let adjustPercentageLimit = $('#X_Table #' + rowId)
                        .find('[row-describedby="adjustPercentageLimit"]')
                        .text();
                    var scatteredYns = scatteredYn;
                    var scatteredYnStr = '否';
                    if (scatteredYns == 1) {
                        scatteredYnStr = '是';
                    }
                    str =
                        `<div>
                            <p>调出机构名称: ${callOutOrgName}</p>
                            <p>商品编码: ${productCode}</p>
                            <p>商品名称: ${productName}</p>
                            <p>规格: ${productSpecification}</p>
                            <p>生产厂家: ${productProduceFactory}</p>
                            <p>调出采购员: ${purchaseUserName}</p>
                            <p>可调拨数量上限：${Math.floor(
                            (productStockCur *
                                Number(adjustPercentageLimit)) /
                            100,
                        )}</p>
                            <p>是否拆零: ` +
                        scatteredYnStr +
                        `</p>
                            <p>中包装数: ` +
                        mediumPackageNumber +
                        `</p>
                        </div>`;
                } else {
                    str = response.msg;
                }
                utils
                    .dialog({
                        title: '提示',
                        content: str,
                    })
                    .showModal();
            },
            complete: () => {
                parent.hideLoading();
            },
        });
    });
    /**
     * 调拨单价
     */
    $('body').on('input', 'input[name=allotPrice]', function (e) {
        let v = $(this).val();
        v = v.replace(/[^\d.]/g, '');
        // var reg = /(^[1-9]\d*(\.\d{1,2})?$)|(^0(\.\d{1,6})?$)/
        if (v.split('.').length > 2) {
            let lastIndex = v.lastIndexOf('.');
            v = v.substring(0, lastIndex) + v.substring(lastIndex + 1);
        }
        if (v.indexOf('.') == 0) {
            v = v.replace('.', '');
        }
        if (v.indexOf('0') == 0) {
            if (v.length == 1) {
                // 0000000
                // v = ''
            } else {
                if (v.indexOf('0.') == 0) { } else {
                    v = v.substring(1);
                }
            }
        }
        if (v.indexOf('.') == -1) {
            v = v.substring(0, 6);
        } else {
            let pointIndex = v.indexOf('.');
            let _vv = v.substring(pointIndex);
            let pointVal = v.substring(0, pointIndex) + _vv.substring(0, 7);
            v = pointVal;
        }
        $(this).val(v);
        let curRowId = $(this).parents('tr').attr('id');
        let curRowData = $('#X_Table').XGrid('getRowData', curRowId);
        $('#X_Table #' + curRowId)
            .find('[row-describedby="productContainTaxMoney"]')
            .text((Number(curRowData['productCount']) * Number(v)).toFixed(2));
    });

    /**
     * 保存按钮
     */
    $('#btn_save').on('click', function () {
        let formData = $('#basicInfo').serializeToJSON();
        let rowDataList = $('#X_Table').XGrid('getRowData');
        rowDataList = rowDataList.filter((item) => {
            return item['callOutOrg'] != '';
        });
        if (!$('#centralizedPurchaseType').val()) {
            utils
                .dialog({
                    content: '请先选择订单属性.',
                    quickClose: true,
                    timeout: 2000,
                })
                .showModal();
            return false;
        }
        if (rowDataList.length == 0) {
            utils
                .dialog({ content: '请先录入数据.', quickClose: true, timeout: 2000 })
                .showModal();
            return false;
        }
        // 调出调入机构不能一样
        let checkOrgAry = rowDataList.filter(
            (item) => item['callOutOrg'] == item['callInOrg'],
        );
        if (checkOrgAry.length != 0) {
            utils
                .dialog({
                    title: '提示',
                    content: `商品名称为： <br />${checkOrgAry
                        .map((item) => item['productCode'])
                        .join('<br />')}<br />的调出调入机构不能相同.`,
                    okValue: '确定',
                    ok: () => { },
                })
                .showModal();
            return false;
        }
        // 调拨数量不能大于库存上限
        let stockAry = rowDataList.filter(
            (item) => Number(item['productCount']) > Number(item['productStockCur']),
        );
        if (stockAry.length != 0) {
            utils
                .dialog({
                    title: '提示',
                    content: `商品名称为： <br />${stockAry
                        .map((item) => item['productCode'])
                        .join('<br />')}<br />的调拨数量不能大于库存上限.`,
                    okValue: '确定',
                    ok: () => { },
                })
                .showModal();
            return false;
        }
        rowDataList.forEach((item, index) => {
            item['linenum'] = index + 1;
        });
        formData['orderPriceSum'] = rowDataList
            .reduce(
                (total, item) => total + Number(item['productContainTaxMoney']),
                0,
            )
            .toFixed(2); // 金额合计
        formData['forecastAllotCost'] = (
            rowDataList.reduce(
                (total, item) => total + Number(item['productContainTaxMoney']),
                0,
            ) *
            (Number($('#purchaseOrgRate').val()) / 100)
        ).toFixed(2); // 预估调拨费用  // 调拨费率由2%需变更为0.5%，对应的预估调拨费用为总金额*调拨费率逻辑保持不变；
        let _state = validateTableData(rowDataList);
        if (!_state) {
            parent.showLoading({ hideTime: 99999 });
            console.log('ajax');
            $.ajax({
                url: '/proxy-purchase/purchase/allocatingPlanOrder/save',
                type: 'POST',
                data: {
                    order: JSON.stringify(formData),
                    products: JSON.stringify(rowDataList),
                },
                beforeSend: function () { },
                success: (res) => {
                    if (res.code == 0) {
                        utils
                            .dialog({
                                title: '提示',
                                content: '保存成功',
                                width: 130,
                                okValue: '确定',
                                ok: () => {
                                    utils.closeTab();
                                },
                            })
                            .showModal();
                    } else {
                        utils
                            .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                            .showModal();
                        return false;
                    }
                },
                complete: () => {
                    parent.hideLoading();
                },
            });
        } else {
            console.log('拦截');
            return false;
        }
    });

    /**
     * 编辑按钮
     */
    $('#btn_edit').on('click', function () {
        $('#processId').val('');
        $('#flow_wrap').css('display', 'none');
        $.ajax({
            type: 'get',
            url: '/proxy-purchase/purchase/allocatingPlanOrder/getAllotPlanNo',
            success: (res) => {
                if (res.code == 0) {
                    $('#oldAllotPlanNo').val($('#allotPlanNo').val());
                    $('#allotPlanNo').val(res.result);
                    XGridOptions['postData'].allotPlanNo = res.result;
                }
                $('#editShow_div').css('display', res.code == 0 ? 'none' : 'block');
                $('#addShow_div').css('display', res.code == 0 ? 'block' : 'none');
                setBtnState(false);
                editBoole = true;
            },
            complete: () => { },
        });
    });
    /**
     * 删除按钮
     */
    $('#btn_delete').on('click', function () {
        utils
            .dialog({
                title: '提示',
                content: '确定删除',
                okValue: '确定',
                ok: () => {
                    $.ajax({
                        type: 'post',
                        url: '/proxy-purchase/purchase/allocatingPlanOrder/delete',
                        data: {
                            allotPlanNo: $('#allotPlanNo').val(),
                        },
                        beforeSend: () => {
                            parent.showLoading();
                        },
                        success: (res) => {
                            if (res.code == 0) {
                                utils
                                    .dialog({
                                        title: '提示',
                                        content: '删除成功',
                                        width: 130,
                                        okValue: '确定',
                                        ok: () => {
                                            utils.closeTab();
                                        },
                                    })
                                    .showModal();
                            } else {
                                utils
                                    .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                                    .showModal();
                                return false;
                            }
                        },
                        complete: () => {
                            parent.hideLoading();
                        },
                    });
                },
                cancelValue: '取消',
                cancel: () => { },
            })
            .showModal();
    });

    function invalideValidityDate() {
        var ttl = $('#X_Table').XGrid('getRowData');
        var ttlf = []
        var ttlT = []
        if (ttl.length !== 0) {
            ttl.map(item => {
                if (item.validityDate === 'false') {
                    ttlf.push(item)
                } else {
                    if (!item.productValidityDate || !item.productProduceDate) {
                        ttlT.push(item)
                    }
                }
            })
        }

        if (ttl.length !== 0 && ttl.length === ttlf.length) {
            return { pass: false, code: "1", msg: "请确认本订单无近效期购进的品种，若仓库收货为近效期将直接拒收！" };
        } else if (ttlT.length !== 0) {
            return { pass: false, code: "2", msg: "当商品为近效期购进时，生产日期和有效期至必须填写！！" };
        } else {
            return { pass: true, code: "0", msg: "" };
        }
    }

    /**
     * 提交按钮
     */
    $('#btn_submit').on('click', function () {
        let invalidateOBJ = invalideValidityDate()
        if (invalidateOBJ.pass === false) {
            utils
                .dialog({
                    title: '提示',
                    content: invalidateOBJ.msg,
                    okValue: '确定',
                    ok: function () {
                        if (invalidateOBJ.code === '1') {
                            submitAssertFunc();
                        }
                    },
                    cancelValue: '取消',
                    cancel: function () { },
                })
                .showModal();
            return;
        }
        submitAssertFunc();
    });

    function submitAssertFunc() {
        $.ajax({
            type: 'post',
            url: '/proxy-purchase/purchase/allocatingPlanOrder/submitCheck',
            data: {
                allotPlanNo: $('#allotPlanNo').val(),
            },
            beforeSend: () => {
                parent.showLoading({ hideTime: 99999 });
            },
            success: (res) => {
                if (res.code == 0) {
                    utils
                        .dialog({
                            title: '提示',
                            content: '提交成功',
                            width: 130,
                            okValue: '确定',
                            ok: () => {
                                utils.closeTab();
                            },
                        })
                        .showModal();
                } else {
                    utils
                        .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                        .showModal();
                    return false;
                }
            },
            complete: () => {
                parent.hideLoading();
            },
        });
    }

    $('body').on('change', '#upfile', function () {
        let value = this.value;
        $('.fileName').html(value).attr('title', value);
    });

    //下载模板
    $('#download').click(function () {
        window.open('/proxy-purchase/purchase/allocatingPlanOrder/downloadTemplate');
    });
});

/**
 * 根据页面状态设置编辑状态
 * @param state
 */
function setBtnState(state) {
    $('.channelSearchIcon').css('display', state ? 'none' : 'block');
    if ($('#allotPlanStatus').val() != 2) {
        $('#checkResult').val('');
        $('#checkResult_div').css('display', 'none');
    }
    $('#remark').prop('disabled', state);
    $('#checkResult').prop('disabled', state);
    $('#centralizedPurchaseType').prop('disabled', state);
    const disabledKeys = [
        'callOutOrg',
        'callInOrg',
        'productCode',
        'allotNum',
        'allotPrice',
        'remark',
    ];
    let tableData = $('#X_Table').XGrid('getRowData');
    tableData.forEach((item) => {
        disabledKeys.forEach((ite) => {
            $('#X_Table #' + item['id'])
                .find('[name=' + ite + ']')
                .prop('disabled', state);
        });
    });
    const btns = ['addRow', 'deleteRow', 'bathImportBtn'];
    btns.forEach((item) => $('#' + item).prop('disabled', state));
}
/**
 * 提交的表格数据为空校验
 */
function validateTableData(tableList) {
    let _state = false;
    // 调出机构、调入机构、商品编码、调拨数量
    const keys = [
        'callOutOrg',
        'callInOrg',
        'productCode',
        'productCount',
        'productContainTaxPrice',
    ];
    for (let i = 0; i < keys.length; i++) {
        let emptyArr = tableList.filter((item) => {
            return item[keys[i]] == '' || item[keys[i]] == 0;
        });
        if (emptyArr.length != 0) {
            _state = true;
            let _str = '以下信息不能为空 <br />';
            emptyArr.forEach((item) => {
                _str += `调出机构： ${utils.getOptText(
                    $('#' + item.rowid).find('[row-describedby="callOutOrg"] select'),
                    item.callOutOrg,
                )}<br />
                        调入机构： ${utils.getOptText(
                    $('#' + item.rowid).find(
                        '[row-describedby="callInOrg"] select',
                    ),
                    item.callInOrg,
                )}<br />
                        商品编码： ${item.productCode}<br />
                        调拨数量： ${item.productCount}<br />
                        调拨单价： ${item.productContainTaxPrice}<br />
                        `;
            });
            utils
                .dialog({
                    title: '提示',
                    content: _str,
                    width: 300,
                    okValue: '确定',
                    ok: () => { },
                })
                .showModal();
            break;
        }
    }
    return _state;
}
/**
 * 调出机构 下拉框切换
 */
function callOutOrgChange(el) {
    clearRowData({
        tableId: 'X_Table',
        rowId: $(el).parents('tr').attr('id'),
        key: 'callOutOrg',
    });
    let callInVal = $(el)
        .parents('tr')
        .find('[row-describedby="callInOrg"] select')
        .val();
    $(el)
        .parents('tr')
        .find('[row-describedby="callInOrg"] select')
        .prop('disabled', ($(el).val() != '') != '' ? false : true);
    // $(el).parents('tr').find('[row-describedby="productCode"] input').prop('disabled', $(el).val() != '' && callInVal != '' ? false : true)
    // $(el).parents('tr').find('[row-describedby="productCode"] i').css('display',$(el).val() != '' && callInVal != '' ? 'block' : 'none')
    // setEmptyRow($(el).parents('tr').attr('id'), '', emptyData)
}
/**
 * 调入机构 下拉框切换
 */
function callInOrgChange(el) {
    const callOutVal = $(el)
        .parents('tr')
        .find('[row-describedby="callOutOrg"] select')
        .val();
    if (!callOutVal) {
        utils
            .dialog({
                content: '先选调出机构，才能选择调入机构.',
                quickClose: true,
                timeout: 2000,
            })
            .showModal();
        return false;
    }
    // 获取当前选中的机构
    const callInVal = $(el).find('option:selected').val();
    // // 若机构取值异常，则不做校验并直接执行选中动作
    // if (!selectedOrgId) {
    //     selectedActionLambda()
    //     return
    // }
    let selectedActionLambda = () => {
        clearRowData({
            tableId: 'X_Table',
            rowId: $(el).parents('tr').attr('id'),
            key: 'callInOrg',
        });
        // 记录当前选中的值，以便业务异常时快速回溯。
        $(el).attr('oldValue', $(el).val());
    };
    $.ajax({
        url: '/proxy-purchase/purchase/supplier/getSuppliserValidityForOrg?outOrgCode=' +
            callOutVal +
            '&inOrgCode=' +
            callInVal,
        async: false,
        error: function () { },
        success: function (data) {
            if (data.code == -1) {
                // 近效期提示
                utils
                    .dialog({
                        title: '提示',
                        content: data.msg,
                        okValue: '确定选择',
                        ok: function () {
                            selectedActionLambda();
                        },
                        cancelValue: '重新选择',
                        cancel: function () {
                            // 点击取消时，将下拉框恢复为本次操作前的原始值
                            $(el).val($(el).attr('oldValue'));
                        },
                    })
                    .showModal();
            } else {
                selectedActionLambda();
            }
        },
    });
}

/**
 * 表格行清空
 * @param params
 */
function clearRowData(params) {
    let clearRowData = {
        productCode: '',
        productName: '',
        productSpecification: '',
        productProduceFactory: '',
        centralizedPurchaseType: '',
        productCount: '',
        productContainTaxPrice: '',
        productContainTaxMoney: '',
        remark: '',
        purchaseUserName: '',
        purchaseUserId: '',
        customerAddress: '',
        customerUser: '',
        customerTel: '',
        centralizedPurchaseName: '',
        centralizedPurchaseType: '',
    };
    if (params['key']) {
        params['key'] == 'callOutOrg' ? (clearRowData['callInOrg'] = '') : null;
    }
    $('#' + params.tableId).XGrid('setRowData', params.rowId, clearRowData);
    let rowData = $('#' + params.tableId).XGrid('getRowData', params.rowId);
    if (rowData['callInOrg'] == '' || rowData['callOutOrg'] == '') {
        $('#' + params.tableId + ' #' + params.rowId)
            .find('[row-describedby="productCode"] input')
            .prop('disabled', true);
        $('#' + params.tableId + ' #' + params.rowId)
            .find('[row-describedby="productCode"] i')
            .css('display', 'none');
    } else {
        $('#' + params.tableId + ' #' + params.rowId)
            .find('[row-describedby="productCode"] input')
            .prop('disabled', false);
        $('#' + params.tableId + ' #' + params.rowId)
            .find('[row-describedby="productCode"] i')
            .css('display', 'block');
    }
    productName_Autocomplete('X_Table', {}, params.rowId);
}

function seleteValidityDate() {
    let productList = $('#X_Table').XGrid('getRowData');
    productList.forEach((item) => {
        //商品标红
        if (item.validityDate === 'true') {
            $('#X_Table  #' + item.rowid).css('background', 'rgba(255,255,0,1)'); //命中,行标黄
        } else {
            $('#X_Table  #' + item.rowid).removeAttr('style'); //去掉标红
        }
    });
}



/**
 * 修改当前行
 * @param sort
 * @param callback
 * @param rowData
 * @returns {boolean}
 */
function setEmptyRow(sort, callback, rowData) {
    if (rowData) {
        setRow(rowData, sort);
    } else {
        let curRowData = $('#X_Table').XGrid('getRowData', sort);
        if (Array.isArray(curRowData)) {
            curRowData = curRowData[0];
        }
        if (curRowData['callOutOrg'] == '' || curRowData['callInOrg'] == '') {
            utils
                .dialog({
                    content: '需先选择调出机构、调入机构，才允许选择商品',
                    quickClose: true,
                    timeout: 2000,
                })
                .showModal();
            return false;
        } else {
            // productDialogShow({sort},function (_data) {
            //     console.log('_data', _data);
            // })
        }
    }
}

function setRow(data, sort) {
    let oldData = $('#X_Table').XGrid('getRowData');
    let disableRows = $.map(oldData, function (item, key) {
        return item.id;
    });
    let repeatAry = [], //重复商品
        firstRow = true; //第一个更改，之后的追加
    data = [].concat(data);
    $.each(data, function (index, val) {
        let lengthFlag = repeatAry.length;
        $.each(oldData, function (key, item) {
            if (
                item.productCode &&
                val.productCode === item.productCode &&
                val.callInOrg === item.callInOrg &&
                val.callOutOrg === item.callOutOrg
            ) {
                repeatAry.push(val);
            }
        });
        if (lengthFlag === repeatAry.length) {
            if (firstRow) {
                firstRow = false;
                tableAddRow({ tableId: 'X_Table', rowData: val, rowId: sort },
                    function (rd) {
                        if (rd && rd.callInOrg) {
                            $('#' + sort)
                                .find('[row-describedby="callInOrg"] select')
                                .val(rd.callInOrg);
                            $('#' + sort)
                                .find('[row-describedby="callInOrg"] select')
                                .prop('disabled', false);
                            $('#' + sort)
                                .find('[row-describedby="productCode"] input')
                                .prop('disabled', false);
                            $('#' + sort)
                                .find('[row-describedby="productCode"] i')
                                .css('display', 'block');
                            $('#' + sort)
                                .find('[row-describedby="productContainTaxPrice"] input')
                                .prop(
                                    'disabled',
                                    rd['canAdjustPriceChange'] == 1 ? false : true,
                                );
                            if (rd['productContainTaxPrice']) {
                                $('#' + sort)
                                    .find('[row-describedby="productContainTaxPrice"] input')
                                    .val(rd['productContainTaxPrice']);
                            }
                        }
                    },
                );
            } else {
                tableAddRow({ tableId: 'X_Table', rowData: val });
            }
        }
    });
}

/**
 * 本地新增行
 * @param tableId
 * @param rowData
 * @param rowId
 * @param cb
 */
function tableAddRow(params, cb) {
    // tableId, rowData, rowId,
    if (params['rowId']) {
        params['rowData']['linenum'] = $(
            '#' + params['tableId'] + ' #' + params['rowId'],
        )
            .find('[row-describedby="linenum"]')
            .text();
        $('#' + params['tableId']).XGrid(
            'setRowData',
            params['rowId'],
            params['rowData'],
        );
    } else {
        let oldData = $('#' + params['tableId']).XGrid('getRowData');
        let sortAry = $.map(oldData, function (item, index) {
            return item.rowid;
        });
        let linenumAry = $.map(oldData, function (item, index) {
            return item.linenum;
        });
        params['rowData']['id'] =
            oldData.length > 0 ?
                (Math.max.apply(null, sortAry.length ? sortAry : [0]) + 1).toString() :
                '1';
        params['rowData']['linenum'] =
            oldData.length > 0 ?
                (
                    Math.max.apply(null, linenumAry.length ? linenumAry : [0]) + 1
                ).toString() :
                '1';
        let lastData = oldData[oldData.length - 1];
        if (!lastData || lastData.productCode) {
            $('#' + params['tableId']).XGrid('addRowData', params['rowData']);
        } else {
            params['rowData']['id'] = (Math.max.apply(null, sortAry) + 1).toString();
            $('#' + params['tableId']).XGrid(
                'addRowData',
                params['rowData'],
                lastData.rowid,
            );
        }
    }
    cb && cb(params['rowData']);
    productName_Autocomplete('X_Table', params['rowData'], params['rowId']);
}

function isNotNull(value) {
    if (value != '' && value != null && value != undefined && !isNaN(value)) {
        return true;
    }
    return false;
}

function delRepeat(dataList) {
    let temp = {},
        len = dataList.length;
    for (let i = 0; i < len; i++) {
        let tmp = dataList[i].productCode;
        if (!temp.hasOwnProperty(tmp)) {
            //hasOwnProperty用来判断一个对象是否有你给出名称的属性或对象
            temp[tmp] = dataList[i];
        }
    }
    len = 0;
    let tempArr = [];
    for (let i in temp) {
        tempArr[len++] = temp[i];
    }
    return tempArr;
}

function deleteRow() {
    let selRow = $('#X_Table').XGrid('getSeleRow');
    if (selRow.length) {
        $.each(selRow, function (index, item) {
            $('#X_Table').XGrid('delRowData', item.id);
        });
        if (!$('#X_Table').XGrid('getRowData').length) {
            tableAddRow({ tableId: 'X_Table', rowData: {} });
        }
    } else {
        utils
            .dialog({ content: '没有选中任何行！', quickClose: true, timeout: 3000 })
            .showModal();
    }
}

let ret = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;

//切换是否集采
function seletePurchaseType(that) {
    change_channelID('', '1');
}
//表格内商品模糊搜索
function productName_Autocomplete(tableId, rowData, rowId) {
    if (!rowData) {
        rowData = $('#' + tableId).XGrid('getRowData');
    }
    if (Array.isArray(rowData)) {
        rowData = rowData[rowData.length - 1];
    }
    // rowId = (rowData.id ? rowData.id : $('#' + tableId).XGrid('getRowData')[0]['rowid']);
    rowId = rowId ? rowId : $('#' + tableId).XGrid('getRowData')[0]['rowid'];
    let $input = $(
        '#' +
        tableId +
        ' tr#' +
        (rowData.id ? rowData.id : rowId) +
        ' input[name=productCode]',
    );

    let currentRowCallInOrg = $('#' + rowId)
        .find('[row-describedby="callInOrg"] select')
        .val();
    let currentRowCallOutOrg = $('#' + rowId)
        .find('[row-describedby="callOutOrg"] select')
        .val();

    // let callInOrgName = utils.getOptText($('#'+rowId).find('[row-describedby="callInOrg"] select'), $('#'+rowId).find('[row-describedby="callInOrg"] select').val())
    let queryStr = `callInOrg=${$('#' + rowId)
        .find('[row-describedby="callInOrg"] select')
        .val()}&callOutOrg=${$('#' + rowId)
            .find('[row-describedby="callOutOrg"] select')
            .val()}&channelCode=${$('#channelId').val()}&centralizedPurchaseType=${$(
                '#centralizedPurchaseType',
            ).val()}`;
    $input.Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/allocatingPlanOrder/product/findProductCode?' + queryStr,
        paramName: 'productCode', //查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        autoSelectFirst: true,
        transformResult: function (response) {
            console.log('response', response);
            response = response.result.list;
            return {
                suggestions: $.map(response, function (dataItem) {
                    return { value: dataItem.productName, data: dataItem.productCode };
                }),
            };
        },
        onSelect: function (result) {
            console.log('result', result);

            let code = result.data,
                val = result.value;
            let tableDatas = $('#' + tableId).XGrid('getRowData');
            let repeatAry = tableDatas.filter(
                (item) =>
                    item['productCode'] == code &&
                    item['callInOrg'] == currentRowCallInOrg &&
                    item['callOutOrg'] == currentRowCallOutOrg,
            );
            if (repeatAry.length != 0) {
                utils
                    .dialog({
                        content: '同一个调入调出 商品编码不得重复',
                        quickClose: true,
                        timeout: 2000,
                    })
                    .showModal();
                $input.val('');
                return false;
            }
            let rowId = $input.parents('tr').attr('id');
            $('tr#' + rowId + ' td[row-describedby=productName] input').attr(
                'oldvalue',
                val,
            );
            setEmptyRow(rowId, '', emptyData);
            parent.showLoading({ hideTime: 99999 });
            let baseData = {
                callInOrg: $('#' + rowId)
                    .find('[row-describedby="callInOrg"] select')
                    .val(),
                callInOrgName: utils.getOptText(
                    $('#' + rowId).find('[row-describedby="callInOrg"] select'),
                    $('#' + rowId)
                        .find('[row-describedby="callInOrg"] select')
                        .val(),
                ),
                callOutOrg: $('#' + rowId)
                    .find('[row-describedby="callOutOrg"] select')
                    .val(),
                callOutOrgName: utils.getOptText(
                    $('#' + rowId).find('[row-describedby="callOutOrg"] select'),
                    $('#' + rowId)
                        .find('[row-describedby="callOutOrg"] select')
                        .val(),
                ),
                centralizedPurchaseType: $('#centralizedPurchaseType').val(),
            };
            $.ajax({
                type: 'POST',
                url: '/proxy-purchase/purchase/allocatingPlanOrder/product/getProductInfo',
                data: {
                    ...baseData,
                    channelCode: $('#channelId').val(),
                    productCode: code,
                },
                success: function (res) {
                    console.log('res', res);
                    if (res.code == '0') {
                        if (res.result != null) {
                            res.result = Object.assign(res.result, baseData);

                            if (res.result.enclosureValidityType === 2) {
                                utils
                                    .dialog({
                                        content: res.result.enclosureValidityMsg,
                                        quickClose: true,
                                        timeout: 2000,
                                    })
                                    .showModal();
                                if (
                                    $('tr#' + rowId)
                                        .find('td[row-describedby="ck"] input[type="checkbox"]')
                                        .prop('checked')
                                ) {
                                    $('tr#' + rowId).trigger('click');
                                }
                                return;
                            }
                            if (res.result.enclosureValidityType === 1) {
                                utils
                                    .dialog({
                                        title: '提示',
                                        content: res.result.enclosureValidityMsg,
                                        okValue: '确定',
                                        ok: function () {
                                            setEmptyRow(
                                                rowId,
                                                function () {
                                                    $(
                                                        'tr#' +
                                                        rowId +
                                                        ' td[row-describedby=productName] input',
                                                    ).attr('oldvalue', val);
                                                    $(
                                                        'tr#' +
                                                        rowId +
                                                        ' td[row-describedby=productCount] input',
                                                    ).focus();
                                                },
                                                res.result,
                                            );
                                        },
                                        cancelValue: '取消',
                                        cancel: function () {
                                            if (
                                                $('tr#' + rowId)
                                                    .find(
                                                        'td[row-describedby="ck"] input[type="checkbox"]',
                                                    )
                                                    .prop('checked')
                                            ) {
                                                $('tr#' + rowId).trigger('click');
                                            }
                                        },
                                    })
                                    .showModal();
                                return;
                            }

                            setEmptyRow(
                                rowId,
                                function () {
                                    $(
                                        'tr#' + rowId + ' td[row-describedby=productName] input',
                                    ).attr('oldvalue', val);
                                    $(
                                        'tr#' + rowId + ' td[row-describedby=productCount] input',
                                    ).focus();
                                },
                                res.result,
                            );
                        } else {
                            utils
                                .dialog({
                                    //title:'提示',
                                    content: '未找到商品信息',
                                    okValue: '确认',
                                    ok: function () { },
                                })
                                .showModal();
                        }
                    } else {
                        utils
                            .dialog({
                                title: '提示',
                                content: `<div style="max-width: 200px; max-height: 200px; overflow: auto; overflow-x: hidden;">${res.msg}</div>`,
                                okValue: '确认',
                                ok: function () { },
                            })
                            .showModal();
                    }
                },
                complete: () => {
                    parent.hideLoading();
                },
            });
        },
        onSearchStart: function (query) {
            console.log('查询参数', query);
            let curRowData = $('#' + tableId).XGrid('getRowData', rowId);
            if (Array.isArray(curRowData)) {
                curRowData = curRowData[0];
            }
            if (curRowData['callOutOrg'] == '' || curRowData['callInOrg'] == '') {
                utils
                    .dialog({
                        content: '需先选择调出机构、调入机构，才允许选择商品',
                        quickClose: true,
                        timeout: 2000,
                    })
                    .showModal();
                return false;
            }
        },
        onNoneSelect: function (params, suggestions) {
            $input.val('');
        },
        onSearchComplete: function (query, suggestions) {
            parent.hideLoading();
            let $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val());
            }
            if (!$ele.is(':focus')) {
                $ele.Autocomplete('hide');
            }
        },
    });
    $input.on({
        dblclick: function (e) {
            let rowId = $(this).parents('tr').attr('id');
            setEmptyRow(rowId);
        },
    });
}

let _dialog = null;
// 批量录入
function batchImport() {
    _dialog = utils
        .dialog({
            title: '批量生单',
            content: `<div id="import_dialog">
                    <div class="upFileBox">
                        <form id= "uploadForm">
                            <span class="fileName"></span>
                            <div class="fileBox">
                                <input type="file" id="upfile" name="upfile" class="file" accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                                <input type="button" value="选择文件" class="btn btn-info" />
                            </div>
                            <input type="button" value="上&nbsp;&nbsp;传" class="btn btn-info" onclick="doUpload()" style="margin: -37px 11px 0px 0px;"/>
                            <input type="reset" value="" style="display: none;"  />
                        </form>
                    </div>
                    <form id="saveForm"><input type="hidden" id="fileName" name="fileName"></form>
                </div>`,
            width: 420,
            height: 300,
            onclose: function (data) {
                $('#SearchBtn').trigger('click');
                $('.fileName').attr('title', '').text('');
                document.getElementById('uploadForm') &&
                    document.getElementById('uploadForm').reset();
                _dialog.close().remove();
            },
        })
        .showModal();
    document.getElementById('uploadForm') &&
        document.getElementById('uploadForm').reset();
}

//业务类型搜索图标
$(document).on('click', '.channelSearchIcon', function () {
    $(this).siblings('input').trigger('dblclick');
});

//业务类型 输入框双击 弹出业务类型列表
$('#channelId_inp').dblclick(function () {
    let baseVal = $('#channelId_inp').val();
    utils
        .channelDialog('0')
        .then((res) => {
            console.log(res);
            let _str_name = '',
                _str_code = '',
                _str_val = '';
            let _str_arr = res.map((item) => {
                return item.channelName;
            });
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map((item) => {
                return item.channelCode;
            });
            _str_code = _str_code_arr.join(',');

            let _str_val_arr = res.map((item) => {
                return item.channelValue;
            });
            _str_val = _str_val_arr.join(',');
            $('#channelId_inp').val(_str_val);
            $('#channelId').val(_str_code);
            change_channelID(baseVal, _str_code);
            startLoaderSelete(res);
        })
        .catch(() => { });
});

function startLoaderSelete(vals) {
    var _html = '';
    if (!vals || vals.length <= 0) {
        return false;
    }
    $.ajax({
        url: '/proxy-purchase/purchase/orderAttribute/dict/queryListByType',
        type: 'GET',
        data: { type: vals[0].parentCode, groupStatus: 1 },
        beforeSend: function () {
            console.log('正在进行，请稍候');
            parent.showLoading({ hideTime: 99999 });
        },
        success: function (data) {
            if (data && data.code == 0 && data.result && data.result.length > 0) {
                $('#centralizedPurchaseType').empty();
                for (var i = 0; i < data.result.length; i++) {
                    _html =
                        _html +
                        '<option value="' +
                        data.result[i].code +
                        '">' +
                        data.result[i].name +
                        '</option>';
                }
                $('#centralizedPurchaseType').append(_html);
            }
        },
        error: function () {
            utils
                .dialog({ content: '请求失败', quickClose: true, timeout: 2000 })
                .showModal();
            parent.hideLoading();
        },
        complete: function () {
            parent.hideLoading();
        },
    });
}

function change_channelID(baseVal, newVal) {
    if (baseVal != newVal) {
        if (editBoole) {
            $('#X_Table')
                .setGridParam({
                    data: [],
                    postData: {
                        pageSize: 300,
                        oldAllotPlanNo: $('#oldAllotPlanNo').val(),
                        allotPlanNo: $('#allotPlanNo').val(),
                    },
                })
                .trigger('reloadGrid');
        } else {
            $('#X_Table')
                .setGridParam({
                    data: [],
                })
                .trigger('reloadGrid');
        }
    }
}

let fileName = '';

function doUpload() {
    let name = $('#upfile').val();
    if (name == '') {
        pageSize;
        utils
            .dialog({
                title: '提示',
                width: 200,
                content: '请选择文件',
                okValue: '确定',
                ok: function () { },
            })
            .showModal();
        return false;
    }
    if (fileName != '' && fileName == name) {
        //重复导入一个文件
        utils
            .dialog({
                title: '提示',
                width: 200,
                content: '该文件已导入过，请确认是否再次导入？',
                okValue: '确定',
                ok: function () {
                    uploadFile({
                        file: $('#upfile')[0].files[0],
                        channelCode: $('#channelId').val(),
                    });
                },
            })
            .showModal();
        return false;
    }
    fileName = name;
    uploadFile({
        file: $('#upfile')[0].files[0],
        channelCode: $('#channelId').val(),
    });
}

function uploadFile({ file, channelCode }) {
    let formData = new FormData();
    formData.append('file', file);
    formData.append('channelCode ', channelCode);
    formData.append(
        'centralizedPurchaseType ',
        $('#centralizedPurchaseType').val(),
    );

    $.ajax({
        url: '/proxy-purchase/purchase/allocatingPlanOrder/exportTemplate',
        type: 'POST',
        async: false,
        data: formData,
        processData: false,
        contentType: false,
        beforeSend: function () {
            console.log('正在进行，请稍候');
            parent.showLoading({ hideTime: 99999 });
        },
        success: function (data) {
            if (data.code == 0) {
                let { result: list } = data;
                _dialog.close().remove();
                _dialog = null;

                let baseRowData = $('#X_Table').XGrid('getRowData');
                if (!(baseRowData.length == 1 && baseRowData[0]['callOutOrg'] == '')) {
                    list = baseRowData.concat(list);
                }
                $('#X_Table').XGrid('clearGridData');
                list.forEach((item) => {
                    item.productContainTaxMoney = (
                        Number(item.productContainTaxPrice) * Number(item.productCount)
                    ).toFixed(2);
                    tableAddRow({ tableId: 'X_Table', rowData: item });
                });
                const keys = ['callInOrg', 'productCode'];
                let _tableData = $('#X_Table').XGrid('getRowData');
                _tableData.forEach((item) => {
                    keys.forEach((ite) => {
                        $('#X_Table #' + item['id'])
                            .find('[name=' + ite + ']')
                            .prop('disabled', false);
                    });
                });
            } else {
                utils
                    .dialog({
                        title: '提示',
                        content: data.msg,
                        okValue: '确定',
                        ok: () => { },
                    })
                    .showModal();
                return;
            }
        },
        error: function () {
            utils
                .dialog({ content: '上传失败', quickClose: true, timeout: 2000 })
                .showModal();
        },
        complete: function () {
            parent.hideLoading();
            $('input[type=reset]').trigger('click');
            $('span.fileName').text('');
        },
    });
}
//近效期计算器
$('#dateComputed').click(function () {
    utils.timeDialog();
});