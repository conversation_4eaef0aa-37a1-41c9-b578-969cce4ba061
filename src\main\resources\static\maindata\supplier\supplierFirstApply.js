var idArr = []; //批准文件经营范围已选中存放数组
var windowSupplierPage = new Date().getTime();
var settleMultipleDateList = {};

let baseid = $('#supplierOrganBaseId').val();
if (baseid) {
  if (
    window.location.href.indexOf(
      'supplierOrganBase/firstSupplierOrganBase/toInsert',
    ) === -1 &&
    window.location.href.indexOf('firstSupplierOrganBase/detail/first') === -1
  ) {
    apiORGDetail();
  } else if (
    window.location.href.indexOf(
      'supplierOrganBase/firstSupplierOrganBase/detail' !== -1,
    ) &&
    window.location.href.indexOf('taskId') !== -1 &&
    window.location.href.indexOf(
      'supplierOrganBase/firstSupplierOrganBase/detail/first',
    ) === -1
  ) {
    apiORGDetail();
  } else {
    apiGetFirstOperate();
  }
}
//  生产/经营范围最多为200字
$('#scopes').on('input propertychange', function () {
  var $this = $(this),
    _val = $this.val($this.val().replace(/&/g, '')),
    count = '';
  if (_val.length > 200) {
    $this.val(_val.substring(0, 200));
    utils
      .dialog({
        content: '生产/经营范围最多输入200字！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
  }
});

//注册地址省
$('#province1').on('change', function () {
  var a = $('select[name="registerProvince"]')
    .find('option[value="' + $('select[name="registerProvince"]').val() + '"]')
    .html();
  $('#registerProvinceName').val(a);
});
//注册地址市
$('#city1').on('change', function () {
  var a = $('select[name="registerCity"]')
    .find('option[value="' + $('select[name="registerCity"]').val() + '"]')
    .html();
  $('#registerCityName').val(a);
});

//基础属性-是否三证合一     初始化rodio是
// 2020-07-14
// setRadioChecked('threeEvidenceAll', '1');
// var threeEvidenceAll = $("input[name='threeEvidenceAll']:checked").val();
// if ("1" == threeEvidenceAll) {
//     $(".organizationCode").attr("readonly", true)//组织机构代码号
//     $(".taxRegistrationCode").attr("readonly", true)//税务登记证号
// }
// 2020-07-14
//基础属性-是否停用   初始化rodio否
setRadioChecked('disableStateBase', '0');
//运营属性-是否停用  初始化rodio否
setRadioChecked('disableState', '0');

//是否三证合一 变化
// 2020-07-14
// $(".threeEvidenceAll").on("change", function () {
//     var $this = $(this);
//     var threeEvidenceAll = $this.val();
//     if ("1" == threeEvidenceAll) {//是三证合一
//         var supplierBusinessNum = $("#supplierBusinessNum").val();
//         $(".organizationCode").attr("readonly", true)//组织机构代码号
//         $(".taxRegistrationCode").attr("readonly", true)//税务登记证号
//         $(".organizationCode").val(supplierBusinessNum)//组织机构代码号
//         $(".taxRegistrationCode").val(supplierBusinessNum)//税务登记证号
//     } else {
//         $(".organizationCode").attr("readonly", false)//组织机构代码号
//         $(".taxRegistrationCode").attr("readonly", false)//税务登记证号
//         $(".organizationCode").val("")//组织机构代码号
//         $(".taxRegistrationCode").val("")//税务登记证号
//     }
// });
//营业执照发生变化
// 2020-07-14
// $("#supplierBusinessNum").on("change", function () {
//     var threeEvidenceAll = $('input:radio[name="threeEvidenceAll"]:checked').val();
//     var supplierBusinessNum = $("#supplierBusinessNum").val();
//     if ("1" == threeEvidenceAll) {
//         $(".organizationCode").val(supplierBusinessNum)//组织机构代码号
//         $(".taxRegistrationCode").val(supplierBusinessNum)//税务登记证号
//     } else {
//         $(".organizationCode").val("")//组织机构代码号
//         $(".taxRegistrationCode").val("")//税务登记证号
//     }
// });
// 2020-07-14
//批准文件证书类型设置不可重复选择
$('#table3').on('mousedown', 'select[name="certificateId"]', function () {
  /**
   * RM 2018-10-11
   * 批准文件 证书类型 选中的不再被禁用
   */
  //selCannotRepeatChoice(this, 'table3', 'certificateId');
});

//初始化5个tab页
var supplierBaseId = $('#supplierBaseId').val(); //基础属性id
var supplierOrganBaseId = $('#supplierOrganBaseId').val(); //运营属性id
var processId = $('#processId').val(); //流程实例id
var pageProperty = $('#pageProperty').val();
var addReturnPageFlag = $('#addReturnPageFlag').val(); //标识是首营申请入口还是点击选择商品的新增入口
var dbPage = $('#dbPage').val(); //编辑页面是否从待办入口进入
//基础属性对应的tab页
if (supplierBaseId && '' != supplierBaseId) {
  /* 批件下沉开始
    * initTable4(supplierBaseId, 0);//年度报告
    initTable3(supplierBaseId, 0);//批准文件
    initOtherFile(supplierBaseId, 0);//其他附件
      批件下沉结束
  */
  if ('insert' == pageProperty && '' != addReturnPageFlag) {
    //引用新增页面
    //初始化工作流
    initWorkFlow('supplierFirstTwo', '');
  } else {
    var fromDbDetailPage = $('#fromDbDetailPage').val();
    var firstDetailPage = $('#firstDetailPage').val();
    var auditStatus = $('#auditStatus').val();
    if ('edit' == pageProperty && dbPage) {
      //待办编辑页面进入
      initWorkFlow('', processId);
    } else if ('detail' == pageProperty && fromDbDetailPage) {
      //待办详情页面进入
      initWorkFlow('', processId);
    } else if ('edit' == pageProperty && !dbPage) {
      //首营申请编辑页面
      var useSupplier = $('#useSupplier').val();
      //判断是否引用主数据
      if ('Y' == useSupplier) {
        //是
        initWorkFlow('supplierFirstTwo', '');
      } else {
        //否
        initWorkFlow('supplierFirstOne', '');
      }
    } else if ('detail' == pageProperty && firstDetailPage) {
      //供应商首营申请详情页
      initWorkFlow('', processId);
    }
  }
} else {
  if ('insert' == pageProperty) {
    //全量新增页面
    //初始化工作流
    initWorkFlow('supplierFirstOne', '');
  }
}
//首营快照页面
var firstOrOrganFlag = $('#firstOrOrganFlag').val();

//运行属性对应tab页面
if (supplierOrganBaseId && '' != supplierOrganBaseId) {
  initTable1(supplierOrganBaseId, 1); //质量保证协议
  initTable2(supplierOrganBaseId, 1); //客户委托书
  initTable4(supplierOrganBaseId, 0); //年度报告
  initTable3(supplierOrganBaseId, 0, null); //批准文件
  initOtherFile(supplierOrganBaseId, 0); //其他附件
} else {
  if ('firstApplySnap' != firstOrOrganFlag) {
    //		initTable3(null, 0);//批准文件
  }
}

if ('firstApplySnap' == firstOrOrganFlag) {
  var snapShotId = $('#snapShotId').val();
  initTable1(snapShotId, 4); //质量保证协议
  initTable2(snapShotId, 4); //客户委托书
  initTable4(snapShotId, 4); //年度报告
  initTable3(snapShotId, 4, null); //批准文件
  initOtherFile(snapShotId, 4); //其他附件
}

//tab切换
$('div[fold=head]').fold({
  sub: 'sub',
});
$('.nav-tabs>li').on('click', function () {
  var $this = $(this),
    $nav_content = $this.parent().next();
  $this.addClass('active').siblings().removeClass('active');
  $nav_content.children('div').eq($this.index()).show().siblings().hide();
});

//质量保证协议
$('#table1').XGrid({
  data: [],
  colNames: [
    '',
    '<i style="color:red;margin-right:4px;">*</i>签订日期',
    '<i style="color:red;margin-right:4px;">*</i>有效期至',
    '<i style="color:red;margin-right:4px;">*</i>签订人',
    '附件',
    '附件数据',
  ],
  colModel: [
    {
      name: 'id', //与反回的json数据中key值对应
      index: 'id', //索引。其和后台交互的参数为sidx
      key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
      hidden: true,
    },
    {
      name: 'signDate',
      index: 'Signdata',
      rowtype: '#grid_BeginDate',
      formatter: function (value) {
        var date = value;
        if (!value) return false;
        date = format(value);
        return date.split(' ')[0];
      },
    },
    {
      name: 'validDate',
      index: 'vld',
      rowtype: '#grid_endDate',
      formatter: function (value) {
        var date = value;
        if (!value) return false;
        date = format(value);
        return date.split(' ')[0];
      },
    },
    {
      name: 'signName',
      index: 'signName',
      rowtype: '#grid_person',
    },
    {
      name: 'enclosureCount',
      index: 'enclosureCount',
      formatter: function (value) {
        var str = '无';
        if (value) {
          str =
            '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
        }
        return str;
      },
      unformat: function (e) {
        e = e.replace(/<[^>]+>/g, '');
        if (e == '无') {
          e = 0;
        }
        return e;
      },
    },
    {
      name: 'enclosureList',
      index: 'enclosureList',
      hidden: true,
      formatter: function (value) {
        if (value) {
          return JSON.stringify(value);
        }
        return JSON.stringify([]);
      },
      unformat: function (value) {
        return JSON.parse(value);
      },
    },
  ],
  rownumbers: true,
  rowNum: 100,
  altRows: true, //设置为交替行表格,默认为false
  pager: '#grid_page1',
  gridComplete: function () {
    if (!$('#supplierBaseId').val() || $('#supplierBaseId').val() != '') {
      if ('Y' != $('#selfEdit').val()) {
        setXGirdInputRead('table1');
      }
    }
    setTimeout(function () {
      utils.setTableInpTit('#table1');
    }, 3000);
  },
});
//客户委托书
$('#table2').XGrid({
  data: [],
  colNames: [
    '',
    '数据',
    '<i style="color:red;margin-right:4px;">*</i>委托书编号',
    '<i style="color:red;margin-right:4px;">*</i>被委托人',
    '<i style="color:red;margin-right:4px;">*</i>性别',
    '<i style="color:red;margin-right:4px;">*</i>电话',
    '<i style="color:red;margin-right:4px;">*</i>证件号码',
    '地址',
    '<i style="color:red;margin-right:4px;">*</i>身份证有效期至',
    '<i style="color:red;margin-right:4px;">*</i>委托书有效期至',
    '<i style="color:red;margin-right:4px;">*</i>授权类型',
    '附件',
    '<i style="color:red;margin-right:4px;">*</i>授权范围',
    '附件数据',
    '经营范围数据',
    '剂型',
  ],
  colModel: [
    {
      name: 'id', //与反回的json数据中key值对应
      index: 'id', //索引。其和后台交互的参数为sidx
      key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
      hidden: true,
    },
    {
      name: 'supplierClientProxyProductVOList',
      index: '',
      hidden: true,
      formatter: function (value) {
        if (value) {
          return JSON.stringify(value);
        }
        return JSON.stringify([]);
      },
      unformat: function (value) {
        if (value) {
          return JSON.parse(value);
        }
        return [];
      },
    },
    {
      name: 'proxyOderNo',
      index: 'proxyOderNo',
      rowtype: '#entrustCode',
    },
    {
      name: 'mandataryName',
      index: 'mandataryName',
      rowtype: '#entrustPerson',
    },
    {
      name: 'mandatarySex',
      index: 'mandatarySex',
      rowtype: '#sexType',
    },
    {
      name: 'mandataryTel',
      index: 'mandataryTel',
      rowtype: '#phoneP',
    },
    {
      name: 'mandataryCertificateNumber',
      index: 'mandataryCertificateNumber',
      rowtype: '#crteP',
    },
    {
      name: 'mandataryAddress',
      index: 'mandataryAddress',
      rowtype: '#posP',
    },
    {
      name: 'identityValidDate',
      index: 'identityValidDate',
      rowtype: '#crteTerm',
      formatter: function (value) {
        var date = value;
        if (!value) return false;
        date = format(value);
        return date.split(' ')[0];
      },
    },
    {
      name: 'proxyValidDate',
      index: 'proxy_vld',
      rowtype: '#entrustTerm',
      formatter: function (value) {
        var date = value;
        if (!value) return false;
        date = format(value);
        return date.split(' ')[0];
      },
    },
    {
      name: 'authorityType',
      index: 'authorityType',
      rowtype: '#grantType',
    },
    {
      name: 'enclosureCount',
      index: 'enclosureCount',
      formatter: function (value) {
        var str = '无';
        if (value) {
          str =
            '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
        }
        return str;
      },
      unformat: function (e) {
        e = e.replace(/<[^>]+>/g, '');
        if (e == '无') {
          e = 0;
        }
        return e;
      },
    },
    {
      name: 'authrange',
      index: 'authrange',
      formatter: function (value, id, rowData) {
        var returnHtmlStr = '';
        if (Number(rowData.authorityType) == 1) {
          returnHtmlStr = '经营范围';
        }
        // else if (Number(rowData.authorityType) == 2) {
        //     returnHtmlStr="选择剂型";
        // }
        else if (Number(rowData.authorityType) == 0) {
          returnHtmlStr = '选择商品';
        } else {
          returnHtmlStr = '';
        }
        return (
          '<a href="javascript:;" class="commodity">' + returnHtmlStr + '</a>'
        );
        //2018.9.6,RL,bug2569,end
      },
      unformat: function (e) {
        return '';
      },
    },
    {
      name: 'enclosureList',
      index: 'enclosureList',
      hidden: true,
      formatter: function (value) {
        if (value) {
          return JSON.stringify(value);
        }
        return JSON.stringify([]);
      },
      unformat: function (value) {
        return JSON.parse(value);
      },
    },
    {
      name: 'supplierClientProxyBusinessScopeVOList',
      hidden: true,
      formatter: function (value) {
        if (value) {
          return JSON.stringify(value);
        }
        return JSON.stringify([]);
      },
      unformat: function (value) {
        return JSON.parse(value);
      },
    },
    {
      name: 'supplierClientProxyTypeVOList',
      hidden: true,
      formatter: function (value) {
        if (value) {
          return JSON.stringify(value);
        }
        return JSON.stringify([]);
      },
      unformat: function (value) {
        return JSON.parse(value);
      },
    },
  ],
  rowNum: 100,
  rownumbers: true,
  altRows: true, //设置为交替行表格,默认为false
  pager: '#grid_page2',
  gridComplete: function () {
    if (!$('#supplierBaseId').val() || $('#supplierBaseId').val() != '') {
      if ('Y' != $('#selfEdit').val()) {
        setXGirdInputRead('table2');
      }
    }
    //手动获取初始的选中商品的名称
    setTimeout(function () {
      if (window.changeBefore) {
        var initKHWT = window.changeBefore.initKHWTDataObj;
        var initTreeIds = [];
        $(initKHWT).each(function (ind, ite) {
          var sortTreesIds = [];
          for (
            var i = 0;
            i < ite.supplierClientProxyProductVOList.length;
            i++
          ) {
            sortTreesIds.push(
              ite.supplierClientProxyProductVOList[i].productCode,
            );
          }
          if (sortTreesIds.length > 0) {
            initTreeIds.push(sortTreesIds);
          }
        });
        var param = initTreeIds
          .toString()
          .substring(0, initTreeIds.toString().length);
        if (param.lastIndexOf(',') == param.length - 1) {
          param = param.substring(0, param.length - 1);
        }
        var initTreeNames = [];
        if (initTreeIds.length > 0) {
          $.ajax({
            type: 'get',
            url: '/proxy-product/product/productOrga/query?ids=' + param,
            dataType: 'json',
            success: function (res) {
              //console.log('选取商品接口')
              //console.log(res)
              if (res.code == 0) {
                var list = res.result.list;
                for (var i = 0; i < initTreeIds.length; i++) {
                  var _objArr = [];
                  for (var k = 0; k < initTreeIds[i].length; k++) {
                    for (var j = 0; j < list.length; j++) {
                      if (Number(initTreeIds[i][k]) == list[j].id) {
                        var obj = {};
                        obj.productCode = list[j].productCode;
                        obj.commonName = list[j].commonName;
                        obj.specifications = list[j].specifications;
                        _objArr.push(obj);
                      }
                    }
                  }
                  initTreeNames.push(_objArr);
                }
                window.changeBefore.initKHWTS_sp = initTreeNames;
              }
            },
          });
        }
      }
      utils.setTableInpTit('#table2');
    }, 5000);
  },
});
//批准文件
$('#table3').XGrid({
  data: [],
  colNames: [
    '',
    '<i style="color:red;margin-right:4px;">*</i>证书类型',
    '<i style="color:red;margin-right:4px;">*</i>证书编号',
    '<i style="color:red;margin-right:4px;">*</i>经营范围',
    '发证日期',
    '<i style="color:red;margin-right:4px;">*</i>有效期至',
    '附件',
    '附件数据',
    '全量经营范围',
  ],
  colModel: [
    {
      name: 'id', //与反回的json数据中key值对应
      index: 'id', //索引。其和后台交互的参数为sidx
      key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
      hidden: true,
    },
    {
      name: 'certificateId',
      index: 'certificateId',
      rowtype: '#certType',
      rowEvent: function (etype, c, d) {
        var credentialId = etype.rowData.certificateId;
        getZtreeData(credentialId, etype.rowData.id);
      },
    },
    {
      name: 'certificateNum',
      index: 'certificateNum',
      rowtype: '#certificateNum',
    },
    {
      name: 'supplierApprovalFileBusinessScopeVOList',
      index: 'supplierApprovalFileBusinessScopeVOList',
      rowtype: '#operScopeZtree',
      rowEvent: function (etype) {
        initBaseDataBuseScope(); //初始化经营范围内容
      },
    },
    {
      name: 'certificationDate',
      index: 'certificationDate',
      rowtype: '#fCrte',
      formatter: function (value) {
        var date = value;
        if (!value) return false;
        date = format(value);
        return date.split(' ')[0];
      },
    },
    {
      name: 'validityDate',
      index: 'validityDate',
      rowtype: '#docTerm',
      formatter: function (value) {
        var date = value;
        if (!value) return false;
        date = format(value);
        return date.split(' ')[0];
      },
    },
    {
      name: 'enclosureCount',
      index: 'enclosureCount',
      formatter: function (value) {
        var str = '无';
        if (value) {
          str =
            '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
        }
        return str;
      },
      unformat: function (e) {
        e = e.replace(/<[^>]+>/g, '');
        if (e == '无') {
          e = 0;
        }
        return e;
      },
    },
    {
      name: 'enclosureList',
      index: 'enclosureList',
      hidden: true,
      formatter: function (value) {
        if (value) {
          return JSON.stringify(value);
        }
        return JSON.stringify([]);
      },
      unformat: function (value) {
        return JSON.parse(value);
      },
    },
    {
      name: 'scopeofoperationVo',
      hidden: true,
    },
  ],
  rownumbers: true,
  rowNum: 100,
  altRows: true, //设置为交替行表格,默认为false
  pager: '#grid_page3',
  gridComplete: function () {
    setTimeout(function () {
      $('#table3 tr').each(function (index) {
        if (index != 0) {
          var credentialId = $(this)
            .find("td[row-describedby='certificateId'] select option:selected")
            .val();
          var zTreeId = $(this).find('.operScopeZtree').attr('id');
          var zTree = $.fn.zTree.getZTreeObj(zTreeId);
          for (var i = 0; i < idArr.length; i++) {
            var item = idArr[i];
            for (var n in item) {
              if (n == credentialId) {
                var aList = item[n];
                for (var s = 0; s < aList.length; s++) {
                  //1018DT：新增渲染时对tr和选中ID的判断条件
                  if (aList[s].approvalFileId == $(this).prop('id')) {
                    zTree != null
                      ? zTree.checkNode(
                        zTree.getNodeByParam(
                          'id',
                          Number(aList[s].businessScopeCode),
                        ),
                        true,
                      )
                      : '';
                  }
                  initBaseDataBuseScope();
                }
                break;
              }
            }
          }
        }
      });

      /* 批件下沉开始
            * if (!$("#supplierBaseId").val() || $("#supplierBaseId").val() != "") {
                if ("Y" != $("#selfEdit").val() || "N" != $("#useSupplier").val()) {
                    setXGirdInputRead("table3");
                }
            }批件下沉结束
            */
      if ('insert' != pageProperty) {
        setTable3Edit();
        if (!$('#supplierBaseId').val() || $('#supplierBaseId').val() != '') {
          if ('Y' != $('#selfEdit').val()) {
            setXGirdInputRead('table3');
          }
        }
      }

      utils.setTableInpTit('#table3');
    }, 3);
  },
});

//年度报告
$('#table4').XGrid({
  data: [],
  colNames: [
    '',
    '<i style="color:red;margin-right:4px;">*</i>报告年份',
    '<i style="color:red;margin-right:4px;">*</i>是否经营异常',
    '<i style="color:red;margin-right:4px;">*</i>是否有行政处罚',
    '有效期至',
    '附件',
    '附件数据',
  ],
  colModel: [
    {
      name: 'id', //与反回的json数据中key值对应
      index: 'id', //索引。其和后台交互的参数为sidx
      key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
      hidden: true,
    },
    {
      name: 'reportDate',
      index: 'reportDate',
      rowtype: '#reportYear',
    },
    {
      name: 'manageAbnormal',
      index: 'manageAbnormal',
      rowtype: '#isAbnormal',
    },
    {
      name: 'administrativePenalty',
      index: 'administrativePenalty',
      rowtype: '#isPunish',
    },
    {
      name: 'validityDate',
      index: 'validityDate',
      rowtype: '#validityDate',
    },
    {
      name: 'enclosureCount',
      index: 'enclosureCount',
      formatter: function (value) {
        var str = '无';
        if (value) {
          str =
            '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
        }
        return str;
      },
      unformat: function (e) {
        e = e.replace(/<[^>]+>/g, '');
        if (e == '无') {
          e = 0;
        }
        return e;
      },
    },
    {
      name: 'enclosureList',
      index: 'enclosureList',
      hidden: true,
      formatter: function (value) {
        if (value) {
          return JSON.stringify(value);
        }
        return JSON.stringify([]);
      },
      unformat: function (value) {
        return JSON.parse(value);
      },
    },
  ],
  rowNum: 100,
  rownumbers: true,
  altRows: true, //设置为交替行表格,默认为false
  pager: '#grid_page4',
  gridComplete: function () {
    /* if (!$("#supplierBaseId").val() || $("#supplierBaseId").val() != "") {
            if ("Y" != $("#selfEdit").val() || "N" != $("#useSupplier").val()) {
                setXGirdInputRead("table4");
            }
        }*/
    if ('insert' != $('#pageProperty').val()) {
      if (!$('#supplierBaseId').val() || $('#supplierBaseId').val() != '') {
        if ('Y' != $('#selfEdit').val()) {
          setXGirdInputRead('table4');
        }
      }
    }
    setTimeout(function () {
      utils.setTableInpTit('#table4');
    }, 1000);
  },
});

if ('insert' == pageProperty) {
  //新增页面
  /*
    * 批件下沉开始
    * var addReturnPageFlag = $("#addReturnPageFlag").val();//是否是全量新增
    if (addReturnPageFlag) {
        //批准文件新增行 删除行
        addRow('#pzwjAddRow', '#table3');
        deleRow('#pzwjDelRow', '#table3', function () {
            initBaseDataBuseScope();
        });
        //年度报告新增行 删除行
        addRow('#ndbgAddRow', '#table4');
        deleRow('#ndbgDelRow', '#table4');
    } else {//非全量新增
        //批准文件新增行 删除行
        $("#pzwjAddRow").attr("style", "display:none;");
        $("#pzwjDelRow").attr("style", "display:none;");
        //年度报告新增行 删除行
        $("#ndbgAddRow").attr("style", "display:none;");
        $("#ndbgDelRow").attr("style", "display:none;");
    }
    批件下沉结束
    */

  //批准文件新增行 删除行
  addRow('#pzwjAddRow', '#table3');
  deleRow('#pzwjDelRow', '#table3', function () {
    initBaseDataBuseScope();
  });
  //年度报告新增行 删除行
  addRow('#ndbgAddRow', '#table4');
  deleRow('#ndbgDelRow', '#table4');
  //客户委托书新增行 删除行
  addRow('#khwtAddRow', '#table2');
  deleRow('#khwtDelRow', '#table2', function () {
    var phone = '';
    var name = '';
    if ($('#table2 tr') && $('#table2 tr').length > 1) {
      console.log(2);
      var id = $('#table2 tr').eq(1).attr('id');
      var rowData = $('#table2').XGrid('getRowData', id);
      phone = rowData.mandataryTel;
      name = rowData.mandataryName;
    }
    $("input[name='mandatary']").val(name);
    $("input[name='mandataryPhone']").val(phone);
  });
  //质量保证协议新增行 删除行
  addRow('#zlbzAddRow', '#table1');
  deleRow('#zlbzDelRow', '#table1');
} else if ('edit' == pageProperty) {
  //编辑页面
  /*
    * 批件下沉开始
    *  var useSupplier = $("#useSupplier").val();//是否引用主数据
    if ("Y" == useSupplier) {//引用主数据
        //批准文件新增行 删除行
        $("#pzwjAddRow").attr("style", "display:none;");
        $("#pzwjDelRow").attr("style", "display:none;");
        //年度报告新增行 删除行
        $("#ndbgAddRow").attr("style", "display:none;");
        $("#ndbgDelRow").attr("style", "display:none;");
    } else {
        //批准文件新增行 删除行
        addRow('#pzwjAddRow', '#table3');
        deleRow('#pzwjDelRow', '#table3');
        //年度报告新增行 删除行
        addRow('#ndbgAddRow', '#table4');
        deleRow('#ndbgDelRow', '#table4');
    }
    批件下沉结束
    */

  //批准文件新增行 删除行
  addRow('#pzwjAddRow', '#table3');
  deleRow('#pzwjDelRow', '#table3');
  //年度报告新增行 删除行
  addRow('#ndbgAddRow', '#table4');
  deleRow('#ndbgDelRow', '#table4');

  //客户委托书新增行 删除行
  addRow('#khwtAddRow', '#table2');
  deleRow('#khwtDelRow', '#table2', function () {
    var phone = '';
    var name = '';
    if ($('#table2 tr') && $('#table2 tr').length > 1) {
      console.log(2);
      var id = $('#table2 tr').eq(1).attr('id');
      var rowData = $('#table2').XGrid('getRowData', id);
      phone = rowData.mandataryTel;
      name = rowData.mandataryName;
    }
    $("input[name='mandatary']").val(name);
    $("input[name='mandataryPhone']").val(phone);
  });
  //质量保证协议新增行 删除行
  addRow('#zlbzAddRow', '#table1');
  deleRow('#zlbzDelRow', '#table1');
} else {
  //详情页面 设置按钮不展示
  //批准文件新增行 删除行
  $('#pzwjAddRow').attr('style', 'display:none;');
  $('#pzwjDelRow').attr('style', 'display:none;');
  //年度报告新增行 删除行
  $('#ndbgAddRow').attr('style', 'display:none;');
  $('#ndbgDelRow').attr('style', 'display:none;');
  //客户委托书新增行 删除行
  $('#khwtAddRow').attr('style', 'display:none;');
  $('#khwtDelRow').attr('style', 'display:none;');
  //质量保证协议新增行 删除行
  $('#zlbzAddRow').attr('style', 'display:none;');
  $('#zlbzDelRow').attr('style', 'display:none;');
}

//客户委托书授权类型变化
$('#table2').on('change', '.grantType', function (ev) {
  var grantType = $(this).find('option:selected').val(); //选中的值
  var $tr = $(this).parents('tr');
  if ('0' == grantType) {
    //选择商品
    $tr.find('.commodity').text('').text('选择商品');
  } else if ('1' == grantType) {
    //经营范围授权
    $tr.find('.commodity').text('').text('经营范围');
  }

  //2018.8.31 17:27,rl,新增剂型，begin
  // else if ("2" == grantType) {//剂型
  //     $tr.find(".commodity").text("").text('选择剂型');
  // }
  //2018.8.31 17:27,rl,新增剂型，end

  //2018.9.5,RL,bug2589,begin
  else {
    $tr.find('.commodity').text('');
  }
  //2018.9.5,RL,bug2589,begin
});

//客户委托书被委托人发生变化
$('#table2').on('keyup', '.mandataryName:first', function (ev) {
  var mandataryName = $.trim($(this).val()); //选中的值
  $("input[name='mandatary']").val(mandataryName);
});

//客户委托书被委托人电话发生变化 
$('#table2').on('keyup', '.mandataryTel:first', function (ev) {
  var mandataryTel = $.trim($(this).val()); //选中的值
  $("input[name='mandataryPhone']").val(mandataryTel);
});
//付款、结算方式点击复选框显示输入框
$(document).on('change', '.parentCode', function () {
  var checked = this.checked;
  if ($(this).val() === '2007') {
    $('.parentCode').each(function () {
      if ($(this).val() === '2002') {
        $(this).attr('checked', false);
        $(this)
          .parents('.paymentSettlement')
          .find('.childCode')
          .addClass('displaynone');
        $(this)
          .parents('.paymentSettlement')
          .find('.childCode')
          .find('input[type=number]')
          .val('');
      }
    });
  }
  if ($(this).val() === '2002') {
    $('.parentCode').each(function () {
      if ($(this).val() === '2007') {
        $(this).attr('checked', false);
        $(this)
          .parents('.paymentSettlement')
          .find('.childCode')
          .addClass('displaynone');
        $(this)
          .parents('.paymentSettlement')
          .find('.childCode')
          .find('input[type=number]')
          .val('');
      }
    });
  }
  if ($(this).val() === '2008') {
    $('.parentCode').each(function () {
      if ($(this).val() === '2003') {
        $(this).attr('checked', false);
        $(this)
          .parents('.paymentSettlement')
          .find('.childCode')
          .addClass('displaynone');
        $(this)
          .parents('.paymentSettlement')
          .find('.childCode')
          .find('input[type=number]')
          .val('');
      }
    });
  }
  if ($(this).val() === '2003') {
    $('.parentCode').each(function () {
      if ($(this).val() === '2008') {
        $(this).attr('checked', false);
        $(this)
          .parents('.paymentSettlement')
          .find('.childCode')
          .addClass('displaynone');
        $(this)
          .parents('.paymentSettlement')
          .find('.childCode')
          .find('input[type=number]')
          .val('');
      }
    });
  }
  let codes = $(this).val();
  if (checked) {
    $(this)
      .parents('.paymentSettlement')
      .find('.childCode')
      .removeClass('displaynone');
    addSettleMultiple(codes);
    $(this)
      .parents('.paymentSettlement')
      .find('.addSettleMultipleDateList')
      .addClass(codes);
    $(this)
      .parents('.paymentSettlement')
      .find('.addSettleMultipleDateList')
      .removeClass('displaynone');
  } else {
    $(this)
      .parents('.paymentSettlement')
      .find('.childCode')
      .addClass('displaynone');
    $(this)
      .parents('.paymentSettlement')
      .find('.childCode')
      .find('input[type=number]')
      .val('');
    clearSettleMultiple(codes);
    $(this)
      .parents('.paymentSettlement')
      .find('.addSettleMultipleDateList')
      .removeClass(codes);
    $(this)
      .parents('.paymentSettlement')
      .find('.addSettleMultipleDateList')
      .addClass('displaynone');
  }
  clickFunctions();
});

function checkSameInput(obj, datas) {
  if (!datas || datas.length === 0) {
    return true;
  }
  let objctArr = obj.supplierExtendItemVOList.filter((item) => {
    return item.parentCode === '2002' || item.parentCode === '2003';
  });
  objectDatas = [
    {
      paymentDay: '',
      settleMethod: '2002',
      settlementCycle: '',
      settlementDate: '',
    },
    {
      paymentDay: '',
      settleMethod: '2003',
      settlementCycle: '',
      settlementDate: '',
    },
  ];
  for (let index = 0; index < objctArr.length; index++) {
    const element = objctArr[index];
    if (element.code === '2002001') {
      objectDatas[0].settlementCycle = element.value;
    }
    if (element.code === '2002002') {
      objectDatas[0].settlementDate = element.value;
    }
    if (element.code === '2002003') {
      objectDatas[0].paymentDay = element.value;
    }
    if (element.code === '2003001') {
      objectDatas[1].settlementCycle = element.value;
    }
    if (element.code === '2003002') {
      objectDatas[1].settlementDate = element.value;
    }
    if (element.code === '2003003') {
      objectDatas[1].paymentDay = element.value;
    }
  }

  let allDatas = objectDatas.concat(datas);
  for (let index = 0; index < allDatas.length; index++) {
    const element = allDatas[index];
    let settlementCycles = allDatas.filter((item) => {
      return (
        item.settleMethod.toString() === element.settleMethod.toString() &&
        item.settlementCycle === element.settlementCycle &&
        item.settlementDate === element.settlementDate &&
        item.paymentDay === element.paymentDay
      );
    });
    if (settlementCycles.length > 1) {
      return false;
    }
  }
  return true;
}

function clickFunctions() {
  $('.2002')
    .off()
    .on('click', function () {
      if (!settleMultipleDateList['2002']) {
        settleMultipleDateList['2002'] = [];
      }
      var length2002 = settleMultipleDateList['2002'].length;
      var index =
        length2002 > 0
          ? settleMultipleDateList['2002'][length2002 - 1].index + 1
          : 0;
      if (length2002 > 3) {
        return;
      }
      addhtml('2002', index);
      settleMultipleDateList['2002'].push({
        settlementCycle: '',
        settlementDate: '',
        paymentDay: '',
        index: index,
        settleMethod: 2002,
      });
      $('.minuteMultipleDateList2002' + index)
        .off()
        .on('click', function (val) {
          console.log(val);
          $('.list2002' + index).remove();
          settleMultipleDateList['2002'] = settleMultipleDateList[
            '2002'
          ].filter((item) => {
            return item.index !== index;
          });
        });
    });

  $('.2003')
    .off()
    .on('click', function () {
      if (!settleMultipleDateList['2003']) {
        settleMultipleDateList['2003'] = [];
      }
      var length2003 = settleMultipleDateList['2003'].length;
      var index =
        length2003 > 0
          ? settleMultipleDateList['2003'][length2003 - 1].index + 1
          : 0;
      if (length2003 > 3) {
        return;
      }
      addhtml('2003', index);
      settleMultipleDateList['2003'].push({
        settlementCycle: '',
        settlementDate: '',
        paymentDay: '',
        index: index,
        settleMethod: 2003,
      });
      $('.minuteMultipleDateList2003' + index)
        .off()
        .on('click', function (val) {
          console.log(val);
          $('.list2003' + index).remove();
          settleMultipleDateList['2003'] = settleMultipleDateList[
            '2003'
          ].filter((item) => {
            return item.index !== index;
          });
        });
    });
}

function addSettleMultiple(code) {
  settleMultipleDateList[code.toString()] = settleMultipleDateList[
    code.toString()
  ]
    ? settleMultipleDateList[code.toString()]
    : [];
}

function clearSettleMultiple(code) {
  let elArray = settleMultipleDateList[code.toString()];
  for (let index = 0; index < elArray.length; index++) {
    const element = elArray[index];
    let elClass = '.list' + code + element.index;
    $(elClass).remove();
  }
  delete settleMultipleDateList[code.toString()];
}

function checkInitAddBtn() {
  let codes = ['2002', '2003'];
  for (let index = 0; index < codes.length; index++) {
    const element = codes[index];
    if (
      settleMultipleDateList[element.toString()] &&
      settleMultipleDateList[element.toString()].length > 0
    ) {
      let classs = '.checkbox' + element;
      let disabled =
        $(classs).find('.parentCode').attr('disabled') === undefined
          ? false
          : true;
      let value =
        $(classs).find('.parentCode').val() === '2002' ||
          $(classs).find('.parentCode').val() === '2003'
          ? true
          : false;
      if (disabled === false && value) {
        $(classs)
          .find('.addSettleMultipleDateList')
          .addClass(element.toString());
        $(classs)
          .parent()
          .find('.addSettleMultipleDateList')
          .removeClass('displaynone');
      }
    }
  }
}

//添加html
function addhtml(code, index) {
  var space =
    "<div style='width:100%;display:flex;flex-direction:row;' class='list" +
    code +
    index +
    "'><div class='col-md-2'></div>";
  var html =
    "<div class='col-md-3 col-xs-4'><span class='cyclechildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>结算周期</span></div><input class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class cyclecode' type='number' placeholder='天' name='cyclecode'>天</span></div>";
  var html1 =
    "<div class='col-md-3 col-xs-4'><span class='datechildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>结算日</span></div><input class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class datecode' type='number' placeholder='日' name='datecode' data-max='31'>日</span></div>";
  var html2 =
    "<div class='col-md-3 col-xs-4'><span class='daychildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>支付日</span></div><input class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class daycode' type='number' placeholder='日' name='daycode' data-max='31'>日</span></div>";
  var end =
    "<div class='minuteMultipleDateList" + code + index + "'>-</div></div>";
  let classs = '.' + code;
  $(classs)
    .parent()
    .append(space + html + html1 + html2 + end);
}

function getMultipleDateList() {
  let objsArr = [];
  Object.keys(settleMultipleDateList).forEach((key) => {
    let objArr = settleMultipleDateList[key];
    objArr.forEach((item) => {
      let obj = {
        settlementCycle: '',
        settlementDate: '',
        paymentDay: '',
        settleMethod: '',
      };
      let classes = '.list' + item.settleMethod + item.index;
      obj.settleMethod = item.settleMethod.toString();
      obj['settlementCycle'] = $(classes).find('.cyclecode').val();
      obj['settlementDate'] = $(classes).find('.datecode').val();
      obj['paymentDay'] = $(classes).find('.daycode').val();
      objsArr.push(obj);
    });
  });
  return objsArr;
}

function apiORGDetail() {
  let baseid = $('#supplierOrganBaseId').val();
  $.ajax({
    url:
      '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/new-add-settle-multiple-date?type=OPERATE_ATTRIBUTE&fid=' +
      baseid,
    type: 'get',
    dataType: 'json',
    success: function (data) {
      const { code, msg, result } = data;
      if (code === 0) {
        settleMultipleDateList = result;
        Object.keys(settleMultipleDateList).forEach((key) => {
          console.log(key);
          let el = settleMultipleDateList[key.toString()];
          el.forEach((item, index) => {
            item['index'] = index;
            addDetailhtml(key, index, item);
          });
          let classs = '.checkbox' + key;
          $(classs).find('.addSettleMultipleDateList').addClass(key.toString());
        });

        checkInitAddBtn();
        clickFunctions();
      } else {
      }
    },
    error: function () { },
  });
}

//初始化详情的数据
function apiGetFirstOperate() {
  let baseid = $('#supplierOrganBaseId').val();
  $.ajax({
    url:
      '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/new-add-settle-multiple-date?type=FIRST_OPERATE&fid=' +
      baseid,
    type: 'get',
    dataType: 'json',
    success: function (data) {
      const { code, msg, result } = data;
      if (code === 0) {
        settleMultipleDateList = result;
        Object.keys(settleMultipleDateList).forEach((key) => {
          console.log(key);
          let el = settleMultipleDateList[key.toString()];
          el.forEach((item, index) => {
            item['index'] = index;
            addDetailhtml(key, index, item);
          });
          let classs = '.checkbox' + key;
          $(classs).find('.addSettleMultipleDateList').addClass(key.toString());
        });

        checkInitAddBtn();
        clickFunctions();
      } else {
      }
    },
    error: function () { },
  });
}

//添加html
function addDetailhtml(code, index, detail) {
  let classs = '.checkbox' + code;
  let readonly =
    $(classs).find('.Filter_SpaceAndStrLen_Class').attr('readonly') ===
      undefined
      ? false
      : true;
  let redOnlyString = readonly ? "readonly='readonly'" : '';
  var space =
    "<div style='width:100%;display:flex;flex-direction:row;' class='list" +
    code +
    index +
    "'><div class='col-md-2'></div>";
  var html =
    "<div class='col-md-3 col-xs-4'><span class='cyclechildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>结算周期</span></div><input " +
    redOnlyString +
    " value='" +
    detail.settlementCycle +
    "' class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class cyclecode' type='number' placeholder='天' name='cyclecode'>天</span></div>";
  var html1 =
    "<div class='col-md-3 col-xs-4'><span class='datechildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>结算日</span></div><input " +
    redOnlyString +
    " value='" +
    detail.settlementDate +
    "' class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class datecode' type='number' placeholder='日' name='datecode' data-max='31'>日</span></div>";
  var html2 =
    "<div class='col-md-3 col-xs-4'><span class='daychildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>支付日</span></div><input " +
    redOnlyString +
    " value='" +
    detail.paymentDay +
    "' class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class daycode' type='number' placeholder='日' name='daycode' data-max='31'>日</span></div>";
  var end = readonly
    ? '</div>'
    : "<div class='minuteMultipleDateList" + code + index + "'>-</div></div>";
  $(classs).append(space + html + html1 + html2 + end);

  readonly === false &&
    $(classs).find('.addSettleMultipleDateList').removeClass('displaynone');

  $('.minuteMultipleDateList' + code + index)
    .off()
    .on('click', function (val) {
      $('.list' + code + index).remove();
      settleMultipleDateList[code.toString()] = settleMultipleDateList[
        code.toString()
      ].filter((item) => {
        return item.index !== index;
      });
    });
}

//初始化显示隐藏付款、结算方式输入框
$('.parentCode').each(function () {
  var checked = this.checked;
  if (checked) {
    $(this)
      .parents('.paymentSettlement')
      .find('.childCode')
      .removeClass('displaynone');
  } else {
    $(this)
      .parents('.paymentSettlement')
      .find('.childCode')
      .addClass('displaynone');
  }
});
//点击选择商品或经营范围
var resultArr = []; //新增，编辑状态临时存放已选中的数据,品种授权弹框内用到
var selectArr = []; //存放选中数据id，做回显选中处理，品种授权弹框内用到
$('#table2').on('click', '.commodity', function (ev) {
  var supplierAuditStatus = $('#supplierAuditStatus').val(); //审核状态
  var $td = $(this).parents('td');
  var $tr = $(this).parents('tr');
  var trId = $tr.attr('id');
  var grantType = $tr.find('.grantType option:selected').val();
  if ('0' == grantType) {
    //选择商品
    let count = $(this).attr('data-openStatus');
    let _this = $(this);
    count = count ? count : 0;
    var selectedProductLists = [];
    if (count == 0) {
      //获取已选择的商品列表
      $.ajax({
        url: '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/getClientProxyProduct',
        type: 'post',
        async: false,
        data: {
          clientOderId: trId,
          type: $('#firstOrOrganFlag').val() == 'firstApplySnap' ? '4' : '1',
        },
        dataType: 'json',
        success: function (data) {
          // if ("" != data.list) {
          //     selectedProductIds = data.list;
          // }
          if (0 == data.code) {
            selectedProductLists = data.result;
          }
        },
      });
    }
    //临时存放已选中数据
    var temporary = $td.attr('data-resultArr');
    resultArr = [];
    if (temporary && temporary != '[]' && typeof temporary === 'string') {
      resultArr = JSON.parse(temporary);
    }
    //获取已选商品id
    var selRowData =
      $('#table2').getRowData(trId).supplierClientProxyProductVOList;
    selectArr = [];
    if (selRowData && selRowData.length > 0) {
      for (var i = 0; i < selRowData.length; i++) {
        selectArr.push(selRowData[i].productCode);
      }
    }
    //跳转到选择商品页面
    utils
      .dialog({
        width: $(window).width() * 0.7,
        height: $(window).height() * 0.6,
        title: '选择商品',
        url: '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/getProductList',
        data: {
          selectedProductIds: selectedProductLists,
          selectArr: selectArr,
          selRowData,
          resultArr,
          isEdit:
            $('#pageProperty').length == 0
              ? $('#isEdit').val()
              : $('#pageProperty').val() == 'detail'
                ? '0'
                : '1',
          count,
        },
        onclose: function () {
          if (this.returnValue) {
            _this.attr('data-openStatus', 1);
            const arr = this.returnValue;
            //详情页设置不可更改
            if ($('#detail_ztree_disabled').length < 1) {
              // var arr = $('#selected_Tableb').getRowData();
              $td.attr('data-resultArr', JSON.stringify(arr));
              if (arr.length > 0) {
                var productIdArr = [];
                for (var i = 0; i < arr.length; i++) {
                  var productCode = arr[i].id;
                  productIdArr.push({
                    productCode: productCode,
                    productNum: arr[i].productCode,
                    commonName: arr[i].commonName,
                    specifications: arr[i].specifications,
                  });
                }
                //添加客户委托书对应的商品
                if (trId) {
                  $('#table2').XGrid('setRowData', trId, {
                    supplierClientProxyProductVOList: arr,
                  });
                }
              } else {
                //已选择商品表格清空时，清空对应项
                if (trId) {
                  $('#table2').XGrid('setRowData', trId, {
                    supplierClientProxyProductVOList: [],
                  });
                }
              }
            }
          }
        },
      })
      .showModal();
  } else if ('1' == grantType) {
    //经营范围授权
    // 批准文件中选中的经营范围 所有
    let busScopeIds = [];
    const fontNode = $('#baseDataBuseScope').find('font');
    $(fontNode).each((index, item) =>
      busScopeIds.push($(item).attr('data-id')),
    );

    //alert("经营范围");
    var htm = $('#operScopeZtree_01');
    $.ajax({
      url: '/proxy-sysmanage/sysmanage/dict/querycommodityscope',
      data: {
        orgCode: '001',
        scopeName: '',
      },
      type: 'post',
      dataType: 'json',
      success: function (data) {
        var initKHWT = window.changeBefore.initKHWTDataObj;
        var initTreeIds = [];
        $(initKHWT).each(function (ind, ite) {
          var sortTreesIds = [];
          for (
            var i = 0;
            i < ite.supplierClientProxyBusinessScopeVOList.length;
            i++
          ) {
            sortTreesIds.push(
              ite.supplierClientProxyBusinessScopeVOList[i].businessScopeCode,
            );
          }
          initTreeIds.push(sortTreesIds);
        });
        var initTreeNames = [];
        for (var i = 0; i < initTreeIds.length; i++) {
          for (var k = 0; k < initTreeIds[i].length; k++) {
            for (var j = 0; j < data.result.length; j++) {
              if (initTreeIds[i][k] == data.result[j].scopeId) {
                initTreeNames.push(data.result[j].name);
              }
            }
          }
        }
        console.log(initTreeNames);
        window.changeBefore.initKHWTS_jyfw = initTreeNames;
        pulicTree(data.result, $('.operScopeZtree_01'));
        utils
          .dialog({
            title: '经营范围',
            width: 500,
            content: htm,
            okValue: '确定',
            ok: function () {
              //详情页设置不可更改
              // if ($('#detail_ztree_disabled').length < 1) {
              //     var data = zTree.getCheckedNodes(true);
              //     var arr = [];
              //     if (data && data.length > 0) {
              //         for (var i = 0; i < data.length; i++) {
              //             if (data[i].name != '经营范围') {
              //                 arr.push({
              //                     businessScopeCode: data[i].id,
              //                     businessScopeName: data[i].name
              //                 })
              //             }
              //         }
              //     }
              //     //保存经营范围数据
              //     $("#table2").XGrid('setRowData', trId, {supplierClientProxyBusinessScopeVOList: arr});
              // }
            },
            cancelValue: '取消',
            cancel: function () { },
            onclose: function () { },
          })
          .showModal();
        //trId
        var rowData = $('#table2').XGrid('getRowData', trId);
        var checkValus = rowData.supplierClientProxyBusinessScopeVOList;
        if (checkValus.length) {
          var tree = $.fn.zTree.getZTreeObj('operScopeZtreeul'); //文件树
          //循环设置是否选中已存在id
          for (var i = 0; i < checkValus.length; i++) {
            var item = checkValus[i];
            if (zTree.getNodeByParam('id', Number(item.businessScopeCode))) {
              tree.checkNode(
                zTree.getNodeByParam('id', Number(item.businessScopeCode)),
                true,
              );
            }
          }
          //详情页设置不可更改复选框
          if ($('#detail_ztree_disabled').length > 0) {
            var nodes = tree.getNodes();
            chkDis(nodes[0], tree);
          }
        }
        // if("2"==supplierAuditStatus){
        //详情页设置不可更改复选框
        var tree = $.fn.zTree.getZTreeObj('operScopeZtreeul'); //文件树
        var nodes = tree.getNodes();
        chkDis(nodes[0], tree);
        // }
      },
      error: function () { },
    });
  }

  //2018.8.31 17:27,rl,新增剂型，begin
  // else if ("2" == grantType) {//剂型
  //     $.ajax({
  //         url: '/proxy-sysmanage/sysmanage/dict/querydosenotpage',
  //         data: {
  //             doseName: "",
  //             isStop: 0
  //         },
  //         type: 'post',
  //         dataType: 'json',
  //         success: function (resp) {
  //             //获取初始的剂型 name
  //             var initKHWT = window.changeBefore.initKHWTDataObj;
  //             var initTreeIds = [];
  //             $(initKHWT).each(function (ind,ite) {
  //                 var sortTreesIds = [];
  //                 for(var i = 0; i<ite.supplierClientProxyTypeVOList.length; i++){
  //                     sortTreesIds.push(ite.supplierClientProxyTypeVOList[i].jixingId);
  //                 }
  //                 initTreeIds.push(sortTreesIds);
  //             });
  //             var initTreeNames = [];
  //             for(var i = 0; i<initTreeIds.length; i++){
  //                 for(var k = 0; k < initTreeIds[i].length; k++){
  //                     for(var j = 0;j < resp.result.length; j++){
  //                         if(initTreeIds[i][k] == resp.result[j].doseid){
  //                             initTreeNames.push(resp.result[j].dosename);
  //                         }
  //                     }
  //                 }
  //             }
  //             console.log(initTreeNames);
  //             window.changeBefore.initKHWTS_jx = initTreeNames;
  //             utils.dialog({
  //                 title: '剂型',
  //                 width: 800,
  //                 height: 500,
  //                 content: bindDrugDialogHtml(resp),
  //                 okValue: '保存',
  //                 cancelValue: '取消',
  //                 onshow: function () {
  //                     $(this.node).find('.ui-dialog-content').css({"overflow-y": 'scroll'});
  //
  //                     //回显checkbox值
  //                     showDrugCheckboxValue(trId);
  //                 },
  //                 ok: function () {
  //                     //保存按钮回调
  //                     saveDrugCheckboxValue(trId);
  //                 },
  //                 cancel: function () {
  //                 },
  //                 onclose: function () {
  //
  //                 },
  //             }).showModal();
  //
  //             //2018.9.7,RL,begin
  //             //设置所有剂型chekbox不可选
  //             if ($('#detail_ztree_disabled').length > 0 || "2"==supplierAuditStatus) {
  //                 $("#selectDrugDialog_02").find("input[type=checkbox]").attr("disabled",true);
  //             }
  //             //2018.9.7,RL,end
  //         },
  //         error: function () {
  //
  //         }
  //     })
  // }
  //2018.8.31 17:27,rl,新增剂型，end
  else {
    utils
      .dialog({ content: '请选择授权类型！', quickClose: true, timeout: 2000 })
      .showModal();
    return false;
  }
});

//2018.9.1 13:26,rl,dialog回显checkbox,begin
function showDrugCheckboxValue(trId) {
  //读隐藏列,回显已经选择好的值
  var rowData = $('#table2').XGrid(
    'getRowData',
    trId,
  ).supplierClientProxyTypeVOList;
  //2018.9.3 10:22,rl
  /*
        if(!rowData) return false;
        rowData=JSON.parse(rowData);
        */
  if (rowData && rowData.length) {
    var drugChkboxUlList = $('#selectDrugDialog_02 ul');
    var targetLi = void 0;
    $.each(rowData, function (i, v) {
      targetLi = drugChkboxUlList.find('li[id=' + v['jixingId'] + ']');
      if (targetLi) {
        targetLi.find('input[type=checkbox]').prop('checked', true);
      }
    });
  }
}

//2018.9.1 13:26,rl,dialog回显checkbox,end

//2018.9.1 12:41,rl,dialog保存回调,begin
function saveDrugCheckboxValue(trId) {
  var checkedDrugIdList = [];
  var drugChkboxList = $('#selectDrugDialog_02 ul li input[type=checkbox]');
  if (drugChkboxList && drugChkboxList.length) {
    $.each(drugChkboxList, function (i, v) {
      var $v = $(v);
      if ($v.prop('checked')) {
        //checkedDrugIdList.push($v.parents('li').attr('id'));
        checkedDrugIdList.push({
          jixingId: $v.parents('li').attr('id'),
          jixingName: $v.parent().text().trim(),
        });
      }
    });
  }
  console.log(checkedDrugIdList);
  //保存选择的剂型
  $('#table2').XGrid('setRowData', trId, {
    supplierClientProxyTypeVOList: checkedDrugIdList,
  });
}

//2018.9.1 12:41,rl,dialog保存回调,end

//2018.8.31 17:27,rl,新增剂型，begin
function bindDrugDialogHtml(drugJson) {
  var drugCheckboxHtml = [];

  drugCheckboxHtml.push('<div id="selectDrugDialog_02">');
  drugCheckboxHtml.push('<ul class="list-inline">');
  if (drugJson && drugJson.result && drugJson.result.length) {
    for (var i = 0, l = drugJson.result.length; i < l; i++) {
      drugCheckboxHtml.push(
        '<li style="width:20%;" id="' + drugJson.result[i]['doseid'] + '" >',
      );
      drugCheckboxHtml.push('<div class="checkbox">');
      drugCheckboxHtml.push('<label>');
      drugCheckboxHtml.push(
        '<input type="checkbox"> ' + drugJson.result[i]['dosename'],
      );
      drugCheckboxHtml.push('</label>');
      drugCheckboxHtml.push('</div>');
      drugCheckboxHtml.push('</li>');
    }
  } else {
    drugCheckboxHtml.push('<li>暂无数据!</li>');
  }
  drugCheckboxHtml.push('</ul>');
  drugCheckboxHtml.push('</div>');

  return drugCheckboxHtml.join('');
}

//2018.8.31 17:27,rl,新增剂型，end

//初始化地址选择

//2018.9.6,RL,bug2782,begin
//$('[data-toggle="distpicker"]').distpicker();
$('[data-toggle="distpicker"]').distpicker({
  province: '请选择',
  city: '请选择',
  district: '请选择',
});
//2018.9.6,RL,bug2782,end

//添加仓库
$('body').on('click', '.addDepot', function () {
  let obj = distpickerHTML(1, 1);
  $('#depotAddress').append(obj.html);
  let storgeAddressSelIdObj = [
    {
      nextNodeWrap: '#stoProvinceSel_wrap_' + obj.radomInit[0],
      nextNodeName: 'repertoryProvince_' + obj.radomInit[0],
      nextNodeId: 'repertoryProvince_' + obj.radomInit[0],
    },
    {
      nextNodeWrap: '#stoCitySel_wrap_' + obj.radomInit[0],
      nextNodeName: 'repertoryCity_' + obj.radomInit[0],
      nextNodeId: 'repertoryCity_' + obj.radomInit[0],
    },
    {
      nextNodeWrap: '#stoDistrictSel_wrap_' + obj.radomInit[0],
      nextNodeName: 'repertoryArea_' + obj.radomInit[0],
      nextNodeId: 'repertoryArea_' + obj.radomInit[0],
    },
  ];
  utils.setAllProDom(
    '#stoProvinceSel_wrap_' + obj.radomInit[0],
    storgeAddressSelIdObj,
    '#storageBox_' + obj.radomInit[0],
    true,
    function () {
      $('#depotAddress select,#depotAddress input').removeAttr(
        'disabled readonly',
      );
      $('#depotAddress .btn').css('display', 'inline-block');
    },
  );
});
//删除仓库地址
$('#supplierBaseDTO').on('click', '.removeDepot', function () {
  $(this).parents('.depotList').remove();
});

//拼装数据
function assembData() {
  //设置所有的框为可读
  setDiable();
  var arr = [];
  //组合仓库数据
  $('.depotList').each(function (index) {
    var se = $(this).find('select');
    var inp = $(this).find('.text-inp');
    var repertoryProvince = se.eq(0).find('option:selected').val();
    var repertoryCity = se.eq(1).find('option:selected').val();
    var repertoryArea = se.eq(2).find('option:selected').val();
    var repertoryDetail = inp.val();
    arr[index] = {
      repertoryProvince: repertoryProvince,
      repertoryCity: repertoryCity,
      repertoryArea: repertoryArea,
      repertoryDetail: repertoryDetail,
    };
  });

  // 对应生产厂商 数据
  let ManufactoryListArr = [];
  $('.ManufactoryList').each((index, item) => {
    let hiddenInpVal = $(item).find('input[name^=Manufactory]').val();
    let inpVal = $(item).find('input[type=text]').val();
    ManufactoryListArr[index] = {
      manufactoryId: hiddenInpVal,
      manufactoryName: inpVal,
    };
  });
  //运营属性
  var supplierOrganBase = $('#supplierOrganBaseDTO').serializeToJSON();
  supplierOrganBase.applicationCode = $('#organBaseApplicationCode').val();
  supplierOrganBase.buyerName = $('#buyerIdVal').val();
  //基础属性
  var supplierBaseDTO = $('#supplierBaseDTO').serializeToJSON();
  var j3 = $('#yyForm').serializeToJSON();
  Object.assign(supplierOrganBase, j3); //基础属性与运营属性合并
  //删除基础属性内的仓库数据，仓库数据单独获取
  delete supplierBaseDTO.repertoryProvince;
  delete supplierBaseDTO.repertoryCity;
  delete supplierBaseDTO.repertoryArea;
  delete supplierBaseDTO.repertoryDetail;

  delete supplierOrganBase.parentCode;
  delete supplierOrganBase.code;
  delete supplierOrganBase.value;

  //运营属性 付款、结算部分
  var supplierExtendItemVoList = [];
  $('.paymentSettlement').each(function () {
    var parentInp = $(this).find('.parentCode');

    if (parentInp.is(':checked')) {
      var parentCode = parentInp.attr('name');
      var parentCodeVal = parentInp.val();
      if ($(this).find('.childCode').length < 1) {
        supplierExtendItemVoList.push({
          [parentCode]: 0,
          code: parentCodeVal,
        });
      }
      $(this)
        .find('.childCode')
        .each(function (index) {
          var json = {};
          var cCode = $(this).find('.cCode');
          var cCodeName = cCode.attr('name');
          var cCodeValue = cCode.val();
          var cValue = $(this).find('.cValue');
          var cValueName = cValue.attr('name');
          var cValueValue = cValue.val();
          if ($.trim(cValueValue) != '') {
            json[parentCode] = parentCodeVal;
            json[cCodeName] = cCodeValue;
            json[cValueName] = cValueValue;
            supplierExtendItemVoList.push(json);
          }
        });
    }
  });
  //console.log(supplierExtendItemVoList)
  supplierOrganBase.supplierExtendItemVOList = supplierExtendItemVoList;
  supplierBaseDTO.supplierRepertoryAddressVOList = arr;
  supplierBaseDTO.supplierManufactoryVOList = ManufactoryListArr;
  //表格数据
  var table1 = $('#table1').getRowData();
  var table2 = $('#table2').getRowData();
  var table3 = $('#table3').getRowData();
  var table4 = $('#table4').getRowData();
  for (var i = 0; i < table1.length; i++) {
    if ('Y' != $('#delQualityAgreement').val()) {
      delete table1[i].id;
    }

    for (var j = 0; j < table1[i].enclosureList.length; j++) {
      delete table1[i].enclosureList[j].type;
    }
  }
  //客户委托书
  syncBusscopeToCustomer();
  for (var i = 0; i < table2.length; i++) {
    var a = $('#delClientProxyOrder').val();
    if ('Y' != $('#delClientProxyOrder').val()) {
      delete table2[i].id;
    }
    delete table2[i].authrange;
    if (
      null == table2[i].supplierClientProxyProductVOList ||
      table2[i].supplierClientProxyProductVOList.length < 1
    ) {
      table2[i].supplierClientProxyProductVOList = [];
    } else {
      $(table2[i].supplierClientProxyProductVOList).each((index, item) => {
        item['productCode'] = item['id'];
      });
    }
    for (var j = 0; j < table2[i].enclosureList.length; j++) {
      delete table2[i].enclosureList[j].type;
    }
  }
  //批准文件
  for (var i = 0; i < table3.length; i++) {
    if ('Y' != $('#delApprovalFile').val()) {
      delete table3[i].id;
    }

    table3[i].supplierApprovalFileBusinessScopeVOList =
      table3[i].supplierApprovalFileBusinessScopeVOList.length != 0
        ? table3[i].supplierApprovalFileBusinessScopeVOList
        : table3[i].scopeofoperationVo
          ? JSON.parse(table3[i].scopeofoperationVo)
          : [];
    //处理经营范围数据
    var scopeList = table3[i].supplierApprovalFileBusinessScopeVOList;
    if (scopeList && scopeList.length > 0) {
      var approvalScopeList = [];
      var scopeJson = {};
      for (var x = 0; x < scopeList.length; x++) {
        if (scopeList[x].name != '经营范围') {
          scopeJson = {};
          scopeJson.businessScopeCode = scopeList[x].id;
          scopeJson.businessScopeName = scopeList[x].name;
          approvalScopeList.push(scopeJson);
        }
      }
      table3[i].supplierApprovalFileBusinessScopeVOList = approvalScopeList;
    }

    for (var j = 0; j < table3[i].enclosureList.length; j++) {
      delete table3[i].enclosureList[j].type;
    }
  }
  //年度报告
  for (var i = 0; i < table4.length; i++) {
    if ('Y' != $('#delYearReport').val()) {
      delete table4[i].id;
    }

    for (var j = 0; j < table4[i].enclosureList.length; j++) {
      delete table4[i].enclosureList[j].type;
    }
  }
  var otherFilesArr = [];
  $("#otherFileBox input[type='checkbox']").each(function () {
    var checked = this.checked;
    if (checked) {
      var v = $(this).val();
      var json = {};
      var imgList = [];
      var newOther = $("input[id='newother" + v + "']");
      var fLen = $("input[id='newother" + v + "']").length;
      json.certificateType = v;
      if (fLen > 0) {
        for (let i = 0; i < fLen; i++) {
          var str = $(newOther[i]).val();
          str = JSON.parse(str);
          imgList = imgList.concat(str);
        }
        //imgList = JSON.parse($("input[id='newother" + v + "']").val())
        json.enclosureList = imgList;
        for (var j = 0; j < json.enclosureList.length; j++) {
          delete json.enclosureList[j].type;
        }
      } else {
        var OtherInp = $("input[id='other" + v + "']");
        var OtherInpLen = $("input[id='other" + v + "']").length;
        if (OtherInpLen > 0) {
          for (let i = 0; i < OtherInpLen; i++) {
            var str = $(OtherInp[i]).val();
            str = JSON.parse(str);
            imgList = imgList.concat(str);
          }
          //imgList = JSON.parse($("input[id='newother" + v + "']").val())
          json.enclosureList = imgList;
          for (var j = 0; j < json.enclosureList.length; j++) {
            delete json.enclosureList[j].type;
          }
        } else {
          json.enclosureList = [];
        }
      }
      otherFilesArr.push(json);
    }
  });

  supplierOrganBase.disableState = $('input[name=disableState]:checked').val(); //0; 修改取值方式，提交的时候是什么就取什么值
  var supplierAllDataVO = {
    supplierOrganBase: supplierOrganBase,
    supplierBase: supplierBaseDTO,
    supplierQualityAgreementList: table1,
    supplierClientProxyOrderList: table2,
    supplierApprovalFileList: table3,
    supplierYearReportList: table4,
    supplierOtherFileList: otherFilesArr,
  };
  return supplierAllDataVO;
}

function checkMoreTimeInput() {
  let vi = true;
  var balanceStyle_ckeck = $('.class_balanceStyle input[name=parentCode]');
  $(balanceStyle_ckeck).each(function (index, item) {
    var a = $(item).parent().siblings().find('.childName');
    $(a).each(function (ind, ite) {
      if ($(ite).text() == '结算日' || $(ite).text() == '支付日') {
        var b = $(ite).parent().siblings()[1];
        if (!b) {
          b = $(ite).parent().siblings()[0];
        }
        if (
          ($(b).parent().hasClass('displaynone') !== true &&
            $(b).parent().hasClass('displaynone') !== true) ||
          $(b).parent().css('display') !== 'none'
        ) {
          if (b.value && parseFloat(b.value) > parseFloat(b.dataset.max)) {
            vi = false;
          }

          if (!b.value || b.value === '') {
            vi = false;
          }
        }
      }
    });
  });
  return vi;
}

//保存方法
function saveData(status) {
  let vis = checkMoreTimeInput();
  if (!vis) {
    utils
      .dialog({
        content: '对账账期/实效实结结算时间录入存在问题，请核实后重新提交！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    return false;
  }
  var userSupplierInsert = $('#userSupplierInsert').val();
  var useSupplier = $('#useSupplier').val();
  var supplierAllDataVO = assembData();
  var bankAccount = supplierAllDataVO.supplierBase.bankAccount;
  var bankName = supplierAllDataVO.supplierBase.bankName;
  var supplierClientProxyOrderList =
    supplierAllDataVO.supplierClientProxyOrderList; //客户委托书
  var supplierApprovalFileList = supplierAllDataVO.supplierApprovalFileList; //批准文件
  var supplierName = supplierAllDataVO.supplierBase.supplierName; //供应商名称
  var supplierRepertoryAddressVOList =
    supplierAllDataVO.supplierBase.supplierRepertoryAddressVOList; //供应商仓库地址
  var supplierYearPortList = supplierAllDataVO.supplierYearReportList; //供应商年度报告
  var supplierManufactoryVOList =
    supplierAllDataVO.supplierBase.supplierManufactoryVOList; //供应商年度报告
  var supplierTypeId = supplierAllDataVO.supplierBase.supplierTypeId;
  //获取页面变化的数据
  var supplierOrganBaseId = $('#supplierOrganBaseId').val();
  if (supplierOrganBaseId) {
    parent.showLoading({ hideTime: 90000 });
    if (window.changeBefore) {
      var editData = returnEditData();
      if (editData) {
        supplierAllDataVO.changeDetails = editData;
      }
    }
    parent.hideLoading();
  }
  var settleMultipleDateListData = getMultipleDateList();
  let checkSameMultiple = checkSameInput(
    supplierAllDataVO.supplierOrganBase,
    settleMultipleDateListData,
  );
  if (checkSameMultiple == false) {
    utils
      .dialog({
        content: '对账账期/实销实结里已存在相同的结算时间信息，请勿重复录入！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    return false;
  }
  supplierAllDataVO.supplierOrganBase['supplierSettleMultipleDateVOList'] =
    settleMultipleDateListData;
  console.log(supplierAllDataVO);
  //校验到货周期与最小供货量
  var arrivalPeriod = supplierAllDataVO.supplierOrganBase.arrivalPeriod;
  var minSupply = supplierAllDataVO.supplierOrganBase.minSupply;
  if (arrivalPeriod && arrivalPeriod > 99999999.99) {
    utils
      .dialog({
        content: '到货周期最大为99999999.99！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
      setSaveDataReadOnly();
    }
    return false;
  }
  if (minSupply && minSupply > 99999999.99) {
    utils
      .dialog({
        content: '最小供货量最大为99999999.99！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
      setSaveDataReadOnly();
    }
    return false;
  }
  if (arrivalPeriod && !/^-?\d+\.?\d{0,2}$/.test(arrivalPeriod)) {
    utils
      .dialog({
        content: '到货周期只能输入两位小数！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
      setSaveDataReadOnly();
    }
    return false;
  }

  if (minSupply && !/^-?\d+\.?\d{0,2}$/.test(minSupply)) {
    utils
      .dialog({
        content: '最小供货量只能输入两位小数！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
      setSaveDataReadOnly();
    }
    return false;
  }
  if (supplierName && supplierName != '') {
    if (!/^[A-Za-z0-9\u4e00-\u9fa5\()（）]+$/.test(supplierName)) {
      utils
        .dialog({
          content: '供应商名称不允许有特殊字符',
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
        setSaveDataReadOnly();
      }
      return false;
    }
  }

    //验证业务对接人，以及联系方式一和联系方式二。
    var validated = validatorForm(status);
    if(!validated){
      return false;
    }
    
  if (1 == status) {
    if (
      false ==
      validataForm(
        bankAccount,
        bankName,
        supplierClientProxyOrderList,
        supplierApprovalFileList,
        supplierRepertoryAddressVOList,
        supplierYearPortList,
      )
    ) {
      if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
        setSaveDataReadOnly();
      }
      return false;
    }
    // 有效期至 跟今天比较，小于今天的都需要拦截
    let curTime = getToday();
    let dataArr = supplierApprovalFileList
      .filter((item, index) => {
        return (
          new Date(item.validityDate).getTime() < new Date(curTime).getTime()
        );
      })
      .map((item, index) => {
        return utils.getOptText(
          $('#table3 [name=certificateId]').eq(0),
          item.certificateId,
        );
      });
    if (dataArr.length > 0) {
      utils
        .dialog({
          title: '提示',
          width: 350,
          content:
            '批准文件-' + dataArr.join('、') + '-已过期，确认继续提交吗？',
          okValue: '确定',
          ok: function () {
            finalSub(
              supplierClientProxyOrderList,
              supplierApprovalFileList,
              supplierAllDataVO,
              status,
            );
          },
        })
        .showModal();
      return false;
    }
    if ($('#table2').XGrid('getRowData').length != 0) {
      let _status = checkAuthorityType();
      if (!_status) {
        utils
          .dialog({
            title: '提示',
            content: '同一个供应商，授权类型只能存在一种',
            okValue: '确定',
            ok: function () { },
          })
          .showModal();
        if ($('#userSupplierInsert').val() != 'N') {
          setSaveDataReadOnly();
        }
        return false;
      }
    }
    let msg = approvalInputValidation(null, $('#supplierTypeId').val());
    if (msg != '') {
      utils
        .dialog({
          title: '提示',
          width: 350,
          content: msg,
          okValue: '确定',
          ok: function () { },
        })
        .showModal();
      if ($('#userSupplierInsert').val() != 'N') {
        setSaveDataReadOnly();
      }
      return false;
    }
    if ($('#supplierTypeId').val() != '58') {
      let _flag = tabDataCheck();
      if (!_flag) {
        setSaveDataReadOnly();
        return false;
      }
    }
  }
  console.log('finalSub');
  // return  false
  finalSub(
    supplierClientProxyOrderList,
    supplierApprovalFileList,
    supplierAllDataVO,
    status,
  );
}

function finalSub(
  supplierClientProxyOrderList,
  supplierApprovalFileList,
  supplierAllDataVO,
  status,
) {
  let baseDataBuseScopeData = [],
    checkedBuseScopeData = [],
    bool_submit = false;
  // 因为没有返回businessScopeName  所以全部拿 ID 值比较
  $('#baseDataBuseScope font').each(function (index, item) {
    baseDataBuseScopeData.push($(item).attr('data-id'));
  });
  $('#table2 tr')
    .not(':eq(0)')
    .each(function (index, item) {
      checkedBuseScopeData = checkedBuseScopeData.concat(
        JSON.parse(
          $(item)
            .find('[row-describedby=supplierClientProxyBusinessScopeVOList]')
            .text(),
        ),
      );
    });
  checkedBuseScopeData = checkedBuseScopeData.map(function (item, index) {
    return item.businessScopeCode;
  });
  let checkbool = supplierClientProxyOrderList.some(function (item, index) {
    return item.authorityType == 1;
  });
  if (
    checkbool &&
    Array.from(new Set(checkedBuseScopeData)).length >
    baseDataBuseScopeData.length
  ) {
    //超出了可选范围
    utils
      .dialog({
        title: '提示',
        content:
          '客户委托书-经营范围授权-授权范围必须小于等于供应商-批准文件-经营范围。',
        okValue: '确定',
        ok: function () { },
      })
      .showModal();
    return false;
  }
  bool_submit = checkedBuseScopeData.some(function (item, index) {
    return baseDataBuseScopeData.indexOf(String(item)) < 0;
  });
  if (bool_submit && checkbool) {
    utils
      .dialog({
        title: '提示',
        content:
          '客户委托书-经营范围授权-授权范围必须小于等于供应商-批准文件-经营范围。',
        okValue: '确定',
        ok: function () { },
      })
      .showModal();
    return false;
  }

  console.log('验证走完了');
  //return false;
  //删除来源字典的经营范围
  if (supplierApprovalFileList && supplierApprovalFileList.length > 0) {
    for (var i = 0; i < supplierApprovalFileList.length; i++) {
      delete supplierApprovalFileList[i].scopeofoperationVo;
    }
  }

  //return false;

  //只有提交审核的时候验证委托人身份证号是都存在 1:提交审核 0:保存草稿
  if (1 == status) {
    //验证必填和正确性
    
    $.ajax({
      url: '/proxy-supplier/supplier/supplierClientProxyOrder/checkClientProxyOrder',
      data: JSON.stringify(supplierAllDataVO),
      type: 'post',
      dataType: 'json',
      async: true,
      contentType: 'application/json',
      success: function (data) {
        var passFlag = data.passFlag;
        var msg = data.msg;
        //当首营为新增供应商可编辑仓库和注册地址，当为选择不能编辑
        if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
          // 2020-07-14 [name=threeEvidenceAll],
          $(
            '[name=organizationCode],[name=taxRegistrationCode], #dataStartTime, #dateEndTime',
          ).prop('disabled', true);
          $('.depotList input, .depotList select').prop('disabled', true);
          $('#registerBox input, #registerBox select').prop('disabled', true);
        }
        if (!passFlag) {
          utils
            .dialog({
              title: '提示',
              content: msg,
              okValue: '确定',
              ok: function () {
                saveDataToBackEnd(status, supplierAllDataVO);
              },
              cancelValue: '取消',
              cancel: function () { },
            })
            .show();
        } else {
          saveDataToBackEnd(status, supplierAllDataVO);
        }
      },
      error: function () {
        submint = false;
        utils
          .dialog({ content: '保存失败', quickClose: true, timeout: 2000 })
          .showModal();
      },
    });
  } else {
    saveDataToBackEnd(status, supplierAllDataVO);
  }
}

//联系方式选项值改变-input联动
$(".tel-label").change(function () {
  var _this = $(this);
  var _input = _this.parents(".row").find('.tel-part');
  _input.val("");
  if(_this.val()==""){
    _input.attr("disabled","disabled");
  }else{
    _input.removeAttr("disabled");
  }
  switch (_this.val()) {
    case "1":
      _input.attr("placeholder", "请输入手机号");
      _input.attr("type", "number");
      break;
    case "2":
      _input.attr("placeholder", "XXXX-XXXXXXX");
      _input.attr("type", "text");
      break;
    default:
      _input.removeAttr("placeholder"); 
      _input.attr("type", "text");
  }
});

//表单验证
function validatorForm(status) {
  var selects = $(".tel-label");
  //提交审核
  if(status==1){
    //业务对接人
    if ($(".contactPerson").val() == "") {
      utils
        .dialog({ content: '业务对接人不能为空', quickClose: true, timeout: 2000 })
        .showModal();
      return false;
    }
  }
  var flag = true;
  selects.each(function (i, e) {
    //必填效验
    var _classResult = $(this).siblings(".input-group-addon").hasClass("require");
    var selected = $(this).val();
    var _val = $(this).parents(".row").find(".tel-part").val();
    //如果有值，并且是请选择
    if(_val!="" && selected==""){
      utils
            .dialog({ content: '请选择业务联系方式选项', quickClose: true, timeout: 2000 })
            .showModal();
          flag=false;
          return false;
    }
    
    switch (selected) {
        case "":
          if (_classResult) {
            //提交审核
            if(status==1){
              if (_val == "") {
                utils
                  .dialog({ content: '业务联系方式一，不能为空', quickClose: true, timeout: 2000 })
                  .showModal();
                flag=false;
                return false;
              }
            }
          }
        break;  
        case "1":
         //效验手机号
         var PhoneResult = utils.validatorPhone(_val);
         if (!PhoneResult) {
           utils
             .dialog({ content: '请输入正确手机号', quickClose: true, timeout: 2000 })
             .showModal();
             flag = false;
             return false;
         }
        break;
        case "2":
          if (_val == "") {
            utils
              .dialog({ content: '请输入电话号码', quickClose: true, timeout: 2000 })
              .showModal();
            flag = false;
            return false;
          }
          break;
        case "3":
          if (_val == "") {
            utils
              .dialog({ content: '请输入微信', quickClose: true, timeout: 2000 })
              .showModal();
            flag = false;
            return false;
          }
          break;
        case "4":
          if (_val == "") {
            utils
              .dialog({ content: '请输入其他联系方式', quickClose: true, timeout: 2000 })
              .showModal();
            flag = false;
            return false;
          }
    }
  });
  return flag;
};



//提交到后端
function saveDataToBackEnd(status, supplierAllDataVO) {
  //保存数据 加载页面
  parent.showLoading({ hideTime: 60000 });
  $.ajax({
    url: '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/insert',
    data: JSON.stringify(supplierAllDataVO),
    type: 'post',
    dataType: 'json',
    contentType: 'application/json',
    success: function (data) {
      parent.hideLoading();
      if (data.code) {
        if (false == data.flowStatus) {
          utils
            .dialog({
              title: '提示',
              content: data.flowMsg,
              width: 300,
              height: 30,
              okValue: '确定',
              ok: function () {
                utils.closeTab('/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toList');
              },
            })
            .showModal();
          $('.ui-dialog-close').hide();
          return false;
          //                    utils.dialog({content: data.flowMsg, quickClose: false, timeout: 2000}).showModal();
        } else {
          if (1 == status) {
            utils
              .dialog({
                title: '提示',
                content: '提交审核成功！',
                width: 300,
                height: 30,
                okValue: '确定',
                ok: function () {
                  utils.closeTab('/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toList');
                },
              })
              .showModal();
            $('.ui-dialog-close').hide();
            return false;
            //                		 utils.dialog({content: "提交审核成功！", quickClose: false, timeout: 2000}).showModal();
          } else if (0 == status) {
            utils
              .dialog({
                title: '提示',
                content: '保存草稿成功！',
                width: 300,
                height: 30,
                okValue: '确定',
                ok: function () {
                  utils.closeTab('/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toList');
                },
              })
              .showModal();
            $('.ui-dialog-close').hide();
            return false;
            //                		 utils.dialog({content: "保存草稿成功！", quickClose: false, timeout: 2000}).showModal();
          } else {
            utils
              .dialog({
                title: '提示',
                content: data.msg,
                width: 300,
                height: 30,
                okValue: '确定',
                ok: function () {
                  utils.closeTab('/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toList');
                },
              })
              .showModal();
            $('.ui-dialog-close').hide();
            return false;
            //                		 utils.dialog({content: data.msg, quickClose: false, timeout: 2000}).showModal();
          }
        }
        setTimeout(function () {
          utils.closeTab('/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toList');
        }, 2000);
      } else {
        if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
          setSaveDataReadOnly();
        }
        if ('Y' == data.sameBuseName || 'Y' == data.sameBuseCode) {
          checkRepeat(
            data.supplierBaseId,
            '供应商名称或营业执照已存在，请检查！查看重复供应商/修改？',
          );
        }
        //业务邮件暂时去掉录入过期批件限制
        // if ("Y" != data.sameBuseCode && "Y" != data.sameBuseName) {
        //     var msg = "";
        //     if(data.expireList && data.expireList.length>0 ){
        //         var expireMsg = "";
        //         $.each(data.expireList, function(index, value) {
        //             if("Y"==value.overDue){
        //                 expireMsg += value.approvalFileName+"，";
        //             }
        //         });
        //         if("" != expireMsg){
        //             msg = "批准文件-"+expireMsg+"已过期，请修改后再提交！";
        //         }else{
        //             msg = data.msg;
        //         }
        //     }else{
        //         msg = data.msg;
        //     }
        //     utils.dialog({
        //         title: '提示',
        //         width:300,
        //         //height:30,
        //         content: msg,
        //         okValue: '确定',
        //         ok: function () {
        //             if("Y"==data.saveRepeat){
        //                 var url = "/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toList";
        //                 utils.openTabs("firstSupplierOrganBaseFirstApply1", "供应商首营申请 ", url)
        //             }
        //         }
        //     }).showModal();
        //
        //
        //
        //
        // }
      }
    },
    error: function () {
      utils
        .dialog({ content: '请求失败', quickClose: false, timeout: 2000 })
        .showModal();
      parent.hideLoading();
    },
    complete: function () {
      parent.hideLoading();
    },
  });
}

//保存草稿
$('#saveRowData').on('click', function () {
  $('#auditStatus').val('0'); // 0:录入中
  saveData(0);
});
//提交审核
$('#submitAssert').on('click', function () {
  $('#auditStatus').val('1'); //1：审核中
  saveData(1);
});

//关闭按钮
$('#closeAssert').on('click', function () {
  var d = utils
    .dialog({
      title: '提示',
      content: '是否保存草稿？',
      width: 300,
      height: 30,
      button: [
        {
          value: '关闭',
          callback: function () {
            utils.closeTab('/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toList');
          },
        },
      ],
      okValue: '保存草稿',
      ok: function () {
        $('#auditStatus').val('0'); // 0:录入中
        saveData(0);
        d.close().remove();
        return false;
      },
    })
    .showModal();
});

//重新提交
$('#resentBtn').on('click', function () {
  var isEdit = $('#isEdit').val();
  var taskId = $('#taskId').val();
  var processId = $('#processId').val();
  var businessId = $('#businessId').val();
  AuditFirstApply(isEdit, 0, taskId, processId, '', businessId, '', 'Y');
});

//修改记录获取表单数据
function getFormData(beforeParam, afterParam, title) {
  var _str = '',
    regiestStr = '',
    storageStr = '',
    _str_reg = '',
    _str_sto = '';
  var beforeRegStr = window.changeBefore.initRegOptsTxt,
    beforeStoStr = window.changeBefore.initRepOptsTxt; // 修改前的注册地址，仓库地址信息
  for (var ite in beforeParam) {
    if (!Array.isArray(beforeParam[ite])) {
      // 只对输入框，取值比较
      if (
        afterParam[ite] != beforeParam[ite] &&
        !Array.isArray(afterParam[ite])
      ) {
        var thisTag = (
          $('input[name="' + ite + '"]').length > 0
            ? $('input[name="' + ite + '"]')
            : $('select[name="' + ite + '"]')
        )
          .parents('.input-group')
          .find('.input-group-addon')
          .text();
        if (thisTag) {
          thisTag =
            thisTag.indexOf('*') == 0 ? thisTag.substring(1).trim() : thisTag; // 修改项名称去掉*号
          if (
            !isNaN(afterParam[ite]) ||
            ite == 'registerProvince' ||
            ite == 'registerCity' ||
            ite == 'registerArea' ||
            ite == 'registerDetail'
          ) {
            // 如果取值为数字时。区域编码  // 后面的不要了    || ite == 'repertoryProvince'  || ite == 'repertoryCity' || ite == 'repertoryArea' || ite == 'repertoryDetail'
            if (
              $('[name=' + ite + ']').attr('data-value') ||
              ite == 'registerProvince' ||
              ite == 'registerCity' ||
              ite == 'registerArea' ||
              ite == 'registerDetail'
            ) {
              //地址下拉框时或者  街道信息  //后面的不要了 || ite == 'repertoryProvince'  || ite == 'repertoryCity'  || ite == 'repertoryArea'  || ite == 'repertoryDetail'
              var optText =
                $('[name=' + ite + ']  option:selected').length > 0
                  ? $('[name=' + ite + ']  option:selected').text()
                  : afterParam[ite];
              if (thisTag == '注册地址') {
                //获取提交是的注册地址的值，然后前后比较。因为 注册地址的信息只有一条，所以直接获取到前后比较就可以得出结果
                regiestStr =
                  getOptText(
                    $('[name=registerProvince]'),
                    afterParam['registerProvince'],
                  ) +
                  getOptText(
                    $('[name=registerCity]'),
                    afterParam['registerCity'],
                  ) +
                  getOptText(
                    $('[name=registerArea]'),
                    afterParam['registerArea'],
                  ) +
                  $('[name=registerDetail]').val();
                _str_reg =
                  beforeRegStr == regiestStr
                    ? ''
                    : thisTag +
                    '的值有修改,【' +
                    beforeRegStr +
                    '】修改为【' +
                    regiestStr +
                    '】;';
              }
            } else {
              //输入的可能是数字，比方天数啥的。而checkbox 一般取得的val 值也是数字，所以放一起判断，
              //如果是checkbox 就取对应值，如果不是就按输入框的值取
              if (thisTag != '仓库地址') {
                // 下面有针对仓库地址的取值方法
                var t =
                  $('[name=' + ite + ']:checked').next().length > 0
                    ? $('[name=' + ite + ']:checked')
                      .next()
                      .text()
                    : $('[name=' + ite + ']').val(); //checkbox 或者input输入框
                var beforeT =
                  $('[name=' + ite + ']:checked').next().length > 0
                    ? $('[name=' + ite + ']:not(:checked)')
                      .next()
                      .text()
                    : beforeParam[ite];
                _str +=
                  thisTag +
                  '的值有修改,【' +
                  beforeT +
                  '】修改为【' +
                  t +
                  '】;\n';
              }
            }
          } else {
            if (_str.indexOf(thisTag) >= 0 && thisTag.indexOf('地址') < 0) {
              //自动补全的框，formdata里前后比较的时候会有重复的数据，需要清除第一条数据，因为第一条数据拿到的都是ID 值
              _str = _str.replace(
                _str.substring(
                  _str.indexOf(thisTag),
                  _str.lastIndexOf('\n') + 1,
                ),
                '',
              );
            }
            if (thisTag != '仓库地址') {
              _str +=
                thisTag +
                '的值有修改,【' +
                beforeParam[ite] +
                '】修改为【' +
                afterParam[ite] +
                '】;\n';
            }
          }
        }
      }
    }
  }
  _str += (_str_reg ? _str_reg + '\n' : '') + (_str_sto ? _str_sto + '\n' : '');
  return _str != '' ? title + '信息有变更:\n' + _str : _str;
}
//修改记录获取表格数据
function getRowData(beforeParam, afterParam, tableId, title) {
  // 使用于表格数据的比较
  var _str = '';
  if (beforeParam.length == 0) {
    //初始没数据
    if (afterParam.length > 0) {
      // 新增了数据
      $(afterParam).each(function (ind, ite) {
        for (var key in ite) {
          var thisTag = $('#' + tableId)
            .find('th[row-describedby=' + key + ']')
            .text();
          if (
            thisTag &&
            $('#' + tableId)
              .find('th[row-describedby=' + key + ']')
              .css('display') != 'none'
          ) {
            // 有值并且不是隐藏字段
            thisTag =
              thisTag.indexOf('*') == 0 ? thisTag.substring(1).trim() : thisTag;
            if (Array.isArray(ite[key])) {
              // 附件list
              var urlStr = '';
              if (key == 'supplierApprovalFileBusinessScopeVOList') {
                // 经营范围
                for (var url = 0; url < afterParam[ind][key].length; url++) {
                  // of afterParam[ind][key]
                  urlStr +=
                    afterParam[ind][key][url]['name'] != '经营范围'
                      ? afterParam[ind][key][url]['name'] + ','
                      : '';
                }
              } else {
                for (var url of afterParam[ind][key]) {
                  urlStr +=
                    '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                }
              }
              _str += thisTag + '的值有修改,【】修改为【' + urlStr + '】;\n';
            } else {
              //判断是select 框  还是输入框
              var _st = $('[name=' + key + ']')
                .eq(ind)
                .find('option:selected')
                .text()
                ? $('[name=' + key + ']')
                  .eq(ind)
                  .find('option:selected')
                  .text()
                : afterParam[ind][key];
              _str += thisTag + '的值有修改,【】修改为【' + _st + '】;\n';
            }
          } else if (thisTag == '附件数据' || key == 'enclosureList') {
            // 附件数据的特殊，有值，附件list 还是隐藏的
            var urlStr = '';
            for (var url of afterParam[ind][key]) {
              urlStr +=
                '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
            }
            _str += thisTag + '的值有修改,【】修改为【' + urlStr + '】;\n';
          }
          // else if( key == 'supplierClientProxyTypeVOList'){ //选择剂型
          //     var urlStr = '';
          //     for(var url of afterParam[ind][key]){
          //         urlStr += url.jixingName+',';
          //     }
          //     _str += thisTag+'的值有修改,【】修改为【'+ urlStr+'】;\n';
          // }
          else if (key == 'supplierClientProxyProductVOList') {
            //选取商品
            var urlStr = '';
            for (var url of afterParam[ind][key]) {
              urlStr +=
                '商品编号:' +
                url['productNum'] +
                ' 通用名:' +
                url['commonName'] +
                ' 规格:' +
                url['specifications'] +
                ',';
            }
            _str += '选取商品的值有修改,【】修改为【' + urlStr + '】;\n';
          } else if (key == 'supplierClientProxyBusinessScopeVOList') {
            //经营范围
            var urlStr = '';
            for (var url of afterParam[ind][key]) {
              urlStr += url['businessScopeName'] + ',';
            }
            _str += '经营范围的值有修改,【】修改为【' + urlStr + '】;\n';
          }
        }
      });
    }
  } else {
    // 初始有数据
    if (afterParam.length > 0) {
      // 修改了数据
      //if(afterParam.length > beforeParam.length){ // 修改或新增了数据
      $(afterParam).each(function (ind, ite) {
        for (var key in ite) {
          // 遍历修改后数据的每一项
          if (beforeParam[ind]) {
            // 初始数据少，修改后数据多，
            if (beforeParam[ind][key] != ite[key]) {
              // 跟初始数据比较每一下标的对应值。虽然通过下标会有问题
              var thisTag = $('#' + tableId)
                .find('th[row-describedby=' + key + ']')
                .text();
              if (
                thisTag &&
                $('#' + tableId)
                  .find('th[row-describedby=' + key + ']')
                  .css('display') != 'none'
              ) {
                // 有值并且不是隐藏字段
                thisTag =
                  thisTag.indexOf('*') == 0
                    ? thisTag.substring(1).trim()
                    : thisTag;
                if (Array.isArray(ite[key])) {
                  // 附件list、经营范围list
                  var urlStr = '';
                  if (key == 'supplierApprovalFileBusinessScopeVOList') {
                    // 经营范围list
                    var _sortStr = '';
                    if (ite[key].length > 0) {
                      var htmlAry = [],
                        beforeLineData = '';
                      htmlAry = getLineData(ite[key]); // 修改后每行的经营范围数据
                      beforeLineData = beforeParam[ind]
                        ? getBeforeLineData(beforeParam[ind][key])
                        : ''; // 修改前每行的经营范围数据
                      if (beforeParam[ind]) {
                        if (
                          ite[key].length !=
                          beforeParam[ind][
                            'supplierApprovalFileBusinessScopeVOList'
                          ].length
                        ) {
                          // 经营范围的值有变化的时候
                          //判断前后数据的经营范围中的不同项并取值
                          var oldHTMLArr = [];
                          if (window.changeBefore.initPZWJDataObj[ind]) {
                            oldHTMLArr = getLineData(
                              window.changeBefore.initPZWJDataObj[ind][
                              'supplierApprovalFileBusinessScopeVOList'
                              ],
                            );
                            $(htmlAry).each(function (i, v) {
                              //if(v.status == oldHTMLArr[i].status){
                              _sortStr += v.name + ',';
                              //}
                            });
                          }
                        } else {
                          // 修改前后当前行的经营范围的总数一致的情况下 判断 选中的状态是否一致，
                          oldHTMLArr = getLineData(
                            window.changeBefore.initPZWJDataObj[ind][
                            'supplierApprovalFileBusinessScopeVOList'
                            ],
                          );
                          $(htmlAry).each(function (i, v) {
                            //if(v.status != oldHTMLArr[i].status){
                            _sortStr += v.name + ',';
                            //}
                          });
                        }
                      } else {
                        $(htmlAry).each(function (i, v) {
                          _sortStr += v.name + ',';
                        });
                      }
                      urlStr = _sortStr;
                    }
                  } else if (key == 'enclosureList') {
                    //附件list
                    if (afterParam[ind][key].length > 0) {
                      for (var url of afterParam[ind][key]) {
                        urlStr +=
                          '附件名称：' +
                          url.fileName +
                          ',附件路径：' +
                          url.filePath;
                      }
                    }
                  }
                  if (beforeLineData == urlStr) {
                    urlStr = '';
                  }
                  _str +=
                    urlStr != ''
                      ? '证书类型:' +
                      getOptText(
                        $('[name=certificateId]').eq(ind),
                        beforeParam[ind]['certificateId'],
                      ) +
                      ' 证书编号:' +
                      beforeParam[ind]['certificateNum'] +
                      ' 的' +
                      thisTag +
                      '值有修改,【' +
                      beforeLineData +
                      '】修改为【' +
                      urlStr +
                      '】;\n'
                      : '';
                } else {
                  //判断是select 框  还是输入框
                  var _st = $('[name=' + key + ']')
                    .eq(ind)
                    .find('option:selected')
                    .text()
                    ? $('[name=' + key + ']')
                      .eq(ind)
                      .find('option:selected')
                      .text()
                    : afterParam[ind][key];
                  //var _beforeST = ($('[name='+key+']').eq(ind).find('option:selected').text()?getOptText($('[name='+key+']').eq(ind),beforeParam[ind][key]):beforeParam[ind][key]);
                  //(beforeParam[ind]?getOptText($('[name='+key+']').eq(ind),beforeParam[ind][key]):'') //如果beforeParam[ind]为真时说明是之前就有的数据，通过方法调用拿值，如果为false时，说明是新增加的数据，所以_beforeST  应该为''.
                  var _beforeST = $('[name=' + key + ']')
                    .eq(ind)
                    .find('option:selected')
                    .text()
                    ? beforeParam[ind]
                      ? getOptText(
                        $('[name=' + key + ']').eq(ind),
                        beforeParam[ind][key],
                      )
                      : ''
                    : beforeParam[ind]
                      ? beforeParam[ind][key]
                      : '';
                  _str +=
                    thisTag +
                    '的值有修改,【' +
                    _beforeST +
                    '】修改为【' +
                    _st +
                    '】;\n';
                }
              } else if (thisTag == '附件数据' || key == 'enclosureList') {
                // 获取附件list，因为附件数据的特殊，有值，附件list 还是隐藏的
                var urlStr = '',
                  beforeFileList = '';
                if (beforeParam[ind] && beforeParam[ind][key].length > 0) {
                  for (var url of beforeParam[ind][key]) {
                    beforeFileList +=
                      '附件名称：' +
                      url.fileName +
                      ',附件路径：' +
                      url.filePath;
                  }
                } else {
                  beforeFileList = '';
                }
                if (afterParam[ind] && afterParam[ind][key].length > 0) {
                  for (var url of afterParam[ind][key]) {
                    urlStr +=
                      '附件名称：' +
                      url.fileName +
                      ',附件路径：' +
                      url.filePath;
                  }
                  _str +=
                    beforeFileList == urlStr
                      ? ''
                      : thisTag +
                      '的值有修改,【' +
                      beforeFileList +
                      '】修改为【' +
                      urlStr +
                      '】;\n';
                }
              } else if (key == 'supplierClientProxyBusinessScopeVOList') {
                // 客户委托书的经营范围
                var urlStr = '',
                  beforeFileList = '';
                if (
                  window.changeBefore.initKHWTS_jyfw &&
                  window.changeBefore.initKHWTS_jyfw.length > 0
                ) {
                  beforeFileList =
                    window.changeBefore.initKHWTS_jyfw.toString() + ',';
                } else {
                  beforeFileList = '';
                }
                if (afterParam[ind] && afterParam[ind][key].length > 0) {
                  for (var url of afterParam[ind][key]) {
                    urlStr += url.businessScopeName
                      ? url.businessScopeName + ','
                      : '';
                  }
                  //_str += (beforeFileList == urlStr?'':'委托书编号:'+beforeParam[ind]['proxyOderNo']+'的经营范围值有修改,【'+beforeFileList+'】修改为【'+ urlStr+'】;\n');
                  _str +=
                    beforeFileList == urlStr
                      ? ''
                      : beforeParam[ind]
                        ? '委托书编号:' +
                        beforeParam[ind]['proxyOderNo'] +
                        '的经营范围值有修改,【' +
                        beforeFileList +
                        '】修改为【' +
                        urlStr +
                        '】;\n'
                        : '新增行:经营范围有修改,修改为【' + urlStr + '】;\n';
                }
              }
              // else if(key == 'supplierClientProxyTypeVOList'){ // 客户委托书的选择剂型
              //     var urlStr = '', beforeFileList = '';
              //     if(window.changeBefore.initKHWTS_jx && window.changeBefore.initKHWTS_jx.length > 0){
              //         beforeFileList = window.changeBefore.initKHWTS_jx.toString()+',';
              //     }else{
              //         beforeFileList = '';
              //     }
              //     if(afterParam[ind] && afterParam[ind][key].length > 0){
              //         for(var url of afterParam[ind][key]){
              //             urlStr += (url.jixingName?(url.jixingName+','):'');
              //         }
              //         _str += (beforeFileList == urlStr?'':(beforeParam[ind]?'委托书编号:'+beforeParam[ind]['proxyOderNo']+'的剂型有修改,【'+beforeFileList+'】修改为【'+ urlStr+'】;\n':'新增行:剂型有修改,修改为【'+urlStr+'】;\n'));
              //     }
              // }
              else if (key == 'supplierClientProxyProductVOList') {
                // 客户委托书的选取商品
                var urlStr = '',
                  beforeFileList = '';
                if (
                  window.changeBefore.initKHWTS_sp &&
                  window.changeBefore.initKHWTS_sp.length > 0
                ) {
                  // for(var i = 0; i<window.changeBefore.initKHWTS_sp.length; i++ ){
                  //     beforeFileList += '商品编号:'+window.changeBefore.initKHWTS_sp[i].productCode+' 通用名:'+window.changeBefore.initKHWTS_sp[i].commonName+' 规格:'+window.changeBefore.initKHWTS_sp[i].specifications
                  // }
                  for (
                    var i = 0;
                    i < window.changeBefore.initKHWTS_sp.length;
                    i++
                  ) {
                    for (
                      var j = 0;
                      j < window.changeBefore.initKHWTS_sp[i].length;
                      j++
                    ) {
                      if (i == ind) {
                        beforeFileList +=
                          '商品编号:' +
                          window.changeBefore.initKHWTS_sp[i][j].productCode +
                          ' 通用名:' +
                          window.changeBefore.initKHWTS_sp[i][j].commonName +
                          ' 规格:' +
                          window.changeBefore.initKHWTS_sp[i][j].specifications;
                      }
                    }
                  }
                } else {
                  beforeFileList = '';
                }
                if (afterParam[ind] && afterParam[ind][key].length > 0) {
                  for (var url of afterParam[ind][key]) {
                    //这个if判断是因为，如果当前行默认是已经有选择商品的数据，同时并没有对当前行的已选商品进行修改。
                    //那么就不会存在下面的productNum的属性。
                    //所以可以理解为前后数据一致，所以把旧的数据赋值给urlStr.
                    if (url['productNum']) {
                      urlStr +=
                        '商品编号:' +
                        url['productNum'] +
                        ' 通用名:' +
                        url['commonName'] +
                        ' 规格:' +
                        url['specifications'] +
                        ',';
                    } else {
                      urlStr = beforeFileList;
                    }
                  }
                  //_str += (beforeFileList == urlStr?'':'委托书编号:'+beforeParam[ind]['proxyOderNo']+'的剂型有修改,【'+beforeFileList+'】修改为【'+ urlStr+'】;\n');
                  _str +=
                    beforeFileList == urlStr
                      ? ''
                      : beforeParam[ind]
                        ? '委托书编号:' +
                        beforeParam[ind]['proxyOderNo'] +
                        '的选取商品有修改,【' +
                        beforeFileList +
                        '】修改为【' +
                        urlStr +
                        '】;\n'
                        : '新增行:选取商品有修改,修改为【' + urlStr + '】;\n';
                }
              }
            }
          } else {
            var thisTag = $('#' + tableId)
              .find('th[row-describedby=' + key + ']')
              .text();
            if (
              thisTag &&
              $('#' + tableId)
                .find('th[row-describedby=' + key + ']')
                .css('display') != 'none'
            ) {
              // 有值并且不是隐藏字段
              thisTag =
                thisTag.indexOf('*') == 0
                  ? thisTag.substring(1).trim()
                  : thisTag;
              if (Array.isArray(ite[key])) {
                // 附件list、经营范围list
                var urlStr = '';
                if (key == 'supplierApprovalFileBusinessScopeVOList') {
                  // 经营范围list
                  var _sortStr = '';
                  if (ite[key].length > 0) {
                    var htmlAry = [],
                      beforeLineData = '';
                    htmlAry = getLineData(ite[key]); // 修改后每行的经营范围数据
                    beforeLineData = beforeParam[ind]
                      ? getBeforeLineData(beforeParam[ind][key])
                      : ''; // 修改前每行的经营范围数据
                    if (beforeParam[ind]) {
                      if (
                        ite[key].length !=
                        beforeParam[ind][
                          'supplierApprovalFileBusinessScopeVOList'
                        ].length
                      ) {
                        // 经营范围的值有变化的时候
                        //判断前后数据的经营范围中的不同项并取值
                        var oldHTMLArr = [];
                        if (window.changeBefore.initPZWJDataObj[ind]) {
                          oldHTMLArr = getLineData(
                            window.changeBefore.initPZWJDataObj[ind][
                            'supplierApprovalFileBusinessScopeVOList'
                            ],
                          );
                          $(htmlAry).each(function (i, v) {
                            //if(v.status == oldHTMLArr[i].status){
                            _sortStr += v.name + ',';
                            //}
                          });
                        }
                      } else {
                        // 修改前后当前行的经营范围的总数一致的情况下 判断 选中的状态是否一致，
                        oldHTMLArr = getLineData(
                          window.changeBefore.initPZWJDataObj[ind][
                          'supplierApprovalFileBusinessScopeVOList'
                          ],
                        );
                        $(htmlAry).each(function (i, v) {
                          //if(v.status != oldHTMLArr[i].status){
                          _sortStr += v.name + ',';
                          //}
                        });
                      }
                    } else {
                      $(htmlAry).each(function (i, v) {
                        _sortStr += v.name + ',';
                      });
                    }
                    urlStr = _sortStr;
                  }
                } else if (key == 'enclosureList') {
                  //附件list
                  for (var url of afterParam[ind][key]) {
                    urlStr +=
                      '附件名称：' +
                      url.fileName +
                      ',附件路径：' +
                      url.filePath;
                  }
                }
                if (beforeLineData == urlStr) {
                  urlStr = '';
                }
                _str +=
                  urlStr != ''
                    ? thisTag +
                    '的值有修改,【' +
                    beforeLineData +
                    '】修改为【' +
                    urlStr +
                    '】;\n'
                    : '';
              } else {
                //判断是select 框  还是输入框
                var _st = $('[name=' + key + ']')
                  .eq(ind)
                  .find('option:selected')
                  .text()
                  ? $('[name=' + key + ']')
                    .eq(ind)
                    .find('option:selected')
                    .text()
                  : afterParam[ind][key];
                //var _beforeST = ($('[name='+key+']').eq(ind).find('option:selected').text()?getOptText($('[name='+key+']').eq(ind),beforeParam[ind][key]):beforeParam[ind][key]);
                //(beforeParam[ind]?getOptText($('[name='+key+']').eq(ind),beforeParam[ind][key]):'') //如果beforeParam[ind]为真时说明是之前就有的数据，通过方法调用拿值，如果为false时，说明是新增加的数据，所以_beforeST  应该为''.
                var _beforeST = $('[name=' + key + ']')
                  .eq(ind)
                  .find('option:selected')
                  .text()
                  ? beforeParam[ind]
                    ? getOptText(
                      $('[name=' + key + ']').eq(ind),
                      beforeParam[ind][key],
                    )
                    : ''
                  : beforeParam[ind]
                    ? beforeParam[ind][key]
                    : '';
                _str +=
                  thisTag +
                  '的值有修改,【' +
                  _beforeST +
                  '】修改为【' +
                  _st +
                  '】;\n';
              }
            } else if (thisTag == '附件数据' || key == 'enclosureList') {
              // 获取附件list，因为附件数据的特殊，有值，附件list 还是隐藏的
              var urlStr = '',
                beforeFileList = '';
              if (beforeParam[ind] && beforeParam[ind][key].length > 0) {
                for (var url of afterParam[ind][key]) {
                  beforeFileList +=
                    '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                }
              } else {
                beforeFileList = '';
              }
              if (afterParam[ind][key].length > 0) {
                for (var url of afterParam[ind][key]) {
                  urlStr +=
                    '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                }
                _str +=
                  thisTag +
                  '的值有修改,【' +
                  beforeFileList +
                  '】修改为【' +
                  urlStr +
                  '】;\n';
              }
            } else if (key == 'supplierClientProxyBusinessScopeVOList') {
              // 客户委托书的经营范围
              var urlStr = '',
                beforeFileList = '';
              if (
                window.changeBefore.initKHWTS_jyfw &&
                window.changeBefore.initKHWTS_jyfw.length > 0
              ) {
                beforeFileList =
                  window.changeBefore.initKHWTS_jyfw.toString() + ',';
              } else {
                beforeFileList = '';
              }
              if (afterParam[ind] && afterParam[ind][key].length > 0) {
                for (var url of afterParam[ind][key]) {
                  urlStr += url.businessScopeName
                    ? url.businessScopeName + ','
                    : '';
                }
                _str +=
                  beforeFileList == urlStr
                    ? ''
                    : beforeParam[ind]
                      ? '委托书编号:' +
                      beforeParam[ind]['proxyOderNo'] +
                      '的经营范围值有修改,【' +
                      beforeFileList +
                      '】修改为【' +
                      urlStr +
                      '】;\n'
                      : '新增行:经营范围有修改,修改为【' + urlStr + '】;\n';
              }
            }
            // else if(key == 'supplierClientProxyTypeVOList'){ // 客户委托书的选择剂型
            //     var urlStr = '', beforeFileList = '';
            //     if(window.changeBefore.initKHWTS_jx && window.changeBefore.initKHWTS_jx.length > 0){
            //         beforeFileList = window.changeBefore.initKHWTS_jx.toString()+',';
            //     }else{
            //         beforeFileList = '';
            //     }
            //     if(afterParam[ind] && afterParam[ind][key].length > 0){
            //         for(var url of afterParam[ind][key]){
            //             urlStr += (url.jixingName?(url.jixingName+','):'');
            //         }
            //         //_str += (beforeFileList == urlStr?'':'委托书编号:'+beforeParam[ind]['proxyOderNo']+'的剂型有修改,【'+beforeFileList+'】修改为【'+ urlStr+'】;\n');
            //         _str += (beforeFileList == urlStr?'':(beforeParam[ind]?'委托书编号:'+beforeParam[ind]['proxyOderNo']+'的剂型有修改,【'+beforeFileList+'】修改为【'+ urlStr+'】;\n':'新增行:剂型有修改,修改为【'+urlStr+'】;\n'));
            //     }
            // }
            else if (key == 'supplierClientProxyProductVOList') {
              // 客户委托书的选取商品
              var urlStr = '',
                beforeFileList = '';
              if (
                window.changeBefore.initKHWTS_sp &&
                window.changeBefore.initKHWTS_sp.length > 0
              ) {
                // for(var i = 0; i<window.changeBefore.initKHWTS_sp.length; i++ ){
                //     beforeFileList += '商品编号:'+window.changeBefore.initKHWTS_sp[i].productCode+' 通用名:'+window.changeBefore.initKHWTS_sp[i].commonName+' 规格:'+window.changeBefore.initKHWTS_sp[i].specifications
                // }
                for (
                  var i = 0;
                  i < window.changeBefore.initKHWTS_sp.length;
                  i++
                ) {
                  for (
                    var j = 0;
                    j < window.changeBefore.initKHWTS_sp[i].length;
                    j++
                  ) {
                    if (i == ind) {
                      beforeFileList +=
                        '商品编号:' +
                        window.changeBefore.initKHWTS_sp[i][j].productCode +
                        ' 通用名:' +
                        window.changeBefore.initKHWTS_sp[i][j].commonName +
                        ' 规格:' +
                        window.changeBefore.initKHWTS_sp[i][j].specifications;
                    }
                  }
                }
              } else {
                beforeFileList = '';
              }
              if (afterParam[ind] && afterParam[ind][key].length > 0) {
                for (var url of afterParam[ind][key]) {
                  //这个if判断是因为，如果当前行默认是已经有选择商品的数据，同时并没有对当前行的已选商品进行修改。
                  //那么就不会存在下面的productNum的属性。
                  //所以可以理解为前后数据一致，所以把旧的数据赋值给urlStr.
                  if (url['productNum']) {
                    urlStr +=
                      '商品编号:' +
                      url['productNum'] +
                      ' 通用名:' +
                      url['commonName'] +
                      ' 规格:' +
                      url['specifications'] +
                      ',';
                  } else {
                    urlStr = beforeFileList;
                  }
                }
                _str +=
                  beforeFileList == urlStr
                    ? ''
                    : beforeParam[ind]
                      ? '委托书编号:' +
                      beforeParam[ind]['proxyOderNo'] +
                      '的选取商品有修改,【' +
                      beforeFileList +
                      '】修改为【' +
                      urlStr +
                      '】;\n'
                      : '新增行:选取商品有修改,修改为【' + urlStr + '】;\n';
              }
            }
          }
        }
      });
      if (title == '质量保证协议') {
        _str += getDeleteData(beforeParam, afterParam, 'signName', '签订人');
      } else if (title == '客户委托书') {
        _str += getDeleteData(
          beforeParam,
          afterParam,
          'proxyOderNo',
          '委托书编号',
        );
      } else if (title == '批准文件') {
        _str += getDeleteData(
          beforeParam,
          afterParam,
          'certificateNum',
          '证书编号',
        );
      } else if (title == '年度报告') {
        _str += getDeleteData(
          beforeParam,
          afterParam,
          'reportDate',
          '报告年份',
        );
      }
      //}else{//有数据被删除
      //     if(title == '质量保证协议'){
      //         _str += getDeleteData(beforeParam,afterParam,'signName','签订人');
      //     }else if(title =="客户委托书"){
      //         _str += getDeleteData(beforeParam,afterParam,'proxyOderNo','委托书编号');
      //     }else if(title =="批准文件"){
      //         _str += getDeleteData(beforeParam,afterParam,'certificateNum','证书编号');
      //     }else if(title =="年度报告"){
      //         _str += getDeleteData(beforeParam,afterParam,'reportDate','报告年份');
      //     }
      //}
    } else {
      // 删除了数据
      _str = title + '清空了数据';
    }
  }
  return _str != '' ? title + '信息有变更:\n' + _str : _str;
}
//获取表格删除的信息
function getDeleteData(BPD, APD, key, str) {
  //获取两个对象的不同项
  var result = [],
    _returnStr = '';
  for (var i = 0; i < BPD.length; i++) {
    var obj = BPD[i];
    var num = obj[key];
    var flag = false;
    for (var j = 0; j < APD.length; j++) {
      var aj = APD[j];
      var n = aj[key];
      if (n == num) {
        flag = true;
        break;
      }
    }
    if (!flag) {
      result.push(obj);
    }
  }
  for (var i = 0; i < result.length; i++) {
    var urlStr = '';
    urlStr = str + result[i][key] + '的数据被删除了;\n';
    _returnStr += urlStr;
  }
  return _returnStr;
}
//其它附件filelist
function getOtherData(beforeParam, afterParam, title) {
  var _str = '',
    beforeStr = '';
  if (beforeParam.length == 0) {
    // 初始没数据
    if (afterParam.length > 0) {
      $(afterParam).each(function (index, item) {
        var val = $(item).val();
        val = JSON.parse(val);
        for (var i of val) {
          _str += i.fileName + ':' + i.filePath;
        }
      });
    }
  } else {
    if (afterParam.length > 0) {
      $(beforeParam).each(function (index, item) {
        var val = $(item).val();
        val = JSON.parse(val);
        for (var i of val) {
          beforeStr += i.fileName + ':' + i.filePath;
        }
      });
      $(afterParam).each(function (index, item) {
        var val = $(item).val();
        val = JSON.parse(val);
        for (var i of val) {
          _str += i.fileName + ':' + i.filePath;
        }
      });
    } else {
      _str = title + '清空了数据';
    }
  }
  return _str != '' ? '\n【' + beforeStr + '】修改为【' + _str + '】;' : '';
}
//其他附件选中项
function getOtherValData(beforeParam, afterParam, title) {
  var _str = '',
    beforeStr = '';
  if (beforeParam.length == 0) {
    // 初始没数据
    if (afterParam.length > 0) {
      $(afterParam).each(function (index, item) {
        _str += item.name + ',';
      });
    }
  } else {
    $(beforeParam).each(function (i, v) {
      beforeStr += v.name + ',';
    });
    if (afterParam.length > 0) {
      $(afterParam).each(function (index, item) {
        _str += item.name + ',';
      });
    } else {
      _str = '';
    }
  }
  if (beforeStr == _str) {
    _str = '';
  }
  return _str != ''
    ? title + '信息有变更:\n' + '【' + beforeStr + '】修改为【' + _str + '】;'
    : _str;
}
// 获取select框修改前的值
function getOptText(el, ev) {
  var _optText = '';
  var opts = $(el).find('option');
  $(opts).each(function (k, v) {
    if ($(this).val() == ev) {
      _optText = $(this).text();
    }
  });
  return _optText;
}

//经营范围
function getLineData(data) {
  var parmId = 'id',
    parmName = 'name';
  var arr = [];
  for (var i = 0; i < data.length; i++) {
    if (data[i].id != 0) {
      // 去掉经营范围 这一层，不计入实际数据数组
      if (data[i].name.indexOf('I类') > -1) {
        var _pobj = {};
        _pobj.id = data[i][parmId];
        _pobj.name = data[i][parmName];
        _pobj.status = false;
        arr.push(_pobj);
      } else {
        if (data[i].name.indexOf('类.') > 0 || data[i].name.indexOf('类') < 0) {
          //不展示 1类和11 类张氏虚拟的父节点，不是真实存在的经营范围
          var _pobj = {};
          _pobj.id = data[i][parmId];
          _pobj.name = data[i][parmName];
          _pobj.status = false;
          arr.push(_pobj);
        }
      }
    }
  }
  return arr;
}
//经营范围获取修改前的数据
function getBeforeLineData(data) {
  var parmId = 'id',
    parmName = 'name';
  var arrStr = '';
  for (var i = 0; i < data.length; i++) {
    if (data[i].id != 0) {
      // 去掉经营范围 这一层，不计入实际数据数组
      if (data[i].name.indexOf('I类') > -1) {
        arrStr += data[i].name + ',';
      } else {
        if (data[i].name.indexOf('类.') > 0 || data[i].name.indexOf('类') < 0) {
          //不展示 1类和11 类张氏虚拟的父节点，不是真实存在的经营范围
          arrStr += data[i].name + ',';
        }
      }
    }
  }
  return arrStr;
}
//审核通过
$('#passBtn').on('click', function () {
  var isEdit = $('#isEdit').val();
  var taskId = $('#taskId').val();
  var processId = $('#processId').val();
  var businessId = $('#businessId').val();
  AuditFirstApply(isEdit, 0, taskId, processId, '', businessId, '', '');
});
//审核不通过
$('#refuseBtn').on('click', function () {
  var isEdit = $('#isEdit').val();
  var taskId = $('#taskId').val();
  var processId = $('#processId').val();
  var businessId = $('#businessId').val();
  AuditFirstApply(isEdit, 1, taskId, processId, '', businessId, '', '');
});
//关闭审核
$('#closeWorkFlowBtn').on('click', function () {
  var isEdit = $('#isEdit').val();
  var taskId = $('#taskId').val();
  var processId = $('#processId').val();
  var businessId = $('#businessId').val();
  var organBaseApplicationCode = $('#organBaseApplicationCode').val();
  AuditFirstApply(
    isEdit,
    2,
    taskId,
    processId,
    '',
    businessId,
    organBaseApplicationCode,
    '',
  );
});

//点击放大镜触发 搜索供应商
$(document).on('click', '#supplierBtn', function (ev) {
  searchSupplierBase(ev);
});
//回车触发 搜索供应商
$(document).on('keydown', '#supplierName', function (ev) {
  if ('Y' != $('#notSelectBaseDataFlag').val()) {
    if (ev.keyCode == 13) {
      searchSupplierBase(ev);
    }
  }
});

//初始化仓库地址回显 需要给select加 data-value 属性
function initDistpicker() {
  $('[data-toggle="distpicker"]').each(function () {
    //省
    var val1 = $.trim($(this).find('select').eq(0).attr('data-value'));
    if (val1 && val1 != '') {
      $(this).find('select').eq(0).val(val1);
      $(this).find('select').eq(0).change();
    }
    //市
    var val2 = $.trim($(this).find('select').eq(1).attr('data-value'));
    if (val1 && val1 != '') {
      $(this).find('select').eq(1).val(val2);
      $(this).find('select').eq(1).change();
    }
    //区
    var val3 = $.trim($(this).find('select').eq(2).attr('data-value'));
    if (val1 && val1 != '') {
      $(this).find('select').eq(2).val(val3);
    }
  });
}

(function () {
  $("[name='repertoryProvince']").on('change', function () {
    console.log('selectProvinceChange');
  });
})();

//获取仓库地址
function distpickerHTML(n, m) {
  var len = n ? n : 1;
  var html = '';
  //2018.9.4,RL,下面html中新增了非空校验,bug2386
  let radomInit = [];
  for (let i = 0; i < len; i++) {
    let _int = parseInt(Math.random() * 100 + Math.random() * 1000);
    html += `<div class="col-md-6 depotList">
	        <div class="input-group">
	            <div class="input-group-addon require"><i class="text-require">*  </i>仓库地址</div>
	            <div class="form-control form-inline distpicker" id="storageBox_${_int}">
	                <div class="row">
	                    <div class="form-group col-md-2" id="stoProvinceSel_wrap_${_int}">
	                        <select class="form-control repertoryProvinceSelect" name="repertoryProvince_${_int}" id="repertoryProvince_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-2" id="stoCitySel_wrap_${_int}">
	                        <select class="form-control repertoryCitySelect" name="repertoryCity_${_int}" id="repertoryCity_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-2" id="stoDistrictSel_wrap_${_int}">
	                        <select class="form-control repertoryAreaSelect" name="repertoryArea_${_int}" id="repertoryArea_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-5">
	                        <input type="text" class="form-control repertoryDetailSelect text-inp Filter_SpaceAndFiveStrLen_Class" name="repertoryDetail" disabled/>
	                    </div>
	                    <div class="form-group btn-box col-md-1">
	                        <button type="button" class="btn ${i == 0 && !m ? 'addDepot' : 'removeDepot'
      }" style="display: none;">
	                            <span class="glyphicon ${i == 0 && !m
        ? 'glyphicon-plus'
        : 'glyphicon-minus'
      }" aria-hidden="true"></span>
	                        </button>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </div>`;
    radomInit.push(_int);
  }
  return {
    html: html,
    radomInit: radomInit,
  };
}

//禁止批准文件内经营范围树可选
function chkDis(obj, zTree) {
  if (obj && '' != obj) {
    zTree.setChkDisabled(zTree.getNodeByParam('id', obj.id), true);
    var children = obj.children;
    if (children && children.length > 0) {
      for (var i = 0; i < children.length; i++) {
        zTree.setChkDisabled(zTree.getNodeByParam('id', children[i].id), true);
        chkDis(children[i], zTree);
      }
    }
  }
}

/**
 * 对页面中表单元素进行赋值
 * @param json 必传 eg:{"a":1,"b":2}
 * @param jq对象 表示:从当前对象下找对应的元素进行赋值
 * */
function loadData(json, el) {
  var obj = json;
  var key, value, tagName, type, arr, thisVal, $el;
  for (x in obj) {
    key = x;
    value = obj[x];
    if (el) {
      $el = el.find("[name='" + key + "']");
    } else {
      $el = $("[name='" + key + "']");
    }
    $el.each(function () {
      tagName = $(this)[0].tagName;
      type = $(this).attr('type');
      thisVal = $(this).val();
      if (tagName == 'INPUT') {
        if (type == 'radio') {
          $(this).attr('checked', thisVal == value);
        } else if (type == 'checkbox') {
          if ($.type(value) == 'array') {
            arr = value;
          } else if ($.type(value) == 'string') {
            arr = value.split(',');
          }
          for (var i = 0; i < arr.length; i++) {
            if (thisVal == arr[i]) {
              $(this).attr('checked', true);
              break;
            }
          }
        } else {
          $(this).val(value);
        }
      } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
        $(this).val(value);

        //
        /*if($(this).attr("name")=="registerProvince"){
                    $(this).siblings('.registeredAddressProvinceInputSearch').val(value);
                }*/
        //
      }
    });
  }
}

//新增行
function addRow(buttonId, tableId) {
  $(buttonId).on('click', function () {
    var rowNumber = $(tableId).find('tr').not(':first').length + 1;
    $(tableId).addRowData({ id: rowNumber });
  });
}

//删除行
function deleRow(buttonId, tableId, fn) {
  $(buttonId).on('click', function () {
    var selectRow = $(tableId).XGrid('getSeleRow');
    if (!selectRow) {
      utils
        .dialog({ content: '请选择删除行！', quickClose: true, timeout: 2000 })
        .showModal();
    } else {
      $(tableId).XGrid('delRowData', selectRow.id);
      utils
        .dialog({ content: '删除成功！', quickClose: true, timeout: 2000 })
        .showModal();
    }
    setTimeout(function () {
      fn && fn();
    }, 1);
  });
}

//质量保证协议
function initTable1(supplierOrganBaseId, type) {
  var xGridDate = [];
  $.ajax({
    url: '/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getQualityAgreementList',
    data: { correlationId: supplierOrganBaseId, type: type },
    type: 'post',
    dataType: 'json',
    success: function (data) {
      if (data.result.list.length > 0) {
        $('#delQualityAgreement').val('Y');
        var dataList = data.result.list;
        for (var i = 0; i < dataList.length; i++) {
          var a = {
            id: dataList[i].id,
            signDate: dataList[i].signDate,
            validDate: dataList[i].validDate,
            signName: dataList[i].signName,
            enclosureCount: dataList[i].enclosureCount,
            enclosureList: dataList[i].enclosureList,
          };
          xGridDate.push(a);
        }
      } else {
        xGridDate = [];
      }

      $('#table1')
        .XGrid('setGridParam', {
          data: xGridDate,
          pager: '#grid_page1',
        })
        .trigger('reloadGrid');
    },
  });
}

//客户委托书
function initTable2(supplierOrganBaseId, type) {
  if (supplierOrganBaseId) {
    var xGridDate = [];
    $.ajax({
      url: '/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getClientProxyOrderList',
      data: { correlationId: supplierOrganBaseId, type: type },
      type: 'post',
      dataType: 'json',
      success: function (data) {
        if (data.result.list.length > 0) {
          $('#delClientProxyOrder').val('Y');
          var dataList = data.result.list;
          for (var i = 0; i < dataList.length; i++) {
            var arr = []; //客户委托书对应的商品
            if (
              dataList[i].supplierClientProxyProductVOList &&
              dataList[i].supplierClientProxyProductVOList.length > 0
            ) {
              $(dataList[i].supplierClientProxyProductVOList).each(function (
                index,
                item,
              ) {
                var obj = {};
                obj.productCode = item.productCode;
                arr.push(obj);
              });
            }

            //2018.9.3 10:36,RL,新增剂型,begin
            var arrDrug = []; //客户委托书对应的剂型
            if (
              dataList[i].supplierClientProxyTypeVOList &&
              dataList[i].supplierClientProxyTypeVOList.length > 0
            ) {
              $.each(
                dataList[i].supplierClientProxyTypeVOList,
                function (iDrug, vDrug) {
                  if (vDrug['jixingId']) {
                    arrDrug.push({
                      jixingId: vDrug['jixingId'],
                    });
                  }
                },
              );
            }
            // 2018.9.3 10:36,RL,新增剂型,end

            var a = {
              id: dataList[i].id,
              proxyOderNo: dataList[i].proxyOderNo,
              mandataryName: dataList[i].mandataryName,
              mandatarySex: dataList[i].mandatarySex,
              mandataryTel: dataList[i].mandataryTel,
              mandataryCertificateNumber:
                dataList[i].mandataryCertificateNumber,
              mandataryAddress: dataList[i].mandataryAddress,
              identityValidDate: dataList[i].identityValidDate,
              proxyValidDate: dataList[i].proxyValidDate,
              authorityType: dataList[i].authorityType,
              enclosureCount: dataList[i].enclosureCount,
              supplierClientProxyProductVOList: arr,
              supplierClientProxyBusinessScopeVOList:
                dataList[i].supplierClientProxyBusinessScopeVOList,
              enclosureList: dataList[i].enclosureList,

              //2018.9.3 10:36,RL,新增剂型,begin
              supplierClientProxyTypeVOList: arrDrug,
              // 2018.9.3 10:36,RL,新增剂型,end
            };
            xGridDate.push(a);
          }
        } else {
          xGridDate = [];
        }
        $('#table2')
          .XGrid('setGridParam', {
            data: xGridDate,
            pager: '#grid_page2',
          })
          .trigger('reloadGrid');
      },
    });
  }
}

//批准文件
function initTable3(supplierBaseId, type, dataType) {
  idArr = [];
  var xGridDate = [];
  $.ajax({
    url: '/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getApprovalFileList',
    data: { correlationId: supplierBaseId, type: type, dataType: dataType },
    type: 'post',
    dataType: 'json',
    success: function (data) {
      var zTreeNodes = [];
      if (data.result && data.result.list.length > 0) {
        $('#delApprovalFile').val('Y');
        var dataList = data.result.list;
        for (var i = 0; i < dataList.length; i++) {
          zTreeNodes = [
            {
              id: 0,
              name: '经营范围',
              // open: true,//展开
              children: [],
            },
          ];
          if (
            dataList[i].scopeofoperationVo &&
            dataList[i].scopeofoperationVo.length > 0
          ) {
            var scopeofoperationVo = dataList[i].scopeofoperationVo;
            $(scopeofoperationVo).each(function (index, item) {
              var _obj = {};
              _obj.id = item.scopeId;
              _obj.name = item.scopeName;
              item.children && item.children.length > 0
                ? (_obj.children = [])
                : '';
              $(item.children).each(function (cindex, citem) {
                var _obj_child = {};
                _obj_child.id = citem.scopeId;
                _obj_child.name = citem.scopeName;
                _obj.children.push(_obj_child);
              });
              zTreeNodes[0].children.push(_obj);
            });
          }
          var certJson = {};
          certJson[dataList[i].certificateId] =
            dataList[i].supplierApprovalFileBusinessScopeVOList;
          idArr.push(certJson);
          if (
            !dataList[i].scopeofoperationVo ||
            dataList[i].scopeofoperationVo.length == 0
          ) {
            zTreeNodes = '';
          }
          var a = {
            id: dataList[i].id,
            certificateId: dataList[i].certificateId,
            certificateNum: dataList[i].certificateNum,
            supplierApprovalFileBusinessScopeVOList: zTreeNodes,
            // certificationOffice: dataList[i].certificationOffice,
            certificationDate: dataList[i].certificationDate,
            validityDate: dataList[i].validityDate,
            enclosureCount: dataList[i].enclosureCount,
            enclosureList: dataList[i].enclosureList,
            scopeofoperationVo: JSON.stringify(dataList[i].scopeofoperationVo),
          };
          xGridDate.push(a);
        }
      } else {
        xGridDate = [];
      }

      $('#table3').XGrid('clearGridData');
      $('#table3')
        .XGrid('setGridParam', {
          data: xGridDate,
          pager: '#grid_page3',
          gridComplete: function () {
            //拼装文件树
            setTimeout(function () {
              $('#table3 tr').each(function (index) {
                if (index != 0) {
                  var credentialId = $(this)
                    .find(
                      "td[row-describedby='certificateId'] select option:selected",
                    )
                    .val();
                  var zTreeId = $(this).find('.operScopeZtree').attr('id');
                  var zTree = $.fn.zTree.getZTreeObj(zTreeId);
                  /**
                   * RM 2018-10-31
                   * 获取树形的所有节点，存入数组，下面比较时需要
                   * 判断所有节点的长度，与 返回的数据节点的长度是否一致，一致的话说明全部已选，需要要对伏父节点经营范围也要勾选
                   */
                  var zTreeNodes = zTree.getNodes()[0];
                  var zTreeNodesArr = [];
                  if (zTreeNodes) {
                    zTreeNodesArr.push(zTreeNodes.name);
                    function getNodeData(n) {
                      var el = n.children;
                      if (el.length > 0) {
                        for (var i = 0; i < el.length; i++) {
                          zTreeNodesArr.push(el[i].name);
                          if (el[i].children && el[i].children.length > 0) {
                            getNodeData(el[i]);
                          }
                        }
                      }
                    }
                    getNodeData(zTreeNodes);
                    //2018-10-31 END
                    for (var i = 0; i < idArr.length; i++) {
                      var item = idArr[i];
                      for (var n in item) {
                        if (n == credentialId) {
                          var aList = item[n];
                          if (null != aList && aList.length > 0) {
                            /**
                             * RM 2018-10-31
                             * 下面三元运算判断
                             * 当子节点全部勾选时，回显，需要给父节点经营范围也打勾。
                             */
                            aList[0].id == 0
                              ? aList.length == zTreeNodesArr.length
                                ? zTree.checkNode(
                                  zTree.getNodeByParam('id', 0),
                                  true,
                                )
                                : ''
                              : aList.length + 1 == zTreeNodesArr.length
                                ? zTree.checkNode(
                                  zTree.getNodeByParam('id', 0),
                                  true,
                                )
                                : '';
                            for (var s = 0; s < aList.length; s++) {
                              /**
                               * RM 2018-10-07
                               * 表格中某一条数据，可能没有经营范围，需要加判断，要不报错
                               */
                              //zTree != null ? zTree.checkNode(zTree.getNodeByParam("id", Number(aList[s].businessScopeCode)), true) : '';
                              //1018DT：新增渲染时对tr和选中ID的判断条件
                              if (
                                zTree != null &&
                                $(this).prop('id') == aList[s].approvalFileId
                              ) {
                                if (
                                  zTree.getNodeByParam(
                                    'id',
                                    Number(aList[s].businessScopeCode),
                                  )
                                ) {
                                  zTree.checkNode(
                                    zTree.getNodeByParam(
                                      'id',
                                      Number(aList[s].businessScopeCode),
                                    ),
                                    true,
                                  );
                                }
                              }
                              initBaseDataBuseScope();
                            }
                          }
                          break;
                        }
                      }
                    }
                  }
                }
              });
              if ('insert' == pageProperty || 'Y' == $('#selfEdit').val()) {
              } else {
                setTable3Edit();
              }
              //                        setTable3Edit();
            }, 3);
          },
        })
        .trigger('reloadGrid');
    },
  });
}

//设置批准文件下不可编辑
function setTable3Edit() {
  if (!$('#supplierBaseId').val() || $('#supplierBaseId').val() != '') {
    var a = $('#selfEdit').val();
    var b = $('#useSupplier').val();
    if (
      'Y' != $('#selfEdit').val() ||
      'N' != $('#useSupplier').val() ||
      'Y' == $('#userSupplierInsert').val()
    ) {
      setXGirdInputRead('table3');
      $('#table3 tr').each(function (index) {
        if (index != 0) {
          var zTreeId = $(this).find('.operScopeZtree').attr('id');
          var zTree = $.fn.zTree.getZTreeObj(zTreeId);
          if ($('#edit').val() != '1' && zTree) {
            var nodes = zTree.getNodes();
            chkDis(nodes[0], zTree);
          }
        }
      });
    }
  }
}

//请求字典获取经营范围树数据
function getZtreeData(credentialId, id) {
  $.ajax({
    type: 'post',
    url: '/proxy-sysmanage/sysmanage/dict/listbycredentialidAndorgcode',
    async: false,
    data: {
      credentialId: credentialId,
      orgCode: '001',
    },
    dataType: 'json',
    success: function (data) {
      var zTreeNodes = [
        {
          id: 0,
          name: '经营范围',
          // open: true,//展开
          children: [],
        },
      ];
      var scopeofoperationVo = [];
      if (!data) return false;
      if (data.result && data.result.length > 0) {
        $(data.result).each(function (index, item) {
          var _obj = {};
          _obj.id = item.scopeId;
          _obj.name = item.scopeName;
          item.children && item.children.length > 0 ? (_obj.children = []) : '';
          $(item.children).each(function (cindex, citem) {
            var _obj_child = {};
            _obj_child.id = citem.scopeId;
            _obj_child.name = citem.scopeName;
            _obj.children.push(_obj_child);
          });
          zTreeNodes[0].children.push(_obj);
          scopeofoperationVo.push(item);
        });
        $('#table3').XGrid('setRowData', id, {
          supplierApprovalFileBusinessScopeVOList: zTreeNodes,
        });
        $('#table3').XGrid('setRowData', id, {
          scopeofoperationVo: JSON.stringify(scopeofoperationVo),
        });
      } else {
        //没有tree数据时清空列
        $('#table3').XGrid('setRowData', id, {
          supplierApprovalFileBusinessScopeVOList: '',
        });
      }

      var zTreeId = $('#table3 #' + id)
        .find('.operScopeZtree')
        .attr('id');
      var zTree = $.fn.zTree.getZTreeObj(zTreeId);

      for (var i = 0; i < idArr.length; i++) {
        var item = idArr[i];
        for (var n in item) {
          if (n == credentialId) {
            var aList = item[n];
            for (var s = 0; s < aList.length; s++) {
              zTree.checkNode(
                zTree.getNodeByParam('id', Number(aList[s].businessScopeCode)),
                true,
              );
            }
          }
        }
      }
    },
    error: function () {
      utils
        .dialog({ content: '加载失败', quickClose: true, timeout: 2000 })
        .showModal();
    },
  });
}

//年度报告
function initTable4(supplierBaseId, type) {
  var xGridDate = [];
  $.ajax({
    url: '/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getYearReportList',
    data: { correlationId: supplierBaseId, type: type },
    type: 'post',
    dataType: 'json',
    success: function (data) {
      if (data.result.list.length > 0) {
        $('#delYearReport').val('Y');
        var dataList = data.result.list;
        for (var i = 0; i < dataList.length; i++) {
          var a = {
            id: dataList[i].id,
            reportDate: dataList[i].reportDate,
            manageAbnormal: dataList[i].manageAbnormal,
            administrativePenalty: dataList[i].administrativePenalty,
            validityDate: Number(dataList[i].reportDate) + 2 + '-06-30',
            enclosureCount: dataList[i].enclosureCount,
            enclosureList: dataList[i].enclosureList,
          };
          xGridDate.push(a);
        }
      } else {
        xGridDate = [];
      }
      $('#table4')
        .XGrid('setGridParam', {
          data: xGridDate,
          pager: '#grid_page4',
        })
        .trigger('reloadGrid');
    },
  });
}

//初始化其他附件为选中
function initOtherFile(supplierBaseId, type) {
  $.ajax({
    url: '/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getOtherFileList',
    data: { correlationId: supplierBaseId, type: type },
    type: 'post',
    dataType: 'json',
    success: function (data) {
      if (!data.otherFileList) return false;
      if (data.otherFileList.length) {
        $('#delOtherFile').val('Y');
        $(data.otherFileList).each(function (fileIndex, fileItem) {
          var enclosureList = fileItem.enclosureList;
          if (enclosureList && enclosureList.length > 0) {
            var imgArr = [];
            $(enclosureList).each(function (imgIndex, imgItem) {
              var imgObj = {};
              imgObj.fileName = imgItem.fileName;
              imgObj.filePath = imgItem.filePath;
              imgArr.push(imgObj);
            });
            var html =
              '<input type="hidden" data-type="' +
              fileItem.certificateType +
              '" id="other' +
              fileItem.certificateType +
              '" value=\'' +
              JSON.stringify(imgArr) +
              "' />";
            $('body').append(html);
          }
          var otherFiles = $('.otherFile').find('input[type=checkbox]');
          $(otherFiles).each(function (index, item) {
            if (fileItem.certificateType == $(this).val()) {
              $(this).attr('checked', 'true');
            }
          });
        });
      } else {
        var otherFileInps = $('#otherFileBox input');
        $(otherFileInps).each(function () {
          $(this).prop('checked', false);
        });
        $('input[id^=other]').remove();
      }
    },
    error: function () {
      utils
        .dialog({ content: '请求失败', quickClose: true, timeout: 2000 })
        .showModal();
    },
  });
}

function format(shijianchuo) {
  //shijianchuo是整数，否则要parseInt转换
  var time = new Date(shijianchuo);
  var y = time.getFullYear();
  var m = time.getMonth() + 1;
  var d = time.getDate();
  var h = time.getHours();
  var mm = time.getMinutes();
  var s = time.getSeconds();
  return (
    y +
    '-' +
    add0(m) +
    '-' +
    add0(d) +
    ' ' +
    add0(h) +
    ':' +
    add0(mm) +
    ':' +
    add0(s)
  );
}

function add0(m) {
  return m < 10 ? '0' + m : m;
}

//拼装树 数据
//data: 接口返回的数据
//el: $tr.find('.operScopeZtree') 对应的tr的树的位置
function pulicTree(data, el) {
  //经营范围 tree
  var zTreeNodes = [
    {
      id: 0,
      name: '经营范围',
      // open: true,//展开
      children: [],
    },
  ];
  if (!data) return false;
  if (data.length > 0) {
    $(data).each(function (index, item) {
      var _obj = {};
      _obj.id = item.scopeId;
      _obj.name = item.scopeName;
      item.children && item.children.length > 0 ? (_obj.children = []) : '';
      $(item.children).each(function (cindex, citem) {
        var _obj_child = {};
        _obj_child.id = citem.scopeId;
        _obj_child.name = citem.scopeName;
        _obj.children.push(_obj_child);
      });
      zTreeNodes[0].children.push(_obj);
    });
  }
  var setting = {
    check: {
      enable: true, //显示勾选框  默认不显示
    },
  };
  zTree = $.fn.zTree.init(el, setting, zTreeNodes);
  return zTreeNodes;
}

function setRadioChecked(name, value) {
  var obj = $('input[name=' + name + ']');
  if ($('input[name=' + name + ']:checked').length < 1) {
    obj.each(function () {
      var val = this.value;
      if (val == value) {
        $(this).prop('checked', true);
      }
    });
  }
}

//初始化经营范围内容
/**
 * initBaseDataBuseScope() 此方法需要在有效期截止日期WdatePicker内调用 eg:<input type="text" placeholder="有效期" onclick="WdatePicker({onpicked:function(dp){initBaseDataBuseScope();}})"  name="validityDate"/>
 *
 * utils.operateRange 拼装数据
 *
 * @param XGrid 数据 $('#table3').XGrid('getRowData')
 *
 * @param 经营范围文件数当前列字段名 scopeOfBusiness
 *
 * @param 有效期截止字段  validityDate
 * */
function initBaseDataBuseScope() {
  var htmlAry = [],
    idAry = [];
  $.each(
    utils.operateRange(
      $('#table3').XGrid('getRowData'),
      'supplierApprovalFileBusinessScopeVOList',
      'validityDate',
    ),
    function (i, v) {
      if (v.status) {
        htmlAry.push(
          '<font style="color: red;" data-id="' +
          v.id +
          '">' +
          v.name +
          '</font>',
        );
      } else {
        htmlAry.push('<font data-id="' + v.id + '">' + v.name + '</font>');
      }
      idAry.push(v.id);
    },
  );
  $('#baseDataBuseScope').html(htmlAry.join(','));
  syncBusscopeToCustomer();
}

function initWorkFlow(key, processInstaId) {
  //supplierFirstTwo  supplierFirstOne(总部审核)
  $('.flow').html('');
  var exportDataFlag = $('#exportDataFlag').val(); //导入数据的标识
  //获取审核流程数据
  if ('Y' != exportDataFlag) {
    $.ajax({
      type: 'POST',
      url:
        '/proxy-product/product/purchaseLimit/queryTotle?key=' +
        key +
        '&processInstaId=' +
        processInstaId,
      async: false,
      error: function () {
        utils
          .dialog({ content: '请求失败！', quickClose: true, timeout: 2000 })
          .showModal();
      },
      success: function (data) {
        if (data.code == 0) {
          $('.flow').process(data.result);
        } else {
          utils
            .dialog({ content: '服务器错误', quickClose: true, timeout: 2000 })
            .showModal();
        }
      },
    });
  }
}

//供应商受用申请审核
/**
 *
 * @param isEdit 是否可编辑   1是  0否
 * @param auditType 审核结果  0审核通过 1审核不通过 2关闭工作流
 * @param comment 备注信息
 * @param taskId 工作流id
 * @param supplierOrgBaseId  业务id
 * @returns
 */
function AuditFirstApply(
  isEdit,
  auditType,
  taskId,
  processId,
  comment,
  supplierOrgBaseId,
  organBaseApplicationCode,
  resentBtn,
) {
  var userSupplierInsert = $('#userSupplierInsert').val();
  var useSupplier = $('#useSupplier').val();
  var title = '';
  if ('0' == auditType) {
    title = '审核通过';
  } else if ('1' == auditType) {
    title = '审核不通过';
  } else if ('2' == auditType) {
    title = '关闭审核 ';
  }

  if (auditType != 1 && auditType != 2) {
    let vis = checkMoreTimeInput();
    if (!vis) {
      utils
        .dialog({
          content: '对账账期/实效实结结算时间录入存在问题，请核实后重新提交！',
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      return false;
    }
    // 当审核不通过 和  关闭审核  的时候 不用做下面判断
    var balanceStyle_ckeck = $('.class_balanceStyle input[name=parentCode]');
    $(balanceStyle_ckeck).each(function (index, item) {
      var a = $(item).parent().siblings().find('.childName');
      $(a).each(function (ind, ite) {
        if ($(ite).text() == '结算日' || $(ite).text() == '支付日') {
          var b = $(ite).parent().siblings()[1];
          $(b)[$(item).is(':checked') ? 'addClass' : 'removeClass'](
            '  {validate:{ isMinMax:true, required :true,}}',
          );
        } else if (
          $(ite).text() == '压批次数' ||
          $(ite).text() == '授信金额' ||
          $(ite).text() == '结算周期'
        ) {
          var b = $(ite).parent().siblings()[1];
          $(b)[$(item).is(':checked') ? 'addClass' : 'removeClass'](
            '  {validate:{integer :true, required :true,}}',
          );
        } else {
          var b = $(ite).parent().siblings()[1];
          $(b)[$(item).is(':checked') ? 'addClass' : 'removeClass'](
            '  {validate:{isAbsInZero :true, required :true,}}',
          );
        }
      });
    });
    if (!validform('yyForm').form()) {
      return false;
    }
    if (isEdit == '1') {
      if ($('#table2').XGrid('getRowData').length != 0) {
        let _status = checkAuthorityType();
        if (!_status) {
          utils
            .dialog({
              title: '提示',
              content: '同一个供应商，授权类型只能存在一种',
              okValue: '确定',
              ok: function () { },
            })
            .showModal();
          return false;
        }
      }
      let msg = approvalInputValidation(null, $('#supplierTypeId').val());
      if (msg != '') {
        utils
          .dialog({
            title: '提示',
            width: 350,
            content: msg,
            okValue: '确定',
            ok: function () { },
          })
          .showModal();
        return false;
      }
      if ($('#supplierTypeId').val() != '58') {
        let _flag = tabDataCheck();
        if (!_flag) {
          return false;
        }
      }
    }
    let baseDataBuseScopeData = [],
      checkedBuseScopeData = [],
      bool_submit = false;
    // 因为没有返回businessScopeName  所以全部拿 ID 值比较
    $('#baseDataBuseScope font').each(function (index, item) {
      baseDataBuseScopeData.push($(item).attr('data-id'));
    });
    $('#table2 tr')
      .not(':eq(0)')
      .each(function (index, item) {
        checkedBuseScopeData = checkedBuseScopeData.concat(
          JSON.parse(
            $(item)
              .find('[row-describedby=supplierClientProxyBusinessScopeVOList]')
              .text(),
          ),
        );
      });
    checkedBuseScopeData = checkedBuseScopeData.map(function (item, index) {
      return item.businessScopeCode;
    });
    if (
      Array.from(new Set(checkedBuseScopeData)).length >
      baseDataBuseScopeData.length
    ) {
      //超出了可选范围
      utils
        .dialog({
          title: '提示',
          content:
            '客户委托书-经营范围授权-授权范围必须小于等于供应商-批准文件-经营范围。',
          okValue: '确定',
          ok: function () { },
        })
        .showModal();
      return false;
    }
    bool_submit = checkedBuseScopeData.some(function (item, index) {
      return baseDataBuseScopeData.indexOf(String(item)) < 0;
    });
    if (bool_submit) {
      utils
        .dialog({
          title: '提示',
          content:
            '客户委托书-经营范围授权-授权范围必须小于等于供应商-批准文件-经营范围。',
          okValue: '确定',
          ok: function () { },
        })
        .showModal();
      return false;
    }
    /**
     * 20200114
     * 线上紧急需求，药品经营许可证和GSP
     */
    // 批准文件-证书类型绑定校验
    // var certificateOption = [
    //     {
    //         certificateIds:[30,32],
    //         msg:'《药品经营许可证》与《GSP证书》必须同时录入'
    //     },
    //     {
    //         certificateIds:[29,31],
    //         msg:'《药品生产许可证》与《GMP证书》必须同时录入'
    //     }
    // ];
    // var supplierApprovalFileList_a = $("#table3").getRowData();//批准文件
    // var certificateMsg = '';
    // certificateOption.forEach(function (item) {
    //     if(supplierApprovalFileList_a.some(function (row) {
    //         return item.certificateIds[0] == row.certificateId;
    //     }) !== supplierApprovalFileList_a.some(function (row) {
    //         return item.certificateIds[1] == row.certificateId;
    //     })){
    //         certificateMsg = item.msg;
    //     }
    // });
    // if(certificateMsg){
    //     utils.dialog({
    //         title: '提示',
    //         content: certificateMsg,
    //         okValue: "确定",
    //         ok: function () {}
    //     }).showModal();
    //     return false;
    // }
  }
  console.log('验证走完了');
  //return false;
  var supplierAllDataVO = {};
  var bankAccount = '';
  var bankName = '';
  var supplierName = '';
  var supplierClientProxyOrderList = []; //客户委托书
  var supplierApprovalFileList = []; //批准文件
  var supplierRepertoryAddressVOList = []; //仓库地址
  var supplierYearPortList = []; //年度报告

  if ('1' == isEdit) {
    //可编辑
    //拼装可编辑数据
    supplierAllDataVO = assembData();
    bankAccount = supplierAllDataVO.supplierBase.bankAccount;
    bankName = supplierAllDataVO.supplierBase.bankName;
    supplierName = supplierAllDataVO.supplierBase.supplierName;
    supplierClientProxyOrderList =
      supplierAllDataVO.supplierClientProxyOrderList; //客户委托书
    supplierApprovalFileList = supplierAllDataVO.supplierApprovalFileList; //批准文件
    supplierAllDataVO.edit = isEdit;
    supplierAllDataVO.auditType = auditType;
    supplierAllDataVO.processId = taskId;
    supplierAllDataVO.comment = comment;
    supplierAllDataVO.supplierOrganBase.approvalProcessId = processId;
    supplierRepertoryAddressVOList =
      supplierAllDataVO.supplierBase.supplierRepertoryAddressVOList; //供应商仓库地址
    supplierYearPortList = supplierAllDataVO.supplierYearReportList; //年度报告
    //获取页面变化的数据
    var supplierOrganBaseId = $('#supplierOrganBaseId').val();
    if (supplierOrganBaseId) {
      parent.showLoading({ hideTime: 60000 });
      if (window.changeBefore) {
        var editData = returnEditData();
        if (editData) {
          supplierAllDataVO.changeDetails = editData;
        }
      }
      parent.hideLoading();
    }
    var settleMultipleDateListData = getMultipleDateList();
    let checkSameMultiple = checkSameInput(
      supplierAllDataVO.supplierOrganBase,
      settleMultipleDateListData,
    );
    if (checkSameMultiple == false) {
      utils
        .dialog({
          content:
            '对账账期/实销实结里已存在相同的结算时间信息，请勿重复录入！',
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      return false;
    }
    supplierAllDataVO.supplierOrganBase['supplierSettleMultipleDateVOList'] =
      settleMultipleDateListData;
  } else {
    supplierAllDataVO.edit = isEdit;
    supplierAllDataVO.auditType = auditType;
    supplierAllDataVO.processId = taskId;
    supplierAllDataVO.comment = comment;
    var supplierOrganBase = {};
    supplierOrganBase.approvalProcessId = processId;
    supplierOrganBase.id = supplierOrgBaseId;
    supplierAllDataVO.supplierOrganBase = supplierOrganBase;
    var supplierBase = {};
    supplierBase.supplierCode = $('#supplierCode').val();
    supplierBase.supplierName = $('#supplierName').val();
    supplierAllDataVO.supplierBase = supplierBase;
  }
  if ('2' == auditType) {
    //关闭审核
    supplierAllDataVO.supplierOrganBase.applicationCode =
      organBaseApplicationCode;
  }

  //校验到货周期与最小供货量
  var arrivalPeriod = supplierAllDataVO.supplierOrganBase.arrivalPeriod;
  var minSupply = supplierAllDataVO.supplierOrganBase.minSupply;
  if (arrivalPeriod && arrivalPeriod > 99999999.99) {
    utils
      .dialog({
        content: '到货周期最大为99999999.99！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
      setSaveDataReadOnly();
    }
    return false;
  }
  if (minSupply && minSupply > 99999999.99) {
    utils
      .dialog({
        content: '最小供货量最大为99999999.99！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
      setSaveDataReadOnly();
    }
    return false;
  }
  if (arrivalPeriod && !/^-?\d+\.?\d{0,2}$/.test(arrivalPeriod)) {
    utils
      .dialog({
        content: '到货周期只能输入两位小数！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();

    return false;
  }
  if (minSupply && !/^-?\d+\.?\d{0,2}$/.test(minSupply)) {
    utils
      .dialog({
        content: '最小供货量只能输入两位小数！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
      setSaveDataReadOnly();
    }
    return false;
  }
  if (supplierName && supplierName != '') {
    if (!/^[A-Za-z0-9\u4e00-\u9fa5\()（）]+$/.test(supplierName)) {
      utils
        .dialog({
          content: '供应商名称不允许有特殊字符',
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
        setSaveDataReadOnly();
      }
      return false;
    }
  }
  if ('1' == isEdit) {
    if ('0' == auditType) {
      if (
        false ==
        validataForm(
          bankAccount,
          bankName,
          supplierClientProxyOrderList,
          supplierApprovalFileList,
          supplierRepertoryAddressVOList,
          supplierYearPortList,
        )
      ) {
        if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
          setSaveDataReadOnly();
        }
        return false;
      }
    }
  }
  //return false;
  if ('1' == auditType) {
    //审核不通过
    $('#auditOpinionStyle').show();
  } else {
    $('#auditOpinionStyle').hide();
  }
  //删除来源字典的经营范围
  if (supplierApprovalFileList && supplierApprovalFileList.length > 0) {
    for (var i = 0; i < supplierApprovalFileList.length; i++) {
      delete supplierApprovalFileList[i].scopeofoperationVo;
    }
  }
  if ('Y' != resentBtn) {
    //非打回到发起人
    $('#auditOpinion').val('');
    if ('2' == auditType) {
      //关闭审核
      utils
        .dialog({
          title: title,
          width: 300,
          height: 30,
          content: '确定关闭此申请？',
          okValue: '确定',
          ok: function () {
            var a = $('#auditOpinion').val();
            if (1 == auditType && '' == a) {
              utils
                .dialog({
                  content: '审核不通过，审核意见不能为空！',
                  quickClose: true,
                  timeout: 2000,
                })
                .showModal();
              return false;
            }
            supplierAllDataVO.comment = $('#auditOpinion').val();
            sendAuditAjax(
              supplierAllDataVO,
              'N',
              userSupplierInsert,
              useSupplier,
            );
          },
          cancelValue: '取消',
          cancel: function () { },
        })
        .showModal();
    } else {
      utils
        .dialog({
          title: title,
          content: $('#container'),
          okValue: '确定',
          ok: function () {
            var a = $('#auditOpinion').val();
            if (1 == auditType && '' == a) {
              utils
                .dialog({
                  content: '审核不通过，审核意见不能为空！',
                  quickClose: true,
                  timeout: 2000,
                })
                .showModal();
              return false;
            }
            supplierAllDataVO.comment = $('#auditOpinion').val();
            sendAuditAjax(
              supplierAllDataVO,
              'N',
              userSupplierInsert,
              useSupplier,
            );
          },
          cancelValue: '取消',
          cancel: function () { },
        })
        .showModal();
    }
  } else {
    //加载页面
    // sendAuditAjax(supplierAllDataVO,"Y",userSupplierInsert,useSupplier);
    //驳回编辑重新提交  当为新增供应商基础信息时允许编辑地址，和三证合一, 当为选择的供应商主数据时为不可编辑
    if ('1' == isEdit) {
      if ('0' == auditType) {
        if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
          $('.depotList input, .depotList select').prop('disabled', true);
          $('#registerBox input, #registerBox select').prop('disabled', true);
          // $('[name=threeEvidenceAll]').prop('disabled', true) // 2020-07-14
        }
      }
    }
    sendAuditAjaxToBackEnd(
      supplierAllDataVO,
      resentBtn,
      userSupplierInsert,
      useSupplier,
    );
  }
}

function sendAuditAjax(
  supplierAllDataVO,
  resentBtn,
  userSupplierInsert,
  useSupplier,
) {
  parent.showLoading({ hideTime: 60000 });
  $.ajax({
    url: '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/Auditing',
    data: JSON.stringify(supplierAllDataVO),
    timeout: 30000,
    type: 'post',
    async: false,
    dataType: 'json',
    contentType: 'application/json',
    success: function (data) {
      parent.hideLoading();
      if (data.code) {
        if (!data.flowStatus) {
          utils
            .dialog({
              title: '提示',
              content: data.flowMsg,
              width: 300,
              height: 30,
              okValue: '确定',
              ok: function () {
                utils.closeTab('/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toList');
              },
            })
            .showModal();
          $('.ui-dialog-close').hide();
          return false;
        } else {
          var alertMsg = '';
          if ('Y' == resentBtn) {
            //打回到发起人重新提交
            alertMsg = '提交成功';
          } else {
            alertMsg = data.msg;
          }
          utils
            .dialog({
              title: '提示',
              content: alertMsg,
              width: 300,
              height: 30,
              okValue: '确定',
              ok: function () {
                utils.closeTab();
              },
            })
            .showModal();
          $('.ui-dialog-close').hide();
          return false;
        }
        setTimeout(function () {
          var mid = parent
            .$('#nav-tab li.active a')
            .attr('href')
            .replace('#', '');
          parent.$('#mainFrameTabs').bTabsClose(mid);
        }, 2000);
      } else {
        if ('Y' == userSupplierInsert || 'Y' == useSupplier) {
          setSaveDataReadOnly();
        }
        if ('Y' == data.sameBuseName || 'Y' == data.sameBuseCode) {
          checkRepeat(
            data.supplierBaseId,
            '供应商名称或营业执照已存在，请检查！查看重复供应商/修改？',
          );
        }
        if ('Y' != data.sameBuseCode && 'Y' != data.sameBuseName) {
          //                     utils.dialog({content: data.msg, quickClose: false, timeout: 2000}).showModal();
          utils
            .dialog({
              title: '提示',
              width: 300,
              height: 30,
              content: data.msg,
              okValue: '确定',
              ok: function () { },
            })
            .showModal();
        }
      }
    },
    error: function () {
      utils
        .dialog({ content: '请求失败', quickClose: true, timeout: 2000 })
        .showModal();
      parent.hideLoading();
    },
    complete: function () {
      parent.hideLoading();
    },
  });
}

//添加委托人身份证号码重复提示
function sendAuditAjaxToBackEnd(
  supplierAllDataVO,
  resentBtn,
  userSupplierInsert,
  useSupplier,
) {
  $.ajax({
    url: '/proxy-supplier/supplier/supplierClientProxyOrder/checkClientProxyOrder',
    data: JSON.stringify(supplierAllDataVO),
    type: 'post',
    dataType: 'json',
    async: true,
    contentType: 'application/json',
    success: function (data) {
      var passFlag = data.passFlag;
      var msg = data.msg;
      if (!passFlag) {
        utils
          .dialog({
            title: '提示',
            content: msg,
            okValue: '确定',
            ok: function () {
              parent.showLoading({ hideTime: 60000 });
              sendAuditAjax(
                supplierAllDataVO,
                resentBtn,
                userSupplierInsert,
                useSupplier,
              );
            },
            cancelValue: '取消',
            cancel: function () { },
          })
          .show();
      } else {
        parent.showLoading({ hideTime: 60000 });
        sendAuditAjax(
          supplierAllDataVO,
          resentBtn,
          userSupplierInsert,
          useSupplier,
        );
      }
    },
    error: function () {
      submint = false;
      utils
        .dialog({ content: '提交失败', quickClose: true, timeout: 2000 })
        .showModal();
    },
  });
}

//设置xgrid input  只读
function setXGirdInputRead(tableId) {
  $('#' + tableId)
    .find('input')
    .prop('disabled', true);
  $('#' + tableId)
    .find('select')
    .prop('disabled', true);
}

//付款、结算只可输入数字
$(document).on('input', '.cValue', function () {
  // var max=$.trim($(this).attr("data-max"));
  // if(max && max != ''){
  // 	$(this).val($(this).val().replace(/[^123456789]/g,''));
  // 	var value=$.trim($(this).val());
  // 	if(Number(value) >=  Number(max))
  // 	{
  // 		$(this).val(max);
  // 		return ;
  // 	}
  // }else{
  // 	$(this).val($.trim($(this).val()).replace(/\D/g,''));
  // }
});

function setDiable() {
  $(
    '#supplierBaseDTO input,#supplierBaseDTO botton,#supplierBaseDTO select',
  ).removeAttr('disabled');
  $('input[name=disableStateBase]').prop('disabled', 'disabled');
  $(table1).find('input').removeAttr('disabled');
  $(table1).find('select').removeAttr('disabled');
  $(table2).find('input').removeAttr('disabled');
  $(table2).find('select').removeAttr('disabled');
  $(table3).find('input').removeAttr('disabled');
  $(table3).find('select').removeAttr('disabled');
  $(table4).find('input').removeAttr('disabled');
  $(table4).find('select').removeAttr('disabled');
  $('.otherFileBoxClass').removeAttr('disabled');
}

function checkRepeat(supplierBaseId, alertMsg) {
  utils
    .dialog({
      title: '提示',
      content: alertMsg,
      width: 300,
      height: 30,
      button: [
        {
          value: '查看',
          callback: function () {
            var url =
              '/proxy-supplier/supplier/supplierOrganBase/supplierBase/toDetail?id=' +
              supplierBaseId +
              '&reapPageFlag=Y';
            utils.openTabs(
              'supplierBaseToDetailReapPageFlag01',
              '重复营业执照或供应商名称',
              url,
              { reload: false },
            );
          },
        },
      ],
      okValue: '修改',
      ok: function () { },
    })
    .showModal();
}

//验证数组内容是否重复
function isRepeat(arr) {
  var hash = [];
  for (var i in arr) {
    if (hash[arr[i]]) return true;
    hash[arr[i]] = true;
  }
  return false;
}

//搜索供应商
function searchSupplierBase(ev) {
  var queryFields = $('#supplierName').val();
  utils
    .dialog({
      url:
        '/proxy-supplier/supplier/supplierOrganBase/toFirstSupplierSerachList/toList?queryFields=' +
        queryFields, //弹框页面请求地址
      title: '搜索供应商',
      width: 1200,
      height: 650,
      data: queryFields, // 给modal 要传递的 的数据
      onclose: function () {
        if (this.returnValue) {
          if (this.returnValue == 'rturnAddPage') {
            window.location.href =
              '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toInsert?pageFlag=' +
              this.returnValue;
          } else {
            $('#userSupplierInsert').val('Y'); //设置为是引用新增
            var data = this.returnValue;
            console.log(data);
            loadData(data, $('#supplierBaseDTO'));
            // 注册地址
            let addressSelIdObj = [
              [
                {
                  nextNodeWrap: '#provinceSel_wrap',
                  nextNodeName: 'registerProvince',
                  nextNodeId: 'province1',
                },
                {
                  nextNodeWrap: '#citySel_wrap',
                  nextNodeName: 'registerCity',
                  nextNodeId: 'registerCity',
                },
                {
                  nextNodeWrap: '#districtSel_wrap',
                  nextNodeName: 'registerArea',
                  nextNodeId: 'district1',
                },
              ],
            ];
            let registerPromiseArray = [];
            let _registerHiddenVal = [
              data.registerProvince,
              data.registerCity,
              data.registerArea,
            ];
            $('#' + addressSelIdObj[0][0]['nextNodeId']).val(
              _registerHiddenVal[0],
            );

            for (let i = 1; i < _registerHiddenVal.length; i++) {
              registerPromiseArray.push(
                utils.setAddressReturnVal(_registerHiddenVal[i - 1]),
              );
            }
            Promise.all(registerPromiseArray).then((data) => {
              console.log(data);
              for (let i = 0; i < data.length; i++) {
                $('#' + addressSelIdObj[0][i + 1]['nextNodeId']).html(data[i]);
                $('#' + addressSelIdObj[0][i + 1]['nextNodeId']).val(
                  _registerHiddenVal[i + 1],
                );
                if (i != 2) {
                  $('#' + addressSelIdObj[0][i + 1]['nextNodeId']).addClass(
                    '{validate:{ required :true}}',
                  );
                }
              }
              let addReturnPageFlag = $('#addReturnPageFlag').val();
              if (addReturnPageFlag == 'rturnAddPage') {
                $('#registerAddress select,#registerAddress input').prop(
                  'disabled',
                  'true',
                );
              }
            });
            var AddressVOList = data.supplierRepertoryAddressVOList;
            //仓库地址
            if (null != AddressVOList) {
              var len = AddressVOList.length;
              $('#depotAddress').find('.depotList').remove();
              var obj = distpickerHTML(len);
              let storgeAddressSelIdObj = [];
              let _storgeHiddenValArr = AddressVOList.map((item) => {
                return [
                  item.repertoryProvince,
                  item.repertoryCity,
                  item.repertoryArea,
                ];
              });
              let _storgeHiddenVal = AddressVOList.map((item) => {
                return [
                  item.repertoryProvince,
                  item.repertoryCity,
                  item.repertoryArea,
                  item.repertoryDetail,
                ];
              });
              let selfEdit = $('#selfEdit').val(),
                editAddress = $('#editAddress').val(),
                _has = $('#selfEdit').length > 0;
              $(obj.radomInit).each((index, item) => {
                let _arr = [
                  {
                    nextNodeWrap: '#stoProvinceSel_wrap_' + item,
                    nextNodeName: 'repertoryProvince_' + item,
                    nextNodeId: 'repertoryProvince_' + item,
                  },
                  {
                    nextNodeWrap: '#stoCitySel_wrap_' + item,
                    nextNodeName: 'repertoryCity_' + item,
                    nextNodeId: 'repertoryCity_' + item,
                  },
                  {
                    nextNodeWrap: '#stoDistrictSel_wrap_' + item,
                    nextNodeName: 'repertoryArea_' + item,
                    nextNodeId: 'repertoryArea_' + item,
                  },
                ];
                storgeAddressSelIdObj.push(_arr);
              });
              $('#depotAddress').html(obj.html);
              $(obj.radomInit).each((index, item) => {
                let storagePromiseArray = [];
                utils.setAllProDom(
                  '#stoProvinceSel_wrap_' + obj.radomInit[index],
                  storgeAddressSelIdObj[index],
                  '#storageBox_' + obj.radomInit[index],
                  true,
                  function () {
                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(
                      _storgeHiddenValArr[index][0],
                    );
                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId'])
                      .parents('.distpicker')
                      .find('[name=repertoryDetail]')
                      .val(_storgeHiddenVal[index][3]);

                    for (
                      let ind = 1;
                      ind < _storgeHiddenValArr[0].length;
                      ind++
                    ) {
                      storagePromiseArray.push(
                        utils.setAddressReturnVal(
                          _storgeHiddenValArr[index][ind - 1],
                        ),
                      );
                    }
                    let allSelArr = storgeAddressSelIdObj[index]
                      .flat()
                      .map((item, index) => {
                        if (index % 4 != 0) {
                          return item['nextNodeId'];
                        }
                      })
                      .filter((item) => {
                        return item;
                      });
                    Promise.all(storagePromiseArray).then((data) => {
                      for (let i = 0; i < data.length; i++) {
                        $('#' + allSelArr[i]).html(data[i]);
                        $('#' + allSelArr[i]).attr('data-depth', i + 2);
                        $('#' + allSelArr[i]).val(
                          _storgeHiddenValArr[index][i + 1],
                        );
                        if (i != 2) {
                          $('#' + allSelArr[i]).addClass(
                            '{validate:{ required :true}}',
                          );
                        }
                      }
                    });
                  },
                );
              });
            } else {
              let obj = distpickerHTML();
              $('#depotAddress').html(obj.html);
              let storgeAddressSelIdObj = [
                {
                  nextNodeWrap: '#stoProvinceSel_wrap_' + obj.radomInit[0],
                  nextNodeName: 'repertoryProvince_' + obj.radomInit[0],
                  nextNodeId: 'repertoryProvince_' + obj.radomInit[0],
                },
                {
                  nextNodeWrap: '#stoCitySel_wrap_' + obj.radomInit[0],
                  nextNodeName: 'repertoryCity_' + obj.radomInit[0],
                  nextNodeId: 'repertoryCity_' + obj.radomInit[0],
                },
                {
                  nextNodeWrap: '#stoDistrictSel_wrap_' + obj.radomInit[0],
                  nextNodeName: 'repertoryArea_' + obj.radomInit[0],
                  nextNodeId: 'repertoryArea_' + obj.radomInit[0],
                },
              ];
              utils.setAllProDom(
                '#stoProvinceSel_wrap_' + obj.radomInit[0],
                storgeAddressSelIdObj,
                '#storageBox_' + obj.radomInit[0],
                true,
              );
              // var html = distpickerHTML();
              // $("#depotAddress").parents('.row').next().append(html);
              // $('[data-toggle="distpicker"]').distpicker();
            }

            let ManufactoryVOList = data.supplierManufactoryVOList,
              ManufactoryHtml = '';
            if (ManufactoryVOList && ManufactoryVOList.length != 0) {
              let len = ManufactoryVOList.length;
              for (let i = 0; i < len; i++) {
                ManufactoryHtml += `<div class="col-md-6 ManufactoryList">
                                                        <div class="input-group">
                                                            <div class="input-group-addon ">${data[
                    'supplierTypeId'
                  ] == '55'
                    ? '<i class="text-require">*  </i>'
                    : ''
                  }对应生产厂商</div>
                                                            <div class="form-control form-inline distpicker">
                                                                <div>
                                                                    <div class="form-group col-md-11">
                                                                        <input type="hidden" id="Manufactory${i}" value="${ManufactoryVOList[i]['manufactoryId']
                  }"  name="Manufactory${i}"/>
                                                                        <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class ${data[
                    'supplierTypeId'
                  ] ==
                    '55'
                    ? ''
                    : ''
                  }" value="${ManufactoryVOList[i]['manufactoryName']
                  }"   id="Manufactory${i}Val" name="Manufactory${i}Val" ${i == 0 ? 'changeApplyFlag="supplierManufactoryVOList"' : ''
                  }  />
                                                                    </div>
                                                                    <div class="form-group btn-box col-md-1">
                                                                        <button type="button" class="btn ${i == 0
                    ? 'btn_addManufacturer'
                    : 'btn_removeManufacturer'
                  } ">
                                                                            <span class="glyphicon ${i ==
                    0
                    ? 'glyphicon-plus'
                    : 'glyphicon-minus'
                  }" aria-hidden="true" ></span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>`; // {validate:{ required :true}}
              }
              $('#manufacturer_row').html(ManufactoryHtml);
              /*  for(let i = 0; i< len; i++){
                            utils.valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},'Manufactory'+i,
                                {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
                        }*/
            } else {
              // 没有回传值，页面结构不动
            }

            //拼装供应商类别
            var stHtml =
              '<div class="input-group-addon require"><i class="text-require">*  </i>供应商类别</div>' +
              '<input type="hidden" class="form-control" id="supplierTypeId" name="supplierTypeId" value="' +
              data.supplierTypeId +
              '"/>' +
              '<input type="text" readonly="readonly" id="supplierTypeIdVal" class="form-control"  value="' +
              data.supplierTypeName +
              '"/>';
            $('.supplierTypeClass').html(stHtml);

            //2018.9.5,RL,bug2551,begin
            //供应商类别为"生产"
            toggleSupplierDrug(data.supplierTypeId == 55 ? 'SC' : '');
            //2018.9.5,RL,bug2551,end

            // //设置注册地址
            // $("#registerAddress").find("select[name='registerProvince']").attr("data-value", data.registerProvince);
            //
            //
            //
            // //2018.9.14,RL,begin
            // $("#registerAddress").find("select[name='registerProvince']").siblings(".registeredAddressProvinceInputSearch").val(data.registerProvinceName);
            // //2018.9.14,RL,begin
            //
            //
            //
            // $("#registerAddress").find("select[name='registerCity']").attr("data-value", data.registerCity);
            // $("#registerAddress").find("select[name='registerArea']").attr("data-value", data.registerArea);
            // $("#registerProvinceName").val(data.registerProvinceName);
            // $("#registerAddress").find("select").each(function () {
            //     var val1 = $.trim($(this).attr("data-value"));
            //     if (val1 && val1 != '') {
            //         $(this).val(val1);
            //         $(this).change();
            //     }
            // })

            //供应商名称只读
            $('#supplierName').attr('readonly', 'readonly'); //设为只读
            $('#fidSupplierBaseId').val(data.id);
            $('#supplierBaseId').val(data.id);
            supplierBaseId = $('#supplierBaseId').val(); //基础属性id
            processId = $('#processId').val(); //基础属性id
            //基础属性对应的tab页
            if (supplierBaseId && '' != supplierBaseId) {
              initTable3(supplierBaseId, 0, 0); //批准文件
              initWorkFlow('supplierFirstTwo', '');
              //禁止input，select，botton，+，-号
              $('#supplierBaseDTO input,#supplierBaseDTO botton').attr(
                'disabled',
                'disabled',
              ); // ,#supplierBaseDTO select
              if ($('#supplierTypeId').val() == '55') {
                $('.ManufactoryList')
                  .eq(0)
                  .find('input[type=text]')
                  .prop({ disabled: false, readonly: false });
                $('.ManufactoryList')
                  .eq(0)
                  .find('.btn_addManufacturer')
                  .prop('disabled', false);
                $('.ManufactoryList')
                  .eq(0)
                  .find('.btn_addManufacturer')
                  .css('display', 'block');
              } else {
                $('.ManufactoryList')
                  .eq(0)
                  .find('input[type=text]')
                  .prop({ disabled: true, readonly: true });
                $('.ManufactoryList')
                  .eq(0)
                  .find('.btn_addManufacturer')
                  .prop('disabled', true);
                $('.ManufactoryList')
                  .eq(0)
                  .find('.btn_addManufacturer')
                  .css('display', 'none');
              }
              $(
                '.warehouseAddressProvinceInputSearch,  .registeredAddressProvinceInputSearch',
              ).removeAttr('disabled readonly'); // .registDetailSelect, .repertoryDetailSelect, , .registAreaSelect
              // $('[name=registerProvinceName]').removeAttr('disabled readonly');
              // $('[name=registerProvinceName]').prev().removeAttr('disabled readonly');
              $('#userSupperliserData').val('Y');
              //将注册地址 仓库地址 设置为只读
              // $('#registerAddress select, #registerAddress input, #depotAddress select, #depotAddress input').prop({'disabled':false, 'readonly':false});
            }
          }
        }
      },
    })
    .showModal();
  ev.stopPropagation();
}

/*
 *供应商所有申请，提交时应校验：
 *质量保证协议的签订日期不能相同，
 *客户委托书的委托书编号不能相同
 *批准文件的证书类型不能相同，
 *年度报告的年份不能相同
 * */
function checkValDiff() {
  var map = {};
  var valDiffOpt = ['签订日期', '委托书编号', '报告年份'];
  var signDateArr = [],
    proxyOderNoArr = [],
    reportDateArr = [];
  var table1RowData = $('#table1').XGrid('getRowData');
  var table2RowData = $('#table2').XGrid('getRowData');
  var table4RowData = $('#table4').XGrid('getRowData');
  if (table1RowData.length > 0) {
    for (var item of table1RowData) {
      signDateArr.push(item.signDate);
    }
    map['签订日期'] = signDateArr;
  }
  if (table2RowData.length > 0) {
    for (var item of table2RowData) {
      proxyOderNoArr.push(item.proxyOderNo);
    }
    map['委托书编号'] = proxyOderNoArr;
  }
  if (table4RowData.length > 0) {
    for (var item of table4RowData) {
      reportDateArr.push(item.reportDate);
    }
    map['报告年份'] = reportDateArr;
  }
  //开始验证每一项都不能有重复项
  if (map) {
    for (key in map) {
      var value = map[key];
      if (value && value.length > 0) {
        if (isRepeat(value)) {
          utils
            .dialog({
              title: '提示',
              width: 300,
              height: 30,
              content: key + '有重复值存在！！！',
              okValue: '确定',
              ok: function () { },
            })
            .showModal();
          return true;
        }
      }
    }
  } else {
    return false;
  }
}

//提交前验证数据
function validataForm(
  bankAccount,
  bankName,
  supplierClientProxyOrderList,
  supplierApprovalFileList,
  supplierRepertoryAddressVOList,
  supplierYearPortList,
  supplierManufactoryAddressVOList,
  supplierTypeId,
) {
  //提交前验证
  if (!validform('supplierBaseDTO').form()) {
    return false;
  }
  //验证注册地址市
  var registCitySelectLen = $('.registCitySelect').find('option').length;
  var registCitySelect = $('.registCitySelect').find('option:selected').text();
  if (registCitySelectLen > 1 && '请选择' == registCitySelect) {
    utils
      .dialog({
        content: '注册地址市为必填项',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    return false;
  }
  //验证注册地址区
  var registerAreaLen = $('.registAreaSelect').find('option').length;
  var registerAreaSelect = $('.registAreaSelect')
    .find('option:selected')
    .text();
  if (registerAreaLen > 1 && '请选择' == registerAreaSelect) {
    utils
      .dialog({
        content: '注册地址区为必填项',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    return false;
  }
  //验证注册地址街道
  var registDetailSelect = $('.registDetailSelect').val();
  if (
    registerAreaLen > 1 &&
    '请选择' != registerAreaSelect &&
    '' == registDetailSelect
  ) {
    utils
      .dialog({
        content: '注册地址街道为必填项',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    return false;
  }

  // //验证对应生产厂商  （暂时隐藏）
  // var manufactorySel = $('#manufacturer_row input');
  // if($('#supplierTypeId').val() == 55){
  //     if(!$(manufactorySel).length||$(manufactorySel).filter(function (index,item) { return !$(item).val() }).length){
  //         utils.dialog({content: '当供应商类别是生产时，对应生产厂商为必填项', quickClose: true, timeout: 2000}).showModal();
  //         return false;
  //     }
  // }else {
  //     if($(manufactorySel).filter(function (index,item) { return $(item).val() }).length){
  //         utils.dialog({content: '当供应商类别不是生产时，不能填写对应生产厂商', quickClose: true, timeout: 2000}).showModal();
  //         return false;
  //     }
  // }

  //验证仓库地址市
  var cityAddressFlag = true;
  var areaAddressFlag = true;
  var detailAddressFlag = true;
  var cityAddressSel = $('#supplierBaseDTO').find('.repertoryCitySelect'); //#depotAddress
  $(cityAddressSel).each(function (index, item) {
    var repertoryCityLen = $(item).find('option').length;
    var repertoryCitySelect = $(item).find('option:selected').text();
    if (repertoryCityLen > 1 && '请选择' == repertoryCitySelect) {
      var provinceName = $(item)
        .parent()
        .prev()
        .find('select')
        .find('option:selected')
        .text();
      utils
        .dialog({
          content: '仓库地址：' + provinceName + '对应市为必填项',
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      cityAddressFlag = false;
      return false;
    }
  });
  if (!cityAddressFlag) {
    return false;
  }
  //验证仓库地址区
  var depotAddressSel = $('#supplierBaseDTO').find('.repertoryAreaSelect'); //#depotAddress
  $(depotAddressSel).each(function (index, item) {
    var repertoryAreaLen = $(item).find('option').length;
    var repertoryAreaSelect = $(item).find('option:selected').text();
    if (repertoryAreaLen > 1 && '请选择' == repertoryAreaSelect) {
      var cityName = $(item)
        .parent()
        .prev()
        .find('select')
        .find('option:selected')
        .text();
      utils
        .dialog({
          content: '仓库地址：' + cityName + '对应区为必填项',
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      areaAddressFlag = false;
      return false;
    }
  });
  if (!areaAddressFlag) {
    return false;
  }
  //验证仓库地址街道
  var detailAddressSel = $('#supplierBaseDTO').find('.repertoryDetailSelect'); //#depotAddress
  $(detailAddressSel).each(function (index, item) {
    var repertoryAreaLen = $(item)
      .parent()
      .prev()
      .find('select')
      .find('option').length;
    var repertoryAreaSelect = $(item)
      .parent()
      .prev()
      .find('select')
      .find('option:selected')
      .text();
    var repertoryDetailSelect = $(item).val();
    if (
      repertoryAreaLen > 1 &&
      '请选择' != repertoryAreaSelect &&
      '' == repertoryDetailSelect
    ) {
      var areaName = $(item)
        .parent()
        .prev()
        .find('select')
        .find('option:selected')
        .text();
      utils
        .dialog({
          content: '仓库地址：' + areaName + '对应街道为必填项',
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      detailAddressFlag = false;
      return false;
    }
  });
  if (!detailAddressFlag) {
    return false;
  }
  // if (supplierRepertoryAddressVOList && supplierRepertoryAddressVOList.length > 0) {
  // 	  if(supplierRepertoryAddressVOList.length > 3){
  // 		  utils.dialog({content: '仓库地址最多添加3条', quickClose: true, timeout: 2000}).showModal();
  // 	        return false;
  // 	  }
  //  }
  //开户银行1、开户户名1、银行账号1，任一个填写，另外两个必须填写；或者三个都为空；或者全有值；
  var bankFlaf = false;
  var bankName1 = $('#bankName1').val(); //开户银行1
  var accountName1 = $('#accountName1').val(); //开户户名1
  var bankAccount1 = $('#bankAccount1').val(); //银行账号1
  if (bankName1 && accountName1 && bankAccount1) {
    bankFlaf = true;
  }
  if (!bankName1 && !accountName1 && !bankAccount1) {
    bankFlaf = true;
  }
  if (!bankFlaf) {
    utils
      .dialog({
        content: '开户银行1、开户户名1、银行账号1,请全部填写或不填写',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    return false;
  }

  var c = $('.class_payStyle input[name=parentCode]:checked');
  var b = $('.class_balanceStyle input[name=parentCode]:checked');
  if (c.length < 1 || b.length < 1) {
    utils
      .dialog({
        title: '提示',
        content: '付款方式和结算方式请至少各选择一项',
        okValue: '确定',
        ok: function () { },
      })
      .showModal();
    return false;
  }
  // 付款方式。结算方式，当checkbox 被选中时在给相应的文本框添加 检验。否则， 如果全部校验的话 表单检验无法通过
  // 以上规则总结：
  // 到货周期：大于0，可以为小数，小数点后保留两位
  // 最小供货量：大于等于0，可以为小数，小数点后保留两位
  // 付款方式：大于0，小于等于100， 至少选一个，可以为小数，小数点后保留两位
  // 结算方式：结算周期、压批次数、授信金额为正整数；结算日、支付日：1-31，
  var payStyle_ckeck = $('.class_payStyle input[name=parentCode]');
  $(payStyle_ckeck).each(function (index, item) {
    console.log($(item).is(':checked'));
    if ($(item).val() != '1015') {
      // 承兑开户账户: 1015
      $(item)
        .parent()
        .siblings()
        .find('input[type=number]')
      [$(item).is(':checked') ? 'addClass' : 'removeClass'](
        '  {validate:{floattwoAndMax :true, required :true,}}',
      );
      $(item)
        .parent()
        .siblings()
        .find('input[type=text]')
      [$(item).is(':checked') ? 'addClass' : 'removeClass'](
        '  {validate:{ required :true,}}',
      );
    } else {
      $(item)
        .parent()
        .siblings()
        .find('input[type=number]')
      [$(item).is(':checked') ? 'addClass' : 'removeClass'](
        '  {validate:{ required :true,}}',
      );
      $(item)
        .parent()
        .siblings()
        .find('input[type=text]')
      [$(item).is(':checked') ? 'addClass' : 'removeClass'](
        '  {validate:{ required :true,}}',
      );
    }
    if ($(item).is(':checked')) {
      $(item)
        .parent()
        .siblings()
        .find('input[type=number]')
        .attr('aria-required', 'true');
      $(item)
        .parent()
        .siblings()
        .find('input[type=text]')
        .attr('aria-required', 'true');
    } else {
      $(item)
        .parent()
        .siblings()
        .find('input[type=number]')
        .attr('aria-required', 'false');
      $(item)
        .parent()
        .siblings()
        .find('input[type=text]')
        .attr('aria-required', 'true');
    }
  });
  var balanceStyle_ckeck = $('.class_balanceStyle input[name=parentCode]');
  $(balanceStyle_ckeck).each(function (index, item) {
    var a = $(item).parent().siblings().find('.childName');
    $(a).each(function (ind, ite) {
      if ($(ite).text() == '结算日' || $(ite).text() == '支付日') {
        var b = $(ite).parent().siblings()[1];
        if (!b) {
          b = $(ite).parent().siblings()[0];
        }
        $(b)[$(item).is(':checked') ? 'addClass' : 'removeClass'](
          '  {validate:{ isMinMax:true, required :true,}}',
        );
      } else if (
        $(ite).text() == '压批次数' ||
        $(ite).text() == '授信金额' ||
        $(ite).text() == '结算周期'
      ) {
        var b = $(ite).parent().siblings()[1];
        $(b)[$(item).is(':checked') ? 'addClass' : 'removeClass'](
          '  {validate:{integer :true, required :true,}}',
        );
      } else {
        var b = $(ite).parent().siblings()[1];
        $(b)[$(item).is(':checked') ? 'addClass' : 'removeClass'](
          '  {validate:{isAbsInZero :true, required :true,}}',
        );
      }
    });
  });
  if (!validform('yyForm').form()) {
    return false;
  }
  if (
    !validform('table1Form').form() ||
    !validform('table2Form').form() ||
    !validform('table3Form').form() ||
    !validform('table4Form').form()
  ) {
    return false;
  }
  if (!bankAccount || bankAccount == '') {
    utils
      .dialog({
        content: '银行账号不能为空！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    return false;
  }
  if (!bankName || bankName == '') {
    utils
      .dialog({
        content: '开户银行不能为空！',
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    return false;
  }
  var checkRes = checkValDiff();
  if (checkRes) {
    //  检查结果有重复值。流程打断结束，阻止提交审核
    return false;
  }
  //校验客户委托书对应的经营范围与选择的商品
  if (supplierClientProxyOrderList && supplierClientProxyOrderList.length > 0) {
    // for (var i = 0; i < supplierClientProxyOrderList.length; i++) {
    //     var item = supplierClientProxyOrderList[i];
    //     if ("0" == item.authorityType) {//品种授权
    //         // var supplierClientProxyProductVOList = item.supplierClientProxyProductVOList;
    //         // if (!supplierClientProxyProductVOList || supplierClientProxyProductVOList.length <= 0) {
    //         //     utils.dialog({content: '客户委托书选择的商品不可为空！', quickClose: true, timeout: 2000}).showModal();
    //         //     return false;
    //         // }
    //     } else if ("1" == item.authorityType) {//经营范围授权
    //         var supplierClientProxyBusinessScopeVOList = item.supplierClientProxyBusinessScopeVOList;
    //         if (!supplierClientProxyBusinessScopeVOList || supplierClientProxyBusinessScopeVOList.length <= 0) {
    //             utils.dialog({content: '客户委托书经营范围不可为空！', quickClose: true, timeout: 2000}).showModal();
    //             return false;
    //         }
    //     }
    //
    //
    //     //2018.9.6,RL,bug2569,begin
    //     // else if("2" == item.authorityType){//剂型授权
    //     //     var supplierClientProxyTypeVOList=item.supplierClientProxyTypeVOList;
    //     //     if (!supplierClientProxyTypeVOList || supplierClientProxyTypeVOList.length <= 0) {
    //     //         utils.dialog({content: '客户委托书选择的剂型不可为空！', quickClose: true, timeout: 2000}).showModal();
    //     //         return false;
    //     //     }
    //     // }
    //     //2018.9.6,RL,bug2569,end
    //
    //
    // }
  } else {
    // utils.dialog({content: '客户委托书没有录入，请录入后再提交！', quickClose: true, timeout: 2000}).showModal();
    // return false;
  }
  /**
   *  2019-02-25 RM
   * 不存在经营范围的证书类型 GMP:31;   GSP:32
   */
  const certificateIdArr = ['31', '32'];
  var pageProperty = $('#pageProperty').val();
  var addReturnPageFlag = $('#addReturnPageFlag').val(); //标识是首营申请入口还是点击选择商品的新增入口
  var dbPage = $('#dbPage').val(); //编辑页面是否从待办入口进入
  var supplierBaseId = $('#supplierBaseId').val(); //基础属性id
  if ('insert' == pageProperty && addReturnPageFlag) {
    //全量新增页面
    if (!supplierBaseId) {
      //校验批准文件对应的经营范围
      if (supplierApprovalFileList && supplierApprovalFileList.length > 0) {
        for (var i = 0; i < supplierApprovalFileList.length; i++) {
          var list =
            supplierApprovalFileList[i].supplierApprovalFileBusinessScopeVOList;
          var scopeofoperationVo = [];
          if (supplierApprovalFileList[i].scopeofoperationVo) {
            let bool = certificateIdArr.every(function (item, index) {
              return supplierApprovalFileList[i].certificateId != item;
            });
            if (bool) {
              if (!list || list.length <= 0) {
                utils
                  .dialog({
                    content: '批准文件对应的经营范围不可为空',
                    quickClose: true,
                    timeout: 2000,
                  })
                  .showModal();
                return false;
              }
            }
          }
          delete supplierApprovalFileList[i].scopeofoperationVo;
        }
      }
    }
  }

  if ('edit' == pageProperty) {
    //首营申请编辑页面
    var useSupplier = $('#useSupplier').val();
    //判断是否引用主数据
    if ('Y' != useSupplier) {
      //是
      //校验批准文件对应的经营范围
      if (supplierApprovalFileList && supplierApprovalFileList.length > 0) {
        for (var i = 0; i < supplierApprovalFileList.length; i++) {
          var list =
            supplierApprovalFileList[i].supplierApprovalFileBusinessScopeVOList;
          var scopeofoperationVo = [];
          if (
            supplierApprovalFileList[i].scopeofoperationVo &&
            'null' != supplierApprovalFileList[i].scopeofoperationVo
          ) {
            let bool = certificateIdArr.every(function (item, index) {
              return supplierApprovalFileList[i].certificateId != item;
            });
            if (bool) {
              if (!list || list.length <= 0) {
                utils
                  .dialog({
                    content: '批准文件对应的经营范围不可为空',
                    quickClose: true,
                    timeout: 2000,
                  })
                  .showModal();
                return false;
              }
            }
          }
          delete supplierApprovalFileList[i].scopeofoperationVo;
        }
      }
    }
  }

  // if (!supplierYearPortList || supplierYearPortList.length <= 0) {
  //     utils.dialog({content: '年度没有录入，请录入后再提交', quickClose: true, timeout: 2000}).showModal();
  //     return false;
  // }
  /**
   * 20200114
   * 线上紧急需求，药品经营许可证和GSP
   */
  // 批准文件-证书类型绑定校验
  // var certificateOption = [
  //     {
  //         certificateIds:[30,32],
  //         msg:'《药品经营许可证》与《GSP证书》必须同时录入'
  //     },
  //     {
  //         certificateIds:[29,31],
  //         msg:'《药品生产许可证》与《GMP证书》必须同时录入'
  //     }
  // ];
  // var certificateMsg = '';
  // certificateOption.forEach(function (item) {
  //     if(supplierApprovalFileList.some(function (row) {
  //         return item.certificateIds[0] == row.certificateId;
  //     }) !== supplierApprovalFileList.some(function (row) {
  //         return item.certificateIds[1] == row.certificateId;
  //     })){
  //         certificateMsg = item.msg;
  //     }
  // });
  // if(certificateMsg){
  //     utils.dialog({
  //         title: '提示',
  //         content: certificateMsg,
  //         okValue: "确定",
  //         ok: function () {}
  //     }).showModal();
  //     return false;
  // }
}

function setSaveDataReadOnly() {
  //禁止input，select，botton，+，-号
  $('#supplierBaseDTO input,#supplierBaseDTO botton').attr(
    'disabled',
    'disabled',
  ); // ,#supplierBaseDTO select
  $(
    '.warehouseAddressProvinceInputSearch, .registDetailSelect, .repertoryDetailSelect, .registeredAddressProvinceInputSearch, .registAreaSelect',
  ).removeAttr('disabled  readonly');
  $('[name=registerProvinceName]').removeAttr('disabled readonly');
  $('[name=registerProvinceName]').prev().removeAttr('disabled readonly');
  $('#supplierBaseDTO .glyphicon-minus,.glyphicon-plus').parent('.btn').hide();
  $('.depotList input, .depotList select').prop('disabled', true);
  $('#registerBox input, #registerBox select').prop('disabled', true);
  $('#userSupperliserData').val('Y');
  //设置所有的其他附件的复选框为只读
  /*  var contractTypes = $('.otherFileBoxClass').find('input[type=checkbox]');
    $(contractTypes).each(function (index, item) {
        $(this).attr("disabled", 'true');
    });
    //设置table3只读
    setXGirdInputRead("table3");
    setXGirdInputRead("table4");*/
}

//返回页面修改的数据
function returnEditData() {
  /**
   */
  // form ID .  修改前的数据。 修改后的数据
  var modifyBaseDataObj = $('#supplierBaseDTO').serializeToJSON(); // 基础属性数据
  var modifyOperateDataObj = $('#yyForm').serializeToJSON(); // 运营属性数据
  var modifyZLBZDataObj = $('#table1').getRowData(); //质量保证协议
  var modifyKHWTDataObj = $('#table2').getRowData(); //客户委托书
  var modifyPZWJDataObj = $('#table3').getRowData(); //批准文件
  var modifyNDBGDataObj = $('#table4').getRowData(); //年度报告
  var modifyQTFJDataObj = $('input[id^=newother]'); // 其他附件filelist
  var otherChecks = $('#otherFileBox').find('input[type=checkbox]:checked'); // 其他附件checkbox
  var modifyOtherChecksArr = []; // 其他附件选中项名称
  $(otherChecks).each(function (i, v) {
    var obj = {};
    obj.name = $(this).next().text();
    obj.checked = true;
    modifyOtherChecksArr.push(obj);
  });
  window.changeAfter = {
    initBaseDataObj: modifyBaseDataObj,
    initOperateDataObj: modifyOperateDataObj,
    initZLBZDataObj: modifyZLBZDataObj,
    initKHWTDataObj: modifyKHWTDataObj,
    initPZWJDataObj: modifyPZWJDataObj,
    initNDBGDataObj: modifyNDBGDataObj,
    initQTFJDataObj: modifyQTFJDataObj,
    initOtherChecked: modifyOtherChecksArr,
  };
  var str = '';
  //基础属性
  str += getFormData(
    window.changeBefore.initBaseDataObj,
    window.changeAfter.initBaseDataObj,
    '基础属性',
  );
  /**
   * 仓库地址 多条数据的时候
   */
  if (window.changeAfter.initBaseDataObj) {
    var BaseDataObjValue = window.changeAfter.initBaseDataObj;
    let _sortStr = '',
      _lastStr = '';
    var _proSortArr = [],
      _citySort_Arr = [],
      _areaSortArr = [],
      _streetSortArr = [];
    for (var ite in BaseDataObjValue) {
      if (Array.isArray(BaseDataObjValue[ite])) {
        // 多条仓库地址
        if (BaseDataObjValue[ite].length > 0) {
          $(BaseDataObjValue[ite]).each(function (index, item) {
            if (index == 0) {
              var optText =
                $('[data-value=' + item + ']  option:selected').length > 0
                  ? $('[data-value=' + item + ']  option:selected')
                    .eq(0)
                    .text()
                  : getOptText($('[name^=' + ite + ']').eq(index), item)
                    ? getOptText($('[name^=' + ite + ']').eq(index), item)
                    : item;
              _sortStr += optText;
            } else {
              if (index > 0) {
                if (ite.indexOf('repertoryProvince') == 0) {
                  _proSortArr = BaseDataObjValue[ite].slice(
                    1,
                    BaseDataObjValue[ite].length + 1,
                  );
                } else if (ite.indexOf('repertoryCity') == 0) {
                  _citySort_Arr = BaseDataObjValue[ite].slice(
                    1,
                    BaseDataObjValue[ite].length + 1,
                  );
                } else if (ite.indexOf('repertoryArea') == 0) {
                  _areaSortArr = BaseDataObjValue[ite].slice(
                    1,
                    BaseDataObjValue[ite].length + 1,
                  );
                } else if (ite.indexOf('repertoryDetail') == 0) {
                  _streetSortArr = BaseDataObjValue[ite].slice(
                    1,
                    BaseDataObjValue[ite].length + 1,
                  );
                }
              }
            }
          });
        }
      } else {
        // 档条仓库地址
        var a = '',
          b = '',
          c = '',
          d = '';
        if (ite.indexOf('repertoryProvince') == 0) {
          a =
            $('[data-value=' + ite + ']  option:selected').length > 0
              ? $('[data-value=' + ite + ']  option:selected')
                .eq(0)
                .text()
              : getOptText(
                $('[name^=repertoryProvince]').eq(0),
                BaseDataObjValue[ite],
              )
                ? getOptText(
                  $('[name^=repertoryProvince]').eq(0),
                  BaseDataObjValue[ite],
                )
                : BaseDataObjValue[ite];
        } else if (ite.indexOf('repertoryCity') == 0) {
          b =
            $('[data-value=' + ite + ']  option:selected').length > 0
              ? $('[data-value=' + ite + ']  option:selected')
                .eq(0)
                .text()
              : getOptText(
                $('[name^=repertoryCity]').eq(0),
                BaseDataObjValue[ite],
              )
                ? getOptText(
                  $('[name^=repertoryCity]').eq(0),
                  BaseDataObjValue[ite],
                )
                : BaseDataObjValue[ite];
        } else if (ite.indexOf('repertoryArea') == 0) {
          c =
            $('[data-value=' + ite + ']  option:selected').length > 0
              ? $('[data-value=' + ite + ']  option:selected')
                .eq(0)
                .text()
              : getOptText(
                $('[name^=repertoryArea]').eq(0),
                BaseDataObjValue[ite],
              )
                ? getOptText(
                  $('[name^=repertoryArea]').eq(0),
                  BaseDataObjValue[ite],
                )
                : BaseDataObjValue[ite];
        } else if (ite.indexOf('repertoryDetail') == 0) {
          d =
            $('[value=' + ite + ']').length > 0
              ? $('[value=' + ite + ']')
                .eq(0)
                .val()
              : BaseDataObjValue[ite]; //(getOptText($('[name=repertoryDetail]').eq(0),BaseDataObjValue[ite])?getOptText($('[name=repertoryDetail]').eq(0),BaseDataObjValue[ite]):BaseDataObjValue[ite])
        }
        _lastStr += a + b + c + d;
      }
    }
    $(_proSortArr).each(function (ik, iv) {
      var a =
        $('[data-value=' + iv + ']  option:selected').length > 0
          ? $('[data-value=' + iv + ']  option:selected')
            .eq(0)
            .text()
          : getOptText($('[name^=repertoryProvince]').eq(ik), iv)
            ? getOptText($('[name^=repertoryProvince]').eq(ik), iv)
            : iv;
      var b =
        $('[data-value=' + _citySort_Arr[ik] + ']  option:selected').length > 0
          ? $('[data-value=' + _citySort_Arr[ik] + ']  option:selected')
            .eq(0)
            .text()
          : getOptText($('[name^=repertoryCity]').eq(ik + 1), _citySort_Arr[ik])
            ? getOptText($('[name^=repertoryCity]').eq(ik + 1), _citySort_Arr[ik])
            : _citySort_Arr[ik];
      var c =
        $('[data-value=' + _areaSortArr[ik] + ']  option:selected').length > 0
          ? $('[data-value=' + _areaSortArr[ik] + ']  option:selected')
            .eq(0)
            .text()
          : getOptText($('[name^=repertoryArea]').eq(ik + 1), _areaSortArr[ik])
            ? getOptText($('[name^=repertoryArea]').eq(ik + 1), _areaSortArr[ik])
            : _areaSortArr[ik];
      //var d = ($('[data-value='+_streetSortArr[ik]+']  option:selected').length > 0?$('[data-value='+_streetSortArr[ik]+']  option:selected').eq(0).text():(getOptText($('[name=repertoryDetail]').eq(ik+1),_streetSortArr[ik])?getOptText($('[name=repertoryDetail]').eq(ik+1),_streetSortArr[ik]):_streetSortArr[ik]));
      var d = _streetSortArr[ik]
        ? $('[value=' + _streetSortArr[ik] + ']').length > 0
          ? $('[value=' + _streetSortArr[ik] + ']')
            .eq(0)
            .val()
          : _streetSortArr[ik]
        : '';
      _lastStr +=
        ik > 0
          ? ',' + (a + b + c + d) + (ik < _proSortArr.length ? ',' : '')
          : a + b + c + d;
    });
    var _finalStr =
      (_sortStr ? _sortStr + ',' : '') + (_lastStr ? _lastStr : '');
    // if(str.indexOf('仓库地址的值')>=0 ){
    //     // 如果是仓库地址的时候例外，因为可能有多条仓库地址
    //     str = str.replace(str.substring(str.indexOf('仓库地址的值'),str.lastIndexOf('\n')+1),'');
    // }
    str +=
      _finalStr == window.changeBefore.initRepOptsTxt
        ? ''
        : '仓库地址的值有修改,【' +
        window.changeBefore.initRepOptsTxt +
        '】修改为【' +
        _finalStr +
        '】;\n'; // 是否有修改
  }
  //运营属性
  str += getFormData(
    window.changeBefore.initOperateDataObj,
    window.changeAfter.initOperateDataObj,
    '运营属性',
  );
  /**
   * 预付款的没有判断，
   */
  //运营属性的付款方式 结算方式
  if (window.changeBefore.initOperateDataObj.value) {
    var changeBeforeValue = window.changeBefore.initOperateDataObj.value;
    $(changeBeforeValue).each(function (index, item) {
      if (item != modifyOperateDataObj.value[index]) {
        var thisTag = $('#yyForm input[name=value]')
          .eq(index)
          .parents('.paymentSettlement')
          .find('label')
          .text()
          .trim();
        var thisChildTag = $('#yyForm input[name=value]')
          .eq(index)
          .parents('.childCode')
          .find('span')
          .text()
          .trim();
        str +=
          thisTag +
          '的值有修改,' +
          thisChildTag +
          '【' +
          changeBeforeValue[index] +
          '】修改为【' +
          modifyOperateDataObj.value[index] +
          '】;\n';
      }
      return;
    });
  }
  //质量保证协议
  str += getRowData(
    window.changeBefore.initZLBZDataObj,
    window.changeAfter.initZLBZDataObj,
    'table1',
    '质量保证协议',
  );
  //客户委托书
  str += getRowData(
    window.changeBefore.initKHWTDataObj,
    window.changeAfter.initKHWTDataObj,
    'table2',
    '客户委托书',
  );
  //批准文件
  str += getRowData(
    window.changeBefore.initPZWJDataObj,
    window.changeAfter.initPZWJDataObj,
    'table3',
    '批准文件',
  );
  //年度报告
  str += getRowData(
    window.changeBefore.initNDBGDataObj,
    window.changeAfter.initNDBGDataObj,
    'table4',
    '年度报告',
  );
  //其他附件选中项
  str += getOtherValData(
    window.changeBefore.initOtherChecked,
    window.changeAfter.initOtherChecked,
    '其他附件',
  );
  //其他附件filelist
  str += getOtherData(
    window.changeBefore.initQTFJDataObj,
    window.changeAfter.initQTFJDataObj,
    '其他附件',
  );

  console.log(str);
  return str;
}
function change_lala(that) {
  let validityDate_val = $(that)
    .parents('td')
    .next()
    .find('input[name=validityDate]')
    .val();
  let validityDateVal = new Date(validityDate_val).getTime();
  let curDate = new Date(utils.getCurDate(new Date())).getTime();
  if (validityDate_val != '' && validityDateVal >= curDate) {
    initBaseDataBuseScope();
  }
}

//添加对应生产厂商
$('body').on('click', '.btn_addManufacturer', function () {
  var obj = ManufactoryHTML();
  $('#manufacturer_row').append(obj.html);
  let _id = 'Manufactory' + obj.radomInt;
  utils.valAutocomplete(
    '/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage',
    { paramName: 'keyWord', params: { isStop: 0 } },
    _id,
    {
      data: 'manufactoryId',
      value: 'manufactoryName',
      brandfactoryName: 'brandfactoryName',
      brandfactoryId: 'brandfactoryId',
      address: 'address',
    },
  );
});
//删除对应生产厂商
$('body').on('click', '.btn_removeManufacturer', function () {
  $(this).parents('.ManufactoryList').remove();
});


initQuesta()

//初始化hover提示
function initQuesta() {
  const questaOption =
  {
    th: 'field2', //审批价差幅度,
    title: `供应商邮箱地址，用于收货异常通知到供应商`,
    width: 460,
    height: 80,
  }

  $('#questaI').mouseover(function (e) {
    $('body').append(`
                    <div id='div_tooltips'>
                        <style>
                            #div_tooltips:after{
                                content: "";
                                width: 0;
                                height: 0;
                                position: absolute;
                                left: ${questaOption.width / 2 - 10}px;
                                bottom: -10px;
                                border-left: solid 10px transparent;
                                border-top: solid 10px white;
                                border-right: solid 10px transparent;
                            }
                        </style>
                        <div id='inner_tooltips'>${questaOption.title}</div>
                    </div>
                `);
    $('#div_tooltips')
      .css({
        boxSizing: 'border-box',
        width: questaOption.width + 'px',
        height: questaOption.height + 'px',
        padding: '10px',
        zIndex: 9999,
        backgroundColor: '#ffffff',
        border: '1px solid #c4c4c4',
        position: 'absolute',
        top: $(e.target).offset().top - questaOption.height - 10 + 'px',
        left: $(e.target).offset().left + 5 - questaOption.width / 2 + 'px',
      })
      .show('fast');
  });

  $('#questaI').mouseout(function (e) {
    $('#div_tooltips').remove();
  });
}

// $(function () {
//     utils.valAutocomplete("/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},"Manufactory",
//         {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
// })
function ManufactoryHTML(n) {
  let len = n ? n : 1;
  let html = '',
    obj = {};
  let radomInt = parseInt(Math.random() * 100) + parseInt(Math.random() * 10);
  let supplierTypeIdVal = $('#supplierTypeIdVal').val();
  for (let i = 0; i < len; i++) {
    html += `<div class="col-md-6 ManufactoryList">
                    <div class="input-group">
                        <div class="input-group-addon ">${supplierTypeIdVal == '生产'
        ? '<i class="text-require">*  </i>'
        : ''
      }对应生产厂商</div>
                        <div class="form-control form-inline distpicker">
                            <div>
                                <div class="form-group col-md-11">
                                    <input type="hidden" id="Manufactory${radomInt}"  name="Manufactory${radomInt}"/>
                                    <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class ${supplierTypeIdVal == '生产' ? '' : ''
      }"  id="Manufactory${radomInt}Val" name="Manufactory${radomInt}Val"  />
                                </div>
                                <div class="form-group btn-box col-md-1">
                                    <button type="button" class="btn btn_removeManufacturer">
                                        <span class="glyphicon glyphicon-minus" aria-hidden="true" ></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`; // {validate:{ required :true}}
  }
  obj = {
    html: html,
    radomInt: radomInt,
  };
  return obj;
}
