$(function () {
    //tab切换
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    $('.nav-tabs>li').on('click', function (){
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
        var tabClassName = $this.find("a").attr("class");
        var snapSupplierOrganBaseId =  $("#snapSupplierOrganBaseId").val(); //运营属性
        //基础属性对应tab页面
        if(null != snapSupplierOrganBaseId && "" != snapSupplierOrganBaseId){
        	  if(tabClassName=='ndbg'){//年度报告
        		  initTable4(snapSupplierOrganBaseId);
        	  }else if(tabClassName=='pzwj'){ //批准文件
        		  initTable3(snapSupplierOrganBaseId);
        	  }else if(tabClassName=='qtfj'){ //其他附件
        		  $.ajax({
                  	url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getOtherFileList',
                  	data:{"correlationId":snapSupplierOrganBaseId,"type":0},
                  	type:"post",
                  	dataType:'json',
                  	success:function(data){
                  		alert("其他附件长度："+data.otherFileList.length)
                  	},
                  	error:function(){
                  		
                  	}
                  });
        	  }else if(tabClassName=='zlbzxy'){//质量保证协议
           		 initTable1(snapSupplierOrganBaseId);
          	  }else if(tabClassName=='khwts'){//客户委托书
          		 initTable2(snapSupplierOrganBaseId);
          	  } 
        }
    });
    
    //切换的质量保证协议
    $('#table1').XGrid({
        data: [{
            id: '',
            signDate: '',
            validDate: '',
            signName: '',
            enclosureCount:''
        }],
        colNames: ['', '签订日期', '有效期至', '签订人', '附件'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'signDate',
            index: 'Signdata',
            rowtype: '#grid_BeginDate',
            formatter:function(value){
            	var date=value;
            	if(!value)return false;
        		date=format(value);
            	return date.split(' ')[0];
            }
        }, {
            name: 'validDate',
            index: 'vld',
            rowtype: '#grid_endDate',
            formatter:function(value){
            	var date=value;
            	if(!value)return false;
        		date=format(value);
            	return date.split(' ')[0];
            }
        }, {
            name: 'signName',
            index: 'signName',
            rowtype: '#grid_person'
        }, {
            name: 'enclosureCount',
            index: 'enclosureCount'
        }
        
        ],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid_page1'
    });
  //客户委托书
    $('#table2').XGrid({
        data: [{
            id: '',
            proxyOderNo: '',
            mandataryName: '',
            mandatarySex: '',
            mandataryTel: '',
            mandataryCertificateNumber: '',
            mandataryAddress: '',
            identityValidDate: '',
            proxyValidDate: '',
            authorityType: '',
            enclosureCount:'',
            supplierClientProxyProductVOList:[]
        }],
        colNames: ['','数据', '委托书编号', '被委托人', '性别', '电话', '证件号码', '地址', '身份证有效期至', '委托书有效期至', '授权类型',
        	'附件','授权范围' 
        ],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
        	name:'supplierClientProxyProductVOList',
        	index:'',
        	hidden:true
        },{
            name: 'proxyOderNo',
            index: 'proxyOderNo',
            rowtype: '#entrustCode'
        }, {
            name: 'mandataryName',
            index: 'mandataryName',
            rowtype: '#entrustPerson'
        }, {
            name: 'mandatarySex',
            index: 'mandatarySex',
            rowtype: '#sexType'
        }, {
            name: 'mandataryTel',
            index: 'mandataryTel',
            rowtype: '#phoneP'
        }, {
            name: 'mandataryCertificateNumber',
            index: 'mandataryCertificateNumber',
            rowtype: '#crteP'
        }, {
            name: 'mandataryAddress',
            index: 'mandataryAddress',
            rowtype: '#posP'
        }, {
            name: 'identityValidDate',
            index: 'identityValidDate',
            rowtype: '#crteTerm',
            formatter:function(value){
            	var date=value;
            	if(!value)return false;
        		date=format(value);
            	return date.split(' ')[0];
            }
        }, {
            name: 'proxyValidDate',
            index: 'proxy_vld',
            rowtype: '#entrustTerm',
            formatter:function(value){
            	var date=value;
            	if(!value)return false;
        		date=format(value);
            	return date.split(' ')[0];
            }
        }, {
            name: 'authorityType',
            index: 'authorityType',
            rowtype: '#grantType'
        }, {
        	name: 'enclosureCount',
            index: 'enclosureCount'
        }, {
            index: 'authrange',
            rowtype: '#commodity'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid_page2'
    });
    //批准文件
    $('#table3').XGrid({
        data: [{
            id: '',
            certificateId: '',
            certificateNum: '',
            scopeOfBusiness: '',
            certificationOffice: '',
            certificationDate: '',
            validityDate: '',
            enclosureCount: ''
        }],
        colNames: ['', '证书类型', '证书编号', '经营范围', '发证机关', '发证日期', '有效期至', '附件'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'certificateId',
            index: 'certificateId',
            rowtype: '#certType'
        }, {
            name: 'certificateNum',
            index: 'certificateNum'
        }, {
            name: 'scopeOfBusiness',
            index: 'scopeOfBusiness',
            rowtype: '#operScopeZtree'
        }, {
            name: 'certificationOffice',
            index: 'certificationOffice'
        }, {
            name: 'certificationDate',
            index: 'certificationDate',
            rowtype: '#fCrte',
            formatter:function(value){
            	var date=value;
            	if(!value)return false;
        		date=format(value);
            	return date.split(' ')[0];
            }
        }, {
            name: 'validityDate',
            index: 'validityDate',
            rowtype: '#docTerm',
            formatter:function(value){
            	var date=value;
            	if(!value)return false;
        		date=format(value);
            	return date.split(' ')[0];
            }
        }, {
        	name: 'enclosureCount',
            index: 'enclosureCount'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid_page3'
    });
    //年度报告
       $('#table4').XGrid({
        data: [{
            id: '',
            reportDate: '',
            manageAbnormal: '',
            administrativePenalty: '',
            enclosureCount: ''
        }],
        colNames: ['', '报告年份', '是否经营异常', '是否有行政处罚', '附件'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'reportDate',
            index: 'reportDate',
            rowtype: '#reportYear'
        }, {
            name: 'manageAbnormal',
            index: 'manageAbnormal',
            rowtype: '#isAbnormal'
        }, {
            name: 'administrativePenalty',
            index: 'administrativePenalty',
            rowtype: '#isPunish'
        }, {
        	name: 'enclosureCount',
            index: 'enclosureCount'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid_page4'
    });

    var snapSupplierOrganBaseId =  $("#snapSupplierOrganBaseId").val(); //运营属性
    //详情加载质量保证协议和批准文件
   	if(null != snapSupplierOrganBaseId && "" !=snapSupplierOrganBaseId){
   		 initTable3(snapSupplierOrganBaseId);//批准文件
   		 initTable1(snapSupplierOrganBaseId);//量保证协
   	}
  
       
       
       
       
   //客户委托书授权类型变化
   $(".grantType").on("change",function(){
       	var grantType = $('.grantType option:selected') .val();//选中的值
       	if("0"==grantType){//选择商品
       		$(".commodity").text("").text('选择商品');
       	}else if("1"==grantType){//经营范围授权
       		$(".commodity").text("").text('经营范围');
       	}
   }) ;
   var clientProductList=[];
   
   
   //点击选择商品或经营范围
    $('.commodity').on('click', function (ev) {
    	var $tr=$(this).parents("tr");
    	var trId=$tr.attr("id");
    	var grantType = $('.grantType option:selected') .val();//选中的值
    	if("0"==grantType){//选择商品
            utils.dialog({
                width: $(window).width() * 0.7,
                height:  $(window).height() * 0.6,
                title: '选择商品',
                url: '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/getProductList',
                data: {
                    'selectArr': selectArr ? selectArr : [],
                    'resultArr': resultArr ? resultArr : []
                },
                okValue: '确定',
                ok: function () {
                    var arr=$('#selected_Tableb').getRowData();
                    if(arr.length > 0){
                        var productIdArr=[];
                        for(var i=0;i<arr.length;i++)
                        {
                            var productCode=arr[i].productCode;
                            productIdArr.push({
                                productCode:productCode
                            })
                        }
                        //添加客户委托书对应的商品
                        if(trId){
                            $("#table2").XGrid('setRowData',trId,{supplierClientProxyProductVOList:JSON.stringify(productIdArr)});
                        }
                    }
                },
                cancelValue: '取消',
                cancel: function () {
                }
            }).showModal();
            ev.stopPropagation();
    	}else if("1"==grantType){//经营范围授权
    		
    		
    	}
    	
    });
   
    
  //经营范围树
    var zTreeObj;
    var zTreeNodes = [{
        id: 0,
        name: "全国",
        // open: true,//展开
        children: [{
            id: 1,
            name: "湖北",
            children: [{
                id: 11,
                name: "武汉"
            },
            {
                id: 12,
                name: "宜昌"
            },
            {
                id: 13,
                name: "荆州"
            },
            {
                id: 14,
                name: "孝感"
            }
            ]
        },
        {
            id: 2,
            name: "湖南",
            // checked:true  //默认选中
        },
        {
            id: 3,
            name: "河南"
        }
        ]
    }];
    var setting = {
        callback: {
            onClick: zTreeOnClick,
        },
        check: {
            enable: true, //显示勾选框  默认不显示
        },
    };
    zTreeObj = $.fn.zTree.init($(".operScopeZtree"), setting, zTreeNodes);
    //选中事件
    function zTreeOnClick(event, treeId, treeNode) {
        console.log(event, treeId, treeNode);
        grid_data = [];
        grid_data = [{
            id: "1",
            type: "2",
            pay: "3",
            name: treeNode.name,
            text: "5"
        }]
    };
    //初始化地址选择
    $('[data-toggle="distpicker"]').distpicker();
    //添加仓库
    $("#addDepot").on("click", function () {
        var html = distpickerHTML();
        $("#depotAddress").append(html);
        $('[data-toggle="distpicker"]').distpicker();
    });
    //删除仓库地址
    $("#depotAddress").on("click", ".removeDepot", function () {
        $(this).parents(".depotList").remove();
    });
   

  
});
//获取仓库地址
function distpickerHTML(n){
	var len=n?n:1;
	var html = '';
	for(var i=0;i<len;i++)
	{
		html += '<div class="col-md-6 depotList">\
	        <div class="input-group">\
	            <div class="input-group-addon">仓库地址</div>\
	            <div data-toggle="distpicker" class="form-control form-inline distpicker">\
	                <div class="row">\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control" name="repertoryProvince"></select>\
	                    </div>\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control" name="repertoryCity"></select>\
	                    </div>\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control" name="repertoryArea"></select>\
	                    </div>\
	                    <div class="form-group col-md-4">\
	                        <input type="text" class="form-control text-inp" name="repertoryDetail"/>\
	                    </div>\
	                    <div class="form-group btn-box col-md-1">\
	                        <button type="button" class="btn removeDepot">\
	                            <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>\
	                        </button>\
	                    </div>\
	                </div>\
	            </div>\
	        </div>\
	    </div>';
	}
	return html;
}
/**
 * 对页面中表单元素进行赋值
 * @param json 必传 eg:{"a":1,"b":2}
 * @param jq对象 表示:从当前对象下找对应的元素进行赋值
 * */
function loadData(json,el) {
    var obj = json;
    console.log(obj)
    var key, value, tagName, type, arr, thisVal,$el;
    for (x in obj) {
        key = x;
        value = obj[x];
        if(el)
        {
        	$el=el.find("[name='" + key + "']");
        }else{
        	$el=$("[name='" + key + "']");
        }
        $el.each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).attr('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    for (var i = 0; i < arr.length; i++) {
                        if (thisVal == arr[i]) {
                            $(this).attr('checked', true);
                            break;
                        }
                    }
                } else {
                    $(this).val(value);
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
            }
        });
    }
}



//质量保证协议
function initTable1(supplierOrganBaseId){
	 var xGridDate=[];
     $.ajax({
       	url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getQualityAgreementList',
       	data:{"correlationId":supplierOrganBaseId,"type":2},
       	type:"post",
       	dataType:'json',
       	success:function(data){
       		if(data.result.list.length>0){
       			var dataList = data.result.list;
       			for(var i=0;i<dataList.length;i++){
       			  var a={
       					id: dataList[i].id,
   			            signDate: dataList[i].signDate,
   			            validDate: dataList[i].validDate,
   			            signName: dataList[i].signName,
   			            enclosureCount:dataList[i].enclosureCount
       		        }
       			   xGridDate.push(a);
       			}
           	}else{
           		xGridDate = [{
           			id: '',
		            signDate: '',
		            validDate: '',
		            signName: '',
		            enclosureCount:''
                  }]
           	}
       		
       		$('#table1').XGrid('setGridParam', {
       			data:xGridDate,
                 pager: '#grid_page1'
             }).trigger('reloadGrid');
       	}
       });
}
//客户委托书
function initTable2(supplierOrganBaseId){
	 var xGridDate=[];
     $.ajax({
       	url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getClientProxyOrderList',
       	data:{"correlationId":supplierOrganBaseId,"type":2},
       	type:"post",
       	dataType:'json',
       	success:function(data){
       		if(data.result.list.length>0){
       			var dataList = data.result.list;
       			for(var i=0;i<dataList.length;i++){
       			  var a={
       					id: dataList[i].id,
       		            proxyOderNo: dataList[i].proxyOderNo,
       		            mandataryName: dataList[i].mandataryName,
       		            mandatarySex: dataList[i].mandatarySex,
       		            mandataryTel: dataList[i].mandataryTel,
       		            mandataryCertificateNumber: dataList[i].mandataryCertificateNumber,
       		            mandataryAddress: dataList[i].mandataryAddress,
       		            identityValidDate: dataList[i].identityValidDate,
       		            proxyValidDate: dataList[i].proxyValidDate,
       		            authorityType: dataList[i].authorityType,
       		            enclosureCount:dataList[i].enclosureCount,
       		            supplierClientProxyProductVOList:dataList[i].supplierClientProxyProductVOList
       		        }
       			   xGridDate.push(a);
       			}
           	}else{
           		xGridDate = [{
	           		 id: '',
	                 proxyOderNo: '',
	                 mandataryName: '',
	                 mandatarySex: '',
	                 mandataryTel: '',
	                 mandataryCertificateNumber: '',
	                 mandataryAddress: '',
	                 identityValidDate: '',
	                 proxyValidDate: '',
	                 authorityType: '',
	                 enclosureCount:'',
	                 supplierClientProxyProductVOList:[]
                  }]
           	}
       		$('#table2').XGrid('setGridParam', {
       			data:xGridDate,
                 pager: '#grid_page2'
             }).trigger('reloadGrid');
       	}
       });
}



//批准文件
function initTable3(supplierBaseId){
	 var xGridDate=[];
     $.ajax({
       	url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getApprovalFileList',
       	data:{"correlationId":supplierBaseId,"type":2},
       	type:"post",
       	dataType:'json',
       	success:function(data){
       		if(data.result.list.length>0){
       			var dataList = data.result.list;
       			for(var i=0;i<dataList.length;i++){
       			  var a={
       		            id: dataList[i].id,
       		            certificateId: dataList[i].certificateId,
       		            certificateNum:dataList[i].certificateNum,
       		            scopeOfBusiness: dataList[i].scopeOfBusiness,
       		            certificationOffice: dataList[i].certificationOffice,
       		            certificationDate: dataList[i].certificationDate,
       		            validityDate: dataList[i].validityDate,
       		            enclosureCount: dataList[i].enclosureCount
       		        }
       			   xGridDate.push(a);
       			}
           	}else{
           		xGridDate = [{
                      id: '',
                      certificateId: '',
                      certificateNum: '',
                      scopeOfBusiness: '',
                      certificationOffice: '',
                      certificationDate: '',
                      validityDate: '',
                      enclosureCount: ''
                  }]
           	}
       		
       		$('#table3').XGrid('setGridParam', {
       			data:xGridDate,
                 pager: '#grid_page3'
             }).trigger('reloadGrid');
       	}
      });
}
//年度报告
function initTable4(supplierBaseId){
	 var xGridDate=[];
     $.ajax({
       	url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getYearReportList',
       	data:{"correlationId":supplierBaseId,"type":2},
       	type:"post",
       	dataType:'json',
       	success:function(data){
       		if(data.result.list.length>0){
       			var dataList = data.result.list;
       			for(var i=0;i<dataList.length;i++){
       			  var a={
       					id: dataList[i].id,
   			            reportDate: dataList[i].reportDate,
   			            manageAbnormal: dataList[i].manageAbnormal,
   			            administrativePenalty: dataList[i].administrativePenalty,
   			            enclosureCount: dataList[i].enclosureCount 
       		        }
       			   xGridDate.push(a);
       			}
           	}else{
           		xGridDate = [{
           			id: '',
       	            reportDate: '',
       	            manageAbnormal: '',
       	            administrativePenalty: '',
       	            enclosureCount: ''
                  }]
           	}
       		$('#table4').XGrid('setGridParam', {
       			data:xGridDate,
                 pager: '#grid_page4'
             }).trigger('reloadGrid');
       	}
       });
	
}

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
var time = new Date(shijianchuo);
var y = time.getFullYear();
var m = time.getMonth()+1;
var d = time.getDate();
var h = time.getHours();
var mm = time.getMinutes();
var s = time.getSeconds();
return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }

