﻿ var getData;
 $(function () {
     /* 模拟数据 */
     /*var data_a = [{
         productCode: "Y010101591",
         productName: "西地碘含片(华素片)",
         size: "1.5毫克*15",
         dosage: "片剂",
         enterprise: "北京华素制药股份有限公司",
         from: "北京",
         unit: "盒",
         number: "10",
         price: "7.300",
         sum: "73.00",
         sterilizationBatch: "",
         batch: "1805072",
         productionDate: "2018-04-30",
         effectiveDate: "2020-04-30",
         storageName: "合格库",
         locationName: "合格库",
         approvalNumber: "国药准字H10910012",
         quality: "合格",
     }, {
         productCode: "Y010101591",
         productName: "西地碘含片(华素片)",
         size: "1.5毫克*15",
         dosage: "片剂",
         enterprise: "北京华素制药股份有限公司",
         from: "北京",
         unit: "盒",
         number: "10",
         price: "7.300",
         sum: "73.00",
         sterilizationBatch: "",
         batch: "1805072",
         productionDate: "2018-04-30",
         effectiveDate: "2020-04-30",
         storageName: "合格库",
         locationName: "合格库",
         approvalNumber: "国药准字H10910012",
         quality: "合格",
     }];

     var data_b = [{
         indentCode: "YBM20180817102154100237",
         carriage: "0",
         salesSum: "1161.25",
         couponSum: "100",
         paySum: "1061.25",
         paySumC: "壹仟零陆拾壹元贰角伍分",
         isOnlinePay: "已支付",
         payType: "A"
     }];

     var data_c = [{
         key: "发票情况",
         value: [],
         other: "",
     },
         {
             key: "结款情况",
             value: [],
             other: "缓存区名称：Y21-56--Y21-56",
         },
         {
             key: "结款方式",
             value: [3],
             other: "",
         },
         {
             key: "备注",
             value: "急用",
             other: "",
         },
     ];*/
     /* 日期格式化 */
     //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
     function dateFormat(time, format) {
         var t = new Date(parseInt(time,10));
         var tf = function (i) {
             return (i < 10 ? '0' : '') + i;
         };
         return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
             switch (a) {
                 case 'yyyy':
                     return tf(t.getFullYear());
                     break;
                 case 'MM':
                     return tf(t.getMonth() + 1);
                     break;
                 case 'mm':
                     return tf(t.getMinutes());
                     break;
                 case 'dd':
                     return tf(t.getDate());
                     break;
                 case 'HH':
                     return tf(t.getHours());
                     break;
                 case 'ss':
                     return tf(t.getSeconds());
                     break;
             }
         });
     }

   var excludeSpecial = function(s) {
        // 去掉转义字符
        s = s.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
        // 去掉特殊字符
        s = s.replace(/\<|\>|\"|\'|\&/g,'');
        return s;
     };


     /* 获取数据 */
     getData = function (printType,outOrderCode) {
         var temp = []; //一个新的临时数组
         for(var i = 0; i < outOrderCode.length; i++){
             if(temp.indexOf(outOrderCode[i]) == -1){
                 temp.push(outOrderCode[i]);
             }
         }
         if(temp.length>10){
             utils.dialog({
                 title:'提示',
                 content:"不同入库单编号不得超过10条",

             }).showModal();
             return ;
         }
         var outOrderCodes = temp.join(',');
         $.ajax({
             url:"/proxy-purchase/purchase/purchaseOrderStoreAndProduct/selectPurchaseStoreOrderPrintData",
             data:{
                 stockOrderNos :  outOrderCodes,
                 printType: printType
             },

             success:function(res){
                 if(res.result==""||res.result==null){
                     utils.dialog({
                         title:'提示',
                         content:"数据为空或格式不正确",

                     }).showModal();
                     return ;
                 }
                 var data=[];
                 var err=[];
                 res.result.forEach(function(item,index){
                     if(item.length>0){
                         item.forEach(function(val,ind){
                             data.push(val)
                         })
                     }else{
                         err.push(1)
                     }
                 })
                 if(err.length>0){
                     utils.dialog({
                         title:'提示',
                         content:"数据为空或格式不正确",
                     }).showModal();
                 }else{
                     webRender(data,printType);
                 }
             },
             error:function(){

             }
         })
     }
     /* 数据渲染 */
     function webRender(data,printType) {
         var box_html = '';
         /* 基本结构拼装 */
         data.forEach(function (item,index) {
             /* 销售出库复核单 */
             var ifSpecialDrugs= item.ifSpecialDrugs;
             var titles = "购进药品验收入库单";
             if(ifSpecialDrugs != null && ifSpecialDrugs != '' && ifSpecialDrugs == 1){
                 titles = "购进药品验收入库单（特殊药品）";
             }
            box_html +=`
            <div class="content indent1">
             <div class="header">
                 <div class="title">`+titles+`</div>
                  
             </div>
             <div class="top">
                 <ul class="info_list">

                     <li>
                         <span class="col_8">部门：<i class="val">质管部</i></span>
                         <span class="col_2"> 单据编号：<i class="val">${item.stockOrderNo}</i></span>
                     </li>
                     
                     <li>
                         <span  class="col_4">供货单位：<i class="val">${item.supplierName}</i></span>
                         <span  class="col_4"  style="padding-left: 30px;">采购员：<i class="val">${item.purchaseUserNames}</i></span>
                         <span   class="col_2" >验收日期：<i class="val">${item.storeTime}</i></span>
                     </li>
                 </ul>`;
              if(item.ifCold){box_html+=`<span class="inden_type inden_type" style="right: 60px!important;">冷</span>`;}
             box_html+=  `<span class="inden_type">购</span>
             </div>
             <table id="table_a_${index}"></table>
             <div class="bottom">
                 <ul class="info_list">
                     <li>
                         <span class="col_2">上架员：<i class="val">${item.shelverUser}</i></span>
                         <span class="col_2">验收员：<i class="val">${item.inspectorUser}</i></span>
                         <span class="col_2">共<i class="val">${item.pageTotal}</i>页，第<i class="val">${item.pageNumber}</i>页</span>
                         <span class="col_4" style="padding-right: 20px; text-align:right;">白联：质管部 红联：财务部 黄联：仓储</span>
                     </li>
                 </ul>
             </div>
             <div style="page-break-after:always"></div>
         </div>
            `;

         });

   $("#box").html(box_html);

         /* 表格初始化 */
        data.forEach(function (item,index) {
             item.list = item.list.map(function (val,key) {
                 delete val.id;
                 return val
             });
             /* 销售出库复核单 */
             $("#table_a_"+index).jqGrid({
                 data: item.list,
                 datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                 height: "auto", //高度，表格高度。可为数值、百分比或'auto'

                 colNames: ['商品编号','药品通用名称(商品名)','规格','单位','生产企业','验收数量','单价','金额','批号','生产日期','有效期至','批准文号','剂型','货位名称', '质量状况','验收结论'],
                 colModel: [
                             {
                                 index: 'productCode',
                                 name: 'productCode',
                                  width: 80,
                                  cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                     //console.log(rowId, tv, rawObject, cm, rdata);
                                     if (rowId == 14) {

                                         return 'colspan=2'
                                     }
                                 }
                             },
                             {
                                    index: 'productName',
                                    name: 'productName',
                                     width: 150,
                                     cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                     //console.log(rowId, tv, rawObject, cm, rdata);
                                     if (rowId == 14) {

                                          return 'colspan=4 style="text-align: left;padding-left:5px;"'
                                     }else if (rowId == 13) {

                                       return 'colspan=6 style="text-align: left;padding-left:5px;"'
                                  }
                                 }
                                }, {
                                    index: 'productSpecification',
                                    name: 'productSpecification',
                                     width: 130,
                                     cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                           if (rowId == 13) {
                                                 return 'colspan=9 style="text-align: left;padding-left:5px;"'
                                            };
                                    }
                                }, {
                                    index: 'productPackUnitSmall',
                                    name: 'productPackUnitSmall',
                                     width: 35,
                                     cellattr: function (rowId, tv, rawObject, cm, rdata) {

                                            if(rowId == 14) {
                                                return 'colspan=9 style="text-align: left;padding-left:5px;"'
                                           };
                                    }
                                }, {
                                    index: 'productProduceFactory',
                                    name: 'productProduceFactory',
                                     width: 125,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        if (rowId == 13) {
                                                return 'style="display:none"'
                                        }
                                    },
                                   formatter:function (e) {
                                         if(e){
                                             return excludeSpecial(e);
                                         }else{
                                             return ""
                                         }
                                     }
                                }, {
                                    index: 'productPackInStoreCount',
                                    name: 'productPackInStoreCount',
                                     width: 70,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                            if (rowId == 13) {
                                                return 'style="display:none"'
                                            };
                                    }
                                }, {
                                    index: 'productContainTaxPrice',
                                    name: 'productContainTaxPrice',
                                     width: 70,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                            if (rowId == 13) {
                                                return 'style="display:none"'
                                            };
                                    }

                                }, {
                                    index: 'productContainTaxMoney',
                                    name: 'productContainTaxMoney',
                                     width: 70,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                            if (rowId == 13) {
                                                return 'style="display:none"'
                                            };
                                    }
                                }, {
                                    index: 'productBatchNo',
                                    name: 'productBatchNo',
                                     width: 100,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                            if (rowId == 13) {
                                                return 'style="display:none"'
                                            };
                                    }
                                }, {
                                    index: 'productProduceDate',
                                    name: 'productProduceDate',
                                     width: 70,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                            if (rowId == 13) {
                                                return 'style="display:none"'
                                            };
                                    }

                                }, {
                                    index: 'productExpireDate',
                                    name: 'productExpireDate',
                                     width: 70,
                                     cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        if (rowId == 13) {
                                            return 'style="display:none"'
                                        };
                                    }

                                }, {
                                    index: 'productApprovalNumber',
                                    name: 'productApprovalNumber',
                                     width: 150,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        if (rowId == 13) {
                                            return 'style="display:none"'
                                        };
                                    }
                                }, {
                                    index: 'productPreparation',
                                    name: 'productPreparation',
                                     width: 70,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                            if (rowId == 13) {
                                                return 'style="display:none"'
                                            };
                                    }
                                }, {
                                    index: 'goodsAllocation',
                                    name: 'goodsAllocation',
                                     width: 70,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                            if (rowId == 13) {
                                                return 'style="display:none"'
                                            };
                                    }
                                } , {
                                    index: 'productQuality',
                                    name: 'productQuality',
                                     width: 65,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                            if (rowId == 13) {
                                                return 'style="display:none"'
                                            };
                                    }
                                }, {
                                     index: 'result',
                                     name: 'result',
                                      width: 65,

                                 }],
                                shrinkToFit: true,
                                rowNum: 12,
                                gridview: true,
                                gridComplete: function () {
                                    var sum_number = $(this).getCol('productPackInStoreCount', false, 'sum');
                                    var sum_sum = $(this).getCol('productContainTaxMoney', false, 'sum');
                                    var data = $(this).getRowData();
                                    //console.log(data);
                                    if (data.length < 12) {
                                        $(this).addRowData(data.length + 1, {}, "last");
                                    } else if (data.length == 12) {
                                        $(this).addRowData(13, {
                                            productCode: "小计",
                                           // productPackInStoreCount: sum_number,
                                            productSpecification:"￥："+ sum_sum.toFixed(2) +"元",
                                        }, "last");
                                    } else if (data.length == 13) {
                                        $(this).addRowData(14, {
                                            productCode: "金额合计(大写)",
                                            productName: chineseNumber(item.priceTaxSum),
                                            productSpecification: "金额合计",
                                            productPackUnitSmall: "￥："+(parseFloat(item.priceTaxSum)).toFixed(2)+"元",
                                        }, "last");
                                    }
                                    //$(this).footerData("set",{"productCode":"合计","number":sum_number,"sum":sum_sum});
                                }
                            });




         });


         if(printType==0){
             /* 打印预览 */
             utils.dialog({
                 title:'预览',
                 content:$('#big_box').html(),
                 //okValue:'确定',

             }).showModal();
             //$("#print_box").contents().find(".ui-dialog-content").css("overflow","auto");
             window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
         }else if(printType==1){
             /* 打印 */
             $("#box").jqprint({
                 globalStyles: true, //是否包含父文档的样式，默认为true
                 mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                 stylesheet: null, //外部样式表的URL地址，默认为null
                 noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                 iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                 append: null, //将内容添加到打印内容的后面
                 prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                 deferred: $.Deferred() //回调函数
             });
         }
     }



//转大写

     function chineseNumber(n) {
         var fraction = ['角', '分'];
         var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
         var unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
         var head = n < 0 ? '负' : '';
         n = Math.abs(n);

         var s = '';

         for (var i = 0; i < fraction.length; i++) {
             s += (digit[Math.floor((n * 100/10) * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
         }
         s = s || '整';
         n = Math.floor(n);

         for (var i = 0; i < unit[0].length && n > 0; i++) {
             var p = '';
             for (var j = 0; j < unit[1].length && n > 0; j++) {
                 p = digit[n % 10] + unit[1][j] + p;
                 n = Math.floor(n / 10);
             }
             s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
         }
         return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
     }

 });