$(function () {
    $('.fold-block[fold=sub]').fold();
    var totalTable = z_utils.totalTable;
    var isKa = $('#isKa').val();
    console.log("接受到传递的值"+isKa)
    $('#addBtn').click(function () {

        window.location.href = "/proxy-finance/finance/purchase/payrequestinfo/addRequestApplication?isKa="+isKa;
    })


    var _tableBody= "";
    var _index =0;
    // tabs 切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $('.nav-content');
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass(
            'active');
        // if ($(this).index() == '0') {
        //
        //    // $('#Y_Tablea tr:first-child input[type="checkbox"]').prop('checked', false).trigger('input');
        // } else {
        //     //选择发票
        //    // $('#isPrepay').val('C02');
        //    // $('#isPrepayStr').val(getFundName('C02'));
        //    // $('#X_Tablea tr:first-child input[type="checkbox"]').prop('checked', false).trigger('input');
        // }
        _tableBody=returnTbles($(this).index());
        $("#searchBtn").trigger('click');
    })
    _tableBody=returnTbles(0);
    function returnTbles(id){
        $("#X_TableBox").hide();
        $("#Y_TableBox").hide();
        $("#Z_TableBox").hide();
        $("#printBtn").hide();
        $("#editBtn").hide();
        _index = id;

        switch (String(id)) {
            case "0":
                $("#X_TableBox").show();
                totalSum();
                $("#printBtn").show();
                $("#editBtn").show();
                return $("#X_Table");
                break;
            case "1":
                $("#Y_TableBox").show();
                totalDetailSum();
                return $("#Y_Table");
                break;
            case "2":
                $("#Z_TableBox").show();
                totalDetailSum();
                return $("#Z_Table");
                break;
            default:
                $("#X_TableBox").show();
                return $("#X_Table");
                break
        }
    }



    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result);
            $("#supplierNo").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#supplierNo").val("");

        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

//供应商双击查询
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#supplierName").val(data.supplierName);
                    $("#supplierNo").val(data.supplierCode);
                }else{

                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }
    var colNamesB = ['供应商编号', '供应商名称', '付款申请单号', '申请人', '款项类型', '支付方式','供应商财务毛利率',  '最早可支付时间','开户银行','开户户名','银行账号','承兑开户行号', '申请日期', '期望支付日期', '申请金额', '本次预付余额抵扣', '本次实付金额','票折抵扣','其他抵扣', '状态','待审批人','审批完成时间','备注'],
        colModelB = [
            {
                name: 'supplierNo'
            }, {
                name: 'supplierName'
            }, {
                name: 'billNo'
            }, {
                name: 'createUser'
            }, {
                name: 'isPrepayStr'
            }, {
                name: 'payableTypeStr',
                editable: true
            },{
                name:'supplierProfitRate',
                width:200
            },  {
				name: 'earliestPayTime'
			}, {
                name: 'bankName'
            }, {
                name: 'accountName'
            }, {
                name: 'bankAccount'
            },{
                name: 'openBankNum'
            },{
                name: 'billTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'expectPayTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'applayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'prepaymentAmountDeduction',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'actualPaymentAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name :'ticketDeduction',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name:'otherDeductions',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'status',
                editable: true,
                formatter: function (val) {
                    if (val == "1") {
                        return "已保存"
                    } else if (val == "2") {
                        return "待审核"
                    } else if (val == "3") {
                        return "审核中";
                    } else if (val == "4") {
                        return "已驳回";
                    } else if (val == "5") {
                        return "待付款"
                    } else if (val == "6") {
                        return "已付款";
                    } else if (val == "7") {
                        return "暂停付款";
                    } else if (val == "8") {
                        return "部分付款";
                    }
                }
            }, {
                name: 'assessor'
            }, {
                name: 'finishTime',
                editable: true,
                formatter:function (value){
                    if(value == null || value == "")
                        return "";
                    var date = new Date(value);
                    var y = date.getFullYear();
                    var m = date.getMonth() + 1;
                    m = m < 10 ? ('0' + m) : m;
                    var d = date.getDate();
                    d = d < 10 ? ('0' + d) : d;
                    var h = date.getHours();
                    h = h < 10 ? ('0' + h) : h;
                    var minute = date.getMinutes();
                    var second = date.getSeconds();
                    minute = minute < 10 ? ('0' + minute) : minute;
                    second = second < 10 ? ('0' + second) : second;
                    return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
                },
            },

            {
                name: 'remarks'
            },{
                name: 'id',
                hidden: 'true',
                hidegrid: true //是否在筛选列时展示此列
            }

        ];
    var allColModelB = JSON.parse(JSON.stringify(colModelB));

    //var colNames = ['供应商编号', '供应商名称', '付款申请单号', '申请人', '款项类型', '支付方式','开户银行','开户户名','银行账号', '申请日期', '期望支付日期', '申请金额', '本次预付余额抵扣', '本次实付金额','票折抵扣','其他抵扣', '状态','待审批人','审批完成时间','是否集采','是否OEM','备注'],
    var colNames = ['供应商编号', '供应商名称', '付款申请单号', '申请人', '款项类型', '支付方式','供应商财务毛利率<i class="questa"></i> ', '最早可支付时间','开户银行','开户户名','银行账号', ' 承兑开户行号 ', '申请日期', '期望支付日期', '申请金额', '本次预付余额抵扣', '本次实付金额','票折抵扣','其他抵扣', '状态','待审批人','审批完成时间','发起部门','单据属性','备注'],
        colModel = [
            {
                name: 'supplierNo'
            }, {
                name: 'supplierName'
            }, {
                name: 'billNo'
            }, {
                name: 'createUser'
            }, {
                name: 'isPrepayStr'
            }, {
                name: 'payableTypeStr',
                editable: true
            },{
                name:'supplierProfitRate',
                width:200
            }, {
				name: 'earliestPayTime'
			},{
                name: 'bankName'
            }, {
                name: 'accountName'
            }, {
                name: 'bankAccount'
            },{
                name: 'openBankNum'
            },{
                name: 'billTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'expectPayTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'applayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'prepaymentAmountDeduction',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'actualPaymentAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name :'ticketDeduction',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name:'otherDeductions',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'status',
                editable: true,
                formatter: function (val) {
                    if (val == "1") {
                        return "已保存"
                    } else if (val == "2") {
                        return "待审核"
                    } else if (val == "3") {
                        return "审核中";
                    } else if (val == "4") {
                        return "已驳回";
                    } else if (val == "5") {
                        return "待付款"
                    } else if (val == "6") {
                        return "已付款";
                    } else if (val == "7") {
                        return "暂停付款";
                    } else if (val == "8") {
                        return "部分付款";
                    }
                }
            }, {
                name: 'assessor'
            }, {
                name: 'finishTime',
                editable: true,
                formatter:function (value){
                    if(value == null || value == "")
                        return "";
                    var date = new Date(value);
                    var y = date.getFullYear();
                    var m = date.getMonth() + 1;
                    m = m < 10 ? ('0' + m) : m;
                    var d = date.getDate();
                    d = d < 10 ? ('0' + d) : d;
                    var h = date.getHours();
                    h = h < 10 ? ('0' + h) : h;
                    var minute = date.getMinutes();
                    var second = date.getSeconds();
                    minute = minute < 10 ? ('0' + minute) : minute;
                    second = second < 10 ? ('0' + second) : second;
                    return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
                },
            },
            // {
            //     name: 'isCollect',
            //     formatter: function (val) {
            //         if (val == "1") {
            //             return "是"
            //         } else if (val == "0") {
            //             return "否"
            //         } else   {
            //             return "";
            //         }
            //     }
            // },
            {
                name: 'sourceDept',
                formatter: function (val) {
                    if (val == "1") {
                        return "省区"
                    } else if (val == "2") {
                        return "集采中心"
                    } else if (val == "3") {
                        return "OEM"
                    } else   {
                        return "";
                    }
                }
            },
            {
                name: 'docAttr',
                formatter: function (val) {
                    if (val == "1") {
                        return "地采"
                    } else if (val == "2") {
                        return "集采"
                    } else if (val == "3") {
                        return "集采-统谈统采"
                    } else if (val == "4") {
                        return "集采-统谈分采"
                    } else if (val == "5") {
                        return "OEM"
                    } else {
                        return "";
                    }
                }
            },
            {
                name: 'remarks'
            },{
                name: 'id',
                hidden: 'true',
                hidegrid: true //是否在筛选列时展示此列
            }

        ];
    var colNameB = ['供应商编号', '供应商名称', '付款申请单号', '申请人', '款项类型', '支付方式','供应商财务毛利率', '最早可支付时间','开户银行','开户户名','银行账号','承兑开户行号', '申请日期', '期望支付日期', '申请金额', '本次预付余额抵扣', '本次实付金额','票折抵扣','其他抵扣', '状态','待审批人','审批完成时间','发起部门','单据属性','备注'],
        colModeB = [
            {
                name: 'supplierNo'
            }, {
                name: 'supplierName'
            }, {
                name: 'billNo'
            }, {
                name: 'createUser'
            }, {
                name: 'isPrepayStr'
            }, {
                name: 'payableTypeStr',
                editable: true
            },{
                name:'supplierProfitRate',
                width:200
            }, {
				name: 'earliestPayTime'
			},{
                name: 'bankName'
            }, {
                name: 'accountName'
            }, {
                name: 'bankAccount'
            },{
                name: 'openBankNum'
            },{
                name: 'billTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'expectPayTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'applayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'prepaymentAmountDeduction',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'actualPaymentAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name :'ticketDeduction',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name:'otherDeductions',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'status',
                editable: true,
                formatter: function (val) {
                    if (val == "1") {
                        return "已保存"
                    } else if (val == "2") {
                        return "待审核"
                    } else if (val == "3") {
                        return "审核中";
                    } else if (val == "4") {
                        return "已驳回";
                    } else if (val == "5") {
                        return "待付款"
                    } else if (val == "6") {
                        return "已付款";
                    } else if (val == "7") {
                        return "暂停付款";
                    } else if (val == "8") {
                        return "部分付款";
                    }
                }
            }, {
                name: 'assessor'
            }, {
                name: 'finishTime',
                editable: true,
                formatter:function (value){
                    if(value == null || value == "")
                        return "";
                    var date = new Date(value);
                    var y = date.getFullYear();
                    var m = date.getMonth() + 1;
                    m = m < 10 ? ('0' + m) : m;
                    var d = date.getDate();
                    d = d < 10 ? ('0' + d) : d;
                    var h = date.getHours();
                    h = h < 10 ? ('0' + h) : h;
                    var minute = date.getMinutes();
                    var second = date.getSeconds();
                    minute = minute < 10 ? ('0' + minute) : minute;
                    second = second < 10 ? ('0' + second) : second;
                    return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
                },
            },
            // {
            //     name: 'isCollect',
            //     formatter: function (val) {
            //         if (val == "1") {
            //             return "是"
            //         } else if (val == "0") {
            //             return "否"
            //         } else   {
            //             return "";
            //         }
            //     }
            // },
            {
                name: 'sourceDept',
                formatter: function (val) {
                    if (val == "1") {
                        return "省区"
                    } else if (val == "2") {
                        return "集采中心"
                    } else if (val == "3") {
                        return "OEM"
                    } else   {
                        return "";
                    }
                }
            },
            {
                name: 'docAttr',
                formatter: function (val) {
                    if (val == "1") {
                        return "地采"
                    } else if (val == "2") {
                        return "集采"
                    } else if (val == "3") {
                        return "集采-统谈统采"
                    } else if (val == "4") {
                        return "集采-统谈分采"
                    } else if (val == "5") {
                        return "OEM"
                    } else {
                        return "";
                    }
                }
            },
            {
                name: 'remarks'
            },{
                name: 'id',
                hidden: 'true',
                hidegrid: true //是否在筛选列时展示此列
            }

        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));
    var colN = "";
    var colM = "";
    if(isKa == "1"){
        colN = colNamesB;
        colM = colModelB;
    }else{
        colN = colNames;
        colM = colModel;
    }
    //设置table高度
    //utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/purchase/payrequestinfo/findPurchasePayrequestInfos',
        colNames: colN,
        colModel: colM,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        attachRow:true,
        multiselect: true,
        postData: {
            isKa:$("#isKa").val(),
            keyWord: $("#keyWord").val(),
            isPrepay: $("#isPrepay").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val(),
            createUser: $("#createUser").val(),
            //isCollect: $("#isCollect").length > 0 ? $("#isCollect").val():"",
            status: $("#status").val(),
            orderNo: $("#orderNo").val(),
            //workerFlowType:$("#workerFlowType").val(),
            supplierInvoiceNo: $("#supplierInvoiceNo").val(),
            sourceDept: $("#sourceDept").val(),
            docAttr: $("#docAttr").val()
        },
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
            var billNo = a.billNo;
            showDetail(billNo);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
        gridComplete: function () {
            var _this = $(this);
            setTimeout(function () {
                if (_this.XGrid('getRowData').length === 0) {
                    utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
                }
            }, 200);
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['applayAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
            //初始化table头部hover提示
            setTimeout(() => {
                initQuesta();
            }, 100);
        },
    });
     //初始化table头部hover提示
     function initQuesta(result) {
        var string = result;

        const questaOption = [{
            th: 'supplierProfitRate', //供应商财务毛利率
            title: `取自采购付款申请建立时，计提口径供应商维度财务毛利率`,
            width: 460,
            height: 80,
        },
        ];
        eachTipView(questaOption);
    }

    function eachTipView(arr) {
        $.each(arr, function (index, item) {
            $('.XGridHead').delegate('th[row-describedby=' + item.th + '] .questa', {
                mouseover: function (e) {
                    $('body').append(`
                        <div id='div_tooltips'>
                            <style>
                                #div_tooltips:after{
                                    content: "";
                                    width: 0;
                                    height: 0;
                                    position: absolute;
                                    left: ${item.width / 2 - 10}px;
                                    bottom: -10px;
                                    border-left: solid 10px transparent;
                                    border-top: solid 10px white;
                                    border-right: solid 10px transparent;
                                }
                            </style>
                            <div id='inner_tooltips'>${item.title}</div>
                        </div>
                    `);
                    $('#div_tooltips')
                        .css({
                            boxSizing: 'border-box',
                            width: item.width + 'px',
                            height: item.height + 'px',
                            padding: '10px',
                            zIndex: 9999,
                            backgroundColor: '#ffffff',
                            border: '1px solid #c4c4c4',
                            position: 'absolute',
                            top: $(e.target).offset().top - item.height - 10 + 'px',
                            left: $(e.target).offset().left + 5 - item.width / 2 + 'px',
                        })
                        .show('fast');
                },
                mouseout: function () {
                    $('#div_tooltips').remove();
                },
                click: function () { },
            });
        });
    }
    //总计
    totalSum();

    $("#searchBtn").on("click", function () {
        if ($("#supplierName").val() == "") {
            $("#supplierNo").val("");
        }
        //校验
        // if (validform("searchForm").form()) {//验证通过 "myform"为需要验证的form的ID
        //     utils.dialog({content: '校验通过！', quickClose: true, timeout: 2000}).showModal();
        // } else {//验证不通过
        //     utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }

        // return false;
        var url ="";
        if(_index==0){
            url = "/proxy-finance/finance/purchase/payrequestinfo/findPurchasePayrequestInfos";
            totalSum();
        }else{
            url = "/proxy-finance/finance/purchase/payrequestinfo/findPayrequestDetail";
            totalDetailSum();
        }
        _tableBody.XGrid('setGridParam', {
            url: url,
            postData: {
                isKa:$("#isKa").val(),
                keyWord: $("#keyWord").val(),
                supplierNo: $("#supplierNo").val(),
                isPrepay: $("#isPrepay").val(),
                startDate: $("#startDate").val(),
                endDate: $("#endDate").val(),
                createUser: $("#createUser").val(),
                status: $("#status").val(),
                //isCollect: $("#isCollect").length > 0 ? $("#isCollect").val():"",
                orderNo: $("#orderNo").val(),
                supplierInvoiceNo: $("#supplierInvoiceNo").val(),
                //workerFlowType:$("#workerFlowType").val(),
                tabType:_index,
                page: 1,
                sourceDept: $("#sourceDept").val(),
                docAttr: $("#docAttr").val()
            }
        }).trigger('reloadGrid');

    });

    //设置显示列
    $("#setBtn").on("click", function () {
        _tableBody.XGrid('filterTableHead');
    });

    // 删除
    $('#deleteBtn').on('click', function () {
        let selRowData = $('#X_Table').XGrid('getSeleRow')
        if(_index !=0){
            utils.dialog({content: '请在付款申请单列表页选择单据.', quickClose: true, timeout: 2000}).showModal()
            return false
        }
        if (!selRowData.length) {
            utils.dialog({content: '请先选择要删除项.', quickClose: true, timeout: 2000}).showModal()
            return false
        }
        let statusAry = selRowData.map(item => item['status'])
        if (Array.from(new Set(statusAry)).length != 1) {
            utils.dialog({ content: '要删除的数据状态不统一.', quickClose: true, timeout: 2000 }).showModal()
            return false;
        }
        if (statusAry[0] != '已保存') {
            utils.dialog({ content: '所选单据非保存状态，不能删除.', quickClose: true, timeout: 2000}).showModal()
            return false;
        }else {
            utils.dialog({
                title: '提示',
                content: '是否确认删除?',
                okValue: '确定',
                ok: function () {
                    $.ajax({
                        url: '/proxy-finance/finance/purchase/payrequestinfo/delPurchasePayrequestInfo',
                        method: 'post',
                        data: {"ids":selRowData.map( item => item['id']).join()},
                        success: (res) => {
                            if (res.code == 1) {
                                utils.dialog({content: '删除成功', quickClose: true, timeout: 2000}).showModal()
                                setTimeout(() => {
                                    $('#searchBtn').click()
                                },2000)
                            }else{
                                utils.dialog({content: '删除失败', quickClose: true, timeout: 2000}).showModal()
                            }
                        },
                        error: (err) => {
                            utils.dialog({content: err.msg, quickClose: true, timeout: 2000}).showModal()
                        }
                    })
                },
                cancelValue: '取消',
                cancel: () => {}
            }).showModal()
        }
    })
    $("#isCollect").on('change',function() {
        let num = $("#isCollect").val()
        if(num == 1) {
            $(".isVistable").css('display','none')
            $('#workerFlowType').val('0')
        }else {
            $('#workerFlowType').val('')
            $(".isVistable").css('display','block')
        }
    })
    //导出
    $('#exportBtn').on('click', function () {

        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            $("#setCol").remove()
            //原始处理逻辑代码
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            var allColModel ="";
            if(isKa == "1"){
                addHtmlA(colNamesB);
                allColModel = allColModelB;
            }else{
                addHtmlA(colNameB); //2.27需要改colNames 为 colNameB
                allColModel = allColModelA;

            }
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            if (allColModel[index].name == "sourceDept") {
                                nameModel += allColModel[index].name + "Str:" + $(this).attr('name') + ","
                            } else if (allColModel[index].name == "docAttr") {
                                nameModel += allColModel[index].name + "Str:" + $(this).attr('name') + ","
                            } else {
                                nameModel += allColModel[index].name + ":" + $(this).attr('name') + ","
                            }
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    // var obj = $("#searchForm").serializeToJSON();
                    // obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    var obj = {
                        isKa:$("#isKa").val(),
                        keyWord: $("#keyWord").val(),
                        supplierNo: $("#supplierNo").val(),
                        isPrepay: $("#isPrepay").val(),
                        startDate: $("#startDate").val(),
                        endDate: $("#endDate").val(),
                        createUser: $("#createUser").val(),
                        status: $("#status").val(),
                        //isCollect: $("#isCollect").length > 0 ? $("#isCollect").val():"",
                        orderNo: $("#orderNo").val(),
                        //workerFlowType:$("#workerFlowType").val(),
                        supplierInvoiceNo: $("#supplierInvoiceNo").val(),
                        sourceDept: $("#sourceDept").val(),
                        docAttr: $("#docAttr").val()
                    }
                    obj["nameModel"] = nameModel;
                    var selOrder =  $('#X_Table').XGrid('getSeleRow');
                    var selectIdOrder = [];
                    if(selOrder == undefined || selOrder == null){

                    }else if(selOrder.length == undefined ){
                        selectIdOrder = selOrder.billNo;
                    }else{
                        $.each(selOrder,function(i,obj){
                            selectIdOrder.push(obj.billNo);
                        })
                    }

                    obj["selectIdOrder"] = selectIdOrder;
                    parent.showLoading({hideTime: 999999999});
                    httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportExcelReceiptList", obj);
                    parent.hideLoading();
                },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })
    });

    //导出明细
    $('#exportDetailBtn').on('click', function () {

        utils.exportAstrictHandle('Y_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            $("#setCol").remove()
            //原始处理逻辑代码
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            var allColModel ="";
            if(isKa == "1"){
                addHtmlA(colNamesY);
                allColModel = allColModelY;
            }else{
                addHtmlA(colNamesY);
                allColModel = allColModelY;

            }

            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            if (allColModel[index].name == "sourceDept") {
                                nameModel += allColModel[index].name + "Str:" + $(this).attr('name') + ","
                            } else if (allColModel[index].name == "docAttr") {
                                nameModel += allColModel[index].name + "Str:" + $(this).attr('name') + ","
                            } else {
                                nameModel += allColModel[index].name + ":" + $(this).attr('name') + ","
                            }
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    // var obj = $("#searchForm").serializeToJSON();
                    // obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    var obj = {
                        isKa:$("#isKa").val(),
                        keyWord: $("#keyWord").val(),
                        supplierNo: $("#supplierNo").val(),
                        isPrepay: $("#isPrepay").val(),
                        startDate: $("#startDate").val(),
                        endDate: $("#endDate").val(),
                        createUser: $("#createUser").val(),
                        status: $("#status").val(),
                        //isCollect: $("#isCollect").length > 0 ? $("#isCollect").val():"",
                        orderNo: $("#orderNo").val(),
                        //workerFlowType:$("#workerFlowType").val()||"",
                        supplierInvoiceNo: $("#supplierInvoiceNo").val(),
                        sourceDept: $("#sourceDept").val(),
                        docAttr: $("#docAttr").val()
                    }
                    obj["nameModel"] = nameModel;
                    var selOrder =  "";
                    var selectIdOrder = [];
                    if(selOrder == undefined || selOrder == null){

                    }else if(selOrder.length == undefined ){
                        selectIdOrder = selOrder.billNo;
                    }else{
                        $.each(selOrder,function(i,obj){
                            selectIdOrder.push(obj.billNo);
                        })
                    }

                    obj["selectIdOrder"] = selectIdOrder;
                    parent.showLoading({hideTime: 999999999});
                    httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportPayrequestDetail", obj);
                    parent.hideLoading();
                },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })
    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none; padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    function showDetail(billNo) {

        utils.openTabs('payrequestinfo','采购付款申请单详情','/proxy-finance/finance/purchase/payrequestinfo/detailPayrequestPage?billNo=' + billNo + '&status=1');

        return false;
//        utils.dialog({
//            title: '采购付款申请单详情',
//            url: '/proxy-finance/finance/purchase/payrequestinfo/detailPayrequestPage?billNo=' + billNo + '&status=' + 1,
//            width: $(window).width() * 0.8,
//            height: $(window).height() * 0.8,
//            data: 'val值', // 给modal 要传递的 的数据
//            onclose: function () {
//                if (this.returnValue) {
//                    var data = this.returnValue;
//                    console.log(data);
//                    $('searchBtn').triangle('click');
//                }
//                $('iframe').remove();
//            }
//        }).showModal();
    }

    // 查看详情
    /*   $('#X_Table').on('click', 'td[row-describedby="billNo"]', function () {
           var billNo = $(this).text();

           return false;
       })*/

    // 编辑信息
    $('#editBtn').bind('click', function () {
        var seleRow = $('#X_Table').XGrid('getSeleRow');

        console.log("我的标识:"+isKa)
        if (seleRow.length>0 && seleRow.length==1) {
            if (seleRow[0].status !== '已保存') {
                utils.dialog({
                    content: '该单已提交审核不可编辑！',
                    quickClose: true,
                    timeout: 2000
                }).show();
                return false;
            }
            utils.dialog({
                title: '编辑采购付款申请单',
                url: '/proxy-finance/finance/purchase/payrequestinfo/editPayrequestPage?billNo=' + seleRow[0].billNo+'&isKa='+isKa,
                width: $(window).width() * 0.95,
                height: $(window).height() * 0.8,
                data: 'val值', // 给modal 要传递的 的数据
                onclose: function () {
                    if (this.returnValue) {
                        var data = this.returnValue;
                        console.log(data)
                    }
                    $('iframe').remove();
                    $('#searchBtn').trigger('click');
                },
                oniframeload: function () {
                    // console.log('iframe ready')
                }
            }).showModal();
            return false;
        }else if(seleRow.length>1){
            utils.dialog({
                content: '仅可选择一行进行编辑操作！',
                quickClose: true,
                timeout: 2000
            }).show();
            return false;
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).show();
        }

    })

    function totalSum() {
        $.ajax({
            url:'/proxy-finance/finance/purchase/payrequestinfo/totalSumPurchasePayrequestInfos',
            type:'post',
            data:{
                isKa:$("#isKa").val(),
                keyWord: $("#keyWord").val(),
                supplierNo: $("#supplierNo").val(),
                isPrepay: $("#isPrepay").val(),
                startDate: $("#startDate").val(),
                endDate: $("#endDate").val(),
                createUser: $("#createUser").val(),
                //isCollect: $("#isCollect").length > 0 ? $("#isCollect").val():"",
                //workerFlowType:$("#workerFlowType").val(),
                status: $("#status").val(),
                orderNo: $("#orderNo").val(),
                supplierInvoiceNo: $("#supplierInvoiceNo").val(),
                sourceDept: $("#sourceDept").val(),
                docAttr: $("#docAttr").val()
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;
                    $("#totalCount").text(data.totalCount);
                    if(data.sumApplayAmount){
                        //$("#sumApplayAmount").text(parseFloat(data.sumApplayAmount).formatMoney('2', '', ',' ,'.'));
                        $("#sumApplayAmount").text(data.sumApplayAmount.toFixed(2));
                        // $("#sumApplayAmount").text(data.sumApplayAmount);
                    }else{
                        $("#sumApplayAmount").text(0);
                    }
					if (data.sumPaymentAmount) {
                        $("#sumPaymentAmount").text(data.sumPaymentAmount.toFixed(2));
                    } else {
                        $("#sumPaymentAmount").text(0);
                    }
                }
            }
        })
    }

    function totalDetailSum() {
        $.ajax({
            url:'/proxy-finance/finance/purchase/payrequestinfo/totalSumPayrequestDetail',
            type:'post',
            data:{
                isKa:$("#isKa").val(),
                keyWord: $("#keyWord").val(),
                supplierNo: $("#supplierNo").val(),
                isPrepay: $("#isPrepay").val(),
                startDate: $("#startDate").val(),
                endDate: $("#endDate").val(),
                createUser: $("#createUser").val(),
                //isCollect: $("#isCollect").length > 0 ? $("#isCollect").val():"",
                //workerFlowType:$("#workerFlowType").val(),
                status: $("#status").val(),
                orderNo: $("#orderNo").val(),
                tabType:_index,
                supplierInvoiceNo: $("#supplierInvoiceNo").val(),
                sourceDept: $("#sourceDept").val(),
                docAttr: $("#docAttr").val()
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;
                    $("#totalCount").text(data.totalCount);
                    if(data.sumApplayAmount){
                        //$("#sumApplayAmount").text(parseFloat(data.sumApplayAmount).formatMoney('2', '', ',' ,'.'));
                        $("#sumApplayAmount").text(data.sumApplayAmount.toFixed(2));
                        // $("#sumApplayAmount").text(data.sumApplayAmount);
                    }else{
                        $("#sumApplayAmount").text(0);
                    }
                    if (data.sumPaymentAmount) {
                        $("#sumPaymentAmount").text(data.sumPaymentAmount.toFixed(2));
                    } else {
                        $("#sumPaymentAmount").text(0);
                    }
                }
            }
        })
    }

    // 打印付款申请单
    $("#printBtn").on("click",function () {
        utils.dialog({
            content:"正在打印...",
            timeout:1000
        }).showModal();
        seleRow(function (ary) {
            console.log(ary);
            $("#print_box")[0].contentWindow.getData(1,ary);
        })
    });

    //var colNamesY = ['供应商编号', '供应商名称', '付款申请单号', '申请人', '款项类型', '支付方式','申请日期', 'ERP发票号/采购订单号','供应商发票号', '申请金额', '汇总扣减金额', '实际付款金额', '最早可付款日期', '不含税金额', '税额','价税合计', '状态','是否集采','是否OEM'],
    var colNamesY = ['供应商编号', '供应商名称', '付款申请单号', '申请人', '款项类型', '支付方式','申请日期', 'ERP发票号/采购订单号','供应商发票号', '申请金额', '汇总扣减金额', '实际付款金额', '最早可付款日期', '不含税金额', '税额','价税合计', '状态','发起部门','单据属性'],
        colModelY = [
            {
                name: 'supplierNo'
            }, {
                name: 'supplierName'
            }, {
                name: 'payrequestNo'
            }, {
                name: 'createUser'
            }, {
                name: 'isPrepayStr'
            }, {
                name: 'payableTypeStr',
                editable: true
            }, {
                name: 'billTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'billNo',
                width: 300
            }, {
                name: 'supplierInvoiceNo'
            },{
                name: 'thisTimeApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'deductionAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
            },{
                name: 'actualPayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
            },{
                name: 'earliestPayTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            },{
                name: 'amount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'taxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'taxLimitAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'status',
                editable: true,
                formatter: function (val) {
                    if (val == "1") {
                        return "已保存"
                    } else if (val == "2") {
                        return "待审核"
                    } else if (val == "3") {
                        return "审核中";
                    } else if (val == "4") {
                        return "已驳回";
                    } else if (val == "5") {
                        return "待付款"
                    } else if (val == "6") {
                        return "已付款";
                    } else if (val == "7") {
                        return "暂停付款";
                    } else if (val == "8") {
                        return "部分付款";
                    }
                }
            },
            {
                name: 'sourceDept',
                formatter: function (val) {
                    if (val == "1") {
                        return "省区"
                    } else if (val == "2") {
                        return "集采中心"
                    } else if (val == "3") {
                        return "OEM"
                    } else   {
                        return "";
                    }
                }
            },
            {
                name: 'docAttr',
                formatter: function (val) {
                    if (val == "1") {
                        return "地采"
                    } else if (val == "2") {
                        return "集采"
                    } else if (val == "3") {
                        return "集采-统谈统采"
                    } else if (val == "4") {
                        return "集采-统谈分采"
                    } else if (val == "5") {
                        return "OEM"
                    } else {
                        return "";
                    }
                }
            },
            // {
            //     name: 'isCollect',
            //     formatter: function (val) {
            //         if (val == "1") {
            //             return "是"
            //         } else if (val == "0") {
            //             return "否"
            //         } else   {
            //             return "";
            //         }
            //     }
            // },
            // {
            //     name: 'workerFlowType',
            //     formatter: function (val) {
            //         if (val == "1") {
            //             return "是"
            //         } else if (val == "0") {
            //             return "否"
            //         } else   {
            //             return "";
            //         }
            //     }
            // },
            {
                name: 'id',
                hidden: 'true',
                hidegrid: true //是否在筛选列时展示此列
            }

        ];
    var allColModelY = JSON.parse(JSON.stringify(colModelY));

    //查询是否灰度环境
    queryIsGrayOfOrgForPaymentRequest().then((isGray)=>{
        if(!isGray){ //非灰度中
            let { colNamesNew,colModelNew } = isGrayToHideColumn({
                colNames:colNamesY,
                colModel:colModelY,
                colHidden:['deductionAmount','actualPayAmount','earliestPayTime']// 汇总扣减金额、实际付款金额、最早可付款日期
            });
            colNamesY = colNamesNew
            colModelY = colModelNew
            allColModelY = JSON.parse(JSON.stringify(colModelY))
        }
        $('#Y_Table').XGrid({
            url: '/proxy-finance/finance/purchase/payrequestinfo/findPayrequestDetail',
            colNames: colNamesY,
            colModel: colModelY,
            rowNum: 20,
            rowList: [20, 50, 100],//分页条数下拉选择
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true,
            attachRow:true,
            multiselect: true,
            postData: {
                isKa:$("#isKa").val(),
                keyWord: $("#keyWord").val(),
                isPrepay: $("#isPrepay").val(),
                startDate: $("#startDate").val(),
                endDate: $("#endDate").val(),
                createUser: $("#createUser").val(),
                //isCollect: $("#isCollect").length > 0 ? $("#isCollect").val():"",
                //workerFlowType:$("#workerFlowType").val(),
                orderNo: $("#orderNo").val(),
                tabType:1,
                supplierInvoiceNo: $("#supplierInvoiceNo").val(),
                status: $("#status").val(),
                sourceDept: $("#sourceDept").val(),
                docAttr: $("#docAttr").val()
            },
            pager: '#ygrid-pager',
            gridComplete: function () {
                var _this = $(this);
                setTimeout(function () {
                    if (_this.XGrid('getRowData').length === 0) {
                        utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
                    }
                }, 200);
                /* 合计行 */
                var data = $(this).XGrid('getRowData');
                var sum_models = ['thisTimeApplayAmount'];
                var lastRowEle = $(this).find("tr[nosele=true]");
                lastRowEle.find("td:first-child").text('合计');
                sum_models.forEach(function (item,index) {
                    lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                });
            },
        });
    })

    //var colNamesZ = ['供应商编号', '供应商名称', '付款申请单号', '申请人', '款项类型', '支付方式','申请日期', 'ERP发票号/采购订单号','供应商发票号', '申请金额', '不含税金额', '税额','价税合计', '状态','是否集采','是否OEM'],
    var colNamesZ = ['供应商编号', '供应商名称', '付款申请单号', '申请人', '款项类型', '支付方式','申请日期', 'ERP发票号/采购订单号','供应商发票号', '申请金额', '不含税金额', '税额','价税合计', '状态','发起部门','单据属性'],
        colModelZ = [
            {
                name: 'supplierNo'
            }, {
                name: 'supplierName'
            }, {
                name: 'payrequestNo'
            }, {
                name: 'createUser'
            }, {
                name: 'isPrepayStr'
            }, {
                name: 'payableTypeStr',
                editable: true
            }, {
                name: 'billTime',
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'billNo',
                width: 300
            }, {
                name: 'supplierInvoiceNo'
            },{
                name: 'thisTimeApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'amount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'taxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },{
                name: 'taxLimitAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'status',
                editable: true,
                formatter: function (val) {
                    if (val == "1") {
                        return "已保存"
                    } else if (val == "2") {
                        return "待审核"
                    } else if (val == "3") {
                        return "审核中";
                    } else if (val == "4") {
                        return "已驳回";
                    } else if (val == "5") {
                        return "待付款"
                    } else if (val == "6") {
                        return "已付款";
                    } else if (val == "7") {
                        return "暂停付款";
                    } else if (val == "8") {
                        return "部分付款";
                    }
                }
            },
            // {
            //     name: 'isCollect',
            //     formatter: function (val) {
            //         if (val == "1") {
            //             return "是"
            //         } else if (val == "0") {
            //             return "否"
            //         } else   {
            //             return "";
            //         }
            //     }
            // },{
            //     name: 'workerFlowType',
            //     formatter: function (val) {
            //         if (val == "1") {
            //             return "是"
            //         } else if (val == "0") {
            //             return "否"
            //         } else   {
            //             return "";
            //         }
            //     }
            // },
            {
                name: 'sourceDept',
                formatter: function (val) {
                    if (val == "1") {
                        return "省区"
                    } else if (val == "2") {
                        return "集采中心"
                    } else if (val == "3") {
                        return "OEM"
                    } else   {
                        return "";
                    }
                }
            },
            {
                name: 'docAttr',
                formatter: function (val) {
                    if (val == "1") {
                        return "地采"
                    } else if (val == "2") {
                        return "集采"
                    } else if (val == "3") {
                        return "集采-统谈统采"
                    } else if (val == "4") {
                        return "集采-统谈分采"
                    } else if (val == "5") {
                        return "OEM"
                    } else {
                        return "";
                    }
                }
            },
            {
                name: 'id',
                hidden: 'true',
                hidegrid: true //是否在筛选列时展示此列
            }

        ];


    $('#Z_Table').XGrid({
        url: '/proxy-finance/finance/purchase/payrequestinfo/findPayrequestDetail',
        colNames: colNamesZ,
        colModel: colModelZ,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        attachRow:true,
        multiselect: true,
        postData: {
            isKa:$("#isKa").val(),
            keyWord: $("#keyWord").val(),
            isPrepay: $("#isPrepay").val(),
            startDate: $("#startDate").val(),
            endDate: $("#endDate").val(),
            createUser: $("#createUser").val(),
            //isCollect: $("#isCollect").length > 0 ? $("#isCollect").val():"",
            //workerFlowType:$("#workerFlowType").val(),
            orderNo: $("#orderNo").val(),
            tabType:2,
            supplierInvoiceNo: $("#supplierInvoiceNo").val(),
            status: $("#status").val(),
            sourceDept: $("#sourceDept").val(),
            docAttr: $("#docAttr").val()
        },
        pager: '#zgrid-pager',
        gridComplete: function () {
            var _this = $(this);
            setTimeout(function () {
                if (_this.XGrid('getRowData').length === 0) {
                    utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
                }
            }, 200);
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['thisTimeApplayAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
        },
    });


    //查询是否灰度环境
    function queryIsGrayOfOrgForPaymentRequest(){
        return new Promise((resolve)=>{
            $.ajax({
                url: "/proxy-finance/finance/purchase/invoice/isGrayOfOrgForPaymentRequest",
                type: "get",
                dataType: 'json',
                success: function (result) {
                    if(result.code === 0){
                        resolve(true)//灰度中
                    }else{
                        resolve(false)//非灰度中
                    }
                },
                error: function(err){
                    resolve(false)//非灰度中
                }
            })
        })
    }
    //重新设置colModel展示项
    function isGrayToHideColumn({ colNames, colModel, colHidden }){
        let colNamesNew = []
        let colModelNew = []
        colModel.forEach((item,index)=>{
            if(!colHidden.includes(item.name)){
                if(colNames[index]){
                    colNamesNew.push(colNames[index])
                }
                colModelNew.push(item)
            }
        });
        return {
            colNamesNew,
            colModelNew
        }
    }
})

/* 获取付款申请单选中项,并获取单据数据(组装) */
function seleRow(callback) {
    var ary = []; // 付款申请单号	array
    var sele_data = $("#X_Table").XGrid("getSeleRow");
    if(sele_data && sele_data.length>0){
        if(!$.isArray(sele_data)){
            ary.push(sele_data.billNo)
        }else {
            sele_data.forEach(function (item,index) {
                ary.push(item.billNo);
            })
        }
    }else {
        utils.dialog({
            title:'预览',
            content:"请选择要打印的单据",
            timeout:2000
        }).show();
        return
    }
    callback(ary);
}

function formatDate(val) {
    /* var date = new Date(val);

     var year = date.getFullYear(),
         month = date.getMonth() + 1,//月份是从0开始的
         day = date.getDate(),
         hour = date.getHours(),
         min = date.getMinutes(),
         sec = date.getSeconds();
     var newTime = year + '-' +
         month + '-' +
         day + ' ' +
         hour + ':' +
         min + ':' +
         sec;*/
    return val;
}
// 发起部门切换
$("#sourceDept").on('change', function () {
    console.log('sourceDept change1')
    let num = $("#sourceDept").val()
    if (num == 1) { // 当发起部门为省区，则订单属性可选地采、集采-统谈分采、集采-统谈统采；默认值是地采；
        $(".isSecond").css('display','inherit')
        $(".isThird").css('display','inherit')
        $(".isFirst").css('display','none')
        $('#docAttr').val('1')
    }  else if (num == 2) { // 当发起部门为集采中心，则订单属性是集采-统谈统采；
        $(".isFirst").css('display','inherit')
        $(".isThird").css('display','inherit')
        $(".isSecond").css('display','none')
        $('#docAttr').val('3')
    } else if (num == 3) { // 当发起部门为OEM，则订单属性为OEM；
        $(".isFirst").css('display','inherit')
        $(".isSecond").css('display','inherit')
        $(".isThird").css('display','none')
        $('#docAttr').val('5')
    } else { // 全部都情况
        $(".isFirst").css('display','inherit')
        $(".isSecond").css('display','inherit')
        $(".isThird").css('display','inherit')
        $('#docAttr').val('')
    }
})

