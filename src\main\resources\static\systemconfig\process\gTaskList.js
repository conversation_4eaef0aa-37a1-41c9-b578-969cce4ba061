var req_url_pre = "/proxy-sysmanage/sysmanage/process";
$(function() {
    // 列表展示
    $('#X_Tableb').XGrid({
        url: req_url_pre + "/listPage",
        mtype: "POST",
        postData: {
            id: $("#claim_hidden_todoId").val(),
            auditStartTime: $("#auditStartTime").val(),
            auditEndTime: $("#auditEndTime").val(),
            taskCreater: $("#taskCreater").val(),
            taskOrgCode: $("#taskOrgCode").val(),
            applyModule: $("#applyModule").val(),
            approvalProcessKey: $("#approvalProcessKey").val(),
            taskType: $("#taskType").val(),
            number: $("#number").val(),
            uniteQuery: $("#uniteQuery").val(),
            indexRemindTask: $("#index_remind_task").val()
        },
        colNames: ['申请日期', '机构', '申请人', '单据编号', '审批流程', '任务类型',
            '名称', '编码', '审批等待时长', '申请金额', '操作'
        ],
        colModel: [{
            name: 'taskCreateTime',
            width: '100',
            formatter: dateFormatter
        }, {
            name: 'taskOrgName',
            width: '250'
        }, {
            name: 'taskCreater',
            width: '100'
        }, {
            name: 'number',
            width: '155'
        }, {
            name: 'approvalProcessKey',
            width: '150',
            formatter: approvalProcessKeyFormatter
        }, {
            name: 'taskType',
            width: '130',
            formatter: function(e, d, rData) {
                return taskTypeFormatter(e, d, rData);
            }
        }, {
            name: 'name',
            width: '240'
        }, {
            name: 'code',
            width: '240'
        }, {
            name: 'intervalSecond',
            width: '240',
            formatter: function(e, d, rData) {
                return secondToDate(e);
            }
        }, {
            name: 'applyMoney',
            width: '240',
            formatter: function(e, d, rData) {
                var applyKeyArr = ['purchaseOrderAdvance', 'purchaseOrderUnadvance', 'majorCustomerPurchaseOrderAdvance',
                    'majorCustomerPurchaseOrderUnadvance', 'focusPurchaseOrderAdvance', 'focusPurchaseOrderUnadvance', 'oemPurchasePayRequest',
                    'keyCustomerPurchaseOrderAdvance', 'keyCustomerPurchaseOrderUnadvance', 'provincePrePaymentDisperseFlow',
                    'provincePrePaymentCenterFlow', 'provincePaymentDisperseFlow', 'provincePaymentCenterFlow', 'centerPrePaymentFlow',
                    'centerPaymentFlow', "productIntroduce", "storageProductLossApply", "storageProductSpillApply", "advance_pay_request", 
                    "provincePrePaymentDisperseFlowExcess", "provincePrePaymentCenterFlowExcess", "centerPrePaymentFlowExcess",
                    "salesReturnOrder","orderSalesOrder","provincePaymentRejectFlow"
                ];
                var applyKey = rData.approvalProcessKey;
                if (applyKeyArr.indexOf(applyKey) > -1) {
                    return e.toFixed(2);
                } else {
                    return "-";
                }
            }
        }, {
            name: 'operation',
            width: '130',
            rowtype: '#operation_complete',
            formatter: function(e, d, rData) {
                if (['1', '3', '4'].includes(String(rData.taskType))) {
                    return {
                        'rowtype': '#operation_look_copy'
                    }
                } else {
                    if (rData.taskCreaterId == rData.userId) {
                        return {
                            'rowtype': '#operation_edit'
                        }
                    } else {
                        if ("true" == String(rData.groupTask)) {
                            return {
                                'rowtype': '#operation_claim'
                            };
                        } else if ("false" == String(rData.groupTask)) {
                            return {
                                'rowtype': '#operation_complete'
                            }
                        }
                    }
                }
            }
        }, {
            name: 'approvalProcessKey_hidden',
            hidden: true,
            formatter: function(e, d, rData) {
                return rData.approvalProcessKey;
            }
        }, {
            name: 'businessId',
            hidden: true
        }, {
            name: 'taskId',
            hidden: true
        }, {
            name: 'approvalProcessId',
            hidden: true
        }, {
            name: 'edit',
            hidden: true
        }, {
            name: 'close',
            hidden: true
        }, {
            name: 'agreed',
            hidden: true
        }, {
            name: 'reject',
            hidden: true
        }, {
            name: 'report',
            hidden: true
        }, {
            name: 'look',
            hidden: true
        },{
            name: 'groupTask',
            hidden: true
        },  {
            name: 'taskTypeOri',
            hidden: true,
            formatter: function(e, d, rData) {
                return rData.taskType;
            }
        }],
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,
        rownumbers: true,
        pager: '#grid-pager',
        ondblClickRow: function(id, dom, obj, index, event) {
            // 双击事件回调函数
            console.log(id, dom, obj, index, event)
        },
        onSelectRow: function(id, dom, obj, index, event) {
            // 选中事件
            // 回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
        },
        gridComplete: function() {
            //抄送未读行标记
            var lookList = $(this).XGrid("getRowData");
            lookList.forEach(function(item, key) {
                console.log('yyyyyyyyy',item.taskTypeOri,item.id);
                if ("false" == item.look && ['1', '4'].includes(item.taskTypeOri)) {
                    $("tr#" + item.id).addClass("look");
                }
            })
        }

    });
})

// 任务转移
$("#taskChangeBtn").click(function() {

    utils.dialog({
        url: req_url_pre + '/toTaskChangePage',
        title: '任务转移',
        // width: $(window).width()*0.8,
        width: $(window).width() * 0.8,
        height: 500,
        onclose: function() {
            //刷新我的任务列表
            $('#X_Tableb').XGrid('setGridParam', {
                url: req_url_pre + "/listPage",
                mtype: "POST",
                postData: {
                    id: "",
                    auditStartTime: $("#auditStartTime").val(),
                    auditEndTime: $("#auditEndTime").val(),
                    taskCreater: $("#taskCreater").val(),
                    taskOrgCode: $("#taskOrgCode").val(),
                    applyModule: $("#applyModule").val(),
                    approvalProcessKey: $("#approvalProcessKey").val(),
                    taskType: $("#taskType").val(),
                    number: $("#number").val(),
                    uniteQuery: $("#uniteQuery").val(),
                    indexRemindTask: ""
                }
            }).trigger('reloadGrid');
        }

    }).showModal();
});

// 导出Excel
$("#exportBtn").click(function() {
    var auditStartTime = $("#auditStartTime").val();
    var auditEndTime = $("#auditEndTime").val();
    var taskCreater = $("#taskCreater").val();
    var taskOrgCode = $("#taskOrgCode").val();
    var applyModule = $("#applyModule").val();
    var approvalProcessKey = $("#approvalProcessKey").val();
    var taskType = $("#taskType").val();
    var number = $("#number").val();
    var uniteQuery = $("#uniteQuery").val();
    if (objEmpty(auditStartTime)) {
        showTips("申请起始日期必选");
        return false;
    }
    if (objEmpty(auditEndTime)) {
        showTips("申请结束日期必选");
        return false;
    }
    if (getTimeDiff(auditStartTime, auditEndTime, 31)) {
        showTips("日期最长间隔31天");
        return false;
    }
    if (objEmpty(applyModule)) {
        showTips("模块必选");
        return false;
    }
    if (objEmpty(approvalProcessKey)) {
        showTips("审批流程必选");
        return false;
    }
    if (objEmpty(taskType)) {
        showTips("任务类型必选");
        return false;
    }
    window.location.href = req_url_pre + "/exportTaskReport?auditStartTime=" + auditStartTime + "&auditEndTime=" +
        auditEndTime + "&taskCreater=" + taskCreater + "&taskOrgCode=" + taskOrgCode + "&applyModule=" + applyModule +
        "&approvalProcessKey=" + approvalProcessKey + "&taskType=" + taskType + "&number=" + number + "&uniteQuery=" +
        uniteQuery;
});

//弹框提示
function showTips(contentText) {
    utils.dialog({
        content: contentText,
        quickClose: true,
        timeout: 2000
    }).showModal();
}

/** 时间差计算 */
function getTimeDiff(startTime, endTime, num) {
    let dValue, days;
    startTime = Date.parse(startTime);
    endTime = Date.parse(endTime);
    dValue = endTime - startTime;
    dValue = Math.abs(dValue);
    days = Math.floor(dValue / (24 * 3600 * 1000));
    return (days + 1) > num;
}


/** 判空 */
function objEmpty(obj) {
    if (obj == null || obj == undefined || obj == "") {
        return true;
    } else {
        return false;
    }
}

// 模块和审批流程联动
$('body').on('change', '#applyModule', function() {
    var apply_module = $("#applyModule").val();
    var approval_process_key_html = '<option selected value="">审批流程</option>';
    switch (apply_module) {
        case "masterData":
            // 主数据
            approval_process_key_html +=
                '<option value="product_first">商品首营申请</option><option value="productUpdate">商品资料变更申请</option>' +
                '<option value="product_prop_update">商品运营属性变更申请</option><option value="productPriceUpdate_t">商品调价申请</option>' +
                '<option value="productBidPrice">商品标价申请</option><option value="productLimit">商品停用申请</option>' +
                '<option value="productReview">商品评审申请</option>' +
                '<option value="productFactoryUpdate">商品包装管理</option><option value="productUpdates">商品上架申请</option>' +
                '<option value="product_off_t">商品下架申请</option><option value="productTaxRelatedChangeApply">商品涉税变更申请</option>' +
                '<option value="productQualityAttrChange">商品质管属性变更申请</option><option value="product_manage_property_t">商品管理属性变更申请</option>' +
                '<option value="productCuringPropertyApply">商品养护属性变更申请</option><option value="productSuggTermPriceAdjm_t">商品建议终端售价调整申请</option>' +
                '<option value="focusProductUpdates">集采商品上架申请</option><option value="focusProductOffLoading">集采商品下架申请</option>' +
                '<option value="supplier_first">供应商首营申请</option><option value="supplierChange">供应商资料变更申请</option>' +
                '<option value="supplierUpdate">供应商运营属性变更申请</option><option value="supplier_stop">供应商停用申请</option>' +
                '<option value="supplierLockingErpApply">供应商锁定申请</option><option value="supplierQualificationChange">供应商资质变更申请</option>' +
                '<option value="customerFirst_apply">客户首营申请</option><option value="customerInfoModifyApply_t">客户资料变更申请</option>' +
                '<option value="customerStopUseQCApply">客户停用申请</option><option value="customerLockingErpApply">客户锁定申请</option>' +
                '<option value="platformCustomerFirst_t">平台客户首营申请</option><option value="platformCustomerInfoModify_t">平台客户变更申请</option>' +
                '<option value="platformCustomerStopErpApply">平台客户停用申请</option>' +
                '<option value="productIntroduce">新品引入申请</option>';
            break;
        case "orderInventory":
            // 订单库存
            approval_process_key_html +=
                '<option value="storageOwnerTransferApply">业务类型转移申请</option><option value="orderSalesOrder">神农手工单</option>' +
                '<option value="orderSalesReturn">销售退回申请</option><option value="orderCommodityProfitAndLoss">商品损溢申请</option>' +
                '<option value="orderDisDrugsToDestroy">不合格药品销毁申请</option><option value="orderDamaged">不合格报损申请</option>' +
                '<option value="unqualifiedDestructionWmsApply">不合格品销毁申请-普罗格</option><option value="unqualifiedReportLossWmsApply">不合格品报损申请-普罗格</option>' +
                '<option value="orderAccountRegulation">调账单申请</option><option value="storageProductSpillApply">库存商品报溢申请</option>' +
                '<option value="storageProductLossApply">损益申请单</option>';
            break;
        case "procurement":
            // 采购
            approval_process_key_html +=
                '<option value="purchaseOrder">     </option><option value="purchaseReturn_t">采购退货申请</option>' +
                '<option value="purchasePlanOrder">华润采购直联订单申请</option><option value="purchasewayOrder">采购订单关闭申请</option>' +
                '<option value="purchaseRetrieve">采购退补价申请</option><option value="dictPurchaseSortManagement">商品采购分类管理申请</option>' +
                '<option value="purchaseSalesTransferOrder">销售调拨单申请</option>';
            break;
        case "finance":
            // 财务
            approval_process_key_html +=
                '<option value="purchaseOrder_pay_apply">采购付款申请</option>' +
                '<option value="pbillFiDepositApply">返利预存申请单</option>' +
                '<option value="pbillFiDepositKeepAccountApply">返利预存下账申请单</option>' +
                '<option value="pbillFiRebateKeepAccountApply">返利下账申请单</option>' +
                '<option value="pbillFiRebateKeepAccountShareApply">商品实收分摊申请</option>' +
                '<option value="pbillFiDepositBackApply">返利预存反冲申请单</option>' +
                '<option value="pbillFiDepositKeepAccountBackApply">返利预存下账反冲申请单</option>' +
                '<option value="pbillFiRebateKeepAccountBackApply">返利下账反冲申请单</option>' +
                '<option value="advance_pay_request">提前付款申请</option>';
            break;
        case "gsp":
            // GSP
            approval_process_key_html +=
                '<option value="carrierApply">新增承运单位申请</option>';
            break;
        default:
            break;
    }
    $("#approvalProcessKey").html(approval_process_key_html);


});

// 查询按钮事件
$("#queryBtn").click(function() {
    $('#X_Tableb').XGrid('setGridParam', {
        url: req_url_pre + "/listPage",
        mtype: "POST",
        postData: {
            id: "",
            auditStartTime: $("#auditStartTime").val(),
            auditEndTime: $("#auditEndTime").val(),
            taskCreater: $("#taskCreater").val(),
            taskOrgCode: $("#taskOrgCode").val(),
            applyModule: $("#applyModule").val(),
            approvalProcessKey: $("#approvalProcessKey").val(),
            taskType: $("#taskType").val(),
            number: $("#number").val(),
            uniteQuery: $("#uniteQuery").val(),
            indexRemindTask: ""
        }
    }).trigger('reloadGrid');
});

//列表按钮事件
$('body').on('click', '#X_Tableb .btn_operation', function(e) {
    var $tr = $(this).closest('tr');
    var data_url = $(this).data('url-type');
    if ($tr.length && $tr.prop('id')) {
        var rData = $('#X_Tableb').XGrid('getRowData', $tr.prop('id'));
        var approvalProcessKey_hidden = rData.approvalProcessKey_hidden;
        var view_id = rData.id;
        var tab_name = getProcessName(approvalProcessKey_hidden, rData);
        var link_url = getProcessLookUrl(approvalProcessKey_hidden, rData);

        if (String(data_url) == "look" || String(data_url) == "look_copy") {
            var jump_url = req_url_pre + "/quertTaskByTaskId?taskId=" + rData.taskId;
            if (String(data_url) == "look_copy") {
                jump_url += "&type=lookCopy";
            }
            // 查看
            $.ajax({
                url: jump_url,
                type: "GET",
                success: function(result) {
                    if (result.result) {
                        // 跳转页面
                        getProcessUrlSpecial(approvalProcessKey_hidden, rData, data_url).then(res => {
                            if (res.status) {
                                utils.openTabs("look_" + view_id, tab_name, res.url);
                            } else {
                                utils.openTabs("look_" + view_id, tab_name, link_url);
                            }
                            if (String(data_url) == "look_copy") {
                                // 保存已读
                                saveLook(rData);
                            }
                        })
                    } else {
                        utils.dialog({
                            content: result.msg,
                            quickClose: true,
                            timeout: 2000
                        }).showModal();
                        setTimeout(function() {
                            $('#X_Tableb').trigger('reloadGrid');
                        }, 2000);
                    }
                }
            });

        } else if (String(data_url) == "apply") {
            // 审核
            $.ajax({
                url: req_url_pre + "/quertTaskByTaskId?taskId=" + rData.taskId,
                type: "GET",
                success: function(result) {
                    if (result.result) {
                        // 跳转页面
                        getProcessUrlSpecial(approvalProcessKey_hidden, rData).then(res => {
                            if (res.status) {
                                utils.openTabs("apply_" + view_id, tab_name, res.url);
                            } else {
                                link_url = getProcessUrl(approvalProcessKey_hidden, rData);
                                utils.openTabs("apply_" + view_id, tab_name, link_url);
                            }
                        })
                    } else {
                        utils.dialog({
                            content: result.msg,
                            quickClose: true,
                            timeout: 2000
                        }).showModal();
                        setTimeout(function() {
                            $('#X_Tableb').trigger('reloadGrid');
                        }, 2000);
                    }
                }
            });

        } else if (String(data_url) == "claim") {
            // 领取
            if (this.claiming) {
                utils.dialog({
                    content: '正在领取中，请稍后',
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                return false;
            };
            this.claiming = true;
            $.ajax({
                url: req_url_pre + "/claim?taskId=" + rData.taskId,
                type: "GET",
                success: function(result) {
                    this.claiming = false;
                    if (result.status) {
                        // 刷新当前列表
                        $('#X_Tableb').trigger('reloadGrid');
                        $.ajax({
                            url: req_url_pre + "/quertTaskByTaskId?taskId=" + rData.taskId,
                            type: "GET",
                            success: function(result) {
                                if (result.result) {
                                    // 跳转页面
                                    getProcessUrlSpecial(approvalProcessKey_hidden, rData).then(res => {
                                        if (res.status) {
                                            utils.openTabs("apply_" + view_id, tab_name, res.url);
                                        } else {
                                            link_url = getProcessUrl(approvalProcessKey_hidden, rData);
                                            utils.openTabs("apply_" + view_id, tab_name, link_url);
                                        }
                                    })
                                } else {
                                    utils.dialog({
                                        content: result.msg,
                                        quickClose: true,
                                        timeout: 2000
                                    }).showModal();
                                    setTimeout(function() {
                                        $('#X_Tableb').trigger('reloadGrid');
                                    }, 2000);
                                }
                            }
                        });
                    } else {
                        utils.dialog({
                            content: result.mess,
                            quickClose: true,
                            timeout: 2000
                        }).showModal();
                    }
                },
                error: function() {
                    this.claiming = false;
                }
            });
        }
    }
    e.stopPropagation();
})

// 时间格式化
function dateFormatter(val) {
    if (val != null && val != "") {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
    } else {
        return "";
    }
};

// 任务类型格式化
function taskTypeFormatter(e, d, rData) {
    var val = rData.taskType;
    if (String(val) != null && String(val) != "") {
        if ("1" == val) {
            return "通知抄送";
        } else if ("3" == val) {
            return "已审核";
        } else if ("4" == val) {
            return "已驳回"
        } else {
            if (rData.taskCreaterId == rData.userId) {
                return "已驳回-待处理";
            } else {
                return "待审核";
            }
        }
    } else {
        return "";
    }
};

// 流程KEY值格式化
function approvalProcessKeyFormatter(val, a, rData) {
    return getProcessName(val, rData);
};

// 保存已读
function saveLook(obj) {
    var params = {
        id: obj.id,
        approvalProcessId: obj.approvalProcessId,
        taskType: obj.taskTypeOri
    };
    $.ajax({
        url: req_url_pre + "/saveLook",
        type: "POST",
        dataType: "json",
        contentType: "application/json;charset=UTF-8",
        data: JSON.stringify(params),
        success: function(result) {
            console.log("已读返回数据:" + result);
        }
    });
};

/** 秒转时分秒汉字 */
function secondToDate(s) {
    let days = Math.floor(s / (24 * 3600)); //计算出小时数
    let leave1 = s % (24 * 3600); //计算天数后剩余的毫秒数
    let hours = Math.floor(leave1 / (3600)); //计算相差分钟数
    let leave2 = leave1 % (3600); //计算小时数后剩余的毫秒数
    let minutes = Math.floor(leave2 / (60)); //计算相差秒数
    let leave3 = leave2 % (60); //计算分钟数后剩余的毫秒数
    let seconds = Math.round(leave3);
    return `<span ${(days * 24 + hours) > 24 ? 'style="color: red;"' : ''}>${days * 24 + hours}小时${minutes}分${seconds}秒</span>`;
};

//一键清除未读
$("#clearBtn").click(function() {
    const list = $('#X_Tableb').XGrid("getRowData");
    if (list instanceof Array) {
        if (list.filter(item => item.look === "false" && ['1', '4'].includes(item.taskTypeOri)).length > 0) {

            const dial = utils.dialog({
                title: '提示',
                content: '当前页面中的未读消息将变为已读，是否确认操作？',
                okValue: '确定',
                cancelValue: '取消',
                ok: function() {
                    $.ajax({
                        url: req_url_pre + '/batch-look',
                        type: 'POST',
                        data: JSON.stringify(list.map(item => {
                            return {
                                id: item.id,
                                taskType: item.taskTypeOri
                            }
                        })),
                        contentType: 'application/json;charset=UTF-8',
                        success: function(res) {
                            if (res.code == 0) {
                                const d = utils.dialog({
                                    content: res.msg
                                }).show();
                                $("#queryBtn").trigger("click");
                                const timer = setTimeout(() => {
                                    d.close().remove();
                                    clearTimeout(timer);
                                }, 2000)
                            }
                        }
                    })
                    dial.close().remove()
                },
                cancel: function() {
                    dial.close().remove()
                }
            })
            dial.showModal();
        } else {
            const d = utils.dialog({
                content: '无未读单据！'
            }).show();
            const timer = setTimeout(() => {
                d.close().remove();
                clearTimeout(timer);
            }, 2000)
        }
    }
})