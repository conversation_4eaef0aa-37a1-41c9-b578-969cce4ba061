    $('#X_Tableb').XGrid({
        url:"/middle/erp2WmsMiddle/queryInfRkZfList",
        colNames: [ '单据编号','单据明细序号','商品id', '状态'],
        colModel: [
            {
                name: 'djbh',
                index: 'djbh',
                width: 100
            },  {
                name: 'djSort',
                index: 'djSort',
                width: 200
            }, {
                name: 'spid',
                index: 'spid',
                width: 200
            }, {
                name: 'wmflg',
                index: 'wmflg',
                width: 60
            }
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        multiselect: true,//是否多选
        pager: '#grid-pager',
        rownumbers: true,
    });

    $("#SearchBtn").on("click", function () {
       /* $("#djbh").val($("#djbh").val().trim());
        $("#djSort").val($("#djSort").val().trim());
        $("#spid").val($("#spid").val().trim());
        $("#wmflg").val($("#wmflg").val().trim());*/
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "djbh": $("#djbh").val().trim(),
                "spid":$("#spid").val().trim(),
                "wmflg":$("#wmflg").val().trim(),
            },page:1
        }).trigger('reloadGrid');
    });
