$(function () {
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('1').then(res => {
            console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });
})
var operatingCustomers = 0;
var urlObjectList = [];
$('#X_Tableb').XGrid({
    url: "/proxy-product/product/productOrga/query",
    colNames: ['机构', '业务类型', 'SKU编码', '商品编码', '原商品编码', '商品名', '通用名', "商品大类", '型号/规格', '生产厂家', '批准文号', '包装单位', '剂型', '是否停用', '是否限销', "是否限采", "首营快照url", "采购员"],
    colModel: [
        {
            name: 'orgCodeValue',
            index: 'orgCodeValue',
            width: 210
        }, {
            name: 'channelId',
            index: 'channelId',
            width: 150
        }, {
            name: 'skuCode',
            index: 'skuCode',
            width: 100
        }, {
            name: 'productCode',
            index: 'productCode',
            width: 100
        }, {
            name: 'oldProductCode',
            index: 'oldProductCode',
            width: 160
        }, {
            name: 'productName',
            index: 'productName',
            width: 180
        }, {
            name: 'commonName',
            index: 'commonName',
            width: 180
        }, {
            name: 'largeCategoryVal',
            index: 'largeCategoryVal',
            width: 80
        }, {
            name: 'specifications',
            index: 'specifications',
            width: 200
        }, {
            name: 'manufacturerValue',
            index: 'manufacturerValue',
            width: 220
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width: 180
        }, {
            name: 'packingUnitValue',
            index: 'packingUnitValue',
            width: 80
        }, {
            name: 'dosageFormValue',
            index: 'dosageFormValue',
            width: 80
        }, {
            name: 'disableStateValue',
            index: 'disableStateValue',
            width: 80
        }, {
            name: 'limitedPinStateVal',
            index: 'limitedPinStateVal',
            width: 80
        }, {
            name: 'limitedProductionStateVal',
            index: 'limitedProductionStateVal',
            width: 80
        }, {
            name: 'imageUrl',
            hidden: true
        }, {
            name: 'buyersNameStr',
            index: 'buyersNameStr',
            width: 80
        }
    ],
    rowNum: 20,
    rowList: [20, 50, 100], //分页条数下拉选择
    altRows: true,//设置为交替行表格,默认为false
    rownumbers: true,//是否展示序号
    multiselect: true,//是否多选
    pager: '#grid-pager',
    postData: {
        "operatingCustomers": operatingCustomers
    },
    ondblClickRow: function (id, dom, obj, index, event) {
        utils.openTabs("productOrgaDetail", "机构商品详情", "/proxy-product/product/productOrga/toDetail?productId=" + id);
    },
    onSelectRow: function (id, dom, obj, index, event) {
        console.log(id, dom, obj, index, event)
        setUrlObjectList(dom, id, obj);
    },
    rownumbers: true,
});
// 生产厂家
valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage", { paramName: "keyWord" }, "manufacturer", { data: "manufactoryId", value: "manufactoryName" });
initBuyer();
var orgCode = $("#loginOrgCode").val();
if (orgCode == '001') {
    $.ajax({
        url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode=" + orgCode,
        async: false,
        dataType: "json",
        success: function (data) {
            var html = '<option value="">请选择</option>';
            if (data.code == 0) {
                var arr = data.result;
                if (arr != null) {
                    for (var i = 0; i < arr.length; i++) {
                        html += '<option value="' + arr[i].orgCode + '">' + arr[i].orgName + '</option>';
                    }
                }
            }
            $("#orgCode").html(html);
        },
        error: function () {
        }
    });
}
$("#SearchBtn").on("click", function () {
    urlObjectList = [];
    $("#productCode").val($("#productCode").val().trim());
    $("#approvalNumber").val($("#approvalNumber").val().trim());
    $("#smallPackageBarCode").val($("#smallPackageBarCode").val().trim());
    $('#X_Tableb').XGrid('setGridParam', {
        postData: {
            "operatingCustomers": operatingCustomers,
            "productCode": $("#productCode").val(),
            "approvalNumber": $("#approvalNumber").val(),
            "smallPackageBarCode": $("#smallPackageBarCode").val(),
            "manufacturer": $("#manufacturer").val(),
            "orgCode": $("#orgCode").val(),
            "disableState": $("#disableState").val(),
            "limitedPinState": $("#limitedPinState").val(),
            "limitedProductionState": $("#limitedProductionState").val(),
            "largeCategory": $("#largeCategory").val(),
            "channelId": $("#channelId").val(),
            "buyer": $("#buyer").val()
        }, page: 1
    }).trigger('reloadGrid');
});

$("#orgCode").on('change', function () {
    $("#buyerVal").val("");
    $("#buyer").val("");
    initBuyer();
});

$("#exportBtn").on("click", function () {
    //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
    utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text()), 1).then(() => {
        return false;
    }).catch(() => {
        //原始处理逻辑代码
        var ck = true;
        // copy this parameter and the below buttons
        var html = $('#exportHtml').html();
        var d = dialog({
            title: '请选择导出字段',
            width: 820,
            height: 400,
            content: html,
            okValue: '确定',
            ok: function () {
                let that = this;
                var arr = [];
                for (var i = 0; i < $(that.node).find('.exportItem').length; i++) {
                    $(that.node).find('.exportItem').eq(i).find('dd input[type="checkbox"]').each(function (index, item) {
                        var checked = $(item).prop('checked');
                        if (checked) {
                            arr.push($.trim($(this).val()))
                        }
                    })
                }
                if (arr.length == 0) {
                    utils.dialog({ content: '请选择后导出', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                var exportAttribute = arr.join(',');
                if (exportAttribute == "") {
                    utils.dialog({ content: '请选择导出属性', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                $("#exportAttribute").val(exportAttribute);

                $("#operatingCustomers").val(operatingCustomers);
                var formData = $("#searchForm").serializeToJSON();
                $("#searchForm").attr("action", "/proxy-product/product/productOrga/exportExcel");
                $("#searchForm").submit();
            },
            cancelValue: '取消',
            cancel: function () { },
            // copy button to other dialogues
            button: [
                {
                    id: 'chooseAll',
                    value: '全选',
                    callback: function () {
                        //debugger;
                        if (!ck) {
                            $(".exportItem input").removeAttr("checked");
                            ck = true;
                        } else if (ck) {
                            $(".exportItem input").prop("checked", "checked");
                            ck = false;
                        } else {
                            return false;
                        };
                        return false;
                    }
                }
            ]
            //copy ends here
        });
        d.showModal();
        $('.ui-dialog-content').css({
            'overflow': 'hidden',
            'overflow-y': 'auto',
        });
    });
});
//业务类型属性导出
$("#exportChannelBtn").on("click", function () {
    $("#searchForm").attr("action", "/proxy-product/product/productOrga/getExcelChannelList");
    $("#searchForm").submit();
});
$(document).on("change", ".exportItem dt input", function () {
    var checked = this.checked;
    if (checked) {
        $(this).parents("dl").find("dd input[type='checkbox']").prop('checked', true);
    } else {
        $(this).parents("dl").find("dd input[type='checkbox']").prop('checked', false);
    }
})
/*$(document).on('change','#grid_checked input',function () {
    var checked=this.checked;
    urlObjectList=[];
    if(checked){
        var selRow=$('#X_Tableb').XGrid('getSeleRow');
        if(selRow && selRow.length > 0){

            for(var i=0;i<selRow.length;i++){
                if(selRow[i].imageUrl != ''){
                    var fileParam={};
                    fileParam.id = selRow[i].id;
                    fileParam.name = selRow[i].productCode;
                    fileParam.url = selRow[i].imageUrl;
                    urlObjectList.push(fileParam);
                }
            }
        }
    }else{
        urlObjectList=[];
    }
})*/
$(document).on("change", ".exportItem dd input[type='checkbox']", function () {
    var inpLen = $(this).parents("dd").find("input[type='checkbox']").length;
    var checkLen = $(this).parents("dd").find("input[type='checkbox']:checked").length;
    if (inpLen == checkLen) {
        $(this).parents("dl").find("dt input[type='checkbox']").prop('checked', true);
    } else {
        $(this).parents("dl").find("dt input[type='checkbox']").prop('checked', false);
    }
})
//首营审批快照
$('#snapshootBtn').on('click', function () {
    var len = $("#X_Tableb").XGrid('getSeleRow');
    if (!len || len.length < 1) {
        utils.dialog({
            title: '提示',
            width: 200,
            content: '请先从列表中选择一条数据',
            okValue: '确定',
            ok: function () { }
        }).show();
        return;
    }
    utils.dialog({
        align: 'top',
        width: 90,
        height: 50,
        padding: 8,
        content: '<div class="changeApplyItem formApprovalBlock"><div class="cSelect">预览</div><div class="cDown">下载</div></div>',
        quickClose: true
    }).show(this);
});

/* $("#X_Tableb").on('change','td[row-describedby="ck"] input',function(){
     var $tr=$(this).parents('tr');
     var rowData=$("#X_Tableb").getRowData($tr.attr('id'));
     var id=$tr.attr('id');
     setUrlObjectList($tr,id,rowData);
 })*/

$('body').on('click', '.cSelect', function () {
    if (urlObjectList.length < 1) {
        utils.dialog({
            title: '提示',
            width: 200,
            content: '没有可预览附件',
            okVlue: '确定',
            ok: function () { }
        }).show();
        return;
    }
    $.viewImg({
        fileParam: {
            name: 'name',
            url: 'url'
        },
        list: urlObjectList
    })
})
$('body').on('click', '.cDown', function () {
    if (urlObjectList.length < 1) {
        utils.dialog({
            title: '提示',
            width: 200,
            content: '没有可下载的附件',
            okVlue: '确定',
            ok: function () { }
        }).show();
        return;
    }
    //批量下载
    var a = [];
    a.push('<form method="post" style="display: none;">');
    urlObjectList.forEach(function (item) {
        console.log(item);
        a.push('<input name="file" value = ' + item.url + '>');
        a.push('<input name="name" value = ' + item.name + '>');
    });
    a.push('</form>');
    var $eleForm = $(a.join(''));
    $eleForm.attr("action", "/proxy-sysmanage/upload/downloadZip");
    //$eleForm.attr("action", "http://**************:8080/upload/download?filepath=G2/M00/00/01/Cgo001tPH3OAXX8_AABEWCsL8kc354.png&name=''");
    $(document.body).append($eleForm);
    //提交表单，实现下载
    $eleForm.submit();
})
/**
 * 下载模板
 */
$('#downChangeTemplete').on('click', function () {
    window.open("/proxy-product/product/productOrga/downChangeClassificationTemplate")
});

//导入
$("#importChange").on("click", function () {
    dialog({
        url: '/proxy-product/product/productOrga/toChangeClassificationFileUpload',
        title: '批量导入',
        width: 600,
        height: 100,
    }).showModal();
});



//批量更新
$('#batchUpdate').on('click', function () {
    utils.dialog({
        align: 'top',
        width: 130,
        height: 50,
        padding: 2,
        content: '<div class="changeApplyItem formApprovalBlock">' +
            '<div class="downTemplate"><a href="/proxy-product/product/productOrga/downChangeClassificationTemplate"  class="download">下载模板</a></div>' +
            '<div class="import" onclick="utils.openTabs(\'productOrgaFileupload\',\'机构商品批量更新数据\',\'/proxy-product/product/productOrga/toChangeClassificationFileUpload\')">批量更新</div>' +
            '</div>',
        quickClose: true
    }).show(this);
});

$('#batchUpdateBuyer').on('click', function () {
    utils.dialog({
        align: 'top',
        width: 130,
        height: 50,
        padding: 2,
        content: '<div class="changeApplyItem formApprovalBlock">' +
            '<div class="downTemplate"><a href="/proxy-product/product/productOrga/downBuyerTemplate"  class="download">下载模板</a></div>' +
            '<div class="import" onclick="utils.openTabs(\'toUpdateBuyerPage\',\'采购员批量更新数据\',\'/proxy-product/product/productOrga/toUpdateBuyerPage\')">批量更新</div>' +
            '</div>',
        quickClose: true
    }).show(this);
});


$('#importProductChannel').on('click', function () {
    utils.dialog({
        align: 'top',
        width: 130,
        height: 50,
        padding: 2,
        content: '<div class="changeApplyItem formApprovalBlock">' +
            '<div class="downTemplate"><a href="/proxy-product/product/productOrga/download/productChannelTemplate"  class="download">下载模板</a></div>' +
            '<div class="import" onclick="utils.openTabs(\'toProductChannelPage\',\'批量导入商品渠道\',\'/proxy-product/product/productOrga/toProductChannelImportPage\')">批量导入</div>' +
            '</div>',
        quickClose: true
    }).show(this);
});

$('#IntroduceBtn').on('click', function () {
    var len = $("#X_Tableb").XGrid('getSeleRow');
    var selRow = $('#X_Tableb').XGrid('getSeleRow');
    if (!len || len.length < 1) {
        utils.dialog({
            title: '提示',
            width: 200,
            content: '请先从列表中选择一条数据',
            okValue: '确定',
            ok: function () { }
        }).show();
        return;
    } else if (len.length > 1) {
        utils.dialog({
            title: '提示',
            width: 200,
            content: '只能选择一条数据',
            okValue: '确定',
            ok: function () { }
        }).show();
        return;
    }
    if (selRow && selRow.length > 0) {
        console.log(selRow[0], 'selRow');
        var orgCodeValue = selRow[0].orgCodeValue;
        var productId = selRow[0].id;
        var productName = selRow[0].productName;
        utils.openTabs("oneKeyIntroduce", "商品引入", "/#/supply/goods/oneKeyIntroduce?productId=" + productId + "&orgCodeValue=" + orgCodeValue + "&productName=" + productName);
    }
});


/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url, param, obj, resParam, select, noneSelect) {
    var resParam = Object.assign({ 'list': 'result' }, resParam);
    $("#" + obj + "Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params: param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader: resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#" + obj).val(result.data);
            $("#" + obj + "Val").attr("data-value", result.value);
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value = $("#" + obj + "Val").val();
            if (value != $("#" + obj + "Val").attr("data-value")) {
                $("#" + obj).val("");
                $("#" + obj + "Val").val("");
            }
        }
    });
}

function setUrlObjectList($tr, id, rowData) {
    var a = rowData;
    var fileParam = {};
    if ($tr.hasClass('selRow') && a.imageUrl) {
        fileParam.id = a.id;
        fileParam.name = a.productCode;
        fileParam.url = a.imageUrl;
        urlObjectList.push(fileParam);
    } else if (!$tr.hasClass('selRow')) {
        $(urlObjectList).map(function (i, v) {
            if (v.id == a.id) {
                urlObjectList.splice(i, 1);
            }
        })
    }
}
function initBuyer() {
    valAutocomplete("/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode", { paramName: 'userName', params: { "orgCode": $("#orgCode").val() } }, "buyer", { data: "id", value: "userName" });
}
