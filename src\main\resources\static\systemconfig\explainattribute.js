$(function () {

   $('#Attribute_Table').XGrid({
        url:'/proxy-sysmanage/sysmanage/dict/queryexplainattribute',
        colNames: ['说明书属性id', '说明书属性名称','助记码', '是否必填','控件类型', '是否停用',  '创建人', '创建日期','操作'],
        colModel: [
            {
                name: 'attributeId',
                index: 'attributeId',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'attributeName',
                index: 'attributeName',
                width: 200,//宽度
                editable: true,//是否可编辑
                edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

            }, {
                name: 'attributeNumber',
                index: 'attributeNumber',
                width: 200,//宽度
                editable: true,//是否可编辑
                edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

            }, {
                name: 'attributeIsrequired',
                index: 'attributeIsrequired',
                formatter:isRequired,
                width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'attributeType',
                index: 'attributeType',
               formatter:attribute,
                width: 60,
                editable: true
            },
            {
                name: 'attributeIsstop',
                index: 'attributeIsstop',
                width: 150,
                editable: true,
                formatter:isShop,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'createUser',
                index: 'createUser',
                width: 250,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'createTime',
                index: 'createTime',
                width: 250,
                sortable: false,
                editable: true,
                formatter:datetimeFormatter,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                formatter:attribuOperation,
                width: 250
            }, {
                name: 'attributeDefaultvalue',
                index: 'attributeDefaultvalue',
                width: 250,
                sortable: false,
                editable: true,
                hidden:true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }
        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        key:'attributeId',
        ondblClickRow: function (id, dom, obj, index, event) {
            //双击事件回调函数
            var el = document.querySelector('#dialog_Block');//html元素
            $('#attributeform')[0].reset();
            var selRow = obj;
            if (selRow) {
                $("[name='attributeId']").val(selRow.attributeId)
                $("[name='attributeName']").val(selRow.attributeName)
                $("[name='attributeNumber']").val(selRow.attributeNumber)
                if(selRow.attributeIsstop=="是"){
                    $(":radio[name='attributeIsstop'][value='1']").prop("checked", "checked");
                }else{
                    $(":radio[name='attributeIsstop'][value='0']").prop("checked", "checked");
                }
                if(selRow.attributeIsrequired=="是"){
                    $(":radio[name='attributeIsrequired'][value='1']").prop("checked", "checked");
                }else{
                    $(":radio[name='attributeIsrequired'][value='0']").prop("checked", "checked");
                }
                if(selRow.attributeType=="文本框"){
                    check(0)
                    $(":radio[name='attributeType'][value='0']").prop("checked", "checked");
                }else{
                    $(":radio[name='attributeType'][value='1']").prop("checked", "checked");
                    check(1)
                }

                $("[name='attributeDefaultvalue']").val(selRow.attributeDefaultvalue)
                var val=$('input[name="attributeDefaultvalue"]').val();
                $('input[data-role="tagsinput"]').tagsinput('removeAll');
                $('input[data-role="tagsinput"]').tagsinput('add',val);
                $("[name='attributeName']").attr("disabled",true);
                $("[name='attributeNumber']").attr("disabled",true);
                $(":radio[name='attributeIsstop']").attr("disabled",true);
                $(":radio[name='attributeIsrequired']").attr("disabled",true);
                $(":radio[name='attributeType']").attr("disabled",true);
                $("[name='attributeDefaultvalue']").attr("disabled",true);
                $(".tagsinput input[type='text']").prop("disabled",true);
                utils.dialog({
                    title: '查看明细',
                    content: el,

                }).showModal();
            }else {
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
        }
    });



    //新增一行
    $('#addAttribute').on('click', function () {
        $(".tagsinput input[type='text']").prop("disabled",false);
        $("[name='attributeName']").attr("disabled",false);
        $(":radio[name='attributeIsstop']").attr("disabled",false);
        $(":radio[name='attributeIsrequired']").attr("disabled",false);
        $(":radio[name='attributeType']").attr("disabled",false);
        $("[name='attributeDefaultvalue']").attr("disabled",false);
        var el = document.querySelector('#dialog_Block');//html元素
        check(0)
        $('#attributeform')[0].reset();
        $("[name='attributeId']").val(null)
        $('input[data-role="tagsinput"]').each(function () {
            $(this).tagsinput('removeAll');
        })
        utils.dialog({
            title: '新增',
            content: el,
            okValue: '确定',
            ok: function () {
                $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                data= decodeURIComponent($("#attributeform").serialize(),true);
                if (validform("attributeform").form()) {
                $.ajax({
                    url: "addoreditexplainattribute",
                    type: "post",
                    data: data,
                    success: function (result) {
                        utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                        setTimeout("location.reload();",1000);
                    }
                })
                } else {//验证不通过
                    utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }

            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).showModal();

    })


    $("#delAttribute").click(function(){

        var selRow =  $("#Attribute_Table").XGrid('getSeleRow');
        if (selRow) {
            $.ajax({
                url:"delateexplainattribute",
                data:{attributeId:selRow.attributeId},
                type:"POST",
                success:function(result){
                    utils.dialog({content:  result.result, quickClose: true, timeout: 2000}).showModal();
                    setTimeout("location.reload();",1000);
                }
            })
        } else {
            utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
        }

    })

    $("#attributeBut").click(function () {

        $('#Attribute_Table').setGridParam({
            url:"queryexplainattribute",
            postData:{
                "attributeName":$('#attributeName').val().replace(/\s+/g,""),
                "isStop":$('#isStop').val()
            }
        }).trigger('reloadGrid');

    });

    function isShop(val){
        if(val==0){
            return "否"
        }else{
            return  "是"
        }
    }

    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })

    function datetimeFormatter(val) {
        if (val != null && val !="") {
            return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
        } else {
            return "";
        }
    };

    function isRequired(val){
        if(val!=null ){
            if(val==0) {
                return "否"
            }else{
                return "是"
            }
        }
    }

    function attribute(val){
        if(val!=null){
        if(val!=0){
            return  "复选框"
        }else{
            return  "文本框"
        }
        }
    }



})

    function attribuOperation(){
        return  "<a href='javascript:;' onclick='editattribute(this)'>编辑</a>"
    }
    function editattribute(obj){
        $(".tagsinput input[type='text']").prop("disabled",false);
        $("[name='attributeName']").attr("disabled",false);
        $(":radio[name='attributeIsstop']").attr("disabled",false);
        $(":radio[name='attributeIsrequired']").attr("disabled",false);
        $(":radio[name='attributeType']").attr("disabled",false);
        $("[name='attributeDefaultvalue']").attr("disabled",false);
        var el = document.querySelector('#dialog_Block');//html元素
        $('#attributeform')[0].reset();
        var id=$(obj).parents('tr').attr('id');
        var selRow = $('#Attribute_Table').XGrid('getRowData',id);
        if (selRow) {

            $("[name='attributeId']").val(selRow.attributeId)
            $("[name='attributeDefaultvalue']").val(selRow.attributeDefaultvalue)
            $("[name='attributeName']").val(selRow.attributeName)
            $("[name='attributeNumber']").val(selRow.attributeNumber)
            if(selRow.attributeIsstop=="是"){
                $(":radio[name='attributeIsstop'][value='1']").prop("checked", "checked");
            }else{
                $(":radio[name='attributeIsstop'][value='0']").prop("checked", "checked");
            }
            if(selRow.attributeIsrequired=="是"){
                $(":radio[name='attributeIsrequired'][value='1']").prop("checked", "checked");
            }else{
                $(":radio[name='attributeIsrequired'][value='0']").prop("checked", "checked");
            }
            if(selRow.attributeType=="文本框"){
                check(0)
                $(":radio[name='attributeType'][value='0']").prop("checked", "checked");
            }else{
                $(":radio[name='attributeType'][value='1']").prop("checked", "checked");
                check(1)
            }

            $("[name='attributeDefaultvalue']").val(selRow.attributeDefaultvalue)
            var val=$('input[name="attributeDefaultvalue"]').val();
            $('input[data-role="tagsinput"]').tagsinput('removeAll');
            $('input[data-role="tagsinput"]').tagsinput('add',val);

            utils.dialog({
                title: '编辑',
                content: el,
                okValue: '确定',
                ok: function () {
                    $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                    data = decodeURIComponent($("#attributeform").serialize(), true);
                    if (validform("attributeform").form()) {
                        $.ajax({
                            url: "addoreditexplainattribute",
                            type: "post",
                            data: data,
                            success: function (result) {
                                utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                                setTimeout("location.reload();",1000);
                            }
                        })

                    } else {//验证不通过
                        utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                },
                cancelValue: '取消',
                cancel: function () {

                }
            }).showModal();
        }else {
            utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
        }

    }


    function check(obj){

        if(obj == '1')
        {
            $("#defaul").hide();
            $("#defaul input").val('');
            $("#typeText").show();
            $("#defaulText").removeAttr("name")
            $("#defaulText").removeAttr("class")
            $("#defaulCheck").attr("name","attributeDefaultvalue");
            $("#defaulCheck").attr("class","form-control");


        }else{
            $("#defaul").show();
            $("#typeText").hide();
            $("#defaulCheck").removeAttr("name")
            $("#defaulCheck").removeAttr("class")
            $("#defaulText").attr("name","attributeDefaultvalue");
            $("#defaulText").attr("class","form-control");
            $('#typeText input[data-role="tagsinput"]').tagsinput('removeAll');


        }
    }
