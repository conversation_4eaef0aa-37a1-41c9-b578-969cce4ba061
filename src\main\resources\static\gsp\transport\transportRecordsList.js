$(function () {

    var totalTable = z_utils.totalTable;



    $("#X_Table").XGrid({
        url: "/proxy-gsp/gsp/transport/toTransportRecordsList",
        postData:{
            startTime: $("#starttime").val(),
            endTime: $("#endTime").val(),
        },
        colNames: ["id","销售出库单号", "销售订单号","发货地址","发货日期","收货单位","收货地址","药品件数","运输方式","货单号",
            "委托经办人","承运单位","运输员", "联系方式", "运输车辆","启运日期", "启运温度" ,"到货时间","到货温度"],
        colModel: [
            {
                name: 'id',
                index: 'id',
                key: true,
                hidden:true,
                hidegrid: true
            },
            {
                name: "saleOutOrderNo",
                width:200
            }, {
                name: "saleOrderNo"
            }, {
                name: "sendAddress"
            }, {
                name: "sendDate",
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                }
            },{
                name: "receiveOrg"
            }, {
                name: "receiveAddress"
            }, {
                name: "drugNum"
            }, {
                name: "transportWay"
            }, {
                name: "goodsBillNo"
            },{
                name: "operator"
            }, {
                name: "carrierOrg"
            },{
                name: "transporter"
            }, {
                name: "telPhone"
            }, {
                name: "busNum"
            }, {
                name: "departDate",
            }, {
                name: "departTemperature"
            }, {
                name: "arriveDate",
                width:220
            }, {
                name: "arriveTemperature"
            }],
        altRows: true,
         rownumbers: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        pager: "#grid-pager",
        //attachRow:true,
        selectandorder: false,//是否展示序号，多选
        onSelectRow: function (id, dom, obj) {

        },
        gridComplete: function () {
            /* 合计行 */
           /* var data = $(this).XGrid('getRowData');
            var sum_models = ['drugNum'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });*/
        },
        onPaging: function (page) {
            $("#X_Table").XGrid("clearGridData");
        },
    });


    /* 查询 */
    $('#search_btn').on('click', function () {
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-gsp/gsp/transport/toTransportRecordsList',
            postData: {
                startTime: $("#starttime").val(),
                endTime: $("#endTime").val(),
                orderNo: $("#order_no").val()
            }
        }).trigger('reloadGrid');
        $("#X_Table").XGrid("clearGridData");
    });

    /**
     * 设置显示列
     */
    // 筛选列
    $("#setBtn").click(function () {
        //获取当前显示表格

        $('#X_Table').XGrid('filterTableHead');
    })

    /* 导出 */
    $('#exportBtn').on('click', function () {
        var tableId = "X_Table";
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                /*  if (!data.length) {
                      data = [data];
                  }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            console.log(colName);

            var obj = {
                startTime: $("#starttime").val(),
                endTime: $("#endTime").val(),
                orderNo: $("#order_no").val(),
                selectData: data,
                colName: colName,
                colNameDesc: colNameDesc
            }
            // obj["nameModel"] = nameModel;
            httpPost("/proxy-gsp/gsp/transport/exportTransportRecordsList", obj);
        });
    });


    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol1" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    //格式化日期：yyyy-MM-dd
    function formatDate(date) {
        var date = new Date(parseInt(date));
        var myyear = date.getFullYear();
        var mymonth = date.getMonth() + 1;
        var myweekday = date.getDate();

        if (mymonth < 10) {
            mymonth = "0" + mymonth;
        }
        if (myweekday < 10) {
            myweekday = "0" + myweekday;
        }
        return (myyear + "-" + mymonth + "-" + myweekday);
    }


})








