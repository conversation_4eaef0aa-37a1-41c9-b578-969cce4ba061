/**
 * Created by 沙漠里的红裤头 on 2019/6/26
 */
function initDate(param) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    var month = new_date.getMonth() + 1;
    var date = new_date.getDate();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    //console.log(new_date, year, month, date);
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#beginTime').val(year + '-' + month + '-01 00:00:00');
    $('#endTime').val(year + '-' + month + '-' + date + ' 23:59:59');
}

$(function () {
    $('div[fold=head]').fold({sub: 'sub'});

    initDate();
    getQulifiledNum();
    //不合格品报损申请单列表 、 不合格品报损申请单商品明细    tab 切换
    $('.saleOrderList_tabs>li').on('click', function () {
        var $this = $(this), $nav_content = $this.parents('.panel-body').next();
        $this.addClass('active').siblings().removeClass('active');
        //$nav_content.children('div').eq($this.index()).show().siblings().hide();
        $nav_content.children('div').eq($this.index()).css('display', 'flex').siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');
    });

    var jsonStore = eval($('#orgListJson').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    var colName = ['机构名称', '报损执行单号','报损申请单号', '部门名称', '申请人', '申请日期', '过账日期','不含税成本金额','移动类型','执行日期'];
    var colModel = [
        {name: 'orgCode',index: 'orgCode',
            formatter: function (e) {
                if(!re.test(e)){
                    return e;
                }
                var result = "";
                $.each(jsonStore,function(idx,item){
                    if(item.orgCode == e){
                        result = item.orgName;
                        return false;
                    }
                });
                return result;
            }
        },
        {name: 'lossOrderExec',index: 'lossOrderExec'},
        {name: 'lossOrder',index: 'lossOrder'},
        {name: 'departmentName',index: 'departmentName', hidden:true, hidegrid:true},
        {name: 'applicant',index: 'applicant'},
        {name: 'createDate',index: 'createDate',
            formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }
        },
        {name: 'accountDate',index: 'accountDate',
            formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }
        },
        {name: 'noTaxPriceSum',index: 'noTaxPriceSum'},
        {name: 'moveType', index: 'moveType',formatter:function (){return '报损出库'}},
        {name: 'updateDate',index: 'updateDate', hidden:true,
            formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }
        }
    ];
    $('#X_Tablea').XGrid({
        url: "/proxy-storage/storage/loss/getLossRequestExecList",
        postData:{
            lossOrderNo:$('#keywordOrderByNew').val(),
            productCode:$('#input_goodName').val(),
            startDate:$('#beginTime').val(),
            endDate:$('#endTime').val(),
            drugType:$("#product_category").find("option:selected").text(),
            applicant:$("#applicant").val()
        },
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'id',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid-pager-a',
        ondblClickRow: function (id, dom, obj, index, event) {
            // utils.openTabs("unqualifiedLossInfo","不合格品报损申请单详情", "/proxy-storage/storage/unqualifiedLoss/unqualifiedLossController/toUnqualifiedLossDetailList?damageRequestCode="+id);
            // console.log('双击行事件', id, dom, obj, index, event,"->-",obj.lossOrder);
            // utils.openTabs("unqualifiedLossInfo", "不合格品报损申请单详情", "/proxy-storage/storage/loss/toOrderRequestDetailList?lossOrderNo="+obj.lossOrder+"&requestDate="+obj.createDate+"&applicant="+obj.applicant+"&orgCode="+obj.orgCode+"&departmentName="+obj.departmentName+"&orderStatus="+obj.orderStatus+"&noTaxPriceSum="+obj.noTaxPriceSum+"&processId="+obj.processInstanceId);
            // utils.openTabs("unqualifiedLossInfo", "不合格品报损申请单详情", "/proxy-storage/storage/loss/toOrderRequestDetailList?lossOrderNo="+obj.lossOrder);
            utils.openTabs("unqualifiedLossExcelInfo", "不合格品报损执行单详情", "/proxy-storage/storage/loss/toLossRequestExecDetailList?lossOrderNo="+obj.lossOrder+"&execDate="+obj.updateDate+"&applicant="+obj.applicant+"&orgName="+obj.orgCode+"&departmentName="+obj.departmentName+"&noTaxPriceSum="+obj.noTaxPriceSum);
        },
        onSelectRow: function (id, dom, obj, index, event) {
            // var $ele = $(event.target);
            // if($ele.attr("class")=="ajust_a"){
            //     var data = $ele.attr("for");
            //     if (obj.orderStatus == "调账中" ){
            //         utils.openTabs("toAddAdjust","新增调账申请单", '/proxy-storage/storage/adjustment/toAdd?orderType=6&orderCode='+data)
            //     }else if (obj.orderStatus == "待处理" ) {
            //         utils.openTabs("toAddAdjust","调账申请单详情", '/proxy-storage/storage/adjustment/toInfo?adjustmentApplyCode='+data)
            //     }
            // }
        },
        gridComplete: function () {},
    });

    //设置显示列
    $("#set_tables_rowa").click(function () {
        switch (getTabInd()) {
            case 0:
                $('#X_Tablea').XGrid('filterTableHead');
                break;
            case 1:
                $('#X_Tableb').XGrid('filterTableHead');
                break;
        }
    });

    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        //列表
        $('#X_Tablea').XGrid('setGridParam', {
            url: "/proxy-storage/storage/loss/getLossRequestExecList",
            postData: {
                lossOrderNo:$('#keywordOrderByNew').val(),
                productCode:$('#input_goodName').val(),
                startDate:$('#beginTime').val(),
                endDate:$('#endTime').val(),
                drugType:$("#product_category").find("option:selected").text(),
                applicant:$("#applicant").val()
            }
        }).trigger("reloadGrid");
        getQulifiledNum();
    });

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#X_Tablea').attr('id');
        //获取当前选中项
        console.log(tableId);
        var data =  $('#X_Tablea').XGrid('getSeleRow');
        console.log(data);
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))


                    colNameDesc.push($(this).parent().text());
                }
            });

            var exportLimitRow = Number($("#exportLimitRow").val());
            if (data.length <= exportLimitRow && data.length >0) {
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
            } else {
                var rows = $('#' + tableId)[0].p.records;
                if(rows > exportLimitRow) {
                    utils.dialog({
                        title: '提示',
                        width: 200,
                        height: 40,
                        cancel: false,
                        content: '您本次导出的数据量过大（已超过'+exportLimitRow+'条），不允许导出，请缩小导出范围。',
                        okValue: '确定',
                        ok: function () {
                            //utils.closeTab();
                        }

                    }).showModal();
                    return;
                }
                data = '';
            }

            console.log(colName);

            //获取form数据
            var formData = {
                colName: colName,
                colNameDesc: colNameDesc,
                selectData: data,
                lossOrderNo:$('#keywordOrderByNew').val(),
                productCode:$('#input_goodName').val(),
                startDate:$('#beginTime').val(),
                endDate:$('#endTime').val(),
                drugType:$("#product_category").find("option:selected").text(),
                applicant:$("#applicant").val()
            }
            httpPost("/proxy-storage/storage/loss/exportLossRequestExecData", formData);
        });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#supplierCode').val(data.productCode);
                    $('#manufName').val(data.manufacturerVal);
                    $('#spec').val(data.specifications);
                }
            },
            oniframeload: function () {}
        }).showModal();
        return false;
    })
})
var parent_orgCode = ""
if(parent.$("#hidesysOrgCode")){
    parent_orgCode = parent.$("#hidesysOrgCode").val();
}else {
    parent_orgCode = parent.hidesysOrgCode
}

//商品名称 搜索
$('#input_goodName').Autocomplete({
    serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+parent_orgCode, //异步请求
    paramName: 'param',//查询参数，默认
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    transformResult: function (response) {
        return {
            suggestions: $.map(response.result.list, function (dataItem) {
                return {
                    value: dataItem.productName,
                    data: dataItem.productCode,
                    manufacturerVal: dataItem.manufacturerVal,
                    specifications: dataItem.specifications,
                };
            })
        };
    },
    triggerSelectOnValidInput: false, // 必选
    // multi: true, //多选要和delimiter一起使用
    // delimiter: ',',
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function (result) {
        //选中回调
        $("#drugCode").val(result.data)
        $("#manufName").val(result.manufacturerVal);
        $("#spec").val(result.specifications);

    },
    onSearchStart: function (params) {
        // console.log('检索开始回调', params)
    },
    onSearchComplete: function (query, suggestions) {

    },
    onSearchError: function (query, jqXHR, textStatus, errorThrown) {
        //查询失败回调
        console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function (container) {
        // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function (params, suggestions) {
        $("#drugCode").val("");
        $("#input_goodName").val("");
        console.log(params, suggestions);
        console.log('没选中回调函数');
        if ($('#input_goodName').val()==''){
            $('#manufName').val('');
            $('#spec').val('');
        }
    }
});

//获取当前tab  的下标
function getTabInd() {
    for (var i = 0; i < $('.saleOrderList_tabs li').length; i++) {
        if ($('.saleOrderList_tabs li').eq(i).hasClass('active')) {
            _ind = $('.saleOrderList_tabs li').eq(i).index();
        }
    }
    return _ind;
};

function getQulifiledNum () {
    //获取form数据
    var formData = {
        lossOrderNo:$('#keywordOrderByNew').val(),
        productCode:$('#input_goodName').val(),
        startDate:$('#beginTime').val(),
        endDate:$('#endTime').val(),
        drugType:$("#product_category").find("option:selected").text(),
        applicant:$("#applicant").val()
    }
    // var formData = $("#searchForm").serializeToJSON();
    //加载总数量
    $.ajax({
        url: '/proxy-storage/storage/loss/findTotalPriceSum',
        dataType: 'json',
        timeout: 8000,
        data: formData,
        success: function (data) {
            if (data.code == 0) {
                var result = data.result;
                if (result != null) {
                    $("#totalCostAmountSum").text(Number(result.noTaxPriceSum).toFixed(2));
                } else {
                    $("#totalCostAmountSum").text('0.00');
                }
            }
        },
        error: function () {
            $("#totalCostAmountSum").text('0.00');
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
};