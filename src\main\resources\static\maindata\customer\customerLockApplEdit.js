$('div[fold=head]').fold({
    sub: 'sub'
});
//显示流程图
var processInstaId=$("#processId").val();
if(!processInstaId || processInstaId == ""){
    processInstaId=$("#approvalProcessId").val();
}
var businessType = $('#businessType').val();
businessType = businessType^1;
// if( ){
//     processInstaId=$("#key").val();
// }
initApprovalFlowChart( processInstaId);
var selArr = [];
var dataid = Number($('#id').val());
$('#table').XGrid({
    url: "/proxy-customer/customer/customerLock/customerLockDetails?id="+dataid,
    colNames: ['', '客户编码', '客户名称', '客户类别', '是否锁定', '业务类型', '<i style="color:red;margin-right:4px;">*</i>申请原因'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        hidden:true
    }, {
        name: 'customerCode',
    }, {
        name: 'customerName',
    }, {
        name: 'customerType',
    }, {
        name: 'isLock',
        formatter: function (e) {
            console.log('---  ' + e)
            if (e == '0') {
                return '否';
            } else if (e == '1') {
                return '是';
            } else {
                return "";
            }
        },unformat: function (e) {
            if (e == '否') {
                return '0';
            } else if (e == '是') {
                return '1';
            } else {
                return "";
            }
        }
    }, {
        name: 'businessType',
        formatter: function (e) {
            if (e == '0') {
                return '解锁';
            } else if (e == '1') {
                return '锁定';
            } else {
                return "";
            }
        },unformat: function (e) {
            if (e == '锁定') {
                return '0';
            } else if (e == '解锁') {
                return '1';
            } else {
                return "";
            }
        }
    },{
        name: 'applReason',
        rowtype: '#applReason'
    },{
        name: 'baseId',
        hidden:true
    },{
        name: 'orgCode',
        index: 'orgCode',
        hidden:true
    },{
        name: 'customerMnemonicCode',
        index: 'customerMnemonicCode',
        hidden:true
    }],
    //key: 'baseId',
    rowNum:0,
    rownumbers: true,//是否展示序号
    altRows: true//设置为交替行表格,默认为false
});


$("#addRow").on("click",function(){
    selArr = $("#table").getRowData();

    utils.dialog({
        url: '/proxy-customer/customer/customerLock/toSearchList?businessType='+businessType,//弹框页面请求地址
//    	url:'/proxy-supplier/supplier/supplierDisableApprovalRecord/searchSupplierPage',
        title: '搜索客户',
        width: 1000,
        data:{
            initDataArr: selArr,
            businessType:businessType,
            isLock: businessType
        },
        height: 600,
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                $("#table").XGrid('clearGridData');
                for(var i = 0 ; i < data.length; i++ ){
                    if(!fidInArr(data[i].id)){ // !fidInArr(data[i].id) ||
                        $('#table').XGrid("addRowData",data[i]);
                    }
                }
            }
        }
    }).showModal();
});

/* 新增行 事件*/
/*$("#addRow").on("click",function(){
    var selArr = $("#table").getRowData();
    utils.dialog({
        url: '/proxy-supplier/supplier/supplierDisableApprovalRecord/toSearchList',//弹框页面请求地址
//    	url:'/proxy-supplier/supplier/supplierDisableApprovalRecord/searchSupplierPage',
        title: '搜索供应商',
        width: 1000,
        data:{
            initDataArr:selArr
        },
        height: 600,
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                var table = $("#table").getRowData();
                console.log(table)
                for(var i = 0 ; i < data.length; i++ ){
                    if(!fidInArr(table,data[i].supplierCode)){
                        $('#table').XGrid("addRowData",data[i]);
                    }
                }
            }
        }
    }).showModal();
});*/

/* 删除行 事件*/
$("#delRow").on("click",function(){
    var selObj = $('#table').XGrid("getSeleRow");
    if(selObj){
        $('#table').XGrid("delRowData",selObj.id);
    }else{
        utils.dialog({"content":"请选择删除行！","timeout":2000}).show();
    }
});
var auditStatus = $("#auditStatus").val();
if(auditStatus != '1'){
    $('input[name = appReason]').attr('disabled','disabled');
}

//数组中查找id
function fidInArr(id){
    let arr = $("#table").getRowData()
    for(var i=0;i<arr.length;i++){
        if(arr[i].baseId == id){
            return true;
        }
    }
    return false;
}
// function fidInArr(arr,supplierOrganId){
//     for(var i=0;i<arr.length;i++)
//     {
//         if(arr[i].supplierOrganId == supplierOrganId)
//         {
//             return true;
//         }
//     }
//     return false;
// }
/**
 * 流程图显示
 */
function  initApprovalFlowChart(processInstaId) {
    //获取审核流程数据
    const key = $("#key").val();
    const url = "/proxy-customer/customer/customerLock/queryTotle?key="+key+"&processInstaId="+processInstaId;
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}




//关闭按钮
$("#closeAssert").on("click", function () {
    var table = $("#table").getRowData();
    if(table == false){
        utils.dialog({content: '请添加锁定申请！', quickClose: true, timeout: 2000}).showModal();
        return;
    }
    for(var i = 0 ; i < table.length; i++  ){
        if(table[i].customerCode==""||table[i].customerCode==null){
            utils.dialog({content: '请选择锁定客户或删除空行！', quickClose: true, timeout: 2000}).showModal();
            return;
        }
    }
   var d = dialog({
        title: "提示",
        content: "是否保存草稿？",
        width:300,
        height:30,
        button:[
            {
                value:'关闭',
                callback:function(){
                	utils.closeTab();
                }
            }
        ],
        okValue: '保存草稿',
        ok: function () {
            $("#auditStatus").val("1");// 0:录入中
            save(1);
            d.close().remove();
            return false;
        }
    }).showModal();
});


function save(auditStatus) {
    //  var sexamine = $("#examine").val()
    var customerLockApplVo = $("#baseApproval").serializeToJSON()
    var table = $("#table").getRowData();

    if(table == false){
        utils.dialog({content: '请添加锁定申请！', quickClose: true, timeout: 2000}).showModal();
        return;
    }
    for(var i = 0 ; i < table.length; i++  ){
        if(table[i].customerCode==""||table[i].customerCode==null){
            utils.dialog({content: '请选择锁定客户或删除空行！', quickClose: true, timeout: 2000}).showModal();
            return;
        }
    }
    if(auditStatus == 2){
        for(var i = 0 ; i < table.length; i++  ){
            if(table[i].applReason==""||table[i].applReason==null){
                utils.dialog({content: '请填写申请原因！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        }

    }
    for(var i = 0 ; i < table.length; i++ ){
        table[i].businessType = businessType^1;
        table[i].createTime = customerLockApplVo.applicationDate;
        table[i].correlationId = customerLockApplVo.id;
        table[i].orgCode = $("#orgCode").val();
    }
    customerLockApplVo.customerLockApplDetailVoList = table;
    //加载页面
    var url;
    if(auditStatus == 2){
        url = "/proxy-customer/customer/customerLock/addCusutomerLockAppl";
    } else {
        url = "/proxy-customer/customer/customerLock/addCusutomerLockApplTemp";
    }
    parent.showLoading({hideTime: 60000});
    $.ajax({
        url: url,
        data: JSON.stringify(customerLockApplVo),
        type: "post",
        dataType: 'json',
        contentType: "application/json",
        success: function (data) {
            parent.hideLoading();
            if(data.result.success){
                var msg = "";
                var msg = "";
                if(auditStatus == 2){
                    msg = "提交成功";
                } else {
                    msg = "保存成功";
                }
                utils.dialog({
                    title: "提示",
                    content:  msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {

                        $('#X_Tableb').XGrid('setGridParam', {
                            postData: {
                                "orgCode": $("#orgCode").val(),
                                "customerCode":$("#customerCode").val(),
                                "auditStatus":$("#auditStatus").val(),
                                "applicantNum":$("#applicantNum").val(),
                                "businessType":$("#businessType").val()
                            },page:1
                        }).trigger('reloadGrid');
                        window.location.reload();

                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;

            }else{
                var msg = "";

                if(data.result.failed0){
                    if(auditStatus == 2){
                        msg = data.result.failed0;
                    } else {
                        msg = data.result.failed0;
                    }
                }else if (data.result.failed1) {
                    msg = data.result.failed1;
                }else if (data.result.failed2) {
                    msg = data.result.failed2;
                }else if (data.result.failed3) {
                    msg = data.result.failed3;
                }else if (data.result.failed4) {
                    msg = data.result.failed4;
                }
                utils.dialog({
                    // title: "提示",
                    content:  msg,
                    quickClose:true
                    /*width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                    }*/
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        },
        error: function () {
            utils.dialog({content: '内部出错！', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}

/* 弹框 */
function showDialog(id){
    var auditStatus = $("#auditStatus").val();
    var search_vl = $("#search_vl").val();
    if(auditStatus !=1){
        return;
    }
    dialog({
        url: "/proxy-customer/customer/customerLock/toSearchCustomerList?businessType="+businessType+"&customerCode="+search_vl,//弹框页面请求地址
        title: '搜索客户',
        width: 1000,
        height: 800,
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                var table = $("#table").getRowData();
                for(var i = 0 ; i < table.length; i++ ){
                    if(data.baseId ==table[i].baseId ){
                        utils.dialog({content: '请不要重复选择供客户！', quickClose: true, timeout: 2000}).showModal();
                        return;
                    }
                }
                $('#table').XGrid("setRowData",id,data);
            }
        }
    }).showModal();
}
