ul,
li {
	list-style: none;
}

.body_div,
body {
	display: block;
	height: 100%;
	width: 100%;
}

.topNav_div {
	height: 50px;
	z-index: 999;
	position: absolute;
	width: 100%;
	background: #102442;
}

.topNav_div #navbar-container {
	padding: 0;
}

.topNav_div #navbar-container #logo {
	margin-left: 20px;
}

.topNav_div .navbar-header {
	height: 50px;
	margin-left: 20px;
	position: relative;
}

.logo_div {
	font-size: 18px;
	width: 210px;
	height: 50px;
	position: relative;
}

.logo_div img {
	position: absolute;
	top: 9px;
	left: 10px;
	display: inline-block;
}

.logo_div span {
	display: inline-block;
	position: absolute;
	height: 50px;
	line-height: 50px;
	left: 43px;
	top: 0;
	color: #FFF;
	font-size: 17px;
}

.logo_div #version {
	position: absolute;
	left: 115px;
	height: 50px;
	line-height: 50px;
}

.navbar .navbar-brand {
	color: #FFF;
	font-size: 12px;
	text-shadow: none;
	padding-top: 10px 0+ 852.7410;
	padding-bottom: 10px;
	height: auto;
}

#user-nav {
	float: left;
	height: 50px;
	line-height: 50px;
	left: 210px;
	position: fixed;
	top: 0px;
}

#user-nav a {
	color: #fff;
	text-decoration: none;
}

.psw_out_btn_area {
	position: absolute;
	top: 50px;
	z-index: 1;
	left: 220px;
	display: none;
}

#user-nav:hover .dropdown-menu {
	display: block;
}

#user-nav .dropdown-menu a {
	color: #AAA;
	line-height: 30px;
	padding: 0 !important;
}

.user-menu {
	padding: 0;
	min-width: 115px;
	margin-bottom: 0;
	padding: 0 10px;
	border: 1px solid #ccc;
	border: 1px solid rgba(0, 0, 0, .15);
	background-clip: padding-box;
	background-color: #FFF;
	border-radius: 0 !important;
	box-shadow: 0 2px 4px rgba(0, 0, 0, .2);
}

.user-menu:before,
.user-menu:after {
	content: "";
	display: inline-block;
	position: absolute;
}

.user-menu:before {
	border-bottom: 7px solid rgba(0, 0, 0, .2);
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
	top: -6px;
	left: auto;
	right: 9px;
}

.user-menu:after {
	border-bottom: 6px solid #FFF;
	border-left: 6px solid transparent;
	border-right: 6px solid transparent;
	top: -5px;
	left: auto;
	right: 10px;
}

.user-menu li,
.user-menu li p {
	height: 30px;
	line-height: 30px;
	cursor: pointer;
	text-align: left !important;
	box-sizing: border-box;
	color: #AAAAAA;
}

.topBtn_area {
	width: 136px;
	line-height: 50px;
	height: 50px;
	position: fixed;
	right: 0px;
	border-left: 1px solid #FFFFFF;
	text-align: center;
}

.topBtn_area a {
	color: #fff;
}

.topBtn_area a:hover {
	text-decoration: none;
}

.topBtn_area a .menu-text {
	margin-left: -15px;
}

#navbar .nav .dropdown-modal:hover .dropdown-menu {
	display: block;
}

#navbar .nav .dropdown-modal .dropdown-menu li {
	width: 200px;
	text-align: center;
}

.bTabs .nav-tabs {
	border: none;
}

.bTabs .nav-tabs>li {
	width: auto;
	height: 32px;
	margin: 0 7px;
	background: #F7F7F7;
	border: 1px solid #E9E9E9;
	position: relative;
}

.bTabs .nav-tabs>li:before,
.bTabs .nav-tabs>li:after {
	content: '';
	display: block;
	width: 15px;
	height: 31px;
	position: absolute;
	background: #D9D9D9;
	top: 0;
	transform: skewX(-20deg);
	border-top-left-radius: 8px;
	left: -10px;
	border-left: 1px solid #E9E9E9;
	border-bottom: 1px solid #e9e9e9;
}

.bTabs .nav-tabs>li a,
.bTabs .nav-tabs>li a:hover {
	border: none;
	padding-top: 7px;
	background: none;
}

.bTabs .nav-tabs>li:after {
	left: auto;
	transform: skewX(20deg);
	border-top-right-radius: 8px;
	border-right: 1px solid #E9E9E9;
	border-left: none;
	right: -10px;
}

.bTabs .nav-tabs>li.active,
.bTabs .nav-tabs>li:hover {
	background: #fff;
	border-bottom-color: #fff;
}

.bTabs .nav-tabs>li.active>a,
.bTabs .nav-tabs>li.active>a:hover,
.bTabs .nav-tabs>li.active>a:focus {
	color: #555555;
	border: none;
	border-bottom: none;
	font-weight: bold;
	cursor: default;
	box-shadow: none;
	margin-top: 0;
}

.bTabs .nav-tabs>li.active:before,
.bTabs .nav-tabs>li.active:after,
.bTabs .nav-tabs>li:hover:before,
.bTabs .nav-tabs>li:hover:after {
	background: #fff;
	z-index: 10;
	border-bottom-color: #fff;
}

.tab_area_div {
	padding-left: 15px;
}

.tab_area_div #nav-tab {
	display: flex;
	margin-bottom: 0;
	height: 32px;
	white-space: nowrap;
	position: absolute;
	padding: 0 7px;
	border-bottom: none;
}

/*      */

.erp_content_div {
	position: absolute;
	top: 50px;
	bottom: 0;
	left: 0;
	right: 0;
}

.leftMenu_div {
	width: 190px;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	/* overflow-y: auto; */
	transition: all 1s;
	background: #102442;
}

/*左侧滚动条样式*/

.leftMenu_div::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 8px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 6px;
}

.leftMenu_div::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #535353;
}

.leftMenu_div::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 10px;
	background: #EDEDED;
}

.sidebar {
	width: 190px;
	float: left;
	position: relative;
	padding-left: 0;
	padding-right: 0;
	z-index: 100;
}

.sidebar:before {
	display: block;
	width: inherit;
	position: absolute;
	top: 0;
	bottom: 0;
	z-index: -1;
	background-color: inherit;
	border-style: inherit;
	border-color: inherit;
	border-width: inherit;
}

.nav-list {
	margin: 0;
	padding: 0;
	list-style: none;
}

.nav-list .nav-header,
.nav-list>li,
.nav-list>li>a {
	margin: 0;
}

.nav-list>li {
	display: block;
	position: relative;
	float: none;
	padding: 0;
	border-style: solid;
	border-width: 1px 0 0;
	background: #23406A;
}

.nav-list>li:hover {
	background: #2DB7F5;
}

.nav-list>li>a {
	display: block;
	height: 39px;
	line-height: 17px;
	padding-left: 7px;
	text-shadow: none !important;
	font-size: 14px;
}

.nav-list li>.arrow {
	display: none;
	position: absolute;
	top: 8px;
	right: 1px;
	z-index: 1027;
}

.nav-list>li .submenu {
	display: none;
	list-style: none;
	margin: 0;
	padding: 0;
	line-height: 1.5;
	position: relative;
}

.nav-list>li>.submenu {
	border-top: 1px solid;
}

.nav-list li.open>.submenu,
.nav-list>li .submenu.collapsing,
.nav-list>li>.submenu li.open>.submenu {
	display: block;
}

.nav-list>li>.submenu:before {
	display: block;
	position: absolute;
	z-index: 1;
	left: 18px;
	top: 0;
	bottom: 0;
	border: 1px dotted;
	border-width: 0 0 0 1px;
}

.nav-list>li .submenu>li {
	margin-left: 0;
	position: relative;
}

.nav-list>li>.submenu>li:before {
	display: block;
	width: 7px;
	position: absolute;
	z-index: 1;
	left: 20px;
	top: 17px;
	border: 1px dotted;
	border-width: 1px 0 0;
}

.nav-list>li .submenu>li>a {
	display: block;
	position: relative;
	padding: 7px 0 9px 37px;
	margin: 0;
	border-top-width: 1px;
	border-top-style: dotted;
}

.nav-list>li .submenu>li>a:hover,
.nav-list>li>a,
a:active,
a:focus {
	text-decoration: none;
}

.nav-list>li a>.arrow {
	display: block;
	width: 14px !important;
	height: 14px;
	line-height: 14px;
	text-shadow: none;
	font-size: 18px;
	position: absolute;
	right: 10px;
	top: 12px;
	padding: 0;
	text-align: center;
}

/* 鼠标悬浮的样式 */

/* .nav-list>li:hover>.hover-content {
	display: block;
}

.nav-list .hover-submenu>li:hover>.hover-menu1 {
	display: block;
	border-left: 1px solid #ECECEC;
	border-bottom-right-radius: 0;
	border-top-left-radius: 0;
} */

.hover-content {
	position: absolute;
	top: 0;
	left: 190px;
	display: none;
}

.hover-menu {
	width: 270px;
	background: #fff;
	position: relative;
	left: 10px;
	box-shadow: 1px 2px 8px 0;
	border-radius: 4px;
}

.hover-menu1 {
	width: max-content;
    min-width: 270px;
    height: max-content;
    min-height: 100%;
	position: absolute;
	left: 271px;
	top: 0;
	background: #fff;
	display: none;
	border-radius: 4px;
	box-shadow: 0 2px 8px 0;
}

#sidebar .hover-submenu>li>a,
#sidebar .hover-submenu1>li>a {
	height: 40px;
	font-size: 14px;
	color: #333333;
	line-height: 40px;
	position: relative;
	width: 100%;
	display: block;
	padding: 1px 0 16px 28px;
}

#sidebar .hover-submenu>li>a>.operate,
#sidebar .hover-submenu1>li>a>.operate {
	position: absolute;
	right: 18px;
	top: 50%;
	margin-top: -12px;
	height: 24px;
	line-height: 24px;
}

#sidebar .hover-menu li>.menu-text,
#sidebar .hover-menu1 li>.menu-text1 {
	font-size: 14px;
	color: #333333;
	font-weight: bold;
	line-height: 40px;
	margin: 0;
	padding-left: 28px;
}

.hover-submenu>li .operate>.apply,
.hover-submenu1>li .operate>.apply {
	width: 44px;
	font-size: 12px;
	color: #fff;
	background: #2DB7F5;
	border: 0;
	outline: none;
	border-radius: 4px;
}

.hover-submenu>li .operate>.batch-apply,
.hover-submenu1>li .operate>.batch-apply {
	width: 60px;
	font-size: 12px;
	color: #fff;
	background: #2DB7F5;
	border: 0;
	outline: none;
	border-radius: 4px;
	margin-left: 16px;
}

.hover-submenu>li:hover,
.hover-submenu1>li:hover {
	background: #E2F8FF;
}

.hover-menu>li>.hover-submenu>li>.hover-tab:hover,
.hover-menu1>li>.hover-submenu1>li>.hover-tab1:hover {
	background: #E2F8FF;
	text-decoration: none;
}

.hover-menu:before {
	content: '';
	position: absolute;
	top: 5px;
	left: -10px;
	width: 0;
	height: 0;
	border-top: 10px solid transparent;
	border-bottom: 10px solid transparent;
	border-right: 10px solid #fff;
}

.activeted {
	background-color: #2698FA;
}

.hover-tab.activeted,
.hover-tab1.activeted {
	display: inline-block;
	width: 100%;
	height: 100%;
	background: rgb(217, 251, 250) !important;
}

/* 批量申请 */

.clickApplyItem {
	display: flex;
	width: 150px;
	position: absolute;
	top: -73px;
	left: 18px;
	background-color: #FFF;
	border: 1px solid #999;
	border-radius: 6px;
	outline: 0;
	padding: 6px;
	z-index: 10;
	background-clip: padding-box;
	font-family: Helvetica, arial, sans-serif;
	font-size: 14px;
	line-height: 1.428571429;
	color: #333;
	transition: transform .15s ease-in-out, opacity .15s ease-in-out;
}

.clickApplyItem:before {
	content: '';
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	overflow: hidden;
	border: 9px dashed transparent;
	bottom: -18px;
	border-top: 9px solid #7C7C7C;
	left: 50%;
	margin-left: -9px;
}

.clickApplyItem:after {
	left: 50%;
	margin-left: -8px;
	bottom: -16px;
	content: "";
	display: block;
	width: 0;
	height: 0;
	position: absolute;
	border: 8px dashed transparent;
	border-top: 8px solid #fff;
}

.clickApplyItem>div {
	justify-content: space-around;
	flex-grow: 1;
	padding-top: 30px;
	position: relative;
	color: #2DB7F5;
	text-align: center;
}

.import {
	margin-right: 12px;
}

.clickApplyItem .import:before {
	content: '';
	width: 24px;
	height: 24px;
	background: url('/proxy-sysmanage/static/images/dr_btn.png') no-repeat;
	background-size: contain;
	position: absolute;
	top: 0px;
	left: 50%;
	transform: translate(-50%, 0);
}

.clickApplyItem .download:before {
	content: '';
	width: 24px;
	height: 24px;
	background: url('/proxy-sysmanage/static/images/mb_btn.png') no-repeat;
	background-size: contain;
	position: absolute;
	top: 0px;
	left: 50%;
	transform: translate(-50%, 0);
}

/* 悬浮样式结束 */

#sidebar .second {
	background: rgb(46, 54, 63) !important;
	font-size: 12px !important;
	margin-left: 8px !important;
}

#sidebar .sub_menu {
	margin-left: 7px;
}

#sidebar .third {
	padding-left: 55px !important;
	background: rgb(46, 54, 63) !important;
	font-size: 10px !important;
}

#sidebar .four {
	background: rgb(46, 54, 63) !important;
	font-size: 10px !important;
	padding-left: 63px !important;
}

#sidebar a {
	color: #fff;
}

#sidebar a.dropdown-toggle {
	padding-left: 30px;
}

#sidebar .nav-list>li .submenu>li>a {
	border: none;
}

/* icon的样式 */

.font_family {
	font-size: 14px;
	margin-right: 20px;
}

.icon-GSPguanli,
.icon-caiwuguanli {
	display: inline-block;
	width: 14px;
	height: 14px;
}

.icon-xitongshezhi {
	margin: 0 23px 10px 0;
}

/* icon的样式 */

.nav .open>a,
.nav .open>a:focus,
.nav .open>a:hover,
.nav li>a:hover {
	background: #2698FA;
}

#sidebar .dropdown-toggle {
	background-color: rgba(216, 216, 216, 0.22);
}

.main_content {
	float: left;
	width: 100%;
	height: 100%;
	padding: 0 0 0 190px;
}

.main-content-inner {
	float: left;
	width: 100%;
	height: 100%;
	background: #EBEDF1;
}

#mainFrameTabs {
	display: flex;
	flex-direction: column;
}

.bTabs .nav-tabs>li {
	width: auto;
	height: 32px;
	margin: 0 7px;
	background: #D9D9D9;
	border: 1px solid #E9E9E9;
	position: relative;
}

#nav-tab>li {
	float: none;
}

.bTabs .nav-tabs>li.active,
.bTabs .nav-tabs>li:hover {
	background: #fff;
	border-bottom-color: #fff;
}

#nav-tab>li:not(:first-child) a {
	overflow: hidden;
	z-index: 10;
}

.bTabs .nav-tabs>li.active>a,
.bTabs .nav-tabs>li.active>a:hover,
.bTabs .nav-tabs>li.active>a:focus {
	color: #555555;
	border: none;
	border-bottom: none;
	font-weight: bold;
	cursor: default;
	box-shadow: none;
	margin-top: 0;
}

.bTabs button.navTabsCloseBtn {
	position: absolute;
	top: 0;
	right: -2px;
	font-size: 17px !important;
	z-index: 19;
}

.bTabs .nav-tabs>li.active:before,
.bTabs .nav-tabs>li.active:after,
.bTabs .nav-tabs>li:hover:before,
.bTabs .nav-tabs>li:hover:after {
	background: #fff;
	z-index: 10;
	border-bottom-color: #fff;
}

.tab-content {
	border: 1px solid #C5D0DC;
	padding: 16px 12px;
	position: relative;
}

.bTabs div.tab-content {
	height: 100% !important;
	/* overflow-y: hidden; */
}

#mainFrameTabs .tab-content {
	padding-top: 31px;
	flex: 1;
}

.loadingBlock {
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	top: 0;
}

.loadingBlock>div {
	display: block;
	width: 40px;
	height: 40px;
	position: absolute;
	top: 46%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.loadingBlock .la-ball-spin-clockwise,
.la-ball-spin-clockwise>div {
	position: relative;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box
}

.loadingBlock .la-ball-spin-clockwise {
	display: block;
	font-size: 0;
	color: #fff
}

.loadingBlock .la-ball-spin-clockwise>div {
	display: inline-block;
	float: none;
	background-color: currentColor;
	border: 0 solid currentColor
}

.loadingBlock .la-ball-spin-clockwise {
	width: 32px;
	height: 32px
}

.loadingBlock .la-ball-spin-clockwise>div {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 8px;
	height: 8px;
	margin-top: -4px;
	margin-left: -4px;
	border-radius: 100%;
	-webkit-animation: ball-spin-clockwise 1s infinite ease-in-out;
	-moz-animation: ball-spin-clockwise 1s infinite ease-in-out;
	-o-animation: ball-spin-clockwise 1s infinite ease-in-out;
	animation: ball-spin-clockwise 1s infinite ease-in-out
}

.loadingBlock .la-ball-spin-clockwise>div:nth-child(1) {
	top: 5%;
	left: 50%;
	-webkit-animation-delay: -.875s;
	-moz-animation-delay: -.875s;
	-o-animation-delay: -.875s;
	animation-delay: -.875s
}

.loadingBlock .la-ball-spin-clockwise>div:nth-child(2) {
	top: 18.1801948466%;
	left: 81.8198051534%;
	-webkit-animation-delay: -.75s;
	-moz-animation-delay: -.75s;
	-o-animation-delay: -.75s;
	animation-delay: -.75s
}

.loadingBlock .la-ball-spin-clockwise>div:nth-child(3) {
	top: 50%;
	left: 95%;
	-webkit-animation-delay: -.625s;
	-moz-animation-delay: -.625s;
	-o-animation-delay: -.625s;
	animation-delay: -.625s
}

.loadingBlock .la-ball-spin-clockwise>div:nth-child(4) {
	top: 81.8198051534%;
	left: 81.8198051534%;
	-webkit-animation-delay: -.5s;
	-moz-animation-delay: -.5s;
	-o-animation-delay: -.5s;
	animation-delay: -.5s
}

.loadingBlock .la-ball-spin-clockwise>div:nth-child(5) {
	top: 94.9999999966%;
	left: 50.0000000005%;
	-webkit-animation-delay: -.375s;
	-moz-animation-delay: -.375s;
	-o-animation-delay: -.375s;
	animation-delay: -.375s
}

.loadingBlock .la-ball-spin-clockwise>div:nth-child(6) {
	top: 81.8198046966%;
	left: 18.1801949248%;
	-webkit-animation-delay: -.25s;
	-moz-animation-delay: -.25s;
	-o-animation-delay: -.25s;
	animation-delay: -.25s
}

.loadingBlock .la-ball-spin-clockwise>div:nth-child(7) {
	top: 49.9999750815%;
	left: 5.0000051215%;
	-webkit-animation-delay: -.125s;
	-moz-animation-delay: -.125s;
	-o-animation-delay: -.125s;
	animation-delay: -.125s
}

.loadingBlock .la-ball-spin-clockwise>div:nth-child(8) {
	top: 18.179464974%;
	left: 18.1803700518%;
	-webkit-animation-delay: 0s;
	-moz-animation-delay: 0s;
	-o-animation-delay: 0s;
	animation-delay: 0s
}

.loadingBlock .la-ball-spin-clockwise.la-2x {
	width: 64px;
	height: 64px
}

.loadingBlock .la-ball-spin-clockwise.la-2x>div {
	width: 12px;
	height: 12px;
	margin-top: -8px;
	margin-left: -8px
}

.loadingBlock .la-ball-spin-clockwise.la-3x>div {
	width: 24px;
	height: 24px;
	margin-top: -12px;
	margin-left: -12px
}

/* demo css gose here */

body {
	background-color: #e2e2e2;
}

.content {
	overflow-y: auto;
}

.content .panel {
	margin-bottom: 0;
}

.content .panel .panel-heading .panel-title {
	display: inline-block;
	margin-right: 15px;
	line-height: 34px;
}

.content .panel .panel-body .input-group {
	margin: 2px 3px;
}

.content .panel .panel-footer {
	height: 60px;
	line-height: 40px;
}

.content .table .panel-heading {
	display: flex;
	justify-content: space-between;
}

.content .table .panel-heading .panel-title {
	line-height: 34px;
}

/***************   滚动条样式  开始 ********************/

::-webkit-scrollbar {
	/*滚动条整体样式*/
	width: 8px;
	/*高宽分别对应横竖滚动条的尺寸*/
	height: 6px;
}

::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/
	border-radius: 10px;
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	background: #535353;
}

::-webkit-scrollbar-track {
	/*滚动条里面轨道*/
	-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
	border-radius: 10px;
	background: #EDEDED;
}

/***************   滚动条样式  结束 ********************/

/***************   上传图片item  开始 ********************/

.img-Upload-Flex {
	display: -webkit-flex;
	/* 容器显示属性 */
	-webkit-flex-wrap: wrap;
	/* 容器内元素可换行 */
	-webkit-justify-content: space-between;
	/* 间距平分 */
	padding-right: 6%;
}

.img-Upload-Flex .imgBlock {
	width: 150px;
	height: 150px;
	background: #4b4b4b;
	flex-wrap: wrap;
	margin: 15px 0 5px;
	overflow: hidden;
}

.img-Upload-Flex .imgBlock img {
	width: 100%;
	height: 100%;
}

.img-Upload-Flex:nth-child(2) {
	display: flex;
	/* 容器显示属性 */
	flex-wrap: wrap;
	/* 容器内元素可换行 */
	justify-content: left;
	/* 间距平分 */
}

.img-Upload-Flex:nth-child(2) .imgBlock {
	margin: 15px 6% 5px 0;
}

/***************   上传图片item  结束 ********************/

/***************   tabs切换部分  开始 ********************/

.nav-content>div {
	display: none;
}

.nav-content>div.active {
	display: block;
}

/***************   tabs切换部分  结束 ********************/

/***************   XGrid暂用CSS  开始 ********************/

.stripedTable tr:nth-child(odd) {
	background: #F9F9F9;
}

.XGridUI tr.hoverRow {
	background: #eff4f7;
}

.XGridUI tr.selRow {
	background: #99CCCC;
}

.XGridUI {
	border-collapse: collapse;
	font-size: 14px;
}

.XGridUI th,
.XGridUI td,
.XGridUI {
	padding: 0;
	margin: 0;
	border: 1px solid black;
}

.XGridUI th {
	line-height: 22px;
	background: #383D41;
	color: #ffffff;
	padding: 8px 3px;
	text-align: center;
}

.XGridUI td {
	padding: 8px 3px;
}

/*.XGridUI td > input[type=text] {
    width: 100%;
}*/

.XGridUI {
	min-width: 100%;
	table-layout: fixed;
	word-break: break-all;
}

#grid-pager #grid-pager_center td {
	padding: 5px;
}

/***************   XGrid暂用CSS  结束 ********************/

/***************   Xseach暂用CSS 开始 ********************/

* {
	padding: 0;
	margin: 0;
}

li {
	list-style: none;
}

/***************   Xseach暂用CSS 结束 ********************/

/* 虚线 */

.hr-dashed-line td {
	border-bottom: 1px dashed #ddd;
}

.word .ele {
	background: #ccc;
}

.word li:hover {
	background: #BCBCBC;
}

.imgcontain {
	display: -webkit-flex;
	flex-wrap: wrap;
}

.upload_Dialog .result img {
	width: 150px;
	height: 150px;
	border: 1px #ccc solid;
	display: block;
	transition: 4s all ease;
}

.upload_Dialog .result img:hover {
	transform: scale(1.1);
}

.upload_Dialog .viewImgItem span {
	width: 20px;
	height: 20px;
	background: #fff;
	border: 1px #ccc solid;
	border-radius: 50%;
	position: absolute;
	top: -10px;
	right: -8px;
	line-height: 18px;
	text-align: center;
	font-size: 16px;
	padding: 1px;
	font-weight: bolder;
	cursor: pointer;
	z-index: 2;
}

.upload_Dialog .imgbox {
	margin-bottom: 6px;
	overflow: hidden;
}

.viewImgItem {
	position: relative;
	width: 150px;
	height: 208px;
	margin: 0 10px 10px;
}

.viewImgItem:hover {
	box-shadow: 0 0 3px 6px rgba(240, 240, 240, .8);
	border-radius: 3px;
}

.imgTypeSel {
	width: 100%;
	height: 22px;
}

.viewImgName {
	margin-top: 6px;
}

.viewImgName input {
	width: 100%;
	height: 22px;
}

.upload_Dialog .imgFile {
	width: 150px;
	height: 150px;
	overflow: hidden;
	position: relative;
	background: url(/proxy-sysmanage/static/images/imgadd.png) no-repeat center;
	background-size: cover;
	margin: 0 10px;
}

.upload_Dialog .imgFile input {
	width: 100%;
	height: 100%;
}

.upload_Dialog .fileload {
	position: absolute;
	top: 0px;
	right: 0px;
	opacity: 0;
}

.upload_Dialog imgcontain {
	width: 100%;
}

.upload_Dialog {
	display: -webkit-flex;
	flex-wrap: wrap;
	min-height: 350px;
	max-height: 650px;
	padding-top: 10px;
	overflow-y: auto;
}

/* 输入框标签，仍需要修改*/

.adm {
	position: absolute;
	right: 0px;
	top: 0px;
	width: 34px;
	height: 35;
	height: 34px;
	display: block;
	z-index: 777;
	line-height: 34px;
	text-align: center;
	cursor: pointer;
}

.iwrap i {
	font-style: normal;
	margin-right: 15px;
	border: 1px solid #ddd;
	border-radius: 34px;
	padding: 0px 10px;
	position: relative;
}

.iwrap i:hover {
	background-color: #e2e2e2;
	cursor: pointer;
}

.iwrap i:hover:after {
	width: 10px;
	height: 10px;
	display: block;
	content: "x";
	color: #000;
	position: absolute;
	top: -11px;
	right: -10px;
}

/*  input 框内搜索按钮 */

.input-group .glyphicon-search {
	position: absolute;
	right: 15px;
	line-height: 34px;
	z-index: 9;
	cursor: pointer;
}

.s50fl {
	width: 49%;
	float: left;
	border: none;
}

.pr30 {
	padding-right: 35px;
}

/* 图片预览样式 */

#maskDiv {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	opacity: 0.7;
	z-index: 888;
	background: #000;
}

#viewImg {
	position: fixed;
	left: 50%;
	top: 10%;
	width: 80%;
	height: 80%;
	z-index: 999;
	margin-left: -40%;
	background: #fff;
}

#viewImg .leftBtn,
#viewImg .rightBtn {
	position: absolute;
	top: 0;
	z-index: 55;
	width: 40px;
	height: 100%;
	display: flex;
	align-items: center;
}

#viewImg .leftBtn {
	left: -50px;
}

#viewImg .leftBtn i {
	background: url("../images/pre.png") no-repeat;
}

#viewImg .rightBtn {
	right: -50px;
}

#viewImg .rightBtn i {
	background: url("../images/next.png") no-repeat;
}

#viewImg .leftBtn i,
#viewImg .rightBtn i {
	display: block;
	width: 40px;
	height: 100px;
	background-size: 100% 100%;
	cursor: pointer;
}

#imgList {
	position: relative;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

#imgList img {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	transition: 1s all;
}

/* jquery ui css bootstrap css 冲突 ，去掉jquery ui css,*/

.ui-autocomplete {
	float: left;
	background: #fff;
	z-index: 988;
	max-height: 300px;
	overflow: auto;
}

.ui-autocomplete .ui-menu-item {
	line-height: 45px;
	height: 45px;
	display: block;
	clear: both;
	padding: 0px 30px;
	cursor: pointer;
}

.ui-autocomplete .ui-menu-item:hover {
	background: #e2e2e2;
}

.strong_title:before {
	width: 3px;
	height: 14px;
	display: block;
	content: "";
	background: black;
	position: absolute;
	left: -20px;
	top: 10px;
}

.strong_title {
	line-height: 34px;
	height: 34px;
	position: relative;
	margin-left: 30px;
	font-weight: 900;
}

.lightBlue {
	color: #419bf9;
	line-height: 34px;
}

/************时间表************/

.Wdate.form-control.grid_date {
	height: 34px;
	border: 1px solid #ccc;
}

.table_wrap {
	width: 100%;
	overflow: auto;
}

.displaynone {
	display: none;
}

.danger {
	color: red;
}

/*根据UI图 更改bootstrap样式 */

.XGridUI th {
	background: rgba(243, 247, 249, .5);
	color: #526069;
	border: 1px solid #e4eaec;
}

.XGridUI td {
	border: 1px solid #e4eaec;
	height: 37px;
	overflow-x: hidden;
	text-align: center;
	min-width: 80px;
}

.content .panel {
	margin-bottom: 10px;
}

.panel-default {
	background: #FFFFFF;
	border-radius: 4px;
	border: none;
}

.panel-default>.panel-heading {
	background-color: #fff;
}

.panel-title {
	border-left: 4px;
}

.btn-info {
	background-color: #2DB7F5;
	border-color: #2DB7F5;
}

.btn-info:active {
	border-color: #2DB7F5;
}

/* 单选复选框 */

.checkbox label {
	vertical-align: bottom;
}

.checkbox input[type=checkbox],
.checkbox input[type=radio] {
	position: relative;
	width: 18px;
	height: 18px;
	vertical-align: middle;
	margin-top: -1px;
}

.checkbox input[type=checkbox]:before,
.checkbox input[type=radio]:before {
	position: absolute;
	left: 0;
	display: inline-block;
	width: 18px;
	height: 18px;
	content: "";
	background-color: #fff;
	border: 1px solid #e4eaec;
	border-radius: 3px;
	-webkit-transition: all .3s ease-in-out 0s;
	-o-transition: all .3s ease-in-out 0s;
	transition: all .3s ease-in-out 0s
}

.checkbox input[type=checkbox]:after,
.checkbox input[type=radio]:after {
	position: absolute;
	top: 0;
	left: 0;
	display: inline-block;
	width: 18px;
	height: 18px;
	padding-top: 1px;
	font-size: 12px;
	line-height: 18px;
	color: #76838f;
	text-align: center
}

.checkbox input[type=radio]:before,
.checkbox input[type=radio]:after {
	border-radius: 50%
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

@font-face {
	font-family: "iconfont";
	src: url('../fonts/iconfont.eot?t=1529656685481');
	/* IE9*/
	src: url('../fonts/iconfont.ttf?t=1529656685481') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
	url('../fonts/iconfont.svg?t=1529656685481#iconfont') format('svg');
	/* iOS 4.1- */
}

.checkbox input[type=checkbox]:checked::after,
.checkbox input[type=radio]:checked::after {
	font-family: "iconfont";
	content: "\e6f4";
}

.checkbox input[type=checkbox]:focus:before,
.checkbox input[type=radio]:focus:before {
	outline: thin dotted;
	outline: 5px auto -webkit-focus-ring-color;
	outline: 0;
	outline-offset: -2px
}

.checkbox input[type=checkbox]:checked::before,
.checkbox input[type=radio]:checked::before {
	border-color: #e4eaec;
	border-width: 1px;
	-webkit-transition: all .3s ease-in-out 0s;
	-o-transition: all .3s ease-in-out 0s;
	transition: all .3s ease-in-out 0s
}

.checkbox input[type=checkbox]:disabled,
.checkbox input[type=radio]:disabled {
	opacity: .65
}

.checkbox input[type=checkbox]:disabled::before,
.checkbox input[type=radio]:disabled::before {
	cursor: not-allowed;
	background-color: #f3f7f9;
	border-color: #e4eaec;
	border-width: 1px
}

.checkbox input[type=checkbox]:checked::before,
.checkbox input[type=radio]:checked::before {
	background-color: #62a8ea;
	border-color: #62a8ea
}

.checkbox input[type=checkbox]:checked::after,
.checkbox input[type=radio]:checked::after {
	color: #fff
}

/* 单选复选框end */

/* 去除input type number箭头 */

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
}

input[type="number"] {
	-moz-appearance: textfield;
}

/* demo css stoped here */

@-webkit-keyframes ball-spin-clockwise {
	0%,
	100% {
		opacity: 1;
		-webkit-transform: scale(1);
		transform: scale(1)
	}
	20% {
		opacity: 1
	}
	80% {
		opacity: 0;
		-webkit-transform: scale(0);
		transform: scale(0)
	}
}

@-moz-keyframes ball-spin-clockwise {
	0%,
	100% {
		opacity: 1;
		-moz-transform: scale(1);
		transform: scale(1)
	}
	20% {
		opacity: 1
	}
	80% {
		opacity: 0;
		-moz-transform: scale(0);
		transform: scale(0)
	}
}

@-o-keyframes ball-spin-clockwise {
	0%,
	100% {
		opacity: 1;
		-o-transform: scale(1);
		transform: scale(1)
	}
	20% {
		opacity: 1
	}
	80% {
		opacity: 0;
		-o-transform: scale(0);
		transform: scale(0)
	}
}

@keyframes ball-spin-clockwise {
	0%,
	100% {
		opacity: 1;
		-webkit-transform: scale(1);
		-moz-transform: scale(1);
		-o-transform: scale(1);
		transform: scale(1)
	}
	20% {
		opacity: 1
	}
	80% {
		opacity: 0;
		-webkit-transform: scale(0);
		-moz-transform: scale(0);
		-o-transform: scale(0);
		transform: scale(0)
	}
}

#nav-tab {
	width: calc(100% - 205px) !important;
	overflow: hidden;
}

#radio_area label {
	margin-right: 50px;
}

.ui-paging-pager td {
	display: block;
	float: left;
	cursor: pointer;
	margin-right: 15px;
	line-height: 45px;
}

.ui-paging-pager td:hover {
	color: #2DB7F5;
}

.left_title,
.left_title_blank {
	display: block;
	float: left;
	line-height: 34px;
	height: 34px;
	text-align: right;
	width: 110px;
	overflow: hidden;
	padding-right: 15px;
}

.flt_rt {
	float: right !important;
	width: calc(100% - 152px) !important;
	border-bottom-left-radius: 4px !important;
	border-top-left-radius: 4px !important;
}

.flx {
	display: flex;
}

.red {
	color: red;
	height: 34px;
	line-height: 34px;
	padding: 0 5px;
}

.autocomplete-suggestions {
	border-radius: 4px;
	border: 1px solid #ccc;
	background: #ffffff;
	overflow: auto;
}

.autocomplete-suggestion {
	padding: 6px 12px;
}

.autocomplete-selected {
	background: #66AFE9;
	color: #ffffff;
	cursor: pointer;
}

.autocomplete-suggestions strong {
	color: #FF6633;
}

.i-red {
	color: red;
	vertical-align: middle;
	margin-right: 4px;
}

.input-group-addon {
	background-color: #FFFFFF !important;
	border: none !important;
	width: 120px !important;
	text-align: right !important;
	float: left;
	display: block;
}

.input-group {
	width: 100% !important;
}

.input-group>.form-control {
	width: calc(100% - 125px) !important;
	float: right;
	display: block;
}

.input-group-addon,
.input-group-btn {
	height: 34px;
	padding: 0;
	line-height: 17px;
	white-space: normal !important;
	display: flex;
	justify-content: space-around;
	flex-direction: column;
}

.input-group .form-control {
	border-bottom-right-radius: 4px !important;
	border-bottom-left-radius: 4px !important;
	border-top-left-radius: 4px !important;
	border-top-right-radius: 4px !important;
}

.input-group .form-control {
	border-bottom-right-radius: 4px !important;
	border-bottom-left-radius: 4px !important;
	border-top-left-radius: 4px !important;
	border-top-right-radius: 4px !important;
}

td[role='gridcell'] .glyphicon-search {
	right: 20px;
	cursor: pointer;
}

.panel-body {
	/* overflow: auto; */
}

.text-require {
	color: red;
	position: absolute;
	right: 0;
}

.s50fl {
	width: 49%;
	float: left;
	border: none;
}

.form-group {
	margin-bottom: 0;
}

.distpicker.form-inline {
	padding: 0;
	position: relative;
}

.distpicker .form-group {
	vertical-align: 0;
}

.distpicker .form-group.btn-box {
	vertical-align: top;
}

.distpicker .form-group.btn-box .btn {
	background: #fff;
}

.distpicker {
	overflow: hidden;
}

.distpicker .row {
	margin-left: 0px;
}

.distpicker .col-md-2,
.distpicker .col-md-3 {
	padding-left: 0;
	padding-right: 0;
}

.distpicker select.form-control {
	border: none;
	height: 30px;
	border-right: 1px solid #ccc;
}

.text-inp {
	-width: 100px;
	height: 30px;
	border: none;
	border-right: 1px solid #ccc
}

.distpicker {
	height: auto;
}

.baseDataBuseScope {
	height: 100px;
	overflow: hidden;
	overflow-y: auto
}

/* 基础属性包装单位 */

.unit-tip {
	position: absolute;
	right: 8px;
	z-index: 99;
	top: 8px;
}

/* 修改tagsinput 处高度 */

.tagsinput.form-control {
	height: auto;
}

/* 商品首映申请页面查看按钮 */

.orgahistorys {
	position: absolute;
	right: 0;
	top: 0;
	z-index: 22;
}