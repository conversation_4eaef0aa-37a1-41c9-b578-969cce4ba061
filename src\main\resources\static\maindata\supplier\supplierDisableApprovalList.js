$(function () {
	//创建供应商列表
    $('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierDisableApprovalRecord/querySupplierDisableApprovalList",
        colNames: ['', '申请日期', '机构', '申请人ID', '申请人', '单据编号', '供应商编码', '供应商名称', '供应商类型','业务类型','审核状态','审核状态id'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true ,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden:true
        }, {
            name: 'applicationTime',
            index: 'applicationTime',
            width:100,
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'orgCode',
            index: 'orgCode',
            width:330
        }, {
            name: 'applicantId',
            index: 'applicantId',
            hidden:true
        }, {
            name: 'applicantName',
            index: 'applicantName',
            width:100
        }, {
            name: 'applicationCode',
            index: 'applicationCode',
            width:140
        }, {
            name: 'supplierCode',
            index: 'supplierCode',
            width:140
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width:220
        }, {
            name: 'supplierTypeName',
            index: 'supplierTypeName'
        },{
            name: 'supplierBusiType',
            index: 'supplierBusiType',
            formatter:function(value){
                if(value=='0'){
                    return '停用'
                }else if(value=='1'){
                    return '解除停用'
                }
            }
        },  {
            name: 'auditStatus',
            index: 'auditStatus',
            width:120,
            formatter: function (e) {
                if (e == '1') {
                    return '录入中'
                } else if (e == '2') {
                    return '审核中'
                }else if (e == '3') {
                    return '审核通过'
                }else if (e == '4') {
                    return '审核不通过'
                }
            }
        },  {
            name: 'auditStatus',
            index: 'auditStatus',
            hidden:true
        }
        ],
        rownumbers:true,
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {
    		 if(obj.auditStatus=="1"){
                 var loginUserId = $("#loginUserId").val();
                 if(obj.applicantId==loginUserId){
                	 var url = '/proxy-supplier/supplier/supplierDisableApprovalRecord/querySupplierDisableApprovalRecord?id='+id ;
             		 utils.openTabs("querySupplierDisableApprovalRecord", "供应商停用 ", url)	
                 }else{
                     utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                 }
             }else {
            	 var url = '/proxy-supplier/supplier/supplierDisableApprovalRecord/querySupplierDisableApprovalRecord?id='+id ;
         		 utils.openTabs("querySupplierDisableApprovalRecord", "供应商停用详情 ", url)	
             }
        },
        onSelectRow: function (id, dom, obj, index, event, obj) {
            console.log('单机行事件', id, dom, obj, index, event, obj);
        },
        pager: '#grid-pager'
    });
    /* 查询按钮事件 */
    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "orgCode": $("#orgCode").val(),
                "supplierName":$("#supplierName").val(),
                "auditStatus":$("#auditStatus").val(),
                "applicationCode":$("#applicationCode").val(),
            },page:1
        }).trigger('reloadGrid');
    });
});
function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }


var orgCode=$("#loginOrgCode").val();
if(orgCode=='001'){
    $.ajax({
        url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode="+orgCode,
        async : false,
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].orgCode+'">'+arr[i].orgName+'</option>';
                    }
                }
            }
            $("#orgCode").html(html);
        },
        error:function () {
        }
    });
}

// 删除草稿
$("#deleteDraftBtn").on("click", function () {
    let selRow = $('#X_Tableb').XGrid('getSeleRow');
    // 表格重新渲染 的参数
    let postData = {
        "orgCode": $("#orgCode").val(),
        "supplierName":$("#supplierName").val(),
        "auditStatus":$("#auditStatus").val(),
        "applicationCode":$("#applicationCode").val()
    }
    // supplierOrganBaseId：  删除草稿需要传递的参数
    let data = {
        supplierDisableId: selRow.length != 0 ? selRow[0].id : ''
    }
    let params = {
        statusName: selRow.length != 0 ? selRow[0].auditStatus : '',
        statusVal:'1',
        applicantId: selRow.length != 0 ? selRow[0].applicantId : '',
        loginUserId: $('#loginUserId').val(),
        url: '/proxy-supplier/supplier/supplierDisableApprovalRecord/deleteDraftsSupplierDisable'
    }
    // 表格id         //  表格中审核状态的字段     // 对应的录入中的状态值   // 删除草稿的url
    utils.deleteDraft('X_Tableb', params , data, postData)
});