var req_url_pre = "/proxy-sysmanage/sysmanage/purchaseOrderActiciti";
// 机构联想
$('#orgCode').Autocomplete({
	serviceUrl: req_url_pre + '/queryOrg',
	paramName: 'orgName',
	minChars: '0',
	maxHeight: '300',
	params: {},
	triggerSelectOnValidInput: false, // 必选
	showNoSuggestionNotice: true, //显示查无结果的container
	noSuggestionNotice: '查询无结果',
	dataReader: {
		'list': 'result',
		'value': 'orgName',
		'data': 'orgCode'
	},
	onSelect: function(result) {
		if (checkInsert(result.data)) {
			$('#org_code_checked').val(result.data);
		} else {
			// 隐藏弹出框
			dia.close();
		}
	},
	onSearchComplete: function(query, suggestions) {}
});

// 保存数据
function save(orgCode) {
	// 组装数据
	var reqDataInfoObj = checkReqDataInfo(orgCode);
	if (typeof reqDataInfoObj == "undefined" || reqDataInfoObj == null || reqDataInfoObj == "") {
		return;
	} else {
		if(reqDataInfoObj.entityVoList.length <= 0) {
			return;
		}
	}
	$.ajax({
		url: req_url_pre + "/save",
		type: "POST",
		dataType: "json",
		contentType: "application/json;charset=UTF-8",
		data: JSON.stringify(reqDataInfoObj),
		success: function(result) {
			// 提示消息
			utils.dialog({
				content: result.msg,
				quickClose: true,
				timeout: 2000
			}).showModal();

			// 刷新页面
			if (result.code == 0 && result.result) {
				setTimeout(function() {
					window.location.href = req_url_pre + "/index";
				}, 2500);
			}
		}
	});
};

// 组装保存请求数据
function checkReqDataInfo(orgCode) {
	// 返回对象
	var reqDataInfoObj = {};
	// 对象内集合
	var reqDataInfoArr = [];
	if (typeof orgCode == "undefined" || orgCode == null || orgCode == "") {
		// 获取全页面的数据, 组装成一个对象, 更新
		var len = $("#formlen form").length;
		for (var i = 0; i < len; i++) {
			// 集合内对象
			var reqDataInfo = {};
			// id
			var tpur = $("#formlen .fms" + i).find(".purchaseOrderActiciti").val();
			// 是否启用
			var tinp = $("#formlen .fms" + i).find("input[name='startbtn']:checked").val();
			// 商品APP售价控制
			var tchea = $("#formlen .fms" + i).find("input[name='startbtna']:checked").val();
			// 商品标准流速控制
			var tcheb = $("#formlen .fms" + i).find("input[name='startbtnb']:checked").val();
			// 商品连续涨价控制
			var tchec = $("#formlen .fms" + i).find("input[name='startbtnc']:checked").val();

			if (!tchea) {
				tchea = 0;
			};
			if (!tcheb) {
				tcheb = 0;
			};
			if (!tchec) {
				tchec = 0;
			};
			reqDataInfo.id = tpur;
			reqDataInfo.specialActivitiRule = tinp;
			reqDataInfo.productAppPriceCtr = tchea;
			reqDataInfo.productStandardVelocityCtr = tcheb;
			reqDataInfo.productContinueMarkupCtr = tchec;
			reqDataInfoArr.push(reqDataInfo);
		};
	} else {
		// 只组装 orgCode, 新增
		// 集合内对象
		var reqDataInfo = {
			orgCode: orgCode
		};
		reqDataInfoArr.push(reqDataInfo);
	}
	reqDataInfoObj.entityVoList = reqDataInfoArr;
	return reqDataInfoObj;
};

// 检测是否允许新增
function checkInsert(orgCode) {
	var bl = false;
	$.ajax({
		url: req_url_pre + "/checkInsert",
		type: "POST",
		async: false,
		data: {
			orgCode: orgCode
		},
		success: function(result) {
			if (result.code == 0 && result.result) {
				bl = result.result;
			} else {
				utils.dialog({
					content: result.msg,
					quickClose: true,
					timeout: 2000
				}).showModal();
				// 解除新建和保存
				$("#edit").removeAttr("disabled");
				$("#submitAssert").removeAttr("disabled");
			}
		}
	});
	return bl;
};

// 新建按钮
$("#create").click(function() {
	// 屏蔽信息和保存
	$("#edit").attr("disabled","disabled");
	$("#submitAssert").attr("disabled","disabled");

	// 赋值
	$("#org_code_checked").val("");
	$("#orgCode").val("");
	// 弹框
	dia = dialog({
		content: $("#create_dia"),
		title: '新建采购订单审批流程管理',
		width: 389,
		height: 96,
		data: 'val值', // 给modal 要传递的 的数据
		onclose: function() {},
		cancelValue: '取消',
		cancel: function() {
			// 解除新建和保存
			$("#edit").removeAttr("disabled");
//			$("#submitAssert").removeAttr("disabled");
		},
		okValue: "确定",
		ok: function() {
			if (checkInsert($('#org_code_checked').val())) {
				save($('#org_code_checked').val());
			};
		}

	}).show();
})

// 保存按钮
$("#submitAssert").click(function() {
	var d = dialog({
		title: '提示',
		content: '确认提交修改信息？',
		okValue: "确定",
		ok: function() {
			save();
		},
		onclose: function() {},
		cancelValue: '取消',
		cancel: function() {

		}
	});
	d.show();
})

$('input[name="startbtn"]').click(function() {
	var cls = $(this).attr("class");
	var indx = cls.replace("input", "");				
	var vl = parseInt($(this).val());
	if (vl === 0) {
		$(".checkbox" + indx).prop("checked", false);

		$(".checkbox" + indx).prop("disabled", "disabled");

	} else {
		$(".checkbox" + indx).removeAttr("disabled");
		$(".checkbox" + indx).prop("checked", false);

	}
})

// 编辑按钮
$("#edit").click(function() {
	// 屏蔽新建
	$("#create").hide();
	$("#cancel").show();
	$("#submitAssert").removeAttr("disabled");
	var len = $(".table form").length;
	for (var i = 0; i < len; i++) {
		$(".input" + i).removeAttr("disabled");
		var tinp = $("#formlen .fms" + i).find("input[name='startbtn']:checked").val();
		if (tinp != "0") {
			$(".checkbox" + i).removeAttr("disabled");
		}
	}
});

//取消按钮
$("#cancel").click(function() {
	// 屏蔽新建
	$("#create").show();
	$("#cancel").hide();
	$("#submitAssert").attr("disabled", "disabled");
	window.location.href = req_url_pre + "/index";
});
