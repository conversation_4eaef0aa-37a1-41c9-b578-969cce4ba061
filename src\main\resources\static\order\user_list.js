$(function () {
    /* 获取dialog上层实例 */
    var dialog = parent.dialog.get(window);
    if (dialog) {
        var dialog_data = dialog.data;
        $('#search_vl').val(dialog_data);
    }

    $('#X_Table').XGrid({
        url:"/proxy-customer/customer/customerBaseAppl/pageList",
        colNames: ['客户编码', '客户名称', '联系人', '联系电话', '收货地址'],
        colModel: [{ name: 'customerCode',      index: 'customerCode',      key: true,    },
            {      name: 'customerName',      index: 'customerName',    },
            {      name: 'responsiblePersons',      index: 'responsiblePersons',    },
            {      name: 'salesmanTel',      index: 'salesmanTel',    },
            {      name: 'registerAddress',      index: 'registerAddress',    }],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        altRows: true, //设置为交替行表格,默认为 false
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            dialog.close(obj);
        },
        pager: '#grid-pager',
    });

    /* 查询 */
    $('#search_list').on('click', function () {
        $('#X_Table').setGridParam({
            url:"/proxy-customer/customer/customerBaseAppl/pageList?customerCode=" + $('#search_vl').val(),
        }).trigger('reloadGrid');
    })



    // 去掉所有input的autocomplete, 显示指定的除外
    /* $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete',
      'off'); */

    /* 模糊搜索 */
    var countries = [{
        value: 'Andorra',
        data: 'AD'
    },
        {
            value: 'Zimbabwe',
            data: 'ZZ'
        }
    ];

    $('#search_vl').Autocomplete({
        serviceUrl: '/proxy-customer/customer/customerBaseAppl/pageList', //异步请求
        // paramName: 'query111',//查询参数，默认 query
        dataType: 'json',
        //lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        /*  dataReader:{
              'value':'manufactoryName',
              'data':'manufactoryId',
               'xxt':"name"
          },*/
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.each(response, function (idnex, dataItem) {
                    return {
                        value: dataItem,
                        data: dataItem
                    };
                })
            };
        },
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            alert('You selected: ' + result.value + ', ' + result.data + ',' + result.xxt);
            // console.log('选中回调')
        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            // console.log(query, suggestions);
            // if (suggestions.length < 1) {
            //     utils.dialog({
            //         title: '查询无结果',
            //         content: '是否新增生产厂家？',
            //         width: '300',
            //         okValue: '确认',
            //         ok: function () {
            //             this.title('提交中…');
            //             return false;
            //         },
            //         cancelValue: '取消',
            //         cancel: function () {
            //             $('input').val('')
            //         }
            //     }).show();
            // }


        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });


})