$(function () {
	var orgCode = $("#orgCode").val();
	//供应商类别
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":8}},"supplierTypeId",{data:"id",value:"name",numCode:"numCode"},function (result) {
    	$("#supplierTypeShortName").val(result.numCode);

        //2018.8.31 18:47,rl,供应商类别为"生产"时，客户委托书授权类型新增剂型，begin
        toggleSupplierDrug(result.numCode);
        //2018.8.31 18:47,rl,供应商类别为"生产"时，客户委托书授权类型新增剂型，end

        // 当供应商类别为生产时，对应生产厂商相关操作 -------(暂时隐藏)--------
        // if(result.data === 55){
        //     // 没有填写对应生产厂商再重置
        //     if(!$('#manufacturer_row input').filter(function (index,item) { return $(item).val() }).length){
        //         $("#manufacturer_row").html(`<div class="col-md-6 ManufactoryList">
        //                             <div class="input-group">
        //                                 <div class="input-group-addon "><i class="text-require">*  </i>对应生产厂商</div>
        //                                 <div class="form-control form-inline distpicker">
        //                                     <div>
        //                                         <div class="form-group col-md-11">
        //                                             <input type="hidden" id="Manufactory" disabled="disabled"  autocomplete="off"  name="Manufactory"/>
        //                                             <input type="text" class="form-control text-inp Filter_SpaceAndFiveStrLen_Class {validate:{ required :true}}" changeApplyFlag="supplierManufactoryVOList" id="ManufactoryVal" name="ManufactoryVal"/>
        //                                         </div>
        //                                         <div class="form-group btn-box col-md-1">
        //                                             <button type="button" class="btn btn_addManufacturer">
        //                                                 <span class="glyphicon glyphicon-plus" ></span>
        //                                             </button>
        //                                         </div>
        //                                     </div>
        //                                 </div>
        //                             </div>
        //                         </div>`)
        //     }
        //     //$("#manufacturer_row input[type=text]").class("form-control  text-inp Filter_SpaceAndFiveStrLen_Class {validate:{ required :true}}");
        // }else {
        //     $("#manufacturer_row").html(`<div class="col-md-6 ManufactoryList">
        //                             <div class="input-group">
        //                                 <div class="input-group-addon ">对应生产厂商</div>
        //                                 <div class="form-control form-inline distpicker">
        //                                     <div>
        //                                         <div class="form-group col-md-11">
        //                                             <input type="hidden" id="Manufactory" disabled="disabled"  autocomplete="off"  name="Manufactory"/>
        //                                             <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class" changeApplyFlag="supplierManufactoryVOList" id="ManufactoryVal" name="ManufactoryVal" disabled="disabled"/>
        //                                         </div>
        //                                         <div class="form-group btn-box col-md-1">
        //                                             <!--<button type="button" class="btn btn_addManufacturer">
        //                                                 <span class="glyphicon glyphicon-plus" ></span>
        //                                             </button>-->
        //                                         </div>
        //                                     </div>
        //                                 </div>
        //                             </div>
        //                         </div>`)
        // }
        // var _id = 'Manufactory';
        // utils.valAutocomplete("/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},_id,
        //     {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});


    });

    //币种
//    valAutocomplete("/dict/queryCurrency",{paramName:'currencyName'},"currencyId",{data:"currencyId",value:"currencyName"});



    var pageProperty = $("#pageProperty").val();//供应商申请页面
    var addReturnPageFlag = $("#addReturnPageFlag").val();//标识是首营申请入口还是点击选择商品的新增入口
    var useSupplier = $("#useSupplier").val();//引用主数据

    if("insert" == pageProperty){
    	//批准文件
        localBatchName();

    }


    var jyfw_checkValObj = [];
    function zTreeOnCheck(event, treeId, treeNode){
		console.log(event, treeId);
		console.log(treeNode);
	}


    //客户委托书对应的全量的经营范围
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommodityscope",{paramName:'scopeName', params:{"orgCode":"001"}},"scopeOfOperation",{data:"simpleCode",value:"scopeName"}
      ,function (result) {
        // localBatchName(result.data);
    });

    //加载字典值
    showDictValue();
    /**
     *RM 2018-11-23
     * 页面渲染完获取初始数据，
     * 设置定时器是因为，如果页面进来就获取的话，对于地址三四级联动的话，地址的信息取到的都为空，
     * 用定时器的话取值正常
     */
    setTimeout(function () {
        var initBaseDataObj = $('#supplierBaseDTO').serializeToJSON(); // 基础属性数据
        var initOperateDataObj = $('#yyForm').serializeToJSON(); // 运营属性数据
        var initZLBZDataObj = $('#table1').getRowData(); //质量保证协议
        var initKHWTDataObj = $('#table2').getRowData();  //客户委托书
        var initPZWJDataObj = $('#table3').getRowData();  //批准文件
        var initNDBGDataObj = $('#table4').getRowData();  //年度报告
        var initQTFJDataObj= $('input[id^=newother]'); // 其他附件 filelist
        var otherChecks = $('#otherFileBox').find('input[type=checkbox]:checked'); // 其他附件checkbox
        var otherChecksArr = [];// 其他附件选中项名称
        //初始的注册地址仓库地址。现在获取是因为之后省份切换的话是没法通过编码拿到 市县的对应的汉字
        var regOptsTxt = ($('[name=registerProvince]').find('option:selected').text()?$('[name=registerProvince]').find('option:selected').text():'')
                        + ($('[name=registerCity]').find('option:selected').text()?$('[name=registerCity]').find('option:selected').text():'')
                        + ($('[name=registerArea]').find('option:selected').text()?$('[name=registerArea]').find('option:selected').text():'')
                        + ($('[name=registerDetail]').val()?$('[name=registerDetail]').val():'');
        var repOptsTxt = '';
        var repSelects = $('[name^=repertoryProvince]');
        $(repSelects).each(function (i,v) {
            var _repTxt = '';
            var repProTxt = $('[name^=repertoryProvince]').eq(i).find('option:selected').text();
            var repCityTxt = $('[name^=repertoryCity]').eq(i).find('option:selected').text();
            var repAreaTxt = $('[name^=repertoryArea]').eq(i).find('option:selected').text();
            var repStreetTxt = $('[name^=repertoryDetail]').eq(i).val();
            _repTxt = ((repProTxt?repProTxt:'')+(repCityTxt?repCityTxt:'')+(repAreaTxt?repAreaTxt:'')+(repStreetTxt?repStreetTxt:'')+(repProTxt == '请选择'?repProTxt:','));
            repOptsTxt += _repTxt //(i>0?(_repTxt+','):_repTxt);
        })
        $(otherChecks).each(function (i,v) {
            var obj = {};
            obj.name = $(this).next().text();
            obj.checked = true
            otherChecksArr.push(obj);
        })

        window.changeBefore = {
            initBaseDataObj:initBaseDataObj,
            initOperateDataObj:initOperateDataObj,
            initZLBZDataObj:initZLBZDataObj,
            initKHWTDataObj:initKHWTDataObj,
            initPZWJDataObj:initPZWJDataObj,
            initNDBGDataObj:initNDBGDataObj,
            initQTFJDataObj:initQTFJDataObj,
            initOtherChecked:otherChecksArr,
            initRegOptsTxt:regOptsTxt,
            initRepOptsTxt:repOptsTxt.substring(0, repOptsTxt.lastIndexOf(','))
        };
    },3000)

    //2018.9.10,RL,bug3016,begin
    if(window.location.href.indexOf('=rturnAddPage')>-1 || window.location.href.indexOf('selfPageEdit=Y')>-1){
        var radioesIsStopUse=$("[name='disableStateBase']");//是否停用
        if(radioesIsStopUse && radioesIsStopUse.length){
            radioesIsStopUse.attr("disabled",true);
            $.each(radioesIsStopUse,function(i,v){
                /**
                 * 2019-03-11
                 * && 隐藏域supplierBaseId 的值为空时 才置为 radio 否，否则根据数据的值显示radio 是
                 */
                if($(v).val()=="0" && $('#supplierBaseId').val() == '' ){
                    $(v).attr("checked",true);
                }
            });
        }
    }
    //2018.9.10,RL,bug3016,end


    //2018.9.13,RL,begin
    // initProvinceInputSearch("registeredAddress");//注册地址
    // initProvinceInputSearch("warehouseAddress","load");//仓库地址
    //2018.9.13,RL,end
    let selfEdit = $('#selfEdit').val(), editAddress = $('#editAddress').val(), _has = ($('#selfEdit').length > 0);
    // 注册地址
    let addressSelIdObj = [
        {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvince',nextNodeId: 'province1'},
        {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCity',nextNodeId: 'registerCity'},
        {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerArea',nextNodeId: 'district1'}
    ];
    let registerPromiseArray = [];
    utils.setAllProDom('#provinceSel_wrap', addressSelIdObj, '#registerBox',true, function () {
        // 注册地址有值回显
        let _registerHiddenVal = eval($('#registerAddressJson').val());
        if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
        _registerHiddenVal.splice(_registerHiddenVal.length - 1);
        if (_has) {
            if ( selfEdit != 'Y' || editAddress != 'true') {
                $('#' + addressSelIdObj[0]['nextNodeId']).prop('disabled', true);
            }else{
                if ($('#resentBtn').css('display') == 'inline-block' || $('#saveRowData').css('display') == 'inline-block') { //  没有办法了才加这么个if这么写。 逻辑已经找不回来了。。。后面接手的大神多担待！！！
                    if (!$('[name=accountName]').prop('readonly')){
                        $('#registerBox select,#registerBox input').removeAttr('disabled readonly');
                    }else{
                        $('#registerBox select,#registerBox input').prop('readonly',true);
                    }
                }else{
                    $('#registerBox select,#registerBox input').prop('readonly',true);
                }
            }
        }
        $('#' + addressSelIdObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
        for (let i = 1; i < _registerHiddenVal.length; i++) {
            registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
        }
        Promise.all(registerPromiseArray).then(data => {
            for (let i = 0; i < data.length; i++) {
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).html(data[i]);
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                if (_has) {
                    if (selfEdit != 'Y' || editAddress != 'true') {
                        $('#' + addressSelIdObj[i + 1]['nextNodeId']).prop('disabled', true);
                    }else{
                        if ($('#resentBtn').css('display') == 'inline-block' || $('#saveRowData').css('display') == 'inline-block') {
                            if (!$('[name=accountName]').prop('readonly')){
                                $('#registerBox select,#registerBox input').removeAttr('disabled readonly');
                            }
                        }else{
                            $('#registerBox select,#registerBox input').prop('readonly',true);
                        }
                    }
                }
            }
        })
    });
    // 仓库地址
    let storgeAddressSelIdObj = []
    let _storgeHiddenVal = eval($('#repertoryAddressJson').val());
    if (!_storgeHiddenVal) _storgeHiddenVal = [['','','','']];
    let _storgeHiddenValArr =  eval($('#repertoryAddressJson').val());
    if (!_storgeHiddenValArr) _storgeHiddenValArr = [['','','','']];
    $(_storgeHiddenValArr).each((index,item) => {
        item.splice(item.length - 1);
    });
    let obj = distpickerHTML(_storgeHiddenValArr.length);
    $(obj.radomInit).each((index, item) => {
        let _arr = [
            {nextNodeWrap: '#stoProvinceSel_wrap_' + item,nextNodeName: 'repertoryProvince_' + item,nextNodeId: 'repertoryProvince_' + item},
            {nextNodeWrap: '#stoCitySel_wrap_' + item,nextNodeName: 'repertoryCity_' + item,nextNodeId: 'repertoryCity_' + item},
            {nextNodeWrap: '#stoDistrictSel_wrap_' + item,nextNodeName: 'repertoryArea_' + item,nextNodeId: 'repertoryArea_' + item}
        ]
        storgeAddressSelIdObj.push(_arr)
    });
    $('#depotAddress').html(obj.html)
    $(obj.radomInit).each((index, item) => {
        let storagePromiseArray = [];
        utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[index], storgeAddressSelIdObj[index], '#storageBox_' + obj.radomInit[index], true,function () {
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(_storgeHiddenValArr[index][0]);
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[index][3]);
            if (_has) {
                if (selfEdit != 'Y' || editAddress != 'true') {
                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').prop('disabled', true);
                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('.btn').css('display','none');
                }else{
                    if ($('#resentBtn').css('display') == 'inline-block' || $('#saveRowData').css('display') == 'inline-block') {
                        if (!$('[name=accountName]').prop('readonly')){
                            $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
                            $('#depotAddress .btn').css('display','inline-block');
                        }
                    }
                }
            }
            for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
                storagePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenValArr[index][ind-1]));
            }
            let allSelArr = storgeAddressSelIdObj[index].flat().map((item, index) => {
                if (index != 0) {
                    return item['nextNodeId']
                }
            }).filter(item => {
                return item
            });
            Promise.all(storagePromiseArray).then(data => {
                for (let i = 0; i < data.length; i++) {
                    $('#' + allSelArr[i]).html(data[i]);
                    $('#' + allSelArr[i]).val(_storgeHiddenValArr[index][i + 1]);
                    if (_has) {
                        if (selfEdit != 'Y' || editAddress != 'true') {
                            $('#' + allSelArr[i]).prop('disabled', true);
                        }else{
                            if ($('#resentBtn').css('display') == 'inline-block' || $('#saveRowData').css('display') == 'inline-block') {
                                if (!$('[name=accountName]').prop('readonly')){
                                    $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
                                    $('#depotAddress .btn').css('display','inline-block');
                                }
                            }
                        }
                    }
                }
                let addReturnPageFlag= $('#addReturnPageFlag').val();
                if (addReturnPageFlag == 'rturnAddPage') {
                    $('#registerAddress select,#registerAddress input').removeAttr('disabled readonly');
                    $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
                    $('#depotAddress .btn').css('display','inline-block');
                }
            })
        })
    })

    const buyerSelect = function(result) {
        //如果当前选择的采购员在已选择采购员列表中则过滤掉
        if (buyerSelectList.some(item => item.data === result.data)) {
            buyerSelectList = buyerSelectList.filter(item => {
                return item.data != result.data
            })
        } else {
            //否则则添加
            buyerSelectList.push(result)
        }
        $("#buyerId").val(buyerSelectList.map(item => item.data).join(","));
        $("#buyerIdVal").val(buyerSelectList.map(item => item.value).join(","));
        /* buyerSelectList = buyerSelectList.filter(item => {
            return item.data != result.data
        }) */
    }

    //供应商采购员
    valAutocompleteWithUser("/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode",{paramName:'userName', params:{"orgCode":orgCode}},"buyerId",{data:"id",value:"userName"}, true, buyerSelect, () => {
        buyerSelectList = [];
    });


    function valAutocompleteWithUser(url,param,obj,resParam, multi, select,noneSelect) {
        var resParam=Object.assign({'list':'result'},resParam);
        $("#"+obj+"Val").Autocomplete({
            serviceUrl: url, //异步请求
            paramName: param.paramName,//查询参数，默认 query
            params:param.params || {},
            dataType: 'json',
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            dataReader:resParam,
            multi: multi,   //多选
            triggerSelectOnValidInput: false, // 必选
            showNoSuggestionNotice: true, //显示查无结果的container
            noSuggestionNotice: '查询无结果',//查无结果的提示语
            onSelect: select,
            onNoneSelect: function (params, suggestions) {
                console.log(params, suggestions);
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
                noneSelect && noneSelect();
            }
        });
    }
    


});

//2018.9.13,RL,仓库地址,可新增多个,省的模糊搜索,begin
function initProvinceInputSearch(flag,type){

    /*
    * flag
    * registeredAddress:注册地址 warehouseAddress:仓库地址
    *
    * type
    * load：页面加载 add:新增地址栏
    *
    */

    var provinceInputSearch;//省input集合

    if(flag=="registeredAddress"){//注册地址

        provinceInputSearch=$(".registeredAddressProvinceInputSearch");

    }else if(flag=="warehouseAddress"){//仓库地址

        if(type=="load"){
            /**
             *  RM：2018-09-28
             *   #depotAddress  改为#supplierBaseDTO
             *   depotAddress 为仓库地址输入框父级的ID 值
             *   supplierBaseDTO 为基础属性form 的ID 值
             *   这样替换的原因是，由于产品要求页面结构按照原型修改。所以新增的仓库地址 就脱离了原来的#depotAddress 的结构范围，
             *   这样在新增之后省级下拉框无法调出
             */
            provinceInputSearch=$("#supplierBaseDTO .warehouseAddressProvinceInputSearch");//绑定所有
        }else if(type=="add"){
            provinceInputSearch=$("#supplierBaseDTO .depotList:last .warehouseAddressProvinceInputSearch");//绑最后append的那个dom
        }
    }

    if(provinceInputSearch && provinceInputSearch.length){
        $.each(provinceInputSearch,function(i,v){
            valAutocompleteByMatchId({
                url:"/proxy-sysmanage/sysmanage/area/findAreaListByKeyWord",
                param:{paramName:'keyWord', params:{"type":1,"keyWord":$(v).val()}},
                obj:$(v),
                resParam:{value:"name",data:"code",pcode:"pcode"},
                select:function(result){
                    // console.log(result);
                    $(v).siblings("select").val(result.data).trigger("change");
                },
                noneSelect:function(){
                    $(v).siblings("select").val("").trigger("change");
                }
            });
        })
    }
}
//2018.9.13,RL,仓库地址,可新增多个,省的模糊搜索,end



/**
 * 2018.8.31 19:46,rl
 * 供应商类别为"生产"时，客户委托书授权类型新增剂型
 * @param numCode
 */
function toggleSupplierDrug(numCode){
    var SC_option_template=$("#grantType option.optDrugFlag");//改模版dom
    var SC_option_table2=$("#table2 [id^='grantType_'] option.optDrugFlag" );//改当前页面已经存在的dom
    if(numCode=='SC'){
        // SC_option_template?SC_option_template.css({"display":'block'}):void 0;
        // SC_option_table2?SC_option_table2.css({"display":'block'}):void 0;
        // 对应生产厂家
        // if($('#selfEdit').val() == 'Y'){
        //     $('.ManufactoryList').eq(0).find('input[type=text]').prop('disabled',false);
        //     $('.ManufactoryList').eq(0).find('#btn_addManufacturer').prop('disabled',false);
        //     $('.ManufactoryList').each((index,item) => {
        //         $(item).find('input[type=text]').addClass(' {validate:{ required :true}}');
        //         $(item).find('.input-group-addon').html('<i class="text-require">*  </i>对应生产厂家')
        //     })
        // }

    }else{
        SC_option_template?SC_option_template.css({"display":'none'}):void 0;
        SC_option_table2?SC_option_table2.css({"display":'none'}):void 0;

        $('.ManufactoryList').not(':first').remove();
        $('.ManufactoryList').eq(0).find('input[name^=Manufactory]').val('');
        $('.ManufactoryList').eq(0).find('input[type=text]').val('');
        $('.ManufactoryList').eq(0).find('input[type=text]').prop('disabled',true);
        $('.ManufactoryList').eq(0).find('#btn_addManufacturer').prop('disabled',true);
        $('.ManufactoryList').each((index,item) => {
            $(item).find('input[type=text]').removeClass(' {validate:{ required :true}}');
            $(item).find('.input-group-addon').html('对应生产厂家')
        })

        //更改已经在select中的值
        var SC_option_select=$("#table2 [id^='grantType_'] select");
        if(SC_option_select && SC_option_select.length){
            $.each(SC_option_select,function(i,v){
                var $v=$(v);
                if($v.val()==$v.find('option.optDrugFlag').val()){
                    $v.val("");
                    $v.parents('tr').find("td[row-describedby=authrange] a").html('');
                }
            });
        }

        //2018.9.3 11:05,RL,清空保存在tr中的值
        var SC_option_drug_tdData=$("#table2 td[row-describedby='supplierClientProxyTypeVOList']");
        if(SC_option_drug_tdData && SC_option_drug_tdData.length){
            $.each(SC_option_drug_tdData,function(i,v){
                $(v).html(JSON.stringify([]));
            })
        }
    }
    //供应商类别关联经营范围
    baseDataBuseScopeOrScopes();
}


/**
 * 加载批准文件对应的证书类型
 * @param simpleCode
 */
function localBatchName() {
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/queryproviderbatchnamebytype",
        async : false,
        dataType:"json",
        success: function (data) {
            console.log(data)
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].batchId+'">'+arr[i].batchName+'</option>';
                    }
                }
            }
            $("select[name='certificateId']").html(html);
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}


//2018.9.12,RL,省市区,begin
function valAutocompleteByMatchId(optionObj) {
    var resParam=Object.assign({'list':'result'},optionObj.resParam);
    optionObj.obj.Autocomplete({
        serviceUrl: optionObj.url, //异步请求
        paramName: optionObj.param.paramName,//查询参数，默认 query
        params:optionObj.param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            optionObj.obj.val(result.value);
            optionObj.select && optionObj.select(result);
            // console.log(optionObj.obj);
        },
        onNoneSelect: function (params, suggestions) {
            // console.log(params, suggestions);
            optionObj.obj.val("");
            optionObj.noneSelect && optionObj.noneSelect();
        }
    });
}
//2018.9.12,RL,省市区,end


/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            $("#"+obj).val(result.data);
            select && select(result)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            $("#"+obj).val("");
            $("#"+obj+"Val").val("");
            noneSelect && noneSelect();
        }
    });
}




/**
 * 字典值回显
 */
function  showDictValue() {
    //供应商类别
    showComValue("supplierTypeId","1020");
    //币种
    showComValue("currencyId","1008");
    //采购员
    loadUserName("buyerId",null);
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showComValue(obj,type) {
    var id =$("#"+obj).val();
    if(id && id!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querybeanbytype?type='+type+"&id="+id,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                console.log(data);
                if(data.code == 0)
                {
                    $("#"+obj+"Val").val(data.result);
                    $("#"+obj+"Val").attr("data-value",data.result);

                     //2018.8.31 19:46,rl,供应商类别为"生产"时，客户委托书授权类型新增剂型,begin
                    if(obj=="supplierTypeId"){
                        toggleSupplierDrug("SC");
                    }
                    //2018.8.31 19:46,rl,供应商类别为"生产"时，客户委托书授权类型新增剂型,end

                }
            }

        })
    }
}


function loadUserName(key,orgaKey) {
    var userId=$("#"+key).val();
    if(userId==""){
        return;
    }
    $.ajax({
        type:"get",
        url: "/proxy-sysmanage/sysmanage/system/queryUserInfoById",
        data:{"userId":userId},
        dataType:"json",
        success: function (data) {
            console.log(data.result);
            if(data.code==0&&data.result!=null){
                var userName=data.result.userName;
                $("#"+key+"Val").val(userName);
                $("#"+key+"Val").attr("data-value",userName);
            }
        },
        error:function () {
        }
    });
}


// 当供应商类别为生产(55)时，经营范围 隐藏，展示 生产/经营范围
function baseDataBuseScopeOrScopes() {
    var supplierTypeId = $("#supplierTypeId").val();
    if(supplierTypeId == 55){
        $("#baseDataBuseScope").parents('.row').hide().find('#scopes').removeClass('{validate:{ required :true}}');
        $("#scopes").parents('.row').show().find('#scopes').addClass('{validate:{ required :true}}');
    }else {
        $("#baseDataBuseScope").parents('.row').show().find('#scopes').addClass('{validate:{ required :true}}');
        $("#scopes").val('').parents('.row').hide().find('#scopes').removeClass('{validate:{ required :true}}');
    }
}
baseDataBuseScopeOrScopes();
