$(function () {
  /* 参数,页面传递的数据 */
  var url = location.search;
  var param = z_utils.parseParams(url);
  console.log(url, param);

  /* 填入初始数据 */
  $('#form_a').JSONToform({
    val_a: '举个栗子'
  })

  /* 合计计算 */
  var totalTable = z_utils.totalTable;

  var colName = ['商品编码', '商品名称', '商品规格', '生产厂家', '商品产地', '单位', '剂型', '库房名称', '商品批号', '生产日期', '有效期至', '数量',
    '翻垛', '外观', '除湿', '加湿', '升温', '降温', '通风', '除尘','养护原因', '是否异常',  '养护结论', '质管员', '复查结果', '批准文号',  '备注'
  ];
  var colModel = [{
    name: 'productCode',
    index: 'productCode'
  }, {
    name: 'productName',
    index: 'productName'
  }, {
    name: 'specifications',
    index: 'specifications'
  }, {
    name: 'manufacturerVal',
    index: 'manufacturerVal'
  }, {
    name: 'producingArea',
    index: 'producingArea'
  }, {
    name: 'packingUnitVal',
    index: 'packingUnitVal'
  }, {
    name: 'dosageFormVal',
    index: 'dosageFormVal'
  }, {
    name: 'wareHouse',
    index: 'wareHouse',
      formatter: function (e) {
          if (e == '1') {
              return '合格库'
          } else if (e == '2'){
              return '不合格库'
          }else{
            return '暂存库'
          }
      }
  }, {
    name: 'productBatch',
    index: 'productBatch'
  }, {
    name: 'productionDate',
    index: 'productionDate',
      formatter:function (e){
        if (e != null && e !="") {
            return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
        } else {
            return e;
        }
    }
  }, {
    name: 'validDate',
    index: 'validDate',
      formatter:function (e){
          if (e != null && e !="") {
              return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
          } else {
              return e;
          }
      }
  }, {
    name: 'storage',
    index: 'storage'
  }, {
    name: 'repile',
    index: 'repile',
    rowtype: '#text14_e',
  }, {
    name: 'appearance',
    index: 'appearance',
    rowtype: '#text14_e',
  }, {
    name: 'dehumidification',
    index: 'dehumidification',
    rowtype: '#text14_e',
  }, {
    name: 'humidification',
    index: 'humidification',
    rowtype: '#text14_e',
  }, {
    name: 'warming',
    index: 'warming',
    rowtype: '#text14_e',
  }, {
    name: 'cooling',
    index: 'cooling',
    rowtype: '#text14_e',
  }, {
    name: 'ventilate',
    index: 'ventilate',
    rowtype: '#text14_e',
  }, {
    name: 'dedusting',
    index: 'dedusting',
    rowtype: '#text14_e',
  }, {
    name: 'checkStatus',
    index: 'checkStatus'
  }, {
          name: 'checkStatus',
          index: 'checkStatus'
      }, {
    name: 'checkCause',
    index: 'checkCause'
  }, {
    name: 'conclusion',
    index: 'conclusion'
  }, {
    name: 'scalerUserid',
    index: 'scalerUserid'
  }, {
    name: 'scalerConclusion',
    index: 'scalerConclusion'
  }, {
    name: 'indeterminacySize',
    index: 'indeterminacySize'
  }, {
    name: 'approvalNumber',
    index: 'approvalNumber'
  }, {
    name: 'remark',
    index: 'remark'
  }];
  $('#table_a').XGrid({
    //data: grid_dataY,
      url:'/proxy-gsp/gsp/stockInCheck/getPlanVoDeatil',
      colNames: colName,
    colModel: colModel,
    rownumbers: true,
    key: 'text1',
      rowNum: 20,
      rowList:[20,50,100],
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {
 
    },
    gridComplete: function () {},
    onSelectRow: function (id, dom, obj, index, event) {
      //选中事件
      //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
      //console.log(id, dom, obj, index, event)
    }
  });


  /* 筛选列 */
  $("#set_tb_rows").click(function () {
    //获取当前显示表格
    var tableId = $('#nav_content .active table').attr('id');
    $('#' + tableId).XGrid('filterTableHead');
  })

  /* 编辑 */
  //判断是否为已驳回状态
  var indent_type = '已驳回';
  if (indent_type == '已驳回') {
    $('#editRowData').show();
  }
  $('#editRowData').on('click', function () {
    var data = $('#table_a').XGrid('getSeleRow');
    if (!data) {
      utils.dialog({
        title: "提示",
        content: "请先选中一行数据"
      }).show();
      return
    }
    window.location = 'return_receive_edit.html';
  });

  /* 返回 */
  $('#goback').on('click', function () {
    window.history.back(-1);
  });


    /* 导出 */
    $('#exportRowData').on('click', function () {
        window.location = '/proxy-gsp/gsp/stockInCheck/getInStockDetail';
    });

})