function approvalInputValidation(ApprovalTableData,supplierType, pageType) {
    let msg = ''
    ApprovalTableData  = ApprovalTableData ? ApprovalTableData :  $('#table3').XGrid('getRowData')
    let supplierClientProxyOrderList = window['changeApplyList'] && window['changeApplyList']['supplierClientProxyOrderList']&&  window['changeApplyList']['supplierClientProxyOrderList']['valueAfter'] && window['changeApplyList']['supplierClientProxyOrderList']['valueAfter'];
    let ClientPowerTableData = supplierClientProxyOrderList ? supplierClientProxyOrderList : $('#table2').XGrid('getRowData')
    // const ClientPowerTableData = $('#table2').XGrid('getRowData')
    switch (supplierType) {
        case '55': // 药品生产
            const requireOptions = ['46', '29']
            let selOptions = ApprovalTableData.map(item => String(item['certificateId']))
            let checkResAry = selOptions.filter(item => requireOptions.indexOf(item) > -1)
            if (checkResAry.length == 0) {
                msg = '供应商类别是药品生产时， 营业执照，药品生产许可证必填'
                return msg
            } else {
                if (Array.from(new Set(checkResAry)).length != 2) {
                    msg = '供应商类别是药品生产时， 营业执照，药品生产许可证必填'
                    return msg
                }
            }
            if (!pageType) {
                let customerDataCheck = ClientPowerTableData.filter(item => item['authorityType'] == '0')
                if (customerDataCheck.length == 0) {
                    msg = `当供应商类别是药品生产时，客户委托书客户类型必须选品种授权`
                    return msg
                }
            }
            console.log('被拦截后不该走这个',);
            break;
        case '56': // 药品批发
            const requireOptions_PF = ['46', '30']
            let selOptions_PF = ApprovalTableData.map(item => String(item['certificateId']))
            let checkResAry_PF = selOptions_PF.filter(item => requireOptions_PF.indexOf(item) > -1)
            if (checkResAry_PF.length == 0) {
                msg = '供应商类别是药品批发时， 营业执照，药品经营许可证必填'
                return msg
            } else {
                if (Array.from(new Set(checkResAry_PF)).length != 2) {
                    msg = '供应商类别是药品批发时， 营业执照，药品经营许可证必填'
                    return msg
                }
            }
            console.log('被拦截后不该走这个',);
            break;
        case '57': // 医疗器械
            const requireOptions_QX = ['46']
            let selOptions_QX = ApprovalTableData.map(item => String(item['certificateId']))
            let checkResAry_QX = selOptions_QX.filter(item => requireOptions_QX.indexOf(item) > -1)
            if (checkResAry_QX.length == 0) {
                msg = '供应商类别是医疗器械时， 营业执照必填'
                return msg
            }
            // let QXSC_requireCheckData = ApprovalTableData.filter(item => item['certificateId'] == '38' || item['certificateId'] == '35')
            // if (QXSC_requireCheckData.length == 0 ) {
            //     msg = `《医疗器械生产许可证》《第一类医疗器械生产备案凭证》至少必填其一，可同时并存`
            //     return msg
            // }
            let QXJY_requireCheckData = ApprovalTableData.filter(item => item['certificateId'] == '37' || item['certificateId'] == '36' || item['certificateId'] == '38' || item['certificateId'] == '35')
            if (QXJY_requireCheckData.length == 0) {
                msg = `《医疗器械经营许可证》《第二类医疗器械经营备案凭证》 《医疗器械生产许可证》《第一类医疗器械生产备案凭证》 至少必填其一，可同时并存`
                return msg
            }
            console.log('被拦截后不该走这个',);
            break;
        case '58': // 其他
            const requireOptions_other = ['46']
            let selOptions_other = ApprovalTableData.map(item =>String( item['certificateId']))
            let checkResAry_other = selOptions_other.filter(item => requireOptions_other.indexOf(item) > -1)
            if (checkResAry_other.length == 0) {
                msg = '供应商类别是其他时， 营业执照必填'
                return msg
            }
            console.log('被拦截后不该走这个',);
            break;
    }
    console.log('msg', msg);
    return msg
}
function requireTab(supplierType, datas) {
    let msg = ''
    const requireTableName = ['年度报告', '质量保证协议', '客户委托书']
    let requireTabName = ''
    switch (supplierType) {
        case '55':
            requireTabName = '药品生产'
            break
        case '56':
            requireTabName = '药品批发'
            break
        case '57':
            requireTabName = '医疗器械'
            break
    }
    if (supplierType != '58') {
        for (let i = 0; i< datas.length; i++) {
            if (datas[i].length == 0) {
                msg = `供应商类别是${requireTabName}时， ${requireTableName[i]}必填`
                return msg
            }
        }
    }
    return msg
}

function changValid(objStr,  nextNode) {
    let flag = false
    let objStrAry = objStr.split('.')
    objStrAry.shift()
    for (let i = 0; i< objStrAry.length; i++) {
        if (window[objStrAry[i]]) {

        }
    }
    // let objStrAry = null;
    // if (typeof objStr == 'string') {
    //     objStrAry = objStr.split('.')
    //     nextNode = objStrAry[0]
    //     objStrAry.shift()
    // } else {
    //     objStrAry = objStr
    // }
    // for (let i = 0; i < objStrAry.length; i++) {
    //     if (window[nextNode]) {
    //         let prevNode = objStrAry.shift()
    //         changValid(objStrAry, window[prevNode])
    //     } else {
    //         return flag = true
    //     }
    // }
    return flag
}
/*
* 4.4 同一个供应商，授权类型有且只能存在一种。
当存在2条不一致的授权类型时，提交时拦截提示：同一个供应商，授权类型只能存在一种。
*/
function checkAuthorityType() {
    let status = true
    const table2Data = $('#table2').XGrid('getRowData')
    let selType = table2Data.map(item => item['authorityType'])
    let selTypeAry = Array.from(new Set(selType))
    if (selTypeAry.length != 1){
        status = false
        console.log('status', status);
        return status
    }
    console.log('status', status);
    return status
}

/**
 * 当 授权类型为 经营范围授权时， 授权经营范围自动关联展示批件中所选的经营范围，此处的经营范围不需再选择一次，仅仅展示。
 */
function syncBusscopeToCustomer() {
    // let BuseScopes = $('.baseDataBuseScope font')
    let BuseScopes = utils.operateRange($('#table3').XGrid('getRowData'), 'supplierApprovalFileBusinessScopeVOList', 'validityDate')
    let BuseScopeAry = []
    $(BuseScopes).each((index, item) => {
        BuseScopeAry.push({
            businessScopeCode: item['id'],
            businessScopeName: item['name']
        })
    })
    let table2Data = $('#table2').XGrid('getRowData')
    if (table2Data.length > 0) {
        $(table2Data).each((index, item) => {
            if (item['authorityType'] == '1') {
                $("#table2").XGrid('setRowData',  item['id'], {supplierClientProxyBusinessScopeVOList: BuseScopeAry});
            }
        })
    }
}

function tabDataCheck() {
    let supplierClientProxyOrderList = window['changeApplyList'] && window['changeApplyList']['supplierClientProxyOrderList']&&  window['changeApplyList']['supplierClientProxyOrderList']['valueAfter'] && window['changeApplyList']['supplierClientProxyOrderList']['valueAfter'],
        supplierYearReportList = window['changeApplyList'] && window['changeApplyList']['supplierYearReportList'] && window['changeApplyList']['supplierYearReportList']['valueAfter'] && window['changeApplyList']['supplierYearReportList']['valueAfter'],
        supplierQualityAgreementList = window['changeApplyList'] && window['changeApplyList']['supplierQualityAgreementList'] && window['changeApplyList']['supplierQualityAgreementList']['valueAfter'] && window['changeApplyList']['supplierQualityAgreementList']['valueAfter']
    let table2Data = supplierClientProxyOrderList ? supplierClientProxyOrderList : $('#table2').XGrid('getRowData'),
        table4Data = supplierYearReportList ? supplierYearReportList : $('#table4').XGrid('getRowData'),
        table1Data = supplierQualityAgreementList ? supplierQualityAgreementList : $('#table1').XGrid('getRowData')
    let msg = requireTab($('#supplierTypeId').val(), [table4Data, table1Data, table2Data])
    if (msg != '') {
        utils.dialog({
            title: '提示',
            content: msg,
            okValue: '确定',
            ok: function () {}
        }).showModal()
        return false
    }
    return true
}

