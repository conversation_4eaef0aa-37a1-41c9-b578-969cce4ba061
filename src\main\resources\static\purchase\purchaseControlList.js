$(function () {
    $('#X_Tablea').XGrid({
        url: '/proxy-purchase/purchase/productSupplier/queryProductSupplierPageList',
        colNames: ['机构名称','机构编码','神农商品编码','商品名称','指定供应商编码','供应商名称','备注','操作人','操作时间'],
        colModel: [
            {
                name: 'orgName',
                index: 'orgName'
            },
            {
                name: 'orgCode',
                index: 'orgCode',
                hidden: true,
                hidegrid: true
            },
            {
                name: 'productCode',
                index: 'productCode'
            },
            {
                name: 'productName',
                index: 'productName'
            },
            {
                name: 'supplierCode',
                index: 'supplierCode',
                formatter: function(val){
                    if(val != null && val == "-1"){
                        return '';
                    }else {
                        return val;
                    }
                }
            },
            {
                name: 'supplierName',
                index: 'supplierName'
            },
            {
                name: 'remark',
                index: 'remark'
            },
            {
                name: 'updateUser',
                index: 'updateUser'
            },
            {
                name: 'updateTime',
                index: 'updateTime',
                formatter: function (e) {
                    if (e != null && e != "") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }
        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid_page',//设置翻页所在html元素名称
        selectandorder: true,
        rowList: [20, 50, 100],
        // attachRow:true,
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件订单', obj);
        },
        gridComplete: function () {}
    })

    $('#orgKey').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/productSupplier/queryOrg', //异步请求
        paramName: 'orgKey',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.orgName,orgCode:dataItem.orgCode};
                })
            };
        },
        onSelect: function (result) {
            // $("#orgKey").val(result.orgName);
            $("#orgCode").val(result.orgCode);
        },
        onNoneSelect: function (params, suggestions) {
            $("#orgKey").val("");
            $("#orgCode").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    })
})
$('#searchBtn').on('click', function () {
    var searchData = $("#searchForm").serializeToJSON();
    $('#X_Tablea').setGridParam({
        url: '/proxy-purchase/purchase/productSupplier/queryProductSupplierPageList?' + $("#searchForm").serialize(),
        postData: {
            "purchaseProductSupplierVo" : JSON.stringify(searchData)
        }
    }).trigger('reloadGrid');
})


/* 导出 */
$('#exportBtn').on('click', function () {
    var searchData = $("#searchForm").serializeToJSON();
    var tableId = 'X_Tablea';
    z_utils.exportTable(tableId, function (that) {
        //需要导出项
        var colName = [];
        var colNameDesc = [];
        $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
            if ($(this).prop("checked")) {
                colName.push($(this).attr('name'))
                colNameDesc.push($(this).parent().text());
            }
        });

        //获取当前选中项
        var data = $('#' + tableId).XGrid('getSeleRow');
        if (data && data.length && data.length > 0) {
            data = data.map(function (item, key) {
                var new_item = {};
                colName.forEach(function (val, index) {
                    new_item[val] = item[val]
                })
                return new_item
            })
            data = JSON.stringify(data);
        } else {
            data = '';
        }
        console.log(colName);
        var obj = {
            orgCode:$("#orgCode").val(),
            productCode:$("#productCode").val(),
            supplierCode:$("#supplierCode").val(),
            supplierName:$("#supplierName").val(),
            productName:$("#productName").val(),
            selectData: data,
            colName: colName,
            colNameDesc: colNameDesc
        }
        utils.httpPost("/proxy-purchase/purchase/productSupplier/exportPurchaseSupplierList", obj);

    });
});


function makeContract(type){
    //获取选中的采购订单
    var ids = "";
    var data = $('#X_Tablea').XGrid('getSeleRow');
    if(data.length <= 0){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    if(data.length > 1){
        utils.dialog({content: "请选中一条数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }

    if (data.length && data.length > 0) {
        data = data.map(function (item, key) {
            if(key > 0 ){
                utils.dialog({content: "请选中一条数据！", quickClose: true, timeout: 4000}).showModal();
                return false;
            }
            ids = item.orderNo;
        })
    }
    if(ids == ""){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    $.ajax({
        method: "POST",
        url: "/proxy-purchase/purchase/upload/checkDownContract",
        data: {
            "orderNo": ids,
            "type":type
        },
        dataType: 'json',
        cache: false,
    }) .done(function( data ) {
        if(data.code==0){
            if(type == 1){
                uploadContractBtn(ids);
            }else if(type == 2){
                deleteContractBtn(ids);
            }else if(type == 3){
                window.open("/proxy-purchase/purchase/upload/downContract?orderNo="+ids);
            }
        } else{
            utils.dialog({content: data.msg, quickClose: true, timeout: 4000}).showModal();
        }
    });
}



$("#deleteBtn").click(function () {
    utils.dialog({
        title: '提示',
        content: '<div style="max-height: 300px;overflow-y: auto;">确认后，将会修改数据，是否确定操作？</div>',
        okValue: '确定',
        ok: function () {
            deleteInfo();
        },
        cancelValue: '返回',
        cancel: function () {
            return;
        },
    }).showModal();

});

// 执行删除操作
function deleteInfo(){
    parent.showLoading();
    var ids = "";
    var data = $('#X_Tablea').XGrid('getSeleRow');
    if (data && data.length && data.length > 0) {
        data = data.map(function (item, key) {
            ids = ids + item.id +",";
        })
    }
    if(ids == ""){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    $.ajax({
        type: "POST",
        url: "/proxy-purchase/purchase/productSupplier/updateYn",
        data:{
            "ids":ids
        },
        error: function () {},
        success: function (res) {
            parent.hideLoading();
            if(res){
                utils.dialog({content: res.msg, quickClose: true, timeout: 4000}).showModal();
                $('#X_Tablea').setGridParam({
                }).trigger('reloadGrid');
            }else{
                utils.dialog({content: "删除错误", quickClose: true, timeout: 4000}).showModal();
            }
        }
    });
}

$("#setBtn").click(function () {
    $('#X_Tablea').XGrid('filterTableHead');
})

$("#newAddBtn").click(function(){
    var url = '/proxy-purchase/purchase/productSupplier/toAdd';
    parent.openTabs('toAdd', '新建采控信息', url);
    // parent.$('#mainFrameTabs').bTabsAdd("toAdd", "新建采控信息", url);
});
