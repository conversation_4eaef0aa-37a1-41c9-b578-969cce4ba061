var emails = 0;
var orderType = $('#orderType').val();
var strongControl = $('#purchaseStrongControl').val();
var orgCode = $('#orgCode').val();

var itemType = $('#itemType').val();
var ifUpdatePrice = $('#ifUpdatePrice').val();
var adjustPercentageLimit = $('#adjustPercentageLimit').val();
var jcIfUpdatePrice = $('#jcIfUpdatePrice').val();
var jcAdjustPercentageLimit = $('#jcAdjustPercentageLimit').val();

var purchaseOrgRate = $('#purchaseOrgRate').val();
var endTimeOptions = {};

getEndTimeMethod();

if (orderType == '1') {
  $('#nav-tab .active a', window.parent.document).html('编辑大客户采购订单');
} else {
  $('#nav-tab .active a', window.parent.document).html('编辑采购订单');
}
var startDate = '';
var _channeld = '';
function seletePurchaseType() {
  change_channelID('', '1');
}
function change_channelID(that) {
  _channeld = $(that).val();
  //清空商品列表
  $('#X_Tablea').clearGridData();
  tableAddRow('X_Tablea', {});
  rowEvent(null);
}

function getNowFormatDate() {
  var date = new Date();
  var seperator1 = '-';
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var strDate = date.getDate() + 1;
  if (month >= 1 && month <= 9) {
    month = '0' + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = '0' + strDate;
  } else if (strDate >= 30) {
    strDate = strDate - 30;
    month = '0' + (parseInt(month) + 1);
  }
  var currentdate = year + seperator1 + month + seperator1 + strDate;
  startDate = currentdate;
  return currentdate;
}

getNowFormatDate();
let storehouseTypeArr = ['1', '2'],
  storehouseAddressArr = [
    '武汉市东西湖区走马岭食品三路1#厂房一层、二层',
    '武汉市东西湖区东西湖大道4889号BCO(13)',
  ];

// 收货仓库 切换
function ckaddress() {
  var storehouseType = $('#storehouseType').val();
  switch (storehouseType) {
    case storehouseTypeArr[0]:
      $('#storehouseAddress').val(storehouseAddressArr[0]);
      break;
    case storehouseTypeArr[1]:
      $('#storehouseAddress').val(storehouseAddressArr[1]);
      break;
  }
}

// 收货地址 切换
function change_storeHouseAddress() {
  var storehouseAddressVal = $('#storehouseAddress').val();
  switch (storehouseAddressVal) {
    case storehouseAddressArr[0]:
      $('#storehouseType').val(storehouseTypeArr[0]);
      break;
    case storehouseAddressArr[1]:
      $('#storehouseType').val(storehouseTypeArr[1]);
      break;
  }
}
$(function () {
  $('div[fold=head]').fold({ sub: 'sub' });
  $('#X_Tablea').XGrid({
    data: [],
    colNames: [
      '来源',
      '来源单号',
      '<i>是否新品</i><i class="questa"></i>',
      '商品编号',
      '商品名称',
      '通用名',
      'TOP排名',
      '规格',
      '小包装单位',
      '生产厂家',
      '产地',
      '商品剂型',
      '订单属性',
      '订单数量',
      '含税单价',
      '<i>是否近效期</i><i class="questa"></i>',
      '生产日期',
      '有效期至',
      '备注',
      '<i>平台近7天成交均价</i><i class="questa"></i>',
      '全国近7天最低采购价',
      '全国近7天最低采购价所属机构',
      '全国最低购进单价',
      '近30天全国平均采购价',
      '涨幅',
      '<i>审批价差幅度</i><i class="questa"></i>',
      '<i>审批周转天数</i><i class="questa"></i>',
      '含税金额',
      ' APP售价',
      '当前价差毛利',
      '连锁APP售价',
      '税率',
      '中包装规格',
      '大包装规格',
      '可调天数',
      '品牌厂家',
      '商品批准文号',
      '批准文号有效日期',
      '是否过期',
      '最后一次购进价',
      '库存数量',
      '当前周转天数',
      '7天日均销售',
      '15天日均销售',
      '30天日均销售',
      '在途数量',
      '库存上限',
      '安全库存',
      '是否协议品种',
      '是否协议内购进',
      '最后供应商编码',
      '最后供应商名称',
      '供应商款期',
      '<i>建议计划量</i>',
      '2个月内连续涨价次数',
      '2个月最后入库单价',
      '30天销量',
      '是否赠品',
      '采购员',
      '60天销量',
      '90天销量',
      '不含税单价',
      '90天最低进价',
      '90天最高进价',
      '<i>可销天数</i>',
      '索引',
      '供应商货期',
      '商品ID',
      '日均销售数量',
      '标准单价',
      '经营范围',
      '商品大类',
      '特殊属性',
      '是否过期时间',
      '商品产地',
      '存储条件',
      '是否为大库存',
      '大库存金额',
      '是否近效期',
      '近效期金额',
    ],
    colModel: [
      {
        name: 'orderSource',
        type: 'orderSource',
        hidden: true,
      },
      {
        name: 'planOrderNo',
        type: 'planOrderNo',
        hidden: true,
      },
      {
        name: 'isNew',  //是否新品
        type: 'isNew',
        hidden: true,
        formatter: function (data) {
          if (parseInt(data) == "0") {
            return "否";
          } else if (data == "1") {
            return "是";
          }
          return data;
        }
      },
      {
        name: 'productCode',
        type: 'productCode',
        // hidegrid: true,
      },
      {
        name: 'productName',
        type: 'productName', //商品名字
        rowtype: '#productName_input',
        width: 200,
        // hidegrid: true,
      },
      {
        name: 'commonName', //通用名
        index: 'commonName',
        // hidegrid: true ,
      },
      {
        name: 'top',
        index: 'top',//top排名
      },
      {
        name: 'productSpecification',
        type: 'productSpecification',
        // hidden: true,
        // hidegrid: true,
      },
      {
        name: 'productPackUnitSmall',
        type: 'productPackUnitSmall',
        // hidegrid: true,
      },
      {
        name: 'productProduceFactory', //厂家
      },
      {
        name: 'productOriginPlace', //产地
        type: 'productOriginPlace',
        formatter: originPlaceMethod,
        width: 120,
      },
      {
        name: 'dosageForm', //商品剂型
        index: 'dosageForm',
        // hidden:true ,
        // hidegrid: true ,
      },
      {
        name: 'centralizedPurchaseName', //是否集采
        type: 'centralizedPurchaseName',
      },
      {
        name: 'productPackCountSmall',
        type: 'productPackCountSmall', //小包装数量
        rowEvent: rowEvent,
        rowtype: '#productPackCountSmall_input',
        formatter: function (data) {
          if (parseInt(data) == '0') {
            return data;
          } else if (data != '' && data != null) {
            return Math.ceil(data);
          }
          return data;
        },
      },
      {
        name: 'productContainTaxPrice', //含税单价
        rowEvent: rowEvent,
        formatter: paymentStatus,
        rowtype: '#productContainTaxPrice_input',
      },
      {
        name: 'validityDate', //是否近效期购进
        index: 'validityDate',
        rowtype: '#validityDate_select',
      },
      {
        name: 'productProduceDate', //生产日期
        index: 'productProduceDate',
        rowtype: '#productProduceDate_timePicker',
      },
      {
        name: 'productValidityDate', //有效期至
        index: 'productValidityDate',
        rowtype: '#productValidityDate_timePicker',
      },
      {
        name: 'productRemark', //备注
        type: 'productRemark',
        width: '350',
        rowtype: '#productRemark_input',
        formatter: function (v, id, obj) {
          if (obj.varietiesAgreement == '1' && obj.varietiesChannel == '0') {
            setTimeout(function () {
              $('#productRemark_input_' + obj.sort + ' input').attr(
                'class',
                'form-control {validate:{ required :false, minlength :5}}',
              );
            }, 0);
          } else if (obj.canPinNumber > 75) {
            // 可销天数
            setTimeout(function () {
              $('#productRemark_input_' + obj.sort + ' input').attr(
                'class',
                'form-control {validate:{ required :false, minlength :5}}',
              );
            }, 0);
          } else if (
            obj.productContainTaxPrice > obj.productLastPrice &&
            obj.productLastPrice > 0
          ) {
            //商品本次采购单价大于最近购进价 并且最后一次购进价大于0
            setTimeout(function () {
              $('#productRemark_input_' + obj.sort + ' input').attr(
                'class',
                'form-control {validate:{ required :false, minlength :5}}',
              );
            }, 0);
          }
          return v;
        },
      },
      {
        name: 'sevenDaysAvgPrice', //平台近7天成交均价
        index: 'sevenDaysAvgPrice',
        width: '200',
      },
      {
        name: 'sevenDaysFloorPrice', //全国近7天最低采购价
        index: 'sevenDaysFloorPrice',
        width: '200',
      },
      {
        name: 'sevenDaysFloorOrgCode', //全国近7天最低采购价所属机构
        index: 'sevenDaysFloorOrgCode',
        width: '230',
      },
      {
        name: 'globalLowestPrice', //全国最低购进单价
        index: 'globalLowestPrice',
        width: '200',
      },
      {
        name: 'avgProductContainTaxPrice', //近30天全国平均采购价
        index: 'avgProductContainTaxPrice',
        width: '200',
      },
      {
        name: 'gain',
        index: 'gain',
      },
      {
        name: 'field2', //审批价差幅度
        index: 'field2',
      },
      {
        name: 'field1', //审批周转天数
        index: 'field1',
      },
      {
        name: 'productContainTaxMoney',
        type: 'productContainTaxMoney',
        formatter: paymentStatus,
      },
      {
        name: 'productAppContainTaxPrice', //App售价
        type: 'productAppContainTaxPrice',
      },
      {
        name: 'marginGrossProfit', //当前价差毛利
        type: 'marginGrossProfit',
        formatter: function (val, rowType, rowData) {
          //App售价
          let productAppContainTaxPrice = Number(
            rowData.productAppContainTaxPrice,
          );
          //含税单价
          let productContainTaxPrice = Number(rowData.productContainTaxPrice);
          if (productAppContainTaxPrice && productContainTaxPrice) {
            return (
              Math.round(
                (productAppContainTaxPrice - productContainTaxPrice) * 100,
              ) / 100
            );
          } else {
            return 0;
          }
        },
      },
      {
        name: 'chainGuidePrice', //连锁APP售价
        type: 'chainGuidePrice',
      },
      {
        name: 'productEntryTax',
      },
      {
        name: 'productPackUnitMedium',
        type: 'productPackUnitMedium',
      },
      {
        name: 'productPackUnitBig',
        type: 'productPackUnitBig',
      },
      {
        name: 'productAdjustDay', //可调天数
        type: 'productAdjustDay',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'productBrandManufacturers', //品牌厂家
        type: 'productBrandManufacturers',
      },
      {
        name: 'productApprovalNumber', //商品批准文号
        type: 'productApprovalNumber',
        width: 160,
      },
      {
        name: 'productApprovalNumberExpireDate', //商品批准文件有效日期
        type: 'productApprovalNumberExpireDate',
      },
      {
        name: 'ifOldApprovalNumber', //是否过期
        index: 'ifOldApprovalNumber',
        formatter: function (data) {
          if (parseInt(data) == '0') {
            return '否';
          } else if (data != '' && data != null) {
            return '是';
          }
          return data;
        },
      },
      {
        name: 'productLastPrice', //最后一次购进价
        type: 'productLastPrice',
        // hidden:true ,
        // hidegrid: true ,
      },
      {
        name: 'productStockCur', //库存数量
        type: 'productStockCur',
        hidden: true,
        hidegrid: true,
        formatter: function (data) {
          if (parseInt(data) == '0') {
            return data;
          } else if (data != '' && data != null) {
            return Math.ceil(data);
          }
          return data;
        },
      },
      {
        name: 'canPinNumber', //当前周转天数
        type: 'canPinNumber',
      },
      // {
      //     name: 'turnoverDefaultDays',//购进后周转天数
      //     type: 'turnoverDefaultDays',
      //     // formatter: function (val,rowType,rowData) {
      //     //     //可用库存
      //     //     let productStockCur = Number(rowData.productStockCur);
      //     //     //订单数量
      //     //     let productPackCountSmall = Number(rowData.productPackCountSmall);
      //     //     //60日均天销量
      //     //     let productSixtySaleForDay = Number(rowData.sixtyAverageDailySales);
      //     //     if(productSixtySaleForDay>0){
      //     //         return  Math.round((productStockCur+productPackCountSmall) / productSixtySaleForDay);
      //     //     }else{
      //     //         return ''
      //     //     }
      //     //
      //     // }
      // },
      {
        name: 'productSevenAverageSale', //7天日均销售
        type: 'productSevenAverageSale',
      },
      {
        name: 'productFifteenAverageSale', //15天日均销售
        type: 'productFifteenAverageSale',
      },
      {
        name: 'productThirtyAverageSale', //30天日均销售
        type: 'productThirtyAverageSale',
      },
      {
        name: 'productPackOnWayCount', //在途数量
        type: 'productPackOnWayCount',
        // hidden:true ,
        // hidegrid: true ,
      },
      {
        name: 'productUpperLimitStock', //库存上限
        type: 'productUpperLimitStock',
      },
      {
        name: 'productStockSafe', //安全库存
        type: 'productStockSafe',
        // hidden:true ,
        // hidegrid: true ,
        formatter: function (data) {
          if (parseInt(data) == '0') {
            return data;
          } else if (data != '' && data != null) {
            return Math.ceil(data);
          }
          return data;
        },
      },
      {
        name: 'varietiesAgreement', //1/0，1代表是协议品种；0代表非协议品种
        type: 'varietiesAgreement',
        formatter: function (data) {
          var val = '否';
          if (data == 1) {
            val = '是';
          } else if (data == 0) {
            val = '否';
          }
          return val;
        },
        unformat: function (data) {
          var val = '0';
          if (data === '是') {
            val = 1;
          } else if (data === '否') {
            val = 0;
          }
          return val;
        },
      },
      {
        name: 'varietiesChannel', //1/0，1代表是业务类型内购进；0代表否
        type: 'varietiesChannel',
        formatter: function (data) {
          var val = '否';
          if (data == 1) {
            val = '是';
          } else if (data == 0) {
            val = '否';
          }
          return val;
        },
        unformat: function (data) {
          var val = '0';
          if (data === '是') {
            val = 1;
          } else if (data === '否') {
            val = 0;
          }
          return val;
        },
      },
      {
        name: 'lastSupplierCode', //最后供应商编码
        index: 'lastSupplierCode', //最后供应商编码
        // hidden:true,
      },
      {
        name: 'lastSupplierName', //最后供应商
        index: 'lastSupplierName', //最后供应商
        // hidden:true,
      },
      {
        name: 'supplierPaymentPeriod', //供应商款期
        index: 'supplierPaymentPeriod',
      },
      {
        name: 'productSuggestPlanCount', //建议计划数量
        type: 'productSuggestPlanCount',
        // hidden:true ,
        // hidegrid: true ,
      },
      {
        name: 'purchaseCount', //2个月内涨价次数
        type: 'purchaseCount',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'lastProductContainTaxPrice', //2个月内入库单价
        type: 'lastProductContainTaxPrice',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'productThirtySale', //30天销量
        type: 'productThirtySale',
        // hidden:true ,
        // hidegrid: true ,
        formatter: function (data) {
          if (parseInt(data) == '0') {
            return data;
          } else if (data != '' && data != null) {
            return Math.ceil(data);
          }
          return data;
        },
      },
      {
        name: 'giftFlag', //1 赠品 0商品
        type: 'giftFlag',
        formatter: function (data) {
          if (data == '1') {
            return '赠品';
          } else {
            return '商品';
          }
        },
        unformat: function (data) {
          if (data == '赠品') {
            return '1';
          } else if (data == '商品') {
            return '0';
          }
          return '';
        },
        // hidden:true,
        // hidegrid: true ,
      },
      {
        name: 'purchaseUser', //采购员
        type: 'purchaseUser',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'productSixtySale', //60天销量
        type: 'productSixtySale',
        hidden: true,
        hidegrid: true,
        formatter: function (data) {
          if (parseInt(data) == '0') {
            return data;
          } else if (data != '' && data != null) {
            return Math.ceil(data);
          }
          return data;
        },
      },
      {
        name: 'productNinetySale', //90天销量
        type: 'productNinetySale',
        hidden: true,
        hidegrid: true,
        formatter: function (data) {
          if (parseInt(data) == '0') {
            return data;
          } else if (data != '' && data != null) {
            return Math.ceil(data);
          }
          return data;
        },
      },
      {
        name: 'productUnitPrice', //不含税单价
        type: 'productUnitPrice',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'productMinPriceNinetyDay', //90天最低进价
        type: 'productMinPriceNinetyDay',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'productMaxPriceNinetyDay', //90天最高进价
        type: 'productMaxPriceNinetyDay',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'productStockTurnDay', //存周转天数
        type: 'productStockTurnDay',
        hidden: true,
        hidegrid: true,
        formatter: function (data) {
          if (parseInt(data) == '0') {
            return data;
          } else if (data != '' && data != null) {
            return Math.ceil(data);
          }
          return data;
        },
      },
      {
        name: 'id',
        index: 'id', //索引。其和后台交互的参数为sidx
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'supplyArrivalPeriod', //供应商货期
        index: 'supplyArrivalPeriod',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'productId', //商品ID
        index: 'productId',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'thirtyAverageSaleVolume', //日均销售数量
        index: 'thirtyAverageSaleVolume',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'standardPrice', //标准单价
        index: 'standardPrice',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'scopeOfOperation', //经营范围
        index: 'scopeOfOperation',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'largeCategory', //商品大类
        index: 'largeCategory',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'specialAttributes', //特殊属性
        index: 'specialAttributes',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'remainDays', //是否过期时间
        index: 'remainDays',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'productOriginPlace', //商品产地
        type: 'productOriginPlace',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'sort', //表格本地键值
        index: 'sort',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'oldProductCode',
        index: 'oldProductCode',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'storageConditions',
        index: 'storageConditions',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'firstPurchaseStore', //表格本地键值
        index: 'firstPurchaseStore',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'bigInventoryProduct', // 是否为大库存1
        index: 'bigInventoryProduct',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'bigInventoryProductPrice', // 大库存金额
        index: 'bigInventoryProductPrice',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'nearExpireProduct', //是否为近效期
        index: 'nearExpireProduct',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'nearExpireProductPrice', //近效期金额
        index: 'nearExpireProductPrice',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'sixtyAverageDailySales', // 60天日均
        index: 'sixtyAverageDailySales',
        hidden: true,
        hidegrid: true,
      },
      {
        name: 'centralizedPurchaseType', //是否集采
        type: 'centralizedPurchaseType',
        hidden: true,
        hidegrid: true,
      },
    ],
    key: 'sort',
    rowNum: 10000,
    maxheight: 400,
    //rownumbers: true,//是否展示序号
    selectandorder: true,
    altRows: true, //设置为交替行表格,默认为false
    gridComplete: function () {
      $('#loadingBlock').show();
      $.ajax({
        url: '/proxy-purchase/purchase/product/getPurchaseGroupPlanOrderProductByOverdueList',
        type: 'post',
        data: {
          simpleCode: $('#simpleCode').val(),
          orderNo: $('#purchaseOrderNo').val(),
          pageNum: 1,
          pageSize: 10000,
          channelId: $('#channelId').val(),
          orderType: $('#orderType').val(),
          supplierCode: $('#find_supplierc').val(),
          centralizedPurchaseType: $('#selectCentralizedPurchaseType').val(),
        },
        success: function (res) {
          console.log(res, 'res');
          var list = res.result.list;
          if (list) {
            $.each(list, function (index, item) {
              tableAddRow('X_Tablea', item);
              var $ele = $(
                '#X_Tablea tr#' +
                (index + 1) +
                ' td[row-describedby=productName] input',
              );
              $ele.attr('oldvalue', $ele.val());
            });

            //数据校验标红
            var data = $('#X_Tablea').XGrid('getRowData');
            if (data.length > 0) {
              $(data).each(function (i) {
                if (data[i].overDue == 'Y' && data[i].overDue != null) {
                  $('#X_Tablea  #' + data[i].id).removeClass('maize');
                  $('#X_Tablea  #' + data[i].id).addClass('redback');
                } else if (
                  data[i].remainDays <= 30 &&
                  data[i].remainDays != ''
                ) {
                  $('#X_Tablea  #' + data[i].id).removeClass('redback');
                  $('#X_Tablea  #' + data[i].id).addClass('maize');
                }
              });
            }
            rowEvent();

            //编辑页，数据加载完成后查询一下商品是否标红
            setTimeout(() => {
              queryProductHit();
            }, 100);
          }
          $('#loadingBlock').hide();
        },
        error: function () {
          $('#loadingBlock').hide();
        },
      });

      hotKey({
        tableId: 'X_Tablea',
        keyFn: {
          productName: function (rowId, col, enterFocus) {
            if (!$(this).attr('oldvalue')) {
              setEmptyRow(rowId, function () {
                enterFocus(rowId, col);
              });
            }
          },
          productPackCountSmall: 'productPackCountSmall',
          productContainTaxPrice: 'productContainTaxPrice',
          productRemark: 'productRemark',
        },
        endFn: function (tr, enterFocus) {
          var trId = $(tr).attr('id');
          var trData = $('#X_Tablea').XGrid('getRowData', trId);
          if (trData.productCode) {
            tableAddRow('X_Tablea', {});
            enterFocus();
          }
        },
      });

      hotKey({
        tableId: 'X_Tablea',
        keyFn: {
          productName: function (rowId, col, enterFocus) {
            if (!$(this).attr('oldvalue')) {
              setEmptyRow(rowId, function () {
                enterFocus(rowId, col);
              });
            }
          },
          productPackCountSmall: 'productPackCountSmall',
          productContainTaxPrice: 'productContainTaxPrice',
          productRemark: 'productRemark',
        },
        endFn: function (tr, enterFocus) {
          var trId = $(tr).attr('id');
          var trData = $('#X_Tablea').XGrid('getRowData', trId);
          if (trData.productCode) {
            tableAddRow('X_Tablea', {});
            enterFocus();
          }
        },
      });

      function hotKey(option) {
        var $table = $('#' + option.tableId),
          keyFn = option.keyFn,
          keyAry = Object.keys(keyFn),
          colInput = '';
        colInput = $.map(keyAry, function (item) {
          return 'td[row-describedby=' + item + '] input';
        }).join(',');
        setTimeout(function () {
          $table.delegate(colInput, {
            keyup: function (e) {
              if (e.keyCode === 13) {
                var $ele = $(e.target),
                  colName = $ele.parents('td').attr('row-describedby'),
                  rowId = $ele.parents('tr').attr('id');
                if ($.isFunction(keyFn[colName])) {
                  keyFn[colName].call(this, rowId, colName, enterFocus);
                } else {
                  enterFocus(rowId, colName);
                }
              } else if (e.keyCode === 37) {
                //左
              } else if (e.keyCode === 39) {
                //右
              } else if (e.keyCode === 38) {
                //上
              } else if (e.keyCode === 40) {
                //下
              }
            },
          });
        });

        function enterFocus(rowId, oldCol) {
          var col = keyAry[keyAry.indexOf(oldCol) + 1],
            $tr = $table.find('tr#' + rowId);
          if (col) {
            $tr.find('td[row-describedby=' + col + '] input').focus();
          } else {
            //需判断是需要切换下一行还是执行结束回调
            if ($tr.next().length > 0) {
              $tr
                .next()
                .find('td[row-describedby=' + keyAry[0] + '] input')
                .focus();
            } else {
              option.endFn($tr[0], function () {
                setTimeout(function () {
                  enterFocus(rowId, oldCol);
                });
              });
            }
          }
        }
      }

      //初始化table头部hover提示
      setTimeout(() => {
        getTipText();
      }, 100);
    },
  });

  function getTipText() {
    $.ajax({
      type: 'GET',
      url: '/proxy-purchase/purchase/purchaseOrder/getValidityDateMsg',
      success: function (response) {
        let { result, code, msg } = response;
        if (code === 0) {
          initQuesta(result);
        }
      },
    });
  }

  //初始化table头部hover提示
  function initQuesta() {
    const questaOption = [
      {
        th: 'field2', //审批销售毛利
        title: `销售毛利=(平台近7天成交均价-购进价)/平台近7天成交均价*100%；平台近7天成交均价包含自营和POP的业务`,
        width: 460,
        height: 80,
      },
      {
        th: 'field1', //审批周转天数
        title: `审批周转天数= (现有库存数量+10天内的商品编码维度在途订单数量+采购数量) /近30天净日均销量；<br/><span style="color:red"></span>如果是集团，则购进后可销天数= (现有库存数量+10天内的商品编码维度在途订单数量+采购数量)/全国近30天净日均销量；`,
        width: 480,
        height: 80,
      },
      {
        th: 'validityDate', //是否近效期购进
        title: string,
        width: 460,
        height: 172,
      },
      {
        th: 'isNew', //是否新品
        title: `前60天不在线的品种`,
        width: 460,
        height: 80,
      },
      {
        th: 'sevenDaysAvgPrice', //平台近7天成交均价
        title: `商品维度数据，包含自营业务和POP业务，T-1指标数据`,
        width: 460,
        height: 80,
      },
    ];

    eachTipView(questaOption);
  }

  function eachTipView(questaOption) {
    $.each(questaOption, function (index, item) {
      $('.XGridHead').delegate('th[row-describedby=' + item.th + '] .questa', {
        mouseover: function (e) {
          $('body').append(
            "<div id='div_tooltips'><style>#div_tooltips:after{content: '';width: 0;height: 0;position: absolute;left: " +
            (item.width / 2 - 10) +
            "px;bottom: -10px;border-left: solid 10px transparent;border-top: solid 10px white;border-right: solid 10px transparent;}</style><div id='inner_tooltips'>" +
            item.title +
            '</div></div>',
          );
          $('#div_tooltips')
            .css({
              boxSizing: 'border-box',
              width: item.width + 'px',
              height: item.height + 'px',
              padding: '10px',
              zIndex: 9999,
              backgroundColor: '#ffffff',
              border: '1px solid #c4c4c4',
              position: 'absolute',
              top: $(e.target).offset().top - item.height - 10 + 'px',
              left: $(e.target).offset().left + 5 - item.width / 2 + 'px',
            })
            .show('fast');
        },
        mouseout: function () {
          $('#div_tooltips').remove();
        },
        click: function () { },
      });
    });
  }

  $('#find_supplierc').dblclick(function () {
    commodity_search_dia($(this).val());
  });

  //供应商模糊查询
  $('#find_supplierc').Autocomplete({
    serviceUrl: '/proxy-purchase/purchase/supplier/loadSupplierData4PurchaseOrderProxy',
    paramName: 'keyword',
    params: {
      pageNum: 1,
      pageSize: 5,
      orgCode: function () {
        return $('#orgCode').val();
      },
    },
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果', //查无结果的提示语
    autoSelectFirst: true,
    onSelect: function (obj) {
      $.ajax({
        type: 'POST',
        url: '/proxy-purchase/purchase/supplier/getSuppliserValidity?organBaseId=' + obj.id,
        async: false,
        error: function () { },
        success: function (data) {
          if (data.code == -1) {
            // 近效期提示
            utils
              .dialog({
                title: '提示',
                content: data.msg,
                okValue: '确定选择',
                ok: function () {
                  selectAction();
                },
                cancelValue: '重新选择',
                cancel: function () {
                  $('#find_supplierc').val(
                    $('#find_supplierc').attr('oldValue'),
                  );
                },
              })
              .showModal();
          } else {
            selectAction();
          }
        },
      });
      function selectAction() {
        $('#find_supplierc').val(obj.data).attr('oldvalue', obj.value);
        $('#supplierName').val(obj.value);
        let orgCode = $('#orgCode').val();
        obj.supplierCode = obj.data;
        obj.supplierName = obj.value;
        $.ajax({
          type: 'POST',
          url:
            '/proxy-purchase/purchase/supplier/getForSupplierVoList4ByPurchaseOrder?id=' +
            obj.id + "&orgCode=" + orgCode,
          async: false,
          error: function () { },
          success: function (data) {
            if (data.supplierFinanceProfitRate != '' &&
              data.supplierFinanceProfitRate != null) {
              obj.supplierFinanceProfitRate = data.supplierFinanceProfitRate
            }
            //付款方式
            if (
              data.supplierPaymentVoList != '' &&
              data.supplierPaymentVoList != null
            ) {
              obj.supplierPaymentVoList = JSON.stringify(
                data.supplierPaymentVoList,
              );
            } else {
              obj.supplierPaymentVoList = '';
            }
            //结算方式
            if (
              data.supplierSettlementVoList != '' &&
              data.supplierSettlementVoList != null
            ) {
              obj.supplierSettlementVoList = JSON.stringify(
                data.supplierSettlementVoList,
              );
            } else {
              obj.supplierSettlementVoList = '';
            }
            //仓库地址
            if (
              data.listSupplyStoreAddress != '' &&
              data.listSupplyStoreAddress != null
            ) {
              obj.listSupplyStoreAddress = JSON.stringify(
                data.listSupplyStoreAddress,
              );
            } else {
              obj.listSupplyStoreAddress = '';
            }

            //经营访问
            if (data.simpleCodeList != null && data.simpleCodeList != '') {
              var ary = [];
              $.each(data.simpleCodeList, function (i, v) {
                ary.push(v);
              });
              obj.simpleCodeList = ary.join(',');
            } else {
              obj.simpleCodeList = '';
            }

            //过期证件
            if (data.overDueList != null && data.overDueList != '') {
              var ary = [];
              $.each(data.overDueList, function (i, v) {
                ary.push(v);
              });
              obj.overDueList = ary.join(',');
            } else {
              obj.overDueList = '';
            }
            //不足一个月
            if (data.overOneMouthList != null && data.overOneMouthList != '') {
              var ary = [];
              $.each(data.overOneMouthList, function (i, v) {
                ary.push(v);
              });
              obj.overOneMouthList = ary.join(',');
            } else {
              obj.overOneMouthList = '';
            }

            //订单类型
            if (data.itemType != null && data.itemType !== '') {
              obj.itemType = data.itemType;
            }
            //是否可修改金额
            if (data.ifUpdatePrice != null && data.ifUpdatePrice !== '') {
              obj.ifUpdatePrice = data.ifUpdatePrice;
            }
            if (data.jcIfUpdatePrice != null && data.jcIfUpdatePrice !== '') {
              obj.jcIfUpdatePrice = data.jcIfUpdatePrice;
            }
            if (
              data.adjustPercentageLimit != null &&
              data.adjustPercentageLimit !== ''
            ) {
              obj.adjustPercentageLimit = data.adjustPercentageLimit;
            }
            if (
              data.jcAdjustPercentageLimit != null &&
              data.jcAdjustPercentageLimit !== ''
            ) {
              obj.jcAdjustPercentageLimit = data.jcAdjustPercentageLimit;
            }
            //数量百分比
            if (
              data.adjustPercentageLimit != null &&
              data.adjustPercentageLimit !== ''
            ) {
              obj.adjustPercentageLimit = data.adjustPercentageLimit;
            }

            if (data.purchaseOrgRate != null && data.purchaseOrgRate !== '') {
              obj.purchaseOrgRate = data.purchaseOrgRate;
            }

            setSupplyInfo(obj);
          },
        });
      }
    },
    dataReader: {
      list: 'result', //结果集，不写返回结果为数组
      listChild: 'list',
      data: 'supplierCode',
      value: 'supplierName',
      id: 'id',
      supplyBussinessUserName: 'supplyBussinessUserName',
      supplyBussinessUserId: 'supplyBussinessUserId',
      supplyBussinessUserMobile: 'supplyBussinessUserMobile',
      supplierPaymentVoList: 'supplierPaymentVoList',
      supplierSettlementVoList: 'supplierSettlementVoList',
      listSupplyStoreAddress: 'listSupplyStoreAddress',
      supplyOperateId: 'supplyOperateId',
      arrivalPeriod: 'arrivalPeriod',
      overOneMouthList: 'overOneMouthList',
      overDueList: 'overDueList',
      simpleCodeList: 'simpleCodeList',
    },
    onNoneSelect: function (params, suggestions) {
      $('#supplierId').val('');
      $('#find_supplierc').val('');
      setTimeout(function () {
        $('#find_supplierc').attr('oldvalue', '');
      }, 200);
    },
    onSearchComplete: function (query, suggestions) {
      var $ele = $(this);
      if (suggestions && suggestions.length === 0) {
        $ele.attr('oldvalue', '');
      } else {
        $ele.attr('oldvalue', $ele.val());
      }
      if (!$ele.is(':focus')) {
        $ele.Autocomplete('hide');
      }
    },
  });

  $('#find_supplierc')
    .on({
      dblclick: function (e) {
        commodity_search_dia($('#find_supplierc').attr('oldvalue'));
      },
      keyup: function (e) {
        if (e.keyCode === 13 && !$('#find_supplierc').attr('oldvalue')) {
          commodity_search_dia();
        } else if (e.keyCode === 13) {
          $(this).blur();
        }
      },
    })
    .siblings('.glyphicon-search')
    .on('click', function (e) {
      commodity_search_dia($('#find_supplierc').attr('oldvalue'));
      e.stopPropagation();
    });
  $('#supplierPaymentVoList').change(function () {
    var sv = $(this).find('option:selected').index();
    $('#zjsy')
      .find('.zjsywp')
      .eq(sv)
      .css('display', 'inline')
      .siblings()
      .css('display', 'none');
  });
  $('#supplierSettlementVoList').change(function () {
    /****llj 联动****/
    if ($(this).val() == 2001) {
      $('#selectadvancePayment').val('1');
    } else {
      $('#selectadvancePayment').val('0');
    }

    if (
      $(this).find('option:selected').val() == '2001' ||
      $(this).find('option:selected').text() == '预付款'
    ) {
      if ($('#selectadvancePayment').val() == '0') {
        $('#selectadvancePayment')
          .find("option[value = '1']")
          .attr('selected', 'selected');
      }
    }
    var ov = $(this).find('option:selected').index();
    $('#jsfs')
      .find('.jsfswp')
      .eq(ov)
      .css('display', 'inline')
      .siblings()
      .css('display', 'none');
    let changeType = $('#supplierSettlementVoList').val();
    if (changeType === '2002' || changeType === '2003') {
      let opaStr = '';
      if (endTimeOptions && Object.keys(endTimeOptions).length > 0) {
        $('.moreTimeParents').removeClass('displaynone');
        endTimeOptions[changeType].forEach((item, index) => {
          let optionStr =
            '结算周期:' +
            item.settlementCycle +
            '天, 结算日:' +
            item.settlementDate +
            '天, 支付日:' +
            item.paymentDay +
            '日';
          opaStr +=
            "<option  value='" +
            item.settleMethod +
            index +
            "'>" +
            optionStr +
            '</option>';
        });
        $('#endTimeSettlementVoList').empty().append(opaStr);
      }
    } else {
      $('.moreTimeParents').addClass('displaynone');
    }
  });

  //计划量
  function rowSuppCode(arrivalPeriod) {
    var ttl = $('#X_Tablea').XGrid('getRowData');
    var huoqi = parseInt(arrivalPeriod);
    $.each(ttl, function (i) {
      var stockCount = parseFloat(ttl[i].productStockCur); //库存数量
      var productPackOnWayCount = parseFloat(ttl[i].productPackOnWayCount); //在途数量
      var productAdjustDay = parseFloat(ttl[i].productAdjustDay); //可调节天数
      var thirtyAverageSaleVolume = parseFloat(ttl[i].thirtyAverageSaleVolume); //日均销售数量
      console.log(
        'huoqi:' +
        huoqi +
        ',stockCount:' +
        stockCount +
        ',productPackOnWayCount' +
        productPackOnWayCount +
        ',productAdjustDay:' +
        productAdjustDay +
        ',thirtyAverageSaleVolume:' +
        thirtyAverageSaleVolume,
      );
      var jihualiang =
        (productAdjustDay + huoqi) * thirtyAverageSaleVolume -
        stockCount -
        productPackOnWayCount;
      console.log('jihualiang:' + jihualiang);
      console.log('jihualiang:' + jihualiang);
      if (jihualiang <= 0) {
        $('#X_Tablea #' + ttl[i].id)
          .find("td[row-describedby='supplyArrivalPeriod']")
          .html(huoqi);
        $('#X_Tablea #' + ttl[i].id)
          .find("td[row-describedby='productSuggestPlanCount']")
          .html(0);
      } else {
        $('#X_Tablea #' + ttl[i].id)
          .find("td[row-describedby='supplyArrivalPeriod']")
          .html(huoqi);
        if (parseInt(jihualiang) == '0') {
          $('#X_Tablea #' + ttl[i].id)
            .find("td[row-describedby='productSuggestPlanCount']")
            .html(0);
        } else if (parseInt(jihualiang) > 0) {
          var jihualiang1 = Math.ceil(jihualiang);
          $('#X_Tablea #' + ttl[i].id)
            .find("td[row-describedby='productSuggestPlanCount']")
            .html(jihualiang1);
        }
      }
      //(可调天数+供应商货期)*日均销售数量 - 当前库存数量 - 当前在途数量)
    });
  }

  function commodity_search_dia(val) {
    dialog({
      url: '/proxy-purchase/purchase/purchaseOrder/toSupplier?orgCode=' + $('#orgCode').val(),
      title: '供应商列表',
      width: $(window).width() * 0.9,
      height: '550px',
      data: val, // 给modal 要传递的 的数据
      onclose: function () {
        if (this.returnValue) {
          var data = this.returnValue;
          var simpleCode = data.simpleCodeList;
          var selectoverStr = '';
          var boolean = false;
          // if (data.overDueList != "") {
          //     selectoverStr = "该供应商{" + data.supplierCode + "}的{" + data.overDueList + "}资质已过期!<br/>";
          //     boolean = true;
          // }
          // if (boolean != true && data.overOneMouthList != "") {
          //     selectoverStr += "该供应商{" + data.supplierCode + "}的{" + data.overOneMouthList + "}资质即将过期!";
          // }
          // if (selectoverStr != "") {
          //     utils.dialog({content: selectoverStr, quickClose: true, timeout: 6000}).showModal();
          //     if (boolean) {
          //         return false;
          //     }
          // }
          if (data.supplierCode != $('#find_supplierc').val()) {
            rowSuppCode(data.arrivalPeriod);
            /*  $("#X_Tablea").XGrid('clearGridData');
                              rowEa(null);*/
          }
          $('#simpleCode').val(simpleCode);
          $("#supplierFinanceProfitRate").val(data.supplierFinanceProfitRate);
          //  console.log(data)
          $('#supplyBussinessUserId').val(data.supplyBussinessUserId);
          $('#arrivalPeriod').val(data.arrivalPeriod);
          $('#arrivalPeriod').attr('readonly', 'readonly');
          $('#supplyOperateId').val(data.id);
          $('#supplyOperateId').attr('readonly', 'readonly');
          //$("#arrivalPeriod").val(data.arrivalPeriod);
          $('#arrivalPeriod').attr('readonly', 'readonly');
          $('#find_supplier').val(data.supplierName);
          $('#transportFactory').val(data.supplierName);
          $('#supplierName').attr('readonly', 'readonly');
          $('#find_supplierc').val(data.supplierCode);
          $('#supplierId').val(data.id); //供应商ID
          $('#supplierName').val(data.supplyBussinessUserName);
          $('#supplyBussinessUserMobile').val(data.supplyBussinessUserMobile);
          $('#supplyBussinessUserMobile').attr('readonly', 'readonly');
          $('#supplierPaymentVoList').val(data.supplierPaymentVoList);

          $('#arrivalPeriod').val(
            data.arrivalPeriod == 'null' ? 1 : data.arrivalPeriod,
          );

          var ts =
            parseInt(data.arrivalPeriod == 'null' ? 1 : data.arrivalPeriod) + 1;
          var curDate = new Date();
          var preDate = new Date(curDate.getTime() + ts * 24 * 60 * 60 * 1000);

          var fmdate = new Date(preDate);
          var y = fmdate.getFullYear();
          var m = fmdate.getMonth() + 1;
          if (m <= 9) {
            m = '0' + m;
          }
          var d = fmdate.getDate();
          if (d <= 9) {
            d = '0' + d;
          }

          $('#expectDeliveryDate').val(y + '-' + m + '-' + d);

          $('#supplierSettlementVoList').val(data.supplierSettlementVoList);
          $('#supplierPaymentVoList').empty();
          $('#supplierSettlementVoList').empty();
          $('#listSupplyStoreAddress').empty();
          if (data.supplierPaymentVoList != '') {
            //解析付款方式
            var ts = JSON.parse(data.supplierPaymentVoList);
            //  console.log(ts);
            var opa = '';

            var opb = '';
            var xts = '';
            $.each(ts, function (i, item) {
              xts = item.childList[0].value;
              if (xts == null) {
                xts = '';
              }
              //   console.log(item.childList[0].name);
              opa +=
                '<option  value="' + item.code + '">' + item.name + '</option>';
              opb +=
                '<div class="input-group zjsywp"> <div class="input-group-addon" >' +
                item.childList[0].name +
                '</div>' +
                '<input type="text"   value="' +
                xts +
                '" class="form-control " readonly="readonly"   placeholder="账期"></div>';
            });
            $('#supplierPaymentVoList').empty().append(opa);
            $('#zjsy').empty().append(opb);
            if (
              $('#supplierSettlementVoList').find('option:selected').val() ==
              '2001' ||
              $('#supplierSettlementVoList').find('option:selected').text() ==
              '预付款'
            ) {
              if ($('#selectadvancePayment').val() == '0') {
                $('#selectadvancePayment')
                  .find("option[value = '1']")
                  .attr('selected', 'selected');
              }
            }
          }

          if (data.supplierSettlementVoList != '') {
            //解析结算方式
            var tx = JSON.parse(data.supplierSettlementVoList);
            // console.log(tx);
            var opx = '';
            var opy = '';
            $.each(tx, function (i, item) {
              opx +=
                '<option value="' + item.code + '">' + item.name + '</option>';
              opy += '<div class="jsfswp">';
              if (item.childList != null) {
                var vnull = '';
                $.each(item.childList, function (i, it) {
                  vnull = it.value;
                  if (vnull == null) {
                    vnull = '';
                    opy +=
                      '<div class="col-md-3"><div class="input-group"><div class="input-group-addon " >' +
                      it.name +
                      '</div>' +
                      '<input value="' +
                      vnull +
                      '" type="text"    class="form-control " placeholder="账期"></div></div>';
                  } else {
                    opy +=
                      '<div class="col-md-3"><div class="input-group"><div class="input-group-addon " >' +
                      it.name +
                      '</div>' +
                      '<input value="' +
                      vnull +
                      '" type="text"  readonly="readonly" class="form-control " placeholder="账期"></div></div>';
                  }
                });
              } else {
              }
              opy += '</div>';
            });

            $('#supplierSettlementVoList').empty().append(opx);
            getEndTimeMethod();
            // $("#jsfs").empty().append(opy);
          }

          if (data.listSupplyStoreAddress != '') {
            //解析仓库地址
            var tx1 = JSON.parse(data.listSupplyStoreAddress);
            var opx1 = '';
            var opy1 = '';
            $.each(tx1, function (i, item) {
              opx1 += '<option value="' + item + '">' + item + '</option>';
            });
            $('#listSupplyStoreAddress').empty().append(opx1);
          }

          //清空商品列表
          $('#X_Tablea')
            .setGridParam({
              data: [],
            })
            .trigger('reloadGrid');
          setTimeout(() => {
            // 放大镜
            /* $('#X_Tablea').find('[row-describedby="productPackCountSmall"] .glyphicon-search').css('display', (data.itemType == 1 ? "block" : 'none'))
                        if (data.itemType == 1) {
                            $('#X_Tablea').find('[row-describedby="productContainTaxPrice"] input').prop('disabled', (data.ifUpdatePrice == 1 ? false : true))
                        }*/
          }, 1000);
        } else {
          $('#supplierId').val('');
          $('#find_supplierc').val('');
        }
      },
    }).showModal();
  }

  function isNotNull(value) {
    if (value != '' && value != null && value != undefined && !isNaN(value)) {
      return true;
    }
    return false;
  }

  function delRepeat(dataList) {
    var temp = {},
      len = dataList.length;
    for (var i = 0; i < len; i++) {
      var tmp = dataList[i].productCode;
      if (!temp.hasOwnProperty(tmp)) {
        //hasOwnProperty用来判断一个对象是否有你给出名称的属性或对象
        temp[tmp] = dataList[i];
      }
    }
    len = 0;
    var tempArr = [];
    for (var i in temp) {
      tempArr[len++] = temp[i];
    }
    return tempArr;
  }

  function commodity_search_di(xid) {
    $('#bodyscrolf').css('overflow', 'hidden');
    var channelId = $('#channelId').val();
    var ttl = $('#X_Tablea').XGrid('getRowData');
    var supplier = $('#find_supplier').val();
    var arrivalPeriod = $('#arrivalPeriod').val();
    var redid = [];
    if (supplier == '') {
      utils
        .dialog({
          content: '供应商不能为空！',
          quickClose: true,
          timeout: 4000,
        })
        .showModal();
      return false;
    } else if (arrivalPeriod == '') {
      utils
        .dialog({
          content: '供应商货期不能为空！',
          quickClose: true,
          timeout: 4000,
        })
        .showModal();
      return false;
    }

    var pays = [];

    if ($('#simpleCode').val() == '') {
      utils
        .dialog({
          content: '供应商的经营范围为空，无法选择商品！',
          quickClose: true,
          timeout: 4000,
        })
        .showModal();
      return false;
    }
    var disableRows = [];
    var ttl = $('#X_Tablea').XGrid('getRowData');
    console.log(ttl);
    if (ttl) {
      if (ttl.length) {
        for (i = 0; i < ttl.length; i++) {
          disableRows.push(ttl[i].id);
        }
      } else {
        disableRows.push(ttl.id);
      }
    }
    console.log(disableRows);
    var selRow = $('#X_Tablea').XGrid('getSeleRow');
    dialog({
      url:
        '/proxy-purchase/purchase/purchaseOrder/toPurchaseProduct?supplyArrivalPeriod=' +
        $('#arrivalPeriod').val() +
        '&supplierId=' +
        $('#supplierId').val() +
        '&supplierName=' +
        supplier +
        '&orderType=' +
        $('#orderType').val() +
        '&channelId=' +
        $('#channelId').val() +
        '&supplierCode=' +
        $('#find_supplierc').val() +
        '&orgCode=' +
        $('#orgCode').val() +
        '&centralizedPurchaseType=' +
        $('#selectCentralizedPurchaseType').val(),
      title: '商品列表',
      width: $(window).width() * 0.9,
      height: $(window).height() * 0.7,
      data: {
        val: '',
        colData: disableRows,
        simpleCode: $('#simpleCode').val(),
      }, // 给modal 要传递的 的数据
      onclose: function () {
        $('#bodyscrolf').css('overflow', 'auto');

        if (this.returnValue) {
          var tstb = [];
          var cltb = [];
          var data = this.returnValue;

          var rdt = $('#X_Tablea').XGrid('getRowData');

          //将商品列表的入库单+商品编号放到集合中
          tstb = $.map(rdt, function (i, v) {
            return v.productCode;
          });
          data = delRepeat(data);
          var remainDaysList = [],
            noDaysList = [],
            endData = [],
            strongControlData = [];
          data = $(data).map(function (i, v) {
            if ($('#orderType').val() == '0' && $('#channelId').val() == '1') {
              if (
                strongControl.indexOf($('#orgCode').val()) == -1 &&
                $('#selectCentralizedPurchaseType').val() != '11' &&
                $('#selectCentralizedPurchaseType').val() != '20'
              ) {
                if (parseFloat(v.productLastPrice) > 0) {
                  v.productContainTaxPrice = v.productLastPrice;
                }
              }
            } else {
              if (parseFloat(v.productLastPrice) > 0) {
                v.productContainTaxPrice = v.productLastPrice;
              }
            }
            cltb.push(v.productCode);
            return v;
          });
          for (let i = 0; i < cltb.length; i++) {
            if (tstb.indexOf(cltb[i]) == -1) {
              if (
                0 <= parseFloat(data[i].remainDays) &&
                parseFloat(data[i].remainDays) <= 30 &&
                data[i].remainDays != ''
              ) {
                remainDaysList.push(data[i].productCode);
                redid.push(data[i].id);
              }
              if (
                parseFloat(data[i].remainDays) < 0 &&
                data[i].remainDays != ''
              ) {
                noDaysList.push(data[i].productCode);
              }
              if (
                $('#orderType').val() == '0' &&
                $('#channelId').val() == '1'
              ) {
                if (
                  strongControl.split(',').indexOf($('#orgCode').val()) >= 0 ||
                  $('#selectCentralizedPurchaseType').val() == '11' ||
                  $('#selectCentralizedPurchaseType').val() == '20'
                ) {
                  if (
                    data[i].productContainTaxPrice == null ||
                    data[i].productContainTaxPrice == ''
                  ) {
                    strongControlData.push(data[i].productCode);
                  }
                }
              }
              endData.push(data[i]);
            } else {
              pays.push(data[i].productCode);
            }
          }

          rowEvent();
          //判断商品即将过期的标红：去掉
          if (redid.length) {
            for (var i = 0; i < redid.length; i++) {
              $('#X_Tablea  #' + redid[i]).removeClass('redback');
              $('#X_Tablea  #' + redid[i]).addClass('maize');
            }
          }
          var contentStr = '';
          if (remainDaysList.length > 0) {
            contentStr +=
              '商品{' + remainDaysList.join(',') + '}，即将过期！<br/>';
          }
          if (noDaysList.length > 0) {
            contentStr += '商品{' + noDaysList.join(',') + '}，已过期！<br/>';
          }
          if (pays.length > 0) {
            contentStr += '商品{' + pays.join(',') + '}，存在重复！';
          }
          if (strongControlData.length > 0) {
            contentStr +=
              '商品{' +
              strongControlData.join(',') +
              '}，没有价目表记录，请先维护！';
          }
          if (contentStr != '') {
            utils
              .dialog({
                title: '提示',
                content: contentStr,
                onclose: function () {
                  endData.forEach(function (item) {
                    tableAddRow('X_Tablea', item);
                  });
                },
              })
              .showModal();
          } else {
            endData.forEach(function (item) {
              tableAddRow('X_Tablea', item);
            });
          }
        }
      },
    }).showModal();
    return false;
  }

  //金额计算
  function rowEvent(xtype) {
    if (null != xtype) {
      var obj = xtype.rowData;
      //备注校验
      var obj = xtype.rowData;
      if (obj.varietiesAgreement == '1' && obj.varietiesChannel == '0') {
        $('#productRemark_input_' + obj.sort + ' input').attr(
          'class',
          'form-control {validate:{ required :false, minlength :5}}',
        );
      } else if (obj.canPinNumber > 75) {
        // 可销天数
        $('#productRemark_input_' + obj.sort + ' input').attr(
          'class',
          'form-control {validate:{ required :false, minlength :5}}',
        );
      } else if (
        obj.productContainTaxPrice > obj.productLastPrice &&
        obj.productLastPrice > 0
      ) {
        //商品本次采购单价大于最近购进价 并且最后一submitAssert次购进价大于0
        $('#productRemark_input_' + obj.sort + ' input').attr(
          'class',
          'form-control {validate:{ required :false, minlength :5}}',
        );
      } else {
        $('#productRemark_input_' + obj.sort + ' input').attr(
          'class',
          'form-control',
        );
      }
      var tr = $('#X_Tablea').XGrid('getRowData', xtype.rowData.id);
      var xid = xtype.rowData.id;
      var pa = parseFloat(tr.productContainTaxPrice);
      var pb = parseFloat(tr.productAppContainTaxPrice);
      var productCode = tr.productCode;
      var pc = parseFloat(tr.productPackCountSmall);
      var pd = parseFloat(tr.productUpperLimitStock); //库存上限
      var pcwayCount = parseFloat(tr.productPackOnWayCount); //在途数量
      var stockCount = parseFloat(tr.productStockCur); //库存数量
      var standardPrice = 0; //标准单价
      if (tr.standardPrice != '') {
        var standardPrice = parseFloat(tr.standardPrice); //标准单价
      }
    }
    var ttl = $('#X_Tablea').XGrid('getRowData');
    var sumMoney = 0;
    var sumTaxMoney = 0;
    var priceTaxSum = 0;

    $.each(ttl, function (i) {
      if (ttl[i].productPackCountSmall == '') {
        ttl[i].productPackCountSmall = '0';
      }
      if (ttl[i].productContainTaxPrice == '') {
        ttl[i].productContainTaxPrice = '0';
      }
      if (ttl[i].productAppContainTaxPrice == '') {
        ttl[i].productAppContainTaxPrice = '0';
      }
      if (ttl[i].productPackCountSmall == '') {
        ttl[i].productPackCountSmall = '0';
      }
      //实时计算周转天数
      if (
        ttl[i].productPackCountSmall != '' &&
        ttl[i].sixtyAverageDailySales != ''
      ) {
        let orgCode = $('[name="orgCode"]').val();
        let channelId = $('#channelId').val();
        let newone = 0;
        //可用库存
        let productStockCur = Number(ttl[i].productStockCur);
        //订单数量
        let productPackCountSmall = Number(ttl[i].productPackCountSmall);
        //60日均天销量
        let productSixtySaleForDay = Number(ttl[i].sixtyAverageDailySales);
        //处理当前周转天数
        $.ajax({
        url: '/proxy-purchase/purchase/product/getProductListNumsByCondition',
        method: 'POST',
        data: JSON.stringify({
            orgCode: orgCode,
            channelId: channelId,
            productCodeList: [ttl[i].productCode],
        }),
        dataType: 'json',
        contentType: 'application/json',
        success: function (resp) {
            if(resp.code === 0){
            if(resp.result != null){
                let name =  '' + orgCode + '-' + channelId + '-' + ttl[i].productCode;
                newone = resp.result[name] == undefined ? 0 : resp.result[name];
            }
            if (productSixtySaleForDay > 0) {
                let turnoverDefaultDays = Math.round(
                (productStockCur + productPackCountSmall + newone) / productSixtySaleForDay,
                );
                $('#X_Tablea  #' + (ttl[i].sort ? ttl[i].sort : ttl[i].id))
                .find("td[row-describedby='turnoverDefaultDays']")
                .html(turnoverDefaultDays);
            }
            }
        },
        error: () => { 
            if (productSixtySaleForDay > 0) {
            let turnoverDefaultDays = Math.round(
                (productStockCur + productPackCountSmall + newone) / productSixtySaleForDay,
            );
            $('#X_Tablea  #' + (ttl[i].sort ? ttl[i].sort : ttl[i].id))
                .find("td[row-describedby='turnoverDefaultDays']")
                .html(turnoverDefaultDays);
            }
        },
        });
        // //可用库存
        // let productStockCur = Number(ttl[i].productStockCur);
        // //订单数量
        // let productPackCountSmall = Number(ttl[i].productPackCountSmall);
        // //60日均天销量
        // let productSixtySaleForDay = Number(ttl[i].sixtyAverageDailySales);
        // if (productSixtySaleForDay > 0) {
        //   let turnoverDefaultDays = Math.round(
        //     (productStockCur + productPackCountSmall) / productSixtySaleForDay,
        //   );
        //   $('#X_Tablea  #' + (ttl[i].sort ? ttl[i].sort : ttl[i].id))
        //     .find("td[row-describedby='turnoverDefaultDays']")
        //     .html(turnoverDefaultDays);
        // }
      }
      if (
        ttl[i].productPackCountSmall != '' &&
        ttl[i].productContainTaxPrice != ''
      ) {
        var pa = parseFloat(ttl[i].productContainTaxPrice);
        var pb = parseFloat(ttl[i].productAppContainTaxPrice);
        var productCode = ttl[i].productCode;
        var pc = parseFloat(ttl[i].productPackCountSmall);
        var pd = parseFloat(ttl[i].productUpperLimitStock); //库存上限
        var pcwayCount = parseFloat(ttl[i].productPackOnWayCount); //在途数量
        var stockCount = parseFloat(ttl[i].productStockCur); //库存数量

        var productEntryTax = ttl[i].productEntryTax.replace('%', '');
        var taxRate = productEntryTax / 100;
        var sumMoneytr =
          parseFloat(ttl[i].productContainTaxPrice) / (1 + parseFloat(taxRate)); //不含税单价 公式为：含税价 /（1+ 税率）
        var productNoTaxMoney = parseFloat(
          sumMoneytr * parseInt(ttl[i].productPackCountSmall),
        ).toFixed(2); //不含税金额  公式为：不含税价 * 退货数量
        var taxMoney =
          ((parseFloat(ttl[i].productContainTaxPrice) *
            parseInt(ttl[i].productPackCountSmall)) /
            (1 + parseFloat(taxRate))) *
          parseFloat(taxRate); //税额：   公式为：含税单价 * 退货数量 /（1+税率）* 税率
        var productContainTaxMoneyProduct = parseFloat(
          parseFloat(ttl[i].productContainTaxPrice) *
          parseInt(ttl[i].productPackCountSmall),
        ).toFixed(2); //含税金额  公式为：含税价 * 采购数量
        console.log(
          productContainTaxMoneyProduct + '-------------' + productNoTaxMoney,
        );
        sumMoney += parseFloat(productNoTaxMoney);
        sumTaxMoney +=
          parseFloat(productContainTaxMoneyProduct) -
          parseFloat(productNoTaxMoney);
        priceTaxSum += parseFloat(productContainTaxMoneyProduct);

        var productEntryTaxtr1 = ttl[i].productEntryTax.replace('%', '');
        var taxRatetr1 = productEntryTaxtr1 / 100;
        var sumMoneytr1 =
          parseFloat(ttl[i].productContainTaxPrice) /
          (1 + parseFloat(taxRatetr1)); //不含税单价 公式为：含税价 /（1+ 税率）
        var productContainTaxMoney1 =
          parseFloat(ttl[i].productContainTaxPrice) *
          parseInt(ttl[i].productPackCountSmall); //含税金额  公式为：含税价 * 采购数量
        var gainMoney =
          ttl[i].productLastPrice == 0
            ? 0
            : parseFloat(
              ttl[i].productContainTaxPrice - ttl[i].productLastPrice,
            ) / parseFloat(ttl[i].productLastPrice);
        if (isNotNull(sumMoneytr1)) {
          $('#X_Tablea #' + ttl[i].sort)
            .find("td[row-describedby='productUnitPrice']")
            .html(sumMoneytr1.toFixed(6)); //不含税单价
        } else {
          $('#X_Tablea #' + ttl[i].sort)
            .find("td[row-describedby='productUnitPrice']")
            .html(0); //不含税单价
        }
        if (isNotNull(productContainTaxMoney1)) {
          //含税金额
          $('#X_Tablea  #' + ttl[i].sort)
            .find("td[row-describedby='productContainTaxMoney']")
            .html(productContainTaxMoney1.toFixed(2));
          //采购价涨幅
          $('#X_Tablea  #' + ttl[i].sort)
            .find("td[row-describedby='gain']")
            .html(
              gainMoney.toFixed(4)
                ? (gainMoney.toFixed(4) > 0
                  ? (gainMoney.toFixed(4) * 100).toFixed(2)
                  : 0) + '%'
                : '0%',
            );
        } else {
          //含税金额
          $('#X_Tablea  #' + ttl[i].sort)
            .find("td[row-describedby='productContainTaxMoney']")
            .html(0);
          //采购价涨幅
          $('#X_Tablea  #' + ttl[i].sort)
            .find("td[row-describedby='gain']")
            .html('0%');
        }
      }
    });
    if (!isNaN(sumMoney)) {
      $('#orderSumMoney').html(sumMoney.toFixed(2));
    } else {
      $('#orderSumMoney').html(0);
    }

    if (!isNaN(sumTaxMoney)) {
      $('#orderSumTaxMoney').html(sumTaxMoney.toFixed(2));
    } else {
      $('#orderSumTaxMoney').html(0);
    }

    if (!isNaN(priceTaxSum)) {
      $('#orderPriceTaxSum').html(priceTaxSum.toFixed(2));
    } else {
      $('#orderPriceTaxSum').html(0);
    }

    if (!isNaN(priceTaxSum)) {
      $('#orderPriiceTaxSumChineseNum').html(
        chineseNumber(priceTaxSum.toFixed(2)),
      );
    } else {
      $('#orderPriiceTaxSumChineseNum').html(0);
    }
  }

  //新增行，不填写数据
  $('#addRow').click(function () {
    commodity_search_di();
  });
  $('#deleRow').click(function () {
    var selRow = $('#X_Tablea').XGrid('getSeleRow');

    if (!selRow) {
      utils
        .dialog({
          content: '没有选中任何行！',
          quickClose: true,
          timeout: 3000,
        })
        .showModal();
      return false;
    }
    var d = dialog({
      title: '提示',
      content: '是否继续执行删除操作？',
      okValue: '确定',
      ok: function () {
        deletex();

        d.close().remove();

        return false;
      },
      cancelValue: '取消',
      cancel: function () { },
    });
    d.show();
  });

  function invalideValidityDate() {
    var ttl = $('#X_Tablea').XGrid('getRowData');
    var ttlf = []
    var ttlT = []
    $('#X_Tablea').XGrid('getRowData').map(item => {
      if (item.validityDate === 'false') {
        ttlf.push(item)
      } else {
        if (!item.productValidityDate || !item.productProduceDate) {
          ttlT.push(item)
        }
      }
    })

    if (ttl.length !== 0 && ttl.length === ttlf.length) {
      return { pass: false, code: "1", msg: "请确认本订单无近效期购进的品种，若仓库收货为近效期将直接拒收！" };
    } else if (ttlT.length !== 0) {
      return { pass: false, code: "2", msg: "当商品为近效期购进时，生产日期和有效期至必须填写！！" };
    } else {
      return { pass: true, code: "0", msg: "" };
    }
  }

  // 提交审核
  $('#submitAssert').click(function () {
    let invalidateOBJ = invalideValidityDate()
    if (invalidateOBJ.pass === false) {
      utils
        .dialog({
          title: '提示',
          content:
            invalidateOBJ.msg,
          okValue: '确定',
          ok: function () {
            if (invalidateOBJ.code === '1') {
              submitAssertFunc();
            }
          },
          cancelValue: '取消',
          cancel: function () { },
        })
        .showModal();
      return;
    }
    submitAssertFunc();
  });

  function submitAssertFunc() {
    // 近效期提示
    $.ajax({
      type: 'POST',
      url:
        '/proxy-purchase/purchase/supplier/getSuppliserValidity?organBaseId=' +
        $('#supplierId').val(),
      async: false,
      error: function () { },
      success: function (data) {
        if (data.code == -1) {
          utils
            .dialog({
              title: '提示',
              content: data.msg,
              okValue: '确认',
              ok: function () {
                submitBlackListTip();
              },
              cancelValue: '取消',
              cancel: function () { },
            })
            .showModal();
        } else {
          submitBlackListTip();
        }
      },
    });
    function submitBlackListTip(){
      // 琦玉
    if( $('#selectadvancePayment').val() == '1'){
      $.ajax({ //请求进行校验是否黑名单
        type: 'GET',
        url: '/proxy-purchase/purchase/supplier/supplier-black-judge?supplierCode='+ $("[name='supplierCode']").val() +'&orgCode='+ $("[name='orgCode']").val() ,
        async: false,
        contentType: 'application/json;charset=utf-8',
        dataType: 'json',
        success: function (response) {
          let { result, code, msg } = response;
          if(result != null){
            utils
              .dialog({
                title: '提示',
                content: result,
                okValue: '确定',
                ok: function () {
                  return 
                },
              })
              .showModal();
          }else{
            submitAction()
          }
      }
      });
    }else{
      submitAction()
    }
    }
    function submitAction() {
      var transportFactory = $("input[name='transportFactory']").val();
      if (transportFactory == '') {
        $('#submitAssert').attr('disabled', false);
        utils
          .dialog({
            content: '承运单位不能为空！',
            quickClose: true,
            timeout: 1000,
          })
          .showModal();
        return false;
      }
      var transportModel = $("select[name='transportModel']").val();
      if (transportModel == '') {
        $('#submitAssert').attr('disabled', false);
        utils
          .dialog({
            content: '承运方式不能为空！',
            quickClose: true,
            timeout: 1000,
          })
          .showModal();
        return false;
      }
      var transportTime = $("input[name='transportTime']").val();
      if (transportTime == '') {
        $('#submitAssert').attr('disabled', false);
        utils
          .dialog({
            content: '启运时间不能为空！',
            quickClose: true,
            timeout: 1000,
          })
          .showModal();
        return false;
      }
      $('#submitAssert').attr('disabled', true).attr('flag', 1);
      setTimeout(function () {
        if ($('#submitAssert').attr('flag') === 1) {
          $('#submitAssert').attr('disabled', false);
        }
      }, 6000);
      var orderTy = $('#orderType').val();
      if (orderTy == 2) {
        var orgRatePercent = purchaseOrgRate / 100;
        var totalPrice = $('#orderPriceTaxSum').text();
        var orgRateAmount = (orgRatePercent * totalPrice).toFixed(2); // 调拨费率由2%需变更为0.5%，对应的预估调拨费用为总金额*调拨费率逻辑保持不变；
        $('#forecastAllotCost').val(orgRateAmount);
      }

      $('#ifSpecialDrugs').val(0);
      if (
        $('#supplierSettlementVoList').val() == '2001' ||
        $('#supplierSettlementVoList').text() == '预付款'
      ) {
        if ($('#selectadvancePayment').val() == '0') {
          $('#submitAssert').attr('disabled', false);
          utils
            .dialog({
              content: '【结算方式】与【是否预付】不匹配，请重新选择！',
              quickClose: true,
              timeout: 4000,
            })
            .showModal();
          return false;
        }
      }
      var supplierCode = $("[name='supplierCode']").val();
      var orderNo = $("input[name='orderNo']").val();
      if (orderNo == '') {
        $('#submitAssert').attr('disabled', false);
        utils
          .dialog({
            content: '采购订单号不能为空！',
            quickClose: true,
            timeout: 1000,
          })
          .showModal();
        return false;
      }
      //如果送货方式为“委外运输时，这三个字段为必填项，否则不能提交，点击时弹出提示“委外运输时，承运方式、承运单位、启运时间为必填项”；
      //大客户不判断
      var orderType = $("[name='orderType']").val();
      if (orderType != '1') {
        var transportFactory = $("input[name='transportFactory']").val();
        var transportModel = $("select[name='transportModel']").val();
        var transportTime = $("input[name='transportTime']").val();
        var deliveryMethod = $("select[name='deliveryMethod']").val();
        if (
          deliveryMethod == '3' &&
          (transportFactory == '' ||
            transportModel == '' ||
            transportModel == 'undefined' ||
            transportTime == '')
        ) {
          $('#submitAssert').attr('disabled', false);
          utils
            .dialog({
              content: '委托运输时，承运方式、承运单位、启运时间为必填项！',
              quickClose: true,
              timeout: 3000,
            })
            .showModal();
          return false;
        }
      }

      var ttl = $('#X_Tablea')
        .XGrid('getRowData')
        .filter(function (item, index) {
          if (!item.productCode) {
            $('#X_Tablea').XGrid('delRowData', item.sort);
          } else {
            return item;
          }
        }); //商品数据
      var plc = [];
      var mflase = false; //是否包含特殊药品
      var nflase = false; //是否包含普通药品
      var mpName = ''; //特殊药品
      var channelIdx = $('#channelId').val();
      for (var i = 0; i < ttl.length; i++) {
        //是否新品
        if (ttl[i].isNew == "是") {
          ttl[i].isNew = "1"
        } else if (ttl[i].isNew == "否") {
          ttl[i].isNew = "0"
        }
        if (
          orderType != '1' &&
          ttl[i].centralizedPurchaseType !=
          $('#selectCentralizedPurchaseType').val()
        ) {
          $('#submitAssert').attr('disabled', false);
          utils
            .dialog({
              content:
                ttl[i].productName +
                '商品为集采/地采商品，与订单表头属性不一致，请修改。',
              quickClose: true,
              timeout: 1000,
            })
            .showModal();
          return false;
        }
        var opcel = $(
          '#' + ttl[i].id + ' td[row-describedby="productOriginPlacestrs"]',
        ).html();
        if (opcel) {
          if (opcel.indexOf('select') == -1) {
            plc.push(opcel);
            ttl[i].productOriginPlace = opcel;
            ttl[i].productOriginPlacestrs = {};
          } else {
            plc.push($('#' + ttl[i].id + '  select').val());
            ttl[i].productOriginPlace = $('#' + ttl[i].id + '  select').val();
            ttl[i].productOriginPlacestrs = {};
          }
        }
        // else{
        //     ttl[i].productOriginPlacestrs = {};
        // }
        if (ttl[i].ifOldApprovalNumber === '是') {
          ttl[i].ifOldApprovalNumber = '1';
        } else {
          ttl[i].ifOldApprovalNumber = '0';
        }
        if (ttl[i].varietiesAgreement === 1 && ttl[i].varietiesChannel === 0) {
          var reg = /[\u4e00-\u9fa5]/;
          var datas = ttl[i].productRemark;
          var nums = 0;
          if (datas.trim().length > 0) {
            datas = datas.split('');
            datas.map(function (item) {
              if (reg.test(item)) nums++;
            });
          }
        }
        if (
          ttl[i].varietiesAgreement === '是' ||
          ttl[i].varietiesAgreement === '1'
        ) {
          ttl[i].varietiesAgreement = '1';
        } else if (
          ttl[i].varietiesAgreement === '否' ||
          ttl[i].varietiesAgreement === '0'
        ) {
          ttl[i].varietiesAgreement = '0';
        }

        //验证是否有商品经营范围是否有为二精 或蛋肽
        //如果有则该单采购员必须为李林才能下单 (采购员账号参照下图)，
        // 否则弹出提示“特殊药品需指定特定采购员采购，不能下单”
        var scopeofOperation = ttl[i].scopeOfOperation;
        var account = $('#account').val();
        var sysOrgCode = $('#sysOrgCode').val();

        if (
          sysOrgCode == '013' &&
          account != 'lilin00' &&
          (scopeofOperation == 'DELJSYP' || scopeofOperation == 'DBTHZJTLJS')
        ) {
          $('#submitAssert').attr('disabled', false);
          utils
            .dialog({
              content: '特殊药品需指定特定采购员采购，不能下单',
              quickClose: true,
              timeout: 1000,
            })
            .showModal();
          return false;
        }
        if (
          sysOrgCode == '013' &&
          (scopeofOperation == 'DELJSYP' || scopeofOperation == 'DBTHZJTLJS')
        ) {
          mflase = true;
          mpName = mpName + ttl[i].productName + ',';
        }
        if (
          sysOrgCode == '013' &&
          scopeofOperation != 'DELJSYP' &&
          scopeofOperation != 'DBTHZJTLJS'
        ) {
          nflase = true;
        }
        if (
          (ttl[i].storageConditions == 4 || ttl[i].storageConditions == 5) &&
          !$('#transportTime').val()
        ) {
          $('#submitAssert').attr('disabled', false);
          utils
            .dialog({
              content:
                ttl[i].productName + '药品为冷冻冷藏商品，需填写"启运时间"',
              quickClose: true,
              timeout: 1000,
            })
            .showModal();
          return false;
        }
        var outnumberedProducts = '';
        var num = Number(ttl[i].productPackCountSmall);
        let limitVal =
          ttl[i]['centralizedPurchaseType'] != '0'
            ? $('#jcAdjustPercentageLimit').val()
            : $('#adjustPercentageLimit').val();
        var interval = Math.floor(
          Number(ttl[i].productStockCur / 100) * Number(limitVal),
        );
        if (num > interval) {
          outnumberedProducts +=
            ttl[i].productName + '(' + ttl[i].productCode + ') ';
        }
      }
      //特殊药品采购订单需单独下单，不能和普药同时下在一个订单内
      if (mflase == true && nflase == true) {
        if (mpName != '') {
          mpName = mpName.substring(0, mpName.length - 1);
        }
        $('#submitAssert').attr('disabled', false);
        utils
          .dialog({
            content: mpName + '商品为特殊药品，不能与普药一起下单。',
            quickClose: true,
            timeout: 1000,
          })
          .showModal();
        return false;
      }

      var tx = $('#X_Tablea').XGrid('getRowData'); //商品数据
      var ttx = $('#basicInfo').serializeToJSON();

      if (orderType != '1') {
        var channelName = $('#channelId_inp').val();
        ttx.channelName = channelName;
      } else {
        ttx.channelId = '1';
        ttx.channelName = 'YBM';
        channelIdx = '1';
      }

      $('#sumForm')
        .find('span.colmd8')
        .each(function (i) {
          ttx[$(this).attr('name')] = $(this).text();
        });
      let supplierSettlementIndex = $('#supplierSettlementVoList').val();
      if (
        supplierSettlementIndex === '2002' ||
        supplierSettlementIndex === '2003'
      ) {
        if (
          !$('#endTimeSettlementVoList').val() ||
          $('#endTimeSettlementVoList').val() === ''
        ) {
          utils
            .dialog({
              content: '请先选择结算时间信息',
              quickClose: true,
              timeout: 3000,
            })
            .showModal();
          $('#submitAssert').attr('disabled', false);
          return false;
        }
        let endTimeStr = $('#endTimeSettlementVoList').val();
        let endTimeIndex = endTimeStr.substring(4, endTimeStr.length);
        let endTimeObj = endTimeOptions[supplierSettlementIndex][endTimeIndex];
        ttx['settlementCycle'] = endTimeObj.settlementCycle + '天';
        ttx['settlementDay'] = endTimeObj.settlementDate + '日';
        ttx['paymentDay'] = endTimeObj.paymentDay + '日';
      }
      var orderType = $("[name='orderType']").val();
      var checkAppList = [];
      var checkOfTips = [];
      var checkPriceList = [];
      var productCodeList = [];
      //负毛利需要校验的商品集合
      var checkProudctList = [];
      var flag = false;

      // 调拨采购时校验
      if (orderType != '1' && itemType == 1) {
        // 调拨数量提示
        if (outnumberedProducts.trim() != '') {
          utils
            .dialog({
              content:
                outnumberedProducts +
                '商品调拨数量大于可调拨上限，请修改数量。',
              cancelValue: '返回',
              cancel: function () { },
            })
            .showModal();
          $('#submitAssert').attr('disabled', false);
          return false;
        } else {
          if (ttl.length >= 1) {
            var orgRatePercent = purchaseOrgRate / 100;
            var totalPrice = $('#orderPriceTaxSum').text();
            var orgRateAmount = (orgRatePercent * totalPrice).toFixed(2); // 拨费率由2%需变更为0.5%，对应的预估调拨费用为总金额*调拨费率逻辑保持不变；
            var contentStr =
              '申请调拨总金额为' +
              totalPrice +
              '元,' +
              '调拨费率为' +
              purchaseOrgRate +
              '%,预估调拨费用为' +
              orgRateAmount +
              '元.';
            utils
              .dialog({
                title: '调拨费用提醒',
                content: contentStr,
                okValue: '确认继续',
                ok: function () {
                  validateInfo(
                    ttl,
                    supplierCode,
                    channelIdx,
                    productCodeList,
                    checkAppList,
                    checkOfTips,
                    checkPriceList,
                    checkProudctList,
                    flag,
                    ttx,
                  );
                },
                cancelValue: '返回',
                cancel: function () {
                  $('#submitAssert').attr('disabled', false);
                },
              })
              .showModal();
          } else {
            $('#submitAssert').attr('disabled', false);
          }
        }
      } else {
        doNextTask(
          ttl,
          channelIdx,
          productCodeList,
          checkAppList,
          checkOfTips,
          checkPriceList,
          checkProudctList,
          flag,
          ttx,
        );
      }
    }
  }
  var validateInfo = function (
    ttl,
    supplierCode,
    channelIdx,
    productCodeList,
    checkAppList,
    checkOfTips,
    checkPriceList,
    checkProudctList,
    flag,
    ttx,
  ) {
    parent.showLoading({ hideTime: 9999 });
    var orderStatus = 1; //采购订单状态
    var productCodes = []; //商品编码
    var productPackCountSmalls = []; //商品采购数量
    var productContainTaxPrices = []; //调拨采购单价
    var productNames = []; //商品名

    $.each(ttl, function (index, item) {
      productCodes.push(ttl[index].productCode);
      productPackCountSmalls.push(ttl[index].productPackCountSmall);
      productContainTaxPrices.push(ttl[index].productContainTaxPrice);
      productNames.push(ttl[index].productName);
    });
    $.ajax({
      method: 'POST',
      url: '/proxy-purchase/purchase/purchaseOrder/checkOrderRule',
      dataType: 'json',
      data: {
        callOutSupplierCode: $('#find_supplierc').val(),
        channelId: $('#channelId').val(),
        orderNo: $('#purchaseOrderNo').val(),
        orderStatus: orderStatus,
        storehouseAddress: $('#storehouseAddress').val(),
        productCodes: productCodes.join(','),
        productPackCountSmalls: productPackCountSmalls.join(','),
        productContainTaxPrices: productContainTaxPrices.join(','),
        productNames: productNames.join(','),
        remark: $('#supplyRemark').val(),
        centralizedPurchaseType: $('#selectCentralizedPurchaseType').val(),
      },
    }).done(function (data) {
      parent.hideLoading();
      if (data.code === 1) {
        utils
          .dialog({
            title: '提示',
            content: data.msg,
            okValue: '确定',
            ok: function () { },
          })
          .showModal();
        $('#submitAssert').attr('disabled', false);
      } else {
        console.log('PRAJNA', flag);
        doNextTask(
          ttl,
          channelIdx,
          productCodeList,
          checkAppList,
          checkOfTips,
          checkPriceList,
          checkProudctList,
          flag,
          ttx,
        );
      }
    });
    $('#submitAssert').attr('disabled', false);
  };
  var doNextTask = function (
    ttl,
    channelIdx,
    productCodeList,
    checkAppList,
    checkOfTips,
    checkPriceList,
    checkProudctList,
    flag,
    ttx,
  ) {
    if (ttl.length >= 1) {
      $.each(ttl, function (i, item) {
        var productPackCountSmall = item.productPackCountSmall;
        var productContainTaxPrice = item.productContainTaxPrice;
        var pa = parseFloat(item.productContainTaxPrice);
        var pb = parseFloat(item.productAppContainTaxPrice);
        var cp = parseFloat(item.chainGuidePrice);
        var productCode = item.productCode;
        var productName = item.productName;
        var productLastPrice = parseFloat(item.productLastPrice);

        if (channelIdx == '1') {
          if (pa > pb || pa > cp) {
            checkAppList.push(
              productCode + ' ' + productName + ' 备注：' + item.productRemark,
            );
            checkProudctList.push(productCode);
          }
        }

        if (item.varietiesAgreement === 1 && item.varietiesChannel === 0) {
          checkOfTips.push(
            productCode + ' ' + productName + ' 备注：' + item.productRemark,
          );
        }
        var price = pa - productLastPrice;
        if (price > 0 && productLastPrice !== 0) {
          checkPriceList.push(
            productCode + ' ' + productName + ',高出' + price.toFixed(2) + '元',
          );
        }
        if (
          (ttl[i].giftFlag == 0 &&
            (!productContainTaxPrice || productContainTaxPrice < 0.01)) ||
          (ttl[i].giftFlag == 1 &&
            (!productContainTaxPrice || productContainTaxPrice < 0))
        ) {
          $('#submitAssert').attr('disabled', false);
          utils
            .dialog({
              content:
                '含税单价不能为空；且药品必须大于等于0.01元、赠品必须大于等于0元！',
              quickClose: true,
              timeout: 1000,
            })
            .showModal();
          flag = true;
          return false;
        } else if (
          productPackCountSmall <= 0 ||
          productPackCountSmall.length == 0
        ) {
          $('#submitAssert').attr('disabled', false);
          utils
            .dialog({
              content: '订单数量不能为空,或者小于等于零的数！',
              quickClose: true,
              timeout: 1000,
            })
            .showModal();
          flag = true;
          return false;
        }
        productCodeList.push(ttl[i].productCode);
      });
      if (flag) {
        $('#submitAssert').attr('disabled', false);
        return false;
      }
    }
    if (ttl.length >= 1) {
      $.ajax({
        method: 'POST',
        url: '/proxy-purchase/purchase/productSupplier/queryProductSupplierByCode',
        dataType: 'json',
        cache: false,
        data: {
          productCodeList: JSON.stringify(productCodeList),
          supplierCode: $('#find_supplierc').val(),
        },
        success: function (data) {
          if (data.code == 0 && orderType != 1) {
            utils
              .dialog({
                title: '提示',
                content: data.msg,
                okValue: '确定',
                ok: function () { },
              })
              .showModal();
            return;
          } else if (data.code == 2 && orderType != 1) {
            $('#submitAssert').attr('disabled', false);
            let _str = '';
            for (let i = 0; i < data.result.productNameListMsg.length; i++) {
              _str +=
                data.result.productNameListMsg[i] +
                ' ' +
                data.result.productCodeListMsg[i] +
                '<br />';
            }
            utils
              .dialog({
                title: '提示',
                content: _str + '该商品已绑定指定供应商，不可采购！',
                okValue: '确定',
                ok: function () { },
              })
              .showModal();
          } else {
            if (!validform('basicInfo').form()) {
              return false;
            } else if (!validform('validates').form()) {
              $('#submitAssert').attr('disabled', false);
              return false;
            } else {
              checkApplyInfo(
                ttx,
                ttl,
                checkAppList,
                checkProudctList,
                channelIdx,
                checkOfTips,
                productCodeList,
                checkPriceList,
                flag,
              );
            }
          }
        },
      });
    }
  };

  //草稿
  $('#draftAssert').click(function () {
    $('#ifSpecialDrugs').val(0);
    if (
      $('#supplierSettlementVoList').val() == '2001' ||
      $('#supplierSettlementVoList').text() == '预付款'
    ) {
      if ($('#selectadvancePayment').val() == '0') {
        utils
          .dialog({
            content: '【结算方式】与【是否预付】不匹配，请重新选择！',
            quickClose: true,
            timeout: 4000,
          })
          .showModal();
        return false;
      }
    }
        //琦玉
        if( $('#selectadvancePayment').val() == '1'){
          $.ajax({ //请求进行校验是否黑名单
            type: 'GET',
            url: '/proxy-purchase/purchase/supplier/supplier-black-judge?supplierCode='+ $("[name='supplierCode']").val() +'&orgCode='+ $("[name='orgCode']").val() ,
            async: false,
            contentType: 'application/json;charset=utf-8',
            dataType: 'json',
            success: function (response) {
              let { result, code, msg } = response;
              if(result != null){
                utils
                  .dialog({
                    title: '提示',
                    content: result,
                    okValue: '确定',
                    quickClose: true,
                    ok: function () {
                      return 
                    },
                  })
                  .showModal();
              }
          }
          });
          return false;
        }
    var orderNo = $("input[name='planOrderNo']").val();
    if ((orderNo = '')) {
      utils
        .dialog({
          content: '采购订单号不能为空！',
          quickClose: true,
          timeout: 1000,
        })
        .showModal();
      return false;
    }
    var boolean = overOne();
    if (boolean) {
      return false;
    }
    var orderTy = $('#orderType').val();
    if (orderTy == 2) {
      var orgRatePercent = purchaseOrgRate / 100;
      var totalPrice = $('#orderPriceTaxSum').text();
      var orgRateAmount = (orgRatePercent * totalPrice).toFixed(2); // 调拨费率由2%需变更为0.5%，对应的预估调拨费用为总金额*调拨费率逻辑保持不变；
      $('#forecastAllotCost').val(orgRateAmount);
    }

    var ttl = $('#X_Tablea')
      .XGrid('getRowData')
      .filter(function (item) {
        if (!item.productCode) {
          $('#X_Tablea').XGrid('delRowData', item.sort);
        }
        return item.productCode;
      }); //商品数据

    var plc = [];
    for (var i = 0; i < ttl.length; i++) {
      //是否新品
      if (ttl[i].isNew == "是") {
        ttl[i].isNew = "1"
      } else if (ttl[i].isNew == "否") {
        ttl[i].isNew = "0"
      }
      if (
        orderType != '1' &&
        ttl[i].centralizedPurchaseType !=
        $('#selectCentralizedPurchaseType').val()
      ) {
        $('#submitAssert').attr('disabled', false);
        utils
          .dialog({
            content:
              ttl[i].productName +
              '商品为集采/地采商品，与订单表头属性不一致，请修改。',
            quickClose: true,
            timeout: 1000,
          })
          .showModal();
        return false;
      }
      var opcel = $(
        '#' +
        (ttl[i].id ? ttl[i].id : ttl[i].sort) +
        ' td[row-describedby="productOriginPlace"]',
      ).html();
      if (opcel) {
        if (opcel.indexOf('select') == -1) {
          plc.push(opcel);
          ttl[i].productOriginPlace = opcel;
        } else {
          plc.push(
            $('#' + (ttl[i].id ? ttl[i].id : ttl[i].sort) + '  select').val(),
          );
          ttl[i].productOriginPlace = $(
            '#' + (ttl[i].id ? ttl[i].id : ttl[i].sort) + '  select',
          ).val();
        }
      }

      if (ttl[i].ifOldApprovalNumber === '是') {
        ttl[i].ifOldApprovalNumber = '1';
      } else {
        ttl[i].ifOldApprovalNumber = '0';
      }
      if (
        ttl[i].varietiesAgreement === '是' ||
        ttl[i].varietiesAgreement === '1'
      ) {
        ttl[i].varietiesAgreement = '1';
      } else if (
        ttl[i].varietiesAgreement === '否' ||
        ttl[i].varietiesAgreement === '0'
      ) {
        ttl[i].varietiesAgreement = '0';
      }

      //验证是否有商品经营范围是否有为二精 或蛋肽
      var scopeofOperation = ttl[i].scopeOfOperation;
      var sysOrgCode = $('#sysOrgCode').val();
      if (
        sysOrgCode == '013' &&
        (scopeofOperation == 'DELJSYP' || scopeofOperation == 'DBTHZJTLJS')
      ) {
        $('#ifSpecialDrugs').val(1);
      }

      //如果药品为冷冻冷藏商品，需填写启运时间
      if (
        (ttl[i].storageConditions == 4 || ttl[i].storageConditions == 5) &&
        !$('#transportTime').val()
      ) {
        utils
          .dialog({
            content:
              ttl[i].productName + '药品为冷冻冷藏商品，需填写"启运时间"',
            quickClose: true,
            timeout: 1000,
          })
          .showModal();
        return false;
      }
    }
    var ttx = $('#basicInfo').serializeToJSON();
    if (orderType != '1') {
      var channelName = $('#channelId_inp').val();
      ttx.channelName = channelName;
    } else {
      ttx.channelId = '1';
      ttx.channelName = 'YBM';
    }

    $('#sumForm')
      .find('span.colmd8')
      .each(function (i) {
        ttx[$(this).attr('name')] = $(this).text();
      });
    let supplierSettlementIndex = $('#supplierSettlementVoList').val();
    if (
      supplierSettlementIndex === '2002' ||
      supplierSettlementIndex === '2003'
    ) {
      if (
        !$('#endTimeSettlementVoList').val() ||
        $('#endTimeSettlementVoList').val() === ''
      ) {
        utils
          .dialog({
            content: '请先选择结算时间信息',
            quickClose: true,
            timeout: 3000,
          })
          .showModal();
        $('#submitAssert').attr('disabled', false);
        return false;
      }
      let endTimeStr = $('#endTimeSettlementVoList').val();
      let endTimeIndex = endTimeStr.substring(4, endTimeStr.length);
      let endTimeObj = endTimeOptions[supplierSettlementIndex][endTimeIndex];
      ttx['settlementCycle'] = endTimeObj.settlementCycle + '天';
      ttx['settlementDay'] = endTimeObj.settlementDate + '日';
      ttx['paymentDay'] = endTimeObj.paymentDay + '日';
    }
    var flag = false;
    if (ttl.length >= 1) {
      $.each(ttl, function (i, item) {
        var productPackCountSmall = item.productPackCountSmall;
        var productContainTaxPrice = item.productContainTaxPrice;
        if (
          (ttl[i].giftFlag == 0 &&
            (!productContainTaxPrice || productContainTaxPrice < 0.01)) ||
          (ttl[i].giftFlag == 1 &&
            (!productContainTaxPrice || productContainTaxPrice < 0))
        ) {
          utils
            .dialog({
              content:
                '含税单价不能为空；且药品必须大于等于0.01元、赠品必须大于等于0元！',
              quickClose: true,
              timeout: 1000,
            })
            .showModal();
          flag = true;
          return false;
        } else if (
          productPackCountSmall <= 0 ||
          productPackCountSmall.length == 0
        ) {
          utils
            .dialog({
              content: '订单数量不能为空,或者小于等于零的数！',
              quickClose: true,
              timeout: 1000,
            })
            .showModal();
          flag = true;
          return false;
        }
      });
      if (flag) {
        return false;
      }
    }

    if (!validform('basicInfo').form()) {
      return false;
    } else if (!validform('validates').form()) {
      return false;
    } else if (ttl.length >= 1) {
      $('#X_Tablea tr[id]').removeClass('redback');
      $('#X_Tablea tr[id]').removeClass('maize');
      //parent.showLoading();
      var supplierFinanceProfitRate = $('#supplierFinanceProfitRate').val();
      ttx['supplierFinanceProfitRate'] = supplierFinanceProfitRate;
      var annexStr = "";
      for (var i = 0; i < annex.length; i++) {
        annexStr += annex[i].fileName + "," + annex[i].filePath + ",";
      }
      ttx['attachmentLink'] = annexStr.slice(0, -1);
      $.ajax({
        method: 'POST',
        url: '/proxy-purchase/purchase/groupPlanOrder/saveDrap',
        data: {
          purchaseOrderVoData: JSON.stringify(ttx),
          purchaseOrderProductVoData: JSON.stringify(ttl),
        },
        dataType: 'json',
        cache: false,
      }).done(function (data) {
        //parent.hideLoading();
        if (data.code == 0) {
          utils
            .dialog({ content: data.msg, quickClose: true, timeout: 3000 })
            .showModal();

          setTimeout(function () {
            utils.closeTab("editPage");
          }, 2000);
        } else if (data.code == 3) {
          if (data.list != null) {
            for (var i = 0; i < data.list.length; i++) {
              $('#X_Tablea  #' + data.list[i].id).removeClass('maize');
              $('#X_Tablea  #' + data.list[i].id).addClass('redback');
            }
          }
          utils
            .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
            .showModal();
        } else if (data.code == 2) {
          var tDate = $('#X_Tablea').XGrid('getRowData');
          if (tDate.length) {
            for (var i = 0; i < tDate.length; i++) {
              $('#X_Tablea  #' + tDate[i].id).removeClass('maize');
              $('#X_Tablea  #' + tDate[i].id).addClass('redback');
            }
          }
          utils
            .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
            .showModal();
        } else {
          utils
            .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
            .showModal();
        }
      });
    } else {
      utils
        .dialog({
          content: '最少选择一条商品！',
          quickClose: true,
          timeout: 4000,
        })
        .showModal();
      tableAddRow('X_Tablea', {});
    }
  });
  //设置显示列
  $('#set_tables_rowa').click(function () {
    $('#X_Tablea').XGrid('filterTableHead', 820);
  });
  $('#settleMethod').change(function () {
    var vl = $(this).val();

    if (vl == 0) {
      $('#jszq').css('display', 'none');
      $('.dateRange').css('display', 'block');
    } else if (vl == 1) {
      $('#jszq').css('display', 'block');
      $('.dateRange').css('display', 'none');
    } else if (vl == 2) {
      $('#jszq').css('display', 'none');
      $('.dateRange').css('display', 'block');
    }
  });

  $('body').on(
    'click',
    '#X_Tablea [row-describedby="productPackCountSmall"] .glyphicon-search',
    function () {
      let rowId = $(this).parents('tr').attr('id'); //
      let curRowData = $('#X_Tablea').XGrid('getRowData', rowId);
      let isJC = Number(curRowData['centralizedPurchaseType']); // 是， 否
      let limitVal = isJC
        ? $('#jcAdjustPercentageLimit').val()
        : $('#adjustPercentageLimit').val();
      $.ajax({
        type: 'POST',
        url: '/proxy-purchase/purchase/purchaseOrder/getCallOutOrgProduct',
        async: false,
        data: {
          productCode: curRowData.productCode,
          channelId: $('#channelId').val(),
          supplierCode: $('#find_supplierc').val(),
        },
        success: function (response) {
          let str = '';
          if (response.result != null) {
            var orgName =
              response.result.value === null ? '' : response.result.value;
            var buyerName =
              response.result.data === null ? '' : response.result.data;
            var scatteredYn =
              response.result.data === null ? '' : response.result.scatteredYn;
            var mediumPackageNumber =
              response.result.data === null
                ? ''
                : response.result.mediumPackageNumber;
            var scatteredYnStr = '否';
            if (scatteredYn == 1) {
              scatteredYnStr = '是';
            }
            str =
              `<div>
                            <p>调出机构名称: ` +
              orgName +
              `</p>
                            <p>商品编码: ` +
              curRowData.productCode +
              `</p>
                            <p>商品名称: ` +
              curRowData.productName +
              `</p>
                            <p>规格: ` +
              curRowData.productSpecification +
              `</p>
                            <p>厂家: ` +
              curRowData.productProduceFactory +
              `</p>
                            <p>调出机构采购员: ` +
              buyerName +
              `</p>
                            <p>调拨数量已做限定,不能超过` +
              Math.floor(
                curRowData.productStockCur * (Number(limitVal) / 100),
              ) +
              `个</p>
                            <p>是否拆零: ` +
              scatteredYnStr +
              `</p>
                            <p>中包装数: ` +
              mediumPackageNumber +
              `</p>
                        </div>`;
          } else {
            str = response.msg;
          }
          utils
            .dialog({
              title: '提示',
              content: str,
            })
            .showModal();
        },
      });
    },
  );

  $('#X_Tablea')
    .on('mouseover', '.questa', function (e) {
      //$("th[row-describedby='productSuggestPlanCount'] .questa").mouseover(function (e) {

      $('body').append(
        "<div id='div_toop'><div id='inner'>建议计划量 = （可调天数+供应商货期） * 前30天日均销售数量 - 当前库存数量 - 当前在途数量</div></div>",
      );
      $('#div_toop')
        .css({
          top: e.pageY - 70 + 'px',
          position: 'absolute',
          left: e.pageX - 220 + 'px',
        })
        .show('fast');
    })
    .mouseout(function () {
      $('#div_toop').remove();
    })
    .mousemove(function (e) { });
  $('#X_Tablea')
    .on('mouseover', '.questb', function (e) {
      //$("th[row-describedby='productStockTurnDay'] .questb").mouseover(function (e) {

      $('body').append(
        "<div id='div_toops'><div id='inner'>库存周转天数 =（当前库存成本单价 * 当前库存数量）/（当前库存成本单价 * 前30天日均销量）</div></div>",
      );
      $('#div_toops')
        .css({
          top: e.pageY - 70 + 'px',
          position: 'absolute',
          left: e.pageX - 490 + 'px',
        })
        .show('fast');
    })
    .mouseout(function () {
      $('#div_toops').remove();
    })
    .mousemove(function (e) { });

  //订单数量change
  $('#X_Tablea').on(
    'change',
    '[row-describedby="productPackCountSmall"] input[name="productPackCountSmall"]',
    function () {
      //let rowId = $(this).parents('tr').attr('id');
      //let curRowData = $('#X_Tablea').XGrid('getRowData', rowId);
      queryProductHit();
    },
  );
  
  //含税单价change
  $('#X_Tablea').on(
    'change',
    '[row-describedby="productContainTaxPrice"] input[name="productContainTaxPrice"]',
    function () {
      //let rowId = $(this).parents('tr').attr('id');
      //let curRowData = $('#X_Tablea').XGrid('getRowData', rowId);
      queryProductHit();
    },
  );
});
//订单数量失去焦点
function productPackCountSmalOnblur(e){
  var rowId = $(e).parents('tr').attr('id');
  var curRowData = $('#X_Tablea').XGrid('getRowData', rowId);
  var orgCode = $('[name="orgCode"]').val();
  var channelId = $('[name="channelId"]').val();
  queryProductHit();
  //处理当前周转天数
  $.ajax({
      url: '/proxy-purchase/purchase/product/getProductListNumsByCondition',
      method: 'POST',
      data: JSON.stringify({
      orgCode: orgCode,
      channelId: channelId,
      productCodeList: [curRowData.productCode],
      }),
      dataType: 'json',
      contentType: 'application/json',
      success: function (resp) {
      if(resp.code === 0 ){
          console.log(orgCode, channelId, curRowData.productCode);
          let str = ''+ orgCode + '-' + channelId + '-' + curRowData.productCode;
          let dat = resp.result == null ? 0 : resp.result[str];
          if((curRowData.productPackCountSmall || curRowData.productPackCountSmall == 0) && (curRowData.productStockCur || curRowData.productStockCur == 0) && (dat || dat === 0) && Number(curRowData.sixtyAverageDailySales) != 0){
          var days = ((curRowData.productPackCountSmall * 1) + (curRowData.productStockCur * 1) + dat) / (curRowData.sixtyAverageDailySales * 1)
          let field1 = Math.round(days);
          $('#X_Tablea').XGrid('setRowData', curRowData.rowid, {
              field1: field1,
          });
          console.log(resp.result[`${orgCode}-${channelId}-${curRowData.productCode}`],`${orgCode}-${channelId}-${curRowData.productCode}`)
          }
      }
      },
      error: () => { },
  });
}
function seleteValidityDate() {
  queryProductHit();
}
//修改当前行
function setEmptyRow(sort, callback, rowData) {
  var basicForm = {
    supplier: {
      val: $('#find_supplier').val(),
      msg: '供应商不能为空！',
    },
    arrivalPeriod: {
      val: $('#arrivalPeriod').val(),
      msg: '供应商货期不能为空！',
    },
    simpleCode: {
      val: $('#simpleCode').val(),
      msg: '供应商的经营范围不能为空！',
    },
  };
  for (var key in basicForm) {
    if (!basicForm[key].val) {
      utils
        .dialog({
          content: basicForm[key].msg,
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      return false;
    }
  }
  var oldData = $('#X_Tablea').XGrid('getRowData');
  var disableRows = $.map(oldData, function (item, key) {
    return item.id;
  });
  if (rowData) {
    setRow(rowData, sort);
  } else {
    utils
      .dialog({
        url:
          '/proxy-purchase/purchase/purchaseOrder/toPurchaseProduct?supplyArrivalPeriod=' +
          $('#arrivalPeriod').val() +
          '&supplierId=' +
          $('#supplierId').val() +
          '&supplierCode=' +
          $('#find_supplierc').val() +
          '&supplierName=' +
          encodeURI($('#find_supplier').val()) +
          '&orderType=' +
          $('#orderType').val() +
          '&channelId=' +
          $('#channelId').val() +
          '&orgCode=' +
          $('#orgCode').val() +
          '&centralizedPurchaseType=' +
          $('#selectCentralizedPurchaseType').val(),
        title: '商品列表',
        width: $(window).width() * 0.9,
        height: $(window).height() * 0.7,
        data: {
          val: $('#X_Tablea')
            .find('tr#' + sort + ' td[row-describedby=productName] input')
            .attr('oldvalue'),
          colData: disableRows,
          simpleCode: $('#simpleCode').val(),
        }, // 给modal 要传递的 的数据
        onclose: function () {
          if (!this.returnValue) return false;
          var data = [].concat(this.returnValue);
          if (data) {
            setRow(data, sort);
          } else {
            callback && callback();
          }
        },
      })
      .showModal();
  }

  function setRow(data, sort) {
    var remainDaysAry = [], //即将过期商品
      noDaysAry = [], //过期商品
      repeatAry = [], //重复商品
      strongControlData = []; //价目表商品
    fristRow = true; //第一个更改，之后的追加
    data = [].concat(data);
    $.each(data, function (index, val) {
      var lengthFlag = repeatAry.length;
      $.each(oldData, function (key, item) {
        if (item.productCode && val.productCode === item.productCode) {
          repeatAry.push(val);
        }
      });
      if (lengthFlag === repeatAry.length && Boolean(val.productCode)) {
        if ($('#orderType').val() == '0' && $('#channelId').val() == '1') {
          if (
            strongControl.indexOf($('#orgCode').val()) == -1 &&
            $('#selectCentralizedPurchaseType').val() != '11' &&
            $('#selectCentralizedPurchaseType').val() != '20'
          ) {
            if (parseFloat(val.productLastPrice) > 0) {
              val.productContainTaxPrice = val.productLastPrice;
            }
          } else {
            if (
              val.productContainTaxPrice == null ||
              val.productContainTaxPrice == ''
            ) {
              strongControlData.push(val);
            }
          }
        } else {
          if (parseFloat(val.productLastPrice) > 0) {
            val.productContainTaxPrice = val.productLastPrice;
          }
        }

        if (
          val.remainDays &&
          0 <= parseFloat(val.remainDays) &&
          parseFloat(val.remainDays) <= 30
        ) {
          remainDaysAry.push(val);
        }
        if (parseFloat(val.remainDays) < 0 && val.remainDays != '') {
          noDaysAry.push(val);
        }
        if (fristRow) {
          fristRow = false;
          tableAddRow('X_Tablea', val, sort);
        } else {
          tableAddRow('X_Tablea', val);
        }
      }
    });
    rowEvent();
    //提示信息
    var contentStr = '';
    //判断商品即将过期的标红：去掉
    if (remainDaysAry.length) {
      for (var i = 0; i < remainDaysAry.length; i++) {
        $('#X_Tablea  #' + remainDaysAry[i].sort).removeClass('redback');
        $('#X_Tablea  #' + remainDaysAry[i].sort).addClass('maize');
      }
      contentStr +=
        '商品{' +
        remainDaysList
          .map(function (item) {
            return item.productCode;
          })
          .join(',') +
        '}，即将过期！<br/>';
    }
    if (noDaysAry.length) {
      for (var i = 0; i < noDaysAry.length; i++) {
        $('#X_Tablea  #' + noDaysAry[i].sort).removeClass('redback');
        $('#X_Tablea  #' + noDaysAry[i].sort).addClass('maize');
      }
      contentStr +=
        '商品{' +
        noDaysAry
          .map(function (item) {
            return item.productCode;
          })
          .join(',') +
        '}，已过期！<br/>';
    }
    if (repeatAry.length > 0) {
      contentStr +=
        '商品{' +
        repeatAry
          .map(function (item) {
            return item.productCode;
          })
          .join(',') +
        '}，存在重复！';
    }
    if (strongControlData.length > 0) {
      contentStr +=
        '商品{' +
        strongControlData
          .map(function (item) {
            return item.productCode;
          })
          .join(',') +
        '}，没有价目表记录，请先维护！';
    }

    if (contentStr) {
      utils
        .dialog({
          content: contentStr,
          quickClose: true,
          timeout: 4000,
          onclose: function () {
            callback && callback();
            let rowdata = $('#X_Tablea').XGrid('getRowData');
            $(rowdata).each((index, item) => {
              if (
                $('#orderType').val() == '0' &&
                $('#channelId').val() == '1' &&
                (strongControl.split(',').indexOf($('#orgCode').val()) >= 0 ||
                  $('#selectCentralizedPurchaseType').val() == '11' ||
                  $('#selectCentralizedPurchaseType').val() == '20')
              ) {
                if (item.id) {
                  $('#X_Tablea #' + item.rowid)
                    .find('[row-describedby="productContainTaxPrice"] input')
                    .prop('disabled', true);
                }
              }
            });
          },
        })
        .showModal();
    } else {
      callback && callback();
    }
  }
}

//本地新增行
function tableAddRow(tableId, rowData, rowId) {
  var orderType = $('#orderType').val();
  /* if (orderType != null && orderType != "" && orderType == "1") {
        if (rowData.productId == "" || rowData.productId == null) {
            rowData.productId = rowData.id;
        }
    } else {
        rowData.productId = rowData.id;
    }*/

  if (rowId) {
    $('#' + tableId).XGrid('setRowData', rowId, rowData);
  } else {
    var oldData = $('#' + tableId).XGrid('getRowData');
    var sortAry = $.map(oldData, function (item, index) {
      return item.sort;
    });
    rowData.sort =
      oldData.length > 0 ? (Math.max.apply(null, sortAry) + 1).toString() : '1';
    var lastData = oldData[oldData.length - 1];
    if (!lastData || lastData.productCode) {
      $('#' + tableId).XGrid('addRowData', rowData);
    } else {
      rowData.sort = (Math.min.apply(null, sortAry) - 1).toString();
      $('#' + tableId).XGrid('addRowData', rowData, 'before', lastData.sort);
    }
    let rowdata = $('#X_Tablea').XGrid('getRowData');
    $(rowdata).each((index, item) => {
      if (
        $('#orderType').val() == '0' &&
        $('#channelId').val() == '1' &&
        (strongControl.split(',').indexOf($('#orgCode').val()) >= 0 ||
          $('#selectCentralizedPurchaseType').val() == '11' ||
          $('#selectCentralizedPurchaseType').val() == '20')
      ) {
        if (item.id) {
          $('#X_Tablea #' + item.rowid)
            .find('[row-describedby="productContainTaxPrice"] input')
            .prop('disabled', true);
        }
      }
    });
  }
  setTimeout(() => {
    // 放大镜
    let jcIfUpdatePrice_flag = $('#jcIfUpdatePrice').val(),
      ifUpdatePrice_flag = $('#ifUpdatePrice').val();
    //   $('#X_Tablea').find('[row-describedby="productPackCountSmall"] .glyphicon-search').css('display', (itemType == 1 ? "block" : 'none'))
    let rowData = $('#X_Tablea').XGrid('getRowData');
    /*rowData.forEach(item => {
            if (item['centralizedPurchaseType'] == '0'){ // 是否集采： 0 为否
                $('#X_Tablea #' + item.rowid).find('[row-describedby="productContainTaxPrice"] input').prop('disabled',  (ifUpdatePrice_flag == 1 ? false : true))
            } else {
                $('#X_Tablea #' + item.rowid).find('[row-describedby="productContainTaxPrice"] input').prop('disabled',  (jcIfUpdatePrice_flag == 1 ? false : true))
            }
        })*/
  }, 1000);
  productName_Autocomplete(tableId, rowData);
}
/********************llj*********************/
var supplierSettlementVoListOption = [];
/**************************************************/
//供应商信息赋值
function setSupplyInfo(obj) {
  if (obj) {
    var data = obj;
    var simpleCode = data.simpleCodeList;
    var selectoverStr = '';
    var boolean = false;

    itemType = obj.itemType;
    ifUpdatePrice = obj.ifUpdatePrice;
    adjustPercentageLimit = obj.adjustPercentageLimit;
    purchaseOrgRate = obj.purchaseOrgRate;
    supplierFinanceProfitRate = obj.supplierFinanceProfitRate;
    //赋值供应商维度财务毛利率
    $("#supplierFinanceProfitRate").val(data.supplierFinanceProfitRate);
    //内部交易-调拨-订单类型
    $('#purchaseOrderType').attr('readonly', 'readonly');
    let purchaseOrderTypeStr = '';
    if ($('#orderType').val() != 1) {
      switch (data.itemType) {
        case 0:
          purchaseOrderTypeStr = '常规采购';
          break;
        case 1:
          purchaseOrderTypeStr = '调拨采购';
          break;
      }

      $('#purchaseOrderTypeCode').val(data.itemType);
      if (data.itemType == 1) {
        //调拨单
        $('#orderType').val(2);
      } else {
        $('#orderType').val(0);
      }
    } else {
      purchaseOrderTypeStr = '常规采购';
    }

    $('#purchaseOrderType').val(purchaseOrderTypeStr);

    // if (data.overDueList != "") {
    //     selectoverStr = "该供应商{" + data.supplierCode + "}的{" + data.overDueList + "}资质已过期!<br/>";
    //     boolean = true;
    // }
    // if (data.overOneMouthList != "") {
    //     selectoverStr += "该供应商{" + data.supplierCode + "}的{" + data.overOneMouthList + "}资质即将过期!";
    // }
    // if (selectoverStr != "") {
    //     utils.dialog({content: selectoverStr, quickClose: true, timeout: 6000}).showModal();
    //     if (boolean) {
    //         return false;
    //     }
    // }
    if (data.supplierCode != $('#find_supplierc').val()) {
      rowSuppCode(data.arrivalPeriod);
      /*  $("#X_Tablea").XGrid('clearGridData');
                  rowEa(null);*/
    }
    $('#simpleCode').val(simpleCode);
    //  console.log(data)
    $('#supplyBussinessUserId').val(data.supplyBussinessUserId);
    $('#arrivalPeriod').val(data.arrivalPeriod);
    $('#arrivalPeriod').attr('readonly', 'readonly');
    $('#supplyOperateId').val(data.id);
    $('#supplyOperateId').attr('readonly', 'readonly');
    //$("#arrivalPeriod").val(data.arrivalPeriod);
    $('#arrivalPeriod').attr('readonly', 'readonly');
    $('#find_supplier').val(data.supplierName);
    $('#transportFactory').val(data.supplierName);
    $('#supplierName').attr('readonly', 'readonly');
    $('#find_supplierc').val(data.supplierCode);
    $('#supplierId').val(data.id); //供应商ID
    $('#supplierName').val(data.supplyBussinessUserName);
    $('#supplyBussinessUserMobile').val(data.supplyBussinessUserMobile);
    $('#supplyBussinessUserMobile').attr('readonly', 'readonly');
    $('#supplierPaymentVoList').val(data.supplierPaymentVoList);

    $('#ifUpdatePrice').val(data.ifUpdatePrice); //地采
    $('#jcIfUpdatePrice').val(data.jcIfUpdatePrice); //集采
    $('#adjustPercentageLimit').val(data.adjustPercentageLimit);
    $('#jcAdjustPercentageLimit').val(data.jcAdjustPercentageLimit);

    $('#arrivalPeriod').val(
      data.arrivalPeriod == 'null' ? 1 : data.arrivalPeriod,
    );

    var ts =
      parseInt(data.arrivalPeriod == 'null' ? 1 : data.arrivalPeriod) + 1;
    var curDate = new Date();
    var preDate = new Date(curDate.getTime() + ts * 24 * 60 * 60 * 1000);

    var fmdate = new Date(preDate);
    var y = fmdate.getFullYear();
    var m = fmdate.getMonth() + 1;
    if (m <= 9) {
      m = '0' + m;
    }
    var d = fmdate.getDate();
    if (d <= 9) {
      d = '0' + d;
    }

    $('#expectDeliveryDate').val(y + '-' + m + '-' + d);

    $('#supplierSettlementVoList').val(data.supplierSettlementVoList);
    $('#supplierPaymentVoList').empty();
    $('#supplierSettlementVoList').empty();
    $('#listSupplyStoreAddress').empty();
    if (data.supplierPaymentVoList != '') {
      //解析付款方式
      var ts = JSON.parse(data.supplierPaymentVoList);
      //  console.log(ts);
      var opa = '';

      var opb = '';
      var xts = '';
      $.each(ts, function (i, item) {
        xts = item.childList[0].value;
        if (xts == null) {
          xts = '';
        }
        //   console.log(item.childList[0].name);
        opa += '<option  value="' + item.code + '">' + item.name + '</option>';
        opb +=
          '<div class="input-group zjsywp"> <div class="input-group-addon" >' +
          item.childList[0].name +
          '</div>' +
          '<input type="text"   value="' +
          xts +
          '" class="form-control " readonly="readonly"   placeholder="账期"></div>';
      });
      /********************llj*********************/
      supplierSettlementVoListOption = JSON.parse(
        data.supplierSettlementVoList,
      );
      seleteSelectadvancePaymentOption();
      /********************************/
      $('#supplierPaymentVoList').empty().append(opa);
      $('#zjsy').empty().append(opb);
      if (
        $('#supplierSettlementVoList').find('option:selected').val() ==
        '2001' ||
        $('#supplierSettlementVoList').find('option:selected').text() ==
        '预付款'
      ) {
        if ($('#selectadvancePayment').val() == '0') {
          $('#selectadvancePayment')
            .find("option[value = '1']")
            .attr('selected', 'selected');
        }
      }
    }

    if (data.supplierSettlementVoList != '') {
      //解析结算方式
      var tx = JSON.parse(data.supplierSettlementVoList);
      // console.log(tx);
      var opx = '';
      var opy = '';
      $.each(tx, function (i, item) {
        opx += '<option value="' + item.code + '">' + item.name + '</option>';
        opy += '<div class="jsfswp">';
        if (item.childList != null) {
          var vnull = '';
          $.each(item.childList, function (i, it) {
            vnull = it.value;
            if (vnull == null) {
              vnull = '';
              opy +=
                '<div class="col-md-3"><div class="input-group"><div class="input-group-addon " >' +
                it.name +
                '</div>' +
                '<input value="' +
                vnull +
                '" type="text"    class="form-control " placeholder="账期"></div></div>';
            } else {
              opy +=
                '<div class="col-md-3"><div class="input-group"><div class="input-group-addon " >' +
                it.name +
                '</div>' +
                '<input value="' +
                vnull +
                '" type="text"  readonly="readonly" class="form-control " placeholder="账期"></div></div>';
            }
          });
        } else {
        }
        opy += '</div>';
      });

      $('#supplierSettlementVoList').empty().append(opx);
      getEndTimeMethod();
      // $("#jsfs").empty().append(opy);
    }

    if (data.listSupplyStoreAddress != '') {
      //解析仓库地址
      var tx1 = JSON.parse(data.listSupplyStoreAddress);
      var opx1 = '';
      var opy1 = '';
      $.each(tx1, function (i, item) {
        opx1 += '<option value="' + item + '">' + item + '</option>';
      });
      $('#listSupplyStoreAddress').empty().append(opx1);
    }
  }
  //清空商品列表
  $('#X_Tablea')
    .setGridParam({
      data: [],
    })
    .trigger('reloadGrid');
}

function getEndTimeMethod() {
  $.ajax({
    type: 'GET',
    url:
      '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/all-settle-multiple-date?organBaseId=' +
      $('#supplierId').val(),
    dataType: 'json',
    success: function (response) {
      let { result, code, msg } = response;
      if (Object.keys(result).length > 0) {
        endTimeOptions = result;
        let changeType = $('#supplierSettlementVoList').val();
        if (changeType !== '2003' && changeType !== '2002') {
          return;
        }
        $('.moreTimeParents').removeClass('displaynone');
        let opaStr = '';
        // let valA = $('.jsfswp').find('.'+changeType+'001').val();
        // let valB = $('.jsfswp').find('.'+changeType+'002').val();
        // let valC = $('.jsfswp').find('.'+changeType+'003').val();
        let selectIndex = '';
        endTimeOptions[changeType.toString()].forEach((item, index) => {
          // if(valA === item.settlementCycle+'天' && valB === item.settlementDate+'日' && valC === item.paymentDay+'日'){
          //     selectIndex = item.settleMethod + index;
          // }
          let optionStr =
            '结算周期:' +
            item.settlementCycle +
            '天, 结算日:' +
            item.settlementDate +
            '天, 支付日:' +
            item.paymentDay +
            '日';
          opaStr +=
            "<option  value='" +
            item.settleMethod +
            index +
            "'>" +
            optionStr +
            '</option>';
        });
        $('#endTimeSettlementVoList').empty().append(opaStr);
        $('#endTimeSettlementVoList').val(selectIndex);
      }
    },
  });
}

//计划量
function rowSuppCode(arrivalPeriod) {
  var ttl = $('#X_Tablea').XGrid('getRowData');
  var huoqi = parseInt(arrivalPeriod);
  $.each(ttl, function (i) {
    var stockCount = parseFloat(ttl[i].productStockCur); //库存数量
    var productPackOnWayCount = parseFloat(ttl[i].productPackOnWayCount); //在途数量
    var productAdjustDay = parseFloat(ttl[i].productAdjustDay); //可调节天数
    var thirtyAverageSaleVolume = parseFloat(ttl[i].thirtyAverageSaleVolume); //日均销售数量
    console.log(
      'huoqi:' +
      huoqi +
      ',stockCount:' +
      stockCount +
      ',productPackOnWayCount' +
      productPackOnWayCount +
      ',productAdjustDay:' +
      productAdjustDay +
      ',thirtyAverageSaleVolume:' +
      thirtyAverageSaleVolume,
    );
    var jihualiang =
      (productAdjustDay + huoqi) * thirtyAverageSaleVolume -
      stockCount -
      productPackOnWayCount;
    console.log('jihualiang:' + jihualiang);
    console.log('jihualiang:' + jihualiang);
    if (jihualiang <= 0) {
      $('#X_Tablea #' + ttl[i].id)
        .find("td[row-describedby='supplyArrivalPeriod']")
        .html(huoqi);
      $('#X_Tablea #' + ttl[i].id)
        .find("td[row-describedby='productSuggestPlanCount']")
        .html(0);
    } else {
      $('#X_Tablea #' + ttl[i].id)
        .find("td[row-describedby='supplyArrivalPeriod']")
        .html(huoqi);
      if (parseInt(jihualiang) == '0') {
        $('#X_Tablea #' + ttl[i].id)
          .find("td[row-describedby='productSuggestPlanCount']")
          .html(0);
      } else if (parseInt(jihualiang) > 0) {
        var jihualiang1 = Math.ceil(jihualiang);
        $('#X_Tablea #' + ttl[i].id)
          .find("td[row-describedby='productSuggestPlanCount']")
          .html(jihualiang1);
      }
    }
    //(可调天数+供应商货期)*日均销售数量 - 当前库存数量 - 当前在途数量)
  });
}

function commodity_search_dia(val) {
  dialog({
    url: '/proxy-purchase/purchase/purchaseOrder/toSupplier?orgCode=' + $('#orgCode').val(),
    title: '供应商列表',
    width: $(window).width() * 0.9,
    height: '550px',
    data: val, // 给modal 要传递的 的数据
    onclose: function () {
      if (this.returnValue) {
        var data = this.returnValue;
        var simpleCode = data.simpleCodeList;
        var selectoverStr = '';
        var boolean = false;
        // if (data.overDueList != "") {
        //     selectoverStr = "该供应商{" + data.supplierCode + "}的{" + data.overDueList + "}资质已过期!<br/>";
        //     boolean = true;
        // }
        // if (data.overOneMouthList != "") {
        //     selectoverStr += "该供应商{" + data.supplierCode + "}的{" + data.overOneMouthList + "}资质即将过期!";
        // }
        // if (selectoverStr != "") {
        //     utils.dialog({content: selectoverStr, quickClose: true, timeout: 6000}).showModal();
        //     if (boolean) {
        //         return false;
        //     }
        // }
        if (data.supplierCode != $('#find_supplierc').val()) {
          rowSuppCode(data.arrivalPeriod);
          /*  $("#X_Tablea").XGrid('clearGridData');
                          rowEa(null);*/
        }
        $('#simpleCode').val(simpleCode);
        //  console.log(data)
        $('#supplyBussinessUserId').val(data.supplyBussinessUserId);
        $('#arrivalPeriod').val(data.arrivalPeriod);
        $('#arrivalPeriod').attr('readonly', 'readonly');
        $('#supplyOperateId').val(data.id);
        $('#supplyOperateId').attr('readonly', 'readonly');
        //$("#arrivalPeriod").val(data.arrivalPeriod);
        $('#arrivalPeriod').attr('readonly', 'readonly');
        $('#find_supplier').val(data.supplierName);
        $('#transportFactory').val(data.supplierName);
        $('#supplierName').attr('readonly', 'readonly');
        $('#find_supplierc').val(data.supplierCode);
        $('#supplierId').val(data.id); //供应商ID
        $('#supplierName').val(data.supplyBussinessUserName);
        $('#supplyBussinessUserMobile').val(data.supplyBussinessUserMobile);
        $('#supplyBussinessUserMobile').attr('readonly', 'readonly');
        $('#supplierPaymentVoList').val(data.supplierPaymentVoList);

        //内部交易-调拨-订单类型
        $('#purchaseOrderType').attr('readonly', 'readonly');
        let purchaseOrderTypeStr = '';
        switch (data.itemType) {
          case 0:
            purchaseOrderTypeStr = '常规采购';
            break;
          case 1:
            purchaseOrderTypeStr = '调拨采购';
            break;
        }
        $('#purchaseOrderType').val(purchaseOrderTypeStr);

        $('#arrivalPeriod').val(
          data.arrivalPeriod == 'null' ? 1 : data.arrivalPeriod,
        );

        var ts =
          parseInt(data.arrivalPeriod == 'null' ? 1 : data.arrivalPeriod) + 1;
        var curDate = new Date();
        var preDate = new Date(curDate.getTime() + ts * 24 * 60 * 60 * 1000);

        var fmdate = new Date(preDate);
        var y = fmdate.getFullYear();
        var m = fmdate.getMonth() + 1;
        if (m <= 9) {
          m = '0' + m;
        }
        var d = fmdate.getDate();
        if (d <= 9) {
          d = '0' + d;
        }

        $('#expectDeliveryDate').val(y + '-' + m + '-' + d);

        $('#supplierSettlementVoList').val(data.supplierSettlementVoList);
        $('#supplierPaymentVoList').empty();
        $('#supplierSettlementVoList').empty();
        $('#listSupplyStoreAddress').empty();
        if (data.supplierPaymentVoList != '') {
          //解析付款方式
          var ts = JSON.parse(data.supplierPaymentVoList);
          //  console.log(ts);
          var opa = '';

          var opb = '';
          var xts = '';
          $.each(ts, function (i, item) {
            xts = item.childList[0].value;
            if (xts == null) {
              xts = '';
            }
            //   console.log(item.childList[0].name);
            opa +=
              '<option  value="' + item.code + '">' + item.name + '</option>';
            opb +=
              '<div class="input-group zjsywp"> <div class="input-group-addon" >' +
              item.childList[0].name +
              '</div>' +
              '<input type="text"   value="' +
              xts +
              '" class="form-control " readonly="readonly"   placeholder="账期"></div>';
          });
          /********************llj*********************/
          supplierSettlementVoListOption = JSON.parse(
            data.supplierSettlementVoList,
          );
          seleteSelectadvancePaymentOption();
          /**********************************************/
          $('#supplierPaymentVoList').empty().append(opa);
          $('#zjsy').empty().append(opb);
          if (
            $('#supplierSettlementVoList').find('option:selected').val() ==
            '2001' ||
            $('#supplierSettlementVoList').find('option:selected').text() ==
            '预付款'
          ) {
            if ($('#selectadvancePayment').val() == '0') {
              $('#selectadvancePayment')
                .find("option[value = '1']")
                .attr('selected', 'selected');
            }
          }
        }

        if (data.supplierSettlementVoList != '') {
          //解析结算方式
          var tx = JSON.parse(data.supplierSettlementVoList);
          // console.log(tx);
          var opx = '';
          var opy = '';
          $.each(tx, function (i, item) {
            opx +=
              '<option value="' + item.code + '">' + item.name + '</option>';
            opy += '<div class="jsfswp">';
            if (item.childList != null) {
              var vnull = '';
              $.each(item.childList, function (i, it) {
                vnull = it.value;
                if (vnull == null) {
                  vnull = '';
                  opy +=
                    '<div class="col-md-3"><div class="input-group"><div class="input-group-addon " >' +
                    it.name +
                    '</div>' +
                    '<input value="' +
                    vnull +
                    '" type="text"    class="form-control " placeholder="账期"></div></div>';
                } else {
                  opy +=
                    '<div class="col-md-3"><div class="input-group"><div class="input-group-addon " >' +
                    it.name +
                    '</div>' +
                    '<input value="' +
                    vnull +
                    '" type="text"  readonly="readonly" class="form-control " placeholder="账期"></div></div>';
                }
              });
            } else {
            }
            opy += '</div>';
          });

          $('#supplierSettlementVoList').empty().append(opx);
          getEndTimeMethod();
          // $("#jsfs").empty().append(opy);
        }

        if (data.listSupplyStoreAddress != '') {
          //解析仓库地址
          var tx1 = JSON.parse(data.listSupplyStoreAddress);
          var opx1 = '';
          var opy1 = '';
          $.each(tx1, function (i, item) {
            opx1 += '<option value="' + item + '">' + item + '</option>';
          });
          $('#listSupplyStoreAddress').empty().append(opx1);
        }

        //清空商品列表
        $('#X_Tablea')
          .setGridParam({
            data: [],
          })
          .trigger('reloadGrid');
        setTimeout(() => {
          // 放大镜   //commodity_search_dia
          /* $('#X_Tablea').find('[row-describedby="productPackCountSmall"] .glyphicon-search').css('display', (itemType == 1 ? "block" : 'none'))
                    if (itemType == 1) {
                        $('#X_Tablea').find('[row-describedby="productContainTaxPrice"] input').prop('disabled', (ifUpdatePrice == 1 ? false : true))
                    }*/
        }, 1000);
      } else {
        $('#supplierId').val('');
        $('#find_supplierc').val('');
      }
    },
  }).showModal();
}

function isNotNull(value) {
  if (value != '' && value != null && value != undefined && !isNaN(value)) {
    return true;
  }
  return false;
}

function delRepeat(dataList) {
  var temp = {},
    len = dataList.length;
  for (var i = 0; i < len; i++) {
    var tmp = dataList[i].productCode;
    if (!temp.hasOwnProperty(tmp)) {
      //hasOwnProperty用来判断一个对象是否有你给出名称的属性或对象
      temp[tmp] = dataList[i];
    }
  }
  len = 0;
  var tempArr = [];
  for (var i in temp) {
    tempArr[len++] = temp[i];
  }
  return tempArr;
}

function commodity_search_di(xid) {
  $('#bodyscrolf').css('overflow', 'hidden');

  var ttl = $('#X_Tablea').XGrid('getRowData');
  var supplier = $('#find_supplier').val();
  var arrivalPeriod = $('#arrivalPeriod').val();
  var redid = [];
  if (supplier == '') {
    utils
      .dialog({ content: '供应商不能为空！', quickClose: true, timeout: 4000 })
      .showModal();
    return false;
  } else if (arrivalPeriod == '') {
    utils
      .dialog({
        content: '供应商货期不能为空！',
        quickClose: true,
        timeout: 4000,
      })
      .showModal();
    return false;
  }

  var pays = [];

  if ($('#simpleCode').val() == '') {
    utils
      .dialog({
        content: '供应商的经营范围为空，无法选择商品！',
        quickClose: true,
        timeout: 4000,
      })
      .showModal();
    return false;
  }
  var disableRows = [];
  var ttl = $('#X_Tablea').XGrid('getRowData');
  console.log(ttl);
  if (ttl) {
    if (ttl.length) {
      for (i = 0; i < ttl.length; i++) {
        disableRows.push(ttl[i].id);
      }
    } else {
      disableRows.push(ttl.id);
    }
  }
  console.log(disableRows);
  var selRow = $('#X_Tablea').XGrid('getSeleRow');
  dialog({
    url:
      '/proxy-purchase/purchase/purchaseOrder/toPurchaseProduct?supplyArrivalPeriod=' +
      $('#arrivalPeriod').val() +
      '&simpleCode=' +
      $('#simpleCode').val() +
      '&supplierId=' +
      $('#supplierId').val() +
      '&supplierName=' +
      supplier +
      '&orderType=' +
      $('#orderType').val() +
      '&orgCode=' +
      $('#orgCode').val() +
      '&centralizedPurchaseType=' +
      $('#selectCentralizedPurchaseType').val(),
    title: '商品列表',
    width: $(window).width() * 0.9,
    height: $(window).height() * 0.7,
    data: {
      val: '',
      colData: disableRows,
    }, // 给modal 要传递的 的数据
    onclose: function () {
      $('#bodyscrolf').css('overflow', 'auto');

      if (this.returnValue) {
        var tstb = [];
        var cltb = [];
        var data = this.returnValue;

        var rdt = $('#X_Tablea').XGrid('getRowData');

        //将商品列表的入库单+商品编号放到集合中
        tstb = $.map(rdt, function (i, v) {
          return v.productCode;
        });
        data = delRepeat(data);
        var remainDaysList = [];
        data = $(data).map(function (i, v) {
          if (parseFloat(v.productLastPrice) > 0) {
            v.productContainTaxPrice = v.productLastPrice;
          }

          cltb.push(v.productCode);
          return v;
        });
        for (let i = 0; i < cltb.length; i++) {
          if (tstb.indexOf(cltb[i]) == -1) {
            if (data[i].remainDays <= 30 && data[i].remainDays != '') {
              remainDaysList.push(data[i].productCode);
              redid.push(data[i].id);
            }
            tableAddRow('X_Tablea', data[i]);
          } else {
            pays.push(data[i].productCode);
          }
        }

        rowEvent();
        //判断商品即将过期的标红：去掉
        if (redid.length) {
          for (var i = 0; i < redid.length; i++) {
            $('#X_Tablea  #' + redid[i]).removeClass('redback');
            $('#X_Tablea  #' + redid[i]).addClass('maize');
          }
        }
        var contentStr = '';
        if (remainDaysList.length > 0) {
          contentStr +=
            '商品{' + remainDaysList.join(',') + '}，即将过期！<br/>';
        }
        if (pays.length > 0) {
          contentStr += '商品{' + pays.join(',') + '}，存在重复！';
        }
        if (contentStr != '') {
          utils
            .dialog({
              content: contentStr,
              quickClose: true,
              timeout: 4000,
            })
            .showModal();
        }
      }
    },
  }).showModal();
  return false;
}

//金额计算
function rowEvent(xtype) {
  if (null != xtype) {
    var tr = $('#X_Tablea').XGrid('getRowData', xtype.rowData.id);
    var xid = xtype.rowData.id;
    var pa = parseFloat(tr.productContainTaxPrice);
    var pb = parseFloat(tr.productAppContainTaxPrice);
    var productCode = tr.productCode;
    var pc = parseFloat(tr.productPackCountSmall);
    var pd = parseFloat(tr.productUpperLimitStock); //库存上限
    var pcwayCount = parseFloat(tr.productPackOnWayCount); //在途数量
    var stockCount = parseFloat(tr.productStockCur); //库存数量
    var standardPrice = 0; //标准单价
    if (tr.standardPrice != '') {
      var standardPrice = parseFloat(tr.standardPrice); //标准单价
    }
  }
  var ttl = $('#X_Tablea').XGrid('getRowData');
  var sumMoney = 0;
  var sumTaxMoney = 0;
  var priceTaxSum = 0;

  $.each(ttl, function (i) {
    if (ttl[i].productPackCountSmall == '') {
      ttl[i].productPackCountSmall = '0';
    }
    if (ttl[i].productContainTaxPrice == '') {
      ttl[i].productContainTaxPrice = '0';
    }
    if (ttl[i].productAppContainTaxPrice == '') {
      ttl[i].productAppContainTaxPrice = '0';
    }
    if (ttl[i].productPackCountSmall == '') {
      ttl[i].productPackCountSmall = '0';
    }
    if (
      ttl[i].productPackCountSmall != '' &&
      ttl[i].productContainTaxPrice != ''
    ) {
      var pa = parseFloat(ttl[i].productContainTaxPrice);
      var pb = parseFloat(ttl[i].productAppContainTaxPrice);
      var productCode = ttl[i].productCode;
      var pc = parseFloat(ttl[i].productPackCountSmall);
      var pd = parseFloat(ttl[i].productUpperLimitStock); //库存上限
      var pcwayCount = parseFloat(ttl[i].productPackOnWayCount); //在途数量
      var stockCount = parseFloat(ttl[i].productStockCur); //库存数量
      var productEntryTax = ttl[i].productEntryTax.replace('%', '');
      var taxRate = productEntryTax / 100;
      var sumMoneytr =
        parseFloat(ttl[i].productContainTaxPrice) / (1 + parseFloat(taxRate)); //不含税单价 公式为：含税价 /（1+ 税率）
      var productNoTaxMoney = parseFloat(
        sumMoneytr * parseInt(ttl[i].productPackCountSmall),
      ).toFixed(2); //不含税金额  公式为：不含税价 * 退货数量
      var taxMoney =
        ((parseFloat(ttl[i].productContainTaxPrice) *
          parseInt(ttl[i].productPackCountSmall)) /
          (1 + parseFloat(taxRate))) *
        parseFloat(taxRate); //税额：   公式为：含税单价 * 退货数量 /（1+税率）* 税率
      var productContainTaxMoneyProduct = parseFloat(
        parseFloat(ttl[i].productContainTaxPrice) *
        parseInt(ttl[i].productPackCountSmall),
      ).toFixed(2); //含税金额  公式为：含税价 * 采购数量
      console.log(
        productContainTaxMoneyProduct + '-------------' + productNoTaxMoney,
      );
      sumMoney += parseFloat(productNoTaxMoney);
      sumTaxMoney +=
        parseFloat(productContainTaxMoneyProduct) -
        parseFloat(productNoTaxMoney);
      priceTaxSum += parseFloat(productContainTaxMoneyProduct);

      var productEntryTaxtr1 = ttl[i].productEntryTax.replace('%', '');
      var taxRatetr1 = productEntryTaxtr1 / 100;
      var sumMoneytr1 =
        parseFloat(ttl[i].productContainTaxPrice) /
        (1 + parseFloat(taxRatetr1)); //不含税单价 公式为：含税价 /（1+ 税率）
      var productContainTaxMoney1 =
        parseFloat(ttl[i].productContainTaxPrice) *
        parseInt(ttl[i].productPackCountSmall); //含税金额  公式为：含税价 * 采购数量
      var gainMoney =
        ttl[i].productLastPrice == 0
          ? 0
          : parseFloat(
            ttl[i].productContainTaxPrice - ttl[i].productLastPrice,
          ) / parseFloat(ttl[i].productLastPrice);
      if (isNotNull(sumMoneytr1)) {
        $('#X_Tablea #' + ttl[i].sort)
          .find("td[row-describedby='productUnitPrice']")
          .html(sumMoneytr1.toFixed(6)); //不含税单价
      } else {
        $('#X_Tablea #' + ttl[i].sort)
          .find("td[row-describedby='productUnitPrice']")
          .html(0); //不含税单价
      }
      if (isNotNull(productContainTaxMoney1)) {
        //含税金额
        $('#X_Tablea  #' + ttl[i].sort)
          .find("td[row-describedby='productContainTaxMoney']")
          .html(productContainTaxMoney1.toFixed(2));
        //采购价涨幅
        $('#X_Tablea  #' + ttl[i].sort)
          .find("td[row-describedby='gain']")
          .html(
            gainMoney.toFixed(4)
              ? (gainMoney.toFixed(4) > 0 ? gainMoney.toFixed(4) : 0) + '%'
              : '0%',
          );
      } else {
        //含税金额
        $('#X_Tablea  #' + ttl[i].sort)
          .find("td[row-describedby='productContainTaxMoney']")
          .html(0);
        //采购价涨幅
        $('#X_Tablea  #' + ttl[i].sort)
          .find("td[row-describedby='gain']")
          .html('0%');
      }
    }
  });
  if (!isNaN(sumMoney)) {
    $('#orderSumMoney').html(sumMoney.toFixed(2));
  } else {
    $('#orderSumMoney').html(0);
  }

  if (!isNaN(sumTaxMoney)) {
    $('#orderSumTaxMoney').html(sumTaxMoney.toFixed(2));
  } else {
    $('#orderSumTaxMoney').html(0);
  }

  if (!isNaN(priceTaxSum)) {
    $('#orderPriceTaxSum').html(priceTaxSum.toFixed(2));
  } else {
    $('#orderPriceTaxSum').html(0);
  }

  if (!isNaN(priceTaxSum)) {
    $('#orderPriiceTaxSumChineseNum').html(
      chineseNumber(priceTaxSum.toFixed(2)),
    );
  } else {
    $('#orderPriiceTaxSumChineseNum').html(0);
  }
}

function deletex() {
  var selRow = $('#X_Tablea').XGrid('getSeleRow');
  if (selRow.length) {
    $.each(selRow, function (index, item) {
      $('#X_Tablea').XGrid('delRowData', item.sort);
    });
    if (!$('#X_Tablea').XGrid('getRowData').length) {
      tableAddRow('X_Tablea', {});
    }
    setTimeout(() => {
      queryProductHit();
    }, 100);
    rowEvent(null);
  } else {
    utils
      .dialog({ content: '没有选中任何行！', quickClose: true, timeout: 3000 })
      .showModal();
  }
}

function checkGoods(ttx, ttl) {
  const para = [];
  ttl.map(function (product) {
    para.push({
      productCode: product.productCode,
      productName: product.productName,
      orgCode: ttx.orgCode,
    });
  });
  $.ajax({
    method: 'POST',
    url: '/proxy-purchase/purchase/purchaseOrderProduct/checkOrderProductCertificate',
    data: JSON.stringify(para),
    dataType: 'json',
    contentType: 'application/json',
    //cache: false,
  }).done(function (data) {
    if (data.code == 0 && data.result && data.result.length > 0) {
      var contentStr = '';
      var immediatelyStr = '';
      data.result.map(function (goods) {
        if (goods.remainingDay < 0) {
          contentStr +=
            goods.productName +
            ' {' +
            goods.productCode +
            '} ' +
            goods.batchName +
            ' 已过期<br/>';
        } else {
          immediatelyStr +=
            goods.productName +
            '{' +
            goods.productCode +
            '} ' +
            goods.batchName +
            ' 有效期至' +
            goods.validityDateStr +
            ' 剩余有效期 ' +
            goods.remainingDay +
            '天<br/>';
        }
      });
      if (contentStr != '') {
        contentStr +=
          "<br/><p style='text-align: right'>请联系相关人员进行维护</p>";
        utils.dialog({ content: contentStr, timeout: 3000 }).showModal();
        $('#submitAssert').attr('disabled', false);
        return;
      } else {
        immediatelyStr +=
          "<br/><br/><p style='text-align: right'>请联系相关人员进行维护</p>";
        utils
          .dialog({
            content: immediatelyStr,
            okValue: '继续提交',
            ok: function () {
              tiji(ttx, ttl);
            },
            cancelValue: '暂不提交',
            cancel: function () {
              $('#submitAssert').attr('disabled', false);
              return;
            },
          })
          .showModal();
      }
    } else if (data.code != 0) {
      utils
        .dialog({ content: data.msg, quickClose: true, timeout: 3000 })
        .showModal();
    } else {
      tiji(ttx, ttl);
    }
  });
}

function tiji(ttx, ttl) {
  $('#submitAssert').attr('disabled', true).attr('flag', 2);
  var supplierFinanceProfitRate = $('#supplierFinanceProfitRate').val();
  ttx['supplierFinanceProfitRate'] = supplierFinanceProfitRate;
  var annexStr = "";
  for (var i = 0; i < annex.length; i++) {
    annexStr += annex[i].fileName + "," + annex[i].filePath + ",";
  }
  ttx['attachmentLink'] = annexStr.slice(0, -1);
  parent.showLoading();
  $.ajax({
    method: 'POST',
    url: '/proxy-purchase/purchase/groupPlanOrder/save',
    data: {
      purchaseOrderVoData: JSON.stringify(ttx),
      purchaseOrderProductVoData: JSON.stringify(ttl),
    },
    dataType: 'json',
    cache: false,
  }).done(function (data) {
    parent.hideLoading();
    if (data.code == 0) {
      utils
        .dialog({ content: data.msg, quickClose: true, timeout: 3000 })
        .showModal();
      setTimeout(function () {
        utils.closeTab("editPage");
        // window.location.href = "/proxy-purchase/purchase/purchaseOrder/toList";
      }, 2000);
    } else if (data.code == 3) {
      $('#submitAssert').attr('disabled', false);
      if (data.list != null) {
        utils
          .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
          .showModal();
        for (var i = 0; i < data.list.length; i++) {
          $('#X_Tablea  #' + data.list[i].id).removeClass('maize');
          $('#X_Tablea  #' + data.list[i].id).addClass('redback');
        }
      }
    } else if (data.code == 2) {
      $('#submitAssert').attr('disabled', false);
      utils
        .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
        .showModal();
      var tDate = $('#X_Tablea').XGrid('getRowData');
      if (tDate.length) {
        for (var i = 0; i < tDate.length; i++) {
          $('#X_Tablea  #' + tDate[i].id).removeClass('maize');
          $('#X_Tablea  #' + tDate[i].id).addClass('redback');
        }
      }
    } else if (data.code == 4) {
      $('#submitAssert').attr('disabled', false);
      if (data.list != null) {
        var simpleStr = '';
        for (var i = 0; i < data.list.length; i++) {
          $('#X_Tablea  #' + data.list[i].id).removeClass('maize');
          $('#X_Tablea  #' + data.list[i].id).addClass('redback');
          simpleStr =
            simpleStr +
            '  商品{' +
            data.list[i].productCode +
            '}所属经营范围' +
            data.list[i].scopeName +
            '；</br>';
        }
        //data.returnList.join(',')
        var supplieStr1 =
          '不在供应商' + $('#find_supplierc').val() + '的经营范围之内！';
        simpleStr = simpleStr + supplieStr1;
        utils
          .dialog({ content: simpleStr, quickClose: true, timeout: 4000 })
          .showModal();
      }
    } else if (data.code == 5) {
      $('#submitAssert').attr('disabled', false);
      $('#X_Tablea tr[id]').addClass('redback');
      utils
        .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
        .showModal();
    } else {
      $('#submitAssert').attr('disabled', false);
      utils
        .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
        .showModal();
    }
  });
}

function overOne() {
  var selectoverStr = '';
  var overDueList = $('#find_overDueListStr').val();
  var overOneMouthList = $('#find_overOneMouthListStr').val();
  var supplierCode = $('#find_supplierc').val();
  var boolean = false;
  // if (overDueList != "") {
  //     selectoverStr = "该供应商{" + supplierCode + "}的{" + overDueList + "}资质已过期!<br/>";
  //     boolean = true;
  // }
  // if (overOneMouthList != "") {
  //     selectoverStr += "该供应商{" + supplierCode + "}的{" + overOneMouthList + "}资质即将过期!";
  // }
  // if (selectoverStr != "") {
  //     utils.dialog({content: selectoverStr, quickClose: true, timeout: 6000}).showModal();
  // }
  return boolean;
}

function checkProductOver() {
  var data = $('#X_Tablea').XGrid('getRowData');
  var booleanFlg = false;
  if (data.length > 0) {
    $(data).each(function (i) {
      if (data[i].overDue == 'Y' && data[i].overDue != null) {
        booleanFlg = true;
      }
    });
  }
  return booleanFlg;
}

var ret = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;

function emailsc() {
  var str = $('#mailAddress').val();
  if (str != '') {
    str = str.trim().replace(/\s/g, '');
    var emailarr = str.split(',');
    //   console.log(emailarr);
    if (emailarr.length > 3) {
      utils
        .dialog({
          content: '邮箱超过了3个!!!',
          quickClose: true,
          timeout: 1000,
        })
        .showModal();
      emails = 3;
      return false;
    } else {
      emails = 0;
    }
    var reg =
      /^((([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6}),)*(([a-z0-9_\.-]+)@([\da-z\.-]+)\.([a-z\.]{2,6})))$/;
    if (reg.test(str)) {
    } else {
      utils
        .dialog({
          content: '邮箱校验不通过!!!',
          quickClose: true,
          timeout: 1000,
        })
        .showModal();
      return false;
      //$("#mailAddress").val("");
    }
  }
}

//负毛利
function checkApplyInfo(
  ttx,
  ttl,
  checkAppList,
  checkProudctList,
  channelIdx,
  checkOfTips,
  productCodeList,
  checkPriceList,
  flag,
) {
  var annexStr = "";
  for (var i = 0; i < annex.length; i++) {
    annexStr += annex[i].fileName + "," + annex[i].filePath + ",";
  }
  ttx['attachmentLink'] = annexStr.slice(0, -1);
  $.ajax({
    method: 'POST',
    url: '/proxy-purchase/purchase/groupPlanOrder/checkApplyInfo',
    data: {
      purchaseOrderVoData: JSON.stringify(ttx),
      purchaseOrderProductVoData: JSON.stringify(ttl),
    },
    dataType: 'json',
  }).done(function (datas) {
    if (datas.code !== 0) {
      utils
        .dialog({
          title: '提示',
          content: datas.msg,
          okValue: '确定',
          ok: function () { },
        })
        .showModal();
      return;
    }
    if (ttl.length >= 1) {
      $('#X_Tablea tr[id]').removeClass('redback');
      $('#X_Tablea tr[id]').removeClass('maize');
      var _str = '';

      var err = '';
      var rowdata = $('#X_Tablea').XGrid('getRowData');
      //判断负毛利是否进行判断，0-判断，1-判断
      var minusGrossProfit = 0;
      var mgproduct = [];
      if (checkAppList.length != 0) {
        $.ajax({
          method: 'POST',
          url: '/proxy-purchase/purchase/negativenumber/except/queryNegativeNumberExceptData',
          dataType: 'json',
          cache: false,
          async: false,
          data: {
            productCodeList: JSON.stringify(checkProudctList),
            channelId: channelIdx,
            orgCode: $('#orgCode').val(),
          },
          success: function (data) {
            $('#submitAssert').attr('disabled', false);
            //code 0参数错误，1-不拦截，2-商品拦截，3-所有拦截
            if (data.code == 0) {
              utils
                .dialog({
                  content: data.msg,
                  quickClose: true,
                  timeout: 2000,
                })
                .showModal();
              return false;
            } else if (data.code == 1) {
              minusGrossProfit = 0;
            } else if (data.code == 2) {
              minusGrossProfit = 1;
              mgproduct = data.result;
              if (
                mgproduct == null ||
                (mgproduct != null && mgproduct.length == 0)
              ) {
                minusGrossProfit = 0;
              }
            } else if (data.code == 3) {
              minusGrossProfit = 2;
            } else {
              utils
                .dialog({
                  content: data.msg,
                  quickClose: true,
                  timeout: 2000,
                })
                .showModal();
              return false;
            }
          },
        });
        //负毛利
        if (minusGrossProfit == 0) {
          checkAppList = [];
        } else if (minusGrossProfit == 1) {
          err = rowdata
            .filter((item, index) => {
              return (
                Number(item.productContainTaxPrice) >
                Number(item.productAppContainTaxPrice) >
                Number(item.chainGuidePrice)
              );
            })
            .filter((ite, ind) => {
              return mgproduct.indexOf(ite.productCode) > -1;
            })
            .map((item, index) => {
              return item.productCode + ' ' + item.productName;
            });
        } else if (minusGrossProfit == 2) {
          err = rowdata
            .filter((item, index) => {
              return (
                Number(item.productContainTaxPrice) >
                Number(item.productAppContainTaxPrice) >
                Number(item.chainGuidePrice)
              );
            })
            .map((item, index) => {
              return item.productCode + ' ' + item.productName;
            });
        }
      }

      if (err.length > 0) {
        var msgs = err.join('</br>');
        _str +=
          '<h4 style="font-size: 16px">负毛利提醒</h4>' +
          msgs +
          ' 须同时低于APP售价与连锁APP售价</br>';
      }
      // 采购价涨幅提醒
      var brr = rowdata
        .filter((item, index) => {
          return Number(item.gain.substring(0, item.gain.length - 1)) > 0;
        })
        .sort(getSortFun('desc', 'gain'))
        .map((item, index) => {
          return (
            item.productCode +
            ' ' +
            item.productName +
            ' 涨价差额:' +
            (item.productContainTaxPrice - item.productLastPrice).toFixed(6) +
            '元 涨幅:' +
            item.gain +
            ' 备注：' +
            item.productRemark
          );
        });
      if (brr.length > 0) {
        var msgs = brr.join('</br>');
        _str += '<h4 style="font-size: 16px">价格涨幅提醒</h4>' + msgs;
      }
      if (checkOfTips.length > 0) {
        var msgs = checkOfTips.join('</br>');
        _str += '<h4 style="font-size: 16px">非协议渠道购进提醒</h4>' + msgs;
      }
      // 大库存提醒
      var crr = rowdata
        .filter((item, index) => {
          return Number(item.bigInventoryProduct) == 1;
        })
        .map((item, index) => {
          return (
            item.productCode +
            ' ' +
            item.productName +
            ' 可销天数:' +
            item.canPinNumber +
            '天 大库存金额:' +
            item.bigInventoryProductPrice +
            '元 备注：' +
            item.productRemark
          );
        });
      if (crr.length > 0) {
        var msgs = crr.join('</br>');
        _str += '<h4 style="font-size: 16px">大库存提醒</h4>' + msgs;
      }

      // 近效期提醒
      var drr = rowdata
        .filter((item, index) => {
          return Number(item.nearExpireProduct) == 1;
        })
        .map((item, index) => {
          return (
            item.productCode +
            ' ' +
            item.productName +
            ' 近效期金额:' +
            item.nearExpireProductPrice +
            '元 备注：' +
            item.productRemark
          );
        });
      if (drr.length > 0) {
        var msgs = drr.join('</br>');
        _str += '<h4 style="font-size: 16px">近效期提醒</h4>' + msgs;
      }
      var newMap = {};
      var productData = rowdata.map(item => {return item.productCode });

      if(rowdata.length > 0){
        //处理当前周转天数
        $.ajax({
          url: '/proxy-purchase/purchase/product/getProductListNumsByCondition',
          method: 'POST',
          data: JSON.stringify({
            orgCode: ttx.orgCode,
            channelId: ttx.channelId,
            productCodeList: productData,
          }),
          dataType: 'json',
          contentType: 'application/json',
          success: function (resp) {
            if(resp.code === 0){
              if(resp.result != null){
                newMap = resp.result;
              }
            //可销天数提醒
      var daysAvailableForSale = rowdata
        .filter((item, index) => {
          //60日均天销量
          var productSixtySaleForDay = Number(item.sixtyAverageDailySales);
          //可用库存
          var productStockCur = Number(item.productStockCur);
          //10天内在途订单的商品数量总和
        let name =  '' + ttx.orgCode + '-' + ttx.channelId + '-' + item.productCode;
        let newday = newMap[name] == undefined ? 0 : newMap[name];
          //订单数量
          var productPackCountSmall = Number(item.productPackCountSmall);
          //可销天数(默认36为了提醒近期无销售)
          var daysAvailableForSale = 36;
          if (productSixtySaleForDay > 0) {
            daysAvailableForSale =
              (productStockCur + productPackCountSmall + newday) /
              productSixtySaleForDay;
          }
          return daysAvailableForSale > 35;
        })
        .map((item, index) => {
          //60日均天销量
          var productSixtySaleForDay = Number(item.sixtyAverageDailySales);
          //可用库存
          var productStockCur = Number(item.productStockCur);
          //订单数量
          var productPackCountSmall = Number(item.productPackCountSmall);
          //库存可销天数
          var amountDaysAvailableForSale = 0;
          //10天内在途订单的商品数量总和
        let name =  '' + ttx.orgCode + '-' + ttx.channelId + '-' + item.productCode;
        let newday = newMap[name] == undefined ? 0 : newMap[name];
          //可销天数
          var daysAvailableForSale = 0;
          if (productSixtySaleForDay > 0) {
            amountDaysAvailableForSale = Math.round(
              (productStockCur + newday) / productSixtySaleForDay,
            );
            daysAvailableForSale = Math.round(
              (productStockCur + productPackCountSmall + newday) /
                productSixtySaleForDay,
            );
          } else {
            return (
              item.productCode +
              ' ' +
              item.productName +
              ' 可用库存为' +
              productStockCur +
              ',近2个月无销售。'
            );
          }
          return (
            item.productCode +
            ' ' +
            item.productName +
            ' 当前可销天数为:' +
            amountDaysAvailableForSale +
            '天,采购后可销天数为：' +
            daysAvailableForSale +
            '天'
          );
        });
      if (daysAvailableForSale.length > 0) {
        var msgs = daysAvailableForSale.join('</br>');
        _str += '<h4 style="font-size: 16px">可销天数提醒</h4>' + msgs;
      }
      //当前周转天数校验提醒
      var currentTurnOverDaysReminder = rowdata.filter((item, index) => {
        //60日均天销量
        var productSixtySaleForDay = Number(item.sixtyAverageDailySales);
        //当前可用库存
        var productStockCur = Number(item.productStockCur);
        //10天内在途订单的商品数量总和
        let name =  '' + ttx.orgCode + '-' + ttx.channelId + '-' + item.productCode;
        let newday = newMap[name] == undefined ? 0 : newMap[name];
        //周转天数
        var turnoverDays = 0;
        if (productSixtySaleForDay > 0) {
          turnoverDays = Math.round((productStockCur + newday)/ productSixtySaleForDay);
        }
        //过滤出周转天数≥90天
        return turnoverDays >= 90;
      });
      //整理出周转天数大于等于90的信息
      var currentTurnOverDaysReminderInfo = currentTurnOverDaysReminder.map(
        (item, index) => {
          //商品编码
          var code = item.productCode;
          //商品名称
          var name = item.productName;
          return code + '   ' + name;
        },
      );
      //用于过滤出符合条件的信息
      var DaysproductCodeArr = currentTurnOverDaysReminder.map(
        (item, index) => {
          //商品编码
          var code = item.productCode;
          return code;
        },
      );
      var turnoverDaysHtmls = '';
      if (currentTurnOverDaysReminderInfo.length > 0) {
        var msgs = '';
        currentTurnOverDaysReminderInfo.forEach((item, index) => {
          if ((index + 1) % 2 != 0) {
            msgs += `<div style="display:inline-block;margin-right:20px;width:250px">${item}</div>`;
          } else {
            msgs += `<div style="display:inline-block;width:250px">${item}</div></br>`;
          }
        });
        turnoverDaysHtmls = '<div>';
        turnoverDaysHtmls +=
          '<p  style="font-size: 14px;color:red">温馨提示：若无特殊情况，建议“过滤后提交”，走“特殊审批”存在被驳回风险！</p>';
        turnoverDaysHtmls +=
          '<h4 style="font-size: 16px;font-weight:700">以下商品当前周转天数超90天，禁止提交！</h4>';
        turnoverDaysHtmls += msgs;
        turnoverDaysHtmls +=
          '<div>' +
          '<textarea id="copyTurnoverDays" class="btn btn-info" style="float: right;margin-top: -966px;">' +
          currentTurnOverDaysReminderInfo
            .join('</br>')
            .replace(/\<\/br>/g, '\n');

        turnoverDaysHtmls += '</textarea> ' + '</div>' + '</div>';
      }
      var dialogTip;
      var turnoverDaysFunc = function () {
        dialogTip = utils
          .dialog({
            title: `提示`,
            content:
              '<div style="max-height: 300px;overflow-y: auto;">' +
              turnoverDaysHtmls +
              '</div>',
            okValue: '过滤后提交',
            ok: function () {
              var ttlInfo = ttl.filter((item, index) => {
                var code = item.productCode;
                var okInfo = DaysproductCodeArr.find((codeItem) => {
                  return codeItem == code;
                });
                return okInfo ? false : true;
              });
              if (ttlInfo && ttlInfo.length > 0) {
                checkGoods(ttx, ttlInfo);
              } else {
                utils
                  .dialog({
                    content: '请至少保留一条有效数据！',
                    quickClose: true,
                    timeout: 2000,
                  })
                  .show();
                $('#submitAssert').attr('disabled', false);
              }
            },
            statusbar:
              '<button type="button" class="btn btn-info" id="turnoverDaysBtn" style="float: left;margin-top: -6px;">复制信息</button><button type="button" class="btn btn-info" id="specialApprovalBtn" style="float: left;margin-top: -6px;margin-left: 200px;">特殊审批</button>',
          })
          .showModal();
        $('#turnoverDaysBtn').on('click', function () {
          var flag = utils.copyText($('#copyTurnoverDays').val());
          if (flag) {
            setTimeout(() => {
              dialogTip.close().remove();
            }, 200);
            utils
              .dialog({
                content: '复制成功！',
                quickClose: true,
                timeout: 2000,
              })
              .show();
          }
        });
        $('#specialApprovalBtn').on('click', function () {
          setTimeout(() => {
            dialogTip.close().remove();
          }, 200);
          checkGoods(ttx, ttl);
        });
      };
      if (checkAppList.length != 0) {
			//负毛利提示
			var msg = checkAppList.join('</br>');
			var priceMsg = checkPriceList.join('</br>');
			var htmls = '<div>' +
					// '<h4 style="font-size: 16px">负毛利提示</h4>' +
					//     '<h5 style="font-size: 16px">采购价大于APP售价，依据公司要求，不允许下单，请及时调价！</h5>'+
					// '<div>'+
					// msg+
					'</div>' +
					'<textarea id="copyInput" class="btn btn-info" style="float: right;margin-top: -966px;"  >'
					+ (err.length != 0 ? '\n 负毛利提醒 \n' + err.join('</br>').replace(/\<\/br>/g, "\n") : '')
					+ '\n 采购价大于APP售价与连锁APP售价，依据公司要求，不允许下单，请及时调价！\n' + msg.replace(/\<\/br>/g, "\n")
					+ (brr.length != 0 ? '\n 采购价涨幅提醒 \n' + brr.join('</br>').replace(/\<\/br>/g, "\n") : '')
					+ (crr.length != 0 ? '\n 大库存提醒 \n' + crr.join('</br>').replace(/\<\/br>/g, "\n") : '')
					+ (drr.length != 0 ? '\n 近效期提醒 \n' + drr.join('</br>').replace(/\<\/br>/g, "\n") : '')
					+ (checkOfTips.length > 0 ? '\n 非协议渠道购进 \n' + checkOfTips.join('</br>').replace(/\<\/br>/g, "\n") : '')
					+ (daysAvailableForSale.length > 0 ? '\n 可销天数提醒 \n' + daysAvailableForSale.join('</br>').replace(/\<\/br>/g, "\n") : '')
					+ datas.result
			htmls += '</textarea> ' +
					'</div>';
			_str += '</br></br>' + datas.result;
			htmls += _str;
				utils.dialog({
					title: '提示',
					content: '<div style="max-height: 300px; overflow-y: auto;">' + htmls + '</div>',
					button: [{
						value: '复制信息',
						callback: function () {
							var flag = utils.copyText($("#copyInput").val());
							if (flag) utils.dialog({
								content: '复制成功！',
								quickClose: true,
								timeout: 2000
							}).show()
							//
							// $("#copyInput").select(); // 选择对象
							// var flag = document.execCommand("Copy","false",null); // 执行浏览器复制命令
							// if(flag) utils.dialog({content: '复制成功！', quickClose: true, timeout: 2000}).show();
						}
					}]
				}).showModal();

		} else if (brr.length > 0 || crr.length > 0 || drr.length > 0 || checkOfTips.length > 0 || daysAvailableForSale.length>0 || datas.result.length > 0) {
			utils.dialog({
				title: '提示',
				// content: '<div>' +
				//
				//                 '<div style="width: 470px;max-height: 300px;overflow-y: auto">' +
				//                 priceMsg + (_str != ''?_str: '')+
				//                 '</div>' +
				//                 ' ' +
				//                 '</div>',
				content: '<div style="max-height: 300px;overflow-y: auto;">'
				+ (brr.length != 0 ? '\n 价格涨幅提醒 \n' + brr.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ (crr.length != 0 ? '\n 大库存提醒 \n' + crr.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ (drr.length != 0 ? '\n 近效期提醒 \n' + drr.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ (checkOfTips.length > 0 ? '\n 非协议渠道购进提醒 \n' + checkOfTips.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ (daysAvailableForSale.length > 0 ? '\n 可销天数提醒 \n' + daysAvailableForSale.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ datas.result
				+ '<textarea id="copyprice2" class="btn btn-info" style="display:none; float: right;margin-top: -966px;"  >'
				+ (brr.length != 0 ? '\n 价格涨幅提醒 \n' + brr.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ (crr.length != 0 ? '\n 大库存提醒 \n' + crr.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ (drr.length != 0 ? '\n 近效期提醒 \n' + drr.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ (checkOfTips.length > 0 ? '\n 非协议渠道购进提醒 \n' + checkOfTips.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ (daysAvailableForSale.length > 0 ? '\n 可销天数提醒 \n' + daysAvailableForSale.join('</br>').replace(/\<\/br>/g, "\n") : '')
				+ datas.result
				+ '</textarea></div>',
				okValue: '继续提交',
				ok: function () {
					if(currentTurnOverDaysReminderInfo.length>0){
						turnoverDaysFunc()
					}else{
						checkGoods(ttx, ttl);
					}
				},
				cancelValue: '暂不提交',
				cancel: function () {
					return;
				},
				statusbar: '<button type="button" class="btn btn-info" id="copyPriceBtn2" style="float: right;margin-top: -6px;">复制信息</button>'
			}).showModal();
			$("#copyPriceBtn2").on("click", function () {
				var flag = utils.copyText($("#copyprice2").val());
				if (flag) utils.dialog({
					content: '复制成功！',
					quickClose: true,
					timeout: 2000
				}).show()
			})
        } else {
                checkGoods(ttx, ttl)
        }
    }
    },
          error: () => { },
        });
      }
      // //可销天数提醒
      // var daysAvailableForSale = rowdata
      //   .filter((item, index) => {
      //     //60日均天销量
      //     var productSixtySaleForDay = Number(item.sixtyAverageDailySales);
      //     //可用库存
      //     var productStockCur = Number(item.productStockCur);
      //     //订单数量
      //     var productPackCountSmall = Number(item.productPackCountSmall);
      //     //可销天数(默认36为了提醒近期无销售)
      //     var daysAvailableForSale = 36;
      //     if (productSixtySaleForDay > 0) {
      //       daysAvailableForSale =
      //         (productStockCur + productPackCountSmall) /
      //         productSixtySaleForDay;
      //     }
      //     return daysAvailableForSale > 35;
      //   })
      //   .map((item, index) => {
      //     //60日均天销量
      //     var productSixtySaleForDay = Number(item.sixtyAverageDailySales);
      //     //可用库存
      //     var productStockCur = Number(item.productStockCur);
      //     //订单数量
      //     var productPackCountSmall = Number(item.productPackCountSmall);
      //     //库存可销天数
      //     var amountDaysAvailableForSale = 0;
      //     //可销天数
      //     var daysAvailableForSale = 0;
      //     if (productSixtySaleForDay > 0) {
      //       amountDaysAvailableForSale = Math.round(
      //         productStockCur / productSixtySaleForDay,
      //       );
      //       daysAvailableForSale = Math.round(
      //         (productStockCur + productPackCountSmall) /
      //         productSixtySaleForDay,
      //       );
      //     } else {
      //       return (
      //         item.productCode +
      //         ' ' +
      //         item.productName +
      //         ' 可用库存为' +
      //         productStockCur +
      //         ',近2个月无销售。'
      //       );
      //     }
      //     return (
      //       item.productCode +
      //       ' ' +
      //       item.productName +
      //       ' 当前可销天数为:' +
      //       amountDaysAvailableForSale +
      //       '天,采购后可销天数为：' +
      //       daysAvailableForSale +
      //       '天'
      //     );
      //   });
      // if (daysAvailableForSale.length > 0) {
      //   var msgs = daysAvailableForSale.join('</br>');
      //   _str += '<h4 style="font-size: 16px">可销天数提醒</h4>' + msgs;
      // }
      // //当前周转天数校验提醒
      // var currentTurnOverDaysReminder = rowdata.filter((item, index) => {
      //   //60日均天销量
      //   var productSixtySaleForDay = Number(item.sixtyAverageDailySales);
      //   //当前可用库存
      //   var productStockCur = Number(item.productStockCur);
      //   //周转天数
      //   var turnoverDays = 0;
      //   if (productSixtySaleForDay > 0) {
      //     turnoverDays = Math.round(productStockCur / productSixtySaleForDay);
      //   }
      //   //过滤出周转天数≥90天
      //   return turnoverDays >= 90;
      // });
      // //整理出周转天数大于等于90的信息
      // var currentTurnOverDaysReminderInfo = currentTurnOverDaysReminder.map(
      //   (item, index) => {
      //     //商品编码
      //     var code = item.productCode;
      //     //商品名称
      //     var name = item.productName;
      //     return code + '   ' + name;
      //   },
      // );
      // //用于过滤出符合条件的信息
      // var DaysproductCodeArr = currentTurnOverDaysReminder.map(
      //   (item, index) => {
      //     //商品编码
      //     var code = item.productCode;
      //     return code;
      //   },
      // );
      // var turnoverDaysHtmls = '';
      // if (currentTurnOverDaysReminderInfo.length > 0) {
      //   var msgs = '';
      //   currentTurnOverDaysReminderInfo.forEach((item, index) => {
      //     if ((index + 1) % 2 != 0) {
      //       msgs +=
      //         `<div style="display:inline-block;margin-right:20px;width:250px">` +
      //         item +
      //         `</div>`;
      //     } else {
      //       msgs +=
      //         `<div style="display:inline-block;width:250px">` +
      //         item +
      //         `</div></br>`;
      //     }
      //   });
      //   turnoverDaysHtmls = '<div>';
      //   turnoverDaysHtmls +=
      //     '<p  style="font-size: 14px;color:red">温馨提示：若无特殊情况，建议“过滤后提交”，走“特殊审批”存在被驳回风险！</p>';
      //   turnoverDaysHtmls +=
      //     '<h4 style="font-size: 16px;font-weight:700">以下商品当前周转天数超90天，禁止提交！</h4>';
      //   turnoverDaysHtmls += msgs;
      //   turnoverDaysHtmls +=
      //     '<div>' +
      //     '<textarea id="copyTurnoverDays" class="btn btn-info" style="float: right;margin-top: -966px;">' +
      //     currentTurnOverDaysReminderInfo
      //       .join('</br>')
      //       .replace(/\<\/br>/g, '\n');
      //   turnoverDaysHtmls += '</textarea> ' + '</div>' + '</div>';
      // }
      // var dialogTip;
      // var turnoverDaysFunc = function () {
      //   dialogTip = utils
      //     .dialog({
      //       title: `提示`,
      //       content:
      //         '<div style="max-height: 300px;overflow-y: auto;">' +
      //         turnoverDaysHtmls +
      //         '</div>',
      //       okValue: '过滤后提交',
      //       ok: function () {
      //         var ttlInfo = ttl.filter((item, index) => {
      //           var code = item.productCode;
      //           var okInfo = DaysproductCodeArr.find((codeItem) => {
      //             return codeItem == code;
      //           });
      //           return okInfo ? false : true;
      //         });
      //         if (ttlInfo && ttlInfo.length > 0) {
      //           checkGoods(ttx, ttlInfo);
      //         } else {
      //           utils
      //             .dialog({
      //               content: '请至少保留一条有效数据！',
      //               quickClose: true,
      //               timeout: 2000,
      //             })
      //             .show();
      //           $('#submitAssert').attr('disabled', false);
      //         }
      //       },
      //       statusbar:
      //         '<button type="button" class="btn btn-info" id="turnoverDaysBtn" style="float: left;margin-top: -6px;">复制信息</button><button type="button" class="btn btn-info" id="specialApprovalBtn" style="float: left;margin-top: -6px;margin-left: 200px;">特殊审批</button>',
      //     })
      //     .showModal();
      //   $('#turnoverDaysBtn').on('click', function () {
      //     var flag = utils.copyText($('#copyTurnoverDays').val());
      //     if (flag) {
      //       setTimeout(() => {
      //         dialogTip.close().remove();
      //       }, 200);
      //       utils
      //         .dialog({
      //           content: '复制成功！',
      //           quickClose: true,
      //           timeout: 2000,
      //         })
      //         .show();
      //     }
      //   });
      //   $('#specialApprovalBtn').on('click', function () {
      //     setTimeout(() => {
      //       dialogTip.close().remove();
      //     }, 200);
      //     checkGoods(ttx, ttl);
      //   });
      // };
      // if (checkAppList.length != 0) {
      //   //负毛利提示
      //   var msg = checkAppList.join('</br>');
      //   var priceMsg = checkPriceList.join('</br>');
      //   var htmls =
      //     '<div>' +
      //     '</div>' +
      //     '<textarea id="copyInput" class="btn btn-info" style="float: right;margin-top: -966px;"  >' +
      //     (err.length != 0
      //       ? '\n 负毛利提醒 \n' + err.join('</br>').replace(/\<\/br>/g, '\n')
      //       : '') +
      //     '\n 采购价大于APP售价与连锁APP售价，依据公司要求，不允许下单，请及时调价！\n' +
      //     msg.replace(/\<\/br>/g, '\n') +
      //     (brr.length != 0
      //       ? '\n 价格涨幅提醒 \n' + brr.join('</br>').replace(/\<\/br>/g, '\n')
      //       : '') +
      //     (crr.length != 0
      //       ? '\n 大库存提醒 \n' + crr.join('</br>').replace(/\<\/br>/g, '\n')
      //       : '') +
      //     (drr.length != 0
      //       ? '\n 近效期提醒 \n' + drr.join('</br>').replace(/\<\/br>/g, '\n')
      //       : '') +
      //     (checkOfTips.length > 0
      //       ? '\n 非协议渠道购进 \n' +
      //       checkOfTips.join('</br>').replace(/\<\/br>/g, '\n')
      //       : '') +
      //     (daysAvailableForSale.length > 0
      //       ? '\n 可售天数提醒 \n' +
      //       daysAvailableForSale.join('</br>').replace(/\<\/br>/g, '\n')
      //       : '') +
      //     datas.result;
      //   htmls += '</textarea> ' + '</div>';
      //   _str += '</br></br>' + datas.result;
      //   htmls += _str;

      //   if (
      //     $('#find_supplier').val().indexOf('小药药') >= 0 ||
      //     orderType == '1'
      //   ) {
      //     utils
      //       .dialog({
      //         title: '提示',
      //         content:
      //           '<div style="max-height: 300px;overflow-y: auto;">' +
      //           htmls +
      //           '</div>',
      //         okValue: '继续提交',
      //         ok: function () {
      //           if (currentTurnOverDaysReminderInfo.length > 0) {
      //             turnoverDaysFunc();
      //           } else {
      //             checkGoods(ttx, ttl);
      //           }
      //         },
      //         cancelValue: '暂不提交',
      //         cancel: function () {
      //           $('#submitAssert').attr('disabled', false);
      //           return;
      //         },
      //         statusbar:
      //           '<button type="button" class="btn btn-info" id="copyBtn" style="float: right;margin-top: -6px;">复制信息</button>',
      //       })
      //       .showModal();

      //     $('#copyBtn').on('click', function () {
      //       $('#copyInput').select(); // 选择对象
      //       var flag = document.execCommand('Copy', 'false', null); // 执行浏览器复制命令
      //       if (flag)
      //         utils
      //           .dialog({
      //             content: '复制成功！',
      //             quickClose: true,
      //             timeout: 2000,
      //           })
      //           .show();
      //     });
      //   } else {
      //     utils
      //       .dialog({
      //         title: '提示',
      //         content:
      //           '<div style="max-height: 300px; overflow-y: auto;">' +
      //           htmls +
      //           '</div>',
      //         button: [
      //           {
      //             value: '复制信息',
      //             callback: function () {
      //               $('#submitAssert').attr('disabled', false);
      //               $('#copyInput').select(); // 选择对象
      //               var flag = document.execCommand('Copy', 'false', null); // 执行浏览器复制命令
      //               if (flag)
      //                 utils
      //                   .dialog({
      //                     content: '复制成功！',
      //                     quickClose: true,
      //                     timeout: 2000,
      //                   })
      //                   .show();
      //               return false;
      //             },
      //           },
      //         ],
      //       })
      //       .showModal();
      //   }
      // } else if (
      //   brr.length > 0 ||
      //   crr.length > 0 ||
      //   drr.length > 0 ||
      //   checkOfTips.length > 0 ||
      //   daysAvailableForSale.length > 0
      // ) {
      //   var priceMsg = checkPriceList.join('</br>');
      //   utils
      //     .dialog({
      //       title: '提示',
      //       content:
      //         '<div style="max-height: 300px;overflow-y: auto;">' +
      //         _str +
      //         '</br>' +
      //         datas.result +
      //         '<textarea id="copyprice2" class="btn btn-info" style="display:none; float: right;margin-top: -966px;"  >' +
      //         (brr.length != 0
      //           ? '\n 价格涨幅提醒 \n' +
      //           brr.join('</br>').replace(/\<\/br>/g, '\n')
      //           : '') +
      //         (crr.length != 0
      //           ? '\n 大库存提醒 \n' +
      //           crr.join('</br>').replace(/\<\/br>/g, '\n')
      //           : '') +
      //         (drr.length != 0
      //           ? '\n 近效期提醒 \n' +
      //           drr.join('</br>').replace(/\<\/br>/g, '\n')
      //           : '') +
      //         (checkOfTips.length > 0
      //           ? '\n 非协议渠道购进提醒 \n' +
      //           checkOfTips.join('</br>').replace(/\<\/br>/g, '\n')
      //           : '') +
      //         (daysAvailableForSale.length > 0
      //           ? '\n 可售天数提醒 \n' +
      //           daysAvailableForSale.join('</br>').replace(/\<\/br>/g, '\n')
      //           : '') +
      //         '</br>' +
      //         datas.result +
      //         '</textarea></div>',
      //       okValue: '继续提交',
      //       ok: function () {
      //         if (currentTurnOverDaysReminderInfo.length > 0) {
      //           turnoverDaysFunc();
      //         } else {
      //           checkGoods(ttx, ttl);
      //         }
      //       },
      //       cancelValue: '暂不提交',
      //       cancel: function () {
      //         $('#submitAssert').attr('disabled', false);
      //         return;
      //       },
      //       statusbar:
      //         '<button type="button" class="btn btn-info" id="copyPriceBtn2" style="float: right;margin-top: -6px;">复制信息</button>',
      //     })
      //     .showModal();
      //   $('#copyPriceBtn2').on('click', function () {
      //     var flag = utils.copyText($('#copyprice2').val());
      //     if (flag)
      //       utils
      //         .dialog({
      //           content: '复制成功！',
      //           quickClose: true,
      //           timeout: 2000,
      //         })
      //         .show();
      //   });
      // } else {
      //   checkGoods(ttx, ttl);
      // }
    } else {
      $('#submitAssert').attr('disabled', false);
      utils
        .dialog({
          content: '最少选择一条商品！',
          quickClose: true,
          timeout: 4000,
        })
        .showModal();
      tableAddRow('X_Tablea', {});
    }
  });
  var getSortFun = function (order, sortBy) {
    var ordAlpah = order == 'asc' ? '>' : '<';
    var sortFun = new Function(
      'a',
      'b',
      'return Number(parseFloat(a.' +
      sortBy +
      '))' +
      ordAlpah +
      '(Number(parseFloat(b.' +
      sortBy +
      ')) ? (Number(parseFloat(b.' +
      sortBy +
      '))): 0)?1:-1',
    );
    return sortFun;
  };
}

function paymentStatus(cellV) {
  if (cellV != undefined && cellV != '' && cellV != null) {
    cellV = parseFloat(cellV);
    return cellV;
  }
  return '';
}

//校验商品要选择
function validates() {
  if (validform('validates').form()) {
  } else {
    //验证不通过

    return false;
  }
}

function originPlaceMethod(cellvalue) {
  if (!cellvalue) {
    return '';
  } else {
    if (cellvalue.isFlg == '1') {
      var xz = cellvalue.xuanzhong;
      if (!cellvalue.allPlace) {
        return cellvalue.xuanzhong;
      }
      var sap = cellvalue.allPlace.split(',');
      //反转字符串
      var ap = sap.reverse();

      console.log(sap);
      console.log(ap);
      if (ap.length == 1) {
        return xz;
      } else {
        var ss = '<select class="form-control">';

        for (var i = 0; i < ap.length; i++) {
          if (xz == ap[i]) {
            ss +=
              '<option selected="selected" value="' +
              ap[i] +
              '">' +
              ap[i] +
              '</option>';
          } else {
            ss += '<option value="' + ap[i] + '">' + ap[i] + '</option>';
          }
        }
        ss += '</select>';
        return ss;
      }
    } else {
      var trs = new Array(); //定义一数组

      trs = cellvalue.split(','); //字符分割
      var strs = trs.reverse();
      if (strs.length == 1) {
        return cellvalue;
      }
      if (strs.length == 1) {
        return cellvalue;
      }
      var ss = '<select class="form-control">';
      for (var i = 0; i < strs.length; i++) {
        ss += '<option value="' + strs[i] + '">' + strs[i] + '</option>';
      }
      ss += '</select>';
      return ss;
    }
  }
}
//ceshi
//表格内商品模糊搜索
function productName_Autocomplete(tableId, rowData, rowId) {
  if (!rowData) {
    rowData = $('#' + tableId).XGrid('getRowData');
  }
  rowId = rowData.sort
    ? rowId
    : $('#' + tableId).XGrid('getRowData')[0]['rowid'];
  var $input = $(
    '#' +
    tableId +
    ' tr#' +
    (rowData.sort ? rowData.sort : rowId) +
    ' input[name=productName]',
  );
  $input.Autocomplete({
    serviceUrl: '/proxy-purchase/purchase/supplier/findPruchaseProductByMnemonInfo', //异步请求
    paramName: 'keyword', //查询参数，默认 query
    params: {
      simpleCode: '',
      channelId: function () {
        return $('#channelId').val();
      }, //_channeld
      orgCode: $('#orgCode').val(),
      centralizedPurchaseType: function () {
        return $('#selectCentralizedPurchaseType').val();
      },
    },
    dataType: 'json',
    zIndex: 9,
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true,
    noSuggestionNotice: '查询无结果',
    autoSelectFirst: true,
    transformResult: function (response) {
      return {
        suggestions: $.map(response, function (dataItem) {
          return { value: dataItem.productName, data: dataItem.productCode };
        }),
      };
    },
    onSelect: function (result) {
      var code = result.data,
        val = result.value;
      var rowId = $input.parents('tr').attr('id');
      var emptyData = {
        productCode: '',
        productName: '',
        commonName: '',
        productSpecification: '',
        productPackUnitSmall: '',
        productProduceFactory: '',
        productOriginPlace: '',
        dosageForm: '',
        productPackCountSmall: '',
        productContainTaxPrice: '',
        productContainTaxMoney: '',
        productAppContainTaxPrice: '',
        productEntryTax: '',
        productPackUnitMedium: '',
        productPackUnitBig: '',
        productAdjustDay: '',
        productBrandManufacturers: '',
        productApprovalNumber: '',
        productApprovalNumberExpireDate: '',
        ifOldApprovalNumber: '',
        productLastPrice: '',
        productStockCur: '',
        canPinNumber: '',
        chainGuidePrice: '',
        productSevenAverageSale: '',
        productFifteenAverageSale: '',
        productThirtyAverageSale: '',
        productPackOnWayCount: '',
        productUpperLimitStock: '',
        productStockSafe: '',
        varietiesAgreement: '',
        productRemark: '',
        lastSupplierCode: '',
        lastSupplierName: '',
        supplierPaymentPeriod: '',
        productSuggestPlanCount: '',
        purchaseCount: '',
        lastProductContainTaxPrice: '',
        productThirtySale: '',
        productSixtySale: '',
        productUnitPrice: '',
        productMinPriceNinetyDay: '',
        productMaxPriceNinetyDay: '',
        productStockTurnDay: '',
        id: '',
        supplyArrivalPeriod: '',
        productId: '',
        thirtyAverageSaleVolume: '',
        standardPrice: '',
        scopeOfOperation: '',
        largeCategory: '',
        specialAttributes: '',
        remainDays: '',
      };
      $('tr#' + rowId + ' td[row-describedby=productName] input').attr(
        'oldvalue',
        val,
      );
      setEmptyRow(rowId, '', emptyData);
      var channelId = $('#channelId').val();
      if (orderType == '1') {
        channelId = '1';
      }
      //sypzjg ==1 不需要拦截
      var sypzjg = $('#sypzjg').val();
      sypzjg = 1;  //写死不拦截
      $.ajax({
        type: 'POST',
        url: '/proxy-purchase/purchase/product/loadProductData?supplyArrivalPeriod',
        data: {
          supplyArrivalPeriod: $('#arrivalPeriod').val(),
          productAdjustDay: 30,
          simpleCode: $('#simpleCode').val(),
          supplierId: $('#supplierId').val(),
          keyword: code,
          factoryOption: '',
          factorySelect: '',
          channelId: channelId,
          supplierCode: $('#find_supplierc').val(),
          supplierName: $('#find_supplier').val(),
          pageNum: 1,
          pageSize: 10,
          orderType: orderType,
          orgCode: $('#orgCode').val(),
          centralizedPurchaseType: $('#selectCentralizedPurchaseType').val(),
        },
        success: function (res) {
          var rowData = res.result.list;
          if (rowData != null && rowData.length) {
            //sypzjg ==1 不需要拦截 // sypzjg == 0 &&
            if (
              sypzjg == 0 &&
              rowData[0]['firstPurchaseStore'] != null &&
              rowData[0]['firstPurchaseStore'] == 0
            ) {
              //首营商品
              //首营供应商
              var firstBattalionSupplier = rowData[0]['firstBattalionSupplier'];
              var supplierId = $('#supplierId').val();

              if (
                firstBattalionSupplier != null &&
                firstBattalionSupplier != '' &&
                firstBattalionSupplier != supplierId
              ) {
                $input.val('');
                utils
                  .dialog({
                    content:
                      rowData[0]['productName'] +
                      '商品为首次采购，需选择对应首营供应商！',
                    quickClose: true,
                    timeout: 2000,
                  })
                  .showModal();
                return false;
              }
            }
            if (rowData[0]['enclosureValidityType'] === 2) {
              utils
                .dialog({
                  content: rowData[0]['enclosureValidityMsg'],
                  quickClose: true,
                  timeout: 2000,
                })
                .showModal();
              return;
            }

            if (rowData[0]['enclosureValidityType'] === 1) {
              utils
                .dialog({
                  title: '提示',
                  content: rowData[0]['enclosureValidityMsg'],
                  okValue: '确定',
                  ok: function () {
                    setEmptyRow(
                      rowId,
                      function () {
                        $(
                          'tr#' +
                          rowId +
                          ' td[row-describedby=productName] input',
                        ).attr('oldvalue', val);
                        $(
                          'tr#' +
                          rowId +
                          ' td[row-describedby=productPackCountSmall] input',
                        ).focus();
                      },
                      rowData,
                    );
                  },
                })
                .showModal();
              return;
            }

            setEmptyRow(
              rowId,
              function () {
                $(
                  'tr#' + rowId + ' td[row-describedby=productName] input',
                ).attr('oldvalue', val);
                $(
                  'tr#' +
                  rowId +
                  ' td[row-describedby=productPackCountSmall] input',
                ).focus();
              },
              rowData,
            );
          } else {
            utils
              .dialog({
                //title:'提示',
                content:
                  '此商品不符合新增条件(当前采购员商品;供应商经营范围内;不是淘汰,过期,限采,停用商品)！',
                okValue: '确认',
                ok: function () { },
              })
              .showModal();
          }
        },
      });
    },
    onSearchStart: function (query) {
      if (!$('#find_supplier').val()) {
        utils
          .dialog({
            content: '请先选择供应商',
            quickClose: true,
            timeout: 2000,
          })
          .showModal();
        $(this).Autocomplete('disable');
      } else {
        $(this).Autocomplete('enable');
      }
    },
    onNoneSelect: function (params, suggestions) {
      $input.val('');
    },
    onSearchComplete: function (query, suggestions) {
      var $ele = $(this);
      if (suggestions && suggestions.length === 0) {
        $ele.attr('oldvalue', '');
      } else {
        $ele.attr('oldvalue', $ele.val());
      }
      if (!$ele.is(':focus')) {
        $ele.Autocomplete('hide');
      }
    },
  });
  $input
    .on({
      dblclick: function (e) {
        var rowId = $(this).parents('tr').attr('id');
        setEmptyRow(rowId);
      },
    })
    .siblings('.glyphicon-search')
    .on('click', function (e) {
      var rowId = $(this).parents('tr').attr('id');
      setEmptyRow(rowId);
      e.stopPropagation();
    });
}

function validateProductInfo() {
  if (validform('validates').form()) {
    console.log(111);
    return false;
  }

  var ttl = $('#X_Tablea')
    .XGrid('getRowData')
    .filter(function (item, index) {
      if (!item.productCode) {
        $('#X_Tablea').XGrid('delRowData', item.sort);
      } else {
        return item;
      }
    });
  if (ttl.length >= 1) {
    $.each(ttl, function (i, item) {
      var productContainTaxPrice = item.productContainTaxPrice;
      if (
        (ttl[i].giftFlag == '0' &&
          (!productContainTaxPrice || productContainTaxPrice < 0.01)) ||
        (ttl[i].giftFlag == '1' &&
          (!productContainTaxPrice || productContainTaxPrice < 0))
      ) {
        utils
          .dialog({
            content:
              '含税单价不能为空；且药品必须大于等于0.01元、赠品必须大于等于0元！',
            quickClose: true,
            timeout: 1000,
          })
          .showModal();
        flag = true;
        return false;
      }
    });
  }
}

//业务类型搜索图标
$(document).on('click', '.glyphicon-search', function () {
  $(this).siblings('input').trigger('dblclick');
});

//业务类型 输入框双击 弹出业务类型列表
$('#channelId_inp').dblclick(function () {
  utils.channelDialog('0', 1).then((res) => {
    console.log(res);
    let _str_name = '',
      _str_code = '',
      _str_val = '';
    let _str_arr = res.map((item) => {
      return item.channelName;
    });
    _str_name = _str_arr.join(',');

    let _str_code_arr = res.map((item) => {
      return item.channelCode;
    });
    _str_code = _str_code_arr.join(',');
    let _str_val_arr = res.map((item) => {
      return item.channelValue;
    });
    _str_val = _str_val_arr.join(',');
    $('#channelId_inp').val(_str_val);
    $('#channelId').val(_str_code);
    change_channelID(_str_code);
    startLoaderSelete(res);
  });
});
function startLoaderSelete(vals) {
  var _html = '';
  if (!vals || vals.length <= 0) {
    return false;
  }
  $.ajax({
    url: '/proxy-purchase/purchase/orderAttribute/dict/queryListByType',
    type: 'GET',
    data: { type: vals[0].parentCode, groupStatus: 1 },
    beforeSend: function () {
      console.log('正在进行，请稍候');
      parent.showLoading({ hideTime: 99999 });
    },
    success: function (data) {
      if (data && data.code == 0 && data.result && data.result.length > 0) {
        $('#selectCentralizedPurchaseType').empty();
        for (var i = 0; i < data.result.length; i++) {
          _html =
            _html +
            '<option value="' +
            data.result[i].code +
            '">' +
            data.result[i].name +
            '</option>';
        }
        $('#selectCentralizedPurchaseType').append(_html);
      }
    },
    error: function () {
      utils
        .dialog({ content: '请求失败', quickClose: true, timeout: 2000 })
        .showModal();
      parent.hideLoading();
    },
    complete: function () {
      parent.hideLoading();
    },
  });
}
/******************************************llj**************************/
function seleteSelectadvancePaymentOption() {
  var _options = '';
  var _booles = false;
  if (supplierSettlementVoListOption.length > 0) {
    for (var n = 0; n < supplierSettlementVoListOption.length; n++) {
      if (supplierSettlementVoListOption[n].code == 2001) {
        _booles = true;
      }
    }
  }
  if (_booles) {
    supplierSettlementVoListOption.length > 1
      ? (_options =
        '<option value="0">否</option><option value="1">是</option>')
      : (_options = '<option value="1">是</option>');
  } else {
    _options = '<option value="0">否</option>';
  }
  if(_options == '<option value="1">是</option>'){
    //琦玉
    $.ajax({
      type: 'GET',
      url: '/proxy-purchase/purchase/supplier/supplier-black-judge?supplierCode='+ $("[name='supplierCode']").val() +'&orgCode='+ $("[name='orgCode']").val() ,
      async: false,
      contentType: 'application/json;charset=utf-8',
      dataType: 'json',
      success: function (response) {
        let { result, code, msg } = response;
        if(result != null){
          utils
            .dialog({
              title: '提示',
              content: result,
              okValue: '确定',
              ok: function () {
                return
              },
            })
            .showModal();
        }
    }
    });
}
  $('#selectadvancePayment').empty().append(_options);
}
/****llj 联动****/
$('#selectadvancePayment').on('change', function () {
  var _id = '';
  if ($(this).val() == 1) {
    $('#supplierSettlementVoList').val('2001');
  } else {
    if (supplierSettlementVoListOption.length > 0) {
      for (var n = 0; n < supplierSettlementVoListOption.length; n++) {
        if (supplierSettlementVoListOption[n].code != 2001) {
          _id = supplierSettlementVoListOption[n].code;
          break;
        }
      }
    }
    $('#supplierSettlementVoList').val(_id);
  }
  //琦玉 设置是否预付款变为是弹窗拦截
  if( $('#selectadvancePayment').val() == '1'){
    $.ajax({
      type: 'GET',
      url: '/proxy-purchase/purchase/supplier/supplier-black-judge?supplierCode='+ $("[name='supplierCode']").val() +'&orgCode='+ $("[name='orgCode']").val() ,
      async: false,
      contentType: 'application/json;charset=utf-8',
      dataType: 'json',
      success: function (response) {
        let { result, code, msg } = response;
        console.log('yyyy',result);
        if(result != null){
          utils
            .dialog({
              title: '提示',
              content: result,
              okValue: '确定',
              ok: function () {
                console.log('zmt');
              },
            })
            .showModal();
        }
    }
  })
  } 
});
/****                    **********************************************************************llj***/

//查询是否标红商品
function queryProductHit() {
  //组装请求参数ttx
  let ttx = $('#basicInfo').serializeToJSON();
  //如果是大客户则默认传值为1，否则获取下拉框
  if (orderType == '1') {
    ttx.channelName = 'YBM';
    ttx.channelId = '1';
  } else {
    ttx.channelName = $('#channelId_inp').val();
  }

  $('#sumForm')
    .find('span.colmd8')
    .each(function (i) {
      ttx[$(this).attr('name')] = $(this).text();
    });

  //组装请求参数ttl
  let ttl = $('#X_Tablea')
    .XGrid('getRowData')
    .filter(function (item) {
      if (!item.productCode) {
        $('#X_Tablea').XGrid('delRowData', item.sort);
      } else {
        //订单数量和含税单价有值
        if (item.productPackCountSmall && item.productContainTaxPrice) {
          return item;
        }
      }
    });
  if (ttl.length) {
    for (var i = 0; i < ttl.length; i++) {
      var opcel = $(
        '#' + ttl[i].id + ' td[row-describedby="productOriginPlace"]',
      ).html();
      if (opcel) {
        if (opcel.indexOf('select') == -1) {
          ttl[i].productOriginPlace = opcel;
        } else {
          ttl[i].productOriginPlace = $('#' + ttl[i].id + '  select').val();
        }
      }
      if (ttl[i].ifOldApprovalNumber === '是') {
        ttl[i].ifOldApprovalNumber = '1';
      } else {
        ttl[i].ifOldApprovalNumber = '0';
      }
      if (
        ttl[i].varietiesAgreement === '是' ||
        ttl[i].varietiesAgreement === '1'
      ) {
        ttl[i].varietiesAgreement = '1';
      } else if (
        ttl[i].varietiesAgreement === '否' ||
        ttl[i].varietiesAgreement === '0'
      ) {
        ttl[i].varietiesAgreement = '0';
      }
      if (ttl[i].isNew == "是") {
        ttl[i].isNew = "1";
      } else if (ttl[i].isNew == "否") {
        ttl[i].isNew = "0";
      }
    }
  } else {
    return;
  }

  //查询是否标红
  $.ajax({
    url: '/proxy-purchase/purchase/groupPlanOrder/approve/data/create',
    method: 'POST',
    data: {
      purchaseOrderVoData: JSON.stringify(ttx),
      purchaseOrderProductVoData: JSON.stringify(ttl),
    },
    dataType: 'json',
    success: function (resp) {
      if (
        Object.prototype.hasOwnProperty.call(
          resp,
          'purchaseApproveDataVoList',
        ) &&
        Array.isArray(resp['purchaseApproveDataVoList'])
      ) {
        let productList = $('#X_Tablea').XGrid('getRowData');
        resp['purchaseApproveDataVoList'].forEach((item) => {
          let currentProduct = productList.filter(
            (product) => product.productCode === item.productCode,
          ); //找商品
          if (currentProduct.length) {
            //商品标红
            if (item.tagFlag && currentProduct[0].validityDate === 'false') {
              $('#X_Tablea  #' + currentProduct[0].rowid).css(
                'background',
                'rgba(254,95,85,1)',
              ); //命中,行标红
            } else if (
              !item.tagFlag &&
              currentProduct[0].validityDate === 'true'
            ) {
              $('#X_Tablea  #' + currentProduct[0].rowid).css(
                'background',
                'rgba(255,255,0,1)',
              ); //命中,行标黄
            } else if (
              item.tagFlag &&
              currentProduct[0].validityDate === 'true'
            ) {
              $('#X_Tablea  #' + currentProduct[0].rowid).css(
                'background',
                'rgba(255,128,0,1)',
              ); //命中,行标橙
            } else {
              $('#X_Tablea  #' + currentProduct[0].rowid).removeAttr('style'); //去掉标红
            }
            // //反向赋值field1，field2
            // $('#X_Tablea').XGrid('setRowData', currentProduct[0].rowid, {
            //   field1: item.field1,
            //   field2: item.field2,
            // });
          }
        });
      }
    },
    error: () => { },
  });
}
//编辑附件
var annex = [];
(function () {
  var fileStr = $('#attachmentsData').text() || '';
  var fileArr = fileStr.split(",")
  if (!fileStr == '') {
    // console.log(fileArr)
    if (fileArr.length > 0) {
      for (var i = 0; i < fileArr.length; i++) {
        var item = { "fileName": fileArr[i++], "filePath": fileArr[i] }
        annex.push(item)
      }
    }
    $("#editAttachmentspic").text('查看附件').css({ "background-color": "#ff8200", "border-color": "#ff8200" })
    $('#attachmentsData').remove();
  }
}())
var key = 'id';
function editAttachments() {
  var typeList = [];
  var eChoImgList = key == 'id' ? annex : [];
  var inpdata = null, inpdataArr = [], inpadataFinalArr = [];
  if ($('input[id^=newother]').length > 0) {
    inpdata = $('input[id^=newother]')
  } else {
    inpdata = $('input[id^=other]')
  }
  $(inpdata).each(function (index, item) {
    inpdataArr.push(JSON.parse(item.value));
  })
  for (var i = 0; i < inpdataArr.length; i++) {
    for (var j = 0; j < inpdataArr[i].length; j++) {
      inpadataFinalArr.push(inpdataArr[i][j])
    }
  }
  var text = $.trim($(this).text());
  var v = $(this).prev("input").val();
  typeList.push({
    text: text,
    value: v
  })
  if ($("input[id^='newother']").length > 0) {
    var newOther = $("input[id='newother" + v + "']");
    var imgArr = [], newImgArr = [];
    for (var i = 0; i < newOther.length; i++) {
      var str = $(newOther[i]).val();
      console.log(newOther[i]);
      str = JSON.parse(str)
      imgArr = imgArr.concat(str);
    }
    var type = $("input[id='newother" + v + "']").attr('data-type');
    for (var i = 0; i < imgArr.length; i++) {
      imgArr[i].type = type
    }
    eChoImgList = eChoImgList.concat(imgArr);
  } else {
    if ($("input[id='other" + v + "']").length > 0) {
      var newOther = $("input[id='other" + v + "']");
      var imgArr = [], newImgArr = [];
      for (var i = 0; i < newOther.length; i++) {
        var str = $(newOther[i]).val();
        str = JSON.parse(str)
        imgArr = imgArr.concat(str);
      }
      var type = $("input[id='other" + v + "']").attr('data-type');
      for (var i = 0; i < imgArr.length; i++) {
        imgArr[i].type = type
      }
      eChoImgList = eChoImgList.concat(imgArr);
    }
  }
  $(this).upLoad({
    typeList: typeList,//格式[{text:xxx,value:xxx}]
    eChoImgList: eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
    purchaseOrder: 1,//采购订单的附件的唯一标识
    fileParam: {
      name: 'fileName',
      url: 'filePath'
    },
    urlBack: function (data) {
      //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
      //存放数据
      //files
      // console.log(data)
      annex = data;
      key = 'noId'
      var html = '';
      $('input[id^=newother]').remove();
      var obj = {}
      for (var name in data) {
        var list = [];
        console.log(data);
        list.push(data[name])
        if (list.length > 0) {
          var arr = JSON.stringify(list);
          //RenMin：  这段 注释的意义详情见  supplierApplyFile.js 文件同位置。
          //html='<input type="hidden" data-type="'+name+'" id="newother'+name+'" value=\''+arr+'\' />';
          html = '<input type="hidden" data-type="' + data[name].type + '" id="newother' + data[name].type + '" value=\'' + arr + '\' />';
          $("body").append(html);
        }
      }
    }
  });

}