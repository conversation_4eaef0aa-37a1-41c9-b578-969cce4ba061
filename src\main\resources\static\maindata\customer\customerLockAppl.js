$('div[fold=head]').fold({
    sub: 'sub'
});

var businessType = $('#businessType').val();
businessType = businessType^1;
//显示流程图
var key = $("#key").val();
initApprovalFlowChart(key);
/* 表格加载 */
$('#table').XGrid({
    colNames: ['', '客户编码','客户名称', '客户类别','是否锁定','业务类型','<i style="color:#ff0000;margin-right:4px;">*</i>申请原因'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        hidden:true
    }, {
        name: 'customerCode',
        editable:''
    }, {
        name: 'customerName',
    }, {
        name: 'customerType',
    }, {
        name: 'isLock',
        formatter: function (e) {
            if (e == '0') {
                return '否';
            } else if (e == '1') {
                return '是';
            } else {
                return "";
            }
        },unformat: function (e) {
            if (e == '否') {
                return '0';
            } else if (e == '是') {
                return '1';
            } else {
                return "";
            }
        }
    }, {
        name: 'businessType',
        formatter: function (e) {
            if (e == '0') {
                return '解锁';
            } else if (e == '1') {
                return '锁定';
            } else {
                return "";
            }
        },unformat: function (e) {
            if (e == '解锁') {
                return '0';
            } else if (e == '锁定') {
                return '1';
            } else {
                return "";
            }
        }
    },{
        name: 'applReason',
        rowtype: '#appContentT'
    },{
        name: 'customerMnemonicCode',
        index: 'customerMnemonicCode',
        width:140,
        hidden:true
    },{
        name: 'orgCode',
        index: 'orgCode',
        width:140,
        hidden:true
    },{
        name: 'baseId',
        hidden: true
    }],
    rowNum: 999,
    rownumbers: true,//是否展示序号
    altRows: true
});
var selArr = [];
/* 新增行 事件*/
$("#addRow").on("click",function(){
    selArr = $("#table").getRowData();
    utils.dialog({
        url: '/proxy-customer/customer/customerLock/toSearchList?businessType='+businessType,//弹框页面请求地址
        title: '搜索客户',
        width: 1000,
        data:{
            initDataArr: selArr,
            isLock:businessType
        },
        height: 600,
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                $("#table").XGrid('clearGridData');
                for(var i = 0 ; i < data.length; i++ ){
                    if(!fidInArr(data[i].baseId)){
                        $('#table').XGrid("addRowData",data[i]);
                    }
                }
            }
        }
    }).showModal();
});
//数组中查找id
function fidInArr(id){
    let arr = $("#table").getRowData()
    for(var i=0;i<arr.length;i++){
        if(arr[i].id == id){
            return true;
        }
    }
    return false;
}
// function fidInArr(arr,supplierOrganId){
//     for(var i=0;i<arr.length;i++)
//     {
//         if(arr[i].supplierOrganId == supplierOrganId)
//         {
//             return true;
//         }
//     }
//     return false;
//
// }
/* 删除行 事件*/
$("#delRow").on("click",function(){
    var selObj = $('#table').XGrid("getSeleRow");
    if(selObj){
        $('#table').XGrid("delRowData",selObj.id);
    }else{
        utils.dialog({"content":"请选择删除行！","timeout":2000}).show();
    }
});


/**
 * 流程图显示
 */
function  initApprovalFlowChart(key) {
    //获取审核流程数据
    var key = $("#key").val();
    const url = "/proxy-customer/customer/customerLock/queryTotle?key="+key;
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}

//关闭按钮
$("#closeAssert").on("click", function () {
    var d = dialog({
        title: "提示",
        content: "是否保存草稿？",
        width:300,
        height:30,
        button:[
            {
                value:'关闭',
                callback:function(){
                	 utils.closeTab();
                }
            }
        ],
        okValue: '保存草稿',
        ok: function () {
            $("#auditStatus").val("1");// 0:录入中
            save(1);
            d.close().remove();
            return false;
        }
    }).showModal();
});

function save(auditStatus) {
    var sexamine = $("#examine").val();
    var customerLockApplVo = $("#baseApproval").serializeToJSON();
    var table = $("#table").getRowData();

    if(table == false){
        utils.dialog({content: '请添加锁定申请！', quickClose: true, timeout: 2000}).showModal();
        return;
    }
    for(var i = 0 ; i < table.length; i++  ){
        if(table[i].customerCode==""||table[i].customerCode==null){
            utils.dialog({content: '请选择锁定客户或删除空行！', quickClose: true, timeout: 2000}).showModal();
            return;
        }
    }
    if(auditStatus == 2){
        for(var i = 0 ; i < table.length; i++  ){
            if(table[i].applReason==""||table[i].applReason==null){
                utils.dialog({content: '请填写申请原因！', quickClose: true, timeout: 2000}).showModal();
                return;
            }
        }
    }
    for(var i = 0 ; i < table.length; i++ ){
        table[i].baseId = table[i].id;
        table[i].createTime = customerLockApplVo.applicationDate;
        table[i].correlationId = customerLockApplVo.id;
    }
    customerLockApplVo.businessType = businessType^1;
    customerLockApplVo.customerLockApplDetailVoList = table;
    //加载页面
    var url;
    if(auditStatus == 2){
        url = "/proxy-customer/customer/customerLock/addCusutomerLockAppl";
    } else {
        url = "/proxy-customer/customer/customerLock/addCusutomerLockApplTemp";
    }
    parent.showLoading({hideTime: 60000});
    $.ajax({
        url: url,
        data: JSON.stringify(customerLockApplVo),
        type: "post",
        dataType: 'json',
        contentType: "application/json",
        success: function (data) {
            parent.hideLoading();
            if(data.result.success){
                if (sexamine==""){
                    $("#examine").val(data.keyId)
                }
                var msg = "";
                if(auditStatus == 2){
                    msg = "提交成功";
                } else {
                    msg = "保存成功";
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else{
                var msg = "";
                if(data.result.failed0){
                    msg = data.result.failed0;
                }else if (data.result.failed1) {
                    msg = data.result.failed1;
                }else if (data.result.failed2) {
                    msg = data.result.failed2;
                }else if (data.result.failed3) {
                    msg = data.result.failed3;
                }else if (data.result.failed4) {
                    msg = data.result.failed4;
                }
                utils.dialog({
                    // title: "提示",
                    content: msg,
                    // width:300,
                    // height:30,
                    quickClose:true
                    /*okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                        utils.closeTab();
                    }*/
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        },
        error: function () {
            utils.dialog({content: '内部出错！', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
