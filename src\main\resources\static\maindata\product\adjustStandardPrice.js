
$("#text-count").text(100-$("#remark").val().length);
var pageType=$("#pageType").val();
//批量导入
$("#batchAdjustPrice").on("click", function () {
    dialog({
        url: '/proxy-product/product/adjustPrice/toFileupload?applyType=2',
        title: '批量导入',
        width: 420,
        height: 200,
        onclose: function (data) {
            if (this.returnValue) {
                var dataArray = this.returnValue;
                var rows = $('#X_Table').XGrid('getRowData');
                for (var x = 0; x < dataArray.length; x++) {
                    for (var i = 0; i < rows.length; i++) {
                        if (dataArray[x].productId == rows[i].productId) {
                            $('#X_Table').XGrid('delRowData', dataArray[x].productId);
                        }
                    }
                }
                for (var x = 0; x < dataArray.length; x++) {
                    $('#X_Table').XGrid('addRowData', {id: x + 1}, 'last');
                    $('#X_Table').XGrid('setRowData', x + 1, dataArray[x]);
                }
            }
        }
    }).showModal();
});
$('div[fold=head]').fold({
    sub: 'sub'
});
$('#X_Table').XGrid({
    url:"/proxy-product/product/adjustPrice/audi/details",
    colNames: ['', '', '商品编码', '商品名', '通用名', '型号/规格', '生产厂家', '包装单位', '剂型', '标准价格', '申请标准价格'],
    colModel: [{
        name: 'productId',
        index: 'productId',
        hidden: true
    }, {
        name: 'orgCode',
        index: 'orgCode',
        hidden: true
    }, {
        name: 'productCode',
        index: 'productCode'
    }, {
        name: 'productName',
        index: 'productName'
    }, {
        name: 'commonName',
        index: 'commonName'
    }, {
        name: 'specifications',
        index: 'specifications'
    }, {
        name: 'manufacturerName',
        index: 'manufacturerName'
    }, {
        name: 'packingUnitValue',
        index: 'packingUnitValue'
    }, {
        name: 'dosageFormValue',
        index: 'dosageFormValue'
    }, {
        name: 'standardPrice',
        index: 'standardPrice'
    }, {
        name: 'applyStandardPrice',
        index: 'applyStandardPrice',
        rowtype: '#applyStandardPrice'
    }],
    postData: {
        "recordId": $("#recordId").val()
    },
    rowNum: 0,
    rownumbers: true,
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {
        //console.log('双击行事件', id, dom, obj, index, event);
    },
    onSelectRow: function (id, dom, obj, index, event) {
        //console.log('单机行事件', id, dom, obj, index, event);
    }
});

//新增行
$("#addRow").on("click", function () {
    var resultArr = $('#X_Table').getRowData();
    if(resultArr.length > 0)
        {
            for (var i = 0; i < resultArr.length; i++) {
                resultArr[i].id=resultArr[i].productId;
            }
        }
    dialog({
        url: '/proxy-product/product/adjustPrice/searchProduct',
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            "orgCode": $("#recordOrgCode").val(),
            "disableState": 0,
            resultArr: resultArr
        }, // 给modal 要传递的 的数据
        onclose: function (data) {
            var data = this.returnValue;
            console.log(data)
            if (data) {
                var temp = [];
                var rows = data.resultArr;
                for (var i = 0; i < rows.length; i++) {
                    var orgCode = rows[i].orgCode;
                    temp.push(orgCode)
                }
                temp = unique(temp);
                if (temp.length > 1) {
                    utils.dialog({content: '请选择同一机构商品', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                for (var i = 0; i < rows.length; i++) {
                    var id = rows[i].productId;
                    if (!findInArr(id)) {
                        rows[i].productId = rows[i].id;
                        $('#X_Table').XGrid('addRowData', rows[i]);
                    }
                }
            }
        }
    }).showModal();
});
//列表内查询id
function findInArr(id) {
    var arr = $('#X_Table').getRowData();
    for (var i = 0; i < arr.length; i++) {
        var ID = arr[i].productId;
        if (ID == id) {
            return true;
        }
    }
    return false;
}

//删除行
$("#deleRow").on("click", function () {
    var selectRow = $('#X_Table').XGrid('getSeleRow');
    if (!selectRow) {
        utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
    } else {
        utils.dialog({
            title:"提示",
            width:300,
            height:30,
            okValue: '确定',
            content: "确定删除此条记录?",
            ok: function () {
                $('#X_Table').XGrid('delRowData', selectRow.id);
                utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    }
});
//关闭按钮
$("#closePage").on("click", function () {
    dialog({
        title: "提示",
        content: "是否保存草稿？",
        width: 300,
        height: 30,
        okValue: '保存',
        button: [
            {
                value: '关闭',
                callback: function () {
                    utils.closeTab();
                }
            }
        ],
        ok: function () {
            var rowData = $('#X_Table').getRowData();
            if (rowData.length == 0) {
                utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            $("#saveCaoGao").click()
            setTimeout(function () {
                utils.closeTab();
            }, 2000)
        }
    }).showModal();
});

$(".submitAudit").click(function () {
    var status = this.getAttribute("status");
    var formData = $("#applyForm").serializeToJSON();
    var rowData = $('#X_Table').getRowData();
    if (rowData.length == 0) {
        utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    //提交审核验证
    if (status == 1) {
        for (var i = 0; i < rowData.length; i++) {
            var selectRow = rowData[i];
            if (selectRow.applyStandardPrice == "" || selectRow.applyStandardPrice <= 0) {
                utils.dialog({content: '申请标准价格不能为空或0', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        }
    }

    var adjustPriceApprovalRecordVo;
    var  requestUrl;
    if (pageType == 0) {
        //拼接工作流key值,新加字段“备注”,状态,调价类型
        formData =$.extend(formData,{"workProcessKey":$("#workProcessKey").val()},{"remark":$("#remark").val()},{"statues":status},{"applyType":2});
        adjustPriceApprovalRecordVo={"adjustPriceApprovalRecordVo":formData};
        requestUrl = "/proxy-product/product/adjustPrice/saveAdjustPrice";
    }
    if (pageType == 1) {
        //拼接工作流key值,新加字段“备注”,状态,调价类型
        adjustPriceApprovalRecordVo={"adjustPriceApprovalRecordVo":{"id":$("#recordId").val(),"orgCode":$("#recordOrgCode").val(),"statues":status,"applicantId":$("#applicantId").val(),"applicationCode":$("#applicationCode").val(),"workProcessKey":$("#workProcessKey").val(),"remark":$("#remark").val()}};
        requestUrl = "/proxy-product/product/adjustPrice/adjustPriceApplyEditSave";
    }
    var adjustPriceApprovalRecordDetailVos={"adjustPriceApprovalRecordDetailVos":rowData};
    //拼装链接两个json对象
    var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
    parent.showLoading();
    $.ajax({
        type: "POST",
        url: requestUrl,
        data: places,
        async: false,
        dataType:'json',
        contentType: "application/json",
        error: function () {
            alert("提交失败！");
        },
        success: function (data) {
            if (data.code==0){
                var msg = "";
                if (status==0){
                    msg = '保存成功';
                }else if (status==1){
                    msg = '提交审核成功';
                }else {
                    msg = '操作成功';
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
            }
        },
        complete:function(){
            parent.hideLoading();
        }
    });
});


$('.audiPass').on('click', function () {
    $('#auditOpinion').val('');
    var status=this.getAttribute("status");
    if(status==1){
        $('#opinion').show();
    }else {
        $('#opinion').hide();
    }
    utils.dialog({
        title: '审核',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            if (status==1) {
                if ($("#auditOpinion").val()==""){
                    utils.dialog({content: '审批意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
            }
            submitAudiInfo(status,$("#auditOpinion").val());
        },
        cancelValue: '取消',
        cancel: function () {
            $("#auditOpinion").val("");
        }
    }).showModal();
});


function submitAudiInfo(status,auditOpinion){
    var rows =  $('#X_Table').XGrid('getRowData');
    var adjustPriceApprovalRecordVo={"adjustPriceApprovalRecordVo":{"id":$("#recordId").val()}};
    var adjustPriceApprovalRecordDetailVos={"adjustPriceApprovalRecordDetailVos":rows};
    //拼装链接两个json对象
    var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
    console.log(places);
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/adjustPrice/passAdjustPrice?statues="+status+"&auditOpinion="+auditOpinion+"&taskId="+$("#taskId").val(),
        data: places,
        async: false,
        dataType:'json',
        contentType: "application/json",
        error: function () {
            alert("提交失败！");
        },
        success: function (data) {
            console.log(data);
            if (data.code==0){
                if(data.result!=null&&data.result.flowStatus!=undefined&&!data.result.flowStatus){
                    utils.dialog({
                        title: "提示",
                        content: data.result.flowMsg,
                        width:300,
                        height:30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    return false;
                }
                var msg="";
                if(status==0){
                    msg = '恭喜审核通过';
                }
                if(status==1){
                    msg = '驳回成功';
                }
                if(status==2){
                    msg = '流程已关闭';
                }
                utils.dialog({
                    title: "提示",
                    content:msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
            }
        }
    });
}
$('.auditPass').on('click', function () {
    var status=this.getAttribute("status");
    utils.dialog({
        title:"关闭审核",
        width:300,
        height:30,
        okValue: '确定',
        content: "确定关闭此申请？",
        ok: function () {
            submitAudiInfo(status,"");
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
});
$('#submitAuditAgain').on('click', function () {
    var rowData = $('#X_Table').getRowData();
    if (rowData.length == 0) {
        utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    //提交审核验证
    for (var i = 0; i < rowData.length; i++) {
            var selectRow = rowData[i];
            if (selectRow.applyStandardPrice == "" || selectRow.applyStandardPrice <= 0) {
                utils.dialog({content: '申请标准价格不能为空或0', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        }
    utils.dialog({
        title:"提交审核",
        width:300,
        height:30,
        okValue: '确定',
        content: "确定提交申请？",
        ok: function () {
            var adjustPriceApprovalRecordVo={"adjustPriceApprovalRecordVo":{"id":$("#recordId").val(),"orgCode":$("#recordOrgCode").val(),"statues":1,"applicantId":$("#applicantId").val(),"applicationCode":$("#applicationCode").val(),"remark":$("#remark").val()}};
            var adjustPriceApprovalRecordDetailVos={"adjustPriceApprovalRecordDetailVos":rowData};
            //拼装链接两个json对象
            var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
            $.ajax({
                type: "POST",
                url: "/proxy-product/product/adjustPrice/submitAuditAgain?taskId="+$("#taskId").val(),
                data: places,
                async: false,
                dataType:'json',
                contentType: "application/json",
                error: function () {
                    alert("提交失败！");
                },
                success: function (data) {
                    if (data.code==0){
                        utils.dialog({
                            title: "提示",
                            content: '提交审核成功！',
                            width:300,
                            height:30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                        $(".ui-dialog-close").hide();
                        return false;
                    }else {
                        utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
                    }
                }
            });

        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
})
//获取审核流程数据
var workUrl="";
if (pageType == 0 || pageType == 1){
    workUrl="/proxy-product/product/purchaseLimit/queryTotle?key="+$("#workProcessKey").val();
}
if (pageType==2 || pageType==3 || pageType==4){
    workUrl = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+$("#processId").val();
}
$.ajax({
    type: "POST",
    url: workUrl,
    async: false,
    error: function () {
        utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
    },
    success: function (data) {
        if (data.code == 0) {
            $('.flow').process(data.result);
        } else {
            utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
        }
    }
});

/**
 * 设置输入两位小数 并且控制可输入的最大值的位数
 *
 * obj this对象
 * size 最大值位数 可不传
 * */
function setFixedTwo(obj, size) {
    var value = obj.value;
    var n = '';
    if (size) {
        for (var i = 0; i < size; i++) {
            n += '9';
        }
        n += '.99';
        if (Number(value) > Number(n)) {
            value = n;
        }
    }
    obj.value = toDecimal2(value);
}

//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x * 100) / 100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}

$('input[name="applyStandardPrice"]').blur(function () {
    var num = toDecimal2(this.value);
    this.value = num;
})

//数组去重
function unique(arr) {
    var res = [arr[0]];
    for (var i = 1; i < arr.length; i++) {
        var repeat = false;
        for (var j = 0; j < res.length; j++) {
            if (arr[i] === res[j]) {
                repeat = true;
                break;
            }
        }
        if (!repeat) {
            res.push(arr[i]);
        }
    }
    return res;
}
/*字数限制（备注）*/
$("#remark").on("input propertychange", function () {
    var $this = $(this),
        _val = $this.val(),
        count = "";
    if (_val.length > 100) {
        $this.val(_val.substring(0, 100));
    }
    count = 100 - $this.val().length;
    $("#text-count").text(count);
});

