var applicationOperation = "0,1";
var largeCategoryArray = new Array();
largeCategoryArray[0] = "中药饮片";
largeCategoryArray[1] = "食品";
largeCategoryArray[2] = "保健食品";
largeCategoryArray[3] = "中药材";
largeCategoryArray[4] = "药食同源";
$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //判断是否机构详情页
    var pageType = $("#pageType").val();
    if (pageType != null && pageType == "orga") {
        $(".orgahistorys").show();
        //$("#packingList").show();
        applicationOperation = "0,1,2,3,4,5,6,7,8,9,10,11";
        $("#div_firstShelfTime").show();
        $("#div_recentShelfTime").show();
    }
    var baseProductId = $("#baseProductId").val();
    var approvalFileType = $("#approvalFileType").val();
    var orgaProductId = $("#orgaProductId").val();//机构供应商id
    var orgCode = $("#orgCode").val();//机构供应商机构号

    //主仓回显
    setOptionHtml();
    //批准文件加载
    var scopeOfOperation = $("#scopeOfOperation").val();
    localBatchName(1);
    //业务类型属性
    if (pageType != null && pageType == "orga") {//机构详情页展示EC上下架状态
        $('#X_Table_Channel').XGrid({
            url: "/proxy-product/product/productChannelJiCai/toList?type=" + approvalFileType + "&correlationId=" + orgaProductId,
            colNames: ['id', '生效状态', '类别', '<i class="i-red">*</i>类别', '<i class="i-red">*</i>业务类型', '<i class="i-red">*</i>供货价', '<i class="i-red">*</i>APP售价', '智鹿总部采购价', '连锁APP售价','荷叶大药房采购价',
                '<i class="i-red">*</i>票面毛利率', '<i class="i-red">*</i>APP销售价是否维价', '出库价维价关联客户类型', '出库价维价关联客户类型ARR', '建议终端售价', '<i class="i-red">*</i>终端零售价是否维价',
                '<i class="i-red">*</i>电商商品状态', '实时综合毛利率区间', '毛利率等级', '<i class="i-red">*</i>生效状态', '是否集采'],
            colModel: [
                {
                    name: 'id',
                    index: 'id',//索引。其和后台交互的参数为sidx
                    key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                    hidden: true
                }, {
                    name: 'effectiveState',
                    index: 'effectiveState',
                    hidden: true
                }, {
                    name: 'channelType',
                    index: 'channelType',
                    hidden: true
                }, {
                    name: 'channelTypeVal',
                    index: 'channelTypeVal'
                }, {
                    name: 'channelId',
                    index: 'channelId',
                }, {
                    name: 'supplyPrice',
                    index: 'supplyPrice',
                    rowtype: '#supplyPrice',
                }, {
                    name: 'appPrice',
                    index: 'appPrice',
                    rowtype: '#appPrice',
                }, {
                    name: 'zhiluPrice',
                    index: 'zhiluPrice',
                    rowtype: '#zhiluPrice',
                }, {
                    name: 'chainGuidePrice',
                    index: 'chainGuidePrice',
                    rowtype: '#chainGuidePrice',
                }, {
                    name: 'heyePrice',
                    index: 'heyePrice',
                    rowtype: '#heyePrice',
                }, {
                    name: 'parGrossMargin',
                    index: 'parGrossMargin',
                    rowtype: '#parGrossMargin',
                }, {
                    name: 'dimensionSalesPriceYn',
                    index: 'dimensionSalesPriceYn',
                    rowtype: '#dimensionSalesPriceYn',
                }, {
                    name: 'pharmacyType',
                    index: 'pharmacyType',
                    rowtype: '#pharmacyType',
                }, {
                    name: 'pharmacyTypeArr',
                    index: 'pharmacyTypeArr',
                    hidden: true,
                }, {
                    name: 'terminalPrice',
                    index: 'terminalPrice',
                    rowtype: '#terminalPrice',
                }, {
                    name: 'dimensionTerminalPriceYn',
                    index: 'dimensionTerminalPriceYn',
                    rowtype: '#dimensionTerminalPriceYn',
                }, {
                    name: 'ecStatusVal',
                    index: 'ecStatusVal',
                }, {
                    name: 'grossMarginRange',
                    index: 'grossMarginRange',
                    width: 130,
                    rowtype: '#grossMarginRange',
                }, {
                    name: 'grossMarginGrade',
                    index: 'grossMarginGrade',
                    rowtype: '#grossMarginGrade',
                }, {
                    name: 'effectiveStateVal',
                    index: 'effectiveStateVal'
                }, {
                    name: 'centralizedPurchaseType',
                    index: 'centralizedPurchaseType',
                    formatter: function (value) {
                        if (value == 0) {
                            return "否"
                        } else {
                            return "是"
                        }
                    }
                }],
            rowNum: 10,
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true,
            gridComplete: function (res) {
                var rowData = $("#X_Table_Channel").XGrid('getRowData');
                if (rowData && rowData.length) {
                    rowData.forEach(function (item) {
                        let arr = item['pharmacyTypeArr']
                        let _node = $('#X_Table_Channel #' + item.id + ' [name=pharmacyType]')
                        for (let i = 0; i < _node.length; i++) {
                            if (arr.indexOf($(_node[i]).val()) > -1) {
                                $(_node[i]).prop('checked', true)
                            }
                        }
                    })
                }
            }
        });
    } else {
        $('#X_Table_Channel').XGrid({
            url: "/proxy-product/product/productChannelJiCai/toList?type=" + approvalFileType + "&correlationId=" + orgaProductId,
            colNames: ['id', '生效状态', '类别', '<i class="i-red">*</i>类别', '<i class="i-red">*</i>业务类型', '<i class="i-red">*</i>供货价', '<i class="i-red">*</i>APP售价', '智鹿总部采购价', '连锁APP售价','荷叶大药房采购价',
                '<i class="i-red">*</i>票面毛利率', '<i class="i-red">*</i>APP销售价是否维价', '出库价维价关联客户类型', '出库价维价关联客户类型ARR', '建议终端售价', '<i class="i-red">*</i>终端零售价是否维价', '实时综合毛利率区间', '毛利率等级', '<i class="i-red">*</i>生效状态'],
            colModel: [
                {
                    name: 'id',
                    index: 'id',//索引。其和后台交互的参数为sidx
                    key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                    hidden: true
                }, {
                    name: 'effectiveState',
                    index: 'effectiveState',
                    hidden: true
                }, {
                    name: 'channelType',
                    index: 'channelType',
                    hidden: true
                }, {
                    name: 'channelTypeVal',
                    index: 'channelTypeVal'
                }, {
                    name: 'channelId',
                    index: 'channelId',
                }, {
                    name: 'supplyPrice',
                    index: 'supplyPrice',
                    rowtype: '#supplyPrice',
                }, {
                    name: 'appPrice',
                    index: 'appPrice',
                    rowtype: '#appPrice',
                }, {
                    name: 'zhiluPrice',
                    index: 'zhiluPrice',
                    rowtype: '#zhiluPrice',
                }, {
                    name: 'chainGuidePrice',
                    index: 'chainGuidePrice',
                    rowtype: '#chainGuidePrice',
                }, {
                    name: 'heyePrice',
                    index: 'heyePrice',
                    rowtype: '#heyePrice',
                }, {
                    name: 'parGrossMargin',
                    index: 'parGrossMargin',
                    rowtype: '#parGrossMargin',
                }, {
                    name: 'dimensionSalesPriceYn',
                    index: 'dimensionSalesPriceYn',
                    rowtype: '#dimensionSalesPriceYn',
                }, {
                    name: 'pharmacyType',
                    index: 'pharmacyType',
                    rowtype: '#pharmacyType',
                }, {
                    name: 'pharmacyTypeArr',
                    index: 'pharmacyTypeArr',
                    hidden: true,
                }, {
                    name: 'terminalPrice',
                    index: 'terminalPrice',
                    rowtype: '#terminalPrice',
                }, {
                    name: 'dimensionTerminalPriceYn',
                    index: 'dimensionTerminalPriceYn',
                    rowtype: '#dimensionTerminalPriceYn',
                }, {
                    name: 'grossMarginRange',
                    index: 'grossMarginRange',
                    rowtype: '#grossMarginRange',
                }, {
                    name: 'grossMarginGrade',
                    index: 'grossMarginGrade',
                    rowtype: '#grossMarginGrade',
                }, {
                    name: 'effectiveStateVal',
                    index: 'effectiveStateVal'
                }],
            rowNum: 10,
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true,
            gridComplete: function (res) {
                var rowData = $("#X_Table_Channel").XGrid('getRowData');
                if (rowData && rowData.length) {
                    rowData.forEach(function (item) {
                        let arr = item['pharmacyTypeArr']
                        let _node = $('#X_Table_Channel #' + item.id + ' [name=pharmacyType]')
                        for (let i = 0; i < _node.length; i++) {
                            if (arr.indexOf($(_node[i]).val()) > -1) {
                                $(_node[i]).prop('checked', true)
                            }
                        }
                    })
                }
            }
        });
    }
    //商品说明书
    loadProductInstruction(baseProductId, approvalFileType);
    var pageFlag = $("#pageFlag").val();
    if ("basePase" == pageFlag) {
        var url = "/proxy-product/product/productApprovalFileJiCai/toPageList?type=" + approvalFileType + "&correlationId=" + baseProductId + "&baseDataFlag=true";
        $('#X_Table').XGrid({
            url: url,
            colNames: ['', '机构', '<i class="i-red">*</i>批件名称', '<i class="i-red">*</i>批件编号', '<i class="i-red">*</i>签发日期', '<i class="i-red">*</i>有效期至', '附件', '附件数据'],
            colModel: [
                {
                    name: 'id',
                    index: 'id',//索引。其和后台交互的参数为sidx
                    key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                    hidden: true
                },
                {
                    name: 'orgName',
                    index: 'orgName',

                },
                {
                    name: 'batchName',
                    index: 'batchName',
                    rowtype: '#batchName',
                }, {
                    name: 'batchCode',
                    index: 'batchCode'
                }, {
                    name: 'issueDateStr',
                    index: 'issueDateStr'

                }, {
                    name: 'validityDateStr',
                    index: 'validityDateStr'
                }, {
                    name: 'enclosure',
                    index: 'enclosure',
                    formatter: function (value) {
                        var str = '无';
                        if (value) {
                            str = '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
                        }
                        return str;
                    },
                    unformat: function (e) {
                        e = e.replace(/<[^>]+>/g, '');
                        if (e == "无") {
                            e = 0;
                        }
                        return e;
                    }
                }, {
                    name: 'enclosureList',
                    index: 'enclosureList',
                    hidden: true,
                    formatter: function (value) {
                        if (value) {
                            return JSON.stringify(value);
                        }
                        return JSON.stringify([]);
                    }
                }],
            rowNum: 10,
            altRows: true,//设置为交替行表格,默认为false
            pager: '#X_Table_PAGE',
            rownumbers: true,
            gridComplete: function () {

            }
        });
    } else {
        var url = "/proxy-product/product/productApprovalFileJiCai/toList?type=" + approvalFileType + "&correlationId=" + orgaProductId + "&baseDataFlag=false";
        $('#X_Table').XGrid({
            url: url,
            colNames: ['', '<i class="i-red">*</i>批件名称', '<i class="i-red">*</i>批件编号', '<i class="i-red">*</i>签发日期', '<i class="i-red">*</i>有效期至', '附件', '附件数据'],
            colModel: [
                {
                    name: 'id',
                    index: 'id',//索引。其和后台交互的参数为sidx
                    key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                    hidden: true
                }, {
                    name: 'batchName',
                    index: 'batchName',
                    rowtype: '#batchName',
                }, {
                    name: 'batchCode',
                    index: 'batchCode'
                }, {
                    name: 'issueDateStr',
                    index: 'issueDateStr'

                }, {
                    name: 'validityDateStr',
                    index: 'validityDateStr'
                }, {
                    name: 'enclosure',
                    index: 'enclosure',
                    formatter: function (value) {
                        var str = '无';
                        if (value) {
                            str = '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
                        }
                        return str;
                    },
                    unformat: function (e) {
                        e = e.replace(/<[^>]+>/g, '');
                        if (e == "无") {
                            e = 0;
                        }
                        return e;
                    }
                }, {
                    name: 'enclosureList',
                    index: 'enclosureList',
                    hidden: true,
                    formatter: function (value) {
                        if (value) {
                            return JSON.stringify(value);
                        }
                        return JSON.stringify([]);
                    }
                }],
            rowNum: 10,
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true
        });
    }

    //修改记录
    if ($('#modifyTable').length > 0) {
        var commodity_code = $("#commodity_code").val();
        var productOrgCode = $("#productOrgCode").val();
        $('#modifyTable').XGrid({
            url: "/proxy-product/product/approvalRecord/toList?productCode=" + commodity_code + "&applicationOperation=" + applicationOperation + "&orgCode=" + productOrgCode,
            colNames: ['申请时间', '申请操作', '申请人', '单据编号', '审核状态', '申请明细', '审批流程'],
            colModel: [{
                name: 'applicationTimeStr', //与反回的json数据中key值对应
                index: 'applicationTimeStr', //索引。其和后台交互的参数为sidx
                key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            }, {
                name: 'applicationOperationValue',
                index: 'applicationOperationValue'
            }, {
                name: 'applicantValue',
                index: 'applicantValue'
            }, {
                name: 'applicationCode',
                index: 'applicationCode'
            }, {
                name: 'auditStatus',
                index: 'auditStatus',
                formatter: function (value, grid, rows, state) {
                    if (value == 0) {
                        return "录入中";
                    } else if (value == 1) {
                        return "审核中";
                    } else if (value == 2) {
                        return "审核通过";
                    } else if (value == 3) {
                        return "审核不通过";
                    }
                }
            }, {
                name: 'applicationDetails',
                index: 'applicationDetails',
                formatter: function (value, b, rowData) {
                    if (rowData.applicationOperation != 0) {
                        var arr = value.split('；');
                        var html = '<div class="modifyTheRecord">\n' +
                            '<div class="itemContent"><div class="mrLeft">';
                        for (var i = 0; i < arr.length; i++) {
                            html += '<p>' + arr[i] + '</p>';
                        }
                        html += '</div></div>' +
                            '\t\t<div class="mrRight">\n' +
                            '\t\t\t<a href="javascript:;" class="moreBtn">展开</a>\n' +
                            '\t\t</div>\n' +
                            '\t</div>\n';
                        return html;
                    } else {
                        value = value.replace(/&lt;/g, '<').replace(/&gt;/g, '>');
                        return value;
                    }
                }
            }, {
                name: 'taskId',
                index: 'taskId',
                formatter: function (value, grid, rows, state) {
                    if (value != '') {
                        return "<a href = 'javascript:void(0)' onclick='toFlowChart(" + value + ")'>查看</a>";
                    } else {
                        return "";
                    }
                }
            }],
            rowNum: 10,
            altRows: true, //设置为交替行表格,默认为false
            pager: '#modifyTable_page',
            rownumbers: true,
            gridComplete: function () {

            }
        });
    }
    /*首营申请修改记录*/
    $('#firstEditRecordTable').XGrid({
        url: "/proxy-product/product/productFirstJiCai/getFirstEditRecords?applicationCode=" + $("#applicationCode").val(),
        colNames: ['变更时间', '变更人', '单据编号', '变更明细'],
        colModel: [{
            name: 'createTimeStr',
            index: 'createTimeStr'
        }, {
            name: 'createUserValue',
            index: 'createUserValue'
        }, {
            name: 'applicationCode',
            index: 'applicationCode'
        }, {
            name: 'changeDetails',
            index: 'changeDetails',
            formatter: function (value, b, rowData) {
                var arr = value.split(';');
                var html = '<div class="modifyTheRecord">\n' +
                    '<div class="itemContent"><div class="mrLeft">';
                for (var i = 0; i < arr.length; i++) {
                    html += '<p>' + arr[i] + '</p>';
                }
                html += '</div></div>' +
                    '\t\t<div class="mrRight">\n' +
                    '\t\t\t<a href="javascript:;" class="moreBtn">展开</a>\n' +
                    '\t\t</div>\n' +
                    '\t</div>\n';
                return html;
            }
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#firstEditRecord_page',
        rownumbers: true,
        gridComplete: function () {

        }
    });
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    //修改记录申请明细展开收起按钮
    $("#modifyTable,#firstEditRecordTable").on("click", ".moreBtn", function () {
        var type = $.trim($(this).text());
        var tr = $(this).parents("tr");
        var innerHeiht = 0;
        if (type == '展开') {
            innerHeiht = tr.find(".mrLeft").innerHeight();
            $(this).html('收起');
        } else if (type == '收起') {
            innerHeiht = 40;
            $(this).html('展开');
        }
        if (innerHeiht < 40) {
            innerHeiht = 40;
        }
        tr.find(".modifyTheRecord .itemContent").animate({
            height: innerHeiht
        }, 500)
    })




    //受托厂家是否显示
    initChecked();
    //多选框选中
    loadData("keyConservationCategories");
    loadData("specialAttributes");
    loadData("storageAttribute");
    // loadData("operatingCustomers");
    // loadData("specialProvision");
    function loadData(key) {
        var tagName, type, arr, thisVal;
        var value = $("#" + key).val();
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (type == 'checkbox') {
                if ($.type(value) == 'array') {
                    arr = value;
                } else if ($.type(value) == 'string') {
                    arr = value.split(',');
                }
                for (var i = 0; i < arr.length; i++) {
                    if (thisVal == arr[i]) {
                        $(this).attr('checked', true);
                        break;
                    }
                }
            }
        });
    }
    // 温度范围特殊处理
    var temperatureRange = $('#temperatureRanges').val();
    if (temperatureRange != null) {
        var tempArr = temperatureRange.split(',');
        for (var i = 0; i < tempArr.length; i++) {
            if (i == 0) {
                $('#temperatureRange1').val(tempArr[0]);
            }
            if (i == 1) {
                $('#temperatureRange2').val(tempArr[1]);
            }
        }
    }

    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    $("input").attr("readonly", true);
    $("textarea").attr("readonly", true);
    $("input[type='checkbox']").attr("disabled", true);
    $("input[type='radio']").attr("disabled", true);
    $("select").attr("disabled", "disabled");
    $("#rowOperationsDiv").hide();
    $(".bootstrap-tagsinput span[data-role='remove']").hide();
    $("#auditOpinion").removeAttr("readonly");
    $(".glyphicon-search").hide();
    //信息回显
    showDictValue();
    //流程图显示
    var processInstaId = $("#processId").val();
    if (processInstaId != undefined && processInstaId != "") {//已提交审核加载流程图
        initApprovalFlowChart(processInstaId);
    }
    $('.audiPass').on('click', function () {
        $('#auditOpinion').val('');
        var status = this.getAttribute("status");
        var title = "审核通过";
        if (status == 4) {
            title = "审核不通过";
            $('#opinion').show();
        } else {
            $('#opinion').hide();
        }
        var d = utils.dialog({
            title: title,
            content: $('#container'),
            okValue: '确定',
            ok: function () {
                //审核不通过，意见不能为空
                if (status != 2) {
                    if ($("#auditOpinion").val() == "") {
                        utils.dialog({ content: '审批意见不能为空!', quickClose: true, timeout: 2000 }).showModal();
                        return false;
                    }
                }
                d.close();
                submitAuditInfo(status, $("#auditOpinion").val());
            },
            cancelValue: '取消',
            cancel: function () { }
        });
        d.showModal();
    });

    setTimeout(function () {
        let scopeOfOperationVal = $('#scopeOfOperationVal').attr('data-value');
        if (scopeOfOperationVal == '中药饮片') {
            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地')
        }
    }, 200)
})
//设置主仓
function setOptionHtml() {
    let html = ''
    const selectObjAry = []
    $('#selectOrgsGroup label input[type=checkbox]:checked').each(function (index, item) {
        const nameStr = $(item).parent().attr('data-name')
        selectObjAry.push({ name: nameStr, value: item.value })

    })
    selectObjAry.forEach(function (item, index) {
        html += '<option value=' + item.value + '>' + item.name + '</option>'

    })
    $("#masterCode").html(html);
    $("#masterCode").val($("#masterCodeValue").attr('value'));


}
/**
 * 字典值回显
 */
function showDictValue() {
    //申请人,申请人机构回显
    //loadUserName("applicant","applicantOrgCode");
    //商品大类
    showComValue("largeCategory", "1017");
    //生产厂家--添加地址信息
    //showManufacturerInfo("manufacturer",$("#manufacturer").val());
    showComValue("manufacturer", "1003");
    //包装单位
    showComValue("packingUnit", "1002");
    //剂型
    showComValue("dosageForm", "1001");
    // 委托厂家--添加地址信息
    //showManufacturerInfo("entrustmentManufacturer",$("#entrustmentManufacturer").val());
    //showComValue("entrustmentManufacturer","1003");
    //存储条件
    showComValue("storageConditions", "1019");
    //处方分类
    showComValue("prescriptionClassification", "1016");
    //所属经营范围
    //所属经营范围
    var simpleCode = $("#scopeOfOperation").val();
    var orgCode = $("#applicantOrgCode").val();
    if (simpleCode != "") {
        $.ajax({
            url: '/proxy-sysmanage/sysmanage/dict/queryscopebycode?orgCode=' + orgCode + "&simpleCode=" + simpleCode,
            type: 'get',
            dataType: 'json',
            async: false,
            success: function (data) {
                console.log(data);
                if (data.code == 0) {
                    var simpleCodeName = "";
                    for (var i = 0; i < data.result.length; i++) {
                        if (i != data.result.length - 1) {
                            simpleCodeName = simpleCodeName + data.result[i].name + ",";
                        } else {
                            simpleCodeName = simpleCodeName + data.result[i].name;
                        }
                    }
                    $("#scopeOfOperationVal").val(simpleCodeName);
                    $("#scopeOfOperationVal").attr("data-value", simpleCodeName);
                    $("#scopeOfOperationVal").attr('title', simpleCodeName);
                }
            }
        })
    }
    /*// 一级分类 firstCategory
    showComValue("firstCategory","1025");
    //二级分类 secondCategory
    showComValue("secondCategory","1026");
    //三级分类 thirdCategory
    showComValue("thirdCategory","1027");*/
    //商品定位
    showCommodityPosition($("#commodityPosition").val())
    // 商品定位二级
    showSecondPosition($("#commodityPosition").val(), $('#secondCommodityPosition').val());
    //首营供应商 firstBattalionSupplier
    var supplierId = $("#supplierCode").val();
    if (supplierId != undefined && supplierId != "") {
        $.ajax({
            url: '/proxy-supplier/supplier/supplierOrganBase/supplierBase/ajaxSupplierBaseList?auditStatus=2&disableStateBase=0&queryFields=' + supplierId,
            type: 'get',
            dataType: 'json',
            success: function (data) {
                console.log(data);
                if (data != null && data != undefined && data.result != null && data.result.list.length > 0) {
                    // $("#firstBattalionSupplierVal").val(data.supplierName);
                    $("#supplierCodeVal").val(data.result.list[0].supplierName);
                }
            }
        })
    }
    //品牌
    /*    var brand=$("#brand").val();
        if(brand!="" && undefined != brand){
            $.ajax({
                url:'/proxy-sysmanage/sysmanage/brand/queryById?id='+brand,
                type:'get',
                dataType:'json',
                success:function(data){
                    console.log(data);
                    if(data!=null&&data!=undefined){
                        $("#brandVal").val(data.result.brandName);
                        $("#brandVal").attr("data-value",data.result.brandName);
                    }
                }
            })
        }*/
}

/*function showManufacturerInfo(obj,manuId) {
    if(manuId!=undefined&&manuId!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querymanufactorybyId?manuId='+manuId,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if (data.code==0){
                    $("#"+obj+"Val").val(data.result.manufactoryName);
                    $("#"+obj+"Address").val(data.result.address);
                }
            }
        })
    }
}*/
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function showComValue(obj, type) {
    var id = $("#" + obj).val();
    if (id != undefined && id != "") {
        $.ajax({
            url: '/proxy-sysmanage/sysmanage/dict/querybeanbytype?type=' + type + "&id=" + id,
            type: 'get',
            dataType: 'json',
            async: false,
            success: function (data) {
                //console.log(data);
                if (data.code == 0) {
                    $("#" + obj + "Val").val(data.result);
                    $("#" + obj + "Val").attr("data-value", data.result);
                    $("#" + obj + "Val").attr("title", data.result);
                    if (obj == "largeCategory") {
                        if (data.result == "中药饮片") {
                            $(".productrate").show();
                            //Is_ZYYP_flag = true
                            $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                            $('#maintenancePeriod').prev().find('i').remove(); // 养护周期
                            $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                            $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                            $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                            $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                        } else {
                            $(".productrate").hide();
                        }
                        if (largeCategoryArray.indexOf(data.result) > -1) {
                            $(".productrate").show();
                        } else {
                            $(".productrate").hide();
                        }
                    }
                }
            }
        })
    }
}


// 展示商品定位一级
function showCommodityPosition(currentPositionId) {
    if (currentPositionId) {
        $.ajax({
            type: "get",
            url: "/proxy-product/product/dict/common/queryCommon?type=3&commonName=",
            async: false,
            dataType: "json",
            success: function (data) {
                if (data.result && data.result.length) {
                    const selectedCommodityPosition = data.result.find(item => item.id == currentPositionId)
                    if (selectedCommodityPosition) {
                        $('#commodityPosition').val(selectedCommodityPosition.id)
                        $('#commodityPositionVal').val(selectedCommodityPosition.name)
                        refreshRequiredFields(selectedCommodityPosition)
                    }
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
}
// 展示商品定位二级
function showSecondPosition(parentId, secondId) {
    if (parentId) {
        $.ajax({
            type: "get",
            url: "/proxy-product/product/dict/common/queryByParentId",
            async: false,
            data: { "parentId": parentId },
            dataType: "json",
            success: function (data) {
                let options = ['<option value="">请选择</option>']
                if (data.result) {
                    options = options.concat(data.result.map(item => {
                        return "<option value=" + item.id + ">" + item.name + "</option>"
                    }))
                }
                $('#secondCommodityPositionVal').html(options.join(""))
                $('#secondCommodityPositionVal').val(secondId)
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
}
// 商品二级定位变更时，将其同步到隐藏字段
function onSecondCommodityPositionChanged() {
    $('#secondCommodityPosition').val($('#secondCommodityPositionVal').val())
}

function refreshRequiredFields(selectedItem) {
    if (selectedItem.requiredFlag) {
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).html("<i class='text-require'>*  </i>商品定位二级")
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).addClass('require')
        $('#secondCommodityPositionVal').addClass('{validate:{ required :true}}')
    } else {
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).find('i').remove()
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).removeClass('require')
        $('#secondCommodityPositionVal').removeClass('{validate:{ required :true}}')
    }
    if (selectedItem.modeRequiredFlag) {
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).html("<i class='text-require'>*  </i>集采签约方式")
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).addClass('require')
        $('#purchaseContractMode').addClass('{validate:{ required :true}}')
    } else {
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).find('i').remove()
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).removeClass('require')
        $('#purchaseContractMode').removeClass('{validate:{ required :true}}')
    }
}


function loadUserName(key, orgaKey) {
    var userId = $("#" + key).val();
    if (userId == "") {
        return;
    }
    $.ajax({
        type: "get",
        url: "/proxy-sysmanage/sysmanage/system/queryUserInfoById",
        data: { "userId": userId },
        dataType: "json",
        success: function (data) {
            //console.log(data.result);
            if (data.code == 0 && data.result != null) {
                var userName = data.result.userName;
                if (orgaKey != null) {//申请人机构显示
                    $("#" + orgaKey + "Val").val(data.result.orgName);
                }
                $("#" + key + "Val").val(userName);
                $("#" + key + "Val").attr("data-value", userName);
            }
        },
        error: function () {
        }
    });
}
/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj) {
    var parentId = $(obj).parents("tr").attr("id");
    var data = $('#X_Table').getRowData(parentId);
    if (data.enclosureList) {
        $.viewImg({
            fileParam: {
                name: 'enclosureName',
                url: 'enclosureUrl'
            },
            list: JSON.parse(data.enclosureList)
        })
    }
}
/**
 * 审核按钮操作
 */
function submitAuditInfo(auditStatus, auditOpinion) {
    parent.showLoading();
    var productData = {};
    var applicationCode = $("#applicationCode").val();
    var taskId = $("#taskId").val();
    productData.applicationCode = applicationCode;
    productData.taskId = taskId;
    productData.auditStatus = auditStatus;
    productData.auditOpinion = auditOpinion;
    productData.updataStutus = "0";
    //商品编码与机构编码
    productData.orgCode = $("#applicantOrgCode").val();
    productData.productCode = $("#commodity_code").val();
    console.log(productData);
    var data = JSON.stringify(productData);
    console.log(data);
    $.ajax({
        type: "post",
        url: "/proxy-product/product/productFirstJiCai/auditProduct",
        async: false,
        data: data,
        dataType: "json",
        contentType: "application/json",
        success: function (data) {
            var msg = data.result.msg;
            var approvalFileStatus = data.result.approvalFileStatus;
            if (approvalFileStatus != undefined && !approvalFileStatus) {
                utils.dialog({
                    title: "提示",
                    content: data.result.msg,
                    width: 300,
                    height: 30
                }).showModal();
                return false;
            }
            if (msg == "sucess") {
                if (auditStatus == 2) {
                    msg = "恭喜审核通过！";
                } else if (auditStatus == 4) {
                    msg = "驳回成功！";
                } else if (auditStatus == 3) {
                    msg = "流程已关闭！";
                }
            }
            utils.dialog({
                title: "提示",
                content: msg,
                width: 300,
                height: 30,
                okValue: '确定',
                ok: function () {
                    utils.closeTab();
                }
            }).showModal();
            $(".ui-dialog-close").hide();
            return false;
        },
        error: function () {
            utils.dialog({ content: '审核失败', quickClose: true, timeout: 2000 }).showModal();
        },
        complete: function () {
            parent.hideLoading();
        }
    });
}

function initAdjustPriceHistorys() {
    var productId = $("#orgaProductId").val();
    dialog({
        url: '/proxy-product/product/adjustPrice/adjustPriceHistory',
        title: '商品APP售价调价历史记录列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: { "productId": productId },
        onclose: function () {
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
}
function initBuyHistorys() {
    var productId = $("#orgaProductId").val();
    dialog({
        url: '/proxy-product/product/orgApproval/attributeChangeHistory',
        title: '商品采购员历史记录列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: { "productId": productId },
        onclose: function () {
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
}
//判断委托产家是否显示
function initChecked() {
    var type = $("input[name='entrustmentProduction']:checked").val();
    if (type == 1) {
        $(".entManufacturerDiv").show();
        $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>受托厂家')
    } else {
        $(".entManufacturerDiv").hide();
        $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('受托厂家')
    }
};
/**
 * 加载所属经营范围对应的批准文件
 * @param simpleCode
 */
function localBatchName(type) {
    $.ajax({
        type: "post",
        url: "/proxy-sysmanage/sysmanage/dict/getBatchnamesByType",
        async: false,
        data: { "type": type },
        dataType: "json",
        success: function (data) {
            var html = '<option value="0">商品附件</option>';
            if (data.code == 0) {
                var arr = data.result;
                if (arr != null) {
                    for (var i = 0; i < arr.length; i++) {
                        html += '<option value="' + arr[i].batchId + '">' + arr[i].batchName + '</option>';
                    }
                }
            }
            $("select[name='batchName']").html(html);
        },
        error: function () {
            utils.dialog({ content: '加载失败', quickClose: true, timeout: 2000 }).showModal();
        }
    });
}
/**
 * 流程图显示
 */
function initApprovalFlowChart(processInstaId) {
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/queryTotle?processInstaId=" + processInstaId,
        async: false,
        success: function (data) {
            if (data.code == 0 && data.result != null) {
                console.log(data.result)
                $('.flow').process(data.result);
            }
        },
        error: function () {
        }
    });
}

/**
 * 修改记录查看流程图
 * @returns
 */
function toFlowChart(processInstaId) {
    dialog({
        url: '/proxy-product/product/approvalRecord/toFlowChart',
        title: '审批流程',
        width: 1200,
        height: 520,
        data: { "processInstaId": processInstaId },
        onclose: function () {
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
}
function loadProductInstruction(baseProductId, type) {
    $.ajax({
        type: "post",
        url: "/proxy-product/product/productInstruction/toList",
        async: false,
        data: { "correlationId": baseProductId, "type": type },
        dataType: "json",
        success: function (data) {
            //console.log(data)
            if (data.code == 0) {
                var result = data.result;
                var html = getProductHTML(result);
                $("#insForm").html(html);
            } else {
                $("#insForm").html('');
            }
        },
        error: function () {
            utils.dialog({ content: '加载失败', quickClose: true, timeout: 2000 }).showModal();
        }
    });
}
/* 商品主数据带回说明书
 * @param arr
 * @returns {string}
 */
function getProductHTML(arr) {
    arr = arr == null ? [] : arr;
    var left = [], right = [];
    var html = '';
    for (var i = 0; i < arr.length; i++) {
        var type = arr[i].controlTypes;
        if (type == 1) {
            //复选框
            left.push(arr[i]);
        } else {
            //文本框/
            right.push(arr[i]);
        }
    }
    var list = left.concat(right);
    for (var i = 0; i < list.length; i++) {
        var cType = list[i].controlTypes;
        var attr = list[i].checked;
        var attributeRequired = list[i].attributeRequired;
        if (attributeRequired == null || attributeRequired == "null") {
            attributeRequired = "";
        }
        if (cType == 0) {
            //文本框
            html += '<div class="row attributeList">\n' +
                '        <div class="input-group">\n' +
                '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="' + attributeRequired + '">' +
                '           <input type="hidden" class="checkval" name="checked" value="' + attr + '">' +
                '           <input type="hidden" class="controlTypes" name="controlTypes" value="0">' +
                '           <input type="hidden" class="instructionsId" name="id" value="' + list[i].id + '">' +
                '           <label class="input-group-addon require">';
            if (Number(attributeRequired) && Number(attributeRequired) == 1) {
                //必填
                html += '<i class="text-require" style="color:red;display: contents;">*  </i>';
            }
            html += list[i].attribute + ':<input type="hidden" class="attributeName" name="attribute" value="' + list[i].attribute + '" /></label>' +
                '           <textarea class="form-control attributeValue only-read';
            if (Number(attributeRequired) && Number(attributeRequired) == 1) {
                //必填
                html += ' {validate:{ required :true}}';
            }
            html += '" name="attributeValue" >' + list[i].attributeValue + '</textarea>\n' +
                '       </div>\n' +
                '</div>';

        } else if (cType == 1) {
            //复选框
            html += '<div class="row attributeList">\n' +
                '        <div class="input-group">\n' +
                '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="' + attributeRequired + '">' +
                '           <input type="hidden" class="checkval" name="checked" value="' + attr + '">' +
                '           <input type="hidden" class="controlTypes" name="controlTypes" value="1">' +
                '           <input type="hidden" class="instructionsId" name="id" value="' + list[i].id + '">' +
                '           <label class="input-group-addon require">';
            if (Number(attributeRequired) && Number(attributeRequired) == 1) {
                //必填
                html += '<i class="text-require" style="color:red;display: contents;">*  </i>';
            }
            html += list[i].attribute + ':<input type="hidden" class="attributeName" name="attribute" value="' + list[i].attribute + '" /></label>' +
                '               <div class="form-control" style="height:auto;min-height:34px;">\n' +
                '                       <div class="checkbox" style="margin:0;">';
            var attrVal = list[i].checked;
            if (attrVal && attrVal != '') {
                var item = attrVal.split(',');//把复选框属性转换为数组
                var attributeValue = list[i].attributeValue;
                var checkList = attributeValue.split(',');
                for (var j = 0; j < item.length; j++) {

                    html += '<label style="margin-right: 14px;"><input type="checkbox" ';
                    if (checkList && checkList.length > 0) {
                        for (var x = 0; x < checkList.length; x++) {
                            if (checkList[x] == item[j]) {
                                html += 'checked="checked"';
                                break;
                            }
                        }
                    }
                    html += ' name="attributeValue" value="' + item[j] + '" class="only-read">' +
                        '                  ' + item[j] + '</label>';

                }
            }
            html += '              </div>\n' +
                '               </div>' +
                '           </div>\n' +
                '       </div>';
        }
    }
    return html;
}
