$('div[fold=head]').fold({
    sub: 'sub'
});

$(function () {
    //显示流程图
    initApprovalFlowChart();
})


// var id =  Number($('#uuid').val())
var id =  $("#appId").val();

$('#table1').XGrid({
    url: "/proxy-customer/customer/disuse/toDetailed?id="+id,
    colNames: ['', '客户编码', '客户名称', '客户类别', '目前状态','业务类型', '<i style="color:red;margin-right:4px;">*</i>申请原因'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        hidden:true
    }, {
        name: 'customerCode',
        editable:''
    }, {
        name: 'customerName',
    }, {
        name: 'customerType',
    }, {
        name: 'serviceType',
        formatter: function (e) {
            if (e == '0') {
                return '启用状态';
            } else if (e == '1') {
                return '停用状态';
            } else {
                return "";
            }
        },unformat: function (e) {
            if (e == '启用状态') {
                return '0';
            } else if (e == '停用状态') {
                return '1';
            } else {
                return "";
            }
        }
    }, {
        name: 'serviceType',
        formatter: function (e) {
            if (e == '0') {
                return '停用';
            } else if (e == '1') {
                return '解除停用';
            } else {
                return "";
            }
        },unformat: function (e) {
            if (e == '停用') {
                return '0';
            } else if (e == '解除停用') {
                return '1';
            } else {
                return "";
            }
        }
    },{
        name: 'pursueReason',
        rowtype: '#appContentT'
    }/*,{
        name:'supplierTypeId',
        hidden:true
    }*/,{
        name:'customerType',
        hidden:true
    },{
        name: 'orgCode',
        index: 'orgCode',
        hidden:true
    },{
        name: 'customerOrganId',
        hidden:true
    },{
        name: 'keyId',
        hidden:true
    },{
        name: 'baseId',
        hidden: true
    }],
    key:'baseId',
    rowNum: 999,
    altRows: true, //设置为交替行表格,默认为false
    rownumbers: true,//是否展示序号
    ondblClickRow: function (id,dom,obj,index,event) {
        //console.log('双击行事件', id,dom,obj,index,event);
    },
    onSelectRow: function (id,dom,obj,index,event,obj) {
        ///console.log('单机行事件', id,dom,obj,index,event,obj);
    },

});

function save(auditStatus) {
    var sexamine = $("#appId").val()
    var CustomerDisableInfoVo = {};
    CustomerDisableInfoVo['customerDisuserVo'] = $("#baseApproval").serializeToJSON();
    var table = $("#table1").getRowData();

    if(table == false){
        utils.dialog({content: '请添加停用申请！', quickClose: true, timeout: 2000}).showModal();
        return;
    }

    for(var i = 0 ; i < table.length; i++  ){
        if(table[i].pursueReason==""||table[i].pursueReason==null){
            utils.dialog({content: '请填写申请原因！', quickClose: true, timeout: 2000}).showModal();
            return false;
        } else {
            //限制字符长度 ，数据库为100，
            console.log("申请原因长度： " + table[i].pursueReason.length)
            if (table[i].pursueReason.length > 100) {
                utils.dialog({content: '申请原因请少于100个字符！', quickClose: true, timeout: 2000}).showModal();
                return;
            }
        }
    }


    for(var i = 0 ; i < table.length; i++ ){
        // if(table[i].supplierTypeId != ""){
        //     table[i].supplierType=table[i].supplierTypeId;
        // }
        table[i].serviceType=table[i].serviceType;

        // table[i].supplierOrganId=table[i].id;
    }
    CustomerDisableInfoVo.customerDisuserDetailVoList = table;
    if ( sexamine!=""){
        CustomerDisableInfoVo['customerDisuserVo'].id = sexamine;
    }
    CustomerDisableInfoVo['customerDisuserVo'].auditStatus = auditStatus;

    //加载页面
    parent.showLoading({hideTime: 60000});
    $.ajax({
        url: "/proxy-customer/customer/disuse/addCusutomerStopApplTemp",
        data: JSON.stringify(CustomerDisableInfoVo),
        type: "post",
        dataType: 'json',
        contentType: "application/json",
        success: function (data) {
            console.log(data);
            parent.hideLoading();
            if(data.code == 0){
                if (sexamine==""){
                    $("#examine").val(data.keyId)
                }
                var msg = "";
                if(data.message){
                    msg = data.message;
                }else{
                    msg="保存成功";
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        //刷新列表
                        $('#table').XGrid('setGridParam', {
                            postData: {
                                "orgCode": $("#orgCode").val(),
                                "applicationNumber":$("#applicationNumber").val(),
                                "customerName":$("#customerName").val(),
                                "auditStatus":$("#auditStatus").val()
                            },page:1
                        }).trigger('reloadGrid');

                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else{
                var msg = "";
                if(data.message){
                    msg = data.message;
                }else{
                    msg="保存失败";
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        },
        error: function () {
            utils.dialog({content: '内部出错！', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}

/* 新增行 事件*/
var selArr = [];
$("#addRow").on("click",function(){
    selArr = $("#table1").getRowData();
    utils.dialog({
        // url: '/proxy-customer/customer/customerBaseAppl/toCustomerList',//弹框页面请求地址
        url: "/proxy-customer/customer/disuse/toSearchList", //跳转弹框页
        title: '客户列表',
        width: 1000,
        height: 600,
        data:{
            initDataArr: selArr,
            isLock: '' //
        },
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                // var arr = [];
                // arr = selArr.concat(data)
                // arr = Array.from(new Set(arr));
                // //console.log(arr)
                /*接口   data.id  */
                // $("#table1").XGrid('clearGridData');
                // table[i].serviceType = data.disabledYn;
                // table[i].serviceType=table[i].serviceType;
                var str = "";
                for (let j = 0; j < selArr.length ; j++) {
                    str += selArr[j].customerCode + ";";
                }

                for(var i = 0 ; i < data.length; i++ ){
                    if(str.indexOf(data[i].customerCode) != -1) {
                        continue;
                    }else{
                        $('#table1').XGrid("addRowData",data[i]);
                    }
                }

            }
        }
    }).showModal();
});
//数组中查找id
function fidInArr(arr,supplierOrganId){
    for(var i=0;i<arr.length;i++)
    {
        if(arr[i].supplierOrganId == supplierOrganId)
        {
            return true;
        }
    }
    return false;

}
/* 删除行 事件*/
$("#delRow").on("click",function(){
    var selObj = $('#table1').XGrid("getSeleRow");
    if(selObj){
        $('#table1').XGrid("delRowData",selObj[0].id);
    }else{
        utils.dialog({"content":"请选择删除行！","timeout":2000}).show();
    }
});

/**
 * 流程图显示
 */
function  initApprovalFlowChart() {
    //获取审核流程数据
    var processId = $("#processId").val();
    var key = $("#key").val()
    const url = "/proxy-customer/customer/disuse/queryTotle?key=" + key + "&processInstaId=" + processId ;
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}


//关闭按钮
$("#closeAssert").on("click", function () {
    var table = $("#table").getRowData();
    if(table == false){
        utils.dialog({content: '请添加申请！', quickClose: true, timeout: 2000}).showModal();
        return;
    }
    for(var i = 0 ; i < table.length; i++  ){
        if(table[i].customerCode==""||table[i].customerCode==null){
            utils.dialog({content: '请选择客户或删除空行！', quickClose: true, timeout: 2000}).showModal();
            return;
        }
    }
    var d = dialog({
        title: "提示",
        content: "是否保存草稿？",
        width:300,
        height:30,
        button:[
            {
                value:'关闭',
                callback:function(){
                    utils.closeTab();
                }
            }
        ],
        okValue: '保存草稿',
        ok: function () {
            $("#auditStatus").val("1");// 0:录入中
            save(1);
            d.close().remove();
            return false;
        }
    }).showModal();
});
