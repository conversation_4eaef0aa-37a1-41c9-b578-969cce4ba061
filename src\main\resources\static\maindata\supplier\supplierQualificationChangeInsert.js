var supplierBaseId =  $("#supplierBaseId").val(); //基础属性id
var supplierOrganBaseId =  $("#supplierOrganBaseId").val(); //运营属性id
var approvalId =  $("#approvalId").val(); //申请id
var pageType = $("#pageType").val();
var processId = $("#processId").val();
var taskId = $("#taskId").val();
var approvalProcessId = $("#approvalProcessId").val();
//获取仓库地址
function distpickerHTML(n,m) {
    var len = n ? n : 1;
    var html = '';
    //2018.9.4,RL,下面html中新增了非空校验,bug2386
    let radomInit = [];
    for (let i = 0; i < len; i++) {
        let _int = parseInt(Math.random() * 100 + Math.random() * 1000);
        html += `<div class="col-md-6 depotList">
	        <div class="input-group">
	            <div class="input-group-addon require">仓库地址</div>
	            <div class="form-control form-inline distpicker" id="storageBox_${_int}">
	                <div class="row">
	                    <div class="form-group col-md-2" id="stoProvinceSel_wrap_${_int}">
	                        <select class="form-control repertoryProvinceSelect" name="repertoryProvince_${_int}" id="repertoryProvince_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-2" id="stoCitySel_wrap_${_int}">
	                        <select class="form-control repertoryCitySelect" name="repertoryCity_${_int}" id="repertoryCity_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-2" id="stoDistrictSel_wrap_${_int}">
	                        <select class="form-control repertoryAreaSelect" name="repertoryArea_${_int}" id="repertoryArea_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-5" style="position: initial;">
	                        <input type="text" class="form-control repertoryDetailSelect text-inp Filter_SpaceAndFiveStrLen_Class" name="repertoryDetail"/>
	                    </div>
	                    <div class="form-group btn-box col-md-1">
	                        <button type="button" class="btn ${i == 0 && !m ? 'addDepot': 'removeDepot'}">
	                            <span class="glyphicon ${i == 0 && !m ? 'glyphicon-plus': 'glyphicon-minus'}" aria-hidden="true"></span>
	                        </button>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </div>`;
        radomInit.push(_int);
    }
    return {
        html: html,
        radomInit: radomInit
    };
}
// 初始化仓库地址
function initDistpicker(){
    $('[data-toggle="distpicker"]').each(function(){
        //省
        var val1=$.trim($(this).find("select").eq(0).attr("data-value"));
        if(val1 && val1 != '')
        {
            $(this).find("select").eq(0).val(val1);
            $(this).find("select").eq(0).change();
        }
        //市
        var val2=$.trim($(this).find("select").eq(1).attr("data-value"));
        if(val1 && val1 != '')
        {
            $(this).find("select").eq(1).val(val2);
            $(this).find("select").eq(1).change();
        }
        //区
        var val3=$.trim($(this).find("select").eq(2).attr("data-value"));
        if(val1 && val1 != '')
        {
            $(this).find("select").eq(2).val(val3);
        }
    });
}
// 全量数据不可编辑
function disable(){
    $("input[type='text']").attr("disabled","disabled");
    $("input[type='tel']").attr("disabled","disabled");
    $("input[type='number']").attr("disabled","disabled");
    $("input[type='checkbox']").attr("disabled","disabled");
    $("input[type='radio']").attr("disabled","disabled");
    $("textarea").attr("disabled","disabled");
    $("select").attr("disabled","disabled");
    $(".addDepot").hide();
    $(".removeDepot").hide();
    $("#btn_addManufacturer").hide();
    $("#btn_removeManufacturer").hide();
    $(".rowBtn").attr("disabled","disabled");
}
// 全量数据可编辑
function able(){
    $("input[type='text']").removeAttr("disabled");
    $("input[type='tel']").removeAttr("disabled");
    $("input[type='number']").removeAttr("disabled");
    $("input[type='checkbox']").removeAttr("disabled");
    $("input[type='radio']").removeAttr("disabled");
    $("textarea").removeAttr("disabled");
    $("select").removeAttr("disabled");
    $(".addDepot").show();
    $(".removeDepot").show();
    $("#btn_addManufacturer").show();
    $("#btn_removeManufacturer").show();
}
disable();
$(function () {
    // 注册地址
    let addressSelIdObj = [
        {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvince',nextNodeId: 'province1'},
        {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCity',nextNodeId: 'registerCity'},
        {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerArea',nextNodeId: 'district1'}
    ];
    let registerPromiseArray = [];
    utils.setAllProDom('#provinceSel_wrap', addressSelIdObj, '#registerBox',true, function () {
        // 注册地址有值回显
        let _registerHiddenVal = eval($('#registerAddressJson').val());
        if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
        _registerHiddenVal.splice(_registerHiddenVal.length - 1);
        $('#' + addressSelIdObj[0]['nextNodeId']).prop('disabled', true);
        $('#' + addressSelIdObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
        for (let i = 1; i < _registerHiddenVal.length; i++) {
            registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
        }
        Promise.all(registerPromiseArray).then(data => {
            console.log(data)
            for (let i = 0; i < data.length; i++) {
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).html(data[i]);
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).prop('disabled', true);
            }
        })
        disable();
    });
    //
    // utils.setAllProDom('#provinceSel_wrap', addressSelIdObj, '#registerBox', true,function () {
    //     // 注册地址有值回显
    //     let _registerHiddenVal = eval($('#registerAddressJson').val());
    //     if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
    //     _registerHiddenVal.splice(_registerHiddenVal.length - 1);
    //     $('#' + addressSelIdObj[0]['nextNodeId']).prop('disabled', true);
    //     $('#' + addressSelIdObj[0]['nextNodeId']).addClass('{validate:{ required :true}}')
    //     $('#' + addressSelIdObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
    //     $('#' + addressSelIdObj[0]['nextNodeId']).attr('data-value',_registerHiddenVal[0]);
    //     for (let i = 1; i < _registerHiddenVal.length; i++) {
    //         utils.setAddressReturnVal(_registerHiddenVal[i-1]).then(str => {
    //             $('#' + addressSelIdObj[i]['nextNodeId']).html(str);
    //             $('#' + addressSelIdObj[i]['nextNodeId']).val(_registerHiddenVal[i]);
    //             $('#' + addressSelIdObj[i]['nextNodeId']).attr('data-value',_registerHiddenVal[i]);
    //             $('#' + addressSelIdObj[i]['nextNodeId']).prop('disabled', true);
    //             $('#' + addressSelIdObj[i]['nextNodeId']).addClass('{validate:{ required :true}}')
    //         }).catch(err => {
    //             console.log(err)
    //         })
    //     }
    //     disable();
    // });

    // 仓库地址
    let storgeAddressSelIdObj = [];
    let _storgeHiddenVal = eval($('#repertoryAddressJson').val());
    if (!_storgeHiddenVal) _storgeHiddenVal = [['','','','']];
    let _storgeHiddenValArr =  eval($('#repertoryAddressJson').val());
    if (!_storgeHiddenValArr) _storgeHiddenValArr = [['','','','']];
    $(_storgeHiddenValArr).each((index,item) => {
        item.splice(item.length - 1);
    });
    let obj = distpickerHTML(_storgeHiddenValArr.length);
    $(obj.radomInit).each((index, item) => {
        let _arr = [
            {nextNodeWrap: '#stoProvinceSel_wrap_' + item,nextNodeName: 'repertoryProvince_' + item,nextNodeId: 'repertoryProvince_' + item},
            {nextNodeWrap: '#stoCitySel_wrap_' + item,nextNodeName: 'repertoryCity_' + item,nextNodeId: 'repertoryCity_' + item},
            {nextNodeWrap: '#stoDistrictSel_wrap_' + item,nextNodeName: 'repertoryArea_' + item,nextNodeId: 'repertoryArea_' + item}
        ]
        storgeAddressSelIdObj.push(_arr)
    });
    $('#depotAddress').html(obj.html)
    $(obj.radomInit).each((index, item) => {
        let storagePromiseArray = [];
        utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[index], storgeAddressSelIdObj[index], '#storageBox_' + obj.radomInit[index], true,function () {
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(_storgeHiddenValArr[index][0]);
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[index][3]);
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
            for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
                storagePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenValArr[index][ind-1]));
            }
            let allSelArr = storgeAddressSelIdObj[index].flat().map((item, index) => {
                if (index != 0) {
                    return item['nextNodeId']
                }
            }).filter(item => {
                return item
            });
            Promise.all(storagePromiseArray).then(data => {
                for (let i = 0; i < data.length; i++) {
                    $('#' + allSelArr[i]).html(data[i]);
                    $('#' + allSelArr[i]).val(_storgeHiddenValArr[index][i + 1]);
                    $('#' + allSelArr[i]).prop('disabled', true);
                }
            })
            disable();
        })
    })
})
// 如果是新增页 或者草稿
if(pageType==0||pageType==1){
    // 能重新选择商品
}else{
    $("#supplierBtn").hide();
}
// 如果是驳回编辑页或者草稿页  资料变更按钮显示
if(pageType==1||pageType==4){
    $("#changeApplyBtn").show();
}

//质量保证协议上传附件
upLoadFile("#zlbzUpload",'#table1','signDate');
//客户委托书上传附件
upLoadFile("#khwtUpload",'#table2','proxyOderNo');
// 审核流程
function  initApprovalFlowChart(url) {
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result)
                $('.flow').process(data.result);
            }
        },
        error: function () {
        }
    });
}

// 修改后数据拼装
if (pageType==0){
    disable();
    $('#supplierName').removeAttr('readonly').removeAttr('disabled');
    $("#changeApplyBtn").hide();
    // 修改字段结构初始化
    $.changApply_insertData({
        name: 'columnValue',
        status: 'changeStatus',
        afterValue: 'valueAfter',
        beforeValue: 'valueBefore'
    });  //按钮id：changeApplyBtn
    var url = "/proxy-product/product/purchaseLimit/queryTotle?key=supplierQualificationChange";
    initApprovalFlowChart(url);
}else {
    //编辑页面
    /*if (pageType==1||pageType==4){
        disable();
    }
    //审核页面
    if (pageType==2||pageType==3){
        disable();
    }*/
    // 审核页和驳回编辑页
    if(pageType==3||pageType==4){
        var url = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+processId;
        initApprovalFlowChart(url);
    }
    // 草稿页
    if(pageType==1){
        var url = "/proxy-product/product/purchaseLimit/queryTotle?key=supplierQualificationChange";
        initApprovalFlowChart(url);
    }
    // 详情页
    if(pageType==2){
        var url = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+approvalProcessId;
        initApprovalFlowChart(url);
    }
    $.ajax({
        url:'/proxy-supplier/supplier/supplierQualificationChangeController/querySupplierOrganBaseAfterDetail',
        data:{"id":approvalId},
        type:"post",
        success:function(data){
            if(data!=null&&data!=undefined){
                //字段修改全部
                var changeObj=data.result.supplierOrganBaseChangeRecordVOList;
                var obj = {};
                var objList = {};
                /*if(data.result.supplierOrganBaseApprovalRecordVO.supplierExtendTypeVOList){
                    var list = data.result.supplierOrganBaseApprovalRecordVO.supplierExtendTypeVOList;
                    for (i=0;i<list.length;i++){
                        var meth = list[i];
                        if(meth.type==1){
                            pay.push(meth);
                        } else{
                            settle.push(meth);
                        }
                    }
                }*/
                $.each(changeObj,function (i, v) {
                    // 判断是否为质量保证协议
                    if(v.columnValue=="supplierQualityAgreementList"){
                        objList.supplierQualityAgreementList = {
                            "columnValue":"supplierQualityAgreementList",
                            "valueAfter":{},
                            "changeStatus":-1
                        };
                        objList.supplierQualityAgreementList.valueAfter = data.result.supplierQualityAgreementList;
                    }else if(v.columnValue=="supplierClientProxyOrderList"){
                        // 判断是否为客户委托书
                        objList.supplierClientProxyOrderList = {
                            "columnValue":"supplierClientProxyOrderList",
                            "valueAfter":{},
                            "changeStatus":-1
                        };
                        objList.supplierClientProxyOrderList.valueAfter = data.result.supplierClientProxyOrderList;
                    }else if(v.columnValue=="supplierApprovalFileList"){
                        // 判断是否为批准文件
                        objList.supplierApprovalFileList = {
                            "columnValue":"supplierApprovalFileList",
                            "valueAfter":{},
                            "changeStatus":-1
                        };
                        objList.supplierApprovalFileList.valueAfter = data.result.supplierApprovalFileList;
                    }else if(v.columnValue=="supplierYearReportList"){
                        // 年度报告
                        objList.supplierYearReportList = {
                            "columnValue":"supplierYearReportList",
                            "valueAfter":{},
                            "changeStatus":-1
                        };
                        objList.supplierYearReportList.valueAfter = data.result.supplierYearReportList;
                    }else if(v.columnValue=="supplierOtherFileList"){
                        // 其他附件
                        objList.supplierOtherFileList = {
                            "columnValue":"supplierOtherFileList",
                            "valueAfter":{},
                            "changeStatus":-1
                        };
                        objList.supplierOtherFileList.valueAfter = data.result.supplierOtherFileList;
                    }else if(v.columnValue=="supplierExtendItemVOList"){
                        // 付款方式结算方式
                        obj.supplierExtendItemVOList = {
                            "columnValue":"supplierExtendItemVOList",
                            "valueAfter":{},
                            "changeStatus":-1
                        };
                        obj.supplierExtendItemVOList.valueAfter =data.result.supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList;
                    }else{
                        v.changeStatus=-1;
                        obj[v.columnValue]=v;
                    }
                });
                //编辑页面
                if (pageType==1||pageType==4){
                    // 修改字段结构初始化
                    $.changApply_insertData();  //按钮id：changeApplyBtn
                }
                //审核页面和详情页
                if (pageType==2||pageType==3){
                    var objView = obj;
                    $.extend(objView,objList);
                    $.changeApply_selectData(obj);
                }
                window.changeApply=obj;
                window.changeApplyList=objList;
                window.changeApplyBak=JSON.parse(JSON.stringify(obj));
                window.changeApplyListBak=JSON.parse(JSON.stringify(objList));
            }
        }
    })
}



//type 2：主数据变更 3：运营属性变更
if(supplierOrganBaseId==null||supplierOrganBaseId==''){
    // 质量保证协议
    xGridTable1({
        id: "",
        signDate: "",
        validDate: "",
        signName: "",
        enclosureCount:"",
        enclosureList:[]
    });
    // 客户委托书
    xGridTable2({
        id: "",
        proxyOderNo: "",
        mandataryName: "",
        mandatarySex: "",
        mandataryTel: "",
        mandataryCertificateNumber: "",
        mandataryAddress: "",
        identityValidDate: "",
        proxyValidDate: "",
        authorityType: "",
        enclosureCount:"",
        supplierClientProxyProductVOList:[],
        supplierClientProxyBusinessScopeVOList:[],
        enclosureList:[]
        //2018.9.3 15:36,RL,新增剂型,begin
        ,supplierClientProxyTypeVOList:[]
        // 2018.9.3 15:36,RL,新增剂型,end
    });
    //批准文件
    xGridTable3({
        id: "",
        certificateId: "",
        certificateNum:"",
        supplierApprovalFileBusinessScopeVOList: [],
        certificationOffice: "",
        certificationDate: "",
        validityDate: "",
        enclosureCount: "",
        enclosureList:[]
    });
    //年度报告
    xGridTable4(xGridData = {
        id: "",
        reportDate: "",
        manageAbnormal: "",
        administrativePenalty: "",
        enclosureCount: "",
        enclosureList:[]
    });
}else{
    // 质量保证协议
    initTable1(supplierOrganBaseId,1);
    // 客户委托书
    initTable2(supplierOrganBaseId,1);
    // 批准文件经营范围
    table3(supplierOrganBaseId);
    //批准文件
    initTable3(supplierOrganBaseId,0);
    //年度报告table初始化
    initTable4(supplierOrganBaseId,0);
    // 其他附件
    initOtherFile(supplierOrganBaseId,0);
}
// 批准文件 经营范围填充
function table3(supplierBaseId){
    $.ajax({
        url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getApprovalFileList',
        data:{"correlationId":supplierBaseId,"type":0},
        type:"post",
        dataType:'json',
        success:function(data){
            console.log(data);
            var approvalFileList = data.result.list;
            for(var i=0;i<approvalFileList.length;i++){
                var approvalFile = approvalFileList[i];
                // 日期转换
                if (approvalFile.validityDate != null) {
                    approvalFile.validityDate = FormatAllDate(approvalFile.validityDate);
                }

                var ids = approvalFile.supplierApprovalFileBusinessScopeVOList;
                var names = approvalFile.scopeofoperationVo;
                for(var j=0;j<ids.length;j++){
                    for(var k=0;k<names.length;k++){
                        if(ids[j].businessScopeCode==names[k].scopeId){
                            ids[j].id = ids[j].businessScopeCode;
                            ids[j].name = names[k].scopeName;
                            continue;
                        }
                        if(names[k].children!=null){
                            var childrens = names[k].children;
                            for(var p=0;p<childrens.length;p++){
                                if(ids[j].businessScopeCode==childrens[p].scopeId){
                                    ids[j].id = ids[j].businessScopeCode;
                                    ids[j].name = childrens[p].scopeName;
                                }
                            }
                        }
                    }
                }
            }
            var htmlAry = [], idAry = [];
            $.each(utils.operateRange(approvalFileList,'supplierApprovalFileBusinessScopeVOList','validityDate'), function (i, v) {
                if (v.status) {
                    htmlAry.push('<font style="color: red;">' + v.name + '</font>');
                } else {
                    htmlAry.push(v.name);
                }
                idAry.push(v.id);
            });
            //console.log(htmlAry.join(','))
            $('#baseDataBuseScope').html(htmlAry.join(','));
        }
    });
}

// string转日期
function FormatAllDate(sDate) {
    var date = new Date(sDate);
    var seperator1 = "-";
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    //月
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    //日
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    //格式化后日期为：yyyy-MM-dd HH:mm:ss
    var currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate;
    return currentdate;
}


//客户委托书被委托人发生变化
$('#table2').on('keyup','.mandataryName:first', function (ev) {
    var mandataryName = $.trim($(this).val());//选中的值
    if(mandataryName !=""){
        $("input[name='mandatary']").val(mandataryName);
    }
});

//客户委托书被委托人电话发生变化
$('#table2').on('keyup','.mandataryTel:first', function (ev) {
    var mandataryTel = $.trim($(this).val());//选中的值
    $("input[name='mandataryPhone']").val(mandataryTel);
});
//初始化地址选择
// $('[data-toggle="distpicker"]').distpicker();
// initDistpicker();
//添加仓库
$("#depotAddress").on("click",".addDepot",function () {
    let obj = distpickerHTML(1,1);
    $("#depotAddress").append(obj.html);
    let storgeAddressSelIdObj = [
        {nextNodeWrap: '#stoProvinceSel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryProvince_' + obj.radomInit[0],nextNodeId: 'repertoryProvince_' + obj.radomInit[0]},
        {nextNodeWrap: '#stoCitySel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryCity_' + obj.radomInit[0],nextNodeId: 'repertoryCity_' + obj.radomInit[0]},
        {nextNodeWrap: '#stoDistrictSel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryArea_' + obj.radomInit[0],nextNodeId: 'repertoryArea_' + obj.radomInit[0]}
    ]
    utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[0], storgeAddressSelIdObj, '#storageBox_' + obj.radomInit[0],true, function () {
        $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
        $('#depotAddress .btn').css('display','inline-block');
    })
});
//删除仓库地址
$("#depotAddress").on("click", ".removeDepot", function () {
    $(this).parents(".depotList").remove();
});

//点击放大镜触发
$(document).on("click", "#supplierBtn", function (ev) {
    search(ev);
});
//回车触发
$(document).on("keydown", "#supplierName", function (ev) {
    if(ev.keyCode==13){
        search(ev);
    }
});
// 模糊搜索供应商名称
function search(ev){
    // 再次修改草稿页和新增页
    if(pageType==0||pageType==1){
        var supplierName = $("#supplierName").val();
        dialog({
            url: '/proxy-supplier/supplier/supplierQualificationChangeController/toSearchQualificationChange',//弹框页面请求地址
            title: '搜索供应商',
            width: 1000,
            height: 650,
            data: supplierName, // 给modal 要传递的 的数据
            onclose:function(){
                if(this.returnValue)
                {
                    var data=this.returnValue;
                    $.ajax({
                        url:'/proxy-supplier/supplier/supplierQualificationChangeController/queryOrganBaseById',
                        data:{"id":data.id},
                        type:"post",
                        dataType:'json',
                        success:function(data){
                            // 清空变更属性
                            window.changeApply = {};//变更obj
                            window.changeApply_ = {};//变更临时obj
                            window.changeApplyList = {};//变更obj-list
                            window.changeApplyList_ = {};//变更临时obj-list
                            var vo = data.result;
                            loadData(vo.supplierBase,$("#base"));
                            loadData(vo.supplierOrganBase,$("#operat"));
                            // todo 初始化注册地址
                            // 注册地址
                            let addressSelIdObj = [
                                [
                                    {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvince',nextNodeId: 'province1'},
                                    {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCity',nextNodeId: 'registerCity'},
                                    {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerArea',nextNodeId: 'district1'}
                                ]
                            ];
                            let registerPromiseArray = [];
                            let _registerHiddenVal = [vo.supplierBase.registerProvince, vo.supplierBase.registerCity, vo.supplierBase.registerArea];
                            $('#' + addressSelIdObj[0][0]['nextNodeId']).val(_registerHiddenVal[0]);
                            $('#' + addressSelIdObj[0][0]['nextNodeId']).attr('data-value', _registerHiddenVal[0]);
                            for (let i = 1; i < _registerHiddenVal.length; i++) {
                                registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
                            }
                            Promise.all(registerPromiseArray).then(data => {
                                console.log(data)
                                for (let i = 0; i < data.length; i++) {
                                    $('#' + addressSelIdObj[0][i+1]['nextNodeId']).html(data[i]);
                                    $('#' + addressSelIdObj[0][i+1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                                    $('#' + addressSelIdObj[0][i+1]['nextNodeId']).prop('disabled', true);
                                }
                            })

                            var AddressVOList=vo.supplierBase.supplierRepertoryAddressVOList;
                            if(null !=AddressVOList){
                                var len = AddressVOList.length;
                                $("#depotAddress").find('.depotList').remove();
                                var obj = distpickerHTML(len);
                                let storgeAddressSelIdObj = [];
                                let _storgeHiddenValArr = AddressVOList.map(item => {
                                    return [item.repertoryProvince, item.repertoryCity, item.repertoryArea]
                                });
                                let _storgeHiddenVal = AddressVOList.map(item => {
                                    return [item.repertoryProvince, item.repertoryCity, item.repertoryArea, item.repertoryDetail]
                                });
                                $(obj.radomInit).each((index, item) => {
                                    let _arr = [
                                        {nextNodeWrap: '#stoProvinceSel_wrap_' + item,nextNodeName: 'repertoryProvince_' + item,nextNodeId: 'repertoryProvince_' + item},
                                        {nextNodeWrap: '#stoCitySel_wrap_' + item,nextNodeName: 'repertoryCity_' + item,nextNodeId: 'repertoryCity_' + item},
                                        {nextNodeWrap: '#stoDistrictSel_wrap_' + item,nextNodeName: 'repertoryArea_' + item,nextNodeId: 'repertoryArea_' + item}
                                    ]
                                    storgeAddressSelIdObj.push(_arr)
                                });
                                $('#depotAddress').html(obj.html)
                                $(obj.radomInit).each((index, item) => {
                                    let storagePromiseArray = [];
                                    utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[index], storgeAddressSelIdObj[index], '#storageBox_' + obj.radomInit[index],true, function () {
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(_storgeHiddenValArr[index][0]);
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[index][3]);
                                        // $('#' + storgeAddressSelIdObj[0][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').attr('changeApplyFlag', 'supplierRepertoryAddressVOList');
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').prop('disabled', true);
                                        $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('.btn').css('display','none');
                                        disable();
                                        for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
                                            storagePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenValArr[index][ind-1]));
                                        }
                                        let allSelArr = storgeAddressSelIdObj[index].flat().map((item, index) => {
                                            if (index % 3 != 0) {
                                                return item['nextNodeId']
                                            }
                                        }).filter(item => {
                                            return item
                                        });
                                        Promise.all(storagePromiseArray).then(data => {
                                            for (let i = 0; i < data.length; i++) {
                                                $('#' + allSelArr[i]).html(data[i]);
                                                $('#' + allSelArr[i]).val(_storgeHiddenValArr[index][i + 1]);
                                                $('#' + allSelArr[i]).prop('disabled', true);
                                            }
                                        })

                                    })
                                })
                            }else{
                                let obj = distpickerHTML();
                                $("#depotAddress").html(obj.html);
                                let storgeAddressSelIdObj = [
                                    {nextNodeWrap: '#stoProvinceSel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryProvince_' + obj.radomInit[0],nextNodeId: 'repertoryProvince_' + obj.radomInit[0]},
                                    {nextNodeWrap: '#stoCitySel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryCity_' + obj.radomInit[0],nextNodeId: 'repertoryCity_' + obj.radomInit[0]},
                                    {nextNodeWrap: '#stoDistrictSel_wrap_' + obj.radomInit[0],nextNodeName: 'repertoryArea_' + obj.radomInit[0],nextNodeId: 'repertoryArea_' + obj.radomInit[0]}
                                ]
                                utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[0], storgeAddressSelIdObj, '#storageBox',true, function () {
                                    disable();
                                });
                            }

                            // $('[data-toggle="distpicker"]').distpicker();
                            // initDistpicker();
                            // 对应生产厂商
                            let ManufactoryVOList = vo.supplierBase.supplierManufactoryVOList,
                                ManufactoryHtml = '';
                            if(ManufactoryVOList.length != 0){
                                let len = ManufactoryVOList.length;
                                for(let i = 0; i< len; i++){
                                    ManufactoryHtml += `<div class="col-md-6 ManufactoryList">
                                                        <div class="input-group">
                                                            <div class="input-group-addon ">${vo.supplierBase['supplierTypeId'] == "55" ? '<i class="text-require">*  </i>': ''}对应生产厂商</div>
                                                            <div class="form-control form-inline distpicker">
                                                                <div>
                                                                    <div class="form-group col-md-11">
                                                                        <input type="hidden" id="Manufactory${i}" value="${ManufactoryVOList[i]['manufactoryId']}"  name="Manufactory${i}"/>
                                                                        <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class ${vo.supplierBase['supplierTypeId'] == "55" ? '' : ''}" value="${ManufactoryVOList[i]['manufactoryName']}"   id="Manufactory${i}Val" name="Manufactory${i}Val" />
                                                                    </div>
                                                                    <div class="form-group btn-box col-md-1">
                                                                        <!--<button type="button" class="btn ${i == 0 ? 'btn_addManufacturer': 'btn_removeManufacturer'} ">
                                                                            <span class="glyphicon ${i == 0 ? 'glyphicon-plus': 'glyphicon-minus'}" aria-hidden="true" ></span>
                                                                        </button>-->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>`; // {validate:{ required :true}}
                                }
                                $("#manufacturer_row").html(ManufactoryHtml);
                            }else{
                                // 没有回传值，页面结构不动
                                $("#manufacturer_row").html('');
                            }
                            var extendItemList=vo.supplierOrganBase.supplierExtendItemVOList;
                            // console.log(extendItemList)
                            var boolExtendFlag = false;
                            if(extendItemList.length > 0)
                            {
                                for(var i=0;i<extendItemList.length;i++)
                                {
                                    var parentCode=extendItemList[i].parentCode;
                                    var code=extendItemList[i].code;
                                    if(parentCode==0){
                                        parentCode=code;
                                    }
                                    var parent=$('.parentCode[value="'+parentCode+'"]');
                                    if(!parent.is(":checked"))
                                    {
                                        parent.prop("checked",true);
                                    }
                                    if(code && code != '')
                                    {
                                        var value=extendItemList[i].value;
                                        var cCode=parent.parents('.paymentSettlement').find('.cCode[value="'+code+'"]');
                                        var cValue=cCode.parent('.childCode').find('.cValue');
                                        cValue.val(value);
                                    }
                                    //当选中纸质银行，3月，6月 付款方式显示承兑返点,转账返点，银行，户名，账户
                                    if(parentCode==1010 ||parentCode==1003||parentCode==1004){
                                        boolExtendFlag = true;
                                    }
                                }
                                if(boolExtendFlag){
                                    $('.paymentNode').css('display','block');
                                }else{
                                    $('.paymentNode').css('display','none');
                                }

                            }

                            initPaymentSettlement();

                            $("#supplierBaseId").val(vo.supplierBase.id);
                            $("#supplierOrganBaseId").val(vo.supplierOrganBase.id);
                            $("#organBaseApplicationCode").val(data.applicationCode);
                            // 客户委托书
                            initTable1(vo.supplierOrganBase.id,1);
                            initTable2(vo.supplierOrganBase.id,1);
                            //供应商类别关联经营范围
                            baseDataBuseScopeOrScopes();
                            // 批准文件经营范围
                            // table3(vo.supplierBase.id);
                            table3(vo.supplierOrganBase.id);
                            //批准文件
                            initTable3(vo.supplierOrganBase.id,0);
                            //年度报告table初始化
                            initTable4(vo.supplierOrganBase.id,0);
                            // 其他附件
                            initOtherFile(vo.supplierOrganBase.id,0);
                            disable();
                            $("#changeApplyBtn").show();
                            $.clear_changeApply();
                        },
                        error:function(){

                        }
                    });
                }
                disable();
                if(!$("#supplierBaseId").val()){
                    $('#supplierName').removeAttr('readonly').removeAttr('disabled');
                }
            }
        }).showModal();
        ev.stopPropagation();
    }
}
//初始化地址选择
$('[data-toggle="distpicker"]').distpicker();
//添加仓库
$(".addDepot").on("click", function () {
    var html = '<div class="col-md-6 depotList">\
                <div class="input-group">\
                    <div class="input-group-addon">仓库地址</div>\
                    <div data-toggle="distpicker" class="form-control form-inline distpicker">\
                        <div class="row">\
                            <div class="form-group col-md-2">\
                                <select class="form-control" name="repertoryProvince"></select>\
                            </div>\
                            <div class="form-group col-md-2">\
                                <select class="form-control" name="repertoryCity"></select>\
                            </div>\
                            <div class="form-group col-md-2">\
                                <select class="form-control" name="repertoryArea"></select>\
                            </div>\
                            <div class="form-group col-md-4">\
                                <input type="text" class="form-control text-inp" name="repertoryDetail"/>\
                            </div>\
                            <div class="form-group btn-box col-md-1">\
                                <button type="button" class="btn removeDepot">\
                                    <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>\
                                </button>\
                            </div>\
                        </div>\
                    </div>\
                </div>\
            </div>';
    $("#depotAddress").append(html);
    $('[data-toggle="distpicker"]').distpicker();
});
//删除仓库地址
$("#depotAddress").on("click", ".removeDepot", function () {
    $(this).parents(".depotList").remove();
});
//批量上传
$("#appBatch").on("click", function () {

});
//拼装数据
function pushData(){
    //运营属性修改
    var supplierOrganBaseApprovalRecordVO=$("#supplierOrganBaseApprovalRecordVO").serializeToJSON();
    supplierOrganBaseApprovalRecordVO.baseId =  $("#supplierBaseId").val(); //基础属性id
    supplierOrganBaseApprovalRecordVO.organBaseId =  $("#supplierOrganBaseId").val(); //运营属性id

    supplierOrganBaseApprovalRecordVO.orgCode = $("input[name='orgCode']").val();
    supplierOrganBaseApprovalRecordVO.supplierCode = $("input[name='supplierCode']").val();
    supplierOrganBaseApprovalRecordVO.applicantId = $("input[name='applicantId']").val();
    supplierOrganBaseApprovalRecordVO.applicationTime = $("input[name='applicationTime']").val();
    supplierOrganBaseApprovalRecordVO.applicationCode = $("input[name='applicationCode']").val();

    delete supplierOrganBaseApprovalRecordVO.parentCode;
    delete supplierOrganBaseApprovalRecordVO.code;
    delete supplierOrganBaseApprovalRecordVO.value;
    delete supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList;
    var supplierAllDataVO={};

    //运营属性 付款、结算部分
   /* var supplierExtendItemVoList=[];
    $(".paymentSettlement").each(function(){
      var parentInp=$(this).find(".parentCode");
      if(parentInp.is(":checked"))
      {
          var parentCode=parentInp.attr('name');
          var parentCodeVal=parentInp.val();
          $(this).find(".childCode").each(function(index){
              var json={};
              var cCode=$(this).find(".cCode");
              var cCodeName=cCode.attr('name');
              var cCodeValue=cCode.val();
              var cValue=$(this).find(".cValue");
              var cValueName=cValue.attr('name');
              var cValueValue=cValue.val();
              if($.trim(cValueValue) != '')
              {
                  json[parentCode]=parentCodeVal;
                  json[cCodeName]=cCodeValue;
                  json[cValueName]=cValueValue;
                  supplierExtendItemVoList.push(json);
              }
          });
      }

    });
    supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList = supplierExtendItemVoList;*/
    //修改后数据拼装
    var changeApplyTemp = JSON.parse(JSON.stringify(window.changeApply));
    var supplierOrganBaseChangeRecordVOList=[];
    $.each(changeApplyTemp,function (c, v) {
        if(c=="supplierExtendItemVOList"){
            // 修改后的结算方式付款方式
            var supplierExtendItemVOList = changeApplyTemp.supplierExtendItemVOList.valueAfter;
            supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList = [];
            if(supplierExtendItemVOList){
                for(var i=0;i<supplierExtendItemVOList.length;i++){
                    var bean = supplierExtendItemVOList[i];
                    if(!supplierExtendItemVOList[i].code || supplierExtendItemVOList[i].code == undefined){
                        supplierExtendItemVOList[i].code=supplierExtendItemVOList[i].parentCode;
                        supplierExtendItemVOList[i].parentCode=0;
                    }
                    supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList.push(bean);
                }
            }
            // 字段变更表删除修改前后字段
            delete changeApplyTemp.supplierExtendItemVOList.valueBefore;
            delete changeApplyTemp.supplierExtendItemVOList.valueAfter;
        }
        supplierOrganBaseChangeRecordVOList.push(v);
    });
   // table等特殊数据
    var changeApplyListTemp = JSON.parse(JSON.stringify(window.changeApplyList));
    // 结算方式 付款方式

    if(changeApplyListTemp.supplierQualityAgreementList){
        if(changeApplyListTemp.supplierQualityAgreementList.changeStatus!=0){
            var table1 = changeApplyListTemp.supplierQualityAgreementList.valueAfter;
            for(var i=0;i<table1.length;i++){
                delete table1[i].id;
                if(typeof table1[i].enclosureList == 'string')
                {
                    table1[i].enclosureList=JSON.parse(table1[i].enclosureList);
                }
                for(var j=0;j<table1[i].enclosureList.length;j++)
                {
                    delete table1[i].enclosureList[j].type
                }
            }
            supplierAllDataVO.supplierQualityAgreementList=table1;
        }
        var quality = {
            "columnValue":"supplierQualityAgreementList",
            "changeStatus":changeApplyListTemp.supplierQualityAgreementList.changeStatus
        };
        supplierOrganBaseChangeRecordVOList.push(quality);
    }

    // 批准文件 supplierApprovalFileList:table3,
    if(changeApplyListTemp.supplierApprovalFileList){
        var table3 = changeApplyListTemp.supplierApprovalFileList.valueAfter;
        if(changeApplyListTemp.supplierApprovalFileList.changeStatus!=0){
            let msg = approvalInputValidation(table3, $('#supplierTypeId').val())
            if (msg != '') {
                utils.dialog({
                    title: '提示',
                    width: 350,
                    content: msg,
                    okValue: '确定',
                    ok: function () {}
                }).showModal();
                disable()
                return  false
            }
            for(var i=0;i<table3.length;i++){
                delete table3[i].id;
                delete table3[i].scopeofoperationVo;
                //处理经营范围数据
                var scopeList=table3[i].supplierApprovalFileBusinessScopeVOList;
                if(scopeList && scopeList.length > 0){
                    var approvalScopeList=[];
                    var scopeJson={};
                    for(var x=0;x<scopeList.length;x++)
                    {
                        if(scopeList[x].name != '经营范围'){
                            scopeJson={};
                            scopeJson.businessScopeCode=scopeList[x].id;
                            approvalScopeList.push(scopeJson);
                        }
                    }
                    table3[i].supplierApprovalFileBusinessScopeVOList=approvalScopeList;
                }
                if(typeof table3[i].enclosureList == 'string')
                {
                    table3[i].enclosureList=JSON.parse(table3[i].enclosureList);
                }
                for(var j=0;j<table3[i].enclosureList.length;j++)
                {
                    delete table3[i].enclosureList[j].type
                }
            }
            supplierAllDataVO.supplierApprovalFileList=table3;
        }
        var approvalFile = {
            "columnValue":"supplierApprovalFileList",
            "changeStatus":changeApplyListTemp.supplierApprovalFileList.changeStatus
        };
        supplierOrganBaseChangeRecordVOList.push(approvalFile);
    } else {
        let msg = approvalInputValidation($('#table3').XGrid('getRowData'), $('#supplierTypeId').val())
        if (msg != '') {
            utils.dialog({
                title: '提示',
                width: 350,
                content: msg,
                okValue: '确定',
                ok: function () {}
            }).showModal();
            disable()
            return  false
        }
    }
    // 客户委托书
    if(changeApplyListTemp.supplierClientProxyOrderList){
        if(changeApplyListTemp.supplierClientProxyOrderList.changeStatus!=0){
            var table2 = changeApplyListTemp.supplierClientProxyOrderList.valueAfter;
            if ($('#supplierTypeId').val() == '55') {
                let customerDataCheck = table2.filter(item => item['authorityType'] == '0')
                if (customerDataCheck.length == 0) {
                    utils.dialog({
                        title: '提示',
                        width: 350,
                        content: '当供应商类别是药品生产时，客户委托书授权类型必须选品种授权',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                    return false
                }
            }
            //客户委托书
            for(var i=0;i<table2.length;i++){
                delete table2[i].id;
                delete table2[i].authrange;
                if(null ==table2[i].supplierClientProxyProductVOList || table2[i].supplierClientProxyProductVOList.length<1){
                    table2[i].supplierClientProxyProductVOList=[];
                }
                if(typeof table2[i].enclosureList == 'string')
                {
                    table2[i].enclosureList=JSON.parse(table2[i].enclosureList);
                }
                for(var j=0;j<table2[i].enclosureList.length;j++)
                {
                    delete table2[i].enclosureList[j].type
                }
                if(typeof table2[i].supplierClientProxyBusinessScopeVOList == 'string')
                {
                    table2[i].supplierClientProxyBusinessScopeVOList=JSON.parse(table2[i].supplierClientProxyBusinessScopeVOList);
                }
                if(typeof table2[i].supplierClientProxyBusinessScopeVOList == 'string'){
                    table2[i].supplierClientProxyTypeVOList=JSON.parse(table2[i].supplierClientProxyTypeVOList);
                }
            }
            supplierAllDataVO.supplierClientProxyOrderList=table2;
        }
        var proxy = {
            "columnValue":"supplierClientProxyOrderList",
            "changeStatus":changeApplyListTemp.supplierClientProxyOrderList.changeStatus
        };
        supplierOrganBaseChangeRecordVOList.push(proxy);
    } else {
        if ($('#supplierTypeId').val() == '55') {
            let table2 = $('#table2').XGrid('getRowData')
            let customerDataCheck = table2.filter(item => item['authorityType'] == '0')
            if (customerDataCheck.length == 0) {
                utils.dialog({
                    title: '提示',
                    width: 350,
                    content: '当供应商类别是药品生产时，客户委托书授权类型必须选品种授权',
                    okValue: '确定',
                    ok: function () {}
                }).showModal();
                return false
            }
        }
    }
    // 年度报告 supplierYearReportList:table4,
    if(changeApplyListTemp.supplierYearReportList){
        var table4 = changeApplyListTemp.supplierYearReportList.valueAfter;
        if(changeApplyListTemp.supplierYearReportList.changeStatus!=0){
            for(var i=0;i<table4.length;i++){
                delete table4[i].id;
                for(var j=0;j<table4[i].enclosureList.length;j++)
                {
                    delete table4[i].enclosureList[j].type
                }
            }
            supplierAllDataVO.supplierYearReportList=table4;
        }
        var yearReport = {
            "columnValue":"supplierYearReportList",
            "changeStatus":changeApplyListTemp.supplierYearReportList.changeStatus
        };
        supplierOrganBaseChangeRecordVOList.push(yearReport);
    }
    // 其他附件
    //supplierOtherFileList:otherFilesArr,
    if(changeApplyListTemp.supplierOtherFileList){
        var supplierOtherFileList = changeApplyListTemp.supplierOtherFileList.valueAfter;
        if(changeApplyListTemp.supplierOtherFileList.changeStatus!=0){
            supplierAllDataVO.supplierOtherFileList=supplierOtherFileList;
        }
        var otherFile = {
            "columnValue":"supplierOtherFileList",
            "changeStatus":changeApplyListTemp.supplierOtherFileList.changeStatus
        };
        supplierOrganBaseChangeRecordVOList.push(otherFile);
    }


    supplierAllDataVO.supplierOrganBaseChangeRecordVOList=supplierOrganBaseChangeRecordVOList;
    supplierAllDataVO.supplierOrganBaseApprovalRecordVO=supplierOrganBaseApprovalRecordVO;
    console.log(supplierAllDataVO);
    return supplierAllDataVO;
}
function changeData() {
    var changeApply = window.changeApply;
    var ary=[];
    $.each(changeApply,function (c, v) {
        ary.push(v);
    });
    return ary;
}

//新增的ajax
function insert(supplierAllDataVO){
    var auditStatus = supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus;
    var successMsg = "保存成功！";
    // 提交审核
    if(auditStatus==1){
        successMsg = "恭喜提交审核成功！";
    }
    $.ajax({
        url:'/proxy-supplier/supplier/supplierQualificationChangeController/supplierQualificationChangeSave',
        data:JSON.stringify(supplierAllDataVO),
        type:"post",
        dataType:'json',
        contentType: "application/json",

        success:function(data){
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(status!=0){
                if(typeof(taskStatus) != "undefined"){
                    if(taskStatus==true){
                        utils.dialog({content: successMsg, timeout: 2000}).showModal();
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)
                    }else{// 任务流失败
                        utils.dialog({
                            title: "提示",
                            content: data.result.taskMsg + "，已暂时保存为草稿",
                            width: 300,
                            height: 30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                    }
                }else{
                    utils.dialog({content: '保存成功',timeout: 2000}).showModal();
                    setTimeout(function(){
                        utils.closeTab();
                    },2000)
                }
            }else {// 保存失败
                utils.dialog({content: data.result.msg,timeout: 2000}).showModal();
            }
        },
        error:function(){
            utils.dialog({content: '保存失败',timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 更新的ajax
function update(supplierAllDataVO,dataFrom){
    var auditStatus = supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus;
    var successMsg = "保存成功！";
    // 提交审核
    if(auditStatus==1){
        successMsg = "恭喜提交审核成功！";
    }
    $.ajax({
        url:'/proxy-supplier/supplier/supplierQualificationChangeController/supplierQualificationChangeUpdate',
        data:JSON.stringify(supplierAllDataVO),
        type:"post",
        dataType:'json',
        contentType: "application/json",

        success:function(data){
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(status!=0){
                if(typeof(taskStatus) != "undefined"){
                    if(taskStatus==true){
                        utils.dialog({content: successMsg, timeout: 2000}).showModal();
                        /*if(dataFrom=='againAssert'){
                            setTimeout(function(){
                                location.href='/process/gTaskList';
                            },2000)
                        }else{*/
                            setTimeout(function(){
                                utils.closeTab();
                            },2000)

                    }else{// 任务流失败
                        utils.dialog({
                            title: "提示",
                            content: data.result.taskMsg + "，已暂时保存为草稿",
                            width: 300,
                            height: 30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                    }
                }else{
                    utils.dialog({content: '保存成功', timeout: 2000}).showModal();
                    /*if(dataFrom=='againAssert'){
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)
                    }else{*/
                    setTimeout(function(){
                        utils.closeTab();
                    },2000)

                }
            }else {// 保存失败
                utils.dialog({content: data.result.msg,timeout: 2000}).showModal();
            }
        },
        error:function(){
            utils.dialog({content: '保存失败', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 通过ajax
function passAjax(){
    var supplierProcessVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":taskId,
        "id":approvalId
    };
    $.ajax({
        type:"post",
        url: "/proxy-supplier/supplier/supplierQualificationChangeController/passSupplierQualificationChange",
        async : false,
        data:JSON.stringify(supplierProcessVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(taskStatus==true){
                utils.dialog({content: '恭喜审核通过！', timeout: 2000}).showModal();
                setTimeout(function(){
                    utils.closeTab();
                },2000)
            }else {// 任务流失败
                utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '保存失败',timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 不通过ajax
function noPassAjax(){
    /*if(!$("#auditOpinion").val()){
        parent.hideLoading();
        utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
        return false;
    }*/
    var supplierProcessVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":taskId,
        "id":approvalId
    };
    $.ajax({
        type:"post",
        url: "/proxy-supplier/supplier/supplierQualificationChangeController/noPassSupplierQualificationChange",
        async : false,
        data:JSON.stringify(supplierProcessVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(taskStatus==true){
                utils.dialog({content: '驳回成功！', timeout: 2000}).showModal();
                setTimeout(function(){
                    utils.closeTab();
                },2000)
            }else {// 任务流失败
                utils.dialog({content: data.result.taskMsg,timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '驳回失败！',timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 撤销
function withdraw(){
    var supplierProcessVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":taskId,
        "id":approvalId
    };
    $.ajax({
        type:"post",
        url: "/proxy-supplier/supplier/supplierQualificationChangeController/supplierQualificationChangeWithdraw",
        async : false,
        data:JSON.stringify(supplierProcessVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(status!=0){
                if(typeof(taskStatus) != "undefined"){
                    if(taskStatus==true){
                        utils.dialog({
                            title: "提示",
                            content: "流程已关闭",
                            width:300,
                            height:30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                    }else{// 任务流失败
                        utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
                    }
                }else{
                    utils.dialog({content: '未知错误！', quickClose: true, timeout: 2000}).showModal();
                    setTimeout(function(){
                        utils.closeTab();
                    },2000)
                }

            }else{// 保存失败
                utils.dialog({content: data.result.msg, quickClose: true, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '撤销失败！', quickClose: true, timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}

//关闭按钮
$("#closePage").on("click", function () {
    var d =  dialog({
        title: "提示",
        content: "是否保存草稿？",
        width:300,
        height:30,
        okValue: '保存草稿',
        ok: function () {
            $("#saveRowData").click();
            d.close().remove();
            return false;
        },
        button:[
            {
                value:'关闭',
                callback:function(){
                    // var  mid = parent.$('#nav-tab li.active a').attr('href').replace('#', '');
                    // parent.$('#mainFrameTabs').bTabsClose(mid);
                    utils.closeTab();
                }
            }
        ]
    }).showModal();
});
//保存草稿
$("#saveRowData").on("click",function(){
    if(utils.allSavaBtnsHidden()){
        return false;
    }
    able();
    parent.showLoading({hideTime: 90000});
    if(!$("#supplierOrganBaseId").val()){
        disable();
        utils.dialog({content: '请选择要修改的供应商，才能继续保存', quickClose: true, timeout: 2000}).showModal();
        $('#supplierName').removeAttr('readonly').removeAttr('disabled');
        parent.hideLoading();
    }else{
        // 状态 审核中
        var supplierAllDataVO = pushData();
        supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus = 0;
        if(pageType==0){
            // 草稿 新增
//        	if($('#supplierTypeId').val() == '55'){
//        		if(window.changeApply && !window.changeApply['scopes']){
//            		parent.hideLoading();
//            		utils.dialog({
//            			title:'提示',
//            			content: '生产/经营范围不能为空',
//            			okValue: '确定',
//            			ok: function(){}
//            		}).showModal();
//            		disable();
//            		return false;
//            	}
//        	}
            insert(supplierAllDataVO);
        }else if(pageType==1){
            // 草稿再次编辑
            supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =$("#approvalId").val();
            update(supplierAllDataVO,'saveRowData');
        }
        disable();
    }
});
//提交审核
$("#submitAssert").on("click",function(){
    if(utils.allSavaBtnsHidden()){
        return false;
    }
    able();
    // parent.showLoading({hideTime: 90000});
    if(!$("#supplierOrganBaseId").val()){
        disable();
        utils.dialog({content: '请选择要修改的供应商，才能继续保存', quickClose: true, timeout: 2000}).showModal();
        $('#supplierName').removeAttr('readonly').removeAttr('disabled');
        parent.hideLoading();
    }else{
        // 状态 审核中
        var supplierAllDataVO = pushData();
        if(supplierAllDataVO.supplierOrganBaseChangeRecordVOList.length<1){
            utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
            disable();
            return false;
        }
        supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus = 1;
        var supplierBase = {
            "supplierName":$("#supplierName").val()
        };
        supplierAllDataVO.supplierBase = supplierBase;
        if($('#supplierTypeId').val() == '55'){
            if(window.changeApply && window.changeApply['scopes'] && window.changeApply['scopes']['afterValue'] == ''){
    		//if(window.changeApply && !window.changeApply['scopes']){
        		parent.hideLoading();
        		utils.dialog({
        			title:'提示',
        			content: '生产/经营范围不能为空',
        			okValue: '确定',
        			ok: function(){}
        		}).showModal();
        		disable();
        		return false;
        	}

            const temp = supplierAllDataVO.supplierClientProxyOrderList ? supplierAllDataVO.supplierClientProxyOrderList : $('#table2').XGrid('getRowData')
            if(temp && $('#supplierTypeId').val() == '55'){
                let customerDataCheck = temp.filter(item => item['authorityType'] == '0')
                if (customerDataCheck.length == 0) {
                    let msg = `当供应商类别是药品生产时，客户委托书授权类型必须选品种授权`
                    utils.dialog({
                        title: '提示',
                        width: 350,
                        content: msg,
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                    return  false
                }
            }
    	}
        let _flag = tabDataCheck()
        if (!_flag) {
            disable();
            return false
        }
        // 客户委托书 只能同一种授权类型
        let supplierClientProxyOrderList = window['changeApplyList'] && window['changeApplyList']['supplierClientProxyOrderList']&&  window['changeApplyList']['supplierClientProxyOrderList']['valueAfter'] && window['changeApplyList']['supplierClientProxyOrderList']['valueAfter'];
        let table2Data = supplierClientProxyOrderList ? supplierClientProxyOrderList : $('#table2').XGrid('getRowData')
        let authorityTypes = table2Data.map(item => item['authorityType'])
        let authorityTypesAry = Array.from(new Set(authorityTypes))
        if (table2Data != 0) {
            if (authorityTypesAry.length != 1){
                utils.dialog({
                    title: "提示",
                    content: '同一个供应商，授权类型只能存在一种',
                    okValue: '确定',
                    ok: function () {}
                }).showModal()
                disable();
                return false
            }
        }
        $.ajax({
            url: '/proxy-supplier/supplier/supplierClientProxyOrder/checkClientProxyOrder',
            data: JSON.stringify(supplierAllDataVO),
            type:"post",
            dataType:"json",
            async : true,
            contentType: "application/json",
            success: function (data) {
                var passFlag=data.passFlag;
                var msg=data.msg;
                if (!passFlag) {
                    utils.dialog({
                        title:'提示',
                        content: msg,
                        okValue: '确定',
                        ok: function () {
                            parent.showLoading({hideTime: 90000});
                            if(pageType==0) {
                                // 草稿 新增
                                insert(supplierAllDataVO);
                            }else if(pageType==1){
                                // 草稿再次编辑
                                supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =$("#approvalId").val();
                                update(supplierAllDataVO,'submitAssert');
                            }
                        },
                        cancelValue: '取消',
                        cancel: function () {}
                    }).show();
                }else{
                    parent.showLoading({hideTime: 90000});
                    if(pageType==0) {
                        // 草稿 新增
                        insert(supplierAllDataVO);
                    }else if(pageType==1){
                        // 草稿再次编辑
                        supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =$("#approvalId").val();
                        update(supplierAllDataVO,'submitAssert');
                    }
                }
            },
            error:function () {
                submint=false;
                utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
            }
        });
        // if(pageType==0) {
        //     // 草稿 新增
        //     insert(supplierAllDataVO);
        // }else if(pageType==1){
        //     // 草稿再次编辑
        //     supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =$("#approvalId").val();
        //     update(supplierAllDataVO,'submitAssert');
        // }
        disable();
    }
});
// 审核意见
function dialogData(title,status){
    utils.dialog({
        title:title,
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            parent.showLoading({hideTime: 90000});
            if(status=="pass"){
                passAjax();
            }else if(status=="noPass" ){
                if ($("#auditOpinion").val() == "") {
                    parent.hideLoading();
                    utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                noPassAjax();
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}
// 驳回编辑
function dialogAgain(title,content,status){
    utils.dialog({
        title:title,
        width:300,
        height:30,
        content: content,
        okValue: '确定',
        ok: function () {
            parent.showLoading({hideTime: 90000});
            if(status=="pass"){
                able();
                // 状态 审核中
                var supplierAllDataVO = pushData();
                if(supplierAllDataVO.supplierOrganBaseChangeRecordVOList.length<1){
                    utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
                    parent.hideLoading();
                    disable();
                    return false;
                }
                supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus = 1;
                supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =$("#approvalId").val();
                var supplierBase = {
                    "supplierName":$("#supplierName").val()
                };
                supplierAllDataVO.supplierBase = supplierBase;
                var supplierProcessVO = {
                    "auditOpinion":$("#auditOpinion").val(),
                    "taskId":taskId,
                    "id":$("#approvalId").val()
                };
                supplierAllDataVO.supplierProcessVO = supplierProcessVO;
                update(supplierAllDataVO,'againAssert');
            }else if(status=="withdraw"){
                withdraw();
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}

//通过
$("#pass").on("click",function(){
    var title = "审核通过";
    var status = "pass";
    $("#container textarea").removeAttr("disabled");
    $('#opinion').hide();
    if($('#supplierTypeId').val() == '55'){
        if(window.changeApply && window.changeApply['scopes'] && window.changeApply['scopes']['afterValue'] == ''){
		//if(window.changeApply && !window.changeApply['scopes']){
    		parent.hideLoading();
    		utils.dialog({
    			title:'提示',
    			content: '生产/经营范围不能为空',
    			okValue: '确定',
    			ok: function(){}
    		}).showModal();
    		disable();
    		return false;
    	}
	}
    // 审批意见弹窗
    dialogData(title,status);
});
//不通过
$("#noPass").on("click",function(){
    var title = "审核不通过";
    var status = "noPass";
    $("#container textarea").removeAttr("disabled");
    $('#opinion').show();
    // 审批意见弹窗
    dialogData(title,status);
});
//再次提交
$("#againAssert").on("click",function(){
    var title = "重新提交";
    var contant = "确定重新提交申请？";
    var status = "pass";
    $("#container textarea").removeAttr("disabled");
    // 再次提交审核弹窗
    //dialogAgain(title,contant,status);
    var supplierAllDataVO = pushData();
    if (!supplierAllDataVO) {
        return false
    }
    let _flag = tabDataCheck()
    if (!_flag) {
        disable();
        return false
    }
    //验证委托人身份证号是否重复
    $.ajax({
        url: '/proxy-supplier/supplier/supplierClientProxyOrder/checkClientProxyOrder',
        data: JSON.stringify(supplierAllDataVO),
        type:"post",
        dataType:"json",
        async : true,
        contentType: "application/json",
        success: function (data) {
            var passFlag=data.passFlag;
            var msg=data.msg;
            if (!passFlag) {
                utils.dialog({
                    title:'提示',
                    content: msg,
                    okValue: '确定',
                    ok: function () {
                        parent.showLoading({hideTime: 90000});
                        if(status=="pass"){
                            able();
                            // 状态 审核中
                            var supplierAllDataVO = pushData();
                            if(supplierAllDataVO.supplierOrganBaseChangeRecordVOList.length<1){
                                utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
                                parent.hideLoading();
                                disable();
                                return false;
                            }
                            supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus = 1;
                            supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =$("#approvalId").val();
                            var supplierBase = {
                                "supplierName":$("#supplierName").val()
                            };
                            supplierAllDataVO.supplierBase = supplierBase;
                            var supplierProcessVO = {
                                "auditOpinion":$("#auditOpinion").val(),
                                "taskId":taskId,
                                "id":$("#approvalId").val()
                            };
                            supplierAllDataVO.supplierProcessVO = supplierProcessVO;
                            update(supplierAllDataVO,'againAssert');
                        }else if(status=="withdraw"){
                            withdraw();
                        }
                    },
                    cancelValue: '取消',
                    cancel: function () {}
                }).show();
            }else{
                // 再次提交审核弹窗
                dialogAgain(title,contant,status);
            }
        },
        error:function () {
            submint=false;
            utils.dialog({content: '提交失败', quickClose: true, timeout: 2000}).showModal();
        }
    });

});
// 撤销
$("#withdraw").on("click",function(){
    var title = "关闭审核";
    var contant = "确定关闭审核？";
    var status = "withdraw";
    // 撤销弹窗
    dialogAgain(title,contant,status);
});
// 关闭
$("#close").on("click",function(){
    utils.closeTab();
});
