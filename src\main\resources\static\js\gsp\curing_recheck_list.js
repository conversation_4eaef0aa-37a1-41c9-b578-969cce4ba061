$(function () {
  /* 日期初始化 */
  z_utils.initDate('begint', 'endt')

  /* table_a */
  //data
  var grid_dataY = [];
  var g_item = {
    text1: "2",
    text2: "",
    text3: "",
    text4: "",
    text5: "",
    text6: "",
    text7: "",
    text8: "",
    text9: "",
    text10: "",
    text11: "",
    text12: "2",
    text13: "",
    text14: "",
  };
  var g_item1 = {
    text1: "1",
    text2: "",
    text3: "",
    text4: "",
    text5: "",
    text6: "",
    text7: "",
    text8: "",
    text9: "",
    text10: "",
    text11: "",
    text12: "2",
    text13: "",
    text14: "",
  };
  for (let i = 0; i < 20; i++) {
    if (i === 0) {
      grid_dataY.push(g_item1);
    } else {
      grid_dataY.push(g_item);
    }
  }
  var colName = ['养护计划单据编号', '商品编号', '日期', '商品名称', '生产厂家', '批号', '商品规格', '商品产地', '养护类别', '养护原因', '检验结果',
    '养护员', '复查结果', '不合格数量', '复查意见'
  ];
  var colModel = [{
    name: 'text1',
    index: 'text1'
  }, {
    name: 'text2',
    index: 'text2'
  }, {
    name: 'text3',
    index: 'text3'
  }, {
    name: 'text4',
    index: 'text4'
  }, {
    name: 'text5',
    index: 'text5'
  }, {
    name: 'text6',
    index: 'text6'
  }, {
    name: 'text7',
    index: 'text7'
  }, {
    name: 'text8',
    index: 'text8'
  }, {
    name: 'text9',
    index: 'text9'
  }, {
    name: 'text10',
    index: 'text10'
  }, {
    name: 'text11',
    index: 'text11'
  }, {
    name: 'text12',
    index: 'text12'
  }, {
    name: 'text13',
    index: 'text13'
  }, {
    name: 'text14',
    index: 'text14'
  }, {
    name: 'text15',
    index: 'text15'
  }];
  $('#table_a').XGrid({
    data: grid_dataY,
    colNames: colName,
    colModel: colModel,
    rownumbers: true,
    key: 'text1',
    rowNum: -1,
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {

    },
    gridComplete: function () {

    },
    onSelectRow: function (id, dom, obj, index, event) {
      //选中事件
      //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
      //console.log(id, dom, obj, index, event)
    }
  });

  // 筛选列
  $("#set_tb_rows").click(function () {
    //获取当前显示表格
    var tableId = $('#nav_content .active table').attr('id');
    $('#' + tableId).XGrid('filterTableHead');
  })

  /* 查询 */
  $('#searchBtn').on('click', function (e) {
    //获取form数据
    var data = $('#form_a').serializeToJSON();
    console.log(data);
    //更新表格数据
    $('#table_a').XGrid('setGridParam', {
      data: grid_dataY,
    }).trigger("reloadGrid");
  });

  /* 保存 */
  $('#saveRowData').on('click', function () {
    //获取所有项
    var data = $('#table_a').XGrid('getRowData');

  });
})