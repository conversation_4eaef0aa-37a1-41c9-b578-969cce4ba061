$(function () {
    /* 参数,页面传递的数据 */
    /* 接收数据要求：
     type:来源类型(0:出库异常 1:损益异常 2:移库异常 3:报损异常 4:空白);
     id:唯一标识;
     data:其他数据;
     */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);
    var paramId = -1;
    $.ajax({
        type: 'get',
        url: '/proxy-storage/storage/StorageOwner/apply/listAdjustAfterChannels',
        async: false,
        contentType: 'application/json;charset=utf-8',
        dataType: 'json',
        error: function () { },
        success: function (data) {
          if (data.code === 0) {
            let strList = '';
            if (data.result.length > 1) {
              strList += "";
            }
            data.result.forEach((item) => {
              strList += "<option  value='" + item.index + "'>" + item.name + '</option>';
            });
            $('#channelId').empty().append(strList);
          }
        },
    });
    //var paramId = 0;
    //设置显示详情类别
    $('.content .abnormal-info').eq(paramId).addClass('abnormal-info-active');


    var colName = ['id','商品编码','原商品编码','商品大类','商品名称', '规格/型号','单位', '生产厂家', '生产厂家编码', '库别', '库房名称', '批号', '生产日期', '有效期至', '神农可用库存',
        '调整前业务类型','调整前业务类型','本次调整数量', '调整后业务类型','调整后业务类型', '备注'
    ];
    var colModel = [
        {
        name: 'id',
        index: 'id',
        hidden:true,
        hideGrid:true
    },{
        name: 'productCode',
        index: 'productCode'
    },{
        name: 'oldProductCode',
        index: 'oldProductCode'
    },{
        name:'drugClass',
        index:'drugClass'
    } ,{
        name: 'productName',
        index: 'productName'
    }, {
        name: 'specifications',
        index: 'specifications'
    },{
        name: 'packingUnit',
        index: 'packingUnit'
    }, {
        name: 'manufName',
        index: 'manufName'
    },{
        name: 'manufCode',
        index: 'manufCode',
        hidden:true,
        hideGrid:true
    }, {
        name: 'storageType',
        index: 'storageType',
        hidden:true,
        hideGrid:true,
    },{
        name: 'storeTypeName',
        index: 'storeTypeName'
    },{
        name: 'batchNo',
        index: 'batchNo'

    }, {
        name: 'productDate',
        index: 'productDate'
    }, {
        name: 'validateDate',
        index: 'validateDate'

    }, {
        name: 'erpStorageAmount',
        index: 'erpStorageAmount'
    }, {
        name: 'adjustBeforeChannelId',
        index: 'adjustBeforeChannelId'
    },{
        name: 'adjustBeforeChannelName',
        index: 'adjustBeforeChannelName',
        hidden:true
    },{
        name: 'adjustAmount',
        index: 'adjustAmount',
        rowtype: '#text12_e'
    }, {
        name: 'adjustAfterChannelId',
        index: 'adjustAfterChannelId',
        rowtype: '#sel_channel'
    },{
        name: 'adjustAfterChannelName',
        index: 'adjustAfterChannelName',
        hidden:true
    },{
        name: 'remarks',
        index: 'remarks'
    }];

    $('#table_a').XGrid({
        //data: grid_dataY,
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'id',
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function () {

        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event);
            console.log("id========" + id);
            var ele = $(event.target);
            if(ele.hasClass('channel_id_select')){
                /* 业务类型放大镜 */
                utils.channelDialog('0','',true).then( res => {
                    console.log(res)
                if(!Array.isArray(res)){
                    res = [res]
                }
                let _str_name = '', _str_code = '';
                let _str_arr = res.map(item => {
                    return item.channelCode;
            })
                _str_name = _str_arr.join(',');

                let _str_code_arr = res.map(item => {
                    return item.channelCode;
            })





                if (_str_code_arr) {
                    if (_str_code_arr[0] == '162') {
                        utils.dialog({
                            title: '提示',
                            content: "162渠道是线下渠道,不支持货主转移",
                            width: '300',
                            cancel: false,
                            okValue: '确认',
                            ok: function () {

                            }
                        });

                        return;
                    }
                }


                _str_code = _str_code_arr.join(',')
                $(ele).parents('.input-group').find('[name=channelId]').val(_str_name)

                if($(ele).parents('td').attr('row-describedby') == 'adjustBeforeChannelId'){
                    $(ele).parents('tr').find('[row-describedby="adjustBeforeChannelId"]').text(_str_code)
                }else{
                    $(ele).parents('tr').find('[row-describedby="adjustAfterChannelId"]').text(_str_code)
                }

            }).catch(err => {
                    console.log(err)
            });

            }
        }
    });

    /* 提交审核 */
    $('#sub_check').on('click', function () {

        //输入内容校验
        if(!validform('form_a').form()) return;
        if(!validform('table_form').form()) return;

        var form_data = $('#form_a').serializeToJSON();
        var table_data = $('#table_a').XGrid('getRowData');
        if(table_data.length == 0){
            utils.dialog({
                title: '提示',
                content: '明细不能为空',
                width: '300',
                cancel: false,
                okValue: '确认',
                ok: function () {}
            }).showModal();
            return false;
        }

        //提交总条数不能超过50条
        if(table_data.length > 50){
            utils.dialog({
                title: '提示',
                content: '商品总数不能超过50条',
                width: '340',
                cancel: false,
                okValue: '确认',
                ok: function () {}
            }).showModal();
            return false;
        }

       // $("#sub_check").attr('disabled',true);

        //将校验“本次调整数量不得大于神农可用库存”修改至提交时进行校验
        let rowDataIndexArr = [];//存储不合规索引
        table_data.forEach((rowDataItem,rowDataIndex)=>{
            if(Number(rowDataItem['adjustAmount']) > Number(rowDataItem['erpStorageAmount'])){
                rowDataIndexArr.push(rowDataIndex+1);
            }
        })
        if(rowDataIndexArr.length){
            utils.dialog({
                title: '提示',
                content: `第${ rowDataIndexArr.join(',') }行，业务类型转移数量不能超过ERP库存可用数量！`,
                okValue: '确定',
                ok: function () {}
            }).showModal()
            return false;
        }

        utils.dialog({
            content:"正在保存...",
            timeout:1000
        }).showModal();

        var detailArray = JSON.stringify(table_data);

        form_data.detailArray = detailArray;

        $.ajax({
            url:"/proxy-storage/storage/StorageOwner/apply/saveAndApprove",
            type:"post",
            dataType:"json",
            data:form_data,
            success:function(result){
                if(result.code == 1){
                    utils.dialog({
                        title: '提示',
                        content: result.msg,
                        width: '300',
                        cancel: false,
                        okValue: '确认',
                        ok: function () {
                            $("#sub_check").attr('disabled',false);
                        }
                    }).showModal();
                }else if(result.code == 0){
                    utils.closeTab({reload:true});
                }

            },
            error:function(result){
                console.log("访问出现异常");
                console.log(result);
                //启用按钮
                $("#sub_check").attr('disabled',false);
            },
        })

        console.log(form_data, table_data);
    });

    /* 返回 */
    $('#goback').on('click', function () {
        utils.dialog({
            title: '提示',
            content: '返回后当前页面数据将丢失，是否继续',
            okValue: '确定',
            ok: function () {
                utils.closeTab({reload:true});
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal()
    });

    /* 新增 */
    $('#addRoW').on('click', function () {
        var all_data = $('#table_a').XGrid('getRowData');
        var commodity_id = all_data?(all_data.length ? Number(all_data[all_data.length-1].id)+1 : 1):0;
        utils.dialog({
            title: '商品列表',
            url: '/proxy-storage/storage/StorageOwner/apply/toCommodityList',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.85,
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    var batchDataArray = data.batchData;
                    var productData = data.product;

                    var rowData1 =  {};
                    rowData1.productCode = productData.productCode;
                    rowData1.productName = productData.productName;
                    rowData1.oldProductCode = productData.oldProductCode;
                    rowData1.specifications = productData.specifications;
                    rowData1.storeTypeName = batchDataArray.storeName;
                    rowData1.drugClass = productData.largeCategoryVal;
                    rowData1.packingUnit = productData.packingUnitVal;
                    rowData1.manufName = productData.manufacturerVal;
                    rowData1.manufCode = productData.manufacturer;
                    rowData1.specifications = productData.specifications;
                    rowData1.batchNo =  batchDataArray.batchCode;
                    rowData1.productDate =  batchDataArray.productDateStr;
                    rowData1.validateDate =  batchDataArray.validateDateStr;
                    rowData1.remarks =  '';
                    rowData1.storageType = batchDataArray.storageType;
                    rowData1.adjustAmount = 0;
                    rowData1.erpStorageAmount = batchDataArray.batchStockNum ;
                    rowData1.adjustBeforeChannelId = batchDataArray.channelId;
                    rowData1.adjustBeforeChannelName = batchDataArray.channelName;
                    rowData1.id = Number(commodity_id)+1;
                    if(all_data&&all_data.length>0){
                        var flag = all_data.some(function (item,index) {
                            return item.productCode==rowData1.productCode&&item.batchCode==rowData1.batchCode;
                        });
                        if(!flag) $('#table_a').XGrid('addRowData', rowData1, 'last');
                    }else {
                        $('#table_a').XGrid('addRowData', rowData1, 'last');
                    }


                }
                //$('iframe').remove();
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();



    });

    /* 删除 */
    $('#removeRoW').on('click', function () {
        var data = $('#table_a').XGrid('getSeleRow');
        if (!data) {
            utils.dialog({
                title: "提示",
                width: 200,
                height:80,
                content: "请先选中一行数据",
                timeout:2000,
            }).showModal();
            return
        }
        //删除二次确认
        utils.dialog({
            title: "提示",
            content: "确认删除当前选中行？",
            okValue: '确定',
            ok: function () {
                if (data.length) {
                    $.each(data, function (index, item) {
                        $('#table_a').XGrid('delRowData', item.id);
                    })
                } else {
                    $('#table_a').XGrid('delRowData', data.id);
                }
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    });

    //模板下载
    $('#btnDownloadTemplate').on('click',function(){
        window.open("/proxy-storage/storage/StorageOwner/apply/template");
    });
    //批量导入
    $('#btnImportBatch').on('click',function(){
        let batchImportDialog = utils.dialog({
            title: '批量导入',
            width: 400,
            height: 64,
            content: $("#importBatchDialog"),
            okValue: '开始导入',
            cancelValue: '关闭',
            onshow: function () {
                $('#inputFilePath').val("");
                $('#inputFileSelected').val("");
            },
            ok: function () {
                batchImportDialog.close().remove();
                handleUplaod()
            },
            cancel: function () {},
            onclose: function () {},
        }).showModal();
    });
    //上传
    function handleUplaod(){
        let files = $("#inputFileSelected")[0].files;

        if(!files.length){
            utils.dialog({
                title: "提示",
                width: 200,
                content: "请选择文件",
                okValue: "确定",
                ok: function () {}
            }).showModal();
            return false;
        }

        var fileName = files[0].name;
        var fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLocaleLowerCase();
        if(['xls','xlsx'].indexOf(fileType) == -1){
            utils.dialog({content: "请上传正确格式的文件！", quickClose: false, timeout: 2000}).showModal();
            return false;
        }

        var loading = dialog({
            title: '加载中',
            fixed: true,
            width: 200,
            quickClose: false,
            cancel: false
        }).showModal();

        var formData = new FormData();
        formData.append("file", files[0]);

        setTimeout(()=>{
            $.ajax({
                url: '/proxy-storage/storage/StorageOwner/apply/importApply',
                type : 'POST',
                async : false,
                data : formData,
                processData : false,
                contentType : false,
                beforeSend: function(){
                    console.log("正在上传，请稍候");
                },
                success: function(resp) {
                    loading.close();
                    if(resp.code == 0){
                        var productList = resp.result;
                        if(productList && Array.isArray(productList) && productList.length){
                            handleBackfillAfterUpload(productList);
                        }
                    }else {
                        utils.dialog({content: resp.msg || "上传失败", quickClose: true, timeout: 2000}).showModal();
                    }
                },error:function () {
                    loading.close();
                    utils.dialog({content:"上传失败", quickClose: true, timeout: 2000}).showModal();
                }
            });
        },200);
    }
    //上传完毕后回填返回的数据
    function handleBackfillAfterUpload(productList){
        let productRepeatNotice = false;//是否需要去重提醒
        let productRepeatCount = 0;//回显时重复商品数量
        productList.forEach( product => {
            let all_data = $('#table_a').XGrid('getRowData');
            let commodity_id = all_data?(all_data.length ? Number(all_data[all_data.length-1].id)+1 : 1):0;
            let rowData =  {};
            rowData.productCode = product.productCode;
            rowData.productName = product.productName;
            rowData.oldProductCode = product.oldProductCode;
            rowData.specifications = product.specifications;
            rowData.storeTypeName = product.storeTypeName;
            rowData.drugClass = product.drugClass;
            rowData.packingUnit = product.packingUnit;
            rowData.manufName = product.manufName;
            rowData.manufCode = product.manufCode;
            rowData.batchNo =  product.batchNo;
            rowData.productDate =  product.productDate;
            rowData.validateDate =  product.validateDate;
            rowData.remarks =  product.remarks;
            rowData.storageType = product.storageType;
            rowData.adjustAmount = product.adjustAmount;
            rowData.erpStorageAmount = product.erpStorageAmount ;
            rowData.adjustBeforeChannelId = product.adjustBeforeChannelId;
            rowData.adjustBeforeChannelName = product.adjustBeforeChannelName;
            rowData.adjustAfterChannelId = product.adjustAfterChannelId;
            rowData.adjustAfterChannelName = product.adjustAfterChannelName;
            rowData.id = Number(commodity_id) + 1;
            if(all_data && all_data.length > 0){
                var flag = all_data.some(function (item,index) {
                    return item.productCode==rowData.productCode && item.batchNo==rowData.batchNo;
                });
                if(flag){
                    productRepeatNotice = true;
                    productRepeatCount++;
                }else{
                    $('#table_a').XGrid('addRowData', rowData, 'last');
                }
            }else {
                productRepeatNotice = false;
                $('#table_a').XGrid('addRowData', rowData, 'last');
            }
        });
        if(productRepeatNotice && productRepeatCount>0){
            utils.dialog({content:"已自动过滤重复数据", quickClose: true, timeout: 2000}).showModal();
        }
    }
})

// 校验业务类型转移调整数量
function check_moveNum(el) {
    let amountVal = Number($(el).parents('td').prev().prev().prev().text());
    if(Number($(el).val()) > amountVal){
        utils.dialog({
            title: '提示',
            content: '业务类型转移数量不能超过ERP库存可用数量！',
            okValue: '确定',
            ok: function () {
                $(el).val(amountVal);
            }
        }).showModal()
    }
}
