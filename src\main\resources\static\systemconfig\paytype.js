$(function () {
    var $X_Table = $('#X_Table');
    $X_Table.XGrid({
        //data: "/dict/findPayTypeListByParam",
        mtype:'post',
        dataType:'json',
        url: '/proxy-sysmanage/sysmanage/dict/findPayTypeListByParam',
        postData:{
            "type":0
        },
        colNames: [ '支付方式编号', '支付方式名称', '状态','创建人','最后一次修改人','创建时间', '最后一次修改时间'],
        colModel: [

            {
            name: 'typeNum'
        }, {
            name: 'typeName'
        },{
            name: 'yn',
            index:'yn',
            formatter:ynFormatter
        },{
            name:'createUser'
            },{
            name: 'updateUser'
        },{
            name:'createTime'
            },{
            name:'updateTime'
            //formatter:datetimeFormatter
        }
        ],
        //multiselect: true, //多选
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,
        key:'id',
        rowNum: 10,
        allEvent:function(){
            var seleData=$('#X_Table').XGrid('getSeleRow');
        },
        ondblClickRow: function (e, c, a, b) {
         //   console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
         //   console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
    });

    function datetimeFormatter(val) {
        if (val != null && val !="") {
            return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
        } else {
            return "";
        }
    };

    //激活状态转换
    function ynFormatter(val){
        if(0 == val) {
            return "冻结";
        } else if(1 == val){
            return "激活";
        }else {
            return "";
        }
    }


    // 新增
    $('#addBtn').bind('click', function () {
        $('#payTypeForm')[0].reset();
        $("[name='id']").val(null);
        $("[name='type']").val(0);
        utils.dialog({
            title: '新建支付方式',
            content: $('#modal'),
            width: 450,
            okValue: '确认',
            cancelValue: '取消',
            cancel: true,
            ok: function () {
                if ($("input[name='typeName']").val() == '' || $("input[name='typeName']").val() == null) {
                    utils.dialog({
                        content: '不能为空！',
                        quickClose: true,
                        timeout: 2000
                    }).showModal();
                    return false;
                }
                data= decodeURIComponent($("#payTypeForm").serialize(),true);
                if (validform("payTypeForm").form()) {//验证通过 "myform"为需要验证的form的ID
                    $.ajax({
                        url: "addPayType",
                        type: "post",
                        data: data,
                        success: function (result) {
                            utils.dialog({content: result.result, quickClose: true}).showModal();
                            setTimeout("location.reload();",1000);
                        }
                    })
                } else {//验证不通过
                    utils.dialog({content: '特殊字符仅支持"-"，"_"', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                }
        }).show();
    })

    // 修改
    $('#editBtn').bind('click', function () {
        var selRow = $X_Table.XGrid('getSeleRow');
        var el = document.querySelector('#modal');//html元素
        $('#payTypeForm')[0].reset();
        $("[name='id']").val(null)
        $("[name='type']").val(0);
        if (selRow) {
            if (selRow.length) {
                utils.dialog({content: '只可选择一项进行修改！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }else{
                $("[name='id']").val(selRow.id);
               $("[name='typeNum']").val(selRow.typeNum);
                $("[name='typeName']").val(selRow.typeName);
                $("[name='subject']").val(selRow.subject);
                $("[name='account']").val(selRow.account);
                utils.dialog({
                    title: '修改支付方式',
                    content: el,
                    width: 450,
                    okValue: '确认',
                    cancelValue: '取消',
                    cancel: true,
                    ok: function () {
                        if ($("input[name='typeName']").val() == ''|| $("input[name='typeName']").val() == null) {
                            utils.dialog({
                                content: '不能为空',
                                quickClose: true,
                                timeout: 2000
                            }).showModal();
                            return false;
                        }
                        data= decodeURIComponent($("#payTypeForm").serialize(),true);
                        if (validform("payTypeForm").form()) {//验证通过 "myform"为需要验证的form的ID
                            $.ajax({
                                url: "updatePayType",
                                type: "post",
                                data: data,
                                success: function (result) {
                                    utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                                    setTimeout("location.reload();",1000);
                                }
                            })
                        } else {//验证不通过
                            utils.dialog({content: '"特殊字符仅支持“-”，“_”"', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                    }
                }).show();
            }
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }


    })

    //激活状态转换
    function ynFormatterToNum(val){
        if('冻结' == val) {
            return 0;
        } else if('激活' == val){
            return 1;
        }else {
            return null;
        }
    }

    // 冻结/激活
    $('#activOrFreezeBtn').bind('click', function () {
        var selRow = $X_Table.XGrid('getSeleRow');
        var array =[];
        if (selRow) {
            if(!$.isArray(selRow)){
                selRow=[selRow];
            }
            if (selRow.length) {
                $.each(selRow, function (index, item) {
                    var yn = ynFormatterToNum(item.yn);
                    item.yn = yn;
                })
            } else {
                $X_Table.XGrid('delRowData', selRow);
            }
              $.ajax({
                  url: "batchUpdate",
                    type: "POST",
                  dataType: "json",
                  contentType: "application/json; charset=utf-8",
                  data:JSON.stringify({list:selRow}),
                  success: function (result) {
                       utils.dialog({content: result.result, quickClose: true}).showModal();
                      setTimeout("location.reload();",1000);
                  }
              })

        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }
    })
})
function checkdata(){

    return  "<input type='checkbox'  id='check'>"
}