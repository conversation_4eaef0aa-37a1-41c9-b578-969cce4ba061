$(function () {

    $('#Manu_Table').XGrid({
        url:'/proxy-sysmanage/sysmanage/dict/querymanufactory',
        colNames: ['生产厂家id','生产厂家名称','生产厂家地址','所属品牌厂家','助记码', '是否停用', '关键词',  '创建人', '创建日期'/*,'操作'*/],
        colModel: [
            {
                name: 'manufactoryId',
                index: 'manufactoryId',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'manufactoryName',
                index: 'manufactoryName',
                width: 200,//宽度
                editable: true,//是否可编辑
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'address'
            }, {
                name: 'brandfactoryName',
                index: 'brandfactoryName',
                width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'manufactoryNumber',
                index: 'manufactoryNumber',
                width: 150,
                editable: true,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }
            , {
                name: 'isStop',
                index: 'isStop',
                width: 150,
                editable: true,
                formatter:isShop,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            },
            {
                name: 'manufactoryKeyword',
                index: 'manufactoryKeyword',
                width: 150,
                editable: true,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            },
            {
                name: 'createUser',
                index: 'createUser',
                width: 150,
                editable: true,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            },
            {
                name: 'createTime',
                index: 'createTime',
                width: 250,
                sortable: false,
                editable: true,
                formatter:datetimeFormatter,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            },
            /*{
                name:"",
                sortable: false,
                editable: true,
                formatter:manuOperation
            },*/{
                name: 'brandfactoryId',
                index: 'brandfactoryId',
                width: 150,
                editable: true,
                hidden:true,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }

        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        key:'manufactoryId',
        ondblClickRow: function (id, dom, obj, index, event) {
            //双击事件回调函数
            var el = document.querySelector('#dialog_Block');//html元素
            $('#Manuform')[0].reset();
            var timer=null;
            var selRow = obj;
            if (selRow) {
                $("[name='manufactoryName']").val(selRow.manufactoryName)
                $("[name='manufactoryKeyword']").val(selRow.manufactoryKeyword)
                $("[name='address']").val(selRow.address)
                $("[name='brandfactoryName']").val(selRow.brandfactoryName)
                $("[name='manufactoryId']").val(selRow.manufactoryId)
                $("[name='brandfactoryId']").val(selRow.brandfactoryId)
                $("[name='manufactoryNumber']").val(selRow.manufactoryNumber)
                var val=$('input[name="manufactoryKeyword"]').val();
                $('input[data-role="tagsinput"]').tagsinput('removeAll');
                $('input[data-role="tagsinput"]').tagsinput('add',val);
                if(selRow.isStop=="是"){
                    $(":radio[name='isStop'][value='1']").prop("checked", "checked");
                }else{
                    $(":radio[name='isStop'][value='0']").prop("checked", "checked");
                }
                $("[name='manufactoryName']").attr("disabled",true);
                $(".tagsinput input[type='text']").prop("disabled",true);
                $("[name='brandfactoryName']").attr("disabled",true);
                $(":radio[name='isStop']").attr("disabled",true);
                $("[name='address']").attr("disabled",true);

                utils.dialog({
                    title: '查看明细',
                    content: el,

                }).showModal();
                $(".tag").dblclick(function (ev) {
                    return false;
                })

            }else {
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
        },
        gridComplete:function(){
            $("td[row-describedby='createTime']").css("white-space","nowrap");
        }
    });
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })

})

        //新增一行
        $('#addManu').on('click', function () {
            $("[name='manufactoryName']").attr("disabled",false);
            $(".tagsinput input[type='text']").prop("disabled",false);
            $("[name='address']").attr("disabled",false);
            $("[name='brandfactoryName']").attr("disabled",false);
            $(":radio[name='isStop']").attr("disabled",false);
            var el = document.querySelector('#dialog_Block');//html元素
            var timer=null;
            $('#Manuform')[0].reset();
            $("[name='manufactoryId']").val(null)
            $('input[data-role="tagsinput"]').each(function () {
                $(this).tagsinput('removeAll');
            })
            utils.dialog({
                title: '新增',
                content: el,
                width:'630px',
                okValue: '确定',
                ok: function () {
                    $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                    data= decodeURIComponent($("#Manuform").serialize(),true);
                    var id = $("[name='brandfactoryId']").val();
                    var name =$("[name='brandfactoryName']").val();
                    if(name!=""&&id==""){
                        utils.dialog({content: '品牌厂家未维护，请维护后再提交！', quickClose: true, timeout: 2000}).showModal();
                        $("[name='brandfactoryName']").val("");
                        return false;
                    }
                    if (validform("Manuform").form()) {
                        $.ajax({
                            url: "addoreditmanufactory",
                            type: "post",
                            data: data,
                            success: function (result) {
                                utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                                setTimeout("location.reload();",1000);
                            }
                        })
                    } else {//验证不通过
                        utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    return false;
                },
                cancelValue: '取消',
                cancel: function () {
                }
            }).showModal();

        })


        //删除选中行
        $('#delManu').on('click', function () {
            var selRow = $('#Manu_Table').XGrid('getSeleRow');
            if (selRow) {
                $.ajax({
                    url:"delatemanufactorybyid",
                    data:{manufactoryId:selRow.manufactoryId},
                    type:"POST",
                    success:function(result){
                        utils.dialog({content:  result.result, quickClose: true, timeout: 2000}).showModal();
                        setTimeout("location.reload();",1000);
                    }
                })
            } else {
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }


        })

        $("#Manubut").click(function () {
            $('#Manu_Table').setGridParam({
                url:"/proxy-sysmanage/sysmanage/dict/querymanufactory",
                postData:{
                    "keyWord":$('#keyWord').val().replace(/\s+/g,""),
                    "isStop":$('#isStop').val()
                }
            }).trigger('reloadGrid');

        });

        function isShop(val){
            if(val==0){
                return "否"
            } if(val==1){
                return  "是"
            }if(val==null || val==""){
                return  "";
            }
        }

        function datetimeFormatter(val) {
            if (val != null && val !="") {
                return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
            } else {
                return "";
            }
        };


        $('#brand').on("keyup",function () {
            $("[name='brandfactoryId']").val('');
        })
        $('#brand').Autocomplete({
            serviceUrl: '/proxy-sysmanage/sysmanage/dict/querybrandfactorynotpage', //异步请求
            paramName: 'brandFactoryName',//查询参数，默认 query
            // dataType: 'json',
            //  lookup:, //监听数据 value显示文本，data为option的值
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            params:{
                isStop:0
            },
            triggerSelectOnValidInput: false, // 必选
            showNoSuggestionNotice: true, //显示查无结果的container
            noSuggestionNotice: '查询无结果',//查无结果的提示语
            // tabDisabled: true,
            dataReader:{
                'list':'result',
                'value':'brandfactoryName',
                'data':'brandfactoryId'
            },
            onSelect: function (result) {
                //选中回调
                $("[name='brandfactoryId']").val(result.data)
            },onSearchComplete: function (query, suggestions) {
                //匹配结果后回调
                console.log(suggestions.length);

                if (suggestions.length <1) {
                    $("[name='brandfactoryId']").val('');
                    return false;
                }

            }


        })

        function  manuOperation(){
            return  "<a href='javascript:;' onclick='editManu(this)'>编辑</a>"
        }

        //修改一行
        function editManu(obj){
            $("[name='manufactoryName']").attr("disabled",false);
            $(".tagsinput input[type='text']").prop("disabled",false);
            $("[name='address']").attr("disabled",false);
            $("[name='brandfactoryName']").attr("disabled",false);
            $(":radio[name='isStop']").attr("disabled",false);
            var el = document.querySelector('#dialog_Block');//html元素
            $('#Manuform')[0].reset();
            var timer=null;
            var id=$(obj).parents('tr').attr('id');
            var selRow = $('#Manu_Table').XGrid('getRowData',id);
            if (selRow) {
                $("[name='manufactoryName']").val(selRow.manufactoryName)
                $("[name='manufactoryKeyword']").val(selRow.manufactoryKeyword)
                $("[name='address']").val(selRow.address)
                $("[name='brandfactoryName']").val(selRow.brandfactoryName)
                $("[name='manufactoryId']").val(selRow.manufactoryId)
                $("[name='manufactoryNumber']").val(selRow.manufactoryNumber)
                $("[name='brandfactoryId']").val(selRow.brandfactoryId)
                var val=$('input[name="manufactoryKeyword"]').val();
                $('input[data-role="tagsinput"]').tagsinput('removeAll');
                $('input[data-role="tagsinput"]').tagsinput('add',val);
                if(selRow.isStop=="是"){
                    $(":radio[name='isStop'][value='1']").prop("checked", "checked");
                }else{
                    $(":radio[name='isStop'][value='0']").prop("checked", "checked");
                }
                utils.dialog({
                    title: '编辑',
                    content: el,
                    width:'630px',
                    okValue: '确定',
                    ok: function () {
                        $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                        data = decodeURIComponent($("#Manuform").serialize(), true);
                        var id = $("[name='brandfactoryId']").val();
                        var name =$("[name='brandfactoryName']").val();
                        if(name!=""&&id==""){
                            utils.dialog({content: '品牌厂家未维护，请维护后再提交！', quickClose: true, timeout: 2000}).showModal();
                            $("[name='brandfactoryName']").val("");
                            return false;
                        }
                        if (validform("Manuform").form()) {
                            $.ajax({
                                url: "addoreditmanufactory",
                                type: "post",
                                data: data,
                                success: function (result) {
                                    utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                                    setTimeout("location.reload();",1000);
                                }
                            })

                        } else {//验证不通过
                            utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                    },
                    cancelValue: '取消',
                    cancel: function () {

                    }
                }).showModal();
            }else {
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }
        }
