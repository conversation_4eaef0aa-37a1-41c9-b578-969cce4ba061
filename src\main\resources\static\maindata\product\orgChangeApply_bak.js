$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //保存草稿
    $("#saveRowData").on("click", function () {
        $("#statues").val(0);
        savedData();
    });
    //提交审核
    $("#submitAssert").on("click", function () {
        $("#statues").val(1);
        savedData();
    });
    $("#search_commodity").dblclick(function () {
        commodity_search_dia();
    });
    //供应价和app价计算
    $('#supplyPrice,#appPrice').keyup(function(){
        var supplyPrice=$("#supplyPrice").val();
        var appPrice=$("#appPrice").val();
        if(supplyPrice!=null&&supplyPrice!=""&&appPrice!=null&&appPrice>0){
            var value=1-supplyPrice/appPrice;
            $("#parGrossMargin").val(value.toFixed(6));
        }else {
            $("#parGrossMargin").val("");
        }
    });
})

/**
 * 搜索商品主数据
 * @returns {boolean}
 */
function commodity_search_dia() {
    var productCode=$("#search_commodity").val();
    dialog({
        url: '/proxy-product/product/adjustPrice/searchProduct',
        title: '商品列表',
        width: 1000,
        height: 650,
        data: productCode,
        onclose: function () {
            if (this.returnValue) {
                var data = this.returnValue;
                console.log(data);
                //加载主数据信息
                var orgProductId=data.id;
                $("#orgProductId").val(orgProductId);
                $.ajax({
                    type:"post",
                    url: "/proxy-product/product/orgApproval/getOrgProInfo",
                    async : false,
                    data:{"orgProductId":orgProductId},
                    dataType:"json",
                    success: function (data) {
                        var result=data.result;
                        console.log(data);
                        loadProductData(result.productBaseInfoVo);
                        loadProductData(result.productOrganizationVo);
                    },
                    error:function (XMLHttpRequest, textStatus, errorThrown) {
                        utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
                    }
                });
            }
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
};
function loadProductData(json) {
    var obj = json;
    var key, value, tagName, type, arr, thisVal;
    for (x in obj) {
        key = x;
        value = obj[x];
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).prop('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    if(arr!=null){
                        for (var i = 0; i < arr.length; i++) {
                            if (thisVal == arr[i]) {
                                $(this).prop('checked', true);
                                break;
                            }
                        }
                    }
                } else {
                    $(this).val(value);
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //加载字典值
    showDictValue();
}
function autocomplete(isEmpty,obj,data) {
    $("#" + obj + "Val").Autocomplete({
        // serviceUrl: 'data.json', //异步请求
        // paramName: 'query111',//查询参数，默认 query
        // dataType: 'json',
        lookup: data, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        onSelect: function (result) {
            $("#" + obj).val(result.data);
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
            $("#" + obj).val("");
            if (isEmpty) {
                $("#" + obj + "Val").val("");
            }
        }
    });
}
function savedData() {
    /**
     * 申请属性数据 applyFormVo
     * 基础数据 productBaseInfoVo
     * 运营属性数据 productOrganizationVo
     */
    //申请属性
    var applicationAttributeVo=$("#applicationAttributeVo").serializeToJSON();
    //5个基础属性
    var baseAttribute={"smallPackageBarCode":$("input[name='smallPackageBarCode']").val(),"piecePackageBarCode":$("input[name='piecePackageBarCode']").val(),"mediumPackageBarCode":$("input[name='mediumPackageBarCode']").val(),"secondCategory":$("#secondCategory").val(),"thirdCategory":$("#thirdCategory").val()};
    //运营属性
    var productOrganizationVo=$("#productOrganizationVo").serializeToJSON();

    var approvalRecordVoTemp = (JSON.stringify(applicationAttributeVo) + JSON.stringify(baseAttribute)+ JSON.stringify(productOrganizationVo)).replace(/}{/, ',');
    approvalRecordVoTemp=JSON.parse(approvalRecordVoTemp);
    var approvalRecordVo={"approvalRecordVo":approvalRecordVoTemp}
    var propertyApprovalDetailVosArray = new Array();
    $.each(window.changeApply,function (e,v) {
        propertyApprovalDetailVosArray.push(v);
    })
    var propertyApprovalDetailVos={"propertyApprovalDetailVos":propertyApprovalDetailVosArray};
    var data = (JSON.stringify(approvalRecordVo) + JSON.stringify(propertyApprovalDetailVos)).replace(/}{/, ',');
    console.log(data);
    $.ajax({
        type:"post",
        url: "/proxy-product/product/orgApproval/saveOrgApproval",
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            utils.dialog({content: '保存成功', quickClose: true, timeout: 2000}).showModal();
        },
        error:function (XMLHttpRequest, textStatus, errorThrown) {
            utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}