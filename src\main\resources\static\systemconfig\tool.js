ToolUtil = $.ToolUtil = ({
    //对象字符串与表字段互转(eg:'oneItemCode'-->'one_item_code')
    underscoreName: function (fieldName) {
        var result = "";
        if (fieldName !== null && fieldName !== '' && typeof(fieldName) !== 'undefined') {
            result += fieldName.substring(0, 1).toLowerCase();
            for (var i = 1; i < fieldName.length; i++) {
                var s = fieldName.substring(i, i + 1);
                // 在大写字母前添加下划线
                if (s === s.toUpperCase()) {
                    result += "_";
                }
                // 其他字符直接转成大写
                result += s.toLowerCase();
            }
        }
        return result;
    },
    //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
    dateFormat: function (time, format) {

        var t = new Date(parseInt(time,10));
        var tf = function (i) {
            return (i < 10 ? '0' : '') + i;
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        });
    },
    //求两个日期的时间差
    differentDays: function (startTime, endTime,type) {
        if(!endTime){
            endTime = new Date();    //结束时间
        }else{
            endTime = new Date(endTime);
        }
        startTime = new Date(startTime);
        var dateDiff = endTime.getTime() - startTime.getTime();
        switch (type) {
            case 1:	//天
                return Math.floor(dateDiff/(24*3600*1000));
                break;
            case 2:	//时
                return Math.floor(dateDiff/(3600*1000));
                break;
            case 3:	//分
                return Math.floor(dateDiff/(60*1000));
                break;
            case 4:	//秒
                return Math.floor(dateDiff/1000);
                break;
        }
    },
    //js实现list

    List: function () {

        var _this = new Object();

        _this.value = [];

        /* 添加 */
        _this.add = function (obj) {
            return _this.value.push(obj);
        };

        /* 大小 */
        _this.size = function () {
            return _this.value.length;
        };

        /* 返回指定索引的值 */
        _this.get = function (index) {
            return _this.value[index];
        };

        /* 删除指定索引的值 */
        _this.remove = function (index) {
            _this.value1 = [];
            _this.value[index] = '';
            for (var i = 0; i < _this.size(); i++) {
                if (_this.value[i] != '') {
                    _this.value1.push(_this.value[i]);
                }
            }
            _this.value = _this.value1;
            delete _this.value1;
            return _this.value;
        };

        _this.removeObject = function (object) {
            _this.value1 = [];
            for (var i = 0; i < _this.size(); i++) {
                if (_this.value[i] != object) {
                    _this.value1.push(_this.value[i]);
                }
            }
            _this.value = _this.value1;
            delete _this.value1;
            return _this.value;
        };

        /* 删除全部值 */
        _this.removeAll = function () {
            return _this.value = [];
        };

        /* 是否包含某个对象 */
        _this.constains = function (obj) {
            for (var i in _this.value) {
                if (obj == _this.value[i]) {
                    return true;
                } else {
                    continue;
                }
            }
            return false;
        };
        return _this;
    },
    //数组工具类
    Arrays: function () {

        var _this = new Object();

        _this.value = [];

        //数组转List
        _this.asList = function (arrayObj) {
            for (var i = 0; i < arrayObj.length; i++) {
                _this.value.push(arrayObj[i]);
            }
            return _this.value;
        }

        //List转数组
        _this.toArray = function (listObj) {
            _this.value = ToolUtil.List();
            for (var i = 0; i < listObj.lengthl; i++) {
                _this.value.add(listObj[i])
            }
            return _this.value;
        }

        return _this;
    },
    //Map工具类
    Map : function()
    {

        var struct = function (key, value) {
            this.key = key;
            this.value = value;
        };

        var put = function (key, value) {
            for (var i = 0; i < this.arr.length; i++) {
                if (this.arr[i].key === key) {
                    this.arr[i].value = value;
                    return;
                }
            }
            this.arr[this.arr.length] = new struct(key, value);
        };


        var get = function (key) {
            for (var i = 0; i < this.arr.length; i++) {
                if (this.arr[i].key === key) {
                    return this.arr[i].value;
                }
            }
            return null;
        };

        var remove = function (key) {
            var v;
            for (var i = 0; i < this.arr.length; i++) {
                v = this.arr.pop();
                if (v.key === key) {
                    continue;
                }
                this.arr.unshift(v);
            }
        };

        var size = function () {
            return this.arr.length;
        };

        var isEmpty = function () {
            return this.arr.length <= 0;
        };

        this.arr = new Array();
        this.get = get;
        this.put = put;
        this.remove = remove;
        this.size = size;
        this.isEmpty = isEmpty;
    }

})
;

