$(function () {
    $('#X_Tablea').XGrid({
        url: 'queryPageList',
        colNames: ['状态','机构','采购订单号','采购员','供应商编码','供应商名称','附件','归档意见','归档时间','归档确认人','机构'],
        colModel: [
            {
                name: 'statusName',
                index: 'statusName'
            },
            {
                name: 'orgName',
                index: 'orgName'
            },
            {
                name: 'orderNo',
                index: 'orderNo'
            },
            {
                name: 'purchaseUserName',
                index: 'purchaseUserName'
            },
            {
                name: 'supplierCode',
                index: 'supplierCode'
            },
            {
                name: 'supplierName',
                index: 'supplierName'
            },
            {
                name: 'accessoryName',
                index: 'accessoryName'
            },
            {
                name: 'remark',
                index: 'remark'

            },
            {
                name: 'fileTime',
                index: 'fileTime',
                formatter: function (e) {
                    if (e != null && e != "") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            },
            {  name: 'fileName',
                index: 'fileName'
            },
            {
                name: 'orgCode',
                index: 'orgCode',
                hidden: true,//是否隐藏
                hidegrid: true
            }
        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-a',//设置翻页所在html元素名称
       // selectandorder: true,
        rownumbers:true,
        rowList: [20, 50, 100],
        // attachRow:true,
        ondblClickRow: function (id, dom, obj, index, event) {

            //console.log('双击行事件', obj);
            this.returnValue = obj;
            // console.log(obj)
            utils.openTabs('purchaseOrderDetail','采购订单详情','/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNo='+obj.orderNo+'&type=0');
            return obj;

        },
        gridComplete: function () {}
    })

    $('#orgKey').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/productSupplier/queryOrg', //异步请求
        paramName: 'orgKey',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.orgName,orgCode:dataItem.orgCode};
                })
            };
        },
        onSelect: function (result) {
            // $("#orgKey").val(result.orgName);
            $("#orgCode").val(result.orgCode);
        },
        onNoneSelect: function (params, suggestions) {
            $("#orgKey").val("");
            $("#orgCode").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    })
})

//查询
$('#searchBtn').on('click', function () {
    var searchData = $("#searchForm").serializeToJSON();
    $('#X_Tablea').setGridParam({
        url: '/proxy-purchase/purchase/contract/queryPageList?' + $("#searchForm").serialize(),
        postData: {
            "purchaseOrderContractVo" : JSON.stringify(searchData)
        }
    }).trigger('reloadGrid');
})


/* 导出 */
$('#exportBtn').on('click', function () {
    var searchData = $("#searchForm").serializeToJSON();
    var tableId = 'X_Tablea';
    z_utils.exportTable(tableId, function (that) {
        //需要导出项
        var colName = [];
        var colNameDesc = [];
        $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
            if ($(this).prop("checked")) {
                colName.push($(this).attr('name'))
                colNameDesc.push($(this).parent().text());
            }
        });

        //获取当前选中项
        var data = $('#' + tableId).XGrid('getSeleRow');
        if (data && data.length && data.length > 0) {
            data = data.map(function (item, key) {
                var new_item = {};
                colName.forEach(function (val, index) {
                    new_item[val] = item[val]
                })
                return new_item
            })
            data = JSON.stringify(data);
        } else {
            data = '';
        }
        console.log(colName);
        var obj = {
            orgCode:$("#orgCode").val(),
            status:$("#status").val(),
            supplierCode:$("#supplierCode").val(),
            supplierName:$("#supplierName").val(),
            orderNo:$("#orderNo").val(),
            purchaseUserName:$("#purchaseUserName").val(),
            selectData: data,
            colName: colName,
            colNameDesc: colNameDesc
        }
        utils.httpPost("/proxy-purchase/purchase/contract/exportList", obj);

    });
});


//归档驳回
$('#reject').on('click', function () {
    var rowData = $('#X_Tablea').XGrid('getSeleRow');
    if(rowData.length <= 0){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    if(rowData.length > 1){
        utils.dialog({content: "请选中一条数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    utils.dialog({
        title:'归档驳回',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            $.ajax({
                url: "/proxy-purchase/purchase/contract/fileOperate",
                data: {
                    "orderNo": rowData[0].orderNo,
                    "status": 5,
                    "orgCode": rowData[0].orgCode,
                    "remark": $("#remark").val(),
                },
                success: function (result) {

                    if(result.code == 0){
                        utils.dialog({content: "操作成功!", quickClose: true, timeout: 6000}).showModal();
                        setTimeout(function () {
                            window.location ='/proxy-purchase/purchase/contract/toList';
                        }, 2000);

                    }else {
                        utils.dialog({content:result.msg, quickClose: true, timeout: 2000}).showModal();
                    }

                }
            })


        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();



});


//确认归档
$('#confirm').on('click', function () {
    var rowData = $('#X_Tablea').XGrid('getSeleRow');
    if(rowData.length <= 0){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    if(rowData.length > 1){
        utils.dialog({content: "请选中一条数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    utils.dialog({
        title:'确认归档',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            $.ajax({
                url: "/proxy-purchase/purchase/contract/fileOperate",
                data: {
                    "orderNo": rowData[0].orderNo,
                    "status": 6,
                    "orgCode": rowData[0].orgCode,
                    "remark": $("#remark").val(),
                },
                success: function (result) {

                    if(result.code == 0){
                        utils.dialog({content: "操作成功!", quickClose: true, timeout: 6000}).showModal();
                        setTimeout(function () {
                            window.location ='/proxy-purchase/purchase/contract/toList';
                        }, 2000);

                    }else {
                        utils.dialog({content:result.msg, quickClose: true, timeout: 2000}).showModal();

                    }
                }
            })


        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();


});



/**
 * 打印合同页面
 */
$("#printContractBtn").click(function(){
    var data =  $('#X_Tablea').XGrid('getSeleRow');
    var orderNos = "";
    if(data.length <= 0){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    if(data.length > 1){
        utils.dialog({content: "请选中一条数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }

    if (data.length && data.length > 0) {
        data = data.map(function (item, key) {
            if(key > 0 ){
                utils.dialog({content: "请选中一条数据！", quickClose: true, timeout: 4000}).showModal();
                return false;
            }
            orderNos = item.orderNo;
        })
    }
    if(orderNos == ""){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    var flag = false;
    $.ajax({
        type: "POST",
        url: "/proxy-purchase/purchase/upload/updateConstactStatus",
        data:{
            "orderNo":orderNos
        },
        error: function () {},
        success: function (res) {
            if(res){
                if(res.code == 0){
                    utils.dialog({
                        content:"正在打印...",
                        timeout:2000
                    }).showModal();
                    if(data && data.length > 0){
                        $("#print_box")[0].contentWindow.getData(1,orderNos);
                    }else {
                        utils.dialog({content: "请先选中要打印的单据", quickClose: true, timeout: 2000}).showModal();
                    }
                }
            }else{
                utils.dialog({content: res.msg, quickClose: true, timeout: 4000}).showModal();
            }
        }
    });
});

function makeContract(type){
    if(type == 1){
        $("#filePath").val("");
    }
    //获取选中的采购订单
    var ids = "";
    var data = $('#X_Tablea').XGrid('getSeleRow');
    if(data.length <= 0){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    if(data.length > 1){
        utils.dialog({content: "请选中一条数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }

    if (data.length && data.length > 0) {
        data = data.map(function (item, key) {
            if(key > 0 ){
                utils.dialog({content: "请选中一条数据！", quickClose: true, timeout: 4000}).showModal();
                return false;
            }
            ids = item.orderNo;
        })
    }
    if(ids == ""){
        utils.dialog({content: "请先选中数据！", quickClose: true, timeout: 4000}).showModal();
        return false;
    }
    $.ajax({
        method: "POST",
        url: "/proxy-purchase/purchase/upload/checkDownContract",
        data: {
            "orderNo": ids,
            "type":type
        },
        dataType: 'json',
        cache: false,
    }) .done(function( data ) {
        if(data.code==0){
            if(type == 1){
                uploadContractBtn(ids);
            }else if(type == 2){
                deleteContractBtn(ids);
            }else if(type == 3){
                window.open("/proxy-purchase/purchase/upload/downContract?orderNo="+ids);
            }
        } else{
            utils.dialog({content: data.msg, quickClose: true, timeout: 4000}).showModal();
        }
    });
}

//筛选列b
$("#set_tables_rowa").click(function () {
    $('#X_Tablea').XGrid('filterTableHead');
})