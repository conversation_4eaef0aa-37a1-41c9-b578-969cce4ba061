

// 其他附件 如果存在附件则不让取消
$("#otherFileBox input[type='checkbox']").on("change",function(){
    var checked=this.checked;
    if(!checked)
    {
        var val=$(this).val();
        var v=$('#other'+val).val();
    	if(v && v!= '')
    	{
            var imgs=JSON.parse($('#other'+val).val());
            if(imgs.length > 0)
            {
            	$(this).prop('checked',true);
                return false;
            }
            $('#other'+val).remove();
		}
    }
});
//其他附件查看
$("#qtfjView").on("click",function(){
	//获取type类型
	var imgList=[];
	var checkInp=$("#otherFileBox input[type='checkbox']:checked");
    if(checkInp.length){
        if(window.changeApplyList.supplierOtherFileList){ // 资料变更编辑保存之后再点查看附件按钮 取变量中的值，因为input other 的值此时已经包含修改后的附件 内容，而 此按钮只支持查看修改之前的附件数据
            var arr = [], oldArr = [];
            if(window.changeApplyList.supplierOtherFileList.valueBefore == undefined){
                var otherInp = $('input[id^=other]');
                for(var i = 0; i< otherInp.length; i++){
                    var list = $(otherInp[i]).val();
                    list = JSON.parse(list);
                    oldArr.push(list);
                }
                for(let i = 0; i< oldArr.length; i++){
                    for(let j = 0; j< oldArr[i].length; j++){
                        var obj = {};
                        obj.fileName = oldArr[i][j].fileName;
                        obj.filePath = oldArr[i][j].filePath;
                        arr.push(obj)
                    }
                }
            }else{
                oldArr = window.changeApplyList.supplierOtherFileList.valueBefore;
                for(let i = 0; i< oldArr.length; i++){
                    for(let j = 0; j< oldArr[i].enclosureList.length; j++){
                        var obj = {};
                        obj.fileName = oldArr[i].enclosureList[j].fileName;
                        obj.filePath = oldArr[i].enclosureList[j].filePath;
                        arr.push(obj)
                    }
                }
            }

            imgList = arr;
        }else{ // 页面加载完 直接查看附件  取input   other开头的输入框的值，
            for(var i=0;i<checkInp.length;i++){
                var type=checkInp.eq(i).val();
                var inp=$("#other"+type);
                if(inp.length)
                {
                    var imgArr=JSON.parse(inp.val());
                    imgList=imgList.concat(imgArr);
                }
            }
        }

    }else{
        utils.dialog({
            title:'提示',
            content:'没有可以查看的附件内容。',
            okValue:'确定',
            ok:function () {}
        }).showModal()
        return false;
    }
	$.viewImg({
        fileParam:{
            name:'fileName',
            url:'filePath'
        },
        list:imgList
    })
});
/**
 * 上传附件
 * 
 * */

function upLoadFile(obj,table,typeName){
	$(obj).on("click", function () {
		//获取type类型
		var typeList=[];
		var eChoImgList=[];
		var $table=$(table);
		var rowData=$table.getRowData();
		var $tr=$table.find("tr").not(":first");
		for(var i=0;i<$tr.length;i++){//RM_DEV
			var dataVal='';
			var dataJson={};
		    var el=$tr.eq(i).find("[name='"+typeName+"']").get(0);
		    dataVal=$.trim($(el).val());
		    if(dataVal == '' || !dataVal){
		    	utils.dialog({content: '请选择附件类型,表格除序号外第一列', quickClose: true, timeout: 2000}).showModal();
		    	return false;
		    }
		    if(el.tagName == 'INPUT'){
		    	dataJson={
                    text:$(el).val(),
                    value:$(el).val(),
                    lineNum:i
                };
		    }else if(el.tagName == 'SELECT'){
		    	dataJson={
                    text:$(el).find("option:selected").text(),
                    value:$(el).find("option:selected").val(),
                    lineNum:i
                };
		    }
		    typeList.push(dataJson);
		    //添加已存在附件
		    if(rowData[i].enclosureList.length > 0){
		    	for(var f=0;f<rowData[i].enclosureList.length;f++){
		    		rowData[i].enclosureList[f].type=dataVal;
                    rowData[i].enclosureList[f].lineNum = i;
		    	}
		        eChoImgList = eChoImgList.concat(rowData[i].enclosureList);
		    }
		}
		$(this).upLoad({
		    typeList:typeList,//格式[{text:xxx,value:xxx}]
		    eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
		    fileParam:{
		        name:'fileName',
		        url:'filePath'
		    },
		    urlBack: function (data) {
		        //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'enclosureList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData[j][typeName] == data[i].type  && j == data[i].lineNum){
                            l.push(list);
                        }
                    }
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    $table.setRowData(trId,{'enclosureList':l});
                    $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
                }
	        }
	    });
	});
}
function ndbgUploadFun() {
    $("#ndbgUpload").on("click", function() {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#table4');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var dataVal='';
            var dataJson={};
            var el=$tr.eq(i).find("[name='"+'reportDate'+"']").get(0);
            dataVal=$.trim($(el).val());
            if(dataVal == '' || !dataVal)
            {
                utils.dialog({content: '请选择附件类型,表格除序号外第一列', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            if(el.tagName == 'INPUT')
            {
                dataJson={
                    text:$(el).val(),
                    value:$(el).val()
                };
            }else if(el.tagName == 'SELECT'){
                dataJson={
                    text:$(el).find("option:selected").text(),
                    value:$(el).find("option:selected").val()
                };
            }
            typeList.push(dataJson);
            //添加已存在附件
            if(rowData[i].enclosureList.length > 0)
            {
                for(var f=0;f<rowData[i].enclosureList.length;f++)
                {
                    rowData[i].enclosureList[f].type=dataVal;
                }
                eChoImgList = eChoImgList.concat(rowData[i].enclosureList);
            }
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'fileName',
                url:'filePath'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'enclosureList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData[j]['reportDate'] == data[i].type){
                            l.push(list);
                        }
                    }
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    $table.setRowData(trId,{'enclosureList':l});
                    $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
                }

            }
        });
    })
}
/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(obj).parents("table").getRowData(parentId);
    if(typeof data.enclosureList != 'string'){
        data.enclosureList=JSON.stringify(data.enclosureList);
	}
    if(data.enclosureList.length > 0)
    {
        $.viewImg({
            fileParam:{
                name:'fileName',
                url:'filePath'
            },
            list:data.enclosureList
        })
    }
}

function qtfjUploadFun(){
	
	//其他附件
	$("#qtfjUpload").on("click", function() {
		//获取type类型
		var typeList=[];
		var eChoImgList=[];
		var checkLen=$("#otherFileBox input[type='checkbox']:checked").length;
		if(checkLen < 1){
            utils.dialog({
                title:'提示',
                content:'请先选择附件类型。',
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
		}
		$("#otherFileBox label").each(function(ind,item){
			var checked=$(item).find("input[type='checkbox']").get(0).checked;
			if(checked)
			{
				var text=$.trim($(item).text());
				var v=$(item).find("input").val();
				typeList.push({
					text:text,
					value:v
				})
                if($("input[id='newother"+v+"']").length > 0){
                    var newOther = $("input[id='newother"+v+"']");
                    var imgArr = [], newImgArr = [];
                    for(var i = 0; i<newOther.length; i++){
                        var str = $(newOther[i]).val();
                        str = JSON.parse(str)
                        imgArr = imgArr.concat(str);
                    }
                    var type = $("input[id='newother"+v+"']").attr('data-type');
                    for(var i = 0; i<imgArr.length; i++){
                        imgArr[i].type = type
                    }
                    eChoImgList = eChoImgList.concat(imgArr);
                }else{
                    var len = $("input[id='other"+v+"']").length;
                    if(len > 0){
                        var imgArr = [];
                        var otherInp = $("input[id='other"+v+"']");
                        for(let i = 0; i < len; i++ ){
                            var str = $(otherInp[i]).val();
                            str = JSON.parse(str);
                            imgArr = imgArr.concat(str);
                            var type = $(otherInp[i]).attr('data-type');
                            for(let j = 0; j< imgArr.length; j++){
                                imgArr[j].type = type
                            }
                        }
                        eChoImgList = eChoImgList.concat(imgArr);
                    }
                }
			}
		});
		$(this).upLoad({
		    typeList:typeList,//格式[{text:xxx,value:xxx}]
		    eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
		    fileParam:{
		        name:'fileName',
		        url:'filePath'
		    },
		    urlBack: function (data) {
		        //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
		        //存放数据
		        //files
		    	console.log(data)
                // window.changeApplyList.supplierOtherFileList = {};
                // window.changeApplyList.supplierOtherFileList.valueAfter = data;
		    	var html='';
                $('input[id^=newother]').remove();
                var obj = {}
                for(var name in data){
                    var list = [];
                    list.push(data[name])
                    //obj[name] = data[name];

		        	if(list.length > 0){
                    //if(JSON.stringify(list) != '{}'){
                        var arr=JSON.stringify(list);
                        //RenMin:  下面代码注释掉的意思是：
                        // 当批量管理附件 点击保存按钮后，不再 向body 中append 修改后的 附件内容。
                        // 因为：查看附件按钮 只支持查看 未修改前的附件内容，而未修改的附件内容在html 中存储于  ID 为 other 开头的隐藏域中
                        // 如果 保存修改之后依然向 body 中append  ，就会把最新的所有数据 覆盖掉  修噶前的数据，这样，在点击查看附件 按钮时 ，
                        // 就会显示  所有的附件内容，而需求本意只是想看到修改前的附件内容。
                        // 所以上面 那行 代码也不能 把 other 的 隐藏域remove 掉， 如果移除，这儿又 不保存，就会有新的问题出现：
                        // 当批量管理附件弹层 中，删掉一部分附件，点击保存按钮。此时弹层关闭，但是  用户又唤起 批量管理附件的弹层，
                        // 此时，就会看到 没有任何附件。 所以上面的代码 并不需要将 input  remove 掉。 不信你试！
                        html='<input type="hidden" data-type="'+data[name].type+'" id="newother'+data[name].type+'" value=\''+arr+'\' />';
                        $("body").append(html);
					}
                }

	        }
	    });
	});
	
}


