$(function () {
    // valAutocomplete("/proxy-product/product/productFirst/queryBuyerList",{paramName:'userNames'},"buyer",{data:"id",value:"userName"});
    valAutocomplete("/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode",{paramName:'userName',params:{"orgCode":$("#orgCode").val()}},"buyer",{data:"id",value:"userName"});

    $('#X_Tableb').XGrid({
        url:"/proxy-product/product/productOrga/queryProductDaysWarning",
        colNames: ["商品大类",'商品编码', '商品名', '通用名', '规格', '生产厂家', '批准文号',  '小包装条码', '批件名称','批件编码','签发日期','有效期至','剩余效期','采购员'],
        colModel: [
            {
                name: 'largeCategoryVal',
                index: 'largeCategoryVal',
                width: 100
            },{
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'productName',
                index: 'productName',
                width: 250
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 250
            },{
                name: 'specifications',
                index: 'specifications',
                width: 140
            },{
                name: 'manufacturerVal',
                index: 'manufacturerVal',
                width: 180
            },{
                name: 'approvalNumber',
                index: 'approvalNumber',
                width: 160
            },{
                name: 'smallPackageBarCode',
                index: 'smallPackageBarCode',
                width: 160
            },{
                name: 'batchName',
                index: 'batchName',
                width: 160
            },
            {
                name: 'batchCode',
                index: 'batchCode',
                width: 130
            },
            {
                name: 'issueDate',
                index: 'issueDate',
                width: 110
            },
            {
                name: 'validityDate',
                index: 'validityDate',
                width: 110
            }, {
                name: 'remainDays',
                index: 'remainDays',
                width: 100,
                formatter:function (value,a,rowData) {
                        var id=rowData.id;
                        console.log(id);

                        setTimeout(function () {
                            if(0 < value && value < 180){
                                $("#"+id).css({
                                    "background":"#FFF9DD",
                                });
                            }else if(value <= 0){
                                $("#"+id).css({
                                    "background":"#FDE5E5",
                                });
                            }else if(value!=0&&!value){
                                $("#"+id).css({
                                    "background":"#FDE5E5",
                                });
                                value = "";
                            }
                        },0);
                        return value;
                }
            },{
                name: 'buyer',
                index: 'buyer',
                width: 200
            },
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        rownumbers: true
    });


    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "largeCategory": $("#largeCategory").val(),
                "productCode":$("#productCode").val(),
                "minDay":$("#minDay").val(),
                "maxDay":$("#maxDay").val(),
                "buyer":$("#buyer").val()
            },page:1
        }).trigger('reloadGrid');
    });

    $("#exportBtn").on("click", function () {
        utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text())).then( () => {
            return false;
            }).catch( () => {
            //原始处理逻辑代码
            utils.dialog({
            title: '提示',
            content:"数据量大的时候耗时较长，请耐心等待。",
            okValue: '确定',
            ok: function () {
                //parent.showLoading()
                var body = document.body;
                var form = $(body).find('form#searchForm');
                $(form).attr("action","/proxy-product/product/productOrga/exportWarningExcel");
                $(form).submit();
                // setTimeout(function () {
                //     parent.hideLoading()
                // },2000)
            },
            cancelValue: '取消',
            cancel: function () { },
        }).showModal();
        });
    });
    $(document).on("change",".exportItem dt input",function () {
        var checked=this.checked;
        if(checked)
        {
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',false);
        }
    })
    $(document).on("change",".exportItem dd input[type='checkbox']",function () {
        var inpLen=$(this).parents("dd").find("input[type='checkbox']").length;
        var checkLen=$(this).parents("dd").find("input[type='checkbox']:checked").length;
        if(inpLen == checkLen)
        {
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',false);
        }
    })

})
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    });
}