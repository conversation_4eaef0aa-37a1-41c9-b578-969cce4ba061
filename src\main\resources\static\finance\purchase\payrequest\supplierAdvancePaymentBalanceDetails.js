$(function () {
    var totalTable = z_utils.totalTable;
    var colNames = ['供应商编号', '供应商名称',  '系统处理类型', '单据类型', '采购订单号', '关联单号', '处理时间','释放预付金额','占用金额','扣减金额','预付余额','可用余额'];
    var colModel = [
        {
            name: 'supplierNo',
            index: 'supplierNo',
            width: 150
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 150
        }, {
            name: 'sysHandleType',
            index: 'sysHandleType',
            width: 150,
            formatter: function (val){
                if (val =="1"){
                    return "释放"
                }else if(val =="2"){
                    return "占用"
                }else if(val =="3"){
                    return "占用回冲-驳回";
                }else if(val =="4"){
                    return "扣减";
                }else if(val =="5"){
                    return "扣减-预付款退回";
                }
            }
        }, {
            name: 'billType',
            index: 'billType',
            width: 150,
            formatter: function (val){
                if (val =="1"){
                    return "采购订单"
                }else if(val =="2"){
                    return "采购付款申请"
                }else if(val =="3"){
                    return "采购付款"
                }else if(val =="4"){
                    return "采购退货"
                }
            }
        }, {
            name: 'purchaseOrderNo',
            index: 'purchaseOrderNo',
            width: 150
        }, {
            name: 'paymentApplyforOrderNo',
            index: 'paymentApplyforOrderNo',
            width: 150
        }, {
            name: 'handleTime',
            index: 'handleTime',
            width: 150,
            sortable: false,
            editable: true,
            formatter:function (value){
                if(value){
                    return moment(value).format('YYYY-MM-DD');
                }else{
                    return ''
                }
            }
        }, {
            name: 'releasePrepaymentAmount',
            index: 'releasePrepaymentAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }, {
            name: 'occupyAmount',
            index: 'occupyAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }, {
            name: 'deductionOccupyAmount',
            index: 'deductionOccupyAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }, {
            name: 'prepaymentAmount',
            index: 'prepaymentAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }, {
            name: 'availableAmount',
            index: 'availableAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }
    ];

    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/supplierAdvancePaymentBalanceDetails/getSupplierAdvancePaymentBalanceDetails',
        // url: 'http://localhost:8080/account/find',
        colNames: colNames,
        colModel: colModel,
        postData: {"advancePaymentId": $("#id").val()},
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        attachRow:true,
        ondblClickRow: function (id, dom, obj, index, event) {
            //this.returnValue = obj;
            //  window.location.href = "/proxy-finance/finance/purchase/payrequestinfo/detailPayrequestPage?billNo="+obj.billNo+"&status=1";
            //showDetail(obj.billNo);
            //return obj;
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            var _this = $(this);
            setTimeout(function () {
                if(_this.XGrid('getRowData').length === 0){
                    utils.dialog({content:'查询无数据',quickClose:true,timeout:2000}).show();
                }
            },200);
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            var sumreleasePrepaymentAmount = ['releasePrepaymentAmount'];
            sumreleasePrepaymentAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sumoccupyAmount = ['occupyAmount'];
            sumoccupyAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sumdeductionOccupyAmount = ['deductionOccupyAmount'];
            sumdeductionOccupyAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        },
        pager: '#grid-pager'
    });




})