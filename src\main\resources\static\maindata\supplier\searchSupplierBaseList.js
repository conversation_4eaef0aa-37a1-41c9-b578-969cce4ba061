	var parent_dialog = parent.dialog.get(window);
	var keyword=parent_dialog.data;
	$('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierOrganBase/supplierBase/ajaxSupplierBaseList?queryFields="+keyword+"&searchSupplierBase=Y&disableStateBase=0",
        colNames: ['', '供应商编码', '供应商名称', '供应商类型', '业务员', '业务员电话', '营业执照号','审核状态'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                hidden:true
            }, {
                name: 'supplierCode',
                index: 'supplierCode',
                width: 200

            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 60
            }, {
                name: 'supplierTypeName',
                index: 'supplierTypeName',
                width: 150
            }, {
                name: 'delivery',
                index: 'delivery',
				hidden: true,
                width: 250
            }, {
                name: 'deliveryPhone',
                index: 'deliveryPhone',
				hidden: true,
                width: 250
            }, {
                name: 'supplierBusinessNum',
                index: 'supplierBusinessNum',
                width: 250
            }, {
                name: 'auditStatusName',
                index: 'auditStatusName',
                width: 250
            }

        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
        	ajaxSelectOneData(id);
      },
    });
    //查询
    $("#SearchBtn").on("click", function () {
    	$('#X_Tableb').XGrid('setGridParam', {
    		url:"/proxy-supplier/supplier/supplierOrganBase/supplierBase/ajaxSupplierBaseList",
    		postData: {
                "queryFields": $("#queryFields").val(),
                "supplierBusinessNum":$("#queryFields").val(),
                "searchSupplierBase":"Y",
                "disableStateBase":0
            },page:1
          
        }).trigger('reloadGrid');
    });
    //选择
    $("#SelectBtn").on("click", function () {
    	selectOneSupplier();
    	
    });
    //新增
    $("#NewAddBtn").on("click", function () {
        parent_dialog.close('rturnAddPage');
    	
    });
    
    //同步供应商主数据
    $("#SynchroBtn").on("click", function () {
    	 var d = utils.dialog({
             title: "提示",
             content: "两次同步时间间隔请大于等于2分钟，否则会导致供应商重复同步，请确定是否同步？",
             width: 300,
             height: 30,
             okValue: '确定',
             ok: function () {
                 d.close().remove();
                 $.ajax({
                     type: "post",
                     url: "/proxy-supplier/supplier/supplierOrganBase/saveWGData",
                     success: function (data) {
                         var result = data.code;
                         if (result == '0') {
                             if(data.result=="-1") {
                                 utils.dialog({content: '请稍后重试', quickClose: true, timeout: 2000}).showModal();
                                 return false;
                             }else{
                            	 if("{}"==data.result){
                            		 utils.dialog({content: "没有查询到未同步状态的供应商", quickClose: true, timeout: 2000}).showModal();
                            	 }else{
                            		 utils.dialog({content: '供应商同步成功', quickClose: true, timeout: 2000}).showModal();
                            	 }
                                 return false;
                             }
                         }
                     }
                 });
                 return false;
             },
             cancelValue: '取消',
             cancel: function () {
                 d.close().remove();
             }
         }).showModal();

    	
    });
    
    
    

    
    function selectOneSupplier(){
        var selectRow =  $('#X_Tableb').XGrid('getSeleRow');
        if (!selectRow){
     	   utils.dialog({content: '请选择行！', quickClose: true, timeout: 2000}).showModal();
        }else {
        	ajaxSelectOneData(selectRow.id)
        }
     
    	
    }
    
    function ajaxSelectOneData(rowId){
    	 $.ajax({
          	url:'/proxy-supplier/supplier/supplierOrganBase/supplierBase/toDetailByOne',
          	data:{
          		id:rowId
          	},
          	type:"post",
          	dataType:'json',
          	success:function(data){
          		console.log(data)
          		if("Y"== data.firstApplyFlag){
          			var d =  utils.dialog({
                           title: "提示",
                           content: "本供应商已申请首营，不可重复申请!",
                           width:300,
                           height:30,
                           okValue: '确定',
                           ok: function () {
                          	 d.close().remove();
                               return false;

                           }
                       }).showModal();
          			return false;
          			//alert("本供应商已申请首营，不可重复申请!");
          		}else{
                    parent_dialog.close(data);
          		}
          	},
          	error:function(){
          		
          	}
          });
    	
    	
    }

