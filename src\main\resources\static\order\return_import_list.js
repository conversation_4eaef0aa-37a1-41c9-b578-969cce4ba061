$(function () {
    /* 日期初始化 */
    z_utils.initDate('beginDate', 'endDate')

    /* tabs 切换 */
    z_utils.tableCut('flex');

    /* 合计计算  */
    var totalTable = z_utils.totalTable;

    /*查询页面总计*/
    getTotalNum();

    var colName = ['机构名称', '销售退回入库单号', '销售退回收货单号','销售订单编号', '客户编号', '客户名称', '部门名称', '验收员', '创建日期', '过账日期', '价税合计', '活动优惠金额',
        '余额抵扣优惠金额', '退回返利金额','运费' , '实付金额','是否退运费' ,'税额', '移动类型','是否FBP','是否特药','订单类型'
    ];
    var colModel = [{name: 'institutionName',    index: 'institutionName',  },
        {    name: 'refundRequestStorageCode',    index: 'refundRequestStorageCode' ,  width:'250' },
        {    name: 'salesReturnCode',    index: 'salesReturnCode',  width:'250',  },
        {    name: 'ecOrderCode',    index: 'ecOrderCode' ,  width:'250', },
        {    name: 'customerCode',    index: 'customerCode'  },
        {    name: 'customerName',    index: 'customerName',  width:'400',  },
        {    name: 'deptName',    index: 'deptName'  ,formatter: function (val) {
                if(val){
                    return "质管部";
                }else {
                    return '';
                }
            }   },
        {    name: 'checker',    index: 'checker'  },
        {    name: 'createTime',    index: 'createTime',formatter:function (e){
            if (e != null && e !="") {
                return ToolUtil.dateFormat(e, 'yyyy-MM-dd HH:mm:ss');
            } else {
                return "";
            }
        }},
        {    name: 'accountTime',    index: 'accountTime',formatter:function (e){
            if (e != null && e !="") {
                return ToolUtil.dateFormat(e, 'yyyy-MM-dd HH:mm:ss');
            } else {
                return "";
            }
        }},
        {    name: 'taxAmount',    index: 'taxAmount'  },
        {    name: 'activityDiscountAmount',    index: 'activityDiscountAmount' ,
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            } },
        {    name: 'balanceDiscountAmount',    index: 'balanceDiscountAmount'  ,
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            }},
        {    name: 'amountOfRebate',    index: 'amountOfRebate'  ,
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            } },
          {  name: 'freightAmount',  index: 'isRefundFreight',
              formatter: function (e) {
                  if (e != null && e != '') {
                      var array = e.toString().split(".");
                      if (array.length == 1){
                          return e.toString() + ".00"
                      }else if (array.length ==2){
                          var elngt2 = array[1].length;
                          if (elngt2 == 1){
                              return e.toString()+"0"
                          }else {
                              return e;
                          }
                      }else {
                          return e;
                      }
                  }else {
                      return e;
                  }
              }
          },
        {    name: 'paymentAmount',    index: 'paymentAmount' ,
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            } },
        { name: 'isRefundFreight', index: 'isRefundFreight',
        formatter: function (e) {
        if (e == 1) {
            return '是'
        }else {
            return '否'
        }
    }},
        {    name: 'tax',    index: 'tax' ,
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            } },
        { name: 'mobileType',    index: 'mobileType' ,formatter:function (e) {
            if(e==203){
                return '销售出库退货';
            }else{
                return '-';
            }
        }  },{
            name: 'isFbp',
            index: 'isFbp',
            formatter: function (e) {
                if (e == '1') {
                    return '是'
                } else {
                    return '否'
                }
            }
        },{
            name: 'isSpecialMedicine',
            index: 'isSpecialMedicine',
            formatter: function (e) {
                if (e == '1') {
                    return '特殊药品'
                } else {
                    return '否'
                }
            }
        },{
            name: 'orderType',
            index: 'orderType',
            formatter: function (e) {
                if (e == '1') {
                    return '普通订单'
                } else if (e == '2') {
                    return '连锁订单'
                }else if (e == '3') {
                    return '神农订单'
                }else if (e == '5') {
                    return '雨诺订单'
                }else if (e == '6') {
                    return '销售调账单'
                }else if (e == '7') {
                    return '智慧脸订单'
                }else if (e == '8') {
                    return '智慧脸2B订单'
                }else if (e == '9') {
                    return '智鹿请货订单（旧）'
                }else if (e == '10') {
                    return '赠品订单'
                }else if (e == '11') {
                    return '内部调拨单'
                }else if (e == '14') {
                    return '智鹿请货订单'
                }else if (e == '15') {
                    return '线下订单'
                }else if (e == '16') {
                    return '三九请货订单'
                }else if (e == '20') {
                    return '老年服务订单'
                }else if (e == '22') {
                    return '商业调拨订单'
                }else if (e == '19') {
                    return '海典订单'
                }else if (e == '18') {
                    return '荷叶订单'
                }else if (e == '17') {
                    return '特价订单'
                }else if (e == '23') {
					return '1#药城订单'
				}else if (e == '24') {
                    return '药师帮订单'
                }else if (e == '25') {
                    return '荷叶2C订单'
                }
            }}];

    $('#table_a').XGrid({
        url:'/proxy-order/order/orderReturnStorage/orderReturnStorageController/getReturnSalesListBase',
        postData:{ "beginDate": $("#beginDate").val(), "endDate": $("#endDate").val()},
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'refundRequestStorageCode',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            /* 双击单据，进入单据详情页面。若单据状态为“待处理”，双击单据跳转至编辑销售退回单页面； */
            location.href = '/proxy-order/order/orderReturnStorage/orderReturnStorageController/toRefundRequestStorageCodeBase?refundRequestStorageCode='+obj.refundRequestStorageCode;
        },
        gridComplete: function () {
            setTimeout(function (param) {
                /* 合计写入 */
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                $(sum_ele[1]).text(totalTable(data, 'activityDiscountAmount'));
                $(sum_ele[2]).text(totalTable(data, 'balanceDiscountAmount'));
                $(sum_ele[3]).text(totalTable(data, 'amountOfRebate'));
                $(sum_ele[4]).text(totalTable(data, 'paymentAmount'));
                $(sum_ele[5]).text(totalTable(data, 'tax'));
                $(sum_ele[6]).text(totalTable(data, 'noTaxCostAmount'));
            }, 200)
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['taxAmount','activityDiscountAmount','balanceDiscountAmount','amountOfRebate','paymentAmount','tax','noTaxCostAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
        },
    });


/*    $('#table_b').XGrid({
        url:'/proxy-order/order/orderReturnStorage/orderReturnStorageController/getProductList',
        colNames: ['销售退回入库单号', '行号', '入库日期', '商品编码', '条码','商品大类', '商品名称', '商品规格', '生产厂家', '商品产地',
            '单位',
            '库房名称', '批号', '生产日期', '有效期至',  '实际退货数量', '含税单价', '实付价', '价税合计', '实付金额','活动优惠金额',
            '余额抵扣优惠金额','退回返利金额', '税率', '税额','不含税成本单价','不含税成本金额'
        ],
        colModel: [{      name: 'refundRequestStorageCode',      index: 'refundRequestStorageCode'    },
            {      name: 'sortNo',      index: 'sortNo'    },
            {      name: 'createTime',      index: 'createTime'  ,formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }  },
            // {      name: 'customerCode',      index: 'customerCode'    },
            // {      name: 'customerName',      index: 'customerName'    },
            {      name: 'productCode',      index: 'productCode'    },
            {      name: 'productBarCode',      index: 'productBarCode'    },
            {      name: 'drugClass',      index: 'drugClass'    },
            {      name: 'productName',      index: 'productName'    },
            {      name: 'specifications',      index: 'specifications'    },
            {      name: 'manufacturer',      index: 'manufacturer'    },
            {      name: 'productOrigin',      index: 'productOrigin'    },
            {      name: 'productUnit',      index: 'productUnit'    },
            {      name: 'warehouseName',      index: 'warehouseName'  },
            {      name: 'batchCode',      index: 'batchCode'    },
            {      name: 'productionTime',      index: 'productionTime'   ,formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            } },
            {      name: 'periodValidity',      index: 'periodValidity'  ,formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }  },
            {      name: 'storageNumber',      index: 'storageNumber'   },
            {      name: 'taxPrice',      index: 'taxPrice'   ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                } },
            {      name: 'paymentPrice',      index: 'paymentPrice'    ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }},
            {      name: 'taxAmount',      index: 'taxAmount'    ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }},
            {      name: 'paymentAmount',      index: 'paymentAmount'  ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }  },
            {      name: 'activityDiscountAmount',      index: 'activityDiscountAmount' ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }   },
            {      name: 'balanceDiscountAmount',      index: 'balanceDiscountAmount' ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }   },
            {    name: 'amountOfRebate',    index: 'amountOfRebate'  ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                } },
            {      name: 'rate',      index: 'rate'    ,
                formatter: function (e) {
                    if ( e != undefined && e != null ) {
                        return e +'%';
                    } else {
                        return '0%'
                    }
                }  },
            {      name: 'tax',      index: 'tax'    },
            {    name: 'noTaxCostPrice',    index: 'noTaxCostPrice'  ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                } },
            {    name: 'noTaxCostAmount',    index: 'noTaxCostAmount'  ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                } },

            ],

        rowNum: 20,
        rowList:[20,50,100],
        key: 'sort',
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_b',
        gridComplete: function () {
            /!* 合计行 *!/
            var data = $(this).XGrid('getRowData');
            var sum_models = ['storageNumber','taxAmount','paymentPrice','activityDiscountAmount','tax','noTaxCostAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
        },
    });*/

    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })

    /* 客户名称 搜索提示（只显示5条） */
    $('#customerName').Autocomplete({
        serviceUrl: '/proxy-customer/customer/customerBaseAppl/pageList', //异步请求
        paramName: 'customerCode',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function(response) {
            return {
                suggestions: $.map(response.result.list, function(dataItem) {
                    return { value: dataItem.customerName, data: dataItem.customerCode };
                })
            };
        },
        onSelect: function (result) {
            //选中回调
            $("#customerCode").val(result.data)
        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
            $("#customerCode").val('');
            $('#customerName').val('');
        }
    });

    /* 商品名称 搜索提示（只显示5条） */
    $('#productName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function(response) {
            return {
                suggestions: $.map(response.result.list, function(dataItem) {
                    return { value: dataItem.productName, data: dataItem.productCode };
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#productCode").val(result.data)

        },

        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
            $("#productName").val('');
            $("#productCode").val('');
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }


        // tabDisabled: true,
    });

    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        //获取form数据
        var data = $('#form_a').serializeToJSON();
        console.log(data);
        //更新表格数据
        var $table_id = $('#nav_content .active .XGridBody table').attr('id');
        //列表
        $('#table_a').XGrid('setGridParam', {
            url:"/proxy-order/order/orderReturnStorage/orderReturnStorageController/getReturnSalesListBase",
            postData:data
            ,page:1
        }).trigger("reloadGrid");
       /* //详情
        $('#table_b').XGrid('setGridParam', {
            url:"/proxy-order/order/orderReturnStorage/orderReturnStorageController/getProductList",
            postData:data,
            page:1
        }).trigger("reloadGrid");*/
        /*查询页面总计*/
        getTotalNum();
    });

    var _ind = 0; // tab 当前项的下标  销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    //获取当前tab  的下标 销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    function getTabInd() {
        for (var i = 0; i < $('.pull-left li').length; i++) {
            if ($('.pull-left li').eq(i).hasClass('active')) {
                _ind = $('.pull-left li').eq(i).index();
            }
        }
        return _ind;
    }
    /* 导出 */
    $('#exportRowData').on('click', function () {
        utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text())).then( () => {
            return false;
    }).catch(() => {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            var formData = $('#form_a').serializeToJSON();
            formData["colName"] = colName;
            formData["colNameDesc"] = colNameDesc;

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length) {
                /*if (!data.length) {
                    data = [data];
                }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
            }
            console.log(colName);

            // console.log(colNameDesc);
            switch (getTabInd()) {
                case 0:
                    httpPost("/proxy-order/order/orderReturnStorage/orderReturnStorageController/exportReturnSalesListBase", formData);
                    break;
                case 1:
                    httpPost("/proxy-order/order/orderReturnStorage/orderReturnStorageController/exportProductList", formData);
                    break;
            }
        });
    });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 客户名称查询 */
    $('#s_user').on('click', function (e) {
        var user_d = $(e.target).prev('input').val();
        utils.dialog({
            url:"/proxy-order/order/orderReturn/orderReturnController/toUserList",
            title: '客户列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $('#customerCode').val(data.customerCode);
                    $('#customerName').val(data.customerName);
                    console.log(data);
                }
            },
            oniframeload: function () {

            }
        }).showModal();
        return false;
    })
    //提取入库数据
    $('#btn_input_data').click(function () {
        utils.dialog({
            title: '提取出库数据',
            url: '/proxy-order/order/orderReturnStorage/orderReturnStorageController/toWmsSalesOrderReturnStorageList',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.8,
            //     data: $("#input_goodName").val(), // 给modal 要传递的 的数据
            //     onclose: function () {
            //         if (this.returnValue) {
            //             var data = this.returnValue;
            //             console.log(data);
            //             $("#input_goodName").val(data.productName);
            //             $("#input_product_hidden").val(data.productCode);
            //         }
            //         $('iframe').remove();
            //     },
            //     oniframeload: function () {
            //         // console.log('iframe ready')
            //     }
        }).showModal();
        return false;
    })
    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var commodity_d = $(e.target).prev('input').val();
        utils.dialog({
            url:"/proxy-order/order/orderReturn/orderReturnController/toCommodityList",
            title: '商品列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            data: commodity_d, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $('#productName').val(data.productName);
                    $('#productCode').val(data.productCode);
                    console.log("this.returnValue", data);
                }
            },
            oniframeload: function () {

            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    });


    /* 获取出库单选中项,并获取单据数据(组装) */
    function seleRow(callback) {
        var ary = [];
        var sele_data = $("#table_a").XGrid("getSeleRow");
        console.log(sele_data);
        if(sele_data.length>0){
            if(!$.isArray(sele_data)){
                ary.push(sele_data.refundRequestStorageCode)
            }else {
                sele_data.forEach(function (item,index) {
                    ary.push(item.refundRequestStorageCode);
                })
            }
        }else {
            utils.dialog({
                title:'预览',
                content:"请选择要打印的单据",
                timeout:2000
            }).show();
            return
        }
        callback(ary);
    }

    /* 打印预览 */
    $("#btn_print_view").on("click",function () {
        seleRow(function (ary) {
            $("#print_box")[0].contentWindow.getData(0,ary);
        })
    });
    $("#btn_print").on("click",function () {
        utils.dialog({
            content:"正在打印...",
            timeout:1000
        }).showModal();
        seleRow(function (ary) {
            $("#print_box")[0].contentWindow.getData(1,ary);
        })
    });
})
//查询总合计数目
function getTotalNum () {
    var formData = $("#form_a").serializeToJSON();
    var exceptionTypeArray = formData.exceptionType;
    var exceptionTypeStr = "";
    if (exceptionTypeArray != undefined && exceptionTypeArray != '') {
        for (var i = 0; i < exceptionTypeArray.length; i++) {
            exceptionTypeStr = exceptionTypeStr + exceptionTypeArray[i] + ",";
        }
    }

    formData.exceptionType = exceptionTypeStr;
    if(!formData.customerName){
        formData.customerCode = ''
    }
    // if(!formData.productName){
    //     formData.productCode = ''
    // }
    //加载总数量
    $.ajax({
        url: '/proxy-order/order/orderReturnStorage/orderReturnStorageController/selectSalesOrderReturnStorageStatisticsTotal',
        dataType: 'json',
        timeout: 10000, //6000
        data:formData,
        success: function (data) {
            // alert(data.code);
            if (data.code==0){
                var static = data.result;
                $("#taxAmountSum").text(Number(static.taxAmountSum).toFixed(2));
                $("#activityDiscountAmountSum").text(Number(static.activityDiscountAmountSum).toFixed(2));
                $("#balanceDiscountAmountSum").text(Number(static.balanceDiscountAmountSum).toFixed(2));
                $("#amountOfRebateSum").text(Number(static.amountOfRebateSum).toFixed(2));
                $("#paymentAmountSum").text(Number(static.paymentAmountSum).toFixed(2));
                $("#taxSum").text(Number(static.taxSum).toFixed(2));
                $("#noTaxCostAmountSum").text(Number(static.noTaxCostAmountSum).toFixed(2));
            }
        },
        error: function () {
            $("#taxAmountSum").text('0.00');
            $("#activityDiscountAmountSum").text("0.00");
            $("#balanceDiscountAmountSum").text("0.00");
            $("#amountOfRebateSum").text("0.00");
            $("#paymentAmountSum").text("0.00");
            $("#taxSum").text("0.00");
            $("#noTaxCostAmountSum").text("0.00");
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}