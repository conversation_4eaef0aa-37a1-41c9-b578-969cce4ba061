$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);

    /* 填入初始数据 */


    /* 审批流 */
    /**
     * 流程图显示
     */
        //根据流程实例ID加载流程图
    var processInstaId=$("#processId").val();
    initApprovalFlowChart(processInstaId);
    function  initApprovalFlowChart(processInstaId) {
        //获取审核流程数据
        $.ajax({
            type: "POST",
            url: "/proxy-storage/storage/adjustment/queryProcessHistory",
            dataType:"json",
            data:{processInstaId:processInstaId},
            success: function (data) {
                if (data.code==0&&data.result!=null){
                    console.log(data.result);
                    $('.flow').html("")
                    $('.flow').process(data.result);
                }
            },
            error: function () {}
        });
    }

    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    /* table_a */
    var colName = ['id', '商品编码','原商品编码','商品大类', '商品名称', '商品规格', '生产厂家', '产地', '单位', '库房名称', '批号', '生产日期', '有效期至', '调账数量',
         '不含税成本金额', '业务类型'
    ];
    // '不含税成本单价',
    var colModel = [
        {
            name: 'id',
            index: 'id',
            hidden:true,
            hidegrid:true

        },  {
            name: 'productCode',
            index: 'productCode'
        }, 	{
            name: 'oldProductCode',
            index: 'oldProductCode'
        },
        {
            name: 'drugClass',
            index: 'drugClass'
        }, {
            name: 'productName',
            index: 'productName'
        }, {
            name: 'specifications',
            index: 'specifications'
        }, {
            name: 'manufacturerValue',
            index: 'manufacturerValue'
        }, {
            name: 'producingArea',
            index: 'producingArea'
        }, {
            name: 'packingUnitValue',
            index: 'packingUnitValue'
        }, {
            name: 'storeName',
            index: 'storeName',
            formatter: function (e) {
                if(!re.test(e)){
                    return e;
                }
                var result = "";
                $.each(jsonStore,function(idx,item){
                    if(item.numCode == e){
                        result = item.name;
                        return false;
                    }
                });
                return result;
            }
        }, {
            name: 'batchCode',
            index: 'batchCode'
        }, {
            name: 'manufactureTime',
            index: 'manufactureTime',
            formatter: dateFormatter
        }, {
            name: 'expiryTime',
            index: 'expiryTime',
            formatter: dateFormatter
        }, {
            name: 'adjustmentNumber',
            index: 'adjustmentNumber'
        },  {
            name: 'costAmount',
            index: 'costAmount'
        }, {
            name: 'channelId',
            index: 'channelId'
        },


        ];

    // {
    //     name: 'costPrice',
    //         index: 'costPrice',
    //     formatter: function (e) {
    //     if (e == '' || e == undefined || isNaN(e)) {
    //         return '0'
    //     } else  {
    //         return Number(e).toFixed(2)
    //     }
    //
    // }
    // },

    $('#table_a').XGrid({
        url:"/proxy-storage/storage/adjustment/findDetailList",
        postData:{adjustmentApplyCode:$("#adjustmentApplyCode").val()},
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'id',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
        },
        /*gridComplete: function () {
            setTimeout(function (param) {
                /!* 合计写入 *!/
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                $(sum_ele[0]).text(totalTable(data, 'text12'));
                $(sum_ele[1]).text(totalTable(data, 'text15'));
                $(sum_ele[2]).text(totalTable(data, 'text16'));
            }, 200)
        },*/
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    };

    /* 筛选列 */
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        //var tableId = $('#nav_content .active table').attr('id')
        $('#table_a').XGrid('filterTableHead');
    })

    /* 编辑 */
    //判断是否为已驳回状态
    var indent_type = $("#status").val();
    if (indent_type == '已驳回') {
        $('#editRowData').show();
    }
    $('#editRowData').on('click', function () {
        utils.openTabs("toUpdateAdjust","调账申请单", '/proxy-storage/storage/adjustment/toEdit?id='+$("#id").val(),{},function () {
            location.reload();
        });
    });

    /* 返回 */
    $('#goback').on('click', function () {
        utils.closeTab();

    });

    /* 取消订单 */
    $('#cancel').on('click', function () {

        utils.dialog({
            title: '提示',
            width: '300',
            content: "确认取消当前订单吗?",
            cancel: false,
            okValue: '确认',
            ok: function () {
                $.ajax({
                    url:"/proxy-storage/storage/adjustment/cancelOrder",
                    type:"post",
                    dataType:"json",
                    data:{id:$("#id").val()},
                    success:function(result){
                        if(result.status == 0){
                            utils.dialog({
                                title: '提示',
                                content: result.msg,
                                width: '300',
                                cancel: false,
                                okValue: '确认',
                                ok: function () {
                                }
                            }).showModal();
                        }else if(result.status == 1){
                            utils.dialog({
                                title: '提示',
                                content: "取消订单成功",
                                width: '300',
                                cancel: false,
                                okValue: '确认',
                                ok: function () {
                                    utils.closeTab();
                                }
                            }).showModal();
                        }
                    }
                })

            },
            cancelValue: '取消',
            cancel: function () {

            }

        }).showModal();




    });
})
//页面打印
var codeNum  = $("#adjustmentApplyCode").val();

$("#printAdjustApply").on("click",function () {
    utils.dialog({
        content:"正在打印...",
        timeout:1000,
    }).showModal();
    setTimeout(function () {
        $("#print_box")[0].contentWindow.getData(0,codeNum);
    },0)
});
