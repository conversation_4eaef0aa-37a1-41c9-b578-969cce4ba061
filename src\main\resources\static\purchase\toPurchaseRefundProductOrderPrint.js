﻿ var getData;
 $(function () {
     /* 模拟数据 */
     /*var data_a = [{
         productCode: "Y010101591",
         productName: "西地碘含片(华素片)",
         size: "1.5毫克*15",
         dosage: "片剂",
         enterprise: "北京华素制药股份有限公司",
         from: "北京",
         unit: "盒",
         number: "10",
         price: "7.300",
         sum: "73.00",
         sterilizationBatch: "",
         batch: "1805072",
         productionDate: "2018-04-30",
         effectiveDate: "2020-04-30",
         storageName: "合格库",
         locationName: "合格库",
         approvalNumber: "国药准字H10910012",
         quality: "合格",
     }, {
         productCode: "Y010101591",
         productName: "西地碘含片(华素片)",
         size: "1.5毫克*15",
         dosage: "片剂",
         enterprise: "北京华素制药股份有限公司",
         from: "北京",
         unit: "盒",
         number: "10",
         price: "7.300",
         sum: "73.00",
         sterilizationBatch: "",
         batch: "1805072",
         productionDate: "2018-04-30",
         effectiveDate: "2020-04-30",
         storageName: "合格库",
         locationName: "合格库",
         approvalNumber: "国药准字H10910012",
         quality: "合格",
     }];

     var data_b = [{
         indentCode: "YBM20180817102154100237",
         carriage: "0",
         salesSum: "1161.25",
         couponSum: "100",
         paySum: "1061.25",
         paySumC: "壹仟零陆拾壹元贰角伍分",
         isOnlinePay: "已支付",
         payType: "A"
     }];

     var data_c = [{
         key: "发票情况",
         value: [],
         other: "",
     },
         {
             key: "结款情况",
             value: [],
             other: "缓存区名称：Y21-56--Y21-56",
         },
         {
             key: "结款方式",
             value: [3],
             other: "",
         },
         {
             key: "备注",
             value: "急用",
             other: "",
         },
     ];*/
     /* 日期格式化 */
     //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
     function dateFormat(time, format) {
         var t = new Date(parseInt(time,10));
         var tf = function (i) {
             return (i < 10 ? '0' : '') + i;
         };
         return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
             switch (a) {
                 case 'yyyy':
                     return tf(t.getFullYear());
                     break;
                 case 'MM':
                     return tf(t.getMonth() + 1);
                     break;
                 case 'mm':
                     return tf(t.getMinutes());
                     break;
                 case 'dd':
                     return tf(t.getDate());
                     break;
                 case 'HH':
                     return tf(t.getHours());
                     break;
                 case 'ss':
                     return tf(t.getSeconds());
                     break;
             }
         });
     }



     /* 获取数据 */
     getData = function (printType,outOrderCode) {
         var outOrderCodes = outOrderCode.join(',');
         $.ajax({

             url:"/proxy-purchase/purchase/purchaseRefundProductOrder/findPurchaseRefundProductOrderPrintList",
             data:{
                 purchaseRefundProductOrderNoList :  outOrderCodes,
                 printType: printType
             },

             success:function(res){
                 if(res.result==""||res.result==null){
                     utils.dialog({
                         title:'提示',
                         content:"数据为空或格式不正确",

                     }).showModal();
                     return ;
                 }
                 if($.isArray(res.result)){
                     webRender(res.result,printType);
                 }else {
                     utils.dialog({
                         title:'提示',
                         content:"数据为空或格式不正确",

                         ok:function () {}
                     }).showModal();
                 }
             },
             error:function(){

             }
         })
     }

        var excludeSpecial = function(s) {
             // 去掉转义字符
             s = s.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
             // 去掉特殊字符
             s = s.replace(/\<|\>|\"|\'|\&/g,'');
             return s;
          };

     /* 数据渲染 */
     function webRender(data,printType) {
         var box_html = '';
         //var companyName = $("#companyName").val();
         //var companyName = data[0].companyName;
         /* 基本结构拼装 */
         data.forEach(function (item,index) {
             var ifSpecialDrugs= item.ifSpecialDrugs;
             var titles = "购进退出复核单(随货同行)";
             if(ifSpecialDrugs != null && ifSpecialDrugs != '' && ifSpecialDrugs == 1){
                 titles = "购进退出复核单(随货同行、特殊药品)";
             }
             /* 销售出库复核单 */
            box_html +=`
            <div class="content indent1">
             <div class="header">
                 <div class="title">`+item.companyName+titles+`</div>

             </div>
             <div class="top">
                 <ul class="info_list">

                     <li>
                         <span class="col_8">单位编号：<i class="val">${item.supplierCode}</i></span>
                         <span class="col_2" style="margin-left:-26px;"> </span>
                     </li>
                     <li>
                         <span class="col_8">供货单位：<i class="val">${item.supplierName}</i></span>
                         <span class="col_2" >单据编号：<i class="val">${item.orderRefundProductNo}</i></span>
                     </li>
                     <li>
                         <span class="col_4">收货地址：<i class="val">${item.supplyStoreAddress}</i></span>
                         <span class="col_2">采购员：<i class="val">${item.storeUser}</i></span>
                         <span class="col_2">开票员：<i class="val">${item.createUser}</i></span>
                         <span class="col_2">发货日期：<i class="val">${(item.realOutTimeFormat)}</i></span>

                     </li>
                 </ul>
                 <span class="inden_type">退</span>
             </div>
             <table id="table_a_${index}"></table>
             <div class="bottom">
                 <ul class="info_list">
                     <li >

                         <span class="col_2">保管员：<i class="val"> </i></span>
                         <span class="col_2">复核员：<i class="val">${item.checkName==null?'':item.checkName}</i></span>
                         <span class="col_2">共<i class="val">${item.pageTotal}</i>页，第<i class="val">${item.pageNumber}</i>页</span>
                         <span class="col_4" style="padding-right: 20px; text-align:right;"> 白联：仓储 红联：财务部 黄联：随货同行</span>
                     </li>

                 </ul>
             </div>
             <div style="page-break-after:always"></div>
         </div>
            `;

         });

   $("#box").html(box_html);

         /* 表格初始化 */
        data.forEach(function (item,index) {
             item.purchaseRefundProductOrderProductPrintDtos = item.purchaseRefundProductOrderProductPrintDtos.map(function (val,key) {
                 delete val.id;
                 return val
             });
             /* 销售出库复核单 */
             $("#table_a_"+index).jqGrid({
                 data: item.purchaseRefundProductOrderProductPrintDtos,
                 datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                 height: "auto", //高度，表格高度。可为数值、百分比或'auto'

                 colNames: ['商品名称','规格/型号','生产企业','单位','单价','数量','金额','批号/序列号','生产日期','有效期至','库房名称','货位名称','注册证号/备案号', '质量'],
                 colModel: [{
                                    index: 'productName',
                                    name: 'productName',
                                    width: 120,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        if (rowId == 14) {
                                            //金额合计(大写) name
                                            return 'colspan=2'
                                        }
                                    }
                                }, {
                                    index: 'productSpecification',
                                    name: 'productSpecification',
                                    width: 130,
                                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        //console.log(rowId, tv, rawObject, cm, rdata);
                                        if (rowId == 14) {
                                            //金额合计(大写) value
                                            return 'colspan=4 style="text-align: left;padding-left:5px;"'

                                        }else if (rowId == 13) {

                                            return 'colspan=3 style="text-align: left;padding-left:5px;"'
                                       }
                                    }
                                }, {
                                    index: 'productProduceFactory',
                                    name: 'productProduceFactory',
                                    width: 170,

                                      formatter:function (e) {
                                         if(e){
                                             return excludeSpecial(e);
                                         }else{
                                             return ""
                                         }
                                     }
                                }, {
                                    index: 'productPackUnitSmall',
                                    name: 'productPackUnitSmall',
                                    width: 40,
                                     cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                        if (rowId == 14) {
                                             return 'colspan=8 style="text-align: left;padding-left:5px;"'
                                        }
                                    },
                                }, {
                                    index: 'productContainTaxPrice',
                                    name: 'productContainTaxPrice',
                                    width: 70,
                                     formatter:function (e) {
                                        if(e){
                                          return parseFloat(e).toFixed(3);
                                        }else{
                                          return ""
                                        }
                                    }

                                }, {
                                    index: 'realOutCount',
                                    name: 'realOutCount',
                                    width: 70,
                                     formatter:function (e) {
                                         if(e){
                                             return parseFloat(e).toFixed(2);
                                         }else{
                                             return ""
                                         }
                                     }

                                }, {
                                    index: 'realRefundContainTax',
                                    name: 'realRefundContainTax',
                                    width: 120,
                                     formatter:function (e) {
                                         if(e){
                                             return parseFloat(e).toFixed(2);
                                         }else{
                                             return ""
                                         }
                                     }

                                }, {
                                    index: 'productBatchNo',
                                    name: 'productBatchNo',
                                    width: 120,
                                    summaryType: function (value, name, record) {
                                        console.log(value, name, record);

                                        return value
                                    }
                                }, {
                                    index: 'productProduceDate',
                                    name: 'productProduceDate',
                                    width: 90,
                                }, {
                                    index: 'productExpireDate',
                                    name: 'productExpireDate',
                                    width: 90,
                                }, {
                                    index: 'storeroomName',
                                    name: 'storeroomName',
                                    width: 70,
                                }, {
                                    index: 'shelfLocationName',
                                    name: 'shelfLocationName',
                                    width: 70,
                                }, {
                                    index: 'productApprovalNumber',
                                    name: 'productApprovalNumber',
                                    width: 170,


                                } , {
                                    index: 'checkEvaluate',
                                    name: 'checkEvaluate',
                                    width: 60,

                                }],
                                shrinkToFit: true,
                                rowNum: 12,
                                gridview: true,
                                gridComplete: function () {
                                    var sum_number = $(this).getCol('realOutCount', false, 'sum');
                                    var sum_sum = $(this).getCol('realRefundContainTax', false, 'sum');
                                    var data = $(this).getRowData();
                                    //console.log(data);
                                    if (data.length < 12) {
                                        $(this).addRowData(data.length + 1, {}, "last");
                                    } else if (data.length == 12) {
                                        $(this).addRowData(13, {
                                            productName: "小计",
                                            productPackUnitSmall: parseFloat(sum_number).toFixed(2),
                                           productContainTaxPrice : parseFloat(sum_sum).toFixed(2),

                                        }, "last");
                                    } else if (data.length == 13) {
                                        $(this).addRowData(14, {
                                            productName: "金额合计(大写)：",
                                            productSpecification: item.totalRealPayAmountDesc,
                                            productProduceFactory: "金额合计：",
                                           productPackUnitSmall: "￥："+parseFloat(item.actualRefundContainTax).toFixed(2)+"元",
                                        }, "last");
                                    }
                                }
                            });
         });

         if(printType==0){
             /* 打印预览 */
             utils.dialog({
                 title:'预览',
                 content:$('#big_box').html(),


             }).showModal();
             //$("#print_box").contents().find(".ui-dialog-content").css("overflow","auto");
             window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
         }else if(printType==1){
             /* 打印 */
             $("#box").jqprint({
                 globalStyles: true, //是否包含父文档的样式，默认为true
                 mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                 stylesheet: null, //外部样式表的URL地址，默认为null
                 noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                 iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                 append: null, //将内容添加到打印内容的后面
                 prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                 deferred: $.Deferred() //回调函数
             });
         }
     }
 });