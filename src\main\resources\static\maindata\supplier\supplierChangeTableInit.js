var treeCheckArr = []; // 树形   数据 数组
var idArr=[];//批准文件经营范围已选中存放数组
var zTree=null;//树对象

$('div[fold=head]').fold({
    sub: 'sub'
});
$('.nav-tabs>li').on('click', function (){
    var $this = $(this),
        $nav_content = $this.parent().next();
    $this.addClass('active').siblings().removeClass('active');
    $nav_content.children('div').eq($this.index()).show().siblings().hide();
});
//初始化仓库地址回显 需要给select加 data-value 属性
$('.depotList').each(function(){
    //省
    var val1=$.trim($(this).find("select").eq(0).attr("data-value"));
    if(val1 && val1 != '')
    {
        $(this).find("select").eq(0).val(val1);
        $(this).find("select").eq(0).change();
    }
    //市
    var val2=$.trim($(this).find("select").eq(1).attr("data-value"));
    if(val1 && val1 != '')
    {
        $(this).find("select").eq(1).val(val2);
        $(this).find("select").eq(1).change();
    }
    //区
    var val3=$.trim($(this).find("select").eq(2).attr("data-value"));
    if(val1 && val1 != '')
    {
        $(this).find("select").eq(2).val(val3);
    }
});
//付款、结算方式点击复选框显示输入框
$(document).on("change", ".parentCode", function () {
    var checked = this.checked;
    var code = $(this).val();
    let fkss = '.'+ code;
    if (checked) {
        $(this).parents('.paymentSettlement').find('.childCode').show();
        addSettleMultiple(code.toString())
        $(this).parents('.paymentSettlement').find('.childCode').show();
        $(this).parents(".paymentSettlement").find(fkss).removeClass("displaynone");
    }else{
        $(this).parents('.paymentSettlement').find('.childCode').hide();
        clearSettleMultiple(code.toString())
        $(this)
        .parents(".paymentSettlement")
        .find(fkss)
        .addClass("displaynone");
        removeAllChilds(code);
    }
    if ($(this).val() === '2007'){
        $('.parentCode').each(function () {
            if($(this).val() === '2002'){
                $(this).attr('checked',false)
                $(this).parents('.paymentSettlement').find('.childCode').hide();
            }
        })
    }
    if ($(this).val() === '2002'){
        $('.parentCode').each(function () {
            if($(this).val() === '2007'){
                $(this).attr('checked',false)
                $(this).parents('.paymentSettlement').find('.childCode').hide();
            }
        })
    }
    if ($(this).val() === '2008'){
        $('.parentCode').each(function () {
            if($(this).val() === '2003'){
                $(this).attr('checked',false)
                $(this).parents('.paymentSettlement').find('.childCode').hide();
            }
        })
    }
    if ($(this).val() === '2003'){
        $('.parentCode').each(function () {
            if($(this).val() === '2008'){
                $(this).attr('checked',false)
                $(this).parents('.paymentSettlement').find('.childCode').hide();
            }
        })
    }
    clickFunctions();
})
//初始化付款、结算方式显示输入框
initPaymentSettlement();
//客户委托书授权类型变化
$('#table2').on('change','.grantType', function (ev) {
    var grantType = $(this).find('option:selected') .val();//选中的值
    var $tr=$(this).parents("tr");
    if("0"==grantType){//选择商品
        $tr.find(".commodity").text("").text('选择商品');
    }else if("1"==grantType){//经营范围授权
        $tr.find(".commodity").text("").text('经营范围');
    }

    //2018.9.3,RL,begin
    // else if("2"==grantType){
    //     $tr.find(".commodity").text("").text('选择剂型');
    // }
    //2018.9.3,RL,end

    //2018.9.6,RL,begin
    else {
        $tr.find(".commodity").text("");
    }
    //2018.9.6,RL,end

}) ;
//客户委托书被委托人发生变化
$('#table2').on('keyup','.mandataryName:first', function (ev) {
    var mandataryName = $.trim($(this).val());//选中的值
    if(mandataryName !=""){
        $("input[name='mandatary']").val(mandataryName);
    }
});

//客户委托书被委托人电话发生变化
$('#table2').on('keyup','.mandataryTel:first', function (ev) {
    var mandataryTel = $.trim($(this).val());//选中的值
    $("input[name='mandataryPhone']").val(mandataryTel);
});
//付款、结算只可输入数字
$(document).on('input','.cValue',function(){
    // var max=$.trim($(this).attr("data-max"));
    // if(max && max != ''){
    //     $(this).val($(this).val().replace(/[^123456789]/g,''));
    //     var value=$.trim($(this).val());
    //     if(Number(value) >=  Number(max))
    //     {
    //         $(this).val(max);
    //         return ;
    //     }
    // }else{
    //     $(this).val($.trim($(this).val()).replace(/\D/g,''));
    // }
});
//运营属性—》客户委托书：点击选择商品或经营范围
var resultArr=[];//新增，编辑状态临时存放已选中的数据,品种授权弹框内用到
var selectArr=[];//存放选中数据id，做回显选中处理，品种授权弹框内用到
$('body').on('click','.commodity', function (ev) {
    var $td=$(this).closest('td');
    var $tr=$(this).closest("tr");
    var $table=$(this).closest('table');
    var trId=$tr.attr("id");
    var grantType =$tr.find('.grantType option:selected').val();
    if("0"==grantType){//选择商品
        let count = $(this).attr('data-openStatus')
        let _this = $(this)
        let flag = window.changeApplyList && window.changeApplyList.supplierClientProxyOrderList && window.changeApplyList.supplierClientProxyOrderList.valueAfter
        let _type = $(this).parents('table').eq(0).attr('id') == 'changeApplyTable' ? '9' : '1'
        count = ($(this).parents('table').eq(0).attr('id') == 'changeApplyTable' ? ( (flag && !count) ? 0 : 1) : (count ? count : 0))
        var selectedProductLists = [];
        //获取已选择的商品列表
        $.ajax({
            url:'/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/getClientProxyProduct',
            type:'post',
            async:false,
            data:{
                "clientOderId":trId,
                "type": _type
            },
            dataType:'json',
            success:function(data){
                // if ("" != data.list) {
                //     selectedProductIds = data.list;
                // }
                if (0 == data.code) {
                    selectedProductLists = data.result;
                }
            }
        });
        //临时存放已选中数据
        var temporary=$td.attr('data-resultArr');
        resultArr=[];
        if(temporary && temporary != '[]' &&   typeof temporary === 'string')
        {
            resultArr=JSON.parse(temporary);
        }
        //获取已选商品id
        var selRowData=$table.getRowData(trId).supplierClientProxyProductVOList;
        selectArr=[];
        if(selRowData && selRowData.length > 0){
            for(var i=0;i<selRowData.length;i++){
                selectArr.push(selRowData[i].productCode);
            }
        }
        //跳转到选择商品页面
        utils.dialog({
            width: $(window).width() * 0.7,
            height:  $(window).height() * 0.6,
            title: '选择商品',
            url: '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/getProductList',
            data: {
                "selectedProductIds": selectedProductLists,
                'selectArr': selectArr,
                selRowData,
                resultArr,
                isEdit:  $('#pageProperty').length == 0  ? $('#isEdit').val() : ($('#pageProperty').val() == 'detail' ? '0' : '1'),
                count
            },
            onclose: function() {
                if(this.returnValue){
                    _this.attr('data-openStatus', 1)
                    const arr = this.returnValue
                    //详情页设置不可更改
                    if ($('#isEdit').val() == '1') {
                        // var arr = $('#selected_Tableb').getRowData();
                        $td.attr('data-resultArr', JSON.stringify(arr));
                        if(arr.length > 0){
                            var productIdArr=[];
                            for(var i=0;i<arr.length;i++){
                                var productCode=arr[i].id;
                                productIdArr.push(Object.assign({...arr[i]}, { productCode:productCode}))
                                // productIdArr.push({
                                //     productCode:productCode
                                // })
                            }
                            //添加客户委托书对应的商品
                            if(trId){
                                $table.XGrid('setRowData',trId,{supplierClientProxyProductVOList:productIdArr});
                            }
                        }else{
                            $table.XGrid('setRowData',trId,{supplierClientProxyProductVOList:[]});
                        }
                    }
                }
            }
        }).showModal();
        //详情页设置隐藏可选数据
        if($('#isEdit').val() == '0') {
            $('#isEditHTML').hide();
        }
    }else if("1"==grantType){//经营范围授权
        //alert("经营范围");
        var htm = $('#operScopeZtree_01');
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querycommodityscope',
            data:{
                orgCode:'001',
                scopeName:''
            },
            type:"post",
            dataType:'json',
            success:function(data){
                console.log(data)
                pulicTree(data.result,$('.operScopeZtree_01'))
                utils.dialog({
                    title: '经营范围',
                    width: 500,
                    content: htm,
                    okValue: '确定',
                    ok: function () {
                        //设置不可更改 0不可编辑 1可编辑
                        if($('#isEdit').val() == '1') {
                            var data = zTree.getCheckedNodes(true);
                            console.log(data);
                            var arr = [];
                            if (data && data.length > 0) {
                                for (var i = 0; i < data.length; i++) {
                                    if (data[i].name != '经营范围') {
                                        arr.push({
                                            businessScopeCode: data[i].id
                                        })
                                    }
                                }
                            }
                            //保存经营范围数据
                            $table.XGrid('setRowData', trId, {supplierClientProxyBusinessScopeVOList: JSON.stringify(arr)});
                        }
                    },
                    cancelValue: '取消',
                    cancel: function () {
                    },
                    onclose:function(){

                    }
                }).showModal();
                //trId
                var rowData=$table.XGrid('getRowData',trId);
                var checkValus=rowData.supplierClientProxyBusinessScopeVOList;
                console.log(rowData);
                var tree = $.fn.zTree.getZTreeObj("operScopeZtreeul");//文件树
                // if(checkValus.length){
                    //设置不可更改 0不可编辑 1可编辑
                    // if($('#isEdit').val() == '0') {

                    // }
                    //循环设置是否选中已存在id
                    var checkValus=typeof checkValus=='string'? JSON.parse(checkValus):checkValus;
                    for(var i=0;i<checkValus.length;i++){
                        var item=checkValus[i];
                        if (zTree.getNodeByParam( "id",Number(item.businessScopeCode))) {
                            tree.checkNode(zTree.getNodeByParam( "id",Number(item.businessScopeCode)) , true);
                        }
                    }
                    var nodes=tree.getNodes();
                    chkDis(nodes[0],tree,true);
                // }
            },
            error:function(){

            }

        })
    }

    //2018.9.3 15:27,rl,新增剂型，begin
    // else if("2"==grantType){//剂型
    //     $.ajax({
    //         url:'/proxy-sysmanage/sysmanage/dict/querydosenotpage',
    //         data:{
    //             doseName:"",
    //             isStop:0
    //         },
    //         type:'post',
    //         dataType:'json',
    //         success:function (resp) {
    //             utils.dialog({
    //                 title: '剂型',
    //                 width: 800,
    //                 height:500,
    //                 content: bindDrugDialogHtml(resp),
    //                 okValue: '保存',
    //                 cancelValue: '取消',
    //                 onshow:function () {
    //                     $(this.node).find('.ui-dialog-content').css({"overflow-y":'scroll'});
    //
    //                     //回显checkbox值
    //                     showDrugCheckboxValue(trId,$table);
    //                 },
    //                 ok: function () {
    //                     //保存按钮回调
    //                     saveDrugCheckboxValue(trId,$table);
    //                 },
    //                 cancel: function () {
    //                 },
    //                 onclose:function(){
    //
    //                 },
    //             }).showModal();
    //         },
    //         error:function(){
    //
    //         }
    //     })
    // }
    //2018.9.3 15:27,rl,新增剂型，end

    else if(""==grantType){ // 授权类型还没有 选中有效值
        utils.dialog({
            title:'提示',
            content:'请先选择授权类型。',
            okValue:'确定',
            ok:function () {}
        }).showModal();

    }
});

//2018.9.3 15:43,rl,dialog保存回调,begin
function saveDrugCheckboxValue(trId,$table){
    var checkedDrugIdList=[];
    var drugChkboxList=$("#selectDrugDialog_02 ul li input[type=checkbox]");
    if(drugChkboxList && drugChkboxList.length){
        $.each(drugChkboxList,function (i, v) {
            var $v=$(v);
            if($v.prop('checked')){
                //checkedDrugIdList.push($v.parents('li').attr('id'));
                checkedDrugIdList.push({"jixingId":$v.parents('li').attr('id')});
            }
        });
    }
    console.log(checkedDrugIdList);
    //保存选择的剂型
    //$("#table2").XGrid('setRowData', trId, {supplierClientProxyTypeVOList: checkedDrugIdList});
    if($("#isEdit").val()==0){
        //不可编辑
        //所有的checkbox禁用
        //不保存值
    }else if($("#isEdit").val()==1){
        //0不可编辑，1可编辑
        $table.XGrid('setRowData', trId, {supplierClientProxyTypeVOList: checkedDrugIdList});
    }

}
//2018.9.3 15:43,rl,dialog保存回调,end

//2018.9.3 15:43,rl,dialog回显checkbox,begin
function showDrugCheckboxValue(trId,$table){
    //读隐藏列,回显已经选择好的值
    //var rowData=$("#table2").XGrid('getRowData',trId).supplierClientProxyTypeVOList;
    var rowData=$table.XGrid('getRowData',trId).supplierClientProxyTypeVOList;
    if(rowData && rowData.length){
        var drugChkboxUlList=$("#selectDrugDialog_02 ul");
        var targetLi=void 0;
        $.each(rowData,function(i,v){
            targetLi=drugChkboxUlList.find('li[id='+v['jixingId']+']');
            if(targetLi){
                targetLi.find('input[type=checkbox]').prop('checked',true);//.attr("data-disabled",false);
            }
        });

        if($("#isEdit").val()==0){
            //不可编辑
            //所有的checkbox禁用
            drugChkboxUlList.find('input[type=checkbox]').prop('disabled',true);
        }else if($("#isEdit").val()==1){
            drugChkboxUlList.find('input[type=checkbox]').prop('disabled',false);
        }
    }
}
//2018.9.3 15:43,rl,dialog回显checkbox,end

//2018.9.3 15:27,rl,新增剂型，begin
function bindDrugDialogHtml(drugJson){
    var drugCheckboxHtml=[];

    drugCheckboxHtml.push('<div id="selectDrugDialog_02">');
    drugCheckboxHtml.push('<ul class="list-inline">');
    if(drugJson && drugJson.result && drugJson.result.length){
        for(var i=0,l=drugJson.result.length;i<l;i++){
            drugCheckboxHtml.push('<li style="width:20%;" id="'+drugJson.result[i]['doseid']+'" >');
            drugCheckboxHtml.push('<div class="checkbox">');
            drugCheckboxHtml.push('<label>');
            drugCheckboxHtml.push('<input type="checkbox"> '+drugJson.result[i]['dosename']);
            drugCheckboxHtml.push('</label>');
            drugCheckboxHtml.push('</div>');
            drugCheckboxHtml.push('</li>');
        }
    }else{
        drugCheckboxHtml.push('<li>暂无数据!</li>');
    }
    drugCheckboxHtml.push('</ul>');
    drugCheckboxHtml.push('</div>');

    return drugCheckboxHtml.join('');
}
//2018.9.3 15:27 17:27,rl,新增剂型，end

//初始化显示隐藏付款、结算方式输入框
function initPaymentSettlement() {
    $(".parentCode").each(function () {
        var checked = this.checked;
        if (checked) {
            $(this).parents('.paymentSettlement').find('.childCode').show();
        } else {
            $(this).parents('.paymentSettlement').find('.childCode').hide();
        }
    })
}
/**
 * 禁止批准文件内经营范围树可选
 *@param obj  Object 树节点对象
 * @param zTree Object 树对象
 * @param checked  boolean 是否禁止选则  true为禁止
 * */
function chkDis(obj,zTree,checked){
	if(!obj) return;
    zTree.setChkDisabled(zTree.getNodeByParam( "id",obj.id), checked);
    var children=obj.children;
    if(children && children.length > 0)
    {
        for(var i=0;i<children.length;i++)
        {
            zTree.setChkDisabled(zTree.getNodeByParam( "id",children[i].id), checked);
            chkDis(children[i],zTree,checked);
        }
    }
}
//质量保证协议
function initTable1(supplierOrganBaseId,type){
    var xGridData=[];
    $.ajax({
        url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getQualityAgreementList',
        data:{"correlationId":supplierOrganBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            if(data.result.list!=null&& data.result.list.length>0){
                var dataList = data.result.list;
                for(var i=0;i<dataList.length;i++){
                    var a={
                        id: dataList[i].id,
                        signDate: dataList[i].signDate,
                        validDate: dataList[i].validDate,
                        signName: dataList[i].signName,
                        enclosureCount:dataList[i].enclosureCount,
                        enclosureList:dataList[i].enclosureList
                    }
                    xGridData.push(a);
                }
            }
            if(xGridData.length<1){
                xGridData={
                    id: "",
                    signDate: "",
                    validDate: "",
                    signName: "",
                    enclosureCount:"",
                    enclosureList:[]
                }
            }
            xGridTable1(xGridData);
        }
    });
}
function xGridTable1(xGridData){
    $('#table1').XGrid('setGridParam',{data:xGridData}).trigger('reloadGrid');
}
$('#table1').XGrid({
    data: [],
    colNames: ['', '<i class="i-red">*</i>签订日期', '<i class="i-red">*</i>有效期至', '<i class="i-red">*</i>签订人', '附件','<i class="i-red">*</i>附件数据'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        index: 'id', //索引。其和后台交互的参数为sidx
        hidden:true,
        key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    }, {
        name: 'signDate',
        index: 'Signdata',
        rowtype: '#grid_BeginDate',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'validDate',
        index: 'vld',
        rowtype: '#grid_endDate',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'signName',
        index: 'signName',
        rowtype: '#grid_person'
    }, {
        name: 'enclosureCount',
        index: 'enclosureCount',
        formatter:function (value) {
            var str='无';
            if(value)
            {
                str ='<a href="javascript:;" onclick="showImg(this);">'+value+'</a>';
            }
            return str;
        },
        unformat: function (e) {
            e=e.replace(/<[^>]+>/g,'');
            if(e == '无'){
                e = 0;
            }
            return e;
        }
    }, {
        name: 'enclosureList',
        index: 'enclosureList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return JSON.stringify(value);
            }
            return JSON.stringify([]);
        },
        unformat: function (value) {
            return JSON.parse(value);
        }
    }],
    rowNum: 100,
    rownumbers: true,//是否展示序号
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_page1',
    onSelectRow: function (id, dom, obj, index, event) {
    }
});
//客户委托书
function initTable2(supplierOrganBaseId,type){
    var xGridData=[];
    $.ajax({
        url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getClientProxyOrderList',
        data:{"correlationId":supplierOrganBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            console.log(data)
            if(data.result.list!=null&& data.result.list.length>0){
                var dataList = data.result.list;
                for(var i=0;i<dataList.length;i++){
                    var a={
                        id: dataList[i].id,
                        proxyOderNo: dataList[i].proxyOderNo,
                        mandataryName: dataList[i].mandataryName,
                        mandatarySex: dataList[i].mandatarySex,
                        mandataryTel: dataList[i].mandataryTel,
                        mandataryCertificateNumber: dataList[i].mandataryCertificateNumber,
                        mandataryAddress: dataList[i].mandataryAddress,
                        identityValidDate: dataList[i].identityValidDate,
                        proxyValidDate: dataList[i].proxyValidDate,
                        authorityType: dataList[i].authorityType,
                        enclosureCount:dataList[i].enclosureCount,
                        supplierClientProxyProductVOList:dataList[i].supplierClientProxyProductVOList,
                        supplierClientProxyBusinessScopeVOList:dataList[i].supplierClientProxyBusinessScopeVOList,
                        enclosureList:dataList[i].enclosureList

                        //2018.9.3 15:36,RL,新增剂型,begin
                        ,supplierClientProxyTypeVOList:dataList[i].supplierClientProxyTypeVOList
                        // 2018.9.3 15:36,RL,新增剂型,end
                    }
                    xGridData.push(a);
                }

            }
            if(xGridData.length<1){
                xGridData={
                    id: "",
                    proxyOderNo: "",
                    mandataryName: "",
                    mandatarySex: "",
                    mandataryTel: "",
                    mandataryCertificateNumber: "",
                    mandataryAddress: "",
                    identityValidDate: "",
                    proxyValidDate: "",
                    authorityType: "",
                    enclosureCount:"",
                    supplierClientProxyProductVOList:[],
                    supplierClientProxyBusinessScopeVOList:[],
                    enclosureList:[]

                    //2018.9.3 15:36,RL,新增剂型,begin
                    ,supplierClientProxyTypeVOList:[]
                    // 2018.9.3 15:36,RL,新增剂型,end
                }
            }
            xGridTable2(xGridData);
        }
    });
}
function xGridTable2(xGridData) {
    $('#table2').XGrid('setGridParam',{data:xGridData}).trigger('reloadGrid');
}
$('#table2').XGrid({
    data: [],
    colNames: ['','数据', '<i class="i-red">*</i>委托书编号', '<i class="i-red">*</i>被委托人', '<i class="i-red">*</i>性别', '<i class="i-red">*</i>电话', '<i class="i-red">*</i>证件号码', '地址', '<i class="i-red">*</i>身份证有效期至', '<i class="i-red">*</i>委托书有效期至', '<i class="i-red">*</i>授权类型',
        '附件','<i class="i-red">*</i>授权范围','附件数据','经营范围数据','剂型'
    ],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        index: 'id', //索引。其和后台交互的参数为sidx
        hidden:true,
        key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    }, {
        name:'supplierClientProxyProductVOList',
        index:'',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return JSON.stringify(value);
            }
            return JSON.stringify([]);
        },
        unformat: function (value) {
            if(value)
            {
                return JSON.parse(value);
            }
            return [];
        }
    },{
        name: 'proxyOderNo',
        index: 'proxyOderNo',
        rowtype: '#entrustCode'
    }, {
        name: 'mandataryName',
        index: 'mandataryName',
        rowtype: '#entrustPerson'
    }, {
        name: 'mandatarySex',
        index: 'mandatarySex',
        rowtype: '#sexType'
    }, {
        name: 'mandataryTel',
        index: 'mandataryTel',
        rowtype: '#phoneP'
    }, {
        name: 'mandataryCertificateNumber',
        index: 'mandataryCertificateNumber',
        rowtype: '#crteP'
    }, {
        name: 'mandataryAddress',
        index: 'mandataryAddress',
        rowtype: '#posP'
    }, {
        name: 'identityValidDate',
        index: 'identityValidDate',
        rowtype: '#crteTerm',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'proxyValidDate',
        index: 'proxy_vld',
        rowtype: '#entrustTerm',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'authorityType',
        index: 'authorityType',
        rowtype: '#grantType'
    }, {
        name: 'enclosureCount',
        index: 'enclosureCount',
        formatter:function (value) {
            var str='无';
            if(value)
            {
                str ='<a href="javascript:;" onclick="showImg(this);">'+value+'</a>';
            }
            return str;
        },
        unformat: function (e) {
            e=e.replace(/<[^>]+>/g,'');
            if(e == '无'){
                e = 0;
            }
            return e;
        }
    }, {
        name:'authrange',
        index: 'authrange',
        formatter:function (value,id,rowData) {
            if(Number(rowData.authorityType) == 1)
            {
                return '<a href="javascript:;" class="commodity">经营范围</a>';
            }
            //2018.9.3 15:38,RL,begin
            // else if(Number(rowData.authorityType) == 2){
            //     return '<a href="javascript:;" class="commodity">选择剂型</a>';
            // }
            //2018.9.3 15:38,RL,end

            //2018.9.6,RL,begin
            else if(Number(rowData.authorityType) == 0){
                return '<a href="javascript:;" class="commodity">选择商品</a>';
            }
            //2018.9.6,RL,begin

            else{
                return '<a href="javascript:;" class="commodity"></a>';
            }
        },
        unformat: function (e) {
            return '';
        }
    }, {
        name: 'enclosureList',
        index: 'enclosureList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return JSON.stringify(value);
            }
            return JSON.stringify([]);
        },
        unformat: function (value) {
            return JSON.parse(value);
        }
    },{
        name:'supplierClientProxyBusinessScopeVOList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return JSON.stringify(value);
            }
            return JSON.stringify([]);
        },
        unformat: function (value) {
            return JSON.parse(value);
        }
    },{
        name:'supplierClientProxyTypeVOList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return JSON.stringify(value);
            }
            return JSON.stringify([]);
        },
        unformat: function (value) {
            return JSON.parse(value);
        }
    }],
    rowNum: 100,
    rownumbers: true,//是否展示序号
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_page2',
    onSelectRow: function (id, dom, obj, index, event) {
    },

    //2018.9.4,RL,begin
    gridComplete:function(){
        if($("[name=supplierTypeId]").val()!="55"){
            var $table=$(this);
            var $drug_option_dom=$table.find('.optDrugFlag');
            if($drug_option_dom && $drug_option_dom.length){
                $drug_option_dom.hide();
            }
        }
    }
    //2018.9.4,RL,end
});
//批准文件
function initTable3(supplierBaseId,type,$table){
    idArr=[];
    console.log(2)
    $.ajax({
        url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getApprovalFileList',
        data:{"correlationId":supplierBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            console.log(data);
            handleData(data,$table);
        }
    });
}
function handleData(data,$table){
    var xGridData=[];
    if($table){
    	window.idArr_=[];
    }
    if(data.result.list!=null&& data.result.list.length>0){
        var dataList = data.result.list;
        for(var i=0;i<dataList.length;i++){
        	 var zTreeNodes=[];
        	if(dataList[i].scopeofoperationVo){
                zTreeNodes = [{
                    id: 0,
                    name: "经营范围",
                    // open: true,//展开
                    children:[]
                }]
        	}

            console.log(dataList[i].scopeofoperationVo)
            if(dataList[i].scopeofoperationVo && dataList[i].scopeofoperationVo.length > 0){
                var scopeofoperationVo=dataList[i].scopeofoperationVo;
                console.log(scopeofoperationVo)
                scopeofoperationVo = typeof scopeofoperationVo=="string"? JSON.parse(scopeofoperationVo):scopeofoperationVo;
                $(scopeofoperationVo).each(function(index,item){
                    var _obj = {};
                    _obj.id = item.scopeId;
                    _obj.name = item.scopeName;
                    _obj.children = [];
                    $(item.children).each(function(cindex,citem){
                        var _obj_child = {};
                        _obj_child.id = citem.scopeId;
                        _obj_child.name = citem.scopeName;
                        _obj.children.push(_obj_child)
                    })
                    zTreeNodes[0].children.push(_obj)
                })
            }
            console.log(zTreeNodes)
            var certJson={};
            certJson[dataList[i].certificateId]=dataList[i].supplierApprovalFileBusinessScopeVOList;
            certJson['trId']=dataList[i].id;

            //在初始化时和查阅变更时，需要区分全局变量
            if($table){
            	idArr_.push(certJson);
            }else{
            	idArr.push(certJson);
            }

            console.log(dataList[i].certificateId)
            var a={
                id: dataList[i].id,
                certificateId: dataList[i].certificateId,
                certificateNum:dataList[i].certificateNum,
                supplierApprovalFileBusinessScopeVOList: zTreeNodes,
                // certificationOffice: dataList[i].certificationOffice,
                certificationDate: dataList[i].certificationDate,
                validityDate: dataList[i].validityDate,
                enclosureCount: dataList[i].enclosureCount,
                enclosureList:dataList[i].enclosureList,
                scopeofoperationVo:JSON.stringify(dataList[i].scopeofoperationVo)
            }
            xGridData.push(a);
        }
    }
    if(xGridData.length<1){
        xGridData={
            id: "",
            certificateId: "",
            certificateNum:"",
            supplierApprovalFileBusinessScopeVOList: [],
            // certificationOffice: "",
            certificationDate: "",
            validityDate: "",
            enclosureCount: "",
            enclosureList:[]
        }
    }
    xGridTable3(xGridData,$table);
}
function xGridTable3(xGridData,$table) {
    $table=$table|| $('#table3');
    /**
     * RM 2018-10-10
     * 重新引入供应商名称之后，需要清空批准文件表格，否则，上一条供应商的附件还会保存在表格中，
     * 当新数据加载完后，虽然附件数量为无，但是实际表格隐藏域中可能存在数据，，
     * 然后上传附件的时候附件个数和变革中显示的数量对应不上
     */
    $table.XGrid('clearGridData');
    $table.XGrid('setGridParam',{data:xGridData}).trigger('reloadGrid');
}
$('#table3').XGrid({
    data: [],
    colNames: ['', '<i class="i-red">*</i>证书类型', '<i class="i-red">*</i>证书编号', '<i class="i-red">*</i>经营范围',  '<i class="i-red">*</i>发证日期', '<i class="i-red">*</i>有效期至', '附件','附件数据','全量经营范围'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        hidden:true,
        key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    }, {
        name: 'certificateId',
        index: 'certificateId',
        rowtype: '#certType',
        rowEvent: function (etype, c, d) {

            var credentialId=etype.rowData.certificateId;

            getZtreeData(credentialId,etype.rowData.id);

        }
    }, {
        name: 'certificateNum',
        index: 'certificateNum',
        rowtype: '#certificateNum'
    }, {
        name: 'supplierApprovalFileBusinessScopeVOList',
        index: 'supplierApprovalFileBusinessScopeVOList',
        rowtype: '#operScopeZtree',
        rowEvent: function (etype) {
            // initBaseDataBuseScope();//初始化经营范围内容
        }
    }, {
        name: 'certificationDate',
        index: 'certificationDate',
        rowtype: '#fCrte',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'validityDate',
        index: 'validityDate',
        rowtype: '#docTerm',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'enclosureCount',
        index: 'enclosureCount',
        formatter:function (value) {
            var str='无';
            if(value)
            {

                str ='<a href="javascript:;" onclick="showImg(this);">'+value+'</a>';

            }
            return str;
        },
        unformat: function (e) {
            e=e.replace(/<[^>]+>/g,'');
            if(e == '无'){
                e = 0;
            }
            return e;
        }
    }, {
        name: 'enclosureList',
        index: 'enclosureList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return JSON.stringify(value);
            }
            return JSON.stringify([]);
        },
        unformat: function (value) {
            return JSON.parse(value);
        }
    }, {
        name: 'scopeofoperationVo',
        hidden:true,
    }
    ],
    rowNum: 100,
    rownumbers: true,//是否展示序号
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_page3',
    onSelectRow: function (id, dom, obj, index, event) {
    },
    gridComplete: function () {
        var $this=$(this);
        if($this.getRowData().length<=0) return;
        setTimeout(function () {
            var $this_tr=$this.find('tr');
            $this_tr.each(function (index) {
                if(index != 0){
                    var credentialId=$(this).find("td[row-describedby='certificateId'] select option:selected").val();
                    var zTreeId=$(this).find(".operScopeZtree").attr("id");
                    var zTree = $.fn.zTree.getZTreeObj(zTreeId);
                    /**
                     * RM 2018-10-31
                     * 获取树形的所有节点，存入数组，下面比较时需要
                     * 判断所有节点的长度，与 返回的数据节点的长度是否一致，一致的话说明全部已选，需要要对伏父节点经营范围也要勾选
                     */
                    var  zTreeNodes = zTree.getNodes()[0];
                    var zTreeNodesArr = [];
                    if(zTreeNodes){
                        zTreeNodesArr.push(zTreeNodes.name);
                        function getNodeData(n){
                            var el = n.children;
                            if(el.length > 0){
                                for(var i = 0; i<el.length; i++){
                                    zTreeNodesArr.push(el[i].name)
                                    if(el[i].children && el[i].children.length > 0){
                                        getNodeData(el[i])
                                    }else{
                                        el[i].isParent = false
                                    }
                                }
                            }
                        }
                        getNodeData(zTreeNodes);
                        //2018-10-31 END
                        var changeAry=idArr;
                        //如果为查阅变更，则赋对应的变更数据
                        if($(this).closest('table').prop('id')=='changeApplyTable'){
                            changeAry=idArr_;
                        }
                        for(var i=0;i<changeAry.length;i++) {
                            var item = changeAry[i];
                            for(var n in item){
                                if(n == credentialId){
                                    var aList=item[n];

                                    if(aList.length>0){
                                        /**
                                         * RM 2018-10-31
                                         * 下面三元运算判断
                                         * 当子节点全部勾选时，回显，需要给父节点经营范围也打勾。
                                         */
                                        (aList[0].id == 0)?(aList.length == zTreeNodesArr.length?zTree.checkNode(zTree.getNodeByParam( "id",0), true ):''):(aList.length+1 == zTreeNodesArr.length?zTree.checkNode(zTree.getNodeByParam( "id",0), true ):'');

                                        for(var s=0;s<aList.length;s++){
                                            if(item.trId == $(this).prop('id')){
                                                var treeNode=zTree.getNodeByParam( "id",Number(aList[s].businessScopeCode || aList[s].id));
                                                if(treeNode)
                                                    zTree.checkNode(treeNode, true );
                                            }
                                            if($(this).closest('table').prop('id')!='changeApplyTable'){
                                                // initBaseDataBuseScope();
                                            }else{
                                                setTimeout(function(){setDisabledZtree(true,$('#changeApplyTable'))});
                                            }
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            })
            //设置不可更改 0不可编辑 1可编辑
            if($('#isEdit').val() == '0') {
                setDisabledZtree(true);
            }


            $this.find('[row-describedby="supplierApprovalFileBusinessScopeVOList"] span.node_name').filter(function (i, v) {
                if ($(v).html() == 'undefined') {
                    $(v).closest('li').remove()
                }
            })

        },500)
    }
});


// 批准文件 类型 重复选择校验
$("#table3").on("mousedown",'select[name="certificateId"]',function () {
    /**
     * RM 2018-10-10
     * 批准文件 证书类型选中的不再被禁用
     */
    //selCannotRepeatChoice(this,'table3','certificateId');
})

//请求字典获取经营范围树数据
function getZtreeData(credentialId,id){
    console.log(11)
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/listbycredentialidAndorgcode",
        async : false,
        data:{
            credentialId:credentialId,
            orgCode:'001'
        },
        dataType:"json",
        success: function (data) {
            console.log(data);
            var zTreeNodes = [{
                id: 0,
                name: "经营范围",
                // open: true,//展开
                children:[]

            }]
            if(!data )return false;
            if(data.result && data.result.length > 0){
                $(data.result).each(function(index,item){
                    var _obj = {};
                    _obj.id = item.scopeId;
                    _obj.name = item.scopeName;
                    _obj.children = [];
                    $(item.children).each(function(cindex,citem){
                        var _obj_child = {};
                        _obj_child.id = citem.scopeId;
                        _obj_child.name = citem.scopeName;
                        _obj.children.push(_obj_child)
                    })
                    zTreeNodes[0].children.push(_obj)
                })
                for(var i of zTreeNodes[0].children){
                    if(i.children.length == 0){
                        delete  i['children']
                    }
                }
                $('#table3').XGrid('setRowData', id,{supplierApprovalFileBusinessScopeVOList:zTreeNodes,scopeofoperationVo:JSON.stringify(data.result)});
            }else{
            	 $('#table3').XGrid('setRowData', id,{supplierApprovalFileBusinessScopeVOList:'',scopeofoperationVo:''});
            }



            var zTreeId=$('#table3 #'+id).find(".operScopeZtree").attr("id");
            var zTree = $.fn.zTree.getZTreeObj(zTreeId);
            zTree.refresh();


            for(var i=0;i<idArr.length;i++){
                var item=idArr[i];
                for(var n in item){
                    if(n == credentialId) {
                        var aList=item[n];
                        if(null != aList && aList.length>0){
                            for(var s=0;s<aList.length;s++) {
                                if(zTree != null && $(this).prop('id')==aList[s].approvalFileId){
                                    if(zTree.getNodeByParam("id", Number(aList[s].businessScopeCode))){
                                        zTree.checkNode( zTree.getNodeByParam( "id",Number(aList[s].businessScopeCode)), true );
                                    }
                                }

                            }
                        }

                    }
                }
            }

        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
};
//年度报告
function initTable4(supplierBaseId,type){
    var xGridData=[];
    $.ajax({
        url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getYearReportList',
        data:{"correlationId":supplierBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            if( data.result.list!=null&& data.result.list.length>0){
                var dataList = data.result.list;
                for(var i=0;i<dataList.length;i++){
                    var a={
                        id: dataList[i].id,
                        reportDate: dataList[i].reportDate,
                        manageAbnormal: dataList[i].manageAbnormal,
                        administrativePenalty: dataList[i].administrativePenalty,
                        validityDate:(Number(dataList[i].reportDate)+2)+'-06-30',
                        enclosureCount: dataList[i].enclosureCount,
                        enclosureList:dataList[i].enclosureList
                    }
                    xGridData.push(a);
                }
            }
            if(xGridData.length<1){
                xGridData = {
                    id: "",
                    reportDate: "",
                    manageAbnormal: "",
                    administrativePenalty: "",
                    enclosureCount: "",
                    enclosureList:[]
                }
            }
            xGridTable4(xGridData);
        }
    });

}
function xGridTable4(xGridData) {
    $('#table4').XGrid('setGridParam',{data:xGridData}).trigger('reloadGrid');
}
$('#table4').XGrid({
    data: [],
    colNames: ['', '<i class="i-red">*</i>报告年份', '<i class="i-red">*</i>是否经营异常', '<i class="i-red">*</i>是否有行政处罚','有效期至', '附件','附件数据'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        index: 'id', //索引。其和后台交互的参数为sidx
        hidden:true,
        key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    }, {
        name: 'reportDate',
        index: 'reportDate',
        rowtype: '#reportYear'
    }, {
        name: 'manageAbnormal',
        index: 'manageAbnormal',
        rowtype: '#isAbnormal'
    }, {
        name: 'administrativePenalty',
        index: 'administrativePenalty',
        rowtype: '#isPunish'
    },  {
        name: 'validityDate',
        index: 'validityDate',
        rowtype: '#validityDate'
    }, {
        name: 'enclosureCount',
        index: 'enclosureCount',
        formatter:function (value) {
            var str='无';
            if(value)
            {
                str ='<a href="javascript:;" onclick="showImg(this);">'+value+'</a>';
            }
            return str;
        },
        unformat: function (e) {
            e=e.replace(/<[^>]+>/g,'');
            if(e == '无'){
                e = 0;
            }
            return e;
        }
    }, {
        name: 'enclosureList',
        index: 'enclosureList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return JSON.stringify(value);
            }
            return JSON.stringify([]);
        },
        unformat: function (value) {
            return JSON.parse(value);
        }
    }],
    rowNum: 100,
    rownumbers: true,//是否展示序号
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_page4'

    //2018.9.5,RL,bug2407,begin
    ,gridComplete:function(){
        updateReportDateRegexp('table4','showData');
    }
    //2018.9.5,RL,bug2407,begin
});

function change_validityDate(el) {
    console.log(el)
    $(el).parents('tr').find('[name=validityDate]').val((Number($(el).val())+2)+'-06-30')
}
//初始化其他附件为选中
function initOtherFile(supplierBaseId,type){
    $.ajax({
        url:'/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getOtherFileList',
        data:{"correlationId":supplierBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            if(!data.otherFileList) return false;
            if(data.otherFileList.length){
                $(data.otherFileList).each(function(fileIndex,fileItem){
                    var enclosureList = fileItem.enclosureList;
                    if(enclosureList && enclosureList.length>0){
                        var imgArr =[];
                        $(enclosureList).each(function(imgIndex,imgItem){
                            var imgObj={};
                            imgObj.fileName=imgItem.fileName;
                            imgObj.filePath=imgItem.filePath;
                            imgArr.push(imgObj);
                        });
                        var html='<input type="hidden" data-type="'+fileItem.certificateType+'" id="other'+fileItem.certificateType+'" value=\''+JSON.stringify(imgArr)+'\' />';
                        $("body").append(html);
                    }
                    var otherFiles = $('.otherFile').find('input[type=checkbox]');
                    $(otherFiles).each(function(index,item){
                        if(fileItem.certificateType==$(this).val()){
                            $(this).attr("checked",'true')
                        }
                    });
                });
            }
        },
        error:function(){

        }
    });
}
//查看变更数据的图片
$('body').on('click','#id_dataChangeDiv span',function(){
    var  oldFile = window.changeApplyList.supplierOtherFileList.valueBefore;
    var  newFile = window.changeApplyList.supplierOtherFileList.valueAfter;
    var newArr = [], oldFileArr = [], newFileArr = [], oldFileNameArr = [];

    // for(var i = 0; i< oldFile.length; i++){
    //     for(var j = 0; j<oldFile[i].enclosureList.length; j++){
    //         var oldObj = {};
    //         oldObj.type = oldFile[i].certificateType;
    //         oldObj.fileName = oldFile[i].enclosureList[j].fileName;
    //         oldObj.filePath = oldFile[i].enclosureList[j].filePath;
    //         oldFileNameArr.push(oldObj.fileName)
    //         oldFileArr.push(oldObj);
    //     }
    // }
    for(let i = 0; i< newFile.length; i++){
        var a  = newFile[i];
        for(let j = 0; j<a.enclosureList.length; j++){
            var newObj = {};
            newObj.type = a.certificateType;
            newObj.fileName = a.enclosureList[j].fileName;
            newObj.filePath = a.enclosureList[j].filePath;
            newFileArr.push(newObj);
        }
    }
    // for (let i = 0;i<newFileArr.length; i++){
    //     if(oldFileNameArr.indexOf(newFileArr[i].fileName) <0){
    //         newArr.push(newFileArr[i])
    //     }
    // }

    var type = $(this).prev('input').val();
    if(newFileArr.length > 0){
        var demoArr= [];
        for(let i = 0; i<newFileArr.length; i++){
            if(newFileArr[i].type == type){
                demoArr.push(newFileArr[i])
            }
        }
        $.viewImg({
            fileParam:{
                name:'fileName',
                url:'filePath'
            },
            list:demoArr
        })
        return false;
    }else{
        utils.dialog({
            title:'提示',
            content:'没有对应类型的附件。',
            okValue:'确定',
            ok:function () {}
        }).showModal();
        return false;
    }
})
/**
 * 对页面中表单元素进行赋值
 * @param json 必传 eg:{"a":1,"b":2}
 * @param jq对象 表示:从当前对象下找对应的元素进行赋值
 * */
function loadData(json,el) {
    var obj = json;
    console.log(obj)
    var key, value, tagName, type, arr, thisVal,$el;
    for (x in obj) {
        key = x;
        value = obj[x];
        if(el)
        {
            $el=el.find("[name='" + key + "']");
        }else{
            $el=$("[name='" + key + "']");
        }
        $el.each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).attr('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    for (var i = 0; i < arr.length; i++) {
                        if (thisVal == arr[i]) {
                            $(this).attr('checked', true);
                            break;
                        }
                    }
                } else {
                    $(this).val(value);
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
            }
        });
    }
}
/**
 * 设置 批准文件内经营范围文件树不可选
 * @param checked boolean  true为禁止选择
 * */
function setDisabledZtree(checked,$table){
	$table=  $table || $("#table3");
	$table.find('tr').each(function (index) {
        if (index != 0) {
            var zTreeId = $(this).find(".operScopeZtree").attr("id");
            var zTree = $.fn.zTree.getZTreeObj(zTreeId);
            var nodes=zTree.getNodes();
            chkDis(nodes[0],zTree,checked);
        }
    })
}

//新增行
function addRow(buttonId,tableId){
    $("."+tableId).find('input,select,checkbox').removeAttr('disabled');
    $("#"+tableId).addRowData({id:new Date().getTime()});


    //2018.9.4,RL,bug2409,begin
    if((tableId=="table2") && ($("[name=supplierTypeId]").val()!="55")){
        var $table=$("#"+tableId);
        var $drug_option_dom=$table.find('.optDrugFlag');
        if($drug_option_dom && $drug_option_dom.length){
            $drug_option_dom.hide();
        }
    }
    //2018.9.4,RL,bug2409,end

    //2018.9.5,RL,bug2407,begin
    if(tableId=="table4"){
        updateReportDateRegexp(tableId,'addData');
    }
    //2018.9.5,RL,bug2407,end
}

//2018.9.5,RL,年度报告年份选择正则,begin
function getExistYear(tableId,thisValStr){
    var $table=$("#"+tableId);
    var $reportYearInput=$table.find('[id^=reportYear_] input');
    var thisValStrTmp=thisValStr.replace("年","");
    var existYear=[];
    $.each($reportYearInput,function(i,v){
        var $vValue=$(v).val().replace("年","");
        if(thisValStrTmp==$vValue){

        }else{
            $vValue?existYear.push($vValue):"";
        }
    });

    //2018.9.5,RL,bug2628,begin
    //return existYear.join("|");//旧逻辑
    if(existYear.length){
        return "^"+existYear.join("|");
    }else{
        return "";
    }
    //2018.9.5,RL,bug2628,end
}

function updateReportDateRegexp(tableId,type){
    var $table=$("#"+tableId);
    var trTarget=void 0;

    //type
    //showData:页面加载,显示已经保存的数据
    //addData:新增数据
    if(type=='showData'){
        trTarget=$table.find('tr:not(:first)');
    }else if(type=='addData'){
        trTarget=$table.find('tr:last')
    }

    if(trTarget && trTarget.length){
        trTarget.find("[id^=reportYear_] input").unbind("click").on('click',function(){
            var that=this;
            var $thisVal=$(this).val();
            setTimeout(function(){
                WdatePicker({el:that,readOnly:false,dateFmt:'yyyy',disabledDates:[getExistYear(tableId,$thisVal)]});//,maxDate:'%y',onpicked:function(){console.log(1)}
            },0);
        })
    }
}
//2018.9.5,RL,年度报告年份选择正则,end

//删除行
function deleRow(buttonId,tableId){
    var selectRow = $(tableId).XGrid('getSeleRow');
    if (!selectRow) {
        utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
    } else {
        $(tableId).XGrid('delRowData', selectRow.id);
        utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();


        //2018.9.8,RL,bug2954,begin
        if(tableId=="#table2"){
            var getRowDataAll=$(tableId).XGrid('getRowData');
            if(getRowDataAll && getRowDataAll.length){
                $("input[name='mandatary']").val(getRowDataAll[0].mandataryName);
                $("input[name='mandataryPhone']").val(getRowDataAll[0].mandataryTel);
            }else{
                $("input[name='mandatary']").val("");
                $("input[name='mandataryPhone']").val("");
            }
        }
        //2018.9.8,RL,bug2954,end
    }
}

function format(shijianchuo){
    //shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}

function add0(m){
    return m<10?'0'+m:m
}



//拼装树 数据
//data: 接口返回的数据
//el: $tr.find('.operScopeZtree') 对应的tr的树的位置
function pulicTree(data,el){
    //经营范围 tree

    var zTreeNodes = [{
        id: 0,
        name: "经营范围",
        // open: true,//展开
        children:[]

    }]
    if(!data )return false;
    if(data.length > 0){
        $(data).each(function(index,item){
            var _obj = {};
            _obj.id = item.scopeId;
            _obj.name = item.scopeName;
            _obj.children = [];
            $(item.children).each(function(cindex,citem){
                var _obj_child = {};
                _obj_child.id = citem.scopeId;
                _obj_child.name = citem.scopeName;
                _obj.children.push(_obj_child)
            })
            zTreeNodes[0].children.push(_obj)
        })
    }
    var setting = {
        check: {
            enable: true, //显示勾选框  默认不显示
            chkboxType: {"Y": "s", "N": "ps"}
        },
    };
    zTree = $.fn.zTree.init(el, setting, zTreeNodes);
    return zTreeNodes;
}

//选中事件
function zTreeOnClick(event, treeId, treeNode) {
    console.log(event, treeId, treeNode);
    grid_data = [];
    grid_data = [{
        id: "1",
        type: "2",
        pay: "3",
        name: treeNode.name,
        text: "5"
    }]
};

// 树形  checkbox获取选中的值
function getTreeCheckboxVal(){
    console.log('--------------------')
    //var treeCheckboxs = $('.operScopeZtree').find('.chk');
    var treeCheckboxs = document.querySelectorAll('.tree_jyfw .chk')
    console.log(treeCheckboxs)
    var treeCheckList = [];
    $('.tree_jyfw .chk').on('click',function(){
        alert('1111')
        var obj = {};
        obj.certificateType = $(this).attr('title');
        if($(this).hasClass('checkbox_true_full')){
            treeCheckList.push(obj);
        }else{
            for(var i = 0 ; i<treeCheckList.length; i++){
                if(treeCheckList[i].certificateType == obj.certificateType){
                    treeCheckList.splice(i,1)
                }
            }
        }
        console.log(treeCheckList)
    })
//	$(treeCheckboxs).each(function(index,item){
////		console.log($(this))
////		console.log($(item))
//	  $(item).on('click',function(){
//		  console.log('------------------')
//		  var obj = {};
//		  obj.certificateType = $(this).attr('title');
//		  if($(this).hasClass('checkbox_true_full')){
//			  treeCheckList.push(obj);
//		  }else{
//			  for(var i = 0 ; i<treeCheckList.length; i++){
//				  if(treeCheckList[i].certificateType == obj.certificateType){
//					  treeCheckList.splice(i,1)
//				  }
//			  }
//		  }
//		  console.log(treeCheckList)
//		  return treeCheckArr = treeCheckList
//	  })
//	})
//	 return treeCheckArr =  treeCheckList;
}


function setRadioChecked(name,value){
    var obj=$("input[name="+name+"]");
    if($("input[name="+name+"]:checked").length < 1)
    {
        obj.each(function () {
            var val=this.value;
            if(val == value)
            {
                $(this).prop("checked",true);
            }
        })
    }
}
//初始化经营范围内容
/**
 * RM 产品说不 要了
 * initBaseDataBuseScope() 此方法需要在有效期截止日期WdatePicker内调用 eg:<input type="text" placeholder="有效期" onclick="WdatePicker({onpicked:function(dp){initBaseDataBuseScope();}})"  name="validityDate"/>
 *
 * utils.operateRange 拼装数据
 *
 * @param XGrid 数据 $('#table3').XGrid('getRowData')
 *
 * @param 经营范围文件数当前列字段名 scopeOfBusiness
 *
 * @param 有效期截止字段  validityDate
 * */
function initBaseDataBuseScope(){
    var htmlAry = [], idAry = [];
    $.each(utils.operateRange($('#table3').XGrid('getRowData'),'supplierApprovalFileBusinessScopeVOList','validityDate'), function (i, v) {
        if (v.status) {
            htmlAry.push('<font style="color: red;">' + v.name + '</font>');
        } else {
            htmlAry.push(v.name);
        }
        idAry.push(v.id);
    })
    //console.log(htmlAry.join(','))
    $('#baseDataBuseScope').html(htmlAry.join(','));
}

//批准文件
upLoadFile("#pzwjUpload",'#table3','certificateId');
//年度报告
//upLoadFile("#ndbgUpload",'#table4','reportDate');
ndbgUploadFun();
//其它附件 批量管理附件按钮
qtfjUploadFun();
