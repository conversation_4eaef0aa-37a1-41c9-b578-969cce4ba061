(function(){
	const origin = location.origin;
	if (origin.indexOf('test') > -1) {
		document.domain = 'test.ybm100.com'
	} else if (origin.indexOf('stage') > -1) {
		document.domain = 'stage.ybm100.com'
	} else {
		document.domain = 'ybm100.com'
	}
})();

var req_url_pre = "/proxy-sysmanage/sysmanage/process";

$(function() {
	indexRemindTask();
	process();
	textHistory();
	textException();
});

//预警
function indexRemindTask() {
	$.ajax({
		url: req_url_pre + "/indexRemindTask",
		type: "post",
		data: {},
		success: function(res) {
			let _str = ''
			if (res.code == 0 && res.result.list.length > 0) {
				$(res.result.list).each((index, item) => {
					console.log(item)
					_str += `<ul>
								<li>${getProcessName(item.approvalProcessKey)}${item.number}</li>
							</ul>
							${res.result.total > 5? '<p style="color: skyblue; position: absolute; bottom: 0px; right: 20px; z-index: 1;" onclick="bt_getMore()">更多>>></p>': '' }
							`
				})
				$('#remindTaskCon').append(_str)
				utils.dialog({
					title: '审批提醒',
					content: $('#remindTaskCon'),
					width: 300,
					zIndex:1200,
					okValue: '去处理',
					ok: function() {
						bt_getMore();
					}
				}).showModal();
			}
		},
	});
	rejectReminder();
};
var rejectReminderDialog
//驳回提醒
function rejectReminder() {
	$.ajax({
	   url:"/proxy-purchase/purchase/rejectedRemind/queryByUser",
	   type:'get',
	   data:{
	     count:0,
	   },
	   success: function(res) {
         if(res.code == 0 && res.result.length > 0){
		   var arrList = [];
		   if(res.result.length>8){
			  arrList = res.result.slice(0,9)
		   }else{
			  arrList = res.result
		   }
		   let _str = ''
		   _str += '<div id="orderWrap">'
		   _str += '<p  style="font-size: 14px;color:red">注意：超过48小时未处理的或单击进入过详情页面的单据将不再进行提醒！</p>'
		   _str += '<h4 style="font-size: 14px;font-weight:500">您发起的申请已被驳回，请及时处理！</h4>';
		   _str += `<ul style="margin:0;min-height: 150px;">`
		   arrList.forEach((item,index) => {
			  _str+= `<li style="height:26px;line-height:26px;color: skyblue;">${getRejectionUrl(item)}</li>`
		   })
		   _str += res.result.length>8?`<li style="height:26px;line-height:26px;color: skyblue;">......</li>`:''
		   _str += '</ul>'
		   _str += '<div>'+ '<textarea id="copyBillInfo" class="btn btn-info" style="display:none">'
		   res.result.forEach((item,index) => {
			_str+= `${item.orderInfo}`+'\n'
		 })
	       _str += '</textarea> ' +'</div>'
		   _str += '<p  style="font-size: 14px;color:red">注意：本提醒最多展示8条，单击【复制信息】可复制所有驳回信息。</p>'
		   _str +='</div>';
		   setTimeout(() => {
			rejectReminderDialog = utils.dialog({
				title: '驳回提醒',
				content: _str,
				zIndex:1111,
				width: 500,
				okValue:'复制信息',
				ok: function () {
				  var flag = utils.copyText($("#copyBillInfo").val());
				  $.ajax({
					url:"/proxy-purchase/purchase/rejectedRemind/deleteByUser",
					type:'get',
				  })
				  if (flag){
					utils.dialog({
					  content: '复制成功！',
					  quickClose: true,
					  timeout: 2000
					}).show();
				  }else {
					utils.dialog({
					content: '复制失败请重新复制！',
					quickClose: true,
					timeout: 2000
					}).show();
					return false;
				  } 
				},
			})
			rejectReminderDialog.showModal();		
		}, 4000);
		}
	   },
	})
	// _str += `${ceshiArrList.length > 8? '<p style="color: skyblue;float:right;cursor:pointer;margin-right:100px" onclick="bt_getMore()">更多>>></p>': '' }`
};
//驳回提醒单条点击事件
function popup(that) {
  var thisName = $(that).attr('name')
  var thisUrl = $(that).attr('url')
  var thisCurrentType = $(that).attr('currentType')
  var thisOrderNo = $(that).attr('key')
  $.ajax({
	  url:'/proxy-purchase/purchase/rejectedRemind/deleteByOrderNo',
	  type:'get',
	  data:{
		orderNo:thisOrderNo
	  },
	  success:function(res) {
		  if(res.code == 0){
			$("#orderWrap").empty()
			$.ajax({
			  url:"/proxy-purchase/purchase/rejectedRemind/queryByUser",
			  type:'get',
			  data:{
				count:0,
			  },
			  success: function(res) {
				if(res.code == 0 && res.result.length > 0){
				  var arrList = [];
				  if(res.result.length>8){
					 arrList = res.result.slice(0,9)
				  }else{
					 arrList = res.result
				  }
				  let _str = ''
				  _str += '<div id="orderWrap">'
				  _str += '<p  style="font-size: 14px;color:red">注意：超过48小时未处理的或单击进入过详情页面的单据将不再进行提醒！</p>'
				  _str += '<h4 style="font-size: 14px;font-weight:500">您发起的申请已被驳回，请及时处理！</h4>';
				  _str += `<ul style="margin:0;min-height: 150px;">`
				  arrList.forEach((item,index) => {
					 _str+= `<li style="height:26px;line-height:26px;color: skyblue;">${getRejectionUrl(item)}</li>`
				  })
				  _str += res.result.length>8?`<li style="height:26px;line-height:26px;color: skyblue;">......</li>`:''
				  _str += '</ul>'
				  _str += '<div>'+ '<textarea id="copyBillInfo" class="btn btn-info" style="display:none">'
				  res.result.forEach((item,index) => {
				   _str+= `${item.orderInfo}`+'\n'
				})
				_str += '</textarea> ' +'</div>';
				_str += '<p  style="font-size: 14px;color:red">注意：本提醒最多展示8条，单击【复制信息】可复制所有驳回信息。</p>'
				_str +='</div>';
				  $("#orderWrap").append(_str)
			   }else {
				  rejectReminderDialog.close().remove();
			   }
			  },
		   })
		  }
	  }
  })
  utils.openTabs(thisCurrentType, thisName, thisUrl)
}
// 更多
function bt_getMore() {
	var name = "我的任务";
	var	url = "/proxy-sysmanage/sysmanage/process/gTaskList?taskType=0&indexRemindTask=index_remind_task";
	utils.openTabs("index_remind_task", name, url);
}
//编辑待办工作
function process() {
	//待办
	$.ajax({
		url: req_url_pre + "/indexGTask",
		type: "post",
		data: {
			"taskType": 0
		},
		success: function(result) {
			if(result && result.result && result.result.list){
				var list = result.result.list;
				var total = result.result.total;
				if (list != null) {
					$("#auth").html(total);
					var htmls = "<ol>";
					var length = 5;
					if (list.length < 5) {
						length = list.length;
					}
					for (var i = 0; i < length; i++) {
						htmls += "<li>" + getprocessurl(list[i]) + "</li>";
					}
					htmls += "</ol>";
					$("#authlist").html(htmls);
				} else {
					$("#auth").html(0);
				}
				todoclick();
			}
		}
	})
	//抄送
	$.ajax({
		url: req_url_pre + "/indexGTask",
		type: "post",
		data: {
			"taskType": 1
		},
		success: function(result) {
			if(result && result.result && result.result.list){
				var list = result.result.list;
				var total = result.result.total;

				// var total = 0;
				var show_num = 1;
				if (list != null) {
					var htmls = "<ol>";
					var length = 5;
					for (var i = 0; i < list.length; i++) {
						if ('false' == list[i].look) {
							// total ++;
							if (show_num <= 5) {
								htmls += "<li>" + getprocessLookurl(list[i]) + "</li>";
								show_num ++;
							}
						}
					}
					htmls += "</ol>";
					$("#copy").html(total);
					$("#copylist").html(htmls);
				} else {
					$("#copy").html(0);
				}
				//jump();
			}
		}
	})
}

//编辑异常
function textException() {
	$.ajax({
		url: "/proxy-sysmanage/sysmanage/exceptionWarn/list",
		type: "post",
		success: function(result) {
			var htmls = "";
			var list = result.result;
			if (list != null) {
				for (var i = 0; i < list.length; i++) {
					var excep = list[i];
					if (excep.exceptionWarnType == "COMMODITY_VALIDITY") {
						htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='" + excep.exceptionWarnTypeName +
							"' only='" + excep.exceptionWarnTypeName + "'>" + excep.exceptionWarnTypeName + "：近效期商品:" + excep.num +
							"条；</a></li>";
					} else if (excep.exceptionWarnType == "COMMODITY_APTITUDE") {
						htmls += "<li><a href='javascript:void(0)' url='" + excep.linkUrl + "' name='商品资质查询' only='" + excep.exceptionWarnTypeName +
							"'>" + excep.exceptionWarnTypeName + "：剩余效期<180天的商品资质数:" + excep.num + "个，剩余效期<=0的商品资质数:" + excep.numSec +
							"个；</a></li>";
					} else if (excep.exceptionWarnType == "SUPPLIER_REPORT") {
                        htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='供应商年度报告' only='" + excep.exceptionWarnTypeName +
                            "'>" + excep.exceptionWarnTypeName + "：剩余效期<180天的供应商:" + excep.num + "个，剩余效期<=0的供应商" + excep.numSec +
                            "个；</a></li>";
                    } else if (excep.exceptionWarnType == "SUPPLIER_APTITUDE") {
						htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='供应商资质查询' only='" + excep.exceptionWarnTypeName +
							"'>" + excep.exceptionWarnTypeName + "：剩余效期<180天的供应商资质数:" + excep.num + "个，剩余效期<=0的供应商资质数:" + excep.numSec +
							"个；</a></li>";
					} else if (excep.exceptionWarnType == "CUSTOMER_APTITUDE") {
						htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='客户资质查询' only='" + excep.exceptionWarnTypeName +
							"'>" + excep.exceptionWarnTypeName + "：剩余效期<180天的客户资质数:" + excep.num + "个，剩余效期<=0的客户资质数:" + excep.numSec +
							"个；</a></li>";
					} else if (excep.exceptionWarnType == "SALES_ORDER") {
						htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='" + excep.exceptionWarnTypeName +
							"' only='" + excep.exceptionWarnTypeName + "'>" + excep.exceptionWarnTypeName + excep.num + "条；</a></li>";
					} else if (excep.exceptionWarnType == "STOCK_OUT") {
						htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='" + excep.exceptionWarnTypeName +
							"' only='" + excep.exceptionWarnTypeName + "'>" + excep.exceptionWarnTypeName + excep.num + "条；</a></li>";
					} else if (excep.exceptionWarnType == "PROFIT_AND_LOSS") {
						htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='" + excep.exceptionWarnTypeName +
							"' only='" + excep.exceptionWarnTypeName + "'>" + excep.exceptionWarnTypeName + excep.num + "条；</a></li>";
					}else if (excep.exceptionWarnType == "PURCHASE_CLOSESOON") {
                        htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='" + excep.exceptionWarnTypeName +
                            "' only='" + excep.exceptionWarnTypeName + "'>" + excep.exceptionWarnTypeName + excep.num + "条；</a></li>";
                    }else if (excep.exceptionWarnType == "PURCHASE_ADVANCE") {
                        htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='" + excep.exceptionWarnTypeName +
                            "' only='" + excep.exceptionWarnTypeName + "'>" + excep.exceptionWarnTypeName + excep.num + "条；</a></li>";
                    }else if (excep.exceptionWarnType == "PURCHASE_AUTOCLOSE") {
                        htmls += "<li><a href='javascript:void(0)'url='" + excep.linkUrl + "' name='" + excep.exceptionWarnTypeName +
                            "' only='" + excep.exceptionWarnTypeName + "'>" + excep.exceptionWarnTypeName + excep.num + "条；</a></li>";
                    }
				}
				$("#exceptinlist").html(htmls);
				jump();
			}
		}
	})
}

function todoclick() {
	$('.approved,.consulted,small,.order ol li a').on('click', function() {
		var processInstanceId = $(this).attr("obj");
		var key = $(this).attr("key");
		var id = $(this).attr("id");
		var todoId = $(this).attr("todoid");
		var yurl = $(this).attr("url");

		$.ajax({
			url: req_url_pre + "/queryTaskByProcessInstanceId",
			type: "post",
			data: {
				"processInstanceId": processInstanceId
			},
			success: function(result) {
				var obj = result.result;
				var assigen = obj.assignee;
				var url = ""
				var name = getProcessName(key);
				if (assigen == null) {
					name = "我的任务";
					url = "/proxy-sysmanage/sysmanage/process/gTaskList?taskType=0&todoId=" + todoId;
				} else {
					url = yurl;
				}
				utils.openTabs(id, name, url);
			}
		})
	})


}

//编辑历史日志
function textHistory() {
	$.ajax({
		url: "/proxy-sysmanage/sysmanage/sysVersion/findAllVersionInfoList",
		type: "get",
		data: "",
		success: function(result) {
			var htmls = "<h1>系统更新记录</h1>";
			var json = result.result;
			htmls += "<div class='row-content1' style='height: 340px;'>"
			for (var i = 0; i < json.length; i++) {

				htmls += "<div class='row' style='margin: 3px 0 7px;'>";
				htmls += "<div class='col-md-12 col-sm-12 order recordList'>";
				var time = ToolUtil.dateFormat(json[i].releaseTime, 'yyyy-MM-dd HH:mm:ss');
				htmls += "<h2>" + time + "</h2>";
				htmls += "<ol>";
				var versionCode = json[i].versionCode;
				var content = json[i].content;
				for (var n = 0; n < content.length; n++) {
					htmls += "<li>" + content[n] + "</li>";
				}
				htmls += "</ol></div></div>";
			}
			htmls += "</div>";
			$("#divHistoryList").html(htmls);
		}
	})
}

/**
 * 获取抄送a标签信息
 * @param obj
 * @returns {string}
 */
function getprocessurl(obj) {
	var htmls = "";
	var key = obj.approvalProcessKey;
	var businessId = obj.businessId;

	var name = getProcessName(key);
	var url = getProcessUrl(key, obj);
	htmls += "<a href='javascript:void(0)' obj='" + obj.approvalProcessId + "' url='" + url + "' key='" + key + "' id='" +
		businessId + "' todoid='" + obj.id + "'>" + name + obj.number + "</a>";
	return htmls;
}

//根据订单类型获取详情页的URL路径
function getDetailUrl(type,obj){
	var goObj = {
		url:'',
		name:'',
		currentType:'',
	};
	// purchaseId
	switch (type) {
	  case 1 :
      // 采购订单详情
	    goObj.url = "/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNo=" + obj.orderNo + "&type=1";
		goObj.name = "采购订单详情"
		goObj.currentType = "purchaseOrderDetail"
      break;
	  case 2 :
	  // 采购退货详情
	    goObj.url = '/proxy-purchase/purchase/purchaseRefundProductOrder/toDetail?purchaseOrderNo='+obj.orderNo+'&type=1';
		goObj.name = "采购退货详情"
		goObj.currentType = "purchaseRefundProductOrderDetail"
	  break;
	  case 3 :
	  // 退补价详情
	    goObj.url = "/proxy-purchase/purchase/purchaseRefundPriceOrder/toDetail?purchaseId="+obj.orderId+"&type=1";
		goObj.name = "退补价详情"
		goObj.currentType = "purchaseRefundPriceOrderDetail"
	  break;
	  case 4 :
	  // 采购计划单详情
	    goObj.url = '/proxy-purchase/purchase/groupPlanOrder/toDetail?planOrderNo='+obj.orderNo+'&type=1';
		goObj.name = "采购计划订单详情"
		goObj.currentType = "purchaseOrderDetail"
	  break;
	  case 5 :
	  // 采购退货计划详情
	    goObj.url = '/proxy-purchase/purchase/groupRefundPlanOrder/toDetail?purchaseOrderNo='+obj.orderNo+'&type=1';
		goObj.name = "采购退货计划详情"
		goObj.currentType = "purchaseRefundProductOrderDetail"
	  break;
	  case 6 :
	  // 退补价计划单详情
	    goObj.url = "/proxy-purchase/purchase/groupPlanRefundPriceOrder/toDetail?purchaseId="+obj.orderId+"&type=1";
		goObj.name = "退补价计划单详情"
		goObj.currentType = "purchaseRefundPriceOrderDetail"
	  break;
	  case 7 :
	  // 调拨明细单详情
	    goObj.url = '/proxy-purchase/purchase/allocatingPlanOrder/toAdd?type=1&allotPlanNo='+obj.orderNo;
		goObj.name = "调拨明细单详情"
		goObj.currentType = "toAdd"
	  break;
      case 8 :
		// 采购订单详情
		goObj.url = "/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNo=" + obj.orderNo + "&type=1";
		goObj.name = "采购订单详情"
		goObj.currentType = "purchaseOrderDetail"
		break;
	  default:
		goObj.url = "";
	  break;
	}
	return goObj
}
/**
 * 获取驳回a标签信息
 * @param obj
 * @returns {string}
 */
 function getRejectionUrl(obj) {
	var htmls = "";
	var type = obj.orderType
	var orderNo = obj.orderNo;
	var goObj = getDetailUrl(type, obj);
	htmls += "<a href='javascript:void(0)' onclick=popup(this)  url='" + goObj.url + "' currentType='" + goObj.currentType + "' name= '" + goObj.name + "' key='" + orderNo + "' id='" +
	obj.id + "'>" + obj.orderInfo + "</a>";
	return htmls;
}
/**
 * 获取抄送a标签信息
 * @param obj
 * @returns {string}
 */
function getprocessLookurl(obj) {
	var htmls = "";
	var key = obj.approvalProcessKey;
	var businessId = obj.businessId;
	var name = getProcessName(key);
	var look_url = getProcessLookUrl(key, obj);
	var data = JSON.stringify({
		id: obj.id,
		approvalProcessId: obj.approvalProcessId,
		taskType: obj.taskType
	});
	htmls += "<a data='" + data + "' href='javascript:void(0)'url='" + look_url + "' name='" + name + "' only='" +
		businessId + "'>" + name + obj.number + "</a>";
	return htmls;
}

/*跳转的同时，触发事件*/
$("#copylist").on("click", function(e) {
	var ele = $(e.target);
	if (ele.is('a')) {
		var data = JSON.parse(ele.attr('data'));
		saveLook(data);
	}
})

function jump() {
	$('.abnormal .order ol li a').on('click', function() {
		var url = $(this).attr("url");
		var id = $(this).attr("only");
		var name = $(this).attr("name");
		utils.openTabs(id, name, url);
	})
}

//保存已读
function saveLook(obj) {
	var params = {
		id: obj.id,
		approvalProcessId: obj.approvalProcessId,
		taskType: obj.taskType
	};
	$.ajax({
		url: req_url_pre + "/saveLook",
		type: "POST",
		dataType: "json",
		contentType: "application/json;charset=UTF-8",
		data: JSON.stringify(params),
		success: function(result) {
			console.log("已读返回数据:" + result);
		}
	});
};


//获取待处理采购计划单数量
 $(function () {
     if(parent.window.location.href.includes("scm")){
         $("#scm-tip").css('display','none');
         return;
     }
     $.ajax({
         url: "/checkDarkList",
         type: "GET",
         dataType: "json",
         contentType: "application/json;charset=UTF-8",
         success: function(data) {
             if(data.code==0){

                 if(!localStorage.getItem("scm-tip")){
                     utils.dialog({
                         width: 250,
                         height: 100,
                         title: "提示",
                         cancelValue:"取消",
                         content: "神农ERP即将升级为供应链平台，请前往使用",
                         cancel: function(){
                             localStorage.setItem("scm-tip",1);
                             },
                         okValue: '确定',
                         ok: function () {
                             parent.window.location.href = "https://scm." + document.domain
                         },
                     }).showModal();

                 }
             }else {
                 $("#scm-tip").css('display','none');
             }
         }
     });
 })

 function goScm(){
     parent.window.location.href = "https://scm." + document.domain
 }

function closeTip (){
    $("#scm-tip").css('display','none');
}