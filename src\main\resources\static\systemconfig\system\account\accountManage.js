var queryUserListUrl="/proxy-sysmanage/sysmanage/system/queryUserList";

function showDialog(titleText,contentText){
    //提示框
    utils.dialog({
        width: 180,
        height: 30,
        title: titleText,
        content: contentText,
        quickClose: false,
        okValue: '确定',
        ok: function () {}
    }).showModal();
}

function showDialogWithCallback(titleText,contentText,callback){
    //提示框
    utils.dialog({
        width: 180,
        height: 30,
        title: titleText,
        content: contentText,
        quickClose: false,
        okValue: '确定',
        ok: callback()
    }).showModal();
}

//弹框提示
function showTips(contentText){
    utils.dialog({
        content: contentText,
        quickClose: true,
        timeout: 2000
    }).showModal();
}


//初始化查询
$(function () {
    //账号表
    $('#X_Table').XGrid({
        url: '/proxy-sysmanage/sysmanage/system/queryUserList',
        colNames: ['编号','账号', '姓名', '手机', '邮箱', '职位', '角色', '机构', '部门', '状态', '操作'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden:true,
            hidegrid: true
        },
        {
            name: 'account',
            index: 'account',
        },
        {
            name: 'userName',
            index: 'userName'
        }, {
            name: 'mobile',
            index: 'mobile'
        }, {
            name: 'email',
            index: 'email'
        }, {
            name: 'postName',
            index: 'postName'
        }, {
            name: 'roleNames',
            index: 'roleNames'
        }, {
            name: 'orgName',
            index: 'orgName',
            width: 200
        }, {
            name: 'dptName',
            index: 'dptName'
        }, {
            name: 'userStatusName',
            index: 'userStatusName'
        }, {
            name : 'operation',
            width : 300,
            rowtype : '#operation_enable',
            formatter: function (e, d, rData) {
            	if (3 == rData.userStatus) {
                    return {'rowtype': '#operation_stop'};
                } else if(2 == rData.userStatus) {
                    return {'rowtype': '#operation_enable'};
                } else if(1 == rData.userStatus) {
                    return {'rowtype': '#operation_enable'};
                } else{
                     return {'rowtype': '#operation_default'};
                }
            },
            width: 120
        }
        ],
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false,
        rownumbers: true,
        key: 'id',
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager'
    });

    //初始化机构菜单
    initOrgList();
//     initChannelInfoList();
    $("#add_mobile").blur(function(){
        var mobileStr=$("#add_mobile").val();
        if(mobileStr.length==0){
            return false;
        }

        var userId=$("#userId").val();
        if(userId==""){
            userId="0";
        }

     });

 });

 function searchData(){
    var result = searchCheck();
    if(!result.passed){
        return;
    }

    $("#X_Table").setGridParam({
        //url: '/proxy-sysmanage/sysmanage/system/queryUserList',
        url:queryUserListUrl,
        postData: result.data
    }).trigger('reloadGrid');
 }

//查询数据，重置data
$('#seachData').on('click', function () {
    searchData();
});

function saveData(flag){
    var titleStr='编辑';
    utils.dialog({
        title:titleStr+'账号',
        content: $('.container'),
        okValue: '确定',
        ok: function () {
            //验证需要添加的数据
            var result = saveCheck();
            //console.log(flag+"-->checkResult:"+JSON.stringify(result));
            if(!result.passed){
                return false;
            }
            var b=false;
            $.ajax({
                url:"/proxy-sysmanage/sysmanage/system/saveUserOrg",
                type:"post",
                async:false,
                data:result.data,
                traditional: true,
                success:function(res){
                    if(res.code==0){
                       b=true;
                       showTips(titleStr+"账号成功！");
                       searchData();
                    }else{
                       showDialog("提示",res.msg);
                       //showTips(res.msg);
                       b=false;
                    }
                }
            });
            return b;
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}

//验证新建用户，需要的数据
function saveCheck(){
    var result={passed:false};
    var userId=$("#userId").val();
    var erpOrgListArr = $("#add_erp_orgList").val();

    if(!$.isArray(erpOrgListArr)){
        showTips("请选择机构！");
        return result;
    } else if(erpOrgListArr.length < 1){
        showTips("请选择机构！");
        return result;
    }
    var erpOrgListParam = "";
    $(erpOrgListArr).each(function(i,item){
    	erpOrgListParam += item + ",";
    });

    result.passed = true;
    result.data={
    	userId: userId, orgCodes: erpOrgListParam
    }

    return result;
}

//验证查询参数
function searchCheck(){
    var result={passed:false};
    var searchName=$("#searchName").val();
    var searchMobile=$("#searchMobile").val();
    //var searchPostName=$("#searchPostName").val();
    var searchUserStatus = $("#searchStatusList").children('option:selected').val();
    var searchRoleId=$("#roleList").children('option:selected').val();
    var searchOrgCode=$("#orgList").children('option:selected').val();
    var searchPost=$("#query_postList").val();


    if(searchUserStatus ==""){
        searchUserStatus = "-1";
    }

    if(searchOrgCode ==""){
        searchOrgCode = "-1";
    }

    if(searchRoleId ==""){
        searchRoleId = "-1";
    }
    result.passed=true;
    result.data={userName:searchName,mobile:searchMobile,
                userStatus:searchUserStatus,//postName:searchPostName,
                roleId:searchRoleId,orgCode:searchOrgCode,postCode:searchPost};
    return result;
}

//初始化机构列表,添加和编辑通用
function initOrgList(){
    $.ajax({
        url:"/proxy-sysmanage/sysmanage/org/getOrgList",
        type: "get",
        success:function(res){
            if(res&&res.code == 0){
                var defaultOption = "<option value='-1'>--请选择--</option>";
                var options="";
                $.each(res.result, function(i, org){ 
                    options+="<option value='"+org.orgCode+"'>"+org.orgName+"</option>";
                })
                $("#add_orgList").html(defaultOption+options);
                $("#orgList").html("<option value='-1'>--请选择--</option>"+options);
            }else{
                 $("#add_orgList").html("<option value='-1'>--请选择--</option>"+options);
                 $("#orgList").html("<option value='-1'>--请选择--</option>"+options);
            }
        }
    });
}

//选择机构，关联部门，职位，角色    添加和编辑通用
function orgChange(orgCode,user){
    if(!orgCode||orgCode==""){
        orgCode = $("#add_orgList").children('option:selected').val();
    }
    console.log("orgChange--->orgCode="+orgCode);

    $.ajax({
        url:"/proxy-sysmanage/sysmanage/org/getAllListByOrgCode",
        data:{orgCode:orgCode},
        type:"get",
        success:function(data){
            if(data.status){
                //部门
                var dptoptions = "<option value='-1'>--请选择--</option>";
                if(data.dptList){
                     $.each(data.dptList, function(i, dpt){ 
                        dptoptions+="<option value='"+dpt.dptCode+"'>"+dpt.dptName+"</option>";
                     });
                }
                $("#add_dptList").html(dptoptions);
                //职位
                var postoptions = "<option value='-1'>--请选择--</option>";
                $("#add_postList").html(postoptions);
                //角色
                var ret_roleIds = user.roleIds;
                var add_roleList_val = $("#add_roleList");
                if(data.roleList){
                	var htm = '';
                    $.each(data.roleList, function(i, role){ 
                    	htm += "<option value='"+role.id+"'>"+ role.name + "</option>";
                    });
                    add_roleList_val.html("").append(htm);
                    if(ret_roleIds) {
                    	$('#add_roleList').val(ret_roleIds.split(","));
                    }
                }
                $('#add_roleList').selectpicker('refresh');

                if(orgCode||orgCode!=""){
                    //选中下拉列表
                    if(user.dptCode){
                        $("#add_dptList").val(user.dptCode);
                        dptChange(user.dptCode,user.postCode);
                    }
                    if(user.postCode){
                        $("#add_postList").val(user.postCode);
                    }
               }

            }else{
                console.log("getAllListByOrgCode-->获取数据失败！");
            }
        }
    });
}

function dptChange(dptCode,postCode){
    console.log("orgChange--->dptCode="+dptCode+",postCode="+postCode);
    if(!dptCode||dptCode==""){
        dptCode = $("#add_dptList").children('option:selected').val();
    }
    if(dptCode=="-1"){
        $("#add_postList").val("-1");
        return;
    }

    $.ajax({
        url:"/proxy-sysmanage/sysmanage/system/queryPostListByDept",
        type: "get",
        data:{deptCode:dptCode},
        success:function(res){
            var postoptions = "<option value='-1'>--请选择--</option>";
            if(res&&res.code == 0){
                if(res.result){
                     $.each(res.result, function(i, post){ 
                        postoptions+="<option value='"+post.postCode+"'>"+post.postName+"</option>";
                     });
                }
            }
            $("#add_postList").html(postoptions);
            if(postCode&&postCode!=""){
                $("#add_postList").val(postCode);
            }
        }
    });
}

function editData(obj){
    var _this = $(obj);
    var userId = _this.closest('tr').attr('id');
    console.log("-----editData--userId="+userId);

//    resetForm();

    $.ajax({
        type:"get",
        url:'/proxy-sysmanage/sysmanage/system/queryUserById',
        dataType: "json",
        data:{userId:userId},
        success: function(res) {
            if(res&&res.code==0){
                var resultStr=JSON.stringify(res);
                console.log("queryUserById-->result");
                console.log(resultStr);
                var user=res.result;

                //赋值
                $("#userId").val(user.id);
                $("#add_name").val(user.userName);
                $("#add_mobile").val(user.mobile);
                $("#add_email").val(user.email);
                $("#add_account").val(user.account);
                $("#add_employeeNumber").val(user.employeeNumber);
                $("#add_dptName").val(user.dptName);
                $("#add_postName").val(user.postName);
                $("#add_roleNames").val(user.roleNames);
                $("#add_account").attr("disabled",true);
                if(user.employeeNumber != null && user.employeeNumber != "") {
	                $("#add_employeeNumber").attr("disabled",true);
                }
                if(user.orgCode){
                    $("#add_erp_orgList").val(user.orgCode);
                    $("#add_erp_orgList").attr("disabled",true);
                }else{
                    $("#add_orgList").val("-1");
                }

                $("#channelCode").val(user.channelCode);
                $("#channelName").val(user.channelName);

                $(".bigCustomerFlag").each(function() {
                	var bigCustomerFlag = $(this).val();
                	if(bigCustomerFlag == user.bigCustomerFlag) {
            			$(this).prop("checked", true);
            		}
                });

                queryOrgList(user.erpOrgCodes);

                //机构联动 部门 岗位
                orgChange(user.orgCode,user);

                saveData();
            }else{
                showTips("查询用户信息失败！");
            }
        }
    });
}

//选择机构，关联部门，职位，角色    添加和编辑通用
function orgChangeNew(obj,user,flag){
    var dptoptions = "<option value='-1'>--请选择--</option>";
    var orgCodeVal =$(obj).children('option:selected').val();
    console.log("orgChange--->orgCode="+orgCodeVal);
    if(orgCodeVal=="-1"){
        if(flag==1){
            //查询条件--角色下拉框设置为请选择
            $("#roleList").html(dptoptions);//
        }else if(flag==2){
            $("#add_dptList").html(dptoptions);
        }
        //return;
        //orgCodesStr=orgCodes;
    }else{
        if(flag==1){
            //$("#dptList").html(dptoptions);//查询条件无部门
            //------查询条件------机构联动角色
            queryRoleListByOrgCode(orgCodeVal);

        }else if(flag==2){
            //------添加、编辑账号--联动部门
            var dptCode=user.dptCode;
            if(!dptCode){
                dptCode="";
            }
            //----机构联动部门
            quertDeptListByOrgCode(orgCodeVal,dptCode,flag);
        }

    }

}

//查询条件 选择机构联动角色
function queryRoleListByOrgCode(orgCode){
    $.ajax({
        url:"/proxy-sysmanage/sysmanage/system/queryRoleListByParam",
        data:{"orgCode":orgCode},
        type:"get",
        success:function(data){
            var roleoptions = "<option value='-1'>--请选择--</option>";
            if(data.code==0){
                if(data.result){
                     $.each(data.result, function(i, role){ 
                            roleoptions+="<option value='"+role.id+"'>"+role.name+"</option>";
                            //roleoptions+="<option value='"+role.roleCode+"'>"+role.name+"</option>";
                     });
                }
                //选中下拉列表
                //$("#dptList").val(user.dptCode);
            }else{
                console.log("queryRoleListByOrgCode-->获取数据失败！");
            }
            $("#roleList").html(roleoptions);

        }
    });
}

$(function () {
	//业务类型搜索图标
	$(document).on("click", ".glyphicon-search", function () {
	    $(this).siblings("input").trigger("dblclick")
	})

	//业务类型 输入框双击 弹出业务类型列表
	$("#channelName").dblclick(function () {
	    utils.channelDialog('1').then( res => {
	        console.log(res);
	        let _str_name = '', _str_code = '';
	        let _str_arr = res.map(item => {
	            return item.channelName;
	        })
	        _str_name = _str_arr.join(',');

	        let _str_code_arr = res.map(item => {
	            return item.channelCode;
	        })
	        _str_code = _str_code_arr.join(',')
	        $('#channelName').val(_str_name)
	        $('#channelCode').val(_str_code)
	    })
	});
})

    // 初始化下拉多选控件
    $('#add_erp_orgList').selectpicker({
        actionsBox: true,
        selectAllText: '选择全部',
        deselectAllText: '取消全选'
    })
     //查询条件 选择机构
    function queryOrgList(orgCodes){
        $.ajax({
            url:"/proxy-sysmanage/sysmanage/org/getOrgList",
            data:{},
            type:"get",
            success:function(data){
                var roleoptions = "<option value='-1'>--请选择--</option>";
                if(data.code==0){
                    //角色
                    var add_orgList_val = $("#add_erp_orgList");
                    if(data.result){
                        var htm = '';
                        $.each(data.result, function(i, org){
                            htm += "<option value='"+org.orgCode+"'>"+ org.orgName + "</option>";
                        });
                        add_orgList_val.html("").append(htm);
                        console.log(orgCodes);
                        if(orgCodes) {
                            $('#add_erp_orgList').val(orgCodes.split(","));
                        }
                    }
                    $('#add_erp_orgList').selectpicker('refresh');
                }else{
                    console.log("queryRoleListByOrgCode-->获取数据失败！");
                }
            }
        });
    }
