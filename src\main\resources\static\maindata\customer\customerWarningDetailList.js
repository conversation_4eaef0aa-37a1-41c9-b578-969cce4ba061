$(function () {
    var infoId = $("#infoId").val();

    $('#table').XGrid({
        url: "/proxy-customer/customer/customerWarning/customerWarningDetailPageList",
        postData: {
            "statusStr": $("#statusStr option:selected").val(),
            "bdName": $("#bdName").val(),
            "customerName": $("#customerName").val(),
            "credentialType": $("#credentialType").val(),
            "infoId":$("#infoId").val()
        },

        colNames: ['','执行状态','更新完成时间', '预警发送时间',   '客户类型', '客户编码', '客户名称', '联系人', '联系电话', '营业执照号', '证书名称', '证书编号', '发证机关', '发证日期', '有效期至', '剩余效期','预警标识', 'BD姓名', 'BD电话', 'BD邮箱', 'BD钉钉'],
        colModel: [
            {
                name: 'id',
                hidden: true,
                key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            },{
                name: 'status',
                index: 'status',
                formatter: function (value) {
                    let statusAry = ['未处理', '已发送', '已完成'];
                    return statusAry[value]
                }
            },{
                name: 'finshTime',
                index: 'finshTime',
                formatter: function (value) {
                    if (value) {
                        let date = value;
                        if (!value) return false;
                        date = utils.formatDate(value);
                        return date;
                    } else {
                        return ''
                    }
                }
            }, {
                name: 'sendTime',
                index: 'sendTime',
                formatter: function (value) {
                    if (value) {
                        let date = value;
                        if (!value) return false;
                        date = utils.formatDate(value);
                        return date;
                    } else {
                        return ''
                    }

                }
            },{
                name: 'customerType',
                index: 'customerType'
            },
            {
                name: 'customerCode',
                index: 'customerCode'
            },
            {
                name: 'customerName',
                index: 'customerName'
            },
            {
                name: 'contact',
                index: 'contact'
            },
            {
                name: 'phone',
                index: 'phone'
            },
            // {
            //     name: 'mail',
            //     index: 'mail'
            // },
            {
                name: 'businessLicenseNum',
                index: 'businessLicenseNum'
            },
            {
                name: 'credentialType',
                index: 'credentialType'
            },
            {
                name: 'credentialCode',
                index: 'credentialCode'
            },
            {
                name: 'certificationOffice',
                index: 'certificationOffice'
            },
            {
                name: 'openingDate',
                index: 'openingDate',
                formatter: function (value) {
                    if (value) {
                        let date = value;
                        if (!value) return false;
                        date = utils.formatDate(value);
                        return date.split(' ')[0];
                    } else {
                        return ''
                    }
                }
            },
            {
                name: 'validUntil',
                index: 'validUntil',
                formatter: function (value) {
                    let date = value;
                    if (!value) return false;
                    date = utils.formatDate(value);
                    return date.split(' ')[0];
                }
            },
            {
                name: 'dueDay',
                index: 'dueDay'
            },
            {
                name: 'dueDayFlag',
                index: 'dueDayFlag',
                formatter:function (rowId, cellval, colpos, rwdat, _act) {
                    const dueDay = colpos.dueDay;
                    let result = ""
                    if (dueDay>30){
                        result = "临期(>30天)"
                    }else if(dueDay <= 30 && dueDay >= 0){
                        result = "临期(<=30天)"
                    }else if (dueDay < 0 && dueDay >= -30 ){
                        result = "已过期(1-30天)"
                    }else if (dueDay < -30 && dueDay >= -60){
                        result = "已过期(31-60天)"
                    }else if (dueDay < -60 && dueDay >= -90){
                        result = "已过期(61-90天)"
                    }else {
                        result = "已过期(91天以上)"
                    }
                    return result
                }
            },
            {
                name: 'bdName',
                index: 'bdName'
            },
            {
                name: 'bdPhone',
                index: 'bdPhone'
            },
            {
                name: 'bdMail',
                index: 'bdMail'
            },
            {
                name: 'bdDing',
                index: 'bdDing'
            }
        ],
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        ondblClickRow: function (id, c, a, b) {
        },
        onSelectRow: function (e, c, a, b) {
        },
        pager: '#grid-pager'
    });


    $("#searchBtn").on("click", function () {
        console.log( $("#status option:selected").val());
        console.log($("#bdName").val());
        console.log( $("#customerName").val());
        console.log( $("#credentialType").val());
        console.log($("#infoId").val());
        $('#table').XGrid('setGridParam', {
            postData: {
                "statusStr": $("#statusStr option:selected").val(),
                "bdName": $("#bdName").val(),
                "customerName": $("#customerName").val(),
                "credentialType": $("#credentialType").val(),
                "infoId":$("#infoId").val()
            },
            page: 1
        }).trigger('reloadGrid');
    });

    $("#exportBtn").on("click", function () {
        utils.dialog({
            title: '提示',
            content: "数据量大的时候耗时较长，请耐心等待。",
            okValue: '确定',
            ok: function () {
                // let body = document.body;
                // let form = $(body).find('form#searchForm');
                // $(form).attr("action", "/proxy-customer/customer/customerWarning/exportCustomerWarningExcel");
                // $(form).submit();
                let formData = $("#searchForm").serializeToJSON();
                utils.httpPost('/proxy-customer/customer/customerWarning/exportCustomerWarningExcel',formData)
            },
            cancelValue: '取消',
            cancel: function () {
            },
        }).showModal();
    });
})
