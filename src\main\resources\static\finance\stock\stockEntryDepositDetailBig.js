$(function () {

	getTotalCount();
	var sysOrgCode = $("#sysOrgCode").val();
	   //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

//商品名称 双击查询
    $('#productDesc').dblclick(function () {
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#productDesc').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#productDesc').val(data.productName);
                    $('#drugCode').val(data.productCode)
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    })
    
     //商品名称 搜索
    $('#productDesc').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+sysOrgCode, //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)
            console.log('333',result.data)
        },
        onNoneSelect: function (params, suggestions) {
            console.log('没选中回调函数');
            $("#drugCode").val('');
            $("#productDesc").val('');
        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            console.log(query, suggestions);

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
             console.log('container隐藏前回调', container)
        },
        
    });

    var totalTable = z_utils.totalTable;
    var totalTablea = z_utils.totalTablea;
    //仓库
    initStorageTypeSelect();
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/initBalance/findStockEntryDepositDetail',
        postData: {
            "byear": $("#byear").val(),
            "eyear": $("#eyear").val(),
            "startMonth": $("#startMonth").val(),
            "endMonth": $("#endMonth").val(),
            "type" : 2
          },
        colNames: [ '商品编码', '商品名称', '库房', '期初数量', '期初金额',
            '入库数量', '入库金额', '采购入库数量','采购入库金额','入库数量(不含移动入库)','入库金额(不含移动入库)','移动入库数量','移动入库金额','调账入库数量','调账入库金额',
            '盘盈数量', '盘盈金额', '盘亏数量', '盘亏金额', '出库数量', '出库金额',
            '销售出库数量','销售出库金额','出库数量(不含移动出库)','出库金额(不含移动出库)','移动出库数量','移动出库金额','调账出库数量','调账出库金额','报损出库数量','报损出库金额',
            '结存数量', '结存金额','开始月份','结束月份'
        ],
        colModel: [{
            name: 'drugCode'
        }, {
            name: 'drugName'
        }, {
            name: 'storageTypeStr'
        }, {
            name: 'initNumber'
        }, {
            name: 'initMoney'
        }, {
            name: 'rukuNumber'
        }, {
            name: 'rukuMoney'
        }, {
            name: 'caigourukuShuL'
        }, {
            name: 'caigourukuJinE'
        }, {
        	name: 'buhanyidongrukuShuL',
        	width:200
        }, {
            name: 'buhanyidongrukuJinE',
            width:200
        }, {
            name: 'yidongrukuShuL'
        }, {
            name: 'yidongrukuJinE'
        }, {
            name: 'tiaozhangrukuShuL'
        }, {
            name: 'tiaozhangrukuJinE'
        },  {
            name: 'panyingNumber'
        }, {
            name: 'panyingMoney'
        }, {
            name: 'pankuiNumber'
        }, {
            name: 'pankuiMoney'
        }, {
            name: 'chukuNumber'
        }, {
            name: 'chukuMoney'
        }, {
            name: 'xiaoshouchukuShuL'
        }, {
            name: 'xiaoshouchukuJinE'
        }, {
        	name: 'buhanyidongchukuShuL',
        	width:200
        }, {
            name: 'buhanyidongchukuJinE',
            width:200
        }, {
            name: 'yidongchukuShuL'
        }, {
            name: 'yidongchukuJinE'
        }, {
            name: 'tiaozhangchukuShuL'
        }, {
            name: 'tiaozhangchukuJinE'
        }, {
            name: 'baosunchukuShuL'
        }, {
            name: 'baosunchukuJinE'
        }, {
            name: 'jiecunNumber'
        }, {
            name: 'jiecunMoney'
        }, {
            name: 'startMonth'
        }, {
            name: 'endMonth'
        }],
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['initNumber','initMoney','rukuNumber','rukuMoney',
            				  'panyingNumber','panyingMoney','pankuiNumber','pankuiMoney',
            	              'chukuNumber','chukuMoney','baosunchukuShuL','baosunchukuJinE','jiecunNumber','jiecunMoney','buhanyidongrukuShuL','buhanyidongrukuJinE','buhanyidongchukuShuL','buhanyidongchukuJinE'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item, index) {

                if (item == 'rukuNumber' || item == 'initNumber' || item == "panyingNumber" || item == "pankuiNumber" || item == "chukuNumber" || item == "jiecunNumber" || item == "baosunchukuShuL" || item == "buhanyidongrukuShuL" || item == "buhanyidongchukuShuL") {
                    lastRowEle.find("td[row-describedby=" + item + "]").text(totalTablea(data, item))
                } else {

                    lastRowEle.find("td[row-describedby=" + item + "]").text(parseFloat(totalTable(data, item)).formatMoney('2', '', ',', '.'))
                }
                lastRowEle.find("td[row-describedby=" + item + "]").prop("title", "");
            });
        },
        pager: '#grid-pager',
        rownumbers: true
    });

   // 查询数据
   $('#searchBtn').bind('click', function () {
       $('#X_Table').XGrid('clearGridData');
       $('#X_Table').setGridParam({
            url: '/proxy-finance/finance/initBalance/findStockEntryDepositDetail',
            postData: {
              storageType: $("#storageType").val(),
              productDesc: $("#productDesc").val(),
              drugCode: $("#drugCode").val(),
              byear: $("#byear").val(),
              eyear: $("#eyear").val(),
              startMonth: $("#startMonth").val(),
              endMonth: $("#endMonth").val(),
              oldProductCode : $("#oldProductCode").val(),
              type : 2
            }
       }).trigger('reloadGrid');
       getTotalCount();
       
    })
            
   
   // 导出
   $('#exportBtn').bind('click', function () {
       utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
           return false;
       }).catch( () => {
           //原始处理逻辑代码
           var parames = [];
           parames.push({name: "byear", value: $("#byear").val()});
           parames.push({name: "eyear", value: $("#eyear").val()});
           parames.push({name: "startMonth", value: $("#startMonth").val()});
           parames.push({name: "endMonth", value: $("#endMonth").val()});
           parames.push({name: "storageType", value: $("#storageType").val()});
           parames.push({name: "productDesc", value: $("#productDesc").val()});
           parames.push({name: "drugCode", value: $("#drugCode").val()});
           parames.push({name: "oldProductCode", value: $("#oldProductCode").val()});
           Post("/proxy-finance/finance/initBalance/exportExcelRoprtListBig", parames);
       })
   })
   
    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
        $(temp_form).remove();
    }
   
   
    // 筛选列，集成到 xgrid.js 里了
    $("#set_tb_rows").click(function () {
        $('#X_Table').XGrid('filterTableHead');
    })
})


/**
 * 渲染仓库下拉列表
 */
function initStorageTypeSelectTemplate(data) {
    var html = template('storageType-tmp', {list: data});
    document.getElementById('storageType-div').innerHTML = html;
}

/**
 * 获取仓库
 */
function initStorageTypeSelect() {
    $.post("/proxy-sysmanage/sysmanage/dict/querycommonnotpage", {type: 10},
        function(data){
            console.log(data.result);
            if (data.code == 0) {
                var _data = data.result;
                initStorageTypeSelectTemplate(_data);
            }
        }, "json");
}



/*function yearFun() {
    var year = getYear();
    WdatePicker({
        dateFmt: 'yyyy',
        minDate: '2010',
        maxDate:  year
    })
}
function startMonthFun() {
    WdatePicker({
        dateFmt: 'MM',
        realDateFmt:'MM',
        maxDate: getMaxMonth()
    })
}
function endMonthFun() {
    WdatePicker({
        dateFmt: 'MM',
        minDate: '#F{$dp.$D(\'startMonth\')}',
        maxDate: '%y-%M'
    })
}*/

//  开始年月
function startTime() {
    WdatePicker({
        dateFmt: 'yyyy-MM',
        maxDate:'#F{$dp.$D(\'eyear\')}'
    })
}

//  结束年月
function endTime() {
    WdatePicker({
        dateFmt: 'yyyy-MM',
        minDate: '#F{$dp.$D(\'byear\')}',
    })
}

/**
 * 获取当前年份
 */
function getYear() {
    var date = new Date();
    var year = date.getFullYear() + 10;
    return year;
}


/**
 * 获取最大月份
 */
function getMaxMonth() {
    var result,
        date = new Date(),
        curMonth = date.getMonth() + 1,
        endMonth = $('#endMonth').val();
    curMonth = curMonth>9?curMonth+'': '0'+ curMonth;
    if(parseFloat(curMonth)>parseFloat(endMonth)){
        result = endMonth;
    }else {
        result = curMonth;
    }
    return result;
}



$("#byear").blur(function(){
	var by = $(this).val();
	var ey = $("#eyear").val();
	if(by > ey){
		$(this).val("");
		
	}
	console.log(by)
})


$("#eyear").blur(function(){
	var ey = $(this).val();
	var by = $("#byear").val();
	if(by > ey){
		$(this).val("");
		
	}
	console.log(ey)
})



 function getTotalCount () {
            //加载总数量
            $.ajax({
                url: '/proxy-finance/finance/initBalance/stockDetailSum',
                dataType: 'json',
                timeout: 8000, //6000
                data:{
                	 storageType: $("#storageType").val(),
                     productDesc: $("#productDesc").val(),
                     drugCode: $("#drugCode").val(),
                     byear: $("#byear").val(),
                     eyear: $("#eyear").val(),
                     startMonth: $("#startMonth").val(),
                     endMonth: $("#endMonth").val(),
                     oldProductCode : $("#oldProductCode").val()
                },
                success: function (data) {
                    if (data.code == 0 && null != data.result) {
                        $("#initMoneySum").text(parseFloat(data.result.initMoneySum).formatMoney('2', '', ',', '.'));
                        $("#rukuMoneySum").text(parseFloat(data.result.rukuMoneySum).formatMoney('2', '', ',', '.'));
                        $("#panyingMoneySum").text(parseFloat(data.result.panyingMoneySum).formatMoney('2', '', ',', '.'));
                        $("#pankuiMoneySum").text(parseFloat(data.result.pankuiMoneySum).formatMoney('2', '', ',', '.'));
                        $("#chukuMoneySum").text(parseFloat(data.result.chukuMoneySum).formatMoney('2', '', ',', '.'));
                        $("#jiecunMoneySum").text(parseFloat(data.result.jiecunMoneySum).formatMoney('2', '', ',', '.'));
                        $("#buhanyidongrukuJinE").text(parseFloat(data.result.buhanyidongrukuJinE).formatMoney('2', '', ',', '.'));
                        $("#buhanyidongchukuJinE").text(parseFloat(data.result.buhanyidongchukuJinE).formatMoney('2', '', ',', '.'));
                    } else {
                        $("#initMoneySum").text('0.00');
                        $("#rukuMoneySum").text("0.00");
                        $("#panyingMoneySum").text("0.00");
                        $("#pankuiMoneySum").text('0.00');
                        $("#chukuMoneySum").text("0.00");
                        $("#jiecunMoneySum").text("0.00");
                        $("#buhanyidongrukuJinE").text("0.00");
                        $("#buhanyidongchukuJinE").text("0.00");
                    }
                   
                },
                error: function () {
                    $("#initMoneySum").text('0.00');
                    $("#rukuMoneySum").text("0.00");
                    $("#panyingMoneySum").text("0.00");
                    $("#pankuiMoneySum").text('0.00');
                    $("#chukuMoneySum").text("0.00");
                    $("#jiecunMoneySum").text("0.00");
                    $("#buhanyidongrukuJinE").text("0.00");
                    $("#buhanyidongchukuJinE").text("0.00");
                    utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
                }
            });
        
    }