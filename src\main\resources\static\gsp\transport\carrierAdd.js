
$(function(){
    $('div[fold=head]').fold({sub: 'sub'});
	 //附件管理
    $('#table1').XGrid({
    	data: [],    	
        colNames: ['','证件类型','最后上传日期', '最后操作员', '查看附件', '附件地址集合'],
        colModel: [{
            name: 'id',
            index: 'id',
            key: true,
            hidden: true
        }, {
            name: 'licenseType',
            index: 'licenseType',
            rowtype:'#licenseType'
        }, {
            name: 'uploadDate',
            index: 'uploadDate',
            rowtype:'#uploadDate'
        }, {
            name: 'createUserName',
            index: 'createUserName',
            rowtype:'#createUserName'
        }, {
            name: 'licensePhotoCount',
            index: 'licensePhotoCount',
            formatter: function (value) {
	          var str = '无';
	          if (value) {
	              str = '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
	          }
	          return str;
		    },
		    unformat: function (e) {
	          e = e.replace(/<[^>]+>/g, '');
	          if (e == '无') {
	              e = 0;
	          }
	          return e;
		    }       	
        }, {
            name: 'licensePhotoList',
            index: 'licensePhotoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],        
        rowNum: 100,
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_page3',
        gridComplete: function () {
        	
        }
    });    
    
    //运输员信息
    $('#table2').XGrid({
        data: [],
        colNames: ['','姓名', '性别标识', '性别', '驾驶证号','驾照类型', '维护日期'],
        colModel: [{
            name: 'id',
            index: 'id',
            key: true,
            hidden: true
        }, {
            name: 'driverName',
            index: 'driverName'
        }, {
            name: 'sex',
            index: 'sex',
            hidden: true
        }, {
            name: 'sexValue',
            index: 'sexValue'
        }, {
            name: 'drivingLicenseCode',
            index: 'drivingLicenseCode'
        },{
            name: 'drivingLicenseType',
            index: 'drivingLicenseType'
        }, {
            name: 'maintainDate',
            index: 'maintainDate'
//            	,
//        	 formatter:function (e){
//     	        if (e != null && e !="") {
//     	            return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
//     	        } else {
//     	            return "";
//     	        }
//     	    }
        }],
        rowNum: 100,
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_page4',
        gridComplete: function () {

        }
    });
    
    //运输车辆信息
    $('#table3').XGrid({
        data: [],
        colNames: ['', '车牌号', '车辆类型', '维护日期'],
        colModel: [{
            name: 'id',
            index: 'id',
            key: true,
            hidden: true
        }, {
            name: 'carNumber',
            index: 'carNumber'
        }, {
            name: 'vehicleType',
            index: 'vehicleType'
        }, {
            name: 'maintainDate',
            index: 'maintainDate'
//            	,
//            formatter:function (e){
//    	        if (e != null && e !="") {
//    	            return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
//    	        } else {
//    	            return "";
//    	        }
//    	    }
        }],
        rowNum: 100,
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_page4',
        gridComplete: function () {

        }
    });
    // 返回
    $("#close").on("click",function(){
        utils.closeTab();
    });

    //关闭按钮
    $("#closePage").on("click", function () {
        utils.dialog({
            title: "提示",
            content: "关闭后该流程信息将丢失，无法重新审核，是否确认关闭？",
            width:300,
            height:30,
            okValue: '确定',
            ok: function () {
            	$.ajax({            
                    type: "POST",                    
                    url: "/proxy-gsp/gsp/transport/carrierClose",
                    data: {
                    	carrierCode:$('#carrierCode').val()
                    },
                    success: function (result) {            	
                        if (result.code == 0) {   
                        	 utils.dialog({content: '审核关闭成功！', timeout: 2000}).showModal();
                             setTimeout(function(){
                                 utils.closeTab();
                             },2000)                        	
                        } else {
                        	utils.dialog({content: '审核关闭异常！', quickClose: true, timeout: 4000}).showModal();                        	
                        };
                    },
                    error : function() {
                    	utils.dialog({content: '系统出错了', quickClose: true, timeout: 4000}).showModal();                    	
                    }
                });                 
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    });
    //保存草稿
    $("#saveRowData").on("click",function(){    	
    	//获取form数据
        var baseData = $('#form_Base').serializeToJSON(); 
        
        if(baseData.email!=null && baseData.email!='' && 
        		!validatEmail(baseData.email)) {
        	utils.dialog({content: '请输入正确电子邮箱', quickClose: true, timeout: 2000}).showModal();
        	return false;
        }
        if(baseData.sourceArray != null && baseData.sourceArray.length > 0 && 
        		!jQuery.isArray(baseData.sourceArray)) {
        	var sourceArray = [];
        	sourceArray.push(baseData.sourceArray);
        	baseData.sourceArray = sourceArray;
        }        
    
        var licenseList = [];
        licenseList = $('#table1').XGrid('getRowData');       
        var driverList = new Array();
        driverList = $('#table2').XGrid('getRowData');        
        var vehicleList = new Array();
        vehicleList = $('#table3').XGrid('getRowData');        
					
        for(var i=0;i<licenseList.length;i++) {        	
        	licenseList[i].licensePhotoList = JSON.parse(licenseList[i].licensePhotoList);		        		        	
        }
        
        baseData.licenseList = licenseList;
        baseData.driverList = driverList;
        baseData.vehicleList = vehicleList;
        $.ajax({            
            type: "POST",
            dataType: "json",
            contentType:"application/json", 
            url: "/proxy-gsp/gsp/transport/saveCarrier",
            data: JSON.stringify(baseData),
            success: function (result) {            	
                if (result.code == 0) {                	
                	utils.dialog({content: '保存成功！', timeout: 2000}).showModal();
                    setTimeout(function(){
                        utils.closeTab();
                    },2000)
                } else {
                	utils.dialog({content: '保存失败！', quickClose: true, timeout: 4000}).showModal();
                };
            },
            error : function() {
            	utils.dialog({content: '系统出错了！', quickClose: true, timeout: 4000}).showModal();
            }
        });		
        
    });
    
    //提交审核
    $("#submitAssert").on("click",function(){
        utils.dialog({
            title: "提示",
            content: "是否确认将流程提交至下一岗进行审核？",
            width:300,
            height:30,
            okValue: '确定',
            ok: function () {
            	//获取form数据
                var baseData = $('#form_Base').serializeToJSON();
                
                if(baseData.sourceArray != null && baseData.sourceArray.length > 0 && 
                		!jQuery.isArray(baseData.sourceArray)) {
                	var sourceArray = [];
                	sourceArray.push(baseData.sourceArray);
                	baseData.sourceArray = sourceArray;
                }
            	//空值判断
            	var flag = isNotNullVaid(baseData);            	
                if(flag) {	
                	if(!validatEmail(baseData.email)) {
                    	utils.dialog({content: '请输入正确电子邮箱', quickClose: true, timeout: 2000}).showModal();
                    	return false;
                    }
	                var licenseList = [];
	                licenseList = $('#table1').XGrid('getRowData');       
	                var driverList = new Array();
	                driverList = $('#table2').XGrid('getRowData');        
	                var vehicleList = new Array();
	                vehicleList = $('#table3').XGrid('getRowData');
	                if(licenseList == null || licenseList.length == 0) {
	    	        	utils.dialog({content: '附件管理不能为空', quickClose: true, timeout: 2000}).showModal();
	    	        	flag = false;	    	        	
	    	        }
	    			if(flag && (driverList == null || driverList.length == 0)) {
	    				utils.dialog({content: '运输员信息不能为空', quickClose: true, timeout: 2000}).showModal();
	    				flag = false; 	    				
	    			}
	    			if(flag && (vehicleList == null || vehicleList.length == 0)) {
	    				utils.dialog({content: '运输车辆信息不能为空', quickClose: true, timeout: 2000}).showModal();
	    				flag = false;  	    				
	    			}
	    			
	    			if(flag) {	    				
	    		        for(var i=0;i<licenseList.length;i++) {        	
	    		        	licenseList[i].licensePhotoList = JSON.parse(licenseList[i].licensePhotoList);	    		        		        	
	    		        }
	    		        if(flag) {		                
			                baseData.licenseList = licenseList;
			                baseData.driverList = driverList;
			                baseData.vehicleList = vehicleList; 
			            	$.ajax({            
			                    type: "POST",
			                    dataType: "json",
			                    contentType:"application/json", 
			                    url: "/proxy-gsp/gsp/transport/submitApprove",
			                    data: JSON.stringify(baseData),
			                    success: function (result) {            	
			                        if (result.code == 0) {
			                        	utils.dialog({content: '提交审核成功！', timeout: 2000}).showModal();
					                    setTimeout(function(){
					                        utils.closeTab();
					                    },2000)
			                        } else {
			                        	utils.dialog({content: '提交审核失败！', quickClose: true, timeout: 4000}).showModal();
			                        };
			                    },
			                    error : function() {
			                    	utils.dialog({content: '系统出错了！', quickClose: true, timeout: 4000}).showModal();
			                    }
			                });	
	    		        }
	    			}
                }                
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    });
    
    //审核通过
    $('#auditApproval').on('click',function(){     	
    	utils.dialog({
            title: '审核通过',
            content: $('#tg_dialog'),
            okValue: '确定',
            width: 500,
            height: 260,
            ok: function () { 
            	var approveComment = $("#approveComment").val();
            	if(!isNotNull(approveComment)) {		
            		utils.dialog({content: '审核留言不能为空', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}
            	$.ajax({            
                    type: "POST",                    
                    url: "/proxy-gsp/gsp/transport/carrierApprove",
                    data: {
                    	carrierCode:$("#carrierCode").val(),
                    	comment:$("#approveComment").val(),
                    	taskId:$("#taskId").val(),
                    	processId:$("#processId").val(),
                    	edit:$("#edit").val(),
                    	close:$("#close").val(),
                    	agree:$("#agree").val(),
                    	reject:$("#reject").val(),
                    	report:$("#report").val()
                    },
                    success: function (result) {            	
                        if (result.code == 0) {
                        	utils.dialog({content: '审核通过！', timeout: 2000}).showModal();
					                    setTimeout(function(){
					                        utils.closeTab();
					                    },2000)
                        } else {
                        	utils.dialog({content: '审核异常！', quickClose: true, timeout: 4000}).showModal();
                        };
                    },
                    error : function() {
                    	utils.dialog({content: '系统出错了', quickClose: true, timeout: 4000}).showModal();
                    }
                });            	
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    	$("#approveComment").val('');        
    })
    
    //批准文件切换
    $('.nav-tabs>li').on('click', function (){
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    });    

    
  //(运输员)删除行
    $("#fjDelRow").click(function () {
    	var seleRow = $('#table1').XGrid('getSeleRow');
    	if (seleRow != null && seleRow.length > 0) {
	        utils.dialog({
	            title: '删除提示',
	            content: '是否确认删除附件？',
	            okValue: '确定',
	            ok: function () {
	            	$.each(seleRow, function (index, item) {
	            		$('#table1').XGrid('delRowData', item.id); 
	            	})
	            },
	            cancelValue: '取消',
	            cancel: function () {}
	        }).show(); 
    	} else {
            utils.dialog({
                content: '请选择要删除的数据',
                timeout: 2000
            }).show()
        }
    });    
    
    //(运输员)新增行
    $('#ysyAddRow').on('click', function () {   	
        utils.dialog({
            title: '新增运输员',
            content: $('#ysy_dialog'),
            okValue: '保存',
            width: 500,
            height: 260,
            ok: function () { 
            	var driverName = $('#driverName').val();
            	var sex = $('#sex').val();
            	var drivingLicenseCode = $('#drivingLicenseCode').val();
            	var drivingLicenseType = $('#drivingLicenseType').val();
            	   	
            	var sexValue = '';
            	if(sex == '0') {
            		sexValue = '男';
            	} else if(sex == '1') {
            		sexValue = '女';
            	}
            	if(!isNotNull(driverName)) {		
            		utils.dialog({content: '姓名不能为空', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}
            	
            	if(sex != '0' && sex != '1') {		
            		utils.dialog({content: '请选择性别', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}
            	
            	if(!isNotNull(drivingLicenseCode)) {		
            		utils.dialog({content: '驾驶证号不能为空', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}
            	
            	if(!isNotNull(drivingLicenseType)) {		
            		utils.dialog({content: '请选择驾照类型', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}
            	
            	
            	var rowData = $('#table2').XGrid('getRowData');
            	if (rowData != null && rowData.length > 0) {
            		for(var i=0; i<rowData.length; i++) {
            			if(drivingLicenseCode == rowData[i].drivingLicenseCode) {
            				utils.dialog({content: '驾驶证号不能重复', quickClose: true, timeout: 2000}).showModal();            				
            				return false;
            			}            			
            		}
            	}
            	
            	var maintainDate = $('#nowDate').val();
            	
                $('#table2').XGrid('addRowData',{
                	driverName: driverName,
                	sex: sex,
                	sexValue: sexValue,
                	drivingLicenseCode: drivingLicenseCode,
                	drivingLicenseType: drivingLicenseType,
                	maintainDate:maintainDate
                });
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
        $('#driverName').val('');
    	$('#sex').val('2');
    	$('#drivingLicenseCode').val('');
    	$('#drivingLicenseType').val('');
    });
    
    //(运输员)删除行
    $("#ysyDelRow").click(function () {
    	var seleRow = $('#table2').XGrid('getSeleRow');
    	if (seleRow != null && seleRow.length > 0) {
	        utils.dialog({
	            title: '删除提示',
	            content: '是否确认删除运输员信息？',
	            okValue: '确定',
	            ok: function () {
	            	$.each(seleRow, function (index, item) {
	            		$('#table2').XGrid('delRowData', item.id); 
	            	})
	            },
	            cancelValue: '取消',
	            cancel: function () {}
	        }).show(); 
    	} else {
            utils.dialog({
                content: '请选择要删除的数据',
                timeout: 2000
            }).show()
        }
    });
    
    //(运输车辆)新增行
    $('#ysclAddRow').on('click', function () {
        utils.dialog({
            title: '新增运输车辆',
            content: $('#yscl_dialog'),
            okValue: '保存',
            width: 500,
            height: 160,
            ok: function () {            	
            	var vehicleType = $('#vehicleType').val();
            	var carNumber = $('#carNumber').val();
            	if(!isNotNull(vehicleType)) {		
            		utils.dialog({content: '车辆类型不能为空', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}
            	if(!isNotNull(carNumber)) {		
            		utils.dialog({content: '车牌号不能为空', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}             	
            	            	
            	var rowData = $('#table3').XGrid('getRowData');
            	if (rowData != null && rowData.length > 0) {
            		for(var i=0; i<rowData.length; i++) {
            			if(carNumber == rowData[i].carNumber) {
            				utils.dialog({content: '车牌号不能重复', quickClose: true, timeout: 2000}).showModal();            				
            				return false;
            			}            			
            		}
            	}
            	
            	var maintainDate = $('#nowDate').val();
            	 $('#table3').XGrid('addRowData',{
            		 vehicleType: vehicleType,
            		 carNumber: carNumber,
            		 maintainDate:maintainDate
                 });
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
        $('#carNumber').val('');
    	$('#vehicleType').val('');
    });
    
    //(运输车辆)删除行
    $("#ysclDelRow").click(function () {
    	var seleRow = $('#table3').XGrid('getSeleRow');
    	//console.log(seleRow.length);
    	if (seleRow != null && seleRow.length > 0) {
	        utils.dialog({
	            title: '删除提示',
	            content: '是否确认删除车辆信息？',
	            okValue: '确定',
	            ok: function () {
	            	$.each(seleRow, function (index, item) {
	            		$('#table3').XGrid('delRowData', item.id); 
	            	})
	            },
	            cancelValue: '取消',
	            cancel: function () {}
	        }).show();
    	} else {
            utils.dialog({
                content: '请选择要删除的数据',
                timeout: 2000
            }).show()
        }
    }); 
    
    var sources= new Array();
    sources = $("#sourceHidden").val().split(",");
   // console.log(sources);
    $(".source").each(function(){
    	var source = $(this).val();    	
    	for(var i = 0; i < sources.length; i++) {  		
    		if(source == sources[i]) {
//    			console.log(source +','+ sources[i]);
    			$(this).attr("checked",true);    			
    			break;
    		}
    	}    	   	
    });
    
    var qualityHidden = $("#qualityHidden").val();   
    if(qualityHidden != null && qualityHidden != '') {
    	$("#quality").find("option[value='" + qualityHidden + "']").attr("selected",true);
    }
    var thermalControl = $("#transportQualityHidden").val();   
    $("#transportQuality").find("option[value='" + thermalControl + "']").attr("selected",true);
    var thermalControl = $("#thermalControlHidden").val();   
    $("#thermalControl").find("option[value='" + thermalControl + "']").attr("selected",true);
    
    if(pageStatus == 1) {
    	if(licenseList != null && licenseList.length >0 && licenseList[0] != null) {
    		addRowDataTable1('#table1', licenseList);
    	}
    	if(driverList != null && driverList.length >0 && driverList[0] != null) {
    		addRowDataTable2('#table2', driverList);
    	}
    	if(vehicleList != null && vehicleList.length >0 && vehicleList[0] != null) {
    		addRowDataTable3('#table3', vehicleList);
    	}
//	    console.log(erpConfigList);	    
    }
  //附件管理上传附件
    upLoadFile("#fjUpload", '#table1', 'licenseType'); 
//    新增附件
    addRow('#fjAddRow', '#table1');
//    deleRow('#fjAddRow', '#table1');
})

//新增行
function addRow(buttonId, tableId) {

    $(buttonId).on("click", function () {
        var rowNumber = $(tableId).find("tr").not(":first").length + 1;
        $(tableId).addRowData({id: rowNumber})
    });
}

//删除行
function deleRow(buttonId, tableId, fn) {
    $(buttonId).on("click", function () {
        var selectRow = $(tableId).XGrid('getSeleRow');
        if (!selectRow) {
            utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
        } else {
            $(tableId).XGrid('delRowData', selectRow.id);
            utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
        }
        setTimeout(function () {
            fn && fn();
        }, 1)
    });
}

function addRowDataTable1(table, list){	
//	console.log(list);
	$.each(list, function (index, item) {		
		$('#table1').XGrid('addRowData',{
			licenseType:item.licenseType,
			uploadDate:item.uploadDate,
			createUserName:item.createUserName,
			licensePhotoCount:item.licensePhotoCount,
			licensePhotoList:item.licensePhotoList			
		});
	})    
}

function addRowDataTable2(table, list){	
//	console.log(list);
	$.each(list, function (index, item) {		
		$('#table2').XGrid('addRowData',{			
			driverName: item.driverName,    
			sex: item.sex, 
			sexValue: item.sexValue, 
			drivingLicenseCode: item.drivingLicenseCode,           
			drivingLicenseType: item.drivingLicenseType,          
			maintainDate: item.maintainDate          
		});
	})    
}

function addRowDataTable3(table, list){		
	$.each(list, function (index, item) {		
		$('#table3').XGrid('addRowData',{			
			carNumber: item.carNumber,
			vehicleType: item.vehicleType,
			maintainDate: item.maintainDate
		});
	})    
}

//附件详情
function showImg(obj) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(obj).parents("table").getRowData(parentId);
   
//    console.log('data2=='+JSON.stringify(data.licensePhotoList));    
//    console.log('data2=='+data.licensePhotoList.length);    
    if(data.licensePhotoList.length > 0)
    {
        $.viewImg({
            fileParam:{
                name:'name',
                url:'url'
            },
            list:data.licensePhotoList
        })
    }
}

/**
 * 上传附件
 * 
 * */
function upLoadFile(obj, table, typeName){	
	$(obj).on("click", function () {
    	//获取type类型
		var typeList=[];
		var valList=[];
		var eChoImgList=[];
		var $table=$(table);
		var rowData=$table.getRowData();
		var $tr=$table.find("tr").not(":first");
		for(var i=0;i<$tr.length;i++) {
			var dataVal='';
			var val='';			
		    var el=$tr.eq(i).find("[name='"+typeName+"']").get(0);
		    dataVal=$.trim($(el).val());		   
		    if(dataVal == '' || !dataVal) {
		    	utils.dialog({content: '请选择附件类型,表格除序号外第一列', quickClose: true, timeout: 2000}).showModal();
		    	return false;
		    }
		    if(el.tagName == 'INPUT') {
		    	val = $(el).val();
		    }else if(el.tagName == 'SELECT'){
		    	val = $(el).find("option:selected").val()
		    }
		    valList.push(val);
		}
//		console.log('valList,'+ valList);
		for(var i=0;i<$tr.length;i++) {
			var dataVal='';
			var dataJson={};
		    var el=$tr.eq(i).find("[name='"+typeName+"']").get(0);
		    dataVal=$.trim($(el).val());		   
		    if(dataVal == '' || !dataVal) {
		    	utils.dialog({content: '请选择附件类型,表格除序号外第一列', quickClose: true, timeout: 2000}).showModal();
		    	return false;
		    }
		    if(el.tagName == 'INPUT') {
		    	dataJson={
				        text:$(el).val(),
				        value:$(el).val(),
                        lineNum: i
				    };
		    }else if(el.tagName == 'SELECT'){
		    	dataJson={
				        text:$(el).find("option:selected").text(),
				        value:$(el).find("option:selected").val(),
                        lineNum: i
				    };
		    }
		    typeList.push(dataJson);
		    licensePhotoList = JSON.parse(rowData[i].licensePhotoList);	
		    
		    //添加已存在附件
		    if(licensePhotoList.length > 0) {		    	
		    	for(var f=0;f<licensePhotoList.length;f++){
		    		licensePhotoList[f].type=dataVal;
		    		licensePhotoList[f].lineNum = i;
                    
		    	}
		        eChoImgList = eChoImgList.concat(licensePhotoList);
		    }
		}
		
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'name',
                url:'url'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}              	
//            	console.log(data);
            	 var uploadDate = $('#nowDate').val();
            	 var createUserName = $('#loginName').val();
            	 
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'licensePhotoList':[]});
                            $table.setRowData(id,{'licensePhotoCount':''});     
                            $table.setRowData(trId,{'uploadDate':uploadDate});
                        	$table.setRowData(trId,{'createUserName':createUserName});
                        }
                    })
                    return false;
                }          
              
                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];                    
                    var count = 0;
                    var count2 = 0;
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()                         
                        if(rowData[j]['licenseType'] == data[i].type){                        	
                            l.push(list);
                            count2 ++;
                            var licensePhotoList2 = JSON.parse(rowData[j].licensePhotoList);
                        	for(var k=0;k<licensePhotoList2.length;k++){
                        		if(licensePhotoList2[k].url == data[i].url) {
                        			count ++;
                        			break;
                        		}
                        	}                            
                        }                        
                    }
                    
//                    console.log('count=' + count+","+'count2=' + count2 + ',uploadDate' + uploadDate + ',createUserName' + createUserName);
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    
                    $table.setRowData(trId,{'licensePhotoList':l});
                    $table.setRowData(trId,{'licensePhotoCount':(l.length == 0?'':l.length)});   
                    if(rowData[j]['createUserName'] == null || rowData[j]['createUserName'] == ''
                    	|| count != count2){  
                    	$table.setRowData(trId,{'uploadDate':uploadDate});
                    	$table.setRowData(trId,{'createUserName':createUserName});
                    }
                    
                }

            }
        });
    })
}

//获取select框修改前的值
function getOptText(el,ev) {
    var _optText = '';
    var opts = $(el).find('option');
    $(opts).each(function (k,v) {
        if( $(this).val() == ev){ _optText =  $(this).text();}
    })
    return _optText
}
function isNotNullVaid(baseData) {
	if(!isNotNull(baseData.carrierName)) {		
		utils.dialog({content: '承运单位名称不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.postCode)) {		
		utils.dialog({content: '邮政编码不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.email)) {		
		utils.dialog({content: '电子邮箱不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.category)) {		
		utils.dialog({content: '拟承运品种不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.address)) {		
		utils.dialog({content: '地址不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.contact)) {		
		utils.dialog({content: '联系人不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.telephone)) {		
		utils.dialog({content: '联系电话不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.licenseCode)) {		
		utils.dialog({content: '许可证号不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.companyManager)) {		
		utils.dialog({content: '企业负责人不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.licenseOrg)) {		
		utils.dialog({content: '许可证发证机关不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.businessLicense)) {		
		utils.dialog({content: '营业执照号不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.licenseDateStr)) {		
		utils.dialog({content: '许可证发证日期不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.licenseExpiryDateStr)) {		
		utils.dialog({content: '许可证有效期不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.licenseAddress)) {		
		utils.dialog({content: '许可证地址不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.corporation)) {		
		utils.dialog({content: '法定代表人不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.businessLicenseOrg)) {		
		utils.dialog({content: '发证机关不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.economicNature)) {		
		utils.dialog({content: '经济性质不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.registerCapita)) {		
		utils.dialog({content: '注册资本不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.businessLicenseDateStr)) {		
		utils.dialog({content: '营业执照发证日期不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.businessLicenseExpiryDateStr)) {		
		utils.dialog({content: '营业期限至不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.registerAddress)) {		
		utils.dialog({content: '注册地址不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else if(!isNotNull(baseData.carrier)) {		
		utils.dialog({content: '承运人员不能为空', quickClose: true, timeout: 2000}).showModal();
		return false;
	} else {
		return true;
	}    
}

function isNotNull(value) {
    if (value != "" && value != null && value != undefined ) {
        return true;
    } else {
    	return false;
    }
    
}

function validatInt(obj) {
	if(obj.value.length==1){
		obj.value=obj.value.replace(/[^0-9]/g,'')
	} else {
		obj.value=obj.value.replace(/\D/g,'')
	}
}

function validatEmail(email) {	
    var reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/;
    return reg.test(email);	
}

/*
 * 校验是邮箱
 */
function validEmail(val){
	var regu=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
	var re = new RegExp(regu);
	return  re.test(val);
}


