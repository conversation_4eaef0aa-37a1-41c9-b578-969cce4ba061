$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    /* 日期初始化 */
    z_utils.initDate('begint', 'endt')
    /* 合计计算  */
    var totalTable = z_utils.totalTable;

    var drugClassType = ""

    /*查询页面总计*/
    //getTotalNum (0);
    /* table_a */
    //data
    var grid_dataY = [];
    var g_item = {
        text1: "2",
        text2: "",
        text3: "",
        text4: "",
        text5: "",


        text6: "",
        text7: "",
        text8: "",
        text9: "",
        text10: "",
        text11: "",
        text12: "2",
        text13: "",
        text14: "",
        text15: "4",
        text16: "6",
        text17: "",
        text18: "",
        text19: "",
        text20: "",
    };
    var g_item1 = {
        text1: "1",
        text2: "",
        text3: "",
        text4: "",
        text5: "",
        text6: "",
        text7: "",
        text8: "",
        text9: "",
        text10: "",
        text11: "",
        text12: "2",
        text13: "",
        text14: "",
        text15: "4",
        text16: "6",
        text17: "",
        text18: "",
        text19: "待处理",
        text20: ""
    };
    for (var i = 0; i < 20; i++) {
        if (i === 0) {
            grid_dataY.push(g_item1);
        } else {
            grid_dataY.push(g_item);
        }
    }
    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d + ' '+ h+':'+minute+':'+second;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }


    function formatDateTime2(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        // return y + '-' + m + '-' + d;
        return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }



    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    var colName = ['id', '单据编码', '商品大类', '原商品编码', '商品编码','商品名称', '通用名','商品规格', '剂型','单位',
        '生产厂家', '库房名称', '业务类型', '批号', '生产日期', '有效期至', '锁定数量',
        '不含税成本单价', '不含税成本金额', '锁定状态', '锁定日期', '解锁操作人', '解锁时间'
    ];

    //, '不含税收入金额 '
    var colModel = [{
        name: 'id',
        index: 'id',
        key: true,
        hidden: true,
        hidegrid: true
    }, {
        name: 'orderCode',
        index: 'orderCode',
        width: 200
    }, {
        name: 'drugClass',
        index: 'drugClass',
        width: 200
    }, {
        name: 'originalDrugCode',
        index: 'originalDrugCode'
    }, {
        name: 'drugCode',
        index: 'drugCode'
    }, 	{
        name: 'drugName',
        index: 'drugName'
    },
        {
            name: 'commonName',
            index: 'commonName',
            width: 220
        }, {
            name: 'specifications',
            index: 'specifications'
        },{
            name: 'dosageForm',
            index: 'dosageForm'
        },{
            name: 'unit',
            index: 'unit'
        },{
            name: 'manufName',
            index: 'manufName'
        },{
            name: 'storageType',
            index: 'storageType'
        },
        {
            name: 'channelId',
            index: 'channelId'
        },
        {
            name: 'batchNum',
            index: 'batchNum'
        }, {
            name: 'productDate',
            index: 'productDate',
            formatter:dateFormatter
        }, {
            name: 'validateDate',
            index: 'validateDate',
            formatter:dateFormatter
        }, {
            name: 'amountIn',
            index: 'amountIn',
            width: 220
        }, {
            name: 'priceIn',
            index: 'priceIn',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'notaxSum',
            index: 'notaxSum',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        },{
            name: 'yn',
            index: 'yn'
        }, {
            name: 'orderDate',
            index: 'orderDate',
            formatter:dateFormatter
        }, {
            name: 'updateUser',
            index: 'updateUser'
        },{
            name: 'updateTime',
            index: 'updateTime',
            formatter:formatDateTime2

        }];
    var urlObjectList = [];
    $('#table_a').XGrid({
        url: "/proxy-storage/storage/lock/listStorageGoodsLock?storageType=1&begint=" + $('#begint').val() + "&endt=" + $('#endt').val() + "&drugName=" + $('#drugName').val() + "&drugCode=" + $('#drugCode').val()
            + "&batchNum=" + $('#batchNum').val() + "&originalDrugCode=" + $('#originalDrugCode').val() + "&drugClass=" + $('#drugClass').val() + "&channelId=" + $("#channelId").find("option:selected").val(),

        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'id',
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            // var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut','inventoryAmount','inventorySum'];
            var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
        },
        onSelectRow: function (id, dom, obj, index, event) {

            setUrlObjectList(dom,id,obj);

            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            //console.log(id, dom, obj, index, event)
        },

    });

    function setUrlObjectList(dom,id,obj){
        var orderCode = obj.orderCode
        if(urlObjectList.indexOf(orderCode) == "-1"){
            urlObjectList.push(orderCode);
        }else{
            urlObjectList.map(function (x,i) {
                console.log('------',x,i)
                if(x == orderCode){
                    urlObjectList.splice(i,1);
                }
            })
        }
        console.log('=======',urlObjectList)
    }



    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    };

    // 筛选列
    $("#setRow").click(function () {
        //获取当前显示表格
        $('#table_a').XGrid('filterTableHead');
    })

    /* 商品名称 搜索提示（只显示5条） */
    var ts1 = [{
        value: 'Andorra',
        data: 'AD'
    },
        {
            value: 'Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '2Andorra',
            data: 'AD'
        },
        {
            value: '2Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '3Andorra',
            data: 'AD'
        },
        {
            value: '3Zimbabwe',
            data: 'ZZ'
        }
    ];

    $('#input_goodName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + $("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#input_goodName").val("");
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });


    //获取当前tab  的下标
    function getTabInd() {
        for (var i = 0; i < $('.leder_tabs li').length; i++) {
            if ($('.leder_tabs li').eq(i).hasClass('active')) {
                _ind = $('.leder_tabs li').eq(i).index();
            }
        }
        return _ind;
    }


    /* 查询 */
    function initTable(index){
        var  url = "/proxy-storage/storage/lock/listStorageGoodsLock";

        //获取form数据  填写的查询条件
        var data = $('#form_a').serializeToJSON();

        console.log(data);
        //更新表格数据
        var $table_id = $('#nav_content .active .XGridBody table').attr('id');
        if ($table_id == 'table_a') {
            //列表
            $('#table_a').XGrid('setGridParam', {
                url: url,
                postData: data
            }).trigger("reloadGrid");
        }
    }

    /* 查询 */
    $('#listStorageGoodsLock').on('click', function (e) {
        btn_search();
    });

    function btn_search(){
        //列表
        $('#table_a').XGrid('setGridParam', {
            url: "/proxy-storage/storage/lock/listStorageGoodsLock",
            postData: {
                drugName: $('#drugName').val(),
                drugCode: $('#drugCode').val(),
                batchNum: $("#batchNum").val(),
                originalDrugCode: $("#originalDrugCode").val(),
                drugClass: $("#drugClass").val(),
                storageType: $("#storageType").find("option:selected").val(),
                begint: $('#begint').val(),
                endt: $("#endt").val(),
                channelId: $("#channelId").find("option:selected").val()
            }
        }).trigger("reloadGrid");
    }

    /**导出禁售效期商品*/
    $('#exportLockProduct').click(function () {
        var tableId = $('#table_a').attr('id');
        //获取当前选中项
        console.log(tableId);
        var data =  $('#table_a').XGrid('getSeleRow');
        console.log(data);
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'));
                    colNameDesc.push($(this).parent().text());
                }
            });

            var exportLimitRow = Number($("#exportLimitRow").val());
            if (data.length <= exportLimitRow && data.length >0) {
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
            } else {
                var rows = $('#' + tableId)[0].p.records;
                if(rows > exportLimitRow) {
                    utils.dialog({
                        title: '提示',
                        width: 200,
                        height: 40,
                        cancel: false,
                        content: '您本次导出的数据量过大（已超过'+exportLimitRow+'条），不允许导出，请缩小导出范围。',
                        okValue: '确定',
                        ok: function () {
                            //utils.closeTab();
                        }

                    }).showModal();
                    return;
                }
                data = '';
            }

            console.log(colName);

            //获取form数据
            var formData = {
                colName: colName,
                colNameDesc: colNameDesc,
                selectData: data,

                drugName: $('#drugName').val(),
                drugCode: $('#drugCode').val(),
                batchNum: $("#batchNum").val(),
                originalDrugCode: $("#originalDrugCode").val(),
                drugClass: $("#drugClass").val(),
                storageType: $("#storageType").find("option:selected").val(),
                createTime: $('#begint').val(),
                updateTime: $("#endt").val(),
                fileName: '禁售效期商品锁定表格',
                orgCode: $("#orgCode").val(),
                channelId: $("#channelId").find("option:selected").val()
            }
            httpPost("/proxy-storage/storage/lock/exportLockProduct", formData);
        });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#drugCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })



    /* 解锁禁效期商品*/
    $('#updateUnlock').on('click', function (e) {
        var selectRow = $('#table_a').XGrid('getSeleRow');
        var orderCodeList = '';
        if (selectRow.length) {
            var flag = true;
            selectRow.forEach(function (item) {
                if (item.yn=='已解锁'){
                    utils.dialog({
                        title: '提示',
                        content:"商品["+item.drugCode+"]已解锁,请重新选择",
                        width: 400,
                        okValue: '确定',
                        ok: function () {

                        }
                    }).showModal();

                    flag = false;

                }
            });

            if (!flag){
                return;
            }


            utils.dialog({
                title: '温馨提示',
                content: '是否确认解除锁定选中商品？',
                okValue: '确定',
                ok: function () {
                    selectRow.forEach(function (item) {
                        orderCodeList +=item.orderCode+','
                    })

                    orderCodeList = orderCodeList.substr(0,orderCodeList.length-1);
                    $.ajax({
                        url: "/proxy-storage/storage/lock/updateUnlock",
                        type: "post",
                        dataType: "json",
                        data: {"orderCode":orderCodeList},//{orderId: selectDate[i].id, orderCode: selectDate[i].orderCode},
                        success: function (result) {
                            console.log(result)
                            if (result.result == 1) {
                                utils.dialog({
                                    title: '提示',
                                    content:"解锁成功",
                                    width: 400,
                                    okValue: '确定',
                                    ok: function () {
                                        //刷新页面
                                        btn_search();
                                    }
                                }).showModal();
                            } else {
                                utils.dialog({
                                    title: '解锁失败',
                                    content: "",
                                    width: 400,
                                    okValue: '确定',
                                    ok: function () {
                                    }
                                }).showModal();
                            }
                        }
                    })

                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal()

        } else {
            utils.dialog({content: '请选择要解除锁定的商品！', quickClose: true, timeout: 2000}).showModal();
        }

    })



})


