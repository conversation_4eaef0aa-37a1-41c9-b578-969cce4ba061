$(function () {
    var totalTable = z_utils.totalTable;
    //开始日期
    /* $('#beginTime').bind('click', function () {
         WdatePicker({el: 'beginTime', maxDate: '#F{$dp.$D(\'endTime\')}'});
     })

     //结束日期
     $('#endTime').bind('click', function () {
         WdatePicker({el: 'endTime', minDate: '#F{$dp.$D(\'beginTime\')}'});
     })
 */
    function getMaxDate(id) {
        var startTime = $('#' + id).val();
        return moment(startTime).add('31', 'days').format("YYYY-MM-DD");
    }


    function getMinDate(id) {
        var startTime = $('#' + id).val();
        return moment(startTime).subtract('31', 'days').format("YYYY-MM-DD");
    }

    //判断两个时间是否相差30天
    function isBetween(beginId, endId) {
        var firstTime = $('#' + beginId).val(),
            lastTime = $('#' + endId).val();

        if (moment(lastTime).isBefore(getMaxDate(beginId)) && moment(firstTime).isAfter(getMinDate(endId))) {
            return true;
        }

        return false;

    }


    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '1', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            //选中了
            window.isSelect = true;
            $("#supplierNo").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue', result.value);

            console.log(result)
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName',
            delivery: 'delivery',
            deliveryPhone: 'deliveryPhone'
        },
        onNoneSelect: function (params, suggestions) {
            //没选中
            $("#supplierNo").val("");
            $("#supplierName").val("");
            window.isSelect = false;
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });

    //放大镜查询
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if (e.keyCode === 13 && !$("#supplierName").attr('oldvalue')) {
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#supplierNo").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                } else {
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();
    }


    var d;
    var colNames = ['供应商编号', '供应商名称', '制单日期', '过账日期', '单据类型', '单据编号', '借方', '贷方', '余额', '制单人', '备注', 'id'],
        colModel = [
            {
                name: 'supplierNo',
                width: 180,
            }, {
                name: 'supplierName',
                width: 220,
            },
            {
                name: 'billTime',
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
                width: 120,
            },
            {
                name: 'tallyTime',
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
                width: 120,
            }, {
                name: 'billType',
                width: 120,
            }, {
                name: 'billNo',
                width: 220,
            }, {
                name: 'debteAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 220,
            }, {
                name: 'creditAmount',
                width: 220,
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                editable: true
            }, {
                name: 'balance',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 220
            }, {
                name: 'billUser',
                width: 100,
            }, {
                name: 'remarks',
                width: 200,
                title: true,
//                 colMouseenter: function (eType) {
//                     var content =  eType.rowData.certificateRate;
//                     if(!content) return false;
//                     d = dialog({
//                         align: 'top',
//                         padding: 10,
//                         content: '<textarea style="max-width: 220px;height:100%;resize: none;border: none;outline:none;text-align: center;" rows="1" readonly>' + content + '</textarea>',
//                         onshow: function () {
//                             var height = $(this.node).find('textarea')[0].scrollHeight;
//                             this.height(height);
//                         }
//                     })
//                     d.show(eType.e.target);
//                 },
//                 colMouseout: function (eType) {
//                     d.close().remove();
//                 },
                formatter: function (val) {
                    // val = val.replace(/\"/g, '“').replace(/\'/, '’');
                    return transEntity(val, true);
                }
            }, {
                name: 'id',
                hidden: true,
                hidegrid: true
            }
        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    window.isFirst = true;
    //设置table高度
    //utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierPayRecords',
        // url: 'http://localhost:8080/account/find',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        attachRow: true,
        multiselect: true,
        postData: {
            supplierName: $("#supplierName").val(),
            keyWord: $("#keyWord").val(),
            startDate: $("#beginTime").val(),
            endDate: $("#endTime").val()
        },
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
            var billNo = a.billNo;
            var type = a.billType;
            showDetail(billNo, type);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
        gridComplete: function () {
            var _this = $(this);
            if (!window.isFirst && !_this.XGrid('getRowData').length) {
                utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
            }
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['debteAmount', 'creditAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item, index) {
                // lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                lastRowEle.find("td[row-describedby=" + item + "]").text(parseFloat(totalTable(data, item)).formatMoney('2', '', ',', '.'))
            });
        }
    });
    totalSum();


    // 手动核销
    $('#shoudongHeXiaoBtn').bind('click', function () {
        //console.log(validform("myform").form());
        //if (validform("searchForm").form()) {//验证通过 "myform"为需要验证的form的ID
        console.log(2222);
        var param = $('#searchForm').serializeToJSON();
        var seleData = $('#X_Table').XGrid('getSeleRow');
        //var idList = [];
        var idList = "";
        if (seleData) {
            if (seleData.length) {
                $.each(seleData, function (index, item) {
                    idList += item.id + ","
                })
            } else {
                var id = seleData.id || '';
                idList = id + ",";
            }
        } else {
            utils.dialog({content: '请选择供应商往来账', quickClose: true, timeout: 2000}).show();
            return false;
        }

        var customerCode = seleData.length ? seleData[0].supplierNo : seleData.supplierNo;
        console.log(idList);
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/shoudongHeXiao",
            data: {
                supplierNo: customerCode,
                ids: idList
            },
            dataType: 'json',
            cache: false
        }).done(function (data) {
            if (data.code == 0) {
                utils.dialog({
                    content: "手动核销成功", quickClose: true,
                    timeout: 3000
                }).showModal();
                setTimeout(function () {
                    $("#searchBtn").trigger("click");
                }, 500);
            } else {
                utils.dialog({
                    content: "手动核销失败", quickClose: true,
                    timeout: 3000
                }).showModal();
            }
        });
        //} else {//验证不通过
        //utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
        //return false;
        //}
    })

    //自动核销
    $('#zidongHeXiaoBtn').bind('click', function () {
        if ($('#beginTime').val() == '') {
            utils.dialog({content: '请选择开始日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#endTime').val() == '') {
            utils.dialog({content: '请选择结束日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }

        if (!isBetween('beginTime', 'endTime')) {
            utils.dialog({content: '起始日期与结束日期不得大于31天！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        utils.dialog({
            title: '提示',
            content: '将对所选日期范围的数据进行自动核销，是否继续？',
            width: 285,
            cancelValue: '否',
            cancel: function () {
            },
            okValue: '是',
            ok: function () {
                $.ajax({
                    method: "POST",
                    url: "/proxy-finance/finance/purchase/payrequestinfo/zidongHeXiao",
                    data: {
                        startDate: $("#beginTime").val(),
                        endDate: $("#endTime").val()
                    },
                    dataType: 'json',
                    cache: false
                }).done(function (data) {
                    if (data.code == 0) {
                        utils.dialog({
                            content: "核销成功，本次核销成功供应商" + data.result.supplierTotal + "条，核销成功单据" + data.result.total + "条",
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                        setTimeout(function () {
                            $("#searchBtn").trigger("click");
                        }, 500);

                    } else {
                        utils.dialog({
                            content: "自动核销失败", quickClose: true,
                            timeout: 3000
                        }).showModal();
                    }
                });
            }
        }).showModal();
        return false;


    })

    //取消核销
    $('#quxiaoHeXiaoBtn').bind('click', function () {
        if ($("#supplierNo").val() == '') {
            if ($('#beginTime').val() == '') {
                utils.dialog({content: '请选择开始日期！', quickClose: true, timeout: 2000}).show();
                return false;
            }
            if ($('#endTime').val() == '') {
                utils.dialog({content: '请选择结束日期！', quickClose: true, timeout: 2000}).show();
                return false;
            }
        }

        if (!isBetween('beginTime', 'endTime')) {
            utils.dialog({content: '起始日期与结束日期不得大于31天！', quickClose: true, timeout: 2000}).show();
            return false;
        }

        utils.dialog({
            title: '提示',
            content: '将对所选日期范围内的数据取消核销，是否继续？',
            width: 285,
            cancelValue: '否',
            cancel: function () {
            },
            okValue: '是',
            ok: function () {
                $.ajax({
                    method: "POST",
                    url: "/proxy-finance/finance/purchase/payrequestinfo/quxiaoHeXiao",
                    data: {
                        startDate: $("#beginTime").val(),
                        endDate: $("#endTime").val(),
                        supplierNo: $("#supplierNo").val()
                    },
                    dataType: 'json',
                    cache: false
                }).done(function (data) {
                    if (data.code == 0) {
                        utils.dialog({
                            content: "取消核销成功，本次取消核销供应商" + data.result.supplierTotal + "条，取消核销单据" + data.result.total + "条",
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                        setTimeout(function () {
                            $("#searchBtn").trigger("click");
                        }, 500);
                        // $("#searchBtn").trigger("click");
                    } else {
                        utils.dialog({
                            content: "取消核销失败", quickClose: true,
                            timeout: 3000
                        }).showModal();
                    }
                });
            }
        }).showModal();
        return false;
    })

    $("#searchBtn").on("click", function () {
        window.isFirst = false;
        //提交前验证
        //console.log(validform("myform").form());
        if (validform("searchForm").form()) {//验证通过 "myform"为需要验证的form的ID
            var param = $('#searchForm').serializeToJSON();
            console.log(param);
            $('#X_Table').XGrid('setGridParam', {
                url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierPayRecords',
                postData: {
                    supplierNo: $("#supplierNo").val(),
                    supplierName: $("#supplierName").val(),
                    keyWord: $("#keyWord").val(),
                    startDate: $("#beginTime").val(),
                    endDate: $("#endTime").val(),
                    page: 1
                }
            }).trigger('reloadGrid');
        }
        totalSum();
    });

    //设置显示列
    $("#setBtn").on("click", function () {
        $('#X_Table').XGrid('filterTableHead');
    });


    //导出
    $('#exportBtn').on('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()), 1).then(() => {
            return false;
        }).catch(() => {
            //原始处理逻辑代码
            if (validform("searchForm").form()) {
                //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                var ck = false;
                // copy this parameter and the below buttons
                var nameModel = "";
                var exportColNames = ['供应商编号', '供应商名称', '制单日期', '过账日期', '单据类型', '单据编号', '借方', '贷方', '余额', '制单人', '备注'];
                addHtmlA(exportColNames);
                dialog({
                    content: $("#setCol"),
                    title: '筛选列',
                    width: $(window).width() * 0.4,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {
                        var newColName = [], newColModel = [];
                        $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                            if ($(this).is(":checked")) {
                                nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                            }
                        });
                        if (nameModel.length == 0) {
                            utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                        // var keyword = $("#keyword").val();
                        // var createTimeStart = $("#createTimeStart").val();
                        // var createTimeEnd = $("#createTimeEnd").val();
                        // var obj = $("#searchForm").serializeToJSON();
                        var obj = {
                            supplierNo: $("#supplierNo").val(),
                            supplierName: $("#supplierName").val(),
                            keyWord: $("#keyWord").val(),
                            startDate: $("#beginTime").val(),
                            endDate: $("#endTime").val()

                        }
                        // obj["pageNum"] = "1";
                        // obj["pageSize"] = "1000000";
                        obj["nameModel"] = nameModel;
                        httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportSupplierPayRecords", obj);
                    },
                    // copy button to other dialogues
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if (!ck) {
                                    $("#checkRow input").prop("checked", false);
                                    ck = true;
                                } else if (ck) {
                                    $("#checkRow input").prop("checked", "checked");
                                    ck = false;
                                } else {
                                    return false;
                                }
                                ;
                                return false;
                            }
                        }
                    ]
                    //copy ends here
                }).showModal();
            } else {
                utils.dialog({content: '请选择供应商', quickClose: true, timeout: 2000}).show();
                return false;
            }
        })
    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none; padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    function showDetail(billNo, type) {
        var title = "";
        var url = "";
        var height = 0;
        console.log(3000)
        if (type.includes('发票调整单')) {
            title = '发票调整单详情';
            url = '/proxy-finance/finance/purchase/invoice/getAdjustInvoiceDetailPage?billNo=' + billNo;
            height = 200;
        } else if (type.includes('发票')) {
            title = "采购发票详情";
            url = '/proxy-finance/finance/purchase/payrequestinfo/detailInvoiceInfo?invoiceNo=' + billNo;
            height = $(window).height() * 0.8;
        } else {
            title = '采购付款单详情';
            url = '/proxy-finance/finance/purchase/payrequestinfo/toPaymentDetail?billNo=' + billNo;
            height = $(window).height() * 0.4;
        }
        utils.dialog({
            title: title,
            url: url,
            width: $(window).width() * 0.8,
            height: height,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                $('iframe').remove();
            }
        }).show();
    }


    function totalSum() {
        $.ajax({
            url: '/proxy-finance/finance/purchase/payrequestinfo/totalSumSupplierPayRecords',
            type: 'post',
            data: {
                supplierNo: $("#supplierNo").val(),
                supplierName: $("#supplierName").val(),
                keyWord: $("#keyWord").val(),
                startDate: $("#beginTime").val(),
                endDate: $("#endTime").val()
            },
            dataType: 'json',
            success: function (result) {
                console.log(result)
                if (result.code == 0) {
                    var data = result.result;
                    if (data.sumDebteAmount) {
                        $("#sumDebteAmount").text(parseFloat(data.sumDebteAmount).formatMoney('2', '', ',', '.'));
                        // $("#sumDebteAmount").text(Number(data.sumDebteAmount).toFixed(2));
                    } else {
                        $("#sumDebteAmount").text('0.00');
                    }
                    if (data.sumCreditAmount) {
                        $("#sumCreditAmount").text(parseFloat(data.sumCreditAmount).formatMoney('2', '', ',', '.'));
                        // $("#sumCreditAmount").text(Number(data.sumCreditAmount).toFixed(2));
                    } else {
                        $("#sumCreditAmount").text('0.00');
                    }
                    /*  if(data.sumBalance){
                         // $("#sumBalance").text(parseFloat(data.sumBalance).formatMoney('2', '', ',' ,'.'));
                          $("#sumBalance").text(data.sumBalance.toFixed(2));
                          //$("#sumBalance").text(data.sumBalance);
                      }else{
                          $("#sumBalance").text(0);
                      }*/

                } else {
                    $("#sumDebteAmount").text('0.00');
                    $("#sumCreditAmount").text('0.00');
                }

            }
        })
    }
})