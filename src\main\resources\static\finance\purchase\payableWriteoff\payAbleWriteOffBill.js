$(function () {
	// getTotalCount();
    function getMaxDate(id) {
        var startTime = $('#' + id).val();
        return moment(startTime).add('31', 'days').format("YYYY-MM-DD");
    }


    function getMinDate(id) {
        var startTime = $('#' + id).val();
        return moment(startTime).subtract('31', 'days').format("YYYY-MM-DD");
    }

    //判断两个时间是否相差30天
    function isBetween(beginId, endId) {
        var firstTime = $('#' + beginId).val(),
            lastTime = $('#' + endId).val();

        if (moment(lastTime).isBefore(getMaxDate(beginId)) && moment(firstTime).isAfter(getMinDate(endId))) {
            return true;
        }

        return false;

    }


    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '1', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            //选中了
            window.isSelect = true;
            $("#supplierNo").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);

            console.log(result)
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName',
            delivery: 'delivery',
            deliveryPhone: 'deliveryPhone'
        },
        onNoneSelect: function (params, suggestions) {
            //没选中
            $("#supplierNo").val("");
            $("#supplierName").val("");
            window.isSelect = false;
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    //放大镜查询
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#supplierNo").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();
    }


    var d;
    var colNames = ['供应商编号', '供应商名称', '核销日期', '核销单号', '核销类型', '单据号', '单据金额', '本次核销金额','剩余未核销金额', '付款单据类型', '付款单据号', '付款单据金额', 'id'],
        colModel = [
            {
                name: 'supplierNo',
                index: 'supplierNo',
            }, {
                name: 'supplierName',
                index: 'supplierName',
            },
            {
                name: 'createTime',
                index: 'createTime',
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            },
            {
                name: 'writeOffNo',
                index: 'writeOffNo',
                width: 170
            }, {
                name: 'writeOffType',
                index: 'writeOffType',
                //包含3中核销类型，发票付款单、红蓝发票、红蓝付款单；
            }, {
                name: 'billNo',
                index: 'billNo',
                width: 170
            }, {
                name: 'billAmount',
                index: 'billAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
            }, {
                name: 'writeOffAmount',
                index: 'writeOffAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                sortable: false,
                editable: true,
            },{
                name: 'unWriteOffAmount',
                index: 'unWriteOffAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                sortable: false,
                editable: true,
            }, {
                name: 'payBillType',
                index: 'payBillType',
                //发票付款单-->付款单;红蓝发票-->采购发票;红蓝付款单-->付款单
            }, {
                name: 'payBillNo',
                index: 'payBillNo',
                width: 170
            },{
                name: 'payAmount',
                index: 'payAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                sortable: false,
                editable: true,
            }, {
                name: 'id',
                hidden: true,
                hidegrid: true
            }
        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    var totalTable = z_utils.totalTable;
    window.isFirst = true;
    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/payablewriteoff/findPayAbleWriteOffDetail',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        multiselect: true,
        attachRow:true,
        postData: {
            supplierName: $("#supplierName").val(),
            startDate: $("#beginTime").val(),
            endDate: $("#endTime").val(),
            writeOffNo: $("#writeOffNo").val(),
            billNo: $("#billNo").val()
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
        gridComplete: function () {
        	 /* 合计行 */
       	    var data = $(this).XGrid('getRowData');
            var sum_models = ['billAmount','writeOffAmount','unWriteOffAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                // lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        	
            // var _this = $(this);
            // if (!window.isFirst && !_this.XGrid('getRowData').length) {
            //     utils.dialog({content: '无查询数据', quickClose: true, timeout: 2000}).show();
            // }
        }
    });

    //取消核销
    $('#quxiaoHeXiaoBtn').bind('click', function () {
        seleRow(function (ary) {
            console.log(ary);
            //执行取消操作: 删除核销单，回写对应单据的核销状态及核销金额
            $.ajax({
                url: "/proxy-finance/finance/purchase/payablewriteoff/cancelWriteOff",
                data: {
                    "writeOffNos":JSON.stringify(ary)
                },
                type: "post",
                success: function (result) {
                    if (result.code == 0) {
                        utils.dialog({
                            content: result.result,
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                        $('#X_Table').XGrid('setGridParam', {
                            url: '/proxy-finance/finance/purchase/payablewriteoff/findPayAbleWriteOffDetail',
                            postData: {
                                supplierName: $("#supplierName").val(),
                                startDate: $("#beginTime").val(),
                                endDate: $("#endTime").val(),
                                writeOffNo: $("#writeOffNo").val(),
                                billNo: $("#billNo").val()
                            },
                            page: 1
                        }).trigger("reloadGrid");
                    } else {
                        utils.dialog({
                            content: result.result,
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                    }
                }
            })
        })
    });

    /* 获取付款申请单选中项,并获取单据数据(组装) */
    function seleRow(callback) {
        var ary = []; // 核销单号	array
        var sele_data = $("#X_Table").XGrid("getSeleRow");
        if(sele_data){
            if(!$.isArray(sele_data)){
                ary.push(sele_data.writeOffNo)
            }else {
                sele_data.forEach(function (item,index) {
                    ary.push(item.writeOffNo);
                })
            }
        }else {
            utils.dialog({content: '请选择需要取消的核销单！', quickClose: true, timeout: 2000}).show();
            return;
        }
        callback(ary);
    }

    $("#searchBtn").on("click", function () {
        window.isFirst = false;
        //提交前验证
        //console.log(validform("myform").form());
        if (validform("searchForm").form()) {//验证通过 "myform"为需要验证的form的ID
            var param = $('#searchForm').serializeToJSON();
            console.log(param);
            $('#X_Table').XGrid('setGridParam', {
                url: '/proxy-finance/finance/purchase/payablewriteoff/findPayAbleWriteOffDetail',
                postData: {
                    supplierNo: $("#supplierNo").val(),
                    supplierName: $("#supplierName").val(),
                    startDate: $("#beginTime").val(),
                    endDate: $("#endTime").val(),
                    writeOffNo: $("#writeOffNo").val(),
                    billNo: $("#billNo").val(),
                    page: 1
                }
            }).trigger('reloadGrid');
        }

    });

    //设置显示列
    $("#setBtn").on("click", function () {
        $('#X_Table').XGrid('filterTableHead');
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
})