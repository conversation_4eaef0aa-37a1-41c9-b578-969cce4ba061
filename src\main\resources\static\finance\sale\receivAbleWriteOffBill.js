$(function () {
	// getTotalCount();
    function getMaxDate(id) {
        var startTime = $('#' + id).val();
        return moment(startTime).add('31', 'days').format("YYYY-MM-DD");
    }


    function getMinDate(id) {
        var startTime = $('#' + id).val();
        return moment(startTime).subtract('31', 'days').format("YYYY-MM-DD");
    }

    //判断两个时间是否相差30天
    function isBetween(beginId, endId) {
        var firstTime = $('#' + beginId).val(),
            lastTime = $('#' + endId).val();

        if (moment(lastTime).isBefore(getMaxDate(beginId)) && moment(firstTime).isAfter(getMinDate(endId))) {
            return true;
        }

        return false;

    }
    //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
//客户名称双击查询
    $("#customerName").dblclick(function () {
        utils.dialog({
            title: '客户列表',
            url: '/proxy-finance/finance/sale/financeSaleController/toCustomerList', 
            width: $(window).width() * 0.8,
            height: 600,
            data: $('#input_custorm').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#customerName").val(data.customerName);
                    $("#customerNumber").val(data.customerCode)
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    })


    initCustomerAutocomplete();

    /**
     * 初始化客户模糊搜索
     */
    function initCustomerAutocomplete() {
        //单据编号
        $('#customerName').Autocomplete({
            serviceUrl: '/customerBaseAppl/pageList', //异步请求
            paramName: 'customerCode',//查询参数，默认 query
            dataType: 'json',
            //lookup: countries, //监听数据 value显示文本，data为option的值
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            triggerSelectOnValidInput: false, // 必选
            transformResult: function (response) {
                return {
                    suggestions: $.map(response.result.list, function (dataItem) {
                        return {
                            value: dataItem.customerName,
                            data: dataItem.customerCode
                        };
                    })
                };
            },

            // showNoSuggestionNotice: true, //显示查无结果的container
            // noSuggestionNotice: '查询无结果',//查无结果的提示语
            // tabDisabled: true,
            onSelect: function (result) {
                $("#intercourseNum").val(result.data);
                $("#customerNumber").val(result.data);
                //选中回调
                // alert('You selected: ' + result.value + ', ' + result.data);
                // console.log('选中回调')
            },
            onSearchStart: function (params) {
                // console.log('检索开始回调', params)
            },
            onSearchComplete: function (query, suggestions) {
                //匹配结果后回调
                var isFocus = $(this).is(":focus");
                if(!isFocus){
                    $('#customerName').Autocomplete().hide();
                }
            },
            onSearchError: function (query, jqXHR, textStatus, errorThrown) {
                //查询失败回调
                //console.log(query, jqXHR, textStatus, errorThrown)
            },
            onHide: function (container) {
                // console.log('container隐藏前回调', container)
            },
            onNoneSelect: function (params, suggestions) {
                $("#intercourseNum").val("");
                // console.log(params, suggestions);
                // console.log('没选中回调函数');
                $("#customerNumber").val('');
                $('#customerName').val('')
            }
        });
    }



    var d;
    var colNames = ['客户编码', '客户名称', '核销日期', '核销单号', '核销类型', '单据号', '单据金额', '本次核销金额','剩余未核销金额', '收款单据类型', '收款单据号', '收款单据金额', 'id'],
        colModel = [
            {
                name: 'customerNumber',
                index: 'customerNumber',
                width: 200,//宽度
            }, {
                name: 'customerName',
                index: 'customerName',
                width: 60
            },
            {
                name: 'createTime',
                index: 'createTime',
                width: 150,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            },
            {
                name: 'writeOffNo',
                index: 'writeOffNo',
                width: 150
            }, {
                name: 'writeOffType',
                index: 'writeOffType',
                width: 150
                //包含3中核销类型，发票付款单、红蓝发票、红蓝付款单；
            }, {
                name: 'billNo',
                index: 'billNo',
                width: 150
            }, {
                name: 'billAmount',
                index: 'billAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 250,
            }, {
                name: 'writeOffAmount',
                index: 'writeOffAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 250,
                sortable: false,
                editable: true,
            },{
                name: 'unWriteOffAmount',
                index: 'unWriteOffAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 250,
                sortable: false,
                editable: true,
            }, {
                name: 'payBillType',
                index: 'payBillType',
                width: 150
                //发票付款单-->付款单;红蓝发票-->采购发票;红蓝付款单-->付款单
            }, {
                name: 'payBillNo',
                index: 'payBillNo',
                width: 150
            },{
                name: 'payAmount',
                index: 'payAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 250,
                sortable: false,
                editable: true,
            }, {
                name: 'id',
                hidden: true,
                hidegrid: true
            }
        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    var totalTable = z_utils.totalTable;
    window.isFirst = true;
    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/sale/receivablewriteoff/findReceivAbleWriteOffDetail',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        multiselect: true,
        attachRow:true,
        postData: {
            customerName: $("#customerName").val(),
            startDate: $("#beginTime").val(),
            endDate: $("#endTime").val(),
            writeOffNo: $("#writeOffNo").val(),
            billNo: $("#billNo").val()
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
        gridComplete: function () {
        	 /* 合计行 */
       	    var data = $(this).XGrid('getRowData');
            var sum_models = ['billAmount','writeOffAmount','unWriteOffAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        	
            // var _this = $(this);
            // if (!window.isFirst && !_this.XGrid('getRowData').length) {
            //     utils.dialog({content: '无查询数据', quickClose: true, timeout: 2000}).show();
            // }
        }
    });

    //取消核销
    $('#quxiaoHeXiaoBtn').bind('click', function () {
        seleRow(function (ary) {
            console.log(ary);
            //执行取消操作: 删除核销单，回写对应单据的核销状态及核销金额
            $.ajax({
                url: "/proxy-finance/finance/sale/receivablewriteoff/cancelWriteOff",
                data: {
                    "writeOffNos":JSON.stringify(ary)
                },
                type: "post",
                success: function (result) {
                    if (result.code == 0) {
                        utils.dialog({
                            content: result.result,
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                        $('#X_Table').XGrid('setGridParam', {
                            url: '/proxy-finance/finance/sale/receivablewriteoff/findReceivAbleWriteOffDetail',
                            postData: {
                                customerName: $("#customerName").val(),
                                startDate: $("#beginTime").val(),
                                endDate: $("#endTime").val(),
                                writeOffNo: $("#writeOffNo").val(),
                                billNo: $("#billNo").val()
                            },
                            page: 1
                        }).trigger("reloadGrid");
                    } else {
                        utils.dialog({
                            content: result.result,
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                    }
                }
            })
        })
    });

    /* 获取付款申请单选中项,并获取单据数据(组装) */
    function seleRow(callback) {
        var ary = []; // 核销单号	array
        var sele_data = $("#X_Table").XGrid("getSeleRow");
        if(sele_data){
            if(!$.isArray(sele_data)){
                ary.push(sele_data.writeOffNo)
            }else {
                sele_data.forEach(function (item,index) {
                    ary.push(item.writeOffNo);
                })
            }
        }else {
            utils.dialog({content: '请选择需要取消的核销单！', quickClose: true, timeout: 2000}).show();
            return;
        }
        callback(ary);
    }

    $("#searchBtn").on("click", function () {
        window.isFirst = false;
        //提交前验证
        //console.log(validform("myform").form());
        if (validform("searchForm").form()) {//验证通过 "myform"为需要验证的form的ID
            var param = $('#searchForm').serializeToJSON();
            console.log(param);
            $('#X_Table').XGrid('setGridParam', {
                url: '/proxy-finance/finance/sale/receivablewriteoff/findReceivAbleWriteOffDetail',
                postData: {
                    customerNumber: $("#customerNumber").val(),
                    customerName: $("#customerName").val(),
                    startDate: $("#beginTime").val(),
                    endDate: $("#endTime").val(),
                    writeOffNo: $("#writeOffNo").val(),
                    billNo: $("#billNo").val(),
                    page: 1
                }
            }).trigger('reloadGrid');
        }

    });

    //设置显示列
    $("#setBtn").on("click", function () {
        $('#X_Table').XGrid('filterTableHead');
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
})