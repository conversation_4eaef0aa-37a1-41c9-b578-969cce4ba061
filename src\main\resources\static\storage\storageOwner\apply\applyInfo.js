var taskId = null;
$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);
    taskId = utils.getQueryString('taskId');
    if(param.type == 1){
        let dom = `<button type="button" class="btn btn-info" onclick="auditPassCilck()">
                        审批通过
                    </button>
                    <button type="button" class="btn btn-info" style="background-color:#FB9B1F;border-color: #FB9B1F;" onclick="auditRejectCilck()">
                        审批驳回
                    </button>`
        $('#newButton').html(dom);
    }
    var processId = $('#approvalProcessId').text(); //后续需修改，默认展示90047
    // var type = "${type!}";
    // var CloseprocessId = "${approvalCloseProcessId!}"; //后续需修改，默认展示90047
    var hasQueryProcessNode = false;//审批流是否请求完成
    // var currentProcessNode = '';//审批流当前处理节点
    if(processId){
        $.ajax({
            type: "GET",
            url: "/proxy-storage/storage/StorageOwner/apply/queryTotle?processInsId=" + processId,
            async: false,
            error: function () {
                utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
            },
            success: function (data) {
                if (data.code == 0) {
                    if (data.result != null) {
                        $('.flow').process(data.result);
                        // currentProcessNode = getCurrentProcessNode(data.result);
                        // $('#tip').html(setTipsByApproveFlow(currentProcessNode))
                    }
                } else {
                    utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
                }
            },
            complete:()=>{
                hasQueryProcessNode = true
            }
        });
    }else if($('#orderStatus').text == '审核中' ){
        let dom = `<button type="button" class="btn btn-info" id="auditPass">
                        复核通过
                    </button>
                    <button type="button" class="btn btn-info" id="auditReject" style="background-color:#FB9B1F;border-color: #FB9B1F;">
                        驳回
                    </button>`
        $('#newButton').html(dom);
    }
        // if (CloseprocessId != "") {
        //     $.ajax({
        //         type: "POST",
        //         url: "/proxy-purchase/purchase/purchaseRefundProductOrder/queryTotle?processInstaId=" + CloseprocessId,
        //         async: false,
        //         error: function () {
        //             utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
        //         },
        //         success: function (data) {
        //             if (data.code == 0) {
        //                 if (data.result != null) {
        //                     $('.flow1').process(data.result);
        //                 }
        //             } else {
        //                 utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
        //             }
        //         }
        //     });
        // }

    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    /* table_a */
    var colName = ['id','商品编码','原商品编码','商品大类','商品名称', '规格/型号','单位', '生产厂家', '库房名称', '批号', '生产日期', '有效期至', '神农可用库存',
          '调整前业务类型','本次调整数量', '调整后业务类型', '备注'
    ];
    // '不含税成本单价',
    var colModel = [{
        name: 'id',
        index: 'id',
        hidden:true,
        hideGrid:true
    },{
        name: 'productCode',
        index: 'productCode'
    },{
        name: 'oldProductCode',
        index: 'oldProductCode'
    },{
        name:'drugClass',
        index:'drugClass'
    } ,{
        name: 'productName',
        index: 'productName'
    }, {
        name: 'specifications',
        index: 'specifications'
    },{
        name: 'packingUnit',
        index: 'packingUnit'
    }, {
        name: 'manufName',
        index: 'manufName'
    }, {
        name: 'storageType',
        index: 'storageType',
        formatter: function (e) {
            if(!re.test(e)){
                return e;
            }
            var result = "";
            $.each(jsonStore,function(idx,item){
                if(item.numCode == e){
                    result = item.name;
                    return false;
                }
            });
            return result;
        }
    },{
        name: 'batchNo',
        index: 'batchNo'

    }, {
        name: 'productDate',
        index: 'productDate'
    }, {
        name: 'validateDate',
        index: 'validateDate'

    }, {
        name: 'erpStorageAmount',
        index: 'erpStorageAmount'
    },{
        name: 'adjustBeforeChannelId',
        index: 'adjustBeforeChannelId'

    },{
        name: 'adjustAmount',
        index: 'adjustAmount'
    },{
        name: 'adjustAfterChannelId',
        index: 'adjustAfterChannelId'

    },{
        name: 'remarks',
        index: 'remarks'
    }

    ];


    $('#table_a').XGrid({
        url:"/proxy-storage/storage/StorageOwner/apply/findDetailList",
        postData:{orderCode:$("#orderCode").text()},
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'id',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
        },
        /*gridComplete: function () {
            setTimeout(function (param) {
                /!* 合计写入 *!/
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                $(sum_ele[0]).text(totalTable(data, 'text12'));
                $(sum_ele[1]).text(totalTable(data, 'text15'));
                $(sum_ele[2]).text(totalTable(data, 'text16'));
            }, 200)
        },*/
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    };

    /* 筛选列 */
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        //var tableId = $('#nav_content .active table').attr('id')
        $('#table_a').XGrid('filterTableHead');
    })



    /* 返回 */
    $('#goback').on('click', function () {
        utils.closeTab();

    });
    /* 审核通过审核 */
    $('#auditPass').on('click', function () {
        //输入内容校验
        // if(!validform('form_a').form()) return;
        var form_data = {
            orderCode:$('#orderCode').text(),
            reviewOpinion:$('#adjustReason').text(),
            // orderStatus:$('#orderStatus').text(),
            // createUser:$('#createUser').text(),
            // approvalProcessId:$('#approvalProcessId').text(),
            // organName:$('#organName').text(),
            // createTimeStr:$('#createTimeStr').text(),
        };
        $("#auditPass").attr('disabled',true);

        utils.dialog({
            title: '温馨提示：',
            content: '复核通过后该申请不可撤销，是否确认复核通过？',
            okValue: '确定',
            ok: function () {
                $.ajax({
                    url: "/proxy-storage/storage/StorageOwner/apply/auditPass",
                    type: 'post',
                    dataType: 'json',
                    data:form_data,
                    success:function(result){
                        if(result.code == 1){
                            utils.dialog({
                                title: '温馨提示：',
                                content: result.msg,
                                width: '300',
                                cancel: false,
                                okValue: '确认',
                                ok: function () {
                                    $("#auditPass").attr('disabled',false);
                                }
                            }).showModal();
                        }else if(result.code == 0){
                            utils.closeTab({reload:true});
                        }
                        $("#auditPass").attr('disabled',true);
                    },
                    error:function(result){
                        console.log("访问出现异常");
                        console.log(result);
                        //启用按钮
                        $("#auditPass").attr('disabled',false);
                    }
                });
            },
            cancelValue: '取消',
            cancel: function () {$("#auditPass").attr('disabled',false);}
        }).showModal();
        console.log(form_data);
    });

    /* 审核通过驳回 */
    $('#auditReject').on('click', function () {
        //输入内容校验
        // if(!validform('form_a').form()) return;
        var form_data = {
            orderCode:$('#orderCode').text(),
            reviewOpinion:$('#adjustReason').text(),
            // orderStatus:$('#orderStatus').text(),
            // createUser:$('#createUser').text(),
            // approvalProcessId:$('#approvalProcessId').text(),
            // organName:$('#organName').text(),
            // createTimeStr:$('#createTimeStr').text(),
        };
        $("#auditReject").attr('disabled',true);
        utils.dialog({
            title: '温馨提示：',
            content: '驳回后该申请单将被取消，是否确认驳回？',
            okValue: '确定',
            ok: function () {
                $.ajax({
                    url: "/proxy-storage/storage/StorageOwner/apply/auditReject",
                    type: 'post',
                    dataType: 'json',
                    data:form_data,
                    success:function(result){
                        if(result.code == 1){
                            utils.dialog({
                                title: '温馨提示：',
                                content: result.msg,
                                width: '300',
                                cancel: false,
                                okValue: '确认',
                                ok: function () {
                                    $("#auditReject").attr('disabled',false);
                                }
                            }).showModal();
                        }else if(result.code == 0){
                            utils.closeTab({reload:true});
                        }
                        $("#auditReject").attr('disabled',true);
                    },
                    error:function(result){
                        console.log("访问出现异常");
                        console.log(result);
                        //启用按钮
                        $("#auditReject").attr('disabled',false);
                    }
                });
            },
            cancelValue: '取消',
            cancel: function () { $("#auditReject").attr('disabled',false);}
        }).showModal();
        console.log(form_data);
    });
})
/**审批驳回 */
function auditRejectCilck(){
    console.log('审批驳回');
    $("#container label").addClass("redstar");
    utils.dialog({
        title: '审批驳回',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            //审核不通过，意见不能为空
            if ($("#auditOpinion").val()==""){
                utils.dialog({content:'审批意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }else{
                $.ajax({
                    method: "POST",
                    url: "/proxy-storage/storage/StorageOwner/apply/auditRejectNew",
                    data: {
                        orderCode:$("#orderCode").text(),
                        reviewOpinion:$("#auditOpinion").val(),
                        taskId:taskId,
                    },
                    dataType: 'json',
                    cache: false,
                    error: function () {
                        parent.hideLoading();
                        utils.dialog({content:'请求审批处理失败！', quickClose: true, timeout: 2000}).showModal();
                    },
                    success: function (data) {
                        parent.hideLoading();
                        if (data.code==0){
                            utils.dialog({content: data.msg, quickClose: true, timeout: 6000}).showModal();
                            setTimeout(function(){  utils.closeTab(); }, 2000);
                        }else {
                            utils.dialog({content:data.msg, quickClose: true, timeout: 6000}).showModal();
                        }
                        $("#auditOpinion").val('');
                    }
                });
            }

        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}
/**审批通过 */
function auditPassCilck(){
    console.log('审批通过');
    $("#container label").addClass("redstar");
    utils.dialog({
        title: '审批通过',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            //审核不通过，意见不能为空
            if ($("#auditOpinion").val()==""){
                utils.dialog({content:'审批意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }else{
                $.ajax({
                    method: "POST",
                    url: "/proxy-storage/storage/StorageOwner/apply/auditPassNew",
                    data: {
                        orderCode:$("#orderCode").text(),
                        reviewOpinion:$("#auditOpinion").val(),
                        taskId:taskId,
                    },
                    dataType: 'json',
                    cache: false,
                    error: function () {
                        parent.hideLoading();
                        utils.dialog({content:'请求审批处理失败！', quickClose: true, timeout: 2000}).showModal();
                    },
                    success: function (data) {
                        parent.hideLoading();
                        if (data.code==0){
                            utils.dialog({content: data.msg, quickClose: true, timeout: 6000}).showModal();
                            setTimeout(function(){  utils.closeTab(); }, 2000);
                        }else {
                            utils.dialog({content:data.msg, quickClose: true, timeout: 6000}).showModal();
                        }
                        $("#auditOpinion").val('');
                    }
                });
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}

