$(function () {

    $('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/ajaxFirstSupplierOrgBaseList",
        colNames: ['', '申请日期', '机构','申请人ID','申请人','单据编号','供应商编码', '供应商名称', '供应商类型', '业务对接人', '业务联系方式一', '业务联系方式二', '营业执照号','审核状态','审核状态'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 50,
                hidden:true
            },  {
                name: 'applicationTime',
                index: 'applicationTime',
                width: 100,
                formatter:function(value){
                    var date=value;
                    if(!value)return false;
                    date=format(value);
                    return date.split(' ')[0];
                }
            },  {
                name: 'orgName',
                index: 'orgName',
                width: 350
            },{
                name: 'applicantId',
                index: 'applicantId',
                hidden:true
            },  {
                name: 'applicantName',
                index: 'applicantName',
                width: 100
            },  {
                name: 'applicationCode',
                index: 'applicationCode',
                width: 200
            }, {
                name: 'supplierCode',
                index: 'supplierCode',
                width: 160

            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 250
            }, {
                name: 'supplierTypeName',
                index: 'supplierTypeName',
                width: 100
            }, {
                name: 'contactPerson',
                index: 'contactPerson',
                width: 150
            }, {
                name: 'contactMessage1',
                index: 'contactMessage1',
                width: 150
            }, {
                name: 'contactMessage2',
                index: 'contactMessage2',
                width: 150
            }, {
                name: 'supplierBusinessNum',
                index: 'supplierBusinessNum',
                width: 250
            }, {
                name: 'auditStatusName',
                index: 'auditStatusName',
                width: 120
            },{
            	 name: 'auditStatus',
                 index: 'auditStatus',
                 width: 250,
                 hidden:true
            },{
                name: 'applicantId',
                index: 'applicantId',
                width: 250,
                hidden:true
            }

        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            //录入中的进入可编辑页面
            if("0"==obj.auditStatus){
                var loginUserId = $("#loginUserId").val();
                if(obj.applicantId==loginUserId){
                    var url = "/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/detail/first?businessId="+id+"&selfPageEdit=Y";
                    utils.openTabs("firstSupplierOrganBaseFirstEdit", "供应商首营详情 ", url)
                }else{
                    utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                }
//        		window.location="/supplierOrganBase/firstSupplierOrganBase/detail/first?businessId="+id+"&selfPageEdit=Y";
            }else{
                var url = "/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/detail/first?businessId="+id;
                utils.openTabs("firstSupplierOrganBaseFirstDetail", "供应商首营详情", url);
//        		window.location="/supplierOrganBase/firstSupplierOrganBase/detail/first?businessId="+id;

            }
        	
        },


    });

    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "queryFields": $("#queryFields").val(),
                "auditStatus":$("#auditStatus").val()
            },page:1
        }).trigger('reloadGrid');
    });

    // 删除草稿
    $("#deleteDraftBtn").on("click", function () {
        let selRow = $('#X_Tableb').XGrid('getSeleRow');
        // 表格重新渲染 的参数
        let postData = {
            "queryFields": $("#queryFields").val(),
            "auditStatus":$("#auditStatus").val()
        }
        // supplierOrganBaseId：  删除草稿需要传递的参数
        let data = {
            supplierOrganBaseId: selRow.length != 0 ? selRow[0].id : '',
        }
        let params = {
            statusName: selRow.length != 0 ? selRow[0].auditStatus: '', //  表格中审核状态的字段
            statusVal: '0', // 对应的录入中的状态值
            url: '/proxy-supplier/supplier/supplierOrganBase/deleteDraft', // 删除草稿的url
            applicantId: selRow.length != 0 ? selRow[0].applicantId : '', //选中行申请人的ID
            loginUserId: $('#loginUserId').val() // 当前登录人 ID
        }
                            // 表格id
        utils.deleteDraft('X_Tableb', params, data, postData)
    });



    
    //批量申请
    $('#supplierBatchApply').on('click', function () {
        utils.dialog({
            align: 'top',
            width: 130,
            height: 50,
            padding: 2,
            content:'<div class="changeApplyItem formApprovalBlock"><div class="import" onclick="utils.openTabs(\'firstSupplierOrganBaseToFileupload\',\'供应商首营批量申请导入数据\',\'/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/toFileupload\',\{reload: false\})">导入数据</div><div class="downTemplate"><a href="/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/downTemplate"  class="download">下载模板</a></div></div>',
            quickClose: true
        }).show(this);
    });
    
});
function format(shijianchuo) {
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }