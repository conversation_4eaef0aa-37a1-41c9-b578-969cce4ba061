.col-mdp-3{
     width: 100% !important;
     display: block;
     clear: both;
     height: 35px;
 }
 .halfs{
     width: 50%;
      float: left;
     display: inline-block;
     vertical-align: top;
 }
 .content .panel {
     margin-bottom: 0px !important;
 }
 .process-container {
     list-style: none;
     width: 800px !important;
     display: block;
     overflow: auto;
     padding: 0px;
     margin: 0px;
 }
 .process-step{
     display: block;
 }
 .body_div, body{
     width: 800px !important;
 }
 .XGridUI{
     width: 780px !important;
 }
 td,th {
      font-size: 12px;
      white-space: normal !important;
      height: auto !important;

      word-wrap:break-word !important;
      word-break:break-all !important;
      line-height: 23px !important;

 }
 .halfs div, .halfs input{
     display: block;
     float: left;

 }
 .panel-title{
    margin: 0px;
 }
 .bodywraps, headwraps{
     display: block;
     clear: both;
 }

 .halfs-addon{
    width: 120px;
    float: left;
 }
 input, select, textarea{
    display: block;
    float: right;
    border: transparent;
    width: 267px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
 }

 .process-step {
     float: left;
     width: 150px;
     height: 125px;
     display: block;
     font-size: 12px;
     margin-bottom: 15px;
 }
.process-step p{
    word-break: break-word;
}
 .tips-more{
    display: none;
 }

 .status-success>span {
     display: none;
 }

 .XGridUI{
    border: 1px solid #000;
     border-color: #000 !important;
     border-collapse: collapse;
 }

 .XGridUI td, .XGridUI th{
    border: 1px solid black;
 }
select::-ms-expand {
    display: none;
}

select {
    -webkit-appearance: none;
    -moz-appearance: none;
    text-indent: 1px;
    text-overflow: '';
}

body, #print_divsa{
    margin: 0px auto;
}
.halfs .halfs-addon, .halfs input, .halfs select, .halfs  textarea{
    line-height: 35px;
}


th[row-describedby="taxAmount"],td[row-describedby="taxAmount"],
th[row-describedby="taxLimitAmount"],td[row-describedby="taxLimitAmount"],
th[row-describedby="amount"],td[row-describedby="amount"]{
    width: 90px !important;
    text-align: center;
}
.halfs span{
    display: block;
    float: left;
    width: 267px;
    text-align: left;
    line-height: 35px;
    font-size: 12px;
}