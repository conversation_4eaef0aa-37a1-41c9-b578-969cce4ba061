var applicationOperation = "0,1";
var largeCategoryArray = new Array();
largeCategoryArray[0] = "中药饮片";
largeCategoryArray[1] = "食品";
largeCategoryArray[2] = "保健食品";
largeCategoryArray[3] = "中药材";
largeCategoryArray[4] = "药食同源";

//选中的批准文件行
var selectApprovalRow = null;
//记录批准文件url，用于重新刷新表格
var pizhunwenjianUrl = ''
// 预览
$("#preview_btn").on('click', function () {
    var fileArrPreview = [];
    let allFiles = $('.uploadFiles_div>div');
    if (allFiles.length > 0) {
        $(allFiles).each(function (index, item) {
            let obj = {};
            obj.url = $(item).attr('data-url');
            obj.name = $(item).find('a').text();
            fileArrPreview.push(obj)
        });
        $.viewImg({
            fileParam: {
                name: 'name',
                url: 'url'
            },
            list: fileArrPreview
        })
    }
})

//清空相关附件
function resetUploadFilesDiv() {
    $('.uploadFiles_div').find('div').remove()
    $('.uploadFiles_div').css('display', 'none')
}

//删除图片
function btn_delFile(el) {
    utils.dialog({
        title: "提示",
        width: 200,
        content: "确定要删除此文件?",
        okValue: "确定",
        ok: function () {
            let len = $(el).parents('.uploadFiles_div').find('div').length;
            $(el).parent().remove();
            if ((len - 1) == 0) {
                $('.uploadFiles_div').css('display', 'none')
            }
        },
        cancelValue: '取消',
        cancel: function () {
        }
    }).showModal();

}

//上传按钮点击事件
function uploadFile() {
    let len = $('.uploadFiles_div>div').length;
    if (len < 5) {
        utils.dialog({
            title: '上传附件',
            width: 500,
            height: 100,
            content: $('#upload_dialog'),
            okValue: '确定',
            cancelValue: '取消',
            ok: function () {
                console.log('ok');
                UploadFileFn(function (list, filename) {
                    //previewUpload(list, filename);
                });
            },
            cancel: function () {
                console.log('cancel');
            },
        }).showModal()
    } else {
        utils.dialog({
            title: '提示',
            content: '最多支持传5个附件。',
            okValue: '确定',
            ok: function () {
            }
        }).showModal();
    }

}

function packingAndDownloading() {
    var urlArr = [];
    $("td[row-describedby=enclosureList]").each((index, item) => {
        var json = JSON.parse($(item).html());
        if (json != null && json.length > 0) {
            json.forEach(function (element) {
                urlArr.push(element.enclosureUrl);
            })
        }
    })
    if (urlArr && urlArr.length > 0) {
        var url = urlArr.join(',');
        downLoad(url);
    } else {
        utils.dialog({ content: '当前没有可以下载附件', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
}
function downLoad(urlObjectList) {
    var a = [];
    a.push('<form method="post" style="display: none;">');
    a.push('<input name="urls" value = ' + urlObjectList + '>');
    a.push('</form>');
    var $eleForm = $(a.join(''));
    $eleForm.attr("action", "/proxy-product/product/productOrga/downLoadImage");
    //$eleForm.attr("action", "http://192.168.101.45:8080/upload/download?filepath=G2/M00/00/01/Cgo001tPH3OAXX8_AABEWCsL8kc354.png&name=''");
    $(document.body).append($eleForm);
    //提交表单，实现下载
    $eleForm.submit();
}

function packingAndDownloadingStamp() {
    var urlArr = [];
    $("td[row-describedby=enclosureList]").each((index, item) => {
        var json = JSON.parse($(item).html());
        if (json != null && json.length > 0) {
            json.forEach(function (element) {
                urlArr.push(element.signatureImageUrl)
            })
        }
    })
    if (urlArr && urlArr.length > 0) {
        var url = urlArr.join(',');
        downLoad(url);
    } else {
        utils.dialog({ content: '当前没有可以下载附件', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
}



//编辑延期
function editDelayClick() {
    console.log('编辑延期');
    if (selectApprovalRow == null || (selectApprovalRow.batchName != 3)) {
        utils.dialog({
            title: '提示',
            content: '未选中、或选中非《药品再注册批件》的其他文件',
            timeout: 2000
        }).show();
        return;
    }
    //给延期弹框上的元素赋值
    //批件截止日期，设置值没有问题，取值有问题，取值可能有多个相同id的
    //获取选中条的数据,设置
    $('#pijianvalidityDate').val(selectApprovalRow.validityDateStr2)
    //业务延期日期
    $('#lastRenewValidityDate').val(selectApprovalRow.renewValidityDate)
    //清空修改至
    $('#renewValidityDate').val('')
    utils.dialog({
        title: '延期日期修改',
        width: 500,
        height: 400,
        content: $('.editDelayBox'),
        okValue: '保存并变更',
        cancelValue: '取消',
        button: [
            {
                value: '上传附件',
                callback: function () {
                    uploadFile();
                    return false;
                },
                autofocus: true
            },
        ],
        onshow: function () {
            $("").appendTo($("#previewBox").show().find("ul"));
            $("#previewBox").find("li").remove();
            $("#btnUpload").removeAttr("disabled");
        },
        ok: function () {
            console.log('取消');
            let res = saveDelayDateChange()
            resetUploadFilesDiv();
            return res;
        },
        cancel: function () {
            resetUploadFilesDiv()
        },
        onclose: function () {
            resetUploadFilesDiv()
        },

    }).showModal();
}

// 保存变更
function saveDelayDateChange() {
    // 获取form表单
    console.log($("#editDelayForm").serializeToJSON())
    const editDelayForm = $("#editDelayForm").serializeToJSON();
    let fileNames = [];
    let fileUrls = [];
    let allFiles = $('.uploadFiles_div>div');
    if (allFiles.length > 0) {
        $(allFiles).each(function (index, item) {
            let obj = {};
            const url = $(item).attr('data-url');
            const name = $(item).find('a').text();
            fileNames.push(name)
            fileUrls.push(url)
        });
    }
    let fileName = fileNames.join(',')
    let fileUrl = fileUrls.join(',')
    if (editDelayForm.renewValidityDate == null || editDelayForm.renewValidityDate.length == 0) {
        utils.dialog({
            content: '延期至为必填项',
            timeout: 1000
        }).show();
        return false
    }
    let param = {
        validityDate: editDelayForm.pijianvalidityDate,
        lastRenewValidityDate: editDelayForm.lastRenewValidityDate,
        renewValidityDate: editDelayForm.renewValidityDate,
        fileName: fileName,
        fileUrl: fileUrl,
        correlationId: selectApprovalRow.correlationId,
        batchCode: selectApprovalRow.batchCode,
    }
    $.ajax({
        type: 'post',
        data: param,
        url: '/proxy-product/product/renewDateRecord/saveRenewDateRecord',
        success: (res) => {
            // 更新批准文件接口
            if (res.code === 0) {
                utils.dialog({
                    content: '保存变更成功',
                    timeout: 1000
                }).show();
                $('#X_Table').XGrid('setGridParam', {
                    url: pizhunwenjianUrl,
                }).trigger('reloadGrid');
                //重置选中
                selectApprovalRow = null;
            }
        },
        error: function (err) {
            utils.dialog({
                content: '保存变更失败',
                timeout: 1000
            }).show();
        }
    }
    )
    return true;
}

var prevUploadFileName = "";//上一次上传的文件名
//选中文件回调
$("#btnUpload").on("change", function () {
    const selectLength = $("#btnUpload")[0].files.length
    const len = $('.uploadFiles_div>div').length;
    const total = selectLength + len;
    if (total > 5) {
        utils.dialog({
            title: '提示',
            content: "最多支持传5个附件，请重新选择",
            okValue: '确定',
            ok: function () {
            },
            onclose: function () {
                $("#btnUpload").val("");
                $('#filePath').val("");
            }
        }).showModal();
        return
    }
    $('#filePath').val($(this).val())
    var fileName = this.files[0].name;
    //是否选择了文件
    if (!fileName) {
        utils.dialog({
            title: "提示",
            width: 200,
            content: "请选择文件",
            okValue: "确定",
            ok: function () {
            }
        }).showModal();
        return false;
    }
});

//上传附件
function UploadFileFn(cb) {
    var files = $("#btnUpload")[0].files
    var formData = new FormData();
    for (var i = 0; i < files.length; i++) {
        var file = files[i];
        var fileName = file.name;
        var fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLocaleLowerCase();
        var imageType = ['png', 'jpg', 'jpeg', 'webp'];
        if (imageType.indexOf(fileType) == -1) {
            utils.dialog({ content: "只允许上传图片格式", quickClose: false, timeout: 2000 }).showModal();
            return false;
        }
        var maxsize = 5 * 1024 * 1024; //5M
        var size = file.size; //图片大小
        if (size > maxsize) {
            //                alert('图片过大，单张图片大小不超过2M');
            utils.dialog({ content: "文件过大，文件大小不能超过5M", quickClose: false, timeout: 2000 }).showModal();
            return false;
        }
        if (!file) {
            utils.dialog({
                title: "提示",
                width: 200,
                content: "请选择文件",
                okValue: "确定",
                ok: function () {
                }
            }).showModal();
            return false;
        }
        formData.append("files", file);
        formData.append("name", file.name);
    }


    // formData.files = file
    // formData.name = file.name
    var loading = dialog({
        title: '上传中',
        fixed: true,
        width: 200,
        quickClose: false,
        cancel: false
    }).showModal();

    $.ajax({
        url: '/proxy-finance/finance/purchase/payrequestinfo/upload',
        type: 'POST',
        async: false,
        data: formData,
        processData: false,
        contentType: false,
        beforeSend: function () {
            console.log("正在上传");
        },
        success: function (data) {
            loading.close();
            if (data.code == 0) {
                prevUploadFileName = file.name;
                cb(data.result[0], prevUploadFileName);
                utils.dialog({ content: "上传成功", quickClose: false, timeout: 2000 }).showModal();
            } else if (data.code == 1) {
                utils.dialog({ content: "上传成功", quickClose: false, timeout: 2000 }).showModal();
            } else {
                utils.dialog({ content: "上传失败", quickClose: true, timeout: 2000 }).showModal();
            }
            // 展示附件
            let fileList = data.result;
            if (fileList && fileList.length != 0) {
                let str = '';
                $(fileList).each((index, item) => {
                    str += `<div data-url="` + item.enclosureUrl + `" style="display: inline-block; padding: 10px 20px 10px 10px; position: relative;">
                                        <span style=" position: absolute;width: 15px;height: 15px;top: 0;right: 0;z-index: 1;cursor: pointer;" onclick="btn_delFile(this)"><img src="/proxy-sysmanage/static/images/close.png"></span>
                                        <a href='javascript:;' onclick='imgPreview("` + item.enclosureUrl + `")' data-url="` + item.enclosureUrl + `" data-name="` + item.enclosureName + `">` + item.enclosureName + `</a></div>`
                    // <a href='`+item.enclosureUrl+`' target="_blank" download="`+item.enclosureName+`">`+item.enclosureName+`</a></div>`
                });
                $('.uploadFiles_div').append(str);
                $('.uploadFiles_div').css('display', 'block')
            }
        }, error: function () {
            loading.close();
            utils.dialog({ content: "上传失败", quickClose: true, timeout: 2000 }).showModal();
        }, complete: function () {
            $("#btnUpload").val("");
            $('#filePath').val("");
        }
    });
}

//延期日期修改详情表格字段
const delayModifyDetailColMaps = [
    { colName: '', name: 'id', hidden: true },
    { colName: '变更时间', name: 'updateTime', index: 'updateTime', width: 250 },
    { colName: '修改人', name: 'updateUserVal', index: 'updateUserVal', width: 180 },
    { colName: '批件截止日期', name: 'validityDate', index: 'validityDate' },
    { colName: '原延期日期', name: 'lastRenewValidityDate', index: 'lastRenewValidityDate', width: 250 },
    { colName: '修改至', name: 'renewValidityDate', index: 'renewValidityDate', width: 180 },
    { colName: '相关附件', name: 'fileName', index: 'fileName', formatter: fileFormatter },
    { colName: '相关附件url', name: 'fileUrl', index: 'fileUrl', hidden: true },
]

function imgPreview(imgUrl) {
    $.viewImg({
        fileParam: {
            name: 'name',
            url: 'url'
        },
        list: [imgUrl]
    })
}

//延期修改详情中的附件列格式化
function fileFormatter(value, a, obj, c) {
    let imgUrls = obj.fileUrl.split(',');
    let imgNames = obj.fileName.split(',');
    let str = '';
    for (let i = 0; i < imgNames.length; i++) {
        let name = imgNames[i];
        let url = imgUrls[i];
        str += '<button class="btn btn-link" onclick="imgPreview(\'' + url + '\')">' + name + '</button>'
    }
    return str
}

// 创建表格
$('#delayModifyTable').XGrid({
    colNames: delayModifyDetailColMaps.map(item => {
        return item.colName
    }),
    colModel: delayModifyDetailColMaps.map(item => {
        delete item.colName
        return item
    }),
    // 设置为交替行表格,默认为false
    altRows: true,
    // 设置每页展示行数，-1 表示全部展示
    rowNum: -1,
    height: 200,
    // 是否展示序号
    rownumbers: true
});

//延期日期详情列表数据获取
function fetchData(correlationId, batchCode) {
    const param = {
        correlationId: correlationId,
        batchCode: batchCode
    }
    $.ajax({
        type: 'post',
        data: param,
        url: '/proxy-product/product/renewDateRecord/getRenewDateRecord',
        success: (res) => {
            $('#delayModifyTable').XGrid('clearGridData');
            $('#delayModifyTable').XGrid('setGridParam', {
                data: res.result
            }).trigger('reloadGrid')
        },
        error: function (err) {
            utils.dialog({
                content: '延期日期详情列表查询失败',
                timeout: 1000
            }).show();
        }
    }
    )
}

//打开延期日期详情弹框
function showDelayModifyDetail(correlationId, batchCode) {
    fetchData(correlationId, batchCode);
    utils.dialog({
        title: '延期日期修改详情',
        width: $(window).width() * 0.7,
        height: $(window).height() * 0.5,
        content: $('.delayModifyDetail'),
        okValue: '关闭',
        ok: function () {
        },
        onshow: function () {
            $('.ui-dialog-content').css('overflow', 'auto')
        }
    }).showModal()
}

$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //判断是否机构详情页
    var pageType = $("#pageType").val();
    if (pageType != null && pageType == "orga") {
        $(".orgahistorys").show();
        //$("#packingList").show();
        applicationOperation = "0,1,2,3,4,5,6,7,8,9,10,11";
        $("#div_firstShelfTime").show();
        $("#div_recentShelfTime").show();
    }
    var baseProductId = $("#baseProductId").val();
    var approvalFileType = $("#approvalFileType").val();
    var orgaProductId = $("#orgaProductId").val();//机构供应商id
    var orgCode = $("#orgCode").val();//机构供应商机构号


    //批准文件加载
    var scopeOfOperation = $("#scopeOfOperation").val();
    localBatchName(1);
    //业务类型属性
    if (pageType != null && pageType == "orga") {//机构详情页展示EC上下架状态
        $('#X_Table_Channel').XGrid({
            url: "/proxy-product/product/productChannel/toList?type=" + approvalFileType + "&correlationId=" + orgaProductId,
            colNames: ['id', '生效状态', '类别', '<i class="i-red">*</i>类别', '<i class="i-red">*</i>业务类型', '<i class="i-red">*</i>采购员', '<i class="i-red">*</i>供货价', '<i class="i-red">*</i>APP售价', '区域类型', '智鹿总部采购价', '连锁APP售价','荷叶大药房采购价',
                '<i class="i-red">*</i>票面毛利率', '<i class="i-red">*</i>APP销售价是否维价', '出库价维价关联客户类型', '出库价维价关联客户类型ARR', '建议终端售价', '<i class="i-red">*</i>终端零售价是否维价',
                '<i class="i-red">*</i>电商商品状态', '实时综合毛利率区间', '毛利率等级', '<i class="i-red">*</i>生效状态', '是否集采'],
            colModel: [
                {
                    name: 'id',
                    index: 'id',//索引。其和后台交互的参数为sidx
                    key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                    hidden: true
                }, {
                    name: 'effectiveState',
                    index: 'effectiveState',
                    hidden: true
                }, {
                    name: 'channelType',
                    index: 'channelType',
                    hidden: true
                }, {
                    name: 'channelTypeVal',
                    index: 'channelTypeVal'
                }, {
                    name: 'channelId',
                    index: 'channelId',
                }, {
                    name: 'buyerVal',
                    index: 'buyerVal',
                    rowtype: '#buyerVal',
                }, {
                    name: 'supplyPrice',
                    index: 'supplyPrice',
                    rowtype: '#supplyPrice',
                }, {
                    name: 'newAppPrice',
                    index: 'newAppPrice',
                    formatter: function (val, rowType, rowData) {
                        if (!rowData.areaChannelList) {
                            return rowData.appPrice || ''
                        } else {
                            let areaList = getProductChannelAreaList(rowData.areaChannelList);
                            let appPriceHtml = ''
                            if (areaList && areaList.length > 0) {
                                for (let i = 0; i < areaList.length; i++) {
                                    appPriceHtml += `<div class="tdHtml">${areaList[i].appPrice || ''}</div>`
                                }
                            }
                            return appPriceHtml
                        }
                    }
                },
                {
                    name: 'areaName',
                    index: 'areaName',
                    formatter: function (val, rowType, rowData) {
                        if (!rowData.areaChannelList) {
                            return '默认'
                        } else {
                            let areaList = getProductChannelAreaList(rowData.areaChannelList);
                            let areaHtml = `<div id="Area">`
                            if (areaList && areaList.length > 0) {
                                for (let i = 0; i < areaList.length; i++) {
                                    if (i == 0) {
                                        areaHtml += `<div class="tdHtml"><span class="AreaInfo">默认</span></div>`
                                    } else {
                                        areaHtml += `<div class="tdHtml"><span class="AreaInfo">${areaList[i].areaName || ''}</span></div>`
                                    }
                                }
                            }
                            areaHtml += `</div>`
                            return areaHtml
                        }
                    },
                    unformat: function (e) {
                        return ''
                    }
                }, {
                    name: 'zhiluPrice',
                    index: 'zhiluPrice',
                    rowtype: '#zhiluPrice',
                }, {
                    name: 'chainGuidePrice',
                    index: 'chainGuidePrice',
                    rowtype: '#chainGuidePrice',
                }, {
                    name: 'heyePrice',
                    index: 'heyePrice',
                    rowtype: '#heyePrice',
                }, {
                    name: 'grossMarginStrHtml',
                    index: 'grossMarginStrHtml',
                    formatter: function (val, rowType, rowData) {
                        if (!rowData.areaChannelList) {
                            return rowData.parGrossMargin || ''
                        } else {
                            let areaList = getProductChannelAreaList(rowData.areaChannelList);
                            let grossMarginStrHtml = ''
                            if (areaList && areaList.length > 0) {
                                for (let i = 0; i < areaList.length; i++) {
                                    grossMarginStrHtml += `<div class="tdHtml">${areaList[i].parGrossMargin || ''}</div>`
                                }
                            }
                            return grossMarginStrHtml
                        }
                    }
                }, {
                    name: 'dimensionSalesPriceYn',
                    index: 'dimensionSalesPriceYn',
                    rowtype: '#dimensionSalesPriceYn',
                }, {
                    name: 'pharmacyType',
                    index: 'pharmacyType',
                    rowtype: '#pharmacyType',
                }, {
                    name: 'pharmacyTypeArr',
                    index: 'pharmacyTypeArr',
                    hidden: true,
                }, {
                    name: 'terminalPrice',
                    index: 'terminalPrice',
                    rowtype: '#terminalPrice',
                }, {
                    name: 'dimensionTerminalPriceYn',
                    index: 'dimensionTerminalPriceYn',
                    rowtype: '#dimensionTerminalPriceYn',
                }, {
                    name: 'ecStatusVal',
                    index: 'ecStatusVal',
                }, {
                    name: 'grossMarginRange',
                    index: 'grossMarginRange',
                    width: 130,
                    rowtype: '#grossMarginRange',
                }, {
                    name: 'grossMarginGrade',
                    index: 'grossMarginGrade',
                    rowtype: '#grossMarginGrade',
                }, {
                    name: 'effectiveStateVal',
                    index: 'effectiveStateVal'
                }, {
                    name: 'centralizedPurchaseType',
                    index: 'centralizedPurchaseType',
                    formatter: function (value) {
                        if (value == 0) {
                            return "否"
                        } else {
                            return "是"
                        }
                    }
                }, {
                    name: 'appPrice',
                    index: 'appPrice',
                    hidegrid: true,
                    hidden: true
                },
                {
                    name: 'areaChannelList',
                    index: 'areaChannelList',
                    hidegrid: true,
                    hidden: true,
                    formatter: function (val, rowType, rowData) {
                        if (typeof val == 'string' || !val) {
                            return val || ''
                        } else {
                            return JSON.stringify(val)
                        }
                    }
                }, {
                    name: 'parGrossMargin',
                    index: 'parGrossMargin',
                    hidegrid: true,
                    hidden: true,
                },],
            rowNum: 10,
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true,
            gridComplete: function (res) {
                var rowData = $("#X_Table_Channel").XGrid('getRowData');
                if (rowData && rowData.length) {
                    rowData.forEach(function (item) {
                        let arr = item['pharmacyTypeArr']
                        let _node = $('#X_Table_Channel #' + item.id + ' [name=pharmacyType]')
                        for (let i = 0; i < _node.length; i++) {
                            if (arr.indexOf($(_node[i]).val()) > -1) {
                                $(_node[i]).prop('checked', true)
                            }
                        }
                    })
                }
            }
        });
    } else {
        $('#X_Table_Channel').XGrid({
            url: "/proxy-product/product/productChannel/toList?type=" + approvalFileType + "&correlationId=" + orgaProductId,
            colNames: ['id', '生效状态', '类别', '<i class="i-red">*</i>类别', '<i class="i-red">*</i>业务类型', '<i class="i-red">*</i>采购员', '<i class="i-red">*</i>供货价', '<i class="i-red">*</i>APP售价', '区域类型', '智鹿总部采购价', '连锁APP售价','荷叶大药房采购价',
                '<i class="i-red">*</i>票面毛利率', '<i class="i-red">*</i>APP销售价是否维价', '出库价维价关联客户类型', '出库价维价关联客户类型ARR', '建议终端售价', '<i class="i-red">*</i>终端零售价是否维价', '实时综合毛利率区间', '毛利率等级', '<i class="i-red">*</i>生效状态'],
            colModel: [
                {
                    name: 'id',
                    index: 'id',//索引。其和后台交互的参数为sidx
                    key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                    hidden: true
                }, {
                    name: 'effectiveState',
                    index: 'effectiveState',
                    hidden: true
                }, {
                    name: 'channelType',
                    index: 'channelType',
                    hidden: true
                }, {
                    name: 'channelTypeVal',
                    index: 'channelTypeVal'
                }, {
                    name: 'channelId',
                    index: 'channelId',
                }, {
                    name: 'buyerVal',
                    index: 'buyerVal',
                    rowtype: '#buyerVal',
                }, {
                    name: 'supplyPrice',
                    index: 'supplyPrice',
                    rowtype: '#supplyPrice',
                }, {
                    name: 'newAppPrice',
                    index: 'newAppPrice',
                    formatter: function (val, rowType, rowData) {
                        if (!rowData.areaChannelList) {
                            return rowData.appPrice
                        } else {
                            let areaList = getProductChannelAreaList(rowData.areaChannelList);
                            let appPriceHtml = ''
                            if (areaList && areaList.length > 0) {
                                for (let i = 0; i < areaList.length; i++) {
                                    appPriceHtml += `<div class="tdHtml">${areaList[i].appPrice || ''}</div>`
                                }
                            }
                            return appPriceHtml
                        }
                    }
                }, {
                    name: 'areaName',
                    index: 'areaName',
                    formatter: function (val, rowType, rowData) {
                        if (!rowData.areaChannelList) {
                            return '默认'
                        } else {
                            let areaList = getProductChannelAreaList(rowData.areaChannelList);
                            let areaHtml = `<div id="Area">`
                            if (areaList && areaList.length > 0) {
                                for (let i = 0; i < areaList.length; i++) {
                                    if (i == 0) {
                                        areaHtml += `<div class="tdHtml"><span class="AreaInfo">默认</span></div>`
                                    } else {
                                        areaHtml += `<div class="tdHtml"><span class="AreaInfo">${areaList[i].areaName || ''}</span></div>`
                                    }
                                }
                            }
                            areaHtml += `</div>`
                            return areaHtml
                        }
                    },
                    unformat: function (e) {
                        return ''
                    }
                }, {
                    name: 'zhiluPrice',
                    index: 'zhiluPrice',
                    rowtype: '#zhiluPrice',
                }, {
                    name: 'chainGuidePrice',
                    index: 'chainGuidePrice',
                    rowtype: '#chainGuidePrice',
                }, {
                    name: 'heyePrice',
                    index: 'heyePrice',
                    rowtype: '#heyePrice',
                }, {
                    name: 'grossMarginStrHtml',
                    index: 'grossMarginStrHtml',
                    formatter: function (val, rowType, rowData) {
                        if (!rowData.areaChannelList) {
                            return rowData.parGrossMargin || ''
                        } else {
                            let areaList = getProductChannelAreaList(rowData.areaChannelList);
                            let grossMarginStrHtml = ''
                            if (areaList && areaList.length > 0) {
                                for (let i = 0; i < areaList.length; i++) {
                                    grossMarginStrHtml += `<div class="tdHtml">${areaList[i].parGrossMargin || ''}</div>`
                                }
                            }
                            return grossMarginStrHtml
                        }
                    }
                }, {
                    name: 'dimensionSalesPriceYn',
                    index: 'dimensionSalesPriceYn',
                    rowtype: '#dimensionSalesPriceYn',
                }, {
                    name: 'pharmacyType',
                    index: 'pharmacyType',
                    rowtype: '#pharmacyType',
                }, {
                    name: 'pharmacyTypeArr',
                    index: 'pharmacyTypeArr',
                    hidden: true,
                }, {
                    name: 'terminalPrice',
                    index: 'terminalPrice',
                    rowtype: '#terminalPrice',
                }, {
                    name: 'dimensionTerminalPriceYn',
                    index: 'dimensionTerminalPriceYn',
                    rowtype: '#dimensionTerminalPriceYn',
                }, {
                    name: 'grossMarginRange',
                    index: 'grossMarginRange',
                    rowtype: '#grossMarginRange',
                }, {
                    name: 'grossMarginGrade',
                    index: 'grossMarginGrade',
                    rowtype: '#grossMarginGrade',
                }, {
                    name: 'effectiveStateVal',
                    index: 'effectiveStateVal'
                }, {
                    name: 'appPrice',
                    index: 'appPrice',
                    hidegrid: true,
                    hidden: true,
                }, {
                    name: 'areaChannelList',
                    index: 'areaChannelList',
                    hidegrid: true,
                    hidden: true,
                    formatter: function (val, rowType, rowData) {
                        if (typeof val == 'string' || !val) {
                            return val || ''
                        } else {
                            return JSON.stringify(val)
                        }
                    }
                }, {
                    name: 'parGrossMargin',
                    index: 'parGrossMargin',
                    hidegrid: true,
                    hidden: true,
                }],
            rowNum: 10,
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true,
            gridComplete: function (res) {
                var rowData = $("#X_Table_Channel").XGrid('getRowData');
                if (rowData && rowData.length) {
                    rowData.forEach(function (item) {
                        let arr = item['pharmacyTypeArr']
                        let _node = $('#X_Table_Channel #' + item.id + ' [name=pharmacyType]')
                        for (let i = 0; i < _node.length; i++) {
                            if (arr.indexOf($(_node[i]).val()) > -1) {
                                $(_node[i]).prop('checked', true)
                            }
                        }
                    })
                }
            }
        });
    }
    /**
     * 获取JSON
     * @param areaChannelList
     * @returns {*[]|any}
     */
    function getProductChannelAreaList(areaChannelList) {
        if (!areaChannelList) {
            return []
        }
        if (typeof areaChannelList == 'string') {
            return $.parseJSON(areaChannelList)
        }
        return areaChannelList
    }
    //商品说明书
    loadProductInstruction(baseProductId, approvalFileType);
    var pageFlag = $("#pageFlag").val();
    if ("basePase" == pageFlag) {
        var url = "/proxy-product/product/productApprovalFile/toPageList?type=" + approvalFileType + "&correlationId=" + baseProductId + "&baseDataFlag=true";
        $('#X_Table').XGrid({
            url: url,
            colNames: ['', '机构', '<i class="i-red">*</i>批件名称', '<i class="i-red">*</i>批件编号', '<i class="i-red">*</i>签发日期', '<i class="i-red">*</i>有效期至', '附件', '附件数据'],
            colModel: [
                {
                    name: 'id',
                    index: 'id',//索引。其和后台交互的参数为sidx
                    key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                    hidden: true
                },
                {
                    name: 'orgName',
                    index: 'orgName',

                },
                {
                    name: 'batchName',
                    index: 'batchName',
                    rowtype: '#batchName',
                }, {
                    name: 'batchCode',
                    index: 'batchCode'
                }, {
                    name: 'issueDateStr',
                    index: 'issueDateStr'

                }, {
                    name: 'validityDateStr',
                    index: 'validityDateStr'
                }, {
                    name: 'enclosure',
                    index: 'enclosure',
                    formatter: function (value) {
                        var str = '无';
                        if (value) {
                            str = '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
                        }
                        return str;
                    },
                    unformat: function (e) {
                        e = e.replace(/<[^>]+>/g, '');
                        if (e == "无") {
                            e = 0;
                        }
                        return e;
                    }
                }, {
                    name: 'enclosureList',
                    index: 'enclosureList',
                    hidden: true,
                    formatter: function (value) {
                        if (value) {
                            return JSON.stringify(value);
                        }
                        return JSON.stringify([]);
                    }
                }],
            rowNum: 10,
            altRows: true,//设置为交替行表格,默认为false
            pager: '#X_Table_PAGE',
            rownumbers: true,
            gridComplete: function () {

            }
        });
    } else {
        var url = "/proxy-product/product/productApprovalFile/toList?type=" + approvalFileType + "&correlationId=" + orgaProductId + "&baseDataFlag=false";
        pizhunwenjianUrl = url;
        $('#X_Table').XGrid({
            url: url,
            colNames: ['', '<i class="i-red">*</i>批件名称', '<i class="i-red">*</i>批件编号', '<i class="i-red">*</i>签发日期', '<i class="i-red">*</i>有效期至', '附件', '附件数据',
                '附件(签章)'],
            colModel: [
                {
                    name: 'id',
                    index: 'id',//索引。其和后台交互的参数为sidx
                    key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                    hidden: true
                },
                {
                    name: 'batchName',
                    index: 'batchName',
                    rowtype: '#batchName',
                }, {
                    name: 'batchCode',
                    index: 'batchCode'
                }, {
                    name: 'issueDateStr',
                    index: 'issueDateStr'

                }, {
                    name: 'validityDateStr',
                    index: 'validityDateStr',
                    width: 300,
                    formatter: function (value, a, obj, c, d) {
                        if (obj.showRenewValidityDate == true) {
                            $('#editDelayBtn').show()
                        }
                        if (obj.renewValidityDate != null && obj.renewValidityDate.length) {
                            //展示编辑延期按钮
                            const date = obj.renewValidityDate
                            const correlationId = obj.correlationId;
                            const batchCode = obj.batchCode;
                            //业务延期日期
                            const delay = '<a class="delayDate btn btn-default" style="display: inline-block;border: 1px solid red;color: red;margin-left: 20px"  onclick="showDelayModifyDetail(\'' + correlationId + '\',\'' + batchCode + '\')">' + '延至' + '<span class="delayDateVal">' + date + '</span>' + '</a>';
                            //业务截止日期
                            const validateDay = '<span class="validateDay" style="margin-left: 120px;">' + value + '</span>'
                            return validateDay + delay;
                        }
                        return value
                    }
                }, {
                    name: 'enclosure',
                    index: 'enclosure',
                    formatter: function (value) {
                        var str = '无';
                        if (value) {
                            str = '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
                        }
                        return str;
                    },
                    unformat: function (e) {
                        e = e.replace(/<[^>]+>/g, '');
                        if (e == "无") {
                            e = 0;
                        }
                        return e;
                    }
                }, {
                    name: 'enclosureList',
                    index: 'enclosureList',
                    hidden: true,
                    formatter: function (value) {
                        if (value) {
                            return JSON.stringify(value);
                        }
                        return JSON.stringify([]);
                    }
                }, {
                    name: 'signatureImageNumber',
                    index: 'signatureImageNumber',
                    formatter: function (value) {
                        var str = '无';
                        if (value) {
                            str = '<a href="javascript:;" onclick="showSignatureImg(this);">' + value + '</a>';
                        }
                        return str;
                    },
                }, {
                    name: 'showRenewValidityDate',
                    index: 'showRenewValidityDate',
                    hidden: true
                },
                {
                    name: 'renewValidityDate',
                    index: 'renewValidityDate',
                    hidden: true
                },
                {
                    name: 'correlationId',
                    index: 'correlationId',
                    hidden: true
                },
                {
                    name: 'validityDateStr2',
                    index: 'validityDateStr2',
                    formatter: function (a, b, obj, d, e) {
                        return obj.validityDateStr;
                    },
                    hidden: true
                }
            ],
            rowNum: 10,
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true,
            onSelectRow: function (a, b, c, d, e) {
                if (selectApprovalRow != null && selectApprovalRow.id == c.id) {
                    // 当选中当id一样，说明是取消选中，设置为空
                    selectApprovalRow = null;
                } else {
                    selectApprovalRow = c;
                }
            }
        });
    }

    //修改记录
    if ($('#modifyTable').length > 0) {
        var commodity_code = $("#commodity_code").val();
        var productOrgCode = $("#productOrgCode").val();
        $('#modifyTable').XGrid({
            url: "/proxy-product/product/approvalRecord/toList?productCode=" + commodity_code + "&applicationOperation=" + applicationOperation + "&orgCode=" + productOrgCode,
            colNames: ['申请时间', '申请操作', '申请人', '单据编号', '审核状态', '申请明细', '审批流程'],
            colModel: [{
                name: 'applicationTimeStr', //与反回的json数据中key值对应
                index: 'applicationTimeStr', //索引。其和后台交互的参数为sidx
                key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            }, {
                name: 'applicationOperationValue',
                index: 'applicationOperationValue'
            }, {
                name: 'applicantValue',
                index: 'applicantValue'
            }, {
                name: 'applicationCode',
                index: 'applicationCode'
            }, {
                name: 'auditStatus',
                index: 'auditStatus',
                formatter: function (value, grid, rows, state) {
                    if (value == 0) {
                        return "录入中";
                    } else if (value == 1) {
                        return "审核中";
                    } else if (value == 2) {
                        return "审核通过";
                    } else if (value == 3) {
                        return "审核不通过";
                    }
                }
            }, {
                name: 'applicationDetails',
                index: 'applicationDetails',
                formatter: function (value, b, rowData) {
                    if (rowData.applicationOperation != 0) {
                        var arr = value.split('；');
                        var html = '<div class="modifyTheRecord">\n' +
                            '<div class="itemContent"><div class="mrLeft">';
                        for (var i = 0; i < arr.length; i++) {
                            html += '<p>' + arr[i] + '</p>';
                        }
                        html += '</div></div>' +
                            '\t\t<div class="mrRight">\n' +
                            '\t\t\t<a href="javascript:;" class="moreBtn">展开</a>\n' +
                            '\t\t</div>\n' +
                            '\t</div>\n';
                        return html;
                    } else {
                        value = value.replace(/&lt;/g, '<').replace(/&gt;/g, '>');
                        return value;
                    }
                }
            }, {
                name: 'taskId',
                index: 'taskId',
                formatter: function (value, grid, rows, state) {
                    if (value != '') {
                        return "<a href = 'javascript:void(0)' onclick='toFlowChart(" + value + ")'>查看</a>";
                    } else {
                        return "";
                    }
                }
            }],
            rowNum: 10,
            altRows: true, //设置为交替行表格,默认为false
            pager: '#modifyTable_page',
            rownumbers: true,
            gridComplete: function () {

            }
        });
    }
    /*首营申请修改记录*/
    $('#firstEditRecordTable').XGrid({
        url: "/proxy-product/product/productFirst/getFirstEditRecords?applicationCode=" + $("#applicationCode").val(),
        colNames: ['变更时间', '变更人', '单据编号', '变更明细'],
        colModel: [{
            name: 'createTimeStr',
            index: 'createTimeStr'
        }, {
            name: 'createUserValue',
            index: 'createUserValue'
        }, {
            name: 'applicationCode',
            index: 'applicationCode'
        }, {
            name: 'changeDetails',
            index: 'changeDetails',
            formatter: function (value, b, rowData) {
                var arr = value.split(';');
                var html = '<div class="modifyTheRecord">\n' +
                    '<div class="itemContent"><div class="mrLeft">';
                for (var i = 0; i < arr.length; i++) {
                    html += '<p>' + arr[i] + '</p>';
                }
                html += '</div></div>' +
                    '\t\t<div class="mrRight">\n' +
                    '\t\t\t<a href="javascript:;" class="moreBtn">展开</a>\n' +
                    '\t\t</div>\n' +
                    '\t</div>\n';
                return html;
            }
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#firstEditRecord_page',
        rownumbers: true,
        gridComplete: function () {

        }
    });
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    //修改记录申请明细展开收起按钮
    $("#modifyTable,#firstEditRecordTable").on("click", ".moreBtn", function () {
        var type = $.trim($(this).text());
        var tr = $(this).parents("tr");
        var innerHeiht = 0;
        if (type == '展开') {
            innerHeiht = tr.find(".mrLeft").innerHeight();
            $(this).html('收起');
        } else if (type == '收起') {
            innerHeiht = 40;
            $(this).html('展开');
        }
        if (innerHeiht < 40) {
            innerHeiht = 40;
        }
        tr.find(".modifyTheRecord .itemContent").animate({
            height: innerHeiht
        }, 500)
    })


    //受托厂家是否显示
    initChecked();
    //多选框选中
    loadData("keyConservationCategories");
    loadData("specialAttributes");
    loadData("storageAttribute");
    // loadData("operatingCustomers");
    // loadData("specialProvision");
    function loadData(key) {
        var tagName, type, arr, thisVal;
        var value = $("#" + key).val();
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (type == 'checkbox') {
                if ($.type(value) == 'array') {
                    arr = value;
                } else if ($.type(value) == 'string') {
                    arr = value.split(',');
                }
                for (var i = 0; i < arr.length; i++) {
                    if (thisVal == arr[i]) {
                        $(this).attr('checked', true);
                        break;
                    }
                }
            }
        });
    }

    // 温度范围特殊处理
    var temperatureRange = $('#temperatureRanges').val();
    if (temperatureRange != null) {
        var tempArr = temperatureRange.split(',');
        for (var i = 0; i < tempArr.length; i++) {
            if (i == 0) {
                $('#temperatureRange1').val(tempArr[0]);
            }
            if (i == 1) {
                $('#temperatureRange2').val(tempArr[1]);
            }
        }
    }

    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    $("input").attr("readonly", true);
    $("textarea").attr("readonly", true);
    $("input[type='checkbox']").attr("disabled", true);
    $("input[type='radio']").attr("disabled", true);
    $("select").attr("disabled", "disabled");
    $("#rowOperationsDiv").hide();
    $(".bootstrap-tagsinput span[data-role='remove']").hide();
    $("#auditOpinion").removeAttr("readonly");
    $(".glyphicon-search").hide();
    //信息回显
    showDictValue();
    //流程图显示
    var processInstaId = $("#processId").val();
    if (processInstaId != undefined && processInstaId != "") {//已提交审核加载流程图
        initApprovalFlowChart(processInstaId);
    }
    $('.audiPass').on('click', function () {
        $('#auditOpinion').val('');
        var status = this.getAttribute("status");
        var title = "审核通过";
        if (status == 4) {
            title = "审核不通过";
            $('#opinion').show();
        } else {
            $('#opinion').hide();
        }
        var d = utils.dialog({
            title: title,
            content: $('#container'),
            okValue: '确定',
            ok: function () {
                //审核不通过，意见不能为空
                if (status != 2) {
                    if ($("#auditOpinion").val() == "") {
                        utils.dialog({ content: '审批意见不能为空!', quickClose: true, timeout: 2000 }).showModal();
                        return false;
                    }
                }
                d.close();
                submitAuditInfo(status, $("#auditOpinion").val());
            },
            cancelValue: '取消',
            cancel: function () {
            }
        });
        d.showModal();
    });

    setTimeout(function () {
        let scopeOfOperationVal = $('#scopeOfOperationVal').attr('data-value');
        if (scopeOfOperationVal == '中药饮片') {
            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地')
        }
    }, 200)
})

/**
 * 字典值回显
 */
function showDictValue() {
    //申请人,申请人机构回显
    //loadUserName("applicant","applicantOrgCode");
    //商品大类
    showComValue("largeCategory", "1017");
    //生产厂家--添加地址信息
    //showManufacturerInfo("manufacturer",$("#manufacturer").val());
    showComValue("manufacturer", "1003");
    //包装单位
    showComValue("packingUnit", "1002");
    //剂型
    showComValue("dosageForm", "1001");
    // 委托厂家--添加地址信息
    //showManufacturerInfo("entrustmentManufacturer",$("#entrustmentManufacturer").val());
    //showComValue("entrustmentManufacturer","1003");
    //存储条件
    showComValue("storageConditions", "1019");
    //处方分类
    showComValue("prescriptionClassification", "1016");
    //所属经营范围
    //所属经营范围
    var simpleCode = $("#scopeOfOperation").val();
    var orgCode = $("#applicantOrgCode").val();
    if (simpleCode != "") {
        $.ajax({
            url: '/proxy-sysmanage/sysmanage/dict/queryscopebycode?orgCode=' + orgCode + "&simpleCode=" + simpleCode,
            type: 'get',
            dataType: 'json',
            async: false,
            success: function (data) {
                console.log(data);
                if (data.code == 0) {
                    var simpleCodeName = "";
                    for (var i = 0; i < data.result.length; i++) {
                        if (i != data.result.length - 1) {
                            simpleCodeName = simpleCodeName + data.result[i].name + ",";
                        } else {
                            simpleCodeName = simpleCodeName + data.result[i].name;
                        }
                    }
                    $("#scopeOfOperationVal").val(simpleCodeName);
                    $("#scopeOfOperationVal").attr("data-value", simpleCodeName);
                    $("#scopeOfOperationVal").attr('title', simpleCodeName);
                }
            }
        })
    }
    /*// 一级分类 firstCategory
    showComValue("firstCategory","1025");
    //二级分类 secondCategory
    showComValue("secondCategory","1026");
    //三级分类 thirdCategory
    showComValue("thirdCategory","1027");*/
    //商品定位
    showCommodityPosition($("#commodityPosition").val())
    // 商品定位二级
    showSecondPosition($("#commodityPosition").val(), $('#secondCommodityPosition').val());
    //首营供应商 firstBattalionSupplier
    var supplierId = $("#firstBattalionSupplier").val();
    if (supplierId != undefined && supplierId != "") {
        $.ajax({
            url: '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/firstSupplierOrgBaseById?id=' + supplierId,
            type: 'get',
            dataType: 'json',
            success: function (data) {
                console.log(data);
                if (data != null && data != undefined) {
                    $("#firstBattalionSupplierVal").val(data.supplierName);
                }
            }
        })
    }
    //品牌
    /*    var brand=$("#brand").val();
        if(brand!="" && undefined != brand){
            $.ajax({
                url:'/proxy-sysmanage/sysmanage/brand/queryById?id='+brand,
                type:'get',
                dataType:'json',
                success:function(data){
                    console.log(data);
                    if(data!=null&&data!=undefined){
                        $("#brandVal").val(data.result.brandName);
                        $("#brandVal").attr("data-value",data.result.brandName);
                    }
                }
            })
        }*/
}

/*function showManufacturerInfo(obj,manuId) {
    if(manuId!=undefined&&manuId!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querymanufactorybyId?manuId='+manuId,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if (data.code==0){
                    $("#"+obj+"Val").val(data.result.manufactoryName);
                    $("#"+obj+"Address").val(data.result.address);
                }
            }
        })
    }
}*/
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function showComValue(obj, type) {
    var id = $("#" + obj).val();
    if (id != undefined && id != "") {
        $.ajax({
            url: '/proxy-sysmanage/sysmanage/dict/querybeanbytype?type=' + type + "&id=" + id,
            type: 'get',
            dataType: 'json',
            async: false,
            success: function (data) {
                //console.log(data);
                if (data.code == 0) {
                    $("#" + obj + "Val").val(data.result);
                    $("#" + obj + "Val").attr("data-value", data.result);
                    $("#" + obj + "Val").attr("title", data.result);
                    if (obj == "largeCategory") {
                        if (data.result == "中药饮片") {
                            $("#Registrant").text('化妆品备案人/注册人');
                            $(".productrate").show();
                            //Is_ZYYP_flag = true
                            $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                            $('#maintenancePeriod').prev().find('i').remove(); // 养护周期
                            $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                            $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                            $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                            $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                        }else if(data.result=="医疗器械"){
                            $("#Registrant").text('医疗器械注册人/备案人名称');
                        } else {
                            $("#Registrant").text('化妆品备案人/注册人');
                            $(".productrate").hide();
                        }
                        if (largeCategoryArray.indexOf(data.result) > -1) {
                            $(".productrate").show();
                        } else {
                            $(".productrate").hide();
                        }
                    }
                }
            }
        })
    }
}

// 展示商品定位一级
function showCommodityPosition(currentPositionId) {
    var pageType = $("#pageType").val();
    if ("orga" == pageType) {
        pageType = 2;
    }
    if (currentPositionId) {
        $.ajax({
            type: "get",
            url: "/proxy-product/product/dict/common/queryCommon?type=3&commonName=&pageType=" + pageType,
            async: false,
            dataType: "json",
            success: function (data) {
                if (data.result && data.result.length) {
                    const selectedCommodityPosition = data.result.find(item => item.id == currentPositionId)
                    if (selectedCommodityPosition) {
                        $('#commodityPosition').val(selectedCommodityPosition.id)
                        $('#commodityPositionVal').val(selectedCommodityPosition.name)
                        refreshRequiredFields(selectedCommodityPosition)
                    }
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
}

// 展示商品定位二级
function showSecondPosition(parentId, secondId) {
    var pageType = $("#pageType").val();
    if ("orga" == pageType) {
        pageType = 2;
    }
    if (parentId) {
        $.ajax({
            type: "get",
            url: "/proxy-product/product/dict/common/queryByParentId",
            async: false,
            data: { "parentId": parentId, "pageType": pageType },
            dataType: "json",
            success: function (data) {
                let options = ['<option value="">请选择</option>']
                if (data.result) {
                    options = options.concat(data.result.map(item => {
                        return "<option value=" + item.id + ">" + item.name + "</option>"
                    }))
                }
                $('#secondCommodityPositionVal').html(options.join(""))
                $('#secondCommodityPositionVal').val(secondId)
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
}

// 商品二级定位变更时，将其同步到隐藏字段
function onSecondCommodityPositionChanged() {
    $('#secondCommodityPosition').val($('#secondCommodityPositionVal').val())
}

function refreshRequiredFields(selectedItem) {
    if (selectedItem.requiredFlag) {
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).html("<i class='text-require'>*  </i>商品定位二级")
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).addClass('require')
        $('#secondCommodityPositionVal').addClass('{validate:{ required :true}}')
    } else {
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).find('i').remove()
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).removeClass('require')
        $('#secondCommodityPositionVal').removeClass('{validate:{ required :true}}')
    }
    if (selectedItem.modeRequiredFlag) {
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).html("<i class='text-require'>*  </i>集采签约方式")
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).addClass('require')
        $('#purchaseContractMode').addClass('{validate:{ required :true}}')
    } else {
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).find('i').remove()
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).removeClass('require')
        $('#purchaseContractMode').removeClass('{validate:{ required :true}}')
    }
}

function loadUserName(key, orgaKey) {
    var userId = $("#" + key).val();
    if (userId == "") {
        return;
    }
    $.ajax({
        type: "get",
        url: "/proxy-sysmanage/sysmanage/system/queryUserInfoById",
        data: { "userId": userId },
        dataType: "json",
        success: function (data) {
            //console.log(data.result);
            if (data.code == 0 && data.result != null) {
                var userName = data.result.userName;
                if (orgaKey != null) {//申请人机构显示
                    $("#" + orgaKey + "Val").val(data.result.orgName);
                }
                $("#" + key + "Val").val(userName);
                $("#" + key + "Val").attr("data-value", userName);
            }
        },
        error: function () {
        }
    });
}

/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj) {
    var parentId = $(obj).parents("tr").attr("id");
    var data = $('#X_Table').getRowData(parentId);
    if (data.enclosureList) {
        $.viewImg({
            fileParam: {
                name: 'enclosureName',
                url: 'enclosureUrl'
            },
            list: JSON.parse(data.enclosureList)
        })
    }
}

function showSignatureImg(obj) {
    var parentId = $(obj).parents("tr").attr("id");
    var data = $('#X_Table').getRowData(parentId);
    if (data.enclosureList) {
        var list = JSON.parse(data.enclosureList);
        var array = new Array();
        for (var i = 0; i < list.length; i++) {
            if (list[i].signatureImageUrl !== '') {
                array.push(list[i]);
            }
        }
        $.viewImg({
            fileParam: {
                name: 'enclosureName',
                url: 'signatureImageUrl'
            },
            list: array
        })
    }
}

/**
 * 审核按钮操作
 */
function submitAuditInfo(auditStatus, auditOpinion) {
    parent.showLoading();
    var productData = {};
    var applicationCode = $("#applicationCode").val();
    var taskId = $("#taskId").val();
    productData.applicationCode = applicationCode;
    productData.taskId = taskId;
    productData.auditStatus = auditStatus;
    productData.auditOpinion = auditOpinion;
    productData.updataStutus = "0";
    //商品编码与机构编码
    productData.orgCode = $("#applicantOrgCode").val();
    productData.productCode = $("#commodity_code").val();
    console.log(productData);
    var data = JSON.stringify(productData);
    console.log(data);
    $.ajax({
        type: "post",
        url: "/proxy-product/product/productFirst/auditProduct",
        async: false,
        data: data,
        dataType: "json",
        contentType: "application/json",
        success: function (data) {
            var msg = data.result.msg;
            var approvalFileStatus = data.result.approvalFileStatus;
            if (approvalFileStatus != undefined && !approvalFileStatus) {
                utils.dialog({
                    title: "提示",
                    content: data.result.msg,
                    width: 300,
                    height: 30
                }).showModal();
                return false;
            }
            if (msg == "sucess") {
                if (auditStatus == 2) {
                    msg = "恭喜审核通过！";
                } else if (auditStatus == 4) {
                    msg = "驳回成功！";
                } else if (auditStatus == 3) {
                    msg = "流程已关闭！";
                }
            }
            utils.dialog({
                title: "提示",
                content: msg,
                width: 300,
                height: 30,
                okValue: '确定',
                ok: function () {
                    utils.closeTab();
                }
            }).showModal();
            $(".ui-dialog-close").hide();
            return false;
        },
        error: function () {
            utils.dialog({ content: '审核失败', quickClose: true, timeout: 2000 }).showModal();
        },
        complete: function () {
            parent.hideLoading();
        }
    });

}

function initAdjustPriceHistorys() {
    var productId = $("#orgaProductId").val();
    dialog({
        url: '/proxy-product/product/adjustPrice/adjustPriceHistory',
        title: '商品APP售价调价历史记录列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: { "productId": productId },
        onclose: function () {
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
}

function initBuyHistorys() {
    var productId = $("#orgaProductId").val();
    dialog({
        url: '/proxy-product/product/orgApproval/attributeChangeHistory',
        title: '商品采购员历史记录列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: { "productId": productId },
        onclose: function () {
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
}

//判断委托产家是否显示
function initChecked() {
    var type = $("input[name='entrustmentProduction']:checked").val();
    if (type == 1) {
        $(".entManufacturerDiv").show();
        $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>受托厂家')
    } else {
        $(".entManufacturerDiv").hide();
        $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('受托厂家')
    }
};

/**
 * 加载所属经营范围对应的批准文件
 * @param simpleCode
 */
function localBatchName(type) {
    $.ajax({
        type: "post",
        url: "/proxy-sysmanage/sysmanage/dict/getBatchnamesByType",
        async: false,
        data: { "type": type },
        dataType: "json",
        success: function (data) {
            var html = '<option value="0">商品附件</option>';
            if (data.code == 0) {
                var arr = data.result;
                if (arr != null) {
                    for (var i = 0; i < arr.length; i++) {
                        html += '<option value="' + arr[i].batchId + '">' + arr[i].batchName + '</option>';
                    }
                }
            }
            $("select[name='batchName']").html(html);
        },
        error: function () {
            utils.dialog({ content: '加载失败', quickClose: true, timeout: 2000 }).showModal();
        }
    });
}

/**
 * 流程图显示
 */
function initApprovalFlowChart(processInstaId) {
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/queryTotle?processInstaId=" + processInstaId,
        async: false,
        success: function (data) {
            if (data.code == 0 && data.result != null) {
                console.log(data.result)
                $('.flow').process(data.result);
            }
        },
        error: function () {
        }
    });
}

/**
 * 修改记录查看流程图
 * @returns
 */
function toFlowChart(processInstaId) {
    dialog({
        url: '/proxy-product/product/approvalRecord/toFlowChart',
        title: '审批流程',
        width: 1200,
        height: 520,
        data: { "processInstaId": processInstaId },
        onclose: function () {
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
}

function loadProductInstruction(baseProductId, type) {
    $.ajax({
        type: "post",
        url: "/proxy-product/product/productInstruction/toList",
        async: false,
        data: { "correlationId": baseProductId, "type": type },
        dataType: "json",
        success: function (data) {
            //console.log(data)
            if (data.code == 0) {
                var result = data.result;
                var html = getProductHTML(result);
                $("#insForm").html(html);
            } else {
                $("#insForm").html('');
            }
        },
        error: function () {
            utils.dialog({ content: '加载失败', quickClose: true, timeout: 2000 }).showModal();
        }
    });
}

/* 商品主数据带回说明书
 * @param arr
 * @returns {string}
 */
function getProductHTML(arr) {
    arr = arr == null ? [] : arr;
    var left = [], right = [];
    var html = '';
    for (var i = 0; i < arr.length; i++) {
        var type = arr[i].controlTypes;
        if (type == 1) {
            //复选框
            left.push(arr[i]);
        } else {
            //文本框/
            right.push(arr[i]);
        }
    }
    var list = left.concat(right);
    for (var i = 0; i < list.length; i++) {
        var cType = list[i].controlTypes;
        var attr = list[i].checked;
        var attributeRequired = list[i].attributeRequired;
        if (attributeRequired == null || attributeRequired == "null") {
            attributeRequired = "";
        }
        if (cType == 0) {
            //文本框
            html += '<div class="row attributeList">\n' +
                '        <div class="input-group">\n' +
                '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="' + attributeRequired + '">' +
                '           <input type="hidden" class="checkval" name="checked" value="' + attr + '">' +
                '           <input type="hidden" class="controlTypes" name="controlTypes" value="0">' +
                '           <input type="hidden" class="instructionsId" name="id" value="' + list[i].id + '">' +
                '           <label class="input-group-addon require">';
            if (Number(attributeRequired) && Number(attributeRequired) == 1) {
                //必填
                html += '<i class="text-require" style="color:red;display: contents;">*  </i>';
            }
            html += list[i].attribute + ':<input type="hidden" class="attributeName" name="attribute" value="' + list[i].attribute + '" /></label>' +
                '           <textarea class="form-control attributeValue only-read';
            if (Number(attributeRequired) && Number(attributeRequired) == 1) {
                //必填
                html += ' {validate:{ required :true}}';
            }
            html += '" name="attributeValue" >' + list[i].attributeValue + '</textarea>\n' +
                '       </div>\n' +
                '</div>';

        } else if (cType == 1) {
            //复选框
            html += '<div class="row attributeList">\n' +
                '        <div class="input-group">\n' +
                '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="' + attributeRequired + '">' +
                '           <input type="hidden" class="checkval" name="checked" value="' + attr + '">' +
                '           <input type="hidden" class="controlTypes" name="controlTypes" value="1">' +
                '           <input type="hidden" class="instructionsId" name="id" value="' + list[i].id + '">' +
                '           <label class="input-group-addon require">';
            if (Number(attributeRequired) && Number(attributeRequired) == 1) {
                //必填
                html += '<i class="text-require" style="color:red;display: contents;">*  </i>';
            }
            html += list[i].attribute + ':<input type="hidden" class="attributeName" name="attribute" value="' + list[i].attribute + '" /></label>' +
                '               <div class="form-control" style="height:auto;min-height:34px;">\n' +
                '                       <div class="checkbox" style="margin:0;">';
            var attrVal = list[i].checked;
            if (attrVal && attrVal != '') {
                var item = attrVal.split(',');//把复选框属性转换为数组
                var attributeValue = list[i].attributeValue;
                var checkList = attributeValue.split(',');
                for (var j = 0; j < item.length; j++) {

                    html += '<label style="margin-right: 14px;"><input type="checkbox" ';
                    if (checkList && checkList.length > 0) {
                        for (var x = 0; x < checkList.length; x++) {
                            if (checkList[x] == item[j]) {
                                html += 'checked="checked"';
                                break;
                            }
                        }
                    }
                    html += ' name="attributeValue" value="' + item[j] + '" class="only-read">' +
                        '                  ' + item[j] + '</label>';

                }
            }
            html += '              </div>\n' +
                '               </div>' +
                '           </div>\n' +
                '       </div>';
        }
    }
    return html;
}
