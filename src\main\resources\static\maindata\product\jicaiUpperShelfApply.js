$('div[fold=head]').fold({
    sub: 'sub'
});
var colNames = ['商品ID', '商品唯一ID', '原商品ID', '机构code', '机构', '业务类型', '商品编号', '原商品编码', '小包装条码', '商品名称', '通用名', '商品规格', '生产厂家', '单位',
    '经营状态','上次下架时间','上次下架原因','上次下架备注','上次下架附件',
    '申请商品定位一级', '品类策略审核商品定位一级', '申请商品定位二级', '申请商品定位二级', '品类策略审核商品定位二级', '申请集采签约方式', '品类策略审核集采签约方式', '是否专供', 'APP销售价是否维价',
    '终端零售价是否维价', '应季类型', '建议终端售价', '价格管理部审核建议终端售价', '建议APP售价', '价格管理部最终定价', '建议智鹿总部采购价', '价格管理部审核智鹿总部采购价', '建议连锁APP售价',
    '价格管理部审核连锁APP售价','建议荷叶大药房采购价','价格管理部审核荷叶大药房采购价','连锁票面毛利率', '票面毛利率', '终端毛利率', '价格对比', '备注','采购员ID', '采购员', '销售状态', '库存数量',
    '最后含税进价', '最后入库时间', '最后供应商', '商品产地', '剂型', '处方分类','批准文号', '存储条件', '底价', '一级分类', '二级分类', '三级分类', '四级分类', '单据详情id','爆款标识'];
var colModel = [
    {
        name: 'productId',
        index: 'productId',
        hidden: true,
        hidegrid: true
    }, {
        name: 'productIdChannel',
        index: 'productIdChannel',
        hidden: true,
        hidegrid: true
    }, {
        name: 'oldProductId',
        index: 'oldProductId',
        hidden: true,
        hidegrid: true
    }, {
        name: 'orgCode',
        index: 'orgCode',
        hidden: true,
        hidegrid: true
    }, {
        name: 'orgName',
        index: 'orgName',
        width: 210
    }, {
        name: 'channelId',
        index: 'channelId',
        width: 100
    }, {
        name: 'productCode',
        index: 'productCode',
        width: 100
    }, {
        name: 'oldProductCode',
        index: 'oldProductCode',
        width: 140
    }, {
        name: 'smallPackageBarCode',
        index: 'smallPackageBarCode',
        width: 130
    }, {
        name: 'productName',
        index: 'productName',
        width: 110
    }, {
        name: 'commonName',
        index: 'commonName',
        width: 110
    }, {
        name: 'specifications',
        index: 'specifications',
        width: 140
    }, {
        name: 'manufacturerVal',
        index: 'manufacturerVal',
        width: 240
    }, {
        name: 'packingUnitVal',
        index: 'packingUnitVal',
        width: 80
    }, {
        name: 'operatingStateVal',
        index: 'operatingStateVal',
        width: 80
    }, {
        name: 'lastLowerShelfTime',
        index: 'lastLowerShelfTime',
        width: 200,
        formatter: function (value) {
            if (value) {
                return new Date(value).Format('yyyy-MM-dd');
            }
        }
    }, {
        name: 'lastLowerShelfReason',
        index: 'lastLowerShelfReason',
        width: 200
    }, {
        name: 'lastLowerShelfRemark',
        index: 'lastLowerShelfRemark',
        width: 150
    }, {
        name: 'lastLowerShelfEnclosureUrl',
        index: 'lastLowerShelfEnclosureUrl',
        formatter: function (val,rowType,rowData) {
            if(val) {
                return `<a href="javascript:;" data-url="${val}" class="file_a">📎</a>`;
            }else {
                return '';
            }
        },
        unformat: function (val,rowModel,ele) {
            if($(ele).find('a.file_a').length > 0) {
                return $(ele).find('a.file_a').attr('data-url');
            }else {
                return '';
            }
        }
    }, {
        name: 'commodityPosition',
        index: 'commodityPosition',
        rowtype: '#commodityPosition',
        width: 150
    }, {
        name: 'auditCommodityPosition',
        index: 'auditCommodityPosition',
        rowtype: '#auditCommodityPosition',
        width: 200
    }, {
        name: 'secondCommodityPosition',
        index: 'secondCommodityPosition',
        hidden: true,
        hidegrid: true
    }, {
        name: 'secondCommodityPositionVal',
        index: 'secondCommodityPositionVal',
        rowtype: '#secondCommodityPosition',
        width: 150
    }, {
        name: 'auditSecondCommodityPosition',
        index: 'auditSecondCommodityPosition',
        rowtype: '#auditSecondCommodityPosition',
        width: 200
    },
    {
        name: 'purchaseContractMode',
        index: 'purchaseContractMode',
        rowtype: '#purchaseContractMode',
        width: 150
    }, {
        name: 'auditPurchaseContractMode',
        index: 'auditPurchaseContractMode',
        rowtype: '#auditPurchaseContractMode',
        width: 200
    }, {
        name: 'exclusiveYn',
        index: 'exclusiveYn',
        hidden: true,
        hidegrid: true,
        width: 80
    }, {
        name: 'dimensionSalesPriceYn',
        index: 'dimensionSalesPriceYn',
        rowtype: '#dimensionSalesPriceYn',
        width: 180
    }, {
        name: 'dimensionTerminalPriceYn',
        index: 'dimensionTerminalPriceYn',
        rowtype: '#dimensionTerminalPriceYn',
        width: 150
    }, {
        name: 'seasonalVarieties',
        index: 'seasonalVarieties',
        rowtype: '#seasonalVarieties',
        width: 120
    }, {
        name: 'terminalPrice',
        index: 'terminalPrice',
        formatter: function (e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#terminalPrice',
        width: 130
    }, {
        name: 'auditTerminalPrice',
        index: 'auditTerminalPrice',
        formatter: function (e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#auditTerminalPrice',
        width: 220
    }, {
        name: 'appPrice',
        index: 'appPrice',
        formatter: function (e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#appPrice',
        width: 110
    }, {
        name: 'auditAppPrice',
        index: 'auditAppPrice',
        formatter: function (e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#auditAppPrice',
        width: 220
    }, {
        name: 'zhiluPrice',
        index: 'zhiluPrice',
        formatter: function (e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#zhiluPrice',
        width: 150
    }, {
        name: 'auditZhiluPrice',
        index: 'auditZhiluPrice',
        formatter: function (e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#auditZhiluPrice',
        width: 220
    }, {
        name: 'chainGuidePrice',
        index: 'chainGuidePrice',
        formatter: function (e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#chainGuidePrice',
        width: 140
    }, {
        name: 'auditChainGuidePrice',
        index: 'auditChainGuidePrice',
        formatter: function (e) {
            if (e != null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#auditChainGuidePrice',
        width: 220
    },{
        name: 'heyePrice',
        index: 'heyePrice',
        formatter: function (e) {
            if(e!=null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#heyePrice',
        width:180
    }, {
        name: 'auditHeyePrice',
        index: 'auditHeyePrice',
        formatter: function (e) {
            if(e!=null) {
                return Number(e).toFixed(2);
            }
        },
        rowtype: '#auditHeyePrice',
        width:240
    }, {
        name: 'chainParGrossMargin',
        index: 'chainParGrossMargin',
        width: 130
    }, {
        name: 'parGrossMargin',
        index: 'parGrossMargin',
        width: 120
    }, {
        name: 'terminalGrossMargin',
        index: 'terminalGrossMargin',
        width: 120
    }, {
        name: 'comparison',
        index: 'comparison',
        width: 100,
        formatter: function (e) {
            return `<button type="button" class="btn btn-info comparison">对比</button>`;
        },
        unformat: function (e) {
            return ''
        }
    }, {
        name: 'remarks',
        index: 'remarks',
        rowtype: '#remarks',
        width: 220
    }, {
        name: 'buyer',
        index: 'buyer',
        hidden: true,
        hidegrid: true
    }, {
        name: 'buyerVal',
        index: 'buyerVal',
        width: 80
    }, {
        name: 'ecProductStatus',
        index: 'ecProductStatus',
        width: 80
    }, {
        name: 'inventoryQuantity',
        index: 'inventoryQuantity',
        width: 80
    }, {
        name: 'lastIncludingTaxPurchasePrice',
        index: 'lastIncludingTaxPurchasePrice',
        width: 110
    }, {
        name: 'lastStorageTime',
        index: 'lastStorageTime',
        width: 200,
        formatter: function (value) {
            if (value) {
                return new Date(value).Format('yyyy-MM-dd');
            }
        }
    }, {
        name: 'lastSupplier',
        index: 'lastSupplier',
        width: 220
    }, {
        name: 'producingArea',
        index: 'producingArea',
        width: 80
    }, {
        name: 'dosageFormVal',
        index: 'dosageFormVal',
        width: 100
    }, {
        name: 'prescriptionClassificationVal',
        index: 'prescriptionClassificationVal',
        width: 100
    }, {
        name: 'approvalNumber',
        index: 'approvalNumber',
        width: 160
    }, {
        name: 'storageConditionsVal',
        index: 'storageConditionsVal',
        width: 80
    }, {
        name: 'floorPrice',
        index: 'floorPrice',
        width: 80,
        formatter: function (value) {
            return value || 0;
        }
    }, {
        name: 'firstCategoryVal',
        index: 'firstCategoryVal',
        width: 100
    }, {
        name: 'secondCategoryVal',
        index: 'secondCategoryVal',
        width: 120
    }, {
        name: 'thirdCategoryVal',
        index: 'thirdCategoryVal',
        width: 120
    }, {
        name: 'fourCategoryVal',
        index: 'fourCategoryVal',
        width: 100
    }, {
        name: 'id',
        index: 'id',
        hidden: true,
        hidegrid: true
    },{
        name:'inVogue',
        index:'inVogue',
        hidden:true,
        hidegrid:true
    }];
var pageType = $("#pageType").val();
resetDisabled();// 设置可编辑单元格
$('#X_Table').XGrid({
    data: [],
    colNames: colNames,
    colModel: colModel,
    rowNum: 20,
    rownumbers: true,
    key: 'productIdChannel',
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {
        //console.log('双击行事件', id, dom, obj, index, event);
    },
    onSelectRow: function (id, dom, obj, index, event) {
        //console.log('单机行事件', id, dom, obj, index, event);
    },
    gridComplete: function () {
        $("#X_Table [name=commodityPosition]").each((index, item) => {
            onCommodityPositionChanged(item)
        })
    }
});

//设置显示列
$("#setRow").click(function () {
    //判断，如果是本地数据，提示将清空数据
    if (pageType == '1') {//草稿编辑页面
        utils.dialog({
            content: '设置筛选列将清空本地未保存数据，是否继续清空?',
            title: '提示',
            cancelValue: '取消',
            cancel: true,
            okValue: '确定',
            ok: function () {
                $('#X_Table').XGrid('filterTableHead', 1000);
            }
        }).showModal();
    } else {
        $('#X_Table').XGrid('filterTableHead', 1000);
    }
});
//新增行
$("#addRow").on("click", function () {
    var resultArr = $('#X_Table').getRowData();
    dialog({
        url: '/proxy-product/product/upperShelf/tojicaiUpperShelfSearchProduct',
        title: '待上架商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            channelId: (resultArr.length != 0) ? resultArr[0].channelId : '',
            channelName: (resultArr.length != 0) ? resultArr[0].channelVal : ''
        }, // 给modal 要传递的 的数据
        onclose: function (data) {
            var data = this.returnValue;
            if (data) {
                var rows = data.resultArr;
                var orgProductIds = "";
                var channelId = "";
                for (var i = 0; i < rows.length; i++) {
                    var productId = rows[i].orgProductIds;
                    if (i == 0) {
                        orgProductIds = productId;
                        channelId = rows[i].channelId;
                    } else {
                        orgProductIds = orgProductIds + "," + productId;
                    }
                }
                parent.showLoading();
                $.ajax({
                    type: "post",
                    url: "/proxy-product/product/upperShelf/getOrgProInfo",
                    async: true,
                    data: {"channelId": channelId, "orgProductIds": orgProductIds},
                    dataType: "json",
                    success: function (data) {
                        if (data.code == 0) {
                            var resultRows = data.result;
                            //没有库存的商品信息
                            var nonInventoryQuantityMsg = new Array();
                            for (var i = 0; i < resultRows.length; i++) {
                                var productId = resultRows[i].productId;
                                if (!findInArr(productId)) {
                                    resultRows[i].id = resultRows[i]['productId'];
                                    // 计算
                                    if (Number(resultRows[i]['chainGuidePrice'] == 0)) {
                                        resultRows[i]['chainParGrossMargin'] = 0 + '%'
                                    } else {
                                        resultRows[i]['chainParGrossMargin'] = utils.toDecimal2((Number(resultRows[i]['chainGuidePrice']) - Number(resultRows[i]['lastIncludingTaxPurchasePrice'])) / Number(resultRows[i]['chainGuidePrice']) * 100) + "%"
                                    }
                                    var inventoryQuantity = resultRows[i].inventoryQuantity;
                                    if (Number(inventoryQuantity) > 0){
                                        $('#X_Table').XGrid('addRowData', resultRows[i]);
                                    }else {
                                        nonInventoryQuantityMsg.push('机构:'+resultRows[i].orgName+'，商品编码:'+resultRows[i].productCode+'库存为0，已剔除')
                                    }
                                }
                            }
                            let rowData = $('#X_Table').XGrid('getRowData');
                            $(rowData).each((index, item) => {
                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {auditCommodityPosition: item.commodityPosition})
                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {auditSalesClassification: item.salesClassification})
                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {auditAppPrice: item.appPrice})

                                //建议智鹿总部采购价
                                //$("#X_Table").XGrid('setRowData', item.productIdChannel, {zhiluPrice: item.lastIncludingTaxPurchasePrice})
                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {zhiluPrice: item.zhiluPrice})

                                //价格管理部审核智鹿总部采购价
                                //$("#X_Table").XGrid('setRowData', item.productIdChannel, {auditZhiluPrice: item.lastIncludingTaxPurchasePrice})
                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {auditZhiluPrice: item.zhiluPrice})

                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {auditTerminalPrice: item.terminalPrice})
                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {auditChainGuidePrice: item.chainGuidePrice})

                                //建议荷叶大药房采购价
                                //$("#X_Table").XGrid('setRowData', item.productIdChannel, {heyePrice: item.lastIncludingTaxPurchasePrice})
                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {heyePrice: item.heyePrice})

                                //价格管理部审核荷叶大药房采购价
                                //$("#X_Table").XGrid('setRowData', item.productIdChannel, {auditHeyePrice: item.lastIncludingTaxPurchasePrice})
                                $("#X_Table").XGrid('setRowData', item.productIdChannel, {auditHeyePrice: item.heyePrice})
                            })
                            $("#X_Table [name=commodityPosition]").each((index, item) => {
                                onCommodityPositionChanged(item)
                            })
                            if(nonInventoryQuantityMsg.length > 0){
                                var msg = nonInventoryQuantityMsg.join('<br/>');
                                utils.dialog({
                                    title: '温馨提示',
                                    content: `<p style="word-break: break-word;width: 480px; height: 200px;overflow:auto;">`+msg+`</p>`,
                                    width: 480,
                                    height: 200,
                                    okValue: '确定',
                                    ok: function () {}
                                }).showModal()
                            }
                        }
                    },
                    complete: function () {
                        parent.hideLoading();
                    }
                });
            }
        }
    }).showModal();
});

//列表内查询id
function findInArr(productId) {
    var arr = $('#X_Table').getRowData();
    for (var i = 0; i < arr.length; i++) {
        var productID = arr[i].productId;
        if (productID == productId) {
            return true;
        }
    }
    return false;
}

//删除行
$("#deleRow").on("click", function () {
    var selectRow = $('#X_Table').XGrid('getSeleRow');
    if (selectRow.length == 0) {
        utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
    } else {
        utils.dialog({
            title: "提示",
            width: 300,
            height: 30,
            okValue: '确定',
            content: "确定删除此条记录?",
            ok: function () {
                $('#X_Table').XGrid('delRowData', selectRow[0].productIdChannel);
                utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).showModal();
    }
});
//关闭按钮
$("#closePage").on("click", function () {
    var d = dialog({
        title: "提示",
        content: "是否保存草稿？",
        width: 300,
        height: 30,
        okValue: '保存',
        button: [
            {
                value: '关闭',
                callback: function () {
                    utils.closeTab();
                }
            }
        ],
        ok: function () {
            var rowData = $('#X_Table').getRowData();
            if (rowData.length == 0) {
                d.close();
                utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            $("#saveCaoGao").click()
            setTimeout(function () {
                utils.closeTab();
            }, 2000)
        }
    }).showModal();
    $(".ui-dialog-close").hide();
});

function fixRowData(rowData) {
    // 数据格式修正，避免出现后端不支持的数据格式
    rowData.filter(item => typeof (item.secondCommodityPosition) != 'string').map(item => item.secondCommodityPosition = item.secondCommodityPosition.secondCommodityPosition)
}

/*提交审核*/
$("#submitAudit").click(function () {
    var rowData = $('#X_Table').getRowData();
    fixRowData(rowData)
    if (rowData.length == 0) {
        utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    if (rowData.length > 200) {
        utils.dialog({content: '最多添加200条商品', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    if (!checkSubmit()) {
        return false;
    }
    //拼接工作流key值,状态
    var data=JSON.stringify(rowData);
    parent.showLoading();
    $(".textdisabled").attr("disabled", true);
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/upperShelf/saveJicaiUpperShelfInfo",
        data: data,
        async: false,
        dataType: 'json',
        contentType: "application/json",
        error: function () {
            utils.dialog({content: "提交失败！", quickClose: true, timeout: 2000}).showModal();
        },
        success: function (data) {
            if (data.code==0){
                var msg = '提交审核成功';
                if (data.result.code == 1){
                    msg = data.result.msg;
                }
                utils.dialog({
                    title: "提示",
                    content: `<p style="word-break: break-word;width: 340px; height: 100px;overflow:auto;">`+msg+`</p>`,
                    width: 340,
                    height: 100,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                utils.dialog({
                    title: "提示",
                    content: `<p style="word-break: break-word;width: 340px; height: 100px;overflow:auto;">${data.result}</p>`,
                    width: 340,
                    height: 100,
                    okValue: '确定',
                    ok: function () {
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        },
        complete: function () {
            parent.hideLoading();
        }
    });
});
//申请人驳回后提交审核
$('#auditSubmit').on('click', function () {
    if (!checkSubmit()) {
        return false;
    }
    utils.dialog({
        title: "重新提交",
        content: "确定重新提交申请？",
        width: 300,
        height: 30,
        okValue: '确定',
        ok: function () {
            submitAuditInfo(2, "");
        },
        cancelValue: '取消',
        cancel: function () {
        }
    }).showModal();
});

function checkSubmit() {
    var rowData = $('#X_Table').getRowData();
    // var message = "";
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        if (selectRow.commodityPosition == "") {
            utils.dialog({content: '申请商品定位不能为空', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if (selectRow.salesClassification == "") {
            utils.dialog({content: '申请销售分类属性标签不能为空', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if (selectRow.dimensionSalesPriceYn == "") {
            utils.dialog({content: 'APP销售价是否维价不能为空', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if (selectRow.appPrice == "" || selectRow.appPrice <= 0) {
            utils.dialog({content: '建议APP售价不能为空或小于0', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if (Number(selectRow.appPrice) > Number(selectRow.terminalPrice)) {
            utils.dialog({content: '建议APP售价不能大于建议终端售价', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if (selectRow.terminalPrice == "" || selectRow.terminalPrice <= 0) {
            utils.dialog({content: '建议终端售价不能为空或0', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        /*if (parseFloat(selectRow.terminalPrice) < parseFloat(selectRow.appPrice)) {
            message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
        }*/
        if (selectRow.seasonalVarieties == "0" || selectRow.seasonalVarieties == "") {
            utils.dialog({content: '应季类型不能为空', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if (selectRow.chainGuidePrice <= 0) {
            utils.dialog({content: '建议连锁APP售价大于0', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        // if (selectRow.zhiluPrice == "") {
        //     utils.dialog({content: '建议智鹿总部采购价不能为空', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }
        // if (selectRow.heyePrice == "") {
        //     utils.dialog({content: '建议荷叶大药房采购价不能为空', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }
    }
    /*if (message != "") {
        checkPrice(rowData);
        return false;
    }*/
    return checkPrice(rowData);
}

function checkPrice(rowData, type) {
    /*
    * 价格相关提示：
    * -------1.申请智鹿总部采购价-最后含税进价< 0，只能走特殊调价流程 list1
    * 2.申请/审核连锁APP售价＞申请/审核APP售价 list2
    * 3.连锁票面毛利率为负数 list3
    * 4.票面毛利率为负数 list4
    */
    var cackObj = {
        list1: [],
        list2: [],
        list3: [],
        list4: [],
        list5: []
    };
    rowData.forEach(function (item, key) {
        // if ((parseFloat(item.zhiluPrice) - parseFloat(item.lastIncludingTaxPurchasePrice)) < 0 && type != 'audit') {
        //     cackObj.list1.push({orgName:item.orgName,productCode: item.productCode, productName: item.productName});
        // }
        //审核校验智鹿采购总部采购价负毛利
        /*        if ((parseFloat(item.auditZhiluPrice) - parseFloat(item.lastIncludingTaxPurchasePrice)) < 0 && type === 'audit') {
                    cackObj.list1.push({productCode:item.productCode,productName:item.productName});
                }*/
        if (item.inVigue === 0 && parseFloat(item.chainGuidePrice) > parseFloat(item.appPrice)) {
            cackObj.list2.push({orgName:item.orgName,productCode: item.productCode, productName: item.productName});
        }
        if (item.inVigue === 0 && parseFloat(item.auditChainGuidePrice) > parseFloat(item.auditAppPrice) && type === 'audit') {
            cackObj.list2.push({orgName:item.orgName,productCode: item.productCode, productName: item.productName});
        }
        if (parseFloat(item.chainParGrossMargin) / 100 < 0) {
            cackObj.list3.push({orgName:item.orgName,productCode: item.productCode, productName: item.productName});
        }
        if (parseFloat(item.parGrossMargin) / 100 < 0) {
            cackObj.list4.push({orgName:item.orgName,productCode: item.productCode, productName: item.productName});
        }
        // if ((parseFloat(item.heyePrice) - parseFloat(item.lastIncludingTaxPurchasePrice)) < 0 && type != 'audit') {
        //     cackObj.list5.push({productCode:item.productCode,productName:item.productName});
        // }
    });
    var msg = '';
    // if (cackObj.list1.length) {
    //     msg += "以下商品申请/审核智鹿总部采购价<最后含税进价，请重新录入价格或删除商品！<br/>" +
    //         cackObj.list1.map(function (item) {
    //             return item.orgName+','+item.productCode + ',' + item.productName + '<br/>'
    //         }).join()
    // }
    if (cackObj.list2.length) {
        msg += "以下商品申请/审核连锁APP售价＞申请/审核APP售价，请重新录入价格或删除商品！<br/>" +
            cackObj.list2.map(function (item) {
                return item.orgName+','+item.productCode + ',' + item.productName + '<br/>'
            }).join()
    }
    if (cackObj.list3.length) {
        msg += "以下商品连锁票面毛利率为负数，请重新录入价格或删除商品！<br/>" +
            cackObj.list3.map(function (item) {
                return item.orgName+','+item.productCode + ',' + item.productName + '<br/>'
            }).join()
    }
    if (cackObj.list4.length) {
        msg += "以下商品票面毛利率为负数，请重新录入价格或删除商品！<br/>" +
            cackObj.list4.map(function (item) {
                return item.orgName+','+item.productCode + ',' + item.productName + '<br/>'
            }).join()
    }
    // if(cackObj.list5.length){
    //     msg += "以下商品申请/审核荷叶大药房采购价<最后含税进价，请重新录入价格或删除商品！<br/>" +
    //         cackObj.list5.map(function (item) { return item.productCode + ',' + item.productName + '<br/>'}).join()
    // }
    if (msg) {
        var d = utils.dialog({
            title: "提示",
            content: msg
                + "<textarea id=\"copyInput\" class=\"btn btn-info\" style=\"float: right;margin-top: -966px;\"  >" + msg.replace(/(<br\/>)/g, '\n') + "</textarea>",
            okValue: '复制信息',
            ok: function () {
                $("#copyInput").select(); // 选择对象
                var flag = document.execCommand("Copy", "false", null); // 执行浏览器复制命令
                if (flag) utils.dialog({content: '复制成功！', quickClose: true, timeout: 2000}).show();
                d.close();
            },
            cancelValue: '返回',
            cancel: function () {
            }
        }).showModal();
        return false;
    } else {
        return true;
    }
}

//数组去重
function unique(arr) {
    var res = [arr[0]];
    for (var i = 1; i < arr.length; i++) {
        var repeat = false;
        for (var j = 0; j < res.length; j++) {
            if (arr[i] === res[j]) {
                repeat = true;
                break;
            }
        }
        if (!repeat) {
            res.push(arr[i]);
        }
    }
    return res;
}

// 设置可编辑单元格
function resetDisabled() {
    $(".applyUpperShelf").attr("disabled", false);
}

Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}


//
function change_relevance(_this, baseInp, inp) {
    $(_this).parents('tr').find('[name=' + inp + ']').val($(_this).parents('tr').find('[name=' + baseInp + ']').val());
}

/**
 * 当 Table 中任意条目的 商品一级定位 字段发生变化时，调用此函数
 * @param _this 当前变更的商品一级定位的 DOM 元素
 */
function onCommodityPositionChanged(_this) {
    // 修改审核字段
    change_relevance(_this, 'commodityPosition', 'auditCommodityPosition')
    // 加载二级定位列表
    $.ajax({
        type: "get",
        url: "/proxy-product/product/dict/common/queryByParentId",
        async: false,
        data: {"parentId": $(_this).val()},
        dataType: "json",
        success: function (data) {
            let options = ['<option value="" selected="selected">请选择</option>']
            if (data.result) {
                options = options.concat(data.result.map(item => {
                    return "<option value=" + item.id + ">" + item.name + "</option>"
                }))
            }
            // 为 <select> 元素设置最新的 <option> 列表
            $(_this).parents('tr').find('[name=secondCommodityPosition]').html(options.join(''))
            // 为当前行的 auditSecondCommodityPosition 列设置 <option> 列表
            $(_this).parents('tr').find("[name=auditSecondCommodityPosition]").html(options.join(""))
            // 获取 secondCommodityPosition 隐藏字段中的值
            let oldSecondCommodityPositionValue = $(_this).parents('tr').find("[row-describedby='secondCommodityPosition']").text()
            if (!data.result.filter(item => item.id == oldSecondCommodityPositionValue).length) {
                // 如果此前相中的值在新的 option 列表中不存在，则将其置空
                oldSecondCommodityPositionValue = ''
            }
            // 为当前行的 secondCommodityPosition 列设置隐藏字段中的值
            $(_this).parents('tr').find('[name=secondCommodityPosition]').val(oldSecondCommodityPositionValue)
            // 为当前行的 auditSecondCommodityPosition 列设置隐藏字段中的值
            $(_this).parents('tr').find('[name=auditSecondCommodityPosition]').val(oldSecondCommodityPositionValue)
            // 为当前行的 auditPurchaseContractMode 列设置申请集采签约方式的值
            $(_this).parents('tr').find('[name=auditPurchaseContractMode]').val($(_this).parents('tr').find("[name='purchaseContractMode']").val())
            // 修改二级定位、签约方式是否必填
            const isSecondPositionRequired = $(_this).find('option:selected').attr('requiredFlag')
            const isContractRequired = $(_this).find('option:selected').attr('modeRequiredFlag')
            setRequired($(_this).parents('tr').find("[name=secondCommodityPosition]"), isSecondPositionRequired)
            setRequired($(_this).parents('tr').find("[name=purchaseContractMode]"), isContractRequired)
            refreshFgColor($(_this).parents('tr').find("[name=secondCommodityPosition]"))
            refreshFgColor($(_this).parents('tr').find("[name=purchaseContractMode]"))
            resetPurchaseContractMode($(_this).parents('tr').find("[name=purchaseContractMode]"), false, isContractRequired)
            togglePurchaseContractModeOptionsDisabled($(_this).parents('tr').find("[name=purchaseContractMode]"), isContractRequired)
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

/**
 * 当 Table 中任意条目的 商品定位二级 字段发生变更时，调用此函数
 */
function onSecondCommodityPositionChanged(_this, isAudit) {
    if (!isAudit) {
        // 为 secondCommodityPosition 隐藏字段设置最所的值
        $(_this).parents('tr').find('[row-describedby=secondCommodityPosition]').text($(_this).parents('tr').find("[name=secondCommodityPosition]").val())
        // 为 auditSecondCommodityPosition 设置最新的选中值
        $(_this).parents('tr').find('[name=auditSecondCommodityPosition]').val($(_this).parents('tr').find("[name=secondCommodityPosition]").val())
    }
    // 刷新字段颜色
    refreshFgColor(_this)
}

/**
 * 若所选商品定位一级非 集采签约 ，则将重置集采签约方式字段，将其置为 "请选择"
 * @param _this 集采签约方式 DOM
 * @param isAudit 是否属于审核字段
 */
function resetPurchaseContractMode(_this, isAudit, isContractRequired) {
    if (!isContractRequired) {
        $(_this).val("")
        onPurchaseContractModeChanged(_this, isAudit)
    }
}

/**
 * 开关集采签约方式下拉列表的 可用状态
 * @param _this 集采签约方式 DOM
 * @param isContractRequired 集采签约方式是否必填
 */
function togglePurchaseContractModeOptionsDisabled(_this, isContractRequired) {
    if (isContractRequired) {
        $(_this).children().removeAttr("disabled")
    } else {
        $(_this).children().attr("disabled", "disabled")
    }
}

function onPurchaseContractModeChanged(_this, isAudit) {
    if (!isAudit) {
        // 为 auditSecondCommodityPosition 设置最新的选中值
        $(_this).parents('tr').find('[name=auditPurchaseContractMode]').val($(_this).val())
    }
    // 刷新字段颜色
    refreshFgColor(_this)
}

/**
 * 设置 DOM 元素的 required 属性
 * @param _this 将要操作的 DOM 元素
 * @param isRequired
 */
function setRequired(_this, isRequired) {
    if (_this && isRequired) {
        _this.attr('required', true)
        _this.css('color', 'red')
    } else {
        _this.removeAttr('required')
        _this.css('color', '')
    }
}

/**
 * 根据 DOM 元素自身的 required 属性，刷新其颜色
 *      若 DOM 元素含有 required 属性且其 val() 为空，将其设置为红色
 *      否则，将其恢复至普通颜色
 * @param _this 将要操作的 DOM 元素
 */
function refreshFgColor(_this) {
    if ($(_this).attr('required') && !$(_this).val()) {
        $(_this).css('color', 'red')
    } else {
        $(_this).css('color', '')
    }
}

//设置标准价
function toGrossMargin(obj, baseInp) {
    var trobj = $(obj).parents('tr');
    var appPrice = trobj.find('[name=appPrice]').val();//APP价
    var auditAppPrice = trobj.find('[name=auditAppPrice]').val();//审核APP价
    if (!baseInp) {
        appPrice = auditAppPrice;
    }
    //连锁票面毛利率
    var auditChainGuidePrice = trobj.find('[name=auditChainGuidePrice]').val();//价格管理部审核连锁APP售价
    var floorPrice = trobj.find('[row-describedby=lastIncludingTaxPurchasePrice]').text();//底价
    if (auditChainGuidePrice && floorPrice) {
        var value1 = auditChainGuidePrice == 0 ? 0 : (auditChainGuidePrice - floorPrice) / auditChainGuidePrice * 100;
        value1 = utils.toDecimal2(value1);
        trobj.find('[row-describedby=chainParGrossMargin]').text(value1 + "%");
    } else {
        trobj.find('[row-describedby=chainParGrossMargin]').text("");
    }
    //票面毛利率
    var lastIncludingTaxPurchasePrice = trobj.find('[row-describedby=lastIncludingTaxPurchasePrice]').text();//最后进货价
    if (appPrice != null && appPrice > 0 && lastIncludingTaxPurchasePrice != null) {
        var value = 100 - lastIncludingTaxPurchasePrice * 100 / appPrice;
        value = utils.toDecimal2(value);
        trobj.find('[row-describedby=parGrossMargin]').text(value + "%");
    } else {
        trobj.find('[row-describedby=parGrossMargin]').text("");
    }
    //终端毛利率
    var terminalPrice = trobj.find('[name=terminalPrice]').val();//建议终端价
    var auditTerminalPrice = trobj.find('[name=auditTerminalPrice]').val();//审核建议终端价
    if (!baseInp) {
        terminalPrice = auditTerminalPrice;
    }
    if (terminalPrice != null && terminalPrice > 0 && appPrice != null) {
        var value = 100 - appPrice * 100 / terminalPrice;
        value = utils.toDecimal2(value);
        trobj.find('[row-describedby=terminalGrossMargin]').text(value + "%");
    } else {
        trobj.find('[row-describedby=terminalGrossMargin]').text("");
    }
};

/* 对比 */
$("#X_Table").on('click', '.comparison', function (e) {
    var rowId = $(e.target).parents('tr').attr('id');
    var rowData = $("#X_Table").getRowData(rowId);
    $.ajax({
        type: "get",
        url: "/proxy-product/product/upperShelf/priceCompare",
        data: {
            productCode: rowData.productCode
        },
        beforeSend: function () {
            //parent.showLoading();
        },
        success: function (res) {
            var listData = res.result.list;
            listData.sort(function (a, b) {
                return a.appPrice - b.appPrice
            });
            let _html = `
                <div id="comparisonDialog_div">
                    <div class="dialog_con">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">当前公司：${rowData.orgName}</div>
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">当前商品：${rowData.productCode}${rowData.productName} ${rowData.specifications}</div>
                        <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6">申请APP售价：${rowData.appPrice}</div>
                        <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6">申请连锁APP售价：${rowData.chainGuidePrice}</div>
                    </div>
                    <div class="panel-body">
                        <table id="comparison_table" />
                    </div>
                </div>
                 `
            var comparison_dialog = utils.dialog({
                title: '价格对比',
                content: _html,
                width: 500,
                height: 400,
                cancelValue: '返回',
                cancel: function () {
                }
            }).showModal();
            $('#comparison_table').XGrid({
                //url:'/proxy-product/product/upperShelf/priceCompare',
                data: listData,
                colNames: ['其他子公司', 'APP售价', '连锁APP售价'],
                colModel: [
                    {
                        name: 'orgName',
                        index: 'orgName',
                        width: 210
                    },
                    {
                        name: 'appPrice',
                        index: 'appPrice',
                        width: 120
                    },
                    {
                        name: 'chainGuidePrice',
                        index: 'chainGuidePrice',
                        width: 120
                    }
                ],
                key: 'channelCode',
                rowNum: 20,
                altRows: true, //设置为交替行表格,默认为false
                rownumber: true
            });
        },
        complete: function () {
            //parent.hideLoading();
        }
    });
});
$('body').on('input', '#X_Table input.TwoDecimalHndle', function () {
    utils.TwoDecimalHndle(this, 12)
})
$('body').on('blur', '#X_Table input.TwoDecimalHndle', function () {
    $(this).val(utils.toDecimal2($(this).val()))
    if ($(this).attr('data-inp')) {
        $(this).parents('tr').find('[name=' + $(this).attr("data-inp") + ']').val($(this).parents('tr').find('[name=' + $(this).attr("name") + ']').val());
    }
    toGrossMargin(this, $(this).attr('name'));
});

$("#X_Table").on('click','.file_a',function (e) {
    var url = $(e.target).attr('data-url');
    var content=`<div class="changeApplyItem" style="line-height: 50px;text-align: center;">
                    <a style="flex: 1;" href="${url}" target="_blank" class="download">查看下载</a>
                </div>`;
    utils.dialog({
        align: 'top',
        width: 130,
        height: 50,
        padding: 2,
        content:content,
        quickClose: true
    }).show(this);
});
