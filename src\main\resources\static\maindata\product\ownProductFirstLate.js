function confirmDel() {
    var orgCode = $("#orgCode").val();
    var productCode = $("#productCode").val();
    var operateType = 1;
    $.ajax({
        type: "post",
        url: "/proxy-product/product/tools/processOwnProductFirstLate?orgCode="+orgCode+"&productCode="+productCode+"&operateType="+operateType+"",
        async: false,
        // data: {
        //     "orgCode":$("#orgCode").val(),
        //     "productCode":$("#productCode").val(),
        //     "operateType":1
        // },
        success: function (data) {
            utils.dialog({ content: data.result, quickClose: true, timeout: 2000 }).showModal();
        },
        error: function () {
            utils.dialog({ content: '操作失败', quickClose: true, timeout: 2000 }).showModal();
        },
        complete: function () {
            parent.hideLoading();
        }
    });
}

function setRelation() {
    var orgCode = $("#orgCode").val();
    var productCode = $("#productCode").val();
    var operateType = 2;
    $.ajax({
        type: "post",
        url: "/proxy-product/product/tools/processOwnProductFirstLate?orgCode="+orgCode+"&productCode="+productCode+"&operateType="+operateType+"",
        async: false,
        // data: {
        //     "orgCode":$("#orgCode").val(),
        //     "productCode":$("#productCode").val(),
        //     "operateType":2
        // },
        success: function (data) {
            utils.dialog({ content: data.result, quickClose: true, timeout: 2000 }).showModal();
        },
        error: function () {
            utils.dialog({ content: '操作失败', quickClose: true, timeout: 2000 }).showModal();
        },
        complete: function () {
            parent.hideLoading();
        }
    });
}