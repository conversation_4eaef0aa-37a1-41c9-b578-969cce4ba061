var selArr = [];
$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //显示流程图
    // var processInstaId = $("#processId").val();
    // if (!processInstaId || processInstaId == "") {
    //
    // }
    var processInstaId = $("#processInstanceId").val();
    initApprovalFlowChart(processInstaId);


    var id = Number($("#appId").val());

    $('#table1').XGrid({
        url: "/proxy-customer/customer/disuse/toDetailed?id=" + id,
        colNames: ['', '客户编码', '客户名称', '客户类别', '目前状态', '业务类型', '<i style="color:red;margin-right:4px;">*</i>申请原因'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'customerCode',
            editable: ''
        }, {
            name: 'customerName',
        }, {
            name: 'customerType',
        }, {
            name: 'serviceType',
            formatter: function (e) {
                if (e == '0') {
                    return '启用状态';
                } else if (e == '1') {
                    return '停用状态';
                } else {
                    return "";
                }
            },unformat: function (e) {
                if (e == '启用状态') {
                    return '0';
                } else if (e == '停用状态') {
                    return '1';
                } else {
                    return "";
                }
            }
        }, {
            name: 'serviceType',
            formatter: function (e) {
                if (e == '0') {
                    return '停用';
                } else if (e == '1') {
                    return '解除停用';
                } else {
                    return "";
                }
            },unformat: function (e) {
                if (e == '停用') {
                    return '0';
                } else if (e == '解除停用') {
                    return '1';
                } else {
                    return "";
                }
            }
        }, {
            name: 'pursueReason',
            // rowtype: '#appContentT'
        }
            //     {
            //     name: 'auditOpinion',
            //     rowtype: '#appContentT'
            // }
            , {
                name: 'customerType',
                hidden: true
            }, {
                name: 'orgCode',
                index: 'orgCode',
                hidden: true
            }, {
                name: 'customerOrganId',
                hidden: true
            }, {
                name: 'keyId',
                hidden: true
            }, {
                name: 'baseId',
                hidden: true
            }],
        key: 'baseId',
        rowNum: 9999,
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件', id,dom,obj,index,event);
        },
        onSelectRow: function (id, dom, obj, index, event, obj) {
            ///console.log('单机行事件', id,dom,obj,index,event,obj);
        },

    });


});

function examine(auditStatus, auditOpinion) {
    var id = Number($('#appId').val());
    var taskId = Number($('#taskId').val());
    var customerDisableInfoVo = $("#recordVo").serializeToJSON();
    if (auditStatus == 4) {
        customerDisableInfoVo.passOrNo = "N";
    } else {
        customerDisableInfoVo.passOrNo = "Y";
    }
    // //限制字符长度 ，数据库为255，
    // console.log( "审核意见长度： " + auditOpinion.length)
    // if(auditOpinion.length > 100){
    //     utils.dialog({content: '审核意见请少于100个字符！', quickClose: true, timeout: 2000}).showModal();
    //     return;
    // }

    customerDisableInfoVo.auditOpinion = auditOpinion;
    customerDisableInfoVo.taskId = Number($("#taskId").val());
    customerDisableInfoVo.id = Number($('#appId').val());

    $.ajax({
        url: "/proxy-customer/customer/disuse/audit",
        data: JSON.stringify(customerDisableInfoVo),
        type: "post",
        dataType: 'json',
        contentType: "application/json",
        success: function (data) {
            if (data.code == 0) {
                utils.dialog({
                    title: "提示",
                    content: data.result,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            } else {//审核不通过
                utils.dialog({
                    title: "错误提示",
                    content: data.msg,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        },
        error: function () {
            utils.dialog({content: '内部出错！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

/**
 * 流程图显示
 */
function initApprovalFlowChart(processInstaId) {
    var key = $("#key").val();
    const url = "/proxy-customer/customer/disuse/queryTotle?processInstaId=" + processInstaId + "&key=" +key;
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code == 0 && data.result != null) {
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {
        }
    });
}


$("#submitAssert").click(function () {
    // if(!$(this).hasClass('disablebtn')) {
    //     $(this).addClass('disablebtn')
        utils.dialog({
            title: '审批留言',
            content: $('#verify_modal'),
            width: 500,
            cancelValue: '取消',
            okValue: '确定',
            ok: function () {
                examine(2, $("#ly").val())
            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).showModal();
    // }


});
//重新提交
$("#reSubmitAssert").click(function () {
    examine(2, $("#auditOpinion").val())
});


$("#submitUnAssert").click(function () {

    // if(!$(this).hasClass('disablebtn')) {
    //     $(this).addClass('disablebtn')
        utils.dialog({
            title: '审批驳回留言',
            content: $('#reject_modal'),
            width: 500,
            cancelValue: '取消',
            okValue: '确定',
            ok: function () {
                if ($("#lyp").val() == "") {
                    utils.dialog({content: '审核不通过，审核意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                examine(4, $("#lyp").val())

            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).showModal();

    // }

});


$("#close").click(function () {
        var taskId = $("#taskId").val()
        close(taskId);
    }
)

/**
 * 关闭审核流
 */
function close(taskId) {
    //获取审核流程数据
    var dataid = $('#appId').val();
    var title = '客户锁定审核';

    //关闭审核
    utils.dialog({
        title: " 关闭审批流",
        width: 300,
        height: 30,
        content: "确定关闭此申请？",
        okValue: '确定',
        ok: function () {
            $.ajax({
                type: "POST",
                url: "/proxy-customer/customer/disuse/closeProcess?taskId=" + taskId + "&id=" + dataid,
                async: false,
                success: function (data) {
                    if (data.staus != 0) {
                        utils.dialog({
                            title: "提示",
                            content: data.message,
                            width: 300,
                            height: 30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                        $(".ui-dialog-close").hide();
                        return false;
                    } else {
                        utils.dialog({
                            // title: "提示",
                            content: data.message,
                            quickClose: true
                            /*width:300,
                            height:30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }*/
                        }).showModal();
                        $(".ui-dialog-close").hide();
                        return false;
                    }
                },
                error: function () {
                    utils.dialog({content: '内部出错！', quickClose: true, timeout: 2000}).showModal();
                }
            });
        },
        cancelValue: '取消',
        cancel: function () {
        }
    }).showModal();
}


/*
/!* 弹框 *!/
function showDialog(id){
    dialog({
        url: '/proxy-supplier/supplier/supplierDisableApprovalRecord/toSearchList',//弹框页面请求地址
        title: '搜索供应商',
        width: 1000,
        height: 800,
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                var table = $("#table").getRowData();
                for(var i = 0 ; i < table.length; i++ ){
                    if(data.keyId ==table[i].keyId ){
                        utils.dialog({content: '请不要重复选择供应商！', quickClose: true, timeout: 2000}).showModal();
                        return;
                    }
                }
                $('#table').XGrid("setRowData",id,data);
            }
        }
    }).showModal();
}*/

//数组中查找id
function fidInArr(id) {
    for (var i = 0; i < selArr.length; i++) {
        if (selArr[i].id == id) {
            return true;
        }
    }
    return false;
}
