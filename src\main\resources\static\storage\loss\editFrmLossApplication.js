
// 返回按钮
function btn_cancel(){
    utils.dialog({
        title: '温馨提示',
        content: '是否确认取消申请?',
        okValue: '确定',
        ok: function () {
            // 页面上需要tackId值
            $.ajax({
                type: "POST",
                url: "/proxy-storage/storage/loss/requestLossClose?taskId="+$("#taskId").val()+"&lossOrderNo="+$("#lossRequestNo").val(),
                async: false,
                success: function (data) {
                    console.log(data);
                    utils.dialog({
                        title: '温馨提示',
                        content: data.msg,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab()
                        }
                    }).showModal()
                },
                error: function () {
                    utils.dialog({
                        title: '温馨提示',
                        content: data.msg,
                        okValue: '确定',
                        ok: function () {}
                    }).showModal()
                }
            });
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal()
    //utils.closeTab();
}
// 删除行
$('#detelRow').on('click',function(){
    var selectRow = $('#X_Table').XGrid('getSeleRow');

    if (selectRow.length) {
        utils.dialog({
            title: '温馨提示',
            content: '是否确认删除选中商品行？',
            okValue: '确定',
            ok: function () {
                selectRow.forEach(function (item) {
                    $('#X_Table').XGrid('delRowData',item.id);
                })
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal()

    } else {
        utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
    }
});

$("#addRowData").on("click", function () {
    var tableData  = $('#X_Table').XGrid('getRowData');
    utils.dialog({
        url: '/proxy-storage/storage/loss/toUnQualifiedList',
        title: '不合格库商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        /*data: {
            resultArr: resultArr
        },*/ // 给modal 要传递的 的数据
        onclose: function (data) {
            var data = this.returnValue;
            console.log(data)
            if (data) {
                var seleData = data.seleData;
                seleData.forEach(function (item) {
                    tableData.some(function (val) {
                        return item.productCode === val.productCode && item.batchNum === val.batchNum
                    }) || $('#X_Table').XGrid('addRowData', item);
                });
                /*var rows = data.resultArr;
                for (var i = 0; i < rows.length; i++) {
                    var id = rows[i].id;
                    if (!findInArr(id)) {
                        rows[i].productCode = rows[i].id;
                        $('#X_Table').XGrid('addRowData', rows[i]);
                    }
                }*/
            }
        }
    }).showModal();
});

tablea(["-1"]);
function tablea(idsArr){
    $('#X_Table').XGrid({
        url:"/proxy-storage/storage/loss/getLossRequestOrderDetailData",
        postData:{lossOrderNo:$("#lossRequestNo").val()},
        colNames: [ '商品编码','原商品编码','商品大类','商品名称','商品规格', '产地' ,'生产厂家', '单位', '库房名称','业务类型', '批号', '生产日期', '有效期至',
            '不合格可用库存','报损数量',  '不含税成本单价', '不含税成本金额','移库原因'],
        colModel: [
            {name: 'productCode',index: 'productCode'},
            {name: 'oldProductCode',index: 'oldProductCode'},
            {name: 'drugClass',index: 'drugClass'},
            {name: 'productName',index: 'productName'},
            {name: 'specifications',index: 'specifications'},
            {name: 'producingArea',index: 'producingArea'},
            {name: 'manufacturerName',index: 'manufacturerName'},
            {name: 'unit',index: 'unit'},
            {name: 'storageName',index: 'storageName',formatter:function(){
                   return "不合格库";
            }
             },
            {name: 'channelId',index: 'channelId'},
            {name: 'batchNum',index: 'batchNum'},
            {name: 'channelId',index: 'channelId', hidden:true},
            {name: 'productDate',index: 'productDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            },
            {name: 'validateDate',index: 'validateDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            } ,
            {name: 'amount',index: 'amount'},
            {name: 'lossNum',index: 'lossNum', rowtype: '#inp_lossNum'},
            {name: 'noTaxPrice',index: 'noTaxPrice',
                formatter: function (e) {
                    if (e) {
                        return parseFloat(e).toFixed(2)
                    }else {
                        return 0;
                    }
                }
            },
            {name: 'noTaxCostNum',index: 'noTaxCostNum'},
            {name: 'wmsTransferReason',index: 'wmsTransferReason'}
        ],
        altRows: true, //设置为交替行表格,默认为false
        rowNum: 500,
        key:"productCode",
        rowList:[20,50,100,500],
        selectandorder: true,//是否展示序号，多选
        //attachRow:true,
        ondblClickRow: function (id, dom, obj, index, event) {},
        onSelectRow: function (id, dom, obj, index, event) {},
        gridComplete: function () {
            let total = 0;
            var data = $(this).XGrid('getRowData');
            data.forEach(function (item, index) {
                if (item.noTaxCostNum) {
                    total += Number(item.noTaxCostNum)
                    //$('#X_Table #' + item['id']).addClass('warnning')
                }
            });
            $('#total_noTaxCostNum').text(total)
        },
        //pager: '#grid_page'
    });
};


function timeStamp2String(time){
    var datetime = new Date();
    datetime.setTime(time);
    var year = datetime.getFullYear();
    var month = datetime.getMonth() + 1;
    var date = datetime.getDate();
    date = date.toString().length <= 1 ? '0' + date : date;
    month = month.toString().length <= 1 ? '0' + month : month;
    return year + "-" + month + "-" + date;
};

//保存
function btn_save() {
    var rowdata = $('#X_Table').getRowData();
    if(rowdata.length == 0){
        utils.dialog({
            title:'温馨提示',
            content:'请选择数据！',
            okValue:'确定',
            ok:function () {}
        }).showModal();
        return false;
    };
    let _ind = 0;
    let bool = rowdata.some( (item,index) => {
        _ind = index;
        return item.lossNum == ''
    })
    if(bool){
        utils.dialog({
            title: '提示',
            content: ( _ind + 1) + '行 '+$('#X_Table tr').eq(_ind+1).find('td[row-describedby="productName"]').text()+' 的报损数量不能为空.',
            okValue: '确定',
            ok: function () {}
        }).showModal();
        return false;
    }
    utils.dialog({
        title: '提示',
        content: '是否确认提交审核到下一岗进行审核？',
        okValue: '确定',
        ok: function () {
            //下面保存
            $.ajax({
                url: "/proxy-storage/storage/loss/editOrSaveLossRequestListData",
                type:'post',
                dataType:'json',
                //contentType : 'application/json;charset=UTF-8',
                data: {
                    lossRequestNo:$("#lossRequestNo").val(),
                    lossDate:$("#requestDate").val(),
                    unStorageProductJson:JSON.stringify(rowdata),
                    taskId:$("#taskId").val(),
                    processInstanceId:$("#processId").val()
                },
                success: function(data){
                    if(data.code == 0){
                        utils.dialog({content: '提交成功', quickClose: true, timeout: 2000}).show();
                        setTimeout(function () {
                            utils.closeTab();
                            //utils.openTabs('unqualifiedLoss-unqualifiedLossController-jumpunqualifiedLossList-WMS','不合格品报损申请单',"/proxy-storage/storage/loss/toOrderRequestList");
                        },2000)
                    }else{
                        utils.dialog({content: data.msg, quickClose: true, timeout: 2000}).show();
                    }
                }
            });
        }

    }).showModal()
}

//列表内查询id
function findInArr(id) {
    var arr = $('#X_Table').getRowData();
    for (var i = 0; i < arr.length; i++) {
        var ID = arr[i].productCode;
        if (ID == id) {
            return true;
        }
    }
    return false;
}

// 报损数量
function check_lossNum(el) {
    console.log('lossnum')
    let amountVal = Number($(el).parents('td').prev().text());
    let noTaxPriceVal = Number($(el).parents('td').next().text());
    if(Number($(el).val()) > amountVal){
        utils.dialog({
            title: '提示',
            content: '报损数量不能超过不合格库存可用数量！',
            okValue: '确定',
            ok: function () {
                $(el).val(amountVal);
                getNoTaxCostNum(el,amountVal,noTaxPriceVal);
            }
        }).showModal()
    }
    getNoTaxCostNum(el,Number($(el).val()),noTaxPriceVal);
}
// 不含税 成本金额计算
function getNoTaxCostNum(el,num,price) {
    let total = price * num;
    $(el).parents('tr').find('td[row-describedby="noTaxCostNum"]').text(total.toFixed(2));
    span_total();
}
//  b报损数量 输入框  键盘按下 计算总数
function count_lossNum(el) {
    let noTaxPriceVal = Number($(el).parents('td').next().text());
    let total = noTaxPriceVal * (Number($(el).val()));
    $(el).parents('tr').find('td[row-describedby="noTaxCostNum"]').text(total.toFixed(2));
    span_total();
}
function span_total() {
    var _a = 0, _b = 0, _c = 0;
    var rowdata = $('#X_Table').getRowData();
    rowdata.forEach(function (item,index) {
        _a += Number(item.noTaxCostNum?item.noTaxCostNum:0);
    });
    $('.span_total').text(_a.toFixed(2));
}