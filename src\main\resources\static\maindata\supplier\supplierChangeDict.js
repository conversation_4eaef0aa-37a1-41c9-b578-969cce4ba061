$(function () {
	/**
	 * @type { {data:number, value:string, list:any}[] }
	 */
	let buyerSelectList = [];
	const supplierSelect = function (result) {
		$("#supplierTypeId").val(result.data);
		baseDataBuseScopeOrScopes();
	}
	const buyerSelect = function(result) {
		//如果当前选择的采购员在已选择采购员列表中则过滤掉
		if (buyerSelectList.some(item => item.data === result.data)) {
			buyerSelectList = buyerSelectList.filter(item => {
				return item.data != result.data
			})
		} else {
			//否则则添加
			buyerSelectList.push(result)
		}
		$("#buyerId").val(buyerSelectList.map(item => item.data).join(","));
		$("#buyerIdVal").val(buyerSelectList.map(item => item.value).join(","));
		/* buyerSelectList = buyerSelectList.filter(item => {
			return item.data != result.data
		}) */
		
	}
	const currencySelect = function (result) {
		$("#currencyId").val(result.data);
	}
	var orgCode = $("#orgCode").val();
	//供应商采购员
	valAutocomplete("/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode",{paramName:'userName', params:{"orgCode":orgCode}},"buyerId",{data:"id",value:"userName"}, true, buyerSelect, () => {
		buyerSelectList = [];
	});
	//供应商类别
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":8}},"supplierTypeId",{data:"id",value:"name",numCode:"numCode"},false, supplierSelect);
    //币种
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryCurrency",{paramName:'currencyName'},"currencyId",{data:"currencyId",value:"currencyName"}, false, currencySelect);
    //加载字典值
    //showDictValue();
});
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam, multi, select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
		multi: multi,   //多选
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: select,
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            $("#"+obj).val("");
            $("#"+obj+"Val").val("");
            noneSelect && noneSelect();
        }
    });
}
/**
 * 字典值回显
 */
/*function  showDictValue() {
    //供应商类别
    showComValue("supplierTypeId","1020");
    //币种
    showComValue("currencyId","1008");
}*/
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showComValue(obj,type) {
    var id =$("#"+obj).val();
    if(id!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querybeanbytype?type='+type+"&id="+id,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                console.log(data);
                if(data.code == 0)
                {
                    $("#"+obj+"Val").val(data.result);
                    $("#"+obj+"Val").attr("data-value",data.result);
                }
            }
            
        })
    }
}
// 采购员回显
function loadUserName(key,orgaKey) {
    var userId=$("#"+key).val();
    if(userId==""){
        return;
    }
    $.ajax({
        type:"get",
        url: "/proxy-sysmanage/sysmanage/system/queryUserInfoById",
        data:{"userId":userId},
        dataType:"json",
        success: function (data) {
            console.log(data.result);
            if(data.code==0&&data.result!=null){
                var userName=data.result.userName;
                if(orgaKey!=null){//申请人机构显示
                    $("#"+orgaKey+"Val").val(data.result.orgName);
                }else {
                    userName=userName+"-"+data.result.dptName;
                }
                $("#"+key+"Val").val(userName);
                $("#"+key+"Val").attr("data-value",userName);
            }
        },
        error:function () {
        }
    });
}

// 当供应商类别为生产(55)时，经营范围 隐藏，展示 生产/经营范围
function baseDataBuseScopeOrScopes() {
    var supplierTypeId = $("#supplierTypeId").val();
    if(supplierTypeId == 55){
        $("#baseDataBuseScope").parents('.row').hide().find('#scopes').removeClass('{validate:{ required :true}}');
        $("#scopes").parents('.row').show().find('#scopes').addClass('{validate:{ required :true}}');
    }else {
        $("#baseDataBuseScope").parents('.row').show().find('#scopes').addClass('{validate:{ required :true}}');
        $("#scopes").val('').parents('.row').hide().find('#scopes').removeClass('{validate:{ required :true}}');
    }
}
baseDataBuseScopeOrScopes();