var taskId = null;
$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);
    taskId = utils.getQueryString('taskId');
    if(param.type == 1){
        let dom = `<button type="button" class="btn btn-info" onclick="auditPassCilck()">
                        通过
                    </button>
                    <button type="button" class="btn btn-info" style="background-color:#FB9B1F;border-color: #FB9B1F;" onclick="auditRejectCilck()">
                        驳回
                    </button>`
        $('#newButton').html(dom);
    }

//   /!* 审批流 *!/
//     /!**
//      * 流程图显示
//      *!/
        //根据流程实例ID加载流程图
    var processInstaId=$("#auditId").val();
    var key=$("#processId").val();
    initApprovalFlowChart(key,processInstaId);
    function  initApprovalFlowChart(key,processInstaId) {
        //获取审核流程数据
        $.ajax({
            type: "get",
            url: "/proxy-order/order/salesOrder/queryTotle?processInstaId="+processInstaId,
            async: false,
            success: function (data) {
                if (data.code==0&&data.result!=null){
                    console.log(data.result);
                    $('.flow').html("")
                    $('.flow').process(data.result);
                }
            },
            error: function () {}
        });
    }

    //退回原因展示
    setAreaSendBack();
    function setAreaSendBack(){
        let reason = $('#hiddenInputSendBackReason').val();
        let remark = $('#hiddenInputSendBackRemark').val();
        if( reason && remark){
            $('#ColReturnBackReason').css('display','block');
            let inputVal = `${ reason }（${ remark }）`;
            $('#inputSendBack').val(inputVal).prop('title',inputVal);
        }else{
            $('#ColReturnBackReason').css('display','none');
        }
    }

    /* 合计 计算 */
    var totalTable = z_utils.totalTable;

    var colName = ['退货原因', '商品编码','原商品编码','商品大类', '商品名称','通用名', '商品规格', '生产厂家', '产地', '单位', '批号', '生产日期', '有效期至', '本次退货数量', '实际退货数量',
        '含税单价', '价税合计', '实付金额', '活动优惠金额', '余额抵扣优惠金额','退回返利金额', '税率', '税额','业务类型','id'
    ];
    var colModel = [{ name: 'returnReason',index: 'returnReason',formatter:function (e) {

        if(e==1){
            return '整单拒收';
        }else if(e==2){
            return '药品漏发';
        }else if(e==3){
            return '药品错发';
        }else if(e==4){
            return '药品破损';
        }else if(e==5){
            return '效期不好';
        }else if(e==6){
            return '批号不符';
        } else if(e==7){
            return '税票有误';
        }else if(e==8){
            return '无检验报告单';
        }else if(e==9){
            return '采购价偏高';
        } else if(e==10){
            return '实物与图片不符';
        }else if(e==11){
            return '采购单重复';
        }else if(e==12){
            return '商品其他质量问题';
        }else if(e==13){
            return '拦截';
        }else if(e==14){
            return '缺货未发';
        }else if(e==15){
            return '召回';
        }else if(e==16){
            return '水剂不发货';
        }else {
            return e;
        }
    } },
        { name: 'productCode', index: 'productCode' },
        {name: 'oldProductCode', index: 'oldProductCode'},
        { name: 'drugClass', index: 'drugClass' },
        { name: 'productName', index: 'productName'},
        { name: 'commonName', index: 'commonName'},
        { name: 'specifications',index: 'specifications'},
        { name: 'manufacturer', index: 'manufacturer'},
        { name: 'productOrigin', index: 'productOrigin'},
        { name: 'productUnit',index: 'productUnit'},
        { name: 'batchCode', index: 'batchCode' },
        { name: 'productionTime', index: 'productionTime',formatter:dateFormatter},
        { name: 'periodValidity',index: 'periodValidity',formatter:dateFormatter},
        { name: 'returnsNumber',index: 'returnsNumber'},
        { name: 'actualReturnNumber',index: 'actualReturnNumber'},
        { name: 'taxPrice',index: 'taxPrice',
            formatter:function(e){
                return Number(e).toFixed(2);
            }
        },
        { name: 'taxAmount',    index: 'taxAmount'  ,
            formatter:function(e){
                return Number(e).toFixed(2);
            }},
        { name: 'paymentAmount',    index: 'paymentAmount'  ,
            formatter:function(e){
                return Number(e).toFixed(2);}},
        { name: 'activityDiscountAmount',    index: 'activityDiscountAmount'  ,
            formatter:function(e){
                return Number(e).toFixed(2);
            }},
        { name: 'balanceDiscountAmount',    index: 'balanceDiscountAmount' ,
            formatter:function(e){
                return Number(e).toFixed(2);
            } },
        {      name: 'amounOfRebate',      index: 'amounOfRebate',
            formatter:function(e){
                return Number(e).toFixed(2);
            }
        },
        { name: 'rate',    index: 'rate',
            formatter: function (e) {
                if ( e != undefined && e != null ) {
                    return e +'%';
                } else {
                    return '0%'
                }
            } },
        { name: 'tax',    index: 'tax' ,
            formatter:function(e){
                return Number(e).toFixed(2);}},
        { name: 'channelId',index: 'channelId'},
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,
            hidden: true,
            hidegrid: true

        },



            ];



    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }






    $('#table_a').XGrid({
        url:'/proxy-order/order/orderReturn/orderReturnController/getDetailList?salesReturnCode=' + $("#salesReturnCode").val(),
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'sort',
        rowNum: 100,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            /* 双击单据，进入单据详情页面。若单据状态为“待处理”，双击单据跳转至编辑销售退回单页面； */
            if (obj.text15 == '待处理') {
                window.location = 'return_receive_edit.html';
            } else {
                window.location = 'return_receive_info.html';
            }
        },
        gridComplete: function () {
            setTimeout(function (param) {
                /* 合计写入 */
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                $(sum_ele[1]).text(totalTable(data, 'tax'));
                $(sum_ele[2]).text(totalTable(data, 'paymentAmount'));
            }, 200)
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });


    /* 筛选列 */
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })

    /* 编辑 */
    //判断是否为已驳回状态
    /*if ($('#documentsState').val() =='1') {
        $('#editRowData').show();
    }*/
    $('#editRowData').on('click', function () {
        window.location = '/proxy-order/order/orderReturn/orderReturnController/toUpdate?salesReturnCod='+$('#salesReturnCode').val();
    });
//
    /* 返回 */
    $('#goback').on('click', function () {
        utils.closeTab();
    });

    /* 导出 */
    $('#exportDetailData').on('click', function () {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            var formData = {};
            formData["colName"] = colName;
            formData["colNameDesc"] = colNameDesc;
            httpPost("/proxy-order/order/orderReturn/orderReturnController/exportReturnDetailList?salesReturnCode="+$("#salesReturnCode").val(), formData);

        });
    });


    /* 取消退单数据 */
    $('#cancelSalesReturn').on('click', function () {
    	 if($("#documentsState").val()!='7'){
             utils.dialog({
                 title: '提示',
                 content: "只有待验销售退回单才能取消 ",
                 width: 400,
                 okValue: '确定',
                 ok: function () {

                 }

             }).showModal();

             return;
         }else {
        $("#salesReturnCode").val();
        utils.dialog({
            title: '温馨提示',
            content: '取消后该单据将无法进行后续的验收、入库操作，是否确认取消？',
            okValue: '确定',
            ok: function () {
                $.ajax({
                    url: "/proxy-order/order/orderBigCusReturn/bigCusOrderReturnController/cancelSalesReturn",
                    type:'post',
                    dataType:'json',
                    data: {salesReturnCode:$("#salesReturnCode").val()},
                    success: function(data){
                        if(data.code == 0){
                            utils.dialog({
                                title: '温馨提示',
                                content: '保存成功',
                                okValue: '确定',
                                ok: function () {
                                     window.location.href="/proxy-order/order/orderBigCusReturn/bigCusOrderReturnController/jumpBigCusOrderReturnList";
                                }

                            }).showModal()
                        }else{
                            utils.dialog({
                                title: '温馨提示',
                                content: data.msg,
                                okValue: '确定',
                                ok: function () {
                                    return;
                                }

                            }).showModal()
                        }
                    }
                });
            }

         }).showModal()
       }
    });




    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
})
/**审批驳回 */
function auditRejectCilck(){
    console.log('审批驳回');
    $("#container label").addClass("redstar");
    utils.dialog({
        title: '审批驳回',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            //审核不通过，意见不能为空
            if ($("#auditOpinion").val()==""){
                utils.dialog({content:'审批意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }else{
                $.ajax({
                    method: "POST",
                    url: "/proxy-order/order/orderReturn/orderReturnController/v2/rejectAudit",
                    data: JSON.stringify({
                        // orderCode:$("#orderCode").text(),
                        commit:$("#auditOpinion").val(),
                        taskId:taskId,
                        approve:'reject',
                        salesReturnCode:$('#salesReturnCode').val(),
                    }),
                    contentType: 'application/json',
                    dataType: 'json',
                    cache: false,
                    error: function () {
                        parent.hideLoading();
                        utils.dialog({content:'请求审批处理失败！', quickClose: true, timeout: 2000}).showModal();
                    },
                    success: function (data) {
                        parent.hideLoading();
                        if (data.code==0){
                            utils.dialog({content: data.msg, quickClose: true, timeout: 6000}).showModal();
                            setTimeout(function(){  utils.closeTab(); }, 2000);
                        }else {
                            utils.dialog({content:data.msg, quickClose: true, timeout: 6000}).showModal();
                        }
                        $("#auditOpinion").val('');
                    }
                });
            }

        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}
/**审批通过 */
function auditPassCilck(){
    console.log('审批通过');
    // $("#container label").addClass("redstar");
    utils.dialog({
        title: '审批通过',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            $.ajax({
                method: "POST",
                url: "/proxy-order/order/orderReturn/orderReturnController/v2/rejectAudit",
                data: JSON.stringify({
                    // orderCode:$("#orderCode").text(),
                    commit:$("#auditOpinion").val(),
                    taskId:taskId,
                    approve:'pass',
                    salesReturnCode:$('#salesReturnCode').val(),
                }),
                contentType: 'application/json',
                dataType: 'json',
                cache: false,
                error: function () {
                    parent.hideLoading();
                    utils.dialog({content:'请求审批处理失败！', quickClose: true, timeout: 2000}).showModal();
                },
                success: function (data) {
                    parent.hideLoading();
                    if (data.code==0){
                        utils.dialog({content: data.msg, quickClose: true, timeout: 6000}).showModal();
                        setTimeout(function(){  utils.closeTab(); }, 2000);
                    }else {
                        utils.dialog({content:data.msg, quickClose: true, timeout: 6000}).showModal();
                    }
                    $("#auditOpinion").val('');
                }
            });
            //审核不通过，意见不能为空
            // if ($("#auditOpinion").val()==""){
            //     utils.dialog({content:'审批意见不能为空！', quickClose: true, timeout: 2000}).showModal();
            //     return false;
            // }else{
            // }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}