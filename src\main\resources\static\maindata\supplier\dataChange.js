
//*********************************变更申请模块 开始 *********************************
/*window.changeApply = {};//变更obj
    window.changeApply_ = {};//变更临时obj
    $('#changeApplyBtn').on('click', function () {
        var $d = $('[changeApplyFlag]');

                if ($d.siblings('[changeApplyBlock]').length <= 0) {
                    $d.after('<div class="checkbox" changeApplyBlock  style="position:absolute;z-index:999;right: -50px;top:0px;"><input changeApplyCheckBox type="checkbox"><input changeApplyBtn type="button" value="保存" style="display: none"><div style="display: none;position: relative;" changeApplyTip>已申请<div class="changeApplyTipClose">×</div></div></div>');
                }

        $d.each(function (i, v) {
            if ($(v).siblings('[changeApplyBlock]').length <= 0) {
                if ($(v).is('a')) {
                    $(v).css('padding-right', '25px');
                    //$(v).after('<i class="yulan"></i><input changeApplyBtn class="changeApplyBtn" type="button" value="保存" style="display: none">');
                    $(v).after('<i class="yulan"></i><i changeApplyBtn class="changeApplyBtn yulanBtn"></i>');
                } else {

                    $(v).after('<i class="yulan yulanInput"></i><i changeApplyBtn class="changeApplyBtn"></i>');

                    $(v).after('<div class="checkbox" changeApplyBlock  style="position:absolute;z-index:999;right: -50px;top:0px;"><input changeApplyCheckBox type="checkbox"><input changeApplyBtn type="button" value="保存" style="display: none"><div style="display: none;position: relative;" changeApplyTip>已申请<div class="changeApplyTipClose">×</div></div></div>');
                    
                }
            }


        })
    });
	
    $('[changeapplyflag]').siblings('input').attr('readonly', 'readonly');
    $('[changeapplyflag]').attr('readonly', 'readonly');

    $('body').on('click', '.yulan', function () {
        展开操作框
        window.changeApply_d = dialog({
            align: 'top',
            width: 130,
            height: 55,
            padding: 6,
            content: $('.changeApplyItem'),
            quickClose: true
        }).show(this);
        window.changeApply_e = this;//储存对应节点
    }).on('click', '.cEdit', function () {
        点击编辑
        var $t = $(window.changeApply_e);//找到对应节点
        var selObj;
        var $t_flag = $t.siblings('[changeapplyflag]');//标记元素
        window.changeApply_d.close();//关闭对应dialog
        $t.hide();//隐藏三个点
        $t.siblings('[changeApplyBtn]').show();//展示保存按钮

        console.log($t_flag.prop('type'));
        switch ($t_flag.prop('type')) {
            case 'radio'://为单选
                console.log('单选');
                $t.siblings('input[type=radio]').removeAttr('readonly');//解除禁用
                var $t_flag_radio = $t.siblings('input[type=radio]:checked');
                selObj = {
                    name: $t_flag.prop('name'),
                    beforeValue: $t_flag_radio.val(),
                }
                break;
            case 'text'://为文本
                console.log('文本');
                $t_flag.removeAttr('readonly');//解除禁用
                selObj = {
                    name: $t_flag.prop('name'),
                    beforeValue: $t_flag.val(),
                }
                break;
            case ''://为tabs切换
                console.log('tabs切换');
                if ($t_flag.is('a')) {
                    $t_flag.css('padding-right', '38px');//改变tabs宽度
                    var $t_ul = $t.parents('ul.nav');
                    var $t_table = $t_ul.next('.nav-content').find('table.XGridUI:visible');
                    selObj = {
                        name: $t_flag.prop('name'),
                        beforeValue: $('#' + $t_table.prop('id')).XGrid('getRowData'),
                    }
                }
                break;
        }

        //从 变更对象 添加该属性
        window.changeApply_[$t_flag.prop('name')] = selObj;

    }).on('click', '.changeApplyBtn', function () {
        数据保存
        var $t = $(this), selObj;
        var $t_flag = $t.siblings('[changeapplyflag]');//标记元素

        switch ($t_flag.prop('type')) {
            case 'radio'://为单选
                console.log('单选');
                $t.siblings('input[type=radio]').attr('readonly', 'readonly');//禁用
                var $t_flag_radio = $t.siblings('input[type=radio]:checked');
                selObj = {
                    name: $t_flag.prop('name'),
                    afterValue: $t_flag_radio.val(),
                    afterText: $t_flag_radio.nextAll('label').eq(0).text(),
                    status: '1'
                }
                break;
            case 'text'://为文本
                console.log('文本');
                $t_flag.attr('readonly', 'readonly');//禁用
                selObj = {
                    name: $t_flag.prop('name'),
                    afterValue: $t_flag.val(),
                    afterText: $t_flag.val(),
                    status: '1'
                }
                break;
            case ''://为tabs切换
                console.log('tabs切换');
                var $t_ul = $t.parents('ul.nav');
                var $t_table = $t_ul.next('.nav-content').find('table.XGridUI:visible');
                $t_flag.css('padding-right', '25px');

                selObj = {
                    afterValue: $('#' + $t_table.prop('id')).XGrid('getRowData'),
                    status: '1'
                }
                break;
        }

        $.extend(selObj, window.changeApply_[$t_flag.prop('name')]);
        window.changeApply[$t_flag.prop('name')] = selObj;

        $t.hide().siblings('.yulan').show();
    }).on('click', '.cSelect', function () {
        展示变更
        utils.dialog({
            title: '　',
            width: 600,
            height: 500,
            content: ''
        }).show();
    }).on('click', '.cDelete', function () {
    	var $t = $(window.changeApply_e);
        var $t_flag = $t.siblings('[changeapplyflag]');//标记元素
        window.changeApply_d.close();//关闭对应dialog
        //从 变更对象 清空该属性
        delete window.changeApply[$t_flag.prop('name')];
    });
*/

//*********************************变更申请模块 结束 *********************************
