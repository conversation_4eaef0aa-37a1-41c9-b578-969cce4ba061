var insFormHtml="";
var largeCategoryOld="";

var saveUrl="/proxy-product/product/orgApproval/saveOrgApproval";
var editUrl="/proxy-product/product/orgApproval/editOrgApprovalSave";
var submitAuditAgainUrl="/proxy-product/product/orgApproval/submitAuditAgain";

//品牌厂家
var brandManufacturersBefore="";
var brandManufacturersBeforeVal="";
var indateBefore = "";
var indateTypeBefore="";
var secondCategoryBefore = "";
var secondCategoryBeforeVal = "";
var thirdCategoryBefore = "";
var thirdCategoryBeforeVal = "";
var largeCategoryArray = new Array();
largeCategoryArray[0] = "中药饮片";
largeCategoryArray[1] = "食品";
largeCategoryArray[2] = "保健食品";
largeCategoryArray[3] = "中药材";
largeCategoryArray[4] = "药食同源";
//let base_manufacturerAddress = ''; // 变更前 生产厂家
//let base_entrustmentManufacturerAddress = ''; // 变更前 生产厂家
$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //tabs输入框只能输入数字限制
    $(document).on("input",".bootstrap-tagsinput input",function(){
        this.value=this.value.replace(/\D/g,'');
        $(this).attr("maxlength",15);
    });
    $(document).on("input","#inputdia input",function(){
        if($(this).hasClass('NAN_TYPE_CLASS')){ // 允许汉字类型

        }else{
            this.value=this.value.replace(/\D/g,'');
        }
        $(this).attr("maxlength",15);
    });
    $('body').on('click', '.cEdit', function (){
        var $t = $(window.changeApply_e); //找到对应节点
        var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
        //console.log($t_flag.attr('changeapplyflag'));
        var changeapplyflag = $t_flag.attr('changeapplyflag');
        if("secondCategory"==changeapplyflag){//二级分类
            //三级分类可编辑
            $('#thirdCategoryVal').removeAttr('disabled');
            $('#thirdCategoryVal').nextAll('.yulanInput').hide();
            //保存二级分类
            var btn_save_largeCategory = $('#div_secondCategory').find('.changeApplyBtn');

            $(btn_save_largeCategory).unbind("click"); //移除click
            $(btn_save_largeCategory).on('click',function() {
                if($('#secondCategory').val()==""){//判断是否选择二级分类
                    utils.dialog({content: '请选择二级分类', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                if($('#thirdCategory').val()==""){//判断是否选择三级分类
                    utils.dialog({content: '请选择三级分类', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                //保存关联的三级分类
                delete window.changeApply["thirdCategory"];
                //保存三级的修改
                var thirdCategoryBeforeObj = {
                    attribute:"thirdCategory",
                    changeBefore:thirdCategoryBefore,
                    changeAfter: $('#thirdCategory').val(),
                    afterText:$('#thirdCategoryVal').val(),
                    changeType: '1'
                }
                window.changeApply["thirdCategory"] = thirdCategoryBeforeObj;
                //三级分类 回显后不可编辑
                $('#thirdCategory').val(thirdCategoryBefore);
                $('#thirdCategoryVal').val(thirdCategoryBeforeVal);
                $("#thirdCategoryVal").attr('data-value',thirdCategoryBeforeVal);
                $('#thirdCategoryVal').attr('disabled','disabled');
                $('#thirdCategoryVal').nextAll('.yulanInput').show().addClass('yulanInput_after');
            })
        }else if("thirdCategory"==changeapplyflag){//三级分类
            var btn_save_largeCategory = $('#div_thirdCategory').find('.changeApplyBtn');
            $(btn_save_largeCategory).unbind("click"); //移除click
            $(btn_save_largeCategory).on('click',function() {
                delChangeApply("secondCategory");
            })
        }else  if("manufacturer"==changeapplyflag) {//生产厂家
            var btn_save_manufacturer = $('#div_manufacturer').find('.changeApplyBtn');
            $(btn_save_manufacturer).unbind("click"); //移除click
            $(btn_save_manufacturer).on('click',function() {
                //保存关联的品牌厂家
                var brandManufacturersObj = {
                    attribute:"brandManufacturers",
                    changeBefore:brandManufacturersBefore,
                    changeAfter: $('#brandManufacturers').val(),
                    afterText:$('#brandManufacturersVal').val(),
                    changeType: '1'
                }
                window.changeApply["brandManufacturers"] = brandManufacturersObj;
                //保存后显示修改前的
                $('#brandManufacturers').val(brandManufacturersBefore);
                $('#brandManufacturersVal').val(brandManufacturersBeforeVal);
            })
        }
        var name = $t_flag.attr('name');
        if("supplyPrice"==name){//供货价
            var btn_save = $('#div_supplyPrice').find('.changeApplyBtn');
            $(btn_save).unbind("click"); //移除click
            $(btn_save).on('click',function() {
                //保存关联的三级分类
                delete window.changeApply["standardPrice"];
                //保存三级的修改
                var standardPriceObj = {
                    attribute:"standardPrice",
                    changeBefore:$('#standardPrice').val(),
                    changeAfter: $('#supplyPrice').val(),
                    afterText:$('#supplyPrice').val(),
                    changeType: '1'
                }
                window.changeApply["standardPrice"] = standardPriceObj;
            });
        }else if("indateValue"==name){//有效期
            $('#indate').removeAttr('disabled');
            $('#indateType').removeAttr('disabled');
            var btn_save = $('#id_indateValue').find('.changeApplyBtn');
            $(btn_save).unbind("click"); //移除click
            $(btn_save).on('click',function() {
                if(!$("#indate").hasClass('ignore')){
                    if($("#indate").val()==""|| $('#indateType').val()==""){
                        utils.dialog({content: '有效期不能为空', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
                $("#indateValue").val( $("#indate").val()+$("#indateType").val());
                $("#indate").val(indateBefore);
                $("#indateType").val(indateTypeBefore);
                $('#indate').attr('disabled','disabled');
                $('#indateType').attr('disabled','disabled');;
            });
        }
    })
    //删除按钮
    $('body').on('click', '.cDelete', function (){
        var $t = $(window.changeApply_e); //找到对应节点
        var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
        //console.log($t_flag.attr('changeapplyflag'));
        var changeapplyflag = $t_flag.attr('changeapplyflag');
        if("secondCategory"==changeapplyflag||"thirdCategory"==changeapplyflag){//二级分类
            delChangeApply("secondCategory");
            delChangeApply("thirdCategory");
        }else if("manufacturer"==changeapplyflag) {//生产厂家
            //删除关联的品牌厂家
            delChangeApply("brandManufacturers");
        }
        var name = $t_flag.attr('name');
        if("supplyPrice"==name) {//删除供货价修改，同时删除标准价修改
            //删除通用名助记码
            delChangeApply("standardPrice");
            return;
        }
    })
    var pageType=$("#pageType").val();
    //非申请页，回显商品数据
    if (pageType != 0){
       var orgProductId= $("#orgProductId").val();
       obtainOrgProductInfo(orgProductId);
        //隐藏搜索图标及不可搜索
        $(".glyphicon-search").hide();
        $("#search_commodity").attr('disabled','disabled');

        $('.addTagBtn').removeClass('disBlock').addClass('disNone');
        $('.only-read').prev('div').find('input').removeClass('disBlock').addClass('disNone');
    }
    readOnly();
    $("#changeApplyBtn").hide();
    //工作流，流程图
    var workUrl="";
    if (pageType == 0 || pageType == 1){
        workUrl="/proxy-product/product/purchaseLimit/queryTotle?key="+$("#workProcessKey").val();
    }
    if (pageType==2 || pageType==3 || pageType==4){
        workUrl = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+$("#processId").val();
    }
    //获取审核流程数据
    $.ajax({
            type: "POST",
            url: workUrl,
            async: false,
            error: function () {
                utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
            },
            success: function (data) {
                if (data.code==0){
                    $('.flow').process(data.result);
                }else {
                    utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
                }
            }
        });
    $("input[name='entrustmentProduction']").click(function(){
        if ($(this).val()==0){
            $("#entrustmentManufacturer").val("");
            $("#entrustmentManufacturerVal").val("");
            $(".entManufacturerDiv").hide();
            $('input[name=entrustmentManufacturerVal]')['addClass'](' ignore');
        }else {
            $(".entManufacturerDiv").show();
            if($('#largeCategoryVal').attr('data-value') != '中药饮片'){
                $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>受托厂家')
                $('input[name=entrustmentManufacturerVal]')['addClass'](' {validate:{ required :true}}');
                $('input[name=entrustmentManufacturerVal]')['removeClass'](' ignore');
            }
        }
    })
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    /**
     *  V1.7.2.1
     *  增加限制： 安全库存天数< 库存上线天数， 安全库存天数【10,20】天
     *  库存上线天数【30,40】天
     *  失去焦点时判断。不对的时候要 给出提示，提示出合法值的范围
     */
    let safetyInventoryObj = {
        inpName: 'safetyInventory',
        tagName: '安全库存天数',
        validityVal: [10,20]
    }
    if(validityCheck(safetyInventoryObj)){
        return false;
    }
    let inventoryUpperLimitObj = {
        inpName: 'inventoryUpperLimit',
        tagName: '库存上限天数',
        validityVal: [30,40]
    }
    if(validityCheck(inventoryUpperLimitObj)){
        return false;
    }

    //多选框选中
    loadData("keyConservationCategories");
    loadData("specialAttributes");
    //loadData("storageAttribute");
    // loadData("operatingCustomers");
    // loadData("specialProvision");
    function loadData(key) {
        var tagName, type, arr, thisVal;
        var value = $("#"+key).val();
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (type == 'checkbox') {
                if ($.type(value) == 'array') {
                    arr = value;
                } else if ($.type(value) == 'string') {
                    arr = value.split(',');
                }
                for (var i = 0; i < arr.length; i++) {
                    if (thisVal == arr[i]) {
                        $(this).attr('checked', true);
                        break;
                    }
                }
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //保存草稿
    $("#saveRowData").on("click", function () {
    	if(utils.allSavaBtnsHidden()){
	        return false;
	    }
        parent.showLoading();
        $("#statues").val(0);
        if (pageType==0){
            butSubmint(saveUrl);
        }
        if (pageType==1){
            butSubmint(editUrl);
        }
    });
    //提交审核
    $("#submitAssert").on("click", function () {
    	if(utils.allSavaBtnsHidden()){
	        return false;
	    }
        //提交前验证
        if (validform("applicationAttributeVo").form()) {
            parent.showLoading();
            $("#statues").val(1);
           /* var supplyPrice = $("#supplyPrice").val();//供货价
            var appPrice = $("#appPrice").val();//APP价
            if(Number(supplyPrice)>Number(appPrice)){
                var d =  dialog({
                    title: "提示",
                    content: "APP售价低于供货价，已造成<span style='color: red'>负毛利</span>，请慎重填写！",
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        butSubmint();
                        d.close().remove();
                        return false;
                    },
                    cancelValue: '取消',
                    cancel: function () {
                        d.close().remove();
                    }
                }).showModal();
            }else {

            }*/
            if (pageType==0){
                butSubmint(saveUrl);
            }
            if (pageType==1){
                butSubmint(editUrl);
            }
        } else {//验证不通过
            utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    });
    //关闭按钮
    $("#closePage").on("click", function () {
    	if(utils.allSavaBtnsHidden()){
	        return false;
	    }
        var d =  dialog({
            title: "提示",
            content: "是否保存草稿？",
            width:300,
            height:30,
            okValue: '保存草稿',
            button:[
                {
                    value:'关闭',
                    callback:function(){
                        utils.closeTab();
                    }
                }
            ],
            ok: function () {
                parent.showLoading();
                $("#statues").val(0);
                if (pageType==0){
                    butSubmint(saveUrl);
                }
                if (pageType==1){
                    butSubmint(editUrl);
                }
                utils.closeTab();
            }
        }).showModal();
    });
    largeCategoryOld=$("#largeCategory").val();
    insFormHtml=$("#insForm").html();
    //二级分类 secondCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querytwocategory",{paramName:'categoryName',params:{"pid":$("#firstCategory").val()}
            ,isSearch:function(params){
                var code=$("#firstCategory").val();
                //console.log(code);
                if(code != '')
                {
                    params.pid=code;
                    return params;
                }
                return false;
            }},"secondCategory",{data:"id",value:"categoryName"},""
        ,function (result2) {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
            $("#thirdCategoryVal").focus(function () {
                if($.trim($("#secondCategoryVal").val()) == '')
                {
                    $(this).blur();
                }
            })
        },function () {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
        });
    //三级分类 thirdCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querythreecategory",{paramName:'categoryName',params:{"pid":$("#secondCategory").val()}
        ,isSearch:function(params){
            var code=$("#secondCategory").val();
            if(code != '')
            {
                params.pid=code;
                return params;
            }
            return false;
        }},"thirdCategory",{data:"id",value:"categoryName"});
    //采购员 buyer
    var orgCode = $("#recordOrgCode").val();
    valAutocomplete("/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode",{paramName:'userName', params:{"orgCode":$("#recordOrgCode").val()}
        ,isSearch:function(params){
            var code=$("#recordOrgCode").val();
            //console.log(code);
            if(code != '')
            {
                params.orgCode=code;
                return params;
            }
            return false;
        }},"buyer",{data:"id",value:"userName"},"-");
    //商品定位 commodityPosition
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":3}},"commodityPosition",{data:"id",value:"name"});
    //生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord",params:{"isStop":0}},"manufacturer"
        ,{data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"},""
        ,function (result) {
            $("#brandManufacturers").val(result.brandfactoryId);
            $("#brandManufacturersVal").val(result.brandfactoryName);
        },function () {
            $("#brandManufacturers").val("");
            $("#brandManufacturersVal").val("");
        });
    //品牌厂家
    /*valAutocomplete("/proxy-sysmanage/sysmanage/dict/querybrandfactorynotpage",{paramName:"brandFactoryName",params:{"isStop":0}},"brandManufacturers"
        ,{data:"brandfactoryId",value:"brandfactoryName",brandfactoryId:"brandfactoryId"},""
        ,function (result) {
            $("#brandManufacturers").val(result.brandfactoryId);
        },function () {
            var val=$.trim($('#brandManufacturersVal').val());
            if(val == '')
            {
                $("#brandManufacturers").val("");
            }
        });*/
    // 委托厂家
/*    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord",params:{"isStop":0}},"entrustmentManufacturer",{data:"manufactoryId",value:"manufactoryName",address:"address"},"",
        function (result) {
            $("#entrustmentManufacturerAddress").val(result.address);
        },function () {
            $("#entrustmentManufacturerAddress").val("");
        });*/
    //品牌字典
    //valAutocomplete("/proxy-sysmanage/sysmanage/brand/list",{paramName:'brandName'},"brand",{data:"id",value:"brandName"});
    //首营供应商 firstBattalionSupplier
    valAutocomplete("/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/firstSupplierOrgBaseList",{paramName:'supplierName',params:{"orgCode":$("#recordOrgCode").val()}
        ,isSearch:function(params){
            var code=$("#recordOrgCode").val();
            //console.log(code);
            if(code != '')
            {
                params.orgCode=code;
                return params;
            }
            return false;
        }},"firstBattalionSupplier",{data:"id",value:"supplierName"});
    //加载字典值
    showDictValue();
    //供应价和app价计算
    $('#supplyPrice,#appPrice').keyup(function(){
        var supplyPrice=$("#supplyPrice").val();
        var appPrice=$("#appPrice").val();
        if(supplyPrice!=null&&supplyPrice!=""&&appPrice!=null&&appPrice>0){
            var value=(1-supplyPrice/appPrice)*100;
            value=toDecimal2(value);
            $("#parGrossMargin").val(value+"%");
        }else {
            $("#parGrossMargin").val("");
        }
    });
    /**
     * 输入价格显示两位小数
     * @param obj
     */
    $('#supplyPrice,#appPrice,#terminalPrice,#guideCostPrice,#chainGuidePrice').blur(function(){
        var num=toDecimal2(this.value);
        this.value=num;
    });
    //审核 是否显示搜索图标
    $("#search_commodity").dblclick(function () {
            commodity_search_dia();
    });
    //审核按钮
    $('.auditPass').on('click', function () {
        $('#auditOpinion').val('');
        //操作类型（0通过，1 驳回，2 关闭）
        var status=this.getAttribute("status");
        var title="审核通过";
        let statusTitle = new Map([
            [1,'审核不通过'],
            [2,'关闭审核'],
            ['default','审核通过']
        ]);
        title = statusTitle.get(Number(status)) || statusTitle.get('default');
        (status==1)?$('#opinion').show():$('#opinion').hide();
        if (status!=2) {
            utils.dialog({
                title: title,
                content: $('#container'),
                okValue: '确定',
                ok: function () {
                    //审核不通过，意见不能为空
                    if (status == 1) {
                        if ($("#auditOpinion").val() == "") {
                            utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                    }
                    submitAuditInfo(status, $("#auditOpinion").val());
                },
                cancelValue: '取消',
                cancel: function () {
                    $("#auditOpinion").val("");
                }
            }).showModal();
        }else {
            title="关闭审核";
            utils.dialog({
                title:title,
                width:300,
                height:30,
                okValue: '确定',
                content: "确定关闭此申请？",
                ok: function () {
                    submitAuditInfo(status,"");
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        }

    });
    $('#submitAuditAgain').on('click', function () {
    	if(utils.allSavaBtnsHidden()){
	        return false;
	    }
        $("#statues").val(1);
        utils.dialog({
            title:"提交审核",
            width:300,
            height:30,
            okValue: '确定',
            content: "确定提交申请？",
            ok: function () {
                $("#statues").val(1);
                butSubmint(submitAuditAgainUrl+"?taskId="+$("#taskId").val())
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    })
    //console.log('变更初始化...');
  //初始化变更数据
    /*$.changeApply_selectData(data, {
        name: 'attribute',
        status: 'changeType',
        afterValue: 'changeAfter',
        beforeValue: 'changeBefore'
    });*/    //申请页面
    if (pageType==0){
        $('[changeapplyflag]').siblings('input').attr('readonly', 'readonly');
        $('[changeapplyflag]').attr('disabled','disabled');
        $('#search_commodity').removeAttr('readonly').removeAttr('disabled');
        //开启变更事件
        $.changApply_insertData({
            name: 'attribute',
            status: 'changeType',
            afterValue: 'changeAfter',
            beforeValue: 'changeBefore'
        });  //按钮id：changeApplyBtn
    }else {
        $.ajax({
            url:'/proxy-product/product/orgApproval/getPropertyChangeDetail?correlationId='+$('#approvalRecordId').val()+"&type=1",
            type:'get',
            dataType:'json',
            success:function(data){
                if(data!=null&&data!=undefined){
                	//RM
                	var obj={};
                    var objList = {};
                    $.each(data.result,function (i, v) {
                        v.changeType=-1;
                        obj[v.attribute]=v;
                        if(v.attribute=="approvalFile"){
                            objList.approvalFile = {
                                "attribute":"approvalFile",
                                "changeAfter":{}
                            };
                            //获取变更之后的批准文件
                            $.ajax({
                                type:"post",
                                url: "/proxy-product/product/productApprovalFile/toList",
                                async : false,
                                data:{"type": 1,"correlationId": $('#approvalRecordId').val(),"orgCode":$('#recordOrgCode').val()},
                                success: function (data) {
                                    objList.approvalFile.changeAfter = data.result.list;
                                },
                                error:function () {
                                    utils.dialog({content: '获取失败', quickClose: true, timeout: 2000}).showModal();
                                }
                            });
                        }
                        //获取变更之后的业务类型属性
                        if(v.attribute=="productChannel"){
                            objList.productChannel = {
                                "attribute":"productChannel",
                                "changeAfter":{}
                            };

                            $.ajax({
                                type:"post",
                                url: "/proxy-product/product/productChannel/toList",
                                async : false,
                                data:{"type": 1,"correlationId": $('#approvalRecordId').val(),"orgCode":$('#recordOrgCode').val()},
                                success: function (data) {
                                    objList.productChannel.changeAfter = data.result.list;
                                },
                                error:function () {
                                    utils.dialog({content: '获取失败', quickClose: true, timeout: 2000}).showModal();
                                }
                            });
                        }
                        if(v.attribute=="largeCategory"){
                            largeCategoryAfterVal=v.afterText;
                        }
                    });
                	//RM


                    //编辑页面
                    if (pageType==1||pageType==4){
                        var obj={};
                        $.each(data.result,function (i, v) {
                            v.changeType=-1;
                            obj[v.attribute]=v;
                        })
                        //开启变更事件
                        $.changApply_insertData({
                            name: 'attribute',
                            status: 'changeType',
                            afterValue: 'changeAfter',
                            beforeValue: 'changeBefore'
                        });  //按钮id：changeApplyBtn
                        window.changeApply=obj;
                        window.changeApplyBak=obj;
                      //RM
                        window.changeApplyList=objList;
                        window.changeApplyListBak=objList;
                      //RM
                    }
                    //审核页面
                    if (pageType==2||pageType==3){
                    	//RM
                    	window.changeApply=obj;
                    	 window.changeApplyList=objList;
                    	//RM
                        $.changeApply_selectData(data.result, {
                            name: 'attribute',
                            status: 'changeType',
                            afterValue: 'changeAfter',
                            beforeValue: 'changeBefore'
                        });
                    }
                    $.each(data.result,function (i, v) {
                        if(v.attribute=="entrustmentManufacturer"){
                            $(".entManufacturerDiv").show();
                        }
                    })
                    $('.only-read').prev('div').find('input').removeClass('disBlock').addClass('disNone');
                }
            }
        })
    }

    //插入变更数据
    //window.changeApply

    setTimeout(function () {
        let scopeOfOperationVal = $('#scopeOfOperationVal').attr('data-value');
        if(scopeOfOperationVal == '中药饮片'){
            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地')
        }
    },200)



    // 从商品资料变更申请搬过来的批准文件的相关代码
    $(".rowBtn").attr("disabled","disabled");
    //批准文件加载
    var scopeOfOperation= $("#scopeOfOperation").val();
    localBatchName(1);
    $('#X_Table').XGrid({
        //url:"/proxy-product/product/productApprovalFile/toList?type=0&correlationId="+baseProductId,
        colNames: ['','<i class="i-red">*</i>批件名称', '<i class="i-red">*</i>批件编号', '<i class="i-red">*</i>签发日期', '<i class="i-red">*</i>有效期至', '附件','附件数据'],
        colModel: [
            {
                name: 'id',
                index: 'id',
                hidden:true
            },
            {
                name: 'batchName',
                index: 'batchName',
                rowtype: '#batchName',
            }, {
                name: 'batchCode',
                index: 'batchCode',
                rowtype: '#batchCode',
            }, {
                name: 'issueDateStr',
                index: 'issueDateStr',
                rowtype: '#issueDate'
            }, {
                name: 'validityDateStr',
                index: 'validityDateStr',
                rowtype: '#validityDate'
            }, {
                name: 'enclosure',
                index: 'enclosure',
                formatter:function (value) {
                    var str='无';
                    if(value)
                    {
                        str ='<a href="javascript:;" onclick="showImg(this);">'+value+'</a>';
                    }
                    return str;
                },
                unformat: function (e) {
                    e=e.replace(/<[^>]+>/g,'');
                    if(e=="无"){
                        e=0;
                    }
                    return e;
                }
            },{
                name:'enclosureList',
                index:'enclosureList',
                hidden:true,
                formatter:function (value) {
                    if(value)
                    {
                        return JSON.stringify(value);
                    }
                    return JSON.stringify([]);
                }
            }],
        rowNum: 10,
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        gridComplete: function () {
            initFileSelected();
        }
    });

    //批量上传
    $("#batchUpload").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#X_Table');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("select[name='batchName'] option:selected");
            typeList.push({
                text:sel.text(),
                value:sel.val(),
                lineNum: i
            });
            //console.log(sel.val())
            //添加已存在附件
            if(rowData[i].enclosureList.length > 0){
                rowData[i].enclosureList=JSON.parse(rowData[i].enclosureList);
                for(var j=0;j<rowData[i].enclosureList.length;j++){
                    rowData[i].enclosureList[j].type=sel.val();
                    rowData[i].enclosureList[j].lineNum = i;
                }
                eChoImgList = eChoImgList.concat(rowData[i].enclosureList);
            }
        }
        if(typeList.length < 1)
        {
            utils.dialog({content: '请先新增一行并选择批准文件', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                //console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'enclosureList':[]});
                            $table.setRowData(id,{'enclosure':''});
                        }
                    })
                    return false;
                }
                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData[j]['batchName'] == data[i].type  && j == data[i].lineNum){
                            l.push(list);
                        }
                    }
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    $table.setRowData(trId,{'enclosureList':l});
                    $table.setRowData(trId,{'enclosure':(l.length == 0?'':l.length)});
                }
            }
        });
    });

    var rowNumber=1;
    $("#addRowData").on("click",function () {
        rowNumber++;
        $('#X_Table').addRowData({id:rowNumber});
        initFileSelected();
    });
    $("#deleRow").on("click",function () {
        var selectRow = $('#X_Table').XGrid('getSeleRow');
        if (!selectRow) {
            utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
        } else {
            utils.dialog({
                title:"提示",
                width:300,
                height:30,
                okValue: '确定',
                content: "确定删除此条记录?",
                ok: function () {
                    $('#X_Table').XGrid('delRowData', selectRow.id);
                    utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        }
    });

    //是否专供
    // $("input[name='specialProvision']").change(function() {
    //     let specialProvisionVal = $(this).val();
    //     /**
    //      * RM 2019-08-16
    //      */
    //     if ($("input[name='specialProvision']:checked").length > 0) {
    //         // 版本一
    //         if (specialProvisionVal == '1') { // 操作 非专供
    //             $('#specialProvision').parent().find('[name=specialProvision]').not(':first').prop('checked', false);
    //         } else {
    //             if(specialProvisionVal == '2' ){ // 单店专供
    //                 $(this).parents('.checkbox').find('.tag_3').prop('checked',false)
    //             }else if(specialProvisionVal == '3'){// 连锁专供
    //                 $(this).parents('.checkbox').find('.tag_2').prop('checked',false)
    //             }
    //             $('#specialProvision').parent().find('[name=specialProvision]:first').prop({
    //                 'checked': false,
    //                 'disabled': false
    //             });// 非专供 不允许选
    //         }
    //     } else {
    //         // 没有选中项时 全部放开允许点击
    //         $('#specialProvision').parent().find('[name=specialProvision]').prop('disabled', false);
    //     }
    // })

    /**
     * 业务类型属性
     */
    $('#X_Table_Channel').XGrid({
        //url:"/proxy-product/product/productApprovalFile/toList?type=0&correlationId="+baseProductId,
        data:[],
        colNames: ['类别', '<i class="i-red">*</i>类别','<i class="i-red">*</i>业务类型', '<i class="i-red">*</i>采购员','采购员ID', '<i class="i-red">*</i>供货价', '<i class="i-red">*</i>APP售价','智鹿总部采购价','连锁APP售价','荷叶大药房采购价', '<i class="i-red">*</i>票面毛利率','<i class="i-red">*</i>APP销售价是否维价','建议终端价', '<i class="i-red">*</i>终端零售价是否维价','<i class="i-red">*</i>生效状态'],
        colModel: [
            /*     {
                     name: 'id',
                     index: 'id',
                     hidden:true
                 },*/
            {
                name: 'channelType',
                index: 'channelType',
                hidden:true
            },{
                name: 'channelTypeVal',
                index: 'channelTypeVal'
            }, {
                name: 'channelId',
                index: 'channelId'
                // rowtype: '#channelVal',
            }, {
                name: 'buyerVal',
                index: 'buyerVal',
                rowtype: '#buyerVal',
            },   {
                name: 'buyer',
                index: 'buyer',
                hidden: true,
            },{
                name: 'supplyPrice',
                index: 'supplyPrice',
                rowtype: '#supplyPrice'
            }, {
                name: 'appPrice',
                index: 'appPrice',
                rowtype: '#appPrice'
            },{
                name: 'zhiluPrice',
                index: 'zhiluPrice',
                rowtype: '#zhiluPrice',
            }, {
                name: 'chainGuidePrice',
                index: 'chainGuidePrice',
                rowtype: '#chainGuidePrice',
            }, {
                name: 'heyePrice',
                index: 'heyePrice',
                rowtype: '#heyePrice',
            }, {
                name: 'parGrossMargin',
                index: 'parGrossMargin',
                rowtype: '#parGrossMargin'
            }, {
                name: 'dimensionSalesPriceYn',
                index: 'dimensionSalesPriceYn',
                rowtype: '#dimensionSalesPriceYn',
            }, {
                name: 'terminalPrice',
                index: 'terminalPrice',
                rowtype: '#terminalPrice',
            },{
                name: 'dimensionTerminalPriceYn',
                index: 'dimensionTerminalPriceYn',
                rowtype: '#dimensionTerminalPriceYn',
            },{
                name: 'effectiveState',
                index: 'effectiveState',
                rowtype: '#effectiveState',
            }],
        rowNum: 10,
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        gridComplete:function (res) {
            initFileSelected();
        }
    });

})

/**
 * 安全库存天数  库存上限天数  合法值判断
 * @param obj
 * @returns {boolean}
 *
 */
function validityCheck(obj){
    let bool = false;
    $('[name='+obj.inpName+']').on('blur',function () {
        let thisVal = $(this).val();
       /* if(!thisVal){
            utils.dialog({
                title: '提示',
                content: obj.tagName + '为必填项.',
                okValue: '确定',
                ok: function () {}
            }).showModal();
            bool = true;
            return bool;
        }*/
        if(thisVal){
            if(thisVal < obj.validityVal[0] || thisVal > obj.validityVal[1]){
                utils.dialog({
                    title: '提示',
                    content: obj.tagName + '的取值范围是【'+obj.validityVal[0]+','+obj.validityVal[1]+'】天.',
                    okValue: '确定',
                    ok: function () {}
                }).showModal();
                $(this).val('');
                bool = true;
                return bool;
            }
        }else {
            bool = true;
            return bool;
        }
    })
    return bool;
}
$("#X_Table").on("mousedown",'select[name="batchName"]',function () {
    /**
     * RM 2018-10-11
     * 批准文件 批件名称 选中的不再被禁用
     */
    //selCannotRepeatChoice(this,'X_Table','batchName');
})
//批准文件 的批件名称 change切换监听，当批件名称为商品附件时 移除 必填检验的require, 否则，当名称切换为别的类型的时候还得再把这个require添加上
function change_batchName(el) {
    var arr = $(el).parents('td').nextAll().find('input');
    $.each(arr,function (ind,ele) {
        if(ind != 1){ // 核准内容不是必填项
            $(ele)[(el.value != '0')?'addClass':'removeClass']('{validate:{ required :true}}')
            $(ele)[(el.value != '0')?'removeClass':'addClass']('ignore')
        }
    })
}
/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(obj).parents("table").getRowData(parentId);
    if(typeof data.enclosureList != 'string'){
        data.enclosureList=JSON.stringify(data.enclosureList);
    }
    if(data.enclosureList.length > 0)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            list:JSON.parse(data.enclosureList)
        })
    }
}
/**
 * 加载所属经营范围对应的批准文件
 * @param simpleCode
 */
function localBatchName(type) {
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/getBatchnamesByType",
        async : false,
        data:{"type":type},
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option><option value="0">商品附件</option>';//-999
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].batchId+'">'+arr[i].batchName+'</option>';
                    }
                }
            }
            $("select[name='batchName']").html(html);
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
function loadUserName(key,orgaKey) {
    var userId=$("#"+key).val();
    if(userId==""){
        return;
    }
    $.ajax({
        type:"get",
        url: "/proxy-sysmanage/sysmanage/system/queryUserInfoById",
        data:{"userId":userId},
        dataType:"json",
        success: function (data) {
            //console.log(data.result);
            if(data.code==0&&data.result!=null){
                var userName=data.result.userName;
                if(orgaKey!=null){//申请人机构显示
                    $("#"+orgaKey+"Val").val(data.result.orgName);
                }
                $("#"+key+"Val").val(userName);
                $("#"+key+"Val").attr("data-value",userName);
            }
        },
        error:function () {
        }
    });
}
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
var Is_ZYYP_flag = false; // 商品大类是否为中药饮片
function valAutocomplete(url,param,obj,resParam,querydelimiter,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    var options={
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        querydelimiter:querydelimiter,
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
            if(obj == 'largeCategory'){
                if(result.value == '中药饮片'){
                    Is_ZYYP_flag = true
                    $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                    $('#maintenancePeriod').parents('.input-group').find('div').eq(0).find('i').remove(); // 养护周期
                    $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                    $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                    $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                    $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                    $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                }else{
                    if(Is_ZYYP_flag){
                        $('input[name=indate]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>有效期') // 有效期
                        $('#maintenancePeriod').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>养护周期'); // 养护周期
                        $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>批准文号') // 批准文号
                        $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>标准库ID') // 标准库ID
                        $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>是否委托生产')//是否委托生产
                        $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('产地') // 产地
                        $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('质量标准') // 质量标准
                    }
                }
            }
        },
        onNoneSelect: function (params, suggestions) {
            //console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    }
    if(param.isSearch)
    {
        options.isSearch=param.isSearch;
    }
    $("#"+obj+"Val").Autocomplete(options);
}

//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x*100)/100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}
/**
 * 设置输入两位小数 并且控制可输入的最大值的位数
 *
 * obj this对象
 * size 最大值位数 可不传
 * */
function setFixedTwo(obj,size) {
    var value=obj.value;
    var n='';
    if(size)
    {
        for(var i=0;i<size;i++)
        {
            n+='9';
        }
        n+='.99';
        if(Number(value) > Number(n))
        {
            value=n;
        }
    }
    obj.value=value.replace(/[^\d.]/g,'').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');
}

function  readOnly() {
    $('[changeapplyflag]').siblings('input').attr('readonly', 'readonly');
    $('[changeapplyflag]').attr('disabled','disabled');
    $('.only-read').attr('disabled','disabled');

}
/**
 * 搜索商品机构商品
 * @returns {boolean}
 */
function commodity_search_dia() {
    var keyword=$("#search_commodity").val();
    var pageType=$("#pageType").val();
    var loginOrgCode = $("#recordOrgCode").val();
    if (pageType == 0){
        loginOrgCode = $("#loginOrgCode").val()
    }
    dialog({
        url: '/proxy-product/product/orgApproval/searchProduct',
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {"orgCode":loginOrgCode,"keyword":keyword,"limitedPinState":0,"limitedProductionState":0,"disableState":0},
        onclose: function () {
            if (this.returnValue) {
                var data = this.returnValue;
                //console.log(data);
                //加载主数据信息
                var orgProductId=data.id;
                $("#orgProductId").val(orgProductId);
                //设置机构
                $("#recordOrgCode").val(data.orgCode);
                obtainOrgProductInfo(orgProductId);
                $("#search_commodity").attr('disabled','disabled');
            }
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
};

/**
 * 主数据内容回显
 * @param json
 */
function loadProductData(json) {
    var obj = json;
    var key, value, tagName, type, arr, thisVal;
    for (x in obj) {
        key = x;
        value = obj[x];

        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).prop('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    if(arr!=null){
                        for (var i = 0; i < arr.length; i++) {
                            if (thisVal == arr[i]) {
                                $(this).prop('checked', true);
                                break;
                            }
                        }
                    }
                } else {
                    if(key=='indate'){
                        if(value == 0){
                            $(this).val('*');
                        }else if(value == -1){
                            $(this).val('-');
                        }else {
                            $(this).val(value);
                        }
                    }else{
                        $(this).val(value);
                    }
                    $(this).attr('title',value);
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
                $(this).attr('title',value);
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //加载字典值
    showDictValue();
    //受托厂家是否显示
    initChecked();
}
//判断委托产家是否显示
function initChecked(){
    var type=$("input[name='entrustmentProduction']:checked").val();
    if(type == 1)
    {
        $(".entManufacturerDiv").show();
    }else{
        $(".entManufacturerDiv").hide();
    }
};

var checkSubmitFlag= false;
function checkSubmit(){
    if (checkSubmitFlag==true){
        return false;
    }
    checkSubmitFlag=true;
    return true;
}
function  valTest_area_Stand(name,title) {
    var flag = true;
    if(changeApply[name]){ // 先判断name是否有修改
        if(changeApply[name]['afterText'] == undefined){
            diaFun(title)
            flag = false;
        }else if(changeApply[name]['afterText'] == ''){
            diaFun(title)
            flag = false;
        }
    }else{
        if(name == 'producingArea'){ // 产地
            if($('input[name='+name+']').prev().find('span').length < 1){ // 如果没有修改，那name的原始数据也应该有值，才可以提交审核
                diaFun(title)
                flag = false;
            }
        }
        if(name == 'qualityStandard'){ // 质量标准
            if($('input[name='+name+']').val() == ''){
                diaFun(title)
                flag = false;
            }
        }
    }
    return flag;
}
function valMust_area_stand(name,title) {
    var flag = true;
    if(window.changeApply[name]){
        if(window.changeApply[name]['afterText'] == undefined){
            diaFun(title)
            flag = false;
        }else if(window.changeApply[name]['afterText'] == ''){
            diaFun(title)
            flag = false;
        }
    }else{
        if($('input[name='+name+']').length > 1){
            if($('input[name='+name+']:checked').val() == undefined){
                diaFun(title)
                flag = false;
            }
        }else{
            if($('input[name='+name+']').val() == ''){
                diaFun(title)
                flag = false;
            }
        }
    }
    return flag;
}
function  diaFun(title) {
    parent.hideLoading();
    utils.dialog({
        title:'提示',
        content: title+'信息不能为空。',
        okValue:'确定',
        ok:function () {}
    }).showModal();
    return false;
}


/**
 * 保存数据
 */
function  butSubmint(url) {
    /*if (!checkSubmit()){
        utils.dialog({content: '正在提交。。。', quickClose: true, timeout: 2000}).showModal();
        return false;
    }*/
    //商品编码为空，未选择商品验证
    if ($("#commodity_code").val()==""){
        utils.dialog({content: '请先选择商品', quickClose: true, timeout: 2000}).showModal();
        $('#search_commodity').removeAttr('readonly').removeAttr('disabled');
        parent.hideLoading();
        return false;
    }

    //是否委托厂家为“是”，委托厂家为空判断
    if (window.changeApply['entrustmentProduction'] != undefined&&window.changeApply['entrustmentProduction']['changeAfter']==1){
        if (window.changeApply['entrustmentManufacturer'] == undefined){
            parent.hideLoading();
            utils.dialog({content: '委托厂家不能为空', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    }

    /**
     * RM 2018-10-30
     * 当提交审核的时候需要再次校验。因为用户数据的修改顺序hi导致产地为空的时候修改了商品大类
     * 导致，此时产地，质量标准应该必填了，却没有数据，去提交导致流程错误
     */
    if($('#largeCategoryVal').attr('data-value') == '中药饮片'){
        if(!valTest_area_Stand('producingArea','产地')){ return false;}
        if(!valTest_area_Stand('qualityStandard','质量标准')){ return false;}
    }else{
        //非中药饮片时，下面都为必填项，提交时需要做为空判断
        if(!valMust_area_stand('approvalNumber','批准文号')){ return false;}
        if(!valMust_area_stand('standardProductId','标准库ID')){ return false;}
        //if(!valMust_area_stand('indateValue','有效期')){ return false;}
        if(window.changeApply.indateValue){
            if(window.changeApply['indateValue']['afterText'] == undefined){
                diaFun('有效期')
                return false
            }else if(window.changeApply['indateValue']['afterText'] == '月'){
                diaFun('有效期')
                return false
            }
        }else{
            if($('input[name=indateValue]').val() == ''){
                diaFun('有效期')
                return false
            }
        }
        if(!valMust_area_stand('maintenancePeriod','养护周期')){ return false;}

    }
    // if(!valMust_area_stand('operatingCustomers','可经营客户')){ return false;}
    // if(!valMust_area_stand('specialProvision','是否专供')){ return false;}
    var productDataVo=getSavedData();
    // 提交审核 判断是否有修改
    if ($("#statues").val()==1){
        if(productDataVo.propertyApprovalDetailVos&&productDataVo.propertyApprovalDetailVos.length<1){
            utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
            $('#search_commodity').removeAttr('readonly').removeAttr('disabled');
            return false;
        }
    }
    var data=JSON.stringify(productDataVo);
    // console.log('--------------------------------------------')
    //console.log(productDataVo)
    $.ajax({
        type:"post",
        url: url,
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            if (data.code==0){
                var taskStatus=data.result.taskStatus;
                if(taskStatus!=undefined&&!taskStatus) {
                    utils.dialog({
                        title: "提示",
                        content: data.result.msg + " 暂时保存为草稿",
                        width: 300,
                        height: 30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    return false;
                }else {
                    var msg="";
                    if ($("#statues").val()==0){
                        msg =  '保存成功';
                    }else if ($("#statues").val()==1){
                        msg = '提交审核成功';
                    }else {
                        msg = '保存成功';
                    }
                    utils.dialog({
                        title: "提示",
                        content: msg,
                        width:300,
                        height:30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    $(".ui-dialog-close").hide();
                    return false;
                }
            }else {
                utils.dialog({content: data.result,timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '保存失败', timeout: 2000}).showModal();
            /*checkSubmitFlag= false;*/
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}

/**
 * 获取提交数据
 * @returns {string}
 */
function getSavedData() {
    unReadOnly();
    //申请属性数据
    var applicationAttributeVo=$("#applicationAttributeVo").serializeToJSON();
    var changeApply = window.changeApply;
    /**
     * RM
     * 批准文件
     */
    let approvalFileDtosRes = null;
    var changeApplyList = window.changeApplyList;
    // 批准文件字符串转回数组
    $.each(changeApplyList,function(k,v){
    	if(typeof v.changeAfter == 'string'){
    		v.changeAfter = JSON.parse(v.changeAfter)
    	}
    	if(typeof v.changeBefore == 'string'){
    		v.changeBefore = JSON.parse(v.changeBefore)
    	}
    })
    if(changeApplyList.approvalFile){
        var approvalFileDtos=changeApplyList.approvalFile.changeAfter;
        if(approvalFileDtos!=undefined && "" != approvalFileDtos){
            for(var i=0;i<approvalFileDtos.length;i++)
            {
                if(approvalFileDtos[i].enclosureList)
                {
                    if (typeof approvalFileDtos[i].enclosureList == 'string') {
                        approvalFileDtos[i].enclosureList=JSON.parse(approvalFileDtos[i].enclosureList);
                    }
                }
            }
            approvalFileDtosRes=approvalFileDtos;
        }
    };
    //业务类型属性
    var productChannelVoList = [];
    if(changeApplyList.productChannel){
        productChannelVoList=changeApplyList.productChannel.changeAfter;
    };


    var ary=[];
    $.each(changeApplyList,function (c, v) {
//        v.changeAfter="";
//        v.changeBefore="";
        v.changeAfter=JSON.stringify(v.changeAfter);
        v.changeBefore=JSON.stringify(v.changeBefore);
        ary.push(v);
    })
    $.each(changeApply,function (c, v) {
        if(ary.indexOf(v) < 0){
            ary.push(v);
        }
    });
    //RM
    //清除变更前后未变的数据
    for (var i = ary.length-1; i >= 0; i--) {
        if (ary[i].changeType!=0&&ary[i].changeBefore==ary[i].changeAfter&&ary[i].attribute!='approvalFile'&&ary[i].attribute!='productChannel'){
        	ary[i].changeType = 0;
        }
    }

    //RM
    //受托厂家，是否保存逻辑
    if (window.changeApply.entrustmentManufacturer != undefined){
        if (window.changeApply.entrustmentProduction != undefined&&window.changeApply.entrustmentProduction.changeAfter==0){
            window.changeApply.entrustmentManufacturer.changeType=0;
        }
    }
    //运营属性变更为1
    applicationAttributeVo.applyType=5;
    var workProcessKey = $("#workProcessKey").val();
    applicationAttributeVo.commonName= $('#search_commodity').val();
    var productData={
        'approvalRecordVo':applicationAttributeVo,
        'propertyApprovalDetailVos':ary,
        'workProcessKey':workProcessKey,
        'approvalFileDtos':approvalFileDtosRes,
        'productChannelVoList':productChannelVoList
    };
    readOnly();
    return  productData;
}
function  unReadOnly() {
    $(".only-read").each(function(){
        $(this).removeAttr('disabled');
    });}
/**
 * 字典值回显
 */
function  showDictValue() {
    loadUserName("buyer",null);//采购员回显
    //商品大类
    showComValue("largeCategory","1017");
    //生产厂家--添加地址信息
    //showManufacturerInfo("manufacturer",$("#manufacturer").val());
    //showComValue("manufacturer","1003");
    //品牌厂家
    //包装单位
    showComValue("packingUnit","1002");
    //剂型
    showComValue("dosageForm","1001");
    // 委托厂家--添加地址信息
    //showManufacturerInfo("entrustmentManufacturer",$("#entrustmentManufacturer").val());
    //showComValue("entrustmentManufacturer","1003");
    //存储条件
    showComValue("storageConditions","1019");
    //处方分类
    showComValue("prescriptionClassification","1016");
    //所属经营范围
    var simpleCode =$("#scopeOfOperation").val();
    var orgCode = $("#applicantOrgCode").val();
    if(simpleCode!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/queryscopebycode?orgCode='+orgCode+"&simpleCode="+simpleCode,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if(data.code == 0)
                {
                    var simpleCodeName = "";
                    for (var i = 0;i<data.result.length;i++){
                        if (i!=data.result.length-1){
                            simpleCodeName = simpleCodeName + data.result[i].name+",";
                        } else {
                            simpleCodeName = simpleCodeName + data.result[i].name;
                        }
                    }
                    $("#scopeOfOperationVal").val(simpleCodeName);
                    $("#scopeOfOperationVal").attr("data-value",simpleCodeName);
                    $("#scopeOfOperationVal").attr("title",simpleCodeName);
                }
            }
        })
    }
    /*// 一级分类 firstCategory
    showComValue("firstCategory","1025");
    //二级分类 secondCategory
    showComValue("secondCategory","1026");
    //三级分类 thirdCategory
    showComValue("thirdCategory","1027");*/
    //商品定位
    showComValue("commodityPosition","1013");
    //首营供应商 firstBattalionSupplier
    var supplierId=$("#firstBattalionSupplier").val();
    if(supplierId!=""){
        $.ajax({
            url:'/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/firstSupplierOrgBaseById?id='+supplierId,
            type:'get',
            dataType:'json',
            success:function(data){
                //console.log(data);
                if(data!=null&&data!=undefined){
                    $("#firstBattalionSupplierVal").val(data.supplierName);
                    $("#firstBattalionSupplierVal").attr("data-value",data.supplierName);
                }
            }
        })
    }
    //品牌
/*    var brand=$("#brand").val();
    if(brand!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/brand/queryById?id='+brand,
            type:'get',
            dataType:'json',
            success:function(data){
                //console.log(data);
                if(data!=null&&data!=undefined){
                    $("#brandVal").val(data.result.brandName);
                    $("#brandVal").attr("data-value",data.result.brandName);
                }
            }
        })
    }*/



}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showComValue(obj,type) {
    var id =$("#"+obj).val();
    if(id!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querybeanbytype?type='+type+"&id="+id,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                //console.log(data);
                if(data.code == 0)
                {
                    $("#"+obj+"Val").val(data.result);
                    $("#"+obj+"Val").attr("data-value",data.result);
                    $("#"+obj+"Val").attr("title",data.result);
                    if(obj=="largeCategory"){
                        if(data.result=="中药饮片") {
                            $("#Registrant").text('化妆品备案人/注册人');
                            $(".productrate").show();
                            if(data.result == '中药饮片'){
                                //Is_ZYYP_flag = true
                                $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                                $('#maintenancePeriod').prev().find('i').remove(); // 养护周期
                                $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                                $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                                $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                                $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                                $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                            }

                        }else if(data.result=="医疗器械"){
                            $("#Registrant").text('医疗器械注册人/备案人名称');
                        }else{
                            $("#Registrant").text('化妆品备案人/注册人');
                            $(".productrate").hide();
                        }
                        if(largeCategoryArray.indexOf(data.result)>-1) {
                            $(".productrate").show();
                        }else{
                            $(".productrate").hide();
                        }
                    }
                }
            }
        })
    }
}
/*function showManufacturerInfo(obj,manuId) {
    if(manuId!=undefined&&manuId!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querymanufactorybyId?manuId='+manuId,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if (data.code==0){
                    $("#"+obj+"Val").val(data.result.manufactoryName);
                    $("#"+obj+"Address").val(data.result.address);
                    if (obj=='manufacturer'){
                        base_manufacturerAddress = data.result.address;
                    }
                    if (obj=='entrustmentManufacturer'){
                        base_entrustmentManufacturerAddress = data.result.address;
                    }
                }
            }
        })
    }
}*/
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showValue(obj,data) {
    var key =$("#"+obj).val();
    for(var i=0;i<data.length;i++){
        if(data[i].data==key){
            $("#"+obj+"Val").val(data[i].value);
            $("#"+obj+"Val").attr('title',data[i].value);
        }
    }
}

/**
 * 审核按钮操作
 */
function submitAuditInfo(status,auditOpinion) {
    var productData={
        'recordId':$('#approvalRecordId').val(),
        'statues':status,
        'auditOpinion':auditOpinion,
        "taskId":$("#taskId").val(),
        "recordApplicationTime":$("#applicationTimeRetrun").val()
    };
    var data=JSON.stringify(productData);
    //console.log(data);
    $.ajax({
        type:"post",
        url: "/proxy-product/product/orgApproval/auditOrgApprovalDo",
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var msg = "";
            // if(status==0){
            //     msg =  '恭喜审核通过';
            // }
            // if(status==1){
            //     msg = '驳回成功';
            // }
            // if(status==2){
            //     msg = "流程已关闭！";
            // }
            let statusMsg = new Map([
                [0,'恭喜审核通过'],
                [1,'驳回成功'],
                [2,'流程已关闭'],
            ])
            msg = statusMsg.get(Number(status));
            utils.dialog({
                title: "提示",
                content: msg,
                width:300,
                height:30,
                okValue: '确定',
                ok: function () {
                    utils.closeTab();
                }
            }).showModal();
            $(".ui-dialog-close").hide();
            return false;
        },
        error:function () {
            utils.dialog({content: '操作失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
//根据机构商品ID，加载页面数据
function obtainOrgProductInfo(orgProductId){

    $.ajax({
        type:"post",
        url: "/proxy-product/product/orgApproval/getOrgProInfo",
        async : true,
        data:{"orgProductId":orgProductId},
        dataType:"json",
        success: function (data) {
            var result=data.result;
            //console.log(data);
            $('input[data-role="tagsinput"]').tagsinput('removeAll');
            loadProductData(result.productBaseInfoVo);
            loadProductData(result.productOrganizationVo);
            brandManufacturersBefore=result.productOrganizationVo.brandManufacturers;
            $("#changeApplyBtn").show();
            secondCategoryBefore = $("#secondCategory").val();
            secondCategoryBeforeVal = $("#secondCategoryVal").val();
            thirdCategoryBefore=$("#thirdCategory").val();
            thirdCategoryBeforeVal=$("#thirdCategoryVal").val();
            brandManufacturersBeforeVal=$("#brandManufacturersVal").val();
            indateBefore = $("#indate").val();
            indateTypeBefore=$("#indateType").val();
            $("#indateValue").val(indateBefore+indateTypeBefore);
            var supplyPrice=toDecimal2($("#supplyPrice").val());
            $("#supplyPrice").val(supplyPrice);
            var appPrice=toDecimal2($("#appPrice").val());
            $("#appPrice").val(appPrice);
            var terminalPrice=toDecimal2($("#terminalPrice").val());
            $("#terminalPrice").val(terminalPrice);
            $('.only-read').prev('div').find('input').removeClass('disBlock').addClass('disNone');


            //加载批准文件 按钮置灰
            var simpleCode= $("#scopeOfOperation").val();
            localBatchName(1);

            $('#X_Table').setGridParam({
                url:"/proxy-product/product/productApprovalFile/toList",
                postData: {
                    "type": 0,
                    "correlationId": orgProductId,
                    "orgCode":$("#recordOrgCode").val(),


                },page:1,
                gridComplete: function () {
                    if(pageType==2||pageType==3){//初始化只读处理
                        $("input").attr("readonly",true);
                        $("input[type='checkbox']").attr("disabled",true);
                        $("input[type='radio']").attr("disabled",true);
                        $("select").attr("disabled","disabled");
                    }
                    $('#X_Table input,#X_Table select').attr('disabled','disabled');
                    //加载说明书
                    $('#insForm textarea').attr('disabled','disabled');
                    $('#insForm input').attr('disabled','disabled');
                }
            }).trigger('reloadGrid');
            //todo 业务类型属性
            $('#X_Table_Channel').setGridParam({
                url:"/proxy-product/product/productChannel/toList",
                postData: {
                    "type": 0,
                    "correlationId": orgProductId,
                    "orgCode":$("#recordOrgCode").val(),


                },page:1,
                gridComplete: function () {
                    if(pageType==2||pageType==3){//初始化只读处理
                        $("input").attr("readonly",true);
                        $("input[type='checkbox']").attr("disabled",true);
                        $("input[type='radio']").attr("disabled",true);
                        $("select").attr("disabled","disabled");
                    }
                    $('#X_Table_Channel input,#X_Table_Channel select').attr('disabled','disabled');
                    // 初始化采购员模糊搜索
                    var rowData = $("#X_Table_Channel").XGrid('getRowData');
                    if (rowData && rowData.length) {
                        var orgCode = $("#applicationOrgCode").val();
                        rowData.forEach(function (item) {
                            $("#" + item.id + " input[name=buyerVal]").Autocomplete({
                                serviceUrl: "/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode", //异步请求
                                paramName: 'userName',//查询参数，默认 query
                                params: {"orgCode": orgCode},
                                dataType: 'json',
                                querydelimiter: "-",
                                minChars: '0', //触发自动匹配的最小字符数
                                maxHeight: '300', //默认300高度
                                dataReader: {list: 'result', data: "id", value: "userName"},
                                triggerSelectOnValidInput: false, // 必选
                                showNoSuggestionNotice: true, //显示查无结果的container
                                noSuggestionNotice: '查询无结果',//查无结果的提示语
                                onSelect: function (result) {
                                    $("#X_Table_Channel").XGrid('setRowData', item.id, {buyer: result.data})
                                },
                                onNoneSelect: function (params, suggestions) {
                                    $("#X_Table_Channel").XGrid('setRowData', item.id, {buyer: ''});
                                    $("#" + item.id + " input[name=buyerVal]").val('');
                                }
                            });
                        })
                    }
                }
            }).trigger('reloadGrid');

        },
        error:function (XMLHttpRequest, textStatus, errorThrown) {
            utils.dialog({content: '加载数据失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
/**
 * 删除已修改字段
 */
function delChangeApply(name) {
    if (window.changeApplyBak && changeApplyBak[name]) {
        if (window.changeApply[name]) {
            window.changeApply[name] = {};
            window.changeApply[name]["changeType"] = 0;
            window.changeApply[name]["attribute"] = name;
        }
    } else {
        delete window.changeApply[name];
    }
    $('#'+name+'Val').nextAll('.yulanInput').removeClass('yulanInput_after');
}
function initFileSelected() {
}


//设置标准价
function toStandardPriceAndparGrossMargin(obj) {
    toDecimal2(obj);
    var channelrowdatas=$('#X_Table_Channel').getRowData();
    $.each(channelrowdatas,function (index,item) {
        var supplyPrice = item.supplyPrice;//供货价
        var appPrice = item.appPrice;//APP价
        if(supplyPrice!=null&&supplyPrice!=""&&appPrice!=null&&appPrice>0){
            var value=(1-supplyPrice/appPrice)*100+"";
            value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');
            $('#X_Table_Channel').XGrid('setRowData',channelrowdatas[index].id,{parGrossMargin:value+"%"})
        }else{
            $('#X_Table_Channel').XGrid('setRowData',channelrowdatas[index].id,{parGrossMargin:""})
        }
        if(item.channelId=='1'){
            $("#standardPrice").val(supplyPrice);
        }
    })
};
