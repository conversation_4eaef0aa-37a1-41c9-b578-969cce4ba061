/* 默认属性 */
ul,li{
    list-style: none;
}
/* 表头多余部分隐藏，表头对齐 */
.ui-jqgrid .ui-jqgrid-resize-ltr{
    display: none;
}
.ui-jqgrid .ui-jqgrid-htable th div {
    white-space: normal !important;
    height: auto !important;
}
/* 表格边框 */
.ui-th-column th.ui-th-column,
.ui-jqgrid .ui-jqgrid-htable th.ui-th-column {
    border-width: 1px 1px 1px 0;
    border-color: inherit;
    border-style: solid;
}
.ui-jqgrid .ui-jqgrid-htable .ui-th-div{
    margin-top: 0;
}
.ui-th-column th.ui-th-column:first-child,
.ui-jqgrid .ui-jqgrid-htable th.ui-th-column:first-child {
    border-left-width: 1px;
}
.ui-jqgrid tr.jqgrow td:first-child,
.ui-jqgrid tr.jqgroup td:first-child {
    border-left-color: inherit;
    border-left-style: solid;
    border-left-width: 1px;
}
/*.table_c tr.jqgrow:nth-child(2) td {
    border-top-color: inherit;
    border-top-style: solid;
    border-top-width: 1px;
}*/
/* 复选框 */
.check {
    display: inline-block;
    position: relative;
    width: 20px;
    height: 20px;
    border: 1px solid black;
}
.tick:before,
.tick:after {
    content: '';
    pointer-events: none;
    position: absolute;
    color: black;
    border: 1px solid #000;
    background-color: white;
}
.tick:before {
    width: 4px;
    height: 0;
    left: 23%;
    top: 50%;
    transform: skew(0deg, 50deg);
}
.tick:after {
    width: 7px;
    height: 0;
    left: 40%;
    top: 40%;
    transform: skew(0deg, -50deg);
}
/* 主体样式 */
#box{
    color: #000 !important;
    font-family: SimSun !important;
    font-size: 22px;
}
#box table{
    border-color: #000 !important;
    font-size: 20px;
    /* border-width: 0.5px;
    border-style: solid;
    border-top:none; */
}
#box .ui-jqgrid tr.jqgrow td {
    word-break: break-all;
    white-space: normal !important;
    height: 50px;
    vertical-align:center;
    text-align: center;
}
#box .ui-jqgrid tr.ui-jqgrid-labels th{
    height: 50px;
    font-weight: normal;
    vertical-align:center;
    text-align: center;
}

#big_box {
    width: 0;
    height: 0;
    overflow: hidden;
}
#box {
    box-sizing: border-box;
    width: 1080px;
    padding: 0 20px;
}
#box .header {
    text-align: right;
    border-bottom: 1px solid #000;
    margin: 0 20px 40px;
    font-size: 18px;
}
#box .header .h_num {
    font-size: 22px;
    font-weight: bold;
}

#box .title {
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    margin-top: -20px;
}
#box .footer {
    margin-top: 20px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
}
#box .table_header {
    margin-bottom: 10px;
    margin-top: -30px;
}
/*#box .table_footer1 {
    margin-top: 10px;
}
#box .table_footer2 {
    margin-top: 40px;
}*/
#box .content {
    position: relative;
}
#box .content .con-item1 {
    font-weight: bold;
}
#box .content .con-item1 .con-item1-val{
    display: inline-block;
    padding: 0 30px;
    border-bottom: 1px solid #000;
}
#box .content .con-item2 .con-item2-val{
    display: inline-block;
    padding: 0 50px;
    border-bottom: 1px solid #000;
}
#box .content .boxClearfix:after{
    content: "";
    display: block;
    height: 0;
    clear:both;
    visibility: hidden;
}
#box .content .signature {
    font-size: 20px;
}

#box .content .signature .signature-item {
    display: flex;
    /* justify-content: space-around; */
    margin: 10px 0;
    /* flex: 1;
    padding: 15px; */
}
/* #box .content .signature .signature-con .signature-item:last-child {
    border-left: 1px solid #000;
} */
div.ui-jqgrid-view table.ui-jqgrid-btable {
    border-style: none;
    border-top-style: none;
    border-collapse: separate;
}
div.ui-jqgrid-view table.ui-jqgrid-btable td {
    border-left-style: none;
}

div.ui-jqgrid-view table.ui-jqgrid-htable {
    border-style: none;
    border-top-style: none;
    border-collapse: separate;
}
div.ui-jqgrid-view table.ui-jqgrid-btable th {
    border-left-style: none;
}
