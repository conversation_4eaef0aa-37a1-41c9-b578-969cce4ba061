$(function () {

//	getTotalCount();
	var sysOrgCode = $("#sysOrgCode").val();
	   //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
//    var orgCode = $("#orgList").val();
//    console.log('orgCode='+orgCode);
//商品名称 双击查询
    $('#productDesc').dblclick(function () {
        utils.dialog({
            title: '商品列表',
            url: '/proxy-finance/finance/initBalance/toCommodityList?orgCode='+$("#orgList").val(),
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#productDesc').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#productDesc').val(data.productName);
                    $('#drugCode').val(data.productCode)
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    })
    
     //商品名称 搜索
    $('#productDesc').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$("#orgList").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)
            console.log('333',result.data)
        },
        onNoneSelect: function (params, suggestions) {
            console.log('没选中回调函数');
            $("#drugCode").val('');
            $("#productDesc").val('');
        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            console.log(query, suggestions);

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
             console.log('container隐藏前回调', container)
        },
        
    });

    var totalTable = z_utils.totalTable;
    var totalTablea = z_utils.totalTablea;
    //仓库
    initStorageTypeSelect();
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/initBalance/findStockEntryDepositDetailByOrgCode',
        postData: {
            "byear": $("#byear").val(),
            "eyear": $("#eyear").val(),
            "startMonth": $("#startMonth").val(),
            "endMonth": $("#endMonth").val(),
            "orgCode": $("#orgList").val(),
            "type" : 1
          },
        colNames: [ '分公司名称', '商品编码', '商品名称', '库房', '期初数量', 
            '入库数量', '盘盈数量', '盘亏数量', '出库数量', '结存数量', '开始月份','结束月份'
        ],
//        colNames: [ '分公司名称', '商品编码', '商品名称', '库房', '期初数量', '期初金额',
//            '入库数量', '入库金额', '盘盈数量', '盘盈金额', '盘亏数量', '盘亏金额', '出库数量', '出库金额', '结存数量', '结存金额','开始月份','结束月份'
//        ],
        colModel: [{
            name: 'orgName',
        }, {
            name: 'drugCode',
        }, {
            name: 'drugName',
        }, {
            name: 'storageTypeStr',
        }, {
            name: 'initNumber',
        }, 
//        {
//            name: 'initMoney',
//        }, 
        {
            name: 'rukuNumber',
        }, 
//        {
//            name: 'rukuMoney',
//        }, 
        {
            name: 'panyingNumber',
        }, 
//        {
//            name: 'panyingMoney',
//        }, 
        {
            name: 'pankuiNumber',
        }, 
//        {
//            name: 'pankuiMoney',
//        }, 
        {
            name: 'chukuNumber',
        }, 
//        {
//            name: 'chukuMoney',
//        }, 
        {
            name: 'jiecunNumber',
        }, 
//        {
//            name: 'jiecunMoney',
//        }, 
        {
            name: 'startMonth',
        }, {
            name: 'endMonth',
        }],
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['initNumber','initMoney','rukuNumber','rukuMoney',
            				  'panyingNumber','panyingMoney','pankuiNumber','pankuiMoney',
            	              'chukuNumber','chukuMoney','jiecunNumber','jiecunMoney'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item, index) {

                if (item == 'rukuNumber' || item == 'initNumber' || item == "panyingNumber" || item == "pankuiNumber" || item == "chukuNumber" || item == "jiecunNumber") {
                    lastRowEle.find("td[row-describedby=" + item + "]").text(totalTablea(data, item))
                } else {

                    lastRowEle.find("td[row-describedby=" + item + "]").text(parseFloat(totalTable(data, item)).formatMoney('2', '', ',', '.'))
                }
                lastRowEle.find("td[row-describedby=" + item + "]").prop("title", "");
            });
        },
        pager: '#grid-pager',
        rownumbers: true
    });

   // 查询数据
   $('#searchBtn').bind('click', function () {
       $('#X_Table').XGrid('clearGridData');
       $('#X_Table').setGridParam({
            url: '/proxy-finance/finance/initBalance/findStockEntryDepositDetailByOrgCode',
            postData: {
              storageType: $("#storageType").val(),
              productDesc: $("#productDesc").val(),
              drugCode: $("#drugCode").val(),
              byear: $("#byear").val(),
              eyear: $("#eyear").val(),
              startMonth: $("#startMonth").val(),
              endMonth: $("#endMonth").val(),
              oldProductCode : $("#oldProductCode").val(),
              orgCode: $("#orgList").val(),
              type : 1
            }
       }).trigger('reloadGrid');
//       getTotalCount();
       
    })
            
   
   // 导出
   $('#exportBtn').bind('click', function () {
       utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
           return false;
       }).catch( () => {
           //原始处理逻辑代码
           var parames = [];
           parames.push({name: "byear", value: $("#byear").val()});
           parames.push({name: "eyear", value: $("#eyear").val()});
           parames.push({name: "startMonth", value: $("#startMonth").val()});
           parames.push({name: "endMonth", value: $("#endMonth").val()});
           parames.push({name: "storageType", value: $("#storageType").val()});
           parames.push({name: "productDesc", value: $("#productDesc").val()});
           parames.push({name: "drugCode", value: $("#drugCode").val()});
           parames.push({name: "oldProductCode", value: $("#oldProductCode").val()});
           parames.push({name: "orgCode", value: $("#orgList").val()});
           Post("/proxy-finance/finance/initBalance/exportExcelRoprtListByOrgCode", parames);
       })
   })
   
    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
        $(temp_form).remove();
    }
   
   
    // 筛选列，集成到 xgrid.js 里了
    $("#set_tb_rows").click(function () {
        $('#X_Table').XGrid('filterTableHead');
    })
})


/**
 * 渲染仓库下拉列表
 */
function initStorageTypeSelectTemplate(data) {
    var html = template('storageType-tmp', {list: data});
    document.getElementById('storageType-div').innerHTML = html;
}

/**
 * 获取仓库
 */
function initStorageTypeSelect() {
    $.post("/proxy-sysmanage/sysmanage/dict/querycommonnotpage", {type: 10},
        function(data){
            console.log(data.result);
            if (data.code == 0) {
                var _data = data.result;
                initStorageTypeSelectTemplate(_data);
            }
        }, "json");
}



/*function yearFun() {
    var year = getYear();
    WdatePicker({
        dateFmt: 'yyyy',
        minDate: '2010',
        maxDate:  year
    })
}
function startMonthFun() {
    WdatePicker({
        dateFmt: 'MM',
        realDateFmt:'MM',
        maxDate: getMaxMonth()
    })
}
function endMonthFun() {
    WdatePicker({
        dateFmt: 'MM',
        minDate: '#F{$dp.$D(\'startMonth\')}',
        maxDate: '%y-%M'
    })
}*/

//  开始年月
function startTime() {
    WdatePicker({
        dateFmt: 'yyyy-MM',
        maxDate:'#F{$dp.$D(\'eyear\')}'
    })
}

//  结束年月
function endTime() {
    WdatePicker({
        dateFmt: 'yyyy-MM',
        minDate: '#F{$dp.$D(\'byear\')}',
    })
}

/**
 * 获取当前年份
 */
function getYear() {
    var date = new Date();
    var year = date.getFullYear() + 10;
    return year;
}


/**
 * 获取最大月份
 */
function getMaxMonth() {
    var result,
        date = new Date(),
        curMonth = date.getMonth() + 1,
        endMonth = $('#endMonth').val();
    curMonth = curMonth>9?curMonth+'': '0'+ curMonth;
    if(parseFloat(curMonth)>parseFloat(endMonth)){
        result = endMonth;
    }else {
        result = curMonth;
    }
    return result;
}



$("#byear").blur(function(){
	var by = $(this).val();
	var ey = $("#eyear").val();
	if(by > ey){
		$(this).val("");
		
	}
	console.log(by)
})


$("#eyear").blur(function(){
	var ey = $(this).val();
	var by = $("#byear").val();
	if(by > ey){
		$(this).val("");
		
	}
	console.log(ey)
})



// function getTotalCount () {
//            //加载总数量
//            $.ajax({
//                url: '/proxy-finance/finance/initBalance/stockDetailSumByOrgCode',
//                dataType: 'json',
//                timeout: 8000, //6000
//                data:{
//                	 storageType: $("#storageType").val(),
//                     productDesc: $("#productDesc").val(),
//                     drugCode: $("#drugCode").val(),
//                     byear: $("#byear").val(),
//                     eyear: $("#eyear").val(),
//                     startMonth: $("#startMonth").val(),
//                     endMonth: $("#endMonth").val(),
//                     oldProductCode : $("#oldProductCode").val(),
//                     orgCode : $("#orgList").val()
//                },
//                success: function (data) {
//                 	if(data.code==0 && null != data.result){
//                    	 $("#initMoneySum").text(Number(data.result.initMoneySum).toFixed(2));
//                         $("#rukuMoneySum").text(Number(data.result.rukuMoneySum).toFixed(2));
//                         $("#panyingMoneySum").text(Number(data.result.panyingMoneySum).toFixed(2));
//                         $("#pankuiMoneySum").text(Number(data.result.pankuiMoneySum).toFixed(2));
//                         $("#chukuMoneySum").text(Number(data.result.chukuMoneySum).toFixed(2));
//                         $("#jiecunMoneySum").text(Number(data.result.jiecunMoneySum).toFixed(2));
//                	}else{
//                		  $("#initMoneySum").text('0.00');
//                          $("#rukuMoneySum").text("0.00");
//                          $("#panyingMoneySum").text("0.00");
//                          $("#pankuiMoneySum").text('0.00');
//                          $("#chukuMoneySum").text("0.00");
//                          $("#jiecunMoneySum").text("0.00");
//                	}
//                   
//                },
//                error: function () {
//                    $("#initMoneySum").text('0.00');
//                    $("#rukuMoneySum").text("0.00");
//                    $("#panyingMoneySum").text("0.00");
//                    $("#pankuiMoneySum").text('0.00');
//                    $("#chukuMoneySum").text("0.00");
//                    $("#jiecunMoneySum").text("0.00");
//                    utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
//                }
//            });
//        
//    }