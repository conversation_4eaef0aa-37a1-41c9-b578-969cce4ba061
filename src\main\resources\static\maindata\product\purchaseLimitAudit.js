$(function () {
    $('#X_Table').XGrid({
        url: "/proxy-product/product/purchaseLimit/audi/details",
        postData: {
            "recordId": $("#recordId").val()
        },
        colNames: ['','机构编码','商品编码', '商品名', '通用名', '型号/规格', '生产厂家','包装单位', '剂型', '限销状态', '限采状态', '停用状态','停（启）用原因'],
        colModel: [{
            name: 'productId',
            index: 'productId',
            hidden:true
        }, {
            name: 'orgCode',
            index: 'orgCode',
            hidden:true
        }, {
            name: 'productCode',
            index: 'productCode'
        }, {
            name: 'productName',
            index: 'productName'
        }, {
            name: 'commonName',
            index: 'commonName'
        }, {
            name: 'specifications',
            index: 'specifications'
        }, {
            name: 'manufacturerName',
            index: 'manufacturerName'
        }, {
            name: 'packingUnitValue',
            index: 'packingUnitValue'
        }, {
            name: 'dosageFormValue',
            index: 'dosageFormValue'
        }, {
            name: 'limitedPinState',
            index: 'limitedPinState',
            formatter: function (e) {
                if (e == 0) {
                    return '否'
                } else if (e == 1) {
                    return '是'
                } else {
                    return '未知'
                }
            }

        }, {
            name: 'limitedProductionState',
            index: 'limitedProductionState',
            formatter: function (e) {
                if (e == 0) {
                    return '否'
                } else if (e == 1) {
                    return '是'
                } else {
                    return '未知'
                }
            }
        }, {
            name: 'disableState',
            index: 'disableState',
            formatter: function (e) {
                if (e == 0) {
                    return '否'
                } else if (e == 1) {
                    return '是'
                } else {
                    return '未知'
                }
            }
        }, {
            name: 'disableReason',
            index: 'disableReason'
        }],
        rowNum: 0,
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager'
    });
    //是否选择限销
    if ($("#limitedPinStateYes").is(':checked')){
        $("#limitedBatchNumberDiv").show();
        $("#noticePurchaseDiv").show();
        $("#batchNumberRestrictionDiv").show();

        $("#limitedBatchNumberDiv input:radio").attr("name","limitedBatchNumber");
        $("#noticePurchase").attr("name","noticePurchase");
        $("#batchNumberRestriction").attr("name","batchNumberRestriction");
        //判断限制批号
        var limitedBatchNumber =$('[name="limitedBatchNumber"]:checked').val()
        if (limitedBatchNumber==0){
            $("#batchNumberRestrictionDiv").hide();
            $("#batchNumberRestriction").removeAttr("name");
        }else {
            $("#batchNumberRestrictionDiv").show();
            $("#batchNumberRestriction").attr("name","batchNumberRestriction");
        }
    }else {
        $("#limitedBatchNumberDiv").hide();
        $("#noticePurchaseDiv").hide();
        $("#batchNumberRestrictionDiv").hide();

        $("#limitedBatchNumberDiv input:radio").removeAttr("name");
        $("#noticePurchase").removeAttr("name");
        $("#batchNumberRestriction").removeAttr("name");
    }
})
function submitAudiInfo(status,auditOpinion){
    var submitData ={
        "id":$("#recordId").val(),
        "statues":status,
        "auditOpinion":auditOpinion,
        "taskId":$("#taskId").val()
    } ;
    var rowData = $('#X_Table').getRowData();
    var purchaseLimitSaveVo = {
        "purchaseLimitApprovalRecordVo":submitData,
        "purchaseLimitApprovalRecordDetailVos":rowData,
    };
    resultData=JSON.stringify(purchaseLimitSaveVo);
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/audi/passAudit",
        data: resultData,
        async: false,
        dataType:'json',
        contentType: "application/json",
        error: function () {
            alert("提交失败！");
        },
        success: function (data) {
            if (data.code==0){
                var msg="";
                if(status==0){
                    msg =  '恭喜审核通过';
                }
                if(status==1){
                    msg =  '驳回成功';
                }
                var taskStatus=data.result.taskStatus;
                if(taskStatus!=undefined&&!taskStatus){
                    msg= data.result.msg
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                console.log(data.result);
                utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
            }
        }
    });
}
$('.auditPass').on('click', function () {
    var status=this.getAttribute("status");
    var title="审核通过";
    if(status==1){
        title="审核不通过";
        $('#opinion').show();
    }else {
        $('#opinion').hide();
    }
    utils.dialog({
        title: title,
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            //审核不通过，意见不能为空
            if (status==1) {
                if ($("#auditOpinion").val()==""){
                    utils.dialog({content: '审批意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
            }
            submitAudiInfo(status,$("#auditOpinion").val());
        },
        cancelValue: '取消',
        cancel: function () {
            $("#auditOpinion").val("");
        }
    }).showModal();
});

$(function () {
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+$("#processId").val(),
        async: false,
        error: function () {
            utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
        },
        success: function (data) {
            if (data.code==0){
                $('.flow').process(data.result);
            }else {
                utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
            }
        }
    });
})