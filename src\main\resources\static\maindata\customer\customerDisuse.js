


$(function () {

    //显示流程图
    var key = $("#key").val();
    initApprovalFlowChart(key);

    $('#table').XGrid({
        url: "/proxy-customer/customer/disuse/toList",
        colNames: ['', '申请日期', '机构', '申请人', '单据编号', '客户编码', '客户名称', '业务类型', '申请原因','审核状态','审核完成时间'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            hidden: true,
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'applicationDate',
            index: 'applicationDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'orgCodeName',
            index: 'orgCodeName'
        }, {
            name: 'applicantName',
            index: 'applicantName'
        }, {
            name: 'applicationNumber',
            index: 'applicationNumber'
        }, {
            name: 'customerCode',
            index: 'customerCode'
        }, {
            name: 'customerName',
            index: 'customerName'
        }, {
            name: 'serviceType',
            index: 'serviceType',
            formatter: function (e) {
                if (e == '0') {
                    return '停用'
                } else if (e == '1') {
                    return '解除停用'
                }
            }
        }, {
            name: 'pursueReason',
            index: 'pursueReason',
            width: 150
        }, {
            name: 'auditStatus',
            index: 'auditStatus',
            formatter: function (e) {
                if (e == '1') {
                    return '录入中'
                } else if (e == '2') {
                    return '审核中'
                }else if (e == '3') {
                    return '审核通过'
                }else if (e == '4') {
                    return '审核不通过'
                }
            },
            unformat: function (e) {
                if (e == '录入中') {
                    return '1'
                } else if (e == '审核中') {
                    return '2'
                }else if (e == '审核通过') {
                    return '3'
                }else if (e == '审核不通过') {
                    return '4'
                }
            }
        }, {
                name: 'auditTime',
                index: 'auditTime',
                formatter: function (e) {
                    if(e){
                        return format(e);
                    }
                },
                width:200
        },{
            name: 'applicant',
            index: 'applicant',
            hidden: true
        }

        ],
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        ondblClickRow: function (id, c, a, b) {
            console.log('双击行事件', id, c, a, b);
            let auditStatus = a.auditStatus;
            let applicationNumber = a.applicationNumber;
            let processId = a.approvalProcessId;
            if(auditStatus==1){
                var loginUserId = $("#loginUserId").val();
                if(a.applicant!=loginUserId){
                    utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
            }
            utils.openTabs("customerDisuseDetail", "客户停用详情", "/proxy-customer/customer/disuse/toDetailedSkip?id="+id+'&auditStatus='+auditStatus+'&applicationNumber=' +applicationNumber+'&processId='+ processId);
            // location.href='/proxy-customer/customer/disuse/toDetailedSkip?id='+id
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);

        },
        pager: '#grid-pager'
    });

    //删除
    $('#delete').on('click', function () {
        var selData = $('#table').XGrid("getSeleRow");
        if(!selData.length){
            utils.dialog({title: '提示',content:'请至少选择一行！',timeout:3000}).show();
            return false;
        }
        $.each(selData, function (index, item) {
            if ($('#loginUserId').val() != item.applicant) {
                utils.dialog({title:'温馨提示:',content:'只允许删除自己录入的单据！',timeout: 3000}).show();
                return false;
            }

            if (item.auditStatus == 1) { // 审核中
                utils.dialog({
                    title: '温馨提示:',
                    content: '确定要删除选中的单据吗？',
                    width: 300,
                    height: 50,
                    okValue: '【是】',
                    ok: function () {
                        $.ajax({
                            type: "GET",
                            url: "/proxy-customer/customer/disuse/updateCustomerDisuse?applicationNumber="+item.applicationNumber ,
                            async: false,
                            success: function (data) {
                                if (data.code==0&&data.result!=null){
                                    console.log(data.result);
                                    console.log("更新 删除成功");
                                    // window.location.reload();
                                    //刷新列表
                                    $('#table').XGrid('setGridParam', {
                                        postData: {
                                            "orgCode": $("#orgCode").val(),
                                            "applicationNumber":$("#applicationNumber").val(),
                                            "customerName":$("#customerName").val(),
                                            "auditStatus":$("#auditStatus").val()
                                        },page:1
                                    }).trigger('reloadGrid');

                                }
                            },
                            error: function (data) {
                                if (data.code==1&&data.result!=null){
                                    utils.dialog({title:'温馨提示:',content:'只允许删除自己录入的单据！',timeout: 3000}).show();
                                    return false;
                                }
                            }
                        });
                    },
                    cancelValue: '【否】',
                    cancel: function () {
                    }
                }).showModal();

            } else {
                utils.dialog({title:'温馨提示:',content:'只允许删除录入中的单据！',timeout: 3000}).show();
                return false;
            }
        });
    });

    $("#SearchBtn").on("click", function () {
        $('#table').XGrid('setGridParam', {
            postData: {
                "orgCode": $("#orgCode option:selected").val(),
                "applicationNumber":$("#applicationNumber").val(),
                "customerName":$("#customerName").val(),
                "auditStatus":$("#auditStatus").val()
            },page:1
        }).trigger('reloadGrid');
    });
    $("#appBtn").on("click", function () {
        console.log('申请');
        utils.openTabs("appBtn", "客户停用详情", "/proxy-customer/customer/disuse/toAddCustomerDisableApprovalRecord");
    });

})

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }

/**
 * 流程图显示
 */
function  initApprovalFlowChart(key) {
    //获取审核流程数据
    var key = $("#key").val();
    const url = "/proxy-customer/customer/disuse/queryTotle?key="+key;
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}
