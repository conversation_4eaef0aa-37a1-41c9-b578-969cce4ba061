﻿ var getData;
 $(function () {
     function dateFormat(time, format) {
         var t = new Date(parseInt(time,10));
         var tf = function (i) {
             return (i < 10 ? '0' : '') + i;
         };
         return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
             switch (a) {
                 case 'yyyy':
                     return tf(t.getFullYear());
                     break;
                 case 'MM':
                     return tf(t.getMonth() + 1);
                     break;
                 case 'mm':
                     return tf(t.getMinutes());
                     break;
                 case 'dd':
                     return tf(t.getDate());
                     break;
                 case 'HH':
                     return tf(t.getHours());
                     break;
                 case 'ss':
                     return tf(t.getSeconds());
                     break;
             }
         });
     }

     /* 数据渲染 */
     getData = function (type,data) {
         var box_html = '';
         data.inStorages = (data.inStorages.length != 0) ?data.inStorages : [{}];
         data.inStorages.forEach(function (item,key) {
             if(key%15 === 0){
                 box_html += `
                    <div class="header">${data.orgName}商品质量档案</div>
                        <div class="content">
                          <div class="file_about">
                            <span>建档人：${data.applicantName}</span>
                            <span>建档日期：${data.auditTime?dateFormat(data.auditTime,'yyyy-MM-dd'):''}</span>
                            <span>单据编号：${data.applicationCode}</span>
                          </div>
                      <div class="file_detail">
                      <table class="file_table">
                          <tbody>
                            <tr class="h">
                              <td colspan="7">商品通用名称</td>
                              <td colspan="21">${data.commonName}</td>
                            </tr>
                            <tr class="h">
                              <td colspan="7">品种类别</td>
                              <td colspan="7">${data.varietyType}</td>
                              <td colspan="7">规格/型号</td>
                              <td colspan="7">${data.specifications}</td>
                            </tr>
                            <tr class="h">
                              <td colspan="7">生产企业</td>
                              <td colspan="7">${data.manufacturerName}</td>
                              <td colspan="7">有 效 期</td>
                              <td colspan="7">${data.indate}` + `${data.indateType}</td>
                            </tr>
                            <tr class="h">
                              <td colspan="7">备案号</td>
                              <td colspan="7">${data.approvalNumber}</td>
                              <td colspan="7">商品包装、标签和说明书情况</td>
                              <td colspan="7">${data.productExplain}</td>
                            </tr>
                            <tr class="h">
                              <td colspan="7">质量标准</td>
                              <td colspan="7">${data.qualityStandard}</td>
                              <td colspan="7">储藏条件</td>
                              <td colspan="7">${data.storageConditionsName}</td>
                            </tr>
                            <tr class="h">
                              <td colspan="7">建档原因及目的</td>
                              <td colspan="7">${data.reasonAndPurpose}</td>
                              <td colspan="7">首批进货日期</td>
                              <td colspan="7">${data.firstPurchaseInStorageTime}</td>
                            </tr>
                            <tr class="h">
                              <td colspan="28">进货质量评审情况</td>
                            </tr>
                            <tr>
                              <td colspan="4">进货日期</td>
                              <td colspan="4">产品批号/序列号</td>
                              <td colspan="4">进货数量</td>
                              <td colspan="4">质量状况</td>
                              <td colspan="4">原因分析</td>
                              <td colspan="4">处理措施</td>
                              <td colspan="4">备注</td>
                            </tr>
                `;
                 /* 表格部分*/
                 if(JSON.stringify(item) != "{}"){
	                 for (var i = 0; i < 15; i++) {
	                     var i_item = data.inStorages[key + i];
	                     if(i_item){
	                         box_html +=`
	                            <tr>
	                              <td colspan="4">${i_item.storeTime}</td>
	                              <td colspan="4">${i_item.productBatchNo}</td>
	                              <td colspan="4">${i_item.productPackInStoreCount}</td>
	                              <td colspan="4">${i_item.qualityStatus}</td>
	                              <td colspan="4">${i_item.causeAnalysis||''}</td>
	                              <td colspan="4">${i_item.precautions||''}</td>
	                              <td colspan="4">${i_item.remarks||''}</td>
	                            </tr>`;
	                     }else {
	                         box_html +=`
	                            <tr>
	                              <td colspan="4"></td>
	                              <td colspan="4"></td>
	                              <td colspan="4"></td>
	                              <td colspan="4"></td>
	                              <td colspan="4"></td>
	                              <td colspan="4"></td>
	                              <td colspan="4"></td>
	                            </tr>`;
	                     }
	                 }
                 }
                 box_html +='</tbody></table></div><div style="page-break-after:always"></div>';
             }
         });

         $("#box").html(box_html);

         if(type == 0){
             /* 打印预览 */
             utils.dialog({
                 title:'预览',
                 //width: 600,
                 height: $(window.parent).height() * 0.8,
                 content:$('#big_box').html()
             }).showModal();
             window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
         }else if(type == 1){
             /* 打印 */
             $("#big_box").jqprint({
                 globalStyles: true, //是否包含父文档的样式，默认为true
                 mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                 stylesheet: null, //外部样式表的URL地址，默认为null
                 noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                 iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                 append: null, //将内容添加到打印内容的后面
                 prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                 deferred: $.Deferred() //回调函数
             });
         }
     }
 });