//其他相关功能修改 1.机构首营
var largeCategoryArray = new Array();
largeCategoryArray[0] = "中药饮片";
largeCategoryArray[1] = "食品";
largeCategoryArray[2] = "保健食品";
largeCategoryArray[3] = "中药材";
largeCategoryArray[4] = "药食同源";
//let base_largeCategoryVal = '', base_largeCategory = '';
$(function () {
    // 页面加载成功后，尝试关闭无权限字段
    tryDisabledNoPermissionField()
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    $(".keyConservation").click(function(){
        if ($(this).val()==1){
            $("#maintenancePeriod").val("30");
        }else {
            $("#maintenancePeriod").val("90");
        }
    });
    //tabs输入框只能输入数字限制
    $(document).on("input",".tagdiv .bootstrap-tagsinput input",function(){
       this.value=this.value.replace(/\D/g,'');
        $(this).attr("maxlength",15);
    });
    $(document).on("input","#inputdia input",function(){
       //this.value=this.value.replace(/\D/g,'');
        if($(this).hasClass('NAN_TYPE_CLASS')){ // 允许汉字类型

        }else{
            this.value=this.value.replace(/\D/g,'');
        }
       $(this).attr("maxlength",15);
    });
    //限购数量、安全库存、库存上线只可输入0或者1以上数字
    $('.filterNumber').on('focus',function () {
        $(this).attr('maxlength',9);
    }).on('input',function(){
        var value=$.trim(this.value);
        var reg=/^0/;
        if(reg.test(value))
        {
            this.value=0;
            return;
        }
        this.value=value.replace(/[^\d]/g,'');
    });

    /**
     *  V1.7.2.1
     *  增加限制： 安全库存天数< 库存上线天数， 安全库存天数【10,20】天
     *  库存上线天数【30,40】天
     *  失去焦点时判断。不对的时候要 给出提示，提示出合法值的范围
     */
    let safetyInventoryObj = {
        inpName: 'safetyInventory',
        tagName: '安全库存天数',
        validityVal: [10,20]
    }
    if(validityCheck(safetyInventoryObj)){
        return false;
    }
    let inventoryUpperLimitObj = {
        inpName: 'inventoryUpperLimit',
        tagName: '库存上限天数',
        validityVal: [30,40]
    }
    if(validityCheck(inventoryUpperLimitObj)){
        return false;
    }

    //批准文件证书类型设置不可重复选择
    $("#X_Table").on("mousedown",'select[name="batchName"]',function () {
        /**
         *RM 2018-10-10
         * 批准文件 批件名称选中的不再被禁用
         */
        //selCannotRepeatChoice(this,'X_Table','batchName');
    })
    var baseProductId=$("#baseProductId").val();//主数据主键ID
    var baseApplicationCode=$("#baseApplicationCode").val();//主数据对应单据编码
    var applicationCode=$("#applicationCode").val();//首营申请对应单据编号
    var orgaProductId=$("#orgaProductId").val();//机构供应商id
    var orgCode=$("#orgCode").val();//机构供应商机构号


    // //采购分类设置默认值
    if($("#purchaseType").val()==""){
        $("#purchaseType").val("C");
    }
    // 修改默认值为 3
    $('#reviewStatus').val('3')
    $("#purchaseType").attr('disabled','disabled');
    //批准文件加载
    var scopeOfOperation= $("#scopeOfOperation").val();
    localBatchName(1);
    //业务类型属性
    $('#X_Table_Channel').XGrid({
        url:"/proxy-product/product/productChannel/toList?type=0&correlationId="+orgaProductId,
        colNames: ['id','生效状态','类别', '<i class="i-red">*</i>类别', '<i class="i-red">*</i>业务类型','采购员id','<i class="i-red">*</i>采购员', '<i class="i-red">*</i>供货价', '<i class="i-red">*</i>APP售价','智鹿总部采购价','连锁APP售价','荷叶大药房采购价',
            '<i class="i-red">*</i>票面毛利率', '<i class="i-red">*</i>APP销售价是否维价','出库价维价关联客户类型','出库价维价关联客户类型ARR', '建议终端售价', '<i class="i-red">*</i>终端零售价是否维价','实时综合毛利率区间','毛利率等级','<i class="i-red">*</i>生效状态'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true
            },{
                name: 'effectiveState',
                index: 'effectiveState',
                hidden:true
            },{
                name: 'channelType',
                index: 'channelType',
                hidden:true
            },{
                name: 'channelTypeVal',
                index: 'channelTypeVal'
            },{
                name: 'channelId',
                index: 'channelId'
            },{
                name: 'buyer',
                index: 'buyer',
                hidden:true
            },{
                name: 'buyerVal',
                index: 'buyerVal',
                rowtype: '#buyerVal',
            },{
                name: 'supplyPrice',
                index: 'supplyPrice',
                rowtype: '#supplyPrice',
            },{
                name: 'appPrice',
                index: 'appPrice',
                rowtype: '#appPrice',
            },{
                name: 'zhiluPrice',
                index: 'zhiluPrice',
                rowtype: '#zhiluPrice',
            }, {
                name: 'chainGuidePrice',
                index: 'chainGuidePrice',
                rowtype: '#chainGuidePrice',
            },{
                name: 'heyePrice',
                index: 'heyePrice',
                rowtype: '#heyePrice',
            },{
                name: 'parGrossMargin',
                index: 'parGrossMargin',
                rowtype: '#parGrossMargin',
            },{
                name: 'dimensionSalesPriceYn',
                index: 'dimensionSalesPriceYn',
                rowtype: '#dimensionSalesPriceYn',
            },{
                name: 'pharmacyType',
                index: 'pharmacyType',
                width: 170,
                rowtype: '#pharmacyType',
            },{
                name: 'pharmacyTypeArr',
                index: 'pharmacyTypeArr',
                hidden: true,
            },{
                name: 'terminalPrice',
                index: 'terminalPrice',
                rowtype: '#terminalPrice',
            },{
                name: 'dimensionTerminalPriceYn',
                index: 'dimensionTerminalPriceYn',
                rowtype: '#dimensionTerminalPriceYn',
            },{
                name: 'grossMarginRange',
                index: 'grossMarginRange',
                rowtype: '#grossMarginRange',
            },{
                name: 'grossMarginGrade',
                index: 'grossMarginGrade',
                rowtype: '#grossMarginGrade',
            },{
                name:'effectiveStateVal',
                index:'effectiveStateVal'
            }],
        rowNum: 10,
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        // key:'channelId',
        gridComplete:function (res) {
            // 初始化采购员模糊搜索
            var rowData = $("#X_Table_Channel").XGrid('getRowData');
            if(rowData && rowData.length){
                var orgCode = $("#applicantOrgCode").val();
                rowData.forEach(function (item) {
                    $("#"+item.id+" input[name=buyerVal]").Autocomplete({
                        serviceUrl: "/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode", //异步请求
                        paramName: 'userName',//查询参数，默认 query
                        params:{"orgCode":orgCode},
                        dataType: 'json',
                        querydelimiter:"-",
                        minChars: '0', //触发自动匹配的最小字符数
                        maxHeight: '300', //默认300高度
                        dataReader:{list:'result',data:"id",value:"userName"},
                        triggerSelectOnValidInput: false, // 必选
                        showNoSuggestionNotice: true, //显示查无结果的container
                        noSuggestionNotice: '查询无结果',//查无结果的提示语
                        onSelect: function (result) {
                            $("#X_Table_Channel").XGrid('setRowData',item.id,{buyer:result.data})
                        },
                        onNoneSelect: function (params, suggestions) {
                            $("#X_Table_Channel").XGrid('setRowData',item.id,{buyer:''});
                            $("#"+item.id+" input[name=buyerVal]").val('');
                        }
                    });

                    let arr = item['pharmacyTypeArr']
                    let dimensionSalesPriceYnVal = item['dimensionSalesPriceYn']

                    let _node = $('#X_Table_Channel #'+item.id+' [name=pharmacyType]')
                    for (let i = 0; i < _node.length; i++) {
                        if(arr.indexOf($(_node[i]).val()) > -1) {
                            $(_node[i]).prop('checked', true)
                        }
                        if (dimensionSalesPriceYnVal == 0) {
                            $(_node[i]).prop('disabled', true)
                        }
                    }
                })
            }
        }
    });
    //if("" != orgaProductId && "" != orgCode){
	    $('#X_Table').XGrid({
	        url:"/proxy-product/product/productApprovalFile/toList?type=0&correlationId="+orgaProductId+"&orgCode="+orgCode,
	        colNames: ['','<i class="i-red">*</i>批件名称', '<i class="i-red">*</i>批件编号', '<i class="i-red">*</i>签发日期', '<i class="i-red">*</i>有效期至', '附件','附件数据','操作'],
	        colModel: [
	            {
	                name: 'id',
	                index: 'id',
	                hidden:true
	            },
	            {
	                name: 'batchName',
	                index: 'batchName',
	                rowtype: '#batchName',
	            }, {
	                name: 'batchCode',
	                index: 'batchCode',
	                rowtype: '#batchCode',
	            }, {
	                name: 'issueDateStr',
	                index: 'issueDateStr',
	                rowtype: '#issueDate'
	            }, {
	                name: 'validityDateStr',
	                index: 'validityDateStr',
	                rowtype: '#validityDate'
	            }, {
	                name: 'enclosure',
	                index: 'enclosure',
	                formatter:function (value) {
	                    var str='无';
	                    if(value)
	                    {
	                        str ='<a href="javascript:;" onclick="showImg(this);">'+value+'</a>';
	                    }
	                    return str;
	                },
	                unformat: function (e) {
	                    e=e.replace(/<[^>]+>/g,'');
	                    if(e=="无"){
	                        e=0;
	                    }
	                    return e;
	                }
	            },{
	                name:'enclosureList',
	                index:'enclosureList',
	                hidden:true,
	                formatter:function (value) {
	                    if(value)
	                    {
	                        return JSON.stringify(value);
	                    }
	                    return JSON.stringify([]);
	                }
	            },{
                    name:'operation',
                    index:'operation',
                    formatter:function (value) {
                        return '<button type="button" class="btn btn-info uploadOneFile_btn">上传附件</button>';
                    },
                    unformat: function (e) {
                        return null;
                    }
                }],
	        rowNum: 10,
	        altRows: true,//设置为交替行表格,默认为false
	        rownumbers: true,
	        gridComplete: function () {
	            tryDisabledNoPermissionField()
	           /* if(baseApplicationCode!=applicationCode){//初始化只读处理
	                $('#X_Table input,#X_Table select').attr('disabled','disabled');
	            }*/
	            initFileSelected();
	        }
	    });
    //}




    /*首营申请修改记录*/
    $('#firstEditRecordTable').XGrid({
        url:"/proxy-product/product/productFirst/getFirstEditRecords?applicationCode="+$("#applicationCode").val(),
        colNames: ['变更时间', '变更人', '单据编号', '变更明细'],
        colModel: [{
            name: 'createTimeStr',
            index: 'createTimeStr'
        }, {
            name: 'createUserValue',
            index: 'createUserValue'
        }, {
            name: 'applicationCode',
            index: 'applicationCode'
        },{
            name: 'changeDetails',
            index: 'changeDetails',
            formatter:function (value,b,rowData) {
                var arr=value.split(';');
                var html='<div class="modifyTheRecord">\n' +
                    '<div class="itemContent"><div class="mrLeft">';
                for(var i=0;i<arr.length;i++)
                {
                    html+='<p>'+arr[i]+'</p>';
                }
                html+='</div></div>' +
                    '\t\t<div class="mrRight">\n' +
                    '\t\t\t<a href="javascript:;" class="moreBtn">展开</a>\n' +
                    '\t\t</div>\n' +
                    '\t</div>\n';
                return html;
            }
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        pager:'#firstEditRecord_page',
        rownumbers: true,
        gridComplete:function () {

        }
    });

    //修改记录申请明细展开收起按钮
    $("#firstEditRecordTable").on("click",".moreBtn",function(){
        var type=$.trim($(this).text());
        var tr=$(this).parents("tr");
        var innerHeiht=0;
        if(type == '展开')
        {
            innerHeiht=tr.find(".mrLeft").innerHeight();
            $(this).html('收起');
        }else if(type == '收起'){
            innerHeiht = 40;
            $(this).html('展开');
        }
        if(innerHeiht < 40){
            innerHeiht=40;
        }
        tr.find(".modifyTheRecord .itemContent").animate({
            height:innerHeiht
        },500)
    })
    //批量上传
    $("#batchUpload").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#X_Table');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("select[name='batchName'] option:selected");
            typeList.push({
                text:sel.text(),
                value:sel.val(),
                lineNum:i
            });
            //console.log(sel.val())
            //添加已存在附件
            if(rowData[i].enclosureList.length > 0){
                rowData[i].enclosureList=JSON.parse(rowData[i].enclosureList);
                for(var j=0;j<rowData[i].enclosureList.length;j++){
                    rowData[i].enclosureList[j].type=sel.val();
                    rowData[i].enclosureList[j].lineNum = i;
                }
                eChoImgList = eChoImgList.concat(rowData[i].enclosureList);
            }
        }
        if(typeList.length < 1){
            utils.dialog({content: '请先新增一行并选择批准文件', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        $(this).upLoad({
            maxSize:1.5*1024 * 1024,
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'enclosureList':[]});
                            $table.setRowData(id,{'enclosure':''});
                        }
                    })
                    return false;
                }
                //存放数据
                //files
                // for(var name in data)
                // {
                //     var list=data[name];
                //     for(var i=0;i<rowData.length;i++)
                //     {
                //         if(rowData[i].batchName == name)
                //         {
                //             var trId=$table.find("tr").eq(i+1).attr('id');
                //             $table.setRowData(trId,{'enclosureList':list});//存储时只能存字符串，取值时用JSON.parse转换
                //             $table.setRowData(trId,{'enclosure':(list.length == 0?'':list.length)});
                //             break;
                //         }
                //     }
                // }
                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData[j]['batchName'] == data[i].type && j == data[i].lineNum ){
                            l.push(list);
                        }
                    }
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    $table.setRowData(trId,{'enclosureList':l});
                    $table.setRowData(trId,{'enclosure':(l.length == 0?'':l.length)});
                }
            }
        },true);
    });
    //单条上传
    $("#X_Table").on("click","button.uploadOneFile_btn",function (e) {
        var $eleTr = $(e.target).parents("tr");
        var eleId = $eleTr.attr('id');
        var eleIineNum = $eleTr.find("td[row-describedby='rn']").text();
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#X_Table');
        var eleRowData=$table.getRowData(eleId);
        var sel=$eleTr.find("select[name='batchName'] option:selected");
        typeList.push({
            text:sel.text(),
            value:sel.val(),
            lineNum:eleIineNum
        });
        if(eleRowData.enclosureList.length > 0){
            eleRowData.enclosureList=JSON.parse(eleRowData.enclosureList);
            for(var j=0;j<eleRowData.enclosureList.length;j++){
                eleRowData.enclosureList[j].type=sel.val();
                eleRowData.enclosureList[j].lineNum = eleIineNum;
            }
            eChoImgList = eChoImgList.concat(eleRowData.enclosureList);
        }
        $(this).upLoad({
            maxSize:1.5*1024 * 1024,
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                if($.isEmptyObject(data)){
                    $table.setRowData(eleId,{'enclosureList':[]});
                    $table.setRowData(eleId,{'enclosure':''});
                    return false;
                }
                var lary = [];
                for(let i = 0; i<data.length; i++){
                    var list = data[i];
                    if(eleRowData['batchName'] == data[i].type && eleIineNum == data[i].lineNum ){
                        lary.push(list);
                    }
                }
                $table.setRowData(eleId,{'enclosureList':lary});
                $table.setRowData(eleId,{'enclosure':(lary.length == 0?'':lary.length)});
            }
        },true);
    });

    //初始化只读处理
    if(baseApplicationCode!=applicationCode){//引用主数据
        readOnly();
        $("#auditNoPass").show();
    }
    //首营新增商品名可输入
    if(baseProductId==""){
        //首营新增可输入商品名查询
        $("#search_commodity").removeAttr('disabled');
        $("#search_commodity").attr("readonly",true);
    }else {
        loadProductInstruction(baseProductId);
    }
    //首营申请默认值
    radioDefaultChecked();
    //根据是否委托判断委托厂家是否显示
    $("input[name='entrustmentProduction']").click(function(){
        if ($(this).val()==0){
            $("#entrustmentManufacturer").val("");
            $("#entrustmentManufacturerVal").val("");
            $(".entManufacturerDiv").hide();
            $('input[name=entrustmentManufacturerVal]')['addClass'](' ignore');
        }else {
            $(".entManufacturerDiv").show();
            if($('#largeCategoryVal').attr('data-value') != '中药饮片'){
                $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>受托厂家')
                $('input[name=entrustmentManufacturerVal]')['addClass'](' {validate:{ required :true}}');
                $('input[name=entrustmentManufacturerVal]')['removeClass']('ignore');
            }

        }
    })
    var rowNumber=1;
    $("#addRowData").on("click",function () {
        rowNumber++;
        $('#X_Table').addRowData({id:rowNumber});
        initFileSelected();
    });
    $("#deleRow").on("click",function () {
        var selectRow = $('#X_Table').XGrid('getSeleRow');
        if (!selectRow) {
            utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
        } else {
            utils.dialog({
                title:"提示",
                width:300,
                height:30,
                okValue: '确定',
                content: "确定删除此条记录?",
                ok: function () {
                    $('#X_Table').XGrid('delRowData', selectRow.id);
                    utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        }

    });
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //多选框选中
    loadData("keyConservationCategories");
    loadData("specialAttributes");
    loadData("storageAttribute");
    // loadData("operatingCustomers");
    // loadData("specialProvision");
    function loadData(key) {
        var tagName, type, arr, thisVal;
        var value = $("#"+key).val();
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (type == 'checkbox') {
                if ($.type(value) == 'array') {
                    arr = value;
                } else if ($.type(value) == 'string') {
                    arr = value.split(',');
                }
                for (var i = 0; i < arr.length; i++) {
                    if (thisVal == arr[i]) {
                        $(this).attr('checked', true);
                        break;
                    }
                }
            }
        });
    }
    // 温度范围特殊处理
    var temperatureRange = $('#temperatureRanges').val();
    if(temperatureRange!=null){
        var tempArr = temperatureRange .split(',');
        for(var i=0;i<tempArr.length;i++){
            if(i==0){
                $('#temperatureRange1').val(tempArr[0]);
            }
            if(i==1){
                $('#temperatureRange2').val(tempArr[1]);
            }
        }
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //删除草稿
    $("#delData").on("click", function () {
        var d =  dialog({
            title: "提示",
            content: "是否确定删除本草稿？",
            width:300,
            height:30,
            okValue: '确定',
            ok: function () {
                var applicationCode = $("#applicationCode").val();
                $.ajax({
                    type: "POST",
                    url: "/proxy-product/product/productFirst/delete?applicationCode="+applicationCode,
                    async: false,
                    success: function (data) {
                        if (data.code==0&&data.result!=null){
                            utils.closeTab();
                        }
                    },
                    error: function () {}
                });
                return false;
            },
            cancelValue: '取消',
            cancel: function () {
                d.close().remove();
                return false;
            }
        }).showModal();
    });
    //保存草稿
    $("#saveRowData").on("click", function () {
        $("#statues").val(0);
        butSubmint();
    });

    //提交审核
    $("#submitAssert").on("click", function () {
        //提交前验证
        var batchNames = $('#X_Table').find('select[name=batchName]');
        $.each(batchNames,function (index,item) {
            if(item.value == '0'){ //批件名称选中项为  商品附件时
                var arr = $(item).parents('td').nextAll().find('input');
                $.each(arr,function (ind,el) {
                    $(el).removeClass('{validate:{ required :true}}')
                })
            }
        })
      // 仅在质管员页面对批件进行校验。
      if ($("#zhiGuanUpdateFlag").val()){
        //批准文件有效期校验
        var $table=$('#X_Table');
        var rowdatas=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        var nowtime = new Date(new Date().Format("yyyy-MM-dd"));
        var validityflag=false;
        var selVal="";
        //验证批准文件
        if (!rowdatas || rowdatas.length <= 0) {
            utils.dialog({content: '批准文件没有录入，请录入后再提交', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        let resStatus = inputValidate()
        if (!resStatus) return false
        $.each(rowdatas,function (index,item) {
            //把字符串格式转化为日期类
            if(item.validityDateStr!="") {
                var issueDate = new Date(item.validityDateStr);
                if (nowtime >= issueDate) { //批准文件有效期至过期
                    selVal=$tr.eq(index).find("select[name='batchName'] option:selected").text();
                    validityflag=true;
                    return false;
                }
            }
        })
        if(validityflag){
            utils.dialog({
                title: '提示',
                content: '批准文件-'+selVal+'-已过期，确认继续提交吗？',
                width: 300,
                height: 30,
                okValue: '确定',
                ok: function () {finalSub()}
            }).showModal();
            return false
        }
      }
        //校验药店关联类型
        let tableData = $('#X_Table_Channel').getRowData()
        var flag = false;
        $(tableData).each((index, item) => {
            let checkval = []
            let _node = $('#X_Table_Channel #' + item.id + ' [name=pharmacyType]')
            for (let i = 0; i < _node.length; i++) {
                if ($(_node[i]).prop('checked')) {
                    checkval.push($(_node[i]).val())
                }
            }
            if(item.dimensionSalesPriceYn=='1'&&checkval.join()==''){
                flag=true;
                return false;
            }
        })
        if(flag){
            utils.dialog({
                title: '提示',
                content: '出库价维价关联客户类型必填',
                width:300,
                height:30,
                okValue: '确定',
                ok: function () {
                }
            }).showModal()
            return false;
        }
        finalSub()
    });

    /**
     * 判断商品大类和所属经营范围。在批准文件中有没有录入相应的信息
     * @returns {boolean}
     */
    function inputValidate() {
        let _resObj = {}, _resStatus = true
        const tableData = $('#X_Table').XGrid('getRowData')
        const targetData = tableData.map(item => {
            return {
                selectedValId: item['batchName']
            }
        })
        // 商品大类  关联判断
        _resObj = approvalInputValidation(targetData, $('[name=largeCategoryVal]').val())
        if (JSON.stringify(_resObj) != '{}') {
            utils.dialog({
                title: '提示',
                content: _resObj.message,
                okValue: '确定',
                ok: function () {
                }
            }).showModal()
            _resStatus = false
            return _resStatus
        }
        // 所属经营范围  关联判断
        if ($('[name=largeCategoryVal]').val() != '赠品') {
            let _selectedVal = null, _selectedValId = null
            let scopeOfOperationId = $('[name=scopeOfOperation]').val(),
                scopeOfOperationVal = $('[name=scopeOfOperationVal]').val()
            if (scopeOfOperationId.indexOf(',') > -1) {
                _selectedValId = scopeOfOperationId.split(',')
            } else {
                _selectedValId = [scopeOfOperationId]
            }
            for (let i = 0; i < _selectedValId.length; i++) {
                _resObj = approvalInputValidation(targetData, _selectedValId[i])
                if (JSON.stringify(_resObj) != '{}') {
                    utils.dialog({
                        title: '提示',
                        content: _resObj.message,
                        okValue: '确定',
                        ok: function () {
                        }
                    }).showModal()
                    _resStatus = false
                    return _resStatus
                }
                // console.log('批件校验通过');
            }
        }
        return _resStatus
    }
    function finalSub() {
        /**
         * RM 2018-10-25
         * 当商品大类选中项为中药饮片时，有些内容转为非必填，有些内容转为必填，
         * 详细内容见  商品资料变更申请
         */
        var isZYYP_text = ($('#largeCategoryVal').attr('data-value') == '中药饮片'),
            true_zYYP = ($('#scopeOfOperationVal').attr('data-value') == '中药饮片');
        $('input[name=qualityStandard]')[isZYYP_text?'addClass':'removeClass'](' {validate:{ required :true}}'); // 质量标准
        $('input[name=qualityStandard]')[isZYYP_text?'removeClass':'addClass']('ignore'); //当 商品大类非中药饮片时，忽略 质量标准的校验
        if(true_zYYP){
            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
        }else{
            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('产地') // 产地
        }
        if((isZYYP_text || true_zYYP) && $('input[name=producingArea]').prev('div').find('span').length < 1){ // 产地
            utils.dialog({
                title:'提示',
                content:'产地不能为空',
                width:300,
                height:30,
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
        }
        if(isZYYP_text && $('input[name=smallPackageBarCode]').prev('div').find('span').length < 1){ // 小包装条码
            utils.dialog({
                title:'提示',
                content:'小包装条码不能为空',
                width:300,
                height:30,
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
        }

        // 要忽略校验的ID   '有效期'      ,'养护周期'
        var id_ignoreArr = ['indateType','maintenancePeriod'];
        // 要忽略校验的NAME                   //批准文号      ,标准库ID          ,是否委托   ,有效期,
        var inpName_ignoreArr = ['approvalNumber','standardProductId','entrustmentProduction','indate'];
        add_remove_require(id_ignoreArr,'TYPE_ID');
        add_remove_require(inpName_ignoreArr,'TYPE_NAME');
        function add_remove_require(arr,arrType) {
            $(arr).each(function (i,v) {
                if(arrType == 'TYPE_ID'){
                    $('#'+v)[isZYYP_text?'addClass':'removeClass']('ignore');
                }else{
                    $('input[name='+v+']')[isZYYP_text?'addClass':'removeClass']('ignore');
                }
            })
        }

        var bool = validform("productBaseInfoVo").form() && validform("productOrganizationVo").form()
            && validform("approvalFileVo").form()&&validform("productChannelVo").form();
        if (bool){
            $("#statues").val(1);
            //进项税率
            if($("input[name=salesRate]:checked").length < 1) {
                utils.dialog({content: '请选择进项税率', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            //销项税率
            if($("input[name=entryTaxRate]:checked").length < 1) {
                utils.dialog({content: '请选择销项税率', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            //验证说明书
            if($(".attributeList").length > 0)
            {
                var flag=false;
                $(".attributeList").each(function () {
                    var controlTypes=$.trim($(this).find(".controlTypes").val());
                    var attributeRequired=$.trim($(this).find('.attributeRequired').val());
                    if(attributeRequired == '1')
                    {
                        switch (controlTypes)
                        {
                            case '1':
                                //复选框
                                var len=$(this).find('input[type="checkbox"]:checked').length;
                                if(len < 1)
                                {
                                    flag=true;
                                    return false;
                                }
                                break;
                            case '0':
                                //文本框
                                var val=$.trim($(this).find('textarea').val());
                                if(val == '' || !val)
                                {
                                    flag=true;
                                    return false;
                                }
                                break;
                        }
                    }
                })
                if(flag)
                {
                    utils.dialog({
                        title: "提示",
                        content: "请选择或填写说明书属性",
                        width:300,
                        height:30,
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                    return false;
                }
            }
            var priceFlag=false;
            var channelrowdatas=$('#X_Table_Channel').getRowData();
            $.each(channelrowdatas,function (index,item) {
                var supplyPrice = item.supplyPrice;//供货价
                var appPrice = item.appPrice;//APP价
                if(Number(supplyPrice)>Number(appPrice)){
                    priceFlag=true;
                    return false;
                }
            })
            if(priceFlag){
                var d =  dialog({
                    title: "提示",
                    content: "APP售价低于供货价，已造成<span style='color: red'>负毛利</span>，请慎重填写！",
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        butSubmint();
                        d.close().remove();
                        return false;
                    },
                    cancelValue: '取消',
                    cancel: function () {
                        d.close().remove();
                    }
                }).showModal();
            }else {
                butSubmint();
            }
        } else {//验证不通过
            return false;
        }
    }

    //关闭按钮
    $("#closePage").on("click", function () {
        var d =  dialog({
            title: "提示",
            content: "是否保存草稿？",
            width:300,
            height:30,
            okValue: '保存草稿',
            ok: function () {
                $("#statues").val(0);
                butSubmint();
                d.close().remove();
                return false;
            },
            cancelValue: '关闭',
            cancel: function () {
                utils.closeTab();
                return false;
            }
        }).showModal();
    });
    //商品名
    $("#productName").on("keyup",function (ev) {
        if(ev.keyCode != 13)
        {
            var value=this.value;
            getMnemonicCode(value,'productNameMnemonicCode');
        }
    })
    $("#search_commodity").on("keyup",function (ev) {
        if(ev.keyCode != 13)
        {
            var value=this.value;
            getMnemonicCode(value,'commonNameMnemonicCode');

        }
    })
    function getMnemonicCode(str,id){
        $.ajax({
            url:'/proxy-product/product/productFirst/getMnemonicCode?name='+str,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if(data.code == 0)
                {
                    $("#"+id).val(data.result);
                }
            }
        })
    }
    //生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord",params:{"isStop":0}},"manufacturer"
        ,{data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"},""
        ,function (result) {
            $("#brandManufacturers").val(result.brandfactoryId);
            $("#brandManufacturersVal").val(result.brandfactoryName);
        },function () {
        var val=$.trim($('#manufacturerVal').val());
        if(val == '')
        {
            $("#brandManufacturers").val("");
            $("#brandManufacturersVal").val("");
        }
    });
    //品牌厂家
    valAutocomplete("/proxy-product/product/dict/queryBrandfactory",{paramName:"brandFactoryName"},"brandManufacturers"
        ,{data:"brandfactoryId",value:"brandfactoryName",brandfactoryId:"brandfactoryId"},""
        ,function (result) {
            $("#brandManufacturers").val(result.brandfactoryId);
        },function () {
            var val=$.trim($('#brandManufacturersVal').val());
            if(val == '')
            {
                $("#brandManufacturers").val("");
            }
        });
    //包装单位
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querypackingunitnotpage",{paramName:"packingName",params:{"isStop":0}},"packingUnit",{data:"packingId",value:"packingName"});
    //剂型
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querydosenotpage",{paramName:"doseName",params:{"isStop":0}},"dosageForm",{data:"doseid",value:"dosename"});
    // 委托厂家
/*    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord",params:{"isStop":0}},"entrustmentManufacturer",{data:"manufactoryId",value:"manufactoryName",address:"address"},"",
        function (result) {
            $("#entrustmentManufacturerAddress").val(result.address);
        },function () {
            $("#entrustmentManufacturerAddress").val("");
        });*/
    //存储条件
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":2}},"storageConditions",{data:"id",value:"name"},"",
        function (result) {});
    //处方分类
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":6}},"prescriptionClassification",{data:"id",value:"name",numCode:"numCode"},""
        ,function (result) {
            $("#prescriptionClassificationCode").val(result.numCode);
            $('[name=prescriptionClassificationVal]').val(result.value);
        },function () {
            $("#prescriptionClassificationCode").val("");
            $('[name=prescriptionClassificationVal]').val('');
        });
    //所属经营范围
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommodityscope",{paramName:'scopeName', params:{"orgCode":"001"}},"scopeOfOperation",{data:"simpleCode",value:"scopeName"},""
        ,function (result) {
            localBatchName(1);
        });
    //商品大类
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":7}},"largeCategory",{data:"id",value:"name"},""
        ,function (result) {
            if(largeCategoryArray.indexOf(result.value)>-1) {
                $(".productrate").show();
            }else{
                $(".productrate").hide();
                //进项税率
                $("input[name='entryTaxRate']").prop("checked",false);
                //销项税率
                $("input[name='salesRate']").prop("checked",false);
            }
            $.ajax({
                type:"post",
                url: "/proxy-sysmanage/sysmanage/dict/queryexplaintemplatenotpage",
                async : false,
                data:{"commodityType":result.data,"isStop":0},
                dataType:"json",
                success: function (data) {
                    console.log(data)
                    if(data.code == 0)
                    {
                        var result = data.result;
                        if(result==null){
                            $("#insForm").html("");
                        }else {
                            var html = getHTML(result[0].explainAttribute);
                            $("#insForm").html(html);
                        }
                    }else{
                        $("#insForm").html('');
                    }
                },
                error:function () {
                    utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
                }
            });
        },function () {
            $("#insForm").html("");
        });
    // 一级分类 firstCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryonecategory",{paramName:'categoryName'},"firstCategory",{data:"id",value:"categoryName",simpleCode:"simpleCode"},""
    ,function (result) {
        $("#firstCategoryCode").val(result.simpleCode);
        var value=$("#firstCategoryVal").val();
        if(value != $("#firstCategoryVal").attr("data-value"))
        {
            $("#secondCategory").val('');
            $("#secondCategoryVal").val('');
            $("#secondCategoryVal").attr('data-value','');
            $("#thirdCategory").val('');
            $("#thirdCategoryVal").val('');
            $("#thirdCategoryVal").attr('data-value','');
        }
        $("#secondCategoryVal").focus(function () {
            if($.trim($("#firstCategoryVal").val()) == '')
            {
                $(this).blur();
            }
        })
    },function () {
        var value=$("#firstCategoryVal").val();
        if(value != $("#firstCategoryVal").attr("data-value"))
        {
            $("#firstCategoryCode").val("");
            $("#secondCategory").val('');
            $("#secondCategoryVal").val('');
            $("#secondCategoryVal").attr('data-value','');
            $("#thirdCategory").val('');
            $("#thirdCategoryVal").val('');
            $("#thirdCategoryVal").attr('data-value','');
        }
    });
    //二级分类 secondCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querytwocategory",{paramName:'categoryName',params:{"pid":$("#firstCategory").val()}
        ,isSearch:function(params){
            var code=$("#firstCategory").val();
            if(code != '')
            {
                params.pid=code;
                return params;
            }
            return false;
        }},"secondCategory",{data:"id",value:"categoryName"},""
        ,function (result2) {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
            $("#thirdCategoryVal").focus(function () {
                if($.trim($("#secondCategoryVal").val()) == '')
                {
                    $(this).blur();
                }
            })
        },function () {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
        });
    //三级分类 thirdCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querythreecategory",{paramName:'categoryName',params:{"pid":$("#secondCategory").val()}
    ,isSearch:function(params){
        var code=$("#secondCategory").val();
        if(code != '')
        {
            params.pid=code;
            return params;
        }
        return false;
    }},"thirdCategory",{data:"id",value:"categoryName"});

    var pageType = $("#pageType").val();
    //商品定位 commodityPosition
    valAutocomplete("/proxy-product/product/dict/common/queryCommon",{paramName:'CommonName', params:{"type":3,"pageType":pageType}},"commodityPosition",{data:"id",value:"name",requiredFlag:"requiredFlag",modeRequiredFlag:"modeRequiredFlag"},
      "",result => onCommodityPositionSelected(result));
    //首营供应商 firstBattalionSupplier
    valAutocomplete("/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/firstSupplierOrgBaseList",{paramName:'supplierName',params:{"orgCode":orgCode}},
        "firstBattalionSupplier",{data:"id",value:"supplierName"});
    //品牌字典
    //valAutocomplete("/proxy-sysmanage/sysmanage/brand/list",{paramName:'brandName', params:{"status":"false"}},"brand",{data:"id",value:"brandName"});
    //加载字典值
    showDictValue();
    //是否审核页面
    var applyType=$("#applyType").val();

    if(applyType==""){
        $("#search_commodity").dblclick(function () {
            commodity_search_dia();
        });
        if(baseApplicationCode!=applicationCode){//引用主数据
            initApprovalFlowChart("productFirstIgnore");
        }else {
            initApprovalFlowChart("productFirst");
        }
    }else if(applyType=="apply"){//审核页面隐藏商品搜索图标
        $(".glyphicon-search").hide();
        //根据流程实例ID加载流程图
        var processInstaId=$("#processId").val();
        initApprovalFlowChart("",processInstaId);
    }
    // 重新提交
    $('#auditSubmit').on('click', function () {
        var batchNames = $('#X_Table').find('select[name=batchName]');
        $.each(batchNames,function (index,item) {
            if(item.value == '0'){ //批件名称选中项为  商品附件时
                var arr = $(item).parents('td').nextAll().find('input');
                $.each(arr,function (ind,el) {
                    $(el).removeClass('{validate:{ required :true}}')
                })
            }
        })
        //RM
        var isZYYP_text = ($('#largeCategoryVal').attr('data-value') == '中药饮片');
        $('input[name=qualityStandard]')[isZYYP_text?'addClass':'removeClass'](' {validate:{ required :true}}'); // 质量标准
        $('input[name=qualityStandard]')[isZYYP_text?'removeClass':'addClass']('ignore'); //当 商品大类非中药饮片时，忽略 质量标准的校验
        if(isZYYP_text && $('input[name=producingArea]').prev('div').find('span').length < 1){ // 产地
            utils.dialog({
                title:'提示',
                content:'产地不能为空',
                width:300,
                height:30,
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
        }
        if(isZYYP_text && $('input[name=smallPackageBarCode]').prev('div').find('span').length < 1){ // 小包装条码
            utils.dialog({
                title:'提示',
                content:'小包装条码不能为空',
                width:300,
                height:30,
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
        }
        // 要忽略校验的ID   '有效期'      ,'养护周期'
        var id_ignoreArr = ['indateType','maintenancePeriod'];
        // 要忽略校验的NAME      //批准文号      ,标准库ID          ,是否委托   ,有效期,
        var inpName_ignoreArr = ['approvalNumber','standardProductId','entrustmentProduction','indate'];
        add_remove_require(id_ignoreArr,'TYPE_ID');
        add_remove_require(inpName_ignoreArr,'TYPE_NAME');
        function add_remove_require(arr,arrType) {
            $(arr).each(function (i,v) {
                if(arrType == 'TYPE_ID'){
                    $('#'+v)[isZYYP_text?'addClass':'removeClass']('ignore');
                }else{
                    $('input[name='+v+']')[isZYYP_text?'addClass':'removeClass']('ignore');
                }
            })
        }


        if (validform("productBaseInfoVo").form()
            &&validform("productOrganizationVo").form()
            &&validform("approvalFileVo").form()&&validform("productChannelVo").form()) {

            // 仅在质管员页面对批件进行校验。
            if ($("#zhiGuanUpdateFlag").val()){
                //批准文件有效期校验
                var $table=$('#X_Table');
                var rowdatas=$table.getRowData();
                var $tr=$table.find("tr").not(":first");
                var nowtime = new Date(new Date().Format("yyyy-MM-dd"));
                var validityflag=false;
                var selVal="";
                //验证批准文件
                if (!rowdatas || rowdatas.length <= 0) {
                    utils.dialog({content: '批准文件没有录入，请录入后再提交', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                let resStatus = inputValidate()
                if (!resStatus) return false
                $.each(rowdatas,function (index,item) {
                    //把字符串格式转化为日期类
                    if(item.validityDateStr!="") {
                        var issueDate = new Date(item.validityDateStr);
                        if (nowtime >= issueDate) { //批准文件有效期至过期
                            selVal=$tr.eq(index).find("select[name='batchName'] option:selected").text();
                            validityflag=true;
                            return false;
                        }
                    }
                })
                if(validityflag){
                    utils.dialog({
                        title: '提示',
                        content: '批准文件-'+selVal+'-已过期，确认继续提交吗？',
                        width: 300,
                        height: 30,
                        okValue: '确定',
                        ok: function () {
                            checkSub()
                        }
                    }).showModal();
                    return false
                }
            }
            checkSub()
        } else {//验证不通过
            utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    });

    function checkSub() {
        //验证说明书
        if(checkProductInstruction()) {
            var priceFlag=false;
            var channelrowdatas=$('#X_Table_Channel').getRowData();
            $.each(channelrowdatas,function (index,item) {
                var supplyPrice = item.supplyPrice;//供货价
                var appPrice = item.appPrice;//APP价
                if(Number(supplyPrice)>Number(appPrice)){
                    priceFlag=true;
                    return false;
                }
            })
            if(priceFlag){
                var d = dialog({
                    title: "提示",
                    content: "APP售价低于供货价，已造成<span style='color: red'>负毛利</span>，请慎重填写！",
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function () {
                        submitAuditInfo(2, "","apply");
                        d.close().remove();
                        return false;
                    },
                    cancelValue: '取消',
                    cancel: function () {
                        d.close().remove();
                    }
                }).showModal();
            } else {
                submitAuditInfo(2,"","apply");
            }
        }
    }
    //审核通过按钮
    $('#auditPass').on('click', function () {
        var status=this.getAttribute("status");

        //审核通过前验证
        var batchNames = $('#X_Table').find('select[name=batchName]');
        $.each(batchNames,function (index,item) {
            if(item.value == '0'){ //批件名称选中项为  商品附件时
                var arr = $(item).parents('td').nextAll().find('input');
                $.each(arr,function (ind,el) {
                    $(el).removeClass('{validate:{ required :true}}')
                })
            }
        })
        //RM
        var isZYYP_text = ($('#largeCategoryVal').attr('data-value') == '中药饮片');
        $('input[name=qualityStandard]')[isZYYP_text?'addClass':'removeClass'](' {validate:{ required :true}}'); // 质量标准
        $('input[name=qualityStandard]')[isZYYP_text?'removeClass':'addClass']('ignore'); //当 商品大类非中药饮片时，忽略 质量标准的校验
        if(isZYYP_text && $('input[name=producingArea]').prev('div').find('span').length < 1){ // 产地
            utils.dialog({
                title:'提示',
                content:'产地不能为空',
                width:300,
                height:30,
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
        }
        if(isZYYP_text && $('input[name=smallPackageBarCode]').prev('div').find('span').length < 1){ // 小包装条码
            utils.dialog({
                title:'提示',
                content:'小包装条码不能为空',
                width:300,
                height:30,
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
        }
        // 要忽略校验的ID   '有效期'      ,'养护周期'
        var id_ignoreArr = ['indateType','maintenancePeriod'];
        // 要忽略校验的NAME      //批准文号      ,标准库ID          ,是否委托   ,有效期,
        var inpName_ignoreArr = ['approvalNumber','standardProductId','entrustmentProduction','indate'];
        add_remove_require(id_ignoreArr,'TYPE_ID');
        add_remove_require(inpName_ignoreArr,'TYPE_NAME');
        function add_remove_require(arr,arrType) {
            $(arr).each(function (i,v) {
                if(arrType == 'TYPE_ID'){
                    $('#'+v)[isZYYP_text?'addClass':'removeClass']('ignore');
                }else{
                    $('input[name='+v+']')[isZYYP_text?'addClass':'removeClass']('ignore');
                }
            })
        }
        if (validform("productBaseInfoVo").form()
            &&validform("productOrganizationVo").form()
            &&validform("approvalFileVo").form()) {
            // 仅在质管员页面对批件进行校验。
            if ($("#zhiGuanUpdateFlag").val()){
                //批准文件有效期校验
                var $table=$('#X_Table');
                var rowdatas=$table.getRowData();
                var $tr=$table.find("tr").not(":first");
                var nowtime = new Date(new Date().Format("yyyy-MM-dd"));
                var validityflag=false;
                var selVal="";
                //验证批准文件
                if (!rowdatas || rowdatas.length <= 0) {
                    utils.dialog({content: '批准文件没有录入，请录入后再提交', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                let resStatus = inputValidate()
                if (!resStatus) return false
                $.each(rowdatas,function (index,item) {
                    //把字符串格式转化为日期类
                    if(item.validityDateStr!="") {
                        var issueDate = new Date(item.validityDateStr);
                        if (nowtime >= issueDate) { //批准文件有效期至过期
                            selVal=$tr.eq(index).find("select[name='batchName'] option:selected").text();
                            validityflag=true;
                            return false;
                        }
                    }
                })
                if(validityflag){
                    utils.dialog({
                        title: '提示',
                        content: '批准文件-'+selVal+'-已过期，确认继续提交吗？',
                        width: 300,
                        height: 30,
                        okValue: '确定',
                        ok: function () {
                            checkSubSub(status)
                        }
                    }).showModal();
                    return false
                }
            }
            checkSubSub(status)
        } else {//验证不通过
            utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    });
    function checkSubSub(status) {
        //验证说明书
        if(checkProductInstruction()){
            var priceFlag=false;
            var channelrowdatas=$('#X_Table_Channel').getRowData();
            $.each(channelrowdatas,function (index,item) {
                var supplyPrice = item.supplyPrice;//供货价
                var appPrice = item.appPrice;//APP价
                if(Number(supplyPrice)>Number(appPrice)){
                    priceFlag=true;
                    return false;
                }
            })
            if(priceFlag){
                var d = dialog({
                    title: "提示",
                    content: "APP售价低于供货价，已造成<span style='color: red'>负毛利</span>，请慎重填写！",
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function () {
                        auditProduct("审核通过",status);
                        d.close().remove();
                        return false;
                    },
                    cancelValue: '取消',
                    cancel: function () {
                        d.close().remove();
                    }
                }).showModal();
            } else {
                auditProduct("审核通过",status);
            }
        }
    }
    //审核不通过按钮和关闭审核按钮
    $('.audiPass').on('click', function () {
        $('#auditOpinion').val('');
        var status=this.getAttribute("status");
        if(status==4){
            auditProduct("审核不通过",status);
        }else if(status==3){
            utils.dialog({
                title:"关闭审核",
                width:300,
                height:30,
                okValue: '确定',
                content: "确定关闭此申请？",
                ok: function () {
                    submitAuditInfo(status,"");
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        }
    });

    // 温度范围 onblur校验
    $('[name=temperatureRange]').each(function (index) {
        $(this).on('blur',function () {
            if($(this).val() != ''){
                var that = $(this)
                if(Number($(this).val()) <= 0 || $(this).val().indexOf('.') > 0){
                    utils.dialog({
                        title:'提示',
                        content: '温度范围的值必须为大于0的正整数.',
                        okValue: '确定',
                        ok: function () {
                            setTimeout(function () {
                                that.focus();
                            },200)
                        }
                    }).showModal()
                    return false;
                };
                if($(this).val().indexOf('0') == 0){
                    utils.dialog({
                        title:'提示',
                        content: '温度值的起始值不能为0.',
                        okValue: '确定',
                        ok: function () {
                            setTimeout(function () {
                                that.focus();
                            },200)
                        }
                    }).showModal()
                    return false;
                }
            }
        })
    })
    //

   /* setTimeout(function () {
        let hidden_scopeOfOperationVal = $('#scopeOfOperation').val();
        if(hidden_scopeOfOperationVal == 'DELJSYP' || hidden_scopeOfOperationVal == 'DBTHZJTLJS' ){
            //$('#largeCategoryVal').prop('disabled',true)
            $('.teshushuxing_tag').prop({'checked':true, 'disabled': true})
        }
    },200)*/

    //是否专供
    // $("input[name='specialProvision']").change(function() {
    //     let specialProvisionVal = $(this).val();
    //     /**
    //      * RM 2019-08-16
    //      */
    //     if ($("input[name='specialProvision']:checked").length > 0) {
    //         // 版本一
    //         if (specialProvisionVal == '1') { // 操作 非专供
    //             $('#specialProvision').parent().find('[name=specialProvision]').not(':first').prop('checked', false);
    //         } else {
    //             if(specialProvisionVal == '2' ){ // 单店专供
    //                 $(this).parents('.checkbox').find('.tag_3').prop('checked',false)
    //             }else if(specialProvisionVal == '3'){// 连锁专供
    //                 $(this).parents('.checkbox').find('.tag_2').prop('checked',false)
    //             }
    //             $('#specialProvision').parent().find('[name=specialProvision]:first').prop({
    //                 'checked': false,
    //                 'disabled': false
    //             });// 非专供 不允许选
    //         }
    //     } else {
    //         // 没有选中项时 全部放开允许点击
    //         $('#specialProvision').parent().find('[name=specialProvision]').prop('disabled', false);
    //     }
    // })
})

/**
 * 安全库存天数  库存上限天数  合法值判断
 * @param obj
 * @returns {boolean}
 *
 */
function validityCheck(obj){
    let bool = false;
    $('[name='+obj.inpName+']').on('blur',function () {
        let thisVal = $(this).val();
        /*if(!thisVal){
            utils.dialog({
                title: '提示',
                content: obj.tagName + '为必填项.',
                okValue: '确定',
                ok: function () {}
            }).showModal();
            bool = true;
            return bool;
        }*/
        if(thisVal){
            if(thisVal < obj.validityVal[0] || thisVal > obj.validityVal[1]){
                utils.dialog({
                    title: '提示',
                    content: obj.tagName + '的取值范围是【'+obj.validityVal[0]+','+obj.validityVal[1]+'】天.',
                    okValue: '确定',
                    ok: function () {}
                }).showModal();
                $(this).val('');
                bool = true;
                return bool;
            }
        }else {
            bool = true;
            return bool;
        }
    })
    return bool;
}
//批准文件 的批件名称 change切换监听，当批件名称为商品附件时 移除 必填检验的require, 否则，当名称切换为别的类型的时候还得再把这个require添加上
function change_batchName(el) {
    var arr = $(el).parents('td').nextAll().find('input');
    $.each(arr,function (ind,ele) {
        if(ind != 1){ // 核准内容不是必填项
            $(ele)[(el.value != '0')?'addClass':'removeClass']('{validate:{ required :true}}')
            $(ele)[(el.value != '0')?'removeClass':'addClass']('ignore')
        }
    })
}
/**
 * 填写审批意见
 * @param title
 * @param status
 */
function  auditProduct(title,status) {
    if(status==4){
        $('#opinion').show();
    }else{
        $('#opinion').hide();
    }
    utils.dialog({
        title:title,
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            //审核意见不能为空
            //审核不通过，意见不能为空
            if(status==4) {
                if ($("#auditOpinion").val() == "") {
                    utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
            }
            submitAuditInfo(status,$("#auditOpinion").val());
        },
        cancelValue: '取消',
        cancel: function () {
            $("#auditOpinion").val("");
        }
    }).showModal();

}
function loadUserName(key,orgaKey) {
    var userId=$("#"+key).val();
    if(userId==""){
        return;
    }
    $.ajax({
        type:"get",
        url: "/proxy-sysmanage/sysmanage/system/queryUserInfoById",
        data:{"userId":userId},
        dataType:"json",
        success: function (data) {
            //console.log(data.result);
            if(data.code==0&&data.result!=null){
                var userName=data.result.userName;
                if(orgaKey!=null){//申请人机构显示
                    $("#"+orgaKey+"Val").val(data.result.orgName);
                }
                $("#"+key+"Val").val(userName);
                $("#"+key+"Val").attr("data-value",userName);
            }
        },
        error:function () {
        }
    });
}
function initFileSelected() {

}
/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$('#X_Table').getRowData(parentId);
    if(data.enclosureList)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            list:JSON.parse(data.enclosureList)
        })
    }
}
/**
 * 加载所属经营范围对应的批准文件
 * @param simpleCode
 */
function localBatchName(type) {
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/getBatchnamesByType",
        async : false,
        data:{"type":type},
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option><option value="0">商品附件</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].batchId+'">'+arr[i].batchName+'</option>';
                    }
                }
            }
            $("select[name='batchName']").html(html);
            /**
             * RM 2018-10-10
             *  当所属经营范围 的值改变会后，批准文件的批件名称下拉框 会重置，
             *  相应的，之前已经上传过的附件需要被清空
             */
            var rowdatas = $('#X_Table').XGrid('getRowData');
            for(var i = 0; i< rowdatas.length;i++){
                $('#X_Table').XGrid('setRowData',rowdatas[i].id,{enclosure:'无'})
                $('#X_Table').XGrid('setRowData',rowdatas[i].id,{enclosureList:[]})
            }
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
var Is_ZYYP_flag = false; // 商品大类是否为中药饮片
function valAutocomplete(url,param,obj,resParam,querydelimiter,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    var options={
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        querydelimiter:querydelimiter,
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
            $("#"+obj+"Val").attr("title",result.value);
            $("#"+obj+"Val").val(result.value);
            // 当特殊经营范围选中指定值时，商品大类的也跟着切换指定值
            if(obj =='commodityPosition'){
                if(result.value=='集团首推高毛'||result.value=='省区首推独家'||result.value=='省区首推高毛' ||
                    result.value=='地采高毛' || result.value=='地采战略'|| result.value=='集采高毛' || result.value=='集采战略' ||
                    result.value=='药帮帮品种' || result.value=='自有品牌'|| result.value=='地采品牌' || result.value=='集采品牌' ||result.value=='自营甄选'
                ){
                    $("#"+obj).val("");
                    $("#"+obj+"Val").attr("data-value","");
                    $("#"+obj+"Val").attr("title","");
                    $("#"+obj+"Val").val("");
                    $('#purchaseContractMode').val("")
                    $('#purchaseContractMode').children().attr("disabled","disabled");
                    utils.dialog({content: '首营不允许选择该商品定位,请选择其他商品定位!', quickClose: true, timeout: 2000}).showModal();
                }
            }
            if(obj == 'scopeOfOperation'){
                //去除特定经营范围，改变商品大类
                /*if(result.value == "蛋白同化制剂·肽类激素" || result.value == "第二类精神药品"){
                    $(".productrate").hide();
                    //进项税率
                    $("input[name='entryTaxRate']").prop("checked",false);
                    //销项税率
                    $("input[name='salesRate']").prop("checked",false);
                    $('#largeCategoryVal').val(result.value);
                    $('#largeCategoryVal').attr({'data-value':result.value, 'title': result.value})
                    $('#largeCategoryVal').prop('disabled',true)
                    $('.teshushuxing_tag').prop({'checked':true, 'disabled': true})
                }else{
                    if(base_largeCategoryVal != ''){
                        $('#largeCategoryVal').val(base_largeCategoryVal);
                        $('#largeCategory').val(base_largeCategory)
                    }
                    $('#largeCategoryVal').prop('disabled',false)
                }
                if(result.value == "蛋白同化制剂·肽类激素"){
                    $('#largeCategory').val('140') // 商品大类
                }
                if(result.value == "第二类精神药品"){
                    $('#largeCategory').val('139') // 商品大类
                }
                if(result.value != "第二类精神药品" && result.value != "蛋白同化制剂·肽类激素"){
                    $('.teshushuxing_tag').prop({'checked':false})
                    $('.teshushuxing_tag').prop('disabled',$('.teshushuxing_tag').next().next().prop('disabled'))
                }*/

              /* if(result.value == "蛋白同化制剂·肽类激素" || result.value == "第二类精神药品"){
                   $('.teshushuxing_tag').prop({'checked':true, 'disabled': true})
               }*/
             /*  if(result.value != "第二类精神药品" && result.value != "蛋白同化制剂·肽类激素"){
                   $('.teshushuxing_tag').prop({'checked':false})
                   $('.teshushuxing_tag').prop('disabled',$('.teshushuxing_tag').next().next().prop('disabled'))
               }*/
                if(result.value == '中药饮片'){
                    Is_ZYYP_flag = true
                    $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                    $('#maintenancePeriod').parents('.input-group').find('div').eq(0).find('i').remove(); // 养护周期
                    $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                    $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                    $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                    $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                    $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                }else{
                    if(Is_ZYYP_flag){
                        $('input[name=indate]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>有效期') // 有效期
                        $('#maintenancePeriod').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>养护周期'); // 养护周期
                        $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>批准文号') // 批准文号
                        $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>标准库ID') // 标准库ID
                        $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>是否委托生产')//是否委托生产
                        $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('产地') // 产地
                        $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('质量标准') // 质量标准
                    }
                }
                if(result.value == '中药饮片'){
                    Is_ZYYP_flag = true
                    $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                    $('#maintenancePeriod').parents('.input-group').find('div').eq(0).find('i').remove(); // 养护周期
                    $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                    $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                    $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                    $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                    $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                }else{
                    if(Is_ZYYP_flag){
                        $('input[name=indate]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>有效期') // 有效期
                        $('#maintenancePeriod').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>养护周期'); // 养护周期
                        $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>批准文号') // 批准文号
                        $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>标准库ID') // 标准库ID
                        $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>是否委托生产')//是否委托生产
                        $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('产地') // 产地
                        $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('质量标准') // 质量标准
                    }
                }
            }

            if(obj == 'largeCategory'){
                if(result.value == '中药饮片'){
                    Is_ZYYP_flag = true
                    $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                    $('#maintenancePeriod').parents('.input-group').find('div').eq(0).find('i').remove(); // 养护周期
                    $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                    $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                    $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                    $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                    $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                }else{
                    if(Is_ZYYP_flag){
                        $('input[name=indate]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>有效期') // 有效期
                        $('#maintenancePeriod').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>养护周期'); // 养护周期
                        $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>批准文号') // 批准文号
                        $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>标准库ID') // 标准库ID
                        $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>是否委托生产')//是否委托生产
                        $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('产地') // 产地
                        $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('质量标准') // 质量标准
                    }
                }
            }
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    }
    if(param.isSearch)
    {
        options.isSearch=param.isSearch;
    }
    $("#"+obj+"Val").Autocomplete(options);
}

/**
 * 当切换商品定位一级时，联动修改 集采签约方式、商品定位二级 下拉框当选项
 *
 * @param selectedItem 当前用户选中的 商品定位一级 字段
 */
function onCommodityPositionSelected(selectedItem) {
    var pageType = $("#pageType").val();
    $.ajax({
        type:"get",
        url: "/proxy-product/product/dict/common/queryByParentId",
        async : false,
        data:{"parentId":selectedItem.data,"pageType":pageType},
        dataType:"json",
        success: function (data) {
            let options = ['<option value="">请选择</option>']
            if (data.result){
                options = options.concat( data.result.map(item=>{
                    return "<option value="+item.id+">"+item.name+"</option>"
                }))
            }
            $('#secondCommodityPositionVal').html(options.join(""))
        },
        error:function (XMLHttpRequest, textStatus, errorThrown) {
            // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
    refreshRequiredFields(selectedItem)
    resetPurchaseContactMode(selectedItem)
    refreshPurchaseContractModeList(selectedItem)
}

/**
 * 当前选择的商品定位一级字段若非 集采签约，则重置 集采签约方式 字段，将置为 “请选择”
 * @param selectedItem
 */
function resetPurchaseContactMode(selectedItem) {
    if(!selectedItem.modeRequiredFlag){
        $('#purchaseContractMode').val("")
    }
}

function refreshPurchaseContractModeList(selectedItem){
    if (selectedItem.modeRequiredFlag) {
        $('#purchaseContractMode').children().removeAttr("disabled")
    }else {
        $('#purchaseContractMode').children().attr("disabled","disabled")
    }
}

function refreshRequiredFields(selectedItem) {
    if (selectedItem.requiredFlag) {
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).html("<i class='text-require'>*  </i>商品定位二级")
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).addClass('require')
        $('#secondCommodityPositionVal').addClass('{validate:{ required :true}}')
    }else {
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).find('i').remove()
        $('#secondCommodityPositionVal').parents('.input-group').find('div').eq(0).removeClass('require')
        $('#secondCommodityPositionVal').removeClass('{validate:{ required :true}}')
    }
    if (selectedItem.modeRequiredFlag){
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).html("<i class='text-require'>*  </i>集采签约方式")
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).addClass('require')
        $('#purchaseContractMode').addClass('{validate:{ required :true}}')
    }else {
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).find('i').remove()
        $('#purchaseContractMode').parents('.input-group').find('div').eq(0).removeClass('require')
        $('#purchaseContractMode').removeClass('{validate:{ required :true}}')
    }
}
//设置标准价
function toStandardPriceAndparGrossMargin(obj) {
    toDecimal2(obj);
    var channelrowdatas=$('#X_Table_Channel').getRowData();
    $.each(channelrowdatas,function (index,item) {
        var supplyPrice = item.supplyPrice;//供货价
        var appPrice = item.appPrice;//APP价
        if(supplyPrice!=null&&supplyPrice!=""&&appPrice!=null&&appPrice>0){
            var value=(1-supplyPrice/appPrice)*100+"";
            value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');
            $('#X_Table_Channel').XGrid('setRowData',channelrowdatas[index].id,{parGrossMargin:value+"%"})
        }else{
            $('#X_Table_Channel').XGrid('setRowData',channelrowdatas[index].id,{parGrossMargin:""})
        }
        if(item.channelId=='1'){
            $("#standardPrice").val(supplyPrice);
        }
    })
};
//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(obj) {
    var f = parseFloat(obj.value);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(obj.value*100)/100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    obj.value= s;
    return s;
}
/**
 * 设置输入两位小数 并且控制可输入的最大值的位数
 *
 * obj this对象
 * size 最大值位数 可不传
 * */
function setFixedTwo(obj,size) {
    var value=obj.value;
    var n='';
    if(size)
    {
        for(var i=0;i<size;i++)
        {
            n+='9';
        }
        n+='.99';
        if(Number(value) > Number(n))
        {
            value=n;
        }
    }
    value = value.replace(/[^\d.]/g,'').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3')
    obj.value=value;
}
/**
 *首营默认选中值设置
 */
function radioDefaultChecked() {
    //基本属性默认值
    //养护周期默认值
    if($("#maintenancePeriod").val()==''){
        $("#maintenancePeriod").val(90);
    }
    var productIndateType = $("#productIndateType").val();
    if(productIndateType==""){
        $("#indateType").val("月");
    }
    //是否显示委托厂家处理
    setRadioChecked('entrustmentProduction','0');
    initChecked();
    // 是否税务优惠
    setRadioChecked('taxIncentives','0');
    //限销状态
    setRadioChecked('baseLimitedPinState','0');
    //限采状态
    setRadioChecked('baseLimitedProductionState','0');
    //停用状态
    setRadioChecked('baseDisableState','0');
    //运营属性默认选中
    //是否拆零
    setRadioChecked('scatteredYn','0');
    //APP销售价是否维价
    setRadioChecked('dimensionSalesPriceYn','0');
    //终端零售价是否维价
    setRadioChecked('dimensionTerminalPriceYn','0');
    //限销状态
    setRadioChecked('limitedPinState','0');
    //限采状态
    setRadioChecked('limitedProductionState','0');
    //停用状态
    setRadioChecked('disableState','0');
    //是否外省可售
    setRadioChecked('otherProvincesSale','0');
    //连锁是否维价
    setRadioChecked('dimensionChainPriceYn','0');
    // 是否监管
    setRadioChecked('whetherRegulatory','1');
    //养护类型
    setRadioChecked('keyConservationCategories','2');
    //4+7品种
    setRadioChecked('varietiesYn','0');
    // 是否监管
    setRadioChecked('superviseState','0');
}
function setRadioChecked(name,value){
    var obj=$("input[name="+name+"]");
    if($("input[name="+name+"]:checked").length < 1)
    {
        obj.each(function () {
            var val=this.value;
            if(val == value)
            {
                $(this).prop("checked",true);
            }
        })
    }
}
function  readOnly() {
    //按钮置灰
   /* $("#addRowData").attr("disabled",true);
    $("#deleRow").attr("disabled",true);
    $("#batchUpload").attr("disabled",true);*/
    //加载说明书
    $('#insForm textarea').attr('disabled','disabled');
    $('.only-read').attr('disabled','disabled');
    $('.only-read').attr("readonly",true);
    $('#productBaseInfoVo  .addTagBtn').removeClass('disBlock').addClass('disNone');
}
function  unReadOnly() {
    $(".only-read").each(function(){
        $(this).removeAttr('disabled');
        $(this).removeAttr('readonly');
        if(this.tagName == 'INPUT'){
            if(this.type == 'text') {
                this.value='';
            }else if(this.type == 'hidden') {
                this.value='';
            }else if(this.type == 'number') {
                this.value='';
            }else if(this.type == 'checkbox'){
                $(this).prop('checked',false);
            }
        }

        if($(this).attr('data-disable')){
            $(this).prop('disabled',true)
        }
    });
    $("#X_Table").XGrid('clearGridData')
    $("#addRowData").attr("disabled",false);
    $("#deleRow").attr("disabled",false);
    $("#batchUpload").attr("disabled",false);
    $("#productBaseInfoVo input[data-role=\"tagsinput\"]").val('');
    $("#productBaseInfoVo input[data-role=\"tagsinput\"]").tagsinput('removeAll');
}
/**
 * 搜索商品主数据
 * @returns {boolean}
 */
function commodity_search_dia() {
    var orgCode = $("#applicantOrgCode").val();
    var productName=$("#search_commodity").val();
    var applicationCode=$("#applicationCode").val();
    dialog({
        url: '/proxy-product/product/productFirst/toSearchList',
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {"productName":productName,"orgCode":orgCode}, // 给modal 要传递的 的数据
        onclose: function () {
            if (this.returnValue && this.returnValue != 'add') {
                $('#productBaseInfoVo .addTagBtn').removeClass('disBlock').addClass('disNone');
                $('#productOrganizationVo .addTagBtn').removeClass('disNone').addClass('disBlock');
                $('#productBaseInfoVo  .addTagBtn').parent().find('input').css('display','none');
                var data = this.returnValue;
                console.log(data);
                //加载主数据信息
                var productId=data.id;
                $.ajax({
                    type:"post",
                    url: "/proxy-product/product/productBase/getProductBaseById",
                    async : false,
                    data:{"productId":productId},
                    dataType:"json",
                    success: function (data) {
                        var result=data.result;
                        console.log(data);
                        $('input[data-role="tagsinput"]').tagsinput('removeAll');
                        loadProductData(result.productBaseInfoVo);
                        var temperatureRange = result.productBaseInfoVo.temperatureRange
                        if(temperatureRange!=null){
                            var tempArr = temperatureRange .split(',');
                            for(var i=0;i<tempArr.length;i++){
                                if(i==0){
                                    $('#temperatureRange1').val(tempArr[0]);
                                }
                                if(i==1){
                                    $('#temperatureRange2').val(tempArr[1]);
                                }
                            }
                        }
                        //加载批准文件 按钮置灰
                        var scopeOfOperation= $("#scopeOfOperation").val();
                        localBatchName(1);
                       /* $('#X_Table').setGridParam({
                            url:"/proxy-product/product/productApprovalFile/toList",
                            postData: {
                                "type": 0,
                                "correlationId": $("#baseProductId").val(),
                            },page:1,
                            gridComplete: function () {
                                $('#X_Table input,#X_Table select').attr('disabled','disabled');
                            }
                        }).trigger('reloadGrid');*/
                        //加载说明书
                        var html = getProductHTML(result.instructionsList);
                        $("#insForm").html(html);
                        //var packingHTML=packingProductHTML(result.packingList);
                        //$("#packingList").html(packingHTML);
                        readOnly();
                        //加载流程图
                        initApprovalFlowChart("productFirstIgnore");
                        //加载渠道
                        addProductChannel(result.productBaseInfoVo.offlineBusinessType);

                        $('#productBaseInfoVo  span[data-role=remove]').removeClass('disBlock').addClass('disNone');
                        //base_largeCategoryVal = $('#largeCategoryVal').attr('data-value');
                        //base_largeCategory = $('#largeCategory').val();
                        // 4+7省区中标价执行情况
                        fillFourPlusSevenBiddingStatus(result.productBaseInfoVo.fourPlusSevenSelectedListStatus === 1)
                        // 选择商品后，尝试关闭无权限字段
                        tryDisabledNoPermissionField()
                    },
                    error:function (XMLHttpRequest, textStatus, errorThrown) {
                        utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
                    }
                });
            }else if(this.returnValue && this.returnValue == 'add'){
                $('#scopeOfOperationVal').removeClass('only-read');
                $('#scopeOfOperationVal').prop({'readonly':false, 'disabled': false});
                $('.addTagBtn').removeClass('disNone').addClass('disBlock');
                $('#productBaseInfoVo  .addTagBtn').parent().find('input').css('display','inline-block');
                $(".empty-value").val("");//清空只读文本框和部分隐藏域的值
                //是否委托
                $(":radio[name='entrustmentProduction'][value='0']").prop("checked", "checked");
                $(".entManufacturerDiv").hide();
                // 是否税务优惠
                $(":radio[name='taxIncentives'][value='0']").prop("checked", "checked");
                //是否外省可销
                $(":radio[name='otherProvincesSale'][value='0']").prop("checked", "checked");

                $(".productrate").hide();
                //进项税率
                $("input[name='entryTaxRate']").prop("checked",false);
                //销项税率
                $("input[name='salesRate']").prop("checked",false);
                //$("#packingList").html('');
                $('#insForm').html('');
                $("#commodity_code").val("");
                unReadOnly();
                $("#indateType").val("月");
                $("#maintenancePeriod").val(90);
                //批准文件下拉框
                $("select[name='batchName']").html('<option value="">请选择</option><option value="0">商品附件</option>');
                //加载流程图
                initApprovalFlowChart("productFirst");
                // 商品管理模块 对特殊属性的高值 和易发错 的状态持久禁用，无论什么情况下。
                let checkboxs =$('input[type=checkbox][name=specialAttributes]');
                $(checkboxs).each(function (index,item) {
                    if(!$(item).attr('data-disable')){
                        $(item).removeAttr('readonly disabled')
                    }
                })
            }
            //申请编号不能修改
            $("#applicationCode").val(applicationCode);
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
};
//商品首营新增渠道信息
function addProductChannel(offlineBusinessType) {
    let rowdatas = $('#X_Table_Channel').getRowData()
    if(offlineBusinessType==1){//线下业务
        for(var i = 0; i< rowdatas.length;i++){
            $('#X_Table_Channel').XGrid('setRowData',rowdatas[i].id,{channelId:'162',channelType:5,channelTypeVal:'线下业务'});
        }
        $("#channelId").val(162);
        $("#channelIdVal").val(162);
    }else {
        var orgCode = $("#applicantOrgCode").val();
        if (orgCode == '027') {
            for(var i = 0; i< rowdatas.length;i++){
                $('#X_Table_Channel').XGrid('setRowData',rowdatas[i].id,{channelId:'312',channelType:102,channelTypeVal:'加联盟业务'});
            }
            $("#channelId").val(312);
            $("#channelIdVal").val(312);
        } else if(orgCode == '030' || orgCode == '031'){
            for(var i = 0; i< rowdatas.length;i++){
                $('#X_Table_Channel').XGrid('setRowData',rowdatas[i].id,{channelId:'303',channelType:100,channelTypeVal:'直供'});
            }
            $("#channelId").val(303);
            $("#channelIdVal").val(303);
        }else{
            for(var i = 0; i< rowdatas.length;i++){
                $('#X_Table_Channel').XGrid('setRowData',rowdatas[i].id,{channelId:'1',channelType:1,channelTypeVal:'药帮忙'});
            }
            $("#channelId").val(1);
            $("#channelIdVal").val(1);
        }
    }
}
/**
 * 主数据内容回显
 * @param json
 */
function loadProductData(json) {
    var obj = json;
    var key, value, tagName, type, arr, thisVal;
    for (x in obj) {
        key = x;
        value = obj[x];

        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).prop('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    if(arr!=null){
                        for (var i = 0; i < arr.length; i++) {
                            if (thisVal == arr[i]) {
                                $(this).prop('checked', true);
                                break;
                            }
                        }
                    }
                } else {
                    if(key=='indate'){
                        if(value == 0){
                            $(this).val('*');
                        }else if(value == -1){
                            $(this).val('-');
                        }else {
                            $(this).val(value);
                        }
                    }else{
                        $(this).val(value);
                    }
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
            }
        });
    }
    if(obj['specialAttributes'] == ''){
        $('input[name=specialAttributes]').prop('checked',false)
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //加载字典值
    showDictValue();

    //受托厂家是否显示
    initChecked();
}
/**
 * RM 2018-11-27
 * 页面渲染完获取初始数据，
 * 设置定时器是因为，如果页面进来就获取的话，对于地址三四级联动的话，地址的信息取到的都为空，
 * 用定时器的话取值正常
 */

function initChecked(){
    //判断委托产家是否显示
    var type=$("input[name='entrustmentProduction']:checked").val();
    if(type == 1){
        $(".entManufacturerDiv").show();
        $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>受托厂家');
        $('input[name=entrustmentManufacturerVal]')['addClass'](' {validate:{ required :true}}');
        $('input[name=entrustmentManufacturerVal]')['removeClass']('ignore');
    }else{
        $(".entManufacturerDiv").hide();
        $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('受托厂家')
        $('input[name=entrustmentManufacturerVal]')['addClass'](' ignore');
    }
};

/**
 * 商品主数据带回说明书
 * @param arr
 * @returns {string}
 */
function getProductHTML(arr) {
    arr = arr == null ? [] : arr;
    var left = [], right = [];
    var html = '';
    for (var i = 0; i < arr.length; i++) {
        var type = arr[i].controlTypes;
        if (type == 1) {
            //复选框
            left.push(arr[i]);
        } else {
            //文本框/
            right.push(arr[i]);
        }
    }
    var list = left.concat(right);
    for (var i = 0; i < list.length; i++) {
        var cType = list[i].controlTypes;
        var attr=list[i].checked;
        var attributeRequired = list[i].attributeRequired;
        if(attributeRequired==null||attributeRequired=="null"){
            attributeRequired="";
        }
        if (cType == 0) {
            //文本框
            html += '<div class="row attributeList">\n' +
                '        <div class="input-group">\n' +
                '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="'+attributeRequired +'">' +
                '           <input type="hidden" class="checkval" name="checked" value="'+attr+'">' +
                '           <input type="hidden" class="controlTypes" name="controlTypes" value="0">' +
                '           <input type="hidden" class="instructionsId" name="id" value="'+list[i].id+'">' +
                '           <label class="input-group-addon">';
                if(Number(attributeRequired) && Number(attributeRequired) == 1)
                {
                    //必填
                    html+='<i class="text-require" style="color:red;display: contents;">*  </i>';
                }
            html+= list[i].attribute + ':<input type="hidden" class="attributeName" name="attribute" value="' + list[i].attribute + '" /></label>' +
                '           <textarea class="form-control attributeValue only-read';
                if(Number(attributeRequired) && Number(attributeRequired) == 1)
                {
                    //必填
                    html+=' {validate:{ required :true}}';
                }
                html+='" name="attributeValue" >' + list[i].attributeValue + '</textarea>\n' +
                '       </div>\n' +
                '</div>';

        } else if (cType == 1) {
            //复选框
            html += '<div class="row attributeList">\n' +
                '        <div class="input-group">\n' +
                '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="'+attributeRequired +'">' +
                '           <input type="hidden" class="checkval" name="checked" value="'+attr+'">' +
                '           <input type="hidden" class="controlTypes" name="controlTypes" value="1">' +
                '           <input type="hidden" class="instructionsId" name="id" value="'+list[i].id+'">' +
                '           <label class="input-group-addon">';
                if(Number(attributeRequired) && Number(attributeRequired) == 1)
                {
                    //必填
                    html+='<i class="text-require" style="color:red;display: contents;">*  </i>';
                }
            html+= list[i].attribute + ':<input type="hidden" class="attributeName" name="attribute" value="' + list[i].attribute + '" /></label>' +
                '               <div class="form-control" style="height:auto;min-height:34px;">\n' +
                '                       <div class="checkbox" style="margin:0;">';
            var attrVal = list[i].checked;
            if (attrVal && attrVal != '') {
                var item = attrVal.split(',');//把复选框属性转换为数组
                var attributeValue=list[i].attributeValue;
                var checkList=attributeValue.split(',');
                for (var j = 0; j < item.length; j++) {

                    html += '<label style="margin-right: 14px;"><input type="checkbox" ';
                    if(checkList && checkList.length > 0)
                    {
                        for(var x=0;x<checkList.length;x++)
                        {
                            if(checkList[x] == item[j])
                            {
                                html+='checked="checked"';
                                break;
                            }
                        }
                    }
                    html+=' name="attributeValue" value="' + item[j] + '" class="only-read">' +
                        '                  ' + item[j] + '</label>';

                }
            }
            html += '              </div>\n' +
                '               </div>' +
                '           </div>\n' +
                '       </div>';
        }
    }
    return html;
}

/**
 * 商品包装内容显示
 * @param arr
 * @returns {string}
 */
function packingProductHTML(arr){
    var html='';
    for(var i=0;i<arr.length;i++)
    {
        html+='<div class="col-md-3">\n' +
            '            <div class="row">\n' +

            '                <div class="col-md-12">\n';
        if(arr[i].packingType == '小包装'){
            html+='<div style="height:38px;"></div>';
        }else{
            html+='<div class="input-group">\n' +
                '<div class="input-group-addon">'+arr[i].packingType+'数量</div>\n' +
                '<input type="text" class="form-control" readonly="readonly" value="'+arr[i].packingNumber+'">\n' +
                '</div>\n';
        }
        html+='                </div>\n' +
            '            </div>\n' +
            '            <div class="row">\n' +
            '                <div class="col-md-12">\n' +
            '                    <div class="input-group">\n' +
            '                        <div class="input-group-addon">'+arr[i].packingType+'长</div>\n' +
            '                        <input type="text" class="form-control" readonly="readonly" value="'+arr[i].packingLong+'">\n' +
            '                        <span class="unit-tip">CM</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '            </div>\n' +
            '            <div class="row">\n' +
            '                <div class="col-md-12">\n' +
            '                    <div class="input-group">\n' +
            '                        <div class="input-group-addon">'+arr[i].packingType+'宽</div>\n' +
            '                        <input type="text" class="form-control" readonly="readonly" value="'+arr[i].packingWide+'">\n' +
            '                        <span class="unit-tip">CM</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '            </div>\n' +
            '            <div class="row">\n' +
            '                <div class="col-md-12">\n' +
            '                    <div class="input-group">\n' +
            '                        <div class="input-group-addon">'+arr[i].packingType+'高</div>\n' +
            '                        <input type="text" class="form-control" readonly="readonly" value="'+arr[i].packingHigh+'">\n' +
            '                        <span class="unit-tip">CM</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '            </div>\n' +
            '            <div class="row">\n' +
            '                <div class="col-md-12">\n' +
            '                    <div class="input-group">\n' +
            '                        <div class="input-group-addon">'+arr[i].packingType+'重量</div>\n' +
            '                        <input type="text" class="form-control" readonly="readonly" value="'+arr[i].packingWeight+'">\n' +
            '                        <span class="unit-tip">KG</span>\n' +
            '                    </div>\n' +
            '                </div>\n' +
            '            </div>\n' +
            '        </div>';
    }
    return html;
}

/**
 * 根据说明书模板显示
 * @returns {string}
 */
function getHTML(arr) {
    arr = arr == null ? [] : arr;
    var left = [], right = [];
    var html = '';
    for (var i = 0; i < arr.length; i++) {
        var type = arr[i].attributeType;
        if (type == 1) {
            //复选框
            left.push(arr[i]);
        } else {
            //文本框
            right.push(arr[i]);
        }
    }
    var list = left.concat(right);
    for (var i = 0; i < list.length; i++) {
        var cType = list[i].attributeType;
        var attr=list[i].attributeName;
        if(attr && attr != '')
        {
            var require=list[i].attributeIsrequired;
            if (cType == 0) {
                //文本框
                html += '<div class="row attributeList">\n' +
                    '        <div class="input-group">\n' +
                    '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="'+require+'">' +
                    '           <input type="hidden" class="checkval" name="checked" value="">' +
                    '           <input type="hidden" class="controlTypes" name="controlTypes" value="0">' +
                    '           <input type="hidden" class="instructionsId" name="id" value="">' +
                    '           <label class="input-group-addon">';
                    if(Number(require) == 1)
                    {
                        //必填
                        html+='<i class="text-require" style="color:red;display: contents;">*  </i>';
                    }
                     html+= attr + ':<input type="hidden" class="attributeName" name="attribute" value="' + attr + '" /></label>' +
                    '           <textarea class="form-control attributeValue';
                    if(Number(require) == 1)
                    {
                        //必填
                        html+=' {validate:{ required :true}}';
                    }
                    html+='" name="attributeValue">' + list[i].attributeDefaultvalue + '</textarea>\n' +
                    '       </div>\n' +
                    '</div>';
            } else if (cType == 1) {
                //复选框
                var attrVal = list[i].attributeDefaultvalue;
                html += '<div class="row attributeList">\n' +
                    '        <div class="input-group">\n' +
                    '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="'+list[i].attributeIsrequired +'">' +
                    '           <input type="hidden" class="checkval"  name="checked" value="'+attrVal+'">' +
                    '           <input type="hidden" class="controlTypes" name="controlTypes" value="1">' +
                    '           <input type="hidden" class="instructionsId" name="id" value="">' +
                    '           <label class="input-group-addon">';
                    if(Number(require) == 1)
                    {
                        //必填
                        html+='<i class="text-require" style="color:red;display: contents;">*  </i>';
                    }
                html+= attr + ':<input type="hidden" class="attributeName" name="attribute" value="' + attr+ '" /></label>' +
                    '               <div class="form-control" style="height:auto;min-height:34px;">\n' +
                    '                       <div class="checkbox" style="margin:0;">';
                if (attrVal && attrVal != '') {
                    var item = attrVal.split(',');//把复选框属性转换为数组
                    for (var j = 0; j < item.length; j++) {
                        html += '<label style="margin-right: 14px;"><input type="checkbox" name="attributeValue" value="' + item[j] + '" class="only-read">' + item[j] + '</label>';
                    }
                }
                html += '              </div>\n' +
                    '               </div>' +
                    '           </div>\n' +
                    '       </div>';
            }
        }
    }
    return html;
}

/**
 * 保存数据
 */
var submint=false;
function  butSubmint() {
    //判断通用名
    if($("#search_commodity").val()=="") {
        utils.dialog({content: '商品通用名不能为空', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    //HPX 委托厂家为是，委托厂家必填
    if($('input:radio[name="entrustmentProduction"]:checked').val()==1){
        if($("#entrustmentManufacturer").val()==''){
            utils.dialog({
                title:'提示',
                content: '受托厂家不可以为空',
                okValue: '确定',
                ok: function () {}
            }).showModal()
            return false;
        }
    }
    if(submint){
        utils.dialog({content: '不可重复提交', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    var productDataVo=getSavedData();
    var applicationCode=$("#applicationCode").val();
    var workProcessKey=$("#workProcessKey").val();
    productDataVo.applicationCode=applicationCode;
    productDataVo.workProcessKey=workProcessKey;
    var data=JSON.stringify(productDataVo);
    console.log(data);
    var statues=$("#statues").val();
    // 非必填校验提示 批件附件
    if (statues!=0&&!productDataVo.approvalFileList.every(function (item) { return item.enclosureList.length > 0 })) {
        utils.dialog({
            title:'提示',
            content: '存在批件的附件为空，确认要提交吗?',
            okValue: '确定',
            ok: function () {
                finalSubmint()
            },
            cancelValue: '取消',
            cancel: function () {}
        }).show();
    }else {
        finalSubmint()
    }
    function finalSubmint () {
        submint=true;
        $.ajax({
            type:"post",
            url: "/proxy-product/product/productFirst/save",
            async : false,
            data:data,
            dataType:"json",
            contentType: "application/json",
            success: function (data) {
                var result=data.result.result;
                var list=data.result.list;
                var taskStatus=data.result.taskStatus;

                if(taskStatus!=undefined&&!taskStatus){
                    utils.dialog({
                        title: "提示",
                        content: data.result.msg,
                        width:300,
                        height:30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    return false;
                }
                var approvalFileStatus=data.result.approvalFileStatus;
                if(approvalFileStatus!=undefined&&!approvalFileStatus){
                    submint = false;
                    utils.dialog({
                        title: "提示",
                        content: data.result.msg,
                        width:300,
                        height:30
                    }).showModal();
                    return false;
                }
                if(list!=undefined&&list.length>0){
                    var ids="";
                    for(var i=0;i<list.length;i++){
                        var product=list[i];
                        if(i==0){
                            ids=ids+product.id;
                        }else {
                            ids=ids+","+product.id;
                        }
                    }
                }
                console.log(ids);
                if(result=='1'){
                    var msg = "提交成功!";
                    if($("#statues").val()==0){
                        msg = "保存成功!";
                    }
                    utils.dialog({
                        title: "提示",
                        content: msg,
                        width:300,
                        height:30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    $(".ui-dialog-close").hide();
                    return false;
                }else if(result=='2'){
                    submint=false;
                    utils.dialog({content: '商品已首营，不可重复提交', quickClose: true, timeout: 2000}).showModal();
                }else if(result=='3'||result=='4'){
                    submint=false;
                    var d=dialog({
                        title:"提示",
                        content:"商品已存在，请检查！",
                        okValue: '查看重复商品',
                        ok: function () {
                            var link_url="";
                            if(list.length==1){
                                link_url = "/proxy-product/product/productBase/toDetail?productId="+ids;
                            }else{
                                link_url = "/proxy-product/product/productBase/toRepeatList?ids="+ids;
                            }
                            utils.openTabs("look_product","重复商品", link_url);
                        },
                        cancelValue: '修改',
                        cancel: function () {
                            d.close().remove();
                            $("#productName, #search_commodity").attr("readonly",false);
                        }
                    }).showModal();
                }else if(result=='5'){
                    submint=false;
                    utils.dialog({
                        title: "提示",
                        content:"该单据编号已存在，不可重复提交。",
                        width:300,
                        height:30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    $(".ui-dialog-close").hide();
                    return false;
                }else if(result=='6'){
                    if($("#baseApplicationCode").val()==applicationCode || (!$("#baseProductId").val() && !$("#baseApplicationCode").val())){
                        $("#productName").attr("readonly",false);
                        $("#search_commodity").attr("readonly",false);
                    }
                    submint=false;
                    utils.dialog({content: '不允许经营含麻复方制剂药品，保存失败！', quickClose: true, timeout: 2000}).showModal();
                }
            },
            error:function () {
                submint=false;
                utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
}

/**
 * 获取提交数据
 * @returns {string}
 */
function getSavedData() {
    enabledProductOrganazaionForm();
    //商品名传到后台
    $('#productName').removeAttr('disabled');
    $("#productName").attr("readonly",true);
    //通用名传到后台
    $('#search_commodity').removeAttr('disabled');
    $("#search_commodity").attr("readonly",true);
    /**
     * 申请属性数据 applyFormVo
     * 基础数据 productBaseInfoVo
     * 运营属性数据 productOrganizationVo
     */
    var productDataVo = {};
    //机构信息
    var applicationAttributeVo=$("#applicationAttributeVo").serializeToJSON();
    var productOrganizationVo=$("#productOrganizationVo").serializeToJSON();
    productDataVo.productOrganizationVo=$.extend({}, applicationAttributeVo,productOrganizationVo);

    productDataVo.productOrganizationVo['channelId']=$("#channelId").val();
    productDataVo.productBaseInfoVo=$("#productBaseInfoVo").serializeToJSON();
    productDataVo.productBaseInfoVo['storageConditions'] = $('#storageConditions').val();
    productDataVo.productBaseInfoVo['largeCategory'] = $('#largeCategory').val();
    //
    let checkedLen = $('input[name=specialAttributes]:checked').length;
    if(checkedLen > 1){
        productDataVo.productOrganizationVo['specialAttributes'] = Array.from($('input[name=specialAttributes]:checked').map((index,item) => {
            return $(item).val()
        }))
    }else{
        productDataVo.productOrganizationVo['specialAttributes'] = ($('input[name=specialAttributes]:checked').val() == undefined ? '': $('input[name=specialAttributes]:checked').val())
    }
    //判断温度范围的值,一个有值。另一个也必须有值。后面的值必须大于前面的值
    if(Boolean(productDataVo.productBaseInfoVo.temperatureRange)){
        if(!Array.isArray(productDataVo.productBaseInfoVo.temperatureRange)){
            utils.dialog({
                title:'提示',
                content: '请将温度范围的值填写完整.',
                okValue: '确定',
                ok: function () {}
            }).showModal()
            return false;
        }
    }
    if(productDataVo.productBaseInfoVo['temperatureRange'] && productDataVo.productBaseInfoVo['temperatureRange'].length > 0){ // 即存在两个数值
        let temperature_range = productDataVo.productBaseInfoVo['temperatureRange'];
        if(Number(temperature_range[0]) >= Number(temperature_range[1])){
            utils.dialog({
                title:'提示',
                content: '温度范围后面的值必须大于前面的值.',
                okValue: '确定',
                ok: function () {}
            }).showModal()
            return false;
        }
    }
    if($.isArray(productDataVo.productBaseInfoVo.keyConservationCategories)){
        productDataVo.productBaseInfoVo.keyConservationCategories=productDataVo.productBaseInfoVo.keyConservationCategories.join(',');
    }
    if($.isArray(productDataVo.productOrganizationVo.specialAttributes)){
        productDataVo.productOrganizationVo.specialAttributes=productDataVo.productOrganizationVo.specialAttributes.join(',');
    }
    //可经营客户拼字符串
    // if($.isArray(productDataVo.productOrganizationVo.operatingCustomers)){
    //     productDataVo.productOrganizationVo.operatingCustomers=productDataVo.productOrganizationVo.operatingCustomers.join(',');
    // }
    //是否专供
    // if($.isArray(productDataVo.productOrganizationVo.specialProvision)){
    //     productDataVo.productOrganizationVo.specialProvision=productDataVo.productOrganizationVo.specialProvision.join(',');
    // }
    if($.isArray(productDataVo.productBaseInfoVo.storageAttribute)){
        productDataVo.productBaseInfoVo.storageAttribute=productDataVo.productBaseInfoVo.storageAttribute.join(',');
    }
    if($.isArray(productDataVo.productBaseInfoVo.temperatureRange)){
        productDataVo.productBaseInfoVo.temperatureRange=productDataVo.productBaseInfoVo.temperatureRange.join(',');
    }
    var instructions = [];
    $("#insForm .attributeList").each(function () {
        var instructionsId=$(this).find(".instructionsId").val();
        var attributeName=$(this).find(".attributeName").val();
        var checkval=$(this).find(".checkval").val();
        var controlTypes=$(this).find(".controlTypes").val();
        var attributeRequired=$(this).find(".attributeRequired").val();
        var attributeValue='';
        if(controlTypes == '1')
        {
            // 复选框
            var arr=[];
            $(this).find("[name='attributeValue']").each(function(){
                var checked=this.checked;
                if(checked)
                {
                    var value=$.trim($(this).val());
                    arr.push(value);
                }
            })
            attributeValue=arr.join(',');
        }else if(controlTypes == '0'){
            //文本框
            attributeValue=$(this).find("[name='attributeValue']").val();
        }

        var javaObj = {};
        javaObj["id"] = instructionsId;
        javaObj["attribute"] = attributeName;
        javaObj["attributeValue"] = attributeValue;
        javaObj["checked"] = checkval;
        javaObj["controlTypes"] = controlTypes;
        javaObj["attributeRequired"] = attributeRequired;
        instructions.push(javaObj);
    })
    productDataVo.instructionsList=instructions;
    //商品业务类型属性
    let tableData = $('#X_Table_Channel').getRowData()
    $(tableData).each((index, item) => {
        let checkval = []
        let _node = $('#X_Table_Channel #'+item.id+' [name=pharmacyType]')
        for (let i = 0; i < _node.length; i++) {
            if ($(_node[i]).prop('checked')) {
                checkval.push($(_node[i]).val())
            }
        }
        item['pharmacyType'] =  checkval.join()
        delete item['pharmacyTypeArr']
    })
    productDataVo.channelList= tableData;
    productDataVo.approvalFileList= $('#X_Table').getRowData();
    for(var i=0;i<productDataVo.approvalFileList.length;i++)
    {
        if(productDataVo.approvalFileList[i].enclosureList)
        {
            productDataVo.approvalFileList[i].enclosureList=JSON.parse(productDataVo.approvalFileList[i].enclosureList);
        }
    }
    tryDisabledNoPermissionField()
    return  productDataVo;
}
/**
 * 字典值回显
 */
function  showDictValue() {
    //loadUserName("applicant","applicantOrgCode");//申请人,申请人机构回显
    //商品大类
    showComValue("largeCategory","1017");
    //生产厂家--添加地址信息
    //showManufacturerInfo("manufacturer",$("#manufacturer").val());
    //生产厂家
    showComValue("manufacturer","1003");
    //包装单位
    showComValue("packingUnit","1002");
    //剂型
    showComValue("dosageForm","1001");
    // 委托厂家--添加地址信息
    //showManufacturerInfo("entrustmentManufacturer",$("#entrustmentManufacturer").val());
    // 委托厂家
    //showComValue("entrustmentManufacturer","1003");
    //存储条件
    showComValue("storageConditions","1019");
    //处方分类
    showComValue("prescriptionClassification","1016");
    //所属经营范围
    var simpleCode =$("#scopeOfOperation").val();
    var orgCode = $("#applicantOrgCode").val();
    if(simpleCode!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/queryscopebycode?orgCode='+orgCode+"&simpleCode="+simpleCode,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                console.log(data);
                if(data.code == 0)
                {
                    var simpleCodeName = "";
                    for (var i = 0;i<data.result.length;i++){
                        if (i!=data.result.length-1){
                            simpleCodeName = simpleCodeName + data.result[i].name+",";
                        } else {
                            simpleCodeName = simpleCodeName + data.result[i].name;
                        }
                    }
                    $("#scopeOfOperationVal").val(simpleCodeName);
                    $("#scopeOfOperationVal").attr("data-value",simpleCodeName);
                    $("#scopeOfOperationVal").attr("title",simpleCodeName);
                }
            }
        })
    }
    /*// 一级分类 firstCategory
    showComValue("firstCategory","1025");
    //二级分类 secondCategory
    showComValue("secondCategory","1026");
    //三级分类 thirdCategory
    showComValue("thirdCategory","1027");*/
    //商品定位
    showCommodityPosition($("#commodityPosition").val())
    // 商品定位二级
    showSecondPosition($("#commodityPosition").val(),$('#secondCommodityPosition').val());
    //首营供应商 firstBattalionSupplier
    var supplierId=$("#firstBattalionSupplier").val();
    if(supplierId!=""){
        $.ajax({
            url:'/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/firstSupplierOrgBaseById?id='+supplierId,
            type:'get',
            dataType:'json',
            success:function(data){
                if(data!=null&&data!=undefined){
                    $("#firstBattalionSupplierVal").val(data.supplierName);
                    $("#firstBattalionSupplierVal").attr("data-value",data.supplierName);
                    $("[name=firstBattalionSupplier1]").val(data.supplierName);
                }
            }
        })
    }
    //品牌
/*    var brand=$("#brand").val();
    if(brand!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/brand/queryById?id='+brand,
            type:'get',
            dataType:'json',
            success:function(data){
                console.log(data);
                if(data!=null&&data!=undefined){
                    $("#brandVal").val(data.result.brandName);
                    $("#brandVal").attr("data-value",data.result.brandName);
                }
            }
        })
    }*/

    setTimeout(function () {
        var initBaseOrgDataObj = $('#productBaseInfoVo').serializeToJSON(); // 基础属性数据
        var initProOrgDataObj = $('#productOrganizationVo').serializeToJSON(); // 运营属性数据
        var initPZWJDataObj = $('#X_Table').getRowData();  //批准文件
        window.proChangeBefore = {
            initBaseOrgDataObj: initBaseOrgDataObj,
            initProOrgDataObj:initProOrgDataObj,
            initPZWJDataObj: initPZWJDataObj
        }
    },3000)
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showComValue(obj,type) {
    var id =$("#"+obj).val();
    if(id!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querybeanbytype?type='+type+"&id="+id,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                //console.log(data);
                if(data.code == 0)
                {
                    $("#"+obj+"Val").val(data.result);
                    $("#"+obj+"Val").attr("data-value",data.result);
                    if(obj=="largeCategory"){
                        if(data.result=="中药饮片") {
                            $("#Registrant").text('化妆品备案人/注册人');
                            $(".productrate").show();
                            //Is_ZYYP_flag = true
                            $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                            $('#maintenancePeriod').prev().find('i').remove(); // 养护周期
                            $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                            $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                            $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                            $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                            $('[name=entrustmentManufacturerVal]').parents('.input-group').find('div').eq(0).html('受托厂家');
                            $('input[name=entrustmentManufacturerVal]')['addClass'](' ignore');
                        }else if(data.result=="医疗器械"){
                            $("#Registrant").text('医疗器械注册人/备案人名称');
                        }else{
                            $("#Registrant").text('化妆品备案人/注册人');
                            $(".productrate").hide();
                        }
                        if(largeCategoryArray.indexOf(data.result)>-1) {
                            $(".productrate").show();
                        }else{
                            $(".productrate").hide();
                        }
                    }
                }
            }
        })
    }
}
// 展示商品定位一级
function showCommodityPosition(currentPositionId) {
    var pageType = $("#pageType").val();
    if(currentPositionId){
        $.ajax({
            type:"get",
            url: "/proxy-product/product/dict/common/queryCommon?type=3&commonName=&pageType="+pageType,
            async : false,
            dataType:"json",
            success: function (data) {
                if (data.result && data.result.length){
                    const selectedCommodityPosition = data.result.find(item=>item.id == currentPositionId)
                    if (selectedCommodityPosition){
                        $('#commodityPosition').val(selectedCommodityPosition.id)
                        $('#commodityPositionVal').val(selectedCommodityPosition.name)
                         refreshRequiredFields(selectedCommodityPosition)
                    }
                }
            },
            error:function (XMLHttpRequest, textStatus, errorThrown) {
                // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
}
// 展示商品定位二级
function showSecondPosition(parentId,secondId) {
    var pageType = $("#pageType").val();
    if(parentId){
        $.ajax({
            type:"get",
            url: "/proxy-product/product/dict/common/queryByParentId",
            async : false,
            data:{"parentId":parentId,"pageType":pageType},
            dataType:"json",
            success: function (data) {
                let options = ['<option value="">请选择</option>']
                if (data.result){
                    options = options.concat( data.result.map(item=>{
                        return "<option value="+item.id+">"+item.name+"</option>"
                    }))
                }
                $('#secondCommodityPositionVal').html(options.join(""))
                $('#secondCommodityPositionVal').val(secondId)

            },
            error:function (XMLHttpRequest, textStatus, errorThrown) {
                // utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
}
// 商品二级定位变更时，将其同步到隐藏字段
function onSecondCommodityPositionChanged() {
    $('#secondCommodityPosition').val($('#secondCommodityPositionVal').val())
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showValue(obj,data) {
    var key =$("#"+obj).val();
    for(var i=0;i<data.length;i++){
        if(data[i].data==key){
            $("#"+obj+"Val").val(data[i].value);
        }
    }
}
/*function showManufacturerInfo(obj,manuId) {
    if(manuId!=undefined&&manuId!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querymanufactorybyId?manuId='+manuId,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if (data.code==0){
                    $("#"+obj+"Val").val(data.result.manufactoryName);
                    $("#"+obj+"Address").val(data.result.address);
                }
            }
        })
    }
}*/
/**
 * 流程图显示
 */
function  initApprovalFlowChart(key,processInstaId) {
    if(processInstaId==undefined){
        processInstaId="";
    }
    $("#workProcessKey").val(key)
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/queryTotle?key="+key+"&processInstaId="+processInstaId,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}
//重新编辑 修改记录 数据获取
function getFormData(beforeParam,afterParam,title) {
    var _str = '', regiestStr = '', storageStr = '', _str_reg = '', _str_sto = '';
    for(var ite in beforeParam){
        if(!Array.isArray(beforeParam[ite])){ // 只对输入框，取值比较
            if(afterParam[ite] != beforeParam[ite]  && !Array.isArray(afterParam[ite])){
                var thisTag = ($('input[name="'+ite+'"]').length > 0?$('input[name="'+ite+'"]'):$('select[name="'+ite+'"]')).parents('.input-group').find('.input-group-addon').text();
                if(thisTag){
                    thisTag = ((thisTag.indexOf('*') == 0)?thisTag.substring(1).trim():thisTag);
                    if(!isNaN(afterParam[ite]) || ite == 'registerDetail' || ite == 'repertoryDetail'){ // 如果取值为数字时。区域编码
                        if($('[name='+ite+']').attr('data-value') || ite == 'registerDetail' || ite == 'repertoryDetail'){ //地址下拉框时或者  街道信息
                            var optText = ($('[name='+ite+']  option:selected').length > 0?$('[name='+ite+']  option:selected').text():afterParam[ite]);
                            //地址信息一条数据时。字符串拼接
                            if(thisTag == '注册地址'){
                                regiestStr += optText;
                                _str_reg = thisTag+'的值有修改,修改为'+ regiestStr;
                            }else  if(thisTag == '仓库地址'){
                                storageStr += optText;
                                _str_sto = thisTag+'的值有修改,修改为'+ storageStr;
                            }
                        }else{
                            //输入的可能是数字，比方天数啥的。而checkbox 一般取得的val 值也是数字，所以放一起判断，
                            //如果是checkbox 就取对应值，如果不是就按输入框的值取


                            var t = ($('[name='+ite+']:checked').next().length > 0?$('[name='+ite+']:checked').next().text():($('[name='+ite+']').find('option:selected').length > 0?$('[name='+ite+']').find('option:selected').text():$('[name='+ite+']').val())); //checkbox select或者input输入框
                            var beforeT = ($('[name='+ite+']:checked').next().length > 0?$('[name='+ite+']:not(:checked)').next().text():($('[name='+ite+']').find('option:selected').length > 0?getOptText($('[name='+ite+']'),beforeParam[ite]):beforeParam[ite]));
                            _str += thisTag+'的值有修改,【'+beforeT+'】修改为【'+t+'】;\n';
                        }
                    }else{
                        if(_str.indexOf(thisTag)>=0 && thisTag.indexOf('地址')  < 0){
                            //自动补全的框，formdata里前后比较的时候会有重复的数据，需要清除第一条数据，因为第一条数据拿到的都是ID 值
                            // 如果是仓库地址的时候例外，因为可能有多条仓库地址
                            _str = _str.replace(_str.substring(_str.indexOf(thisTag),_str.lastIndexOf('\n')+1),'')
                        }
                        _str += thisTag+'的值有修改,【'+beforeParam[ite]+'】修改为【'+afterParam[ite]+'】;\n';

                    }
                }
            }
        }
    }
    _str += (_str_reg?_str_reg+'\n':'')+(_str_sto?_str_sto+'\n':'');
    return (_str != ''?title+'信息有变更:\n'+_str:_str);
}
/**
 * 审核按钮操作
 */
function submitAuditInfo(auditStatus,auditOpinion,apply) {
    /**
     * 编辑数据修改记录
     */
    var modifyBaseOrgDataObj = $('#productBaseInfoVo').serializeToJSON(); // 基础属性数据
    var modifyProOrgDataObj = $('#productOrganizationVo').serializeToJSON(); // 运营属性数据
    var modifyPZWJDataObj = $('#X_Table').getRowData();  //批准文件
    window.proChangeAfter = {
        initBaseOrgDataObj: modifyBaseOrgDataObj,
        initProOrgDataObj:modifyProOrgDataObj,
        initPZWJDataObj: modifyPZWJDataObj
    };
    var changeDetails = '';
    //基础属性
    changeDetails += utils.getFormData(window.proChangeBefore.initBaseOrgDataObj,window.proChangeAfter.initBaseOrgDataObj,'基础属性','productBaseInfoVo')
    //运营属性
    changeDetails += utils.getFormData(window.proChangeBefore.initProOrgDataObj,window.proChangeAfter.initProOrgDataObj,'运营属性','productOrganizationVo');
    // 批准文件
    // changeDetails += utils.getRowData(window.proChangeBefore.initPZWJDataObj,window.proChangeAfter.initPZWJDataObj,'批准文件','X_Table')
    changeDetails += utils.getRowData(window.proChangeBefore.initPZWJDataObj,window.proChangeAfter.initPZWJDataObj,'批准文件','X_Table').length>0?"批准文件的值有修改":""
        //console.log(str)

    parent.showLoading();
    var productData = {};
    if(auditStatus==2){
        productData=getSavedData();//表单数据
    }
    var applicationCode=$("#applicationCode").val();
    var workProcessKey=$("#workProcessKey").val();
    var taskId=$("#taskId").val();
    productData.applicationCode=applicationCode;
    productData.workProcessKey=workProcessKey;
    if (changeDetails != ''){
        productData.changeDetails=changeDetails;
    }
    productData.taskId=taskId;
    productData.auditStatus=auditStatus;
    productData.auditOpinion=auditOpinion;
    productData.updataStutus="1";
    //商品编码与机构编码
    productData.orgCode=$("#applicantOrgCode").val();
    productData.productCode=$("#commodity_code").val();
    //console.log(productData);
    var data=JSON.stringify(productData);
    //console.log(data);
    $.ajax({
        type:"post",
        url: "/proxy-product/product/productFirst/auditProduct",
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
        	 console.log(data);
             var msg = data.result.msg;
            var approvalFileStatus=data.result.approvalFileStatus;
            if(approvalFileStatus!=undefined&&!approvalFileStatus){
                utils.dialog({
                    title: "提示",
                    content: data.result.msg,
                    width:300,
                    height:30
                }).showModal();
                return false;
            }
             if("auditError"==msg){
            	 var result=data.result.result;
                 var list=data.result.list;
                 if(list!=undefined&&list.length>0){
                     var ids="";
                     for(var i=0;i<list.length;i++){
                         var product=list[i];
                         if(i==0){
                             ids=ids+product.id;
                         }else {
                             ids=ids+","+product.id;
                         }
                     }
                 }
                 console.log(ids);
                 if('2'==result){
                     utils.dialog({content: '商品已首营，不可重复提交', quickClose: true, timeout: 2000}).showModal();
                     return false;
                 }else if('3'==result || '4'==result){
                     var d=dialog({
                         title:"提示",
                         content:"商品已存在，请检查！",
                         okValue: '查看重复商品',
                         ok: function () {
                             var link_url="";
                             if(list.length==1){
                                 link_url = "/proxy-product/product/productBase/toDetail?productId="+ids;
                             }else{
                                 link_url = "/proxy-product/product/productBase/toRepeatList?ids="+ids;
                             }
                             utils.openTabs("look_product","重复商品", link_url);
                         },
                         cancelValue: '修改',
                         cancel: function () {
                             d.close().remove();
                             $("#productName, #search_commodity").attr("readonly",false);
                         }
                     }).showModal();
                     return false;
                 }else if('5' ==result){
                     utils.dialog({
                         title: "提示",
                         content:"该单据编号已存在，不可重复提交。",
                         width:300,
                         height:30,
                         okValue: '确定',
                         ok: function () {
                             utils.closeTab();
                         }
                     }).showModal();
                     $(".ui-dialog-close").hide();
                     return false;
                 }else if(result=='6'){
                	 if($("#baseApplicationCode").val()==applicationCode || (!$("#baseProductId").val() && !$("#baseApplicationCode").val())){
                		  $("#productName").attr("readonly",false);
                          $("#search_commodity").attr("readonly",false);
                	  }
                	 utils.dialog({content: '不允许经营含麻复方制剂药品，保存失败！', quickClose: true, timeout: 2000}).showModal();
                	 return false;
                 }
        	 }else if(msg=="sucess"){
                if(apply!=undefined&&apply=="apply"){//审批人驳回到申请人重新提交
                    msg = "提交成功！";
                }else if(auditStatus == 2){
                    msg = "恭喜审核通过！";
                }else if(auditStatus == 4){
                    msg = "驳回成功！";
                }else if(auditStatus == 3){//申请人关闭流程
                    msg = "流程已关闭！";
                }
        	 }
            utils.dialog({
                title: "提示",
                content: msg,
                width:300,
                height:30,
                okValue: '确定',
                ok: function () {
                    utils.closeTab();
                }
            }).showModal();
            $(".ui-dialog-close").hide();
            return false;
        },
        error:function () {
            utils.dialog({content: '审核失败', quickClose: true, timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}

function  loadProductInstruction(baseProductId) {
    $.ajax({
        type:"post",
        url: "/proxy-product/product/productInstruction/toList",
        async : false,
        data:{"correlationId":baseProductId,"type":0},
        dataType:"json",
        success: function (data) {
            console.log(data)
            if(data.code == 0)
            {
                var result = data.result;
                var html = getProductHTML(result);
                $("#insForm").html(html);
            }else{
                $("#insForm").html('');
            }
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
function checkProductInstruction(){
    if($(".attributeList").length > 0)
    {
        var flag=false;
        $(".attributeList").each(function () {
            var controlTypes=$.trim($(this).find(".controlTypes").val());
            var attributeRequired=$.trim($(this).find('.attributeRequired').val());
            if(attributeRequired == '1')
            {
                switch (controlTypes)
                {
                    case '1':
                        //复选框
                        var len=$(this).find('input[type="checkbox"]:checked').length;
                        if(len < 1)
                        {
                            flag=true;
                            return false;
                        }
                        break;
                    case '0':
                        //文本框
                        var val=$.trim($(this).find('textarea').val());
                        if(val == '' || !val)
                        {
                            flag=true;
                            return false;keyConservationCategories
                        }
                        break;
                }
            }
        })
        if(flag)
        {
            utils.dialog({
                title: "提示",
                content: "请选择或填写说明书属性",
                width:300,
                height:30,
                okValue: '确定',
                ok: function () {}
            }).showModal();
            return false;
        }
    }
    return true;
}

// 获取select框修改前的值
function getOptText(el,ev) {
    var _optText = '';
    var opts = $(el).find('option');
    $(opts).each(function (k,v) {
        if( $(this).val() == ev){ _optText =  $(this).text();}
    })
    return _optText
}
// 对Date的扩展，将 Date 转化为指定格式的String
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
// 例子：
// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
// (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
Date.prototype.Format = function (fmt) { //author: meizz
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}


function dimensionSalesPriceYnChange(el) {
    let _thisVal = $(el).val()
    if (_thisVal == '0') {
        $(el).parents('tr').find('[row-describedby="pharmacyType"] input').prop('disabled', true).prop('checked',false)
    } else {
        $(el).parents('tr').find('[row-describedby="pharmacyType"] input').prop('disabled', false)
    }

}


/**
 *  回显 4+7 省区中标价执行情况
 * @param isEnrolledFourPlusSeven Boolean 是否全国 4 + 7 入选
 */
function fillFourPlusSevenBiddingStatus(isEnrolledFourPlusSeven){
    if (isEnrolledFourPlusSeven){
        // 红色必填标志
        $('[name=fourPlusSevenBidStatus]').parents('.input-group').find('div:first').addClass("require")
        $('[name=fourPlusSevenBidStatus]').parents('.input-group').find('div:first').html("<i class=\"text-require\">*</i>4+7省区中标价执行情况")
        // 默认值
        $('[name=fourPlusSevenBidStatus]:first').prop('checked',0)
        $('[name=fourPlusSevenBidStatus]:last').prop('checked',1)
        // 添加 changeapplyflag 标志，方便后续添加 编辑按钮
        // $('[name=fourPlusSevenBidStatus]:last').attr('changeapplyflag',"")

        $('[name=fourPlusSevenBidStatus]:first').removeAttr("disabled")
        $('[name=fourPlusSevenBidStatus]:last').removeAttr("disabled")



    }else {
        // 取消红色必填标志
        $('[name=fourPlusSevenBidStatus]').parents('.input-group').find('div:first').removeClass("require")
        $('[name=fourPlusSevenBidStatus]').parents('.input-group').find('div:first').html("4+7省区中标价执行情况")
        // 默认值
        $('[name=fourPlusSevenBidStatus]:first').prop('checked',0)
        $('[name=fourPlusSevenBidStatus]:last').prop('checked',0)
        // 移除 changeapplyflag 标志，避免后续添加 编辑按钮
        // $('[name=fourPlusSevenBidStatus]:last').attr('changeapplyflag',null)

        $('[name=fourPlusSevenBidStatus]:first').attr("disabled","disabled")
        $('[name=fourPlusSevenBidStatus]:last').attr("disabled","disabled")

    }
}

function tryDisabledNoPermissionField(){
    if (!$("#zhiGuanUpdateFlag").val()){
        // 由于非质管员不可编辑质管字段，此处关闭质管相关字段
        disabledQAField()
    }else {
        // 由于质管员仅可编辑质管字段，此处关闭非质管字段
        disabledNonQAField()
    }
}

function disabledQAField(){
    /**
     * 关闭质管字段
     */
    // 件包装数
    $("[name=piecePackageNumber]").attr("disabled",false)
    $("[name=piecePackageNumber]").closest("div").find("input").attr("disabled",false)
    $("[name=piecePackageNumber]").closest("div").find("button").attr("disabled",false)
    // 中包装数
    $("[name=mediumPackageNumber]").attr("disabled",false)
    $("[name=mediumPackageNumber]").closest("div").find("input").attr("disabled",false)
    $("[name=mediumPackageNumber]").closest("div").find("button").attr("disabled",false)
    // 档案号
    $("[name=fileNumber]").attr("disabled",true)
    // 是否监管
    $("[name=superviseState]").attr("disabled",true)
    // 养护类型
    $("[name=keyConservationCategories]").attr("disabled",true)
    // 特殊属性
    $("[name=specialAttributes]").attr("disabled",true)
    // 批准文件按钮，如：新增行、删除行、批量上传附件
    $("#rowOperationsDiv").find("button").attr("disabled",true)
    // 关闭批准文件中的输入元素
    $("#X_Table input,#X_Table button ,#X_Table select").attr("disabled",true)
    /**
     * 移除质管字段的必填标志
     */
    // 件包装数
    $("[name=piecePackageNumber]").closest(".input-group").find(".input-group-addon").removeClass("require")
    $("[name=piecePackageNumber]").closest("div").find("input").removeClass('{validate:{ required :true}}')
    $("[name=piecePackageNumber]").closest(".input-group").find(".input-group-addon .text-require").remove("i")
    // 中包装数
    $("[name=mediumPackageNumber]").closest(".input-group").find(".input-group-addon").removeClass("require")
    $("[name=mediumPackageNumber]").closest("div").find("input").removeClass('{validate:{ required :true}}')
    $("[name=mediumPackageNumber]").closest(".input-group").find(".input-group-addon .text-require").remove("i")
    // 是否监管
    $("[name=superviseState]").closest(".input-group").find(".input-group-addon").removeClass("require")
    $("[name=superviseState]").closest(".input-group").find(".input-group-addon .text-require").remove("i")
    // 养护类型
    $("[name=keyConservationCategories]").closest(".input-group").find(".input-group-addon").removeClass("require")
    $("[name=keyConservationCategories]").closest(".input-group").find(".input-group-addon .text-require").remove("i")
}

function disabledNonQAField(){
    /**
     * 关闭运营属性表单中的所有字段
     */
    $("#productOrganizationVo input,#productOrganizationVo botton,#productOrganizationVo select").attr("disabled",true)
    /**
     * 关闭业务类型表单中的所有字段
     */
    $("#productChannelVo input,#productChannelVo botton,#productChannelVo select").attr("disabled",true)
    /**
     * 仅开启运营属性中的质管字段
     */
    // 件包装数
    $("[name=piecePackageNumber]").attr("disabled",false)
    $("[name=piecePackageNumber]").closest("div").find("input").attr("disabled",false)
    $("[name=piecePackageNumber]").closest("div").find("button").attr("disabled",false)
    // 中包装数
    $("[name=mediumPackageNumber]").attr("disabled",false)
    $("[name=mediumPackageNumber]").closest("div").find("input").attr("disabled",false)
    $("[name=mediumPackageNumber]").closest("div").find("button").attr("disabled",false)
    // 养护类型
    $("[name=keyConservationCategories]").attr("disabled",false)
    // 档案号
    $("[name=fileNumber]").attr("disabled",false)
    // 是否监管
    $("[name=superviseState]").attr("disabled",false)
    // 特殊属性
    $("[name=specialAttributes]").attr("disabled",false)
}

/**
 * 开启商品运营属性的全部字段，以便 form 表单收集数据
 */
function enabledProductOrganazaionForm() {
    $('#productOrganizationVo input,#productOrganizationVo botton,#productOrganizationVo select').removeAttr('disabled');
}
