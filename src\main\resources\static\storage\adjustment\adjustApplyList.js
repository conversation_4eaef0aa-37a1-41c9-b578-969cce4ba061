$(function () {

    // var lastHref = location.href
    //   console.log(lastHref);
    // var reff = document.referrer;
    // console.log(reff)

    $("#table_a").trigger("reloadGrid");
    /* 日期初始化 */
    z_utils.initDate('begint', 'endt')

    /* tabs 切换 */
    z_utils.tableCut('flex');

    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    /* table_a */
    var colName = ['id','机构名称', '调账申请单号', '关联单据类型', '单据编号', '部门名称', '申请人', '申请日期', '调损不含税成本金额', '调溢不含税成本金额', '调账原因',
        '单据状态'
    ];
    var colModel = [
        {
            name: 'id',
            index: 'id',
            hidden:true,
            hidegrid:true
        },
        {
            name: 'orgName',
            index: 'orgName',
            width: 260,
        }, {
            name: 'adjustmentApplyCode',
            index: 'adjustmentApplyCode',
            width: 200,
        }, {
            name: 'adjustType',
            index: 'adjustType',
            formatter: function (e) {
                if (e == '1') {
                    return '销售订单'
                } else if (e == '3') {
                    return '损溢申请单'
                }else if (e == '4') {
                    return '移库申请单'
                }else if (e == '6') {
                    return '报损申请单'
                }
                else {
                    return ''
                }


            }
        }, {
            name: 'businessCode',
            index: 'businessCode',
            width: 230,
        }, {
            name: 'departmentName',
            index: 'departmentName',
            hidden:true, hidegrid:true
        }, {
            name: 'createUser',
            index: 'createUser'
        }, {
            name: 'createTime',
            index: 'createTime',
            formatter: dateFormatter
        }, {
            name: 'adjustLossAmount',
            index: 'adjustLossAmount',
            width: 200,
        }, {
            name: 'adjustProfitAmount',
            index: 'adjustProfitAmount',
            width: 200,
        }, {
            name: 'adjustReason',
            index: 'adjustReason'
        }, {
            name: 'status',
            index: 'status',
            formatter: function (e) {
                if (e == '1') {
                    return '待处理'
                } else if (e == '2') {
                    return '审核中'
                }else if (e == '3') {
                    return '已驳回'
                }else if (e == '4') {
                    return '已完成'
                }else if (e == '5') {
                    return '已取消'
                }
            }
        }];
    // 加载总计
    getQulifiledNum ();
    $('#table_a').XGrid({
        url:"/proxy-storage/storage/adjustment/findList",
        postData:{startTime:$("#begint").val(),endTime:$('#endt').val()},
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'id',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            utils.openTabs("adjustInfo","调账申请单详情", "/proxy-storage/storage/adjustment/toInfo?id="+obj.id,{},function () {
                $('#searchBtn').trigger('click')
            });
        },
        attachRow:true,
        gridComplete: function () {
            /*      setTimeout(function (param) {
                    /!* 合计写入 *!/
                    var data = $('#table_a').XGrid('getRowData');
                    var sum_ele = $('#table_a_sum .sum');
                    //console.log(sum_ele);
                    $(sum_ele[0]).text(totalTable(data,'text12'));
                    $(sum_ele[1]).text(totalTable(data,'text15'));
                    $(sum_ele[2]).text(totalTable(data,'text16'));
                  }, 200)*/
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['adjustLossAmount','adjustProfitAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(z_utils.totalTable(data,item))
            });
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    };
    /* table_b */
    /*  $('#table_b').XGrid({
        url: "/proxy-storage/storage/adjustment/findDetailList",
          postData:{startTime:$("#begint").val(),endTime:$('#endt').val()},
        colNames: ['id','调账申请单号', '行号', '申请日期','商品编码', '条码','商品大类', '商品名称', '商品规格', '生产厂家', '产地',
          '单位', '库房名称', '批号', '生产日期', '有效期至',  '调账数量', '不含税成本单价', '不含税成本金额'
        ],
        colModel: [
          {
            name: 'id',
            index: 'id',
            key:true,
            hidden:true,
            hidegrid:true
          },
          {
          name: 'adjustmentApplyCode',
          index: 'adjustmentApplyCode'
        }, {
          name: 'sortNo',
          index: 'sortNo'
        }, {
          name: 'createTime',
          index: 'createTime',
          formatter: dateFormatter
      },{
          name: 'productCode',
          index: 'productCode'
        }, {
          name: 'smallPackageBarCode',
          index: 'smallPackageBarCode'
        }, {
                name: 'drugClass',
                index: 'drugClass'
            }, {
          name: 'productName',
          index: 'productName'
        }, {
          name: 'specifications',
          index: 'specifications'
        }, {
          name: 'manufacturerName',
          index: 'manufacturerName'
        }, {
          name: 'producingArea',
          index: 'producingArea'
        }, {
          name: 'packingUnitValue',
          index: 'packingUnitValue'
        }, {
          name: 'storeName',
          index: 'storeName',
                formatter: function (e) {
                    if (e == '1') {
                        return '合格库'
                    } else if (e == '2') {
                        return '不合格库'
                    }else if (e == '3') {
                        return '暂存库'
                    }
                }

        }, {
          name: 'batchCode',
          index: 'batchCode'
        }, {
          name: 'manufactureTime',
          index: 'manufactureTime',
            formatter: dateFormatter
        }, {
          name: 'expiryTime',
          index: 'expiryTime'
        }, {
          name: 'adjustmentNumber',
          index: 'adjustmentNumber'
        }, {
          name: 'costPrice',
          index: 'costPrice'
        }, {
          name: 'costAmount',
          index: 'costAmount'
        }],
          rowNum: 20,
          rowList:[20,50,100],
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_b',
        gridComplete: function () {
          /!*setTimeout(function (param) {
            *!//!* 合计写入 *!//!*
            var data = $('#table_b').XGrid('getRowData');
            var sum_ele = $('#table_b_sum .sum');
            //console.log(sum_ele);
            $(sum_ele[0]).text(totalTable(data,'text21'));
            $(sum_ele[1]).text(totalTable(data,'text24'));
            $(sum_ele[2]).text(totalTable(data,'text25'));
            $(sum_ele[3]).text(totalTable(data,'text26'));
            $(sum_ele[4]).text(totalTable(data,'text29'));
          }, 200)*!/
        }
      });*/

    // 筛选列
    $("#set_tb_rows").on('click',function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .table-box .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })


    $('.panel-title').on('click',function(){
        //alert('777')
    })

    /* 商品名称 搜索提示（只显示5条） */
    $('#commodity').Autocomplete({
        serviceUrl: '/orderReturn/orderReturnController/getAllProductList?orgCode=002', //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#productCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            console.log(query, suggestions);
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
            $("#commodity").val('');
            $("#productCode").val('');
        }
    });


    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        //获取form数据
        var formData = $('#form_a').serializeToJSON();
        //更新表格数据
        //列表
        $('#table_a').XGrid('setGridParam', {
            url:"/proxy-storage/storage/adjustment/findList",
            postData: formData,
            page: 1
        }).trigger("reloadGrid");
        getQulifiledNum ();

        /*    //详情
            $('#table_b').XGrid('setGridParam', {
              url:"/proxy-storage/storage/adjustment/findDetailList",
              postData: formData,
              page: 1
            }).trigger("reloadGrid");*/

    });

    /* 新增 */
    $('#addRowData').on('click', function () {
        utils.openTabs("toAddAdjust","新增调账申请单", "/proxy-storage/storage/adjustment/toAdd",{},function(){
            $('#searchBtn').trigger('click')
        });
    });

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            var formData = $('#form_a').serializeToJSON();
            formData["colName"] = colName;
            formData["colNameDesc"] = colNameDesc;

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            var exportLimitRow = Number($("#exportLimitRow").val());
            if (data.length <= exportLimitRow && data.length >0){
                /*if (!data.length) {
                    data = [data];
                }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                formData["selectData"] = data;
            } else {
                var rows = $('#' + tableId)[0].p.records;
                if(rows > exportLimitRow) {
                    utils.dialog({
                        title: '提示',
                        width: 200,
                        height: 40,
                        cancel: false,
                        content: '您本次导出的数据量过大（已超过'+exportLimitRow+'条），不允许导出，请缩小导出范围。',
                        okValue: '确定',
                        ok: function () {
                            //utils.closeTab();
                        }

                    }).showModal();
                    return;
                }
                data = '';
            }
            console.log(colName);

            switch (getTabInd()) {
                case 0:
                    httpPost("/proxy-storage/storage/adjustment/exportApplyList", formData);
                    break;
                case 1:
                    httpPost("/proxy-storage/storage/adjustment/exportApplyDetailList", formData);
                    break;
            }
        });
    });


    //获取当前tab  的下标 销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    function getTabInd() {
        for (var i = 0; i < $('.saleOrderList_tabs li').length; i++) {
            if ($('.saleOrderList_tabs li').eq(i).hasClass('active')) {
                _ind = $('.saleOrderList_tabs li').eq(i).index();
            }
        }
        return _ind;
    };

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var commodity_d = $(e.target).prev('input').val();
        utils.dialog({
            title: '商品列表',
            url: '/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.8,

            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#commodity').val(data.productName)
                    $("#productCode").val(data.productCode);
                }
                $('iframe').remove();
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })
    function getQulifiledNum () {
        //获取form数据
        var formData = $("#form_a").serializeToJSON();
        //加载总数量
        $.ajax({
            url: '/proxy-storage/storage/adjustment/findAdjustAmount',
            dataType: 'json',
            timeout: 8000,
            data:formData,
            success: function (data) {
                if (data.code==0){
                    var result = data.result;
                    if(result!=null){
                        $("#totalAdjustLossAmount").text(Number(result.totalAdjustLossAmount).toFixed(2));
                        $("#totalAdjustProfitAmount").text(Number(result.totalAdjustProfitAmount).toFixed(2));
                    }else {
                        $("#totalAdjustLossAmount").text('0.00');
                        $("#totalAdjustProfitAmount").text('0.00');
                    }
                }
            },
            error: function () {
                $("#totalAdjustLossAmount").text('0.00');
                $("#totalAdjustProfitAmount").text('0.00');
                utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
})