/**
 * Created by 沙漠里的红裤头 on 2019/6/28
 */
var path = '/proxy-storage/storage/unqualifiedDestructionApply';

function initDate(param) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    var month = new_date.getMonth() + 1;
    var date = new_date.getDate();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    //console.log(new_date, year, month, date);
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#begint').val(year + '-' + month + '-01');
    $('#endt').val(year + '-' + month + '-' + date);
}

$(function () {
    $('div[fold=head]').fold({sub: 'sub'});

    initDate();

    //不合格品报损申请单列表 、 不合格品报损申请单商品明细    tab 切换
    $('.saleOrderList_tabs>li').on('click', function () {
        var $this = $(this), $nav_content = $this.parents('.panel-body').next();
        $this.addClass('active').siblings().removeClass('active');
        //$nav_content.children('div').eq($this.index()).show().siblings().hide();
        $nav_content.children('div').eq($this.index()).css('display', 'flex').siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');
    });

    var colName = ['机构名称', '销毁申请单号', '部门名称', '申请人', '申请日期', '单据状态'];
    var colModel = [
        {name: 'orgName',index: 'orgName'},
        {name: 'unqualifiedDestructionApplyCode',index: 'unqualifiedDestructionApplyCode',width:200,
            formatter:function (e,row,data) {
            	return '<a name="toDetail" code="'+e+'" class="to_apply">'+e+'</a>';
            },
        },
        {name: 'departmentName',index: 'departmentName', hidden:true, hidegrid:true},
        {name: 'createUser',index: 'createUser',},
        {name: 'applyTime',index: 'applyTime',
            formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }
        },
        { name: 'documentsStateS', index: 'documentsStateS'//,
        }
    ];
    $('#X_Tablea').XGrid({
        url: path + "/findForPage",
        postData:{
        	unqualifiedDestructionApplyCode:$('#unqualifiedDestructionApplyCode').val(),
        	startDate:$('#begint').val(),
            endDate:$('#endt').val(),
            productCode:$("#productCode").val(),
            largeCategory:$("#largeCategory").find("option:selected").text(),
            documentsState:$("#documentsState").find("option:selected").val()
        },
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'unqualifiedDestructionApplyCode',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid-pager-a',
        ondblClickRow: function (id, dom, obj, index, event) {
        	console.log('双击行事件', id, dom, obj, index, event);
            utils.openTabs("unqualifiedDestructionApplyDetail", "不合格品销毁申请单详情"
            		, "/proxy-storage/storage/unqualifiedDestructionApply/findDetil?unqualifiedDestructionApplyCode=" + $(obj.unqualifiedDestructionApplyCode).attr("code"));
        },
        onSelectRow: function (id, dom, obj, index, event) {
        	var $ele = $(event.target);
        	if($ele.attr('name')==='toDetail'){
        		var code = $(event.target).attr("code");
            	utils.openTabs("unqualifiedDestructionApplyDetail", "不合格品销毁申请单详情", "/proxy-storage/storage/unqualifiedDestructionApply/findDetil?unqualifiedDestructionApplyCode=" + code);
        	}
        },
        gridComplete: function () {},
    });

    //不合格品报损申请单商品明细
    $('#X_Tableb').XGrid({
        url: path + "/findDetilList",
        colNames: ['销毁申请单号','销毁原因','销毁地点','销毁方式','商品编码', '原商品编码','商品大类','商品名称',
                    '商品规格', '生产厂家', '商品产地', '单位',  '库房名称', '业务类型', '批号', '生产日期', '有效期至','销毁数量'
        ],
        colModel: [
            {name: 'unqualifiedDestructionApplyCode',index: 'unqualifiedDestructionApplyCode'},
            {name: 'destructionReason',index: 'destructionReason' },
            {name: 'destructionPlace',index: 'destructionPlace'},
            {name: 'destructionMethod',index: 'destructionMethod'},
            {name: 'productCode',index: 'productCode' },
            {name: 'oldProductCode',index: 'oldProductCode'},
            {name: 'largeCategory',index: 'largeCategory'},
            {name: 'productName',index: 'productName'},
            {name: 'specifications',index: 'specifications'},
            {name: 'manufacturer',index: 'manufacturer',},
            {name: 'productOrigin',index: 'productOrigin',},
            {name: 'productUnit',index: 'productUnit'},
            {name: 'storeHouseName',index: 'storeHouseName',
                formatter: function (e) {
                    return '不合格库'
                }
            },
            {name: 'channelId', index: 'channelId'},
            {name: 'batchCode', index: 'batchCode'},
            {name: 'productionTime',index: 'productionTime' ,
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            },
            {name: 'periodValidity',index: 'periodValidity',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            },
            {name: 'causticExcessiveNumber',index: 'causticExcessiveNumber'}
        ],
        altRows: true, //设置为交替行表格,默认为false
        rowNum: 20,
        rowList:[20,50,100],
        selectandorder: true,//是否展示序号，多选
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件', id, dom, obj, index, event);
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //console.log('单机行事件', id, dom, obj, index, event);
        },
        pager: '#grid-pager-b'
    });

    //设置显示列
    $("#set_tables_rowa").click(function () {
        switch (getTabInd()) {
            case 0:
                $('#X_Tablea').XGrid('filterTableHead');
                break;
            case 1:
                $('#X_Tableb').XGrid('filterTableHead');
                break;
        }
    });

    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        //获取form数据
        var data = $('#form_a').serializeToJSON();
        console.log(data);
        //更新表格数据
        var $table_id = $('#nav_content .active .XGridBody table').attr('id');
        if ($table_id == 'X_Tablea') {
            //列表
            $('#X_Tablea').XGrid('setGridParam', {
            	url: path + "/findForPage",
                postData:{
                	unqualifiedDestructionApplyCode:$('#unqualifiedDestructionApplyCode').val(),
                	startDate:$('#begint').val(),
                    endDate:$('#endt').val(),
                    productCode:$("#productCode").val(),
                    largeCategory:$("#largeCategory").find("option:selected").text(),
                    documentsState:$("#documentsState").find("option:selected").val()
                },
            }).trigger("reloadGrid");
        }
    });

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#X_Tablea').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });
            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            var exportLimitRow = Number($("#exportLimitRow").val());
            if (data.length <= exportLimitRow && data.length >0) {
                /* if (!data.length) {
                     data = [data];
                 }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        if("unqualifiedDestructionApplyCode" == val){
                            var htmlAObj = $(item[val]);
                            new_item[val] = htmlAObj.text();
                        } else {
                            new_item[val] = item[val]
                        }
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                var rows = $('#' + tableId)[0].p.records;
                if(rows > exportLimitRow) {
                    utils.dialog({
                        title: '提示',
                        width: 200,
                        height: 40,
                        cancel: false,
                        content: '您本次导出的数据量过大（已超过'+exportLimitRow+'条），不允许导出，请缩小导出范围。',
                        okValue: '确定',
                        ok: function () {
                            //utils.closeTab();
                        }

                    }).showModal();
                    return;
                }
                data='';
            }
            console.log(colName);

            //获取form数据
            var formData = {
        		unqualifiedDestructionExecutionCode:$('#unqualifiedDestructionExecutionCode').val(),
        		startDate:$('#begint').val(),
                endDate:$('#endt').val(),
                productCode:$("#productCode").val(),
                largeCategory:$("#largeCategory").find("option:selected").val(),
                documentsState:$("#documentsState").find("option:selected").val(),
                colName:colName,
                colNameDesc:colNameDesc,
                selectData:data
            }
            httpPost(path + "/exportList", formData);
        });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#productCode').val(data.productCode);
                    $('#manufName').val(data.manufacturerVal);
                    $('#spec').val(data.specifications);
                }
            },
            oniframeload: function () {}
        }).showModal();
        return false;
    })
})

//商品名称 搜索
$('#input_goodName').Autocomplete({
    serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$("#orgCode").val(), //异步请求
    paramName: 'param',//查询参数，默认
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    transformResult: function (response) {
        return {
            suggestions: $.map(response.result.list, function (dataItem) {
                return {
                    value: dataItem.productName,
                    data: dataItem.productCode,
                    manufacturerVal: dataItem.manufacturerVal,
                    specifications: dataItem.specifications,
                };
            })
        };
    },
    triggerSelectOnValidInput: false, // 必选
    // multi: true, //多选要和delimiter一起使用
    // delimiter: ',',
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function (result) {
        //选中回调
        $("#productCode").val(result.data)
        $("#manufName").val(result.manufacturerVal);
        $("#spec").val(result.specifications);

    },
    onSearchStart: function (params) {
        // console.log('检索开始回调', params)
    },
    onSearchComplete: function (query, suggestions) {

    },
    onSearchError: function (query, jqXHR, textStatus, errorThrown) {
        //查询失败回调
        console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function (container) {
        // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function (params, suggestions) {
        $("#productCode").val("");
        $("#input_goodName").val("");
        console.log(params, suggestions);
        console.log('没选中回调函数');
        if ($('#input_goodName').val()==''){
            $('#manufName').val('');
            $('#spec').val('');
        }
    }
});

//获取当前tab  的下标
function getTabInd() {
    for (var i = 0; i < $('.saleOrderList_tabs li').length; i++) {
        if ($('.saleOrderList_tabs li').eq(i).hasClass('active')) {
            _ind = $('.saleOrderList_tabs li').eq(i).index();
        }
    }
    return _ind;
};


$('#createBtn').on('click', function (e) {
	utils.openTabs('createunqualifiedDestructionApply-WMS','新建不合格品销毁申请单','/proxy-storage/storage/unqualifiedDestructionApply/toSave');
});

$('a[name="toDetail"]').on('click', function(){
	var code = $(this).attr("code");
	utils.openTabs("unqualifiedDestructionApplyDetail", "不合格品销毁申请单详情", "/proxy-storage/storage/unqualifiedDestructionApply/findDetil?unqualifiedDestructionApplyCode=" + code);

});