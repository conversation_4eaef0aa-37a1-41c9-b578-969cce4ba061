$(function () {
	var totalTable = z_utils.totalTable;
    var colName = ['承运单位名称', '承运单位编码', '法人代表', '地址', '联系人', '联系电话', '许可证号','许可证有效期',
        '营业执照号','营业期限至','组织机构代码证','有效期至','税务登记证号','委托运输协议号','开户户名','银行账户','质量体系调查表','备注'];
    var colModel = [{
        name: 'carrierName',
        index: 'carrierName'
    }, {
        name: 'carrierCode',
        index: 'carrierCode',
    }, {
        name: 'corporation',
        index: 'corporation'
    }, {
        name: 'address',
        index: 'address'
    }, {
        name: 'contact',
        index: 'contact'
    }, {
        name: 'telephone',
        index: 'telephone',
    }, {
        name: 'licenseCode',
        index: 'licenseCode',
    }, {
        name: 'licenseExpiryDateStr',
        index: 'licenseExpiryDateStr'
    }, {
        name: 'businessLicense',
        index: 'businessLicense',
    }, {
        name: 'businessLicenseExpiryDateStr',
        index: 'businessLicenseExpiryDateStr'
    }, {
        name: 'carrierOrgCode',
        index: 'carrierOrgCode'
    }, {
        name: 'carrierOrgExpiryDateStr',
        index: 'carrierOrgExpiryDateStr'
    }, {
        name: 'taxRegisterCode',
        index: 'taxRegisterCode',
    }, {
        name: 'transportProtocol',
        index: 'transportProtocol',
    }, {
        name: 'bankAccountName',
        index: 'bankAccountName'
    }, {
        name: 'bankNumber',
        index: 'bankNumber',
    }, {
        name: 'qualitySurvey',
        index: 'qualitySurvey'
    }, {
        name: 'remarks',
        index: 'remarks'
    }];
    $('#table_a').XGrid({
        url:'/proxy-gsp/gsp/transport/carrierList',
        postData:{},
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'carrierCode',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index ) {
            //跳转详情页                
//            console.log(id, obj.carrierCode);
            utils.openTabs("toCarrierDetail","查看承运单位", "/proxy-gsp/gsp/transport/toCarrierDetail?carrierCode=" + obj.carrierCode);
        },
        gridComplete: function () {},
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            //console.log(id, dom, obj, index, event)
        },
        pager: '#grid_pager_a'
    });
    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })

    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        //获取form数据
        var data = $('#form_a').serializeToJSON();        
        //console.log(data);
        //更新表格数据
        $('#table_a').XGrid('setGridParam', {
            url:'/proxy-gsp/gsp/transport/carrierList',
            postData: {
            	"carrierNames": $("#carrierNames").val().trim(),
            	"flowStatus": $("#flowStatus").val(),
            	"licenseStatus": $("#licenseStatus").val()
            },
            colNames: colName,
            colModel: colModel,
            rowNum: 20,
            rowList:[20,50,100],
            altRows: true, //设置为交替行表格,默认为false
            pager: '#grid_pager_a',
        }).trigger("reloadGrid");
    });

    /* 新增 */
    $('#addRowData').on('click', function () {
        utils.openTabs("carrierlistAdd","新增承运单位", "/proxy-gsp/gsp/transport/toCarrierAdd");
    })
    
    /* 编辑 */
    $('#editRowData').on('click', function () {
    	var selRow = $('#table_a').XGrid('getSeleRow');
    	if (selRow.length != 0) {    		
			var carrierCode = selRow[0].carrierCode;			
			$.ajax({            
                type: "POST",                    
                url: "/proxy-gsp/gsp/transport/getCarrier",
                data: {
                	carrierCode:carrierCode
                },
                success: function (result) {            	
                    if (result.code == 0 && result != null) { 
                    	var flowStatus = result.result.flowStatus;
                    	if(flowStatus == 0) {
//            	    		console.log('carrierCode='+carrierCode)
            	    		utils.openTabs("carrierlistEdit","编辑承运单位", "/proxy-gsp/gsp/transport/toCarrierEdit?carrierCode=" +carrierCode 
            	    				+ "&taskId=&processId=&edit=&close=&agree=&reject=&report=");   
            			} else {
            				utils.openTabs("toCarrierDetail","查看承运单位", "/proxy-gsp/gsp/transport/toCarrierDetail?carrierCode=" + carrierCode);
            			}                       	
                    } else {
                    	utils.dialog({content: '未查到数据', quickClose: true, timeout: 4000}).showModal();
                    };
                },
                error : function() {
                	utils.dialog({content: '系统出错了', quickClose: true, timeout: 4000}).showModal();
                }
            });            	
			
    	} else {
    		utils.dialog({content: '请选择要修改的数据', quickClose: true, timeout: 4000}).showModal();
    	}
    })
    
     /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = "table_a";
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                /*  if (!data.length) {
                      data = [data];
                  }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            
            var obj = {
            	carrierNames: $("#carrierNames").val(),
            	flowStatus: $("#flowStatus").val(),
            	licenseStatus: $("#licenseStatus").val(),
//                selectData: data,
                colNames: colName,
                colNameDesc: colNameDesc
            }
            // 是否超出限制    
            utils.exportAstrictHandle('table_a', Number($('#totalPageNum').text()), 1).then( () => {            	
                return false;
            }).catch( () => {            	
            	httpPost("/proxy-gsp/gsp/transport/exportCarrierList", obj);
            })   
           // obj["nameModel"] = nameModel;           
        });
    });
    
    /* 导出当前机构的 */
    /*
    $('#exportRowData').on('click', function () {
        window.location = '/proxy-gsp/gsp/transport/exportCarrierList?carrierNames='+$("#carrierNames").val()+'&flowStatus='+$("#flowStatus").val()+'&licenseStatus='+$("#licenseStatus").val();
    });
    */
    
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();        
        console.log('temp=' + temp.elements.length);
        return temp;
    }
    
})