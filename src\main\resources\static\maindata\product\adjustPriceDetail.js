$(function() {
        $('div[fold=head]').fold({
            sub: 'sub'
        });
        $('#X_Table').XGrid({
            url: "/proxy-product/product/adjustPrice/audi/details",
            postData: {
                "recordId": $("#recordId").val()
            },
            colNames: ['id', 'orgCode', '机构', 'productId', '业务类型', '采购员ID', '采购员', '商品编码', '标准库Id', '商品名', '通用名', '型号/规格', '生产厂家', '包装单位', '剂型', 'TOP排名',
                'APP售价', '区域类型', '智鹿总部采购价', '连锁APP售价', '荷叶大药房采购价', '<i class="i-red">*</i>申请APP售价', 'APP售价涨幅/涨额', '<i class="i-red">*</i>申请智鹿总部采购价',
                '<i class="i-red">*</i>申请连锁APP售价', '<i class="i-red">*</i>申请荷叶大药房采购价', '<i class="i-red">*</i>申请原因', '特殊申请原因', '30天内调价次数', '提交备注', '客诉处理方案', '审核App售价',
                '审核智鹿总部采购价', '审核连锁APP售价', '审核荷叶大药房采购价', '商品中心审核原因', '附件', '在途价', '最后含税进价', '票面毛利率', '票面毛利率', '前30天销量', '库存数量', '底价', '活动类型',
                '价格对比', '市场竞品价', '机构名称', '爆款标识', "商品ec状态", "商品神农审核状态"
            ],
            colModel: [{
                name: 'id', //与反回的json数据中key值对应
                index: 'id', //索引。其和后台交互的参数为sidx
                key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden: true
            }, {
                name: 'orgCode',
                index: 'orgCode',
                hidden: true
            }, {
                name: 'orgCodeValue',
                index: 'orgCodeValue',
            }, {
                name: 'productId',
                index: 'productId',
                hidden: true
            }, {
                name: 'channelId',
                index: 'channelId'
            }, {
                name: 'buyer',
                index: 'buyer',
                hidegrid: true,
                hidden: true
            }, {
                name: 'buyerVal',
                index: 'buyerVal' //采购员
            }, {
                name: 'productCode',
                index: 'productCode'
            }, {
                name: 'standardId',
                index: 'standardId'
            }, {
                name: 'productName',
                index: 'productName'
            }, {
                name: 'commonName',
                index: 'commonName'
            }, {
                name: 'specifications',
                index: 'specifications'
            }, {
                name: 'manufacturerName',
                index: 'manufacturerName'
            }, {
                name: 'packingUnitValue',
                index: 'packingUnitValue'
            }, {
                name: 'dosageFormValue',
                index: 'dosageFormValue'
            }, { //top排名
                name: 'top',
                index: 'top'
            }, {
                name: 'newAppPrice',
                index: 'newAppPrice',
                formatter: function(val, rowType, rowData) {
                    if (!rowData.productChannelAreaList) {
                        return rowData.appPrice || ''
                    } else {
                        let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                        let appPriceHtml = ''
                        if (areaList && areaList.length > 0) {
                            for (let i = 0; i < areaList.length; i++) {
                                appPriceHtml += `<div class="tdHtml">${areaList[i].appPrice || ''}</div>`
                            }
                        }
                        return appPriceHtml
                    }
                }
            }, {
                name: 'areaCode',
                index: 'areaCode',
                formatter: function(val, rowType, rowData) {
                    if (!rowData.productChannelAreaList) {
                        return '<span>默认</span>'
                    } else {
                        let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                        let areaHtml = `<div id="Area">`
                        if (areaList && areaList.length > 0) {
                            for (let i = 0; i < areaList.length; i++) {
                                if (i == 0) {
                                    areaHtml += `<div class="tdHtml"><span class="AreaInfo">默认</span></div>`
                                } else {
                                    areaHtml += `<div class="tdHtml"><span class="AreaInfo">${areaList[i].areaName || ''}</span></div>`
                                }
                            }
                        }
                        areaHtml += `</div>`
                        return areaHtml
                    }
                },
                unformat: function(e) {
                    return ''
                }
            }, {
                name: 'zhiluPrice',
                index: 'zhiluPrice'
            }, {
                name: 'chainGuidePrice',
                index: 'chainGuidePrice'
            }, {
                name: 'heyePrice',
                index: 'heyePrice'
            }, {
                name: 'applyAppPrice',
                index: 'applyAppPrice',
                // rowtype: '#applicationAppPrice'
                formatter: function(val, rowType, rowData) {
                    if (!rowData.productChannelAreaList) {
                        return '<span>' + rowData.applicationAppPrice + '</span>'
                    } else {
                        let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                        let AppPriceHtml = ''
                        if (areaList && areaList.length > 0) {
                            for (let i = 0; i < areaList.length; i++) {
                                AppPriceHtml += `<div class="tdHtml">${areaList[i].applicationAppPrice || ''}</div>`
                            }
                        }
                        return AppPriceHtml
                    }

                }
            }, {
                name: 'appPriceIncreaseRiseHtml',
                index: 'appPriceIncreaseRiseHtml',
                formatter: function(val, rowType, rowData) {
                    if (!rowData.productChannelAreaList) {
                        return rowData.appPriceIncreaseRise || ''
                    } else {
                        let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                        let appPriceIncreaseRiseHtml = ''
                        if (areaList && areaList.length > 0) {
                            for (let i = 0; i < areaList.length; i++) {
                                appPriceIncreaseRiseHtml += `<div class="tdHtml">${areaList[i].appPriceIncreaseRise || ''}</div>`
                            }
                        }
                        return appPriceIncreaseRiseHtml
                    }
                }
            }, {
                name: 'applicationZhiluPrice',
                index: 'applicationZhiluPrice'
            }, {
                name: 'applicationChainGuidePrice',
                index: 'applicationChainGuidePrice'
            }, {
                name: 'applicationHeyePrice',
                index: 'applicationHeyePrice',
                width: 200,
            }, {
                name: 'applicationReasonsTypeValue',
                index: 'applicationReasonsTypeValue'
            }, {
                name: 'specialReason',
                index: 'specialReason',
                hidegrid: true,
                hidden: true
            }, {
                name: 'changeFrequency',
                index: 'changeFrequency'
            }, {
                name: 'detailRemark',
                index: 'detailRemark'
            }, {
                name: 'treatmentPlan',
                index: 'treatmentPlan'
            }, {
                name: 'auditAppPrice',
                index: 'auditAppPrice',
                rowtype: '#auditAppPrice'
            }, {
                name: 'auditZhiluPrice',
                index: 'auditZhiluPrice',
                rowtype: '#auditZhiluPrice'
            }, {
                name: 'auditChainGuidePrice',
                index: 'auditChainGuidePrice',
                rowtype: '#auditChainGuidePrice'
            }, {
                name: 'auditHeyePrice',
                index: 'auditHeyePrice',
                rowtype: '#auditHeyePrice',
                width: 180,
            }, {
                name: 'remarks',
                index: 'remarks',
                rowtype: '#remarks'
            }, {
                name: 'enclosureUrl',
                index: 'enclosureUrl',
                formatter: function(val, rowType, rowData) {
                    if (val) {
                        return `<a href="javascript:;" data-url="${val}" class="file_a">📎</a>`;
                    }
                },
                unformat: function(val, rowModel, ele) {
                    if ($(ele).find('a.file_a').length > 0) {
                        return $(ele).find('a.file_a').attr('data-url');
                    } else {
                        return '';
                    }
                }
            }, {
                name: 'inTransitPrice',
                index: 'inTransitPrice'
            }, {
                name: 'lastPurchasePrice',
                index: 'lastPurchasePrice'
            }, {
                name: 'grossMargin',
                index: 'grossMargin',
                hidegrid: true,
                hidden: true
            }, {
                name: 'grossMarginStrHtml',
                index: 'grossMarginStrHtml',
                formatter: function(val, rowType, rowData) {
                    if (!rowData.productChannelAreaList) {
                        return rowData.grossMarginStr || ''
                    } else {
                        let areaList = getProductChannelAreaList(rowData.productChannelAreaList, rowData);
                        let grossMarginStrHtml = ''
                        if (areaList && areaList.length > 0) {
                            for (let i = 0; i < areaList.length; i++) {
                                grossMarginStrHtml += `<div class="tdHtml">${areaList[i].grossMarginStr || ''}</div>`
                            }
                        }
                        return grossMarginStrHtml
                    }
                }
            }, {
                name: 'salesVolume',
                index: 'salesVolume'
            }, {
                name: 'inventoryQuantity',
                index: 'inventoryQuantity'
            }, {
                name: 'floorPrice',
                index: 'floorPrice'
            }, {
                name: 'promoType',
                index: 'promoType'
            }, {
                name: 'comparison',
                index: 'comparison',
                width: 100,
                formatter: function(e) {
                    return `<button type="button" class="btn btn-info comparison">对比</button>`;
                },
                unformat: function(e) {
                    return ''
                }
            }, {
                name: 'price_show',
                index: 'price_show',
                formatter: function(val, rowType, rowData) {
                    return `<button type="button" class="btn btn-info price_show">查看</button>`;
                }
            }, {
                name: 'orgCodeValue',
                index: 'orgCodeValue',
                hidegrid: true,
                hidden: true
            }, {
                name: 'oldProductCode',
                index: 'oldProductCode',
                hidegrid: true,
                hidden: true
            }, {
                name: 'inVogue',
                index: 'inVogue',
                hidden: true
            }, {
                name: 'productChannelAreaList',
                index: 'productChannelAreaList',
                hidegrid: true,
                hidden: true,
                formatter: function(result) {
                    if (typeof result == 'string') {
                        return result
                    } else {
                        return JSON.stringify(result)
                    }
                }
            }, {
                name: 'appPrice',
                index: 'appPrice',
                hidegrid: true,
                hidden: true,
            }, {
                name: 'appPriceIncreaseRise',
                index: 'appPriceIncreaseRise',
                hidegrid: true,
                hidden: true,
            }, {
                name: 'grossMarginStr',
                index: 'grossMarginStr',
                hidegrid: true,
                hidden: true,
            }, {
                name: 'upperEcAuditStatus',
                index: 'upperEcAuditStatus',
                hidden: true
            }, {
                name: 'upperStatues',
                index: 'upperStatues',
                hidden: true
            }],
            rowNum: 0,
            rownumbers: true,
            altRows: true, //设置为交替行表格,默认为false
            ondblClickRow: function(id, dom, obj, index, event) {
                console.log('双击行事件', id, dom, obj, index, event);
            },
            onSelectRow: function(id, dom, obj, index, event) {
                //console.log('单机行事件', id,dom,obj,index,event);
                initHistorys(obj.productId);
            },
            gridComplete: function() {
                if ($('#taskKey').find('option:selected').attr('processtype') === '1') {
                    var rowData = $('#X_Table').XGrid('getRowData');
                    rowData.forEach(function(item) {
                        $('#X_Table').XGrid('setRowData', item.id, { applicationReasonsTypeValue: item.specialReason });
                    });
                }
            },
            pager: '#grid_page'
        });
    })
    /**
     * 获取JSON
     * @param productChannelAreaList
     * @returns {*[]|any}
     */
function getProductChannelAreaList(productChannelAreaList, rowData) {
    if (!productChannelAreaList) {
        productChannelAreaList = [];
        // productChannelAreaList.push({
        //     areaCode: '0',
        //     areaName: '默认',
        //     appPrice: rowData.appPrice,
        //     grossMargin: rowData.grossMargin,
        //     grossMarginStr: rowData.grossMarginStr,
        //     applyAppPrice: '',
        //     appPriceIncreaseRise: rowData.appPriceIncreaseRise,
        // }
        // )
        // rowData.productChannelAreaList = JSON.stringify(productChannelAreaList)
        return productChannelAreaList
    }
    if (typeof productChannelAreaList == 'string') {
        return $.parseJSON(productChannelAreaList)
    }
    return productChannelAreaList
}

function initHistorys(productId) {
    $('#X_Table_log').XGrid({
        //data: grid_log_data,
        url: "/proxy-product/product/adjustPrice/audi/historys",
        colNames: ['商品编码', '业务类型', '商品名', '通用名', '申请日期', '申请人', '申请前APP售价', '申请后APP售价', '审核APP售价', '涨幅/涨额', '状态', '审核日期', '审核人'],
        colModel: [{
            name: 'productCode',
            index: 'productCode'
        }, {
            name: 'channelId',
            index: 'channelId'
        }, {
            name: 'productName',
            index: 'productName'
        }, {
            name: 'commonName',
            index: 'commonName'
        }, {
            name: 'applicationTime',
            index: 'applicationTime',
            formatter: function(value) {
                return new Date(value).Format('yyyy-MM-dd');
            }
        }, {
            name: 'applicantValue',
            index: 'applicantValue'
        }, {
            name: 'appPrice',
            index: 'appPrice'
        }, {
            name: 'applicationAppPrice',
            index: 'applicationAppPrice'
        }, {
            name: 'auditAppPrice',
            index: 'auditAppPrice'
        }, {
            name: 'appPriceIncreaseRise',
            index: 'appPriceIncreaseRise'
        }, {
            name: 'statues',
            index: 'statues',
            hidden: true
        }, {
            name: 'auditTime',
            index: 'auditTime',
            formatter: function(val, rowType, rowData) {
                var status = rowData.statues;
                if (val != null && (status == 2 || status == 3)) {
                    return new Date(val).Format('yyyy-MM-dd hh:mm:ss');
                } else {
                    return '';
                }
            }
        }, {
            name: 'auditor',
            index: 'auditor',
            formatter: function(val, rowType, rowData) {
                var status = rowData.statues;
                var auditor = rowData.auditor;
                if (val != null && (status == 2 || status == 3)) {
                    return auditor;
                } else {
                    return '';
                }
            }
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        postData: {
            "productId": productId
        },
        ondblClickRow: function(id, dom, obj, index, event) {
            //console.log('双击行事件', id,dom,obj,index,event);
        },
        onSelectRow: function(id, dom, obj, index, event) {
            //console.log('单机行事件', id,dom,obj,index,event);
        },
        pager: '#tableLog_page'
    });
}

function submitAudiInfo(status, auditOpinion) {
    let processTypeFlag = $('#taskKey').find('option:selected').attr('data-val');
    var rows = $('#X_Table').XGrid('getRowData');
    //审核通过，判断明细是否加载完成
    if (status == 0) {
        if (rows.length == 0) {
            utils.dialog({ content: '请等待商品明细加载完成后，再进行审核', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        var subsidiariesClassification = $("#subsidiariesClassification").val();
        var isCheckPrice = $("#isCheckPrice").val();
        if (subsidiariesClassification != undefined && subsidiariesClassification != '') {
            if (isCheckPrice) {
                let changeState = getChangePrice();
                console.log('changeState: ' + changeState);
                if (!changeState) {
                    console.log('拦截');
                    return;
                }
            }
            // var message = "";
            var message1 = "";
            var zhilumessage = "";
            var heyemessage = "";
            for (var i = 0; i < rows.length; i++) {
                var selectRow = rows[i];
                if ((selectRow.auditAppPrice == "" || selectRow.auditAppPrice <= 0) && processTypeFlag != 0) {
                    utils.dialog({ content: '审核APP售价不能为空或0', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                if (selectRow.auditChainGuidePrice == "" || selectRow.auditChainGuidePrice < 0) {
                    utils.dialog({ content: '审核连锁APP售价不能为空', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                // if (selectRow.auditZhiluPrice == "") {
                //     utils.dialog({ content: '审核智鹿总部采购价不能为空', quickClose: true, timeout: 2000 }).showModal();
                //     return false;
                // }
                //申请连锁APP售价>申请APP售价 拦截
                // if (selectRow.inVogue === '0' && parseFloat(selectRow.auditChainGuidePrice) > parseFloat(selectRow.auditAppPrice)) {
                //     message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
                // }
                // if (selectRow.auditHeyePrice == "") {
                //     utils.dialog({ content: '审核荷叶大药房采购价不能为空', quickClose: true, timeout: 2000 }).showModal();
                //     return false;
                // }
                //申请APP售价< 最后含税入库价  负毛利
                if (parseFloat(selectRow.auditAppPrice) < parseFloat(selectRow.lastPurchasePrice)) {
                    message1 = message1 + selectRow.productCode + "：" + selectRow.productName + "</br>";
                } else {
                    //申请连锁APP售价< 底价        负毛利
                    if (parseFloat(selectRow.auditChainGuidePrice) < parseFloat(selectRow.floorPrice)) {
                        message1 = message1 + selectRow.productCode + "：" + selectRow.productName + "</br>";
                    }
                }
                //审核智鹿总部采购价< 最后含税入库价  负毛利
                // if (parseFloat(selectRow.auditZhiluPrice) < parseFloat(selectRow.lastPurchasePrice)) {
                //     zhilumessage = zhilumessage + selectRow.productCode + "：" + selectRow.productName + "</br>";
                // }
                //审核荷叶大药房采购价< 最后含税入库价 负毛利
                // if (parseFloat(selectRow.auditHeyePrice) < parseFloat(selectRow.lastPurchasePrice)) {
                //     heyemessage = heyemessage + selectRow.productCode + "：" + selectRow.productName + "</br>";
                // }
            }
            // if (message != '' && processTypeFlag != 0) {
            //     checkPrice("以下商品申请/审核连锁APP售价＞申请/审核APP售价，请重新录入价格或删除商品！", message);
            //     return;
            // }
            var dataVal = $('#taskKey').find('option:selected').attr('data-val');
            if (dataVal != "0" && message1 != '') {
                checkPrice("以下商品价格出现负毛利，请重新录入价格或删除商品！", message1);
                return;
            }
            if (isCheckPrice) { //机构负责人校验负毛利
                var editFlag = $("#editFlag").val();
                // if (editFlag == "1" && dataVal != "0" && zhilumessage != '') {
                //     checkPrice("审核智鹿总部采购价<最后含税单价，请重新录入价格！", zhilumessage);
                //     return;
                // }
                // if (editFlag == "1" && dataVal != "0" && heyemessage != '') {
                //     checkPrice("审核荷叶大药房采购价<最后含税单价，请重新录入价格！", heyemessage);
                //     return;
                // }
            }
        }
    }
    var adjustPriceApprovalRecordVo = { "adjustPriceApprovalRecordVo": { "id": $("#recordId").val() } };
    var adjustPriceApprovalRecordDetailVos = { "adjustPriceApprovalRecordDetailVos": rows };
    adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos = adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos.map(function(item) {
        if (item.processFlag == 1) {
            item.specialReason = item.applicationReasonsType;
            item.applicationReasonsType = null;
        }
        if (item['promoTypeCode'] != '') {
            item['promoType'] = item['promoTypeCode']
        }
        if (processTypeFlag == 0) {
            item.productChannelAreaList = $.parseJSON(item.productChannelAreaList)
            item.appPrice = null
            item.appPriceIncreaseRise = null
            item.grossMarginStr = null
        } else {
            item.productChannelAreaList = []
        }
        delete item.newAppPrice
        delete item.applyAppPrice
        delete item.appPriceIncreaseRiseHtml
        delete item.grossMarginStrHtml
        return item;
    });
    //拼装链接两个json对象
    var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
    $('.audiPass').attr("disabled",true);
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/adjustPrice/passAdjustPrice?statues=" + status + "&auditOpinion=" + auditOpinion + "&taskId=" + $("#taskId").val() + "&editFlag=" + $("#editFlag").val(),
        data: places,
        async: false,
        dataType: 'json',
        contentType: "application/json",
        error: function () {
			$('.audiPass').attr("disabled",false);
            alert("提交失败！");
        },
        success: function(data) {
            console.log(data);
			$('.audiPass').attr("disabled",false);
            if (data.code == 0) {
                if (data.result != null && data.result.flowStatus != undefined && !data.result.flowStatus) {
                    utils.dialog({
                        title: "提示",
                        content: data.result.flowMsg,
                        width: 300,
                        height: 30,
                        okValue: '确定',
                        ok: function() {
                            utils.closeTab();
                        }
                    }).showModal();
                    return false;
                }
                var msg = "";
                if (status == 0) {
                    msg = '恭喜审核通过';
                }
                if (status == 1) {
                    msg = '驳回成功';
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function() {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            } else {
                utils.dialog({ content: data.result, quickClose: true, timeout: 2000 }).showModal();
            }
        }
    });
}

function getChangePrice() {
    // 校验规则类型
    var type = $('#taskKey').find('option:selected').attr('data-val');
    // 校验规则
    var option = [
        function(changeRange, changeNum) { //特殊调价流程
            return true;
        },
        function(changeRange, changeNum) { //降价＜10%
            return changeRange == 0 && 0 <= changeNum && changeNum < 10;
        },
        function(changeRange, changeNum) { //10%≤降价＜20%
            return changeRange == 0 && 10 <= changeNum && changeNum < 20;
        },
        function(changeRange, changeNum) { //20%≤降价＜50%
            return changeRange == 0 && 20 <= changeNum && changeNum < 50;
        },
        function(changeRange, changeNum) { //降价≥50%
            return changeRange == 0 && changeNum >= 50;
        },
        function(changeRange, changeNum) { //涨价＜50%
            return changeRange == 1 && 0 < changeNum && changeNum < 50;
        },
        function(changeRange, changeNum) { //涨价≥50%
            return changeRange == 1 && changeNum >= 50;
        },
        function(changeRange, changeNum) { //降价＜5%
            return changeRange == 0 && changeNum < 5;
        },
        function(changeRange, changeNum) { //5%≤降价＜10%
            return changeRange == 0 && 5 <= changeNum && changeNum < 10;
        },
        function(changeRange, changeNum) { //10%≤降价＜50%
            return changeRange == 0 && 10 <= changeNum && changeNum < 50;
        },
        function(changeRange, changeNum) { //5%≤降价＜50%
            return changeRange == 0 && 5 <= changeNum && changeNum < 50;
        },
        function(changeRange, changeNum) { //涨价＜20%
            return changeRange == 1 && changeNum < 20;
        },
        function(changeRange, changeNum) { //涨价≥20%
            return changeRange == 1 && changeNum >= 20;
        },
        function(changeRange, changeNum) { //涨价≥20%
            return changeRange == 0 && 10 <= changeNum < 50;
        }
    ];
    // 校验提示
    var allRowdata = $('#X_Table').XGrid('getRowData');
    var errorRow = allRowdata.filter(function(row) {
        if (Number(row.appPrice) == Number(row.auditAppPrice)) { //审核价和原价相同不做幅度判断
            return false;
        }
        var changeRange = 1;
        var changeNum = (Number(row.auditAppPrice) - Number(row.appPrice)).toFixed(2);
        if (changeNum <= 0) { //降价
            changeRange = 0
        }
        changeNum = Math.abs(changeNum);
        var change = 100 * changeNum / Number(row.appPrice);
        if (row.appPrice == "") { //app 售价为空
            change = 100;
        }
        return !option[type](changeRange, change);
    });
    if (errorRow.length) {
        var msg = errorRow.map(function(item) {
            return item.productCode + '-' + item.productName;
        });
        utils.dialog({
            title: '提示',
            content: `<div  style="width: 470px;max-height: 300px;overflow-y: auto">以下商品价格调整范围不满足选定的流程条件，请重新录入或删除商品！<textarea id="copyprice2" readonly style="border:none;line-height: 25px;width: 100%;outline:none;resize:none;">` + msg.join().replace(/,/g, "\n") + `</textarea></div>`,
            okValue: '确定',
            ok: function() {},
            button: [{
                value: '复制信息',
                callback: function() {
                    $("#copyprice2").select(); // 选择对象
                    var flag = document.execCommand("Copy", "false", null); // 执行浏览器复制命令
                    if (flag) utils.dialog({ content: '复制成功！', quickClose: true, timeout: 2000 }).show();
                    return false;
                }
            }],
        }).showModal();
        return false
    } else {
        return true;
    }
}

function checkPrice(content, message) {
    var reg = new RegExp('</br>', "g")
    var copyText = message.replace(reg, '\n');
    var d = utils.dialog({
        title: "提示",
        content: content + "<br/>" + message +
            "<textarea id=\"copyInput\" class=\"btn btn-info\" style=\"float: right;margin-top: -966px;\"  >" + copyText + "</textarea>",
        okValue: '复制信息',
        ok: function() {
            $("#copyInput").select(); // 选择对象
            var flag = document.execCommand("Copy", "false", null); // 执行浏览器复制命令
            if (flag) utils.dialog({ content: '复制成功！', quickClose: true, timeout: 2000 }).show();
            d.close();
        },
        cancelValue: '返回',
        cancel: function() {}
    }).showModal();
}
$('.audiPass').on('click', function() {
    $('#auditOpinion').val('');
    var status = this.getAttribute("status");

    if (status == 1) {
        $('#opinion').show();
        checkBill(status)
    } else {
        // 审核通过
        $('#opinion').hide()
            // 发起请求 校验负毛利
        let processTypeFlag = $('#taskKey').find('option:selected').attr('data-val');
        var rows = $('#X_Table').XGrid('getRowData');
        //审核通过，判断明细是否加载完成
        if (rows.length == 0) {
            utils.dialog({ content: '请等待商品明细加载完成后，再进行审核', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        var subsidiariesClassification = $("#subsidiariesClassification").val();
        var isCheckPrice = $("#isCheckPrice").val();
        if (subsidiariesClassification != undefined && subsidiariesClassification != '') {
            if (isCheckPrice) {
                let changeState = getChangePrice();
                console.log('changeState: ' + changeState);
                if (!changeState) {
                    console.log('拦截');
                    return;
                }
            }
            // var message = "";
            var message1 = "";
            var zhilumessage = "";
            var heyemessage = "";
            for (var i = 0; i < rows.length; i++) {
                var selectRow = rows[i];
                if ((selectRow.auditAppPrice == "" || selectRow.auditAppPrice <= 0) && processTypeFlag != 0) {
                    utils.dialog({ content: '审核APP售价不能为空或0', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                if (selectRow.auditChainGuidePrice == "" || selectRow.auditChainGuidePrice < 0) {
                    utils.dialog({ content: '审核连锁APP售价不能为空', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                // if (selectRow.auditZhiluPrice == "") {
                //     utils.dialog({ content: '审核智鹿总部采购价不能为空', quickClose: true, timeout: 2000 }).showModal();
                //     return false;
                // }
                //申请连锁APP售价>申请APP售价 拦截
                // if (selectRow.inVogue === '0' && parseFloat(selectRow.auditChainGuidePrice) > parseFloat(selectRow.auditAppPrice)) {
                //     message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
                // }
                // if (selectRow.auditHeyePrice == "") {
                //     utils.dialog({ content: '审核荷叶大药房采购价不能为空', quickClose: true, timeout: 2000 }).showModal();
                //     return false;
                // }
                //申请APP售价< 最后含税入库价  负毛利
                if (parseFloat(selectRow.auditAppPrice) < parseFloat(selectRow.lastPurchasePrice)) {
                    message1 = message1 + selectRow.productCode + "：" + selectRow.productName + "</br>";
                } else {
                    //申请连锁APP售价< 底价        负毛利
                    if (parseFloat(selectRow.auditChainGuidePrice) < parseFloat(selectRow.floorPrice)) {
                        message1 = message1 + selectRow.productCode + "：" + selectRow.productName + "</br>";
                    }
                }
                //审核智鹿总部采购价< 最后含税入库价  负毛利
                // if (parseFloat(selectRow.auditZhiluPrice) < parseFloat(selectRow.lastPurchasePrice)) {
                //     zhilumessage = zhilumessage + selectRow.productCode + "：" + selectRow.productName + "</br>";
                // }
                //审核荷叶大药房采购价< 最后含税入库价 负毛利
                // if (parseFloat(selectRow.auditHeyePrice) < parseFloat(selectRow.lastPurchasePrice)) {
                //     heyemessage = heyemessage + selectRow.productCode + "：" + selectRow.productName + "</br>";
                // }
            }
            // if (message != '' && processTypeFlag != 0) {
            //     checkPrice("以下商品申请/审核连锁APP售价＞申请/审核APP售价，请重新录入价格或删除商品！", message);
            //     return;
            // }
            var dataVal = $('#taskKey').find('option:selected').attr('data-val');
            if (dataVal != "0" && message1 != '') {
                checkPrice("以下商品价格出现负毛利，请重新录入价格或删除商品！", message1);
                return;
            }
            if (isCheckPrice) { //机构负责人校验负毛利
                var editFlag = $("#editFlag").val();
                // if (editFlag == "1" && dataVal != "0" && zhilumessage != '') {
                //     checkPrice("审核智鹿总部采购价<最后含税单价，请重新录入价格！", zhilumessage);
                //     return;
                // }
                // if (editFlag == "1" && dataVal != "0" && heyemessage != '') {
                //     checkPrice("审核荷叶大药房采购价<最后含税单价，请重新录入价格！", heyemessage);
                //     return;
                // }
            }
        }
        var adjustPriceApprovalRecordVo = { "adjustPriceApprovalRecordVo": { "id": $("#recordId").val() }, isCheck: true };
        var adjustPriceApprovalRecordDetailVos = { "adjustPriceApprovalRecordDetailVos": rows };
        adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos = adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos.map(function(item) {
            if (item.processFlag == 1) {
                item.specialReason = item.applicationReasonsType;
                item.applicationReasonsType = null;
            }
            if (item['promoTypeCode'] != '') {
                item['promoType'] = item['promoTypeCode']
            }
            console.log(processTypeFlag)
            if (processTypeFlag == 0) {
                item.productChannelAreaList = $.parseJSON(item.productChannelAreaList)
                item.appPrice = null
                item.appPriceIncreaseRise = null
                item.grossMarginStr = null
            } else {
                item.productChannelAreaList = []
            }
            delete item.newAppPrice
            delete item.applyAppPrice
            delete item.appPriceIncreaseRiseHtml
            delete item.grossMarginStrHtml
            return item;
        });
        //拼装链接两个json对象
        let params = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
        $.ajax({
            type: "POST",
            url: "/proxy-product/product/adjustPrice/negativeProfit",
            data: params,
            async: false,
            dataType: 'json',
            contentType: "application/json",
            success: function(data) {
                if (data.code == 0) {
                    let result = data.result;
                    let title = "以下商品出现负毛利，确认要继续操作调价吗？"
                    let message = ""
                    if (result != null && result.length > 0) {
                        for (let i = 0; i < result.length; i++) {
                            let selectRow = result[i];
                            message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
                        }
                        let d = utils.dialog({
                            title: "提示",
                            content: title + "<br/>" + message,
                            okValue: '确认提交',
                            ok: function() {
                                checkBill(status)
                            },
                            cancelValue: '返回',
                            cancel: function() {}
                        }).showModal();
                    } else {
                        checkBill(status)
                    }
                }
            }
        })
    }
    // utils.dialog({
    //     title: '审核',
    //     content: $('#container'),
    //     okValue: '确定',
    //     ok: function () {
    //         //审核不通过，意见不能为空
    //         if (status == 1) {
    //             if ($("#auditOpinion").val() == "") {
    //                 utils.dialog({ content: '审批意见不能为空！', quickClose: true, timeout: 2000 }).showModal();
    //                 return false;
    //             }
    //         }
    //         submitAudiInfo(status, $("#auditOpinion").val());
    //     },
    //     cancelValue: '取消',
    //     cancel: function () {
    //         $("#auditOpinion").val("");
    //     }
    // }).showModal();
});
// 校验通过/审核不通过 审核
function checkBill(status) {
    utils.dialog({
        title: '审核',
        content: $('#container'),
        okValue: '确定',
        ok: function() {
            //审核不通过，意见不能为空
            if (status == 1) {
                if ($("#auditOpinion").val() == "") {
                    utils.dialog({ content: '审批意见不能为空！', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
            }
            submitAudiInfo(status, $("#auditOpinion").val());
        },
        cancelValue: '取消',
        cancel: function() {
            $("#auditOpinion").val("");
        }
    }).showModal();
}

$(function() {
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/queryTotle?processInstaId=" + $("#processId").val(),
        async: false,
        error: function() {
            utils.dialog({ content: '请求失败！', quickClose: true, timeout: 2000 }).showModal();
        },
        success: function(data) {
            if (data.code == 0) {
                $('.flow').process(data.result);
            } else {
                utils.dialog({ content: '服务器错误', quickClose: true, timeout: 2000 }).showModal();
            }
        }
    });
})
Date.prototype.Format = function(fmt) {
        var o = {
            "M+": this.getMonth() + 1, //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    } //查看竞品价格
$("#X_Table").on('click', '.price_show', function(e) {
    var rowId = $(e.target).parents('tr').attr('id');
    var rowData = $("#X_Table").getRowData(rowId);
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/priceComparison/searchSpiderProduct",
        dataType: "json",
        contentType: "application/json",
        timeout: 8000, //6000
        data: JSON.stringify({
            oldProductCode: rowData.oldProductCode,
            orgCode: rowData.orgCode,
            appPrice: rowData.appPrice

        }),
        beforeSend: function() {
            //parent.showLoading();
        },
        success: function(res) {
            var listData = res.result.list;
            if (!listData || listData.length === 0) {
                return utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '抱歉，未查到竞品数据'
                }).showModal();
            };
            let _html = `
                    <div>
                    <div style="padding: 0 15px">
                        <div class="row">
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">通用名：${rowData.commonName}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">生产厂商：${rowData.manufacturerName}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">规格：${rowData.specifications}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">商品编码：${rowData.productCode}</div>
                        </div>
                        <div class="row" style="margin-top:10px;">
                            <div class="col-xs-5 col-sm-5 col-md-5 col-lg-5">机构：${rowData.orgCodeValue}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">APP售价：${rowData.appPrice}</div>
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3">连锁APP售价：${rowData.chainGuidePrice}</div>
                        </div>
                        </div>
                 
                    <div class="panel-body" style=" height: 340px; overflow-x: auto;">
                        <table id="comparison_price_table" />
                    </div>
                </div>
                 `
            var comparison_dialog = utils.dialog({
                title: '市场价格查看',
                content: _html,
                width: 800,
                height: 400,
                cancelValue: '返回',
                cancel: function() {}
            }).showModal();
            $('#comparison_price_table').XGrid({
                data: listData,
                colNames: ['平台', '店铺/供应商', '链接', '价格类型', '数据来源', '更新时间'],
                colModel: [{
                        name: 'websiteName',
                        index: 'websiteName',
                    },
                    {
                        name: 'supplier',
                        index: 'supplier',
                    },
                    {
                        name: 'productUrl',
                        index: 'productUrl',
                        formatter: function(val, rowType, rowData) {
                            return `<a href="${rowData.productUrl}" title="${rowData.productUrl}" target="_blank">链接地址</a>`
                        }
                    },
                    {
                        name: 'websiteName',
                        index: 'websiteName',
                        formatter: function(val, rowType, rowData) {
                            return `折前价：${rowData.price}</br>折后价：${rowData.discountPrice}`
                        }
                    },
                    {
                        name: 'supplier',
                        index: 'supplier',
                        formatter: function(val, rowType, rowData) {
                            return `比价系统`
                        }
                    },
                    {
                        name: 'spiderTime',
                        index: 'spiderTime',
                    }
                ],
                rowNum: 20,
                altRows: true, //设置为交替行表格,默认为false
                rownumber: true
            });
        },
        complete: function() {
            //parent.hideLoading();
        }
    });
});
/* 对比 */
$("#X_Table").on('click', '.comparison', function(e) {
    var rowId = $(e.target).parents('tr').attr('id');
    var rowData = $("#X_Table").getRowData(rowId);
    $.ajax({
        type: "get",
        url: "/proxy-product/product/upperShelf/priceCompare",
        data: {
            productCode: rowData.productCode
        },
        beforeSend: function() {
            //parent.showLoading();
        },
        success: function(res) {
            var listData = res.result.list;
            listData.sort(function(a, b) {
                return a.appPrice - b.appPrice
            });
            let _html = `
                <div id="comparisonDialog_div">
                    <div class="dialog_con">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">当前公司：${rowData.orgCodeValue}</div>
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">当前商品：${rowData.productCode}${rowData.productName} ${rowData.specifications}</div>
                        <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6">APP售价：${rowData.auditAppPrice}</div>
                        <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6">连锁APP售价：${rowData.auditChainGuidePrice}</div>
                    </div>
                    <div class="panel-body" style=" height: 340px; overflow-x: auto;">
                        <table id="comparison_table" />
                    </div>
                </div>
                 `
            var comparison_dialog = utils.dialog({
                title: '价格对比',
                content: _html,
                width: 500,
                height: 400,
                cancelValue: '返回',
                cancel: function() {}
            }).showModal();
            $('#comparison_table').XGrid({
                //url:'/proxy-product/product/upperShelf/priceCompare',
                data: listData,
                colNames: ['其他子公司', 'APP售价', '连锁APP售价'],
                colModel: [{
                        name: 'orgName',
                        index: 'orgName',
                        width: 210
                    },
                    {
                        name: 'appPrice',
                        index: 'appPrice',
                        width: 120
                    },
                    {
                        name: 'chainGuidePrice',
                        index: 'chainGuidePrice',
                        width: 120
                    }
                ],
                key: 'channelCode',
                rowNum: 20,
                altRows: true, //设置为交替行表格,默认为false
                rownumber: true
            });
        },
        complete: function() {
            //parent.hideLoading();
        }
    });
});
$("#X_Table").on('click', '.file_a', function(e) {
    var url = $(e.target).attr('data-url');
    var id = $(e.target).parents('tr').attr('id');
    utils.dialog({
        align: 'top',
        width: 130,
        height: 50,
        padding: 2,
        content: `<div class="changeApplyItem" style="line-height: 50px;text-align: center;">
                    <a style="flex: 1;" href="${url}" target="_blank" class="download">查看下载</a>
                </div>`,
        quickClose: true
    }).show(this);
});
$('body').on('input', '#X_Table input.TwoDecimalHndle', function() {
    utils.TwoDecimalHndle(this, 12)
})
$('body').on('blur', '#X_Table input.TwoDecimalHndle', function() {
    $(this).val(utils.toDecimal2($(this).val()))
})