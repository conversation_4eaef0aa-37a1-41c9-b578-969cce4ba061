var fileName = "";
//
$("#upfile").on("change",function(){
    var value=this.value;
    $(".fileName").html(value).attr("title",value);
});


function doUpload(el) {
    let applyType = $(el).attr('data-applyType')
    var name = $("#upfile").val();
    if(name == ''){
        dialog({
            title:"提示",
            width:200,
            content:'请选择文件',
            okValue: '确定',
            ok: function () {
            }
        }).showModal();
        return false;
    }
    if(fileName!=""&&fileName==name){//重复导入一个文件
        dialog({
            title:"提示",
            width:200,
            content:'该文件已导入过，请确认是否再次导入？',
            okValue: '确定',
            ok: function () {
                uploadFile(applyType);
            }
        }).showModal();
        return false;
    }
    fileName = name;
    uploadFile(applyType);
}
function uploadFile(applyType){
    var formData = new FormData();
    let url = applyType == 1 ? '/proxy-product/product/adjustPrice/upLoadIntelligenceProductExcel' : '/proxy-product/product/adjustPrice/upLoadTerminalPriceExcel'
    formData.append("file",$("#upfile")[0].files[0]);
    formData.append("name",name);
    formData.append("applicationType",$("#applicationType").val());
    var loading = dialog({
        title: '加载中',
        fixed: true,
        width: 200,
        quickClose: false,
        cancel: false
    }).showModal();

    $.ajax({
        url,
        type : 'POST',
        async : false,
        data : formData,
        processData : false,
        contentType : false,
        beforeSend:function(){
            console.log("正在进行，请稍候");
        },
        success : function(data) {
            loading.close();
            if(data.code==0){
                var rowData = data.result.sucessList;
                if(rowData!=null&&rowData.length>0){
                    if (applyType == 1){
                        // 优先校验负毛 再校验活动商品
                        if(data.result.negativeProfitFlag && data.result.negativeProfitProduct.length > 0) {
                            let negativeProfitProduct = data.result.negativeProfitProduct
                            let title = "以下商品出现负毛利，确认要继续操作调价吗？"
                            let message = ""
                            for(let i = 0; i<negativeProfitProduct.length; i++){
                                let selectRow = negativeProfitProduct[i];
                                message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
                            }
                            let d = dialog({
                                title: '提示',
                                button:[
                                    {
                                        value:"取消",
                                        callback:function () {
                                            d.close().remove();
                                        }
                                    }
                                ],
                                okValue: '确定提交',
                                content: title +"<br/>" + message,
                                ok: function(){
                                    activeGoodsFun(rowData).then( () => {
                                        let activeGoods_tableData  = $('#activeGoods_table').XGrid('getRowData');
                                        if (activeGoods_tableData.length>0){
                                            utils.dialog({
                                                title: '活动商品提示',
                                                content: $('#activeGoods_table_div'),
                                                height: 500,
                                                okValue: '确定',
                                                ok: function () {
                                                    let ary1 = [], ary2 = [];

                                                    $('#activeGoods_table tr').not(':eq(0)').each((index, item) => {
                                                        let el= $(item).find(' input[type=checkbox]');
                                                        let checkedStateAry  = []
                                                        $(el).each((ind,ite) =>{
                                                            checkedStateAry.push($(ite).prop('checked'))
                                                        })
                                                        var checkedState  = checkedStateAry.some(item =>item)
                                                        if(checkedState){
                                                            let _obj = {
                                                                productCode: $(item).find('[row-describedby="productCode"]').text(),
                                                                productId: $(item).find('[row-describedby="productId"]').text(),
                                                                appControl:  !$(el[0]).prop('checked') ,
                                                                chainControl: !$(el[1]).prop('checked'),
                                                                appPrice: $(item).find('[row-describedby="appPrice"]').text(),
                                                                chainGuidePrice: $(item).find('[row-describedby="chainGuidePrice"]').text()
                                                            }
                                                            ary1.push(_obj)
                                                        }else{//未选中
                                                            ary2.push($(item).find('[row-describedby="productId"]').text())
                                                        }
                                                    })
                                                    if (ary1.length == 0 &&  activeGoods_tableData.length == rowData.length ){
                                                        utils.dialog({content: '全部商品均为活动商品且选择不继续调价，本次未生成调价申请单', quickClose: true, timeout: 3000}).showModal()
                                                        return false;
                                                    }
                                                    subHndle(data,ary2.toString(), JSON.stringify(ary1) )
                                                },
                                                cancelValue: '取消',
                                                cancel: function () {}
                                            }).showModal();
                                            return false;
                                        }
                                        var d=dialog({
                                            title:"提示",
                                            content:data.result.msg,
                                            button:[
                                                {
                                                    value:"取消",
                                                    callback:function () {
                                                        d.close().remove();
                                                    }
                                                }
                                            ],
                                            okValue: '确定提交',
                                            ok: function () {
                                                subHndle(data)
                                                d.close().remove();
                                            }
                                        }).showModal();
                                    }).catch(err => {
                                        console.log(err);
                                    })
                                }
                            }).showModal();
                        }else {
                            activeGoodsFun(rowData).then( () => {
                                let activeGoods_tableData  = $('#activeGoods_table').XGrid('getRowData');
                                if (activeGoods_tableData.length>0){
                                    utils.dialog({
                                        title: '活动商品提示',
                                        content: $('#activeGoods_table_div'),
                                        height: 500,
                                        okValue: '确定',
                                        ok: function () {
                                            let ary1 = [], ary2 = [];

                                            $('#activeGoods_table tr').not(':eq(0)').each((index, item) => {
                                                let el= $(item).find(' input[type=checkbox]');
                                                let checkedStateAry  = []
                                                $(el).each((ind,ite) =>{
                                                    checkedStateAry.push($(ite).prop('checked'))
                                                })
                                                var checkedState  = checkedStateAry.some(item =>item)
                                                if(checkedState){
                                                    let _obj = {
                                                        productCode: $(item).find('[row-describedby="productCode"]').text(),
                                                        productId: $(item).find('[row-describedby="productId"]').text(),
                                                        appControl:  !$(el[0]).prop('checked') ,
                                                        chainControl: !$(el[1]).prop('checked'),
                                                        appPrice: $(item).find('[row-describedby="appPrice"]').text(),
                                                        chainGuidePrice: $(item).find('[row-describedby="chainGuidePrice"]').text()
                                                    }
                                                    ary1.push(_obj)
                                                }else{//未选中
                                                    ary2.push($(item).find('[row-describedby="productId"]').text())
                                                }
                                            })
                                            if (ary1.length == 0 &&  activeGoods_tableData.length == rowData.length ){
                                                utils.dialog({content: '全部商品均为活动商品且选择不继续调价，本次未生成调价申请单', quickClose: true, timeout: 3000}).showModal()
                                                return false;
                                            }
                                            subHndle(data,ary2.toString(), JSON.stringify(ary1) )
                                        },
                                        cancelValue: '取消',
                                        cancel: function () {}
                                    }).showModal();
                                    return false;
                                }
                                var d=dialog({
                                    title:"提示",
                                    content:data.result.msg,
                                    button:[
                                        {
                                            value:"取消",
                                            callback:function () {
                                                d.close().remove();
                                            }
                                        }
                                    ],
                                    okValue: '确定提交',
                                    ok: function () {
                                        subHndle(data)
                                        d.close().remove();
                                    }
                                }).showModal();
                            }).catch(err => {
                                console.log(err);
                            })
                        }
                    } else {
                        var d=dialog({
                            title:"提示",
                            content:data.result.msg,
                            button:[
                                {
                                    value:"取消",
                                    callback:function () {
                                        d.close().remove();
                                    }
                                }
                            ],
                            okValue: '确定提交',
                            ok: function () {
                                var formData = new FormData();
                                formData.append("fileName", data.result.fileName);
                                $.ajax({
                                    url : "/proxy-product/product/adjustPrice/saveTerminalPriceData",
                                    type : 'POST',
                                    data:formData,
                                    async : false,
                                    processData : false,
                                    contentType : false,
                                    success : function(data) {
                                        if(data.code==0){
                                            utils.dialog({
                                                title: '提示',
                                                width: 300,
                                                height:120,
                                                content:`<textarea id="copyprice2" readonly style="border:none;height:100%;width: 100%;outline:none;resize:none;">`+data.result.msg+`</textarea>` ,
                                                okValue: '确定',
                                                ok: function () {
                                                    // parentDia.close();
                                                },
                                                button:[{
                                                    value:'复制信息',
                                                    callback:function () {
                                                        $("#copyprice2").select(); // 选择对象
                                                        var flag = document.execCommand("Copy","false",null); // 执行浏览器复制命令
                                                        if(flag) utils.dialog({content: '复制成功！', quickClose: true, timeout: 2000}).show();
                                                        return false;
                                                    }
                                                }],
                                            }).showModal();
                                        }
                                    },
                                    error:function () {
                                        parent.hideLoading()
                                        utils.dialog({content:"提交失败", quickClose: true, timeout: 2000}).showModal();
                                    },complete: function () {
                                        $('.fileName').attr('title','').text('')
                                    }
                                });
                                d.close().remove();
                            }
                        }).showModal();
                    }
                }else {
                    utils.dialog({
                        title: '提示',
                        width: 300,
                        height: 240,
                        content: `<p style="word-break: break-word;width: 300px; height: 240px;overflow:auto;">${data.result.msg}</p>`,
                        okValue: '确定',
                        ok: function(){}
                    }).showModal();
                }

            }else {
                utils.dialog({content:"上传失败", quickClose: true, timeout: 2000}).showModal();
                return;
            }
        },error:function () {
            loading.close();
            utils.dialog({content:"上传失败", quickClose: true, timeout: 2000}).showModal();
        },complete: function () {
            $('input[type=reset]').trigger("click");
            $('span.fileName').text('')
            // let file = $("#upfile");
            // file.after(file.clone().val(""));
            // file.remove()
            // $('.fileName').attr('title','').text('')
        }
    });
}
function subHndle(data,list='',listStr='') {
    var formData = new FormData();
    formData.append("fileName", data.result.fileName);
    formData.append("productIds",list);
    formData.append("listStr",listStr);

    $.ajax({
        // url : "/proxy-product/product/adjustPrice/upLoadIntelligenceProductData?fileName="+data.result.fileName  + '&productCode='+list,
        url : "/proxy-product/product/adjustPrice/upLoadIntelligenceProductData",
        type : 'POST',
        data:formData,
        async : false,
        processData : false,
        contentType : false,
        success : function(data) {
            if(data.code==0){
                utils.dialog({
                    title: '提示',
                    width: 300,
                    height:120,
                    content:`<textarea id="copyprice2" readonly style="border:none;height:100%;width: 100%;outline:none;resize:none;">`+data.result.msg+`</textarea>` ,
                    okValue: '确定',
                    ok: function () {
                        // parentDia.close();
                    },
                    button:[{
                        value:'复制信息',
                        callback:function () {
                            $("#copyprice2").select(); // 选择对象
                            var flag = document.execCommand("Copy","false",null); // 执行浏览器复制命令
                            if(flag) utils.dialog({content: '复制成功！', quickClose: true, timeout: 2000}).show();
                            return false;
                        }
                    }],
                }).showModal();
            }
        },
        error:function () {
            parent.hideLoading()
            utils.dialog({content:"提交失败", quickClose: true, timeout: 2000}).showModal();
        },complete: function () {
            $('.fileName').attr('title','').text('')
        }
    });
}
