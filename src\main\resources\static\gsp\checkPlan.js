var month = null, date = null;
function initDate(param) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    month = new_date.getMonth() + 1;
    date = new_date.getDate();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    //console.log(new_date, year, month, date);
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#toTime').val(year + '-' + month + '-01');
    $('#endTime').val(year + '-' + month + '-' + date);
}

$(function () {

    $('#input_goodName').Autocomplete({
        serviceUrl: '/proxy-gsp/gsp/checkPlan/getAllProductList?orgCode=' + $("#orgvalue").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#input_goodName").val("");
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });
    initDate()
    window.curIndex = 0;
    // tabs 切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this), $nav_content = $('.nav-content');
        window.curIndex = $this.index();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('.panel-body').eq($this.index()).addClass('active').siblings().removeClass('active');
        $nav_content.children('.panel-body').eq($this.index()).css("display", "flex").siblings().css("display", "none");
        $("#btn_toggle").children("button").eq($this.index()).show().siblings().hide();
        if (window.curIndex == 1) {
            $("#input_goodName").prop("disabled", false);
            $("#productName").prop("disabled", false);
            $("#drugClass").prop("disabled", false);
            $("#productBatch").prop("disabled", false);
            $("#manufacturerName").prop("disabled", false);
            //  $("#moveType").prop("disabled",false);
        } else {
            $("#input_goodName").prop("disabled", true).val('');
            $("#drugCode").prop("disabled", true).val('');
            $("#productName").prop("disabled", true).val('');
            $("#drugClass").prop("disabled", true).val('');
            $("#productBatch").prop("disabled", true).val('');
            $("#manufacturerName").prop("disabled", true).val('');
        }
    })

    $("#set_tables_rowa").click(function () {
        $('#CheckPlan_Table').XGrid('filterTableHead');
    })
    var var_status = false;
    $('#CheckPlan_Table').XGrid({
        url: "/proxy-gsp/gsp/checkPlan/queryCheckPlanPage",

        colNames: ['养护计划单据编号','养护计划单类型', '日期', '机构名称', '部门名称', '养护类别', '养护计划单据状态', '备注'],
        postData: {
            startTime: $("#toTime").val(),
            orgCode: $("#orgvalue").val()
        },
        colModel: [{
            name: 'checkPlanCode',
            index: 'checkPlanCode',
            key: true
        },{
            name: 'isZyypName',
            index: 'isZyypName'

        }, {
            name: 'checkTimes',
            index: 'checkTimes',
            formatter: function (e) {
                if (e != null && e != "") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }

        }, {
            name: 'orgName',
            index: 'orgName'
        },
            {
                name: 'depaName',
                index: 'depaName'
            }, {
                name: 'checkName',
                index: 'checkName',

            }, {
                name: 'statusName',
                index: 'statusName'

            },
            {
                name: 'remark',
                index: 'remark'
            }
        ],
        altRows: true, //设置为交替行表格,默认为false
        selectandorder: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        // rownumbers: true,//是否展示序号，多选
        /* ondblClickRow: function (id, dom, obj, index, event) {
             $("#checkNum").val(obj.checkPlanCode)
             $("#orgNum").val(obj.orgName)
             $("#deptNum").val(obj.depaName);
             $("#userNum").val(obj.userName)

             $('#title_dialog').text((obj.checkType == '重点养护')?'重点养护计划详情':'普通养护计划详情');
             var el = document.querySelector('#CheckPlanInfo_Table');//html元素
             $('#CheckPlanInfo_Table').XGrid({
                 url: '/proxy-gsp/gsp/checkPlan/queryCheckPlanInfo',
                 postData:{
                     checkPlanCode:obj.checkPlanCode,
                     orgCode:obj.orgCode,

                 },
                 colNames: ['日期', '商品编码', '商品名称', '商品规格', '生产厂家', '批号', '商品产地', '单位',
                     '剂型', '数量', '库房名称', '预计应养护日期', '逾期天数', '有效期至', '养护类别',
                     '养护状态','备注'
                 ],
                 colModel: [
                     {
                         name: 'checkTime',
                         index: 'checkTime',
                         formatter:function (e){
                             if (e != null && e !="") {
                                 return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                             } else {
                                 return "";
                             }
                         }
                         //索引。其和后台交互的参数为sidx
                     }, {
                         name: 'productCode',
                         index: 'productCode'
                     }, {
                         name: 'productName',
                         index: 'productName'

                     }, {
                         name: 'specifications',
                         index: 'specifications'
                     }, {
                         name: 'manufacturerName',
                         index: 'manufacturerName'
                     }, {
                         name: 'productBatch',
                         index: 'productBatch'
                     }, {
                         name: 'producingArea',
                         index: 'producingArea'
                     }, {
                         name: 'packingUnit',
                         index: 'packingUnit'
                     }, {
                         name: 'dosageForm',
                         index: 'dosageForm',
                     }, {
                         name: 'storage',
                         index: 'storage'
                     },{
                         name: 'wareHouse',
                         index: 'wareHouse',
                         formatter:function (e){
                             if (e != null) {
                                 if(e==1){
                                    return  "合格库"
                                 } if(e==2){
                                     return  "不合格库"
                                 } if(e==3){
                                     return  "暂存库"
                                 }

                             } else {
                                 return "";
                             }
                         }
                     },

                     {
                         name: 'predictCheckTime',
                         index: 'predictCheckTime',
                         formatter:function (e){
                             if (e != null && e !="") {
                                 return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                             } else {
                                 return "";
                             }
                         }
                     }, {
                         name: 'expireDay',
                         index: 'expireDay'
                     }, {
                         name: 'validateDate',
                         index: 'validateDate',
                         formatter:function (e){
                             if (e != null && e !="") {
                                 return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                             } else {
                                 return "";
                             }
                         }
                     }, {
                         name: 'checkType',
                         index: 'checkType',
                         formatter: function (e) {
                             if (e == '1') {
                                 return '重点养护'
                             } else if (e == '2') {
                                 return '普通养护'
                             }
                         }
                     }, {
                         name: 'status',
                         index: 'status',
                         formatter: function (e) {
                             if (e == '1') {
                                 return '未养护'
                             } else if (e == '2') {
                                 return '已完成'
                             }
                         }
                     }/!*, {
                         name: 'checkCause',
                         index: 'checkCause',
                         formatter: function (e, c, rowData) {
                             var  checkCause  = "";
                             var count = 0;
                             var count1 = 0;
                             if (e) {
                                 var  str =   e.split(",");
                                 for (var i = 0; i < str.length; i++) {
                                     var s = str[i];
                                     if(s.indexOf("1")>=0){
                                         checkCause+="近效期";
                                         count++;
                                     }if(s.indexOf("2")>=0){
                                         if(count>0){
                                             checkCause+=",短效期";
                                             count1++;
                                         }else{
                                             checkCause+="短效期";
                                             count1++;
                                         }

                                     }if(s.indexOf("3")>=0){
                                         if(count1>0){
                                             checkCause+=",特殊存储要求";
                                         }else if(count>0){
                                             checkCause+=",特殊存储要求";
                                         }else{
                                             checkCause+="特殊存储要求";
                                         }

                                     }

                                 }
                                 return  checkCause;
                             } else {
                                 var id = rowData.id;


                                 return  "空";
                                 //$("#" + id).find('td').eq(0).css('display','none');
                             }

                         },

                     }*!/, {
                         name: 'remark',
                         index: 'remark'
                     }, {
                         name: 'checkPlanCode',
                         index: 'checkPlanCode',
                         hidden:true
                     }, {
                         name: 'id',
                         index: 'id',
                         hidden:true
                     }
                 ],
                 rowNum: 20,
                 rowList:[20,50,100],
                 rownumbers:true,
                 //selectandorder: true,//是否展示序号，多选
                 altRows: true,//设置为交替行表格,默认为false
                 ondblClickRow: function (e, c, a, b) {
                     //console.log('双击行事件',a);
                 },
                 onSelectRow: function (e, c, a, b) {

                 },
                 gridComplete:function(){

                     setTimeout(function () {
                         var data = $('#CheckPlanInfo_Table').XGrid('getRowData');
                         if(date !=null && data[0].checkType!=""){
                             if(data&&data[0].checkType=='普通养护'){
                                 $('#CheckPlanInfo_Table').find('tr').eq(0).find('th').eq(-2).css('display','none');
                                 for(var i = 1; i< $('#CheckPlanInfo_Table').find('tr').length; i++){
                                     $('#CheckPlanInfo_Table').find('tr').eq(i).find('td[row-describedby="checkCause"]').css('display','none');
                                 }
                             }
                         }

                     },200);
                 },

                 pager: '#CheckPlanInfo_pager',
             });

             utils.dialog({
                 title:obj.checkType+'计划详情',
                 content: $('#CheckPlanInfo_Table_wrap'),
                 width:$(window).width() * 0.8,
                 height:$(window).height() * 0.8
             }).showModal();



         },*/
        onSelectRow: function (id, dom, obj, index, event) {
            //console.log('单机行事件', id, dom, obj, index, event);
        },
        pager: '#grid_page',
        gridComplete: function () {
            if (!$(this).XGrid('getRowData').length) {
                utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
            }
        },
    });

    //设置显示列
    $('#setBtn').bind('click', function () {
        var target = window.curIndex ? $('#X_Tablea') : $('#CheckPlan_Table');
        target.XGrid('filterTableHead');
    })
    $('#X_Tablea').XGrid({
        url: "/proxy-gsp/gsp/checkPlan/queryCheckPlanPageInfo",
        colNames: ['日期', '养护计划单据编号', '商品大类', '商品编码', '商品名称', '商品规格', '生产厂家', '批号', '商品产地', '单位',
            '剂型', '数量', '库房名称', '预计应养护日期', '逾期天数', '有效期至', '养护类别',
            '养护状态', '上次养护日期', '备注'],
        postData: {
            startTime: $("#toTime").val(),
            orgCode: $("#orgvalue").val(),
            endTime: $("#endTime").val()
        },
        colModel: [{
            name: 'checkTime',
            index: 'checkTime',
            formatter: function (e) {
                if (e != null && e != "") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }
            //索引。其和后台交互的参数为sidx
        }, {
            name: 'checkPlanCode',
            index: 'checkPlanCode',
            width: 170
        }
            , {
                name: 'drugClass',
                index: 'drugClass'
            }
            , {
                name: 'productCode',
                index: 'productCode'
            }, {
                name: 'productName',
                index: 'productName'

            }, {
                name: 'specifications',
                index: 'specifications'
            }, {
                name: 'manufacturerName',
                index: 'manufacturerName'
            }, {
                name: 'productBatch',
                index: 'productBatch'
            }, {
                name: 'producingArea',
                index: 'producingArea'
            }, {
                name: 'packingUnit',
                index: 'packingUnit'
            }, {
                name: 'dosageForm',
                index: 'dosageForm',
            }, {
                name: 'storage',
                index: 'storage'
            }, {
                name: 'wareHouseName',
                index: 'wareHouseName'
            },

            {
                name: 'predictCheckTime',
                index: 'predictCheckTime',
                formatter: function (e) {
                    if (e != null && e != "") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'expireDay',
                index: 'expireDay'
            }, {
                name: 'validateDate',
                index: 'validateDate',
                formatter: function (e) {
                    if (e != null && e != "") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'checkName',
                index: 'checkName',

            }, {
                name: 'statusName',
                index: 'statusName'
            }, {
                name: 'checkTimes',
                index: 'checkTimes',
                formatter: function (e) {
                    if (e != null && e != "") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'remark',
                index: 'remark'
            }
        ],
        altRows: true, //设置为交替行表格,默认为false
        rowNum: 20,
        rowList: [20, 50, 100],
        selectandorder: true,
      //  rownumbers: true,//是否展示序号，多选
        onSelectRow: function (id, dom, obj, index, event) {
            //console.log('单机行事件', id, dom, obj, index, event);
        },
        gridComplete: function () {
            if (!$(this).XGrid('getRowData').length) {
                utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
            }
        },
        pager: '#grid-pager-a'
    });
    $.ajax({
        url: "/proxy-gsp/gsp/checkPlan/queryOrg",
        type: "get",
        success: function (result) {
            var options = ""
            for (var i = 0; i < result.result.length; i++) {
                options += " <option value=" + result.result[i].orgCode + " >" + result.result[i].orgName + "</option>"
            }
            $("#orgcode").html(options);
            var org = $("#orgvalue").val();
            $("#orgcode").val(org);
        }

    })


    /********************导出********************************/

    /* 导出 */
    $('#exportBtn').on('click', function () {
        var tableId = '';
        var pageId = '';
        if (0 == window.curIndex) {   //第一个选项卡
            tableId = 'CheckPlan_Table';
            pageId = 'grid_page';
        } else {
            tableId = 'X_Tablea';
            pageId = 'grid-pager-a';
        }


        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });
            var len = Number($('#' + pageId +'  #totalPageNum').text());
            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length > 0) {
                /*  if (!data.length) {
                      data = [data];
                  }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                
                len = data.length;
                
                data = JSON.stringify(data);
                // formData["selectData"] = data;
                
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            console.log(colName);
           
            if (0 == window.curIndex) {   //第一个选项卡
                var obj = {
                    startTime: $("#toTime").val(),
                    orgCode: $("#orgvalue").val(),
                    checkType: $("#checkType").find("option:selected").val(),
                    endTime: $("#endTime").val(),
                    selectData: data,
                    colName: colName,
                    colNameDesc: colNameDesc
                } 
                
                utils.exportAstrictHandle(tableId, len, 1).then( () => {
                    return false;
                }).catch( () => {
                	httpPost("/proxy-gsp/gsp/checkPlan/exportCheckPlan", obj);
                });                 
            } else {
                var obj = {
                    startTime: $("#toTime").val(),
                    orgCode:$("#orgvalue").val(),
                    endTime:$("#endTime").val(),
                    drugClass: $("#drugClass").find("option:selected").val(),
                    checkType:$("#checkType").find("option:selected").val(),
                    productCode:$("#drugCode").val(),
                    productName:$("#productName").val(),
                    productBatch:$("#productBatch").val(),
                    manufacturerName:$("#manufacturerName").val(),
                    selectData: data,
                    colName: colName,
                    colNameDesc: colNameDesc
                }
                
                utils.exportAstrictHandle(tableId, len, 1).then( () => {
                    return false;
                }).catch( () => {
                	httpPost("/proxy-gsp/gsp/checkPlan/exportCheckPlanInfo", obj);
                });                
            }


        });

    });


    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        if($("#input_goodName").prop('disabled')) return false;
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-gsp/gsp/checkPlan/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#drugCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    })
});



function httpPost(URL, PARAMS) {
    var temp = document.createElement("form");
    temp.action = URL;
    temp.method = "post";
    temp.style.display = "none";

    for (var x in PARAMS) {
        var opt = document.createElement("textarea");
        opt.name = x;
        opt.value = PARAMS[x];
        temp.appendChild(opt);
    }
    document.body.appendChild(temp);
    temp.submit();

    return temp;
}

//查询
function btn_search() {
    var activeIndex = $("#toggle_wrap li").index($("#toggle_wrap .active"));
    if(activeIndex === 0){
        $('#CheckPlan_Table').setGridParam({
            url: '/proxy-gsp/gsp/checkPlan/queryCheckPlanPage',
            postData: {
                startTime: $("#toTime").val(),
                orgCode:$("#orgvalue").val(),
                checkType:$("#checkType").find("option:selected").val(),
                endTime:$("#endTime").val(),
            }
        }).trigger('reloadGrid');
    }else if(activeIndex === 1){
        $('#X_Tablea').XGrid('clearGridData');
        $('#X_Tablea').setGridParam({
            url: '/proxy-gsp/gsp/checkPlan/queryCheckPlanPageInfo',
            postData: {
                startTime: $("#toTime").val(),
                orgCode:$("#orgvalue").val(),
                endTime:$("#endTime").val(),
                drugClass: $("#drugClass").find("option:selected").val(),
                checkType:$("#checkType").find("option:selected").val(),
                productCode:$("#drugCode").val(),
                productName:$("#productName").val(),
                productBatch:$("#productBatch").val(),
                manufacturerName:$("#manufacturerName").val()
            }
        }).trigger('reloadGrid');
    }
}


function  excelCheckPlan(){
	 utils.exportAstrictHandle('X_Tablea', Number($('#totalPageNum').text()), 1).then( () => {
         return false;
     }).catch( () => {
    	 window.location = '/proxy-gsp/gsp/checkPlan/excelCheckPlanInfo?checkPlanCode='+$("#checkNum").val()+'&orgCode='+$("#orgvalue").val()
     }); 
}

function  btn_output(){
	 utils.exportAstrictHandle('X_Tablea', Number($('#totalPageNum').text()), 1).then( () => {
         return false;
     }).catch( () => {
    	 window.location = '/proxy-gsp/gsp/checkPlan/excelCheckPlan?orgCode='+$("#orgvalue").val()+'&startTime='+$("#toTime").val()+'&endTime='+$("#endTime").val();
     });    
}


function  checkPlanTack(){

    $.ajax({
        url:'/proxy-gsp/gsp/checkPlan/checkPlanTack',
        type:'post',
        data:{dateTime:$("#dateTime").val()},
        success:function(result){
            alert(result.result)
        }

    })

}



























function  updateTack(){

    $.ajax({
        url:'/proxy-gsp/gsp/checkPlan/updateTask',
        type:'get',
        success:function(result){
        }

    })

}