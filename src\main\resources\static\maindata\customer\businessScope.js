var idArr=[];//存放经营范围已选中id

//批准文件
function initTable3(supplierBaseId,type){
    idArr=[];
    var xGridData=[];
    $.ajax({
        url:'/proxy-customer/customer/customerCommonData/getCustomerApprovalFileAll',
        data:{"correlationId":supplierBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            // console.log(3)
            // console.log(data);
            if(data.result.list!=null&& data.result.list.length>0){
                var dataList = data.result.list;
                for(var i=0;i<dataList.length;i++){
                    var zTreeNodes = [{
                        id: 0,
                        name: "经营范围",
                        // open: true,//展开
                        children:[]

                    }]
                    // console.log(dataList[i].scopeofoperationVo)
                    if(dataList[i].scopeofoperationVo && dataList[i].scopeofoperationVo.length > 0){
                        var scopeofoperationVo=dataList[i].scopeofoperationVo;
                        //// console.log(scopeofoperationVo)
                        $(scopeofoperationVo).each(function(index,item){
                            var _obj = {};
                            _obj.id = item.scopeId;
                            _obj.name = item.scopeName;
                            _obj.children = [];
                            $(item.children).each(function(cindex,citem){
                                var _obj_child = {};
                                _obj_child.id = citem.scopeId;
                                _obj_child.name = citem.scopeName;
                                _obj.children.push(_obj_child)
                            })
                            zTreeNodes[0].children.push(_obj)
                        })
                        function getNodeData(n){
                            var el = n.children;
                            if(el.length > 0){
                                for(var i = 0; i<el.length; i++){
                                    if(el[i].children && el[i].children.length > 0){
                                        getNodeData(el[i])
                                    }else{
                                        el[i].isParent = false
                                    }
                                }
                            }
                        }
                        getNodeData(zTreeNodes[0]);
                    }
                    //// console.log(zTreeNodes)
                    var certJson={};
                    certJson[dataList[i].credentialTypeId]=dataList[i].customerBusinessScopeVoList;
                    idArr.push(certJson);
                    //// console.log(dataList[i].credentialTypeId)
                    var a={
                        id: dataList[i].id,
                        credentialTypeId: dataList[i].credentialTypeId,
                        credentialCode:dataList[i].credentialCode,
                        customerBusinessScopeVoList: (zTreeNodes[0].children.length > 0 ? zTreeNodes:[]),
                        certificationOffice: dataList[i].certificationOffice,
                        openingDate: dataList[i].openingDate,
                        validUntil: dataList[i].validUntil,
                        enclosureCount: dataList[i].enclosureCount,
                        customerEnclosureVoList:dataList[i].customerEnclosureVoList
                    }
                    xGridData.push(a);
                }
            }
            if(xGridData.length<1){
                xGridData={
                    id: 1,
                    credentialTypeId: "",
                    credentialCode:"",
                    customerBusinessScopeVoList: [],
                    certificationOffice: "",
                    openingDate: "",
                    validUntil: "",
                    enclosureCount: "",
                    customerEnclosureVoList:[]
                }
            }
            xGridTable3(xGridData);
        }
    });
}
function xGridTable3(xGridData) {
    var customerApplType = $('#customerApplType').val();
    let isChangshaStorage = false;
    // if (customerApplType != '6'){
    //     isChangshaStorage = ($('[id=orgCode]').val() == '007');
    // }
    // console.log('isChangshaStorage  ' + isChangshaStorage);
    let table3ColNames = null, table3ColModel = null;
    var credentialTypeId = null;
    var credentialCode = null;
    var certificationOffice = null;
    var fcertDate = null;
    var zcertDate = null;
    if($('#edit').val() == 1){
        credentialTypeId = '#credentialTypeId';
        credentialCode = '#credentialCode';
        certificationOffice = '#certificationOffice';
        fcertDate = '#fcertDate';
        zcertDate = '#zcertDate';
    }
    if(isChangshaStorage){
        $('#pzwjUplode').css('display','none');
        table3ColNames = ['', '<i class="i-red">*</i>证书类型', '<i class="i-red">*</i>证书编号', '<i class="i-red">*</i>经营范围',
            '<i class="i-red">*</i>发证日期', '<i class="i-red">*</i>有效期至'];
        table3ColModel = [
            {
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true, //与反回的json数据中key值对应
            }, {
                name: 'credentialTypeId',
                rowtype: '#credentialTypeId',
                rowEvent: function (etype, c, d) {
                    var credentialId=etype.rowData.credentialTypeId;
                    // console.log(credentialId,etype.rowData.id)
                    getZtreeData(credentialId,etype.rowData.id);
                }
            }, {
                name: 'credentialCode',
                rowtype: '#credentialCode'
            }, {
                name: 'customerBusinessScopeVoList',
                rowtype: '#operScopeZtree',
                rowEvent: function (etype) {
                    initBaseDataBuseScope();//初始化经营范围内容
                }
            }, {
                name: 'openingDate',
                rowtype: '#fcertDate',
                formatter:function(value){
                    var date=value;
                    if(!value)return false;
                    date=format(value);
                    return date.split(' ')[0];
                }
            }, {
                name: 'validUntil',
                rowtype: '#zcertDate',
                formatter:function(value){
                    var date=value;
                    if(!value)return false;
                    date=format(value);
                    return date.split(' ')[0];
                }
            }
        ]
    }else{
        table3ColNames = ['', '<i class="i-red">*</i>证书类型', '<i class="i-red">*</i>证书编号', '<i class="i-red">*</i>经营范围',
            '<i class="i-red">*</i>发证日期', '<i class="i-red">*</i>有效期至', '附件','附件数据',"操作"];
        table3ColModel = [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true, //与反回的json数据中key值对应
        }, {
            name: 'credentialTypeId',
            rowtype: '#credentialTypeId',
            rowEvent: function (etype, c, d) {
                var credentialId=etype.rowData.credentialTypeId;
                // console.log(credentialId,etype.rowData.id)
                getZtreeData(credentialId,etype.rowData.id);
            }
        }, {
            name: 'credentialCode',
            rowtype: '#credentialCode'
        }, {
            name: 'customerBusinessScopeVoList',
            rowtype: '#operScopeZtree',
            rowEvent: function (etype) {
                initBaseDataBuseScope();//初始化经营范围内容
            }
        }, {
            name: 'openingDate',
            rowtype: '#fcertDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'validUntil',
            rowtype: '#zcertDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#table3";
                    str ='<a href="javascript:;" onclick="showImg(this,\''+tableId+'\');">'+value+'</a>';

                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        }, {
            name: 'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }, {
            name:"UploadeImg",
            formatter:function (val,objs,data) {
                var tableId = "#table3";
                return   '<button  type="button" class="btn btn-info ApprovalUploadeImg" data-id="'+data.id+'" onclick="clickAddImgUploaderImg(this)" disabled="disabled">上传附件</button>';
            }
        },
        ]
    }
    $('#table3').XGrid({
        data: xGridData,
        colNames: table3ColNames,
        colModel: table3ColModel,
        rownumbers: true,
    /*rowNum: 10,
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_page3',*/
        gridComplete: function () {
            setTimeout(function () {
                $("#table3 tr").each(function (index) {
                    if(index != 0){
                        var credentialId=$(this).find("td[row-describedby='credentialTypeId'] select option:selected").val();
                        var zTreeId=$(this).find(".operScopeZtree").attr("id");
                        // console.log($(this).find(".operScopeZtree"),zTreeId)
                        var zTree = $.fn.zTree.getZTreeObj(zTreeId);

                        for(var i=0;i<idArr.length;i++){
                            var item=idArr[i];
                            for(var n in item){
                                if(n == credentialId){
                                    var aList=item[n];
                                    if(aList){
                                        for(var s=0;s<aList.length;s++){
                                            if( zTree.getNodeByParam( "id",Number(aList[s].businessScopeCode))){
                                                zTree.checkNode( zTree.getNodeByParam( "id",Number(aList[s].businessScopeCode)), true );
                                                initBaseDataBuseScope();
                                            }
                                        }
                                        // if(zTree.getNodes()[0].children.length == aList.length){
                                        //     zTree.checkAllNodes(true);
                                        // }
                                        if(zTree.getNodes()[0].children.length >0){
                                    	  let child = zTree.getNodes()[0].children
                                          for(let i = 0; i< child.length ; i++){
                                              if(child[i]  == aList.length){
                                                 zTree.checkNode(zTree.getNodeByParam( "id",child[i].id), true); 
                                              }
                                          }
                                        	  
//                                        if(zTree.getNodes()[0].children[0].children.length > 0){
//                                            if(zTree.getNodes()[0].children[0].children.length == aList.length){
//                                                zTree.checkAllNodes(true);
//                                            }
//                                        }else{
//                                            if(zTree.getNodes()[0].children.length == aList.length){
//                                                zTree.checkAllNodes(true);
//                                            }
//                                        }
                                        }
                                    }
                                    break;
                                }
                            }
                        }

                    }
                })
                utils.setTableInpTit('#table3')
            },3)
            setTimeout(function(){
                $("#table3 tr").each(function (index) {
                    if (index != 0) {
                        var zTreeId = $(this).find(".operScopeZtree").attr("id");
                        var zTree = $.fn.zTree.getZTreeObj(zTreeId);
                        if($('#edit').val() != "1"){
                            var nodes=zTree.getNodes();
                            chkDis(nodes[0],zTree);
                        }
                    }
                })
            },4)
        }
    });
}
function clickAddImgUploaderImg(that){
        var rowData;
        var typeList=[];
        var eChoImgList=[];
        var $table=$("#table3");
        var thisID=$(that).data("id");
        rowData=$("#table3").getRowData(thisID);
        if(rowData&&rowData.customerEnclosureVoList&&rowData.customerEnclosureVoList!="null"&&rowData.customerEnclosureVoList.length>0){
            rowData.customerEnclosureVoList=JSON.parse(rowData.customerEnclosureVoList);
            if(rowData.customerEnclosureVoList.length>0){
                for(var i=0;i<rowData.customerEnclosureVoList.length;i++){
                    rowData.customerEnclosureVoList[i].type=$(that).parent().parent().find("select[name=credentialTypeId] option:selected").val();
                    rowData.customerEnclosureVoList[i].lineNum =$(that).data("id");
                }
            }
            typeList.push({
                text: $(that).parent().parent().find("select[name=credentialTypeId] option:selected").text(),
                value:$(that).parent().parent().find("select[name=credentialTypeId] option:selected").val(),
                lineNum: $(that).data("id")
            });
            $(that).parent().parent().find("select[name=credentialTypeId] option:selected").text();
            eChoImgList = eChoImgList.concat(rowData.customerEnclosureVoList);
            $(that).upLoad({
                typeList:typeList,//格式[{text:xxx,value:xxx}]
                eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
                fileParam:{
                    name:'enclosureName',
                    url:'url'
                },
                urlBack: function (data) {
                    //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                    // console.log(data)
                    if($.isEmptyObject(data)){
                        $table.find("tr").each(function () {
                            var id=$(this).attr('id');
                            if(id&&id==$(that).data("id")){
                                $table.setRowData(id,{'customerEnclosureVoList':[]});
                                $table.setRowData(id,{'enclosureCount':''});
                            }
                        })
                        return false;
                    }
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData.credentialTypeId == data[i].type && $(that).data("id")== data[i].lineNum){
                            l.push(list);
                        }
                    }
                    var trId=thisID;
                    $table.setRowData(trId,{'customerEnclosureVoList':l});
                    $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
                }
            });
        }
}
function chkDis(obj,zTree){
    if(obj){
        zTree.setChkDisabled(zTree.getNodeByParam( "id",obj.id), true);
        var children=obj.children;
        if(children && children.length > 0)
        {
            for(var i=0;i<children.length;i++)
            {
                //// console.log(children[i].id)
                zTree.setChkDisabled(zTree.getNodeByParam( "id",children[i].id), true);
                chkDis(children[i],zTree);
            }
        }else{
            //主数据  经营范围 树的子节点样式
            var node = zTree.getNodeByParam( "id",obj.id)
            node.isParent = false;
            zTree.updateNode(node);
        }
    }
}
//新增行
function addRow(tableId){
    var rowNumber=$(tableId).find("tr").not("first").length+1;
    $(tableId).addRowData({id:rowNumber})
    //委托人类型默认选中采购委托书
    var opt = $(tableId).find('tr:last').find('select[name=mandatorType] option');
    var defVal = null;
    $(opt).each(function(index,item){
        if($(item).text() == '采购委托书'){
            defVal = $(item).val();
        }
    })
    $(tableId).find('tr:last').find('select[name=mandatorType]').find('option[value="'+defVal+'"]').attr("selected",true)
    if(tableId == '#table3'){ // 批准文件
        $(".ApprovalUploadeImg").removeAttr('disabled');
    }
}

//删除行
function deleRow(tableId){
    var selectRow = $(tableId).XGrid('getSeleRow');
    if (!selectRow) {
        utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
    } else {
        $(tableId).XGrid('delRowData', selectRow.id);
        if(tableId == "#table3"){
            initBaseDataBuseScope();
        }
        utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
    }
}
//请求字典获取经营范围树数据
function getZtreeData(credentialId,id){
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/listbycredentialidAndorgcode",
        async : true,
        data:{
            credentialId:credentialId,
            orgCode:'001'
        },
        dataType:"json",
        success: function (data) {
            //// console.log(isInit);
            var zTreeNodes = [{
                id: 0,
                name: "经营范围",
                //open: true,//展开
                children:[]
            }]
            // var isInit = $('#isInit').val();
            // if(isInit){
            //     zTreeNodes[0].checked=true;
            // }
            if(!data )return false;
            if(data.result && data.result.length > 0){
                $(data.result).each(function(index,item){
                    var _obj = {};
                    _obj.id = item.scopeId;
                    _obj.name = item.scopeName;
                    _obj.children = [];
                    // if(isInit){
                    //     _obj.checked=true;
                    // }
                    $(item.children).each(function(cindex,citem){
                        var _obj_child = {};
                        _obj_child.id = citem.scopeId;
                        _obj_child.name = citem.scopeName;
                        // if(isInit){
                        //     _obj_child.checked=true;
                        // }
                        _obj.children.push(_obj_child)
                    })
                    zTreeNodes[0].children.push(_obj)
                })
                for(var i of zTreeNodes[0].children){
                    if(i.children.length == 0){
                        delete  i['children']
                    }
                }
                $('#table3').XGrid('setRowData', id,{customerBusinessScopeVoList:zTreeNodes,scopeofoperationVo:JSON.stringify(data.result)});
            }else{
                $('#table3').XGrid('setRowData', id,{customerBusinessScopeVoList:'',scopeofoperationVo:''});
            }
            // $('#isInit').val(0);
            // //// console.log(zTreeNodes)
            // $('#table3').XGrid('setRowData', id,{customerBusinessScopeVoList:zTreeNodes});

            var zTreeId=$('#table3 #'+id).find(".operScopeZtree").attr("id");
            var zTree = $.fn.zTree.getZTreeObj(zTreeId);

            zTree.refresh();
            for(var i=0;i<idArr.length;i++){
                var item=idArr[i];
                for(var n in item){
                    if(n == credentialId){
                        var aList=item[n];
                        for(var s=0;s<aList.length;s++){
                            if( zTree.getNodeByParam( "id",Number(aList[s].businessScopeCode))){
                                zTree.checkNode( zTree.getNodeByParam( "id",Number(aList[s].businessScopeCode)), true );
                            }
                        }
                    }
                }
            }
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
};

//初始化经营范围内容
/**
 * initBaseDataBuseScope() 此方法需要在有效期截止日期WdatePicker内调用 eg:<input type="text" placeholder="有效期" onclick="WdatePicker({onpicked:function(dp){initBaseDataBuseScope();}})"  name="validityDate"/>
 *
 * utils.operateRange 拼装数据
 *
 * @param XGrid 数据 $('#table3').XGrid('getRowData')
 *
 * @param 经营范围文件数当前列字段名 scopeOfBusiness
 *
 * @param 有效期截止字段  validityDate
 * */
function initBaseDataBuseScope(){
    var htmlAry = [], idAry = [];
    $.each(utils.operateRange($('#table3').XGrid('getRowData'),'customerBusinessScopeVoList','validUntil'), function (i, v) {
        if (v.status) {
            htmlAry.push('<font style="color: red;">' + v.name + '</font>');
        } else {
            htmlAry.push('<font>' + v.name + '</font>');
        }
        idAry.push(v.id);
    })
    //// console.log(htmlAry.join(','))
    $('#baseDataBuseScope').html(htmlAry.join(','));
}








