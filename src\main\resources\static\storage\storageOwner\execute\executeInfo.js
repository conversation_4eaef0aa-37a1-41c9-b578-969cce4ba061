$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);



    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/


    /* table_a */
    var colName = ['id','商品编码','原商品编码','商品大类','商品名称', '规格/型号','单位', '生产厂家', '库房名称', '批号', '生产日期', '有效期至',
          '调整前业务类型','申请调整数量', '实际调整数量','调整后业务类型', '备注'
    ];
    // '不含税成本单价',
    var colModel = [{
        name: 'id',
        index: 'id',
        hidden:true,
        hideGrid:true
    },{
        name: 'productCode',
        index: 'productCode'
    },{
        name: 'oldProductCode',
        index: 'oldProductCode'
    },{
        name:'drugClass',
        index:'drugClass'
    } ,{
        name: 'productName',
        index: 'productName'
    }, {
        name: 'specifications',
        index: 'specifications'
    },{
        name: 'packingUnit',
        index: 'packingUnit'
    }, {
        name: 'manufName',
        index: 'manufName'
    }, {
        name: 'storageType',
        index: 'storageType',
        formatter: function (e) {
            if(!re.test(e)){
                return e;
            }
            var result = "";
            $.each(jsonStore,function(idx,item){
                if(item.numCode == e){
                    result = item.name;
                    return false;
                }
            });
            return result;
        }
    },{
        name: 'batchNo',
        index: 'batchNo'

    }, {
        name: 'productDate',
        index: 'productDate'
    }, {
        name: 'validateDate',
        index: 'validateDate'

    }, {
        name: 'adjustBeforeChannelId',
        index: 'adjustBeforeChannelId'

    },{
        name: 'applyAdjustAmount',
        index: 'applyAdjustAmount'
    },{
        name: 'actualAdjustAmount',
        index: 'actualAdjustAmount'
    },{
        name: 'adjustAfterChannelId',
        index: 'adjustAfterChannelId'

    },{
        name: 'remarks',
        index: 'remarks'
    }

    ];


    $('#table_a').XGrid({
        url:"/proxy-storage/storage/StorageOwner/execute/findDetailList",
        postData:{orderCode:$("#orderCode").val()},
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'id',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
        },
        /*gridComplete: function () {
            setTimeout(function (param) {
                /!* 合计写入 *!/
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                $(sum_ele[0]).text(totalTable(data, 'text12'));
                $(sum_ele[1]).text(totalTable(data, 'text15'));
                $(sum_ele[2]).text(totalTable(data, 'text16'));
            }, 200)
        },*/
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    };

    /* 筛选列 */
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        //var tableId = $('#nav_content .active table').attr('id')
        $('#table_a').XGrid('filterTableHead');
    })



    /* 返回 */
    $('#goback').on('click', function () {
        utils.closeTab();

    });

})

