$(function () {

    // 时间格式化
    function formatDateTime(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }

    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);

    /* 填入初始数据 */
    /*$('#form_a').JSONToform({
      val_a: '举个栗子'
    })*/

    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    /* table_a */
    //data
    var grid_dataY = [{
        sort: "1",
        text1: "",
        text2: "",
        text3: "",
        text4: "",
        text5: "",
        text6: "",
        text7: "",
        text8: "",
        text9: "",
        text10: "",
        text11: "",
        text12: "2",
    }];
    for (let i = 0; i < 20; i++) {
        grid_dataY.push(grid_dataY[0]);
    }

    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/
    var colName = [ '商品编码','原商品编码','商品大类', '商品名称', '商品规格', '生产厂家', '产地', '单位', '库房名称', '批号', '生产日期', '有效期至', '调账数量',
         '不含税成本金额','业务类型'
    ];
    // '不含税成本单价',
    var colModel = [{
        name: 'productCode',
        index: 'productCode',
    },	{
        name: 'oldProductCode',
        index: 'oldProductCode'
    },
        {
        name: 'drugClass',
        index: 'drugClass'
    },{
        name: 'productName',
        index: 'productName'
    }, {
        name: 'productSpec',
        index: 'productSpec'
    }, {
        name: 'productmManufacturer',
        index: 'productmManufacturer'
    }, {
        name: 'producingArea',
        index: 'producingArea'
    }, {
        name: 'producingPackingUnit',
        index: 'producingPackingUnit'
    }, {
        name: 'storeName',
        index: 'storeName',
            formatter: function (e) {
                if(!re.test(e)){
                    return e;
                }
                var result = "";
                $.each(jsonStore,function(idx,item){
                    if(item.numCode == e){
                        result = item.name;
                        return false;
                    }
                });
                return result;
            }
    }, {
        name: 'batchCode',
        index: 'batchCode'
    }, {
        name: 'manufactureTime',
        index: 'manufactureTime',
        formatter: function (e) {
            //console.log(e)
            return formatDateTime(e);
        }
    }, {
        name: 'expiryTime',
        index: 'expiryTime',
        formatter: function (e) {
            //console.log(e)
            return formatDateTime(e);
        }
    }, {
        name: 'adjustmentNumber',
        index: 'adjustmentNumber'
    },  {
        name: 'costAmount',
        index: 'costAmount'
    }, {
            name: 'channelId',
            index: 'channelId'
        }



    ];
    // {
    //     name: 'costPrice',
    //         index: 'costPrice'
    // },
    $('#table_a').XGrid({
        url: "/proxy-storage/storage/adjustmentexecute/findDetailListByExecCode?adjustmentExecuteCode=" + adjustmentExecuteCode,
        // data: grid_dataY,
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'sort',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            /* 双击单据，进入单据详情页面。若单据状态为“待处理”，双击单据跳转至编辑销售退回单页面； */
            /*if (obj.text15 == '待处理') {
              window.location = 'return_receive_edit.html';
            } else {
              window.location = 'return_receive_info.html';
            }*/
        },
        gridComplete: function () {
            setTimeout(function (param) {
                /* 合计写入 */
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                $(sum_ele[0]).text(totalTable(data, 'costAmount'));
                // $(sum_ele[1]).text(totalTable(data, 'text15'));
                // $(sum_ele[2]).text(totalTable(data, 'text16'));
            }, 200)
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });


    /* 筛选列 */
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        //var tableId = $('#nav_content .active table').attr('id');
        $('#table_a').XGrid('filterTableHead');
    })

    /* 返回 */
    $('#goback').on('click', function () {
        utils.closeTab();
    });
})