var insFormHtml="";
var largeCategoryOld="";

var saveUrl="/proxy-product/product/orgApproval/saveOrgApproval";
var editUrl="/proxy-product/product/orgApproval/editOrgApprovalSave";
var submitAuditAgainUrl="/proxy-product/product/orgApproval/submitAuditAgain";
var secondCategoryBefore = "";
var secondCategoryBeforeVal = "";
var thirdCategoryBefore = "";
var thirdCategoryBeforeVal = "";
var largeCategoryArray = new Array();
largeCategoryArray[0] = "中药饮片";
largeCategoryArray[1] = "食品";
largeCategoryArray[2] = "保健食品";
largeCategoryArray[3] = "中药材";
largeCategoryArray[4] = "药食同源";
$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //tabs输入框只能输入数字限制
    $(document).on("input",".bootstrap-tagsinput input",function(){
        this.value=this.value.replace(/\D/g,'');
        $(this).attr("maxlength",15);
    });
    $(document).on("input","#inputdia input",function(){
        if($(this).hasClass('NAN_TYPE_CLASS')){ // 允许汉字类型

        }else{
            this.value=this.value.replace(/\D/g,'');
        }
        $(this).attr("maxlength",15);
    });
    $('body').on('click', '.cEdit', function (){
        var $t = $(window.changeApply_e); //找到对应节点
        var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
        console.log($t_flag.attr('changeapplyflag'));
        var changeapplyflag = $t_flag.attr('changeapplyflag');
        if("secondCategory"==changeapplyflag){//二级分类
            //三级分类可编辑
            $('#thirdCategoryVal').removeAttr('disabled');
            $('#thirdCategoryVal').nextAll('.yulanInput').hide();
            //保存二级分类
            var btn_save_largeCategory = $('#div_secondCategory').find('.changeApplyBtn');

            $(btn_save_largeCategory).unbind("click"); //移除click
            $(btn_save_largeCategory).on('click',function() {
                if($('#secondCategory').val()==""){//判断是否选择二级分类
                    utils.dialog({content: '请选择二级分类', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                if($('#thirdCategory').val()==""){//判断是否选择三级分类
                    utils.dialog({content: '请选择三级分类', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                //保存关联的三级分类
                delete window.changeApply["thirdCategory"];
                //保存三级的修改
                var thirdCategoryBeforeObj = {
                    attribute:"thirdCategory",
                    changeBefore:thirdCategoryBefore,
                    changeAfter: $('#thirdCategory').val(),
                    afterText:$('#thirdCategoryVal').val(),
                    changeType: '1'
                }
                window.changeApply["thirdCategory"] = thirdCategoryBeforeObj;
                //三级分类 回显后不可编辑
                $('#thirdCategory').val(thirdCategoryBefore);
                $('#thirdCategoryVal').val(thirdCategoryBeforeVal);
                $("#thirdCategoryVal").attr('data-value',thirdCategoryBeforeVal);
                $('#thirdCategoryVal').attr('disabled','disabled');
                $('#thirdCategoryVal').nextAll('.yulanInput').show().addClass('yulanInput_after');
            })
        }else if("thirdCategory"==changeapplyflag){//三级分类
            var btn_save_largeCategory = $('#div_thirdCategory').find('.changeApplyBtn');
            $(btn_save_largeCategory).unbind("click"); //移除click
            $(btn_save_largeCategory).on('click',function() {
                delChangeApply("secondCategory");
            })
        }
        var name = $t_flag.attr('name');
        if("supplyPrice"==name){//供货价
            var btn_save = $('#div_supplyPrice').find('.changeApplyBtn');
            $(btn_save).unbind("click"); //移除click
            $(btn_save).on('click',function() {
                //保存关联的三级分类
                delete window.changeApply["standardPrice"];
                //保存三级的修改
                var standardPriceObj = {
                    attribute:"standardPrice",
                    changeBefore:$('#standardPrice').val(),
                    changeAfter: $('#supplyPrice').val(),
                    afterText:$('#supplyPrice').val(),
                    changeType: '1'
                }
                window.changeApply["standardPrice"] = standardPriceObj;
            });
        }
    })
    //删除按钮
    $('body').on('click', '.cDelete', function (){
        var $t = $(window.changeApply_e); //找到对应节点
        var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
        console.log($t_flag.attr('changeapplyflag'));
        var changeapplyflag = $t_flag.attr('changeapplyflag');
        if("secondCategory"==changeapplyflag||"thirdCategory"==changeapplyflag){//二级分类
            delChangeApply("secondCategory");
            delChangeApply("thirdCategory");
        }
        var name = $t_flag.attr('name');
        if("supplyPrice"==name) {//删除供货价修改，同时删除标准价修改
            //删除通用名助记码
            delChangeApply("standardPrice");
            return;
        }
    })
    var pageType=$("#pageType").val();
    //非申请页，回显商品数据
    if (pageType != 0){
       var orgProductId= $("#orgProductId").val();
       obtainOrgProductInfo(orgProductId);
        //隐藏搜索图标及不可搜索
        $(".glyphicon-search").hide();
        $("#search_commodity").attr('disabled','disabled');

        $('.addTagBtn').removeClass('disBlock').addClass('disNone');
        $('.only-read').prev('div').find('input').removeClass('disBlock').addClass('disNone');
    }
    readOnly();
    $("#changeApplyBtn").hide();
        //工作流，流程图
        var workUrl="";
        if (pageType == 0 || pageType == 1){
            workUrl="/proxy-product/product/purchaseLimit/queryTotle?key="+$("#workProcessKey").val();
        }
        if (pageType==2 || pageType==3 || pageType==4){
            workUrl = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+$("#processId").val();
        }
        //获取审核流程数据
        $.ajax({
            type: "POST",
            url: workUrl,
            async: false,
            error: function () {
                utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
            },
            success: function (data) {
                if (data.code==0){
                    $('.flow').process(data.result);
                }else {
                    utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
                }
            }
        });
    $("input[name='entrustmentProduction']").click(function(){
        if ($(this).val()==0){
            $("#entrustmentManufacturer").val("");
            $("#entrustmentManufacturerVal").val("");
            $(".entManufacturerDiv").hide();
        }else {
            $(".entManufacturerDiv").show();
        }
    })
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //多选框选中
    loadData("keyConservationCategories");
    loadData("specialAttributes");
    //loadData("storageAttribute");
    // loadData("operatingCustomers");
    // loadData("specialProvision");
    function loadData(key) {
        var tagName, type, arr, thisVal;
        var value = $("#"+key).val();
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (type == 'checkbox') {
                if ($.type(value) == 'array') {
                    arr = value;
                } else if ($.type(value) == 'string') {
                    arr = value.split(',');
                }
                for (var i = 0; i < arr.length; i++) {
                    if (thisVal == arr[i]) {
                        $(this).attr('checked', true);
                        break;
                    }
                }
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //保存草稿
    $("#saveRowData").on("click", function () {
        parent.showLoading();
        $("#statues").val(0);
        if (pageType==0){
            butSubmint(saveUrl);
        }
        if (pageType==1){
            butSubmint(editUrl);
        }
    });
    //提交审核
    $("#submitAssert").on("click", function () {
        //提交前验证
        if (validform("applicationAttributeVo").form()) {
            parent.showLoading();
            $("#statues").val(1);
           /* var supplyPrice = $("#supplyPrice").val();//供货价
            var appPrice = $("#appPrice").val();//APP价
            if(Number(supplyPrice)>Number(appPrice)){
                var d =  dialog({
                    title: "提示",
                    content: "APP售价低于供货价，已造成<span style='color: red'>负毛利</span>，请慎重填写！",
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        butSubmint();
                        d.close().remove();
                        return false;
                    },
                    cancelValue: '取消',
                    cancel: function () {
                        d.close().remove();
                    }
                }).showModal();
            }else {

            }*/
            if (pageType==0){
                butSubmint(saveUrl);
            }
            if (pageType==1){
                butSubmint(editUrl);
            }
        } else {//验证不通过
            utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    });
    //关闭按钮
    $("#closePage").on("click", function () {
        var d =  dialog({
            title: "提示",
            content: "是否保存草稿？",
            width:300,
            height:30,
            okValue: '保存草稿',
            button:[
                {
                    value:'关闭',
                    callback:function(){
                        utils.closeTab();
                    }
                }
            ],
            ok: function () {
                parent.showLoading();
                $("#statues").val(0);
                if (pageType==0){
                    butSubmint(saveUrl);
                }
                if (pageType==1){
                    butSubmint(editUrl);
                }
                utils.closeTab();
            }
        }).showModal();
    });
    largeCategoryOld=$("#largeCategory").val();
    insFormHtml=$("#insForm").html();
    //二级分类 secondCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querytwocategory",{paramName:'categoryName',params:{"pid":$("#firstCategory").val()}
            ,isSearch:function(params){
                var code=$("#firstCategory").val();
                console.log(code);
                if(code != '')
                {
                    params.pid=code;
                    return params;
                }
                return false;
            }},"secondCategory",{data:"id",value:"categoryName"},""
        ,function (result2) {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
            $("#thirdCategoryVal").focus(function () {
                if($.trim($("#secondCategoryVal").val()) == '')
                {
                    $(this).blur();
                }
            })
        },function () {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
        });
    //三级分类 thirdCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querythreecategory",{paramName:'categoryName',params:{"pid":$("#secondCategory").val()}
        ,isSearch:function(params){
            var code=$("#secondCategory").val();
            if(code != '')
            {
                params.pid=code;
                return params;
            }
            return false;
        }},"thirdCategory",{data:"id",value:"categoryName"});
    //采购员 buyer
    var orgCode = $("#recordOrgCode").val();
    valAutocomplete("/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode",{paramName:'userName', params:{"orgCode":$("#recordOrgCode").val()}
        ,isSearch:function(params){
            var code=$("#recordOrgCode").val();
            console.log(code);
            if(code != '')
            {
                params.orgCode=code;
                return params;
            }
            return false;
        }},"buyer",{data:"id",value:"userName"},"-");
    //商品定位 commodityPosition
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":3}},"commodityPosition",{data:"id",value:"name"});
    //首营供应商 firstBattalionSupplier
    valAutocomplete("/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/firstSupplierOrgBaseList",{paramName:'supplierName',params:{"orgCode":$("#recordOrgCode").val()}
        ,isSearch:function(params){
            var code=$("#recordOrgCode").val();
            console.log(code);
            if(code != '')
            {
                params.orgCode=code;
                return params;
            }
            return false;
        }},"firstBattalionSupplier",{data:"id",value:"supplierName"});
    //加载字典值
    showDictValue();
    //供应价和app价计算
    $('#supplyPrice,#appPrice').keyup(function(){
        var supplyPrice=$("#supplyPrice").val();
        var appPrice=$("#appPrice").val();
        if(supplyPrice!=null&&supplyPrice!=""&&appPrice!=null&&appPrice>0){
            var value=(1-supplyPrice/appPrice)*100;
            value=toDecimal2(value);
            $("#parGrossMargin").val(value+"%");
        }else {
            $("#parGrossMargin").val("");
        }
    });
    /**
     * 输入价格显示两位小数
     * @param obj
     */
    $('#supplyPrice,#appPrice,#terminalPrice,#guideCostPrice,#chainGuidePrice').blur(function(){
        var num=toDecimal2(this.value);
        this.value=num;
    });
    //审核 是否显示搜索图标
    $("#search_commodity").dblclick(function () {
            commodity_search_dia();
    });
    //审核按钮
    $('.auditPass').on('click', function () {
        $('#auditOpinion').val('');
        //操作类型（0通过，1 驳回，2 关闭）
        var status=this.getAttribute("status");
        var title="审核通过";
        let statusTitle = new Map([
            [1,'审核不通过'],
            [2,'关闭审核'],
            ['default','审核通过']
        ]);
        title = statusTitle.get(Number(status)) || statusTitle.get('default');
        (status==1)?$('#opinion').show():$('#opinion').hide();
        if (status!=2) {
            utils.dialog({
                title: title,
                content: $('#container'),
                okValue: '确定',
                ok: function () {
                    //审核不通过，意见不能为空
                    if (status == 1) {
                        if ($("#auditOpinion").val() == "") {
                            utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                    }
                    submitAuditInfo(status, $("#auditOpinion").val());
                },
                cancelValue: '取消',
                cancel: function () {
                    $("#auditOpinion").val("");
                }
            }).showModal();
        }else {
            title="关闭审核";
            utils.dialog({
                title:title,
                width:300,
                height:30,
                okValue: '确定',
                content: "确定关闭此申请？",
                ok: function () {
                    submitAuditInfo(status,"");
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        }

    });
    $('#submitAuditAgain').on('click', function () {
        $("#statues").val(1);
        utils.dialog({
            title:"提交审核",
            width:300,
            height:30,
            okValue: '确定',
            content: "确定提交申请？",
            ok: function () {
                $("#statues").val(1);
                butSubmint(submitAuditAgainUrl+"?taskId="+$("#taskId").val())
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    })
    console.log('变更初始化...');
  //初始化变更数据
    /*$.changeApply_selectData(data, {
        name: 'attribute',
        status: 'changeType',
        afterValue: 'changeAfter',
        beforeValue: 'changeBefore'
    });*/    //申请页面
    if (pageType==0){
        $('[changeapplyflag]').siblings('input').attr('readonly', 'readonly');
        $('[changeapplyflag]').attr('disabled','disabled');
        $('#search_commodity').removeAttr('readonly').removeAttr('disabled');
        //开启变更事件
        $.changApply_insertData({
            name: 'attribute',
            status: 'changeType',
            afterValue: 'changeAfter',
            beforeValue: 'changeBefore'
        });  //按钮id：changeApplyBtn
    }else {
        $.ajax({
            url:'/proxy-product/product/orgApproval/getPropertyChangeDetail?correlationId='+$('#approvalRecordId').val()+"&type=1",
            type:'get',
            dataType:'json',
            success:function(data){
                if(data!=null&&data!=undefined){
                    //编辑页面
                    if (pageType==1||pageType==4){
                        var obj={};
                        $.each(data.result,function (i, v) {
                            v.changeType=-1;
                            obj[v.attribute]=v;
                        })
                        //开启变更事件
                        $.changApply_insertData({
                            name: 'attribute',
                            status: 'changeType',
                            afterValue: 'changeAfter',
                            beforeValue: 'changeBefore'
                        });  //按钮id：changeApplyBtn
                        window.changeApply=obj;
                        window.changeApplyBak=obj;
                    }
                    //审核页面
                    if (pageType==2||pageType==3){
                        $.changeApply_selectData(data.result, {
                            name: 'attribute',
                            status: 'changeType',
                            afterValue: 'changeAfter',
                            beforeValue: 'changeBefore'
                        });
                    }

                    $('.only-read').prev('div').find('input').removeClass('disBlock').addClass('disNone');
                }
            }
        })
    }

    //插入变更数据
    //window.changeApply
    //禁用标记元素


    setTimeout(function () {
        let scopeOfOperationVal = $('#scopeOfOperationVal').attr('data-value');
        if(scopeOfOperationVal == '中药饮片'){
            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地')
        }
    },200)






})
function loadUserName(key,orgaKey) {
    var userId=$("#"+key).val();
    if(userId==""){
        return;
    }
    $.ajax({
        type:"get",
        url: "/proxy-sysmanage/sysmanage/system/queryUserInfoById",
        data:{"userId":userId},
        dataType:"json",
        success: function (data) {
            //console.log(data.result);
            if(data.code==0&&data.result!=null){
                var userName=data.result.userName;
                if(orgaKey!=null){//申请人机构显示
                    $("#"+orgaKey+"Val").val(data.result.orgName);
                }
                $("#"+key+"Val").val(userName);
                $("#"+key+"Val").attr("data-value",userName);
            }
        },
        error:function () {
        }
    });
}
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
var Is_ZYYP_flag = false; // 商品大类是否为中药饮片
function valAutocomplete(url,param,obj,resParam,querydelimiter,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    var options={
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        querydelimiter:querydelimiter,
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
            if(obj == 'largeCategory'){
                if(result.value == '中药饮片'){
                    Is_ZYYP_flag = true
                    $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                    $('#maintenancePeriod').parents('.input-group').find('div').eq(0).find('i').remove(); // 养护周期
                    $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                    $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                    $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                    $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                    $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                }else{
                    if(Is_ZYYP_flag){
                        $('input[name=indate]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>有效期') // 有效期
                        $('#maintenancePeriod').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>养护周期'); // 养护周期
                        $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>批准文号') // 批准文号
                        $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>标准库ID') // 标准库ID
                        $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>是否委托生产')//是否委托生产
                        $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('产地') // 产地
                        $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('质量标准') // 质量标准
                    }
                }
            }
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    }
    if(param.isSearch)
    {
        options.isSearch=param.isSearch;
    }
    $("#"+obj+"Val").Autocomplete(options);
}

//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x*100)/100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}
/**
 * 设置输入两位小数 并且控制可输入的最大值的位数
 *
 * obj this对象
 * size 最大值位数 可不传
 * */
function setFixedTwo(obj,size) {
    var value=obj.value;
    var n='';
    if(size)
    {
        for(var i=0;i<size;i++)
        {
            n+='9';
        }
        n+='.99';
        if(Number(value) > Number(n))
        {
            value=n;
        }
    }
    obj.value=value.replace(/[^\d.]/g,'').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');
}

function  readOnly() {
    $('[changeapplyflag]').siblings('input').attr('readonly', 'readonly');
    $('[changeapplyflag]').attr('disabled','disabled');
    $('.only-read').attr('disabled','disabled');

}
/**
 * 搜索商品机构商品
 * @returns {boolean}
 */
function commodity_search_dia() {
    var keyword=$("#search_commodity").val();
    var pageType=$("#pageType").val();
    var loginOrgCode = $("#recordOrgCode").val();
    if (pageType == 0){
        loginOrgCode = $("#loginOrgCode").val()
    }
    dialog({
        url: '/proxy-product/product/orgApproval/searchProduct',
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {"orgCode":loginOrgCode,"keyword":keyword,"limitedPinState":0,"limitedProductionState":0,"disableState":0},
        onclose: function () {
            if (this.returnValue) {
                var data = this.returnValue;
                //console.log(data);
                //加载主数据信息
                var orgProductId=data.id;
                $("#orgProductId").val(orgProductId);
                //设置机构
                $("#recordOrgCode").val(data.orgCode);
                obtainOrgProductInfo(orgProductId);
                $("#search_commodity").attr('disabled','disabled');
            }
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
};

/**
 * 主数据内容回显
 * @param json
 */
function loadProductData(json) {
    var obj = json;
    var key, value, tagName, type, arr, thisVal;
    for (x in obj) {
        key = x;
        value = obj[x];

        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).prop('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    if(arr!=null){
                        for (var i = 0; i < arr.length; i++) {
                            if (thisVal == arr[i]) {
                                $(this).prop('checked', true);
                                break;
                            }
                        }
                    }
                } else {
                    if(key=='indate'){
                        if(value == 0){
                            $(this).val('*');
                        }else if(value == -1){
                            $(this).val('-');
                        }else {
                            $(this).val(value);
                        }
                    }else{
                        $(this).val(value);
                    }
                    $(this).attr('title',value);
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
                $(this).attr('title',value);
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //加载字典值
    showDictValue();
    //受托厂家是否显示,遮光属性是否显示
    initChecked();
}
//判断委托产家是否显示
function initChecked(){
    var type=$("input[name='entrustmentProduction']:checked").val();
    if(type == 1)
    {
        $(".entManufacturerDiv").show();
    }else{
        $(".entManufacturerDiv").hide();
    }
};

var checkSubmitFlag= false;
function checkSubmit(){
    if (checkSubmitFlag==true){
        return false;
    }
    checkSubmitFlag=true;
    return true;
}
function  valTest_area_Stand(name,title) {
    var flag = true;
    if(changeApply[name]){ // 先判断name是否有修改
        if(changeApply[name]['afterText'] == undefined){
            diaFun(title)
            flag = false;
        }else if(changeApply[name]['afterText'] == ''){
            diaFun(title)
            flag = false;
        }
    }else{
        if(name == 'producingArea'){ // 产地
            if($('input[name='+name+']').prev().find('span').length < 1){ // 如果没有修改，那name的原始数据也应该有值，才可以提交审核
                diaFun(title)
                flag = false;
            }
        }
        if(name == 'qualityStandard'){ // 质量标准
            if($('input[name='+name+']').val() == ''){
                diaFun(title)
                flag = false;
            }
        }
    }
    return flag;
}
function valMust_area_stand(name,title) {
    var flag = true;
    if(window.changeApply[name]){
        if(window.changeApply[name]['afterText'] == undefined){
            diaFun(title)
            flag = false;
        }else if(window.changeApply[name]['afterText'] == ''){
            diaFun(title)
            flag = false;
        }
    }else{
        if($('input[name='+name+']').val() == ''){
            diaFun(title)
            flag = false;
        }
    }
    return flag;
}
function  diaFun(title) {
    parent.hideLoading();
    utils.dialog({
        title:'提示',
        content: title+'信息不能为空。',
        okValue:'确定',
        ok:function () {}
    }).showModal();
    return false;
}


/**
 * 保存数据
 */
function  butSubmint(url) {
    /*if (!checkSubmit()){
        utils.dialog({content: '正在提交。。。', quickClose: true, timeout: 2000}).showModal();
        return false;
    }*/
    //商品编码为空，未选择商品验证
    if ($("#commodity_code").val()==""){
        utils.dialog({content: '请先选择商品', quickClose: true, timeout: 2000}).showModal();
        $('#search_commodity').removeAttr('readonly').removeAttr('disabled');
        parent.hideLoading();
        return false;
    }
    var productDataVo=getSavedData();
    // 提交审核 判断是否有修改
    if ($("#statues").val()==1){
        if(productDataVo.propertyApprovalDetailVos&&productDataVo.propertyApprovalDetailVos.length<1){
            utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
            $('#search_commodity').removeAttr('readonly').removeAttr('disabled');
            return false;
        }
    }
    var data=JSON.stringify(productDataVo);
    $.ajax({
        type:"post",
        url: url,
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            if (data.code==0){
                var taskStatus=data.result.taskStatus;
                if(taskStatus!=undefined&&!taskStatus) {
                    utils.dialog({
                        title: "提示",
                        content: data.result.msg + " 暂时保存为草稿",
                        width: 300,
                        height: 30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    return false;
                }else {
                    var msg="";
                    if ($("#statues").val()==0){
                        msg =  '保存成功';
                    }else if ($("#statues").val()==1){
                        msg = '提交审核成功';
                    }else {
                        msg = '保存成功';
                    }
                    utils.dialog({
                        title: "提示",
                        content: msg,
                        width:300,
                        height:30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    $(".ui-dialog-close").hide();
                    return false;
                }
            }else {
                utils.dialog({content: data.result,timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '保存失败', timeout: 2000}).showModal();
            /*checkSubmitFlag= false;*/
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}

/**
 * 获取提交数据
 * @returns {string}
 */
function getSavedData() {
    unReadOnly();
    //申请属性数据
    var applicationAttributeVo=$("#applicationAttributeVo").serializeToJSON();
    var changeApply = window.changeApply;
    var ary=[];
    $.each(changeApply,function (c, v) {
        ary.push(v);
    });
    //件包装与中包装数量变更为2
    applicationAttributeVo.applyType=2;
    var workProcessKey = $("#workProcessKey").val();
    var productData={'approvalRecordVo':applicationAttributeVo,'propertyApprovalDetailVos':ary,'workProcessKey':workProcessKey};
    readOnly();
    return  productData;
}
function  unReadOnly() {
    $(".only-read").each(function(){
        $(this).removeAttr('disabled');
    });}
/**
 * 字典值回显
 */
function  showDictValue() {
    loadUserName("buyer",null);//采购员回显
    //商品大类
    showComValue("largeCategory","1017");
    //生产厂家--添加地址信息
    //showManufacturerInfo("manufacturer",$("#manufacturer").val());
    //生产厂家
    //showComValue("manufacturer","1003");
    //包装单位
    showComValue("packingUnit","1002");
    //剂型
    showComValue("dosageForm","1001");
    // 委托厂家--添加地址信息
    //showManufacturerInfo("entrustmentManufacturer",$("#entrustmentManufacturer").val());
    // 委托厂家
    //showComValue("entrustmentManufacturer","1003");
    //存储条件
    showComValue("storageConditions","1019");
    //处方分类
    showComValue("prescriptionClassification","1016");
    //所属经营范围
    var simpleCode =$("#scopeOfOperation").val();
    var orgCode = $("#applicantOrgCode").val();
    if(simpleCode!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/queryscopebycode?orgCode='+orgCode+"&simpleCode="+simpleCode,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if(data.code == 0)
                {
                    var simpleCodeName = "";
                    for (var i = 0;i<data.result.length;i++){
                        if (i!=data.result.length-1){
                            simpleCodeName = simpleCodeName + data.result[i].name+",";
                        } else {
                            simpleCodeName = simpleCodeName + data.result[i].name;
                        }
                    }
                    $("#scopeOfOperationVal").val(simpleCodeName);
                    $("#scopeOfOperationVal").attr("data-value",simpleCodeName);
                    $("#scopeOfOperationVal").attr("title",simpleCodeName);
                }
            }
        })
    }
    /*// 一级分类 firstCategory
    showComValue("firstCategory","1025");
    //二级分类 secondCategory
    showComValue("secondCategory","1026");
    //三级分类 thirdCategory
    showComValue("thirdCategory","1027");*/
    //商品定位
    showComValue("commodityPosition","1013");
    //首营供应商 firstBattalionSupplier
    var supplierId=$("#firstBattalionSupplier").val();
    if(supplierId!=""){
        $.ajax({
            url:'/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/firstSupplierOrgBaseById?id='+supplierId,
            type:'get',
            dataType:'json',
            success:function(data){
                //console.log(data);
                if(data!=null&&data!=undefined){
                    $("#firstBattalionSupplierVal").val(data.supplierName);
                    $("#firstBattalionSupplierVal").attr("data-value",data.supplierName);
                }
            }
        })
    }
    //品牌
/*    var brand=$("#brand").val();
    if(brand!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/brand/queryById?id='+brand,
            type:'get',
            dataType:'json',
            success:function(data){
                console.log(data);
                if(data!=null&&data!=undefined){
                    $("#brandVal").val(data.result.brandName);
                    $("#brandVal").attr("data-value",data.result.brandName);
                }
            }
        })
    }*/
}
/*function showManufacturerInfo(obj,manuId) {
    if(manuId!=undefined&&manuId!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querymanufactorybyId?manuId='+manuId,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if (data.code==0){
                    $("#"+obj+"Val").val(data.result.manufactoryName);
                    $("#"+obj+"Address").val(data.result.address);
                }
            }
        })
    }
}*/
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showComValue(obj,type) {
    var id =$("#"+obj).val();
    if(id!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querybeanbytype?type='+type+"&id="+id,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                //console.log(data);
                if(data.code == 0)
                {
                    $("#"+obj+"Val").val(data.result);
                    $("#"+obj+"Val").attr("data-value",data.result);
                    $("#"+obj+"Val").attr("title",data.result);
                    if(obj=="largeCategory"){
                        if(data.result=="中药饮片") {
                            $("#Registrant").text('化妆品备案人/注册人');
                            $(".productrate").show();
                            if(data.result == '中药饮片'){
                                //Is_ZYYP_flag = true
                                $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                                $('#maintenancePeriod').prev().find('i').remove(); // 养护周期
                                $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                                $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                                $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                                $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                                $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                            }

                        }else if(data.result=="医疗器械"){
                            $("#Registrant").text('医疗器械注册人/备案人名称');
                        }else{
                            $("#Registrant").text('化妆品备案人/注册人');
                            $(".productrate").hide();
                        }
                        if(largeCategoryArray.indexOf(data.result)>-1) {
                            $(".productrate").show();
                        }else{
                            $(".productrate").hide();
                        }
                    }
                }
            }
        })
    }
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showValue(obj,data) {
    var key =$("#"+obj).val();
    for(var i=0;i<data.length;i++){
        if(data[i].data==key){
            $("#"+obj+"Val").val(data[i].value);
            $("#"+obj+"Val").attr('title',data[i].value);
        }
    }
}

/**
 * 审核按钮操作
 */
function submitAuditInfo(status,auditOpinion) {
    var productData={'recordId':$('#approvalRecordId').val(),'statues':status,'auditOpinion':auditOpinion,"taskId":$("#taskId").val()};
    var data=JSON.stringify(productData);
    console.log(data);
    $.ajax({
        type:"post",
        url: "/proxy-product/product/orgApproval/auditOrgApprovalDo",
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var msg = "";
            // if(status==0){
            //     msg =  '恭喜审核通过';
            // }
            // if(status==1){
            //     msg = '驳回成功';
            // }
            // if(status==2){
            //     msg = "流程已关闭！";
            // }
            let statusMsg = new Map([
                [0,'恭喜审核通过'],
                [1,'驳回成功'],
                [2,'流程已关闭'],
            ])
            msg = statusMsg.get(Number(status));
            utils.dialog({
                title: "提示",
                content: msg,
                width:300,
                height:30,
                okValue: '确定',
                ok: function () {
                    utils.closeTab();
                }
            }).showModal();
            $(".ui-dialog-close").hide();
            return false;
        },
        error:function () {
            utils.dialog({content: '操作失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
//根据机构商品ID，加载页面数据
function obtainOrgProductInfo(orgProductId){
    $.ajax({
        type:"post",
        url: "/proxy-product/product/orgApproval/getOrgProInfo",
        async : true,
        data:{"orgProductId":orgProductId},
        dataType:"json",
        success: function (data) {
            var result=data.result;
            console.log(data);
            $('input[data-role="tagsinput"]').tagsinput('removeAll');
            loadProductData(result.productBaseInfoVo);
            loadProductData(result.productOrganizationVo);
            $("#changeApplyBtn").show();
            secondCategoryBefore = $("#secondCategory").val();
            secondCategoryBeforeVal = $("#secondCategoryVal").val();
            thirdCategoryBefore=$("#thirdCategory").val();
            thirdCategoryBeforeVal=$("#thirdCategoryVal").val();
            var supplyPrice=toDecimal2($("#supplyPrice").val());
            $("#supplyPrice").val(supplyPrice);
            var appPrice=toDecimal2($("#appPrice").val());
            $("#appPrice").val(appPrice);
            var terminalPrice=toDecimal2($("#terminalPrice").val());
            $("#terminalPrice").val(terminalPrice);
            $('.only-read').prev('div').find('input').removeClass('disBlock').addClass('disNone');
        },
        error:function (XMLHttpRequest, textStatus, errorThrown) {
            utils.dialog({content: '加载数据失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
/**
 * 删除已修改字段
 */
function delChangeApply(name) {
    if (window.changeApplyBak && changeApplyBak[name]) {
        if (window.changeApply[name]) {
            window.changeApply[name] = {};
            window.changeApply[name]["changeType"] = 0;
            window.changeApply[name]["attribute"] = name;
        }
    } else {
        delete window.changeApply[name];
    }
    $('#'+name+'Val').nextAll('.yulanInput').removeClass('yulanInput_after');
}
