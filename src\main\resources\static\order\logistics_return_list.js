

$(function () {
    /* 日期初始化 */
    z_utils.initDate('beginDate', 'endDate');
    /* tabs 切换 */
    z_utils.tableCut('flex');
    var colName = ['机构名称', '销售退回单号', '退款申请单号','销售订单编号', '退款申请日期', '单据日期', '收货日期', '客户编号', '客户名称', '退款金额','付款方式','单据状态', '业务类型','备注','是否特药','订单类型'];
    var colModel = [
        {name: 'institutionName',index: 'institutionName'},
        {name: 'salesReturnCode',index: 'salesReturnCode',  width:'250'},
        {name: 'ecRefundRequestCode',index: 'ecRefundRequestCode',  width:'250'},
        {name: 'ecOrderCode',    index: 'ecOrderCode' ,  width:'250' },
        {name: 'refundRequestTime',index: 'refundRequestTime',formatter:function (e){
            if (e != null && e !="") {
                return ToolUtil.dateFormat(e, 'yyyy-MM-dd HH:mm:ss');
            } else {
                return "";
            }
        }},
        {name: 'applicantTime',index: 'applicantTime',formatter:function (e){
            if (e != null && e !="") {
                return ToolUtil.dateFormat(e, 'yyyy-MM-dd HH:mm:ss');
            } else {
                return "";
            }
        }},
        {name: 'collectGoodsTime',index: 'collectGoodsTime',formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd HH:mm:ss');
                } else {
                    return "";
                }
        }},
        {name: 'customerCode',index: 'customerCode'},
        {name: 'customerName',index: 'customerName',  width:'400',},
        {name: 'paymentAmount',index: 'paymentAmount'},
        {name: 'payType',index: 'payType',formatter:function (e) {
                if (e == '1') {
                    return '在线支付'
                } else if (e == '2') {
                    return '货到付款'
                } else if (e == '3') {
                    return '线下转账'
                } else if (e == '4') {
                    return '银行授信支付 '
                }else if (e == '5') {
                    return '自有账期支付'
                }else {
                    return "";
                }
        }},
        {name: 'documentsState',index: 'documentsState',formatter:function (e) {
            if(e==1){
                return '待处理';
            }else if(e==2){
                return '审核中';
            }else if(e==3){
                return '已驳回';
            }else if(e==4){
                return '入库中';
            }else if(e==5){
                return '已入库';
            }else if(e==6){
                return '已取消';
            }
        }},{name: 'channelId',index: 'channelId'},
        {name: 'remark',index: 'remark'},{
            name: 'isSpecialMedicine',
            index: 'isSpecialMedicine',
            formatter: function (e) {
                if (e == '1') {
                    return '特殊药品'
                } else {
                    return '否'
                }
            }
        },{
            name: 'orderType',
            index: 'orderType',
            formatter: function (e) {
                if (e == '1') {
                    return '普通订单'
                } else if (e == '2') {
                    return '连锁订单'
                }else if (e == '3') {
                    return '神农订单'
                }else if (e == '5') {
                    return '雨诺订单'
                }else if (e == '6') {
                    return '销售调账单'
                }else if (e == '7') {
                    return '智慧脸订单'
                }else if (e == '8') {
                    return '智慧脸2B订单'
                }else if (e == '9') {
                    return '智鹿请货订单（旧)'
                }else if (e == '10') {
                    return '赠品订单'
                }else if (e == '11') {
                    return '内部调拨单'
                }else if (e == '12') {
                    return '智鹿订单'
                }else if (e == '14') {
                    return '智鹿请货订单'
                }else if (e == '15') {
                    return '线下订单'
                }
            }
        }];

    $('#table_a').XGrid({
        url:"/proxy-order/order/logistics/orderReturn/getList",
        postData:{ "beginDate": $("#beginDate").val(), "endDate": $("#endDate").val()},
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'salesReturnCode',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            /* 双击单据，进入单据详情页面。若单据状态为“待处理”，双击单据跳转至编辑销售退回单页面； */
            utils.openTabs("return_receive_info","销售退回收货单详情",  '/proxy-order/order/logistics/orderReturn/detail?salesReturnCode='+obj.salesReturnCode);

        },
        onSelectRow: function (id, dom, obj, index, event) {

        }
    });

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }



    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })


    /* 客户名称 搜索提示（只显示5条） */
    $('#customerName').Autocomplete({
        serviceUrl: '/proxy-customer/customer/customerBaseAppl/pageList', //异步请求
        paramName: 'customerCode',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function(response) {
            return {
                suggestions: $.map(response.result.list, function(dataItem) {
                    return { value: dataItem.customerName, data: dataItem.customerCode };
                })
            };
        },
        onSelect: function (result) {
            //选中回调
            $("#customerCode").val(result.data)
        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });

    /* 商品名称 搜索提示（只显示5条） */
    $('#productName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function(response) {
            return {
                suggestions: $.map(response.result.list, function(dataItem) {
                    return { value: dataItem.productName, data: dataItem.productCode };
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        onSelect: function (result) {
            //选中回调
            $("#productCode").val(result.data)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
        // tabDisabled: true,
    });

    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        //获取form数据
        var data = $('#form_a').serializeToJSON();
        console.log(data);
        var customerName = $("#customerName").val();
        if(customerName==null||customerName==''){
            data.customerCode='';
        }
        //更新表格数据
        var $table_id = $('#nav_content .active .XGridBody table').attr('id');
        console.log($table_id)
        //列表
        $('#table_a').XGrid('setGridParam', {
            url:"/proxy-order/order/logistics/orderReturn/getList",
            postData:data
            ,page:1
        }).trigger("reloadGrid");
    });
    var _ind = 0; // tab 当前项的下标  销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    //获取当前tab  的下标 销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    function getTabInd() {
        for (var i = 0; i < $('.pull-left li').length; i++) {
            if ($('.pull-left li').eq(i).hasClass('active')) {
                _ind = $('.pull-left li').eq(i).index();
            }
        }
        return _ind;
    }
    /* 导出 */
    $('#exportRowData').on('click', function () {

        utils.exportAstrictHandle('table_a', Number($('#totalPageNum').text())).then( () => {
            return false;
      }).catch(() => {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            var formData = $('#form_a').serializeToJSON();
            formData["colName"] = colName;
            formData["colNameDesc"] = colNameDesc;

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length) {
                if (!data.length) {
                    data = [data];
                }
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
            }
            console.log(colName);

            // console.log(colNameDesc);
            switch (getTabInd()) {
                case 0:
                    httpPost("/proxy-order/order/logistics/orderReturn//exportOrderReturnList", formData);
                    break;
                case 1:
                    break;
            }
        });
   });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    /* 客户名称查询 */
    $('#s_user').on('click', function (e) {
        var user_d = $(e.target).prev('input').val();
        utils.dialog({
            url:"/proxy-order/order/logistics/orderReturn/toUserList",
            title: '客户列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $('#customerCode').val(data.customerCode);
                    $('#customerName').val(data.customerName);
                    console.log(data);
                }
            },
            oniframeload: function () {

            }
        }).showModal();
        return false;
    })
    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var commodity_d = $(e.target).prev('input').val();
        utils.dialog({
            url:"/proxy-order/order/logistics/orderReturn/toCommodityList",
            title: '商品列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            data: commodity_d, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $('#productName').val(data.productName);
                    $('#productCode').val(data.productCode);
                    console.log("this.returnValue", data);
                }
            },
            oniframeload: function () {

            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })
})