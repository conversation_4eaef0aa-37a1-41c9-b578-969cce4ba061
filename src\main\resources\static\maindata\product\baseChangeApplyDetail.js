var insFormHtml="";
var largeCategoryOld="";
$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    var baseProductId=$("#baseProductId").val();//主数据主键ID
    var baseApplicationCode=$("#baseApplicationCode").val();//主数据对应单据编码
    var applicationCode=$("#applicationCode").val();//首营申请对应单据编号
    //批准文件加载
    var scopeOfOperation= $("#scopeOfOperation").val();
    if(scopeOfOperation!=""){
        localBatchName(scopeOfOperation);
    }
    $('#X_Table').XGrid({
        //url:"/proxy-product/product/productApprovalFile/toList?type=0&correlationId="+baseProductId,
        colNames: ['','<i class="i-red">*</i>批件名称', '<i class="i-red">*</i>批件编号', '<i class="i-red">*</i>签发日期', '<i class="i-red">*</i>有效期至', '附件','附件数据'],
        colModel: [
            {
                name: 'id',
                index: 'id',
                hidden:true
            },
            {
                name: 'batchName',
                index: 'batchName',
                rowtype: '#batchName',
            }, {
                name: 'batchCode',
                index: 'batchCode',
                rowtype: '#batchCode',
            }, {
                name: 'issueDateStr',
                index: 'issueDateStr',
                rowtype: '#issueDate'
            }, {
                name: 'validityDateStr',
                index: 'validityDateStr',
                rowtype: '#validityDate'
            }, {
                name: 'enclosure',
                index: 'enclosure',
                formatter:function (value) {
                    var str='无';
                    if(value)
                    {
                        str ='<a href="javascript:;" onclick="showImg(this);">'+value+'</a>';
                    }
                    return str;
                },
                unformat: function (e) {
                    e=e.replace(/<[^>]+>/g,'');
                    if(e == '无'){
                        e = 0;
                    }
                    return e;
                }
            },{
                name:'enclosureList',
                index:'enclosureList',
                hidden:true,
                formatter:function (value) {
                    if(value)
                    {
                        return JSON.stringify(value);
                    }
                    return JSON.stringify([]);
                }
            }],
        rowNum: 10,
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        gridComplete: function () {
             if(baseApplicationCode!=applicationCode){//初始化只读处理
                 $('#X_Table input,#X_Table select').attr('disabled','disabled');
            }
            initFileSelected();
        }


    });
    //批量上传
    $("#batchUpload").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#X_Table');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("select[name='batchName'] option:selected");
            typeList.push({
                text:sel.text(),
                value:sel.val()
            });
            console.log(sel.val())
            //添加已存在附件
            if(rowData[i].enclosureList.length > 0)
            {
                rowData[i].enclosureList=JSON.parse(rowData[i].enclosureList);
                for(var j=0;j<rowData[i].enclosureList.length;j++)
                {
                    rowData[i].enclosureList[j].type=sel.val();
                }
                eChoImgList = eChoImgList.concat(rowData[i].enclosureList);
            }
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'enclosureList':[]});
                            $table.setRowData(id,{'enclosure':''});
                        }
                    })
                    return false;
                }
                for(var name in data)
                {
                    var list=data[name];
                    for(var i=0;i<rowData.length;i++)
                    {
                        if(rowData[i].batchName == name)
                        {
                            var trId=$table.find("tr").eq(i+1).attr('id');
                            $table.setRowData(trId,{'enclosureList':JSON.stringify(list)});//存储时只能存字符串，取值时用JSON.parse转换
                            $table.setRowData(trId,{'enclosure':(list.length == 0?'':list.length)});
                            break;
                        }
                    }
                }
            }
        });
    });

    //初始化只读处理和审核按钮处理
    if(baseApplicationCode!=applicationCode){
        readOnly();
        $("#auditNoPass").show();
    }
    //首营新增商品名可输入
    if(baseProductId==""){
        //首营新增可输入商品名查询
        $("#search_commodity").removeAttr('disabled');
    }
    //首营申请默认值
    radioDefaultChecked();
    //根据是否委托判断委托厂家是否显示
    $("input[name='entrustmentProduction']").click(function(){
        if ($(this).val()==0){
            $("#entrustmentManufacturer").val("");
            $("#entrustmentManufacturerVal").val("");
            $("#entManufacturerDiv").hide();
        }else {
            $("#entManufacturerDiv").show();
        }
    })
    var rowNumber=1;
    $("#addRowData").on("click",function () {
        rowNumber++;
        $('#X_Table').addRowData({id:rowNumber});
        initFileSelected();
    });
    $("#deleRow").on("click",function () {
        var selectRow = $('#X_Table').XGrid('getSeleRow');
        if (!selectRow) {
            utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
        } else {
            utils.dialog({
                title:"提示",
                width:300,
                height:30,
                okValue: '确定',
                content: "确定删除此条记录?",
                ok: function () {
                    $('#X_Table').XGrid('delRowData', selectRow.id);
                    utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        }
    });
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //多选框选中
    loadData("keyConservationCategories");
    loadData("specialAttributes");
    function loadData(key) {
        var tagName, type, arr, thisVal;
        var value = $("#"+key).val();
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (type == 'checkbox') {
                if ($.type(value) == 'array') {
                    arr = value;
                } else if ($.type(value) == 'string') {
                    arr = value.split(',');
                }
                for (var i = 0; i < arr.length; i++) {
                    if (thisVal == arr[i]) {
                        $(this).attr('checked', true);
                        break;
                    }
                }
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //保存草稿
    $("#saveRowData").on("click", function () {
        $("#statues").val(0);
        butSubmint();
    });
    //提交审核
    $("#submitAssert").on("click", function () {
        //提交前验证
        if (validform("productBaseInfoVo").form()
           &&validform("productOrganizationVo").form()
           &&validform("approvalFileVo").form()) {
            $("#statues").val(1);
            butSubmint();
        } else {//验证不通过
            utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    });
    //关闭按钮
    $("#closePage").on("click", function () {
        var d =  dialog({
            title: "提示",
            content: "是否保存草稿？",
            width:300,
            height:30,
            okValue: '保存草稿',
            ok: function () {
                $("#statues").val(0);
                butSubmint();
                d.close().remove();
                return false;
            },
            cancelValue: '关闭',
            cancel: function () {
                window.location.href='/proxy-product/product/productFirst/toList';
            }
        }).showModal();
    });
    //商品名
    $("#search_commodity").on("keyup",function (ev) {
        if(ev.keyCode != 13)
        {
            var value=this.value;
            getMnemonicCode(value,'productNameMnemonicCode');
        }
    })
    $("#commonName").on("keyup",function (ev) {
        if(ev.keyCode != 13)
        {
            var value=this.value;
            getMnemonicCode(value,'commonNameMnemonicCode');
        }
    })
    function getMnemonicCode(str,id){
        $.ajax({
            url:'/proxy-product/product/productFirst/getMnemonicCode?name='+str,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if(data.code == 0)
                {
                    $("#"+id).val(data.result);
                }
            }
        })
    }
    //生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord"},"manufacturer"
        ,{data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId"},""
        ,function (result) {
            $("#brandManufacturers").val(result.brandfactoryId);
            $("#brandManufacturersVal").val(result.brandfactoryName);
        },function () {
            $("#brandManufacturers").val("");
            $("#brandManufacturersVal").val("");
        });
    //包装单位
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querypackingunitnotpage",{paramName:"packingName"},"packingUnit",{data:"packingId",value:"packingName"});
    //剂型
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querydosenotpage",{paramName:"doseName"},"dosageForm",{data:"doseid",value:"dosename"});
    // 委托厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord"},"entrustmentManufacturer",{data:"manufactoryId",value:"manufactoryName"});
    //存储条件
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":2}},"storageConditions",{data:"id",value:"name"});
    //处方分类
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":6}},"prescriptionClassification",{data:"id",value:"name",numCode:"numCode"},""
        ,function (result) {
            $("#prescriptionClassificationCode").val(result.numCode);
        },function () {
            $("#prescriptionClassificationCode").val("");
        });
    //所属经营范围
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommodityscope",{paramName:'scopeName', params:{"orgCode":"001"}},"scopeOfOperation",{data:"simpleCode",value:"scopeName"},""
        ,function (result) {
            localBatchName(result.data);
        });
    largeCategoryOld=$("#largeCategory").val();
    insFormHtml=$("#insForm").html();
    //商品大类
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":7}},"largeCategory",{data:"id",value:"name"},""
        ,function (result) {
            if(largeCategoryOld==result.data){
                $("#insForm").html(insFormHtml);
            }else {
                $.ajax({
                    type:"post",
                    url: "/proxy-sysmanage/sysmanage/dict/queryexplaintemplatenotpage",
                    async : false,
                    data:{"commodityType":result.data,"isStop":0},
                    dataType:"json",
                    success: function (data) {
                        console.log(data)
                        if(data.code == 0)
                        {
                            var html = getHTML(data.result);
                            $("#insForm").html(html);
                        }else{
                            $("#insForm").html('');
                        }
                    },
                    error:function () {
                        utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
                    }
                });

            }
        },function () {
            $("#insForm").html("");
        });
    // 一级分类 firstCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryonecategory",{paramName:'categoryName'},"firstCategory",{data:"id",value:"categoryName"},""
    ,function (result) {
        var value=$("#firstCategoryVal").val();
        if(value != $("#firstCategoryVal").attr("data-value"))
        {
            $("#secondCategory").val('');
            $("#secondCategoryVal").val('');
            $("#secondCategoryVal").attr('data-value','');
            $("#thirdCategory").val('');
            $("#thirdCategoryVal").val('');
            $("#thirdCategoryVal").attr('data-value','');
        }
        $("#secondCategoryVal").focus(function () {
            if($.trim($("#firstCategoryVal").val()) == '')
            {
                $(this).blur();
            }
        })
        //二级分类 secondCategory
        valAutocomplete("/proxy-sysmanage/sysmanage/dict/querytwocategory",{paramName:'categoryName',params:{"pid":result.data}},"secondCategory",{data:"id",value:"categoryName"},""
        ,function (result2) {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
            $("#thirdCategoryVal").focus(function () {
                if($.trim($("#secondCategoryVal").val()) == '')
                {
                    $(this).blur();
                }
            })
            //三级分类 thirdCategory
            valAutocomplete("/proxy-sysmanage/sysmanage/dict/querythreecategory",{paramName:'categoryName',params:{"pid":result2.data}},"thirdCategory",{data:"id",value:"categoryName"});

        },function () {
                var value=$("#secondCategoryVal").val();
                if(value != $("#secondCategoryVal").attr("data-value"))
                {
                    $("#thirdCategory").val('');
                    $("#thirdCategoryVal").val('');
                    $("#thirdCategoryVal").attr('data-value','');
                }
        });
    },function () {
        var value=$("#firstCategoryVal").val();
        if(value != $("#firstCategoryVal").attr("data-value"))
        {
            $("#secondCategory").val('');
            $("#secondCategoryVal").val('');
            $("#secondCategoryVal").attr('data-value','');
            $("#thirdCategory").val('');
            $("#thirdCategoryVal").val('');
            $("#thirdCategoryVal").attr('data-value','');
        }
    });
    //加载字典值
    showDictValue();
    //审核 是否显示搜索图标
    // var applyType=$("#applyType").val();
    // if(applyType==""){
        $("#search_commodity").dblclick(function () {
            commodity_search_dia();
        });
    // }else if(applyType=="apply"){//审核页面隐藏商品搜索图标
    //     $(".glyphicon-search").hide();
    // }
    //审核按钮
    $('.audiPass').on('click', function () {
        $('#auditOpinion').val('');
        var status=this.getAttribute("status");
        var title="审核通过";
        if(status==3){
            title="审核不通过";
        }
        utils.dialog({
            title:title,
            content: $('#container'),
            okValue: '确定',
            ok: function () {
                //审核不通过，意见不能为空
                if (status!=2&&$("#auditOpinion").val()==""){
                    utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                parent.submitAuditInfo(status,$("#auditOpinion").val());
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    });

    //开启变更事件
    $.changApply_insertData({
        name: 'attribute',
        status: 'changeType',
        afterValue: 'changeAfter',
        beforeValue: 'changeBefore'
    });  //按钮id：changeApplyBtn

    var productId = $("#productId").val();
    $.ajax({
        type:"post",
        url: "/proxy-product/product/productBase/getProductBaseById",
        async : false,
        data:{"productId":productId},
        dataType:"json",
        success: function (data) {
            var result=data.result;
            console.log(data);
            loadProductData(result.productBaseInfoVo);
            //加载批准文件 按钮置灰
            var simpleCode= $("#scopeOfOperation").val();
            if(scopeOfOperation!=""){
                localBatchName(simpleCode);
            }
            $('#X_Table').setGridParam({
                url:"/proxy-product/product/productApprovalFile/toList",
                postData: {
                    "type": 0,
                    "correlationId": $("#baseProductId").val(),
                },page:1,
                gridComplete: function () {
                    //  $('#X_Table input,#X_Table select').attr('disabled','disabled');
                }
            }).trigger('reloadGrid');
            //加载说明书
            var html = getProductHTML(result.instructionsList);
            $("#insForm").html(html);
            readOnly();
        },
        error:function (XMLHttpRequest, textStatus, errorThrown) {
            utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
})
function initFileSelected() {

}
/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$('#X_Table').getRowData(parentId);
    if(data.enclosureList)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            list:JSON.parse(data.enclosureList)
        })
    }
}
/**
 * 加载所属经营范围对应的批准文件
 * @param simpleCode
 */
function localBatchName(simpleCode) {
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/querycommoditybatchnamebycode",
        async : false,
        data:{"simpleCode":simpleCode},
        dataType:"json",
        success: function (data) {
            var html='<option value="0">商品附件</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].batchId+'">'+arr[i].batchName+'</option>';
                    }
                }
            }
            $("select[name='batchName']").html(html);
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,querydelimiter,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        querydelimiter:querydelimiter,
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    });
}

//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x*100)/100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}
/**
 * 设置输入两位小数 并且控制可输入的最大值的位数
 *
 * obj this对象
 * size 最大值位数 可不传
 * */
function setFixedTwo(obj,size) {
    var value=obj.value;
    var n='';
    if(size)
    {
        for(var i=0;i<size;i++)
        {
            n+='9';
        }
        n+='.99';
        if(Number(value) > Number(n))
        {
            value=n;
        }
    }
    obj.value=value.replace(/[^\d.]/g,'').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');
}
/**
 *首营默认选中值设置
 */
function radioDefaultChecked() {
    //基本属性默认值
    //养护周期默认值
    if($("#maintenancePeriod").val()==''){
        $("#maintenancePeriod").val(90);
    }
    //有效期
    if(!$("#indateType").is(":checked")){
        $("#indateType").val("月");
    }
    //是否显示委托厂家处理
    setRadioChecked('entrustmentProduction','0');
    initChecked();
    // 是否税务优惠
    setRadioChecked('taxIncentives','0');
    //进项税率
    setRadioChecked('entryTaxRate','1');
    //销项税率
    setRadioChecked('salesRate','1');
    //限销状态
    setRadioChecked('baseLimitedPinState','0');
    //限采状态
    setRadioChecked('baseLimitedProductionState','0');
    //停用状态
    setRadioChecked('baseDisableState','0');
}
function setRadioChecked(name,value){
    var obj=$("input[name="+name+"]");
    if($("input[name="+name+"]:checked").length < 1)
    {
        obj.each(function () {
            var val=this.value;
            if(val == value)
            {
                $(this).prop("checked",true);
            }
        })
    }
}
function  readOnly() {
    //按钮置灰
    $("#addRowData").attr("disabled",true);
    $("#deleRow").attr("disabled",true);
    $("#batchUpload").attr("disabled",true);
    //加载说明书
   $('#insForm textarea').attr('disabled','disabled');
   $('.only-read').attr('disabled','disabled');
}
function  unReadOnly() {
    $(".only-read").each(function(){
        $(this).removeAttr('disabled');
        if(this.tagName == 'INPUT')
        {
            if(this.type == 'text') {
                this.value='';
            }else if(this.type == 'hidden') {
                this.value='';
            }else if(this.type == 'number') {
                this.value='';
            }else if(this.type == 'checkbox'){
                $(this).prop('checked',false);
            }
        }
    });
    // $("#X_Table").XGrid('clearGridData')
    // $("#addRowData").attr("disabled",false);
    // $("#deleRow").attr("disabled",false);
    // $("#batchUpload").attr("disabled",false);
    // $("#productBaseInfoVo input[data-role=\"tagsinput\"]").val('');
    // $("#productBaseInfoVo input[data-role=\"tagsinput\"]").tagsinput('removeAll');
}
/**
 * 搜索商品主数据
 * @returns {boolean}
 */
function commodity_search_dia() {
    var orgCode = $("#applicantOrgCode").val();
    var productName=$("#search_commodity").val();
    var applicationCode=$("#applicationCode").val();
    dialog({
        url: '/proxy-product/product/productFirst/toSearchList',
        title: '商品列表',
        width: 1000,
        height: 650,
        data: {"productName":productName,"orgCode":orgCode}, // 给modal 要传递的 的数据
        onclose: function () {
            if (this.returnValue && this.returnValue != 'add') {
                var data = this.returnValue;
                console.log(data);
                //加载主数据信息
                var productId=data.id;
                $.ajax({
                    type:"post",
                    url: "/proxy-product/product/productBase/getProductBaseById",
                    async : false,
                    data:{"productId":productId},
                    dataType:"json",
                    success: function (data) {
                        var result=data.result;
                        console.log(data);
                        loadProductData(result.productBaseInfoVo);
                        //加载批准文件 按钮置灰
                        var simpleCode= $("#scopeOfOperation").val();
                        if(scopeOfOperation!=""){
                            localBatchName(simpleCode);
                        }
                        $('#X_Table').setGridParam({
                            url:"/proxy-product/product/productApprovalFile/toList",
                            postData: {
                                "type": 0,
                                "correlationId": $("#baseProductId").val(),
                            },page:1,
                            gridComplete: function () {
                              //  $('#X_Table input,#X_Table select').attr('disabled','disabled');
                            }
                        }).trigger('reloadGrid');
                        //加载说明书
                        var html = getProductHTML(result.instructionsList);
                        $("#insForm").html(html);
                        readOnly();
                    },
                    error:function (XMLHttpRequest, textStatus, errorThrown) {
                        utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
                    }
                });
            }else if(this.returnValue && this.returnValue == 'add'){
                $("#commonNameMnemonicCode").val("");
                $("#productNameMnemonicCode").val("");
                $("#baseApplicationCode").val("");
                $("#brandManufacturers").val("");
                $("#brandManufacturersVal").val("");
                insFormHtml="";
                largeCategoryOld="";
                //是否委托
                $(":radio[name='entrustmentProduction'][value='0']").prop("checked", "checked");
                $("#entManufacturerDiv").hide();
                // 是否税务优惠
                $(":radio[name='taxIncentives'][value='0']").prop("checked", "checked");
                //进项税率
                $(":radio[name='entryTaxRate'][value='1']").prop("checked", "checked");
                //销项税率
                $(":radio[name='salesRate'][value='1']").prop("checked", "checked");
                //$("#packingList").html('');
                $('#insForm').html('');
                $("#commodity_code").val("");
                unReadOnly();
                $("#indateType").val("月");
                $("#maintenancePeriod").val(90);
                //批准文件下拉框
                $("select[name='batchName']").html('<option value="0">商品附件</option>')
            }
            //申请编号不能修改
            $("#applicationCode").val(applicationCode);
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
};

/**
 * 主数据内容回显
 * @param json
 */
function loadProductData(json) {
    var obj = json;
    var key, value, tagName, type, arr, thisVal;
    for (x in obj) {
        key = x;
        value = obj[x];

        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).prop('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    if(arr!=null){
                        for (var i = 0; i < arr.length; i++) {
                            if (thisVal == arr[i]) {
                                $(this).prop('checked', true);
                                break;
                            }
                        }
                    }
                } else {
                    $(this).val(value);
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //加载字典值
    showDictValue();
    //受托厂家是否显示
    initChecked();
}
//判断委托产家是否显示
function initChecked(){
    var type=$("input[name='entrustmentProduction']:checked").val();
    if(type == 1)
    {
        $("#entManufacturerDiv").show();
    }else{
        $("#entManufacturerDiv").hide();
    }
};

/**
 * 商品主数据带回说明书
 * @param arr
 * @returns {string}
 */
function getProductHTML(arr){
    var html='';
    for(var i=0;i<arr.length;i++)
    {
        html+='<div class="row attributeList">\n' +
            '        <div class="input-group">\n' +
            '<input type="hidden" class="instructionsId" name="id" value="">'+
            '           <label class="input-group-addon">'+arr[i].attribute+':<input type="hidden" class="attributeName" name="attribute" value="'+arr[i].attribute+'" /></label>\n' +
            '           <textarea class="form-control attributeValue" name="attributeValue">'+arr[i].attributeValue+'</textarea>\n' +
            '       </div>\n' +
            '</div>';
    }
    return html;
}

/**
 * 根据说明书模板显示
 * @returns {string}
 */
function getHTML(arr){
    arr=arr == null?[]:arr;
    var html='';
    for(var i=0;i<arr.length;i++)
    {
        html+='<div class="row attributeList">\n' +
            '        <div class="input-group">\n' +
            '<input type="hidden" class="instructionsId" name="id" value="">'+
            '           <label class="input-group-addon">'+arr[i].attributeName+':<input type="hidden" class="attributeName" name="attribute" value="'+arr[i].attributeName+'" /></label>\n' +
            '           <textarea class="form-control attributeValue" name="attributeValue"></textarea>\n' +
            '       </div>\n' +
            '</div>';
    }
    return html;
}

/**
 * 保存数据
 */
function  butSubmint() {
    // var productDataVo=getSavedData();
    // var data=JSON.stringify(productDataVo);
    // console.log(data);
    // $.ajax({
    //     type:"post",
    //     url: "/proxy-product/product/baseApproval/saveBaseChangeApply",
    //     async : false,
    //     data:data,
    //     dataType:"json",
    //     contentType: "application/json",
    //     success: function (data) {
    //         utils.dialog({content: data.msg, quickClose: true, timeout: 2000}).showModal();
    //         // var result=data.result.result;
    //         // var list=data.result.list;
    //         // if(list!=undefined&&list.length>0){
    //         //     var ids="";
    //         //     for(var i=0;i<list.length;i++){
    //         //         var product=list[i];
    //         //         if(i==0){
    //         //             ids=ids+product.id;
    //         //         }else {
    //         //             ids=ids+","+product.id;
    //         //         }
    //         //     }
    //         // }
    //         // console.log(ids);
    //         // if(result=='1'){
    //         //     window.location.href="/proxy-product/product/productFirst/toList";
    //         // }else if(result=='2'){
    //         //     utils.dialog({content: '商品已首营，不可重复提交', quickClose: true, timeout: 2000}).showModal();
    //         // }else if(result=='3'||result=='4'){
    //         //     var d=dialog({
    //         //         title:"提示",
    //         //         content:"商品已存在，请检查！",
    //         //         okValue: '查看重复商品',
    //         //         ok: function () {
    //         //             if(list.length==1){
    //         //                 window.open("/proxy-product/product/productBase/toDetail?productId="+ids);
    //         //             }else{
    //         //                 window.open("/proxy-product/product/productBase/toRepeatList?ids="+ids);
    //         //             }
    //         //         },
    //         //         cancelValue: '修改',
    //         //         cancel: function () {
    //         //             d.close().remove();
    //         //         }
    //         //     }).showModal();
    //         // }
    //     },
    //     error:function () {
    //         utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
    //     }
    // });
}

/**
 * 获取提交数据
 * @returns {string}
 */
function getSavedData() {
    /**
     * 申请属性数据 applyFormVo
     * 基础数据 productBaseInfoVo
     * 运营属性数据 productOrganizationVo
     */
    var productDataVo = {};
    //机构信息
    var propertyApprovalDetails=$("#applicationAttributeVo").serializeToJSON();
                         baseProductId
    // var productOrganizationVo=$("#productOrganizationVo").serializeToJSON();
    // productDataVo.productOrganizationVo=$.extend({}, applicationAttributeVo,productOrganizationVo);
    var productBaseInfoVo=$("#productBaseInfoVo").serializeToJSON();
    productDataVo.baseApprovalRecordVo=$.extend({}, propertyApprovalDetails,productBaseInfoVo);
    if($.isArray(productDataVo.baseApprovalRecordVo.keyConservationCategories))
    {
        productDataVo.baseApprovalRecordVo.keyConservationCategories=productDataVo.baseApprovalRecordVo.keyConservationCategories.join(',');
    }
    if($.isArray(productDataVo.baseApprovalRecordVo.specialAttributes))
    {
        productDataVo.baseApprovalRecordVo.specialAttributes=productDataVo.baseApprovalRecordVo.specialAttributes.join(',');
    }
    var instructions = [];
    $("#insForm .attributeList").each(function () {
        var instructionsId=$(this).find(".instructionsId").val();
        var attributeName=$(this).find(".attributeName").val();
        var attributeValue=$(this).find(".attributeValue").val();

        var javaObj = {};
        javaObj["id"] = instructionsId;
        javaObj["attribute"] = attributeName;
        javaObj["attributeValue"] = attributeValue;
        instructions.push(javaObj);
    })
    productDataVo.instructionsList=instructions;
    productDataVo.approvalFileDtos= $('#X_Table').getRowData();
    for(var i=0;i<productDataVo.approvalFileDtos.length;i++)
    {
        if(productDataVo.approvalFileDtos[i].enclosureList)
        {
            productDataVo.approvalFileDtos[i].enclosureList=JSON.parse(productDataVo.approvalFileDtos[i].enclosureList);
        }
    }
    return  productDataVo;
}

/**
 * 字典值回显
 */
function  showDictValue() {
    //商品大类
    showComValue("largeCategory","1017");
    //生产厂家
    showComValue("manufacturer","1003");
    //包装单位
    showComValue("packingUnit","1002");
    //剂型
    showComValue("dosageForm","1001");
    // 委托厂家
    showComValue("entrustmentManufacturer","1003");
    //存储条件
    showComValue("storageConditions","1019");
    //处方分类
    showComValue("prescriptionClassification","1016");
    //所属经营范围
    //showComValue("scopeOfOperation","1006");
    var simpleCode =$("#scopeOfOperation").val();
    var orgCode = $("#applicantOrgCode").val();
    if(simpleCode!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/queryscopebycode?orgCode='+orgCode+"&simpleCode="+simpleCode,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                console.log(data);
                if(data.code == 0)
                {
                    var simpleCodeName = "";
                    for (var i = 0;i<data.result.length;i++){
                        if (i!=data.result.length-1){
                            simpleCodeName = simpleCodeName + data.result[i].name+",";
                        } else {
                            simpleCodeName = simpleCodeName + data.result[i].name;
                        }
                    }
                    $("#scopeOfOperationVal").val(simpleCodeName);
                    $("#scopeOfOperationVal").attr("data-value",simpleCodeName);
                    $("#scopeOfOperationVal").attr("title",simpleCodeName);
                }
            }
        })
    }


    /*// 一级分类 firstCategory
    showComValue("firstCategory","1025");
    //二级分类 secondCategory
    showComValue("secondCategory","1026");
    //三级分类 thirdCategory
    showComValue("thirdCategory","1027");*/
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showComValue(obj,type) {
    var id =$("#"+obj).val();
    if(id!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querybeanbytype?type='+type+"&id="+id,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                console.log(data);
                if(data.code == 0)
                {
                    $("#"+obj+"Val").val(data.result);
                    $("#"+obj+"Val").attr("data-value",data.result);
                }
            }
        })
    }
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showValue(obj,data) {
    var key =$("#"+obj).val();
    for(var i=0;i<data.length;i++){
        if(data[i].data==key){
            $("#"+obj+"Val").val(data[i].value);
        }
    }
}

/**
 * 审核按钮操作
 */
function submitAuditInfo(status,auditOpinion) {
    var productData = {};
    if(status==2){
        productData=getSavedData();//表单数据
    }
    var applicationCode=$("#applicationCode").val();
    productData.applicationCode=applicationCode;
    productData.status=status;
    productData.auditOpinion=auditOpinion;
    console.log(productData);
    var data=JSON.stringify(productData);
    console.log(data);
    $.ajax({
        type:"post",
        url: "/proxy-product/product/productFirst/auditProduct",
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            utils.dialog({content: '保存成功', quickClose: true, timeout: 2000}).showModal();
        },
        error:function () {
            utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

