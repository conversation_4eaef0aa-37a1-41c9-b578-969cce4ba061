$(function() {
    var dialog = parent.dialog.get(window);
    $('#X_Table').XGrid({
        url: "/proxy-purchase/purchase/purchaseOrderProduct/queryProductLineCloseData?orderNo=" + $("#orderNo").val(),
        colNames: ['商品编码', '商品名称', '规格', '订单数量', '在途数量', '入库数量', '拒收数量'],
        colModel: [{
            name: 'productCode',
            index: 'productCode',

        }, {
            name: 'productName',
            index: 'productName',

        }, {
            name: 'productSpecification',
            index: 'productSpecification',

        }, {
            name: 'productPackCountSmall',
            index: 'productPackCountSmall',

        }, {
            name: 'productPackOnWayCount',
            index: 'productPackOnWayCount',

        }, {
            name: 'productPackInStoreCount',
            index: 'productPackInStoreCount',

        }, {
            name: 'productPackRejectStoreCount',
            index: 'productPackRejectStoreCount',

        }],
        rowNum: 99999,
        rownumbers: true,
        multiselect: true,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function(id, dom, obj, index, event) {},
        onSelectRow: function(id, dom, obj, index, event) {},
        pager: '#grid-pager',
    });

    $("#ok").on("click", function() {
        var sels = $('#X_Table').XGrid('getSeleRow');
        if (sels.length && sels.length > 0) {
            parent.parent.showLoading({ hideTime: 60000 });
            $.ajax({
                url: "/proxy-purchase/purchase/purchaseOrder/closeBatchOrderLine",
                data: {
                    productCodes: sels.map(function(item) { return item.productCode }).join(),
                    orderNo: $("#orderNo").val()
                },
                success: function(res) {
                    parent.parent.hideLoading();
                    if (res.code === 0) {
                        utils.dialog({
                            content: res.msg,
                            quickClose: true,
                            timeout: 2000
                        }).showModal();
                        setTimeout(function() {
                            dialog.close({ flag: 1 });
                        }, 2000)
                    } else {
                        utils.dialog({
                            content: res.msg,
                            cancelValue: '关闭',
                            cancel: function() {

                            }
                        }).showModal();
                    }
                }
            })
        } else {
            utils.dialog({ content: '请选择要关闭的商品！', quickClose: true, timeout: 2000 }).showModal();
        }
    });
});