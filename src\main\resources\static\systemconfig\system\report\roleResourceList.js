// 请求基本路径
var base_req_url = "/proxy-sysmanage/sysmanage/roleResouceCtr";

// 进入即执行
$(function() {
	queryRoleList();
	// 显示样式
	if (isEmpty($("#sys_dept").val())) {
		$("#sys_dept").attr("disabled", "disabled");
	}
});

// 获取角色
function queryRoleList(deptCode) {
//	if(deptCode == null) {
//		deptCode = $('#sys_dept').val();
//	}
	deptCode = "";
	$.ajax({
		url: "/proxy-sysmanage/sysmanage/role/queryRoleListByParam",
		data: {
			deptCode: deptCode
		},
		dataType: 'json',
		type: 'post',
		success: function(data) {
			if (data.code == 0) {
				var roleList = data.result;
				var opHtml = '';
				if (roleList != null && roleList.length > 0) {
					var html = "";
					$.each(roleList, function(index, role) {
						html += "<option value='" + role.id + "'>" + role.name + "</option>";
					});
					opHtml = html;
				}
				if (opHtml == '') {
					opHtml = '<option value="-1">无角色</option>';
					$("#sys_role").attr("disabled", "disabled");
				} else {
					$("#sys_role").removeAttr("disabled");
				}
				$("#sys_role").html(opHtml);
				renderData();
			} else {
				utils.dialog({
					content: data.msg,
					quickClose: true,
					timeout: 2000
				}).showModal();
			}
		}
	});
};

// 绑定部门change事件
$("#sys_dept").change(function() {
	queryRoleList($("#sys_dept").val());
});

//绑定查询click事件
$("#searchBtn").click(function() {
	renderData();
});

//绑定导出click事件
$("#sys_export").click(function() {
	exportRoleResourceReport2(1);
});

//绑定导出所有click事件
$("#sys_export_all").click(function() {
	exportRoleResourceReport2(2);
});

// 导出Excel
function exportRoleResourceReport(exportType) {
	var appCode = $("#sys_org_app_code").val();
	var roleId = $("#sys_role").val();
	if (isEmpty(exportType)) {
		roleId = null;
	}
	 
    if (isEmpty(exportType)) {
    	$.ajax({
    		url: base_req_url + "/roleResourceReportTotalCount",
    		data: {
    			appCode: appCode,
    			roleId: roleId
    		},
    		dataType: 'json',
    		type: 'post',
    		success: function(data) {
    			var msg = data.msg;
    			var totalCount = data.result;
    			// 是否超出限制    
    		    utils.exportAstrictHandle('role_resource_table', totalCount, 1).then( () => {
    		        return false;
    		    }).catch( () => {
    		    	window.location.href = base_req_url + "/exportRoleResourceReport?appCode=" + appCode;
    		    });	
//    			if (data.code == 0) {				
//    				if(!overLimit) {
//    					window.location.href = base_req_url + "/exportRoleResourceReport?appCode=" + appCode;
//    				} 
//    			}
//    			if(data.code != 0 || overLimit) {
//    				utils.dialog({content: msg, quickClose: true, timeout: 4000}).showModal();
//    			}
    		}
    	});		
	} else {		
		// 是否超出限制    
	    utils.exportAstrictHandle('role_resource_table', Number($('#totalPageNum').text()), 1).then( () => {
	        return false;
	    }).catch( () => {
	    	window.location.href = base_req_url + "/exportRoleResourceReport?appCode=" + appCode + "&roleId=" + roleId;
	    });		
	}	
}

/* 导出 */
function exportRoleResourceReport2(exportType) {
    var tableId = "role_resource_table";
    z_utils.exportTable(tableId, function (that) {
        //需要导出项
        var colName = [];
        var colNameDesc = [];
        $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
            if ($(this).prop("checked")) {
                colName.push($(this).attr('name'))
                colNameDesc.push($(this).parent().text());
            }
        });

        //获取当前选中项
        var data = $('#' + tableId).XGrid('getSeleRow');
        if (data && data.length && data.length >0) {
            /*  if (!data.length) {
                  data = [data];
              }*/
            data = data.map(function (item, key) {
                var new_item = {};
                colName.forEach(function (val, index) {
                    new_item[val] = item[val]
                })
                return new_item
            })
            data = JSON.stringify(data);
            // formData["selectData"] = data;
        } else {
            //如果一个也没选就导出全部
            // data = $('#' + tableId).XGrid('getRowData');
            data = '';
        }
//        var deptCode = $("#sys_dept option:selected").val();
//    	var deptName = $("#sys_dept option:selected").text();	
    	var roleId = $("#sys_role option:selected").val();
    	var roleName = $("#sys_role option:selected").text();    	
    	if (isEmpty(exportType)) {
    		roleId = null;
    	}
        var obj = {
//        		deptCode: deptCode,
//        		deptName: deptName,
        		roleId: roleId,
        		roleName: roleName,
            	selectData: data,
            	colNames: colName,
           		colNameDesc: colNameDesc,
           		exportType:exportType
        }
     // obj["nameModel"] = nameModel;
        if (isEmpty(exportType)) {
        	$.ajax({
        		url: base_req_url + "/roleResourceReportTotalCount",
        		data: {
        			appCode: appCode,
        			roleId: roleId
        		},
        		dataType: 'json',
        		type: 'post',
        		success: function(data) {
        			var msg = data.msg;
        			var totalCount = data.result;
        			// 是否超出限制    
        		    utils.exportAstrictHandle('role_resource_table', totalCount, 1).then( () => {
        		        return false;
        		    }).catch( () => {
        		    	httpPost(base_req_url + "/exportRoleResourceReport", obj);
        		    });	
        		}
        	});		
    	} else {		
    		// 是否超出限制    
    	    utils.exportAstrictHandle('role_resource_table', Number($('#totalPageNum').text()), 1).then( () => {
    	        return false;
    	    }).catch( () => {
    	    	httpPost(base_req_url + "/exportRoleResourceReport", obj);
    	    });		
    	}        
    });    
}

function httpPost(URL, PARAMS) {
    var temp = document.createElement("form");
    temp.action = URL;
    temp.method = "post";
    temp.style.display = "none";

    for (var x in PARAMS) {
        var opt = document.createElement("textarea");
        opt.name = x;
        opt.value = PARAMS[x];
        temp.appendChild(opt);
    }
    document.body.appendChild(temp);
    temp.submit();

    return temp;
}

// 导出Excel
//function exportRoleResourceReport(exportType) {
//	var deptCode = $("#sys_dept option:selected").val();
//	var deptName = $("#sys_dept option:selected").text();	
//	var roleId = $("#sys_role option:selected").val();
//	var roleName = $("#sys_role option:selected").text();
//	if (isEmpty(exportType)) {
//		window.location.href = base_req_url + "/exportRoleResourceReport?appCode=" + appCode;
//	} else {
//		window.location.href = base_req_url + "/exportRoleResourceReport?appCode=" + appCode + "&roleId=" + roleId;
//	}
//}


//判断字符是否为空的方法
function isEmpty(obj){
    if(typeof obj == "undefined" || obj == null || obj == ""){
        return true;
    }else{
        return false;
    }
}

// 渲染表格
function renderData() {
//	var deptCode = $("#sys_dept option:selected").val();
//	var deptName = $("#sys_dept option:selected").text();	
	var roleId = $("#sys_role option:selected").val();
	var roleName = $("#sys_role option:selected").text();

	$('#role_resource_table').XGrid({
		url: base_req_url + "/queryRoleResourceReportListPage",
		mtype: "POST",
		postData: {
//			deptCode: deptCode,
//			deptName: deptName,
			roleId: roleId,
			roleName: roleName
		},
		colNames: ['', '角色', '模块', '菜单权限', '菜单对应操作权限', '角色拥有操作权限'],
		colModel: [{
            name: 'id',
            index: 'id',
            key: true,
            hidden: true,
            hidegrid: true
        }, {
			name: 'roleName'
		}, {
			name: 'resourceModuleName'
		}, {
			name: 'resourceMenuName',
		}, {
			name: 'resourceBtnName',
		}, {
			name: 'roleResourceBtnName'
		}],
		rowNum: 20,
		rowList: [20, 50, 100],
		rownumbers: true,
		altRows: true,
		pager: '#grid-pager',
		ondblClickRow: function(id, dom, obj, index, event) {
		},
		onSelectRow: function(id, dom, obj, index, event) {
		}
	});
}
