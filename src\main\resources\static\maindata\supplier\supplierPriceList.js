var dialog = parent.dialog.get(window);
var selData = dialog.data.selData;
var selectType = dialog.data.viewType;
let newDialog = null, _dialog =  parent.dialog.get(window);
$('input[name=channelId]').val('');
$('input[name=promisePrice]').val('');

//  选中数据要怎么显示

$('#table').XGrid({
    url: '/proxy-supplier/supplier/supplierGoodsPrice/getSupplierGoodsPriceList',
    colNames: [
        '',
        'idVal',
        '<i style="color:red;margin-right:4px;">*</i>供应商编码',
        '供应商名称',
        '<i style="color:red;margin-right:4px;">*</i>商品编码',
        '商品名称',
        '关联fid',
        '通用名',
        '规格/型号',
        '生产厂家',
        '业务类型id',
        '<i style="color:red;margin-right:4px;">*</i>业务类型',
        '<i style="color:red;margin-right:4px;">*</i>约定供货价',
        'APP售价',
        '负毛利',
        '采购员',
        '机构',
        '机构code'
    ],
    colModel: [
        {
            name: 'id', //与反回的json数据中key值对应
            hidden:true
        },
        {
            name: 'idVal',
            hidden:true
        },
        {
            name: 'supplierCode',
        },
        {
            name: 'supplierName',
        },
        {
            name: 'productCode',
        },
        {
            name: 'productName',
        },
        {
            name: 'fid',
            hidden:true
        },
        {
            name: 'commonName',
        },
        {
            name: 'specifications',
        },
        {
            name:'manufacturerName',
        },
        {
            name: 'channelId',
            hidden:true
        },
        {
            name: 'channelVal',
            rowtype: '#channelId_inp',
            rowEvent: function (row) {
                // var data = row.rowData;
                // var id = data.id;
                // btn_searchChannel(id)
                // var options = ['继续销售', '待复查'];
                // //data.text24 = options[data.text23];
                // $('#table_a').XGrid('setRowData', id, {text24:options[data.text23]});
            }
        },
        {
            name: 'promisePrice',
            rowtype: '#promisePrice_inp'
        },
        {
            name: 'appPrice',
        },
        {
            name: 'grossProfit',
            hidden:true,
            // formatter: function(val,a,obj,c){
            //     if (obj.appPrice &&  obj.promisePrice && Number(obj.promisePrice) > Number(obj.appPrice)) {
            //         return  '是'
            //     }else{
            //         return  '否'
            //     }
            //
            // }
        },
        {
            name: 'buyer',
            hidden:true
        },
        {
            name: 'orgName',
            hidden:true
        },
        {
            name: 'orgCode',
            hidden:true
        }
    ],
    key: 'idVal',
    rowNum: 20,
    rowList:[20,50,100],
    rownumbers:true,
    altRows: true,//设置为交替行表格,默认为false
});
if (selData) {
    $('#table').XGrid('setGridParam', {
        url: '/proxy-supplier/supplier/supplierGoodsPrice/getSupplierGoodsPriceList',
        postData: {
            'selectType':selectType,
            'ids': selData.map(item => item.id).join(',')
        },page:1
    }).trigger('reloadGrid');
}
function fidInArr(arr,supplierOrganId){
    for(var i=0;i<arr.length;i++){
        if(arr[i].supplierOrganId == supplierOrganId){
            return true;
        }
    }
    return false;
}

function btn_Dismantle() {
    var selObj = $('#table').XGrid("getSeleRow");
    if(selObj.length){
        var rowNumber = $('#table').find("tr").not(":first").length + 1;
        selObj[0]['idVal'] = rowNumber;
        selObj[0]['id'] = '';
        selObj[0]['channelVal'] = '';
        selObj[0]['channelId'] = '';
        selObj[0]['promisePrice'] = ''; //拆行的需要
        selObj[0]['appPrice'] = '';
        $('#table').addRowData(selObj[0])
    }else{
        utils.dialog({"content":"请选择拆分行！","timeout":2000}).showModal();
    }
}
function delRow() {
    var selObj = $('#table').XGrid("getSeleRow");
    if(selObj.length){
        if (selObj[0].id){
            //ajax
            let id = selObj[0].idVal
            $.ajax({
                url:'/proxy-supplier/supplier/supplierGoodsPrice/delSupplierGoodsPriceList',
                type: 'post',
                data: {
                    id:id
                },
                success: function (res) {
                    console.log(res)
                },
                error: function (err) {
                    console.log(err)
                }
            })
        }
        $('#table').XGrid("delRowData",selObj[0].idVal);
        utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();

    }else{
        utils.dialog({"content":"请选择删除行！","timeout":2000}).showModal();
    }
}
function btn_sub() {
    let allData = $('#table').XGrid("getRowData");
    if(allData.length==0){
        utils.dialog({content: '请添加供货价格目录！', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    let checkArr = allData.map(item => item.supplierCode+item.productCode+'-'+item.channelId);
    let indArr = [];
    for(let i = 0 ; i < checkArr.length; i++) {
        for(let j = 1; j < checkArr.length; j++) {
            if(checkArr[i] === checkArr[j] && i!==j) {
                console.log(i+","+j)
                indArr.push(i)
                indArr.push(j)
            }
        }
    }
    let a = allData.filter(item => {
        return item.channelVal == '' || item.promisePrice == ''
    });
    if (a.length != 0) {
        utils.dialog({content: '完整信息', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    let finalIndArr = Array.from(new Set(indArr));
    for (let i = 0 ; i <finalIndArr.length ; i++){
        $('#table tr').not(':first').eq(finalIndArr[i]).css('color','red')
    }
    if (finalIndArr.length > 0){
        utils.dialog({
            title: '提示 ',
            content: '红色标注的数据行，供应商+商品+业务类型存在重复数据，是否继续？',
            okValue: '继续',
            ok: function () {
                $(finalIndArr).each((index,item)=> {
                    delete allData[item]
                })
                let finalArr = allData.filter(item => item);
                aa(finalArr)
            },
            cancelValue: '否',
            cancel: function () {}
        }).showModal()
    } else{
        aa(allData)
    }

}
function aa(allData) {
    if(allData.length>50){
        utils.dialog({content: '最多只能保存50条数据！', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    if (allData.length != 0){
        //
        let arr1 = allData.filter(item => {return  item.appPrice != '' }).filter(item => {return item.appPrice != 0});
        let arr2 = arr1.filter(item => {return Number(item.promisePrice) > 0 && Number(item.promisePrice) <= Number(item.appPrice)});
        if (arr1.length != 0){  //   只判断 APP售价  不为空或不为0 的数据
            if (arr2.length == 0) { // 判断不为空或不为0  的数据的 供货价
                utils.dialog({content: '约定供货价必须大于0并且约定供货价不能大于APP售价', quickClose: true, timeout: 2000}).showModal();
                return  false;
            }
        }else { //   当为空 或为 0 的时候 值判断供货价大于0  就行
            let arr3  = allData.filter(item => {return  item.appPrice == '' }).filter(item => {return item.appPrice == 0});
            let arr4 = arr3.filter(item => {
                return Number(item.promisePrice) <= 0
            })
            if (arr4.length != 0) {
                // 如果有小于等于0的数据
                utils.dialog({content: '约定供货价必须大于0.', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        }

    }
    $.ajax({
        url: '/proxy-supplier/supplier/supplierGoodsPrice/saveSupplierGoodsPrice',
        type: 'post',
        data:{
            'supplierGoodsPriceList':JSON.stringify(allData)
        },
        datatype: 'json',
        success: function (res) {
            console.log(res)
        },
        error: function (err) {
            console.log(err)
        },
        complete: function () {
            _dialog.close();
        }
    })
}
// 约定供货价 - APP售价 = 负毛利
function blur_promisePrice(el) {
    let promisePriceVal = $(el).parents('tr').find('[row-describedby="promisePrice"] input').val(),
        appPriceVal = Number($(el).parents('tr').find('[row-describedby="appPrice"]').text());
    // 约定供货价必须大于0并且约定供货价不能大于APP售价（APP售价为空或0时不做判断

    promisePriceVal = Number((promisePriceVal.indexOf('.') > -1) ? (promisePriceVal.substring(0,promisePriceVal.indexOf('.')+5)): promisePriceVal);
    $(el).val(promisePriceVal);
    if (appPriceVal){
        if (!(promisePriceVal  >  0 && promisePriceVal <= appPriceVal) ){
            utils.dialog({content: '约定供货价必须大于0并且约定供货价不能大于APP售价', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    }
    let flag = (promisePriceVal && appPriceVal && promisePriceVal  > appPriceVal)
    $(el).parents('tr').find('[row-describedby="grossProfit"]').text(flag ? '是':'否');

}





// 业务类型查询
function btn_searchChannel(el) {
    utils.channelDialog('1').then( res => {
        console.log(res)
        let _str_name = '', _str_code = '';
        let _str_arr = res.map(item => {
            return item.channelName;
        })
        _str_name = _str_arr.join(',');

        let _str_code_arr = res.map(item => {
            return item.channelCode;
        })
        _str_code = _str_code_arr.join(',')
        $('#channelId_inp').val(_str_name)
        $('#channelId').val(_str_code)
    }).catch( err =>{
        console.log(err);
    })
}


//取消
$("#cancleBtn").on("click", function () {
    _dialog.close();
});


$(function () {
    //  业务类型放大镜
    $("#table ").on("click", ".glyphicon-search", function () {
        let _this = this;
        let productCode = $(this).parents('tr').find('[row-describedby="productCode"]').text(),
            orgCode = $(this).parents('tr').find('[row-describedby="orgCode"]').text();
        utils.dialog({
            url: '/proxy-supplier/supplier/supplierGoodsPrice/toChannelList',
            title: '业务类型列表',
            width: $(window).width() * 0.93,
            height: $(window).height() * 0.7,
            data: {
                supportMultiple: '0',
                status: ''
            },
            onclose: function () {
                let res = this.returnValue;
                let _str_name = '', _str_code = '';
                let _str_arr = res.map(item => {
                    return item.channelName;
                })
                _str_name = _str_arr.join(',');
                let _str_code_arr = res.map(item => {
                    return item.channelCode;
                })
                _str_code = _str_code_arr.join(',')

                $(_this).siblings('input').val(_str_name);
                $(_this).parents('tr').find('[row-describedby="channelId"]').text(_str_code);
                $.ajax({
                    url: '/proxy-supplier/supplier/supplierGoodsPrice/getProductChannel',
                    type: 'post',
                    data: {
                        orgCode: orgCode,
                        productCode: productCode,
                        channelCode: _str_code // 字符串  逗号隔开的
                    },
                    success: function (res) {
                        let appPrice = res.result.appPrice
                        $(_this).parents('tr').find('[row-describedby="appPrice"]').text(appPrice ? appPrice : '')
                    },
                    error: function (err) {
                        console.log(err);
                    }
                })
            }
        }).showModal()
    })
})

