var orderType = $('#orderType').val();
var closeSoon = $('#closeSoon').val();
var warningOrClose = $('#warningOrClose').val();
if (orderType == '1') {
    $('#nav-tab .active a', window.parent.document).html('大客户采购订单管理');
} else {
    if (closeSoon != null && closeSoon != '') {
        $('#nav-tab .active a', window.parent.document).html('即将关闭的采购订单');
    } else if (warningOrClose != null && warningOrClose == '1') {
        $('#nav-tab .active a', window.parent.document).html('异常预付款订单');
    } else if (warningOrClose != null && warningOrClose == '2') {
        $('#nav-tab .active a', window.parent.document).html('自动关闭预付款订单');
    } else {
        $('#nav-tab .active a', window.parent.document).html('采购订单管理');
    }
}

//创建人模糊搜索
var selectvl = '';
$('#createUserNew1').Autocomplete({
    serviceUrl:'/proxy-purchase/purchase/purchaseRefundProductOrder/getPurchaseRefundProductOrderProductUserName?pageNum=1&pageSize=10&sort=asc&allFlag=true', //异步请求
    paramName: 'userName',//查询参数，默认 query
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    showNoSuggestionNotice: true,
    noSuggestionNotice: '查询无结果',
    triggerSelectOnValidInput: false, // 必选
    transformResult: function (response) {
        return {
            suggestions: $.map(response, function (dataItem) {
                return {value: dataItem.userName, data: dataItem.id};
            })
        };
    },
    onSelect: function (result) {
        $("#createUserNew").val(result.value);
        selectvl = result.value;
    },
    onNoneSelect: function (params, suggestions) {
        $("#createUserNew").val("");
        $("#createUserNew1").val("");
    }
});
var queryVal = getQueryValue('warningOrClose');
if (queryVal != null && queryVal == 2) {
    $('#orderStatus').val('');
}

/**
 * [通过参数名获取url中的参数值]
 * 示例URL:http://htmlJsTest/getrequest.html?uid=admin&rid=1&fid=2&name=小明
 * @param  {[string]} queryName [参数名]
 * @return {[string]}           [参数值]
 */
function getQueryValue(queryName) {
    var query = decodeURI(window.location.search.substring(1));
    var vars = query.split('&');
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split('=');
        if (pair[0] == queryName) {
            return pair[1];
        }
    }
    return null;
}

$(document).on('input propertychange', '#delayDays', function () {
    var limitNum = $(this)
        .val()
        .replace(/[^0-9]+/g, '');
    if (limitNum >= 0 && limitNum <= 100) {
        $(this).val(limitNum);
    } else {
        $(this).val('');
    }
});
$('#addnews').click(function () {
    var orderType = $('#orderType').val();
    var url = '/proxy-purchase/purchase/purchaseOrder/toAdd';
    if (orderType == 1) {
        url = '/proxy-purchase/purchase/purchaseOrder/toZAdd';
    }
    parent.openTabs('toLis', '新建采购订单', url);
    // parent.$('#mainFrameTabs').bTabsAdd("toLis", "新建采购订单", url);
});
//new add 采购合同
var imageType = ['rar', 'zip', 'pdf'];


/**
 * 打印合同页面
 */
$('#printContractBtn').click(function () {
    var data = $('#X_Tablea').XGrid('getSeleRow');
    var orderNos = '';
    if (data.length <= 0) {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    if (data.length > 1) {
        utils
            .dialog({ content: '请选中一条数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }

    if (data.length && data.length > 0) {
        data = data.map(function (item, key) {
            if (key > 0) {
                utils
                    .dialog({
                        content: '请选中一条数据！',
                        quickClose: true,
                        timeout: 4000,
                    })
                    .showModal();
                return false;
            }
            orderNos = item.orderNo;
        });
    }
    if (orderNos == '') {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    var flag = false;
    $.ajax({
        type: 'POST',
        url: '/proxy-purchase/purchase/upload/updateConstactStatus',
        data: {
            orderNo: orderNos,
        },
        error: function () { },
        success: function (res) {
            if (res) {
                if (res.code == 0) {
                    utils
                        .dialog({
                            content: '正在打印...',
                            timeout: 2000,
                        })
                        .showModal();
                    if (data && data.length > 0) {
                        $('#print_box')[0].contentWindow.getData(1, orderNos);
                    } else {
                        utils
                            .dialog({
                                content: '请先选中要打印的单据',
                                quickClose: true,
                                timeout: 2000,
                            })
                            .showModal();
                    }
                } else {
                    utils
                        .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                        .showModal();
                }
            } else {
                utils
                    .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                    .showModal();
            }
        },
    });
});
$('#printContracChapterstBtn').click(function () {
    var data = $('#X_Tablea').XGrid('getSeleRow');
    var orderNos = '';
    if (data.length <= 0) {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    if (data.length > 1) {
        utils
            .dialog({ content: '请选中一条数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }

    if (data.length && data.length > 0) {
        data = data.map(function (item, key) {
            if (key > 0) {
                utils
                    .dialog({
                        content: '请选中一条数据！',
                        quickClose: true,
                        timeout: 4000,
                    })
                    .showModal();
                return false;
            }
            orderNos = item.orderNo;
        });
    }
    if (orderNos == '') {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    var flag = false;
    $.ajax({
        type: "POST",
        url: "/proxy-purchase/purchase/upload/updateContractStatusStamp",
        data: {
            orderNo: orderNos,
        },
        success: (res) => {
            
                if (res.code == 0) {
                    $.ajax({
                        type: "POST",
                        url: "/proxy-purchase/purchase/upload/purchaseOrderContractStampPrint",
                        data: {
                            orderNo: orderNos,
                        },
                        success: (res) => {
                                if (res.code == 0) {
                                  if(res.result.flag==0){
                                 
                                   window.open(res.result.stampFileUrl)
                                  }else{
                                    utils
                                    .dialog({ content: res.result.desc, quickClose: true, timeout: 2000 })
                                    .showModal();
                                  }
                                  
                
                                } else {
                                    utils
                                        .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                                        .showModal();
                                }
                           
                        
                        }
                       })
                  

                } else {
                    utils
                        .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                        .showModal();
                }
           
        
        }
       })
      
});
$('#batchOrderCreation').on('click', function () {
    utils.dialog({
        align: 'top',
        width: 185,
        height: 41,
        padding: 2,
        content: '<div style="margin: 3px;">' +
            '<span><button type="button" class="btn btn-info" id="downloadTemplates">下载模板</button></span>' +
            '<span style="margin-left:9px"><button type="button" class="btn btn-info" id="batchImport">批量导入</button></span>' +
            '</div>',
        quickClose: true
    }).show(this);
})
$(document).on('click', '#downloadTemplates', function() {
    window.open("/proxy-purchase/purchase/purchaseOrder/downloadTemplate?batchCreatePurchase='采购计划单批量建单'");
});
$(document).on('click','#batchImport',function (){
	const fileInput = document.createElement("input");
	fileInput.type = 'file';
	fileInput.onchange = () => {
		const file = fileInput.files[0];
		const form = new FormData();
		form.append('file', file);
		$.ajax({
			url: '/po/org-purchase-plan/purchase-plan/batch-import',
			type: 'post',
			processData: false,  // 禁用 jQuery 的自动处理数据
			contentType: false,
			data: form,
			success: function(data) {
				/* const d = dialog({
					content: data.msg
				});
				d.show();
				setTimeout(function () {
					d.close().remove();
				}, 2000);
				$("#SearchBtn").trigger("click"); */
				if(data.code==0){
					//跳建单页面
                    utils.openTabs("batchOrderCreation", "批量建单", "/#/supply/purchase/batchOrderCreation?key=" + data.result +'&type=1');
				}else {
					utils.dialog({content:data.msg, quickClose: true, timeout: 2000}).showModal();
					return;
				}
			},
			error: function(error) {
				var d = dialog({
					content: '请求出错'
				});
				d.show();
				setTimeout(function () {
					d.close().remove();
				}, 2000);
			}
		})
	}
	fileInput.click();
})
$('#printContracChaptersPaddingtBtn').click(function () { //签章（填充版）
    var data = $('#X_Tablea').XGrid('getSeleRow');
    var orderNos = '';
    if (data.length <= 0) {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    if (data.length > 1) {
        utils
            .dialog({ content: '请选中一条数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }

    if (data.length && data.length > 0) {
        data = data.map(function (item, key) {
            if (key > 0) {
                utils
                    .dialog({
                        content: '请选中一条数据！',
                        quickClose: true,
                        timeout: 4000,
                    })
                    .showModal();
                return false;
            }
            orderNos = item.orderNo;
        });
    }
    if (orderNos == '') {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    //琦玉240511 开发时需要放开
    var flag = false;
    $.ajax({
        type: "POST",
        url: "/proxy-purchase/purchase/upload/updateContractStatusStamp",
        data: {
            orderNo: orderNos,
        },
        success: (res) => {
            
                if (res.code == 0) {
                    $.ajax({
                        type: "POST",
                        url: "/proxy-purchase/purchase/upload/purchaseOrderContractStampPrintNew",
                        data: {
                            orderNo: orderNos,
                        },
                        success: (res) => {
                                if (res.code == 0) {
                                  if(res.result.flag==0){
                                 
                                   window.open(res.result.stampFileUrl)
                                  }else{
                                    utils
                                    .dialog({ content: res.result.desc, quickClose: true, timeout: 2000 })
                                    .showModal();
                                  }
                                  
                
                                } else {
                                    utils
                                        .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                                        .showModal();
                                }
                           
                        
                        }
                       })
                  

                } else {
                    utils
                        .dialog({ content: res.msg, quickClose: true, timeout: 2000 })
                        .showModal();
                }
           
        
        }
       })
      
});
var startDate = '';

function getNowFormatDate() {
    var date = new Date();
    var seperator1 = '-';
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = '0' + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate;
    } else if (strDate >= 30) {
        strDate = strDate - 30;
        month = '0' + (parseInt(month) + 1);
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    startDate = currentdate;
    return currentdate;
}

getNowFormatDate();

//是否选择了机构
function didChooseOrgCode() {
    //检查机构是否选择了,如果没选择,不请求,提示
    if ($('#sysOrgCode').val() == '001' && $('#orgCode').val() == '') {
        //集团的才需要进行此操作
        utils
            .dialog({
                title: '提示',
                content: '尚未选择机构,请先选择',
                okValue: '确定',
                ok: function () { },
            })
            .showModal();
        return false;
    }
    return true;
}

$('#orgCode').change(function (e, b) {
    if ($('#sysOrgCode').val() == '001') {
        //集团的才需要进行此操作
        //清空商品,
        $('#productNameKeyword').val('');
        $('#productCode').val('');
        // 清空供应商
        $('#supplierCodeFor').val('');
        $('#factory_aotuname').val('');
        setTimeout(function () {
            $('#factory_aotuname').attr('oldvalue', '');
        }, 200);
        // 清空采购员的值
        $('#createUserName').val('');
        $('#createUser').val('');
        // 清空商品原编码
        $('#oldProductCode').val('');
    }
});

$('#oldProductCode').focus(function (e) {
    didChooseOrgCode();
});

// function addOrderAttribute() {
//     $.ajax({
//         url: '/proxy-purchase/purchase/orderAttribute/dict/queryListByType',
//         type: 'get',
//         data: { type:1,
//                 orgStatus: 1,
//                },
//         success: function (data) {
//             $.each(data.result, function (infoIndex, info) {  //循环遍历后台传过来的json数据
//                 $("#centralizedPurchaseType").append("<option value='" + info["code"] + "'>" + info["name"] + "</option>");
//             });
//         }
//     });
// }

$(function () {
    $.ajax({
        url: '/proxy-purchase/purchase/supplier/isShowPurchaseButton',
        type: 'GET',
        success: function (data) {
            if(data.code == 0){
                if(!data.result){
                    $('#addnews').hide();
                }
            }
        }
    });
    var currentUrl = window.location.href;
    console.log(currentUrl,'yz');
    // 解码URL
    var decodedUrl = decodeURIComponent(currentUrl);
    console.log(decodedUrl,'yz0');
    // 提取问号后的参数值
    console.log('zmt',222);
    var parameterValue = decodedUrl.split('?')[1];
    console.log(parameterValue,'yz1');
    let paramsOrderNo = getQueryValue('orderNo');
    if(paramsOrderNo){
        $('#orderNo').val(paramsOrderNo)
    }
    if(parameterValue == 'flag=1'){
        console.log('zmt',111);
        $('#onWayDaysOperatorEnum').val('GT');
        $('#onWayDays').val('14');
    }
    $('div[fold=head]').fold({
        sub: 'sub',
    });
    var totalTable = z_utils.totalTable;

    var colNameB = [
        '机构',
        '业务类型',
        '订单属性',
        '来源单号',
        '供应商名称',
        '采购订单编号',
        '创建人',
        '创建日期',
        '采购员',
        'SKU编码',
        'TOP排名',
        '是否新品',
        '商品编号',
        '原商品编码',
        '商品名称',
        '通用名',
        '规格',
        '单位',
        '生产厂家',
        '产地',
        '剂型',
        '订单数量',
        '含税单价',
        '是否近效期',
        '生产日期',
        '有效期至',
        '备注',
        '含税金额',
        '平台近7天成交均价',
        '全国近7天最低采购价',
        '全国近7天最低采购价所属机构',
        '近30天全国最低采购价',
        '审批销售毛利',
        '近30天财务毛利率',
        '审批周转天数',
        'APP售价',
        '当前价差毛利',
        '连锁APP售价',
        '税率',
        '中包装规格',
        '大包装规格',
        '品牌厂家',
        '商品批准文号',
        '批准文号有效日期',
        '是否过期',
        '最后一次购进价',
        '库存数量',
        '当前周转天数',
        '购进后周转天数',
        '7天日均销售',
        '15天日均销售',
        '30天日均销售',
        '大平台7天销量',
        '全国近30天销量',
        '在途数量',
        '库存上限',
        '安全库存',
        '是否协议品种',
        '是否协议内购进',
        '最后供应商编码',
        '最后供应商名称',
        '供应商款期',
        '不含税金额',
        '税额',
        '预计到货时间',
        '采购订单状态',
        "失败原因",
        '到货数量',
        '拒收数量',
        '是否预付',
        '订单类型',
    ];

    var colModelB = [{
        name: 'orgName',
        index: 'orgName',
    },
    {
        name: 'channelId',
        index: 'channelId',
        hidegrid: orderType == '1' ? true : false,
        hidden: orderType == '1' ? true : false,
    },
    {
        name: 'centralizedPurchaseName', //订单属性
        index: 'centralizedPurchaseName',
        hidegrid: orderType == '1' ? true : false,
        hidden: orderType == '1' ? true : false,
    },
    {
        name: 'planOrderNo',
        index: 'planOrderNo',
    },
    {
        name: 'supplierName',
        index: 'supplierName',
    },
    {
        name: 'orderNo',
        index: 'orderNo',
    },
            {
                name: 'createUserNew',
                index: 'createUserNew',
            },
    {
        name: 'createTime',
        index: 'createTime',
    },
    {
        name: 'createUser',
        index: 'createUser',
    },
    {
        name: 'skuCode',
        index: 'skuCode',
    },
    {
        name: 'top',
        index: 'top',
    },
    {
        name: 'isNew', //是否新品
        index: 'isNew',
    },
    {
        name: 'productCode',
        index: 'productCode',
    },
    {
        name: 'oldProductCode',
        index: 'oldProductCode',
    },
    {
        name: 'productName',
        index: 'productName', //商品名字
    },
    {
        name: 'commonName', //通用名
        index: 'commonName',
    },
    {
        name: 'productSpecification',
        index: 'productSpecification',
    },
    {
        name: 'productPackUnitSmall',
        index: 'productPackUnitSmall',
    },
    {
        name: 'productProduceFactory', //厂家
    },
    {
        name: 'productOriginPlace', //产地
        index: 'productOriginPlace',
    },
    {
        name: 'dosageForm', //剂型
        index: 'dosageForm',
    },
    {
        name: 'productPackCountSmall',
        index: 'productPackCountSmall', //小包装数量
    },
    {
        name: 'productContainTaxPrice', //含税单价
        index: 'productContainTaxPrice',
    },
    {
        name: 'validityDateStr', //是否近效期
        index: 'validityDateStr',
    },
    {
        name: 'productProduceDate', //生产日期
        index: 'productProduceDate',
        width: 180,
    }, {
        name: 'productValidityDate', //有效期至
        index: 'productValidityDate',
        width: 180,
    },
    {
        name: 'productRemark', //备注
        index: 'productRemark',
    },
    {
        name: 'productContainTaxMoney', //含税金额
        index: 'productContainTaxMoney',
    },
    {
        name: 'sevenDaysAvgPrice', //平台近7天成交均价
        index: 'sevenDaysAvgPrice',
        width: '200',
    },
    {
        name: 'sevenDaysFloorPrice', //全国近7天最低采购价
        index: 'sevenDaysFloorPrice',
        width: '200',
    },
    {
        name: 'sevenDaysFloorOrgCode', //全国近7天最低采购价所属机构
        index: 'sevenDaysFloorOrgCode',
        width: '230',
    },
    {
        name: 'globalLowestPrice', //近30天全国最低采购价 globalLowestPrice
        index: 'globalLowestPrice',
        width: '200',
    },
    {
        name: 'approveProfit', //审批销售毛利
        index: 'approveProfit',
    },
    {
        name:'thirtyDaysFinanceProfitRateStr', //近30天财务毛利率
        index:'thirtyDaysFinanceProfitRateStr',
        width: '180',
    },
    {
        name: 'approveDays', //审批周转天数
        index: 'approveDays',
    },
    {
        name: 'productAppContainTaxPrice', //App售价
        index: 'productAppContainTaxPrice',
    },
    {
        name: 'marginGrossProfit', //当前价差毛利
        type: 'marginGrossProfit',
        formatter: function (val, rowType, rowData) {
            //App售价
            let productAppContainTaxPrice = Number(
                rowData.productAppContainTaxPrice,
            );
            //含税单价
            let productContainTaxPrice = Number(rowData.productContainTaxPrice);
            if (productAppContainTaxPrice && productContainTaxPrice) {
                return (
                    Math.round(
                        (productAppContainTaxPrice - productContainTaxPrice) * 100,
                    ) / 100
                );
            } else {
                return 0;
            }
        },
    }, {
        name: 'chainGuidePrice', //连锁APP售价
        index: 'chainGuidePrice'
    },
    {
        name: 'productEntryTax', //税率
        index: 'productEntryTax',
    },
    {
        name: 'productPackUnitMedium',
        index: 'productPackUnitMedium',
    },
    {
        name: 'productPackUnitBig',
        index: 'productPackUnitBig',
    },
    {
        name: 'productBrandManufacturers', //品牌厂家
        index: 'productBrandManufacturers',
    },
    {
        name: 'productApprovalNumber', //商品批准文号
        index: 'productApprovalNumber',
    },
    {
        name: 'productApprovalNumberExpireDate', //商品批准文件有效日期
        index: 'productApprovalNumberExpireDate',
    },
    {
        name: 'ifOldApprovalNumberName', //是否过期
        index: 'ifOldApprovalNumberName',
    },
    {
        name: 'productLastPrice', //最后一次购进价
        index: 'productLastPrice',
    },
    {
        name: 'productStockCur', //库存数量
        index: 'productStockCur',
    },
    {
        name: 'canPinNumber', //当前周转天数
        index: 'canPinNumber',
    },
    {
        name: 'turnoverDefaultDays', //购进后周转天数
        type: 'turnoverDefaultDays',
    },
    {
        name: 'productSevenAverageSale', //7天日均销售
        index: 'productSevenAverageSale',
    },
    {
        name: 'productFifteenAverageSale', //15天日均销售
        index: 'productFifteenAverageSale',
    },
    {
        name: 'productThirtyAverageSale', //30天日均销售
        index: 'productThirtyAverageSale',
    },
    {
        name: 'salesQuantity', //大平台7天销量
        type: 'salesQuantity',
    },
    {
        name: 'productThirtySale', //全国近30天销量
        index: 'productThirtySale',
    },
    {
        name: 'productPackOnWayCount', //在途数量
        index: 'productPackOnWayCount',
    },
    {
        name: 'productUpperLimitStock', //库存上限
        index: 'productUpperLimitStock',
    },
    {
        name: 'productStockSafe', //安全库存
        index: 'productStockSafe',
    },
    {
        name: 'varietiesAgreementName', //1/0，1代表是协议品种；0代表非协议品种
        index: 'varietiesAgreementName',
    },
    {
        name: 'varietiesChannelName', //1/0，1代表是协议品种；0代表非协议品种
        index: 'varietiesChannelName',
    },
    {
        name: 'lastSupplierCode', //最后供应商编码
        index: 'lastSupplierCode', //最后供应商编码
    },
    {
        name: 'lastSupplierName', //最后供应商
        index: 'lastSupplierName', //最后供应商
    },
    {
        name: 'supplierPaymentPeriod', //供应商款期
        index: 'supplierPaymentPeriod',
    },
    {
        name: 'procutNoTaxMoney', //不含税金额
        index: 'procutNoTaxMoney',
    },
    {
        name: 'productTaxMoney', //税额
        index: 'productTaxMoney',
    },
    {
        name: 'expectDeliveryDate', //预计到货时间
        index: 'expectDeliveryDate',
    },
    {
        name: 'orderStatus', //采购订单状态
        index: 'orderStatus',
    },
    {
        name: "closeFailReason",
        index: "closeFailReason"
    },
    {
        name: 'productPackInStoreCount', //到货数量
        index: 'productPackInStoreCount',
    },
    {
        name: 'productPackRejectStoreCount', //拒收数量
        index: 'productPackRejectStoreCount',
    },
    {
        name: 'advancePayment', //是否预付
        index: 'advancePayment',
    },
    {
        name: 'orderTypeName', //订单类型
        index: 'orderTypeName',
    }
    ],
        allColModelB = JSON.parse(JSON.stringify(colModelB));

    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $('.nav-content');
        //window.curIndex = $this.index();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content
            .children('div')
            .eq($this.index())
            .css('display', 'flex')
            .siblings()
            .hide();
        $nav_content
            .children('div')
            .eq($this.index())
            .addClass('active')
            .siblings()
            .removeClass('active');
        //$("#btn_toggle").children("button").eq($this.index()).show().siblings().hide();
        var $btn_toggle = $('#btn_toggle');
        if (!$this.index()) {
            $btn_toggle.children('button.active1').show();
            $btn_toggle.children('button.active2').hide();
        } else {
            $btn_toggle.children('button.active2').show();
            $btn_toggle.children('button.active1').hide();
        }
    });

    $('#X_Tableb').XGrid({
        url: '/proxy-purchase/purchase/purchaseOrderProduct/loadPurchaseOrderProductData?' +
            $('#searchForm').serialize(),
        colNames: [
            '机构',
            '业务类型',
            '订单属性',
            '来源单号',
            '供应商名称',
            '采购订单编号',
            '创建人',
            '创建日期',
            '采购员',
            'SKU编码',
            'TOP排名',
            '<i>是否新品</i><i class="questa"></i>',
            '商品编号',
            '原商品编码',
            '商品名称',
            '通用名',
            '规格',
            '单位',
            '生产厂家',
            '产地',
            '剂型',
            '订单数量',
            '含税单价',
            '是否近效期',
            '生产日期',
            '有效期至',
            '<i>平台近7天成交均价</i><i class="questa"></i>',
            '全国近7天最低采购价',
            '全国近7天最低采购价所属机构',
            '近30天全国最低采购价',
            '<i>审批销售毛利</i><i class="questa"></i>',
            '<i>近30天财务毛利率</i><i class="questa"></i>',
            '<i>审批周转天数</i><i class="questa"></i>',
            '含税金额',
            ' APP售价',
            ' 当前价差毛利',
            '连锁APP售价',
            '税率',
            '中包装规格',
            '大包装规格',
            '品牌厂家',
            '商品批准文号',
            '批准文号有效日期',
            '是否过期',
            '最后一次购进价',
            '库存数量',
            '当前周转天数',
            '购进后周转天数',
            '7天日均销售',
            '15天日均销售',
            '30天日均销售',
            '大平台7天销量',
            '全国近30天销量',
            '在途数量',
            '库存上限',
            '安全库存',
            '是否协议品种',
            '是否协议内购进',
            '备注',
            '最后供应商编码',
            '最后供应商名称',
            '供应商款期',
            '不含税金额',
            '税额',
            '预计到货时间',
            '采购订单状态',
            "失败原因",
            '到货数量',
            '拒收数量',
            '是否预付',
            '是否赠品',
            '单行关闭状态',
            '供应商id',
            'ID',
            '订单类型',
        ],

        colModel: [{
            name: 'orgName',
            index: 'orgName',
        },
        {
            name: 'channelId',
            index: 'channelId',
            hidegrid: orderType == '1' ? true : false,
            hidden: orderType == '1' ? true : false,
        },
        {
            name: 'centralizedPurchaseName', //订单属性
            index: 'centralizedPurchaseName',
            hidegrid: orderType == '1' ? true : false,
            hidden: orderType == '1' ? true : false,
            // formatter: function (data) {
            //     if (data == "0") {
            //         return "否";
            //     } else if(data=='1') {
            //         return "是";
            //     }
            //     return "";
            // }
        },
        {
            name: 'planOrderNo',
            index: 'planOrderNo',
            formatter: planOrderNOformatter,
        },
        {
            name: 'supplierName',
            index: 'supplierName',
        },
        {
            name: 'orderNo',
            index: 'orderNo',
        },
            {
                name: 'createUserNew',    //订单创建人
                index: 'createUserNew',
            },
        {
            name: 'createTime',
            index: 'createTime',
        },
        {
            name: 'createUser',
            index: 'createUser',
        },
        {
            name: 'skuCode',
            index: 'skuCode',
        },
        {
            name: 'top',
            index: 'top',
        },
        {
            name: 'isNew',
            index: 'isNew',
            formatter: function (data) {
                if (parseInt(data) == "0") {
                    return "否";
                } else if (data == "1") {
                    return "是";
                }
                return data;
            }
        },
        {
            name: 'productCode',
            index: 'productCode',
        },
        {
            name: 'oldProductCode',
            index: 'oldProductCode',
        },
        {
            name: 'productName',
            index: 'productName', //商品名字
        },
        {
            name: 'commonName', //通用名
            index: 'commonName',
        },
        {
            name: 'productSpecification',
            index: 'productSpecification',
        },
        {
            name: 'productPackUnitSmall',
            index: 'productPackUnitSmall',
        },
        {
            name: 'productProduceFactory', //厂家
            index: 'productProduceFactory',
        },
        {
            name: 'productOriginPlace', //产地
            index: 'productOriginPlace',
        },
        {
            name: 'dosageForm', //剂型
            index: 'dosageForm',
        },
        {
            name: 'productPackCountSmall',
            index: 'productPackCountSmall', //小包装数量
        },
        {
            name: 'productContainTaxPrice', //含税单价
            index: 'productContainTaxPrice',
        },
        {
            name: 'validityDateStr', //是否近效期
            index: 'validityDateStr',
        },
        {
            name: 'productProduceDate', //生产日期
            index: 'productProduceDate',
            width: 180,
        }, {
            name: 'productValidityDate', //有效期至
            index: 'productValidityDate',
            width: 180,
        },
        {
            name: 'sevenDaysAvgPrice', //平台近7天成交均价
            index: 'sevenDaysAvgPrice',
            width: '200',
        },
        {
            name: 'sevenDaysFloorPrice', //全国近7天最低采购价
            index: 'sevenDaysFloorPrice',
            width: '200'
        },
        {
            name: 'sevenDaysFloorOrgCode', //全国近7天最低采购价所属机构
            index: 'sevenDaysFloorOrgCode',
            width: '230'
        },
        {
            name: 'globalLowestPrice', //近30天全国最低采购价
            index: 'globalLowestPrice',
            width: '200',
        },
        {
            name: 'approveProfit', //审批销售毛利
            index: 'approveProfit',
        },
        {
            name:'thirtyDaysFinanceProfitRateStr', //近30天财务毛利率
            index:'thirtyDaysFinanceProfitRateStr',
            width: '180',
        },
        {
            name: 'approveDays', //审批周转天数
            index: 'approveDays',
        },
        {
            name: 'productContainTaxMoney', //含税金额
            index: 'productContainTaxMoney',
        },
        {
            name: 'productAppContainTaxPrice', //App售价
            index: 'productAppContainTaxPrice',
        },
        {
            name: 'marginGrossProfit', //当前价差毛利
            type: 'marginGrossProfit',
            formatter: function (val, rowType, rowData) {
                //App售价
                let productAppContainTaxPrice = Number(
                    rowData.productAppContainTaxPrice,
                );
                //含税单价
                let productContainTaxPrice = Number(rowData.productContainTaxPrice);
                if (productAppContainTaxPrice && productContainTaxPrice) {
                    return (
                        Math.round(
                            (productAppContainTaxPrice - productContainTaxPrice) * 100,
                        ) / 100
                    );
                } else {
                    return 0;
                }
            },
        }, {
            name: 'chainGuidePrice', //连锁APP售价
            index: 'chainGuidePrice'
        },
        {
            name: 'productEntryTax', //税率
            index: 'productEntryTax',
        },
        {
            name: 'productPackUnitMedium',
            index: 'productPackUnitMedium',
        },
        {
            name: 'productPackUnitBig',
            index: 'productPackUnitBig',
        },
        {
            name: 'productBrandManufacturers', //品牌厂家
            index: 'productBrandManufacturers',
        },
        {
            name: 'productApprovalNumber', //商品批准文号
            index: 'productApprovalNumber',
        },
        {
            name: 'productApprovalNumberExpireDate', //商品批准文件有效日期
            index: 'productApprovalNumberExpireDate',
        },
        {
            name: 'ifOldApprovalNumberName', //是否过期
            index: 'ifOldApprovalNumberName',
        },
        {
            name: 'productLastPrice', //最后一次购进价
            index: 'productLastPrice',
            formatter: function (e) {
                return Number(e).toFixed(2);
            },
        },
        {
            name: 'productStockCur', //库存数量
            index: 'productStockCur',
            formatter: function (e) {
                if (e == '0') {
                    return '0.0';
                }
                return e;
            },
        },
        {
            name: 'canPinNumber', //'当前周转天数', ' 购进后周转天数'
            index: 'canPinNumber',
        },
        {
            name: 'turnoverDefaultDays', //'当前周转天数', ' 购进后周转天数'
            type: 'turnoverDefaultDays',
            // formatter: function (val,rowType,rowData) {
            //     //可用库存
            //     let productStockCur = Number(rowData.productStockCur);
            //     //订单数量
            //     let productPackCountSmall = Number(rowData.productPackCountSmall);
            //     //60日均天销量
            //     let productSixtySaleForDay = Number(rowData.sixtyAverageDailySales);
            //     if(productSixtySaleForDay>0){
            //         return  Math.round((productStockCur+productPackCountSmall) / productSixtySaleForDay);
            //     }else{
            //         return ''
            //     }
            //
            // }
        },
        {
            name: 'productSevenAverageSale', //7天日均销售
            index: 'productSevenAverageSale',
        },
        {
            name: 'productFifteenAverageSale', //15天日均销售
            index: 'productFifteenAverageSale',
        },
        {
            name: 'productThirtyAverageSale', //30天日均销售
            index: 'productThirtyAverageSale',
        },
        {
            name: 'salesQuantity', //大平台7天销量
            type: 'salesQuantity',
        },
        {
            name: 'productThirtySale', //全国近30天销量
            index: 'productThirtySale',
        },
        {
            name: 'productPackOnWayCount', //在途数量
            index: 'productPackOnWayCount',
        },
        {
            name: 'productUpperLimitStock', //库存上限
            index: 'productUpperLimitStock',
            formatter: function (e) {
                console.log(typeof e);
                if (isNaN(parseInt(e))) {
                    return 0;
                } else {
                    return e;
                }
            },
        },
        {
            name: 'productStockSafe', //安全库存
            index: 'productStockSafe',
        },
        {
            name: 'varietiesAgreementName', //1/0，1代表是协议品种；0代表非协议品种
            index: 'varietiesAgreementName',
        },
        {
            name: 'varietiesChannelName', //是否协议内购进
            type: 'varietiesChannelName',
        },
        {
            name: 'productRemark', //备注
            type: 'productRemark',
        },
        {
            name: 'lastSupplierCode', //最后供应商编码
            index: 'lastSupplierCode', //最后供应商编码
        },
        {
            name: 'lastSupplierName', //最后供应商
            index: 'lastSupplierName', //最后供应商
        },
        {
            name: 'supplierPaymentPeriod', //供应商款期
            index: 'supplierPaymentPeriod',
        },
        {
            name: 'procutNoTaxMoney', //不含税金额
            index: 'procutNoTaxMoney',
        },
        {
            name: 'productTaxMoney', //税额
            index: 'productTaxMoney',
        },
        {
            name: 'expectDeliveryDate', //预计到货时间
            index: 'expectDeliveryDate',
        },
        {
            name: 'orderStatus', //状态
            index: 'orderStatus',
        },
        {
            name: 'closeFailReason', //状态
            index: 'closeFailReason',
        },
        {
            name: 'productPackInStoreCount', //到货数量
            index: 'productPackInStoreCount',
        },
        {
            name: 'productPackRejectStoreCount', //拒收数量
            index: 'productPackRejectStoreCount',
        },
        {
            name: 'advancePayment', //是否预付
            index: 'advancePayment',
        },
        {
            name: 'giftFlag', //是否赠品
            index: 'giftFlag',
            formatter: function (data) {
                if (data == '1') {
                    return '赠品';
                } else if (data == '0') {
                    return '商品';
                }
                return '';
            },
        },
        {
            name: 'orderCloseStatusName', //单行关闭状态
            index: 'orderCloseStatusName',
        },
        {
            name: 'supplierBaseId',
            index: 'supplierBaseId',
            hidden: true,
            hidegrid: true,
        },
        {
            name: 'id',
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true, //是否隐藏
            hidegrid: true,
        },
        {
            name: 'orderTypeName', //订单类型
            index: 'orderTypeName',
        },
            /* {
                name: 'hasPermission', //付款状态1
                index: 'hasPermission',
                hidden: true, //是否隐藏列
            }, */
        ],
        rowNum: 20,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid-pager-b', //设置翻页所在html元素名称
        selectandorder: true,
        rowList: [20, 50, 100],
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件', obj);
            this.returnValue = obj;
            // console.log(obj)
            // window.location.href = "/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNO=" + obj.orderNo+"&supplierBaseId="+"supplierBaseId";
            var orderNo = obj.orderNo;
            // if(obj.planOrderNo){
            //     orderNo = obj.planOrderNo.match(/<a[^>]*>(.*?)<\/a>/i)[1]
            // }
            utils.openTabs(
                'purchaseOrderDetail',
                '采购订单详情',
                '/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNo=' +
                orderNo +
                '&type=1',
            );
            return obj;
        },
        onSelectRow: function (id, dom, obj, index, event) {

            // 如果没有权限将更改到货时间按钮置灰。
            var $ele = $(event.target);
            var rowDt = $('#X_Tableb').XGrid('getSeleRow');
            let hasNoPerssion = rowDt.find(item => {
                return item.hasPermission === 'false'
            })
            if (hasNoPerssion) {
                $('#reviseArriveDate').attr('disabled', true)
                $('#uploadContractBtn').attr('disabled', true)
                $('#makeContract').attr('disabled', true)
                $('#closeLineData').attr('disabled', true)
                $('#closeRowData').attr('disabled', true)
                $('#deleteContractBtn').attr('disabled', true)
                $('#deleteRowData').attr('disabled', true)

            } else {
                $('#reviseArriveDate').attr('disabled', false)
                $('#uploadContractBtn').attr('disabled', false)
                $('#makeContract').attr('disabled', false)
                $('#closeLineData').attr('disabled', false)
                $('#closeRowData').attr('disabled', false)
                $('#deleteContractBtn').attr('disabled', false)
                $('#deleteRowData').attr('disabled', false)
            }
            if ($ele.attr("class") == "to_apply") {
                var data = $ele.attr("url");
                utils.openTabs(data, "采购计划单详情", data)
            }
        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = [
                'productContainTaxMoney',
                'procutNoTaxMoney',
                'productTaxMoney',
            ];
            var lastRowEle = $(this).find('tr[nosele=true]');
            lastRowEle.find('td:first-child').text('合计');
            sum_models.forEach(function (item, index) {
                lastRowEle
                    .find('td[row-describedby=' + item + ']')
                    .text(totalTable(data, item));
            });
            //初始化table头部hover提示
            setTimeout(() => {
                getTipText();
            }, 100);
        },
    });

    function getTipText() {
        $.ajax({
            type: 'GET',
            url: '/proxy-purchase/purchase/purchaseOrder/getValidityDateMsg',
            success: function (response) {
                let { result, code, msg } = response;
                if (code === 0) {
                    initQuesta(result);
                }
            },
        });
    }
    //初始化table头部hover提示
    function initQuesta(result) {
        var string = result;

        const questaOption = [{
            th: 'approveProfit', //审批销售毛利
            title: `销售毛利=(平台近7天成交均价-购进价)/平台近7天成交均价*100%；平台近7天成交均价包含自营和POP的业务`,
            width: 460,
            height: 80,
        },
        {
            th: 'thirtyDaysFinanceProfitRateStr', //近30天财务毛利率
            title: `为不含税毛利数据，近30天财务毛利率 (不含税) =财务毛利额 (不含税)/收入不含税`,
            width: 520,
            height: 80,
        },
        {
            th: 'approveDays', //审批周转天数
            title: `审批周转天数= (现有库存数量+10天内的商品编码维度在途订单数量+采购数量) /近30天净日均销量<br/><span style="color:red"></span>；如果是集团，则购进后可销天数= (现有库存数量+10天内的商品编码维度在途订单数量+采购数量)/全国近30天净日均销量；`,
            width: 460,
            height: 102,
        },
        {
            th: 'isNew', //是否新品
            title: `前60天不在线的品种`,
            width: 460,
            height: 80,
        },
        {
            th: 'sevenDaysAvgPrice', //平台近7天成交均价
            title: `商品维度数据，包含自营业务和POP业务，T-1指标数据`,
            width: 460,
            height: 80,
        },
        ];
        eachTipView(questaOption);
    }

    function eachTipView(arr) {
        $.each(arr, function (index, item) {
            $('.XGridHead').delegate('th[row-describedby=' + item.th + '] .questa', {
                mouseover: function (e) {
                    $('body').append(`
                        <div id='div_tooltips'>
                            <style>
                                #div_tooltips:after{
                                    content: "";
                                    width: 0;
                                    height: 0;
                                    position: absolute;
                                    left: ${item.width / 2 - 10}px;
                                    bottom: -10px;
                                    border-left: solid 10px transparent;
                                    border-top: solid 10px white;
                                    border-right: solid 10px transparent;
                                }
                            </style>
                            <div id='inner_tooltips'>${item.title}</div>
                        </div>
                    `);
                    $('#div_tooltips')
                        .css({
                            boxSizing: 'border-box',
                            width: item.width + 'px',
                            height: item.height + 'px',
                            padding: '10px',
                            zIndex: 9999,
                            backgroundColor: '#ffffff',
                            border: '1px solid #c4c4c4',
                            position: 'absolute',
                            top: $(e.target).offset().top - item.height - 10 + 'px',
                            left: $(e.target).offset().left + 5 - item.width / 2 + 'px',
                        })
                        .show('fast');
                },
                mouseout: function () {
                    $('#div_tooltips').remove();
                },
                click: function () { },
            });
        });
    }
    //采购订单数据
    var X_Tablea_colNames = [],
        X_Tablea_colModel = [];

    var sysOrgCode = $('#sysOrgCode').val();

    if ($('#isNewBaicao').val() == 'true') {
        (X_Tablea_colNames = [
            '机构',
            '业务类型',
            '订单属性',
            '来源单号',
            '采购订单编号',
            '创建人',
            '供应商编号',
            '供应商名称',
            '支付方式',
            '送货方式',
            '送货人',
            '送货人电话',
            '含税金额',
            '不含税金额',
            '税额',
            '采购订单状态',
            '在途时长',
            '预计到货时间',
            '是否预付',
            '全量交付',
            '付款状态',
            '创建日期',
            '结算方式',
            '供应商仓库地址',
            '运输工具',
            '关闭状态',
            '失败原因',
            '备注',
            '来源',
            'ID',
            '订单合同状态',
            '合同附件',
            '归档意见',
            '订单类型',
        ]),
            (X_Tablea_colModel = [{
                name: 'orgName',
                index: 'orgName',
            },
            {
                name: 'channelId',
                index: 'channelId',
                hidegrid: orderType == '1' ? true : false,
                hidden: orderType == '1' ? true : false,
            },
            {
                name: 'centralizedPurchaseName', //订单属性
                index: 'centralizedPurchaseName',
                hidegrid: orderType == '1' ? true : false,
                hidden: orderType == '1' ? true : false,
            },
            {
                name: 'planOrderNo', //计划单号
                index: 'planOrderNo',
                formatter: planOrderNOformatter,
            },
            {
                name: 'orderNo',
                index: 'orderNo',
            },
            {
                name: 'createUserNew',   //订单创建人
                index: 'createUserNew',
            },
            {
                name: 'supplierCode',
                index: 'supplierCode',
            },
            {
                name: 'supplierName',
                index: 'supplierName', //
            },
            {
                name: 'paymentMethod', //支付方式
                index: 'paymentMethod',
                // hidegrid: true ,
                hidden: true,
            },
            {
                name: 'deliveryMethod', //送货方式1
                index: 'deliveryMethod',
            },
            {
                name: 'supplyBussinessUserName', //送货人
                index: 'supplyBussinessUserName',
                //   hidegrid: true ,
                hidden: true,
            },
            {
                name: 'supplyBussinessUserMobile', //送货人电话
                index: 'supplyBussinessUserMobile',
                // hidegrid: true ,
                hidden: true,
            },
            {
                name: 'orderPriceTaxSum', //含税金额1
                index: 'orderPriceTaxSum',
            },
            {
                name: 'orderSumNoTaxMoney',
                index: 'orderSumNoTaxMoney',
            },
            {
                name: 'orderSumTaxMoney',
                index: 'orderSumTaxMoney',
            },
            {
                name: 'orderStatus', //状态1
                index: 'orderStatus',
                formatter: orderStatusFormatter,
            },
            {
                name: 'onWayDays', //在途时长
                index: 'onWayDays',
            },
            {
                name: 'expectDeliveryDate', //预计到货时间1
                index: 'expectDeliveryDate',
            },
            {
                name: 'advancePayment', //1
                index: 'advancePayment',
            },
            {
                name: 'fullDelivery', //全量交付1
                index: 'fullDelivery',
            },
            {
                name: 'paymentStatus', //付款状态1
                index: 'paymentStatus',
                hidden: true, //是否隐藏列
                hidegrid: true,
            },
            {
                name: 'createDateFormat', //创建日期
                index: 'createDateFormat',
            },
            {
                name: 'settleMethod', //结算方式
                index: 'settleMethod',
                // hidegrid: true ,
                hidden: true,
            },
            {
                name: 'supplyStoreAddress', //供应商仓库地址
                index: 'supplyStoreAddress',
                //  hidegrid: true ,
                hidden: true,
            },
            {
                name: 'supplyTransMethod', //运输工具
                index: 'supplyTransMethod',
                // hidegrid: true ,
            },
            {
                name: 'orderCloseStatusName', //订单关闭状态
                index: 'orderCloseStatusName',
            },
            {
                name: "closeFailReason",
                index: "closeFailReason"
            },
            {
                name: 'supplyRemark', //1
                index: 'supplyRemark',
            },
            {
                name: 'orderSourceName', //来源
                index: 'orderSourceName',
            },
            {
                name: 'id',
                index: 'id', //索引。其和后台交互的参数为sidx
                key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden: true, //是否隐藏列
                hidegrid: true,
            },
            {
                name: 'contractStatus', //订单合同状态
                index: 'contractStatus',
            },
            {
                name: 'contractAccessoryName', //合同附件
                index: 'contractAccessoryName',
            },
            {
                name: 'contractRemark', //归档意见
                index: 'contractRemark',
            },
            {
                name: 'orderTypeName', //订单类型
                index: 'orderTypeName',
            }
                /*  {
                     name: 'saleOrderProcessId', // 调拨销售单的审批流id
                     index: 'saleOrderProcessId',
                     hidden: true, //是否隐藏列
                 },
                 {
                     name: 'hasPermission', //付款状态1
                     index: 'hasPermission',
                     hidden: true, //是否隐藏列
                 }, */
            ]);
    } else {
        (X_Tablea_colNames = [
            '机构',
            '业务类型',
            '订单属性',
            '来源单号',
            '采购订单编号',
            '创建人',
            '供应商编号',
            '供应商名称',
            '支付方式',
            '送货方式',
            '送货人',
            '送货人电话',
            '含税金额',
            '不含税金额',
            '税额',
            '采购订单状态',
            '在途时长',
            '预计到货时间',
            '是否预付',
            '全量交付',
            '付款状态',
            '创建日期',
            '结算方式',
            '供应商仓库地址',
            '运输工具',
            '关闭状态',
            '失败原因',
            '备注',
            '来源',
            'ID',
            '是否特殊药品',
            '订单合同状态',
            '合同附件',
            '归档意见',
            '订单类型',
        ]),
            (X_Tablea_colModel = [{
                name: 'orgName',
                index: 'orgName',
            },
            {
                name: 'channelId',
                index: 'channelId',
                hidegrid: orderType == '1' ? true : false,
                hidden: orderType == '1' ? true : false,
            },
            {
                name: 'centralizedPurchaseName', //订单属性
                index: 'centralizedPurchaseName',
                hidegrid: orderType == '1' ? true : false,
                hidden: orderType == '1' ? true : false,
            },
            {
                name: 'planOrderNo', //计划单号
                index: 'planOrderNo',
            },
            {
                name: 'orderNo',
                index: 'orderNo',
            },
            {
                name: 'createUserNew',  //订单创建人
                index: 'createUserNew',
            },
            {
                name: 'supplierCode',
                index: 'supplierCode',
            },
            {
                name: 'supplierName',
                index: 'supplierName', //
            },
            {
                name: 'paymentMethod', //支付方式
                index: 'paymentMethod',
                // hidegrid: true ,
                hidden: true,
            },
            {
                name: 'deliveryMethod', //送货方式1
                index: 'deliveryMethod',
            },
            {
                name: 'supplyBussinessUserName', //送货人
                index: 'supplyBussinessUserName',
                //   hidegrid: true ,
                hidden: true,
            },
            {
                name: 'supplyBussinessUserMobile', //送货人电话
                index: 'supplyBussinessUserMobile',
                // hidegrid: true ,
                hidden: true,
            },
            {
                name: 'orderPriceTaxSum', //含税金额1
                index: 'orderPriceTaxSum',
            },
            {
                name: 'orderSumNoTaxMoney',
                index: 'orderSumNoTaxMoney',
            },
            {
                name: 'orderSumTaxMoney',
                index: 'orderSumTaxMoney',
            },
            {
                name: 'orderStatus', //状态1
                index: 'orderStatus',
                formatter: orderStatusFormatter,
            },
            {
                name: 'onWayDays', //在途时长
                index: 'onWayDays',
            },
            {
                name: 'expectDeliveryDate', //预计到货时间1
                index: 'expectDeliveryDate',
            },
            {
                name: 'advancePayment', //1
                index: 'advancePayment',
            },
            {
                name: 'fullDelivery', //全量交付1
                index: 'fullDelivery',
            },
            {
                name: 'paymentStatus', //付款状态1
                index: 'paymentStatus',
                hidden: true, //是否隐藏列
                hidegrid: true,
            },
            {
                name: 'createDateFormat', //创建日期
                index: 'createDateFormat',
            },
            {
                name: 'settleMethod', //结算方式
                index: 'settleMethod',
                // hidegrid: true ,
                hidden: true,
            },
            {
                name: 'supplyStoreAddress', //供应商仓库地址
                index: 'supplyStoreAddress',
                //  hidegrid: true ,
                hidden: true,
            },
            {
                name: 'supplyTransMethod', //运输工具
                index: 'supplyTransMethod',
                // hidegrid: true ,
            },
            {
                name: 'orderCloseStatusName', //订单关闭状态
                index: 'orderCloseStatusName',
            },
            {
                name: 'closeFailReason', //关闭原因
                index: 'closeFailReason',
            },
            {
                name: 'supplyRemark', //1
                index: 'supplyRemark',
            },
            {
                name: 'orderSourceName', //来源
                index: 'orderSourceName',
            },
            {
                name: 'id',
                index: 'id', //索引。其和后台交互的参数为sidx
                key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden: true, //是否隐藏列
                hidegrid: true,
            },
            {
                name: 'ifSpecialDrugsName', //是否特殊药品
                index: 'ifSpecialDrugsName',
            },
            {
                name: 'contractStatus', //订单合同状态
                index: 'contractStatus',
            },
            {
                name: 'contractAccessoryName', //合同附件
                index: 'contractAccessoryName',
            },
            {
                name: 'contractRemark', //归档意见
                index: 'contractRemark',
            },
            {
                name: 'orderTypeName', //订单类型
                index: 'orderTypeName',
            }
                /*  {
                     name: 'saleOrderProcessId', // 调拨销售单的审批流id
                     index: 'saleOrderProcessId',
                     hidden: true, //是否隐藏列
                 },
                 {
                     name: 'hasPermission', //付款状态1
                     index: 'hasPermission',
                     hidden: true, //是否隐藏列
                 }, */
            ]);
    }
    var allColModelA = JSON.parse(JSON.stringify(X_Tablea_colModel));

    $('#X_Tablea').XGrid({
        url: '/proxy-purchase/purchase/purchaseOrder/loadPurchaseOrderData?' +
            $('#searchForm').serialize(),
        colNames: X_Tablea_colNames,
        colModel: X_Tablea_colModel,
        rowNum: 20,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid-pager-a', //设置翻页所在html元素名称
        selectandorder: true,
        rowList: [20, 50, 100],
        attachRow: true,
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件订单', obj);
            utils.openTabs(
                'purchaseOrderDetail',
                '采购订单详情',
                '/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseId=' +
                obj.id +
                '&type=1&orderType=' +
                orderType +
                '&saleOrderProcessId=' +
                obj.saleOrderProcessId, {},
                function () {
                    $('#searchBtn').trigger('click');
                },
            );
        },
        onSelectRow: function (id, dom, obj, index, event) {

            // 如果没有权限将更改到货时间按钮置灰。
            var $ele = $(event.target);
            var rowDt = $('#X_Tablea').XGrid('getSeleRow');
            let hasNoPerssion = rowDt.find(item => {
                return item.hasPermission === 'false'
            })
            if (hasNoPerssion) {
                $('#reviseArriveDate').attr('disabled', true)
                $('#uploadContractBtn').attr('disabled', true)
                $('#makeContract').attr('disabled', true)
                $('#closeLineData').attr('disabled', true)
                $('#closeRowData').attr('disabled', true)
                $('#deleteContractBtn').attr('disabled', true)


            } else {
                $('#reviseArriveDate').attr('disabled', false)
                $('#uploadContractBtn').attr('disabled', false)
                $('#makeContract').attr('disabled', false)
                $('#closeLineData').attr('disabled', false)
                $('#closeRowData').attr('disabled', false)
                $('#deleteContractBtn').attr('disabled', false)
            }
            if ($ele.attr("class") == "to_apply") {
                var data = $ele.attr("url");
                utils.openTabs(data, "采购计划单详情", data)
            }
        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = [
                'orderPriceTaxSum',
                'orderSumNoTaxMoney',
                'orderSumTaxMoney',
            ];
            var lastRowEle = $(this).find('tr[nosele=true]');
            lastRowEle.find('td:first-child').text('合计');
            sum_models.forEach(function (item, index) {
                lastRowEle
                    .find('td[row-describedby=' + item + ']')
                    .text(totalTable(data, item));
            });
        },
    });
    //添加计划单超链接
    function planOrderNOformatter(e, row, data) {
        console.log(e)
        if (e != null && e.indexOf('JGJHD') != -1) {
            return '<a href="javascript:;"  url= "/#/supply/purchasePlan/purchasePlanDetail?orderPlanNo=' + e + '"class="to_apply">' + e + '</a>';
        } else {
            return e;
        };
    }
    //订单状态
    function orderStatusFormatter(cellvalue) {
        var reject = $("#orderStatus option[value='" + cellvalue + "']").html();
        if (reject != undefined && reject != '') {
            return reject;
        } else {
            return cellvalue;
        }
    }

    function paymentStatus(cellV) {
        if (cellV == 0) {
            return '未付款';
        } else if (cellV == 1) {
            return '部分付款';
        } else if (cellV == 2) {
            return '已付款';
        }
    }

    function advancePayment(cellT) {
        if (cellT == 0) {
            return '否';
        } else {
            return '是';
        }
    }
    //supplyTransMethodFormatter 送货方式
    function supplyTransMethodFormatter(cellvalue) {
        if (cellvalue == 0) {
            return '送货上门';
        } else if (cellvalue == 1) {
            return '公司配送';
        } else if (cellvalue == 2) {
            return '客户自提';
        } else if (cellvalue == 3) {
            return '委托运输';
        } else if (cellvalue == 4) {
            return '公司自提';
        }
    }
    //是否预付
    function advancePaymentFormatter(cellvalue) {
        var reject = $("#advancePayment option[value='" + cellvalue + "']").html();
        if (reject != undefined && reject != '') {
            return reject;
        } else {
            return cellvalue;
        }
    }

    function getQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    //判断选否选择的无限制
    $("#onWayDaysOperatorEnum").change(function () {
        if ($(this).val() == "UN_LIMIT") {
            $("#onWayDays").val("");
        };
    });
    //当采购订单状态选择在途时
    $("#orderStatus").change(function () {
        //在途
        if ($(this).val() == "2") {
            $("#orderStatus-wrap").show();
            $("#onWayDaysOperatorEnum").attr('disabled', false);
            $("#onWayDays").attr("disabled", false);
        } else {
            $("#orderStatus-wrap").hide();
            $("#onWayDaysOperatorEnum").attr('disabled', true);
            $("#onWayDays").attr("disabled", true);
        }
    })
    //查询数据，重置data
    $('#searchBtn').on('click', function () {
        const warningOrClose = getQueryString('warningOrClose');

        var cs = $('#toggle_wrap .active').attr('id');
        var searchData = $('#searchForm').serializeToJSON();

        if (warningOrClose) {
            searchData.warningOrClose = warningOrClose;
        }
        // var channelId=$("select[name='channelId']").val();
        var channelId = $('#channelId').val();
        searchData.channelId = channelId;
        //订单类型
        var itemType = $('#itemType').val();
        var orderType = $('#orderType').val();
        if (orderType != 1) {
            $('#orderType').val(itemType);
        }

        //判断在途时长是否填写了时间
        if ($("#onWayDaysOperatorEnum").val() != "UN_LIMIT") {
            var checkInt = /^[0-9]\d*$/;
            var waydays = $("#onWayDays").val();
            var checked = true;
            if ($.trim(waydays) == "") {
                checked = false;
            }
            if (!checkInt.test(waydays)) {
                checked = false;
            }
            if (!checked) {
                utils
                    .dialog({
                        content: '在途时长，请输入有效数字',
                        quickClose: false,
                        timeout: 2000,
                    })
                    .showModal();
                return;
            }
        }

        if (cs == 'orderList') {
            $('#X_Tablea')
                .setGridParam({
                    url: '/proxy-purchase/purchase/purchaseOrder/loadPurchaseOrderData?' +
                        $('#searchForm').serialize(),
                    postData: {
                        purchaseOrderProductVoStr: JSON.stringify(searchData),
                    },
                })
                .trigger('reloadGrid');
        } else if (cs == 'productList') {
            //productList
            //商品条件查询数据，重置data
            $('#X_Tableb')
                .setGridParam({
                    url: '/proxy-purchase/purchase/purchaseOrderProduct/loadPurchaseOrderProductData?' +
                        $('#searchForm').serialize(),
                    postData: { a: 19 },
                })
                .trigger('reloadGrid');
        } else {
            return;
        }
    });

    // var prevUploadFileName = "";//上一次上传的文件名
    $('#btnUpload').on('change', function () {
        $('#filePath').val($(this).val());
        var fileName = this.files[0].name;
        var fileType = fileName
            .substring(fileName.lastIndexOf('.') + 1)
            .toLocaleLowerCase();
        if (imageType.indexOf(fileType) == -1) {
            utils
                .dialog({
                    content: '请上传正确格式的文件！如压缩文件以及pdf。',
                    quickClose: false,
                    timeout: 2000,
                })
                .showModal();
            $('#filePath').val('');
            return false;
        }
        var maxsize = 20 * 1024 * 1024; //5M
        var size = this.files[0].size; //图片大小
        if (size > maxsize) {
            utils
                .dialog({
                    content: '文件过大，文件大小不能超过20M',
                    quickClose: false,
                    timeout: 2000,
                })
                .showModal();
            $('#filePath').val('');
            $('#btnUpload, #filePath').val('');
            return false;
        }
        //是否选择了文件
        if (!fileName) {
            utils
                .dialog({
                    title: '提示',
                    width: 200,
                    content: '请选择文件',
                    okValue: '确定',
                    ok: function () { },
                })
                .showModal();
            return false;
        }
    });

    $('#time_range').on('change', function () {
        $('#begint').removeAttr('disabled');
        $('#endt').removeAttr('disabled');
        $('#begint').val('');
        $('#endt').val('');
        var ov = $('#time_range option:selected').val();
        if (ov == 'create') {
            $('#begint').attr('name', ov + 'TimeStart');
            $('#endt').attr('name', ov + 'TimeEnd');
        } else if (ov == 'expectDeliveryDate') {
            $('#begint').attr('name', ov + 'Start');
            $('#endt').attr('name', ov + 'End');
        } else if (ov == 'nodata') {
            $('#begint').attr('name', '');
            $('#endt').attr('name', '');
            $('#begint').attr('disabled', 'disabled');
            $('#endt').attr('disabled', 'disabled');
        }
    });

    //导出
    $('#exportRowData').on('click', function () {
        console.log('导出');
        var cs = $('#toggle_wrap .active').attr('id');
        if (cs == 'orderList') {
            utils
                .exportAstrictHandle(
                    'X_Tablea',
                    Number($('#grid-pager-a #totalPageNum').text()),
                )
                .then(() => {
                    return false;
                })
                .catch(() => {
                    //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                    var ck = false;
                    // copy this parameter and the below buttons
                    var rowDt = $('#X_Tablea').XGrid('getSeleRow');

                    var nameModel = '';
                    let column = X_Tablea_colNames.filter(function (item) {
                        // return item != 'ID' && (orderType == '1' ? item != '业务类型' : true)
                        return item != 'ID';
                    })
                    column.push('自动关单时间');
                    // 去掉id项
                    addHtmlA(
                        column
                    );

                    //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                    var ck = false;
                    // copy this parameter and the below buttons

                    dialog({
                        content: $('#setCol'),
                        title: '筛选列',
                        width: 706,
                        data: 'val值',
                        cancelValue: '取消',
                        cancel: true,
                        okValue: '导出',
                        ok: function () {
                            var newColName = [],
                                newColModel = [];

                            let allColModelACopy = allColModelA.filter((item) => {
                                return orderType == '1' ?
                                    item['name'] != 'channelName' :
                                    true;
                            });

                            //去掉saleOrderProcessId， 去掉是否有操作权限
                            allColModelACopy = allColModelACopy.filter(item => {
                                return item['name'] !== 'saleOrderProcessId' || item['name'] !== 'hasPermission'
                            })
                            allColModelACopy.push({ name: 'predictAutoCloseDate', index: 'predictAutoCloseDate' })
                            $(this.node)
                                .find('#checkRow input[type="checkbox"]')
                                .each(function (index) {

                                    // 去掉id项 上下同步
                                    var filterModel = allColModelACopy.filter(function (item) {
                                        return item.name != 'id';
                                    });
                                    if ($(this).is(':checked')) {
                                        nameModel +=
                                            filterModel[index].name +
                                            ':' +
                                            $(this).attr('name') +
                                            ',';
                                    }
                                });
                            //console.log(nameModel.length)
                            if (nameModel.length == 0) {
                                utils
                                    .dialog({
                                        content: '请选择后导出',
                                        quickClose: true,
                                        timeout: 2000,
                                    })
                                    .showModal();
                                return false;
                            }

                            var obj = $('#searchForm').serializeToJSON();
                            var selOrder = $('#X_Tablea').XGrid('getSeleRow');

                            var selectIdOrder = [];
                            obj['pageNum'] = '1';
                            obj['pageSize'] = '200';
                            obj['nameModel'] = nameModel;
                            obj['purchaseOrderProductVoStr'] = JSON.stringify(
                                $('#searchForm').serializeToJSON(),
                            );
                            if (selOrder == undefined || selOrder == null) { } else if (selOrder.length == undefined) {
                                selectIdOrder = selOrder.id;
                            } else {
                                $.each(selOrder, function (i, obj) {
                                    selectIdOrder.push(obj.id);
                                });
                            }

                            obj['selectIdOrder'] = selectIdOrder;
                            httpPost('/proxy-purchase/purchase/purchaseOrder/exportPurchaseOrderData', obj);
                        },
                        // copy button to other dialogues
                        button: [{
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if (!ck) {
                                    $('#checkRow input').prop('checked', false);
                                    ck = true;
                                } else if (ck) {
                                    $('#checkRow input').prop('checked', 'checked');
                                    ck = false;
                                } else {
                                    return false;
                                }
                                return false;
                            },
                        },],
                        //copy ends here
                    }).showModal();
                });
        } else if (cs == 'productList') {
            //productList
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            utils
                .exportAstrictHandle(
                    'X_Tableb',
                    Number($('#grid-pager-b #totalPageNum').text()),
                )
                .then(() => {
                    return false;
                })
                .catch(() => {
                    var ck = false;
                    // copy this parameter and the below buttons
                    var rowDt = $('#X_Tableb').XGrid('getSeleRow');
                    var nameModel = '';
                    //orderType == '1' ? colNameB.shift(0) : null;
                    addHtmlB(colNameB);
                    dialog({
                        content: $('#setColB'),
                        title: '筛选列',
                        width: 806,
                        data: 'val值',
                        cancelValue: '取消',
                        cancel: true,
                        okValue: '导出',
                        ok: function () {
                            var newColName = [],
                                newColModel = [];
                            allColModelB = allColModelB.filter((item) => {
                                return orderType == '1' ? item['name'] != 'channelName' : true;
                            });
                            $(this.node)
                                .find('#checkRow input[type="checkbox"]')
                                .each(function (index) {
                                    if ($(this).is(':checked')) {
                                        nameModel +=
                                            allColModelB[index].name +
                                            ':' +
                                            $(this).attr('name') +
                                            ',';
                                    }
                                });
                            if (nameModel.length == 0) {
                                utils
                                    .dialog({
                                        content: '请选择后导出',
                                        quickClose: true,
                                        timeout: 2000,
                                    })
                                    .showModal();
                                return false;
                            }
                            var obj = $('#searchForm').serializeToJSON();
                            obj['pageNum'] = '1';
                            obj['pageSize'] = '200';
                            obj['nameModel'] = nameModel;

                            var selProduct = $('#X_Tableb').XGrid('getSeleRow');

                            var selectIdProduct = [];
                            if (selProduct == undefined || selProduct == null) { } else if (selProduct.length == undefined) {
                                selectIdProduct = selProduct.id;
                            } else {
                                $.each(selProduct, function (i, obj) {
                                    console.log(obj.id);
                                    selectIdProduct.push(obj.id);
                                });
                            }

                            obj['selectIdProduct'] = selectIdProduct;
                            console.log('444444444', obj);
                            //debugger
                            httpPost(
                                '/proxy-purchase/purchase/purchaseOrderProduct/exportPurchaseOrderProductData',
                                obj,
                            );
                        },
                        // copy button to other dialogues
                        button: [{
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if (!ck) {
                                    $('#checkRow input').prop('checked', false);
                                    ck = true;
                                } else if (ck) {
                                    $('#checkRow input').prop('checked', 'checked');
                                    ck = false;
                                } else {
                                    return false;
                                }
                                return false;
                            },
                        },],
                        //copy ends here
                    }).showModal();
                });
        } else {
            return;
        }
    });
    //todo
    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s =
                '<div id="setCol" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {
                s +=
                    '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' +
                    arry[i] +
                    '">' +
                    arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';
            }

            s += '</div></div>';
            $('body').append(s);
        }
    }
    //筛选列
    function addHtmlB(arry) {
        if (!$('#setColB')[0]) {
            var s =
                '<div id="setColB" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {
                s +=
                    '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' +
                    arry[i] +
                    '">' +
                    arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';
            }

            s += '</div></div>';
            $('body').append(s);
        }
    }
    //筛选列a
    $('#set_tables_rowa').click(function () {
        $('#X_Tablea').XGrid('filterTableHead');
    });
    //筛选列b
    $('#set_tables_rowb').click(function () {
        $('#X_Tableb').XGrid('filterTableHead');
    });
    //近效期计算器
    $('#dateComputed').click(function () {
        utils.timeDialog();
    });
    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement('form');
        temp.action = URL;
        temp.method = 'post';
        temp.style.display = 'none';

        for (var x in PARAMS) {
            var opt = document.createElement('textarea');
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    //更改到货时间
    $('#reviseArriveDate').click(function () {
        $("input[name='newExpectDeliveryDate'").val('');
        $('#remarksf').val('');
        var cs = $('#toggle_wrap .active').attr('id');
        if (cs == 'orderList') {
            $("input[name='newtime']").val('');
            var vl = document.querySelector('#change_arrivetime');
            var rowDt = $('#X_Tablea').XGrid('getSeleRow');
            if (rowDt == undefined || rowDt == null) {
                utils
                    .dialog({
                        content: '请选中要更改到货日期的记录！',
                        quickClose: true,
                        timeout: 2000,
                    })
                    .showModal();
                return false;
            }
            var selectorderNo = '';
            if (rowDt == undefined || rowDt == null) { } else if (rowDt.length == undefined) {
                selectorderNo = rowDt.orderNo;
            } else {
                $.each(rowDt, function (i, obj) {
                    selectorderNo = selectorderNo + obj.orderNo + ',';
                });
            }
            utils.ajax({ selectorderNo: selectorderNo },
                '/proxy-purchase/purchase/purchaseOrderDeliveryDateChange/getInsertByBatchCount',
                'POST',
                successCallbackOne,
            );

            function successCallbackOne(msgOne) {
                if (msgOne == '0') {
                    utils
                        .dialog({
                            content: '只有在途的订单可以修改到货时间！',
                            quickClose: true,
                            timeout: 8000,
                        })
                        .showModal();
                } else {
                    utils
                        .dialog({
                            title: '更改到货时间',
                            content: vl,
                            okValue: '确定',
                            ok: function () {
                                var newtimes = $(
                                    "#change_arrivetime input[name='newExpectDeliveryDate']",
                                ).val();
                                if (!newtimes) {
                                    utils
                                        .dialog({
                                            content: '交货时间不能为空！',
                                            quickClose: true,
                                            timeout: 30000,
                                        })
                                        .showModal();
                                    return false;
                                }
                                //发送请求
                                var ttx = $('#changeTimeData').serializeToJSON();
                                ttx.purchaseOrderNO = rowDt.orderNo;
                                console.log(JSON.stringify(ttx));
                                $('#example')
                                    .empty()
                                    .append(JSON.stringify(ttx) + '</br>');

                                if (validform('changeTimeData').form()) {
                                    utils.ajax({
                                        newTimeData: JSON.stringify(ttx),
                                        selectorderNo: selectorderNo,
                                    },
                                        '/proxy-purchase/purchase/purchaseOrderDeliveryDateChange/insertByBatch',
                                        'POST',
                                        successCallback,
                                    );

                                    function successCallback(msg) {
                                        $("input[name='newExpectDeliveryDate'").val('');
                                        $('#remarksf').val('');
                                        if (msg.code == 1) {
                                            utils
                                                .dialog({
                                                    content: '更新到货时间成功！',
                                                    quickClose: true,
                                                    timeout: 30000,
                                                })
                                                .showModal();
                                            $('#searchBtn').trigger('click');
                                        } else {
                                            utils
                                                .dialog({
                                                    content: '选中的在途订单的状态已发生变更，只有在途的订单可以修改到货时间！',
                                                    quickClose: true,
                                                    timeout: 8000,
                                                })
                                                .showModal();
                                        }
                                    }
                                } else {
                                    //验证不通过
                                    utils
                                        .dialog({
                                            content: '校验不通过！',
                                            quickClose: true,
                                            timeout: 4000,
                                        })
                                        .showModal();
                                    return false;
                                }
                            },
                            cancelValue: '取消',
                            cancel: function () { },
                        })
                        .showModal();
                }
            }
        } else if (cs == 'productList') {
            utils
                .dialog({
                    content: '请在采购订单列表中选择记录！',
                    quickClose: true,
                    timeout: 2000,
                })
                .showModal();
            return false;
        }
    });

    //关闭订单
    $('#closeRowData').on('click', function () {
        var cs = $('#toggle_wrap .active').attr('id');
        if (cs == 'orderList') {
            //获取选中的id进行批量处理
            var selectIds = '';
            var sels = $('#X_Tablea').XGrid('getSeleRow');
            console.log('选中行：' + sels);
            if (sels == undefined || sels == null) { } else if (sels.length == undefined) {
                selectIds = sels.orderNo;
            } else {
                if ($('#sysOrgCode').val() == '001' && sels.length > 1) {
                    utils
                        .dialog({
                            content: '请选中一条要关闭的记录！',
                            quickClose: true,
                            timeout: 4000,
                        })
                        .showModal();
                    return false;
                }
                $.each(sels, function (i, obj) {
                    console.log(obj.id);
                    selectIds = selectIds + obj.orderNo + ',';
                });
            }

            //判断是否选中
            if (selectIds.length > 0) {
                utils.ajax({ selectorderNo: selectIds },
                    '/proxy-purchase/purchase/purchaseOrderDeliveryDateChange/getInsertByBatchCount',
                    'POST',
                    successCallbackOne,
                );

                function successCallbackOne(msgOne) {
                    if (msgOne == '0') {
                        utils
                            .dialog({
                                content: '只有在途的订单可以关闭订单！',
                                quickClose: true,
                                timeout: 8000,
                            })
                            .showModal();
                    } else {
                        //百草
                        /*  utils
                             .dialog({
                                 title: '提示',
                                 content: '仓库已收货',
                                 width: "600px",
                                 cancelValue: '关闭',
                                 cancel: function() {}
                             })
                             .showModal(); */
                        $.ajax({
                            url: '/proxy-purchase/purchase/purchaseOrder/getOrderTypeByList',
                            data: { orderNoList: selectIds },
                            success: function (result) {
                                if (result.code == 1) {
                                    utils
                                        .dialog({
                                            content: result.msg,
                                            quickClose: true,
                                            timeout: 8000,
                                        })
                                        .showModal();
                                    return;
                                } else {
                                    let b = sels.some((item) => {
                                        return (
                                            item.orderCloseStatusName == '关闭中' ||
                                            item.orderCloseStatusName == '关闭成功'
                                        );
                                    });
                                    if (b) {
                                        utils
                                            .dialog({
                                                title: '提示',
                                                content: '该状态下不可再次关闭！',
                                                okValue: '确定',
                                                ok: function () { },
                                            })
                                            .showModal();
                                        return false;
                                    }
                                    var elx = document.querySelector('#dialog_Block');
                                    $.ajax({
                                        url: '/proxy-purchase/purchase/purchaseOrderProduct/getPurchaseOrderProductCloseStatus',
                                        data: { orderNo: selectIds },
                                        success: function (result) {
                                            if (result.code == 1) {
                                                utils
                                                    .dialog({
                                                        content: result.msg,
                                                        quickClose: true,
                                                        timeout: 8000,
                                                    })
                                                    .showModal();
                                            } else {
                                                $.ajax({
                                                    // 判断订单是否存在审核中的付款申请
                                                    url: '/proxy-purchase/purchase/purchaseOrderProduct/checkPurchaseOrderAdvancePayment',
                                                    data: { orderNo: selectIds },
                                                    success: function (result) {
                                                        if (result.code == 1) {
                                                            utils
                                                                .dialog({
                                                                    content: result.msg,
                                                                    quickClose: true,
                                                                    timeout: 8000,
                                                                })
                                                                .showModal();
                                                        } else {
                                                            utils
                                                                .dialog({
                                                                    title: '提示',
                                                                    content: elx,
                                                                    width: '600px',
                                                                    okValue: '确定',
                                                                    ok: async function () {
                                                                        //调用百草的接口进行校验
                                                                        /* utils
                                                                            .dialog({
                                                                                title: '提示',
                                                                                content: '当前采购订单的货物已到仓库，存在未完成的收货/上架流程，不允许关闭订单！！',
                                                                                width: "600px",
                                                                                cancelValue: '关闭',
                                                                                cancel: function() {}
                                                                            })
                                                                            .showModal(); */


                                                                        if (validform('cloneOrder').form()) {
                                                                            utils.ajax({
                                                                                data: selectIds,
                                                                                orderCloseReason: $('#orderCloseReason').val(),
                                                                            },
                                                                                '/proxy-purchase/purchase/purchaseOrder/closeBatchPurchaseOrder',
                                                                                'POST',
                                                                                successCallback,
                                                                            );
                                                                            $('#orderCloseReason').val('');
                                                                        } else {
                                                                            return false;
                                                                        }

                                                                        function successCallback(msg) {
                                                                            if (msg.flg == false) {
                                                                                utils
                                                                                    .dialog({
                                                                                        content: '发起关闭订单申请流程成功！',
                                                                                        quickClose: true,
                                                                                        timeout: 7000,
                                                                                    })
                                                                                    .showModal();
                                                                                $('#searchBtn').trigger('click');
                                                                            } else {
                                                                                if (msg.code == 1) {
                                                                                    utils
                                                                                        .dialog({
                                                                                            content: '只有在途的订单可以关闭订单！',
                                                                                            quickClose: true,
                                                                                            timeout: 4000,
                                                                                        })
                                                                                        .showModal();
                                                                                    $('#searchBtn').trigger('click');
                                                                                } else if (msg.code == 9) {
                                                                                    utils
                                                                                        .dialog({
                                                                                            content: '数据异常，无法关闭，请联系神农技术人员！',
                                                                                            quickClose: true,
                                                                                            timeout: 4000,
                                                                                        })
                                                                                        .showModal();
                                                                                    $('#searchBtn').trigger('click');
                                                                                } else if (msg.code == 999) {

                                                                                    utils
                                                                                        .dialog({
                                                                                            content: msg.msg,
                                                                                            cancelValue: '关闭',
                                                                                            cancel: function () {


                                                                                            }
                                                                                        })
                                                                                        .showModal();
                                                                                    $('#searchBtn').trigger('click');
                                                                                } else {
                                                                                    var selecttr = '';
                                                                                    console.log(
                                                                                        'msg=========:' +
                                                                                        JSON.stringify(msg),
                                                                                    );
                                                                                    if (
                                                                                        msg.statusList != null &&
                                                                                        msg.statusList.length > 0
                                                                                    ) {
                                                                                        selecttr +=
                                                                                            '采购订单编号{' +
                                                                                            msg.statusList.join(',') +
                                                                                            '}，已发起流程！<br/>';
                                                                                    }
                                                                                    if (
                                                                                        msg.paymentList != null &&
                                                                                        msg.paymentList.length > 0
                                                                                    ) {
                                                                                        selecttr +=
                                                                                            '采购订单编号{' +
                                                                                            msg.paymentList.join(',') +
                                                                                            '}，已申请或已完成付款申请，无法关闭！<br/>';
                                                                                    }
                                                                                    if (
                                                                                        msg.approvalList != null &&
                                                                                        msg.approvalList.length > 0
                                                                                    ) {
                                                                                        selecttr +=
                                                                                            '采购订单编号{' +
                                                                                            msg.approvalList.join(',') +
                                                                                            '}，' +
                                                                                            msg.approverStr +
                                                                                            '！';
                                                                                    }
                                                                                    if (selecttr != '') {
                                                                                        utils
                                                                                            .dialog({
                                                                                                content: selecttr,
                                                                                                quickClose: true,
                                                                                                timeout: 4000,
                                                                                            })
                                                                                            .showModal();
                                                                                        $('#searchBtn').trigger('click');
                                                                                    } else {
                                                                                        utils
                                                                                            .dialog({
                                                                                                content: msg.msg,
                                                                                                quickClose: true,
                                                                                                timeout: 4000,
                                                                                            })
                                                                                            .showModal();
                                                                                        $('#searchBtn').trigger('click');
                                                                                    }
                                                                                }
                                                                            }

                                                                            /*  if (msg.code == 0) {
                                                                                                        utils.dialog({content:'发起关闭订单申请流程成功！', quickClose: true, timeout: 2000}).showModal();
                                                                                                        $("#searchBtn").trigger("click");
                                                                                                    }else if (msg.code == 2) {
                                                                                                        utils.dialog({content:'已提交关闭申请！', quickClose: true, timeout: 2000}).showModal();
                                                                                                        $("#searchBtn").trigger("click");
                                                                                                    }else {
                                                                                                        utils.dialog({
                                                                                                            content: msg.msg,
                                                                                                            quickClose: true,
                                                                                                            timeout: 2000
                                                                                                        }).showModal();
                                                                                                    }*/
                                                                        }
                                                                    },
                                                                    cancelValue: '取消',
                                                                    cancel: function () {
                                                                        $('#orderCloseReason').val('');
                                                                    },
                                                                })
                                                                .showModal();
                                                        }
                                                    },
                                                });
                                            }
                                        },
                                    });
                                }
                            },
                        });
                    }
                }
            } else {
                utils
                    .dialog({
                        content: '请选中要关闭的记录！',
                        quickClose: true,
                        timeout: 4000,
                    })
                    .showModal();
                return false;
            }
        } else if (cs == 'productList') {
            utils
                .dialog({
                    content: '请在采购订单列表中选择记录！',
                    quickClose: true,
                    timeout: 2000,
                })
                .showModal();
            return false;
        }
    });
    //关闭订单行
    $('#closeLineData').on('click', function () {
        var cs = $('#toggle_wrap .active').attr('id');
        if (cs == 'orderList') {
            var sels = $('#X_Tablea').XGrid('getSeleRow');
            if (sels.length && sels.length === 1) {
                var selectIds = sels[0].orderNo;
                var supplierName = sels[0].supplierName;
                var orderTypeName = sels[0].orderTypeName;
                if (orderTypeName.indexOf('调拨采购订单') != -1) {
                    utils
                        .dialog({
                            content: '只有常规采购的订单可以行关闭！',
                            quickClose: true,
                            timeout: 8000,
                        })
                        .showModal();
                    return false;
                }
                //百草

                /*   utils
                      .dialog({
                          title: '提示',
                          content: '当前采购订单的货物已到仓库，存在未完成的收货/上架流程，不允许关闭订单！！',
                          width: "600px",
                          cancelValue: '关闭',
                          cancel: function() {}
                      })
                      .showModal(); */
                utils.ajax({ selectorderNo: selectIds },
                    '/proxy-purchase/purchase/purchaseOrderDeliveryDateChange/getInsertByBatchCount',
                    'POST',
                    successCallbackOne,
                );

                function successCallbackOne(msgOne) {
                    if (msgOne == '0') {
                        utils
                            .dialog({
                                content: '只有在途的订单可以关闭订单！',
                                quickClose: true,
                                timeout: 8000,
                            })
                            .showModal();
                    } else {
                        let b = sels.some((item) => {
                            return (
                                item.orderCloseStatusName == '关闭中' ||
                                item.orderCloseStatusName == '关闭成功'
                            );
                        });
                        if (b) {
                            utils
                                .dialog({
                                    title: '提示',
                                    content: '该状态下不可再次关闭！',
                                    okValue: '确定',
                                    ok: function () { },
                                })
                                .showModal();
                            return false;
                        }
                        $.ajax({
                            url: '/proxy-purchase/purchase/purchaseOrderProduct/getPurchaseOrderProductCloseStatus',
                            data: { orderNo: selectIds },
                            success: function (result) {
                                if (result.code == 1) {
                                    utils
                                        .dialog({
                                            content: result.msg,
                                            quickClose: true,
                                            timeout: 8000,
                                        })
                                        .showModal();
                                } else {
                                    $.ajax({
                                        // 判断订单是否存在审核中的付款申请
                                        url: '/proxy-purchase/purchase/purchaseOrderProduct/checkPurchaseOrderAdvancePayment',
                                        data: { orderNo: selectIds },
                                        success: function (result) {
                                            if (result.code == 1) {
                                                utils
                                                    .dialog({
                                                        content: result.msg,
                                                        quickClose: true,
                                                        timeout: 8000,
                                                    })
                                                    .showModal();
                                            } else {
                                                utils
                                                    .dialog({
                                                        url: '/proxy-purchase/purchase/purchaseOrderProduct/toCloseProductList?orderNo=' +
                                                            selectIds +
                                                            '&supplierName=' +
                                                            supplierName +
                                                            '',
                                                        title: '商品列表',
                                                        width: $(window).width() * 0.9,
                                                        height: $(window).height() * 0.7,
                                                        onclose: function () {
                                                            if (
                                                                this.returnValue &&
                                                                this.returnValue.flag === 1
                                                            ) {
                                                                var searchData =
                                                                    $('#searchForm').serializeToJSON();
                                                                var channelId = $('#channelId').val();
                                                                searchData.channelId = channelId;
                                                                $('#X_Tablea')
                                                                    .setGridParam({
                                                                        url: '/proxy-purchase/purchase/purchaseOrder/loadPurchaseOrderData?' +
                                                                            $('#searchForm').serialize(),
                                                                        postData: {
                                                                            purchaseOrderProductVoStr: JSON.stringify(searchData),
                                                                        },
                                                                    })
                                                                    .trigger('reloadGrid');
                                                                $('#X_Tableb')
                                                                    .setGridParam({
                                                                        url: '/proxy-purchase/purchase/purchaseOrderProduct/loadPurchaseOrderProductData?' +
                                                                            $('#searchForm').serialize(),
                                                                        postData: { a: 19 },
                                                                    })
                                                                    .trigger('reloadGrid');
                                                            }
                                                        },
                                                    })
                                                    .showModal();
                                            }
                                        },
                                    });
                                }
                            },
                        });
                    }
                }
            } else {
                utils
                    .dialog({
                        content: '请选中一条要关闭的记录！',
                        quickClose: true,
                        timeout: 4000,
                    })
                    .showModal();
                return false;
            }
        } else if (cs == 'productList') {
            utils
                .dialog({
                    content: '请在采购订单列表中选择记录！',
                    quickClose: true,
                    timeout: 2000,
                })
                .showModal();
            return false;
        }
    });

    //删除采购订单---start--by TangHuaLiang
    $('#deleteRowData').on('click', function () {
        var cs = $('#toggle_wrap .active').attr('id');
        if (cs == 'orderList') {
            var xtb = $('#X_Tablea').XGrid('getSeleRow');
            //获取选中的id进行批量处理
            var selectIds = '';
            var sels = $('#X_Tablea').XGrid('getSeleRow');
            console.log('选中行：' + sels);
            if (sels == undefined || sels == null) { } else if (sels.length == undefined) {
                selectIds = sels.orderNo;
            } else {
                $.each(sels, function (i, obj) {
                    console.log(obj.id);
                    selectIds = selectIds + obj.orderNo + ',';
                });
            }
            //判断是否选中
            if (selectIds.length > 0) {
                utils.ajax({ selectorderNo: selectIds },
                    '/proxy-purchase/purchase/purchaseOrderDeliveryDateChange/getdeleteByBatchCount',
                    'POST',
                    successCallbackOne,
                );

                function successCallbackOne(msgOne) {
                    if (msgOne == '0') {
                        utils
                            .dialog({
                                content: '只有草稿状态的订单可以删除！',
                                quickClose: true,
                                timeout: 8000,
                            })
                            .showModal();
                    } else {
                        var elx = '是否确认删除订单？';
                        utils
                            .dialog({
                                title: '提示',
                                content: elx,
                                width: '600px',
                                okValue: '确定',
                                ok: function () {
                                    utils.ajax({
                                        data: selectIds,
                                    },
                                        '/proxy-purchase/purchase/purchaseOrder/deleteBatchPurchaseOrder',
                                        'POST',
                                        successCallback,
                                    );

                                    function successCallback(msg) {
                                        if (msg.flg == false) {
                                            utils
                                                .dialog({
                                                    content: '删除成功！',
                                                    quickClose: true,
                                                    timeout: 7000,
                                                })
                                                .showModal();
                                            $('#searchBtn').trigger('click');
                                        } else {
                                            if (msg.code == 1) {
                                                utils
                                                    .dialog({
                                                        content: '只有草稿和待审核状态的订单可以删除！',
                                                        quickClose: true,
                                                        timeout: 4000,
                                                    })
                                                    .showModal();
                                                $('#searchBtn').trigger('click');
                                            } else if (msg.code == 9) {
                                                utils
                                                    .dialog({
                                                        content: '数据异常，无法删除，请联系神农技术人员！',
                                                        quickClose: true,
                                                        timeout: 4000,
                                                    })
                                                    .showModal();
                                                $('#searchBtn').trigger('click');
                                            } else {
                                                var selecttr = '';

                                                if (selecttr != '') {
                                                    utils
                                                        .dialog({
                                                            content: selecttr,
                                                            quickClose: true,
                                                            timeout: 4000,
                                                        })
                                                        .showModal();
                                                    $('#searchBtn').trigger('click');
                                                } else {
                                                    utils
                                                        .dialog({
                                                            content: msg.msg,
                                                            quickClose: true,
                                                            timeout: 4000,
                                                        })
                                                        .showModal();
                                                    $('#searchBtn').trigger('click');
                                                }
                                            }
                                        }
                                    }
                                },
                                cancelValue: '取消',
                                cancel: function () {
                                    $('#orderCloseReason').val('');
                                },
                            })
                            .showModal();
                    }
                }
            } else {
                utils
                    .dialog({
                        content: '请选中要删除的记录！',
                        quickClose: true,
                        timeout: 4000,
                    })
                    .showModal();
                return false;
            }
        } else if (cs == 'productList') {
            utils
                .dialog({
                    content: '请在采购订单列表中选择记录！',
                    quickClose: true,
                    timeout: 2000,
                })
                .showModal();
            return false;
        }
    });
    //删除采购订单---end

    //采购员联想查询
    $('#createUser').Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/purchaseRefundProductOrder/getPurchaseRefundProductOrderProductUserName?pageNum=1&pageSize=5&sort=asc', //异步请求
        paramName: 'userName', //查询参数，默认 query
        params: {
            orgCode: function () {
                return $('#orgCode').val();
            },
        },
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return { value: dataItem.userName, data: dataItem.id };
                }),
            };
        },
        onSearchStart: function (param) {
            return didChooseOrgCode();
        },
        onSelect: function (result) {
            $('#createUserName').val(result.data);
            selectvl = result.value;
        },
        onNoneSelect: function (params, suggestions) {
            $('#createUserName').val('');
            $('#createUser').val('');
        },
    });

    //商品联想搜索
    $('#productNameKeyword').Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/supplier/findPageInfoByMnemonInfo', //异步请求
        paramName: 'keyword', //查询参数，默认 query
        params: {
            orgCode: function () {
                return $('#orgCode').val();
            },
        },
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        maxWidth: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {
                        value: dataItem.productName +
                            '\xa0\xa0\xa0' +
                            dataItem.specifications +
                            '\xa0\xa0\xa0' +
                            dataItem.manufacturerName,
                        data: dataItem.productCode,
                    };
                }),
            };
        },
        onSearchStart: function (param) {
            return didChooseOrgCode();
        },
        onSelect: function (result) {
            $('#productCode').val(result.data);
        },
        onNoneSelect: function (params, suggestions) {
            $('#productNameKeyword').val('');
            $('#productCode').val('');
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val());
            }
            if (!$ele.is(':focus')) {
                $ele.Autocomplete('hide');
            }
        },
    });

    //入库单、退货单联想查询
    // var refundProductNoselectvl = '';
    // $('#orderNo').Autocomplete({
    //     serviceUrl: '/proxy-purchase/purchase/purchaseOrder/getPurchaseOrderAssociate?pageNum=1&pageSize=5&sort=asc&orderType=' +
    //         $('#orderType').val(), //异步请求
    //     paramName: 'orderNo', //查询参数，默认 query
    //     dataType: 'json',
    //     minChars: '0', //触发自动匹配的最小字符数
    //     maxHeight: '300', //默认300高度
    //     showNoSuggestionNotice: true,
    //     noSuggestionNotice: '查询无结果',
    //     triggerSelectOnValidInput: false, // 必选
    //     transformResult: function (response) {
    //         return {
    //             suggestions: $.map(response, function (dataItem) {
    //                 return { value: dataItem, data: dataItem };
    //             }),
    //         };
    //     },
    //     onSelect: function (result) {
    //         refundProductNoselectvl = result.value;
    //     },
    //     onNoneSelect: function (params, suggestions) {
    //         $('#orderNo').val('');
    //     },
    // });

    $('#factory_range').on('change', function () {
        $('#factory_aotuname').attr('name', '');
        $('#factory_aotuname').val('');
        $('#supplierCodeFor').val('');
        $('#factory_aotuname').removeAttr('disabled');
        var operate = $('#factory_range option:selected').val();
        if (operate == 'productBrandManufacturers') {
            $('#factory_aotuname').attr('name', operate);
        } else if (operate == 'productProduceFactory') {
            $('#factory_aotuname').attr('name', operate);
        } else if (operate == 'supplierName') {
            $('#factory_aotuname').attr('name', operate);
        } else {
            $('#factory_aotuname').attr('disabled', 'disabled');
        }
    });
    var ts = '';
    var sv = 'supplierName';
    $('#factory_range').on('change', function () {
        sv = $('#factory_range option:selected').val();
        $('#supplier_autocomplete').val('');
        if (sv == 'productBrandManufacturers') {
            ts = '/proxy-purchase/purchase/purchaseOrder/getBrandFactoryList';
            autos(ts);
            document.getElementById('magnifyingId').style.display = 'none';
        } else if (sv == 'productProduceFactory') {
            ts = '/proxy-purchase/purchase/purchaseOrder/getManuFactoryList';
            autos(ts);
            document.getElementById('magnifyingId').style.display = 'none';
        } else if (sv == 'supplierName') {
            // ts = '/proxy-purchase/purchase/supplier/getOrganBaseListByMnemonInfo';
            ts = '/proxy-purchase/purchase/supplier/getNewOrganBaseListByMnemonInfo';
            autos(ts);
            document.getElementById('magnifyingId').style.display = '';
        } else {
            ts = '/proxy-purchase/purchase/supplier/getScreeningList';
            autos(ts);
        }
    });

    var svs = $('#factory_range').val();
    if (svs == 'supplierName') {
        $('#factory_aotuname')
            .on({
                dblclick: function (e) {
                    commodity_search_dia($('#factory_aotuname').attr('oldvalue'));
                },
                keyup: function (e) {
                    if (e.keyCode === 13 && !$('#factory_aotuname').attr('oldvalue')) {
                        commodity_search_dia();
                    }
                },
            })
            .siblings('.glyphicon-search')
            .on('click', function (e) {
                commodity_search_dia($('#factory_aotuname').attr('oldvalue'));
                e.stopPropagation();
            });
    }

    function commodity_search_dia(val) {
        if (!didChooseOrgCode()) return;
        dialog({
            url: '/proxy-purchase/purchase/purchaseOrder/toSupplierPage',
            title: '供应商列表',
            width: $(window).width() * 0.9,
            height: '550px',
            data: val, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $('#supplierCodeFor').val(data.supplierCode);
                    $('#factory_aotuname').val(data.supplierName);
                } else {
                    $('#supplierCodeFor').val('');
                    $('#factory_aotuname').val('');
                }
            },
        }).showModal();
    }

    var selectvlsupplier = '';

    function autos(ts) {
        $('#factory_aotuname').Autocomplete({
            serviceUrl: ts, //异步请求
            paramName: 'name', //查询参数，默认 query
            params: {
                orgCode: function () {
                    return $('#orgCode').val();
                },
            },
            dataType: 'json',
            //lookup: ts, //监听数据 value显示文本，data为option的值
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            showNoSuggestionNotice: true,
            noSuggestionNotice: '查询无结果',
            triggerSelectOnValidInput: false, // 必选
            autoSelectFirst: true,
            transformResult: function (response) {
                return {
                    suggestions: $.map(response, function (dataItem) {
                        switch (sv) {
                            case 'productBrandManufacturers':
                                return {
                                    value: dataItem.brandfactoryName,
                                    data: dataItem.brandfactoryId,
                                };
                                break;
                            case 'productProduceFactory':
                                return {
                                    value: dataItem.manufactoryName,
                                    data: dataItem.manufactoryId,
                                };
                                break;
                            case 'supplierName':
                                return {
                                    value: dataItem.supplierName,
                                    data: dataItem.supplierCode,
                                };
                                break;
                            default:
                                return { value: dataItem, data: dataItem };
                        }
                    }),
                };
            },
            onSearchStart: function (param) {
                return didChooseOrgCode();
            },
            onSelect: function (result) {
                if (sv == 'supplierName') {
                    $('#supplierCodeFor').val(result.data);
                }
                $('#factory_aotuname').attr('oldvalue', result.value);
                //选中回调
                $('#supplier_autocomplete').attr('selectId', result.data);
                selectvlsupplier = result.value;
            },
            onSearchComplete: function (query, suggestions) {
                var $ele = $(this);
                if (suggestions && suggestions.length === 0) {
                    $ele.attr('oldvalue', '');
                } else {
                    $ele.attr('oldvalue', $ele.val());
                }
                if (!$ele.is(':focus')) {
                    $ele.Autocomplete('hide');
                }
            },
            onNoneSelect: function (params, suggestions) {
                $('#supplierCodeFor').val('');
                $('#factory_aotuname').val('');
                setTimeout(function () {
                    $('#factory_aotuname').attr('oldvalue', '');
                }, 200);
            },
        });
    }
    autos('/proxy-purchase/purchase/supplier/getNewOrganBaseListByMnemonInfo');

    $('#endt').blur(function () {
        tm();
    });

    $('#begint').blur(function () {
        xm();
    });

    function tm() {
        var beginTime = $('#begint').val();
        var endTime = $('#endt').val();
        if (!beginTime) {
            return false;
        }
        beginTime = new Date(beginTime.replace(/-/g, '/'));
        endTime = new Date(endTime.replace(/-/g, '/'));

        var days = endTime.getTime() - beginTime.getTime();
        time = parseInt(days / (1000 * 60 * 60 * 24));

        if (time < 0) {
            utils
                .dialog({
                    content: '截止日期，不能小于起始日期',
                    quickClose: true,
                    timeout: 2000,
                })
                .showModal();
            $('#endt').val('');
            return false;
        }
    }

    function xm() {
        var beginTime = $('#begint').val();
        var endTime = $('#endt').val();
        if (endTime) {
            console.log('ssf');

            beginTime = new Date(beginTime.replace(/-/g, '/'));
            endTime = new Date(endTime.replace(/-/g, '/'));

            var days = endTime.getTime() - beginTime.getTime();
            time = parseInt(days / (1000 * 60 * 60 * 24));

            if (time < 0) {
                utils
                    .dialog({
                        content: '开始天数，不能大于结束天数',
                        quickClose: true,
                        timeout: 2000,
                    })
                    .showModal();
                $('#begint').val('');
                return false;
            }
        } else {
            return false;
        }
    }
});

//业务类型搜索图标
$(document).on('click', '.glyphicon-search', function () {
    $(this).siblings('input').trigger('dblclick');
});

//业务类型 输入框双击 弹出业务类型列表
$('#channelId_inp').dblclick(function () {
    utils.channelDialog('0', 1).then((res) => {
        console.log(res);
        let _str_name = '',
            _str_code = '';
        let _str_arr = res.map((item) => {
            return item.channelName;
        });
        _str_name = _str_arr.join(',');

        let _str_code_arr = res.map((item) => {
            return item.channelCode;
        });
        _str_code = _str_code_arr.join(',');
        $('#channelId_inp').val(_str_name);
        $('#channelId').val(_str_code);
    });
});

function makeContract(type) {
    if (type == 1) {
        $('#filePath').val('');
    }
    //获取选中的采购订单
    var ids = '';
    var data = $('#X_Tablea').XGrid('getSeleRow');
    if (data.length <= 0) {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    if (data.length > 1) {
        utils
            .dialog({ content: '请选中一条数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }

    if (data.length && data.length > 0) {
        data = data.map(function (item, key) {
            if (key > 0) {
                utils
                    .dialog({
                        content: '请选中一条数据！',
                        quickClose: true,
                        timeout: 4000,
                    })
                    .showModal();
                return false;
            }
            ids = item.orderNo;
        });
    }
    if (ids == '') {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    $.ajax({
        method: 'POST',
        url: '/proxy-purchase/purchase/upload/checkDownContract',
        data: {
            orderNo: ids,
            type: type,
        },
        dataType: 'json',
        cache: false,
    }).done(function (data) {
        if (data.code == 0) {
            if (type == 1) {
                uploadContractBtn(ids);
            } else if (type == 2) {
                deleteContractBtn(ids);
            } else if (type == 3) {
                window.open('/proxy-purchase/purchase/upload/downContract?orderNo=' + ids);
            }
        } else {
            utils
                .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
                .showModal();
        }
    });
}

function uploadContractBtn(orderNos) {
    //获取选中的采购订单
    let len = $('.uploadFiles_div>div').length;
    if (len < 2) {
        utils
            .dialog({
                title: '上传附件',
                width: 700,
                height: 120,
                overflow: 'hidden',
                content: $('#upload_dialog').css({
                    height: '120px',
                    'overflow-y': 'hidden',
                }),
                okValue: '确定',
                cancelValue: '取消',
                onshow: function () {
                    $('#btnUpload').removeAttr('disabled');
                },
                ok: function () {
                    UploadFileFn(function (list, filename) { });
                },
                cancel: function () { },
                onclose: function () { },
            })
            .showModal();
    } else {
        utils
            .dialog({
                title: '提示',
                content: '最多支持传1个附件。',
                okValue: '确定',
                ok: function () { },
            })
            .showModal();
        return false;
    }
}

function deleteContractBtn(ids) {
    utils
        .dialog({
            title: '提示',
            content: '<div style="max-height: 300px;overflow-y: auto;">确认是否删除，确认后，清空附件</div>',
            okValue: '确定',
            ok: function () {
                $.ajax({
                    method: 'POST',
                    url: '/proxy-purchase/purchase/upload/deleteContract',
                    data: {
                        orderNo: ids,
                    },
                    dataType: 'json',
                    cache: false,
                }).done(function (data) {
                    if (data.code == 0) {
                        utils
                            .dialog({
                                content: '删除成功！',
                                quickClose: true,
                                timeout: 4000,
                            })
                            .showModal();
                        window.location.reload();
                    } else {
                        utils
                            .dialog({ content: data.msg, quickClose: true, timeout: 4000 })
                            .showModal();
                    }
                });
            },
            cancelValue: '返回',
            cancel: function () {
                return;
            },
        })
        .showModal();
}

function UploadFileFn(cb) {
    var file = $('#btnUpload')[0].files[0];
    if (!file) {
        utils
            .dialog({
                title: '提示',
                width: 200,
                content: '请选择文件',
                okValue: '确定',
                ok: function () { },
            })
            .showModal();
        return false;
    }
    var fileName = file.name;
    var fileType = fileName
        .substring(fileName.lastIndexOf('.') + 1)
        .toLocaleLowerCase();
    if (imageType.indexOf(fileType) == -1) {
        utils
            .dialog({
                content: '请上传正确格式的文件！如压缩文件以及pdf。',
                quickClose: false,
                timeout: 2000,
            })
            .showModal();
        $('#filePath').val('');
        return false;
    }
    var maxsize = 20 * 1024 * 1024; //5M
    var size = file.size; //图片大小
    if (size > maxsize) {
        utils
            .dialog({
                content: '文件过大，文件大小不能超过20M',
                quickClose: false,
                timeout: 2000,
            })
            .showModal();
        $('#filePath').val('');
        return false;
    }
    if (!file) {
        utils
            .dialog({
                title: '提示',
                width: 200,
                content: '请选择文件',
                okValue: '确定',
                ok: function () { },
            })
            .showModal();
        return false;
    }

    var formData = new FormData();
    formData.append('file', file);
    formData.append('name', file.name);
    //获取选中的采购订单
    var orderNos = '';
    var data = $('#X_Tablea').XGrid('getSeleRow');
    if (data.length <= 0) {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }
    if (data.length > 1) {
        utils
            .dialog({ content: '请选中一条数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }

    if (data.length && data.length > 0) {
        data = data.map(function (item, key) {
            if (key > 0) {
                utils
                    .dialog({
                        content: '请选中一条数据！',
                        quickClose: true,
                        timeout: 4000,
                    })
                    .showModal();
                return false;
            }
            orderNos = item.orderNo;
        });
    }
    if (orderNos == '') {
        utils
            .dialog({ content: '请先选中数据！', quickClose: true, timeout: 4000 })
            .showModal();
        return false;
    }

    formData.append('orderNo', orderNos);
    var loading = utils
        .dialog({
            title: '上传中',
            fixed: true,
            width: 200,
            quickClose: false,
            cancel: false,
        })
        .showModal();

    $.ajax({
        url: '/proxy-purchase/purchase/upload/uploadContract',
        type: 'POST',
        async: false,
        data: formData,
        processData: false,
        contentType: false,
        beforeSend: function () {
            console.log('正在上传');
        },
        success: function (data) {
            loading.close();
            console.log('上传返回：', data);
            if (data.code == 0) {
                utils
                    .dialog({ content: '上传成功', quickClose: false, timeout: 2000 })
                    .showModal();
            } else {
                utils
                    .dialog({ content: '上传失败', quickClose: true, timeout: 2000 })
                    .showModal();
            }
        },
        error: function () {
            loading.close();
            utils
                .dialog({ content: '上传失败', quickClose: true, timeout: 2000 })
                .showModal();
        },
        complete: function () {
            $('#btnUpload').val('');
            $('#filePath').val('');
        },
    });
}