$(function () {

    $('#X_Tableb').XGrid({
        url:"/proxy-product/product/productBase/query",
        colNames: ['商品编码', '原商品编码', '商品名', '通用名',"商品大类", '型号/规格', '批准文号', '包装单位', '剂型', '小包装条码', '审核状态', '是否停用', '是否限销',"是否限采"],
        colModel: [
            {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'oldProductCode',
                index: 'oldProductCode',
                width: 160
            }, {
                name: 'productName',
                index: 'productName',
                width: 250
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 250
            },{
                name: 'largeCategoryVal',
                index: 'largeCategoryVal',
                width: 100
            }, {
                name: 'specifications',
                index: 'specifications',
                width: 120
            }, {
                name: 'approvalNumber',
                index: 'approvalNumber',
                width: 250
            }, {
                name: 'packingUnitValue',
                index: 'packingUnitValue',
                width: 100
            }, {
                name: 'dosageFormValue',
                index: 'dosageFormValue',
                width: 100
            }, {
                name: 'smallPackageBarCode',
                index: 'smallPackageBarCode',
                width: 160
            }, {
                name: 'statusValue',
                index: 'statusValue',
                width: 100
            },{
                name: 'baseDisableState',
                index: 'baseDisableState',
                width: 100,
                formatter:function (value,a,rowData) {
                    if(value==0){
                        return "否";
                    }else{
                        return "是";
                    }
                }
            }, {
                name: 'baseLimitedPinState',
                index: 'baseLimitedPinState',
                width: 100,
                formatter:function (value,a,rowData) {
                    if(value==0){
                        return "否";
                    }else{
                        return "是";
                    }
                }
            }, {
                name: 'baseLimitedProductionState',
                index: 'baseLimitedProductionState',
                width: 100,
                formatter:function (value,a,rowData) {
                    if(value==0){
                        return "否";
                    }else{
                        return "是";
                    }
                }
            }
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            utils.openTabs("productBaseDetail", "商品主数据详情", "/proxy-product/product/productBase/toDetail?productId="+id);
        },
        rownumbers: true,
    });
    // 生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord"},"manufacturer",{data:"manufactoryId",value:"manufactoryName"});

    $("#SearchBtn").on("click", function () {
        $("#productCode").val($("#productCode").val().trim());
        $("#approvalNumber").val($("#approvalNumber").val().trim());
        $("#smallPackageBarCode").val($("#smallPackageBarCode").val().trim());
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "productCode": $("#productCode").val(),
                "approvalNumber":$("#approvalNumber").val(),
                "smallPackageBarCode":$("#smallPackageBarCode").val(),
                "statues":$("#statues").val(),
                "largeCategory":$("#largeCategory").val(),
                "baseDisableState":$("#baseDisableState").val(),
                "baseLimitedPinState":$("#baseLimitedPinState").val(),
                "baseLimitedProductionState":$("#baseLimitedProductionState").val()
            },page:1
        }).trigger('reloadGrid');
    });

    $("#exportBtn").on("click", function () {
        //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
        utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text())).then( () => {
            return false;
        }).catch( () => {
        //原始处理逻辑代码
            var ck = true;
        // copy this parameter and the below buttons
        var html = $('#exportHtml').html();
        var d = dialog({
            title: '请选择导出字段',
            width:820,
            height:400,
            content: html,
            okValue: '确定',
            ok: function () {
                var arr=[];
                for(var i=0;i<$(".exportItem").length;i++)
                {
                    $(".exportItem").eq(i).find('dd input[type="checkbox"]').each(function () {
                        var checked=this.checked;
                        if(checked){
                            arr.push($.trim($(this).val()))
                        }
                    })
                }
                if(arr.length == 0){
                    utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                var exportAttribute= arr.join(',');
                if(exportAttribute==""){
                    utils.dialog({content: '请选择导出属性', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                $("#exportAttribute").val(exportAttribute);
                $("#searchForm").attr("action","/proxy-product/product/productBase/exportExcel");
                $("#searchForm").submit();
            },
            cancelValue: '取消',
            cancel: function () { },
            // copy button to other dialogues
            button: [
                {
                    id: 'chooseAll',
                    value: '全选',
                    callback: function () {
                        //debugger;
                        if(!ck){
                            $(".exportItem input").removeAttr("checked");
                            ck = true;
                        }else if(ck){
                            $(".exportItem input").prop("checked","checked");
                            ck = false;
                        }else{
                            return false;
                        };
                        return false;
                    }
                }
            ]
            //copy ends here
        });
        d.showModal();
        });
});
    $(document).on("change",".exportItem dt input",function () {
        var checked=this.checked;
        if(checked)
        {
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',false);
        }
    })
    $(document).on("change",".exportItem dd input[type='checkbox']",function () {
        var inpLen=$(this).parents("dd").find("input[type='checkbox']").length;
        var checkLen=$(this).parents("dd").find("input[type='checkbox']:checked").length;
        if(inpLen == checkLen)
        {
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',false);
        }
    })

})
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    });
}