var getData;
$(function () {
    /* 模拟数据 */
    /*var data_a = [{
        productCode: "Y010101591",
        productName: "西地碘含片(华素片)",
        size: "1.5毫克*15",
        dosage: "片剂",
        enterprise: "北京华素制药股份有限公司",
        from: "北京",
        unit: "盒",
        number: "10",
        price: "7.300",
        sum: "73.00",
        sterilizationBatch: "",
        batch: "1805072",
        productionDate: "2018-04-30",
        effectiveDate: "2020-04-30",
        storageName: "合格库",
        locationName: "合格库",
        approvalNumber: "国药准字H10910012",
        quality: "合格",
    }, {
        productCode: "Y010101591",
        productName: "西地碘含片(华素片)",
        size: "1.5毫克*15",
        dosage: "片剂",
        enterprise: "北京华素制药股份有限公司",
        from: "北京",
        unit: "盒",
        number: "10",
        price: "7.300",
        sum: "73.00",
        sterilizationBatch: "",
        batch: "1805072",
        productionDate: "2018-04-30",
        effectiveDate: "2020-04-30",
        storageName: "合格库",
        locationName: "合格库",
        approvalNumber: "国药准字H10910012",
        quality: "合格",
    }];

    var data_b = [{
        indentCode: "YBM20180817102154100237",
        carriage: "0",
        salesSum: "1161.25",
        couponSum: "100",
        paySum: "1061.25",
        paySumC: "壹仟零陆拾壹元贰角伍分",
        isOnlinePay: "已支付",
        payType: "A"
    }];

    var data_c = [{
        key: "发票情况",
        value: [],
        other: "",
    },
        {
            key: "结款情况",
            value: [],
            other: "缓存区名称：Y21-56--Y21-56",
        },
        {
            key: "结款方式",
            value: [3],
            other: "",
        },
        {
            key: "备注",
            value: "急用",
            other: "",
        },
    ];*/
    /* 日期格式化 */
    //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
    function dateFormat(time, format) {
        var t = new Date(parseInt(time,10));
        var tf = function (i) {
            return (i < 10 ? '0' : '') + i;
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        });
    }



    /* 获取数据 */
    getData = function (printType,outOrderCode) {
        var outOrderCodes = outOrderCode.join(',');
        $.ajax({
            url:"/proxy-order/order/orderOut/orderOut/findOutOrderPrintList",
            data:{
                outOrderCodes:outOrderCodes,
                printType: printType
            },
            success:function(res){
                if($.isArray(res.result)){
                    webRender(res.result,printType);
                }else {
                    utils.dialog({
                        title:'提示',
                        content:"数据为空或格式不正确",
                        okValue:'确定',
                        ok:function () {}
                    }).showModal();
                }
            },
        })
    };
    /* 数据渲染 */
    function webRender(data,printType) {
        var box_html = '';
        //var printType = 0;
        /* 基本结构拼装 */
        data.forEach(function (item,index) {
            console.log(item,item.orgCode);

                /* 销售出库复核单 */
                box_html +=`
                   <div class="content indent1 ${printType==0?'':'no_border'}">
                   <div class="header">
                        <div class="title">${printType==0?'长沙小药药医药科技有限公司销售清单(代合同)':''}</div>
                    </div>
                    <div class="top">
                        <ul class="info_list">
                            <li>
                                <span class="col_8"></span>
                                <span class="col_2" style="margin-left:-80px;"><span class="name">销售单号：</span><i class="val">${item.ecOrderCode}</i></span>
                            </li>
                            <li>
                                <span class="col_8"><span class="name">${printType==0?'客户编号：':''}</span><i class="val">${item.customerCode}</i></span>
                                <span class="col_2" style="margin-left:-80px;"><span class="name">订单日期：</span><i class="val">${item.slaseOrderCreateTime?dateFormat(item.slaseOrderCreateTime, 'yyyy-MM-dd'):""}</i></span>
                            </li>
                            <li>
                                <span class="col_3"><span class="name">${printType==0?'客户名称：':''}</span><i class="val">${item.customerName}</i></span>
                                <span class="col_5"><span class="name"></span><i class="val">${item.receiverPhone}</i></span>
                                <span class="col_2" style="margin-left:-80px;"><span class="name">${printType==0?'发货日期：':''}</span><i class="val">${item.createTime?dateFormat(item.createTime, 'yyyy-MM-dd'):""}</i></span>
                            </li>
                            <li>
                                <span class="col_6"><span class="name">${printType==0?'收货地址：':''}</span><i class="val">${item.address}</i></span>
                                <span class="col_2"><span class="name">${printType==0?'开票员：':''}</span><i class="val">汪高华</i></span>
                                <span class="col_2" style="margin-left:-80px;"><span class="name">${printType==0?'单据编号：':''}</span><i class="val">${item.salesOrderCode}</i></span>
                            </li>
                        </ul>
                        
                    </div>
                    <table id="table_a_${index}"></table>
                    <div class="bottom">
                        <ul class="info_list">
                            <li>
                                <span class="col_4"><span class="name">${printType==0?'发货地址：':''}</span><i class="val">${printType==0?'湖南省长沙县安沙镇物流大道东段9号弘广智能物流园2,3,4栋一楼':''}</i></span>
                                <span class="col_1"><span class="name">${printType==0?'复核员：':''}</span><i class="val"> </i></span>
                                <span class="col_1"><span class="name">${printType==0?'发货人：':''}</span><i class="val"> </i></span>
                                <span class="col_1" style="width: 120px;">
                                    <span class="name" style="width: 30px;"><span style="width: 20px;">${printType==0?'共':''}</span><i class="val">${item.pageTotal}</i></span>
                                    <span class="name" style="width: 60px;"><span style="width: 50px;">${printType==0?'页，第':''}</span><i class="val">${item.pageNumber}</i></span>
                                    <span class="name" style="width: 20px;">${printType==0?'页':''}</span>
                                </span>
                                <span class="col_3" style="width: 450px;">${printType==0?'白联:财务部 红联:随货同行 黄联:销售部 蓝联:储运部 绿联:回执':''}</span>
                            </li>
                            <li style="">
                                <span class="col_3" style="width: 335px;"><span class="name">${printType==0?'销售电话：':''}</span>0731-86395690</span>
                                <span class="col_3"><span class="name">${printType==0?'售后服务：':''}</span>0731-86368589</span>
                                <span class="col_1" style="width: 212px;"><span class="name">${printType==0?'客户签名：':''}</span></span>
                                <span class="col_3">${printType==0?'药品属于特殊商品，非质量问题一律不予退货！':''}</span>
                            </li>
                            <li style="">
                                <span class="col_6" style="margin-left: 10px;">${item.pcs}</span>
                                <span class="col_3"><span class="name">备注：</span><i class="val">${item.customerServiceRemarks?item.customerServiceRemarks:''}</i></span>
                            </li>
                        </ul>
                    </div>
                    
                </div>
                <div style="page-break-after:always"></div>
                `;
        });

        $("#box").html(box_html);

        /* 表格初始化 */
        data.forEach(function (item,index) {

                item.detailVoList = item.detailVoList.map(function (val,key) {
                    delete val.id;
                    return val
                });
                /* 销售出库复核单 */
                var nOptions = [['商品编号', '药品通用名称', '规格', '剂型', '生产企业', '产地',  '数量','单位', '单价', '金额', '批号', '生产日期', '有效期至', '批准文号', '质量状况'],['', '', '', '', '', '', '', '', '', '', '', '', '', '', '']];
                $("#table_a_"+index).jqGrid({
                    data: item.detailVoList,
                    datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                    height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                    //width: "1322.83",
                    colNames: nOptions[printType],
                    colModel: [{
                        index: 'productCode',
                        name: 'productCode',
                        width: 130,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 6) {
                                //金额合计(大写) name
                                return 'colspan=2'
                            }
                        }
                    }, {
                        index: 'productName',
                        name: 'productName',
                        width: 170,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            //console.log(rowId, tv, rawObject, cm, rdata);
                            if (rowId == 5) {
                                return 'colspan=4'
                            } else if (rowId == 6) {
                                //金额合计(大写) value
                                return 'colspan=4 style="text-align: left;padding-left:5px;"'
                            }
                        }
                    }, {
                        index: 'productSpec',
                        name: 'productSpec',
                        width: 100,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 5) {
                               //return 'style="display:none"'
                            } else if (rowId == 6) {
                                //金额合计 name
                                return 'colspan=3'
                            }
                        }
                    }, {
                        index: 'dosageForm',
                        name: 'dosageForm',
                        width: 60,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 5) {
                                return 'style="display:none"'
                            } else if (rowId == 6) {
                                //金额合计 value
                                return 'colspan=3 style="text-align: left;padding-left:30px;"'
                            }
                        }
                    }, {
                        index: 'productmManufacturer',
                        name: 'productmManufacturer',
                        width: 205,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 5) {
                                return 'style="display:none"'
                            } else if (rowId == 6) {
                                //金额合计 value
                                return 'colspan=3 style="text-align: left"'
                            }
                        }
                    }, {
                        index: 'producingArea',
                        name: 'producingArea',
                        width: 70,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 5) {
                                return 'style="display:none"'
                            }
                        }
                    },  {
                        index: 'outStoreNumber',
                        name: 'outStoreNumber',
                        width: 40,
                        summaryType: function (value, name, record) {
                            console.log(value, name, record);
                            if (rowId == 5) {
                                //return 'style="display:none"'
                            }
                            return value
                        }
                    },{
                        index: 'producingPackingUnit',
                        name: 'producingPackingUnit',
                        width: 50,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        }
                    }, {
                        index: 'productTaxPrice',
                        name: 'productTaxPrice',
                        width: 60,
                    }, {
                        index: 'taxAmount',
                        name: 'taxAmount',
                        width: 70,
                    }, {
                        index: 'batchCode',
                        name: 'batchCode',
                        width: 90,
                    }, {
                        index: 'manufactureTime',
                        name: 'manufactureTime',
                        width: 90,
                        formatter:function (e) {
                            if(e){
                                return dateFormat(e, 'yyyy-MM-dd')
                            }else{
                                return ""
                            }
                        }
                    }, {
                        index: 'expiryTime',
                        name: 'expiryTime',
                        width: 80,
                    }, {
                        index: 'approvalNumber',
                        name: 'approvalNumber',
                        width: 160,
                    }, {
                        index: 'quality',
                        name: 'quality',
                        width: 60,
                        formatter:function (val,rowType,rowData) {
                            if(rowData.productCode&&rowData.productCode!="小计"&&rowData.productCode!="金额合计(大写)："){
                                return "合格"
                            }else {
                                return ""
                            }
                        }
                    },/* {
                    index: 'status',
                    name: 'status',
                    hidden: true,
                    hidegrid: true
                },*/ ],
                    shrinkToFit:false,
                    rowNum: 4,
                    gridview: true,
                    gridComplete: function () {
                        var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                        var sum_sum = $(this).getCol('taxAmount', false, 'sum');
                        var data = $(this).getRowData();
                        //console.log(data);
                        if (data.length < 4) {
                            $(this).addRowData(data.length + 1, {}, "last");
                        } else if (data.length == 4) {
                            $(this).addRowData(5, {
                                //小计
                                productCode: "",
                                outStoreNumber: sum_number,
                                taxAmount: sum_sum,
                            }, "last");
                        } else if (data.length == 5) {
                            $(this).addRowData(6, {
                                //金额合计（大写）
                                productCode: "",
                                productName: item.totalRealPayAmountDesc,
                                //金额合计
                                productSpec: "",
                                dosageForm: "&nbsp;"+item.totalRealPayAmount,
                                //暂存区名称
                                productmManufacturer:'暂存区名称： '+item.temporaryAreaName
                            }, "last");
                        }
                        //$(this).footerData("set",{"productCode":"合计","number":sum_number,"sum":sum_sum});
                    }
                });
        });
        //printType = 1;
        if(printType==0){
            /* 打印预览 */
            utils.dialog({
                title:'预览',
                width:$(parent.window).width()-100,
                content:$('#big_box').html(),
                okValue:'确定',
                ok:function () {}
            }).showModal();
            //$("#print_box").contents().find(".ui-dialog-content").css("overflow","auto");
            window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
        }else if(printType==1){
            /* 打印 */
            $("#box").jqprint({
                globalStyles: true, //是否包含父文档的样式，默认为true
                mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                stylesheet: null, //外部样式表的URL地址，默认为null
                noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                append: null, //将内容添加到打印内容的后面
                prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                deferred: $.Deferred() //回调函数
            });
        }
    }
});