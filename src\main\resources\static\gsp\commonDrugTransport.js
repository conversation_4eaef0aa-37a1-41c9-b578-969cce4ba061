$(function(){

    var totalTable = z_utils.totalTable;

    var  colName = ['销售出库单编号','销售订单号','发货地址','发货日期','收货单位','收货地址','药品件数','运输方式','货单号','委托经办人/运输员','承运单位','车牌号'];
    var  colModel = [
        {
        name: 'salesOutNo',
        index: 'salesOutNo',
            width:200,
        key: true
    },{
        name: 'salesOrderNum',
        index: 'salesOrderNum',
            width:200
    },{
        name: 'sendAddres',
        index: 'sendAddres'
    },{
        name: 'sendTime',
        index: 'sendTime',
        formatter:function (e){
            if (e != null && e !="") {
                return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
            } else {
                return "";
            }
        }
    },{
            name: 'receiverUnit',
            index: 'receiverUnit'
        },{
        name: 'receiverAddres',
        index: 'receiverAddres'
    },{
        name: 'drugCount',
        index: 'drugCount'
    },{
        name: 'transportType',
        index: 'transportType'
    },{
        name: 'orderNo',
        index: 'orderNo'
    },{
        name: 'transportUser',
        index: 'transportUser'
    },{
        name: 'carrierUnit',
        index: 'carrierUnit'
    },{
        name: 'carNo',
        index: 'carNo'
    }];


    $('#CommonDrug_Table').XGrid({
        url:"/proxy-gsp/gsp/commonDrug/queryCommonDrugTransport",
        colNames: colName,
        colModel:colModel,
        postData:{
            startTime:$("#startTime").val(),
            endTime:$("#endTime").val()
        },
        altRows: true, //设置为交替行表格,默认为false
        rowNum: 20,
        rowList:[20,50,100],
        selectandorder: true,//是否展示序号，多选
        attachRow:true,

        ondblClickRow: function (id, dom, obj, index, event) {


        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['drugCount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
        },

        pager: '#grid_pager_a'
    })

})

    function query(){
        $('#CommonDrug_Table').setGridParam({
            url: '/proxy-gsp/gsp/commonDrug/queryCommonDrugTransport',
            postData: {
                startTime: $("#startTime").val(),
                salesOrderNum:$("#number").val(),
                endTime:$("#endTime").val(),
            }
        }).trigger('reloadGrid');


    }
    function uploadModel(){

        window.location = '/proxy-gsp/gsp/commonDrug/exportImportTemplet';

    }

    //导入
    $("#exportTemplet").on("click", function () {
        dialog({
            url: '/proxy-gsp/gsp/commonDrug/toFileupload',
            title: '批量导入',
            width: 600,
            height: 500,
            onclose:function () {
                setTimeout("location.reload()",300);
            }
        }).showModal();
    });



/* 导出 */
$('#exportOut').on('click', function () {
    var tableId = "CommonDrug_Table";
    z_utils.exportTable(tableId, function (that) {
        //需要导出项
        var colName = [];
        var colNameDesc = [];
        $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
            if ($(this).prop("checked")) {
                colName.push($(this).attr('name'))
                colNameDesc.push($(this).parent().text());
            }
        });
        
        var len = Number($('#totalPageNum').text());
        //获取当前选中项
        var data = $('#' + tableId).XGrid('getSeleRow');
        if (data && data.length && data.length >0) {
            /*  if (!data.length) {
                  data = [data];
              }*/
            data = data.map(function (item, key) {
                var new_item = {};
                colName.forEach(function (val, index) {
                    new_item[val] = item[val]
                })
                return new_item
            })
            len = data.length;
            data = JSON.stringify(data);
            // formData["selectData"] = data;
        } else {
            //如果一个也没选就导出全部
            // data = $('#' + tableId).XGrid('getRowData');
            data = '';
        }
        console.log(colName);

        var obj = {
            startTime: $("#startTime").val(),
            endTime: $("#endTime").val(),
            salesOrderNum: $("#number").val(),
            selectData: data,
            colName: colName,
            colNameDesc: colNameDesc
        }
        // obj["nameModel"] = nameModel;
        // 是否超出限制            
        utils.exportAstrictHandle('CommonDrug_Table', len, 1).then( () => {
            return false;
        }).catch( () => {
        	httpPost("/proxy-gsp/gsp/commonDrug/exportOut", obj);
        }); 
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
});