$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //显示流程图
    var processInstaId=$("#processId").val();
    if(!processInstaId || processInstaId == ""){
        processInstaId=$("#approvalProcessId").val();
    }
    initApprovalFlowChart( processInstaId);
    var dataid = Number($('#id').val());
    $('#table').XGrid({
        url: "/proxy-supplier/supplier/supplierLockApprovalRecord/queryLockDetailListById?id="+dataid,
        colNames: ['', '供应商编码', '供应商名称', '供应商类别', '是否停用', '业务类型', '<i style="color:red;margin-right:4px;">*</i>申请原因'/*,'审核意见'*/],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden:true
        }, {
            name: 'supplierCode',
        }, {
            name: 'supplierName',
        }, {
            name: 'supplierTypeName',
        }, {
            name: 'isLock',
            formatter: function (e) {
                if (e == '0') {
                    return '否';
                } else if (e == '1') {
                    return '是';
                } else {
                    return "";
                }
            },unformat: function (e) {
                if (e == '否') {
                    return '0';
                } else if (e == '是') {
                    return '1';
                } else {
                    return "";
                }
            }
        }, {
            name: 'isLock_a',
            formatter: function (e) {
                if (e == '0') {
                    return '锁定';
                } else if (e == '1') {
                    return '解除锁定';
                } else {
                    return "";
                }
            },unformat: function (e) {
                if (e == '锁定') {
                    return '0';
                } else if (e == '解除锁定') {
                    return '1';
                } else {
                    return "";
                }
            }
        },{
            name: 'appReason',
            rowtype: '#appReason'
        }/*,{
            name: 'auditOpinion',
            rowtype: '#auditOpinion'
        }*/,{
            name: 'supplierOrganId',
            hidden:true
        },{
            name:'supplierType',
            hidden:true
        },{
            name: 'orgCode',
            index: 'orgCode',
            hidden:true
        },{
            name: 'keyId',
            hidden:true
        },{
            name: 'fid',
            hidden:true
        }
        ],
        rowNum: 0,
        rownumbers: true,//是否展示序号
        altRows: true
    });





    
    $("#addRow").on("click",function(){
    	var lockPageFlag = $("#lockPageFlag").val();
        var selArr = $("#table").getRowData();
        utils.dialog({
            url: '/proxy-supplier/supplier/supplierLockApprovalRecord/toSearchList/'+lockPageFlag,//弹框页面请求地址
            title: '搜索供应商',
            width: 1000,
            data:{
                initDataArr:selArr
            },
            height: 600,
            onclose:function(){
                if(this.returnValue){
                    var data=this.returnValue;
                    var table = $("#table").getRowData();
                    console.log(table)
                    for(var i = 0 ; i < data.length; i++ ){
                        if(!fidInArr(table,data[i].supplierOrganId)){
                            $('#table').XGrid("addRowData",data[i]);
                        }
                    }
                }
            }
        }).showModal();
    });
    
    
    
    
    
    

    /* 删除行 事件*/
    $("#delRow").on("click",function(){
        var selObj = $('#table').XGrid("getSeleRow");
        if(selObj){
            $('#table').XGrid("delRowData",selObj.id);
        }else{
            utils.dialog({"content":"请选择删除行！","timeout":2000}).show();
        }
    });

});


function examine(auditStatus,auditOpinion){
    var dataid = Number($('#id').val());
    var taskId= Number($('#taskId').val());
    var supplierDisableApprovalRecordVO = $("#recordVo").serializeToJSON();
    supplierDisableApprovalRecordVO.auditOpinion= auditOpinion;
    supplierDisableApprovalRecordVO.taskId= taskId;
    var table = $("#table").getRowData();
    for(var i = 0 ; i < table.length; i++  ){
        if(table[i].appReason==""||table[i].appReason==null){
            utils.dialog({content: '请填写申请原因！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    }
    for(var i = 0 ; i < table.length; i++ ){
        table[i].supplierBusiType=table[i].isLock;
    }
    supplierDisableApprovalRecordVO.id=dataid;
    supplierDisableApprovalRecordVO.supplierDisableApprovalRecordDetailVOList = table;
    supplierDisableApprovalRecordVO.auditStatus =auditStatus;
    $.ajax({
        url: "/proxy-supplier/supplier/supplierLockApprovalRecord/passSupplierLockApprovalRecord",
        data: JSON.stringify(supplierDisableApprovalRecordVO),
        type: "post",
        dataType: 'json',
        contentType: "application/json",
        success: function (data) {
            if(data.staus !=0){
            	if("3"==auditStatus){//审核通过
            		 utils.dialog({
                         title: "提示",
                         content: '提交审核成功！',
                         width:300,
                         height:30,
                         okValue: '确定',
                         ok: function () {
                             utils.closeTab();
                         }
                     }).showModal();
            		 $(".ui-dialog-close").hide();
                     return false;
            	}else if("4"==auditStatus){//审核不通过
            		 utils.dialog({
                         title: "提示",
                         content: '驳回成功！',
                         width:300,
                         height:30,
                         okValue: '确定',
                         ok: function () {
                             utils.closeTab();
                         }
                     }).showModal();
            		 $(".ui-dialog-close").hide();
                     return false;
            	}else{
            		 var processInstaId=$("#processId").val();
                     initApprovalFlowChart(processInstaId)
            		 utils.dialog({
                         // title: "提示",
                         content: data.message,
                         quickClose:true
                         /*width:300,
                         height:30,
                         okValue: '确定',
                         ok: function () {
                             utils.closeTab();
                         }*/
                     }).showModal();
                     $(".ui-dialog-close").hide();
                     return false;
            	}
               
            }else{
            	 utils.dialog({
                     //title: "提示",
                     content: data.message,
                     quickClose:true
                    /* width:300,
                     height:30,
                     okValue: '确定',
                     ok: function () {
                         utils.closeTab();
                     }*/
                 }).showModal();
            	 $(".ui-dialog-close").hide();
                 return false;
            }
        },
        error: function () {
            utils.dialog({content: '内部出错！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}


//数组中查找id
function fidInArr(arr,supplierOrganId){
    for(var i=0;i<arr.length;i++)
    {
        if(arr[i].supplierOrganId == supplierOrganId)
        {
            return true;
        }
    }
    return false;
}
/**
 * 流程图显示
 */
function  initApprovalFlowChart(processInstaId) {
    var key = $("#key").val();
    var url = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+processInstaId
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}


$("#submitAssert").click(function () {
    var status=this.getAttribute("status");
    $('#auditOpinion1').val("");
//	$("#auditOpinionStyle").attr("style", "display: none;");
	$('#auditOpinionStyle').hide();
    utils.dialog({
        title: '供应商锁定管理审核',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            examine(3,$("#auditOpinion1").val())
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();

})
//重新提交
$("#reSubmitAssert").click(function () {
    examine(3,$("#auditOpinion1").val())
})



$("#submitUnAssert").click(function () {
    var status=this.getAttribute("status");
    $('#auditOpinion1').val("");
//    $("#auditOpinionStyle").attr("style", "opacity: 1;");
     $('#auditOpinionStyle').show();
    utils.dialog({
        title: '供应商锁定管理审核',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
        	var a = $("#auditOpinion1").val();
            if ($("#auditOpinion1").val()==""){
            	 utils.dialog({content: '审核不通过，审核意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            examine(4,$("#auditOpinion1").val())

        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();

})


$("#close").click(function () {
    var taskId = $("#taskId").val()
        close(taskId);
     }
)

/**
 * 关闭审核流
 */
 function close(taskId){
    //获取审核流程数据
    var dataid = Number($('#id').val());
   //关闭审核
	utils.dialog({
        title: "供应商锁定管理审核",
        width:300,
        height:30,
        content: "确定关闭此申请？",
        okValue: '确定',
        ok: function () {
            $.ajax({
                type: "POST",
                url: "/proxy-supplier/supplier/supplierLockApprovalRecord/closeProcess?taskId="+taskId+"&id="+dataid,
                async: false,
                success: function (data) {
                    if(data.staus !=0){
                    	 utils.dialog({
                             title: "提示",
                             content: data.message,
                             width:300,
                             height:30,
                             okValue: '确定',
                             ok: function () {
                                 utils.closeTab();
                             }
                         }).showModal();
                         $(".ui-dialog-close").hide();
                         return false;
                    }else{
                    	 utils.dialog({
                             // title: "提示",
                             content: data.message,
                             quickClose:true
                             /*width:300,
                             height:30,
                             okValue: '确定',
                             ok: function () {
                                 utils.closeTab();
                             }*/
                         }).showModal();
                         $(".ui-dialog-close").hide();
                         return false;
                    }
                },
                error: function () {
                    utils.dialog({content: '内部出错！', quickClose: true, timeout: 2000}).showModal();
                }
            });
        },
        cancelValue: '取消',
        cancel: function () {
        }
    }).showModal();
}


/* 弹框 */
function showDialog(id){
    dialog({
        url: '/proxy-supplier/supplier/supplierLockApprovalRecord/toSearchList',//弹框页面请求地址
        title: '搜索供应商',
        width: 1000,
        height: 800,
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                var table = $("#table").getRowData();
                for(var i = 0 ; i < table.length; i++ ){
                    if(data.keyId ==table[i].keyId ){
                        utils.dialog({content: '请不要重复选择供应商！', quickClose: true, timeout: 2000}).showModal();
                        return;
                    }
                }
                $('#table').XGrid("setRowData",id,data);
            }
        }
    }).showModal();
}