$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    /* 日期初始化 */
    z_utils.initDate('begint', 'endt')
    /* 合计计算  */
    var totalTable = z_utils.totalTable;

    /*查询页面总计*/
    getTotalNum ();
    /* table_a */
    //data
    var grid_dataY = [];
    var g_item = {
        text1: "2",
        text2: "",
        text3: "",
        text4: "",
        text5: "",


        text6: "",
        text7: "",
        text8: "",
        text9: "",
        text10: "",
        text11: "",
        text12: "2",
        text13: "",
        text14: "",
        text15: "4",
        text16: "6",
        text17: "",
        text18: "",
        text19: "",
        text20: "",
    };
    var g_item1 = {
        text1: "1",
        text2: "",
        text3: "",
        text4: "",
        text5: "",
        text6: "",
        text7: "",
        text8: "",
        text9: "",
        text10: "",
        text11: "",
        text12: "2",
        text13: "",
        text14: "",
        text15: "4",
        text16: "6",
        text17: "",
        text18: "",
        text19: "待处理",
        text20: ""
    };
    for (var i = 0; i < 20; i++) {
        if (i === 0) {
            grid_dataY.push(g_item1);
        } else {
            grid_dataY.push(g_item);
        }
    }
    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d + ' '+ h+':'+minute+':'+second;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }

    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/
    var colName = ['id', '商品编码', '通用名称','商品规格', '剂型', '单位', '生产厂家', '日期','批号','生产日期',  '有效期至', '灭菌批号','数量','批准文号',
        '单据编号'
    ];
    var colModel=[ {
        name: 'id',
        index: 'id',
        key: true,
        hidden: true,
        hidegrid: true
    },{
        name: 'drugCode',
        index: 'drugCode'
    },{
        name: 'commonName',
        index: 'commonName',
        width: 220
    },{
        name: 'specifications',
        index: 'specifications'
    },{
        name: 'dosageFormVal',
        index: 'dosageFormVal' // 剂型
    },{
        name: 'packingUnitVal',
        index: 'packingUnitVal' //包装单位
    },{
        name: 'manufName',
        index: 'manufName'  //生产厂家
    },{
        name: 'postDate',
        index: 'postDate'  //（单据）日期
    },{
        name: 'batchNum',
        index: 'batchNum'
    }, {
        name: 'productDate',
        index: 'productDate'
    },{
        name: 'validateDate',
        index: 'validateDate'
    },{
        name: 'sterilizeBatchNum',
        index: 'sterilizeBatchNum'  //灭菌批号
    },{
        name: 'amountIn',
        index: 'amountIn',   //
        formatter: function (e) {
            return Number(e).toFixed(2);
        }
    },{
        name: 'approvalNumber',
        index: 'approvalNumber' //批准文号
    },{
        name: 'orderCode',
        index: 'orderCode',
        width: 200
    }/*,{
        name: 'moveStorageReason',
        index: 'moveStorageReason',//移库原因 no
        hidden:true

    }*/
    ];



    $('#table_a').XGrid({
        url: "/proxy-storage/storage/ledger/listUnqualifiedGoodsList?startDate=" + $('#begint').val() + "&endDate" + $('#endt').val(),
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'id',
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function () {
           /* /!* 合计行 *!/
            var data = $(this).XGrid('getRowData');
            // var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut','inventoryAmount','inventorySum'];
            var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });*/
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            //console.log(id, dom, obj, index, event)
        }
    });


    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        $('#table_a').XGrid('filterTableHead');
    })

    /* 商品名称 搜索提示（只显示5条） */
    var ts1 = [{
        value: 'Andorra',
        data: 'AD'
    },
        {
            value: 'Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '2Andorra',
            data: 'AD'
        },
        {
            value: '2Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '3Andorra',
            data: 'AD'
        },
        {
            value: '3Zimbabwe',
            data: 'ZZ'
        }
    ];
    $('#input_goodName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + $("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#input_goodName").val("");
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });
    /* 查询 */
    $('#searchBtn').on('click', function () {
        $('#table_a').XGrid('setGridParam', {
            url: "/proxy-storage/storage/ledger/listUnqualifiedGoodsList",
            postData: {
                drugCode: $('#drugCode').val(),
                batchNum: $('#batchNum').val(),
                startDate: $('#begint').val(),
                endDate: $('#endt').val(),
                commonName: $("#commonName").val(),

            }
        }).trigger('reloadGrid');
        $("#table_a").XGrid("clearGridData");
    });

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#table_a').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            var len = Number($('#totalPageNum').text());
            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        var value=item[val];
                        if(('priceIn'==val||'priceOut'==val||'taxCostAmount'==val)&&'--'==value){
                            value='0.00'
                        }
                        new_item[val] = value;
                    })
                    return new_item
                })
                
                len = data.length;
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            console.log(colName);

            //获取form数据
            var formData = {
                drugCode: $('#drugCode').val(),
                batchNum: $('#batchNum').val(),
                startDate: $('#begint').val(),
                endDate: $('#endt').val(),
                colName: colName,
                colNameDesc: colNameDesc,
                selectData: data,
                productCode: $("#productCode").val(),

            }
            // 是否超出限制    
            utils.exportAstrictHandle('table_a', len, 1).then( () => {            	
                return false;
            }).catch( () => {            	
            	httpPost("/proxy-storage/storage/ledger/exportUnqualifiedGoodsList", formData);
            })             
        });
    });
    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#drugCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })
})
//查询总合计数目
function getTotalNum () {
    //加载总数量
    $.ajax({
        url: '/proxy-storage/storage/ledger/selectStorageLedgerViewStatisticsTotal',
        dataType: 'json',
        timeout: 10000, //6000
        data:{
            drugCode: $('#drugCode').val(),
            batchNum: $('#batchNum').val(),
            startDate: $('#begint').val(),
            endDate: $('#endt').val(),
            moveType: $("#moveType").find("option:selected").val(),
            drugClass: $("#drugClass").find("option:selected").val(),
            orderCode: $("#orderCode").val(),
            storageType: $("#storageType").val(),
            oldProductCode:$("#oldProductCode").val(),
            manufName: $("#manufName").val(),
            productCode: $("#productCode").val(),
            companyName: $("#companyName").val()
        },
        success: function (data) {
            // alert(data.code);
            if (data.code==0){
                var static = data.result;
                $("#totalAmountIn").text(Number(static.totalAmountIn).toFixed(2));
                $("#totalNotaxSumIn").text(Number(static.totalNotaxSumIn).toFixed(2));
                $("#totalAmountOut").text(Number(static.totalAmountOut).toFixed(2));
                $("#totalNotaxSumOut").text(Number(static.totalNotaxSumOut).toFixed(2));
                // $("#totalInventoryAmount").text(Number(static.totalInventoryAmount).toFixed(2));
                // $("#totalInventorySum").text(Number(static.totalInventorySum).toFixed(2));
            }else {
                $("#totalAmountIn").text('0.00');
                $("#totalNotaxSumIn").text("0.00");
                $("#totalAmountOut").text("0.00");
                $("#totalNotaxSumOut").text("0.00");
                // $("#totalInventoryAmount").text("0.00");
                // $("#totalInventorySum").text("0.00");
            }
        },
        error: function () {
            $("#totalAmountIn").text('0.00');
            $("#totalNotaxSumIn").text("0.00");
            $("#totalAmountOut").text("0.00");
            $("#totalNotaxSumOut").text("0.00");
            // $("#totalInventoryAmount").text("0.00");
            // $("#totalInventorySum").text("0.00");
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}