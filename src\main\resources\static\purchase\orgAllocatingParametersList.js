let fristColNames=['id','调拨方向','渠道','调入机构','调出机构','订单属性','计划单号','调拨采购单号','采购状态','调出销售单号','调出销售出库单号','销售单状态','采购单申请总金额','预估调拨费用','调拨发货总数量','调拨收货总数量','调拨采购单创建人','调拨采购单创建时间','调拨销售单出库时间'];

let fristcolModel = [
    {
        name :'id',
        index: 'id', //索引。其和后台交互的参数为sidx
        hidden:true,
        hidegrid: true,
        key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    }, {
        name :'allotDirection',
        index: 'allotDirection'
    }, {
        name :'channelId',
        index: 'channelId'
    }, {
        name :'callInOrg',
        index: 'callInOrg'
    }, {
        name :'callOutOrg',
        index: 'callOutOrg'
    }, {
        name :'centralizedPurchaseName',
        index: 'centralizedPurchaseName'
    }, {
        name :'planOrderNo',
        index: 'planOrderNo',
    }, {
        name :'orderNo',
        index: 'orderNo'
    }, {
        name :'orderStatus',
        index: 'orderStatus'
    }, {
        name :'callOutOrgSaleOrderNo',
        index: 'callOutOrgSaleOrderNo'
    }, {
        name :'outOrderCode',//调出销售出库单号
        index: 'outOrderCode'
    }, {
        name :'saleStatus',
        index: 'saleStatus'
    }, {
        name :'orderPriceTaxSum',
        index: 'orderPriceTaxSum'
    }, {
        name :'forecastAllotCost',
        index: 'forecastAllotCost'
    }, {
        name :'orderSendAmount',
        index: 'orderSendAmount'
    }, {
        name :'orderTakeAmount',
        index: 'orderTakeAmount'
    }, {
        name :'createUserName',
        index: 'createUserName'
    }, {
        name :'createTime',
        index: 'createTime'
    }, {
        name :'saleOutTime',
        index: 'saleOutTime'
    }, {
        name: 'centralizedPurchaseType',// 集采属性
        index: 'centralizedPurchaseType',
        hidden: true,
        hidegrid: true
    }
];

let secondColNames=['id','调拨方向','渠道','调入机构','调出机构','调拨采购单号','调出销售单号','调出销售出库单号','调出销售单状态','计划单号','商品编码','商品名称','规格','生产厂家','采购单价','行含税总金额','行总税额','采购数量','销售发货数量','销售退回数量','采购入库数量','拒收数量','在途数量'];

let secondcolModel=[
    {
        name :'id',
        index: 'id', //索引。其和后台交互的参数为sidx
        hidden:true,
        hidegrid: true,
        key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    }, {
        name :'allotDirection',
        index: 'allotDirection',
    }, {
        name :'channelId',
        index: 'channelId',
    }, {
        name :'callInOrg',
        index: 'callInOrg',
    }, {
        name :'callOutOrg',
        index: 'callOutOrg',
    }, {
        name :'orderNo',
        index: 'orderNo',
    }, {
        name :'callOutOrgSaleOrderNo',
        index: 'callOutOrgSaleOrderNo',
    }, {
        name :'outOrderCode',//调出销售出库单号
        index: 'outOrderCode'
    }, {
        name :'saleStatus',
        index: 'saleStatus',
    }, {
        name :'planOrderNo',
        index: 'planOrderNo',
    }, {
        name :'productCode',
        index: 'productCode',
    }, {
        name :'productName',
        index: 'productName',
    }, {
        name :'productSpecification',
        index: 'productSpecification',
    }, {
        name :'productProduceFactory',
        index: 'productProduceFactory',
    }, {
        name :'productContainTaxPrice',
        index: 'productContainTaxPrice',
    }, {
        name :'productContainTaxMoney',
        index: 'productContainTaxMoney',
    }, {
        name :'productTaxMoney',
        index: 'productTaxMoney',
    }, {
        name :'productCount',
        index: 'productCount',
    }, {
        name :'saleSendCount',
        index: 'saleSendCount',
    }, {
        name :'saleReturnCount',
        index: 'saleReturnCount',
    }, {
        name :'productPackInStoreCount',
        index: 'productPackInStoreCount',
    }, {
        name :'productPackRejectStoreCount',
        index: 'productPackRejectStoreCount',
    }, {
        name :'productPackOnWayCount',
        index: 'productPackOnWayCount',
    }
];
// /**
//  * 订单属性
//  * */
// function addOrderAttribute() {
//     var  data = [{value:'0', name:'统筹集采'},{value:'1', name:'统筹统采'}]
//     $.each(data, function (infoIndex, info) {  //循环遍历后台传过来的json数据
//         $("#centralizedPurchaseType").append("<option value='" + info["value"] + "'>" + info["name"] + "</option>");
//         // $("#test").append("<option value='5'>测试5</option>");   //为Select追加一个Option(下拉项)
//         // $("#test").prepend("<option value='0'>测试6</option>");   //为Select插入一个Option(第一个位置)
//     });
//     // $.ajax({
//     //     url: '<%=root%>/employ/bmfwAction!getBillCompanyBilProvCdAndType',
//     //     type: 'post',
//     //     data: 'billStyle=' + billStyle + '&provCd=' + provCd,
//     //     success: function (data) {
//     //         $.each(data, function (infoIndex, info) {  //循环遍历后台传过来的json数据
//     //             $("#test").append("<option value='" + info["sex"] + "'>" + info["name"] + "</option>");
//     //             // $("#test").append("<option value='5'>测试5</option>");   //为Select追加一个Option(下拉项)
//     //             // $("#test").prepend("<option value='0'>测试6</option>");   //为Select插入一个Option(第一个位置)
//     //         });
//     //     }
//     // });
//     // $.getJSON("jsontest.json", function (data) {
//     //     $.each(data, function (infoIndex, info) {
//     //         $("#test").append("<option value='" + info["sex"] + "'>" + info["name"] + "</option>");
//     //     })
//     //     console.log(strHtml);
//     // })
// }
$(function () {
    // addOrderAttribute()
    initDate();
    var sysOrgCode = $("#sysOrgCode").val();
    if ($('#sysOrgCode').val() != '001') {
        $(`#callInOrg`).val($('#sysOrgCode').val())
    }
    $('#seleteTips>li').on('click', function () {
        var $this = $(this), $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        //$nav_content.children('div').eq($this.index()).show().siblings().hide();
        $nav_content.children('div').eq($this.index()).css('display', 'flex').siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');
        // $('#X_Table').XGrid("clearGridData");
        // $('#X_Table2').XGrid("clearGridData");
        btn_search();
    });


    $('#X_Table').XGrid({
        url: "/proxy-purchase/purchase/orgAllocatingParameters/selectAllotOrderPage",
        postData: $("#saleOrderForm").serializeToJSON(),
        colNames:fristColNames ,
        name: 'id', //与反回的json数据中key值对应
        colModel: fristcolModel,
        altRows: true, //设置为交替行表格,默认为false
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        pager: '#grid_page',
        ondblClickRow: function (id, dom, obj, index, event) {
            // this.returnValue = obj;
            // utils.openTabs('purchaseOrderDetail','采购订单详情','/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNo='+obj.orderNo+'&type=1');
            // return obj;
            $('#seleteTips>li').eq(1).click()
            let formData = $("#saleOrderForm").serializeToJSON()
            formData['orderNo'] = obj.orderNo
            $('#X_Table2').setGridParam({
                url: '/proxy-purchase/purchase/orgAllocatingParameters/selectAllotOrderProductPage',
                postData: formData,
                page: 1
            }).trigger('reloadGrid');
        }
    });
    $('#X_Table2').XGrid({
        url: "/proxy-purchase/purchase/orgAllocatingParameters/selectAllotOrderProductPage",
        postData: $("#saleOrderForm").serializeToJSON(),
        colNames:secondColNames ,
        name: 'id', //与反回的json数据中key值对应
        colModel: secondcolModel,
        altRows: true, //设置为交替行表格,默认为false
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        pager: '#grid_pageTwo',
        ondblClickRow: function (id, dom, obj, index, event) {
            this.returnValue = obj;
            utils.openTabs('purchaseOrderDetail','采购订单详情','/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNo='+obj.orderNo+'&type=1');
            return obj;
        }
    });

    //导出
    $('#exportBtn').on('click', function () {
        let tableInd = getTabInd()
        let TableId ="X_Table",
            pagenum = Number($('#grid_page #totalPageNum').text()),
            moduleName = 'purchaseAllotOrder',
            taskCode = '1014';
            fileName = '机构间调拨单据列表'
        if(tableInd == 1){
            TableId ="X_Table2";
            pagenum = Number($('#grid_pageTwo #totalPageNum').text())
            moduleName = 'allotOrderProduct',
            fileName = '机构间调拨商品明细'
            taskCode = '1013';
        }
        utils.exportAstrictHandle(TableId, pagenum).then(()=>{
            return false;
        }).catch(()=> {
            // let tableId = $('#nav_content .active .XGridBody table').attr('id');
            z_utils.exportTable(TableId, function (that) {
                //需要导出项
                let colName = [];
                let colNameDesc = [];
                $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                    if ($(this).prop("checked")) {
                        colName.push($(this).attr('name'))
                        colNameDesc.push($(this).parent().text());
                    }
                });

                //获取form数据
                let formData = $('#saleOrderForm').serializeToJSON();
                formData["colName"] = colName;
                formData["colNameDesc"] = colNameDesc;

                if (colName.length == 0) {
                    utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                parent.showLoading({hideTime: 999999999});

                let colNames= colName.join(",")
                let colNameDescs= colNameDesc.join(",")
                let choiceStatuses = $("#choiceStatus").val();
                let choiceStr = "";
                if(choiceStatuses != 'undefined' && choiceStatuses != null && choiceStatuses != '' && choiceStatuses.length != 0){
                    choiceStr =  choiceStatuses.join(",");
                }
                var createTimeStart = $('#beginTime').val();
                if(createTimeStart != ""){
                    createTimeStart = createTimeStart + " 00:00:00"
                }
                var createTimeEnd = $('#endTime').val();
                if(createTimeEnd != ""){
                    createTimeEnd = createTimeEnd + " 59:59:59";
                }
                let formData2 = {
                    moduleName: moduleName,
                    taskCode: taskCode,
                    colName: colNames,
                    colNameDesc: colNameDescs,
                    fileName: fileName,
                    check: "check",
                    exportParams: {
                        allotDirectionCode: $('#allotDirectionCode').val(),
                        orderNo: $('#orderNo').val(),
                        planOrderNo: $('#planOrderNo').val(),
                        paramProductName: $('#productNameKeyword').val(),
                        callOutOrgSaleOrderNo: $('#callOutOrgSaleOrderNo').val(),
                        callInOrg: $('#callInOrg').val(),
                        centralizedPurchaseType: $('#centralizedPurchaseType').val(),
                        createTimeStart: createTimeStart,
                        createTimeEnd: createTimeEnd,
                        callOutOrg: $('#callOutOrg').val(),
                        choiceStatus:choiceStr,
                    }
                };
                utils.dialog({
                    title: '温馨提示',
                    content: '导出任务提交成功后页面将关闭，是否确认导出？',
                    okValue: '确定',
                    ok: function () {
                        $.ajax({
                            url: "/proxy-purchase/purchase/commonExport/commonCommitExportTask",
                            type: 'post',
                            dataType: 'json',
                            data: {
                                "data":JSON.stringify(formData2)
                            },
                            success: function (res) {
                                if (res) {
                                    if (res.code === 0) {
                                        utils.dialog({
                                            title: '温馨提示',
                                            content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                            okValue: '确定',
                                            ok: function () {

                                            }
                                        }).showModal()
                                    } else {
                                        utils.dialog({
                                            title: '温馨提示',
                                            content: res.msg,
                                            okValue: '确定',
                                            ok: function () { }
                                        }).showModal()
                                    }
                                }
                            }
                        });
                    }
                }).showModal()
                parent.hideLoading();
            });
        })
    })

    $('#productNameKeyword').Autocomplete({
        // purchase/supplier/findPageInfoByMnemonInfo?orgCode=006&keyword=保妇康栓
        // serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?&pageSize=5 ', //异步请求
        serviceUrl: '/proxy-purchase/purchase/supplier/findPageInfoByMnemonInfo?orgCode='+sysOrgCode, //异步请求
        paramName: 'keyword',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.productName +"\xa0\xa0\xa0"+ dataItem.specifications +"\xa0\xa0\xa0"+ dataItem.manufacturerName, data: dataItem.productCode};

                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#productCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            //console.log(query, suggestions);
            if (suggestions.length < 1) {
                $('productNameKeyword').val('')
                $("#productCode").val('')
            }
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#productCode").val("");
            $("#productNameKeyword").val("");
            //$("#s_commodity").trigger("click");
            //console.log(params, suggestions);
            //console.log('没选中回调函数');
        }
    });
})
// 调入 调出机构
function changeOrg(el) {
    let curOrgCode = $('#sysOrgCode').val()
    let key = $(el).find('option:selected').attr('data-type')
    let typeAry = ['In','Out']
    let otherType = typeAry.filter((item, index) => item != key )
    let otherTypeIndex = typeAry.indexOf(otherType.join())
    $(`#call${key}Org`).val(curOrgCode)
    $(`#call${key}Org`).prop('disabled', true)
    $(`#call${typeAry[otherTypeIndex]}Org`).prop('disabled', false)
}
function  btn_output_list(){
    let tableInd = getTabInd()
        moduleName = 'purchaseAllotOrder',
        taskCode = '1014';
    if(tableInd == 1) {
        moduleName = 'allotOrderProduct',
            taskCode = '1013';
    }
    utils.dialog({
        title: '导出列表',
        url: '/proxy-purchase/purchase/commonExport/toExportList?moduleName='+moduleName+'&taskCode='+taskCode,
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {}
    }).showModal();
    return false;
}

//查询
function btn_search() {
    let sels = $("#saleOrderForm select")
    let selsAry = []
    $(sels).each((index, item ) => {
        if($(item).prop('disabled')) {
            $(item).prop('disabled', false)
            selsAry.push(index)
        }
    })
    var formData = $("#saleOrderForm").serializeToJSON();
    var option = [
        {id: '#X_Table', url: '/proxy-purchase/purchase/orgAllocatingParameters/selectAllotOrderPage'},
        {id: '#X_Table2', url: '/proxy-purchase/purchase/orgAllocatingParameters/selectAllotOrderProductPage'},
    ];
    var index_a = getTabInd();
    $(option[index_a].id).setGridParam({
        url: option[index_a].url,
        postData: formData,
        page: 1
    }).trigger('reloadGrid');
   if (selsAry.length != 0) {
       selsAry.forEach((item, index) => {
           $(sels[item]).prop('disabled', true)
       })
   }
}
function getTabInd() {
    let _ind = 0;
    for (var i = 0; i < $('#seleteTips>li').length; i++) {
        if ($('#seleteTips li').eq(i).hasClass('active')) {
            _ind = $('#seleteTips li').eq(i).index();
        }
    }
    return _ind;
}
function initDate(param) {
    var oldTimes = new Date();
    // var newTimes = new Date(oldTimes - 30*24*3600*1000);
    // 开始时间为当前时间减6天
    let yearN = oldTimes.getFullYear();
    let monthN = oldTimes.getMonth() -2;
    let dateN = oldTimes.getDate();
    // 结束时间为当前时间
    let yearO = oldTimes.getFullYear();
    let monthO = oldTimes.getMonth() + 1;
    let dateO = oldTimes.getDate();
    //1位数加0
    monthN = monthN.toString().length <= 1 ? '0' + monthN : monthN;
    monthO = monthO.toString().length <= 1 ? '0' + monthO : monthO;
    dateN = dateN.toString().length <= 1 ? '0' + dateN : dateN;
    dateO = dateO.toString().length <= 1 ? '0' + dateO : dateO;
    //设置开始时间为当前时间减6天，结束时间为当天23:59:59
    $('input[name=createTimeStart]').val(yearN + '-' + monthN + '-' + dateN);
    $('input[name=createTimeEnd]').val(yearO + '-' + monthO + '-' + dateO);
}

function getMaxDate() {
    const curDate = new Date()
    let curY = curDate.getFullYear();
    let curM = curDate.getMonth() +1;
    let curD = curDate.getDate();
    const curTimeStamp = new Date(curY + '-'+curM+'-'+curD + ' 00:00:00').getTime()
    // 开始日期结束日期间距三个月
    // 开始日期往后加三个月
    let ary = [new Date($('#beginTime').val()).getMonth() + 1+1, new Date($('#beginTime').val()).getMonth() + 1+2, new Date($('#beginTime').val()).getMonth() + 1+3]

    let dateTotal = ary.reduce((total, cur) => {
        return total + getMonthDate(curY, cur)
    },0)

    // 开始日期
    const beginTimeStamp = new Date($('#beginTime').val() + ' 00:00:00').getTime() + (dateTotal - 1) * 24 * 3600 * 1000
    return curTimeStamp > beginTimeStamp ? getYMD(beginTimeStamp) : ( curY + '-'+curM+'-'+curD)
}
function getYMD(timestamp) {
    const date = new Date(timestamp)
    let _y = date.getFullYear();
    let _m = date.getMonth() + 1 <10 ? '0'+(date.getMonth() + 1) : date.getMonth() + 1;
    let _d = date.getDate() < 10 ? '0'+date.getDate() : date.getDate();
    return _y +'-'+_m+'-'+_d
}
function getMonthDate(year, month) {
    let d = new Date(year, month, 0);
    return d.getDate();
}
