(function(t){function e(e){for(var n,r,s=e[0],l=e[1],c=e[2],p=0,d=[];p<s.length;p++)r=s[p],Object.prototype.hasOwnProperty.call(i,r)&&i[r]&&d.push(i[r][0]),i[r]=0;for(n in l)Object.prototype.hasOwnProperty.call(l,n)&&(t[n]=l[n]);u&&u(e);while(d.length)d.shift()();return o.push.apply(o,c||[]),a()}function a(){for(var t,e=0;e<o.length;e++){for(var a=o[e],n=!0,s=1;s<a.length;s++){var l=a[s];0!==i[l]&&(n=!1)}n&&(o.splice(e--,1),t=r(r.s=a[0]))}return t}var n={},i={app:0},o=[];function r(e){if(n[e])return n[e].exports;var a=n[e]={i:e,l:!1,exports:{}};return t[e].call(a.exports,a,a.exports,r),a.l=!0,a.exports}r.m=t,r.c=n,r.d=function(t,e,a){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},r.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)r.d(a,n,function(e){return t[e]}.bind(null,n));return a},r.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="/";var s=window["webpackJsonp"]=window["webpackJsonp"]||[],l=s.push.bind(s);s.push=e,s=s.slice();for(var c=0;c<s.length;c++)e(s[c]);var u=l;o.push([0,"chunk-vendors"]),a()})({0:function(t,e,a){t.exports=a("56d7")},"21bb":function(t,e,a){"use strict";a("2dad")},"2dad":function(t,e,a){},"37fd":function(t,e,a){"use strict";a.r(e),e["default"]={get:{claim:"/proxy-sysmanage/sysmanage/process/claim"},post:{listPage:"/proxy-sysmanage/sysmanage/process/listPage",approvePayrequestInfo:"finance/purchase/payrequestinfo/approvePayrequestInfo"}}},"4de4e":function(t,e,a){var n={"./home.js":"37fd"};function i(t){var e=o(t);return a(e)}function o(t){if(!a.o(n,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return n[t]}i.keys=function(){return Object.keys(n)},i.resolve=o,t.exports=i,i.id="4de4e"},"56d7":function(t,e,a){"use strict";a.r(e);a("e623"),a("e379"),a("5dc8"),a("37e1");var n=a("2b0e"),i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"app"}},[a("Header",{staticClass:"main-header",staticStyle:{"z-index":"1005"},attrs:{title:t.title},on:{preClick:t.goBack}},[a("template",{slot:"prefix"},[a("van-icon",{attrs:{name:"arrow-left"}})],1)],2),a("router-view")],1)},o=[],r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"header"},[a("div",{staticClass:"prefix",on:{click:t.preClick}},[t._t("prefix")],2),a("div",{staticClass:"title",on:{click:t.titleClick}},[t._t("default",[t._v(t._s(t.title))])],2),a("div",{staticClass:"suffix",on:{click:t.sufClick}},[t._t("suffix")],2)])},s=[],l={props:["title"],methods:{preClick:function(){this.$emit("preClick")},titleClick:function(){this.$emit("titleClick")},sufClick:function(){this.$emit("sufClick")}}},c=l,u=a("2877"),p=Object(u["a"])(c,r,s,!1,null,null,null),d=p.exports,f={name:"App",computed:{title:function(){return this.$route.meta.title}},components:{Header:d},methods:{goBack:function(){this.$router.go(-1)}}},h=f,v=Object(u["a"])(h,i,o,!1,null,null,null),g=v.exports,m=a("8c4f"),b=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("van-pull-refresh",{on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[a("div",{staticStyle:{"padding-top":"1rem","min-height":"calc(100vh - 4.5rem)"}},[a("van-list",{attrs:{finished:t.finished,"finished-text":"没有更多了"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[t.taskList.length&&t.taskList.length>0?a("div",[t._l(t.taskList,(function(e){return[a("div",{key:e.approvalProcessId,staticClass:"row"},[a("van-button",{staticStyle:{border:"0",height:"100%",padding:"15px"},attrs:{icon:e.taskId!==t.approveData.taskId?"circle":"passed"},on:{click:function(a){return t.change(e)}}}),a("div",{staticStyle:{flex:"1",padding:"1rem","padding-left":"0"},on:{click:function(a){return t.goDetail(e)}}},[a("h3",{staticStyle:{"font-size":".9rem","margin-bottom":"1rem",color:"#222"}},[t._v(" "+t._s("采购付款申请"+e.applyMoney+"元")+" ")]),a("div",{staticStyle:{display:"flex","justify-content":"space-between",color:"#999","font-size":".8rem"}},[a("div",[t._v(t._s("申请人:"+e.taskCreater))]),a("div",[t._v(" "+t._s(t.dateToStr(e.taskCreateTime/1e3,!0,!0))+" ")])])])],1)]}))],2):t._e()])],1)]),t.taskList.length>0&&!t.dialogShow?a("div",{staticStyle:{position:"fixed",right:"0",left:"0",bottom:"0","z-index":"9999"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-around"}},[a("van-button",{staticStyle:{width:"40%","border-radius":"5rem"},attrs:{type:"primary",block:""},on:{click:function(e){return t.approve(2)}}},[t._v("不通过")]),a("van-button",{staticStyle:{width:"40%","border-radius":"5rem"},attrs:{type:"primary",block:""},on:{click:function(e){return t.approve(1)}}},[t._v("通过")])],1)]):t._e(),a("van-dialog",{attrs:{title:"确定驳回?","show-cancel-button":""},on:{confirm:t.claim},model:{value:t.dialogShow,callback:function(e){t.dialogShow=e},expression:"dialogShow"}},[a("van-field",{attrs:{autosize:"",name:"用户名",label:"",type:"textarea",rows:"2",required:"",placeholder:"驳回原因（必填）"},model:{value:t.approveData.message,callback:function(e){t.$set(t.approveData,"message",e)},expression:"approveData.message"}})],1)],1)},k=[],y=(a("a15b"),a("d3b7"),a("99af"),a("d399")),_={data:function(){return{checked:!1,dialogShow:!1,taskList:[],loading:!1,finished:!1,isLoading:!1,approveData:{approveStatus:"",message:"",callFlag:1},pageData:{sort:"asc",applyModule:"finance",pageNum:1,pageSize:20,id:"",auditStartTime:"",auditEndTime:"",taskCreater:"",taskOrgCode:"",approvalProcessKey:"",taskType:0,number:"",uniteQuery:"",indexRemindTask:""},total:0}},created:function(){},components:{},methods:{dateToStr:function(t,e,a){var n=new Date(1e3*t)||new Date,i=n.getFullYear(),o=10>n.getMonth()+1&&a?"0"+(n.getMonth()+1):n.getMonth()+1,r=10>n.getDate()?"0"+n.getDate():n.getDate(),s=10>n.getHours()?"0"+n.getHours():n.getHours(),l=10>n.getMinutes()?"0"+n.getMinutes():n.getMinutes(),c=10>n.getSeconds()?"0"+n.getSeconds():n.getSeconds();return e&&a?[i,o,r].join("-")+" "+[s,l,c].join(":"):!e&&a?[i,o,r].join("-"):e&&!a?[o,r].join("-")+" "+[s,l,c].join(":"):e||a?void 0:[o,r].join(".")},onRefresh:function(){this.pageData.pageNum=1,this.isLoading=!1,this.taskList=[],this.loading=!1,this.finished=!1,this.listPage()},onLoad:function(){var t=this;setTimeout((function(){t.listPage(),t.loading=!0}),500)},listPage:function(){var t=this;_api.home.listPage(this.pageData).then((function(e){t.loading=!1,0===e.code&&e.result.list&&e.result.list.length>0?(t.finished||(t.taskList=t.taskList.concat(e.result.list),t.pageData.pageNum++),t.total=e.result.total,(t.taskList.length===e.result.total||t.taskList.length>e.result.total)&&(t.finished=!0)):t.finished=!0})).finally((function(){t.loading=!1}))},change:function(t){var e=t.number,a=t.taskId,n=t.approvalProcessId;t.taskId===this.approveData.taskId?this.approveData={approveStatus:"",message:"",callFlag:1}:this.approveData={billNo:e,message:"",taskId:a,approvalProcessId:n},this.taskList=[].concat(this.taskList)},approve:function(t){this.approveData.approveStatus=t,this.approveData.message="",1===t&&this.approveData.taskId?this.claim():this.approveData.taskId?this.dialogShow=!0:this.$notify("请先选择要审批的任务")},claim:function(){var t=this;y["a"].loading({duration:0,forbidClick:!0,message:"审批中..."}),_api.home.claim({taskId:this.approveData.taskId}).then((function(e){e.status,t.approvePayrequestInfo()}))},approvePayrequestInfo:function(){var t=this;_api.home.approvePayrequestInfo(this.approveData).then((function(e){y["a"].clear(),0===e.code?(t.$notify(e.result),t.onRefresh(),t.approveData.taskId="",t.approveData.message=""):t.$notify(e.result)}))},goDetail:function(t){var e=this,a=t.number,n=t.taskId,i=t.approvalProcessId;_api.home.claim({taskId:n}).then((function(){var t="businessId=".concat(a,"&taskId=").concat(n,"&processId=").concat(i,"&edit=0&close=0&agree=1&reject=1&report=0");e.$router.push("/ybsTaskList/detail?"+t)}))}}},w=_,x=(a("21bb"),Object(u["a"])(w,b,k,!1,null,null,null)),S=x.exports,j=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.src}})},D=[],C=(a("1276"),a("ac1f"),location.origin+"/proxy-finance/finance/purchase/payrequestinfo/toProcessApproveDetail?"),P={data:function(){var t=location.href;return{src:C+t.split("?")[1]}}},I=P,L=Object(u["a"])(I,j,D,!1,null,null,null),O=L.exports;n["a"].use(m["a"]);var $=[{path:"/ybsTaskList",name:"Home",meta:{title:"神农审批"},component:S},{path:"/ybsTaskList/detail",name:"Detail",meta:{title:"任务详情"},component:O}],T=new m["a"]({mode:"history",base:"/",routes:$}),M=T,q=a("2f62");n["a"].use(q["a"]);var E=new q["a"].Store({state:{},mutations:{},actions:{},modules:{}}),z=a("5530"),H=(a("159b"),a("ddb0"),a("5319"),a("b64b"),a("bc3a")),N=a.n(H),R="",A=N.a.create({headers:{"X-Requested-With":"XMLHttpRequest","Content-Type":"application/x-www-form-urlencoded"},timeout:5e4});A.interceptors.response.use((function(t){var e=t.data;return console.log("=====",e),Promise.resolve(e)}));var F={axios:N.a,$http:A,customize$http:function(t){var e=this;return new Promise((function(a){var n=e.axios.create(t);e.$http=n,a(n)}))},get:function(t,e){return this.$http.get(t,{params:e})},post:function(t,e){var a=new FormData;return Object.keys(e).forEach((function(t){a.append(t,e[t])})),this.$http.post(t,a)},put:function(t,e){return this.$http.put(t,e)},_fromAjaxConfig:function(t,e){var a=this,n={};return Object.keys(t||{}).forEach((function(i){n[i]=function(n){var o=t[i];return/^http/.test(o)||(o=R+o),a[e||"get"](o,n)}})),n}},B={load:function(){window._api||(window._api=F);var t=a("4de4e");t.keys().forEach((function(e){var a=t(e);a=a.default||a;var n=Object(z["a"])(Object(z["a"])({},F._fromAjaxConfig(a.get)),F._fromAjaxConfig(a.post,"post")),i=e.replace(/(\.\/)|(\.js)/g,"");_api[i]||(_api[i]=n)}))}},J=(a("b90b"),a("b970"));a("157a");n["a"].use(J["a"]),B.load(),new n["a"]({router:M,store:E,render:function(t){return t(g)}}).$mount("#app")},b90b:function(t,e,a){}});