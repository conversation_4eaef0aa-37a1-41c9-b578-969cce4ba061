// 去掉所有input的autocomplete, 显示指定的除外

$(function () {

    //格式化日期：yyyy-MM-dd
    function formatDate(date) {
        if(date == null || date == "")
            return "";
        var date = new Date(date);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
    }
    $('.fold-block[fold=sub]').fold();

    var colNames = ['id','供应商编码', '供应商名称','单据类型','单据编号','日期',  '借方金额', '贷方金额', '余额'],
    colModel = [
        {
          name: 'id',
          hidden:true,
          hidegrid: true
        },{
            name: 'supplierCode'
        }, {
            name: 'supplierName'
        },{
            name: 'moveTypeName'
        },{
            name: 'stockOrderNo'
        },{
            name: 'storeTime',
            formatter: function (val) {
                return formatDate(val);
            },
        }, {
            name: 'jieMoney',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
            	
        }, {
            name: 'daiMoney',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        }, {
            name: 'balance',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        }];

    var totalTable = z_utils.totalTable;
    $('#X_Table').XGrid({
         url: '/proxy-finance/finance/purchase/AccountPayableReports/getSupplierPayAbleReportData',
         postData: {
             "keyWord": $("#keyWord").val()
         },
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,//是否展示序号
        altRows: true, //设置为交替行表格,默认为false
        mutltiselect: true,
        attachRow:true,
        maxheight: false,
        onSelectRow: function (e, c, a, b) {
            //console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
        	var data = $(this).XGrid("getRowData");
            /* 合计行 */
            var sum_models = ['jieMoney','daiMoney'];
            /* 合计行 */
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))

            });
        }
    });

    
    
    
    
    
    
    
    // 查询数据
    $('#searchBtn').bind('click', function () {
        window.isFirst = false;
        if (validform("myform").form()) {//验证通过 "myform"为需要验证的form的ID
            var param = $('#myform').serializeToJSON();
            console.log(param);
            $('#X_Table').setGridParam({
                url: '/proxy-finance/finance/purchase/AccountPayableReports/getSupplierPayAbleReportData',
                postData: {
                    "keyWord": $("#keyWord").val()
                }
            }).trigger('reloadGrid');
        }
    })


    // 导出单据
    $('#exportStoreInBtn').bind('click', function () {
        var obj = {
            "keyWord": $("#keyWord").val()
        }
        httpPost("/proxy-finance/finance/purchase/AccountPayableReports/exportSupplierPayAbleReport", obj);
        // //验证导出数据
        // $.ajax({
        //     url : "/proxy-finance/finance/purchase/AccountPayableReports/exportSupplierPayAbleReport",
        //     data:{
        //         "keyWord": $("#keyWord").val()
        //     },
        //     type: "post",
        //     success:function(result){
        //         //校验成功
        //         if(result.code==0){
        //             console.log(result.result);
        //             var url = result.result.filePath;
        //             var extfilename = result.result.extfilename;
        //             var parames = [];
        //             parames.push({ name: "filePath", value: url});
        //             parames.push({ name: "extfilename", value: extfilename});
        //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
        //             parent.hideLoading();
        //         }else{
        //             parent.hideLoading();
        //             utils.dialog({content:result.msg, quickClose: true,
        //                 timeout: 3000}).showModal();
        //
        //         }
        //     }
        // })
    });



// 显示期初余额
    $('#initBalanceBtn').bind('click', function () {
        console.log("看看:"+$("#keyWord").val());
        initTableDialog();
        utils.dialog({
            title: '期初导入余额查询',
            content: $('#modal1'),
            width: $(window).width()*0.8

        }).showModal();

    });
    //期初导入信息
    function initTableDialog() {
        $('#X_Table1').XGrid({
            url: '/proxy-finance/finance/purchase/AccountPayableReports/getImpInitBalanceReportData',
            postData: {
                "supplierNo": $("#keyWord").val()
            },
            colNames: ['id','供应商编码','供应商名称','日期', '期初余额'],
            colModel: [{
                name: 'id',
                hidden: true,
                hidegrid: true
            },{
                name: 'supplierNo'
            },{
                name: 'supplierName'
            }, {
                name: 'createTime',
                formatter:function (value){
                    if(value){
                        return moment(value).format('YYYY-MM-DD');
                    }else{
                        return ''
                    }
                }
            }, {
                name: 'balance',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }],
            // rowNum: 10,
            //multiselect: true,
            rownumbers: true,
            altRows: true, //设置为交替行表格,默认为false
            gridComplete: function () {
                var data = $(this).XGrid("getRowData");
                if(data == null || data.length == 0){
                    utils.dialog({
                        content:"查询无数据",
                        quickClose: true,
                        timeout: 3000
                    }).showModal();
                }
            },
            pager: '#grid-pager1',
        });
    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }

    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/invoice/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            $("#keyWord").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#keyWord").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#keyWord").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();
    }
})