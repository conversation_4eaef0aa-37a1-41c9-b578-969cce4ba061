
$("#text-count").text(100-$("#remark").val().length);
var pageType=$("#pageType").val();
//批量导入
$("#batchAdjustPrice").on("click", function () {
    dialog({
        url: '/proxy-product/product/adjustPrice/toFileupload?applyType=3',
        title: '批量导入',
        width: 420,
        height: 200,
        onclose: function (data) {
            if (this.returnValue) {
                var dataArray = this.returnValue;
                var rows = $('#X_Table').XGrid('getRowData');
                for (var x = 0; x < dataArray.length; x++) {
                    for (var i = 0; i < rows.length; i++) {
                        if (dataArray[x].productId == rows[i].productId) {
                            $('#X_Table').XGrid('delRowData', dataArray[x].productId);
                        }
                    }
                }
                for (var x = 0; x < dataArray.length; x++) {
                    $('#X_Table').XGrid('addRowData', {id: x + 1}, 'last');
                    $('#X_Table').XGrid('setRowData', x + 1, dataArray[x]);
                }
            }
        }
    }).showModal();
});
$('div[fold=head]').fold({
    sub: 'sub'
});
//判断是否是集团
var loginOrgCode = $('#loginOrgCode').val();
let colNames = ['','', '机构','业务类型','商品唯一值', '采购员ID','采购员', '商品编码', '标准库Id','商品名', '通用名', '型号/规格', '生产厂家', '包装单位', '剂型', 'TOP排名','APP售价','建议终端售价','申请终端售价','终端售价涨幅/涨额','<i class="i-red">*</i>申请原因','提交备注'],
    colModel = [
        {
        name: 'productId',
        index: 'productId',
        hidden: true
    },{
        name: 'orgCode',
        index: 'orgCode',
        hidden: true
    },{
        name: 'orgCodeValue',
        index: 'orgCodeValue',
        hidden: loginOrgCode!=='001'
    },{
        name: 'channelId',
        index: 'channelId'
    },{
        name: 'productIdChannel',
        index: 'productIdChannel',
        hidegrid:true,
        hidden: true
    },{
        name: 'buyer',
        index: 'buyer',
        hidegrid:true,
        hidden:true
    },{
        name: 'buyerVal',
        index: 'buyerVal'
    },{
        name: 'productCode',
        index: 'productCode'
    }, {
        name: 'standardId',
        index: 'standardId'
    },{
        name: 'productName',
        index: 'productName'
    }, {
        name: 'commonName',
        index: 'commonName'
    }, {
        name: 'specifications',
        index: 'specifications'
    }, {
        name: 'manufacturerName',
        index: 'manufacturerName'
    }, {
        name: 'packingUnitValue',
        index: 'packingUnitValue'
    }, {
        name: 'dosageFormValue',
        index: 'dosageFormValue'
    },{//top排名
        name: 'top',
        index: 'top'
    },{
        name: 'appPrice',
        index: 'appPrice'
    },{
        name: 'terminalPrice',
        index: 'terminalPrice'
    },{
        name: 'applicationTerminalPrice',
        index: 'applicationTerminalPrice',
        rowtype: '#applicationTerminalPrice'
    },{
        name: 'terminalPriceIncreaseRise',
        index: 'terminalPriceIncreaseRise'
    },{
        name: 'applicationReasonsType',
        index: 'applicationReasonsType',
        rowtype: '#applicationReasonsType'
    }, {
        name: 'detailRemark',
        index: 'detailRemark',
        rowtype: '#detailRemark'
    }];
if(pageType!= '3'){
    (pageType == '0' || pageType == '1') ? colNames : colNames.splice(18,0,'审核终端售价') ;
    (pageType == '0' || pageType == '1') ? colModel : colModel.splice(18,0,{
        name: 'auditTerminalPrice',
        index: 'auditTerminalPrice'
    }) ;
}else{
    pageType  != '3' ? colNames:   colNames.splice(18,0,'审核终端售价') ;
    pageType  != '3' ? colModel:   colModel.splice(18,0,{
        name: 'auditTerminalPrice',
        index: 'auditTerminalPrice',
        rowtype: '#auditTerminalPrice'
    }) ;
}
if (pageType == '3' || pageType == '2'){
    colNames.splice(colNames.length,0,'商品中心审核原因')
    colModel.splice(colNames.length,0,{
        name: 'remarks',
        index: 'remarks',
        rowtype: '#remarks'
    })
}
$('#X_Table').XGrid({
    url:"/proxy-product/product/adjustPrice/audi/details",
    colNames: colNames,
    colModel: colModel,
    postData: {
        "recordId": $("#recordId").val()
    },
    rowNum: 0,
    rownumbers: true,
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {
        //console.log('双击行事件', id, dom, obj, index, event);
    },
    onSelectRow: function (id, dom, obj, index, event) {
        //console.log('单机行事件', id, dom, obj, index, event);
    },
    gridComplete:function () {
        let rowData = $('#X_Table').XGrid('getRowData');
        $(rowData).each((index, item) =>  {
            $('#'+item.id).find('[row-describedby="auditTerminalPrice"] input ').val($('#'+item.id).find('[row-describedby="applicationTerminalPrice"] input ').val())
        })
    }
});

//新增行
$("#addRow").on("click", function () {
    var resultArr = $('#X_Table').getRowData();
    let channelId = resultArr.length > 0 ? resultArr[0].channelId : '',
        channelName = resultArr.length > 0 ? resultArr[0].channelVal : '',
        orgCode = resultArr.length > 0 ? resultArr[0].orgCode : '',
        buyer = resultArr.length > 0 ? resultArr[0].buyer : '',
        buyerVal = resultArr.length > 0 ? resultArr[0].buyerVal : '';
    dialog({
        // url: loginOrgCode=='001'?'/proxy-product/product/adjustPrice/searchJicaiProduct':'/proxy-product/product/adjustPrice/searchProduct',
        // 回退机构单选
        url: '/proxy-product/product/adjustPrice/searchProduct',
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            orgCode: orgCode,
            disableState: 0,
            channelId: channelId, //channelId
            channelName: channelName,
            resultArr: resultArr,
            buyer: buyer,
            buyerVal: buyerVal

        }, // 给modal 要传递的 的数据
        onclose: function (data) {
            var data = this.returnValue;
            console.log(data)
            if (data) {
                var temp = [];
                var rows = data.resultArr;
                for (var i = 0; i < rows.length; i++) {
                    var orgCode = rows[i].orgCode;
                    temp.push(orgCode)
                }
                temp = unique(temp);
                if (temp.length > 1&&loginOrgCode !== '001') {
                    utils.dialog({content: '请选择同一机构商品', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                for (var i = 0; i < rows.length; i++) {
                    var id = rows[i].productId;
                    if (!findInArr(id)) {
                        $('#X_Table').XGrid('addRowData', rows[i]);
                    }
                }
            }
        }
    }).showModal();
});
//列表内查询id
function findInArr(id) {
    var arr = $('#X_Table').getRowData();
    for (var i = 0; i < arr.length; i++) {
        var ID = arr[i].productId;
        if (ID == id) {
            return true;
        }
    }
    return false;
}

//删除行
$("#deleRow").on("click", function () {
    var selectRow = $('#X_Table').XGrid('getSeleRow');
    if (!selectRow) {
        utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
    } else {
        utils.dialog({
            title:"提示",
            width:300,
            height:30,
            okValue: '确定',
            content: "确定删除此条记录?",
            ok: function () {
                $('#X_Table').XGrid('delRowData', selectRow.id);
                utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    }
});
//关闭按钮
$("#closePage").on("click", function () {
    dialog({
        title: "提示",
        content: "是否保存草稿？",
        width: 300,
        height: 30,
        okValue: '保存',
        button: [
            {
                value: '关闭',
                callback: function () {
                    utils.closeTab();
                }
            }
        ],
        ok: function () {
            var rowData = $('#X_Table').getRowData();
            if (rowData.length == 0) {
                utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            $("#saveCaoGao").click()
            setTimeout(function () {
                utils.closeTab();
            }, 2000)
        }
    }).showModal();
});

$(".submitAudit").click(function () {
    var status = this.getAttribute("status");
    var formData = $("#applyForm").serializeToJSON();
    var rowData = $('#X_Table').getRowData();
    if (rowData.length == 0) {
        utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    //提交审核验证
    if (status == 1) {
        for (var i = 0; i < rowData.length; i++) {
            var selectRow = rowData[i];
            if (selectRow.applyStandardPrice == "" || selectRow.applyStandardPrice <= 0) {
                utils.dialog({content: '申请标准价格不能为空或0', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        }
    }

    if (!validform("applyTableForm").form()) {
        return  false;
    }
    let errData = rowData.filter(item =>  Number(item.appPrice) > Number(item.applicationTerminalPrice)).map(item => [item.productCode, item.productName]);
    let errStr =  errData.map(item  =>  item.join('  '))
    if (errStr.length!=0){
        utils.dialog({
            title:'申请终端售价小于APP售价',
            content:'<p>以下商品申请终端售价小于APP售价,请调整后在提交</p>' + errStr.join('<br/>'),
            okValue:'确定',
            ok: function () {

            }
        }).showModal();
        return false;
    }

    var adjustPriceApprovalRecordVo;
    var  requestUrl;
    if (pageType == 0) {
        //拼接工作流key值,新加字段“备注”,状态,调价类型
        formData =$.extend(formData,{"workProcessKey":$("#workProcessKey").val()},{"remark":$("#remark").val()},{"statues":status},{"applyType":3});
        adjustPriceApprovalRecordVo={"adjustPriceApprovalRecordVo":formData};
        requestUrl = "/proxy-product/product/adjustPrice/saveAdjustPrice";
    }
    if (pageType == 1) {
        //拼接工作流key值,新加字段“备注”,状态,调价类型
        adjustPriceApprovalRecordVo={"adjustPriceApprovalRecordVo":{"id":$("#recordId").val(),"orgCode":$("#recordOrgCode").val(),"statues":status,"applyType":3,"applicantId":$("#applicantId").val(),"applicationCode":$("#applicationCode").val(),"workProcessKey":$("#workProcessKey").val(),"remark":$("#remark").val()}};
        requestUrl = "/proxy-product/product/adjustPrice/adjustPriceApplyEditSave";
    }
    var adjustPriceApprovalRecordDetailVos={"adjustPriceApprovalRecordDetailVos":rowData};
    //拼装链接两个json对象
    var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
    parent.showLoading();
    $.ajax({
        type: "POST",
        url: requestUrl,
        data: places,
        async: false,
        dataType:'json',
        contentType: "application/json",
        error: function () {
            alert("提交失败！");
        },
        success: function (data) {
            if (data.code==0){
                var msg = "";
                if (status==0){
                    msg = '保存成功';
                }else if (status==1){
                    msg = '提交审核成功';
                }else {
                    msg = '操作成功';
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                utils.dialog({content: data.result.message, quickClose: true, timeout: 2000}).showModal();
            }
        },
        complete:function(){
            parent.hideLoading();
        }
    });
});


$('.audiPass').on('click', function () {
    $('#auditOpinion').val('');
    var status=this.getAttribute("status");
    if(status==1){
        $('#opinion').show();
    }else {
        $('#opinion').hide();
    }
    utils.dialog({
        title: '审核',
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            if (status==1) {
                if ($("#auditOpinion").val()==""){
                    utils.dialog({content: '审批意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
            }
            submitAudiInfo(status,$("#auditOpinion").val());
        },
        cancelValue: '取消',
        cancel: function () {
            $("#auditOpinion").val("");
        }
    }).showModal();
});


function submitAudiInfo(status,auditOpinion){
    var rows =  $('#X_Table').XGrid('getRowData');
    let errData = rows.filter(item =>  Number(item.appPrice) > Number(item.auditTerminalPrice)).map(item => [item.productCode, item.productName]);
    let errStr =  errData.map(item  =>  item.join('  '))
    if (errStr.length!=0){
        utils.dialog({
            title:'申请终端售价小于APP售价',
            content:'<p>以下商品申请终端售价小于APP售价,请调整后在提交</p>' + errStr.join('<br/>'),
            okValue:'确定',
            ok: function () {

            }
        }).showModal();
        return false;
    }
    var adjustPriceApprovalRecordVo={"adjustPriceApprovalRecordVo":{"id":$("#recordId").val()}};
    var adjustPriceApprovalRecordDetailVos={"adjustPriceApprovalRecordDetailVos":rows};
    //拼装链接两个json对象
    var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
    console.log(places);
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/adjustPrice/passAdjustPrice?statues="+status+"&auditOpinion="+auditOpinion+"&taskId="+$("#taskId").val()+"&editFlag="+$("#editFlag").val(),
        data: places,
        async: false,
        dataType:'json',
        contentType: "application/json",
        error: function () {
            alert("提交失败！");
        },
        success: function (data) {
            console.log(data);
            if (data.code==0){
                if(data.result!=null&&data.result.flowStatus!=undefined&&!data.result.flowStatus){
                    utils.dialog({
                        title: "提示",
                        content: data.result.flowMsg,
                        width:300,
                        height:30,
                        okValue: '确定',
                        ok: function () {
                            utils.closeTab();
                        }
                    }).showModal();
                    return false;
                }
                var msg="";
                if(status==0){
                    msg = '恭喜审核通过';
                }
                if(status==1){
                    msg = '驳回成功';
                }
                if(status==2){
                    msg = '流程已关闭';
                }
                utils.dialog({
                    title: "提示",
                    content:msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
            }
        }
    });
}
$('.auditPass').on('click', function () {
    var status=this.getAttribute("status");
    utils.dialog({
        title:"关闭审核",
        width:300,
        height:30,
        okValue: '确定',
        content: "确定关闭此申请？",
        ok: function () {
            submitAudiInfo(status,"");
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
});
$('#submitAuditAgain').on('click', function () {
    var rowData = $('#X_Table').getRowData();
    if (rowData.length == 0) {
        utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    //提交审核验证
    for (var i = 0; i < rowData.length; i++) {
            var selectRow = rowData[i];
            if (selectRow.applyStandardPrice == "" || selectRow.applyStandardPrice <= 0) {
                utils.dialog({content: '申请标准价格不能为空或0', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        }

    let errData = rowData.filter(item =>  Number(item.appPrice) > Number(item.applicationTerminalPrice)).map(item => [item.productCode, item.productName]);
    let errStr =  errData.map(item  =>  item.join('  '))
    if (errStr.length!=0){
        utils.dialog({
            title:'申请终端售价小于APP售价',
            content:'<p>以下商品申请终端售价小于APP售价,请调整后在提交</p>' + errStr.join('<br/>'),
            okValue:'确定',
            ok: function () {

            }
        }).showModal();
        return false;
    }
    utils.dialog({
        title:"提交审核",
        width:300,
        height:30,
        okValue: '确定',
        content: "确定提交申请？",
        ok: function () {
            var adjustPriceApprovalRecordVo={"adjustPriceApprovalRecordVo":{"id":$("#recordId").val(),"orgCode":$("#recordOrgCode").val(),"statues":1,"applicantId":$("#applicantId").val(),"applicationCode":$("#applicationCode").val(),"remark":$("#remark").val()}};
            var adjustPriceApprovalRecordDetailVos={"adjustPriceApprovalRecordDetailVos":rowData};
            //拼装链接两个json对象
            var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
            $.ajax({
                type: "POST",
                url: "/proxy-product/product/adjustPrice/submitAuditAgain?taskId="+$("#taskId").val(),
                data: places,
                async: false,
                dataType:'json',
                contentType: "application/json",
                error: function () {
                    alert("提交失败！");
                },
                success: function (data) {
                    if (data.code==0){
                        utils.dialog({
                            title: "提示",
                            content: '提交审核成功！',
                            width:300,
                            height:30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                        $(".ui-dialog-close").hide();
                        return false;
                    }else {
                        utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
                    }
                }
            });

        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
})
//获取审核流程数据
var workUrl="";
if (pageType == 0 || pageType == 1){
    workUrl="/proxy-product/product/purchaseLimit/queryTotle?key="+$("#workProcessKey").val();
}
if (pageType==2 || pageType==3 || pageType==4){
    workUrl = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+$("#processId").val();
}
$.ajax({
    type: "POST",
    url: workUrl,
    async: false,
    error: function () {
        utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
    },
    success: function (data) {
        if (data.code == 0) {
            $('.flow').process(data.result);
        } else {
            utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
        }
    }
});

/**
 * 设置输入两位小数 并且控制可输入的最大值的位数
 *
 * obj this对象
 * size 最大值位数 可不传
 * */
function setFixedTwo(obj, size) {
    var value = obj.value;
    var n = '';
    if (size) {
        for (var i = 0; i < size; i++) {
            n += '9';
        }
        n += '.99';
        if (Number(value) > Number(n)) {
            value = n;
        }
    }
    obj.value = toDecimal2(value);
}

//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x * 100) / 100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}

$('input[name="applyStandardPrice"]').blur(function () {
    var num = toDecimal2(this.value);
    this.value = num;
})

//数组去重
function unique(arr) {
    var res = [arr[0]];
    for (var i = 1; i < arr.length; i++) {
        var repeat = false;
        for (var j = 0; j < res.length; j++) {
            if (arr[i] === res[j]) {
                repeat = true;
                break;
            }
        }
        if (!repeat) {
            res.push(arr[i]);
        }
    }
    return res;
}
/*字数限制（备注）*/
$("#remark").on("input propertychange", function () {
    var $this = $(this),
        _val = $this.val(),
        count = "";
    if (_val.length > 100) {
        $this.val(_val.substring(0, 100));
    }
    count = 100 - $this.val().length;
    $("#text-count").text(count);
});



$("#X_Table").on("keyup", ".applicationTerminalPrice", function () {
    var $trId = $(this).parents('tr').attr('id');
    var selectRow = $('#X_Table').getRowData($trId);
    // if (selectRow.packingUnitValue != "") {
    var terminalPrice = selectRow.terminalPrice;
    var zhange = this.value - terminalPrice;
    var zhangfu  = "无穷大";
    if (terminalPrice != 0){
        zhangfu=zhange/terminalPrice;
        zhangfu=(Number(zhangfu)*100).toFixed(2)+"%";
    }
    zhange = Number(zhange).toFixed(2);
    var rise = zhangfu + "/" + zhange;
    $('#X_Table').XGrid('setRowData', selectRow.id, {terminalPriceIncreaseRise: rise});
    // }
})
