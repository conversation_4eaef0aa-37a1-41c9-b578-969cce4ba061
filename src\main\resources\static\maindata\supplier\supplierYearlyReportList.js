
var urlObjectList = [];
$('#X_Tableb').XGrid({
    url:"/proxy-supplier/supplier/supplierOrganBaseFile/getSupplierYearlyReportList",
    colNames: ['id', '供应商类型','供应商编码', '供应商名称', '营业执照号', '业务对接人', '报告年份','有效期至','剩余效期','采购员'],
    colModel: [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        }, {
            name: 'supplierTypeName',
            index: 'supplierTypeName',
            width: 100
        },  {
            name: 'supplierCode',
            index: 'supplierCode',
            width: 200

        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        }, {
            name: 'supplierBusinessNum',
            index: 'supplierBusinessNum',
            width: 130
        },{
            name: 'delivery',
            index: 'delivery',
            width: 150
        }, {
            name: 'reportDate',
            index: 'reportDate',
            width: 200
        }, {
            name: 'validityDate',
            index: 'validityDate',
            width: 200,
            formatter:function(value){
                var date=value;
                if(typeof(date) == "undefined" || !value)return '';
                date=format(value);
                return date.split(' ')[0];
            }
        },{
            name: 'remainDays',
            index: 'remainDays',
            width: 200
        },{
            name: 'buyerIdVal',
            index: 'buyerIdVal',
            width: 200
        }
    ],
    mtype: 'get',
    postData: {
        "supplierName": $("#supplierName").val(),
        "supplierTypeId":$("#supplierTypeId").val(),
        "maxRemainDays":$("#maxRemainDays").val(),
        "minRemainDays":$("#minRemainDays").val()
    },
    rowNum: 20,
    rowList:[20,50,100],
    rownumbers:true,
    altRows: true,//设置为交替行表格,默认为false
    //multiselect: true,//是否多选
    pager: '#grid-pager'
});
//搜索申请人
valAutocomplete("/proxy-supplier/supplier/supplierOrganBaseFile/queryBuyerList",{paramName:'userNames'},"id",{data:"id",value:"userName"});
$("#SearchBtn").on("click", function () {
    urlObjectList=[];
    $('#X_Tableb').XGrid('setGridParam', {
        postData: {
            "supplierName": $("#supplierName").val(),
            "supplierTypeId":$("#supplierTypeId").val(),
            "maxRemainDays":$("#maxRemainDays").val(),
            "minRemainDays":$("#minRemainDays").val(),
            "buyerId":$("#id").val()
        },page:1
    }).trigger('reloadGrid');
});



$.ajax({
    url:'/proxy-sysmanage/sysmanage/dict/querycommonnotpage?type=8',
    type:"post",
    async:false,
    dataType:'json',
    success:function(data){
        console.log("供应商类别："+data.result);
        var html = '';
        $(data.result).each(function(index,item){
            html +=  '<option value="'+item.id+'">&nbsp;'+item.name+'</option>'
        });
        $(".supplierTypeClass").append(html);

    },
    error:function(){
        utils.dialog({content: '请求失败', quickClose: true, timeout: 2000}).showModal();
    }
});
$(document).on('change','#grid_checked input',function () {
    if($(this).prop('checked')){
        var $tr=$(this).parents('tr');
        var rowData=$("#X_Tableb").getRowData($tr.attr('id'));
        var id=$tr.attr('id');
        setUrlObjectList($tr,id,rowData);
    }else{
        urlObjectList = [];
    }
});

$("#exportBtn").on("click", function () {
    utils.exportAstrictHandle('X_Tableb',
        Number($('#totalPageNum').text())).then(()=>{
        return false;
    }).catch(()=>{
        utils.dialog({
            title: '提示',
            content:"数据量大的时候耗时较长，请耐心等待。",
            okValue: '确定',
            ok: function () {
                //parent.showLoading()
                var body = document.body;
                var form = $(body).find('form#searchForm');
                $(form).attr("action","/proxy-supplier/supplier/supplierOrganBaseFile/supplierYearlyReportExportExcel");
                $(form).submit();
                // setTimeout(function () {
                //     parent.hideLoading()
                // },2000)
            },
            cancelValue: '取消',
            cancel: function () { },
        }).showModal();

    })



});

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}

function add0(m){return m<10?'0'+m:m }

/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            $("#"+obj).val(result.data);
            select && select(result)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            $("#"+obj).val("");
            $("#"+obj+"Val").val("");
            noneSelect && noneSelect();
        }
    });
}
