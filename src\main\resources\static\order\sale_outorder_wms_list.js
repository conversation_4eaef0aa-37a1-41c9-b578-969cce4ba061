$(function () {
    /* 获取dialog上层实例  */
    var dialog = parent.dialog.get(window);
    if (dialog) {
        var dialog_data = dialog.data;
        $('#search_vl').val(dialog_data)
    }

    $('#X_Table').XGrid({
        url:'/proxy-order/order/orderOut/orderOut/findWmsSalesOrderList',
        colNames: ['单据编号', '客户编号', '出库日期', '业务员id', '提货方式', '备注', '订单类型','机构编号','行号','商品编号','数量','件数','零散数','批号','生产日期',
                   '有效期至','最后更新时间','是否拉取','自增序号','暂存区名称','装箱数','复核人','发货人','单据序号','自动记账'],
        colModel: [{ name: 'djbh',      index: 'djbh',    },
            {name: 'dwbh',      index: 'dwbh',    },
            {      name: 'rq',      index: 'rq',    },
            {     name: 'ywy',      index: 'ywy',    },
            {      name: 'thfs',    index: 'thfs',   },
            {    name: 'beizhu',    index: 'beizhu',    },
            {    name: 'ddlx',     index: 'ddlx',   },
            {    name: 'yzid',     index: 'yzid',   },
            {    name: 'djSort',     index: 'djSort',   },
            {    name: 'spid',     index: 'spid',   },
            {    name: 'sl',     index: 'sl',   },
            {    name: 'js',     index: 'js',   },
            {    name: 'lss',     index: 'lss',   },
            {    name: 'ph',     index: 'ph',   },
            {    name: 'rqSc',     index: 'rqSc',   },
            {    name: 'yxqz',     index: 'yxqz',   },
            {    name: 'lastmodifytime',     index: 'lastmodifytime',   },
            {    name: 'isZx',     index: 'isZx',   },
            {    name: 'recnum',     index: 'recnum',   },
            {    name: 'zcqmc',     index: 'zcqmc',   },
            {    name: 'zxs',     index: 'zxs',   },
            {    name: 'fuhr',     index: 'fuhr',   },
            {    name: 'defhr',     index: 'defhr',   },
            {    name: 'billsn',     index: 'billsn',   },
            {    name: 'zidongjizhang',     index: 'zidongjizhang',   }
            ],
        rowNum: 0,
       /* rowList:[20,50,100],*/
        key:'productCode',
        rownumbers: true,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            dialog.close(obj);
        },
       /* pager: '#grid-pager',*/
    });


    /* 查询 */
    $('#search_list').on('click', function () {
        $('#X_Table').setGridParam({
            url: '/proxy-order/order/orderOut/orderOut/findWmsSalesOrderList',
            postData: {
                djbh:$('#search_vl').val()
            }
        }).trigger('reloadGrid');
    })
    /* 生成出库单 */
    $('#gen_output_order').on('click', function () {
        $.ajax({
            url: "/proxy-order/order/orderOut/orderOut/generateSalesOutOrder",
            type: "get",
            dataType: "json",
            success: function () {
                dialog.close();
            }
        });
    })
})