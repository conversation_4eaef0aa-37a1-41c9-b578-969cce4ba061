﻿(function () {
    const origin = location.origin;
    if (origin.indexOf('test') > -1) {
        document.domain = 'test.ybm100.com'
    } else if (origin.indexOf('stage') > -1) {
        document.domain = 'stage.ybm100.com'
    } else {
        document.domain = 'ybm100.com'
    }
})();

let CONFIG = {
    VERSION: '',
    STR: {
        ERR_CONNECTION_TIMED_OUT: '网络故障，请检查您的网络！',
        ERR_SERVER_INTERNAL: '服务器内部错误！',
        ERR_MISS_NECESSARY_PARAMS: '缺少必要参数！',
        NOTICE_LOADING: '正在加载',
        NOTICE_OPERATING: '正在操作',
        NOT_FIND: '找不到组件'
    }
}


let utils = {
    ajax: function (objParams, interfaceName, requestType, successCallback, errorCallback, completeCallback) {
        $.ajax({
            url: interfaceName,
            type: requestType,
            dataType: 'json',
            timeout: 30000, //6000
            data: objParams,
            success: function (data) {
                console.log("ajax-xgrid-complete", data)
                if (data.code === 401) {
                    window.top.location.href = xhr.responseJSON.result
                }
                successCallback(data);
            },
            error: function () {
                $.isFunction(errorCallback) ? errorCallback() : '';
            },
            complete: function (data) {
                $.isFunction(completeCallback) ? completeCallback() : '';
            }
        });
    },
    ajax_: function (param) {
        var defaultObj = {
            async: true,
            type: 'get',
            datatype: 'json',
            timeout: 30000
        }
        $.extend(defaultObj, param);
        $.ajax({
            url: defaultObj.url,
            type: defaultObj.type,
            dataType: defaultObj.datatype,
            timeout: defaultObj.timeout, //6000
            data: defaultObj.data,
            async: defaultObj.async,
            success: function (data) {
                defaultObj.cb(data);
            },
            error: function (e) {
                $.isFunction(defaultObj.errorCallback) ? defaultObj.errorCallback(e) : '';
            },
            complete: function (e) {
                $.isFunction(defaultObj.completeCallback) ? defaultObj.completeCallback(e) : '';
            }
        });
    },
    dialog: function (objParams) {
        //http://aui.github.io/artDialog/doc/index.html
        if (window.dialog && typeof window.dialog == 'function') {
            // console.log('window.dialog');
        } else if (parent.dialog && typeof parent.dialog == 'function') {
            // console.log('parent.dialog');
            window.dialog = parent.dialog;
        } else {
            // console.log(CONFIG.STR.NOT_FIND);
            alert(CONFIG.STR.NOT_FIND + ':dialog');
            return false;
        }

        var defaultObj = {};
        $.extend(objParams, defaultObj);
        var d = dialog(objParams);
        if (objParams.timeout) {
            setTimeout(function () {
                d.close().remove();
            }, objParams.timeout);
        }

        return d;

    },
    jsonToString: function (obj) {
        var s = '?';
        for (var o in obj) {
            s += o + "=" + obj[o] + "&";
        }
        return s.slice(0, s.length - 1);
    },
    formatDate: function (time) {
        if (!time) return false;
        var d = new Date(time),
            year = d.getFullYear(),
            month = d.getMonth() + 1,
            day = d.getDate(),
            hour = d.getHours(),
            min = d.getMinutes(),
            sec = d.getSeconds();
        return year + '-' +
            (month < 10 ? '0' + month : month) + '-' +
            (day < 10 ? '0' + day : day) + ' ' +
            (hour < 10 ? '0' + hour : hour) + ':' +
            (min < 10 ? '0' + min : min) + ':' +
            (sec < 10 ? '0' + sec : sec);
    },
    getCurDate: function (time) {
        if (!time) return false;
        var d = new Date(time),
            year = d.getFullYear(),
            month = d.getMonth() + 1,
            day = d.getDate();
        return year + '-' +
            (month < 10 ? '0' + month : month) + '-' +
            (day < 10 ? '0' + day : day);
    },
    setTableHeight: function (tableId) {
        //设置XGrid高度
        var $table = $('#' + tableId),
            offsetTop = $table.offset().top;
        $('body').css({ position: 'absolute', top: '0', bottom: '0' });
        var bodyHeight = $('body').height();
        var maxHeight = bodyHeight - offsetTop - 48 - 8 - 15 - 10;
        $table.closest('.table-box').css('max-height', maxHeight);

    },
    // 操作标签页
    openTabs: function (id, title, url, paramObj, closeFn) {
        window.parentMessage = closeFn;
        //打开标签页
        parent.openTabs(id, title, url, paramObj);
    },
    closeTab: function (data) {
        //关闭当前标签页,跳到父标签页
        var which = parent.$('#nav-tab li.active');
        parent.closeTabs(which, data);
    },
    operateRange: function (data, field, validityDate) {
        //RM-经营范围校验
        var parmId = 'id',
            parmName = 'name';
        var arr = [];
        // for (var i = 0; i < data.length; i++) {
        //     var data_one = data[i][field];
        //     for (var j = 0; j < data_one.length; j++) {
        //         if (data_one[j].id != 0) { // 去掉经营范围 这一层，不计入实际数据数组
        //             if (data_one[j].name.indexOf('I类') > -1){
        //                 var _pobj = {};
        //                 _pobj.id = data_one[j][parmId];
        //                 _pobj.name = data_one[j][parmName];
        //                 _pobj.status = false;
        //                 _pobj.vld = data[i][validityDate].split('-').join('');
        //                 arr.push(_pobj)
        //             } else{
        //                 if (data_one[j].name.indexOf('类.') > 0 || data_one[j].name.indexOf('类') < 0 || data_one[j].name == '第二类精神药品' || data_one[j].name == '蛋白同化制剂·肽类激素') { //不展示 1类和11 类张氏虚拟的父节点，不是真实存在的经营范围
        //                     var _pobj = {};
        //                     _pobj.id = data_one[j][parmId];
        //                     _pobj.name = data_one[j][parmName];
        //                     _pobj.status = false;
        //                     _pobj.vld = data[i][validityDate].split('-').join('');
        //                     arr.push(_pobj)
        //                 }
        //             }
        //         }
        //     }
        // }
        for (var i = 0; i < data.length; i++) {
            var data_one = data[i][field];
            for (var j = 0; j < data_one.length; j++) {
                if (data_one[j].level != 1 && data_one[j].id != 0) { // 去掉经营范围 这一层，不计入实际数据数组
                    // if (data_one[j].name.indexOf('I类') > -1){
                    if (data_one[j].id == 17) { //"I类" 17 转为按编号判断
                        var _pobj = {};
                        _pobj.id = data_one[j][parmId];
                        _pobj.name = data_one[j][parmName];
                        _pobj.status = false;
                        _pobj.vld = String(data[i][validityDate]).split('-').join('');
                        arr.push(_pobj)
                    } else {
                        //  || data_one[j].name.indexOf('类') < 0
                        // if (data_one[j].name.indexOf('类.') > 0 || data_one[j].name == '第二类精神药品' || data_one[j].name == '蛋白同化制剂·肽类激素') { //不展示 1类和11 类张氏虚拟的父节点，不是真实存在的经营范围
                        var _pobj = {};
                        _pobj.id = data_one[j][parmId];
                        _pobj.name = data_one[j][parmName];
                        _pobj.status = false;
                        _pobj.vld = String(data[i][validityDate]).split('-').join('');
                        arr.push(_pobj)
                        //}
                    }
                } else {
                    if (data_one[j].id != 0) {
                        var _pobj = {};
                        _pobj.id = data_one[j][parmId];
                        _pobj.name = data_one[j][parmName];
                        _pobj.status = false;
                        _pobj.vld = data[i][validityDate].split('-').join('');
                        arr.push(_pobj)
                    }

                }
            }
        }
        var date = new Date();
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        var d = date.getDate();
        var curDate = y + ((m < 10) ? '0' : '') + m + ((d < 10) ? '0' : '') + d;
        var r = [];
        for (var i = 0; i < arr.length; i++) {
            var flag = true;
            var temp = arr[i].id;
            for (var j = 0; j < r.length; j++) {
                if (temp === r[j].id) {
                    if (arr[i].vld < curDate) {
                        r[j].status = true;
                    }
                    flag = false;
                    break;
                }
            }
            if (flag) {
                if (arr[i].vld < curDate) {
                    arr[i].status = true;
                }
                r.push(arr[i]);
            }
        }

        r.map(function (item, ind) {
            delete item.vld;
            return item;
        })
        return r;
    },
    /* 给表格中的td 添加title  */
    setTableInpTit: function (tableId) {
        var allTds = $(tableId).find('td');
        if ($(tableId).find('td').children().length == 0) { // 纯表格，
            $(allTds).each(function (index, item) {
                $(item).attr('title', $(item).text());
            })
        } else {
            $(allTds).each(function (index, item) {
                if ($(item).find('input').length > 0) {
                    $(item).attr('title', $(item).find('input').eq(0).val());
                } else if ($(item).find('select').length > 0) {
                    $(item).attr('title', utils.getOptText($(item).find('select').eq(0), $(item).find('select').eq(0).val()));
                } else {
                    $(item).attr('title', $(item).text())
                }
            })
        }
    },
    //获取 下拉框中选中项的文本值
    getOptText: function (el, ev) {
        var _optText = '';
        var opts = $(el).find('option');
        $(opts).each(function (k, v) {
            if ($(this).val() == ev) { _optText = $(this).text(); }
        })
        return _optText
    },
    //html转图片保存到本地
    htmlToCanvas: function (name) { //  保存的文件名
        var shareContent = $('#box', parent.document);
        const w = $('#box', parent.document).children().eq(0).outerWidth() + 50, // 注释稍后添加
            h = $('#box', parent.document).outerHeight();
        var cloneContent = $(shareContent).clone(true);
        cloneContent.css({
            "background-color": "white",
            'paddingLeft': '40px',
            "z-index": "-1",
            "height": h
        });

        $("body").append(cloneContent);
        $('#box', parent.document).append('<a href="" id="a_a"></a>')
        html2canvas($(cloneContent), {
            allowTaint: true,//允许跨域
            taintTest: false,
            background: '#FFFFFF',//设置背景色，页面中有图片不设置这个图片周围会出现细线
            width: w,
            height: h,
            scale: window.devicePixelRatio,
            dpi: 280,				//设置分辨率，数值越大图片越清晰，生成文件越大300以上就没啥区别了
            onrendered: function (canvas) {
                var url = canvas.toDataURL("image/png", 1.0);//一定不能设置JPEG！
                var cans = canvas.getContext('2d');
                cans.translate(100, 100);
                //以下代码为下载此图片功能
                var triggerDownload = $('#box', parent.document).find('#a_a').attr("href", url).attr("download", name + ".png");
                triggerDownload[0].click();//下载图片
                $('#box', parent.document).find('#a_a').remove();
                $(cloneContent).remove();
            }
        });


        // var shareContent =  $('#box', parent.document);
        // var width = shareContent.offsetWidth;
        // var height = shareContent.offsetHeight;
        // var canvas = document.createElement("canvas");
        // var scale = 2;
        //
        // canvas.width = width * scale;
        // canvas.height = height * scale;
        // canvas.getContext("2d").scale(scale, scale);
        //
        // var opts = {
        //     scale: scale,
        //     canvas: canvas,
        //     logging: true,
        //     width: width,
        //     height: height
        // };
        // html2canvas(shareContent, opts).then(function (canvas) {
        //     var context = canvas.getContext('2d');
        //
        //     var img = Canvas2Image.convertToImage(canvas, canvas.width, canvas.height);
        //
        //     document.body.appendChild(img);
        //     $(img).css({
        //         "width": canvas.width / 2 + "px",
        //         "height": canvas.height / 2 + "px",
        //     })
        // });

        // var shareContent = $('#box', parent.document);
        // var width = $(shareContent).outerWidth();
        // var height = $(shareContent).outerHeight()+500; // 加500 是因为底部显示不全。估摸的数字
        // var canvas = document.createElement("canvas");
        // var scale = 2;
        //
        // canvas.width = width * scale;
        // canvas.height = height * scale;
        // canvas.getContext("2d").scale(scale, scale);
        // var opts = {
        //     scale: scale,
        //     canvas: canvas,
        //     logging: true,
        //     width: width * 2,
        //     height: height * 2,
        //     dpi: window.devicePixelRatio * 2,
        // };
        // html2canvas($('#box', parent.document),opts).then(function(canvas) {
        //     var type = 'png';
        //     var imgData = canvas.toDataURL(type);
        //     var _fixType = function(type) {
        //         type = type.toLowerCase().replace(/jpg/i, 'jpeg');
        //         var r = type.match(/png|jpeg|bmp|gif/)[0];
        //         return 'image/' + r;
        //     };
        //     imgData = imgData.replace(_fixType(type),'image/octet-stream');
        //     var saveFile = function(data, filename){
        //         var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        //         save_link.href = data;
        //         save_link.download = filename;
        //         var event = document.createEvent('MouseEvents');
        //         event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        //         save_link.dispatchEvent(event);
        //     };
        //     var filename = name + '.' + type;
        //     if (canvas.msToBlob) {//IE9+浏览器
        //         var blob = canvas.msToBlob();
        //         window.navigator.msSaveBlob(blob, filename);
        //     } else {//firefox,chrome
        //         saveFile(imgData, filename);
        //     }
        // });
    },
    /**
     * 在变更页面，点提交审核或者重新提交 或者保存草稿页面 需要先将未保存的数据进行保存操作
     */
    allSavaBtnsHidden: function () {
        let changeApplyBtns = $('.changeApplyBtn'), editState = false;
        $(changeApplyBtns).each(function (index, item) {
            if ($(item).css('display') != 'none') {
                utils.dialog({
                    title: '提示',
                    content: '请先完成保存操作再进行提交.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                editState = true;
                return editState;
            }
        })
        return editState;
    },
    /**
     * 数据比较获取表单数据
     * @param beforeData  :修改前的数据
     * @param afterData :修改后的数据
     * @param title   : 哪个form表单做了修改
     * @param formId  : 修改的formID .为了限制 页面结构中在不同的form中有相同的name名称
     */
    getFormData: function (beforeData, afterData, title, formId) {
        let resStr = ''; // 所有的修改记录汇总的字符串
        for (let item in beforeData) {
            let nodeTag = $('#' + formId).find('[name=' + item + ']');
            let nodeType = $('#' + formId).find('[name=' + item + ']').prop('type');
            let nodeTitle = $('#' + formId).find('[name=' + item + ']').parents('.input-group').find('.input-group-addon').text();
            nodeTitle = ((nodeTitle.indexOf('*') == 0) ? nodeTitle.substring(1).trim() : nodeTitle);
            let oldVal = beforeData[item], curVal = '';
            switch (nodeType) {
                case 'text':
                    curVal = $(nodeTag).val();
                    if (oldVal != curVal) {
                        resStr += nodeTitle + '的值有修改,【' + oldVal + '】修改为【' + curVal + '】;\n';
                    }
                    break;
                case 'number':
                    if ($(nodeTag).length < 2) {
                        curVal = $(nodeTag).val();
                    } else { // 温度范围 有多个相同name输入框,或者别的
                        let arr = [];
                        $($(nodeTag)).each(function () {
                            arr.push($(this).val())
                        });
                        curVal = arr.sort().toString();
                        oldVal = Array.isArray(oldVal) ? oldVal.sort().toString() : oldVal;
                    }
                    if (oldVal != curVal) {
                        resStr += nodeTitle + '的值有修改,【' + oldVal + '】修改为【' + curVal + '】;\n';
                    }
                    break;
                case 'checkbox':
                    let checkboxs = $('#' + formId + ' [name=' + item + ']:checked');
                    let finalVal = [], finalTitle = [];
                    $(checkboxs).each(function () {
                        finalTitle.push($(this).next('label').text());
                        finalVal.push($(this).val());
                    });
                    if (oldVal.toString() != finalVal.toString()) {
                        //获取修改前的选中项的对应值
                        let allCheckboxs = $('#' + formId + ' [name=' + item + ']'),
                            oldValArr = Array.from(oldVal),
                            oldValTitleArr = [];
                        for (let i = 0, len = oldValArr.length; i < len; i++) {
                            for (let j = 0; j < allCheckboxs.length; j++) {
                                if (oldValArr[i] == $(allCheckboxs[j]).val()) {
                                    oldValTitleArr.push($(allCheckboxs[j]).next('label').text())
                                }
                            }
                        }
                        resStr += nodeTitle + '的值有修改,【' + oldValTitleArr.toString() + '】修改为【' + finalTitle.toString() + '】;\n';
                    }
                    break;
                case 'radio':
                    let baseText = '';
                    let checkedText = $('#' + formId + ' [name=' + item + ']:checked').next('label').text();
                    curVal = $('#' + formId + ' [name=' + item + ']:checked').val();
                    for (let i = 0, length = $('#' + formId + ' [name=' + item + ']').length; i < length; i++) {
                        if (beforeData[item] == $('#' + formId + ' [name=' + item + ']').eq(i).val()) {
                            baseText = $('#' + formId + ' [name=' + item + ']').eq(i).next('label').text();
                            break;
                        }
                    }
                    if (oldVal != curVal) {
                        resStr += nodeTitle + '的值有修改,【' + baseText + '】修改为【' + checkedText + '】;\n';
                    }
                    break;
                case 'select-one':
                    curVal = $(nodeTag).val();
                    if (oldVal != curVal) {
                        resStr += nodeTitle + '的值有修改,【' + utils.getOptText($(nodeTag), oldVal) + '】修改为【' + utils.getOptText($(nodeTag), curVal) + '】;\n';
                    }
                    break;
                case 'textarea':
                    curVal = $(nodeTag).val();
                    if (oldVal != curVal) {
                        resStr += nodeTitle + '的值有修改,【' + oldVal + '】修改为【' + curVal + '】;\n';
                    }
                    break;
            }
        };
        return (resStr != '' ? title + '信息有变更:\n' + resStr : resStr);
    },
    /**
     * 验证手机号
     * @param {*} mobile 
     * @returns 
     */
    validatorPhone: function (mobile) {
        var re = /^1[1,2,3,4,5,6,7,8,9][0-9]{9}$/;
        var result = re.test(mobile);
        if (!result) {
            return false;
        }
        return true;
    },
    /**
     * 验证座机号
     * @param {*} tel 
     */
    validatorTel: function (tel) {
        var re = /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/;
        var result = re.test(tel);
        if (!result) {
            return false;
        }
        return true;
    },
    /**
     * 数据比较获取tab 数据
     * @param beforeData
     * @param afterData
     * @param title
     * @param formId
     */
    getRowData: function (beforeParam, afterParam, title, tableId) {
        var _str = '';
        if (beforeParam.length == 0) { //初始没数据
            if (afterParam.length > 0) { // 新增了数据
                $(afterParam).each(function (ind, ite) {
                    for (var key in ite) {
                        var thisTag = $('#' + tableId).find('th[row-describedby=' + key + ']').text();
                        if (thisTag && $('#' + tableId).find('th[row-describedby=' + key + ']').css('display') != 'none') { // 有值并且不是隐藏字段
                            thisTag = ((thisTag.indexOf('*') == 0) ? thisTag.substring(1).trim() : thisTag);
                            if (Array.isArray(ite[key])) { // 附件list
                                var urlStr = '';
                                if (key == 'supplierApprovalFileBusinessScopeVOList') { // 经营范围
                                    for (var url = 0; url < afterParam[ind][key].length; url++) {// of afterParam[ind][key]
                                        urlStr += (afterParam[ind][key][url]['name'] != '经营范围' ? (afterParam[ind][key][url]['name'] + ',') : '');
                                    }
                                } else {
                                    for (var url of JSON.parse(afterParam[ind][key])) {
                                        urlStr += '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                                    }
                                }
                                _str += thisTag + '的值有修改,【】修改为【' + urlStr + '】;\n';
                            } else {
                                //判断是select 框  还是输入框
                                var _st = ($('[name=' + key + ']').eq(ind).find('option:selected').text() ? $('[name=' + key + ']').eq(ind).find('option:selected').text() : afterParam[ind][key]);
                                _str += thisTag + '的值有修改,【】修改为【' + _st + '】;\n';
                            }
                        } else if (thisTag == '附件数据' || key == 'enclosureList') { // 附件数据的特殊，有值，附件list 还是隐藏的
                            var urlStr = '';
                            for (var url of afterParam[ind][key]) {
                                urlStr += '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                            }
                            _str += thisTag + '的值有修改,【】修改为【' + urlStr + '】;\n';
                        } else if (key == 'supplierClientProxyTypeVOList') { //选择剂型
                            var urlStr = '';
                            for (var url of afterParam[ind][key]) {
                                urlStr += url.jixingName + ',';
                            }
                            _str += thisTag + '的值有修改,【】修改为【' + urlStr + '】;\n';
                        } else if (key == 'supplierClientProxyProductVOList') { //选取商品
                            var urlStr = '';
                            for (var url of afterParam[ind][key]) {
                                urlStr += '商品编号:' + url['productNum'] + ' 通用名:' + url['commonName'] + ' 规格:' + url['specifications'] + ',';
                            }
                            _str += '选取商品的值有修改,【】修改为【' + urlStr + '】;\n';
                        } else if (key == 'supplierClientProxyBusinessScopeVOList') { //经营范围
                            var urlStr = '';
                            for (var url of afterParam[ind][key]) {
                                urlStr += url['businessScopeName'] + ',';
                            }
                            _str += '经营范围的值有修改,【】修改为【' + urlStr + '】;\n';
                        }
                    }
                })
            }
        } else { // 初始有数据
            if (afterParam.length > 0) { // 修改了数据
                //if(afterParam.length > beforeParam.length){ // 修改或新增了数据
                $(afterParam).each(function (ind, ite) {
                    for (var key in ite) { // 遍历修改后数据的每一项
                        if (beforeParam[ind]) { // 初始数据少，修改后数据多，
                            if (beforeParam[ind][key] != ite[key]) { // 跟初始数据比较每一下标的对应值。虽然通过下标会有问题
                                var thisTag = $('#' + tableId).find('th[row-describedby=' + key + ']').text();
                                if (thisTag && $('#' + tableId).find('th[row-describedby=' + key + ']').css('display') != 'none') { // 有值并且不是隐藏字段
                                    thisTag = ((thisTag.indexOf('*') == 0) ? thisTag.substring(1).trim() : thisTag);
                                    if (Array.isArray(ite[key])) { // 附件list、经营范围list
                                        var urlStr = '';
                                        if (key == 'supplierApprovalFileBusinessScopeVOList') { // 经营范围list
                                            var _sortStr = '';
                                            if (ite[key].length > 0) {
                                                var htmlAry = [], beforeLineData = '';
                                                htmlAry = getLineData(ite[key]); // 修改后每行的经营范围数据
                                                beforeLineData = (beforeParam[ind] ? getBeforeLineData(beforeParam[ind][key]) : '');// 修改前每行的经营范围数据
                                                if (beforeParam[ind]) {
                                                    if (ite[key].length != beforeParam[ind]['supplierApprovalFileBusinessScopeVOList'].length) { // 经营范围的值有变化的时候
                                                        //判断前后数据的经营范围中的不同项并取值
                                                        var oldHTMLArr = [];
                                                        if (window.changeBefore.initPZWJDataObj[ind]) {
                                                            oldHTMLArr = getLineData(window.changeBefore.initPZWJDataObj[ind]['supplierApprovalFileBusinessScopeVOList']);
                                                            $(htmlAry).each(function (i, v) {
                                                                //if(v.status == oldHTMLArr[i].status){
                                                                _sortStr += v.name + ',';
                                                                //}
                                                            })
                                                        }
                                                    } else {
                                                        // 修改前后当前行的经营范围的总数一致的情况下 判断 选中的状态是否一致，
                                                        oldHTMLArr = getLineData(window.changeBefore.initPZWJDataObj[ind]['supplierApprovalFileBusinessScopeVOList']);
                                                        $(htmlAry).each(function (i, v) {
                                                            //if(v.status != oldHTMLArr[i].status){
                                                            _sortStr += v.name + ',';
                                                            //}
                                                        })
                                                    }
                                                } else {
                                                    $(htmlAry).each(function (i, v) {
                                                        _sortStr += v.name + ',';
                                                    })
                                                }
                                                urlStr = _sortStr;
                                            }
                                        } else if (key == 'enclosureList') { //附件list
                                            if (afterParam[ind][key].length > 0) {
                                                for (var url of JSON.parse(afterParam[ind][key])) {
                                                    urlStr += '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                                                }
                                            }
                                        }
                                        if (beforeLineData == urlStr) { urlStr = '' }
                                        _str += (urlStr != '' ? '证书类型:' + utils.getOptText($('[name=certificateId]').eq(ind), beforeParam[ind]['certificateId']) + ' 证书编号:' + beforeParam[ind]['certificateNum'] + ' 的' + thisTag + '值有修改,【' + beforeLineData + '】修改为【' + urlStr + '】;\n' : '');
                                    } else {
                                        //判断是select 框  还是输入框
                                        var _st = ($('[name=' + key + ']').eq(ind).find('option:selected').text() ? $('[name=' + key + ']').eq(ind).find('option:selected').text() : afterParam[ind][key]);
                                        //var _beforeST = ($('[name='+key+']').eq(ind).find('option:selected').text()?getOptText($('[name='+key+']').eq(ind),beforeParam[ind][key]):beforeParam[ind][key]);
                                        //(beforeParam[ind]?getOptText($('[name='+key+']').eq(ind),beforeParam[ind][key]):'') //如果beforeParam[ind]为真时说明是之前就有的数据，通过方法调用拿值，如果为false时，说明是新增加的数据，所以_beforeST  应该为''.
                                        var _beforeST = ($('[name=' + key + ']').eq(ind).find('option:selected').text() ? (beforeParam[ind] ? utils.getOptText($('[name=' + key + ']').eq(ind), beforeParam[ind][key]) : '') : (beforeParam[ind] ? beforeParam[ind][key] : ''));
                                        _str += thisTag + '的值有修改,【' + _beforeST + '】修改为【' + _st + '】;\n';
                                    }
                                } else if (thisTag == '附件数据' || key == 'enclosureList') { // 获取附件list，因为附件数据的特殊，有值，附件list 还是隐藏的
                                    var urlStr = '', beforeFileList = '';
                                    if (beforeParam[ind] && beforeParam[ind][key].length > 0) {
                                        for (var url of beforeParam[ind][key]) {
                                            beforeFileList += '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                                        }
                                    } else {
                                        beforeFileList = '';
                                    }
                                    if (afterParam[ind] && afterParam[ind][key].length > 0) {
                                        for (var url of JSON.parse(afterParam[ind][key])) {
                                            urlStr += '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                                        }
                                        _str += (beforeFileList == urlStr ? '' : thisTag + '的值有修改,【' + beforeFileList + '】修改为【' + urlStr + '】;\n');
                                    }
                                } else if (key == 'supplierClientProxyBusinessScopeVOList') { // 客户委托书的经营范围
                                    var urlStr = '', beforeFileList = '';
                                    if (window.changeBefore.initKHWTS_jyfw && window.changeBefore.initKHWTS_jyfw.length > 0) {
                                        beforeFileList = window.changeBefore.initKHWTS_jyfw.toString() + ',';
                                    } else {
                                        beforeFileList = '';
                                    }
                                    if (afterParam[ind] && afterParam[ind][key].length > 0) {
                                        for (var url of JSON.parse(afterParam[ind][key])) {
                                            urlStr += (url.businessScopeName ? (url.businessScopeName + ',') : '');
                                        }
                                        //_str += (beforeFileList == urlStr?'':'委托书编号:'+beforeParam[ind]['proxyOderNo']+'的经营范围值有修改,【'+beforeFileList+'】修改为【'+ urlStr+'】;\n');
                                        _str += (beforeFileList == urlStr ? '' : (beforeParam[ind] ? '委托书编号:' + beforeParam[ind]['proxyOderNo'] + '的经营范围值有修改,【' + beforeFileList + '】修改为【' + urlStr + '】;\n' : '新增行:经营范围有修改,修改为【' + urlStr + '】;\n'));
                                    }
                                } else if (key == 'supplierClientProxyTypeVOList') { // 客户委托书的选择剂型
                                    var urlStr = '', beforeFileList = '';
                                    if (window.changeBefore.initKHWTS_jx && window.changeBefore.initKHWTS_jx.length > 0) {
                                        beforeFileList = window.changeBefore.initKHWTS_jx.toString() + ',';
                                    } else {
                                        beforeFileList = '';
                                    }
                                    if (afterParam[ind] && afterParam[ind][key].length > 0) {
                                        for (var url of JSON.parse(afterParam[ind][key])) {
                                            urlStr += (url.jixingName ? (url.jixingName + ',') : '');
                                        }
                                        _str += (beforeFileList == urlStr ? '' : (beforeParam[ind] ? '委托书编号:' + beforeParam[ind]['proxyOderNo'] + '的剂型有修改,【' + beforeFileList + '】修改为【' + urlStr + '】;\n' : '新增行:剂型有修改,修改为【' + urlStr + '】;\n'));
                                    }
                                } else if (key == 'supplierClientProxyProductVOList') { // 客户委托书的选取商品
                                    var urlStr = '', beforeFileList = '';
                                    if (window.changeBefore.initKHWTS_sp && window.changeBefore.initKHWTS_sp.length > 0) {
                                        // for(var i = 0; i<window.changeBefore.initKHWTS_sp.length; i++ ){
                                        //     beforeFileList += '商品编号:'+window.changeBefore.initKHWTS_sp[i].productCode+' 通用名:'+window.changeBefore.initKHWTS_sp[i].commonName+' 规格:'+window.changeBefore.initKHWTS_sp[i].specifications
                                        // }
                                        for (var i = 0; i < window.changeBefore.initKHWTS_sp.length; i++) {
                                            for (var j = 0; j < window.changeBefore.initKHWTS_sp[i].length; j++) {
                                                if (i == ind) {
                                                    beforeFileList += '商品编号:' + window.changeBefore.initKHWTS_sp[i][j].productCode + ' 通用名:' + window.changeBefore.initKHWTS_sp[i][j].commonName + ' 规格:' + window.changeBefore.initKHWTS_sp[i][j].specifications
                                                }
                                            }
                                        }
                                    } else {
                                        beforeFileList = '';
                                    }
                                    if (afterParam[ind] && afterParam[ind][key].length > 0) {
                                        for (var url of JSON.parse(afterParam[ind][key])) {
                                            //这个if判断是因为，如果当前行默认是已经有选择商品的数据，同时并没有对当前行的已选商品进行修改。
                                            //那么就不会存在下面的productNum的属性。
                                            //所以可以理解为前后数据一致，所以把旧的数据赋值给urlStr.
                                            if (url['productNum']) {
                                                urlStr += '商品编号:' + url['productNum'] + ' 通用名:' + url['commonName'] + ' 规格:' + url['specifications'] + ',';
                                            } else {
                                                urlStr = beforeFileList;
                                            }
                                        }
                                        //_str += (beforeFileList == urlStr?'':'委托书编号:'+beforeParam[ind]['proxyOderNo']+'的剂型有修改,【'+beforeFileList+'】修改为【'+ urlStr+'】;\n');
                                        _str += (beforeFileList == urlStr ? '' : (beforeParam[ind] ? '委托书编号:' + beforeParam[ind]['proxyOderNo'] + '的选取商品有修改,【' + beforeFileList + '】修改为【' + urlStr + '】;\n' : '新增行:选取商品有修改,修改为【' + urlStr + '】;\n'));
                                    }
                                }
                            }
                        } else {
                            var thisTag = $('#' + tableId).find('th[row-describedby=' + key + ']').text();
                            if (thisTag && $('#' + tableId).find('th[row-describedby=' + key + ']').css('display') != 'none') { // 有值并且不是隐藏字段
                                thisTag = ((thisTag.indexOf('*') == 0) ? thisTag.substring(1).trim() : thisTag);
                                if (Array.isArray(ite[key])) { // 附件list、经营范围list
                                    var urlStr = '';
                                    if (key == 'supplierApprovalFileBusinessScopeVOList') { // 经营范围list
                                        var _sortStr = '';
                                        if (ite[key].length > 0) {
                                            var htmlAry = [], beforeLineData = '';
                                            htmlAry = getLineData(ite[key]); // 修改后每行的经营范围数据
                                            beforeLineData = (beforeParam[ind] ? getBeforeLineData(beforeParam[ind][key]) : ''); // 修改前每行的经营范围数据
                                            if (beforeParam[ind]) {
                                                if (ite[key].length != beforeParam[ind]['supplierApprovalFileBusinessScopeVOList'].length) { // 经营范围的值有变化的时候
                                                    //判断前后数据的经营范围中的不同项并取值
                                                    var oldHTMLArr = [];
                                                    if (window.changeBefore.initPZWJDataObj[ind]) {
                                                        oldHTMLArr = getLineData(window.changeBefore.initPZWJDataObj[ind]['supplierApprovalFileBusinessScopeVOList']);
                                                        $(htmlAry).each(function (i, v) {
                                                            //if(v.status == oldHTMLArr[i].status){
                                                            _sortStr += v.name + ',';
                                                            //}
                                                        })
                                                    }
                                                } else {
                                                    // 修改前后当前行的经营范围的总数一致的情况下 判断 选中的状态是否一致，
                                                    oldHTMLArr = getLineData(window.changeBefore.initPZWJDataObj[ind]['supplierApprovalFileBusinessScopeVOList']);
                                                    $(htmlAry).each(function (i, v) {
                                                        //if(v.status != oldHTMLArr[i].status){
                                                        _sortStr += v.name + ',';
                                                        //}
                                                    })
                                                }
                                            } else {
                                                $(htmlAry).each(function (i, v) {
                                                    _sortStr += v.name + ',';
                                                })
                                            }
                                            urlStr = _sortStr;
                                        }
                                    } else if (key == 'enclosureList') { //附件list
                                        for (var url of JSON.parse(afterParam[ind][key])) {
                                            urlStr += '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                                        }
                                    }
                                    if (beforeLineData == urlStr) { urlStr = '' }
                                    _str += (urlStr != '' ? thisTag + '的值有修改,【' + beforeLineData + '】修改为【' + urlStr + '】;\n' : '');
                                } else {
                                    //判断是select 框  还是输入框
                                    var _st = ($('[name=' + key + ']').eq(ind).find('option:selected').text() ? $('[name=' + key + ']').eq(ind).find('option:selected').text() : afterParam[ind][key]);
                                    //var _beforeST = ($('[name='+key+']').eq(ind).find('option:selected').text()?getOptText($('[name='+key+']').eq(ind),beforeParam[ind][key]):beforeParam[ind][key]);
                                    //(beforeParam[ind]?getOptText($('[name='+key+']').eq(ind),beforeParam[ind][key]):'') //如果beforeParam[ind]为真时说明是之前就有的数据，通过方法调用拿值，如果为false时，说明是新增加的数据，所以_beforeST  应该为''.
                                    var _beforeST = ($('[name=' + key + ']').eq(ind).find('option:selected').text() ? (beforeParam[ind] ? utils.getOptText($('[name=' + key + ']').eq(ind), beforeParam[ind][key]) : '') : (beforeParam[ind] ? beforeParam[ind][key] : ''));
                                    _str += thisTag + '的值有修改,【' + _beforeST + '】修改为【' + _st + '】;\n';
                                }
                            } else if (thisTag == '附件数据' || key == 'enclosureList') { // 获取附件list，因为附件数据的特殊，有值，附件list 还是隐藏的
                                var urlStr = '', beforeFileList = '';
                                if (beforeParam[ind] && beforeParam[ind][key].length > 0) {
                                    for (var url of JSON.parse(afterParam[ind][key])) {
                                        beforeFileList += '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                                    }
                                } else {
                                    beforeFileList = '';
                                }
                                if (afterParam[ind][key].length > 0) {
                                    for (var url of JSON.parse(afterParam[ind][key])) {
                                        urlStr += '附件名称：' + url.fileName + ',附件路径：' + url.filePath;
                                    }
                                    _str += thisTag + '的值有修改,【' + beforeFileList + '】修改为【' + urlStr + '】;\n';
                                }
                            } else if (key == 'supplierClientProxyBusinessScopeVOList') { // 客户委托书的经营范围
                                var urlStr = '', beforeFileList = '';
                                if (window.changeBefore.initKHWTS_jyfw && window.changeBefore.initKHWTS_jyfw.length > 0) {
                                    beforeFileList = window.changeBefore.initKHWTS_jyfw.toString() + ',';
                                } else {
                                    beforeFileList = '';
                                }
                                if (afterParam[ind] && afterParam[ind][key].length > 0) {
                                    for (var url of JSON.parse(afterParam[ind][key])) {
                                        urlStr += (url.businessScopeName ? (url.businessScopeName + ',') : '');
                                    }
                                    _str += (beforeFileList == urlStr ? '' : (beforeParam[ind] ? '委托书编号:' + beforeParam[ind]['proxyOderNo'] + '的经营范围值有修改,【' + beforeFileList + '】修改为【' + urlStr + '】;\n' : '新增行:经营范围有修改,修改为【' + urlStr + '】;\n'));
                                }
                            } else if (key == 'supplierClientProxyTypeVOList') { // 客户委托书的选择剂型
                                var urlStr = '', beforeFileList = '';
                                if (window.changeBefore.initKHWTS_jx && window.changeBefore.initKHWTS_jx.length > 0) {
                                    beforeFileList = window.changeBefore.initKHWTS_jx.toString() + ',';
                                } else {
                                    beforeFileList = '';
                                }
                                if (afterParam[ind] && afterParam[ind][key].length > 0) {
                                    for (var url of JSON.parse(afterParam[ind][key])) {
                                        urlStr += (url.jixingName ? (url.jixingName + ',') : '');
                                    }
                                    //_str += (beforeFileList == urlStr?'':'委托书编号:'+beforeParam[ind]['proxyOderNo']+'的剂型有修改,【'+beforeFileList+'】修改为【'+ urlStr+'】;\n');
                                    _str += (beforeFileList == urlStr ? '' : (beforeParam[ind] ? '委托书编号:' + beforeParam[ind]['proxyOderNo'] + '的剂型有修改,【' + beforeFileList + '】修改为【' + urlStr + '】;\n' : '新增行:剂型有修改,修改为【' + urlStr + '】;\n'));
                                }
                            } else if (key == 'supplierClientProxyProductVOList') { // 客户委托书的选取商品
                                var urlStr = '', beforeFileList = '';
                                if (window.changeBefore.initKHWTS_sp && window.changeBefore.initKHWTS_sp.length > 0) {
                                    // for(var i = 0; i<window.changeBefore.initKHWTS_sp.length; i++ ){
                                    //     beforeFileList += '商品编号:'+window.changeBefore.initKHWTS_sp[i].productCode+' 通用名:'+window.changeBefore.initKHWTS_sp[i].commonName+' 规格:'+window.changeBefore.initKHWTS_sp[i].specifications
                                    // }
                                    for (var i = 0; i < window.changeBefore.initKHWTS_sp.length; i++) {
                                        for (var j = 0; j < window.changeBefore.initKHWTS_sp[i].length; j++) {
                                            if (i == ind) {
                                                beforeFileList += '商品编号:' + window.changeBefore.initKHWTS_sp[i][j].productCode + ' 通用名:' + window.changeBefore.initKHWTS_sp[i][j].commonName + ' 规格:' + window.changeBefore.initKHWTS_sp[i][j].specifications
                                            }
                                        }
                                    }
                                } else {
                                    beforeFileList = '';
                                }
                                if (afterParam[ind] && afterParam[ind][key].length > 0) {
                                    for (var url of JSON.parse(afterParam[ind][key])) {
                                        //这个if判断是因为，如果当前行默认是已经有选择商品的数据，同时并没有对当前行的已选商品进行修改。
                                        //那么就不会存在下面的productNum的属性。
                                        //所以可以理解为前后数据一致，所以把旧的数据赋值给urlStr.
                                        if (url['productNum']) {
                                            urlStr += '商品编号:' + url['productNum'] + ' 通用名:' + url['commonName'] + ' 规格:' + url['specifications'] + ',';
                                        } else {
                                            urlStr = beforeFileList;
                                        }
                                    }
                                    _str += (beforeFileList == urlStr ? '' : (beforeParam[ind] ? '委托书编号:' + beforeParam[ind]['proxyOderNo'] + '的选取商品有修改,【' + beforeFileList + '】修改为【' + urlStr + '】;\n' : '新增行:选取商品有修改,修改为【' + urlStr + '】;\n'));
                                }
                            }
                        }
                    }
                })
                if (title == '质量保证协议') {
                    _str += utils.getDeleteData(beforeParam, afterParam, 'signName', '签订人');
                } else if (title == "客户委托书") {
                    _str += utils.getDeleteData(beforeParam, afterParam, 'proxyOderNo', '委托书编号');
                } else if (title == "批准文件") {
                    _str += utils.getDeleteData(beforeParam, afterParam, 'certificateNum', '证书编号');
                } else if (title == "年度报告") {
                    _str += utils.getDeleteData(beforeParam, afterParam, 'reportDate', '报告年份');
                }
            } else { // 删除了数据
                _str = title + '清空了数据'
            }
        }
        return (_str != '' ? title + '信息有变更:\n' + _str : _str);
    },
    //获取表格删除的信息
    getDeleteData: function (BPD, APD, key, str) {
        //获取两个对象的不同项
        var result = [], _returnStr = '';
        for (var i = 0; i < BPD.length; i++) {
            var obj = BPD[i];
            var num = obj[key];
            var flag = false;
            for (var j = 0; j < APD.length; j++) {
                var aj = APD[j];
                var n = aj[key];
                if (n == num) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                result.push(obj);
            }
        }
        for (var i = 0; i < result.length; i++) {
            var urlStr = '';
            urlStr = str + result[i][key] + '的数据被删除了;\n';
            _returnStr += urlStr;
        }
        return _returnStr;
    },
    //其它附件filelist
    getOtherData: function (beforeParam, afterParam, title) {
        var _str = '', beforeStr = '';
        if (beforeParam.length == 0) { // 初始没数据
            if (afterParam.length > 0) {
                $(afterParam).each(function (index, item) {
                    var val = $(item).val();
                    val = JSON.parse(val);
                    for (var i of val) {
                        _str += i.fileName + ':' + i.filePath
                    }
                })
            }
        } else {
            if (afterParam.length > 0) {
                $(beforeParam).each(function (index, item) {
                    var val = $(item).val();
                    val = JSON.parse(val);
                    for (var i of val) {
                        beforeStr += i.fileName + ':' + i.filePath
                    }
                })
                $(afterParam).each(function (index, item) {
                    var val = $(item).val();
                    val = JSON.parse(val);
                    for (var i of val) {
                        _str += i.fileName + ':' + i.filePath
                    }
                })
            } else {
                _str = title + '清空了数据'
            }
        }
        return (_str != '' ? '\n【' + beforeStr + '】修改为【' + _str + '】;' : '');
    },
    //其他附件选中项
    getOtherValData: function (beforeParam, afterParam, title) {
        var _str = '', beforeStr = '';
        if (beforeParam.length == 0) { // 初始没数据
            if (afterParam.length > 0) {
                $(afterParam).each(function (index, item) {
                    _str += item.name + ',';
                })
            }
        } else {
            $(beforeParam).each(function (i, v) {
                beforeStr += v.name + ',';
            })
            if (afterParam.length > 0) {
                $(afterParam).each(function (index, item) {
                    _str += item.name + ',';
                })
            } else {
                _str = '';
            }
        }
        if (beforeStr == _str) { _str = '' };
        return (_str != '' ? title + '信息有变更:\n' + '【' + beforeStr + '】修改为【' + _str + '】;' : _str);
    },
    /* 表格导出  tableId:表格id;ok_callback:确定的回调;cancel_callback:取消的回调; */
    exportTable: function (tableId, width = 710, okCallback, cancelCallback) {
        var ary = $('#' + tableId).XGrid('getTableHead'),
            s = [];
        s.push('<div id="setCol" style="padding-left: 2.4rem"><div class="row" id="checkRow">');
        for (var i = 0; i < ary.length; i++) {
            s.push('<div class="col-md-3"><div class="checkbox"><label><input style="margin-right: 5px"');
            s.push(ary[i].hidden ? ' ' : ' checked ');
            s.push('type="checkbox" name="');
            s.push(ary[i].m);
            s.push('">');
            s.push(ary[i].n);
            s.push('</label></div></div>');
        }
        s.push('</div></div>');
        //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
        var ck = false;
        utils.dialog({
            content: s.join(''),
            title: '导出列',
            width: width,
            okValue: '确定',
            ok: function () {

                var cklen = $("#checkRow .col-md-3 input[type='checkbox']:checked").length;
                if (cklen < 1) {
                    utils.dialog({ content: '请选择后导出', quickClose: true, timeout: 2000 }).showModal();
                    return false;

                } else {
                    okCallback(this);
                }


            },
            cancelValue: '取消',
            cancel: function () {
                cancelCallback ? cancelCallback(this) : null;
            },
            button: [
                {
                    id: 'chooseAll',
                    value: '全选',
                    callback: function () {
                        //debugger;
                        if (!ck) {
                            $("#checkRow input").prop("checked", false);
                            ck = true;
                        } else if (ck) {
                            $("#checkRow input").prop("checked", "checked");
                            ck = false;
                        } else {
                            return false;
                        };
                        return false;
                    }
                }
            ]

        }).showModal();
    },
    // 导出
    exportTableData: function (tableId, formId, exportUrl) {
        utils.exportTable(tableId, 900, function (that) {
            var colName = []; // 列表头的字段 ["salesOrderCode", "receiverPhone", "province"]
            var colNameDesc = []; // 列表头的文字 ["ERP开票单号", "联系电话", "省份"]
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            var formData = $('#' + formId).serializeToJSON();
            formData["headers"] = colNameDesc;
            formData["fieldNames"] = colName;
            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data) {
                if (data.length && data.length > 0) {
                    // data-- 所选中的行的所有数据[{1,2,3,4},{1,2,3,4},{1,2,3,4}] 匹配到要打印的所在列[{2,3},{2,3},{2,3}]
                    data = data.map(function (item, key) {
                        var new_item = {};
                        colName.forEach(function (val, index) {
                            new_item[val] = item[val]
                        });
                        // {"字段"："字段数据"}
                        return new_item
                    });
                    data = JSON.stringify(data);
                    formData["selectData"] = data
                }

                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            // console.log(formData);
            utils.httpPost(exportUrl, formData);
        })
    },
    httpPost: function (URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();
        return temp;
    },
    copyText: function (text) {
        let flag = null;
        let textarea = document.createElement("textarea");
        let currentFocus = document.activeElement;
        document.body.appendChild(textarea);
        textarea.value = text;
        textarea.focus();
        if (textarea.setSelectionRange)
            textarea.setSelectionRange(0, textarea.value.length);
        else
            textarea.select();
        try {
            flag = document.execCommand("copy");
        } catch (eo) {
            flag = false;
        }
        document.body.removeChild(textarea);
        currentFocus.focus();
        return flag;



    },
    // 业务类型列表弹窗，多个地方用到
    showChannelDialog: function (param) {
        return new Promise((resolve, reject) => {
            utils.dialog({
                url: '/proxy-product/product/productFirst/channelListView',
                title: '业务类型列表',
                width: $(window).width() * 0.7,
                height: $(window).height() * 0.7,
                data: {
                    'supportMultiple': param
                },
                onclose: function () {
                    if (this.returnValue) {
                        let data = this.returnValue;
                        resolve(data)
                    } else {
                        reject()
                    }
                }
            }).showModal();
        })
    },
    // 供应商业务类型列表弹窗，
    showSupplierChannelDialog: function (param, productCode, orgCode) {
        return new Promise((resolve, reject) => {
            utils.dialog({
                url: '/proxy-supplier/supplier/supplierGoodsPrice/toChannelList',
                title: '业务类型列表',
                width: $(window).width() * 0.7,
                height: $(window).height() * 0.7,
                data: {
                    'supportMultiple': param,
                    'productCode': productCode,
                    'orgCode': orgCode
                },
                onclose: function () {
                    if (this.returnValue) {
                        let data = this.returnValue;
                        resolve(data)
                    } else {
                        reject()
                    }
                }
            }).showModal();
        })
    },
    //删除草稿
    deleteDraft: function (tableId, params, data, postData) {
        let selRow = $('#' + tableId).XGrid('getSeleRow');
        if (selRow.length == 0) {
            utils.dialog({
                title: '提示',
                content: '请先选中行。',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }

        if (selRow.length > 1) {
            utils.dialog({
                title: '提示',
                content: '只能删除单条草稿',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
    },
    //删除草稿
    deleteDraft: function (tableId, params, data, postData) {
        let selRow = $('#' + tableId).XGrid('getSeleRow');
        if (selRow.length == 0) {
            utils.dialog({
                title: '提示',
                content: '请先选中行。',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if (params.statusName == params.statusVal) {  // 录入中
            //当前行申请人 == 登录人
            if (params.applicantId != params.loginUserId) {
                utils.dialog({
                    title: '提示',
                    content: '只允许删除自己录入的单据',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return false;
            }
            utils.dialog({
                title: '提示',
                content: '确定要删除选中的单据吗?',
                okValue: '是',
                ok: function () {
                    $.ajax({
                        type: 'post',
                        url: params.url,
                        data: data,
                        success: function (res) {
                            // console.log(res)
                            if (res.code == 0) {
                                utils.dialog({ content: '删除草稿成功', quickClose: true, timeout: 2000 }).showModal();
                                $('#' + tableId).XGrid('setGridParam', {
                                    postData: postData,
                                    page: 1
                                }).trigger('reloadGrid');
                            } else {
                                utils.dialog({ content: '只允许删除录入中的数据，请刷新页面重试', quickClose: true, timeout: 2000 }).showModal();
                                return false;
                            }
                        },
                        error: function (err) { }
                    })
                },
                cancelValue: '否',
                cancel: function () { }
            }).showModal();
            return false;
        } else {
            utils.dialog({
                title: '提示',
                content: '只允许删除录入中的数据。',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
    },
    // 获取所有省份 地址
    getAllProList: function () {
        return new Promise((resolve, reject) => {
            utils.ajax({}, '/proxy-sysmanage/sysmanage/area/getProvince', 'get', function (res) {
                if (res.code == 0) {
                    resolve(res.result)
                } else {
                    reject(res)
                }
            })
        })
    },
    //
    setAllProDom: function (parentNode, addressSelIdObj, addressWrapId, isRequire, cb) {
        utils.getAllProList().then(list => {
            let _provinceStr = '';
            let _optStr = '<option value="">&nbsp;&nbsp;-- 请选择 --</option>';
            $(list).each((index, item) => { // 省份 options
                _optStr += `<option value="${item.code}"  data-depth="${item.depth}">${item.name}</option>`
            });
            _provinceStr = `<select class="form-control" id="${addressSelIdObj[0]['nextNodeId']}" name="${addressSelIdObj[0]['nextNodeName']}" data-code="" 
                                data-depth="${list[0]['depth']}"
                                disabled
                                onchange='utils.getAddressOptions(this.value, ${isRequire}, ${JSON.stringify(addressSelIdObj[1])}, "${addressWrapId}")'>${_optStr}</select>`;
            $(parentNode).html(_provinceStr);
            for (let i = 1; i < addressSelIdObj.length; i++) {
                utils.getAddressOptions($(parentNode).val(), (i == addressSelIdObj.length - 1 ? false : isRequire), { 'nextNodeWrap': addressSelIdObj[i]['nextNodeWrap'], 'nextNodeId': addressSelIdObj[i]['nextNodeId'], 'nextNodeName': addressSelIdObj[i]['nextNodeName'] }, addressWrapId);
            }
            return addressSelIdObj;
        }).then(res => {
            return new Promise((resolve, reject) => {
                let _code = $(parentNode).val() ? $(parentNode).val() : '';
                $.ajax({
                    type: 'get',
                    url: '/proxy-sysmanage/sysmanage/area/getNexAreaListByPcode?pCode=' + _code,
                    async: false,
                    success: function (optionObj) {
                        if (optionObj.code == 0) {
                            resolve(optionObj.result)
                        } else {
                            reject(res)
                        }
                    }
                })
            })
        }).catch(err => {
            // console.log(err)
            let _optStr = '<option value="">&nbsp;&nbsp;-- 请选择 --</option>'
            for (let i = 1; i < err.length; i++) {
                let _addressStr = `<select class="form-control" id="${err[i]['nextNodeId']}" name="${err[i]['nextNodeName']}" data-code="" disabled onchange='utils.getAddressOptions(this.value)'>${_optStr}</select>`;
                $(err[i]['nextNodeWrap']).find('select').html(_addressStr);
                utils.clearAddressOptions(err[i]['nextNodeWrap'], addressWrapId)
            }
        }).finally(() => {
            if (cb && cb()) {
                cb()
            }
        })

    },
    // 地址回显值赋值
    setAddressReturnVal: function (parentCode) {
        return new Promise((resolve, reject) => {
            if (parentCode) {
                utils.ajax({}, '/proxy-sysmanage/sysmanage/area/getNexAreaListByPcode?pCode=' + parentCode, 'get', function (res) {
                    if (res.code == 0) {
                        let _optStr = '<option value="">&nbsp;&nbsp;-- 请选择 --</option>';
                        $(res.result).each((index, item) => {
                            _optStr += `<option value="${item.code}"  data-depth="${item.depth}">${item.name}</option>`
                        });
                        resolve(_optStr)
                    } else {
                        let _optStr = '<option value="">&nbsp;&nbsp;-- 请选择 --</option>';
                        resolve(_optStr)
                    }
                })
            } else {
                let _optStr = '<option value="">&nbsp;&nbsp;-- 请选择 --</option>';
                resolve(_optStr)
            }

        })
    },
    // 根据上一级 code 获取下一级市区街道options
    getAddressOptions: function (parentCode, isRequire = false, nextNode_id_name, addressWrapId, cb) {
        // 当客户首营申请页面的收货地址发生变化时，同步修改文本收货地址
        if ($('#customerApplType').val() == 6 && addressWrapId == '#shippingAddressBox') {
            const receiveAddressProvince = $('#province2').val() ? $('#province2 option:selected').text() : ""
            const receiveAddressCity = $('#shippingAddressCityId').val() ? $('#shippingAddressCityId option:selected').text() : ""
            const receiveAddressDistrict = $('#shippingAddressDistrictId').val() ? $('#shippingAddressDistrictId option:selected').text() : ""
            const receiveAddressStreet = $('#shippingAddressStreetId').val() ? $('#shippingAddressStreetId option:selected').text() : ""
            const receiveAddressInput = $('input[name=shippingAddressInput]').val()
            let receiveAddress = ""
            const nextNodeId = nextNode_id_name.nextNodeId
            if (nextNodeId == 'shippingAddressCityId') {
                receiveAddress = receiveAddressProvince
            } else if (nextNodeId == 'shippingAddressDistrictId') {
                receiveAddress = receiveAddressProvince + receiveAddressCity
            } else if (nextNodeId == 'shippingAddressStreetId') {
                receiveAddress = receiveAddressProvince + receiveAddressCity + receiveAddressDistrict
            } else {
                receiveAddress = receiveAddressProvince + receiveAddressCity + receiveAddressDistrict + receiveAddressStreet
            }
            receiveAddress += receiveAddressInput
            // 页面加载过程中，不进行联动修改
            if (!window.customerFirstPageLoading) {
                $('input[name=accompanyAddress]').val(receiveAddress)
            }
        }
        if (nextNode_id_name) {
            nextNode_id_name = {
                'nextNodeWrap': nextNode_id_name['nextNodeWrap'],
                'nextNodeId': nextNode_id_name['nextNodeId'],
                'nextNodeName': nextNode_id_name['nextNodeName']
            }
            let _nextNodeObj = {
                'nextNodeWrap': ($(nextNode_id_name.nextNodeWrap).next().length != 0) ? '#' + $(nextNode_id_name.nextNodeWrap).next().attr('id') : (nextNode_id_name.nextNodeWrap.indexOf('#') == 0 ? '' : '#') + nextNode_id_name.nextNodeWrap,
                'nextNodeId': ($(nextNode_id_name.nextNodeWrap).next().length != 0) ? $(nextNode_id_name.nextNodeWrap).next().find('select').attr('id') : nextNode_id_name.nextNodeId,
                'nextNodeName': ($(nextNode_id_name.nextNodeWrap).next().length != 0) ? $(nextNode_id_name.nextNodeWrap).next().find('select').attr('name') : nextNode_id_name.nextNodeName
            }
            let _addressStr = '';
            if (parentCode) {
                utils.ajax({}, '/proxy-sysmanage/sysmanage/area/getNexAreaListByPcode?pCode=' + parentCode, 'get', function (res) {
                    // console.log(res)
                    if (res.code == 0) {
                        let _optStr = '<option value="">&nbsp;&nbsp;-- 请选择 --</option>'
                        $(res.result).each((index, item) => {
                            _optStr += `<option value="${item.code}">${item.name}</option>`
                        });
                        _addressStr = `<select class='form-control ${isRequire && res.result[0]["depth"] != 4 ? "{validate:{ required :true}}" : ""}' id="${nextNode_id_name.nextNodeId}"
                                    name="${nextNode_id_name.nextNodeName}"
                                    data-code=""
                                    data-depth="${res.result[0]['depth']}"
                                    onchange='utils.getAddressOptions(this.value,${res.result[0]["depth"] != 4 ? true : false}, ${JSON.stringify(_nextNodeObj)}, "${addressWrapId}")'>${_optStr}</select>`;
                        $(nextNode_id_name.nextNodeWrap).html(_addressStr);
                        let addReturnPageFlag = $('#addReturnPageFlag').val();
                        if (addReturnPageFlag == 'rturnAddPage') {
                            $('#registerAddress select,#registerAddress input').removeAttr('disabled readonly');
                            $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
                            $('#depotAddress .btn').css('display', 'inline-block');
                        }
                        utils.clearAddressOptions(nextNode_id_name.nextNodeWrap, addressWrapId)
                    }
                    if (cb && cb()) {
                        cb()
                    }
                })
            } else {
                let _optStr = '<option value="">&nbsp;&nbsp;-- 请选择 --</option>';
                let _depth = $(nextNode_id_name.nextNodeWrap).find('select').attr('data-depth');
                _depth = _depth ? _depth : 1;
                _addressStr = `<select class='form-control ${(_depth != '4' && isRequire) ? "{validate:{ required :true}}" : ""}' id="${nextNode_id_name.nextNodeId}" name="${nextNode_id_name.nextNodeName}" 
                                data-code="" 
                                disabled
                                onchange='utils.getAddressOptions(this.value, ${(_depth != '4' && isRequire) ? true : false}, ${JSON.stringify(_nextNodeObj)}, "${addressWrapId}")'>${_optStr}</select>`;
                $(nextNode_id_name.nextNodeWrap).html(_addressStr);
                utils.clearAddressOptions(nextNode_id_name.nextNodeWrap, addressWrapId)
            }
        }
    },
    /**
     *  地址联动，选了请选择的时候，清空当前节点后面的所有选中项
     * @param nextNodeWrap  当前节点。
     * @param addressWrapNode  当前地址下拉框的外层父节点ID 。
     */
    clearAddressOptions: function (nextNodeWrap, addressWrapId) {
        let arrSels = $(nextNodeWrap).parents(addressWrapId).find('select');
        $(arrSels).each((index, item) => {
            if (index > $(nextNodeWrap).index()) {
                $(item).html('<option value="">&nbsp;&nbsp;-- 请选择 --</option>')
            }
        });
    },
    /**
     * @param s 两个时间的时间差 单位为秒。
     */
    seconds2date: function (s) {
        let days = Math.floor(s / (24 * 3600)) //计算出小时数
        let leave1 = s % (24 * 3600)    //计算天数后剩余的毫秒数
        let hours = Math.floor(leave1 / (3600)) //计算相差分钟数
        let leave2 = leave1 % (3600)        //计算小时数后剩余的毫秒数
        let minutes = Math.floor(leave2 / (60)) //计算相差秒数
        let leave3 = leave2 % (60)      //计算分钟数后剩余的毫秒数
        let seconds = Math.round(leave3)
        return " 相差 " + days + "天 " + hours + "小时 " + minutes + " 分钟" + seconds + " 秒"
    },
    valAutocomplete: function (url, param, obj, resParam, select, noneSelect) {
        var resParam = Object.assign({ 'list': 'result' }, resParam);
        $("#" + obj + "Val").Autocomplete({
            serviceUrl: url, //异步请求
            paramName: param.paramName,//查询参数，默认 query
            params: param.params || {},
            dataType: 'json',
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            dataReader: resParam,
            triggerSelectOnValidInput: false, // 必选
            showNoSuggestionNotice: true, //显示查无结果的container
            noSuggestionNotice: '查询无结果',//查无结果的提示语
            onSelect: function (result) {
                $("#" + obj).val(result.data);
                select && select(result)
            },
            onNoneSelect: function (params, suggestions) {
                //console.log(params, suggestions);
                $("#" + obj).val("");
                $("#" + obj + "Val").val("");
                noneSelect && noneSelect();
            },
            onSearchComplete: function (query, suggestions) {
                var $ele = $(this);
                if (suggestions && suggestions.length === 0) {
                    $ele.attr('oldvalue', '');
                } else {
                    $ele.attr('oldvalue', $ele.val())
                }
                if (!$ele.is(":focus")) {
                    $ele.Autocomplete('hide');
                }
            }
        });
    },
    /**
     * 导出 按钮限制条数
     * @param tableId   表格id
     * @param num  当前表格的总条数
     * @param isSpe  是否特殊表格，是的话只判断总数，不管是否有选中条数；  默认 0 ：不是特殊表， 非0 ： 特殊表
     * @returns {boolean}
     */
    exportAstrictHandle: function (tableId, num, isSpe = 0) {
        let selRow = $('#' + tableId).XGrid('getSeleRow');
        let selRowLen = Array.isArray(selRow) ? selRow.length : 1;
        let limitNum = Number(window.top.limitExportNum);
        if (!Number(isSpe)) {
            num = selRowLen ? selRowLen : num;
        }
        var tips = "您本次导出的数据量过大（已超过" + limitNum + "条），不允许导出，请缩小导出范围。";
        return new Promise((resolve, reject) => {
            if (num > limitNum) {
                utils.dialog({ content: tips, quickClose: true, timeout: 2000 }).showModal()
                resolve(true)
            } else {
                reject(false)
            };
        })
    },

        /**
     * 近效期计算器
     * RM 20230423
     * @returns {Promise<any>}
     */
        timeDialog: function () {
            let _html = `
            <div id="timeDialog_div">
                          <form class="form-horizontal">
                              <div class="row">
                                  <div class="col-sm-12" style="display: flex; flex-direction: row; margin-bottom:10px;">
                                      <div class="input-group-addon require" style="width:80px !important">生产日期：</div>
                                      <input class="Wdate form-control grid_date" style="width:60%" name="createTime"
                                             id="createTime" onfocus="WdatePicker({readOnly:true})">
                                  </div>
                                  <div class="col-sm-12" style="display: flex; flex-direction: row; margin-bottom:10px;">
                                      <div class="input-group-addon require" style="width:80px !important">有效期至：</div>
                                      <input class="Wdate form-control grid_date" style="width:60%" name="endTime"
                                             id="endTime" onfocus="WdatePicker({readOnly:true})">
                                      </div>
                                  <div class="col-sm-12" style="display: flex; flex-direction: row; margin-bottom:10px;">
                                      <div class="input-group-addon require" style="width:80px !important">入库时间：</div>
                                      <input class="Wdate form-control grid_date" style="width:60%; margin-right:5px;" name="currentTime"
                                             id="currentTime" onfocus="WdatePicker({readOnly:true})">
                                      <button type="button" class="btn btn-info" style="width:20%" id="timeDialog_btn" onclick="utils.computedDialog(this)">计算</button>
                                  </div>
                              </div>
                          </form>
                          <div style="display: flex; flex-direction: row; margin-bottom:10px;">
                          <div>是否为近效期：</div>
                          <div style="color:red" id="isjxq">--</div>
                          </div>
                          <div style="display: flex; flex-direction: row; margin-bottom:10px;">
                          <div style="color:red" id="jxqTime">--</div>
                          <div id="jxqTimets">天后将为近效期购进</div>
                          </div>
                      </div>
            `  
            return new Promise((resolve, reject) => {
              let timeDia = utils.dialog({
                  title: '近效期计算器',
                  content: _html,
                  width: 500,
                  height: 250,
                  data: {},
                  onclose: function () {
                      if (this.returnValue) {
                          let data = this.returnValue;
                          resolve(data);
                      } else {
                          reject()
                      }
                  }
              }).showModal();
              window.top.timeDia = timeDia;
            //   $('#currentTime').val(utils.getCurDate(new Date));
          })
          },
    

    /**
     * 近效期计算器（暂时废弃）
     * RM 20230423
     * @returns {Promise<any>}
     */
    // timeDialog: function () {
    //     let _html = `
    //     <div id="timeDialog_div">
    //                   <form class="form-horizontal">
    //                       <div class="row">
    //                           <div class="col-sm-12" style="display: flex; flex-direction: row; margin-bottom:10px;">
    //                               <div class="input-group-addon require" style="width:80px !important">生产日期：</div>
    //                               <input class="Wdate form-control grid_date" style="width:60%" name="alertCreateTime"
    //                                      id="alertCreateTime" onfocus="WdatePicker({readOnly:true})">
    //                           </div>
    //                           <div class="col-sm-12" style="display: flex; flex-direction: row; margin-bottom:10px;">
    //                               <div class="input-group-addon require" style="width:80px !important">有效期：</div>
    //                               <input class="form-control" style="width:60%" name="indateStr" readonly="ture"
    //                                      id="indateStr">
    //                             </div>
    //                           <div class="col-sm-12" style="display: flex; flex-direction: row; margin-bottom:10px;">
    //                               <div class="input-group-addon require" style="width:80px !important">有效期至：</div>
    //                               <input class="form-control" style="width:60%" name="endTime" readonly="ture"
    //                                      id="endTime">
    //                             </div>
    //                           <div class="col-sm-12" style="display: flex; flex-direction: row; margin-bottom:10px;">
    //                               <div class="input-group-addon require" style="width:80px !important">入库时间：</div>
    //                               <input class="Wdate form-control grid_date" style="width:60%; margin-right:5px;" name="alertCurrentTime"
    //                                      id="alertCurrentTime" onfocus="WdatePicker({readOnly:true})">
    //                               <button type="button" class="btn btn-info" style="width:20%" id="timeDialog_btn" onclick="utils.computedDialog(this)">计算</button>
    //                           </div>
    //                       </div>
    //                   </form>
    //                   <div style="display: flex; flex-direction: row; margin-bottom:10px;">
    //                   <div>是否为近效期：</div>
    //                   <div style="color:red" id="isjxq">--</div>
    //                   </div>
    //                   <div style="display: flex; flex-direction: row; margin-bottom:10px;">
    //                   <div style="color:red" id="jxqTime">--</div>
    //                   <div id="jxqTimets">天后将为近效期购进</div>
    //                   </div>
    //               </div>
    //     `
    //     return new Promise((resolve, reject) => {
    //         let timeDia = utils.dialog({
    //             title: '近效期计算器',
    //             content: _html,
    //             width: 500,
    //             height: 270,
    //             data: {},
    //             onclose: function () {
    //                 if (this.returnValue) {
    //                     let data = this.returnValue;
    //                     resolve(data);
    //                 } else {
    //                     reject()
    //                 }
    //             }
    //         }).showModal();
    //         window.top.timeDia = timeDia;
    //         $('#currentTime').val(utils.getCurDate(new Date));
    //     })
    // },


    /**
     * 全局统一 业务类型列表
     * RM 20200307
     * @param supportMultiple  表格单选或者多选 ，1：多选  0 ： 单选
     * @param status 是否启用：1-是，0-否
     * @param isFlag 业务类型为312的是否可选
     * @returns {Promise<any>}
     */
    channelDialog: function (supportMultiple, status = '', isFlag = false) {
        let _html = `
                <div id="channelDialog_div">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-3" style="display: none">
                                <div class="input-group">
                                    <span class="input-group-addon">类别</span>
                                    <select class="form-control" id="channelType" name="channelType" autocomplete="off" onchange="utils.channelChange(this)">
                                         <option value="">全部</option>
                                         <option value="1">药帮忙</option>
                                         <option value="2">宜块钱</option>
                                         <option value="3">控销</option>
                                         <option value="4">控销</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="col-sm-12">
                                    <input class="form-control" placeholder="业务类型" name="channelName" id="channelName" value=""/>
                                </div>
                            </div>
                            <div class="col-sm-4 form-group">
                                <div class="col-sm-12">
                                    <div role="group">
                                        <button type="button" class="btn btn-info" id="searchChannel_btn" data-status="${status}" onclick="utils.searchChannel(this)">查询</button>
                                        <button type="button" class="btn btn-info" id="chooseChannel_btn" data-mult="${supportMultiple}" onclick="utils.chooseChannel(this,${isFlag})">选择</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="panel-body table_wrap">
                        <div class="hidden-y-scroll">
                            <table id="channel_table" />
                        </div>
                    </div>
                    <p id="channel-pager"></p>
                </div>
             `
        return new Promise((resolve, reject) => {
            let channelDia = utils.dialog({
                title: '业务类型列表',
                content: _html,
                width: $(window).width() * 0.6,
                height: $(window).height() * 0.7,
                data: {},
                onclose: function () {
                    if (this.returnValue) {
                        let data = this.returnValue;
                        resolve(data);
                    } else {
                        reject()
                    }
                }
            }).showModal();
            window.top.channelDia = channelDia;
            let options = {
                url: '/proxy-sysmanage/common/channel/listPage',
                postData: {
                    status
                },
                colNames: ['业务类型编码', '业务类型', '类别', '是否启用'],
                colModel: [
                    {
                        name: 'channelCode',
                        index: 'channelCode',
                        hidden: true,
                        width: 50
                    },
                    {
                        name: 'channelCode',
                        index: 'channelCode',
                        width: 60
                    },
                    {
                        name: 'parentCode',
                        index: 'parentCode',
                        width: 60,
                        formatter: function (e) {
                            if (e == '1') {
                                return '药帮忙'
                            } else if (e == '2') {
                                return '宜块钱'
                            } else if (e == '3') {
                                return '控销'
                            } else if (e == '4') {
                                return "委托存储配送";
                            } else if (e == '5') {
                                return "线下业务";
                            } else if (e == '100') {
                                return "直供";
                            } else if (e == '102') {
                                return "加联盟业务";
                            } else if (e == '105') {
                                return "POP-FBP";
                            }
                        },
                        unformat: function (e) {
                            if (e == '药帮忙') {
                                return '1';
                            } else if (e == '宜块钱') {
                                return '2';
                            } else if (e == '控销') {
                                return '3';
                            } else if (e == "委托存储配送") {
                                return "4";
                            } else if (e == "线下业务") {
                                return "5";
                            } else if (e == "直供") {
                                return "100";
                            } else if (e == "加联盟业务") {
                                return "102";
                            } else if (e == "POP-FBP") {
                                return "105";
                            }
                        }
                    },
                    {
                        name: 'status',
                        index: 'status',
                        hidden: true,
                        width: 50
                    }, {
                        name: "channelName",
                        index: "channelName",
                        hidden: true,
                    }
                ],
                key: 'channelCode',
                rowNum: 20,
                rowList: [20, 50, 100],
                altRows: true,//设置为交替行表格,默认为false
                pager: '#channel-pager',
            }
            supportMultiple == '1' ? options['selectandorder'] = true : options['rownumbers'] = true;
            $('#channel_table').XGrid(options);
        })
    },
    // 业务类型类型 下拉框切换
    channelChange: function (el) {
        let _val = $(el).val();
        $("#channelName, #searchChannel_btn").prop('disabled', (_val == '1' || _val == '2') ? true : false);
        if (_val == '1' || _val == '2') {
            $("#channelName").val(utils.getOptText(el, $('#channelType option:selected').val()));
        } else {
            $("#channelName").val('');
        }
        $('#channel_table').XGrid('setGridParam', {
            postData: {
                "parentCode": $('#channelType option:selected').val(),
                "channelName": $("#channelName").val().trim(),
                "status": $('#searchChannel_btn').attr('data-status')
            }, page: 1
        }).trigger('reloadGrid');
        if (_val == '1' || _val == '2') {
            setTimeout(function () {
                let rowdata = $('#channel_table').XGrid('getRowData');
                $('#channel_table #' + rowdata[0]['id']).click();
                $('#channel_table #' + rowdata[0]['id']).attr('nosele', true);
            }, 100)
        } else {
            setTimeout(() => {
                $('#channelDialog_div .XGridHead input[type=checkbox]').prop('checked', false);
            }, 100)
        }
    },
    // 业务类型查询
    searchChannel: function (el) {
        $('#channel_table').XGrid('setGridParam', {
            postData: {
                "parentCode": $('#channelType option:selected').val(),
                "channelName": $("#channelName").val().trim(),
                "status": $(el).attr('data-status')
            }, page: 1
        }).trigger('reloadGrid');
    },


    // 近效期计算
       // 近效期计算
       computedDialog: function (el) {
        if ($('#orgCode').val()==''){//集团的才需要进行此操作
            utils.dialog({
                title: '提示',
                content: '尚未选择机构,请先选择',
                okValue: '确定',
                ok: function () {}
            }).showModal();
            return false
        }

       let createTime = $('#createTime').val()
       let endTime = $('#endTime').val()
       let currentTime = $('#currentTime').val()
       if(!createTime || !endTime || !currentTime){
        utils.dialog({content: '请将数据填写完整', quickClose: true, timeout: 2000}).show();
        return
       }
       
       let datas;
       
        datas = JSON.stringify({
            manufactureDate:createTime,
            validTo:endTime,
            currDate:currentTime,
            orgCode:$("#orgCode")?$("#orgCode").val():undefined
        })

       //请求数据和接下来的逻辑
       $.ajax({
        type: "post",
        url: '/proxy-purchase/purchase/purchaseOrder/calValidityDateNew',
        contentType: "application/json; charset=utf-8",
        data: datas,
        success: function (data) {
            if (data.code === 1) {
              utils
                .dialog({
                  title: "提示",
                  content: data.msg,
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
            } else {
                $('#isjxq').text(data.result.validityDateStr);
                if(data.result.validityDateStr === '是'){
                    $('#jxqTime').hide()
                    $('#jxqTimets').hide()
                }else{
                    $('#jxqTime').show()
                    $('#jxqTimets').show()
                    $('#jxqTime').text(data.result.validityDateDays.toString());
                }
                
            }
          }
        })
    },

    // computedDialog: function (el) {
    //     if ($('#orgCode').val() == '') {//集团的才需要进行此操作
    //         utils.dialog({
    //             title: '提示',
    //             content: '尚未选择机构,请先选择',
    //             okValue: '确定',
    //             ok: function () { }
    //         }).showModal();
    //         return false
    //     }

    //     let createTime = $('#alertCreateTime').val()
    //     let endTime = $('#endTime').val()
    //     let currentTime = $('#alertCurrentTime').val()
    //     let productCode = $('#alertProductCode').val()

    //     if (!createTime || !productCode || !currentTime) {
    //         utils.dialog({ content: '商品编码、生产日期、入库时间为必填项，不可为空！', quickClose: true, timeout: 2000 }).show();
    //         return
    //     }

    //     let datas;

    //     datas = JSON.stringify({
    //         manufactureDate: createTime,
    //         productCode: productCode,
    //         currDate: currentTime,
    //         orgCode: $("#orgCode") ? $("#orgCode").val() : undefined
    //     })

    //     //请求数据和接下来的逻辑
    //     $.ajax({
    //         type: "post",
    //         url: '/proxy-purchase/purchase/purchaseOrder/calValidityDate',
    //         contentType: "application/json; charset=utf-8",
    //         data: datas,
    //         success: function (data) {
    //             if (data.code === 1) {
    //                 utils
    //                     .dialog({
    //                         title: "提示",
    //                         content: data.msg,
    //                         okValue: "确定",
    //                         ok: function () { },
    //                     })
    //                     .showModal();
    //             } else {
    //                 $('#isjxq').text(data.result.validityDateStr);
    //                 if (data.result.validityDateStr === '是') {
    //                     $('#jxqTime').hide()
    //                     $('#jxqTimets').hide()
    //                 } else {
    //                     $('#jxqTime').show()
    //                     $('#jxqTimets').show()
    //                     $('#jxqTime').text(data.result.validityDateDays.toString());
    //                 }

    //                 $('#indateStr').val(data.result.indateStr)
    //                 $('#endTime').val(data.result.productValidityDate)
    //             }
    //         }
    //     })
    // },
    // 业务类型选择
    chooseChannel: function (el, isFlag) {
        let mult = $(el).attr('data-mult');
        let finalRow = [];
        let channelType = $('#channelType option:selected').val();
        let selRow = $('#channel_table').XGrid('getSeleRow');
        /* begin   需求临时变动  注掉下面ifelse  增加临时代码*/
        // if (channelType == '' || channelType == '3') {
        //     if (mult == '1') { // 多选
        //         finalRow = selRow.length == 0 ?  $('#channel_table').XGrid('getRowData') : selRow;
        //     }else{
        //         finalRow = selRow;
        //     }
        // }else{ // 药帮忙 宜块钱
        //     finalRow = selRow;
        // }
        if (selRow && selRow.length > 0) {
            if (isFlag) {
                for (var i = 0; i < selRow.length; i++) {
                    if (selRow[i].channelCode == '312') {
                        utils.dialog({
                            title: "提示",
                            width: 200,
                            height: 80,
                            content: "选择调整后业务类型时,业务类型为312的不可选",
                            timeout: 2000,
                        }).showModal();
                        return;
                    }
                }
            }
            for (var i = 0; i < selRow.length; i++) {
                selRow[i].channelValue = selRow[i].channelName;
                selRow[i].channelName = selRow[i].channelCode;
            }
        }
        finalRow = selRow; // 临时代码
        /*end  需求临时变动， 需求改回去时，临时代码删掉 */

        /*  开始  需求再一次改动， 不要全部和控销全部了。 允许 不选择就返回了，为了清空 业务类型输入框的值  */
        // if (finalRow.length == 0){
        //     utils.dialog({content: '请先选中数据.', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }
        /*  结束   需求再一次改动， 不要全部和控销全部了。 允许 不选择就返回了，为了清空 业务类型输入框的值  */

        /*  需求变动，临时注掉 */
        // if (finalRow.length == $('#channel_table').XGrid('getRowData').length){
        //     if (channelType == '' || channelType == '3') {
        //         finalRow = [
        //             {
        //                 channelName: channelType == '' ? '全部' : '控销全部',
        //                 channelCode: channelType == '' ? '99999' : '88888'
        //             }
        //         ]
        //     }
        // }
        let diaObj = window.top.channelDia;
        diaObj.close(finalRow).remove();
    },
    // 保留两位小数，如果没有小数位，后面挂.00, 最大位数 size, 超出size 值转换为999999999999.99
    TwoDecimalHndle(el, size, cb) {
        let v = $(el).val();
        v = v.replace(/[^\d.]/g, '')
        if (v.split('.').length > 2) {
            let lastIndex = v.lastIndexOf('.')
            v = v.substring(0, lastIndex) + v.substring(lastIndex + 1)
        }
        v = v.replace(/[^\d.]/g, '').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
        if (v.indexOf('.') == 0) {
            v = v.replace('.', '');
        }
        if (v.indexOf('0') == 0) {
            if (v.length == 1) { // 0000000
                v = '0'
            } else {
                if (v.indexOf('0.') == 0) {
                } else {
                    v = v.substring(1)
                }
            }
        }
        if (size) {
            let n = ''
            for (let i = 0; i < size; i++) {
                n += '9';
            }
            n += '.99';
            if (v.indexOf('.') > 0) {
                if (v.substring(0, v.indexOf('.')).length > size) v = n;
            } else {
                v = v.length > 12 ? n : v
            }
        };
        $(el).val(v);
    },
    // 后面挂.00,
    toDecimal2(x) {
        if (isNaN(parseFloat(x))) return '';
        let f = Math.round(x * 100) / 100;
        let s = f.toString();
        let rs = s.indexOf('.');
        if (rs < 0) {
            rs = s.length;
            s += '.';
        }
        while (s.length <= rs + 2) {
            s += '0';
        }
        return s;
    },
    /**
     * 获取url 中指定参数的值 并返回
     * @param name
     * @returns {*}
     */
    getQueryString: (name) => {
        let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        let r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        };
        return null;
    }
}

/*************************     方法扩展到$中   *****************************/
//序列化form为JSON
$.fn.serializeToJSON = function () {
    var o = {},
        a = this.serializeArray();
    $.each(a, function () {
        if (o[this.name]) {
            if (!o[this.name].push) {
                o[this.name] = [o[this.name]];
            }
            o[this.name].push(this.value || '');
        } else {
            o[this.name] = this.value || '';
        }
    });
    return o;
}

//JSON反序列化为form
$.fn.JSONToform = function (jn) {
    var _this = this;
    var _thisDom = $(this).find('input,select,textarea,radio');
    if (Object.prototype.toString.call(jn) == '[object Object]') {
        $.each(jn, function (e, v) {
            var self = $(_this).find('[name=' + e + ']');
            if (self && self.prop('type') == 'radio') {
                //$(_this).find('[name=' + e + '][value=' + v + ']').prop('checked', 'checked');
                $(_this).find('[name=' + e + '][value=' + v + ']').attr('checked', 'checked');
            } else if (self && self.prop('type') == 'checkbox') {
                v ? self.attr('checked', 'checked') : self.attr('checked', false);
            } else if (self && self.prop('type') == 'select-one') {
                v = v ? v : '""';
                self.find('option[value=' + v + ']').attr('selected', 'selected').siblings().removeAttr('selected');
            } else {
                self.attr('value', v);
                //self.val(v);
            }

        })


    } else if (_thisDom.length == 1) {
        var self = _thisDom;

        if (self && self.prop('type') == 'radio') {
            $(_this).prop('checked', 'checked');
        } else if (self && self.prop('type') == 'checkbox') {
            (jn && jn != 'false') ? self.attr('checked', 'checked') : self.attr('checked', false);
        } else if (self && self.prop('type') == 'select-one') {
            self.find('option[value="' + jn + '"]').attr('selected', 'selected').siblings().removeAttr('selected');
        } else if (self && self.prop('type') == 'textarea') {
            $(self).text(jn);
        } else {
            self.attr('value', jn);
        }
    }
}
//dom序列化为JSON，类似form序列化
$.fn.domToJSON = function () {
    var _this = this;
    var _thisDom = $(this).find('input,select,textarea,checkbox');
    var o = {};
    //如果只有一个元素，直接返回值
    if (_thisDom.length == 1) {
        $.each(_thisDom, function (e, v) {
            var self = $(v);
            if (self && self.prop('type') == 'radio') {
                self.prop('checked') ? o = self.val() : '';
            } else if (self && self.prop('type') == 'checkbox') {
                o = self.prop('checked');
            } else {
                o = self.val();
                //self.val(v);
            }
        })
    } else {
        $.each(_thisDom, function (e, v) {
            var self = $(v);
            if (self && self.prop('type') == 'radio') {
                self.prop('checked') ? o[self.prop('name')] = self.val() : '';
            } else if (self && self.prop('type') == 'checkbox') {
                o[self.prop('name')] = self.prop('checked');
            } else {
                o[self.prop('name')] = self.val();
                //self.val(v);
            }
        })
    }


    return o;
}

/******************************  插件部分  ******************************/
//模块折叠
$.fn.fold = function () {
    var t = $(this).find('[fold=sub]').length ? $(this).find('[fold=sub]') : $(this);
    t.find('div').css({
        'cursor': 'pointer',
        'transition': ' transform 1s',
        'transform': 'rotate(0deg)'
    });
    $(t).on('click', function () {
        var p = $(this).parents('[fold=head]') || $(this);
        var that = $(this).find('div');
        that.hasClass('fold-show') ? that.removeClass('fold-show') : that.addClass('fold-show');
        p.next('[fold=body]').stop().slideToggle();
    })
}

//字典检索

$.fn.Xseach = function (par) {
    var t = this;
    var ul = $('<ul class="word" ></ul>');
    // $(t).wrap('<div class="search_Box"></div>')
    $(t).after(ul)
    //失去焦点事件
    $(t).blur(function (e) {
        setTimeout(function () {
            ul.find("li").remove();
            ul.css("display", "none");

        }, 1000);
    });

    ul.on('click', 'li', function (e) {
        var word = $(this).text();
        // console.log(word);
        $(t).val(word);
        ul.find('li').remove();
    });

    //这里 blur事件的触发，比on绑定要先执行，所以要加延时执行，否则将拿不到word

    function myFunction() {
        var tab = ul[0].childNodes;
        var li = ul.find("li");
        // console.log('出发oninput事件');
        var keywords = $(t).val();
        // var keywords = document.getElementById("text").value;
        if (keywords == '') {
            ul.hide();
            return
        }
        ul.css({
            position: 'absolute',
            'z-index': '99',
            background: '#fff',
            top: '34px',
        });
        $.ajax({
            url: par.url + '/su?wd=' + keywords,
            dataType: 'jsonp',
            jsonp: 'cb', //回调函数的参数名(键值)key
            // jsonpCallback: 'fun', //回调函数名(值) value
            beforeSend: function () {
                ul.append('<div>正在加载...</div>');
                ul.css("display", "block")
            },
            success: function (data) {
                ul.empty().show();
                if (data.s == '') {
                    ul.append('<div class="error">找不到  "' + keywords + '"</div>');
                    ul.css("display", "block")
                }
                $.each(data.s, function () {
                    ul.append('<li class="clicks">' + this + '</li>');
                    ul.css("display", "block")
                })
            },
            error: function () {
                ul.empty().show();
                ul.append('<div class="click_work">Fail "' + keywords + '"</div>');
            }
        })


        var i = 0;
        document.onkeydown = function (e) {
            //上
            if (tab.length) {
                var j;
                if (e.keyCode == 40) {
                    for (j = 0; j < tab.length; j++) {
                        tab[j].className = "ele";
                        if (tab[j].className == "ele") {
                            tab[j].className = "";
                        }
                    }
                    if (i < tab.length) {
                        tab[i].className = "ele";
                        var word = tab[i].innerHTML;
                        t.val(word);
                        i = i + 1;
                        if (i == tab.length) {
                            i = 0;
                            j = 0;
                        }
                    }
                }
                //下

                if (e.keyCode == 38) {
                    m = 0
                    for (; m < tab.length; m++) {
                        if (tab[m].className == "ele") {
                            tab[m].className = "";
                            break;
                        }
                    }
                    i = m;
                    if (m > 0) {
                        tab[m - 1].className = "ele";
                        var word = tab[m - 1].innerHTML;
                        t.val(word);
                    } else {
                        tab[tab.length - 1].className = "ele";
                    }
                }
                //enter
                if (e.keyCode == 13) {
                    ul.find("li").remove();
                    ul.css("display", "none")
                }
            }

        }
    }

    $(t).on('input', myFunction);

    // var ul = document.getElementById("word");
}

/**
 * 上传图片
 * @param urlBack  Function 点击确定的回调函数
 * @param typeList Array    可选择的类型 {text:xxx,value:xxx}
 * @param fileParam JSON   必传 图片名称、图片地址 字段名  eg:{name:xxx,url:xxx}
 *  @param eChoImgList Array 可不传 存放回显图片数据 [{name:xx,type:xxx,url:xxxx}]
 */
$.fn.upLoad = function (parmas, onlyAllowPNGJPG) {
    //urlBack
    var typeList = parmas.typeList; //图片显示类型
    var imageType = ['.jpg', '.png', '.jpeg'];
    var textName = [];
    if (typeList.length > 0) {
        for (var a = 0; a < typeList.length; a++) {
            if (typeList[a].value == '') {
                utils.dialog({
                    width: 300,
                    title: '提示',
                    content: '请选择附件类型',
                    okValue: '确定',
                    ok: function () {
                    }
                }).showModal();
                return false;
            }
        }
    } else {
        utils.dialog({
            width: 300,
            title: '提示',
            content: parmas.message || '请新增行并选择附件类型',
            okValue: '确定',
            ok: function () {
            }
        }).showModal();
        return false;
    }

    var thisId = this[0].id;
    var upLoadDialog = 'upload_Dialog' + thisId //弹框外层id
    var fileBtn = 'fileBtn_' + thisId; //弹框内上传按钮
    var imgBox = 'imgcontain' + thisId; //存放图片div
    var eChoImgList = parmas.eChoImgList || []; //存放回显图片数据
    var fileParam = parmas.fileParam; //存存储与数据字段名 ：图片名称、图片地址
    if (!fileParam) {
        alert('请传入数据库对应的 图片名称、图片地址 对应的字段');
        return false
    }


    var upload_Dialog_Html = '<div class="upload_Dialog" id="' + upLoadDialog + '"><div id="' + imgBox + '" class="imgcontain result" name="result">' +
        '<p class="imgFile" id="imgFileP"><input class="fileload" type="file" accept="image/gif,image/jpg,image/jpeg,image/png" multiple="multiple" id="' + fileBtn + '"/></p>' +
        '</div></div>';




    //打开弹窗
    utils.dialog({
        title: '批量管理附件',
        width: 700,
        content: upload_Dialog_Html,
        okValue: '保存',
        ok: function () {
            if (parmas.purchaseOrder == 1) {
                //反回数据
                var imgList = initData();
                console.log(imgList.length);
                if(imgList.length > 0){
                    $("#uploadAttachmentspic").text('查看附件').css({"background-color":"#ff8200","border-color":"#ff8200"})
                    $("#editAttachmentspic").text('查看附件').css({"background-color":"#ff8200","border-color":"#ff8200"})
                }else{
                    $("#uploadAttachmentspic").text('上传附件').css({"background-color":"#31b0d5","border-color":"#269abc"})
                    $("#editAttachmentspic").text('上传附件').css({"background-color":"#31b0d5","border-color":"#269abc"})
                }
                parmas.urlBack && parmas.urlBack(imgList);
                return
                
            }
            for (var i = 0; i < $(".viewImgItem").length; i++) {
                var type = $(".viewImgItem").eq(i).find("select option:selected").val();
                if (type == '' || !type) {
                    utils.dialog({
                        width: 200,
                        title: '提示',
                        content: '请选择附件类型',
                        okValue: '确定',
                        ok: function () {
                        }
                    }).showModal();
                    return false;
                }
            }
            //检测是否有重复名称
            var f = false;
            $(".viewImgItem").each(function () {
                var flag = false;
                var iImgName = $.trim($(this).find('.viewImgName input').val());
                $(".viewImgItem").not($(this)).each(function () {
                    var name = $.trim($(this).find('.viewImgName input').val());
                    if (name == iImgName) {
                        flag = true;
                        return false;
                    }
                })
                if (flag) {
                    f = true;
                    return false;
                }
            })
            if (f) {
                utils.dialog({
                    width: 200,
                    title: '提示',
                    content: '附件名称不可重复',
                    okValue: '确定',
                    ok: function () {
                    }
                }).showModal();
                return false;
            }
            //反回数据
            var imgList = initData();
            parmas.urlBack && parmas.urlBack(imgList);
        },
        cancelValue: '取消',
        cancel: function () {
        }
    }).showModal();


    //以下弹框初始化完成后代码--------------

    //拼装类型select框
    var osHTML = optionsHTML(typeList);
    //显示已存在图片
    initShowImg(eChoImgList);


    //弹框内file
    $('#' + fileBtn).change(function () {
        var that = this;
        var file = this.files;
        var maxsize = 4 * 1024 * 1024; //2M
        if (parmas.maxSize) {
            maxsize = parmas.maxSize
        }
        console.log(file);
        //仅允许png和jpg
        if (onlyAllowPNGJPG) {
            chooseFileWhichOnlyAllowPNGJPG(that, file, maxsize)
            return
        }

        for (var i = 0; i < file.length; i++) {
            var size = file[i].size; //图片大小

            var maxSizeStr = Math.floor(maxsize / 1024 / 1024 * 10) / 10;
            if (size > maxsize) {
                utils.dialog({ content: file[i].name + '图片过大，单张图片大小不超过' + maxSizeStr + 'M', timeout: 2000 }).show();
                that.value = '';
                return false;
            }
        }
        sendFile(file);
    });

    //图片删除按钮
    $('#' + upLoadDialog).on("click", ".closeBtn", function () {
        var _this = $(this);
        utils.dialog({
            width: 200,
            title: '提示',
            content: '确定删除？',
            okValue: '确定',
            ok: function () {
                _this.parent().remove();
            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).showModal();

    })
    //切换类型时改变附件名称
    $('#imgcontain' + thisId).on('change', '.imgTypeSel', function () {
        var inp = $(this).parents('.viewImgItem').find('.viewImgName input');
        var val = $.trim($(this).find('option:selected').text());
        /**
         * RM 2018-10-10
         * lineNum ： 批准文件 证书类型，。产品要求选中项不再被禁用，
         * 类型放开之后，如果多行重复，在上传附件的时候，用行号做一个标识
         * 上传文件之后知道给同类型的哪一行增加了附件，回显的时候 给对应行的附件数量重新赋值
         */
        var lineNum = $.trim($(this).find('option:selected').attr('data-lineNum'));
        var arr = [], indexArr = [], index = 1;
        var optArr = [];
        $('.imgTypeSel').not(this).each(function () {
            var that = this;
            var otherVal = $.trim($(that).find('option:selected').text())
            if (otherVal == val) {
                var ind = $(that).next('p').find('input').val().lastIndexOf('-');
                index = $(that).next('p').find('input').val().substr(ind + 1);
                if (!checkNumber(index)) { index = null; }
                optArr.push(index)
            }
        });
        if (optArr.length > 0) {
            index = Math.max.apply(null, optArr) + 1;
        }
        var s = '-' + index;
        if (val === '请选择') {
            inp.val('');
            $(this).parents('.viewImgItem').find('img').attr('data-type', '');
            $(this).parents('.viewImgItem').find('img').attr('data-lineNum', '');
        } else {
            inp.val(val + '' + s);
            $(this).parents('.viewImgItem').find('img').attr('data-type', val);
            $(this).parents('.viewImgItem').find('img').attr('data-lineNum', lineNum);
        }
    })
    //判断是否为数字。
    function checkNumber(theObj) {
        var reg = /^[0-9]+.?[0-9]*$/;
        if (reg.test(theObj)) {
            return true;
        }
        return false;
    }

    //特殊处理仅仅允许选择png和jpg的场景
    function chooseFileWhichOnlyAllowPNGJPG(that, file, maxsize) {
        var typeError = false;
        for (var i = 0; i < file.length; i++) {
            var type = file[i].type;
            if (!(type == 'image/png' || type == 'image/png' || type == 'image/jpeg')) {
                typeError = true;
            }
        }
        if (typeError) {
            utils.dialog({
                width: 200,
                title: '提示',
                content: '因电子签章所限，只支持上传jpg、png两种格式的图片。',
                okValue: '确定',
                ok: function () {
                }
            }).showModal();
            return false
        }

        for (var i = 0; i < file.length; i++) {
            var size = file[i].size; //图片大小
            var maxSizeStr = Math.floor(maxsize / 1024 / 1024 * 10) / 10;
            if (size > maxsize) {
                utils.dialog({ content: file[i].name + '图片过大，单张图片大小不超过' + maxSizeStr + 'M', timeout: 2000 }).show();
                that.value = '';
                return false;
            }
        }
        sendFile(file);
    }

    //往服务器发送图片
    function sendFile(files) {
        textName.unshift(files[0].name);
        var formData = new FormData();
        for (var i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }
        var d = utils.dialog({
            content: '正在上传..'
        }).showModal();
        $.ajax({
            url: '/proxy-sysmanage/upload/upload',
            data: formData,
            type: 'post',
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function (data) {
                $('#' + fileBtn).val(''); //清空按钮value
                d.close().remove();
                if (data.code == 0) {
                    utils.dialog({
                        title: '提示',
                        width: '200px',
                        content: '上传成功',
                        okValue: '确定',
                        ok: function () {
                        }
                    }).showModal();
                    var imgArr = data.result;
                    if (parmas.purchaseOrder == 1) {
                        html = '';
                        html += imgHTML(...imgArr, textName[0]);
                    } else {
                        var imgArr = data.result;
                        var html = '';
                        for (var i = 0; i < imgArr.length; i++) {
                            html += imgHTML(imgArr[i]);
                        }
                    }
                    $('#' + imgBox).prepend(html);
                    setTimeout(function () {
                        $('#imgcontain' + thisId + ' .imgTypeSel:first').trigger('change')
                    })
                } else {
                    utils.dialog({
                        title: '提示',
                        width: '200px',
                        content: '上传失败',
                        okValue: '确定',
                        ok: function () {
                        }
                    }).showModal();
                }
            },
            error: function () {
                $('#' + fileBtn).val('');
                d.close().remove();
            }
        })
    }

    //如果存在附件则展示已存在附件图片
    function initShowImg(arr) {
        if (arr.length > 0) {
            var html = '';
            for (let i = 0; i < arr.length; i++) {

                let name = arr[i][fileParam.name];
                var url = arr[i][fileParam.url];
                /**
                 * 生产环境中存在部分异常数据，其 name 为空，将导致后续的 indexOf、lastIndexOf 函数异常。
                 *
                 * 此处对 name 进行判断，若 name 为空，则为其设置默认值，避免出现上述问题。
                 **/
                if (!name || name.length === 0) {
                    name = i + ''
                }
                // console.log(name);
                // console.log(i);

                var type = arr[i].type;
                var lineNum = arr[i].lineNum;
                let contractId = arr[i]['contractId'];
                let pictureStatus = arr[i]['pictureStatus'];
                let signatureImageUrl = arr[i]['signatureImageUrl'];
                if(parmas.purchaseOrder == 1){
                    html += imgHTML(url, name, type, lineNum, { contractId, pictureStatus, signatureImageUrl });
                }else{
                    if (name.indexOf('-') != name.lastIndexOf('-')) {
                        name = type + name.substr(name.lastIndexOf('-'))
                    }
                    if (type == undefined) {
                        type = name.substr(0, name.lastIndexOf('-'));
                    }
                    var ind = name.lastIndexOf('-'); // 图片上传保存之后，用户又改变了委托书编号。相应的需要对以保存的附件的名称做修改
                    if (ind < 0) {
                        var newName = type + '-' + name.substr(ind + 1);
                        html += imgHTML(url, newName, type, lineNum, { contractId, pictureStatus, signatureImageUrl });
                    } else {
                        html += imgHTML(url, name, type, lineNum, { contractId, pictureStatus, signatureImageUrl });
                    }
                }
                

            }
            $('#' + imgBox).prepend(html);
            $(".viewImgItem").each(function () {
                var _this = this;
                var type = $(_this).find("img").attr('data-type');
                var imgLineNum = $(_this).find("img").attr('data-lineNum');
                $(_this).find("select option").each(function () {
                    var _that = this;
                    var val = $(_that).val();
                    var optLineNum = $(_that).attr('data-lineNum');
                    if (val == type && imgLineNum == optLineNum) {
                        $(_that).prop("selected", true);
                        return false;
                    }
                });
            });
        }
    }

    //初始化返回数据
    function initData() {
        var arr = [];
        var json = {}
        if (parmas.purchaseOrder == 1) {
            $(".viewImgItem").each(function (index, item) {
                json = {};
                let imgname = $(item).find("img")[0]
                var type = $(item).find("select option:selected").val();
                var name = ($(item).find("a")[0]) ? $(item).find("a")[0].innerText : $(imgname).attr('alt');
                var url = $(item).find('img').attr('src') || $(item).find('a').attr('href');
                var lineNum = $(item).find('img').attr('data-lineNum');
                let contractId = $(item).find('img').attr('data-contractId');
                let pictureStatus = $(item).find('img').attr('data-pictureStatus');
                let signatureImageUrl = $(item).find('img').attr('data-signatureImageUrl');
                json.type = type;
                json.lineNum = lineNum;
                json[fileParam.name] = name;
                json[fileParam.url] = url;
                json['contractId'] = contractId
                json['pictureStatus'] = pictureStatus
                json['signatureImageUrl'] = signatureImageUrl
                arr.push(json);
            });
        } else {
            $(".viewImgItem").each(function (index, item) {
                json = {};
                var type = $(item).find("select option:selected").val();
                var name = $(item).find("input").val();
                var url = $(item).find('img').attr('src') || $(item).find('a').attr('href');
                var lineNum = $(item).find('img').attr('data-lineNum');
                let contractId = $(item).find('img').attr('data-contractId');
                let pictureStatus = $(item).find('img').attr('data-pictureStatus');
                let signatureImageUrl = $(item).find('img').attr('data-signatureImageUrl');
                json.type = type;
                json.lineNum = lineNum;
                json[fileParam.name] = name;
                json[fileParam.url] = url;
                json['contractId'] = contractId
                json['pictureStatus'] = pictureStatus
                json['signatureImageUrl'] = signatureImageUrl
                arr.push(json);
            });
        }

        return arr;
        // var picture = {}; //存放图片数据
        // for (var i = 0; i < typeList.length; i++) {
        //     picture[typeList[i].value] = [];
        // }
        // var type = ''; //图片type值
        // for (var i = 0; i < arr.length; i++) {
        //     type = arr[i].type;
        //     var dataJson = {};
        //     dataJson[fileParam.name] = arr[i].name;
        //     dataJson[fileParam.url] = arr[i].url;
        //     dataJson.type = type;
        //     if (picture[type]) {
        //         //对已存在type类型图片进行去重合并
        //         picture[type] = Array.from(new Set(picture[type].concat([dataJson])));
        //     } else {
        //         picture[type] = [dataJson];
        //     }
        // }
        // return picture;
    }

    //拼装类型select
    function optionsHTML(arr) {
        var optionsHTML = '<option value="">请选择</option>';
        if (arr.length === 1) {
            optionsHTML += '<option value="' + arr[0].value + '" data-lineNum="' + arr[0].lineNum + '" selected>' + arr[0].text + '</option>';
        } else {
            for (var i = 0; i < arr.length; i++) {
                optionsHTML += '<option value="' + arr[i].value + '" data-lineNum="' + arr[i].lineNum + '">' + arr[i].text + '</option>';
            }
        }
        return optionsHTML;
    }

    //获取缩略图html
    function imgHTML(url, name, type, lineNum, _params) {
        if (!_params) {
            _params = {
                contractId: '',
                pictureStatus: '',
                signatureImageUrl: ''
            }
        }
        //上传附件判断是图片还是其他类型
        if (parmas.purchaseOrder == 1) {
            var url = url || '',
                name = name || '',
                type = type || '',
                lineNum = (lineNum == 0 ? '0' : lineNum) || '';
            var suffix = url.substring(url.lastIndexOf(".")) || name.substring(name.lastIndexOf("."));
            console.log(this);
            console.log(url);
            if (!imageType.includes(suffix)) {
                let html = `<div class="viewImgItem">
                           <span class="closeBtn">×</span>
                           <div class=imgbox>
                                 <a href="${url}" target="_blank" data-type="${type}" data-lineNum="${lineNum}" alt="${name}"
                                 data-contractId="${_params['contractId']}"
                                 data-pictureStatus="${_params['pictureStatus']}"
                                 data-signatureImageUrl="${_params['signatureImageUrl']}">${name}</a>
                           </div>
                    </div>`
                return html;
            }
            let html = `<div class="viewImgItem">
                           <span class="closeBtn">×</span>
                           <div class=imgbox>
                                 <img src="${url}" data-type="${type}" data-lineNum="${lineNum}" alt="${name}"
                                 data-contractId="${_params['contractId']}"
                                 data-pictureStatus="${_params['pictureStatus']}"
                                 data-signatureImageUrl="${_params['signatureImageUrl']}"/>
                           </div>
                    </div>`
            return html;
        }
        var url = url,
            name = name || '',
            type = type || '',
            lineNum = (lineNum == 0 ? '0' : lineNum) || '';
        let html = `<div class="viewImgItem">
                           <span class="closeBtn">×</span>
                           <div class=imgbox>
                                <img src="${url}" data-type="${type}" data-lineNum="${lineNum}" alt="${name}"
                                 data-contractId="${_params['contractId']}"
                                 data-pictureStatus="${_params['pictureStatus']}"
                                 data-signatureImageUrl="${_params['signatureImageUrl']}"/>
                           </div>
                           <select class="imgTypeSel">${osHTML}</select>
                           <p class="viewImgName"><input type="text" placeholder="附件名称" value="${name}"/></p>
                    </div>`
        // var html = '<div class="viewImgItem"><span class="closeBtn">×</span><div class=imgbox><img src="' + url + '" data-type="' + type + '" data-lineNum="'+lineNum+'" alt="' + name + '" /></div>' +
        //     '<select class="imgTypeSel">' + osHTML + '</select>' +
        //     '<p class="viewImgName"><input type="text" placeholder="附件名称" value="' + name + '"/></p>' +
        //     '</div>';
        return html;
    }
}


//本地字典是否存在utils


/**
 * 图片预览（支持轮播）
 * @param option Object {}
 *
 * @param option.list  图片数据集合 可以是[url,url2,url3] or [{name:1,type:类型,url:xxx},{xxx}]
 *
 * @param option.fileParam  JSON   必传 图片名称、图片地址 字段名  eg:{name:xxx,url:xxx}
 *
 * @param option.index 当前显示的图片下标 Number
 *
 * eg:   $.viewImg({
            fileParam:{
                name:'fileName',
                url:'filePath'
            },
            list:data.enclosureList
        })
 *
 *
 */
$.extend({
    viewImg: function (option) {
        var url = '';
        var urlList = option.list ? option.list : option;
        if (typeof urlList == 'string') {
            urlList = JSON.parse(urlList);
        }
        var imgLen = 0; var a = [];
        if (Array.isArray(urlList)) {
            imgLen = urlList.length;
        } else {
            a.push(urlList)
            urlList = a;
            imgLen = urlList.length;
        }
        if (imgLen < 1 || !urlList) {
            utils.dialog({
                title: '提示',
                width: '200px',
                content: '没有可预览附件',
                okValue: '确定',
                ok: function () {
                }
            }).showModal();
            return false;
        }
        var html = '<div class="viewImgBox"><p class="vTop"></p>\
                <p class="vBottom"></p>\
                <div id="viewImg">\
            	<div class="leftBtn"><i></i></div>\
            	<div class="rightBtn"><i></i></div>\
                <div id="imgList"></div>\
                </div>\
            </div>';
        utils.dialog({
            title: '预览',
            content: html,
            onclose: function () {
                document.onscroll = null;
            }
        }).showModal();
        try {
            var H = document.documentElement.clientHeight || document.body.clientHeight;
            document.onscroll = function () {
                var top = $('.ui-popup-show.ui-popup-focus').offset().top;
                var h = $('.ui-popup-show.ui-popup-focus').height();
                var scrTop = document.documentElement.scrollTop || document.body.scrollTop;
                if (scrTop < top - 10) {
                    document.documentElement.scrollTop = top - 10;
                }
                if (scrTop > (top + h - H)) {
                    document.documentElement.scrollTop = top + h - H;
                }
            }
        } catch (e) {
        }
        var fileParam = option.fileParam ? option.fileParam : false; //存存储与数据字段名 ：图片名称、图片地址
        if (urlList.length < 2) {
            $(".leftBtn,.rightBtn").hide();
        }
        var html = '';
        for (var i = 0; i < urlList.length; i++) {
            if (typeof urlList[i] == 'object' && fileParam) {
                url = urlList[i][fileParam.url];
            } else {
                url = urlList[i];
            }
            var reg = /(.pdf)$/i;
            var h = Math.round(800 * 1.475);
            if (reg.test(url)) {
                html += '<embed src="' + url + '" type="application/pdf" width="100%" height="' + h + '" class="imgView" data-imgurl="' + url + '">';
            } else {
                html += '<img src="' + url + '" class="imgView" style="cursor: pointer" data-imgurl="' + url + '"/>';
            }
        }
        $('#imgList').append(html);
        var viewer
        // 初始化图片预览
        setTimeout(function () {
            var $image = $('#imgList .imgView');

            if ($image.viewer) {
                $image.viewer({
                    viewed: function () {
                        $image.viewer('zoomTo', 1);
                    }
                });
            } else {
                $('#imgList .imgView').off("click").on("click", function () {
                    viewer = new Viewer(document.getElementById('imgList'), {
                        toolbar: true,  //显示工具条
                        button: true,
                        // inline:true,
                        zIndexInline: 3000,
                        button: true,
                        viewed() {
                            viewer.zoomTo(0.5);   // 图片显示比例 75%
                        },
                        show: function () {        // 动态加载图片后，更新实例
                            viewer.update();
                        },
                    });
                })
            }
        });

        var Index = option.index;
        if (Index) {
            setIndex(Index);
        } else {
            setIndex(0);
            Index = 0;
        }
        var n = Number(Index);

        //左右按钮切换
        $("#viewImg").on("click", ".rightBtn i", function () {
            n++;
            // console.log('+', n)
            if (n >= imgLen) {
                n = 0;
            }
            setIndex(n);
        });
        $("#viewImg").on("click", ".leftBtn i", function () {
            n--;
            // console.log('-', n)
            if (n < 0) {
                n = imgLen - 1;
            }
            setIndex(n);
        });

        //设置图片展示层级
        function setIndex(index) {
            index = Math.abs(index)
            $('#imgList .imgView').css({
                "opacity": "0",
                "z-index": "0"
            });
            var tagName = $('#imgList .imgView').eq(index).get(0).tagName;
            if (tagName == 'IMG') {
                $("#imgList").css("overflow-y", "hidden");
            } else {
                $("#imgList").css("overflow-y", "auto");
            }
            $('#imgList .imgView').eq(index).css({
                "opacity": "1",
                "z-index": "1"
            });
            if (fileParam) {
                //显示图片名称与当前索引
                $(".vTop").html(urlList[index][fileParam.name]);
                $(".vBottom").html((index + 1) + '/' + imgLen);
            }
        }
    }
});


// 动态设置require位置
function setRequireRight() {
    var $require = $('.input-group-addon.require');
    if ($require.length) {
        Array.prototype.forEach.call($require, function (el) {
            var $this = $(el);
            $this.prepend('<i class="text-require">*  </i>');
        });
    }
}

// 去掉所有input的autocomplete, 显示指定的除外
$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete',
    'off');

setRequireRight();

//参数：保留几位小数，货币符号，千位分隔符，小数分隔符
Number.prototype.formatMoney = function (places, symbol, thousand, decimal) {
    places = !isNaN(places = Math.abs(places)) ? places : 2;
    symbol = symbol !== undefined ? symbol : "$";
    thousand = thousand || ",";
    decimal = decimal || ".";
    var number = this,
        negative = number < 0 ? "-" : "",
        i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
        j = (j = i.length) > 3 ? j % 3 : 0;
    return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
};

//实体与html互相转换
function transEntity(str, flag) {

    var keys = Object.keys || function (obj) {
        obj = Object(obj);
        var arr = [];
        for (var a in obj) arr.push(a);
        return arr;
    }
    var invert = function (obj) {
        obj = Object(obj);
        var result = {};
        for (var a in obj) result[obj[a]] = a;
        return result;
    }
    var entityMap = {
        escape: {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&apos;'
        }
    }
    entityMap.unescape = invert(entityMap.escape);
    var entityReg = {
        escape: RegExp('[' + keys(entityMap.escape).join('') + ']', 'g'),
        unescape: RegExp('(' + keys(entityMap.unescape).join('|') + ')', 'g')
    };

    if (typeof str !== 'string') return '';

    if (!flag) {
        return str.replace(entityReg.escape, function (match) {
            return entityMap.escape[match]
        })
    }

    return str.replace(entityReg.unescape, function (match) {
        return entityMap.unescape[match]
    });

}


//转大写

function chineseNumber(n) {
    var fraction = ['角', '分'];
    var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    var unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
    var head = n < 0 ? '负' : '';
    n = Math.abs(n);

    var s = '';

    for (var i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor((n * 1000 / 100) * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    s = s || '整';
    n = Math.floor(n);

    for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = '';
        for (var j = 0; j < unit[1].length && n > 0; j++) {
            p = digit[n % 10] + unit[1][j] + p;
            n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }
    return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
}

//检测字符串长度，ps:区分字符和汉字，汉字length=2
function getByteLen(val) {
    return String(val).length + (String(val).match(/[^\x00-\xff]/g) || "").length; // 加上匹配到的全角字符长度
}

//截取字符串， ps:区分字符和汉字，汉字length=2
function getByteVal(val, max) {
    var returnValue = '';
    var byteValLen = 0;
    for (var i = 0; i < val.length; i++) {
        if (val[i].match(/[^\x00-\xff]/ig) != null)
            byteValLen += 2;
        else
            byteValLen += 1;
        if (byteValLen > max)
            break;
        returnValue += val[i];
    }
    return returnValue;
}

// input框设置关闭记忆功能
$("input[type='text'],input[type='number']").attr('autocomplete', 'off');
$(document).on("focus", "input[type='text'],input[type='number']", function () {
    $(this).attr('autocomplete', 'off');
})

//触发父级b.tabs.js关闭jqContextMenuHide函数
$('body').on('click', function () {
    try {
        parent.$.contextMenu.jqContextMenuHide()
    } catch (err) {
        //parent并没有contextMenu.jqContextMenuHide()
    }
})
// 全角括号转半角，在录入的时候，input 绑定 .full2half 类名
$('body').on('input', '.full2half', function (e) {
    // $(this).val($(this).val().replace(/\（/g, '(').replace(/\）/g, ')'))
    let str = $(this).val()
    let result = "";
    for (let i = 0; i < str.length; i++) {
        if (str.charCodeAt(i) == 12288) {
            result += String.fromCharCode(str.charCodeAt(i) - 12256);
            continue;
        }
        if (str.charCodeAt(i) > 65280 && str.charCodeAt(i) < 65375) result += String.fromCharCode(str.charCodeAt(i) - 65248);
        else result += String.fromCharCode(str.charCodeAt(i));
    }
    $(this).val(result);

})

//公用失去焦点事件&按下事件校验
$('body').on('blur', '.Filter_SpaceAndStrLen_Class', function () {
    //去掉前后空格
    this.value = this.value.replace(/^ +| +|\u3000/g, '');
}).on('keyup input', '.Filter_SpaceAndStrLen_Class', function () {
    //限制长度为50个字符||25个汉字
    if (getByteLen(this.value) > 60) {
        this.value = getByteVal(this.value, 60);
    }
})

//公用失去焦点事件&按下事件校验  开户银行新增要求  内容长度限制改为40个汉字
$('body').on('blur', '.Filter_SpaceAndStrLen_bank_Class', function () {
    //去掉前后空格
    this.value = this.value.replace(/^ +| +|\u3000/g, '');
}).on('keyup input', '.Filter_SpaceAndStrLen_Class', function () {
    //限制长度为80个字符||40个汉字
    if (getByteLen(this.value) > 80) {
        this.value = getByteVal(this.value, 80);
    }
})
//备注&凭证摘要 字符限制
$('body').on('blur', '.Filter_SpaceAndFiveStrLen_Class', function () {
    //去掉前后空格
    this.value = this.value.replace(/^ +| +|\u3000/g, '');

    //禁止<后/
    // this.value = this.value.replace(/<\//g, '<');

}).on('keyup input', '.Filter_SpaceAndFiveStrLen_Class', function () {
    //限制长度为100个字符||50个汉字
    if (getByteLen(this.value) > 100) {
        this.value = getByteVal(this.value, 100);
    }

    //禁止<后/
    // this.value = this.value.replace(/<\//g, '<');

})

//审批意见 400 字符限制
$('body').on('blur', '.Filter_SpaceAnd400StrLen_Class', function () {
    //去掉前后空格
    this.value = this.value.replace(/^ +| +|\u3000/g, '');
}).on('keyup input', '.Filter_SpaceAnd400StrLen_Class', function () {
    //限制长度为100个字符||50个汉字
    if (getByteLen(this.value) > 800) {
        this.value = getByteVal(this.value, 800);
    }

})

// 200 字符限制
$('body').on('blur', '.Filter_SpaceAnd200StrLen_Class', function () {
    //去掉前后空格
    this.value = this.value.replace(/^ +| +|\u3000/g, '');
}).on('keyup input', '.Filter_SpaceAnd200StrLen_Class', function () {
    //限制长度为400个字符||200个汉字
    if (getByteLen(this.value) > 400) {
        this.value = getByteVal(this.value, 400);
    }

})

//table表格内有效期时间控制
function getToday() {
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    var d = date.getDate();
    return y + '-' + ((m < 10) ? '0' + m : m) + '-' + ((d < 10) ? '0' + d : d)
}
//XGrid 中 使用  有效期开始时间
function begDate(that, name) {
    var parentId = $(that).parent().attr('id');
    $(that).attr('id', parentId + 'inp');
    var begDate = $.trim($(that).val());
    var endInp = $(that).parents('tr').find('input[name="' + name + '"]');
    var endDate = $.trim(endInp.val());
    if (begDate != '' && endDate != '' && compareDate(endDate, begDate)) {
        endInp.val(getdifference(begDate, 1));
    }
}

//XGrid 中 使用    有效期截止时间
function endDate(that, name, fn) {
    var begDateId = $(that).parents('tr').find('input[name="' + name + '"]').attr('id');
    if (!begDateId || begDateId == '') {
        var parentId = $(that).parents('tr').find('input[name="' + name + '"]').parent().attr('id');
        begDateId = parentId + 'inp';
        $(that).parents('tr').find('input[name="' + name + '"]').attr('id', begDateId)
    }
    var options = {};
    if (begDateId && begDateId != '') {
        // 当起始日期是今天时，截至日期大于等于今天
        //当起始日期不是今天时，截至日期大于等于今天
        var begDate = $.trim($(that).val());
        var startTime = $('#' + begDateId).val();
        if (startTime == getToday()) {
            options.minDate = '#F{$dp.$D(\'' + begDateId + '\',{d:0})}';
        } else {
            if (startTime < getToday()) {
                options.minDate = '%y-%M-%d';
            } else {
                options.minDate = '#F{$dp.$D(\'' + begDateId + '\',{d:1})}';
            }
        }

    }
    if (fn) {
        options.onpicked = function () {
            fn();
        }
    }
    WdatePicker(options);
}


//XGrid 中 使用    有效期截止时间
function endDate2(that, name, fn, bool = true) {
    var begDateId = $(that).parents('tr').find('input[name="' + name + '"]').attr('id');
    if (!begDateId || begDateId == '') {
        var parentId = $(that).parents('tr').find('input[name="' + name + '"]').parent().attr('id');
        begDateId = parentId + 'inp';
        $(that).parents('tr').find('input[name="' + name + '"]').attr('id', begDateId)
    }
    var options = {};
    if (bool) {
        if (begDateId && begDateId != '') {
            // 当起始日期是今天时，截至日期大于等于今天
            //当起始日期不是今天时，截至日期大于等于今天
            var begDate = $.trim($(that).val());
            var startTime = $('#' + begDateId).val();
            /* if (startTime == getToday()) {
                 options.minDate = '#F{$dp.$D(\'' + begDateId + '\',{d:0})}';
             } else {
                 if (startTime < getToday()) {
                     options.minDate = '%y-%M-%d';
                 } else {
                     options.minDate = '#F{$dp.$D(\'' + begDateId + '\',{d:1})}';
                 }
             }*/
            options.minDate = '#F{$dp.$D(\'' + begDateId + '\',{d:1})}';



        }
    }

    if (fn) {
        options.onpicked = function () {
            fn();
        }
    }
    WdatePicker(options);
}



//form 中input 使用  有效期开始时间
function useInInpBegDate(that, id) {
    var begDate = $.trim($(that).val());
    var endInp = $('#' + id);
    var endDate = $.trim(endInp.val());
    if (begDate != '' && endDate != '' && compareDate(endDate, begDate)) {
        endInp.val(getdifference(begDate, 1));
    }
}

//form 中input 使用   有效期截止时间
function useInInpEndDate(that, id, fn) {
    var begDateId = id
    var options = {};
    if (begDateId && begDateId != '') {
        // 当起始日期是今天时，截至日期大于等于今天
        //当起始日期不是今天时，截至日期大于等于今天
        var begDate = $.trim($(that).val());
        var startTime = $('#' + begDateId).val();
        if (startTime == getToday()) {
            options.minDate = '#F{$dp.$D(\'' + begDateId + '\',{d:0})}';
        } else {
            if (startTime < getToday()) {
                options.minDate = '%y-%M-%d';
            } else {
                options.minDate = '#F{$dp.$D(\'' + begDateId + '\',{d:1})}';
            }
        }

    }
    if (fn) {
        options.onpicked = function () {
            fn();
        }
    }
    WdatePicker(options);
}


/**
 * 比较日期大小 a是否小于b
 *
 * @param a,b
 *            日期：'2017-06-13' '2017/06/13' '2017.06.13'
 *
 */
function compareDate(a, b) {
    var reg = /-|\.|\//g;
    var aArr = a.replace(reg, '-').split('-');
    var bArr = b.replace(reg, '-').split('-');
    var begDate = new Date(aArr[0], parseInt(aArr[1]) - 1, parseInt(aArr[2]))
        .getTime();
    var endDate = new Date(bArr[0], parseInt(bArr[1]) - 1, parseInt(bArr[2]))
        .getTime();
    if (begDate < endDate) {
        return true;
    } else {
        return false;
    }
}

/*
 * getdifference 获取指定日期的间隔日期 fromDate是"yyyy-MM-dd"的日期格式，为指定日期，例如‘2014-10-10’
 * dayInterval表示间隔天数，间隔天数大于0，则得到比指定日期大dayInterval天的日期，间隔天数小于0，则得到比指定日期小dayInterval天的日期
 */
function getdifference(fromDate, dayInterval) {
    var curDate = new Date(Date.parse(fromDate.replace(/-/g, "/")));
    curDate.setDate(curDate.getDate() + dayInterval);
    var year = curDate.getFullYear();
    var month = (curDate.getMonth() + 1) < 10 ? "0" + (curDate.getMonth() + 1) :
        (curDate.getMonth() + 1);
    var day = curDate.getDate() < 10 ? "0" + curDate.getDate() : curDate
        .getDate();
    return year + "-" + month + "-" + day;
}

/**
 * 设置table表格内附件类型列select不可重复选择
 *
 * @param obj this对象
 *
 * @param tableId 需要设置的table  id
 *
 * @param selName  需要设置的select name值
 */
function selCannotRepeatChoice(obj, tableId, selName) {
    var $this = $(obj);
    var arr = [];
    var selVal = $this.val();
    $('#' + tableId).find('tr').each(function () {
        var val = $(this).find('select[name="' + selName + '"] option:selected').val();
        arr.push(val);
    });
    $this.find('option').each(function () {
        var value = $(this).val();
        if (findInArr(value)) {
            $(this).prop('disabled', true).css('color', 'rgba(0,0,0,.2)');
        } else {
            $(this).prop('disabled', false).css('color', 'rgba(0,0,0,1)');
        }
    })
    $this.find('option[value="' + selVal + '"]').prop('disabled', false).css('color', 'rgba(0,0,0,.2)');

    function findInArr(str) {
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] == str) {
                return true;
            }
        }
        return false;
    }
}
// function deepClone(target) {
//     // 定义一个变量
//     let result;
//     // 如果当前需要深拷贝的是一个对象的话
//     if (typeof target === 'object') {
//         // 如果是一个数组的话
//         if (Array.isArray(target)) {
//             result = []; // 将result赋值为一个数组，并且执行遍历
//             for (let i in target) {
//                 // 递归克隆数组中的每一项
//                 result.push(deepClone(target[i]))
//             }
//             // 判断如果当前的值是null的话；直接赋值为null
//         } else if(target===null) {
//             result = null;
//             // 判断如果当前的值是一个RegExp对象的话，直接赋值
//         } else if(target.constructor===RegExp){
//             result = target;
//         }else {
//             // 否则是普通对象，直接for in循环，递归赋值对象的所有值
//             result = {};
//             for (let i in target) {
//                 result[i] = deepClone(target[i]);
//             }
//         }
//         // 如果不是对象的话，就是基本数据类型，那么直接赋值
//     } else {
//         result = target;
//     }
//     // 返回最终结果
//     return result;
// }


function deepClone(target) {
    let copyed_objs = [];//此数组解决了循环引用和相同引用的问题，它存放已经递归到的目标对象
    function _deepCopy(target) {
        if ((typeof target !== 'object') || !target) { return target; }
        for (let i = 0; i < copyed_objs.length; i++) {
            if (copyed_objs[i].target === target) {
                return copyed_objs[i].copyTarget;
            }
        }
        let obj = {};
        if (Array.isArray(target)) {
            obj = [];//处理target是数组的情况
        }
        copyed_objs.push({ target: target, copyTarget: obj })
        Object.keys(target).forEach(key => {
            if (obj[key]) { return; }
            obj[key] = _deepCopy(target[key]);
        });
        return obj;
    }
    return _deepCopy(target);
}
