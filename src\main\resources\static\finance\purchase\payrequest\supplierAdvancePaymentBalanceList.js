$(function () {
    var totalTable = z_utils.totalTable;
    var colNames = ['供应商编号', '供应商名称',  '预付余额', '占用金额', '可用余额', '操作'];
    var colModel = [
        {
            name: 'supplierNo',
            index: 'supplierNo',
            width: 150
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 150
        }, {
            name: 'prepaymentAmount',
            index: 'prepaymentAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }, {
            name: 'occupyAmount',
            index: 'occupyAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }, {
            name: 'availableAmount',
            index: 'availableAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        },{
            width:150,
            name: 'operate',
            align:'center',
            index: 'operate',
            formatter: function (value, grid, rows, state) {

                var html = '<button class="btn btn-white" onclick="preAmountDetail(\''+ rows.id+'\')">预付余额明细</button>';
                return html;
            }
        }
    ];

    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/supplierAdvancePaymentBalance/getSupplierAdvancePaymentBalanceList',
        // url: 'http://localhost:8080/account/find',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        attachRow:true,

        gridComplete: function () {
            var _this = $(this);
            setTimeout(function () {
                if(_this.XGrid('getRowData').length === 0){
                    utils.dialog({content:'查询无数据',quickClose:true,timeout:2000}).show();
                }
            },200);
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');

        },
        pager: '#grid-pager'
    });



    $("#searchBtn").on("click", function () {
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/supplierAdvancePaymentBalance/getSupplierAdvancePaymentBalanceList',
            postData: {
                keyword: $("#keyword").val(),
                prepaymentVal:prepaymentVal()
            }
        }).trigger('reloadGrid');
        totalPayrequestSum();
    });

    function prepaymentVal() {
        if ($("#prepaymentValcheckbox").prop('checked')) {
            return  "1";
        } else {
            return  "0";
        }
    }

    totalPayrequestSum();
    var allColModelA = JSON.parse(JSON.stringify(colModel));
    //导出
    $('#exportBtn').on('click', function () {

        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            addHtmlA(colNames);
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    var obj ={};

                    //obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    obj["nameModel"] = nameModel;
                    httpPost("/proxy-finance/finance/purchase/supplierAdvancePaymentBalance/exportSupplierAdvancePaymentBalanceList", obj);
                },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })

    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none;     padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                if (arry[i] != "操作") {
                    s += '<div class="col-md-3">' +
                        '            <div class="checkbox">' +
                        '                <label>' +
                        '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                        '                </label>' +
                        '            </div>' +
                        '        </div>';
                }

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }

        var input1 = document.createElement("input");
        input1.name = "prepaymentVal";
        input1.value = prepaymentVal();
        temp.appendChild(input1);

        var input2 = document.createElement("input");
        input2.name = "keyword";
        input2.value = $("#keyword").val();
        temp.appendChild(input2);
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    //关键字模糊查询
    $('#keyword1').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result)
            $("#keyword1").val(result.value).attr('oldvalue',result.value);
            $("#keyword").val(result.data);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {

            $("#keyword").val("");
            $("#keyword1").val("");

        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });


    //放大镜查询
    $('#keyword1').on({
        dblclick: function (e) {
            supplierdDalog($("#keyword1").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#keyword1").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#keyword1").attr('oldvalue'))
    });
    //供应商双击查询
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '',  // 给modal 要传递的 的数据
            onclose: function () {
                $('#keyword1').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#keyword1").val(data.supplierName);
                    $("#keyword").val(data.supplierCode);
                }else{
                    $("#keyword").val('');
                    $("#keyword1").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }



    function totalPayrequestSum() {
        $.ajax({
            url:'/proxy-finance/finance/purchase/supplierAdvancePaymentBalance/getSupplierAdvancePaymentBalanceTotalMoney',
            type:'post',
            data:{
                keyword: $("#keyword").val(),
                prepaymentVal:prepaymentVal()
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;

                    if (null == data) {
                        $("#sumprepaymentAmount").text(0);
                        $("#sumoccupyAmount").text(0);
                        $("#sumAvailableAmount").text(0);
                    } else {
                        if (data.sumPrepaymentAmount) {
                            $("#sumprepaymentAmount").text(parseFloat(data.sumPrepaymentAmount).formatMoney('2', '', ',' ,'.'));
                            // $("#sumApplayAmount").text(data.sumApplayAmount.toFixed(2));
                        } else {
                            $("#sumprepaymentAmount").text(0);
                        }
                        if (data.sumOccupyAmount) {
                            $("#sumoccupyAmount").text(parseFloat(data.sumOccupyAmount).formatMoney('2', '', ',' ,'.'));
                        } else {
                            $("#sumoccupyAmount").text(0);
                        }
                        if (data.sumAvailableAmount) {
                            $("#sumAvailableAmount").text(parseFloat(data.sumAvailableAmount).formatMoney('2', '', ',' ,'.'));
                        } else {
                            $("#sumAvailableAmount").text(0);
                        }
                    }


                }
            }
        });
    }


})

function preAmountDetail(id){

    utils.dialog({
        title: '预付余额明细',
        url: '/proxy-finance/finance/purchase/supplierAdvancePaymentBalanceDetails/skipDetailsList?id=' + id,
        width: $(window).width() * 0.95,
        height: $(window).height() * 0.8,
        data: 'val值', // 给modal 要传递的 的数据
        onclose: function () {
            if (this.returnValue) {
                var data = this.returnValue;
                console.log(data)
            }
            $('iframe').remove();
            $('#searchBtn').trigger('click');
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
}