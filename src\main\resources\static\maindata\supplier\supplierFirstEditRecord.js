$(function () {
	var supplierBaseId =  $("#supplierBaseId").val();
	var supplierOrganBaseId =  $("#supplierOrganBaseId").val();  
	
	
	var baseApplicationCode =  $("#organBaseApplicationCode").val(); 
	//修改记录
    $('#supplierFirstEditRecord').XGrid({
    	url:"/proxy-supplier/supplier/supplierOrganBase/supplierOrganBase/ajaxFirstEditRecord?applicationCode="+baseApplicationCode,
        colNames: ['','申请时间', '申请人', '单据编号', '修改明细'],
        colModel: [
        	{
                name: 'id', //与反回的json数据中key值对应
                index: 'id', //索引。其和后台交互的参数为sidx
                key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true
        	},{
            name: 'updateTime', //与反回的json数据中key值对应
            index: 'updateTime', //索引。其和后台交互的参数为sidx
            formatter:function(value){
            	var date=value;
            	if(!value)return false;
        		date=format(value);
        		return date;
            }
    	}, {
            name: 'updateUserName',
            index: 'updateUserName'
        }, {
            name: 'applicationCode',
            index: 'applicationCode'
        },{
            name: 'changeDetails',
            index: 'changeDetails',
            formatter:function (value,b,rowData) {
            	var html='<div class="modifyTheRecord">\n' +
                 '<div class="itemContent"><div class="mrLeft">';
            	var arr=value.split(';');
            	if(arr && arr.length>0){
            		for(var i=0;i<arr.length;i++)
                    {
                        html+='<p>'+arr[i]+'</p>';
                    }
            	}else{
            		if(value){
            			html+='<p>'+value+'</p>';
            		}
            	}
                html+='</div></div>' +
                '\t\t<div class="mrRight">\n' +
                '\t\t\t<a href="javascript:;" class="moreBtn">展开</a>\n' +
                '\t\t</div>\n' +
                '\t</div>\n';
                return html;
            }
        }
        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,
        pager:'#supplierFirstEditRecord_page'
       
        
    });
	
    
    
   //异步加载修改记录 
  
   /* if(baseApplicationCode){
    	  modifyFirstEditRecord(baseApplicationCode)
    }*/
  
    
 
    
  
	
	
	$("[data-toggle='distpicker']").each(function(){
		//省
		var val1=$.trim($(this).find("select").eq(0).attr("data-value"));
		if(val1 && val1 != '')
		{
			$(this).find("select").eq(0).val(val1);
			$(this).find("select").eq(0).change();
		}
		//市
		var val2=$.trim($(this).find("select").eq(1).attr("data-value"));
		if(val1 && val1 != '')
		{
			$(this).find("select").eq(1).val(val2);
			$(this).find("select").eq(1).change();
		}
		//区
		var val3=$.trim($(this).find("select").eq(2).attr("data-value"));
		if(val1 && val1 != '')
		{
			$(this).find("select").eq(2).val(val3);
		}
	});
	
	//修改记录申请明细展开收起按钮
	$("#supplierFirstEditRecord").on("click",".moreBtn",function(){
	    var type=$.trim($(this).text());
	    var tr=$(this).parents("tr");
	    var innerHeiht=0;
	    if(type == '展开')
	    {
	        innerHeiht=tr.find(".mrLeft").innerHeight();
	        $(this).html('收起');
	    }else if(type == '收起'){
	        innerHeiht = 40;
	        $(this).html('展开');
	    }
	    if(innerHeiht < 40){
	        innerHeiht=40;
	    }
	    tr.find(".modifyTheRecord .itemContent").animate({
	        height:innerHeiht
	    },500)
	   
	});

	
});


function modifyFirstEditRecord(baseApplicationCode){
	var xGridDate=[];
    $.ajax({
      	url:'/proxy-supplier/supplier/supplierOrganBase/supplierOrganBase/ajaxFirstEditRecord',
      	data:{"applicationCode":baseApplicationCode},
      	type:"post",
      	dataType:'json',
      	success:function(data){
      		if(data.result.list.length>0){
      			var dataList = data.result.list;
      			for(var i=0;i<dataList.length;i++){
      			  var a={
      					id:dataList[i].id,
      					updateTime: dataList[i].updateTime,
      					updateUserName: dataList[i].updateUserName,
      					applicationCode: dataList[i].applicationCode,
      					changeDetails: dataList[i].changeDetails,
      					
      		        }
      			   xGridDate.push(a);
      			}
          	}else{
          		xGridDate = []
          	}
      		$('#supplierFirstEditRecord').XGrid('setGridParam', {
      			data:xGridDate,
                pager: '#supplierFirstEditRecord_page'
            }).trigger('reloadGrid');
      	}
      });
}




