var getData;
$(function () {
    /* 日期格式化 */
    //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
    function dateFormat(time, format) {
        var t = new Date(parseInt(time,10));
        var tf = function (i) {
            return (i < 10 ? '0' : '') + i;
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        });
    }


    var org_name;
    /* 获取数据 */
    getData = function (printType,data) {
      //  var orderReturnStorageCodes = orderReturnStorageCodes.join(',');
        org_name = $("#orgName").val();
     /*   $.ajax({
            url:"/orderReturn/orderReturnController/getReportListPrint",
            method:"post",
            data:{
                startTime:startTime,
                endTime:endTime,
                drugClass:drugClass,
                printType: printType,
                pageNum:page,
                pageSize:pageSize
            },
            success:function(res){
                if($.isArray(res.result.list)){
                    webRender(res.result.list,printType);
                }else {
                    utils.dialog({
                        title:'提示',
                        content:"数据为空或格式不正确",
                        okValue:'确定',
                        ok:function () {}
                    }).showModal();
                }
            },
        })*/
        webRender(data,printType);
    };
    /* 数据渲染 */
    function webRender(data,printType) {
        var box_html = '';
        /* 基本结构拼装 */
     //   data.forEach(function (item,index) {
           // console.log(item,item.orgCode);
            /* 销售出库复核单 */
            box_html +=`
                   <div class="content" style="width:1080px;margin: 0px auto;">
                    <h3 class="title" style="text-align: center">`+org_name+`销售退回记录</h3>
                    <table id="table_a"></table>
                    <div style="page-break-after:always"></div>
                </div>
                `;
      ///  });

        $("#box").html(box_html);

        /* 表格初始化 */
      //  data.forEach(function (item,index) {

            /* 销售出库复核单 */
            $("#table_a").jqGrid({
                data: data,
                datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                //width: "1322.83",
                colNames:  ['到货日期','购货单位','通用名称','剂型','规格','生产厂家','批准文号','批号',	'生产日期','有效期至','数量','单位',
                    '收货日期','运输方式','储运工具','到货温度','收货员','退货原因'
                ],
                colModel : [
                    {
                    name: 'arriveTime',
                    index: 'arriveTime',
                    formatter:function (value){
                            if(value){
                                return moment(value).format('YYYY-MM-DD');
                            }else{
                                return ''
                            }
                        },
                    width: 80,
                    },
                    {name: 'customerName',index: 'customerName', width: 60},
                    {name: 'productName',index: 'productName', width: 60},
                    {name: 'dosageForm',index: 'dosageForm', width: 60},
                    {name: 'specifications',index: 'specifications', width: 60},
                    {name: 'manufacturer',index: 'manufacturer', width: 60},
                    {name: 'approvalNumber',index: 'approvalNumber', width: 60 },
                    {name: 'batchCode',index: 'batchCode', width: 60},
                    {name: 'productionTime',index: 'productionTime',
                        formatter:function (value){
                            if(value){
                                return moment(value).format('YYYY-MM-DD');
                            }else{
                                return ''
                            }
                        },
                     width: 80
                    },
                    {name: 'periodValidity',index: 'periodValidity',
                        formatter:function (value){
                            if(value){
                                return moment(value).format('YYYY-MM-DD');
                            }else{
                                return ''
                            }
                        },
                        width: 80
                    },
                    {name: 'returnsNumber',index: 'returnsNumber', width: 60},
                    {name: 'productUnit',index: 'productUnit', width: 60},
                    {name: 'receiveTime',index: 'receiveTime',
                        formatter:function (value){
                            if(value){
                                return moment(value).format('YYYY-MM-DD');
                            }else{
                                return ''
                            }
                        },
                        width: 80
                    },
                    {name: 'transportTools',index: 'transportTools', width: 60},
                    {name: 'storageTools',index: 'storageTools', width: 60},
                    {name: 'arriveTemperature',index: 'arriveTemperature', width: 60},
                    {name: 'receivePerson',index: 'receivePerson', width: 60},
                    {name: 'returnReason',index: 'returnReason', width: 60},
                ],
                shrinkToFit:false,
                gridview: true,
                rowNum: 999,
            });
       // });


        if(printType==0){
            /* 打印预览 */
            utils.dialog({
                title:'预览',
                width:$(parent.window).width()-100,
                height:$(parent.window).height() * 0.7,
                content:$('#big_box').html(),
                okValue:'确定',
                ok:function () {}
            }).showModal();
            window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
        }else if(printType==1){
            /* 打印 */
            $("#box").jqprint({
                globalStyles: true, //是否包含父文档的样式，默认为true
                mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                stylesheet: null, //外部样式表的URL地址，默认为null
                noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                append: null, //将内容添加到打印内容的后面
                prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                deferred: $.Deferred() //回调函数
            });
        }
    }
});