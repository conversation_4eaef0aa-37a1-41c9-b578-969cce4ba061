   $('#Template_Table').XGrid({
        url:'/proxy-sysmanage/sysmanage/dict/queryexplaintemplate',
        colNames: ['模板id', '模板名称', '商品大类id', '是否停用', '创建人', '创建日期','操作'],
        colModel: [
            {
                name: 'templateId',
                index: 'templateId',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'templateName',
                index: 'templateName',
                width: 200,//宽度
                editable: true,//是否可编辑
                edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

            }, {
                name: 'templateCommoditytype',
                index: 'templateCommoditytype',
                width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'templateIsstop',
                index: 'templateIsstop',
                width: 150,
                editable: true,
                formatter:isShop,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            },{
                name: 'createUser',
                index: 'createUser',
                width: 250,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'createTime',
                index: 'createTime',
                width: 250,
                sortable: false,
                editable: true,
                formatter:datetimeFormatter,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            },{
                width: 250,
                sortable: false,
                formatter:templaOperation

            }, {
                name: 'templateAttribute',
                index: 'templateAttribute',
                hidden:true
            }
        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        key:'templateId',
        ondblClickRow: function (id, dom, obj, index, event) {


            var el = document.querySelector('#dialog_Block');//html元素
            $('#templateform')[0].reset();
            var selRow = obj;
            if(selRow){
                getcommodityType(selRow.templateCommoditytype)
                $("[name='templateId']").val(selRow.templateId)
                $("[name='templateName']").val(selRow.templateName)
                $("[name='templateCommoditytype']").val(selRow.templateCommoditytype)
                $("[name='templateAttribute']").val(selRow.templateAttribute)
                if(selRow.templateIsstop=="是"){
                    $(":radio[name='templateIsstop'][value='1']").prop("checked", "checked");
                }else{
                    $(":radio[name='templateIsstop'][value='0']").prop("checked", "checked");
                }
                $("[name='templateName']").attr("disabled",true);
                $("[name='commodityName']").attr("disabled",true);
                $(":radio[name='templateIsstop']").attr("disabled",true);
                $("#checkBut").attr("disabled",true);
                utils.dialog({
                    title: '查看明细',
                    content: el,

                }).showModal();
            }else{
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }

        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
        }
    });

            //input小标签回填
            $('input[data-role="tagsinput"]').each(function () {
                $(this).tagsinput('add', $(this).val());
            })

    //选择 按钮
    var retultArr=[];
    $("#orgahistorys").on("click",function () {
        var value=$("[name='templateId']").val();
        var param={
            value:value,
            attr:$("#attribute").val(),
            arr:retultArr
        }
        dialog({
            url: '/proxy-sysmanage/sysmanage/dict/dialogPath',//弹框页面请求地址
            title: '说明书属性',
            width: 1000,
            height: 650,
            data: param,
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    retultArr=data.arr;
                    $("#attribute").val(data.str);
                }
            },
            oniframeload: function () {
                //console.log('iframe ready')
            }
        }).showModal();
    })

    function templaOperation(){

        return  "<a href='javascript:;' onclick='edittemplate(this)'>编辑</a>"
    }

    var timer=null;
    var timer2=null;
    //修改一行
    function  edittemplate(obj){

        var el = document.querySelector('#dialog_Block');//html元素
        $('#templateform')[0].reset();
        var id=$(obj).parents('tr').attr('id');
        var selRow = $('#Template_Table').XGrid('getRowData',id);
        if (selRow) {
            getcommodityType(selRow.templateCommoditytype)
            $("[name='templateId']").val(selRow.templateId)
            $("[name='templateName']").val(selRow.templateName)
            $("[name='templateCommoditytype']").val(selRow.templateCommoditytype)
            $("[name='templateAttribute']").val(selRow.templateAttribute)
            if(selRow.templateIsstop=="是"){
                $(":radio[name='templateIsstop'][value='1']").prop("checked", "checked");
            }else{
                $(":radio[name='templateIsstop'][value='0']").prop("checked", "checked");
            }
            $("[name='templateName']").attr("disabled",false);
            $("[name='commodityName']").attr("disabled",false);
            $(":radio[name='templateIsstop']").attr("disabled",false);
            $("#checkBut").attr("disabled",false);
            utils.dialog({
                title: '编辑',
                content: el,
                okValue: '确定',
                ok: function () {
                    $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                    data = decodeURIComponent($("#templateform").serialize(), true);
                    if (validform("templateform").form()) {//验证通过 "myform"为需要验证的form的ID
                        if($.trim($("[name='templateAttribute']").val()) == ''){
                            $("#attribute").attr("data-original-title","没有匹配项").tooltip("show");
                            clearTimeout(timer);
                            timer=setTimeout(function () {
                                $("#attribute").tooltip("hide");
                            },2000);
                            return false;
                        }
                        if($.trim($("[name='templateCommoditytype']").val()) == ''){
                            $("#commodity").attr("data-original-title","没有匹配项").tooltip("show");
                            clearTimeout(timer2);
                            timer2=setTimeout(function () {
                                $("#commodity").tooltip("hide");
                            },2000);
                            return false;
                        }
                        $.ajax({
                            url: "addoreditexplaintemplate",
                            type: "post",
                            data: data,
                            success: function (result) {
                                utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                                setTimeout("location.reload();",1000);
                            }
                        })

                    } else {//验证不通过
                        utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                },
                cancelValue: '取消',
                cancel: function () {

                }
            }).showModal();
        }else {
            utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
        }


    }


    //新增一行
    $('#addTemplate').on('click', function () {
        var el = document.querySelector('#dialog_Block');//html元素
        $('#templateform')[0].reset();
        $("[name='templateId']").val(null)
        $("[name='templateName']").attr("disabled",false);
        $("[name='commodityName']").attr("disabled",false);
        $(":radio[name='templateIsstop']").attr("disabled",false);
        $("#checkBut").attr("disabled",false);
        utils.dialog({
            title: '新增',
            content: el,
            okValue: '确定',
            ok: function () {
                $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                data= decodeURIComponent($("#templateform").serialize(),true);
                if (validform("templateform").form()) {//验证通过 "myform"为需要验证的form的ID
                    if($.trim($("[name='templateAttribute']").val()) == ''){
                        $("#attribute").attr("data-original-title","没有匹配项").tooltip("show");
                        clearTimeout(timer);
                        timer=setTimeout(function () {
                            $("#attribute").tooltip("hide");
                        },2000);
                        return false;
                    }
                    if($.trim($("[name='templateCommoditytype']").val()) == ''){
                        $("#commodity").attr("data-original-title","没有匹配项").tooltip("show");
                        clearTimeout(timer2);
                        timer2=setTimeout(function () {
                            $("#commodity").tooltip("hide");
                        },2000);
                        return false;
                    }
                    $.ajax({
                        url: "addoreditexplaintemplate",
                        type: "post",
                        data: data,
                        success: function (result) {
                            utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                            setTimeout("location.reload();",1000);
                        }
                    })
                } else {//验证不通过
                    utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                retultArr=[];
            },
            cancelValue: '取消',
            cancel: function () {
                retultArr=[];
            }
        }).showModal();

    })



    $("#templateBut").click(function () {
        $('#Template_Table').setGridParam({
            url:"/proxy-sysmanage/sysmanage/dict/queryexplaintemplate",
            postData:{
                "templateName":$('#templateName').val().replace(/\s+/g,""),
                "isStop":$('#isStop').val(),
                "commodityType":$("#commodityType").val()
            }
        }).trigger('reloadGrid');

    });

    function isShop(val){
        if(val==0){
            return "否"
        }else{
            return  "是"
        }
    }
    function datetimeFormatter(val) {
        if (val != null && val !="") {
            return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
        } else {
            return "";
        }
    };

    $('#commodity').on("keyup",function () {
        $("[name='templateCommoditytype']").val('');
    })
    $('#commodity').Autocomplete({
        serviceUrl: '/proxy-sysmanage/sysmanage/dict/querycommonnotpage', //异步请求
        paramName: 'CommonName',//查询参数，默认 query
        // dataType: 'json',
        //  lookup:, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        params:{
            type:7
        },
        triggerSelectOnValidInput: true, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        dataReader:{
            'list':'result',
            'value':'name',
            'data':'id'
        },
        onSelect: function (result) {
            //选中回调
            $("[name='templateCommoditytype']").val(result.data)
        },onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            if (suggestions.length <1) {
                $("[name='templateCommoditytype']").val('')
                return false;
            }


        }


    })


   $('#Type').on("keyup",function () {
       $("#commodityType").val(null)
   })
   $('#Type').Autocomplete({
       serviceUrl: '/proxy-sysmanage/sysmanage/dict/querycommonnotpage', //异步请求
       paramName: 'CommonName',//查询参数，默认 query
       // dataType: 'json',
       //  lookup:, //监听数据 value显示文本，data为option的值
       minChars: '0', //触发自动匹配的最小字符数
       maxHeight: '300', //默认300高度
       params:{
           type:7
       },
       triggerSelectOnValidInput: true, // 必选
       showNoSuggestionNotice: true, //显示查无结果的container
       noSuggestionNotice: '查询无结果',//查无结果的提示语
       // tabDisabled: true,
       dataReader:{
           'list':'result',
           'value':'name',
           'data':'id'
       },
       onSelect: function (result) {
           //选中回调
           $("#commodityType").val(result.data)
       },onSearchComplete: function (query, suggestions) {
           //匹配结果后回调
           if (suggestions.length <1) {
                $("#commodityType").val(null)
           }


       }


   })

    function  getcommodityType(val){

        $.ajax({
            url:'querybeanbytype',
            type:"post",
            data:{type:1017,id:val},
            success:function (result) {
                $("#commodity").val(result.result)


            }
        })


    }