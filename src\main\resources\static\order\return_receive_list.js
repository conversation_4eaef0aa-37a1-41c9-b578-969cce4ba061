
//获取各种统计总数
function getStatisticsSums() {
    var queryParam = $('#form_a').serializeToJSON();
    //加载总数量
    $.ajax({
        url: '/proxy-order/order/orderReturn/orderReturnController/getStatisticsSums',
        dataType: 'json',
        timeout: 8000, //6000
        data:queryParam,
        success: function (data) {
            if (data.code==0){
                var numResult = data.result;
                if(null != numResult){

                    $("#taxAmountSum").text(parseFloat(numResult.taxAmountSum).formatMoney('2', '', ',', '.'));
                    $("#activityDiscountAmountSum").text(parseFloat(numResult.activityDiscountAmountSum).formatMoney('2', '', ',', '.'));
                    $("#balanceDiscountAmountSum").text(parseFloat(numResult.balanceDiscountAmountSum).formatMoney('2', '', ',', '.'));
                    $("#amountOfRebateSum").text(parseFloat(numResult.amountOfRebateSum).formatMoney('2', '', ',', '.'));
                    $("#paymentAmountSum").text(parseFloat(numResult.paymentAmountSum).formatMoney('2', '', ',', '.'));
                    $("#taxSum").text(parseFloat(numResult.taxSum).formatMoney('2', '', ',', '.'));
                }else {
                    $("#taxAmountSum").text("0.00");
                    $("#activityDiscountAmountSum").text("0.00");
                    $("#balanceDiscountAmountSum").text("0.00");
                    $("#amountOfRebateSum").text("0.00");
                    $("#paymentAmountSum").text("0.00");
                    $("#taxSum").text("0.00");
                }
            }
        },
        error: function () {
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
$(function () {
    /* 日期初始化 */
    z_utils.initDate('beginDate', 'endDate');
    /* tabs 切换 */
    z_utils.tableCut('flex');

    /* 合计计算 */
    var totalTable = z_utils.totalTable;



    var sysOrgCode = $('#orgCode').val();
    var colName = ['机构名称', '销售退回单号', '退款申请单号','销售订单编号', '发票类型','退款申请日期','退款类型', '部门名称', '申请人', '单据日期', '收货日期', '客户编号', '客户名称',
       '物流单号', '价税合计','活动优惠金额','余额抵扣优惠金额', '退回返利金额','运费','实付金额', '总税额', '是否退款','是否退运费','付款方式','单据状态','备注','是否FBP','是否特药','订单类型'];
    var colModel = [
        {name: 'institutionName',index: 'institutionName'},
        {name: 'salesReturnCode',index: 'salesReturnCode',  width:'250'},
        {name: 'ecRefundRequestCode',index: 'ecRefundRequestCode',  width:'250'},
        {name: 'ecOrderCode',    index: 'ecOrderCode' ,  width:'250' },
        {
            name: 'invoiceType',
            index: 'invoiceType',width:'250',
            formatter: function (e) {
                if (e == '1') {
                    return '电子普通发票'
                } else if (e == '2') {
                    return '增值税专用发票(纸质)'
                } else if (e == '3') {
                    return '纸质普通发票'
                } else if(e=='4') {
                    return '增值税电子专用发票';
                } else {

                }
            }
        },
        {name: 'refundRequestTime',index: 'refundRequestTime',formatter:function (e){
            if (e != null && e !="") {
                return ToolUtil.dateFormat(e, 'yyyy-MM-dd HH:mm:ss');
            } else {
                return "";
            }
        }},//
        {name: 'refundType',index: 'refundType',formatter:function (e) {
            if(e==1){
                return '仅退款';
            }else if(e==2){
                return '退货退款';
            }else if(e==3){
                return '仅退货';
            }
        }},
        {name: 'deptName',index: 'deptName',hidden:sysOrgCode=='027'},

        {name: 'applicant',index: 'applicant'},
        {name: 'applicantTime',index: 'applicantTime',formatter:function (e){
            if (e != null && e !="") {
                return ToolUtil.dateFormat(e, 'yyyy-MM-dd HH:mm:ss');
            } else {
                return "";
            }
        }},
        {name: 'collectGoodsTime',index: 'collectGoodsTime',formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd HH:mm:ss');
                } else {
                    return "";
                }
        }},
        {name: 'customerCode',index: 'customerCode'},
        {name: 'customerName',index: 'customerName',  width:'400',},
        {name: 'expressNo',index: 'expressNo'},
        {name: 'taxAmount',index: 'taxAmount'},
        {name: 'activityDiscountAmount',index: 'activityDiscountAmount'},
        {name: 'balanceDiscountAmount',index: 'balanceDiscountAmount'},
        {name: 'amountOfRebate',index: 'amountOfRebate'},
         {
            name: 'freightAmount',
            index: 'freightAmount',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return "0";
                }
            }
        },
        {name: 'paymentAmount',index: 'paymentAmount'},
        {name: 'tax',index: 'tax'},
        {name: 'refund',index: 'refund',formatter:function (e,rowType,rowData) {
                if (e == 0) {
                    return '否';
                } else if (e == 1) {
                    return '是';
                } else {
                    return '-';
                }
            }},
        {name: 'isRefundFreight',index: 'isRefundFreight',formatter:function (e,rowType,rowData) {
                if (e == 0) {
                    return '否';
                } else if (e == 1) {
                    return '是';
                } else {
                    return '-';
                }
            }},
        {name: 'payType',index: 'payType',formatter:function (e) {
                if (e == '1') {
                    return '在线支付'
                } else if (e == '2') {
                    return '货到付款'
                } else if (e == '3') {
                    return '线下转账'
                } else if (e == '4') {
                    return '银行授信支付 '
                }else if (e == '5') {
                    return '自有账期支付'
                }else {
                    return "";
                }
        }},
        {name: 'documentsState',index: 'documentsState',formatter:function (e) {
            if(e==1){
                return '待处理';
            }else if(e==2){
                return '审核中';
            }else if(e==3){
                return '已驳回';
            }else if(e==4){
                return '入库中';
            }else if(e==5){
                return '已入库';
            }else if(e==6){
                return '已取消';
            }
        }},
        {name: 'remark',index: 'remark'}
        ,{
            name: 'isFbp',
            index: 'isFbp',
            formatter: function (e) {
                if (e == '1') {
                    return '是'
                } else {
                    return '否'
                }
            }
        },{
            name: 'isSpecialMedicine',
            index: 'isSpecialMedicine',
            formatter: function (e) {
                if (e == '1') {
                    return '特殊药品'
                } else {
                    return '否'
                }
            }
        },{
            name: 'orderType',
            index: 'orderType',
            formatter: function (e) {
                if (e == '1') {
                    return '普通订单'
                } else if (e == '2') {
                    return '连锁订单'
                }else if (e == '3') {
                    return '神农订单'
                }else if (e == '5') {
                    return '雨诺订单'
                }else if (e == '6') {
                    return '销售调账单'
                }else if (e == '7') {
                    return '智慧脸订单'
                }else if (e == '8') {
                    return '智慧脸2B订单'
                }else if (e == '9') {
                    return '智鹿请货订单（旧)'
                }else if (e == '10') {
                    return '赠品订单'
                }else if (e == '11') {
                    return '内部调拨单'
                }else if (e == '14') {
                    return '智鹿请货订单'
                }else if (e == '15') {
                    return '线下订单'
                }else if (e == '16') {
                    return '三九请货订单'
                }else if (e == '20') {
                    return '老年服务订单'
                }else if (e == '22') {
                    return '商业调拨订单'
                }else if (e == '19') {
                    return '海典订单'
                }else if (e == '18') {
                    return '荷叶订单'
                }else if (e == '17') {
                    return '特价订单'
                }else if (e == '23') {
                    return '1#药城订单'
                }else if (e == '24') {
                    return '药师帮订单'
                }else if (e == '25') {
                    return '荷叶2C订单'
                }
            }
        }];

    $('#table_a').XGrid({
        url:"/proxy-order/order/orderReturn/orderReturnController/getList",
        postData:{ "beginDate": $("#beginDate").val(), "endDate": $("#endDate").val()},
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'salesReturnCode',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            /* 双击单据，进入单据详情页面。若单据状态为“待处理”，双击单据跳转至编辑销售退回单页面； */
            utils.openTabs("return_receive_info","销售退回收货单详情",  '/proxy-order/order/orderReturn/orderReturnController/detail?salesReturnCode='+obj.salesReturnCode);
            // location.href = '/orderReturn/orderReturnController/toApproval?businessId='+obj.salesReturnCode+"&taskId=887554&processId="+obj.auditId;
        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['taxAmount','activityDiscountAmount','balanceDiscountAmount','amountOfRebate','paymentAmount','tax'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            //console.log(id, dom, obj, index, event)
        }
    });



    /* table_b */
/*    $('#table_b').XGrid({
        url:"/orderReturn/orderReturnController/getDetailList",
        colNames: ['销售退回单号','行号', '单据日期', '收货日期','商品编号', '条码','商品大类', '商品名称', '商品规格', '生产厂家', '商品产地',
            '单位', '库房名称', '批号', '生产日期', '有效期至',  '本次退货数量', '实际退货数量',  '含税单价', '价税合计', '实付金额','活动优惠金额','余额抵扣优惠金额','退回返利金额','税率', '税额' ],
        colModel: [{      name: 'salesReturnCode',      index: 'salesReturnCode'    },
            {      name: 'sortNo',      index: 'sortNo'    },
            {      name: 'createTime',      index: 'createTime' ,formatter:dateFormatter },

            {      name: 'collectGoodsTime',      index: 'collectGoodsTime'    ,formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }  },
            {      name: 'productCode',      index: 'productCode'    },
            {      name: 'productBarCode',      index: 'productBarCode'    },
            {      name: 'drugClass',      index: 'drugClass'    },
            {      name: 'productName',      index: 'productName'    },
            {      name: 'specifications',      index: 'specifications'    },
            {      name: 'manufacturer',      index: 'manufacturer'    },
            {      name: 'productOrigin',      index: 'productOrigin'    },
            {      name: 'productUnit',      index: 'productUnit'    },
            {      name: 'warehouseName',      index: 'warehouseName'  , formatter: function (e) {
                if (e == '1') {
                    return '合格库'
                } else if (e == '2') {
                    return '不合格库'
                }else if (e == '3') {
                    return '暂存库'
                }
            }  },
            {      name: 'batchCode',      index: 'batchCode'    },
            {      name: 'productionTime',      index: 'productionTime' ,formatter:dateFormatter  },
            {      name: 'periodValidity',      index: 'periodValidity'  ,formatter:dateFormatter  },
            {      name: 'returnsNumber',      index: 'returnsNumber'    },
            {      name: 'actualReturnNumber',      index: 'actualReturnNumber'    },
            {      name: 'taxPrice',      index: 'taxPrice'    ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }},
            {      name: 'taxAmount',      index: 'taxAmount'    ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }},
            {      name: 'paymentAmount',      index: 'paymentAmount'    ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }},
            {      name: 'activityDiscountAmount',      index: 'activityDiscountAmount'  ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }  },
            {      name: 'balanceDiscountAmount',      index: 'balanceDiscountAmount'  ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }  },
            {      name: 'amounOfRebate',      index: 'amounOfRebate', formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }    },
            {      name: 'rate',      index: 'rate',formatter: function (e) {
                if ( e != undefined && e != '' && e != null ) {
                    return e+ "%"
                } else {
                    return '0%'
                }
            }
            },
            {      name: 'tax',      index: 'tax' ,   }],
        rowNum: 20,
        rowList:[20,50,100],
        key: 'salesReturnCode',
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_b',
        gridComplete: function () {
            setTimeout(function (param) {
                /!* 合计写入 *!/
                var data = $('#table_b').XGrid('getRowData');
                var sum_ele = $('#table_b_sum .sum');
                $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                $(sum_ele[1]).text(totalTable(data, 'paymentAmount'));
                $(sum_ele[2]).text(totalTable(data, 'activityDiscountAmount'));
                $(sum_ele[3]).text(totalTable(data, 'balanceDiscountAmount'));
                $(sum_ele[4]).text(totalTable(data, 'tax'));
            }, 200)
            /!* 合计行 *!/
            var data = $(this).XGrid('getRowData');
            var sum_models = ['taxAmount','paymentAmount','activityDiscountAmount','balanceDiscountAmount','tax'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
        },
    });*/

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }



    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })


    /* 客户名称 搜索提示（只显示5条） */
    $('#customerName').Autocomplete({
        serviceUrl: '/proxy-customer/customer/customerBaseAppl/pageList', //异步请求
        paramName: 'customerCode',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function(response) {
            return {
                suggestions: $.map(response.result.list, function(dataItem) {
                    return { value: dataItem.customerName, data: dataItem.customerCode };
                })
            };
        },
        onSelect: function (result) {
            //选中回调
            $("#customerCode").val(result.data)
        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });

    /* 商品名称 搜索提示（只显示5条） */
    $('#productName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function(response) {
            return {
                suggestions: $.map(response.result.list, function(dataItem) {
                    return { value: dataItem.productName, data: dataItem.productCode };
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#productCode").val(result.data)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
        // tabDisabled: true,
    });

    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        //获取form数据
        var data = $('#form_a').serializeToJSON();
        console.log(data);
        var customerName = $("#customerName").val();
        if(customerName==null||customerName==''){
            data.customerCode='';
        }
        //更新表格数据
        var $table_id = $('#nav_content .active .XGridBody table').attr('id');
        console.log($table_id)
        //列表
        $('#table_a').XGrid('setGridParam', {
            url:"/proxy-order/order/orderReturn/orderReturnController/getList",
            postData:data
            ,page:1
        }).trigger("reloadGrid");
        getStatisticsSums();
       /* //详情
        $('#table_b').XGrid('setGridParam', {
            url:"/orderReturn/orderReturnController/getDetailList",
            postData:data
            ,page:1
        }).trigger("reloadGrid");*/
    });
    /* 查询 */
    $('#saveOrder').on('click', function (e) {
        
        utils.openTabs("now_sale_backlist","新建销售退回收货单","/proxy-order/order/orderReturn/orderReturnController/jumpManualOrderReturnList");
    });



    /* 编辑 */
    $('#editRowData').on('click', function () {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        //获取选中项
        var sele_data = $('#' + tableId).XGrid('getSeleRow');
        console.log(sele_data);
        //判断选中个数和选中数据状态
        if (!sele_data||sele_data.length!=1) {
            utils.dialog({
                title: '提示',
                content: "请勾选一条单据(单选)",
                width: 400,
                okValue: '确定',
                ok: function () {

                }

            }).showModal();
        }else {
            if(sele_data[0].documentsState !="待处理" && sele_data[0].documentsState != '已驳回'){
                utils.dialog({
                    title: '提示',
                    content: "只有待处理或已驳回的销售退回单才能编辑 ",
                    width: 400,
                    okValue: '确定',
                    ok: function () {

                    }

                }).showModal();

                return;
            }else {
                utils.openTabs("return_receive_check","编辑销售退回收货单", '/proxy-order/order/orderReturn/orderReturnController/toUpdate?salesReturnCod='+sele_data[0].salesReturnCode);
            }

        }



    });

    var _ind = 0; // tab 当前项的下标  销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    //获取当前tab  的下标 销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    function getTabInd() {
        for (var i = 0; i < $('.pull-left li').length; i++) {
            if ($('.pull-left li').eq(i).hasClass('active')) {
                _ind = $('.pull-left li').eq(i).index();
            }
        }
        return _ind;
    }
    /* 导出 */
    $('#exportRowData').on('click', function () {

        utils.exportAstrictHandle('table_a', Number($('#totalPageNum').text())).then( () => {
            return false;
      }).catch(() => {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            var formData = $('#form_a').serializeToJSON();
            formData["colName"] = colName;
            formData["colNameDesc"] = colNameDesc;

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length) {
                if (!data.length) {
                    data = [data];
                }
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
            }
            console.log(colName);

            // console.log(colNameDesc);
            switch (getTabInd()) {
                case 0:
                    httpPost("/proxy-order/order/orderReturn/orderReturnController/exportOrderReturnList", formData);
                    break;
                case 1:
                    httpPost("/proxy-order/order/orderReturn/orderReturnController/exportOrderReturnDetailList", formData);
                    break;
            }
        });
   });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    /* 客户名称查询 */
    $('#s_user').on('click', function (e) {
        var user_d = $(e.target).prev('input').val();
        utils.dialog({
            url:"/proxy-order/order/orderReturn/orderReturnController/toUserList",
            title: '客户列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $('#customerCode').val(data.customerCode);
                    $('#customerName').val(data.customerName);
                    console.log(data);
                }
            },
            oniframeload: function () {

            }
        }).showModal();
        return false;
    })
    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var commodity_d = $(e.target).prev('input').val();
        utils.dialog({
            url:"/proxy-order/order/orderReturn/orderReturnController/toCommodityList",
            title: '商品列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            data: commodity_d, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $('#productName').val(data.productName);
                    $('#productCode').val(data.productCode);
                    console.log("this.returnValue", data);
                }
            },
            oniframeload: function () {

            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })
    $('#input_goodName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + $("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#input_goodName").val("");
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });

    /* 商品名称查询 */
    $('#q_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#drugCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })

    //获取总计
    getStatisticsSums();
})