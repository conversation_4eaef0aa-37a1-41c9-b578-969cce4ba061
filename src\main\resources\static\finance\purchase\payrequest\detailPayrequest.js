/**
 * 
 * @param { { text: string, tag:string, style:CSSStyleDeclaration }[] } list 
 * @param { HTMLElement  } parent
 */
const panelTitleContentCreate = (list, parent) => {
	parent.innerText = "";
	for (const i in list) {
		const el = document.createElement(list[i].tag);
		if (!el) {
			console.error("不存在的标签！")
			return 
		}
		el.innerText = list[i].text;
		for (const k in list[i].style) {
			el.style[k] = list[i].style[k];
		}
		parent.appendChild(el);
	}
}
$(function () {
    //动态设置页面高度
    $('.content').css('height', $(window).height());

    /**
     * 获取款项类型
     */
    var fundTypes = JSON.parse($('#fundTypes').val());

    function getFundName(code) {
        var result = '';
        if (fundTypes) {
            $.each(fundTypes, function (index, item) {
                if (item.code == code) {
                    result = item.name;
                }
            })
        }
        return result;
    }

    $.ajax({
        method: "POST",
        url: "/proxy-finance/finance/purchase/payrequestinfo/getPayrequestInfo",
        data: {
            "billNo": $("#billNo").val(),
        },
        dataType: 'json',
        cache: false,
    }).done(function (data) {
        // data.result.createDate = moment(data.result.createDate).format('YYYY-MM-DD HH:mm:ss');
        data.result.expectPayTime = moment(data.result.expectPayTime).format('YYYY-MM-DD');
        $('#myform').JSONToform(data.result);
        $('#basicInfo').JSONToform(data.result);
        $("#remarks").text(data.result.remarks);
        $('#isPrepay').val(data.result.isPrepay);
        $('#supplierProfitRate').val(data.result.supplierProfitRate);
        $('#isPrepayStr').val(getFundName(data.result.isPrepay));
        $('#applayAmount').val(data.result.applayAmount ? data.result.applayAmount.formatMoney('2', '', ',', '.') : '0.00');
        $('#payableBalance').val(data.result.payableBalance ? data.result.payableBalance.formatMoney('2', '', ',', '.') : '0.00');
        $('#businessPayBalance').val(data.result.businessPayBalance ? data.result.businessPayBalance.formatMoney('2', '', ',', '.') : '0.00');
        $('#acceptRebate').val(data.result.acceptRebate ? data.result.acceptRebate.formatMoney('2', '', ',', '.') : '0.00');
        $('#transferRebate').val(data.result.transferRebate ? data.result.transferRebate.formatMoney('2', '', ',', '.') : '0.00');
        $('#prepaymentAmountDeduction').val(data.result.prepaymentAmountDeduction ? data.result.prepaymentAmountDeduction.formatMoney('2', '', ',', '.') : '0.00');
        $('#actualPaymentAmount').val(data.result.actualPaymentAmount ? data.result.actualPaymentAmount.formatMoney('2', '', ',', '.') : '0.00');
        $('#prepaymentAvailableAmount').val(data.result.prepaymentAvailableAmount ? data.result.prepaymentAvailableAmount.formatMoney('2', '', ',', '.') : '0.00');
        $("#a_lastPayTime").val(data.result.lastPayTime);
        $('#ticketDeduction').val(data.result.ticketDeduction ? data.result.ticketDeduction.formatMoney('2', '', ',', '.') : '0.00');
        $('#otherDeductions').val(data.result.otherDeductions ? data.result.otherDeductions.formatMoney('2', '', ',', '.') : '0.00');
		//超额审批流
		if (data.result.excess) {
			/* title.val("");
			const titleSpan = document.createElement("span");
			//预付款额度
			const prepaymentLimitSpan = document.createElement("span");
			//已使用额度包含审核中的
			const usedAmountIncludeUnderReviewSpan = document.createElement("span");
			//超出额度
			const exceedeAmount = document.createElement("span"); */
			const parent = document.getElementById("panel-title");
			panelTitleContentCreate([{
				text: "预付款超额度审批流程",
				tag: "span",
				style: {
					fontSize: "16px",
					color: "red",
					marginRight: "25px"
				}
			}, {
				text: "预付款总额度 : ",
				tag: "span",
				style: {
					color: "black",
					fontSize: "14px",
				}
			}, {
				text: data.result.prepaymentLimit,
				tag: "span",
				style: {
					color: "red",
					fontSize: "14px",
					marginRight: "15px"
				}
			}, {
				text: "预付款已使用额度(不含本次) : ",
				tag: "span",
				style: {
					color: "black",
					fontSize: "14px"
				}
			}, {
				text: data.result.usedAmountIncludeUnderReview,
				tag: "span",
				style: {
					color: "red",
					fontSize: "14px",
					marginRight: "15px"
				}
			}, {
				text: "超额度(包含本次) : ",
				tag: "span",
				style: {
					color: "black",
					fontSize: "14px"
				}
			}, {
				text: data.result.exceedeAmount,
				tag: "span",
				style: {
					color: "red",
					fontSize: "14px"
				}
			}], parent);
		}
		if (data.result.status === 4) {
			$("#editBtn").show();
			document.getElementById("editBtn").onclick = () => {
				window.location.href = `/proxy-finance/finance/purchase/payrequestinfo/addRequestApplication?isKa=${data.result.isKa}&billNo=${$("#billNo").val()}`;
			}
		}
        if (data.result.payableType != '112' && data.result.payableType != '114' && data.result.payableType != '115') {
            $('#accept_transfer_Rebate').hide();
        }
        //应付余额（采购）字段增加展示逻辑：当此金额小于0时，金额字体红色显示
        if (data.result.businessPayBalance < 0) {
            $('#businessPayBalance').css('color','red');
        } else {
            $('#businessPayBalance').css('color','');
        }
        if (data.result.settlementModes == 1) {
            $("#settlementDiv").css("display", "block")
            $("#a_settlementModes").attr("checked", "checked");
            $("#a_settlementModes").val("1");
            $("#a_settlementInterval").val(data.result.settlementInterval);
            $("#a_settlementDay").val(data.result.settlementDay);
            $("#a_paymentDay").val(data.result.paymentDay);
        } else {
            $("#a_settlementModes").attr("checked", false);
            $("#settlementDiv").css("display", "none");
            $("#a_settlementModes").val("2");
        }
        if (data.result.approvalProcessId) {
            loadFolow(data.result.approvalProcessId);
        }
        // 展示附件
        let fileList = data.result.payrequestAccessoryVos;
        if (fileList && fileList.length != 0) {
            let str = '';
            $(fileList).each((index, item) => {
                str += `<div data-url="` + item.enclosureUrl + `" style="display: inline-block;padding: 10px 20px 10px 10px; position: relative;">
                                        <span style=" position: absolute;width: 15px;height: 15px;top: 0;right: 0;z-index: 1;cursor: pointer;"></span>
                                        <a href='javascript:;' onclick='uploadFile(this)' data-url="` + item.enclosureUrl + `" data-name="` + item.enclosureName + `">` + item.enclosureName + `</a></div>`
                // <a href='`+item.enclosureUrl+`' target="_blank" download="`+item.enclosureName+`">`+item.enclosureName+`</a></div>`
            });
            $('.uploadFiles_div').append(str);
            $('.uploadFiles_div').css('display', 'block')
        }

        console.log(data);
        console.log("approvalProcessId" + data.result.approvalProcessId);
        //  loadFolow(data.result.approvalProcessId);
        if (data.result.isPrepay == 'C02') {
            //应付
            $('.nav-tabs>li:eq(0)').removeClass('active');
            $('.nav-tabs>li:eq(1)').addClass('active');
            // $('.nav-content>.panel-body:eq(0)').hide();
            // $('.nav-content>.panel-body:eq(1)').show();
            $('.nav-content>.panel-body:eq(1)').css('display', 'block').siblings().hide();
            $('.nav-content>.panel-body:eq(1)').addClass('active').siblings().removeClass('active');
            loadInvoiceTab(data.result.billNo);
        } else {
            loadOrderTab(data.result.billNo);
        }
        if (data.result.supplierNo) {
            getReportAmount(data.result.supplierNo, data.result.orgName, data.result.businessPayBalance);
        }
    });

    function getReportAmount(supplierNo, orgCode, businessPayBalance) {
        $("#sumInvoicedMoney").val("");
        //$("#businessPayBalance").val("");
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/findSupplierReportAmount",
            data: {
                supplierNo: supplierNo,
                orgCode: orgCode
            },
            dataType: 'json',
            cache: false,
        }).done(function (data) {
            console.log("获取累计发票金额,应付余额(采购):" + data);
            $("#sumInvoicedMoney").val(parseFloat(data.result.sumInvoicedMoney).formatMoney('2', '', ',', '.'));
            //$("#businessPayBalance").val(parseFloat(data.result.businessPayBalance).formatMoney('2', '', ',', '.'));
            $("#addUpPurchaseAmount").val(parseFloat(data.result.addUpPurchaseAmount).formatMoney('2', '', ',', '.'));
            getStorageReportAmount(supplierNo, orgCode, businessPayBalance);
        });
    }

    function getStorageReportAmount(supplierNo, orgCode, businessPayBalance) {
        $("#storageMoney").val("");
        $("#payableStorageScale").val("");
        $("#badStorageMoney").val("");

        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/findStorageReportAmount",
            data: {
                supplierNo: supplierNo,
                orgCode: orgCode,
                businessPayBalance: businessPayBalance
            },
            dataType: 'json',
            cache: false,
        }).done(function (data) {
            console.log("获取库存金额:" + data);
            $("#storageMoney").val(parseFloat(data.result.storageMoney).formatMoney('2', '', ',', '.'));
            //应付库存比=应付余额（采购）/库存金额*100%；不能整除的情况，百分比小数点后保留两位小数；库存金额为0时,显示 '-'
            if (data.result.storageMoney == '0' || data.result.storageMoney == '0.00' || data.result.storageMoney == '') {
                $("#payableStorageScale").val("-");
            } else {
                $("#payableStorageScale").val(data.result.payableStorageScale);
            }
            $("#badStorageMoney").val(parseFloat(data.result.badStorageMoney).formatMoney('2', '', ',', '.'));
        });
    }

    window.isFirst = true;

    /**
     * 加载订单数据开始
     * am billNo
     */
    function loadOrderTab(billNo) {
        var colNames = ['隱藏列', '单据类型', '单据编号', '业务类型', '供应商编号', '供应商名称', '送货方式', '是否预付', '业务对接人', '业务联系方式一', '供应商仓库地址',
            '预计到货时间', '金额合计',
            '税额合计', '税价合计', '本次申请金额', '累计申请金额'
        ];
        var colModel = [
            {
                name: 'id',
                hidden: true
            },
            {
                name: 'billType',
                index: 'billType'
            },
            {
                name: 'billNo',
                index: 'billNo'
            }, {
                name: 'channelName'
            },
            {
                name: 'supplierNo',
                index: 'supplierNo'
            },
            {
                name: 'supplierName',
                index: 'supplierName'
            },
            {
                name: 'deliveryMethod',
                index: 'deliveryMethod',
                formatter: function (val) {
                    if (val == "0") {
                        return "厢式送货";
                    } else if (val == "1") {
                        return "冷藏车";
                    } else if (val == "2") {
                        return "保温车";
                    } else if (val == "3") {
                        return "冷藏箱";
                    } else if (val == "4") {
                        return "其他封闭式车辆"
                    } else {
                        return ""
                    }
                },
                unformat: function (val) {
                    if (val == "厢式送货") {
                        return "0";
                    } else if (val == "冷藏车") {
                        return "1";
                    } else if (val == "保温车") {
                        return "2";
                    } else if (val == "冷藏箱") {
                        return "3";
                    } else if (val == "其他封闭式车辆") {
                        return "4"
                    }

                }
            }, {
                name: 'isPrepay',
                index: 'isPrepay',
                width: 250,
                sortable: false,
                editable: true,
                formatter: function (val) {
                    if (val == "1") {
                        return "是"
                    } else if (val == "0") {
                        return "否"
                    } else {
                        return "";
                    }
                },
                unformat: function (val) {
                    if (val == "是") {
                        return "1"
                    } else if (val == "否") {
                        return "0"
                    } else {
                        return "";
                    }
                }
            },
            {
                name: 'deliveryman',
                index: 'deliveryman'
            },
            {
                name: 'deliveryTel',
                index: 'deliveryTel'
            }, {
                name: 'storeAddress',
                index: 'storeAddress'
            }, {
                name: 'expectArrivalTime',
                index: 'expectArrivalTime',
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return '';
                    }
                },
            }, {
                name: 'amount',
                index: 'amount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'taxAmount',
                index: 'taxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'taxLimitAmount',
                index: 'taxLimitAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'thisTimeApplayAmount',
                index: 'thisTimeApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'totalApplayAmount',
                index: 'totalApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }
        ];

        var url;
        url = '/proxy-finance/finance/purchase/payrequestinfo/selectOrderByPayReqNo?reqNo=' + billNo

        $('#X_Tablea').XGrid({
            //data: grid_data,
            url: url,
            // url: 'http://localhost:8080/account/find',
            colNames: colNames,
            colModel: colModel,
            rownumbers: true,
            rowNum: 20,
            rowList: [20, 50, 100],//分页条数下拉选择
            altRows: true,//设置为交替行表格,默认为false
            multiselect: true,
            //rownumbers:true,
            // selectandorder: true,
            gridComplete: function () {
                setTimeout(function (param) {
                    var data = $('#X_Tablea').XGrid('getRowData');
                    $('#X_Tablea').XGrid('addRowData', {
                        id: 11111,
                        amount: totalTable(data, 'amount'),
                        taxAmount: totalTable(data, 'taxAmount'),
                        taxLimitAmount: totalTable(data, 'taxLimitAmount'),
                        thisTimeApplayAmount: totalTable(data, 'thisTimeApplayAmount'),
                        totalApplayAmount: totalTable(data, 'totalApplayAmount')

                    }, 'last');
                    $('#X_Tablea tr:last-child td:eq(1)').html('');
                    $('#X_Tablea tr:last-child td:first-child').html('合计');
                    if (window.isFirst) {
                        $('#X_Tablea input[type=checkbox]').prop('checked', true).trigger('input');
                        window.isFirst = false;
                    }
                }, 200)

            },
            onSelectRow: function (id, dom, obj, index, event) {
                //选中事件
                //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
                //$('#basicInfo').JSONToform(obj);
                // console.log(id, dom, obj, index, event)
            },
            ondblClickRow: function (id, dom, obj, index, event) {
                //console.log('双击行事件', obj);
                this.returnValue = obj;
                // console.log(obj)
                // window.location.href = "/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNO=" + obj.orderNo+"&supplierBaseId="+"supplierBaseId";
                if (obj.billNo) {
                    utils.openTabs('financePurchaseOrderDetail', '采购订单详情', '/proxy-finance/finance/purchase/payrequestinfo/toOrderDetail?purchaseOrderNo=' + obj.billNo + '&type=1', {reload: false});
                }
                return obj;
            },
            pager: '#grid_pager1',
        });

        // 查询
        $('#searchBtn').bind('click', function (param) {
            $('#X_Tablea').XGrid('setGridParam', {
                url: '/proxy-finance/finance/purchase/payrequestinfo/loadPurchaseOrder',
                postData: {
                    page: 1
                }
            }).trigger('reloadGrid');
        })


        $('#X_Tablea').on('click', 'input[type="checkbox"]', function () {
            console.log('sss')
        })
    }

    ////////////////////////////加载订单数据结束////////////////////////////////////////////////


/////////////////////////////////加载发票数据开始/////////////////////////////////////////////////////////////////////
    function loadInvoiceTab(billNo) {
        var colNames = ['隱藏列', 'ERP发票编号', '开票日期', '供应商编号', '供应商名称', '发票不含税金额', '是否预付', '送货人', '送货人电话', '供应商仓库地址',
            '预计到货时间', '金额合计',
            '税额合计', '税价合计', '状态', '本次申请金额', '累计申请金额'
        ];
        //var colNames1 = ['隱藏列','隐藏列','ERP发票编码', '开票日期', '供应商编号', '供应商名称', '发票不含税金额合计', '发票税额合计', '发票价税合计', '未核销金额','本次申请金额','累计申请金额','最早可付款日期',"汇总扣减金额","实际付款金额"];
        var colNames1 = ['隱藏列', '隐藏列', 'ERP发票编码', '供应商发票号', '开票日期', '供应商编号', '供应商名称', '发票不含税金额合计', '发票税额合计', '发票价税合计', '未核销金额', '本次申请金额', "汇总扣减金额", "实际付款金额", '累计申请金额', '最早可付款日期'];
        var colModel1 = [
            {
                name: 'id',
                hidden: true
            },
            {
                name: 'isInit',
                hidden: true
            },
            {
                name: 'invoiceNo',
                index: 'invoiceNo'
            }, {
				name: 'supplierInvoiceNumber',
				index: 'supplierInvoiceNumber'
			}, {
                name: 'invoiceCreateDate',
                index: 'invoiceCreateDate',
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return '';
                    }
                }
            },
            {
                name: 'supplierNo',
                index: 'supplierNo'
            },
            {
                name: 'supplierName',
                index: 'supplierName'
            },
            {
                name: 'noTotalTaxAmount',
                index: 'noTotalTaxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'totalInvoiceValue',
                index: 'totalInvoiceValue',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'totalInvoiceTax',
                index: 'totalInvoiceTax',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'unwriteoffMoney',
                index: 'unwriteoffMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'thisTimeApplayAmount',
                index: 'thisTimeApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'deductionAmount',
                index: 'deductionAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'actualPayAmount',
                index: 'actualPayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'totalApplayAmount',
                index: 'totalApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'earliestPayTime',
                index: 'earliestPayTime'
            }
            ];

        var url;
        url = '/proxy-finance/finance/purchase/payrequestinfo/selectInvoiceByPayReqNo?billNo=' + billNo;


        $('#Y_Tablea').XGrid({
            url: url,
            colNames: colNames1,
            colModel: colModel1,
            rowNum: 20,
            rownumbers: true,
            altRows: true, //设置为交替行表格,默认为false
            multiselect: true,
            pager: '#grid_pager2',
            ondblClickRow: function (e, c, a, b) {
                console.log('双击行事件', e, c, a, b);
                var billNo = a.invoiceNo;
                var isInit = a.isInit;
                if (billNo) {
                    showDetail(billNo, isInit);
                }

            },
            gridComplete: function () {
                queryIsGrayOfOrgForPaymentRequest().then((isGray) => {
                    if (!isGray) {
                        colModel1.forEach(colModelItem => {
                            if (['earliestPayTime', 'deductionAmount', 'actualPayAmount'].includes(colModelItem.name)) {
                                colModelItem.hidden = true
                            }
                        })
                        $('#Y_Tablea').setGridParam({
                            colModel: colModel1
                        });
                    }
                })
                setTimeout(function (param) {
                    var data = $('#Y_Tablea').XGrid('getRowData');
                    $('#Y_Tablea').XGrid('addRowData', {
                        id: 222222,
                        noTotalTaxAmount: totalTable(data, 'noTotalTaxAmount'),
                        totalInvoiceValue: totalTable(data, 'totalInvoiceValue'),
                        totalInvoiceTax: totalTable(data, 'totalInvoiceTax'),
                        unwriteoffMoney: totalTable(data, 'unwriteoffMoney'),
                        actualPayAmount: totalTable(data, 'actualPayAmount'),
                        thisTimeApplayAmount: totalTable(data, 'thisTimeApplayAmount'),
                        totalApplayAmount: totalTable(data, 'totalApplayAmount')

                    }, 'last');
                    $('#Y_Tablea tr:last-child td:eq(1)').html('');
                    $('#Y_Tablea tr:last-child td:first-child').html('合计');
                    if (window.isFirst) {
                        $('#Y_Tablea input[type=checkbox]').prop('checked', true).trigger('input');
                        window.isFirst = false;
                    }
                }, 200)

            },
        });

        // 查询
        $('#searchInvoiceBtn').bind('click', function (param) {
            $('#X_Tablea').XGrid('setGridParam', {
                url: '/proxy-finance/finance/purchase/payrequestinfo/getPayreqInvoice',
                postData: {
                    page: 1
                }
            }).trigger('reloadGrid');
        })

    }

    //查询是否灰度环境
    function queryIsGrayOfOrgForPaymentRequest() {
        return new Promise((resolve) => {
            $.ajax({
                url: "/proxy-finance/finance/purchase/invoice/isGrayOfOrgForPaymentRequest",
                type: "get",
                dataType: 'json',
                success: function (result) {
                    if (result.code === 0) {
                        resolve(true)//灰度中
                    } else {
                        resolve(false)//非灰度中
                    }
                },
                error: function (err) {
                    resolve(false)//非灰度中
                }
            })
        })
    }

    /////////////////////////////////加载发票数据结束/////////////////////////////////////////////////////////////////////

    ////////////////////////////////公共方法开始//////////////////////////////////////////////////////////
    //合计
    function totalTable(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat(item[colName]);
        })
        return count.toFixed(2);

    }

    function loadFolow(processId) {

        //132501   90047 purchaseReturn
        // var processId="${processId!}";
        // var taskId="${taskId!}"; //后续需修改，默认展示90047
        $.ajax({
            type: "POST",
            // url: "/proxy-purchase/purchase/purchaseRefundProductOrder/queryTotle?key=purchaseReturn",
            url: "/proxy-finance/finance/purchase/payrequestinfo/queryTotle?processInstaId=" + processId,
            async: false,
            error: function () {
                alert("审批意见不能为空！");
            },
            success: function (data) {
                if (data.code == 0) {
                    if (data.result != null) {
                        $('.flow').process(data.result);
                    }
                } else {
                    utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
                }
            }
        });
    }


    //保存按钮绑定
    $("#saveBtn").click(function () {
        var tempArr = [],
            orderData = $('#X_Tablea').XGrid('getSeleRow'),
            invoiceData = $('#Y_Tablea').XGrid('getSeleRow');

        if (orderData && !orderData.length) {
            tempArr.push(orderData);
            orderData = tempArr;
        }
        console.log(orderData);
        //return false;
        var ttx = $("#basicInfo").serializeToJSON();
        /*  $("#sumForm").find("span.col-md-8").each(function(i){
              ttx[$(this).attr("name")] = $(this).text();
          });*/
        // console.log(JSON.stringify(ttx));
        //  $("#example").empty().append(JSON.stringify(ttx)+"</br>"+JSON.stringify(tx));
        /*  if (validform("basicInfo").form()) {
              utils.dialog({content: '校验通过！', quickClose: true, timeout: 4000}).showModal();
          } else {//验证不通过
              utils.dialog({content: '校验不通过！', quickClose: true, timeout: 4000}).showModal();
              return false;
          }*/

        $('#X_Tablea').on('change', 'input[type="checkbox"]', function () {
            if ($(this).prop('checked')) {
                var rowId = $(this).closest('tr').attr('id');
                var obj = $('#X_Tablea').XGrid('getRowData', rowId);
                $('#basicInfo').JSONToform(obj);
            }
        })

        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/editPayrequest",
            data: {
                "PayrequestOrderDetailVoData": JSON.stringify(orderData),
                "PayrequestInvoiceDetailVoData": JSON.stringify(invoiceData),
                "PurchasePayrequestInfoVo": JSON.stringify(ttx)
            },
            dataType: 'json',
            cache: false,
        }).done(function (msg) {
            alert("Data Saved: " + msg);
        });


    });

    // 转办
    $('#transfer').bind('click', function () {
        utils.dialog({
            title: '审批驳回留言',
            content: $('#transfer_modal'),
            width: 500,
            cancelValue: '取消',
            cancel: true,
            okValue: '确定',
            ok: function () {
                console.log('确认回调')
            }
        }).show()
    })

    // 驳回
    $('#reject').bind('click', function () {

        utils.dialog({
            title: '审批驳回留言',
            content: $('#reject_modal'),
            width: 500,
            cancelValue: '取消',
            cancel: true,
            okValue: '确定',
            ok: function () {
                var data = new Object();
                data.billNo = $("#billNo").val();
                data.message = $("#lyp").text();
                data.approveStatus = 2;
                $.ajax({
                    method: "POST",
                    url: "/proxy-finance/finance/purchase/payrequestinfo/approvePayrequestInfo",
                    data: {
                        billNo: $("#billNo").val(),
                        message: $("#lyp").text(),
                        approveStatus: 2,
                        callFlag: "1"
                    },
                    dataType: 'json',
                    cache: false,
                }).done(function (msg) {
                    utils.dialog({content: '驳回成功', quickClose: true, timeout: 2000}).show();
                });
            }
        }).show()
    })


    let iloading = false;
    //其他机构余额
    $("#showOtherBusinessPayBalance").bind("click", function(event) {
        event.stopPropagation();
        if (iloading) return
        iloading = true;
        //请求
        $.ajax({
            url: "/proxy-finance/finance/purchase/payRequestInfoAppend/findSupplierOrgBalance",
            method: "POST",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({
                supplierCode: $("#supplierNo").val()
            })
        }).done(function(data) {
            if (data.code == 0) {
                if (!data.result || data.result.length == 0) {
                    var d = dialog({
                        content: '未查询到机构'
                    });
                    d.show();
                    setTimeout(function () {
                        d.close().remove();
                    }, 2000);
                } else {
                    const result = data.result;
                    const table = document.createElement("table");
                    result.sort((oldVal,curVal) => oldVal.supplierBalance - curVal.supplierBalance);
                    table.setAttribute("id","XX_Table");
                    const formData = [];
                    result.forEach((val,index) => {
                        formData.push({
                            id : index + 1,
                            orgName: val.orgName,
                            supplierBalance: val.supplierBalance
                        })
                    })
                    utils.dialog({
                        title: "所有机构应付余额（采购）",
                        content: table,
                        cancel: false,
						okValue: "关闭",
                        ok: function() {
                            
                            setTimeout(() => {document.getElementById("XX_Table").remove();},0)
                            this.close().remove();
                        }
                        
                    }).showModal();

                    $("#XX_Table").XGrid({
                        colNames: ["序号", "机构", "应付余额（采购）"],
                        colModel: [{
                            name: "id",
                            index: "id"
                        }, {
                            name: "orgName",
                            index: "orgName"
                        }, {
                            name: "supplierBalance",
                            index: "supplierBalance"
                        }],
                        data: formData
                    })
					setTimeout(() => {
                        const tds = document.querySelectorAll(`#XX_Table td[row-describedby="supplierBalance"]`);
                        formData.forEach((val, index) => {
                            if (val.supplierBalance < 0) {
                                console.log("asdasdasdasdasdsadasd")
                                tds[index].style.color = "red";
                            }
                        })
                    }, 0)
                }
            } else {
                var d = dialog({
                    content: '获取数据失败'
                });
                d.show();
                setTimeout(function () {
                    d.close().remove();
                }, 2000);
            }
            iloading = false;
        }).fail((error) => {
            var d = dialog({
                content: '请求出错'
            });
            d.show();
            setTimeout(function () {
                d.close().remove();
            }, 2000);
            iloading = false;
        })
    });

    // 审批
    $('#verify').bind('click', function () {

        utils.dialog({
            title: '审批留言',
            content: $('#verify_modal'),
            width: 500,
            cancelValue: '取消',
            cancel: true,
            okValue: '确定',
            ok: function () {
                var data = new Object();
                data.billNo = $("#billNo").val();
                data.message = $("#ly").text();
                data.approveStatus = 1;
                $.ajax({
                    method: "POST",
                    url: "/proxy-finance/finance/purchase/payrequestinfo/approvePayrequestInfo",
                    data: {
                        billNo: $("#billNo").val(),
                        message: $("#ly").text(),
                        approveStatus: 1,
                        callFlag: "1"
                    },
                    dataType: 'json',
                    cache: false,
                }).done(function (msg) {
                    utils.dialog({content: '审批成功', quickClose: true, timeout: 2000}).show();
                });
            }
        }).show()

    })

    // 返回    
    /* $('#return').bind('click', function () {
         // 关闭弹窗
         dialog.close();
     })*/


    $('#X_Tablea').on('change', 'input[type="checkbox"]', function () {
        //console.log('1231')
    })

    function showDetail(billNo, isInit) {
        var title = "采购发票详情";
        var url = '/proxy-finance/finance/purchase/payrequestinfo/detailInvoiceInfo?invoiceNo=' + billNo + '&isInit=' + isInit;
        if (isInit == '1') {
            utils.dialog({
                title: title,
                url: url,
                width: $(window).width() * 0.8,
                data: 'val值', // 给modal 要传递的 的数据
                onclose: function () {
                    $('iframe').remove();
                }
            }).show();
        } else {
            utils.dialog({
                title: title,
                url: url,
                width: $(window).width() * 0.8,
                height: $(window).height() * 0.8,
                data: 'val值', // 给modal 要传递的 的数据
                onclose: function () {
                    $('iframe').remove();
                }
            }).show();
        }
    }

    $("#approveBtn").bind('click', function () {
        utils.dialog({
            title: '审批详情',
            content: $('#approveInfo_modal'),
            width: 1500,
            height: $(window).height() * 0.8
        }).show()

    });
    loadApproveInfo();

    function loadApproveInfo() {
        var colNames = ['处理节点名称', '处理人', '处理时间', '状态', '处理意见'];
        var colModel = [
            {
                name: 'nodeName',
                index: 'nodeName'
            }, {
                name: 'handler',
                index: 'handler',

            }, {
                name: 'handTime',
                index: 'handTime',
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD HH:mm:ss');
                    } else {
                        return '';
                    }
                }
            }, {
                name: 'handStatusStr',
                index: 'handStatusStr'
            }, {
                name: 'handDetail',
                index: 'handDetail'
            }];

        $('#approveInfo_Tablea').XGrid({
            url: "/proxy-finance/finance/purchase/payrequestinfo/selectApproveInfoByPayReqNo?billNo=" + $("#billNo").val(),
            colNames: colNames,
            colModel: colModel,
            rowNum: 99999999,
            rownumbers: true,
            altRows: true //设置为交替行表格,默认为false
        });
    }


    $("#pauseBtn").bind('click', function () {
        utils.dialog({
            title: '提示',
            content: "暂停付款操作成功后，该采购付款申请单将无法进行采购付款处理，请谨慎操作！",
            width: 500,
            cancelValue: '取消',
            cancel: true,
            okValue: '确定',
            ok: function () {
                if ($("#f_createUser").val() == $("#f_loginUser").val()) {
                    var data = new Object();
                    data.billNo = $("#billNo").val();
                    $.ajax({
                        method: "POST",
                        url: "/proxy-finance/finance/purchase/payrequestinfo/pausePayrequestInfo",
                        data: data,
                        dataType: 'json',
                        cache: false,
                        success: function (data) {
                            console.log(data);
                            if (data.code == "0") {
                                utils.dialog({content: '暂停付款成功', quickClose: true, timeout: 2000}).show();
                                setTimeout("window.location.reload()", 2000);
                            } else {
                                utils.dialog({content: '暂停付款失败', quickClose: true, timeout: 2000}).show();
                            }
                        }
                    });
                } else {
                    utils.dialog({content: '仅申请人可进行此操作', quickClose: true, timeout: 2000}).show();
                }
            }
        }).show()
    });


    $("#cancellBtn").bind('click', function () {
        if ($("#f_createUser").val() == $("#f_loginUser").val()) {
            var data = new Object();
            data.billNo = $("#billNo").val();
            $.ajax({
                method: "POST",
                url: "/proxy-finance/finance/purchase/payrequestinfo/cancellPayrequestInfo",
                data: data,
                dataType: 'json',
                cache: false,
                success: function (data) {
                    console.log(data);
                    if (data.code == "0") {
                        utils.dialog({content: '取消暂停付款成功', quickClose: true, timeout: 2000}).show();
                        setTimeout("window.location.reload()", 2000);
                    } else {
                        utils.dialog({content: '取消暂停付款失败', quickClose: true, timeout: 2000}).show();
                    }
                }
            });

        } else {
            utils.dialog({content: '仅申请人可进行此操作', quickClose: true, timeout: 2000}).show();
        }

    });
    // 预览
    $("#preview_btn").on('click', function () {
        var fileArrPreview = [];
        let allFiles = $('.uploadFiles_div>div');
        if (allFiles.length > 0) {
            $(allFiles).each(function (index, item) {
                let obj = {};
                obj.url = $(item).attr('data-url');
                obj.name = $(item).find('a').text();
                fileArrPreview.push(obj)
            });
            $.viewImg({
                fileParam: {
                    name: 'name',
                    url: 'url'
                },
                list: fileArrPreview
            })
        }
    })
})


// 下载附件
function uploadFile(that) {
    var a = [];
    var urls = $(that).attr('data-url');
    var names = $(that).attr('data-name');
    // var fileArr = [];
    // let allFiles = $('.uploadFiles_div>div');
    // if(allFiles.length > 0) {
    //     $(allFiles).each(function(index, item) {
    //         let obj = {};
    //         obj.url = $(item).attr('data-url');
    //         obj.name = $(item).find('a').text();
    //         fileArr.push(obj)
    //     });
    //
    // }
    //
    // $.viewImg({
    //     fileParam:{
    //         name:'name',
    //         url:'url'
    //     },
    //     list:fileArr
    // })
    downloadImg(urls, names)
    //  a.push('<form style="display: none" method="post">');
    //  a.push('<input name="file" value = ' + urls + '>');
    //  a.push('<input name="name" value = '+ names + '>');
    //  a.push('</form>');
    //
    //  var $eleForm = $(a.join(''));
    // // $eleForm.attr("action", "/proxy-finance/finance/purchase/payrequestinfo/download");
    //  $eleForm.attr("action", urls);
    //  $eleForm.attr("target","");
    //  $eleForm.attr("method","get");//设置请求类型
    //  $(document.body).append($eleForm);
    //  // 提交表单，实现下载
    //  $eleForm.submit();
    //  $eleForm.remove();
    //  a.push('<form style="display: none" method="post">');
    //  a.push('<input name="file" value = ' + urls + '>');
    //  a.push('<input name="name" value = '+ names + '>');
    //  a.push('</form>');
    //
    //  var $eleForm = $(a.join(''));
    // // $eleForm.attr("action", "/proxy-finance/finance/purchase/payrequestinfo/download");
    //  $eleForm.attr("action", urls);
    //  $eleForm.attr("target","");
    //  $eleForm.attr("method","get");//设置请求类型
    //  $(document.body).append($eleForm);
    //  // 提交表单，实现下载
    //  $eleForm.submit();
    //  $eleForm.remove();
}

function downloadImg(src, name) {
    var x = new XMLHttpRequest();
    //禁止浏览器缓存；否则会报跨域的错误
    x.open("GET", src + '?t=' + new Date().getTime(), true);
    x.responseType = 'blob';
    x.onload = function (e) {
        var url = window.URL.createObjectURL(x.response)
        var a = document.createElement('a');
        a.href = url
        a.download = name
        a.click()
    }
    x.send();
}
