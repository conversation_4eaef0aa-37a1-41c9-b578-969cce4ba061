$(function () {
    getTotalCount();
    //开始日期
    /* $('#beginTime').bind('click', function () {
         WdatePicker({el: 'beginTime', maxDate: '#F{$dp.$D(\'endTime\')}'});
     })

     //结束日期
     $('#endTime').bind('click', function () {
         WdatePicker({el: 'endTime', minDate: '#F{$dp.$D(\'beginTime\')}'});
     })
 */
    function getMaxDate(id) {
        var startTime = $('#' + id).val();
        return moment(startTime).add('31', 'days').format("YYYY-MM-DD");
    }


    function getMinDate(id) {
        var startTime = $('#' + id).val();
        return moment(startTime).subtract('31', 'days').format("YYYY-MM-DD");
    }

    //判断两个时间是否相差30天
    function isBetween(beginId, endId) {
        var firstTime = $('#' + beginId).val(),
            lastTime = $('#' + endId).val();

        if (moment(lastTime).isBefore(getMaxDate(beginId)) && moment(firstTime).isAfter(getMinDate(endId))) {
            return true;
        }

        return false;

    }


    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '1', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            //选中了
            window.isSelect = true;
            $("#supplierNo").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue', result.value);

            console.log(result)
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName',
            delivery: 'delivery',
            deliveryPhone: 'deliveryPhone'
        },
        onNoneSelect: function (params, suggestions) {
            //没选中
            $("#supplierNo").val("");
            $("#supplierName").val("");
            window.isSelect = false;
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });

    //放大镜查询
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if (e.keyCode === 13 && !$("#supplierName").attr('oldvalue')) {
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#supplierNo").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                } else {
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();
    }


    var d;
    var colNames = ['供应商编号', '供应商名称', '制单日期', '过账日期', '单据类型', '单据编号', '借方', '贷方', '核销状态', '剩余未核销金额', '制单人', '备注', 'id'],
        colModel = [
            {
                name: 'supplierNo',
                index: 'supplierNo',
                width: 200,//宽度
            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 60
            },
            {
                name: 'billTime',
                index: 'billTime',
                width: 150,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            },
            {
                name: 'tallyTime',
                index: 'tallyTime',
                width: 150,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'billType',
                index: 'billType',
                width: 150
            }, {
                name: 'billNo',
                index: 'billNo',
                width: 150
            }, {
                name: 'debteAmount',
                index: 'debteAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 250,
            }, {
                name: 'creditAmount',
                index: 'creditAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 250,
                sortable: false,
                editable: true,
            }, {
                name: 'hexiaoStateStr'
            },
            {
                name: 'hexiaoMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'billUser',
                index: 'billUser',
                width: 150
            }, {
                name: 'remarks',
                title: true,
//                 colMouseenter: function (eType) {
//                     var content = eType.rowData.remarks;
//                     if (!content) return false;
//                     d = dialog({
//                         align: 'top',
//                         padding: 10,
//                         content: '<textarea style="max-width: 220px;height:100%;resize: none;border: none;outline:none;text-align: center;" rows="1" readonly>' + content + '</textarea>',
//                         onshow: function () {
//                             var height = $(this.node).find('textarea')[0].scrollHeight;
//                             this.height(height);
//                         }
//                     })
//                     d.show(eType.e.target);
//                 },
//                 colMouseout: function (eType) {
//                     d.close().remove();
//                 },
                formatter: function (val) {
                    // val = val.replace(/\"/g, '“').replace(/\'/, '’');
                    return transEntity(val, true);
                }
            }, {
                name: 'id',
                hidden: true,
                hidegrid: true

            }, {
                name: 'hexiaoState',
                hidden: true,
                hidegrid: true
            },
        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    var totalTable = z_utils.totalTable;
    window.isFirst = true;
    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierPayRecords',
        // url: 'http://localhost:8080/account/find',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        multiselect: true,
        attachRow: true,
        postData: {
            supplierName: $("#supplierName").val(),
            keyWord: $("#keyWord").val(),
            startDate: $("#beginTime").val(),
            endDate: $("#endTime").val(),
        },
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
            var billNo = a.billNo;
            var type = a.billType
            showDetail(billNo, type);
        },
        onSelectRow: function (e, c, a, b) {
            var selectRow = $("#X_Table").XGrid('getRowData', e);
            console.log('xxx')
            // 已核销的，不可选中
            if (selectRow.hexiaoState === '3') {
                $('#X_Table').find("tr[id=" + selectRow.id + "] td input[type=checkbox]").prop('checked', false);
                $('#X_Table').find("tr[id=" + selectRow.id + "] td input[type=checkbox]").trigger('input')
            }
        },
        pager: '#grid-pager',
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['debteAmount', 'creditAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item, index) {
                lastRowEle.find("td[row-describedby=" + item + "]").text(totalTable(data, item))
            });
            for (var k = 0; k < data.length; k++) {
                var currentItem = $('#X_Table').XGrid('getRowData', data[k].id);
                $('#X_Table').find("tr[id=" + currentItem.id + "] td input[type=checkbox]").prop('disabled', currentItem.hexiaoState === '3')
            }
            var selectAllCb = $('#X_Table').find("tbody:first-child tr th input[type=checkbox]")
            selectAllCb.on('change', function () {
                for (var k = 0; k < data.length; k++) {
                    var currentItem = $('#X_Table').XGrid('getRowData', data[k].id);
                    if (currentItem.hexiaoState === '3') {
                        $('#X_Table').find("tr[id=" + currentItem.id + "] td input[type=checkbox]").prop('checked', false)
                        $('#X_Table').find("tr[id=" + currentItem.id + "]").removeClass('selRow').removeAttr('clickselrow');
                    }
                }
            })
            var _this = $(this);
            if (!window.isFirst && !_this.XGrid('getRowData').length) {
                utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
            }
        }
    });


    // 手动核销
    $('#shoudongHeXiaoBtn').bind('click', function () {
        //console.log(validform("myform").form());
        //if (validform("searchForm").form()) {//验证通过 "myform"为需要验证的form的ID
        console.log(2222);
        var param = $('#searchForm').serializeToJSON();
        var seleData = $('#X_Table').XGrid('getSeleRow');
        //var idList = [];
        var idList = "";
        if (seleData) {
            if (seleData.length) {
                $.each(seleData, function (index, item) {
                    idList += item.id + ","
                })
            } else {
                var id = seleData.id || '';
                idList = id + ",";
            }
        } else {
            utils.dialog({content: '请选择供应商往来账', quickClose: true, timeout: 2000}).show();
            return false;
        }

        var customerCode = seleData.length ? seleData[0].supplierNo : seleData.supplierNo;
        console.log(idList);
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/shoudongHeXiao",
            data: {
                supplierNo: customerCode,
                ids: idList
            },
            dataType: 'json',
            cache: false
        }).done(function (data) {
            if (data.code == 0) {
                utils.dialog({
                    content: "手动核销成功", quickClose: true,
                    timeout: 3000
                }).showModal();
                setTimeout(function () {
                    $("#searchBtn").trigger("click");
                }, 500);
            } else {
                utils.dialog({
                    content: "手动核销失败", quickClose: true,
                    timeout: 3000
                }).showModal();
            }
        });
        //} else {//验证不通过
        //utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
        //return false;
        //}
    })

    //自动核销
    $('#zidongHeXiaoBtn').bind('click', function () {
        if ($('#beginTime').val() == '') {
            utils.dialog({content: '请选择开始日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#endTime').val() == '') {
            utils.dialog({content: '请选择结束日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }

        if (!isBetween('beginTime', 'endTime')) {
            utils.dialog({content: '自动核销所选日期范围不能大于31天，请重新选择截止日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        utils.dialog({
            title: '提示',
            content: '将对所选日期范围的数据进行自动核销，是否继续？',
            width: 285,
            cancelValue: '否',
            cancel: function () {
            },
            okValue: '是',
            ok: function () {
                $.ajax({
                    method: "POST",
                    url: "/proxy-finance/finance/purchase/payrequestinfo/zidongHeXiao",
                    data: {
                        startDate: $("#beginTime").val(),
                        endDate: $("#endTime").val(),
                        supplierNo: $("#supplierNo").val()
                    },
                    dataType: 'json',
                    cache: false
                }).done(function (data) {
                    if (data.code == 0) {
                        utils.dialog({
                            content: "核销成功，本次核销成功供应商" + data.result.supplierTotal + "条，核销成功单据" + data.result.total + "条",
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                        setTimeout(function () {
                            $("#searchBtn").trigger("click");
                        }, 500);

                    } else {
                        utils.dialog({
                            content: "自动核销失败", quickClose: true,
                            timeout: 3000
                        }).showModal();
                    }
                });
            }
        }).showModal();
        return false;


    })

    //取消核销
    $('#quxiaoHeXiaoBtn').bind('click', function () {
        if ($("#supplierNo").val() == '') {
            if ($('#beginTime').val() == '') {
                utils.dialog({content: '请选择开始日期！', quickClose: true, timeout: 2000}).show();
                return false;
            }
            if ($('#endTime').val() == '') {
                utils.dialog({content: '请选择结束日期！', quickClose: true, timeout: 2000}).show();
                return false;
            }
        }

        // if (!isBetween('beginTime', 'endTime')) {
        //     utils.dialog({content: '取消核销所选日期范围不能大于31天，请重新选择截止日期！', quickClose: true, timeout: 2000}).show();
        //     return false;
        // }

        utils.dialog({
            title: '提示',
            content: '<h5>将对所选择日期范围内的数据取消核销，请谨慎操作！<br>如取消单笔核销记录，请到应收核销明细操作。<br><br>是否继续？</h5><h6>注意：2020-01-07之后的核销记录，可在应收核销明细进行取消核销操作。</h6>',
            width: 350,
            cancelValue: '否',
            cancel: function () {
            },
            okValue: '是',
            ok: function () {
                $.ajax({
                    method: "POST",
                    url: "/proxy-finance/finance/purchase/payrequestinfo/quxiaoHeXiao",
                    data: {
                        startDate: $("#beginTime").val(),
                        endDate: $("#endTime").val(),
                        supplierNo: $("#supplierNo").val()
                    },
                    dataType: 'json',
                    cache: false
                }).done(function (data) {
                    if (data.code == 0) {
                        utils.dialog({
                            content: "取消核销成功，本次取消核销供应商" + data.result.supplierTotal + "条，取消核销单据" + data.result.total + "条",
                            quickClose: true,
                            timeout: 3000
                        }).showModal();
                        setTimeout(function () {
                            $("#searchBtn").trigger("click");
                        }, 500);
                        // $("#searchBtn").trigger("click");
                    } else {
                        utils.dialog({
                            content: "取消核销失败", quickClose: true,
                            timeout: 3000
                        }).showModal();
                    }
                });
            }
        }).showModal();
        return false;
    })

    $("#searchBtn").on("click", function () {
        window.isFirst = false;
        //提交前验证
        //console.log(validform("myform").form());
        if (validform("searchForm").form()) {//验证通过 "myform"为需要验证的form的ID
            var param = $('#searchForm').serializeToJSON();
            console.log(param);
            var hexiaoType = $("#hexiaoType").val();

            if (hexiaoType == null || hexiaoType.length == 0 || hexiaoType.length == 3) {
                hexiaoType = []
            }
            $('#X_Table').XGrid('setGridParam', {
                url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierPayRecords',
                postData: {
                    supplierNo: $("#supplierNo").val(),
                    supplierName: $("#supplierName").val(),
                    keyWord: $("#keyWord").val(),
                    startDate: $("#beginTime").val(),
                    endDate: $("#endTime").val(),
                    hexiaoStateListStr: JSON.stringify(hexiaoType),
                    page: 1
                }
            }).trigger('reloadGrid');
            getTotalCount();
        }

    });

    //设置显示列
    $("#setBtn").on("click", function () {
        $('#X_Table').XGrid('filterTableHead');
    });


    //导出
    $('#exportBtn').on('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()), 1).then(() => {
            return false;
        }).catch(() => {
            //原始处理逻辑代码
            if (validform("searchForm").form()) {

                //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                var ck = false;
                // copy this parameter and the below buttons
                var nameModel = "";
                var exportColNames = ['供应商编号', '供应商名称', '制单日期', '过账日期', '单据类型', '单据编号', '借方', '贷方', '核销状态', '剩余未核销金额', '制单人', '备注'];
                addHtmlA(exportColNames);
                dialog({
                    content: $("#setCol"),
                    title: '筛选列',
                    width: $(window).width() * 0.4,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {
                        var newColName = [], newColModel = [];
                        $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                            if ($(this).is(":checked")) {
                                nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                            }
                        });
                        if (nameModel.length == 0) {
                            utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                        // var keyword = $("#keyword").val();
                        // var createTimeStart = $("#createTimeStart").val();
                        // var createTimeEnd = $("#createTimeEnd").val();
                        // var obj = $("#searchForm").serializeToJSON();
                        var hexiaoType = $("#hexiaoType").val();

                        if (hexiaoType == null || hexiaoType.length == 0 || hexiaoType.length == 3) {
                            hexiaoType = [];
                        }
                        var obj = {
                            supplierNo: $("#supplierNo").val(),
                            supplierName: $("#supplierName").val(),
                            keyWord: $("#keyWord").val(),
                            startDate: $("#beginTime").val(),
                            endDate: $("#endTime").val(),
                            hexiaoStateListStr: JSON.stringify(hexiaoType)
                        }
                        // obj["pageNum"] = "1";
                        // obj["pageSize"] = "1000000";
                        obj["nameModel"] = nameModel;
                        httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportSupplierHexiaoRecords", obj);
                    },
                    // copy button to other dialogues
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if (!ck) {
                                    $("#checkRow input").prop("checked", false);
                                    ck = true;
                                } else if (ck) {
                                    $("#checkRow input").prop("checked", "checked");
                                    ck = false;
                                } else {
                                    return false;
                                }
                                ;
                                return false;
                            }
                        }
                    ]
                    //copy ends here

                }).showModal();
            } else {
                utils.dialog({content: '请选择供应商', quickClose: true, timeout: 2000}).show();
                return false;
            }
        })
    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none; padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    function showDetail(billNo, type) {
        var title = "";
        var url = "";
        var height = 0;
        if (type.includes('发票调整单')) {
            title = '发票调整单详情';
            url = '/proxy-finance/finance/purchase/invoice/getAdjustInvoiceDetailPage?billNo=' + billNo;
            height = 200;
        } else if (type.includes('发票')) {
            title = "采购发票详情";
            url = '/proxy-finance/finance/purchase/payrequestinfo/detailInvoiceInfo?invoiceNo=' + billNo;
            height = $(window).height() * 0.8;
        } else {
            title = '采购付款单详情';
            url = '/proxy-finance/finance/purchase/payrequestinfo/toPaymentDetail?billNo=' + billNo;
            height = $(window).height() * 0.4;
        }
        utils.dialog({
            title: title,
            url: url,
            width: $(window).width() * 0.9,
            height: height,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                $('iframe').remove();
            }
        }).show();
    }


    function getTotalCount() {
        window.isFirst = false;
        //提交前验证
        //console.log(validform("myform").form());
        if (validform("searchForm").form()) {//验证通过 "myform"为需要验证的form的ID
            var param = $('#searchForm').serializeToJSON();
            console.log(param);
            var hexiaoType = $("#hexiaoType").val();

            if (hexiaoType == null || hexiaoType.length == 0 || hexiaoType.length == 3) {
                hexiaoType = []
            }
            //加载总数量
            $.ajax({
                url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierPayRecordsCount',
                dataType: 'json',
                timeout: 8000, //6000
                data: {
                    supplierNo: $("#supplierNo").val(),
                    supplierName: $("#supplierName").val(),
                    keyWord: $("#keyWord").val(),
                    startDate: $("#beginTime").val(),
                    endDate: $("#endTime").val(),
                    hexiaoStateListStr: JSON.stringify(hexiaoType)
                },
                success: function (data) {
                    if (data.code == 0 && null != data.result) {
                        $("#debteAmountSum").text(Number(data.result.debteAmountSum).toFixed(2));
                        $("#creditAmountSum").text(Number(data.result.creditAmountSum).toFixed(2));
                        //	$("#hexiaoMoneySum").text(Number(data.result.hexiaoMoneySum).toFixed(2));
                    } else {
                        $("#debteAmountSum").text('0.00');
                        $("#creditAmountSum").text("0.00");
                        //$("#hexiaoMoneySum").text("0.00");
                    }
                },
                error: function () {
                    $("#debteAmountSum").text('0.00');
                    $("#creditAmountSum").text("0.00");
                    //  $("#hexiaoMoneySum").text("0.00");
                    utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
                }
            });
        } else {
            $("#debteAmountSum").text('0.00');
            $("#creditAmountSum").text("0.00");
            //  $("#hexiaoMoneySum").text("0.00");


        }
    }
})