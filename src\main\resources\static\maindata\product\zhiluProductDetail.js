$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    /*首营申请修改记录*/
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

     //修改记录申请明细展开收起按钮
    $("#modifyTable,#firstEditRecordTable").on("click",".moreBtn",function(){
        var type=$.trim($(this).text());
        var tr=$(this).parents("tr");
        var innerHeiht=0;
        if(type == '展开')
        {
            innerHeiht=tr.find(".mrLeft").innerHeight();
            $(this).html('收起');
        }else if(type == '收起'){
            innerHeiht = 40;
            $(this).html('展开');
        }
        if(innerHeiht < 40){
            innerHeiht=40;
        }
        tr.find(".modifyTheRecord .itemContent").animate({
            height:innerHeiht
        },500)
    })
    //受托厂家是否显示
    //多选框选中
    loadData("keyConservationCategories");
    loadData("specialAttributes");
    function loadData(key) {
        var tagName, type, arr, thisVal;
        var value = $("#"+key).val();
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (type == 'checkbox') {
                if ($.type(value) == 'array') {
                    arr = value;
                } else if ($.type(value) == 'string') {
                    arr = value.split(',');
                }
                for (var i = 0; i < arr.length; i++) {
                    if (thisVal == arr[i]) {
                        $(this).attr('checked', true);
                        break;
                    }
                }
            }
        });
    }
    // 温度范围特殊处理
    var temperatureRange = $('#temperatureRanges').val();
    if(temperatureRange!=null){
        var tempArr = temperatureRange .split(',');
        for(var i=0;i<tempArr.length;i++){
            if(i==0){
                $('#temperatureRange1').val(tempArr[0]);
            }
            if(i==1){
                $('#temperatureRange2').val(tempArr[1]);
            }
        }
    }

    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    $("input").attr("readonly",true);
    $("textarea").attr("readonly",true);
    $("input[type='checkbox']").attr("disabled",true);
    $("input[type='radio']").attr("disabled",true);
    $("select").attr("disabled","disabled");
    $("#rowOperationsDiv").hide();
    $(".bootstrap-tagsinput span[data-role='remove']").hide();
    $("#auditOpinion").removeAttr("readonly");
    $(".glyphicon-search").hide();
    //信息回显
    showDictValue();
    setTimeout(function () {
        let scopeOfOperationVal = $('#scopeOfOperationVal').attr('data-value');
        if(scopeOfOperationVal == '中药饮片'){
            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地')
        }
    },200)
})
/**
 * 字典值回显
 */
function  showDictValue() {
    //商品大类
    showComValue("largeCategory","1017");
    showComValue("manufacturer","1003");
    //包装单位
    showComValue("packingUnit","1002");
    //剂型
    showComValue("dosageForm","1001");
    // 委托厂家--添加地址信息
    //存储条件
    showComValue("storageConditions","1019");
    //处方分类
    showComValue("prescriptionClassification","1016");
    //所属经营范围
    var simpleCode =$("#scopeOfOperation").val();
    var orgCode = $("#applicantOrgCode").val();
    if(simpleCode!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/queryscopebycode?orgCode='+orgCode+"&simpleCode="+simpleCode,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                console.log(data);
                if(data.code == 0)
                {
                    var simpleCodeName = "";
                    for (var i = 0;i<data.result.length;i++){
                        if (i!=data.result.length-1){
                            simpleCodeName = simpleCodeName + data.result[i].name+",";
                        } else {
                            simpleCodeName = simpleCodeName + data.result[i].name;
                        }
                    }
                    $("#scopeOfOperationVal").val(simpleCodeName);
                    $("#scopeOfOperationVal").attr("data-value",simpleCodeName);
                    $("#scopeOfOperationVal").attr('title',simpleCodeName);
                }
            }
        })
    }
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showComValue(obj,type) {
    var id =$("#"+obj).val();
    if(id!=undefined&&id!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querybeanbytype?type='+type+"&id="+id,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                //console.log(data);
                if(data.code == 0)
                {
                    $("#"+obj+"Val").val(data.result);
                    $("#"+obj+"Val").attr("data-value",data.result);
                    $("#"+obj+"Val").attr("title",data.result);
                    if(obj=="largeCategory"){
                        if(data.result=="中药饮片") {
                            $(".productrate").show();
                            //Is_ZYYP_flag = true
                            $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                            $('#maintenancePeriod').prev().find('i').remove(); // 养护周期
                            $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                            $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                            $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                            $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                            $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                        }else{
                            $(".productrate").hide();
                        }
                    }
                }
            }
        })
    }
}
/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$('#X_Table').getRowData(parentId);
    if(data.enclosureList)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            list:JSON.parse(data.enclosureList)
        })
    }
}
