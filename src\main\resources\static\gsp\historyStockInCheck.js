$(function () {

    $('#X_Table').XGrid({
        url: '/proxy-gsp/gsp/stockInCheck/historyStockInCheckDetail',
        colNames: ['商品编码', '商品名称', '商品规格', '生产厂家','批号','实际养护数量', '养护记录单据编号', '日期', '机构名称', '部门名称','养护员','养护类别','单据状态','备注'],
        postData:{
            status:1
        },
        colModel: [
          {
                name: 'productCode',
                index: 'productCode'

            }, {
                name: 'productName',
                index: 'productName'

            }, {
                name: 'specifications',
                index: 'specifications'
            }, {
                name: 'manufacturerVal',
                index: 'manufacturerVal'
            },{
                name:'productBatch',
                index:'productBatch'
            },{
                name: 'amount',
                index: 'amount'
            }, {
                name: 'checkPlanCode',
                index: 'checkPlanCode',
                width:200
            }, {
                name: 'completionDate',
                index: 'completionDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'orgName',
                index: 'orgName'
            }, {
                name: 'deptName',
                index: 'deptName'
            }, {
                name: 'userName',
                index: 'userName'
            }, {
                name: 'checkType',
                index: 'checkType',
                formatter: function (e) {
                    if (e == '1') {
                        return '重点养护'
                    } else if (e == '2') {
                        return '普通养护'
                    } else if (e == '3') {
                        return '临时养护'
                    }
                }
            },{
                name: 'documentStatus',
                index: 'documentStatus'
            },{
                name: 'remark',
                index: 'remark'
            }
        ],
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号，多选
        ondblClickRow: function (e, c, a, b) {
        },

        pager: '#grid-pager',
    });

})

            //查询
            function btn_search() {
                $('#X_Table').setGridParam({
                    url: '/proxy-gsp/gsp/stockInCheck/historyStockInCheckDetail',
                    postData: {
                        startTime:$("#beginTime").val(),
                        endTime:$("#endTime").val(),
                        productCode:$("#productCode").val(),
                        checkType:$("#checkType").val(),
                        productName:$("#productName").val(),
                        status:1
                    }
                }).trigger('reloadGrid');
            }

            function excel(){
            	utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()), 1).then( () => {
                    return false;
                }).catch( () => {
                	window.location = '/proxy-gsp/gsp/stockInCheck/excelHistoryStockInCheck?startTime='+$("#beginTime").val()+'&endTime='+$("#endTime").val()
                		+'&productCode='+$("#productCode").val()+'&checkType='+$("#checkType").val()+'&productName='+$("#productName").val()+"&status=1";
                });    
                

            }