var orderType = "${orderType}";
if (orderType == "1") {
    $("#nav-tab .active a", window.parent.document).html("新建大客户采购订单");
} else {
    $("#nav-tab .active a", window.parent.document).html("建议采购订单详情");
}
var startDate = "";
var _channeld = '';

function change_channelID(that) {
    _channeld = $(that).val();
    productName_Autocomplete('X_Tablea', {});
    //清空商品列表
    $('#X_Tablea').setGridParam({
        data: []
    }).trigger('reloadGrid');
}

$(function () {
    //传过来的业务类型值不为空时，为页面业务类型赋值
    let supplierId = $('#supplierId').val();
    var orgCode = $("[name='orgCode']").val();
    $('#X_Tablea').XGrid({
        url: '/proxy-purchase/purchase/adviceOrderProduct/getProductByOrderNo?orderNo=' + $('#orderNo').val(),
        //data:[],
        //colNames: ['商品编号', '商品名称', '通用名', '规格', '小包装单位2', '生产厂家', '产地','商品剂型','建议采购数量','订单数量', '含税单价','采购价涨幅', '含税金额', ' APP售价', '税率', '中包装规格', '大包装规格', '可调天数',  '品牌厂家','商品批准文号', '批准文号有效日期', '是否过期', '最后一次购进价','库存数量','可销天数','7天日均销售','15天日均销售','30天日均销售', '在途数量','库存上限','安全库存','是否协议品种','是否协议内购进','备注','最后供应商编码','最后供应商名称','供应商款期', '2个月内连续涨价次数','2个月最后入库单价', '30天销量','是否为赠品','建议计划数量','60天销量', '90天销量',  '不含税单价', '90天最低进价', '90天最高进价','<i>可销天数</i>',   '索引',  '供应商货期','商品ID','日均销售数量','标准单价','经营范围','商品大类','特殊属性','是否过期时间',],
        colNames: ['索引', '备货状态', '上次转采购单时间', '商品编号', '商品名称', '建议采购数量', '规格', '生产厂家', '采购数量', '采购价', '预计可销天数', '30天销量', '可用库存', '在途数量', '可销天数', '日均销量'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                //key: true,
                hidden: true,//是否隐藏
                hidegrid: true
            },
            {
                name: 'choiceStatus',
                index: 'choiceStatus',//备货状态
                width: '100',
                formatter: function (val) {
                    if (val == 5) {
                        return "超高库存";
                    } else if (val == 4) {
                        return "高库存";
                    } else if (val == 3) {
                        return "正常";
                    } else if (val == 2) {
                        return "需补货";
                    } else if (val == 1) {
                        return "即将断货";
                    } else if (val == 0) {
                        return "断货";
                    } else if (val == 6) {
                        return "近期无销售";
                    } else {
                        return "";
                    }
                },
                unformat: function (value) {
                    if (value) {
                        if (value == '超高库存') {
                            return '5';
                        } else if (value == '高库存') {
                            return '4';
                        } else if (value == '正常') {
                            return '3';
                        } else if (value == '需补货') {
                            return '2';
                        } else if (value == '即将断货') {
                            return '1';
                        } else if (value == '断货') {
                            return '0';
                        } else if (value == '近期无销售') {
                            return '6';
                        } else {
                            return "";
                        }
                    } else {
                        return '';
                    }
                }
            },
            {
                name: 'recentPurchaseTime',
                index: 'recentPurchaseTime',//上次转采购单时间
                width: '200'
            },
            {
                name: 'productCode',
                index: 'productCode',//商品编号
                width: '100'
            }, {
                name: 'productName',
                index: 'productName',//商品名称
                rowtype: "#productName_input",
                width: '200',
            }, {
                name: 'recommendedPurchNumber',//建议采购数量
                index: 'recommendedPurchNumber',
            },
            {
                name: 'productSpecification',//规格
                index: 'productSpecification',
                width: '100'
            },
            {
                name: 'productProduceFactory', //生产厂家
                index: 'productProduceFactory',
            },
            {
                name: 'purchNumber',//采购数量
                index: 'purchNumber',
                width: '120',
                rowEvent: rowEvent,
                rowtype: "#productPurchNumber_input",
                formatter: function (data) {
                    if (parseInt(data) == "0") {
                        return data;
                    } else if (data != "" && data != null) {
                        return (Math.ceil(data));
                    }
                    return data;
                }
            },
            {
                name: 'purchPrice', //采购价
                index: 'purchPrice',
                rowtype: "#productPurchPrice_input",
            },
            {
                name: 'predictCanSellDay',//预计可销天数
                index: 'predictCanSellDay',
                hidden: false,
            }
            , {
                name: 'productThirtySale',
                index: 'productThirtySale',//30天销量
            }, {
                name: 'amountQualifiedUse',//可用库存
                index: 'amountQualifiedUse',
                //rowEvent: rowEvent,
            }, {
                name: 'amountIng',//在途数量
                index: 'amountIng'
            },
            {
                name: 'canSellDay',//可销天数
                index: 'canSellDay',
            }, {
                name: 'averageDailySales',//日均销量
                index: 'averageDailySales',
            },
            {
                name: 'isNewAdd',
                index: 'isNewAdd',//是否是新增商品(1：是 0：否)
                hidden: true,
                hidegrid: true
            }
            , {
                name: 'sort',//表格本地键值
                index: 'sort',
                hidden: true,
                hidegrid: true,
                formatter: function (a, b, c, d) {
                    return c.id;
                }
            }
        ],
        //maxheight: false,
        key: 'sort',
        rowNum: 10000,
        //rownumbers: true,//是否展示序号
        selectandorder: true,
        //multiselect: true,
        altRows: true,//设置为交替行表格,默认为false
        //attachRow:true,
        gridComplete: function () {
            var list = $('#X_Tablea').XGrid('getRowData');
            if (list.length != 0) {
                $.each(list, function (index, item) {
                    productName_Autocomplete('X_Tablea', item)
                });
            }
            /*setTimeout(function () {
                tableAddRow('X_Tablea', {})
            }, 300);*/
            rowEvent();
            parent.hideLoading();
        }
    });

    $('div[fold=head]').fold({sub: 'sub'});

    //新增行，不填写数据
    $("#addRow").click(function () {
        commodity_search_di();
    });

    //删除商品
    $("#deleRow").click(function () {

        //获取选中的id进行批量处理
        let orderNo = $('#orderNo').val();
        var selectIds = "";
        var sels = $('#X_Tablea').XGrid('getSeleRow');
        console.log("选中行：" + sels);
        let addRowArr = [], originRowArr = [];
        if (sels.length) {
            $(sels).each((index, item) => {
                if (item.isNewAdd == '1') {
                    addRowArr.push(item)
                } else {
                    originRowArr.push(item)
                }
            })
            var elx = "是否继续执行删除操作？";
            utils.dialog({
                title: '提示',
                content: elx,
                width: "200px",
                okValue: '确定',
                ok: function () {
                    $(addRowArr).each((index, item) => {
                        $('#X_Tablea').XGrid('delRowData', item.id);
                    });
                    if (originRowArr.length) {
                        utils.ajax(
                            {
                                "orderNo": orderNo,
                                "productIds": originRowArr.map(item => item.id).join(',')
                            },
                            "/proxy-purchase/purchase/adviceOrderProduct/deleteProductByIds", "POST", successCallback);

                        function successCallback(result) {
                            if (result > 0) {
                                $('#X_Tablea').setGridParam({
                                    data: []
                                }).trigger('reloadGrid');
                            } else {
                                utils.dialog({
                                    content: '数据异常，无法删除，请联系神农技术人员！',
                                    quickClose: true,
                                    timeout: 2000
                                }).showModal();
                            }
                        }
                    }
                    utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
                },
                cancelValue: '取消',
                cancel: function () {
                }
            }).showModal();
        } else {
            utils.dialog({
                title: '提示',
                content: '是否继续执行删除操作？',
                okValue: '确定',
                ok: function () {
                    deletex();
                },
                cancelValue: '取消',
                cancel: function () {
                }
            }).showModal();
            return false;

        }
    });
    // 编辑
    $('#edit').click(function () {
        $(this).css('display', 'none');
        $("#save").css('display', 'inline-block');
        $('#addRow, #deleRow').prop('disabled', false);
        $('#generatePurchaseOrder').prop('disabled', true);
        $('#X_Tablea [name=productPurchNumber]').prop('readonly', false);
        $('#X_Tablea [name=productPurchPrice]').prop('readonly', false);
        setTimeout(function () {
            tableAddRow('X_Tablea', {});
            $('#X_Tablea [name=productPurchNumber]').prop('readonly', false);
            $('#X_Tablea [name=productPurchPrice]').prop('readonly', false);
        }, 300);
    })
    //保存
    $("#save").click(function () {
        var orderNo = $('#orderNo').val();
        var ttl = $('#X_Tablea').XGrid('getRowData').filter(function (item) {
            if (!item.productCode) {
                $('#X_Tablea').XGrid('delRowData', item.sort)
            } else {
                return item
            }
        });//商品数据
        var flag = false;
        if (ttl.length >= 1) {
            $.each(ttl, function (i, item) {
                var recommendedPurchNumber = item.recommendedPurchNumber;
                if (!isPositiveInteger(recommendedPurchNumber)) {
                    utils.dialog({content: '采购数量必须为正整数', quickClose: true, timeout: 1000}).showModal();
                    flag = true;
                    return false;
                }
            });
            if (flag) {
                return false;
            }
        }

        if (!validform("validates").form()) {
            return false;
        } else if (ttl.length >= 1) {
            $(this).css('display', 'none');
            $("#edit").css('display', 'inline-block');
            $('#addRow, #deleRow').prop('disabled', true);
            $('#generatePurchaseOrder').prop('disabled', false);
            $("#X_Tablea tr[id]").removeClass("redback");
            $("#X_Tablea tr[id]").removeClass("maize");
            parent.showLoading();

            $.ajax({
                method: "POST",
                url: "/proxy-purchase/purchase/adviceOrderProduct/save",
                data: {
                    "adviceOrderProductVoData": JSON.stringify(ttl),
                    "orderNo": orderNo
                },
                dataType: 'json',
                cache: false,
            }).done(function (data) {
                parent.hideLoading();
                if (data.code == 0) {
                    utils.dialog({content: data.msg, quickClose: true, timeout: 3000}).showModal();
                    $('#X_Tablea').setGridParam({
                        data: []
                    }).trigger('reloadGrid');
                } else if (data.code == 3) {
                    if (data.list != null) {
                        for (var i = 0; i < data.list.length; i++) {
                            $("#X_Tablea  #" + data.list[i].id).removeClass("maize");
                            $("#X_Tablea  #" + data.list[i].id).addClass("redback");
                        }
                    }
                    utils.dialog({content: data.msg, quickClose: true, timeout: 4000}).showModal();
                } else if (data.code == 2) {
                    var tDate = $('#X_Tablea').XGrid('getRowData');
                    if (tDate.length) {
                        for (var i = 0; i < tDate.length; i++) {
                            $("#X_Tablea  #" + tDate[i].id).removeClass("maize");
                            $("#X_Tablea  #" + tDate[i].id).addClass("redback");
                        }
                    }
                    utils.dialog({content: data.msg, quickClose: true, timeout: 4000}).showModal();
                } else {
                    utils.dialog({content: data.msg, quickClose: true, timeout: 4000}).showModal();

                }
                $('#X_Tablea [name=productPurchNumber]').prop('readonly', true);
                $('#X_Tablea [name=productPurchPrice]').prop('readonly', true);
            });
        } else {
            utils.dialog({content: '最少选择一条商品！', quickClose: true, timeout: 4000}).showModal();
            tableAddRow('X_Tablea', {});
        }
    });
    //设置显示列
    $("#set_tables_rowa").click(function () {
        $('#X_Tablea').XGrid('filterTableHead');
    });

    //建议采购订单详情转采购订单
    $('#generatePurchaseOrder').on('click', function () {

        let rowDt = $('#X_Tablea').XGrid('getSeleRow');
        let orgCode = $('#orgCode').val();
        let orderNo = $('#orderNo').val();
        let supplierCode = $('#supplierCode').val();
        let channelId = $('#channelId').val();
        let supplierName = $('#supplierName').val();
        let supplierId = $('#supplierId').val();
        let simpleCodes = $('#simpleCodes').val();
        var selectData = $('#X_Tablea').XGrid('getRowData').filter(function (item) {
            if (!item.productCode) {
                $('#X_Tablea').XGrid('delRowData', item.sort)
            }
            return item.productCode
        });//商品数据
        //判断是否有选中
        if (selectData.length > 0) {
            let formData = new FormData();
            formData.append("supplierCode", supplierCode);
            formData.append("supplierName", supplierName);
            formData.append("simpleCodes", simpleCodes);
            formData.append("channelId", channelId);
            formData.append("orgCode", orgCode);
            formData.append("productData", JSON.stringify(selectData));
            //对商品进行验证
            $.ajax({
                url: '/proxy-purchase/purchase/adviceOrderProduct/generatePurchaseOrderByAdviceOrderProduct',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function () {
                    console.log("正在进行，请稍候");
                },
                success: function (data) {


                    if (data.code == 0) {
                        console.log(data.result);
                        let obj = data.result;
                        let products = JSON.stringify(data.result);
                        //products = products.substring(1,products.length-1);
                        products = encodeURIComponent(encodeURIComponent(products));
                        url = "/proxy-purchase/purchase/purchaseOrder/toAdd?encodeProductList=" + products + "&supplierId=" + supplierId + "&supplierCode=" + supplierCode + "&supplierName=" + supplierName + "&channelId=" + channelId + "&orgCode=" + orgCode + "&adviceOrderNo=" + orderNo + "&fromWhere=adviceProduct" + "&orgCode=" + orgCode;

                        parent.$('#mainFrameTabs').bTabsAdd("toLis", "新建采购订单", url);
                    } else {
                        utils.dialog({
                            title: '提示',
                            content: '<div class="totalWrap" style="height:100%;">' +
                                '<div class="totalContent" style="overflow-y:scroll;height: inherit;">' + data.msg + '</div>' +
                                '<textarea id="copyInput" class="btn btn-info" style="float: right;margin-top: -966px;"  >' + data.msg + '</textarea> ' +
                                '</div>',
                            width: 360,
                            height: 150,
                            okValue: '确认',
                            ok: function () {

                            },
                            statusbar: '<button type="button" class="btn btn-info" id="copyBtn" style="float: right;margin-top: -6px;">复制信息</button>'
                        }).showModal();
                        $("#copyBtn").on("click", function () {
                            $("#copyInput").select(); // 选择对象
                            let flag = document.execCommand("Copy", "false", null); // 执行浏览器复制命令
                            if (flag) utils.dialog({content: '复制成功！', quickClose: true, timeout: 2000}).show();
                        })
                    }
                },
                error: function () {
                    utils.dialog({content: '转采购订单失败！', quickClose: true, timeout: 4000}).showModal();
                    return false;
                }
            });
        } else {
            utils.dialog({title: '提示', content: '请先选中商品行', quickClose: true, timeout: 4000}).showModal();
            return false;
        }
    });
});


//修改当前行
function setEmptyRow(sort, callback, rowData) {
    var basicForm = {
        supplier: {
            val: $("#supplierCode").val(),
            msg: '供应商不能为空！'
        },
        arrivalPeriod: {
            val: $("#arrivalPeriod").val(),
            msg: '供应商货期不能为空！'
        },
        simpleCode: {
            val: $("#simpleCodes").val(),
            msg: '供应商的经营范围不能为空！'
        },
    }
    for (var key in basicForm) {
        if (!basicForm[key].val) {
            utils.dialog({content: basicForm[key].msg, quickClose: true, timeout: 2000}).showModal();
            return false
        }
    }
    var oldData = $('#X_Tablea').XGrid('getRowData');
    var disableRows = $.map(oldData, function (item, key) {
        return item.id
    });
    if (rowData) {
        setRow(rowData, sort)
    } else {
        utils.dialog({
            //url: '/proxy-purchase/purchase/purchaseOrder/toPurchaseProduct?supplyArrivalPeriod=' + $("#arrivalPeriod").val() + '&simpleCode=' + $("#simpleCode").val()+'&supplierId='+$("#supplierId").val()+'&supplierCode='+$("#find_supplierc").val()+'&supplierName='+encodeURI($("#find_supplier").val())+'&orderType='+$("#orderType").val()+'&channelId='+$("#channelId").val(),
            url: '/proxy-purchase/purchase/purchaseOrder/toPurchaseProduct?supplyArrivalPeriod=' + $("#arrivalPeriod").val() + '&simpleCode=' + $("#simpleCodes").val() + '&supplierId=' + $("#supplierId").val() + '&supplierCode=' + $("#supplierCode").val() + '&supplierName=' + encodeURI($("#supplierName").val()) + '&channelId=' + $("#channelId").val(),
            title: '商品列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            data: {
                val: $('#X_Tablea').find('tr#' + sort + ' td[row-describedby=productName] input').attr('oldvalue'),
                colData: disableRows
            }, // 给modal 要传递的 的数据
            onclose: function () {
                var data = [].concat(this.returnValue);
                if (this.returnValue) {
                    setRow(data, sort)
                    rowEvent();
                } else {
                    callback && callback();
                }
            }
        }).showModal();
    }

    function setRow(data, sort) {
        var remainDaysAry = [], //即将过期商品
            repeatAry = [], //重复商品
            fristRow = true; //第一个更改，之后的追加
        data = [].concat(data)
        $.each(data, function (index, val) {
            var lengthFlag = repeatAry.length;
            $.each(oldData, function (key, item) {
                if (item.productCode && val.productCode === item.productCode) {
                    repeatAry.push(val);
                }
            });
            if (lengthFlag === repeatAry.length) {
                val.purchNumber = val.productSuggestPlanCount;
                val.purchPrice = val.purchPrice;
                val.recentPurchaseTime = val.recentPurchaseTime;
                val.productProduceFactory = val.productBrandManufacturers;
                val.recommendedPurchNumber = parseInt(val.productSuggestPlanCount);
                val.amountIng = parseInt(val.productPackOnWayCount);
                val.canSellDay = val.canPinNumber;
                val.averageDailySales = val.productSixtySale;
                val.amountQualifiedUse = parseInt(val.amountQualifiedUse);
                val.isNewAdd = "1";
                if (parseFloat(val.productLastPrice) > 0) {
                    val.productContainTaxPrice = val.productLastPrice;
                }
                if (val.remainDays && val.remainDays <= 30) {
                    remainDaysAry.push(val);
                }

                if (fristRow) {
                    fristRow = false;
                    tableAddRow('X_Tablea', val, sort)
                } else {
                    tableAddRow('X_Tablea', val)
                }
            }
        });

        //rowEvent();
        //提示信息
        var contentStr = "";
        //判断商品即将过期的标红：去掉
        /*if(remainDaysAry.length){
            for(var i = 0;i < remainDaysAry.length;i++){
                $("#X_Tablea  #"+remainDaysAry[i].sort).removeClass("redback");
                $("#X_Tablea  #"+remainDaysAry[i].sort).addClass("maize");
            }
            contentStr +="商品{" + remainDaysList.map(function (item) {
                return item.productCode
            }).join(',') + "}，即将过期！<br/>";
        }*/
        if (repeatAry.length > 0) {
            contentStr += "商品{" + repeatAry.map(function (item) {
                return item.productCode
            }).join(',') + "}，存在重复！";
        }

        if (contentStr) {
            utils.dialog({
                content: contentStr,
                quickClose: true,
                timeout: 4000,
                onclose: function () {
                    callback && callback();
                }
            }).showModal();
        } else {
            callback && callback();
        }
    }
};

//本地新增行
function tableAddRow(tableId, rowData, rowId) {
    rowData.productId = rowData.id;
    rowData.recommendedPurchNumber = rowData.productSuggestPlanCount;
    if (rowId) {
        $('#' + tableId).XGrid('setRowData', rowId, rowData);
        $('#X_Tablea [name=productPurchNumber]').prop('readonly', false);
        $('#X_Tablea [name=productPurchPrice]').prop('readonly', false);
        setTimeout(function () {
            tableAddRow('X_Tablea', {});
            $('#X_Tablea [name=productPurchNumber]').prop('readonly', false);
            $('#X_Tablea [name=productPurchPrice]').prop('readonly', false);
        }, 300);
    } else {
        var oldData = $('#' + tableId).XGrid('getRowData');
        var sortAry = $.map(oldData, function (item, index) {
            return item.sort
        });
        rowData.sort = oldData.length > 0 ? (Math.max.apply(null, sortAry) + 1).toString() : '1';
        if (oldData.length > 0) {
            rowData.id = Math.max.apply(null, sortAry) + 1
        }
        var lastData = oldData[oldData.length - 1];
        if (!lastData || lastData.productCode) {
            $('#' + tableId).XGrid('addRowData', rowData);
        } else {
            rowData.sort = (Math.min.apply(null, sortAry) - 1).toString();
            $('#' + tableId).XGrid('addRowData', rowData, 'before', lastData.rowid);
        }
    }
    productName_Autocomplete(tableId, rowData);
}

//计划量
function rowSuppCode(arrivalPeriod) {
    var ttl = $('#X_Tablea').XGrid('getRowData');
    var huoqi = parseInt(arrivalPeriod);
    $.each(ttl, function (i, item) {
        if (item.productCode) {
            // var stockCount = parseFloat(ttl[i].productStockCur);//库存数量
            // var productPackOnWayCount = parseFloat(ttl[i].productPackOnWayCount);//在途数量
            // var productAdjustDay = parseFloat(ttl[i].productAdjustDay);//可调节天数
            // var thirtyAverageSaleVolume = parseFloat(ttl[i].thirtyAverageSaleVolume);//日均销售数量
            // console.log("huoqi:"+huoqi+",stockCount:"+stockCount+",productPackOnWayCount"+productPackOnWayCount+",productAdjustDay:"+productAdjustDay+",thirtyAverageSaleVolume:"+thirtyAverageSaleVolume)
            // var jihualiang =( (productAdjustDay+huoqi) * thirtyAverageSaleVolume) -stockCount -productPackOnWayCount;
            var jihualiang = parseFloat(ttl[i].productSuggestPlanCount);
            console.log("jihualiang:" + jihualiang);
            if (jihualiang <= 0) {
                $("#X_Tablea #" + ttl[i].id).find("td[row-describedby='supplyArrivalPeriod']").html(huoqi);
                $("#X_Tablea #" + ttl[i].id).find("td[row-describedby='productSuggestPlanCount']").html(0);
            } else {
                $("#X_Tablea #" + ttl[i].id).find("td[row-describedby='supplyArrivalPeriod']").html(huoqi);
                if (parseInt(jihualiang) == "0") {
                    $("#X_Tablea #" + ttl[i].id).find("td[row-describedby='productSuggestPlanCount']").html(0);
                } else if (parseInt(jihualiang) > 0) {
                    var jihualiang1 = Math.ceil(jihualiang);
                    $("#X_Tablea #" + ttl[i].id).find("td[row-describedby='productSuggestPlanCount']").html(jihualiang1);
                }
            }
        }
    })
}

function isNotNull(value) {
    if (value != "" && value != null && value != undefined && !isNaN(value)) {
        return true;
    }
    return false;
}

function delRepeat(dataList) {
    var temp = {}, len = dataList.length;
    for (var i = 0; i < len; i++) {
        var tmp = dataList[i].productCode;
        if (!temp.hasOwnProperty(tmp)) {//hasOwnProperty用来判断一个对象是否有你给出名称的属性或对象
            temp[tmp] = dataList[i];
        }
    }
    len = 0;
    var tempArr = [];
    for (var i in temp) {
        tempArr[len++] = temp[i];
    }
    return tempArr;
}

//根据建议采购订单号查询商品
function commodity_search_di(xid, callback) {
    $("#bodyscrolf").css("overflow", "hidden");
    var disableRows = [];
    var ttl = $('#X_Tablea').XGrid('getRowData');
    console.log(ttl);
    if (ttl) {
        if (ttl.length) {
            for (i = 0; i < ttl.length; i++) {
                disableRows.push(ttl[i].id);
            }
        } else {
            disableRows.push(ttl.id);
        }
    }
    ;
    console.log(disableRows);
    var redid = [];
    let simpleCodes = $("#simpleCodes").val();
    let supplierId = $("#supplierId").val();
    let supplierCode = $("#supplierCode").val();
    let supplierName = $("#supplierName").val();
    let channelId = $("#channelId").val();
    let arrivalPeriod = $("#arrivalPeriod").val();
    var pays = [];
    utils.dialog({
        //url: '/proxy-purchase/purchase/purchaseOrder/toPurchaseProduct?supplyArrivalPeriod=' + $("#arrivalPeriod").val() + '&simpleCode=' + $("#simpleCode").val()+'&supplierId='+$("#supplierId").val()+"&supplierName="+encodeURI(supplier)+'&supplierCode='+$("#find_supplierc").val()+'&supplierName='+$("#find_supplier").val()+'&orderType='+$("#orderType").val()+'&channelId='+$("#channelId").val(),
        url: '/proxy-purchase/purchase/purchaseOrder/toPurchaseProduct?supplyArrivalPeriod=' + arrivalPeriod + '&simpleCode=' + simpleCodes + '&supplierId=' + supplierId + '&supplierCode=' + supplierCode + '&supplierName=' + supplierName + '&channelId=' + channelId,
        title: '商品列表',
        width: $(window).width() * 0.9,
        height: $(window).height() * 0.7,
        data: {
            val: '',
            colData: disableRows,
        }, // 给modal 要传递的 的数据
        onclose: function () {
            $("#bodyscrolf").css("overflow", "auto");
            if (this.returnValue) {
                var tstb = [];
                var cltb = [];
                var data = this.returnValue;
                var rdt = $("#X_Tablea").XGrid("getRowData");
                //将商品列表的入库单+商品编号放到集合中
                tstb = $.map(rdt, function (v, i) {
                    return v.productCode
                });
                data = delRepeat(data);
                $(data).each((index, item) => {
                    $(item)[0]['purchNumber'] = $(item)[0]['productSuggestPlanCount'];
                    $(item)[0]['purchPrice'] = Number($(item)[0]['productLastPrice']).toFixed(2);
                    $(item)[0]['recommendedPurchNumber'] = $(item)[0]['productSuggestPlanCount'];
                    if ($(item)[0]['productPackOnWayCount'] != "" && $(item)[0]['productPackOnWayCount'] != null) {
                        $(item)[0]['amountIng'] = Math.ceil($(item)[0]['productPackOnWayCount']);
                    }
                    let amountQualifiedUse = $(item)[0].amountQualifiedUse == '' ? '0' : $(item)[0].amountQualifiedUse;
                    $(item)[0]['canSellDay'] = $(item)[0]['canPinNumber'];
                    $(item)[0]['averageDailySales'] = $(item)[0]['productSixtySale'];
                    $(item)[0]['amountQualifiedUse'] = parseInt(amountQualifiedUse);//取计划信息表可用库存量
                    $(item)[0]['isNewAdd'] = "1";
                    if ($(item)[0].averageDailySales != '0') {
                        let purchNumber = $(item)[0].purchNumber == '' ? '0' : $(item)[0].purchNumber;
                        let averageDailySales = $(item)[0].averageDailySales == '' ? '0' : $(item)[0].averageDailySales;
                        let amountIng = $(item)[0]['amountIng'] == '' ? '0' : $(item)[0]['amountIng'];
                        let ret = (parseFloat(amountQualifiedUse) + parseFloat(amountIng) + parseFloat(purchNumber)) / parseFloat(averageDailySales);
                        $(item)[0]['predictCanSellDay'] = Number(ret).toFixed(2);
                    }
                });
                console.log(data);
                var remainDaysList = [];
                data = $(data).map(function (i, v) {
                    if (parseFloat(v.productLastPrice) > 0) {
                        v.productContainTaxPrice = v.productLastPrice;
                    }
                    cltb.push(v.productCode);
                    return v;
                });
                for (let i = 0; i < cltb.length; i++) {
                    if (tstb.indexOf(cltb[i]) == -1) {
                        if (data[i].remainDays <= 30 && data[i].remainDays != "") {
                            remainDaysList.push(data[i].productCode);
                            redid.push(data[i].id);
                        }
                        tableAddRow('X_Tablea', data[i]);
                    } else {
                        pays.push(data[i].productCode)
                    }
                }
                //判断商品即将过期的标红：去掉
                if (redid.length) {
                    for (var i = 0; i < redid.length; i++) {
                        $("#X_Tablea  #" + redid[i]).removeClass("redback");
                        $("#X_Tablea  #" + redid[i]).addClass("maize");
                    }
                }
                var contentStr = "";
                if (remainDaysList.length > 0) {
                    contentStr += "商品{" + remainDaysList.join(',') + "}，即将过期！<br/>";
                }
                if (pays.length > 0) {
                    contentStr += "商品{" + pays.join(',') + "}，存在重复！";
                }
                if (contentStr != "") {
                    utils.dialog({
                        content: contentStr,
                        quickClose: true,
                        timeout: 4000
                    }).showModal();
                }
            }
            $('#X_Tablea [name=productPurchNumber]').prop('readonly', false);
            $('#X_Tablea [name=productPurchPrice]').prop('readonly', false);
        }
    }).showModal();
    return false;
}

//金额计算
function rowEvent(xtype) {
    if (xtype) {
        var tr = $('#X_Tablea').XGrid('getRowData', xtype.rowData.sort);
        var xid = xtype.rowData.id;
    }
    var ttl = $('#X_Tablea').XGrid('getRowData');
    var sumMoney = 0;
    var sumTaxMoney = 0;
    var priceTaxSum = 0;
    $.each(ttl, function (i) {
        if (ttl[i].amountQualifiedUse == "") {
            ttl[i].amountQualifiedUse = "0";
        }
        if (ttl[i].amountIng == "") {
            ttl[i].amountIng = "0";
        }
        if (ttl[i].purchNumber == "") {
            ttl[i].purchNumber = "0";
        }
        if (ttl[i].averageDailySales == "") {
            ttl[i].averageDailySales = "0";
        }
        var amountQualifiedUseTemp = ttl[i].amountQualifiedUse;
        var aa = ttl[i].amountIng;
        var purchNumber = ttl[i].purchNumber;
        var dailySales = ttl[i].averageDailySales;

        if (dailySales != '0') {
            //预计可销天数 = 可用库存+在途数量+采购数量）/日均销量
            var predictCanSellDay = (parseFloat(ttl[i].amountQualifiedUse) + parseFloat(ttl[i].amountIng) + parseFloat(ttl[i].purchNumber)) / parseFloat(ttl[i].averageDailySales);
            //productPackCountSmall订单数量
            if (isNotNull(predictCanSellDay)) {
                //预计可销天数
                $("#X_Tablea  #" + (ttl[i].sort ? ttl[i].sort : ttl[i].id)).find("td[row-describedby='predictCanSellDay']").html(predictCanSellDay.toFixed(2));
            }
        }
    });
    if (isNotNull(sumMoney)) {
        $("#orderSumMoney").html(sumMoney.toFixed(2));
    } else {
        $("#orderSumMoney").html(0);
    }

    if (isNotNull(sumTaxMoney)) {
        $("#orderSumTaxMoney").html(sumTaxMoney.toFixed(2));
    } else {
        $("#orderSumTaxMoney").html(0);
    }

    if (isNotNull(priceTaxSum)) {
        priceTaxSum = priceTaxSum.toFixed(2);
        $("#orderPriceTaxSum").html(priceTaxSum);
        $("#orderPriiceTaxSumChineseNum").html(chineseNumber(priceTaxSum));
    } else {
        $("#orderPriceTaxSum").html(0);
        $("#orderPriiceTaxSumChineseNum").html("");
    }


}

function deletex() {
    var selRow = $('#X_Tablea').XGrid('getSeleRow');
    if (selRow.length) {
        $.each(selRow, function (index, item) {
            $('#X_Tablea').XGrid('delRowData', item.sort);
        });
        if (!$('#X_Tablea').XGrid('getRowData').length) {
            tableAddRow('X_Tablea', {})
        }
        rowEvent(null);
    } else {
        utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 3000}).showModal();

    }
}


//校验商品要选择
function validates() {

    if (validform("validates").form()) {


    } else { //验证不通过

        return false;
    }
}

function getSupplilyInfo(id) {

    $.ajax({
        type: "POST",
        url: "/proxy-purchase/purchase/supplier/getRefundSupplier?id=" + id,
        async: false,
        error: function () {
        },
        success: function (data) {

            if (data.listSupplyStoreAddress != "" && data.listSupplyStoreAddress != null) {
                obj.listSupplyStoreAddress = JSON.stringify(data.listSupplyStoreAddress);
            } else {
                obj.listSupplyStoreAddress = "";
            }
        }
    });
    return obj;
}

//表格内商品模糊搜索
function productName_Autocomplete(tableId, rowData, rowId) {
    if (!rowData) {
        rowData = $('#' + tableId).XGrid('getRowData');
    }
    rowId = (rowData.sort ? rowId : $('#' + tableId).XGrid('getRowData')[0]['rowid']);
    var $input = $('#' + tableId + ' tr#' + (rowData.sort ? rowData.sort : rowId) + ' input[name=productName]');
    $input.Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/supplier/findPruchaseProductByMnemonInfo', //异步请求
        paramName: 'keyword',//查询参数，默认 query
        params: {
            simpleCode: $("#simpleCodes").val(),
            channelId: $("#channelId").val()//_channeld
        },
        dataType: 'json',
        zIndex: 9,
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        autoSelectFirst: true,
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        onSelect: function (result) {
            var code = result.data, val = result.value;
            var rowId = $input.parents('tr').attr('id');
            var emptyData = {
                productCode: '',
                productName: '',
                commonName: '',
                productSpecification: '',
                productPackUnitSmall: '',
                productProduceFactory: '',
                productOriginPlace: '',
                dosageForm: '',
                productPackCountSmall: '',
                productContainTaxPrice: '',
                productContainTaxMoney: '',
                productAppContainTaxPrice: '',
                productEntryTax: '',
                productPackUnitMedium: '',
                productPackUnitBig: '',
                productAdjustDay: '',
                productBrandManufacturers: '',
                productApprovalNumber: '',
                productApprovalNumberExpireDate: '',
                ifOldApprovalNumber: '',
                productLastPrice: '',
                productStockCur: '',
                canPinNumber: '',
                productSevenAverageSale: '',
                productFifteenAverageSale: '',
                productThirtyAverageSale: '',
                productPackOnWayCount: '',
                productUpperLimitStock: '',
                productStockSafe: '',
                varietiesAgreement: '',
                productRemark: '',
                lastSupplierCode: '',
                lastSupplierName: '',
                supplierPaymentPeriod: '',
                productSuggestPlanCount: '',
                purchaseCount: '',
                lastProductContainTaxPrice: '',
                productThirtySale: '',
                productSixtySale: '',
                productUnitPrice: '',
                productMinPriceNinetyDay: '',
                productMaxPriceNinetyDay: '',
                productStockTurnDay: '',
                id: '',
                supplyArrivalPeriod: '',
                productId: '',
                thirtyAverageSaleVolume: '',
                standardPrice: '',
                scopeOfOperation: '',
                largeCategory: '',
                specialAttributes: '',
                remainDays: '',


            }
            $('tr#' + rowId + ' td[row-describedby=productName] input').attr('oldvalue', val);
            var channelId = $("#channelId").val();
            setEmptyRow(rowId, '', emptyData);
            $.ajax({
                type: "POST",
                url: "/proxy-purchase/purchase/product/loadProductData?supplyArrivalPeriod",
                data: {
                    supplyArrivalPeriod: $("#arrivalPeriod").val(),
                    productAdjustDay: 30,
                    simpleCode: $("#simpleCodes").val(),
                    supplierId: $("#supplierId").val(),
                    keyword: code,
                    factoryOption: '',
                    factorySelect: '',
                    supplierCode: $("#supplierCode").val(),
                    supplierName: $("#supplierName").val(),
                    channelId: channelId,
                    pageNum: 1,
                    pageSize: 10
                },
                success: function (res) {
                    var rowData = res.result.list;
                    if (rowData != null && rowData.length) {
                        setEmptyRow(rowId, function () {
                            $('tr#' + rowId + ' td[row-describedby=productName] input').attr('oldvalue', val);
                            $('tr#' + rowId + ' td[row-describedby=purchNumber] input').focus();
                        }, rowData);
                        $("#save").css('display', 'inline-block');
                        $('#addRow, #deleRow').prop('disabled', false);
                        $('#generatePurchaseOrder').prop('disabled', true);
                        $('#X_Tablea [name=productPurchNumber]').prop('readonly', false);
                        $('#X_Tablea [name=productPurchPrice]').prop('readonly', false);
                    } else {
                        utils.dialog({
                            //title:'提示',
                            content: '此商品不符合新增条件(当前采购员商品;供应商经营范围内;不是淘汰,过期,限采,停用商品)！',
                            okValue: '确认',
                            ok: function () {
                            }
                        }).showModal();
                    }

                }
            });
        },
        onSearchStart: function (query) {
            if (!$('#supplierCode').val()) {
                utils.dialog({content: '数据异常，关联供应商未找到', quickClose: true, timeout: 2000}).showModal();
                $(this).Autocomplete('disable');
            } else {
                $(this).Autocomplete('enable');
            }
        },
        onNoneSelect: function (params, suggestions) {
            $input.val('')
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });
    $input.on({
        dblclick: function (e) {
            var rowId = $(this).parents('tr').attr('id');
            setEmptyRow(rowId);
        },
    }).siblings('.glyphicon-search').on("click", function () {
        var rowId = $(this).parents('tr').attr('id');
        setEmptyRow(rowId);
    });
}

function validateProductPurchNumberInfo() {
    validform('validates').form();
    var ttl = $('#X_Tablea').XGrid('getRowData').filter(function (item, index) {
        if (!item.productCode) {
            $('#X_Tablea').XGrid('delRowData', item.sort)
        } else {
            return item
        }
    });
    if (ttl.length >= 1) {
        $.each(ttl, function (i, item) {
            var purchNumber = item.purchNumber;
            isPositiveInteger(purchNumber);
            if (!isPositiveInteger(purchNumber)) {
                utils.dialog({
                    content: '采购数量必须为正整数',
                    quickClose: true,
                    timeout: 1000
                }).showModal();
                flag = true;
                return false;
            }
        });
    }
}

function isPositiveInteger(s) {//是否为正整数
    var re = /^[0-9]+$/;
    return re.test(s)
}
