var addReturnPageFlag = $("#addReturnPageFlag").val();//标识是首营申请入口还是点击选择商品的新增入口
var pageProperty = $("#pageProperty").val();//供应商申请页面
var useSupplier = $("#useSupplier").val();//引用主数据
if("insert" == pageProperty){//新增页面
	//是否是全量新增页面
	/*if(addReturnPageFlag){//是
		//批准文件
		upLoadFile("#pzwjUpload",'#table3','certificateId');
		//年度报告
		upLoadFile("#ndbgUpload",'#table4','reportDate');
		qtfjUploadFun();
	}else{
		//批准文件
		$("#pzwjUpload").attr("style","display:none;");
		//年度报告
		$("#ndbgUpload").attr("style","display:none;");
		$("#qtfjUpload").attr("style","display:none;");
	}*/
	//批准文件
	upLoadFile("#pzwjUpload",'#table3','certificateId');
	//年度报告
	upLoadFile("#ndbgUpload",'#table4','reportDate');
	qtfjUploadFun();
	//质量保证协议上传附件
	upLoadFile("#zlbzUpload",'#table1','signDate');
	//客户委托书上传附件
	upLoadFile("#khwtUpload",'#table2','proxyOderNo');
	
}else if("edit"==pageProperty){//详情页面
	/* var useSupplier = $("#useSupplier").val();//是否引用主数据
	 if("Y"==useSupplier){//引用主数据
		//批准文件
		$("#pzwjUpload").attr("style","display:none;");
		//年度报告
		$("#ndbgUpload").attr("style","display:none;");
		$("#qtfjUpload").attr("style","display:none;");
	 }else{
		//批准文件
		upLoadFile("#pzwjUpload",'#table3','certificateId');
		//年度报告
		upLoadFile("#ndbgUpload",'#table4','reportDate');
		qtfjUploadFun();
	 }*/
	//批准文件
	upLoadFile("#pzwjUpload",'#table3','certificateId');
	//年度报告
	upLoadFile("#ndbgUpload",'#table4','reportDate');
	qtfjUploadFun();
 	//质量保证协议上传附件
	upLoadFile("#zlbzUpload",'#table1','signDate');
	//客户委托书上传附件
	upLoadFile("#khwtUpload",'#table2','proxyOderNo');
}else{
	//质量保证协议上传附件
	$("#zlbzUpload").attr("style","display:none;");
	//客户委托书上传附件
	$("#khwtUpload").attr("style","display:none;");
	//批准文件
	$("#pzwjUpload").attr("style","display:none;");
	//年度报告
	$("#ndbgUpload").attr("style","display:none;");
	$("#qtfjUpload").attr("style","display:none;");
	
}

// 其他附件 如果存在附件则不让取消
$("#otherFileBox input[type='checkbox']").on("change",function(){
    var checked=this.checked;
    if(!checked)
    {
        var val=$(this).val();
        var v=$('#other'+val).val();
    	if(v && v!= '')
    	{
            var imgs=JSON.parse($('#other'+val).val());
            if(imgs.length > 0)
            {
            	$(this).prop('checked',true);
                return false;
            }
            $('#other'+val).remove();
		}
    }
});
//其他附件查看
$("#qtfjView").on("click",function(){
	//获取type类型
	var imgList=[];
	var checkInp=$("#otherFileBox input[type='checkbox']:checked");
	if(checkInp.length) {
        var id_name = ($('input[id^=newother]').length > 0) ? 'newother' : 'other';
        if (id_name == 'newother') {
            var inp = $("input[id^=newother]");
            for (let i = 0; i < inp.length; i++) {
                var val = $(inp[i]).val();
                var imgArr = JSON.parse(val);
                imgList = imgList.concat(imgArr);
            }
        } else {
            for (var i = 0; i < checkInp.length; i++) {
                var type = checkInp.eq(i).val();
                var inp = $("#" + id_name + type);
                if (inp.length) {
                    var imgArr = JSON.parse(inp.val());
                    imgList = imgList.concat(imgArr);
                }
            }
        }
    }
	/*else{
        utils.dialog({
            title:'提示',
            content:'请先选择附件类型。',
            okValue:'确定',
            ok:function () {}
        }).showModal()
        return false;
	}*/
	if(imgList && imgList.length>0){
		$.viewImg({
	        fileParam:{
	            name:'fileName',
	            url:'filePath'
	        },
	        list:imgList
	    })
	}else{
        utils.dialog({
            title:'提示',
            content:'没有附件内容。',
            okValue:'确定',
            ok:function () {}
        }).showModal()
        return false;
    }
	
});
/**
 * 上传附件
 * 
 * */

function upLoadFile(obj,table,typeName){
	$(obj).on("click", function () {
		//获取type类型
		var typeList=[];
		var valList=[];
		var eChoImgList=[];
		var $table=$(table);
		var rowData=$table.getRowData();
		var $tr=$table.find("tr").not(":first");
		for(var i=0;i<$tr.length;i++)
		{
			var dataVal='';
			var val='';
		    var el=$tr.eq(i).find("[name='"+typeName+"']").get(0);
		    dataVal=$.trim($(el).val());
		    if(dataVal == '' || !dataVal)
		    {
		    	utils.dialog({content: '请选择附件类型,表格除序号外第一列', quickClose: true, timeout: 2000}).showModal();
		    	return false;
		    }
		    if(el.tagName == 'INPUT')
		    {
		    	val = $(el).val();
		    }else if(el.tagName == 'SELECT'){
		    	val = $(el).find("option:selected").val()
		    }
		    valList.push(val);
		}
		var flaga = isRepeatFile(valList);
		if(isRepeatFile(valList)){
            /**
             * RM 2018-10-11
             * 添加if 判断， 因为批准文件的证书类型改了需求之后，可以重复了，
             * 而，年度报告， 其它附件的还需要做重复判断
             */
		    if($(table).parents('.panel-default').find('li.active').text().trim() != '批准文件'){
                utils.dialog({content: '表格除序号外第一列不能重复', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
		}
		
		for(var i=0;i<$tr.length;i++)
		{
			var dataVal='';
			var dataJson={};
		    var el=$tr.eq(i).find("[name='"+typeName+"']").get(0);
		    dataVal=$.trim($(el).val());
		    if(dataVal == '' || !dataVal)
		    {
		    	utils.dialog({content: '请选择附件类型,表格除序号外第一列', quickClose: true, timeout: 2000}).showModal();
		    	return false;
		    }
		    if(el.tagName == 'INPUT')
		    {
		    	dataJson={
				        text:$(el).val(),
				        value:$(el).val(),
                        lineNum: i
				    };
		    }else if(el.tagName == 'SELECT'){
		    	dataJson={
				        text:$(el).find("option:selected").text(),
				        value:$(el).find("option:selected").val(),
                        lineNum: i
				    };
		    }
		    typeList.push(dataJson);
		    //添加已存在附件
		    if(rowData[i].enclosureList.length > 0)
		    {
		    	for(var f=0;f<rowData[i].enclosureList.length;f++){
		    		rowData[i].enclosureList[f].type=dataVal;
                    rowData[i].enclosureList[f].lineNum = i;
		    	}
		        eChoImgList = eChoImgList.concat(rowData[i].enclosureList);
		    }
		}
		$(this).upLoad({
		    typeList:typeList,//格式[{text:xxx,value:xxx}]
		    eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
		    fileParam:{
		        name:'fileName',
		        url:'filePath'
		    },
		    urlBack: function (data) {
		        //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'enclosureList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData[j][typeName] == data[i].type && j == data[i].lineNum){
                            l.push(list);
                        }
                    }
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    $table.setRowData(trId,{'enclosureList':l});
                    $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
                }
	        }
	    });
	});
}

/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(obj).parents("table").getRowData(parentId);
    if(typeof data.enclosureList != 'string'){
        data.enclosureList=JSON.stringify(data.enclosureList);
	}
    if(data.enclosureList.length > 0)
    {
        $.viewImg({
            fileParam:{
                name:'fileName',
                url:'filePath'
            },
            list:data.enclosureList
        })
    }
}

function qtfjUploadFun(){
	
	//其他附件
	$("#qtfjUpload").on("click", function() {
		//获取type类型
		var typeList=[];
		var eChoImgList=[];
        /*
        * 操作流程：
        * 点击批量管理附件按钮时候、先判断 条件1：checkbox 是否有选中，条件2： 是否弹窗里已经含有附件。
        * 情况1： 如果没勾选checkbox。 也没有包含附件，。 打开弹窗后下拉框option 显示所有的checkbox 项；
        * 情况2： 如果checkbox有勾选，但是没有包含附件， 碳层下拉框只显示选中的checkbox项；
        * 情况3：既有勾选，也有包含附件。弹层下拉框显示所有的checkbox选项、
        * */
		var checkLen=$("#otherFileBox input[type='checkbox']:checked").length;
//        var inpdata = $('input[id^=newother]'),inpdataArr = [], inpadataFinalArr = [];
        var inpdata =null,inpdataArr = [], inpadataFinalArr = [];
        if($('input[id^=newother]').length > 0){
            inpdata = $('input[id^=newother]')
        }else{
            inpdata = $('input[id^=other]')
        }
        $(inpdata).each(function (index,item) {
            inpdataArr.push(JSON.parse(item.value));
        })
        for(var i = 0; i<inpdataArr.length; i++ ){
            for(var j = 0; j<inpdataArr[i].length; j++){
                inpadataFinalArr.push(inpdataArr[i][j])
            }
        }
        $("#otherFileBox label").each(function(){
            if(checkLen < 1 && inpadataFinalArr.length <1){  // 情况1
                var text=$.trim($(this).text());
                var v=$(this).prev("input").val();
                typeList.push({
                    text:text,
                    value:v
                })
            }else if(checkLen > 0 && inpadataFinalArr.length <1 ){ // 情况2
                var checked=$(this).prev('input[type=checkbox]')[0].checked; //$(this).find("input[type='checkbox']").get(0).checked;
                if(checked){
                    var text=$.trim($(this).text());
                    var v=$(this).prev("input").val();
                    typeList.push({
                        text:text,
                        value:v
                    })
                }
            }else if(checkLen > 0 && inpadataFinalArr.length > 0){ // 情况3
                var text=$.trim($(this).text());
                var v=$(this).prev("input").val();
                typeList.push({
                    text:text,
                    value:v
                })
                if($("input[id^='newother']").length > 0){
                    var newOther = $("input[id='newother"+v+"']");
                    var imgArr = [], newImgArr = [];
                    for(var i = 0; i<newOther.length; i++){
                        var str = $(newOther[i]).val();
                        str = JSON.parse(str)
                        imgArr = imgArr.concat(str);
                    }
                    var type = $("input[id='newother"+v+"']").attr('data-type');
                    for(var i = 0; i<imgArr.length; i++){
                        imgArr[i].type = type
                    }
                    eChoImgList = eChoImgList.concat(imgArr);
                }else{
                    if($("input[id='other"+v+"']").length > 0){
                        var newOther = $("input[id='other"+v+"']");
                        var imgArr = [], newImgArr = [];
                        for(var i = 0; i<newOther.length; i++){
                            var str = $(newOther[i]).val();
                            str = JSON.parse(str)
                            imgArr = imgArr.concat(str);
                        }
                        var type = $("input[id='other"+v+"']").attr('data-type');
                        for(var i = 0; i<imgArr.length; i++){
                            imgArr[i].type = type
                        }
                        eChoImgList = eChoImgList.concat(imgArr);
                    }
				}
            }else{  //情况4：去掉了勾选，但还有包含附件。弹层下拉框显示所有的checkbox选项、
                var text=$.trim($(this).text());
                var v=$(this).prev("input").val();
                typeList.push({
                    text:text,
                    value:v
                })
                if($("input[id^='newother']").length > 0){
                    var newOther = $("input[id='newother"+v+"']");
                    var imgArr = [], newImgArr = [];
                    for(var i = 0; i<newOther.length; i++){
                        var str = $(newOther[i]).val();
                        str = JSON.parse(str)
                        imgArr = imgArr.concat(str);
                    }
                    var type = $("input[id='newother"+v+"']").attr('data-type');
                    for(var i = 0; i<imgArr.length; i++){
                        imgArr[i].type = type
                    }
                    eChoImgList = eChoImgList.concat(imgArr);
                }else{
                    if($("input[id='other"+v+"']").length > 0){
                        var newOther = $("input[id='other"+v+"']");
                        var imgArr = [], newImgArr = [];
                        for(var i = 0; i<newOther.length; i++){
                            var str = $(newOther[i]).val();
                            str = JSON.parse(str)
                            imgArr = imgArr.concat(str);
                        }
                        var type = $("input[id='other"+v+"']").attr('data-type');
                        for(var i = 0; i<imgArr.length; i++){
                            imgArr[i].type = type
                        }
                        eChoImgList = eChoImgList.concat(imgArr);
                    }
				}
            }
        });
		$(this).upLoad({
		    typeList:typeList,//格式[{text:xxx,value:xxx}]
		    eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
		    fileParam:{
		        name:'fileName',
		        url:'filePath'
		    },
		    urlBack: function (data) {
		        //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
		        //存放数据
		        //files
		    	console.log(data)
                var inp = $('#otherFileBox input')
		    	var html='';
                $('input[id^=newother]').remove();
                var obj = {}
		        for(var name in data){
                    var list = [];
                    list.push(data[name])
		        	if(list.length > 0){
                        var arr=JSON.stringify(list);
                        //RenMin：  这段 注释的意义详情见  supplierApplyFile.js 文件同位置。
                        //html='<input type="hidden" data-type="'+name+'" id="newother'+name+'" value=\''+arr+'\' />';
                        html='<input type="hidden" data-type="'+data[name].type+'" id="newother'+data[name].type+'" value=\''+arr+'\' />';
                        $("body").append(html);
					}
	            }

                var AllInpHide = $('input[id^=newother]');
                if(AllInpHide.length > 0){ //  如果存在附件，再去设置checkbox的回显。
                    for(var i = 0; i<inp.length; i++){
                        inp[i].checked = false;
                        for(var j = 0; j<AllInpHide.length; j++){
                            if(inp[i].value == AllInpHide[j].getAttribute('data-type')){
                                inp[i].checked = true
                            }
                        }
                    }
                }else{ //  如果不存在附件，或者说附件被删除，设置checkbox的 全部未选中。
                    for(var i = 0; i<inp.length; i++){
                        inp[i].checked = false;
                    }
                }
	        }
	    });
	});
	
}

$(function () {
    //其它附件  双击选项预览对应的附件
    $('#otherFileBox label').each(function () {
        $(this).dblclick(function () {
            var imgList=[];
            var type = $(this).prev().val();
            
            var inp= $("input[id^='newother']");
            if(inp.length){
                if(inp.length > 1){
                    for(var i = 0; i<inp.length; i++){
                    	if($(inp[i]).attr('data-type') == type){
                    		var imgArr=JSON.parse($(inp[i]).val());
                        	imgList=imgList.concat(imgArr);
                    	}
                    }
                }else{
                    var imgArr=JSON.parse(inp.val());
                    imgList=imgList.concat(imgArr);
                }
            }else{
            	var inp= $("input[id^='other']");
            	if(inp.length){
                    if(inp.length > 1){
                        for(var i = 0; i<inp.length; i++){
                        	if($(inp[i]).attr('data-type') == type){
                        		var imgArr=JSON.parse($(inp[i]).val());
                            	imgList=imgList.concat(imgArr);
                        	}
                        }
                    }else{
                        var imgArr=JSON.parse(inp.val());
                        imgList=imgList.concat(imgArr);
                    }
                }
            }
            $.viewImg({
                fileParam:{
                    name:'enclosureName',
                    url:'filePath'
                },
                list:imgList
            });
        })
    })
})

//验证数组内容是否重复
function isRepeatFile(arr){ 
    var hash = []; 
    if(arr && arr.length>0){
    	 for(var i in arr) { 
    	        if(hash[arr[i]]) 
    	        return true; 
    	        hash[arr[i]] = true; 
    	    } 
    }
    return false;  
   
}

function change_validityDate(el) {
    console.log(el)
    $(el).parents('tr').find('[name=validityDate]').val((Number($(el).val())+2)+'-06-30')
}

