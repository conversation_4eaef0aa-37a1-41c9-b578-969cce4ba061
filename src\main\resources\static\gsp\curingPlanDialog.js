$(function () {
  /* 获取dialog上层实例 */
  var dialog = parent.dialog.get(window);
  if (dialog) {
    var dialog_data = dialog.data;
    //$('#search_vl').val(dialog_data)
  }

  /* table */

  $('#X_Table').XGrid({
   // data: grid_data,
     url: '/proxy-gsp/gsp/stockInCheck/findCheckPlanDeatail',
     postData:{
      orgCode:dialog_data
    },
    colNames: ['养护计划单据编号','养护计划单类型', '日期', '机构名称', '部门名称', '养护类别', '单据状态','隐藏列'],
    colModel: [{
      name: 'checkPlanCode',
      index: 'checkPlanCode',
      key: true,

    },{
        name: 'isZyypName',
        index: 'isZyypName'

    },  {
      name: 'checkTimes',
      index: 'checkTimes',
        formatter:function (e){
        if (e != null && e !="") {
            return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
        } else {
            return "";
        }
    }

    }, {
      name: 'orgName',
      index: 'orgName',

    }, {
      name: 'depaName',
      index: 'depaName',

    }, {
      name: 'checkType',
      index: 'checkType',
        formatter: function (e) {
        if (e == '1') {
            return '重点养护'
        } else if (e == '2') {
            return '普通养护'
        }else {
          return e
        }
    },
    unformat:function (e) {
        if (e == '重点养护') {
            return 1
        } else if (e == '普通养护') {
            return 2
        }else {
            return e
        }
    }

    }, {
      name: 'status',
      index: 'status',
        formatter: function (e) {
        if (e == '1') {
            return '已完成'
        } else if (e == '2') {
            return '未养护'
        }
    }
    },
    {
      name:'hidden_status',
        index:'hidden_status',
        hidden:true,
        formatter: function (e) {
            return '0'

    }},
        {
            name:'isZyyp',
            index:'isZyyp',
            hidden:true
           }],
      rowNum: 20,
      rowList:[20,50,100],
    rownumbers: true,
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {
      console.log('双击行事件', obj);
      var _obj = {
        code:obj.checkPlanCode,
        checkType:obj.checkType,
          isZyyp:obj.isZyyp,
        data:dialog_data,
      }
      dialog.close(_obj);
    },
    pager: '#grid-pager',
  });

  /* 查询 */
  $('#search_list').on('click', function () {
    $('#X_Table').setGridParam({
    //  url: 'http://www.baidu.com?name=' + $('#a').val(),
        url:'/proxy-gsp/gsp/stockInCheck/findCheckPlanDeatail',
          postData: {
              startTime:$("#startTime").val(),
              endTime:$("#endTime").val(),
              checkPlanCode:$("#checkPlanCode").val(),
              isZyyp:$("#isZyyp").val(),
          }
    }).trigger('reloadGrid');
  })

    /* 确定 */
    $("#sub").on("click",function () {
        var obj = $("#X_Table").XGrid("getSeleRow");
        if(obj){
            var _obj = {
                code:obj.checkPlanCode,
                checkType:obj.checkType,
                isZyyp:obj.isZyyp,
                data:dialog_data,
                hidden_status:0
            }
            dialog.close(_obj);
        }else {
            utils.dialog({
                title:"提示",
                content:"请先选中一行数据"
            }).show();
        }
    });


//
    // 去掉所有input的autocomplete, 显示指定的除外
  /* $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete',
    'off'); */

  /* 模糊搜索 */
  var countries = [{
      value: 'Andorra',
      data: 'AD'
    },
    {
      value: 'Zimbabwe',
      data: 'ZZ'
    }
  ];

  $('#search_vl').Autocomplete({
    serviceUrl: 'http://localhost/x.json', //异步请求
    // paramName: 'query111',//查询参数，默认 query
    dataType: 'json',
    //lookup: countries, //监听数据 value显示文本，data为option的值
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    /*  dataReader:{
          'value':'manufactoryName',
          'data':'manufactoryId',
           'xxt':"name"
      },*/
    triggerSelectOnValidInput: false, // 必选
    transformResult: function (response) {
      return {
        suggestions: $.each(response, function (idnex, dataItem) {
          return {
            value: dataItem,
            data: dataItem
          };
        })
      };
    },
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function (result) {
      //选中回调
      alert('You selected: ' + result.value + ', ' + result.data + ',' + result.xxt);
      // console.log('选中回调')
    },
    onSearchStart: function (params) {
      // console.log('检索开始回调', params)
    },
    onSearchComplete: function (query, suggestions) {
      //匹配结果后回调
      // console.log(query, suggestions);
      // if (suggestions.length < 1) {
      //     utils.dialog({
      //         title: '查询无结果',
      //         content: '是否新增生产厂家？',
      //         width: '300',
      //         okValue: '确认',
      //         ok: function () {
      //             this.title('提交中…');
      //             return false;
      //         },
      //         cancelValue: '取消',
      //         cancel: function () {
      //             $('input').val('')
      //         }
      //     }).show();
      // }


    },
    onSearchError: function (query, jqXHR, textStatus, errorThrown) {
      //查询失败回调
      console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function (container) {
      // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function (params, suggestions) {
      console.log(params, suggestions);
      console.log('没选中回调函数');
    }
  });


})