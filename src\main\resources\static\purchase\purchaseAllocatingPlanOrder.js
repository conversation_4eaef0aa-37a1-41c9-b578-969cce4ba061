$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //进入页面自动查询是否有驳回提示
    $(function () {
        $.ajax({
                method: "GET",
                url: "/proxy-purchase/purchase/allocatingPlanOrder/product/productRejectNum",                
            }).done(function (data) {
                if(data.result != 0 && (typeof data.result == 'number')){
                    console.log('yz',data);
                    $('#message-count').text(data.result);
                    $("#message-container").show();
                }else{
                    console.log('zmt',data);
                    $('#message-count').text(0);
                    $("#message-container").hide();
                }
            })
    })
    $('.nav-tabs>li').on('click', function () {
        let $this = $(this), $nav_content = $('.nav-content');
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).css('display', 'flex').siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');
        var $orderPlanProduct = $("#orderPlanProduct");
        if(!$this.index()){
            $orderPlanProduct.hide();
        }else{
            if($('#message-count').text() > 0){
                    $('#orderPlanProductStatus').val('3');
                    document.getElementById('searchBtn').click();
                    $.ajax({
                    method: "GET",
                    url: "/proxy-purchase/purchase/allocatingPlanOrder/product/resetProductReject",                
                }).done(function (data) {
                    $('#message-count').text(0);
                    $('#message-container').hide();
                })
            };
            $orderPlanProduct.show();
        }
    })
    let orderType = $('#orderType').val()
    //单据编号
    $('#TransferPlan_table').XGrid({
        url:"/proxy-purchase/purchase/allocatingPlanOrder/queryPageInfo",
        colNames: ['', '计划单状态编码','计划单状态', '调拨计划单号','订单属性状态码','订单属性','计划单总金额','预估调拨费用','创建人', '创建时间', '备注'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true,
                hidegrid: true
            },
            {
                name: 'allotPlanStatus',
                index: 'allotPlanStatus',
                width: 100,
                hidden:true,
                hidegrid: true
            },
            {
                name: 'allotPlanStatusName',
                index: 'allotPlanStatusName',
                width: 100,
            },
            {
                name: 'allotPlanNo',
                index: 'allotPlanNo',
                width: 350,
                formatter:function(a,b,c){
                    console.log('c',c);
                    if(c.allotPlanStatus == '6'){
                        return '<span style="margin-right: 10px;">'+ a + '</span>' +'<span style="color:red">部分驳回</span>';
                    }else{
                        return '<p>'+ a + '</p>';
                    }
                }
            },
            {
                name: 'centralizedPurchaseType',
                index: 'centralizedPurchaseType',
                hidden:true,
                hidegrid: true
            },
            {
                name: 'centralizedPurchaseName',
                index: 'centralizedPurchaseName',
            },
            {
                name: 'orderPriceSum',
                index: 'orderPriceSum',
                width: 100
            },
            {
                name: 'forecastAllotCost',
                index: 'forecastAllotCost',
                width: 200
            },
            {
                name: 'createUserName',
                index: 'createUserName',
                width: 160
            },
            {
                name: 'createTime',
                index: 'createTime',
                width: 250
            },
            {
                name: 'remark',
                index: 'remark',
                width: 100
            }
        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#TransferPlan_pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            let from = obj['allotPlanNo'].indexOf('DB');
            let ob = obj['allotPlanNo'].substring(from,from+14);
            let url = '/proxy-purchase/purchase/allocatingPlanOrder/toAdd?type=1&allotPlanNo='+ob;
            // 调拨单状态 0.草稿 1.校验中 2.校验未通过 3.审核中 4.已审核通过 5.驳回
            let _title = ''
            switch (obj['allotPlanStatus']) {
                case '0':
                case '2':
                case '5':
                    _title = '编辑调拨明细单'
                    break
                case '1':
                case '3':
                case '4':
                    _title = '调拨明细单详情'
                    break
            }
            parent.openTabs("toAdd", '调拨明细单详情', url);
        },
    });

    $('#ProductDetail_table').XGrid({
        url:"/proxy-purchase/purchase/allocatingPlanOrder/product/queryPageInfo",
        colNames: ['', '调拨计划单号', '调出机构编码','调出机构','调入机构编码','调入机构','商品编码','商品名称','规格','TOP排名','商品审核状态','驳回原因', '生产厂家', '订单属性状态码','订单属性','调拨数量','调拨价格', '是否近效期购进','生产日期', '有效期至'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true,
                hidegrid: true
            },
            {
                name: 'allotPlanNo',
                index: 'allotPlanNo',
                width: 150,
            },
            {
                name: 'callOutOrg',
                index: 'callOutOrg',
                hidden:true,
                hidegrid: true
            },
            {
                name: 'callOutOrgName',
                index: 'callOutOrgName',
                width: 250
            },
            {
                name: 'callInOrg',
                index: 'callInOrg',
                hidden:true,
                hidegrid: true
            },
            {
                name: 'callInOrgName',
                index: 'callInOrgName',
                width: 250
            },
            {
                name: 'productCode',
                index: 'productCode',
                width: 100
            },
            {
                name: 'productName',
                index: 'productName',
                width: 200
            },
            {
                name: 'productSpecification',
                index: 'productSpecification',
                width: 160
            },
            {
                name: 'top',
                index: 'top',//top排名
                // hidegrid: true,
              },{
                name:'orderPlanProductStatus', //商品审核状态
                index:'orderPlanProductStatus',
            },{
                name:'rejectionReason', //驳回原因
                index:'rejectionReason',  
            },{
                name: 'productProduceFactory',
                index: 'productProduceFactory',
                width: 250
            },
            {
                name: 'centralizedPurchaseType',
                index: 'centralizedPurchaseType',
                hidden:true,
                hidegrid: true
            },
            {
                name: 'centralizedPurchaseName',
                index: 'centralizedPurchaseName',
                width: 100
            },
            {
                name: 'productCount',
                index: 'productCount',
                width: 100
            },
            {
                name: 'productContainTaxPrice',
                index: 'productContainTaxPrice',
                width: 100
            },
            {
                name: 'validityDateStr',
                index: 'validityDateStr',
                width: 120
            },
            {
                name: 'productProduceDate', //生产日期
                index: 'productProduceDate',
                width: 180,
            },{
                name: 'productValidityDate', //有效期至
                index: 'productValidityDate',
                width: 180,
            }
        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#ProductDetail_pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            console.log(id,dom,obj,index,event);
            let url = '/proxy-purchase/purchase/allocatingPlanOrder/toAdd?type=1&allotPlanNo='+obj['allotPlanNo'];
            parent.openTabs("toAdd", "编辑调拨明细单", url);
        },
    });

    //查询数据，重置data
    $('#searchBtn').on('click', function () {
        var cs = $("#toggle_wrap .active").attr("id");
        if (cs == "TransferPlanList") {
            //加载左边联想的数据
            $('#TransferPlan_table').setGridParam({
                url: '/proxy-purchase/purchase/allocatingPlanOrder/queryPageInfo?' + $("#searchForm").serialize()
            }).trigger('reloadGrid');
        } else if (cs == "ProductDetailList") {
            //加载右边联想的数据
            $('#ProductDetail_table').setGridParam({
                url: '/proxy-purchase/purchase/allocatingPlanOrder/product/queryPageInfo?' + $("#searchForm").serialize()
            }).trigger('reloadGrid');
        } else {
            return;
        }
    })

    /**
     * 新建
     */
    $('#createBtn').on('click', function () {
        let url = '/proxy-purchase/purchase/allocatingPlanOrder/toAdd?type=0';
        parent.openTabs("toAdd", "新建调拨明细单", url);
    })
    //导出
    $('#exportBtn').on('click', function () {
        let _curTabIndex = $('.nav-tabs>li.active').index();
        let tableIdAry = ['TransferPlan_table', 'ProductDetail_table'],
            pagerIdAry = ['TransferPlan_pager', 'ProductDetail_pager'],
            taskCodeAry = ['1017','1018'],
            fileNameAry = ['调拨计划单列表','调拨计划商品明细列表'],
            moduleNameAry = ['purchasePlanOrder','purchasePlanProduct'];

        utils.exportAstrictHandle(tableIdAry[_curTabIndex],Number($('#' + pagerIdAry[_curTabIndex] + ' #totalPageNum').text())).then(()=>{
            return false;
        }).catch(()=> {
            let tableId = tableIdAry[_curTabIndex];
            //获取当前选中项
            console.log(tableId);
            let data =  $('#' + tableId).XGrid('getSeleRow');
            console.log(data);
            z_utils.exportTable(tableId, function (that) {
                //需要导出项
                let colName = [];
                let colNameDesc = [];
                $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                    if ($(this).prop("checked")) {
                        colName.push($(this).attr('name'))
                        colNameDesc.push($(this).parent().text());
                    }
                });
                if (data ) {
                    if (data.length && data.length >0) {
                        data = data.map(function (item, key) {
                            let new_item = {};
                            colName.forEach(function (val, index) {
                                new_item[val] = item[val]
                            })
                            return new_item
                        })
                        data = JSON.stringify(data);
                    }

                    // formData["selectData"] = data;
                } else {
                    utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
                //获取form数据
                let fd = $('#searchForm').serializeToJSON()
                let formData = {
                    moduleName: moduleNameAry[_curTabIndex],
                    taskCode: taskCodeAry[_curTabIndex],
                    colName: colName.join(","),
                    colNameDesc: colNameDesc.join(","),
                    fileName: fileNameAry[_curTabIndex],
                    check: 'check',
                    exportParams: {
                        ...fd
                    }
                }
                utils.dialog({
                    title: '温馨提示',
                    content: '导出任务提交成功后页面将关闭，是否确认导出？',
                    okValue: '确定',
                    ok: function () {
                        $.ajax({
                            url: "/proxy-purchase/purchase/commonExport/commonCommitExportTask",
                            type: 'post',
                            dataType: 'json',
                            data: {
                                "data":JSON.stringify(formData)
                            },
                            success: function (res) {
                                if (res) {
                                    if (res.code === 0) {
                                        utils.dialog({
                                            title: '温馨提示',
                                            content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                            okValue: '确定',
                                            ok: function () {

                                            }
                                        }).showModal()
                                    } else {
                                        utils.dialog({
                                            title: '温馨提示',
                                            content: res.msg,
                                            okValue: '确定',
                                            ok: function () {

                                            }
                                        }).showModal()
                                    }
                                }
                            }
                        });
                    }
                }).showModal()
            });
        })
    });
})

//业务类型搜索图标
$(document).on("click", ".glyphicon-search", function () {
    $(this).siblings("input").trigger("dblclick")
})

//业务类型 输入框双击 弹出业务类型列表
$("#channelId_inp").dblclick(function () {
    utils.channelDialog('0').then(res => {
        console.log(res)
        let _str_name = '', _str_code = '';
        let _str_arr = res.map(item => {
            return item.channelName;
        })
        _str_name = _str_arr.join(',');

        let _str_code_arr = res.map(item => {
            return item.channelCode;
        })
        _str_code = _str_code_arr.join(',')
        $('#channelId_inp').val(_str_name)
        $('#channelId').val(_str_code)
    })
});


//近效期计算器
$("#dateComputed").click(function () {
    utils.timeDialog()
});


function  btn_output_list(){

    var cs = $("#toggle_wrap .active").attr("id");
    var taskCode = "";
    var moduleName = "";
    if(cs == "TransferPlanList"){
        taskCode = "1017";
        moduleName = "purchasePlanOrder";
    }else if(cs == "ProductDetailList"){
        taskCode = "1018";
        moduleName = "purchasePlanProduct";
    }
    utils.dialog({
        title: '导出列表',
        url: '/proxy-purchase/purchase/commonExport/toExportList?moduleName='+moduleName+'&taskCode='+taskCode,
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
}
typeof data != 'object' && data != 0