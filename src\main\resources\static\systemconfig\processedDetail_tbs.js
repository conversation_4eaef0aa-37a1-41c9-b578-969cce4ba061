﻿ var listdds = {};

var batchs = $("#batch").val();
  function getData() {
        $.ajax({
            url:"queryCompleteTask",
            data:{
                 "batch": batchs

            },
            success:function(res){
                if(res.result){

                    var arr = []
                    for (var i in res.result) {
                        arr.push(res.result[i]); //属性

                    }
                     webRender(arr);
                 }  else {
                          utils.dialog({
                              title:'提示',
                              content:"数据为空或格式不正确",
                              okValue:'确定',
                              ok:function () {}
                          }).showModal();

                  }

            }
        });
   }

getData();
function webRender(data) {
        var box_html = '';

            data.forEach(function (item,index) {
                console.log(item,item.orgCode);
                box_html +='<div class="table_wrap"><span>'+item[0].orgName+'</span><table id="table_a_'+index+'"></table></div>';

            });

   $("#tablelists").html(box_html);
        data.forEach(function (item,index) {



                $("#table_a_"+index).XGrid({
                    data: item,
                    datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                    colNames: ['id','分类', '机构', '商品标准流速', '单位','占比','分类规则','orgCode'],
                    colModel: [{
                        name: 'id',
                        index: 'id', //索引。其和后台交互的参数为sidx
                        hidden:true

                    }, {
                        name: 'sort',

                    }, {
                        name: 'orgName',

                    }, {
                        name: 'productCriterion',

                    }, {
                        name: 'unit',

                    },{
                        name: 'proportion',

                    },{
                        name:'sortRule',
                       formatter:function (e){
                            if(e == 'null'){
                                return '';
                            }else{
                                return e;
                            }
                        }
                    },
                        {
                            name: 'orgCode',
                            hidden: true,
                        }
                    ],
                    rowNum: 100,
                    rownumbers: 0,
                    altRows: true, //设置为交替行表格,默认为false
                    ondblClickRow: function (e, c, a, b) {
                        console.log('双击行事件', e, c, a, b);
                    },
                    onSelectRow: function (e, c, a, b) {
                        console.log('单机行事件', e, c, a, b);
                    },
                    //pager: '#grid-pager',
                });


     });



    };


function showTips(contentText){
    utils.dialog({
        content: contentText,
        quickClose: true,
        timeout: 2000
    }).showModal();
}


function showDialog(titleText,contentText){
    //提示框
    utils.dialog({
        width: 180,
        height: 30,
        title: titleText,
        content: contentText,
        quickClose: false,
        okValue: '确定',
        ok: function () {}
    }).showModal();
}

$(function(){


    initApprovalFlowChart($("#processId").val())

    function  initApprovalFlowChart(processId) {
        //获取审核流程数据
        $.ajax({
            type: "POST",
            url: "queryProcessHistory",
            dataType:"json",
            data:{batch:processId},
            success: function (data) {
                if (data.code==0&&data.result!=null){
                    console.log(data.result);
                    $('.flow').html("")
                    $('.flow').process(data.result);
                }
            },
            error: function () {}
        });
    }


//审批通过
    $("#completeTask").click(function(){

        $("#remark").val('')
        var batch = $("#batch").val();
        var taskId = $("#taskId").val();
          $(this).css("display","none");
          $("#commitProcess").css("display","none")
        utils.dialog({
            title: '审核通过',
            content: $('#check_pass_msg'),
            okValue: '确定',
            ok: function () {
                if(!validform('check_pass_form').form()) return false;
                $.ajax({
                    url:'completeTask',
                    type:'post',
                    data:{businessId:batch,taskId:taskId,comment:$("#remark").val()},
                    success:function(result){
                        if(result.code==0){
                            showTips("审核成功!");
                            setTimeout(function(){ utils.closeTab(); }, 1500);
                        }if(result.code==1){
                            showTips("审核失败!")
                        }

                    }
                })
            },
            cancelValue: '取消',
            cancel: function () {
                     $("#completeTask").css("display","inline-block");
                     $("#commitProcess").css("display","inline-block");
            }
        }).showModal()








    })


// 审批驳回
    $("#commitProcess").click(function(){
        $(this).css("display","none");
        $("#completeTask").css("display","none");
        $("#remark").val('')


        utils.dialog({
            title: '审核不通过',
            content: $('#check_pass_msg'),
            okValue: '确定',
            ok: function () {
                if(!validform('check_pass_form').form()) return false;
                $.ajax({
                    url:'commitProcess',
                    type:'post',
                    data:{batch:$("#batch").val(),taskId:$("#taskId").val(),comment:$("#remark").val()},
                    success:function(result){
                        if(result.code==0){
                            showTips("关闭成功!")
                             setTimeout(function(){ utils.closeTab(); }, 1500);
                        }if(result.code==1){
                            showTips("关闭失败!")
                        }

                    }
                })
            },
            cancelValue: '取消',
            cancel: function () {
                   $("#completeTask").css("display","inline-block");
                 $("#commitProcess").css("display","inline-block");
            }
        }).showModal()





    })


})



