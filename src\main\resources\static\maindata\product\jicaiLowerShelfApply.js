$('div[fold=head]').fold({
    sub: 'sub'
});
var date1 = new Date();
var curMonth = date1.getMonth();
curMonth = (curMonth == 0) ? '12' : curMonth;
var lastMonth = ((curMonth - 1) == 0) ? '12' : (curMonth - 1);
var lastLastMonth = ((lastMonth - 1) == 0) ? '12' : (lastMonth - 1);
var colNames = ['ID', '商品id', '机构编码', '机构', '业务类型', '商品唯一值', '原商品id', '商品编号', '原商品编码', '小包装条码', '商品名称', '通用名', '商品规格', '生产厂家', '单位',
    '采购员', '销售状态', '下架原因', '原因说明', "替换编码", '原因说明', '附件', '商品定位一级ID', '商品定位一级', '商品定位二级ID', '商品定位二级', '集采签约方式ID', '集采签约方式', '销售定位ID', '销售定位', '经营状态隐藏字段', '经营状态', '审核经营状态隐藏字段', '合格品库可用库存数量', '总库存数量',
    '最后含税进价', '最后供应商', 'APP售价', '终端建议零售价', '商品产地', '剂型', '处方分类', '批准文号',
    '一级分类', '二级分类', '三级分类', '四级分类', '应季类型', 'TOP排名', lastLastMonth + '月总销量', lastMonth + '月总销量', curMonth + '月总销量', '月至今netGMV', '当月销量', '上月客户数', '当月客户数', '市占率', '30天内售罄天数', '60天内售罄天数', '是否属于首推高毛专区的商品'
];
var colModel = [{
    name: 'id',
    index: 'id',
    hidden: true,
    hidegrid: true
}, {
    name: 'productId',
    index: 'productId',
    hidden: true,
    hidegrid: true
}, {
    name: 'orgCode',
    index: 'orgCode',
    hidden: true,
    hidegrid: true
}, {
    name: 'orgName',
    index: 'orgName',
    width: 210
}, {
    name: 'channelId',
    index: 'channelId',
    width: 100
}, {
    name: 'productIdChannel',
    index: 'productIdChannel',
    hidden: true,
    hidegrid: true
}, {
    name: 'oldProductId',
    index: 'oldProductId',
    hidden: true,
    hidegrid: true
}, {
    name: 'productCode',
    index: 'productCode',
    width: 100
}, {
    name: 'oldProductCode',
    index: 'oldProductCode',
    width: 140
}, {
    name: 'smallPackageBarCode',
    index: 'smallPackageBarCode',
    width: 140
}, {
    name: 'productName',
    index: 'productName',
    width: 110
}, {
    name: 'commonName',
    index: 'commonName',
    width: 110
}, {
    name: 'specifications',
    index: 'specifications',
    width: 140
}, {
    name: 'manufacturerName',
    index: 'manufacturerName',
    width: 240
}, {
    name: 'packingUnitVal',
    index: 'packingUnitVal',
    width: 80
}, {
    name: 'buyerVal',
    index: 'buyerVal',
    width: 80
}, {
    name: 'ecStatusVal',
    index: 'ecStatusVal',
    width: 80
}, {
    name: 'lowerShelfReason',
    index: 'lowerShelfReason',
    rowtype: '#lowerShelfReason',
    width: 200
}, {
    name: 'remarks',
    index: 'remarks',
    //rowtype: '#remarks',
    width: 250,
    formatter: (val, rowType, rowData) => {
        let htmlArr = [];
        htmlArr.push('<select name="remarks" class="form-control applyUpperShelf">');
        htmlArr.push('<option value="">请选择</option>');

        //根据原因说明拼装select的option
        let remarksOptionElement = [];
        //下架原因select
        if (rowData.lowerShelfReason) {
            const options = remarksOptionsAdapter(rowData.lowerShelfReason)
            options.forEach(optionItem => {
                remarksOptionElement.push(`<option value="${optionItem.value}" selected="${rowData.remarks === optionItem.value}">${optionItem.label}</option>`)
            });
            //remarks兼容旧的input数据
            if (rowData.remarks) {
                let remarksOldValueFilter = options.some(optionItem => {
                    return optionItem.value === rowData.remarks
                })
                if (!remarksOldValueFilter) {
                    remarksOptionElement.push(`<option value="${rowData.remarks}" selected="true">${rowData.remarks}</option>`)
                }
            }
        } else {
            //remarks兼容旧的input数据
            if (rowData.remarks) {
                remarksOptionElement.push(`<option value="${rowData.remarks}" selected="true">${rowData.remarks}</option>`)
            }
        }
        if (remarksOptionElement.length) {
            htmlArr.push(remarksOptionElement.join(''))
        }

        htmlArr.push('</select>')
        return htmlArr.join('')
    }
}, {
    name: 'replaceEncoding',
    index: 'replaceEncoding',
    hidden: true,
    hidegrid: true,
    width: 200,
    formatter: (a, b, arr) => {
        if (a) { return `<div style='display: flex;justify-content: center;'><input type='text' onclick='selectProduct(this)' value=${a} class="form-control applyUpperShelf" id='replaceEncoding' name='replaceEncoding' maxlength='200'><div style='background-color: black;color: white;border-radius: 50%;width: 20px ;height: 20px;text-align: center;' onmouseenter='replaceEncodingTip(this)'  onmouseleave='questionTip.close().remove()'  specifications='${arr.replaceSpecifications}'    productName='${arr.replaceProductName}'     manufacturerName='${arr.replaceManufacturerName}' >?</div> </div>` } else {
            return `<div style='display: flex;justify-content: center;'><input type='text' onclick='selectProduct(this)'  class="form-control applyUpperShelf" id='replaceEncoding' name='replaceEncoding' maxlength='200'><div style='background-color: black;color: white;border-radius: 50%;width: 20px ;height: 20px;text-align: center;' onmouseenter='replaceEncodingTip(this)'  onmouseleave='questionTip.close().remove()'   specifications=''    productName=''     manufacturerName=''  >?</div> </div>`
        }
    },
}, {
    name: 'reasonRemarks',
    index: 'reasonRemarks',
    hidden: false,
    hidegrid: true,
    formatter: (reasonRemarks) => {
        if (reasonRemarks) { return `<input type='text' class="form-control applyUpperShelf"   id='reasonRemarks' name='reasonRemarks' maxlength='200' value=${reasonRemarks} onmouseenter='enterReasonRemarks(this)' onmouseleave='leaveReasonRemarks(this)'>` } else {
            return `<input type='text' class="form-control applyUpperShelf"  id='reasonRemarks' name='reasonRemarks' maxlength='200' onmouseenter='enterReasonRemarks(this)' onmouseleave='leaveReasonRemarks(this)' >`
        }

    },
    width: 200
}, {
    name: 'enclosureUrl',
    index: 'enclosureUrl',
    formatter: function(val, rowType, rowData) {
        if (val) {
            return `<a href="javascript:;" data-url="${val}" class="file_a">📎</a>`;
        } else {
            return `<button type="button" class="btn btn-info file_btn">上传</button><input style="display: none" class="file_input" type="file" id="${rowData.productIdChannel}_file"/>`;
        }
    },
    unformat: function(val, rowModel, ele) {
        if ($(ele).find('a.file_a').length > 0) {
            return $(ele).find('a.file_a').attr('data-url');
        } else {
            return '';
        }
    }
}, {
    name: 'commodityPosition',
    index: 'commodityPosition',
    hidden: true,
    hidegrid: true
}, {
    name: 'commodityPositionVal',
    index: 'commodityPositionVal',
    width: 150
}, {
    name: 'secondCommodityPosition',
    index: 'secondCommodityPosition',
    hidden: true,
    hidegrid: true
}, {
    name: 'secondCommodityPositionVal',
    index: 'secondCommodityPositionVal',
    width: 150
}, {
    name: 'purchaseContractMode',
    index: 'purchaseContractMode',
    hidden: true,
    hidegrid: true
}, {
    name: 'purchaseContractModeVal',
    index: 'purchaseContractModeVal',
    width: 150
}, {
    name: 'salesClassificationId',
    index: 'salesClassificationId',
    hidden: true,
    hidegrid: true
}, {
    name: 'salesClassification',
    index: 'salesClassification',
    width: 120
}, {
    name: 'operatingState',
    index: 'operatingState',
    hidden: true,
    hidegrid: true
}, {
    name: 'operatingStateVal',
    index: 'operatingStateVal',
    width: 120
}, {
    name: 'auditOperatingState',
    index: 'auditOperatingState',
    hidden: true,
    hidegrid: true
}, {
    name: 'inventoryQuantity', //【库存数量】名称改为【合格品库可用库存数量】
    index: 'inventoryQuantity',
    width: 170
}, {
    name: 'allStockAmount', //总库存数量
    index: 'allStockAmount',
    width: 110
}, {
    name: 'lastIncludingTaxPurchasePrice',
    index: 'lastIncludingTaxPurchasePrice',
    width: 110
}, {
    name: 'lastSupplier',
    index: 'lastSupplier',
    width: 220
}, {
    name: 'appPrice',
    index: 'appPrice',
    formatter: function(e) {
        if (e != null) {
            return Number(e).toFixed(2);
        }
    },
    width: 110
}, {
    name: 'terminalPrice',
    index: 'terminalPrice',
    formatter: function(e) {
        if (e != null) {
            return Number(e).toFixed(2);
        }
    },
    width: 130
}, {
    name: 'producingArea',
    index: 'producingArea',
    width: 80
}, {
    name: 'dosageFormVal',
    index: 'dosageFormVal',
    width: 100
}, {
    name: 'prescriptionClassificationVal',
    index: 'prescriptionClassificationVal',
    width: 100
}, {
    name: 'approvalNumber',
    index: 'approvalNumber',
    width: 160
}, {
    name: 'firstCategoryVal',
    index: 'firstCategoryVal',
    width: 100
}, {
    name: 'secondCategoryVal',
    index: 'secondCategoryVal',
    width: 120
}, {
    name: 'thirdCategoryVal',
    index: 'thirdCategoryVal',
    width: 120
}, {
    name: 'fourCategory',
    index: 'fourCategory',
    width: 100
}, {
    name: 'seasonalVarietiesVal',
    index: 'seasonalVarietiesVal',
    width: 120
}, {
    name: 'top',
    index: 'top',
    width: 120
}, {
    name: 'threeMonthSaleVolume',
    index: 'threeMonthSaleVolume',
    width: 120
}, {
    name: 'twoMonthSaleVolume',
    index: 'twoMonthSaleVolume',
    width: 120
}, {
    name: 'oneMonthSaleVolume',
    index: 'oneMonthSaleVolume',
    width: 120
}, {
    name: 'currentNetGMV', //月至今netGMV
    index: 'currentNetGMV',
    width: 170
}, {
    name: 'currentMonthSaleAmount', //当月销量
    index: 'currentMonthSaleAmount',
    width: 120
}, {
    name: 'lastMonthCustomerNum', //上月客户数
    index: 'lastMonthCustomerNum',
    width: 120
}, {
    name: 'currentMonthCustomerNum', //当月客户数
    index: 'currentMonthCustomerNum',
    width: 120
}, {
    name: 'marketRatio', //市占率
    index: 'marketRatio',
    width: 120
}, {
    name: 'thirtySaleoutDays',
    index: 'thirtySaleoutDays',
    width: 170
}, {
    name: 'sixtySaleoutDays',
    index: 'sixtySaleoutDays',
    width: 170
}, {
    name: 'productFirstGm',
    index: 'productFirstGm',
    hidden: true,
    hidegrid: true
}, ];
$('#X_Table').XGrid({
    data: [],
    colNames: colNames,
    colModel: colModel,
    rowNum: 20,
    rownumbers: true,
    key: 'productIdChannel',
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function(id, dom, obj, index, event) {
        //console.log('双击行事件', id, dom, obj, index, event);
    },
    onSelectRow: function(id, dom, obj, index, event) {
        //console.log('单机行事件', id, dom, obj, index, event);
    }
});

//下架原因返回原因说明
function remarksOptionsAdapter(reasonValue) {
    const remarksOptions = {
        /* '1': [ //厂家因素
            { value: '厂家停产(规格、原料)', label: '厂家停产(规格、原料)' },
            { value: '政策原因(带量采购、医保中标等)', label: '政策原因(带量采购、医保中标等)' },
            { value: '供货渠道管控(线上渠道禁销等)', label: '供货渠道管控(线上渠道禁销等)' }
        ],
        '2': [ //品种替换
            { value: '同品更换(条码、品牌等)', label: '同品更换(条码、品牌等)' },
            { value: '异规更换(规格调整)', label: '异规更换(规格调整)' },
            { value: '同通用名替换', label: '同通用名替换' }
        ],
        '3': [ //质量问题
            { value: '上传相关依据附件(邮件或召回函等)', label: '上传相关依据附件(邮件或召回函等)' }
        ],
        '4': [ //销售问题
            { value: '连续3个月不满足动销标准', label: '连续3个月不满足动销标准' },
            { value: '连续3个月销售数量为0', label: '连续3个月销售数量为0' },
            { value: '下游客户被限制或禁售', label: '下游客户被限制或禁售' }
        ],
        '5': [ //集采合作
            { value: '地采商品限制', label: '地采商品限制' }
        ] */
        '6': [ //厂家因素
            { value: '厂家停产(规格、原料)', label: '厂家停产(规格、原料)' },
            { value: '政策原因(带量采购、医保中标等)', label: '政策原因(带量采购、医保中标等)' },
            { value: '供货渠道管控(线上渠道禁销、代理（大包）等)', label: '供货渠道管控(线上渠道禁销、代理（大包）等)' },
            { value: '销售渠道管控（下游客户被限制或禁售）', label: '销售渠道管控（下游客户被限制或禁售）' },
            { value: '厂家批次效期，暂不购进', label: '厂家批次效期，暂不购进' }
        ],
        '7': [ //销售问题
            { value: '连续3个月不满足动销标准', label: '连续3个月不满足动销标准' },
            { value: '连续3个月销售数量为0', label: '连续3个月销售数量为0' },
            { value: '库存清理下架（不良库存，外省调拨）', label: '库存清理下架（不良库存，外省调拨）' },
            { value: '其他', label: '其他' }
        ],
        '8': [ //编码变更
            { value: '同品更换（条码、品牌、其他资质变更等）', label: '同品更换（条码、品牌、其他资质变更等）' },
            { value: '异规更换（规格调整）', label: '异规更换（规格调整）' },
            { value: '采购方式变更（集地采转换）', label: '采购方式变更（集地采转换）' }
        ],
        '9': [ //销售问题
            { value: '上传相关依据附件（邮件或召回函等）', label: '上传相关依据附件（邮件或召回函等）' }

        ]
    }
    return remarksOptions[reasonValue] || []
}

//设置显示列
$("#setRow").click(function() {
    $('#X_Table').XGrid('filterTableHead', 1000);
});
//选择建议下架商品库
$(".selectAdviceProduct").on("click", function() {
    var resultArr = $('#X_Table').getRowData();
    var dataFlag = this.getAttribute("dataFlag");
    dialog({
        url: '/proxy-product/product/lowerShelf/toJicaiLowerShelfSearchProduct?dataFlag=' + dataFlag,
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            "orgCode": $("#recordOrgCode").val(),
            channelId: (resultArr.length != 0) ? resultArr[0].channelId : '',
            channelName: (resultArr.length != 0) ? resultArr[0].channelVal : ''
        }, // 给modal 要传递的 的数据
        onclose: function(data) {
            var data = this.returnValue;
            if (data) {
                parent.showLoading();
                var rows = data.resultArr;
                var orgProductIds = "";
                var channelId = "";
                for (var i = 0; i < rows.length; i++) {
                    var productId = rows[i].orgProductIds;
                    if (i == 0) {
                        orgProductIds = productId;
                        channelId = rows[i].channelId;
                    } else {
                        orgProductIds = orgProductIds + "," + productId;
                    }
                }
                $.ajax({
                    type: "post",
                    url: "/proxy-product/product/lowerShelf/getOrgProInfo",
                    async: true,
                    data: { "channelId": channelId, "orgProductIds": orgProductIds },
                    dataType: "json",
                    success: function(data) {
                        if (data.code == 0) {
                            var arr = data.result;
                            for (var i = 0; i < arr.length; i++) {
                                var productId = arr[i].productId;
                                if (!findInArr(productId)) {
                                    arr[i].id = arr[i]['productId'];
                                    $('#X_Table').XGrid('addRowData', arr[i]);
                                    addBackGroundColor()
                                }
                            }
                        }
                    },
                    complete: function() {
                        parent.hideLoading();
                    }
                });
            }
        }
    }).showModal();
});
//列表内查询id
function findInArr(productId) {
    var arr = $('#X_Table').getRowData();
    for (var i = 0; i < arr.length; i++) {
        var productID = arr[i].productId;
        if (productID == productId) {
            return true;
        }
    }
    return false;
}
//含商品池的首推高毛专区的商品时整行颜色区分 
function addBackGroundColor() {
    var arr = [];
    arr = $('#X_Table').getRowData();
    console.log(arr, '我在这a ')
    if (arr && arr.length > 0) {
        arr.forEach(ele => {
            if (ele.productFirstGm == '1') {
                console.log('我在这', ele.productIdChannel)
                $("#X_Table  #" + ele.productIdChannel).css('background', 'rgba(254,95,85,1)')
            }
        });
    }
}
//删除行
$("#deleRow").on("click", function() {
    var selectRow = $('#X_Table').XGrid('getSeleRow');
    if (selectRow.length == 0) {
        utils.dialog({ content: '请选择删除行！', quickClose: true, timeout: 2000 }).showModal();
    } else {
        utils.dialog({
            title: "提示",
            width: 300,
            height: 30,
            okValue: '确定',
            content: "确定删除此条记录?",
            ok: function() {
                $('#X_Table').XGrid('delRowData', selectRow[0].id);
                utils.dialog({ content: '删除成功！', quickClose: true, timeout: 2000 }).showModal();
            },
            cancelValue: '取消',
            cancel: function() {}
        }).showModal();
    }
});
//提交Ajax请求
function submitFuncAjax(rowData) {
    var data = JSON.stringify(rowData);
    parent.showLoading();
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/lowerShelf/saveJicaiLowerShelfInfo",
        data: data,
        async: false,
        dataType: 'json',
        contentType: "application/json",
        error: function() {
            utils.dialog({ content: "提交失败！", quickClose: true, timeout: 2000 }).showModal();
        },
        success: function(data) {
            if (data.code == 0) {
                var msg = '提交审核成功';
                if (data.result.code == 1) {
                    msg = data.result.msg;
                }
                utils.dialog({
                    title: "提示",
                    content: `<p style="word-break: break-word;width: 340px; height: 100px;overflow:auto;">` + msg + `</p>`,
                    width: 340,
                    height: 100,
                    okValue: '确定',
                    ok: function() {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            } else {
                utils.dialog({
                    title: "提示",
                    content: `<p style="word-break: break-word;width: 340px; height: 100px;overflow:auto;">${data.result}</p>`,
                    width: 340,
                    height: 100,
                    okValue: '确定',
                    ok: function() {}
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        },
        complete: function() {
            parent.hideLoading();
        }
    });
}
//提交审核
$(".submitAudit").click(function() {
    var rowData = $('#X_Table').getRowData();
    if (rowData.length == 0) {
        utils.dialog({ content: '至少添加一种商品', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    if (rowData.length > 200) {
        utils.dialog({ content: '最多添加200条商品', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    if (!checkSubmit()) {
        return false;
    }

    // 补全remarks
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        //补全reasonRemarks和编码变更
        selectRow.reasonRemarks = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="reasonRemarks"] input').val()
        selectRow.replaceEncoding = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="replaceEncoding"] input').val()
        const $remarksSelect = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="remarks"] select')
        selectRow.remarks = $remarksSelect.val()
    }

    let firstHighWoolAreaStr = '';
    let firstHighWoolAreaList = [];
    rowData.forEach(item => {
        if (item.productFirstGm == '1') {
            firstHighWoolAreaList.push(item.productCode)
        }
    });
    if (firstHighWoolAreaList && firstHighWoolAreaList.length > 0) {
        firstHighWoolAreaStr = firstHighWoolAreaList.join();
        // utils.dialog({
        //     title: "提示",
        //     content: `<div style="word-break: break-all">下架单包含
        //                 <span style="color:red">${firstHighWoolAreaList.length}</span>个首推品种
        //                 <span style="color:red">${firstHighWoolAreaStr}</span>，确认提交吗？
        //               </div>`,
        //     width: 340,
        //     okValue: '确定',
        //     cancelValue: '取消',
        //     cancel: function () {},
        //     ok: function () {
        //         submitFuncAjax(rowData);
        //     }
        // }).showModal();
        utils.dialog({
            content: `<div style="word-break: break-all">下架单包含
                        <span style="color:red">${firstHighWoolAreaList.length}</span>个首推品种
                        <span style="color:red">${firstHighWoolAreaStr}</span>，首推高毛专区内商品不允许操作下架！
                      </div>`,
            quickClose: true,
            timeout: 2000
        }).showModal();
        return false
    } else {
        submitFuncAjax(rowData);
    }
});

/**
 * 必填校验
 * @returns {boolean}
 */
function checkSubmit() {
    var rowData = $('#X_Table').getRowData();
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        //补全reasonRemarks和编码变更
        selectRow.reasonRemarks = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="reasonRemarks"] input').val()
        selectRow.replaceEncoding = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="replaceEncoding"] input').val()
            // 补全remarks
        const $remarksSelect = $('#X_Table #' + selectRow.productIdChannel).find('[row-describedby="remarks"] select')
        selectRow.remarks = $remarksSelect.val()

        if (selectRow.lowerShelfReason == "") {
            utils.dialog({ content: '下架原因不能为空', quickClose: true, timeout: 2000 }).showModal();
            return false;
        } else if (selectRow.lowerShelfReason == "6" || selectRow.lowerShelfReason == "7" || selectRow.lowerShelfReason == "8" || selectRow.lowerShelfReason == "9") {
            if (selectRow.remarks == "") {
                utils.dialog({ content: '原因说明不能为空', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
            var content = "";

            // //替换的编码与原编码相同
            // if(selectRow.lowerShelfReason == "2"&&selectRow.remarks!=''&&selectRow.remarks==selectRow.productCode){
            //     content="备注填写的新商品编码不能与商品编码相同，请检查！<br>";
            // }

            //判断是否上传附件
            if (selectRow.lowerShelfReason == "9" && selectRow.enclosureUrl == '') {
                content = '下架原因为质量问题时，必须上传诸如但不限于下列可以证明质量问题的凭证！<br>' +
                    'a、包装问题： 仓库提供凭证采购上传<br>' +
                    'b、厂家召回： 厂家提供凭证采购上传<br>' +
                    'c、质管通知有质量问题 ： 质管提供凭证采购上传';
            }
            if (selectRow.lowerShelfReason == "7" && selectRow.remarks == "其他" && selectRow.reasonRemarks == "") {
                content = '请填写原因备注'
            }
            //判断选择编码变更是否，填写了编码

            if (selectRow.lowerShelfReason == "8" && selectRow.replaceEncoding == "") {
                content = '请填写替换的编码'
            }

            // if(selectRow.lowerShelfReason == "5"&&selectRow.remarks!=''&&selectRow.remarks.length<5){
            //     content =  '下架原因=“其他”时，备注必填，且必须大于等于5个汉字。';
            // }

            if (content != "") {
                utils.dialog({
                    title: '提示',
                    width: 340,
                    height: 100,
                    content: content,
                    okValue: '确定',
                    ok: function() {}
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }
        }
    }
    return true;
}
$("#X_Table").on('click', '.file_btn', function(e) {
    $(e.target).parents('td').find('.file_input').trigger('click');
});
$("#X_Table").on('change', '.file_input', function(e) {
    sendFile(this.files, $(e.target).parents('tr').attr('id'));
});

function sendFile(files, id) {
    var formData = new FormData();
    for (var i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }
    var d = utils.dialog({
        content: '正在上传..'
    }).showModal();
    $.ajax({
        url: '/proxy-sysmanage/upload/upload',
        data: formData,
        type: 'post',
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(res) {
            d.close().remove();
            if (res.code == 0) {
                utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '上传成功'
                }).showModal();
                $("#X_Table").XGrid('setRowData', id, { enclosureUrl: res.result[0] })
            } else {
                utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '上传失败'
                }).showModal();
            }
        },
        error: function() {
            d.close().remove();
        }
    })
}

$("#X_Table").on('click', '.file_a', function(e) {
    var url = $(e.target).attr('data-url');
    var id = $(e.target).parents('tr').attr('id');
    var content = `<div class="changeApplyItem" style="line-height: 50px;text-align: center;">
                    <a style="flex: 1;" href="${url}" target="_blank" class="download">查看下载</a>
                    <a style="flex: 1;" href="javascript:;" class="remove_file" data-id="${id}">删除</a>
                </div>`;
    utils.dialog({
        align: 'top',
        width: 130,
        height: 50,
        padding: 2,
        content: content,
        quickClose: true
    }).show(this);
});
$("body").on('click', '.remove_file', function(e) {
    $("#X_Table").XGrid('setRowData', $(e.target).attr('data-id'), { enclosureUrl: '' });
    $(e.target).parents('.ui-popup').hide();
    $(".ui-popup-backdrop").hide();
});
$("#X_Table").on('change', '.lowerShelfReason', function(e) {
    const $remarksSelector = $(e.target).parents('tr').find('[row-describedby="remarks"] select');
    //清空remarks的值，移除option
    $remarksSelector.val('').find('option:not(:first)').remove();
    //组装原因说明
    const reasonIndex = $(this).val();
    if (reasonIndex == 8) {
        $('#X_Table').setGridParam({
            colModel: [{
                name: 'replaceEncoding',
                index: 'replaceEncoding',
                width: 200,
                hidden: false,
                formatter: (a, b, arr) => {
                    return "<div style='display: flex;justify-content: center;'><input type='text' onclick='selectProduct(this)' class='form-control applyUpperShelf' id='replaceEncoding' name='replaceEncoding' maxlength='200'><div style='background-color: black;color: white;border-radius: 50%;width: 20px ;height: 20px;text-align: center;' onmouseenter='replaceEncodingTip(this)'  onmouseleave='questionTip.close().remove()'   specifications=''    productName=''     manufacturerName=''  >?</div> </div>"
                },

            }]
        })

        var $reasonRemarks = $(e.target).parents('tr').find('[row-describedby="reasonRemarks"] #reasonRemarks')
            //当选择编码时，禁用原因备注框
        $reasonRemarks.attr("disabled", "disabled")
        $reasonRemarks.val("")

    } else {
        $(e.target).parents('tr').find('[row-describedby="reasonRemarks"] #reasonRemarks').removeAttr('disabled');

        $(e.target).parents('tr').find('[row-describedby="replaceEncoding"] #replaceEncoding').val("")

    }
    let optionElement = [];
    if (reasonIndex) {
        const remarksOption = remarksOptionsAdapter(reasonIndex)
        remarksOption.forEach(optionItem => {
            optionElement.push(`<option value="${optionItem.value}">${optionItem.label}</option>`)
        });
    }
    if (optionElement.length) {
        $remarksSelector.append(optionElement.join(''));
    }
});
//?提示框
var questionTip;

function replaceEncodingTip(e) {
    questionTip = utils.dialog({
        content: $(e).prev().val() + e.getAttribute('productname') + "<br>" + e.getAttribute('specifications') + "<br>" + e.getAttribute('manufacturername'),
    })
    questionTip.show(e)

}
//选择替换编码商品
function selectProduct(e) {
    const $remarksSelector = $(e).parents('tr').find('[row-describedby="lowerShelfReason"] select');
    console.log($remarksSelector.val())
    if ($remarksSelector.val() != 8) {
        var d = utils.dialog({
            content: '仅编码变更可选'
        });
        d.show();
        setTimeout(function() {
            d.close().remove();
        }, 2000);

    } else {
        var resultArr = $('#X_Table').getRowData();
        dialog({
            url: '/proxy-product/product/lowerShelf/toProductCodeQuery',
            title: '商品列表',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.8,
            data: {
                "orgCode": $("#recordOrgCode").val(),
                channelId: (resultArr.length != 0) ? resultArr[0].channelId : '',
                channelName: (resultArr.length != 0) ? resultArr[0].channelVal : ''
            }, // 给modal 要传递的 的数据
            onclose: function(data) {
                console.log(this.returnValue)

                if (this.returnValue.resultArr) {
                    var data = this.returnValue.resultArr[0]

                    /* if (this.returnValue.resultArr.length - 1 >= 0) {
                        var data = this.returnValue.resultArr[this.returnValue.resultArr.length - 1];
                        $(e).val(data.productCode)
                        console.log(data)
                    } */

                    $(e).val(data.productCode)
                    let $EncodingTip = $(e).next()
                    $EncodingTip.attr("productname", data.productName)
                    $EncodingTip.attr("specifications", data.specifications)
                    $EncodingTip.attr("manufacturername", data.manufacturerName)
                }

            }
        }).showModal();
    }
}

function enterReasonRemarks(e) {
    showReasonRemarks = utils.dialog({
            content: $(e).val()
        }).show(e)
        //实时判断鼠标在不在备注原因上
    nowIsReasonRemarks = true

}

function leaveReasonRemarks(e) {
    showReasonRemarks.close().remove()
    nowIsReasonRemarks = false
}