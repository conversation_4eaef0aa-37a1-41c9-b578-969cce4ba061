$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);

    /* 填入初始数据 */
    /*  $('#form_a').JSONToform({
        val_a: '举个栗子'
      })*/


    /* 审批流 */
    /**
     * 流程图显示
     */
        //根据流程实例ID加载流程图
    var processInstaId = $("#processId").val();
    initApprovalFlowChart(processInstaId);

    function initApprovalFlowChart(processInstaId) {
        //获取审核流程数据
        $.ajax({
            type: "POST",
            url: "/proxy-storage/storage/adjustment/queryProcessHistory",
            dataType: "json",
            data: {processInstaId: processInstaId},
            success: function (data) {
                if (data.code == 0 && data.result != null) {
                    console.log(data.result);
                    $('.flow').html("")
                    $('.flow').process(data.result);
                }
            },
            error: function () {
            }
        });
    }

    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    /* table_a */
    var colName = ['id', '商品编码','商品原编码', '商品名称', '商品规格', '生产厂家', '产地', '单位', '库房名称', '批号', '生产日期', '有效期至', '调账数量',
       '不含税成本金额','业务类型'
    ];
    // '不含税成本单价',
    var colModel = [
        {
            name: 'id',
            index: 'id',
            hidden: true,
            hidegrid: true
        },{
            name: 'productCode',
            index: 'productCode'
        },{
            name: 'oldProductCode',
            index: 'oldProductCode'
        },{
            name: 'productName',
            index: 'productName'
        }, {
            name: 'specifications',
            index: 'specifications'
        }, {
            name: 'manufacturerValue',
            index: 'manufacturerValue'
        }, {
            name: 'producingArea',
            index: 'producingArea'
        }, {
            name: 'packingUnitValue',
            index: 'packingUnitValue'
        }, {
            name: 'storeName',
            index: 'storeName',
            formatter: function (e) {
                if(!re.test(e)){
                    return e;
                }
                var result = "";
                $.each(jsonStore,function(idx,item){
                    if(item.numCode == e){
                        result = item.name;
                        return false;
                    }
                });
                return result;
            }
        }, {
            name: 'batchCode',
            index: 'batchCode'

        }, {
            name: 'productDateStr',
            index: 'productDateStr'

        }, {
            name: 'validateDateStr',
            index: 'validateDateStr'

        }, {
            name: 'adjustmentNumber',
            index: 'adjustmentNumber'
        },  {
            name: 'costAmount',
            index: 'costAmount'
        },
        {
            name: 'channelId',
            index: 'channelId'
        }


        // {
        //     name: 'costPrice',
        //     index: 'costPrice',
        //     formatter: function (e) {
        //         if (e == '' || e == undefined || isNaN(e)) {
        //             return '0'
        //         } else  {
        //             return Number(e).toFixed(2)
        //         }
        //
        //     }
        // },

    ];
    $('#table_a').XGrid({
        url: '/proxy-storage/storage/adjustment/findDetailList',
        colNames: colName,
        colModel: colModel,
        postData: {adjustmentApplyCode: $("#adjustmentApplyCode").val(), orgCode: $("#orgCode").val()},
        rownumbers: true,
        key: 'id',
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',

        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });


    /* 筛选列 */
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })

    /* 审核通过 */
    $('#check_pass').on('click', function () {

        utils.dialog({
            title: '审核通过',
            content: $('#check_pass_msg'),
            okValue: '确定',
            ok: function () {

                if(!validform('check_pass_form').form()) return false;

                $.ajax({
                    type: "POST",
                    url: "/proxy-storage/storage/adjustment/commitProcess",
                    dataType:"json",
                    data:{approveResult :1,id:$("#businessId").val(),taskId:$("#taskId").val(),approveMessage:$("#remark").val()},
                    async: false,
                    success: function (data) {
                        if(data.code==1){
                            utils.dialog({
                                title: '提示',
                                width: 200,
                                height: 40,
                                content: data.msg,
                                okValue: '确定',
                                ok: function () {
                                    return ;
                                }

                            }).showModal();
                        }else{
                            utils.dialog({
                                title: '提示',
                                width: 200,
                                height: 40,
                                content: '审核成功',
                                cancel: false,
                                okValue: '确定',
                                ok: function () {
                                    utils.closeTab();
                                }


                            }).showModal()


                        }
                    },
                    error: function () {}
                });
            },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal()






});


    /* 审核驳回 */
    $('#check_nopass').on('click', function () {
        utils.dialog({
            title: '审核驳回',
            content: $('#check_nopass_msg'),
            okValue: '确定',
            ok: function () {
                if(!validform('check_nopass_form').form()) return false;
                $.ajax({
                    type: "POST",
                    url: "/proxy-storage/storage/adjustment/commitProcess",
                    dataType:"json",
                    data:{approveResult:2,id:$("#businessId").val(),taskId:$("#taskId").val(),approveMessage:$("#remarks").val()},
                    async: false,
                    success: function (data) {
                        if (data.code == 1) {
                            utils.dialog({
                                title: '提示',
                                width: 200,
                                height: 40,
                                content: data.msg,
                                okValue: '确定',
                                ok: function () {
                                    return;
                                }

                            }).showModal()
                        } else {
                            utils.dialog({
                                title: '提示',
                                width: 200,
                                height: 40,
                                content: '驳回成功',
                                cancel: false,
                                okValue: '确定',
                                ok: function () {
                                    utils.closeTab();
                                }
                            }).showModal()


                        }
                    },
                    error: function () {
                    }
                });
            },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal()
    });

    /* 审核驳回 */
    $('#closeTab').on('click', function () {
        utils.closeTab()
    })


})