$(function () {
   
    $('#X_Tableb').XGrid({
       /* url:"/supplierOrganBase/supplierBase/ajaxSupplierBaseList?minRemainDays="+$("#minRemainDays").val(),*/
        url:"/proxy-supplier/supplier/supplierOrganBase/supplierBase/ajaxSupplierBaseList",
        colNames: ['', '供应商编码', '供应商名称', '供应商类型', '营业执照号','审核状态','是否停用'/*,'剩余效期'*/],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                hidden:true
            }, {
                name: 'supplierCode',
                index: 'supplierCode',
                width: 200

            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 60
            }, {
                name: 'supplierTypeName',
                index: 'supplierTypeName',
                width: 150
            }, {
                name: 'supplierBusinessNum',
                index: 'supplierBusinessNum',
                width: 250
            }, {
                name: 'auditStatusName',
                index: 'auditStatusName',
                width: 250
            }, {
                name: 'disableStateBase',
                index: 'disableStateBase',
                formatter:function(value){
                	if(value==0){
                		return "否"
                	}else{
                		return "是"
                	}
                	
                },
                width: 250
            }
           /* , {
                name: 'remainDays',
                index: 'remainDays',
                width: 250,
                formatter:function (value,a,rowData) {
                    if(value || value==0)
                    {
                        var id=rowData.id;
                        setTimeout(function () {
                            if(0 < value && value < 180){
                                $("#"+id).css({
                                    "background":"#FFF9DD",
                                });
                            }else if(value <= 0){
                                $("#"+id).css({
                                    "background":"#FDE5E5",
                                });
                            }
                        },0);

                        return ''+value+'';
                    }else{
                        return '无';
                    }
                }
            }*/

        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            var url = "/proxy-supplier/supplier/supplierOrganBase/supplierBase/toDetail?id="+id;
    		utils.openTabs("supplierBaseToDetail", "供应商主数据详情 ", url)	
           
        },
    });

  
    
    
    $("#SearchBtn").on("click", function () {
    	var a = $("#minRemainDays").val();
        $('#X_Tableb').XGrid('setGridParam', {
        	url:"/proxy-supplier/supplier/supplierOrganBase/supplierBase/ajaxSupplierBaseList",
            postData: {
                "queryFields": $("#queryFields").val(),
                "auditStatus":$("#auditStatus").val(),
                "supplierTypeId":$("#supplierTypeId").val(),
               /* "maxRemainDays":$("#maxRemainDays").val(),
                "minRemainDays":$("#minRemainDays").val(),*/
                "disableStateBase":$("#disableStateBase").val()
            },page:1
        }).trigger('reloadGrid');
    });

    
    $.ajax({
       	url:'/proxy-sysmanage/sysmanage/dict/querycommonnotpage?type=8',
       	type:"post",
       	async:false,
       	dataType:'json',
       	success:function(data){
       		console.log("供应商类别："+data.result);
       		var html = '';
	      $(data.result).each(function(index,item){
	    	  html +=  '<option value="'+item.id+'">&nbsp;'+item.name+'</option>'
	      });
       	   $(".supplierTypeClass").append(html);
       		
       	},
       	error:function(){
       		 utils.dialog({content: '请求失败', quickClose: true, timeout: 2000}).showModal();
       	}
       });

})