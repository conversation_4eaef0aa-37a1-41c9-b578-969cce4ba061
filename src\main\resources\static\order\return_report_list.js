// $('#cold-table').hide();
// // 展示经营范围列的表头
// var businessScopeTableColumns = ['通用名称（商品名称）', '规格/型号', '剂型', '单位','生产厂家', '商品产地','所属经营范围', '购货单位名称', '数量', '含税单价', '价税合计', '到货日期',
//     '批号','生产日期','有效期至', '灭菌批号','批准文号/注册证号/备案凭证号', '商品大类','存储条件', '销售员', '业务类型'];
// // 展示不含经营范围列的表头
// var noBusinessScopeTableColumns = ['通用名称（商品名称）', '规格/型号', '剂型', '单位','生产厂家', '商品产地', '购货单位名称', '数量', '含税单价', '价税合计', '到货日期',
//     '批号','生产日期','有效期至', '灭菌批号','批准文号/注册证号/备案凭证号', '商品大类','存储条件', '销售员', '业务类型'];
// // 展示经营范围列的表模型
// var businessScopeColModel = [
//     {name: 'productName',index: 'productName'},
//     {name: 'specifications',index: 'specifications'},
//     {name: 'dosageForm',index: 'dosageForm'},
//     {name: 'productUnit',index: 'productUnit'},
//     {name: 'manufacturer',index: 'manufacturer'},
//     {name: 'productOrigin',index: 'productOrigin'},
//     {name: 'scopeOfOperation',index: 'scopeOfOperation'},
//     {name: 'customerName',index: 'customerName'},
//     {name: 'returnsNumber',index: 'returnsNumber'},
//     {name: 'taxPrice',index: 'taxPrice'},
//     {name: 'taxAmount',index: 'taxAmount',
//         formatter: function (val) {
//             return parseFloat(val).formatMoney('2', '', ',' ,'.');
//         },
//         unformat: function (val) {
//             return val.replace(/,/g ,'');
//         },
//     },
//     {name: 'collectGoodsTime',index: 'collectGoodsTime',
//         formatter:function (value){
//             if(value){
//                 return moment(value).format('YYYY-MM-DD');
//             }else{
//                 return ''
//             }
//         }
//     },
//     {name: 'batchCode',index: 'batchCode'},
//     {name: 'productionTime',index: 'productionTime',
//         formatter:function (value){
//             if(value){
//                 return moment(value).format('YYYY-MM-DD');
//             }else{
//                 return ''
//             }
//         }
//     },
//     {name: 'periodValidity',index: 'periodValidity',
//         formatter:function (value){
//             if(value){
//                 return moment(value).format('YYYY-MM-DD');
//             }else{
//                 return ''
//             }
//         }
//     },
//     {name: 'sterilizationCode',index: 'sterilizationCode'},
//     {name: 'approvalNumber',index: 'approvalNumber', width: 200 },
//     {name: 'drugClass',index: 'drugClass'},
//     {name: 'storageConditions',index: 'storageConditions'},
//     {name: 'salesPersonName',index: 'salesPersonName'}
//     ,
//     {name: 'channelId',index: 'channelId'}
// ];
// // 展示不含经营范围列的表头
// var noBusinessScopeColModel =    [
//     {name: 'productName',index: 'productName'},
//     {name: 'specifications',index: 'specifications'},
//     {name: 'dosageForm',index: 'dosageForm'},
//     {name: 'productUnit',index: 'productUnit'},
//     {name: 'manufacturer',index: 'manufacturer'},
//     {name: 'productOrigin',index: 'productOrigin'},
//     {name: 'customerName',index: 'customerName'},
//     {name: 'returnsNumber',index: 'returnsNumber'},
//     {name: 'taxPrice',index: 'taxPrice'},
//     {name: 'taxAmount',index: 'taxAmount',
//         formatter: function (val) {
//             return parseFloat(val).formatMoney('2', '', ',' ,'.');
//         },
//         unformat: function (val) {
//             return val.replace(/,/g ,'');
//         },
//     },
//     {name: 'collectGoodsTime',index: 'collectGoodsTime',
//         formatter:function (value){
//             if(value){
//                 return moment(value).format('YYYY-MM-DD');
//             }else{
//                 return ''
//             }
//         }
//     },
//     {name: 'batchCode',index: 'batchCode'},
//     {name: 'productionTime',index: 'productionTime',
//         formatter:function (value){
//             if(value){
//                 return moment(value).format('YYYY-MM-DD');
//             }else{
//                 return ''
//             }
//         }
//     },
//     {name: 'periodValidity',index: 'periodValidity',
//         formatter:function (value){
//             if(value){
//                 return moment(value).format('YYYY-MM-DD');
//             }else{
//                 return ''
//             }
//         }
//     },
//     {name: 'sterilizationCode',index: 'sterilizationCode'},
//     {name: 'approvalNumber',index: 'approvalNumber', width: 200 },
//     {name: 'drugClass',index: 'drugClass'},
//     {name: 'storageConditions',index: 'storageConditions'},
//     {name: 'salesPersonName',index: 'salesPersonName'}
//     ,
//     {name: 'channelId',index: 'channelId'}
// ];
var colNames;
var colModel;
var currentTabIndex = '1'
returnColNamesAndModel("1")

var type = 1;
var tableId = '#X_Table';
var printBoxId = '#print_box';
var url = "/proxy-order/order/orderReturn/orderReturnController/getReportList";
$("#cold_chain_table_box").hide();
$("#medicine_chain_table_box").hide();
$("#special_chain_table_box").hide();
$("#apparatus_chain_table_box").hide();
$("#non_drug_chain_table_box").hide();



function returnColNamesAndModel(index) {
    switch (String(index)) {
        case '1':
        case '3':
            colNames = ['收货日期', '客户编码', '购货单位名称', '商品编码', '通用名称', '剂型', '规格', '批准文号', '生产厂家', '上市许可持有人', '包装单位', '数量', '批号', '生产日期', '有效期至', '单价', '金额', '退货原因',
                //'收货员',
                '商品大类', '所属经营范围', '存储条件'
            ]
            colModel = [{
                    name: 'collectGoodsTime',
                    index: 'collectGoodsTime',
                    width: 200 //宽度
                        ,
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    }
                },
                {
                    name: 'customerCode',
                    index: 'customerCode',
                    width: 200 //宽度

                }, {
                    name: 'customerName',
                    index: 'customerName',
                    width: 200 //宽度

                }, {
                    name: 'productCode',
                    index: 'productCode',
                    width: 200 //宽度

                }, {
                    name: 'productName',
                    index: 'productName',
                    width: 200 //宽度

                }, {
                    name: 'dosageForm',
                    index: 'dosageForm',
                    width: 200 //宽度

                }, {
                    name: 'specifications',
                    index: 'specifications',
                    width: 200 //宽度

                },
                {
                    name: 'approvalNumber',
                    index: 'approvalNumber',
                    width: 200 //宽度

                },
                {
                    name: 'manufacturer',
                    index: 'manufacturer',
                    width: 200 //宽度

                },
                {
                    name: 'marketAuthor',
                    index: 'marketAuthor',
                    width: 200 //宽度

                }, {
                    name: 'productUnit',
                    index: 'productUnit',
                    width: 200 //宽度

                }, {
                    name: 'returnsNumber',
                    index: 'returnsNumber',
                    width: 200 //宽度

                },
                {
                    name: 'batchCode',
                    index: 'batchCode',
                    width: 200 //宽度
                }, {
                    name: 'productionTime',
                    index: 'productionTime',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'periodValidity',
                    index: 'periodValidity',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'taxPrice',
                    index: 'taxPrice',
                    width: 200 //宽度

                }, {
                    name: 'taxAmount',
                    index: 'taxAmount',
                    formatter: function(val) {
                        return parseFloat(val).formatMoney('2', '', ',', '.');
                    },
                    unformat: function(val) {
                        return val.replace(/,/g, '');
                    },
                    width: 200 //宽度

                }, {
                    name: 'returnReason',
                    index: 'returnReason',
                    width: 200 //宽度

                },
                //{
                //    name: 'receivePerson',
                //    index: 'receivePerson',
                //    width: 200 //宽度
                //},
                {
                    name: 'drugClass',
                    index: 'drugClass',
                    width: 200 //宽度

                }, {
                    name: 'scopeOfOperation',
                    index: 'scopeOfOperation',
                    width: 200 //宽度

                }, {
                    name: 'storageConditions',
                    index: 'storageConditions',
                    width: 200 //宽度

                }
            ]
            break
        case '2':
            colNames = ['收货日期', '客户编码', '购货单位名称', '运输方式', '到货温度', '到货时间', '商品编码', '通用名称', '剂型', '规格', '批准文号', '生产厂家', '上市许可持有人', '包装单位', '数量', '批号', '生产日期', '有效期至', '单价', '金额', '退货原因',
                //'收货员',
                '商品大类', '所属经营范围', '存储条件', '承运单位', '温控方式', '启运时间'
            ]
            colModel = [{
                    name: 'collectGoodsTime',
                    index: 'collectGoodsTime',
                    width: 200 //宽度
                        ,
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    }
                },
                {
                    name: 'customerCode',
                    index: 'customerCode',
                    width: 200 //宽度

                }, {
                    name: 'customerName',
                    index: 'customerName',
                    width: 200 //宽度

                }, {
                    name: 'transportTools',
                    index: 'transportTools',
                    width: 200 //宽度

                }, {
                    name: 'arriveTemperature',
                    index: 'arriveTemperature',
                    width: 200 //宽度
                },
                {
                    name: 'arriveTime',
                    index: 'arriveTime',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'productCode',
                    index: 'productCode',
                    width: 200 //宽度
                }, {
                    name: 'productName',
                    index: 'productName',
                    width: 200 //宽度
                },
                {
                    name: 'dosageForm',
                    index: 'dosageForm',
                    width: 200 //宽度
                }, {
                    name: 'specifications',
                    index: 'specifications',
                    width: 200 //宽度

                },
                {
                    name: 'approvalNumber',
                    index: 'approvalNumber',
                    width: 200 //宽度

                },
                {
                    name: 'manufacturer',
                    index: 'manufacturer',
                    width: 200 //宽度

                },
                {
                    name: 'marketAuthor',
                    index: 'marketAuthor',
                    width: 200 //宽度

                }, {
                    name: 'productUnit',
                    index: 'productUnit',
                    width: 200 //宽度

                }, {
                    name: 'returnsNumber',
                    index: 'returnsNumber',
                    width: 200 //宽度

                },
                {
                    name: 'batchCode',
                    index: 'batchCode',
                    width: 200 //宽度
                }, {
                    name: 'productionTime',
                    index: 'productionTime',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'periodValidity',
                    index: 'periodValidity',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'taxPrice',
                    index: 'taxPrice',
                    width: 200 //宽度

                }, {
                    name: 'taxAmount',
                    index: 'taxAmount',
                    formatter: function(val) {
                        return parseFloat(val).formatMoney('2', '', ',', '.');
                    },
                    unformat: function(val) {
                        return val.replace(/,/g, '');
                    },
                    width: 200 //宽度
                }, {
                    name: 'returnReason',
                    index: 'returnReason',
                    width: 200 //宽度

                },
                //{
                //   name: 'receivePerson',
                //  index: 'receivePerson',
                //   width: 200 //宽度
                //},
                {
                    name: 'drugClass',
                    index: 'drugClass',
                    width: 200 //宽度

                }, {
                    name: 'scopeOfOperation',
                    index: 'scopeOfOperation',
                    width: 200 //宽度

                }, {
                    name: 'storageConditions',
                    index: 'storageConditions',
                    width: 200 //宽度

                }, {
                    name: 'carrier',
                    index: 'carrier',
                    width: 200 //宽度

                },
                {
                    name: 'temperatureControlMode',
                    index: 'temperatureControlMode',
                    width: 200 //宽度

                }, {
                    name: 'departureTime',
                    index: 'departureTime',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }
            ]
            break;
        case '4':
            colNames = ['收货日期', '客户编码', '购货单位名称', '商品编码', '品名', '规格', '批准文号', '生产厂家', '产地', '上市许可持有人', '包装单位', '数量', '批号', '生产日期', '有效期至', '单价', '金额', '退货原因',
                //'收货员',
                '商品大类', '所属经营范围', '存储条件'
            ]
            colModel = [{
                    name: 'collectGoodsTime',
                    index: 'collectGoodsTime',
                    width: 200 //宽度
                        ,
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    }
                },
                {
                    name: 'customerCode',
                    index: 'customerCode',
                    width: 200 //宽度

                }, {
                    name: 'customerName',
                    index: 'customerName',
                    width: 200 //宽度

                }, {
                    name: 'productCode',
                    index: 'productCode',
                    width: 200 //宽度

                }, {
                    name: 'productName',
                    index: 'productName',
                    width: 200 //宽度

                }, {
                    name: 'specifications',
                    index: 'specifications',
                    width: 200 //宽度

                },
                {
                    name: 'approvalNumber',
                    index: 'approvalNumber',
                    width: 200 //宽度

                },
                {
                    name: 'manufacturer',
                    index: 'manufacturer',
                    width: 200 //宽度

                },
                {
                    name: 'productOrigin',
                    index: 'productOrigin',
                    width: 200 //宽度
                },
                {
                    name: 'marketAuthor',
                    index: 'marketAuthor',
                    width: 200 //宽度

                }, {
                    name: 'productUnit',
                    index: 'productUnit',
                    width: 200 //宽度

                }, {
                    name: 'returnsNumber',
                    index: 'returnsNumber',
                    width: 200 //宽度

                },
                {
                    name: 'batchCode',
                    index: 'batchCode',
                    width: 200 //宽度
                }, {
                    name: 'productionTime',
                    index: 'productionTime',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'periodValidity',
                    index: 'periodValidity',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'taxPrice',
                    index: 'taxPrice',
                    width: 200 //宽度

                }, {
                    name: 'taxAmount',
                    index: 'taxAmount',
                    formatter: function(val) {
                        return parseFloat(val).formatMoney('2', '', ',', '.');
                    },
                    unformat: function(val) {
                        return val.replace(/,/g, '');
                    },
                    width: 200 //宽度

                }, {
                    name: 'returnReason',
                    index: 'returnReason',
                    width: 200 //宽度

                },
                //{
                //    name: 'receivePerson',
                //    index: 'receivePerson',
                //    width: 200 //宽度
                //},
                {
                    name: 'drugClass',
                    index: 'drugClass',
                    width: 200 //宽度

                }, {
                    name: 'scopeOfOperation',
                    index: 'scopeOfOperation',
                    width: 200 //宽度

                }, {
                    name: 'storageConditions',
                    index: 'storageConditions',
                    width: 200 //宽度

                }
            ]
            break;
        case '5':
            colNames = ['收货日期', '客户编码', '购货单位名称', '商品编码', '通用名称', '剂型', '规格/型号', '注册证号/备案凭证号', '生产厂家', '上市许可持有人', '包装单位', '数量', '批号/序列号', '灭菌批号', '生产日期', '有效期至/失效期', '单价', '金额', '退货原因',
                //'收货员',
                '商品大类', '所属经营范围','医疗器械注册人/备案人名称','受托生产企业名称','UDI','原出库单号','退货原因', '存储条件'
            ]
            colModel = [{
                    name: 'collectGoodsTime',
                    index: 'collectGoodsTime',
                    width: 200 //宽度
                        ,
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    }
                },
                {
                    name: 'customerCode',
                    index: 'customerCode',
                    width: 200 //宽度

                }, {
                    name: 'customerName',
                    index: 'customerName',
                    width: 200 //宽度

                }, {
                    name: 'productCode',
                    index: 'productCode',
                    width: 200 //宽度

                }, {
                    name: 'productName',
                    index: 'productName',
                    width: 200 //宽度

                }, {
                    name: 'dosageForm',
                    index: 'dosageForm',
                    width: 200 //宽度

                }, {
                    name: 'specifications',
                    index: 'specifications',
                    width: 200 //宽度

                },
                {
                    name: 'approvalNumber',
                    index: 'approvalNumber',
                    width: 200 //宽度

                },
                {
                    name: 'manufacturer',
                    index: 'manufacturer',
                    width: 200 //宽度

                },
                {
                    name: 'marketAuthor',
                    index: 'marketAuthor',
                    width: 200 //宽度

                }, {
                    name: 'productUnit',
                    index: 'productUnit',
                    width: 200 //宽度

                }, {
                    name: 'returnsNumber',
                    index: 'returnsNumber',
                    width: 200 //宽度

                },
                {
                    name: 'batchCode',
                    index: 'batchCode',
                    width: 200 //宽度
                }, {
                    name: 'sterilizationCode',
                    index: 'sterilizationCode',
                    width: 200 //宽度
                }, {
                    name: 'productionTime',
                    index: 'productionTime',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'periodValidity',
                    index: 'periodValidity',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'taxPrice',
                    index: 'taxPrice',
                    width: 200 //宽度

                }, {
                    name: 'taxAmount',
                    index: 'taxAmount',
                    formatter: function(val) {
                        return parseFloat(val).formatMoney('2', '', ',', '.');
                    },
                    unformat: function(val) {
                        return val.replace(/,/g, '');
                    },
                    width: 200 //宽度

                }, {
                    name: 'returnReason',
                    index: 'returnReason',
                    width: 200 //宽度

                },
                //{
                //    name: 'receivePerson',
                //    index: 'receivePerson',
                //    width: 200 //宽度
                //},
                {
                    name: 'drugClass',
                    index: 'drugClass',
                    width: 200 //宽度

                }, {
                    name: 'scopeOfOperation',
                    index: 'scopeOfOperation',
                    width: 200 //宽度

                },{
                    name: 'filingsAuthor', //医疗器械注册人/备案人名称
                    index: 'filingsAuthor',
                    width: 200 //宽度
                },{
                    name: 'entrustedManufacturer',//受托生产企业名称
                    index: 'entrustedManufacturer',
                    width: 200 //宽度
                },{
                    name: 'udi',//UDI
                    index: 'udi',
                    width: 200 //宽度
                },{
                    name: 'outOrderCode',//原出库单号
                    index: 'outOrderCode',
                    width: 200 //宽度
                },{
                    name: 'returnReason',//退货原因
                    index: 'returnReason',
                    width: 200 //宽度
                }, {
                    name: 'storageConditions',
                    index: 'storageConditions',
                    width: 200 //宽度

                }
            ]
            break;
        case '6':
            colNames = ['收货日期', '客户编码', '购货单位名称', '商品编码', '通用名称', '规格', '批准文号', '生产厂家', '包装单位', '数量', '批号', '生产日期', '有效期至', '单价', '金额', '退货原因',
                //'收货员',
                '商品大类', '所属经营范围', '存储条件'
            ]
            colModel = [{
                    name: 'collectGoodsTime',
                    index: 'collectGoodsTime',
                    width: 200 //宽度
                        ,
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    }
                },
                {
                    name: 'customerCode',
                    index: 'customerCode',
                    width: 200 //宽度

                }, {
                    name: 'customerName',
                    index: 'customerName',
                    width: 200 //宽度

                }, {
                    name: 'productCode',
                    index: 'productCode',
                    width: 200 //宽度

                }, {
                    name: 'productName',
                    index: 'productName',
                    width: 200 //宽度

                }, {
                    name: 'specifications',
                    index: 'specifications',
                    width: 200 //宽度

                },
                {
                    name: 'approvalNumber',
                    index: 'approvalNumber',
                    width: 200 //宽度

                },
                {
                    name: 'manufacturer',
                    index: 'manufacturer',
                    width: 200 //宽度

                }, {
                    name: 'productUnit',
                    index: 'productUnit',
                    width: 200 //宽度

                }, {
                    name: 'returnsNumber',
                    index: 'returnsNumber',
                    width: 200 //宽度

                },
                {
                    name: 'batchCode',
                    index: 'batchCode',
                    width: 200 //宽度
                }, {
                    name: 'productionTime',
                    index: 'productionTime',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'periodValidity',
                    index: 'periodValidity',
                    formatter: function(value) {
                        if (value) {
                            return moment(value).format('YYYY-MM-DD');
                        } else {
                            return ''
                        }
                    },
                    width: 200 //宽度

                }, {
                    name: 'taxPrice',
                    index: 'taxPrice',
                    width: 200 //宽度

                }, {
                    name: 'taxAmount',
                    index: 'taxAmount',
                    formatter: function(val) {
                        return parseFloat(val).formatMoney('2', '', ',', '.');
                    },
                    unformat: function(val) {
                        return val.replace(/,/g, '');
                    },
                    width: 200 //宽度

                }, {
                    name: 'returnReason',
                    index: 'returnReason',
                    width: 200 //宽度

                },
                //{
                //    name: 'receivePerson',
                //    index: 'receivePerson',
                //    width: 200 //宽度
                //},
                {
                    name: 'drugClass',
                    index: 'drugClass',
                    width: 200 //宽度

                }, {
                    name: 'scopeOfOperation',
                    index: 'scopeOfOperation',
                    width: 200 //宽度

                }, {
                    name: 'storageConditions',
                    index: 'storageConditions',
                    width: 200 //宽度

                }
            ]
            break
    }
}
// 初始化 两个 table
function allColModelA() {
    return JSON.parse(JSON.stringify(colModel));
}
// 筛选列弹窗内容
function addHtmlA(array) {
    $("div").remove('#setCol');
    if (!$('#setCol')[0]) {
        var s = '<div id="setCol" style="display: none;">' +
            '    <div class="row" id="checkRow">';
        for (var i = 0; i < array.length; i++) {
            s += '<div class="col-md-3">' +
                '            <div class="checkbox">' +
                '                <label>' +
                '                    <input style="margin-right: 10px" checked type="checkbox" name="' + array[i] + '">' + array[i] +
                '                </label>' +
                '            </div>' +
                '        </div>';

        }

        s += '</div></div>';
        $("body").append(s);
    }
}
//发送POST请求跳转到指定页面
function httpPost(URL, PARAMS) {
    var temp = document.createElement("form");
    temp.action = URL;
    temp.method = "post";
    temp.style.display = "none";
    for (var x in PARAMS) {
        var opt = document.createElement("textarea");
        opt.name = x;
        opt.value = PARAMS[x];
        temp.appendChild(opt);
    }
    document.body.appendChild(temp);
    temp.submit();

    return temp;
}

function specialForWuHanJiangSuZhengZhou() {
    var orgCode = $("#orgCode").val()
    var specialOrg = ['013', '002', '006']
    if (specialOrg.indexOf(orgCode) == -1) {
        return false
    }
    return true
}
// function getCurrentTabIndex() {
//     return currentTabIndex;
// }
// function isSpecial() {
//     return getCurrentTabIndex() == '2'
// }
// function isColdChainLedger() {
//     return getCurrentTabIndex() == '3'
// }

function getType() {
    console.log(currentTabIndex)
    return currentTabIndex
}
//销售记录   tab 切换

$('.leder_tabs>li').on('click', function() {
    var $this = $(this);
    $this.addClass('active').siblings().removeClass('active');
    currentTabIndex = $this.attr('tabindex')
    if ($this.index() != 2) {
        sessionStorage.setItem('leder_ind', '-1');
    }
    initTable(currentTabIndex)
});

function getCurrentTableId() {
    var str = "#X_Table"
    switch (String(currentTabIndex)) {
        case "2":
            str = '#X_Table_1'
            break;
        case "3":
            str = '#X_Table_2'
            break;
        case '4':
            str = '#X_Table_3'
            break;
        case '5':
            str = '#X_Table_4'
            break;
        case '6':
            str = '#X_Table_5'
            break;
    }
    return str
}
function getCurrentTableDiv() {
    var str = "#normal_table_box"
    switch (String(currentTabIndex)) {
        case "2":
            str = '#cold_chain_table_box'
            break;
        case "3":
            str = '#medicine_chain_table_box'
            break;
        case '4':
            str = '#special_chain_table_box'
            break;
        case '5':
            str = '#apparatus_chain_table_box'
            break;
        case '6':
            str = '#non_drug_chain_table_box'
            break;
    }
    return str
}

function initTable(index, boole) {
    //获取form数据
    var data = $('#form_a').serializeToJSON();
    if (!boole) {
        $("#productCode").val('');
        $("#productName").val('');
        $("#customerName").val('');
        $("#simpleCode").val('');
        $("#scopeName").val('');
        $("#drugClass").val("");
        $("#storageType").val("");
    }
    $("#storageConditions").val('');
    $("#normal_table_box").hide();
    $("#cold_chain_table_box").hide();
    $("#medicine_chain_table_box").hide();
    $("#special_chain_table_box").hide();
    $("#apparatus_chain_table_box").hide();
    $("#non_drug_chain_table_box").hide();
    returnColNamesAndModel(currentTabIndex);
    tableId = getCurrentTableId();
    let tableDiv = getCurrentTableDiv();
    switch (String(index)) {
        case '1':
            $("#drugClass").attr("disabled", true)
            $("#storageType").attr("disabled", false)
            $("#normal_table_box").show();
            break
        case "3":
            $("#drugClass").attr("disabled", true)
            $("#storageType").attr("disabled", false)
            $("#medicine_chain_table_box").show();
            break;
        case "4":
            $("#drugClass").attr("disabled", true)
            $("#storageType").attr("disabled", false)
            $("#special_chain_table_box").show();
            break;
        case '5':
            $("#drugClass").attr("disabled", true)
            $("#storageType").attr("disabled", false)
            $("#apparatus_chain_table_box").show();
            break;
        case '6':
            $("#drugClass").attr("disabled", false)
            $("#non_drug_chain_table_box").show();
            break;
        case '2':
            $("#drugClass").attr("disabled", false)
            $("#storageType").attr("disabled", true)
            $("#cold_chain_table_box").show();
            break;

    }
    $(tableId).XGrid('clearGridData');
    showPageLoading()
    $(tableId).XGrid('setGridParam', {
        url: url,
        postData: {
            startTime: $("#startTime").val(),
            endTime: $("#endTime").val(),
            drugClass: $("#drugClass").val(),
            storageConditions: $("#storageConditions").val(),
            productCode: $("#productCode").val(),
            customerCode: $("#customerCode").val(),
            scopeOfOperation: $("#scopeOfOperation").val(),
            type: getType()
        },
        gridComplete: function() {
            hidePageLoading();
            let rowIds = $(tableId).XGrid("getRowData");
                console.log(rowIds)
            if(rowIds.length==0){
                $(tableDiv + " .XGridHead").css({ "overflow" : "scroll" });
                 $(tableDiv + " .XGridBody").css({ "overflow" : "hidden" });
            }else{
                $(tableDiv + " .XGridHead").css({ "overflow" : "hidden" });
                $(tableDiv + " .XGridBody").css({ "overflow" : "auto"});
            }
        },
        onAjaxComplete: function(res) {
            hidePageLoading()
        },
    }).trigger("reloadGrid");

}
$("#set_tb_rows").click(function() {
    //获取当前显示表格
    $(tableId).XGrid('filterTableHead');
})

$('#input_custorm').on('blur', function() {
    setTimeout(function() {
        $('.autocomplete-suggestions').css('display', 'none');
    }, 3000)
});
//客户名称搜索
$('#input_custorm').Autocomplete({
    serviceUrl: '/proxy-customer/customer/customerBaseAppl/pageList', //异步请求
    paramName: 'customerCode', //查询参数，默认 query
    dataType: 'json',
    // lookup: ts, //监听数据 value显示文本，data为option的值
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    triggerSelectOnValidInput: false, // 必选
    // multi: true, //多选要和delimiter一起使用
    // delimiter: ',',
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果', //查无结果的提示语
    transformResult: function(response) {
        return {
            suggestions: $.map(response.result.list, function(dataItem) {
                return { value: dataItem.customerName, data: dataItem.customerCode };
            })
        };
    },
    // tabDisabled: true,
    onSelect: function(result) {
        //选中回调
        $("#customerCode").val(result.data);
    },
    onSearchStart: function(params) {
        // // console.log('检索开始回调', params)
    },
    onSearchComplete: function(query, suggestions) {
        var $ele = $(this);
        if (!$ele.is(":focus")) {
            $ele.Autocomplete('hide');
        }
    },
    onSearchError: function(query, jqXHR, textStatus, errorThrown) {
        //查询失败回调
        // console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function(container) {
        // // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function(params, suggestions) {
        // console.log('没选中回调函数', params, suggestions);
        $("#input_custorm").val('');
        $("#customerCode").val('');
    }
});
//放大镜查询
$(document).on("click", ".glyphicon-search", function() {
    $(this).siblings("input").trigger("dblclick")
});
//客户名称双击查询
$("#input_custorm").dblclick(function() {
    utils.dialog({
        title: '客户列表',
        url: '/proxy-order/order/orderReturn/orderReturnController/toCustomerList',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: $('#input_custorm').val(), // 给modal 要传递的 的数据
        onclose: function() {
            if (this.returnValue) {
                var data = this.returnValue;
                console.log(data)
                $("#input_custorm").val(data.customerName);
                $("#customerCode").val(data.customerCode);
            }
        },
        oniframeload: function() {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
})

function startTables() {
    returnColNamesAndModel("1");
    $('#X_Table').XGrid({
        data: [],
        postData: {
            startTime: $("#startTime").val(),
            endTime: $("#endTime").val(),
            drugClass: $("#drugClass").val(),
            storageConditions: $("#storageConditions").val(),
            productCode: $("#productCode").val(),
            customerCode: $("#customerCode").val(),
            scopeOfOperation: $("#scopeOfOperation").val(),
            type: getType()
        },
        colNames: colNames,
        colModel: colModel,
        rownumbers: true,
        //  multiselect: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function(e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function() {
            hidePageLoading()
        },
        onAjaxComplete: function() {
            hidePageLoading();
        },
        onAjaxComplete: function(res) {
            hidePageLoading()
        },
        pager: '#grid-pager',
    });
    returnColNamesAndModel("2");
    $('#X_Table_1').XGrid({
        data: [],
        colNames: colNames,
        colModel: colModel,
        rownumbers: true,
        //  multiselect: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function(e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function() {
            hidePageLoading()
        },
        onAjaxComplete: function() {
            hidePageLoading();
        },
        pager: '#grid-pager1',
    });
    returnColNamesAndModel("3");
    $('#X_Table_2').XGrid({
        data: [],
        colNames: colNames,
        colModel: colModel,
        rownumbers: true,
        //  multiselect: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function(e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function() {
            hidePageLoading()
        },
        onAjaxComplete: function() {
            hidePageLoading();
        },
        pager: '#grid-pager2',
    });
    returnColNamesAndModel("4");
    $('#X_Table_3').XGrid({
        data: [],
        colNames: colNames,
        colModel: colModel,
        rownumbers: true,
        //  multiselect: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function(e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function() {
            hidePageLoading()
        },
        onAjaxComplete: function() {
            hidePageLoading();
        },
        pager: '#grid-pager3',
    });
    returnColNamesAndModel("5");
    $('#X_Table_4').XGrid({
        data: [],
        colNames: colNames,
        colModel: colModel,
        rownumbers: true,
        //  multiselect: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function(e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function() {
            hidePageLoading()
        },
        onAjaxComplete: function() {
            hidePageLoading();
        },
        pager: '#grid-pager4',
    });
    returnColNamesAndModel("6");
    $('#X_Table_5').XGrid({
        data: [],
        colNames: colNames,
        colModel: colModel,
        rownumbers: true,
        //  multiselect: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function(e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function() {
            hidePageLoading()
        },
        onAjaxComplete: function() {
            hidePageLoading();
        },
        pager: '#grid-pager5',
    });
    returnColNamesAndModel("1");
}

function switchYTable() {
    $('#X_Table').XGrid('clearGridData');
    $('#Y_Table').XGrid('clearGridData');
    $('#common-table').hide();
    $('#cold-table').show();
    showPageLoading()
    $('#Y_Table').XGrid('setGridParam', {
        url: url,
        postData: {
            startTime: $("#startTime").val(),
            endTime: $("#endTime").val(),
            drugClass: $("#drugClass").val(),
            storageConditions: $("#storageConditions").val(),
            productCode: $("#productCode").val(),
            customerCode: $("#customerCode").val(),
            type: getType(),
            scopeOfOperation: $("#scopeOfOperation").val(),
        },
        gridComplete: function() {
            hidePageLoading()
        },
        onAjaxComplete: function() {
            hidePageLoading();
        }
    }).trigger("reloadGrid");
}

function initDate(beginId, endId) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    var month = new_date.getMonth() + 1;
    var date = new_date.getDate();
    var hour = new_date.getHours();
    var minute = new_date.getMinutes();
    var second = new_date.getSeconds();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    hour = hour.toString().length <= 1 ? '0' + hour : hour;
    minute = minute.toString().length <= 1 ? '0' + minute : minute;
    second = second.toString().length <= 1 ? '0' + second : second;
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#' + beginId).val(year + '-' + month + '-01' + ' ' + "00" + ":" + "00" + ":" + "00");
    $('#' + endId).val(year + '-' + month + '-' + date + ' ' + hour + ":" + minute + ":" + second);
}

function dateFormatter(inputTime) {
    if (inputTime == null || inputTime == "")
        return "";
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    var minute = date.getMinutes();
    var second = date.getSeconds();
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d;
    // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
}

// 筛选列
$("#setBtn").click(function() {
    //获取当前显示表格
    $(tableId).XGrid('filterTableHead');
});

/* 查询 */
$('#searchBtn').on('click', function(e) {
    //获取form数据
    var data = $('#form_a').serializeToJSON();
    data['type'] = getType()
    console.log(data);
    //更新表格数据
    var $table_id = $('#nav_content .active .XGridBody table').attr('id');
    console.log($table_id)
        //列表
    showPageLoading()
    initTable(currentTabIndex, true)
});

//导出
$('#exportRowData').on('click', function() {
    //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
    var ck = false;
    // copy this parameter and the below buttons
    var nameModel = "";
    var colNameTemp;
    var colModelATemp;

    colNameTemp = colNames;
    colModelATemp = colModel;
    addHtmlA(colNameTemp);
    dialog({
        content: $("#setCol"),
        title: '筛选列',
        width: $(window).width() * 0.4,
        data: 'val值',
        cancelValue: '取消',
        cancel: true,
        okValue: '导出',
        ok: function() {
            var newColName = [],
                newColModel = [];
            $(this.node).find('#checkRow input[type="checkbox"]').each(function(index) {
                if ($(this).is(":checked")) {
                    nameModel += colModelATemp[index].name + ":" + $(this).attr('name') + ","
                }
            });
            if (nameModel.length == 0) {
                utils.dialog({ content: '请选择后导出', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
            // var keyword = $("#keyword").val();
            // var createTimeStart = $("#createTimeStart").val();
            // var createTimeEnd = $("#createTimeEnd").val();
            // var obj = $("#searchForm").serializeToJSON();

            var scopeOfOperationVal = "";
            if ($("#scopeOfOperation").length > 0) {
                scopeOfOperationVal = $("#scopeOfOperation").val()
            }

            var obj = {
                    startTime: $("#startTime").val(),
                    endTime: $("#endTime").val(),
                    drugClass: $("#drugClass").val(),
                    storageConditions: $("#storageConditions").val(),
                    type: getType(),
                    orgCode: $("#orgCode").val(),
                    customerCode: $("#customerCode").val(),
                    scopeOfOperation: scopeOfOperationVal,
                    productCode: $("#productCode").val()
                }
                // obj["pageNum"] = "1";
                // obj["pageSize"] = "1000000";
            obj["nameModel"] = nameModel;
            // 是否超出限制
            utils.exportAstrictHandle(tableId.slice(1), Number($('#totalPageNum').text()), 1).then(() => {
                return false;
            }).catch(() => {
                httpPost("/proxy-order/order/orderReturn/orderReturnController/exportReportList", obj);
            })
        },
        // copy button to other dialogues
        button: [{
                id: 'chooseAll',
                value: '全选',
                callback: function() {
                    //debugger;
                    if (!ck) {
                        $("#checkRow input").prop("checked", false);
                        ck = true;
                    } else if (ck) {
                        $("#checkRow input").prop("checked", "checked");
                        ck = false;
                    } else {
                        return false;
                    };
                    return false;
                }
            }]
            //copy ends here

    }).showModal();

});

/* 打印预览 */
$("#btn_print_view").on("click", function() {
    var currentPageData = $(tableId).getRowData();
    
    console.log(tableId);
    console.log(currentPageData);
    if (currentPageData.length == 0) {
        utils.dialog({ content: '无打印数据', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    $(printBoxId)[0].contentWindow.getData(0, currentPageData);

});
$("#btn_print").on("click", function() {
    /* var data = $('#form_a').serializeToJSON();
   var startTime = $("#startTime").val();
   var  endTime = $("#endTime").val();
   var drugClass = $("#drugClass").val();
   var page = $("#input_grid-pager input").val();
   var pageSize = $("#select_grid-pager select option:selected").val();
   var orgName = $("#orgName").val();
     $("#print_box")[0].contentWindow.getData(1,data,page,pageSize,startTime,endTime,drugClass,orgName);*/
    var currentPageData = $(tableId).getRowData();
    if (currentPageData.length == 0) {
        utils.dialog({ content: '无打印数据', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    utils.dialog({
        content: "正在打印...",
        timeout: 1000
    }).showModal();
    $(printBoxId)[0].contentWindow.getData(1, currentPageData);

});
/* 商品名称查询 */
$('#s_commodity').on('click', function(e) {
    var input_goodName = $("#input_goodName").val();
    //商品名称 双击查询
    utils.dialog({
        title: '商品列表',
        url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
        width: $(window).width() * 0.8,
        height: 600,
        data: input_goodName, // 给modal 要传递的 的数据
        onclose: function() {
            if (this.returnValue) {
                var data = this.returnValue;
                console.log(data)
                $('#productName').val(data.productName);
                $('#productCode').val(data.productCode);
            }
        },
        oniframeload: function() {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
    //alert('查询商品名称')
});
/* 商品名称查询 */
$('#s_commodity').on('click', function(e) {
    var input_goodName = $("#productName").val();
    //商品名称 双击查询
    utils.dialog({
        title: '商品列表',
        url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
        width: $(window).width() * 0.8,
        height: 600,
        data: input_goodName, // 给modal 要传递的 的数据
        onclose: function() {
            if (this.returnValue) {
                var data = this.returnValue;
                console.log(data)
                $('#productName').val(data.productName);
                $('#productCode').val(data.productCode);
            }
        },
        oniframeload: function() {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
    //alert('查询商品名称')
});
$('#productName').Autocomplete({
    serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + $("#orgCode").val(), //异步请求
    paramName: 'param', //查询参数，默认
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果', //查无结果的提示语
    transformResult: function(response) {
        return {
            suggestions: $.map(response.result.list, function(dataItem) {
                return { value: dataItem.productName, data: dataItem.productCode };
            })
        };
    },
    triggerSelectOnValidInput: false, // 必选
    // multi: true, //多选要和delimiter一起使用
    // delimiter: ',',
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function(result) {
        //选中回调
        $("#productCode").val(result.data)

    },
    onSearchStart: function(params) {
        // console.log('检索开始回调', params)
    },
    onSearchComplete: function(query, suggestions) {

    },
    onSearchError: function(query, jqXHR, textStatus, errorThrown) {
        //查询失败回调
        console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function(container) {
        // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function(params, suggestions) {
        $("#productCode").val("");
        $("#productName").val("");
        console.log(params, suggestions);
        console.log('没选中回调函数');
    }
});
//所属经营范围聚焦查询
$('#scopeName').Autocomplete({
    serviceUrl: '/proxy-order/order/salesOrder/queryScopePage', //异步请求
    paramName: 'keyword', //查询参数，默认
    params: {
        'type': function() {
            return getType()
        }
    },
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果', //查无结果的提示语
    transformResult: function(response) {
        return {
            suggestions: $.map(response, function(dataItem) {
                return { value: dataItem.scopeName, data: dataItem.simpleCode };
            })
        };
    },
    triggerSelectOnValidInput: false, // 必选
    // multi: true, //多选要和delimiter一起使用
    // delimiter: ',',
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function(result) {
        //选中回调
        $("#scopeOfOperation").val(result.data)
    },
    onSearchStart: function(params) {
        // console.log('检索开始回调', params)
    },
    onSearchComplete: function(query, suggestions) {

    },
    onSearchError: function(query, jqXHR, textStatus, errorThrown) {
        //查询失败回调
        console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function(container) {
        // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function(params, suggestions) {
        $("#scopeOfOperation").val("");
        $("#scopeName").val("");
        console.log(params, suggestions);
        console.log('没选中回调函数');
    }
});
// // 初始化表头,根据是否有机构,显示列不一致
// function initTableColumns(){
//     colName = specialForWuHanJiangSuZhengZhou()?businessScopeTableColumns:noBusinessScopeTableColumns
//     colModel = specialForWuHanJiangSuZhengZhou()?businessScopeColModel:noBusinessScopeColModel
// }

function showPageLoading() {
    $('.myloadingBlock').show();
    setTimeout(function() {
        hidePageLoading();
    }, 60000);
}

function hidePageLoading() {
    $('.myloadingBlock').hide();
}
$(function() {
    // 初始化表头,根据是否有机构,显示列不一致
    // initTableColumns();
    // switchXTable();
    /* 日期初始化 */
    startTables();
    initDate('startTime', 'endTime');
    showPageLoading();
    initTable(currentTabIndex)
        // $('#X_Table').XGrid({
        //     //data: [],
        //     url:"/proxy-order/order/orderReturn/orderReturnController/getReportList",
        //     colNames: colNames,
        //     colModel: colModel,
        //     rownumbers: true,
        //     rowNum: 20,
        //     rowList:[20,50,100],
        //     altRows: true, //设置为交替行表格,默认为false
        //     pager: '#x-grid-pager',
        //     postData: {
        //         startTime: $("#startTime").val(),
        //         endTime:$("#endTime").val(),
        //         drugClass : $("#drugClass").val(),
        //         storageConditions : $("#storageConditions").val(),
        //         /*   channelId : $("#channelId").val(),*/
        //         productCode : $("#productCode").val(),
        //         customerName : $("#customerName").val(),
        //         scopeOfOperation:$("#scopeOfOperation").val(),
        //         type: getType()
        //     },
        //     ondblClickRow: function (id, dom, obj, index, event) {
        //         //console.log('双击行事件', obj);
        //
        //         // location.href = '/orderReturn/orderReturnController/toApproval?businessId='+obj.salesReturnCode+"&taskId=887554&processId="+obj.auditId;
        //     },
        //     onSelectRow: function (id, dom, obj, index, event) {
        //         //选中事件
        //         //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
        //         //console.log(id, dom, obj, index, event)
        //     },
        //     gridComplete:function () {
        //         hidePageLoading()
        //     },
        //     onAjaxComplete: function(res){
        //         hidePageLoading()
        //     },
        // });
})