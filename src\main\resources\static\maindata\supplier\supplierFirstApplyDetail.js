$(function () {
	var supplierBaseId =  $("#supplierBaseId").val();
	var supplierOrganBaseId =  $("#supplierOrganBaseId").val();  
	var baseApplicationCode =  $("#baseApplicationCode").val(); 
	var baseOrOrgan = $("#baseOrOrgan").val();//是共供应商首营是机构供应航
	//修改记录
    $('#modifyRecord').XGrid({
        data: [/*{
        id: '',
        applyId:'',
    	applicationTime: '',
		applicationOper: '',
		applicationName: '',
		applicationCode: '',
		type:'',
		applicationDetail: '',
		approvalProcessId:''	
        }*/],
        colNames: ['','','申请时间', '申请操作', '申请人', '单据编号','审核状态','', '申请明细', '审批流程'],
        colModel: [
        	{
                name: 'id', //与反回的json数据中key值对应
                index: 'id', //索引。其和后台交互的参数为sidx
                key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true
        	},{
        		name: 'applyId', //与反回的json数据中key值对应
                index: 'applyId', //索引。其和后台交互的参数为sidx
                hidden:true
        	},{
            name: 'applicationTime', //与反回的json数据中key值对应
            index: 'applicationTime', //索引。其和后台交互的参数为sidx
            formatter:function(value){
            	var date=value;
            	if(!value)return false;
        		date=format(value);
        		return date;
            }
    	}, {
            name: 'applicationOper',
            index: 'applicationOper'
        }, {
            name: 'applicationName',
            index: 'applicationName'
        }, {
            name: 'applicationCode',
            index: 'applicationCode'
        },  {
            name: 'auditStatusName',
            index: 'auditStatusName'
        },{
        	name:"type",
        	index:"type",
        	hidden:true
        },{
            name: 'applicationDetail',
            index: 'applicationDetail',
            formatter:function (value,b,rowData) {
                if(rowData.type != 6)
                {
                    var arr=value.split('；');
                    var html='<div class="modifyTheRecord">\n' +
                        '<div class="itemContent"><div class="mrLeft">';
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<p>'+arr[i]+'</p>';
                    }
                    html+='</div></div>' +
                    '\t\t<div class="mrRight">\n' +
                    '\t\t\t<a href="javascript:;" class="moreBtn">展开</a>\n' +
                    '\t\t</div>\n' +
                    '\t</div>\n';
                    return html;
                }else{
                	return "<a href = '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/detail/firstApplySnap?pageParam="+rowData.applyId+"'>"+value+"</a>";
                }
            }
        }, {
            name: 'approvalProcessId',
            index: 'approvalProcessId',
            formatter: function (value, grid, rows, state) {
            		if(value == -1){
            			return "";
					}else{
						return "<a href = 'javascript:void(0)' onclick='toFlowChart("+value+")'>查看</a>";
					}
            }
        }
        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,
        pager:'#modifyRecord_page'
    });
	
   //异步加载修改记录  6:供应商首营申请   2:资料变更申请 3:运行属性变更申请 7:停用申请
    if(null != baseOrOrgan ){
    	var correlationId = "";
    	if("base"==baseOrOrgan){//主数据id
    		correlationId = supplierBaseId;
    	}else{//机构供应商id
    		correlationId = supplierOrganBaseId;
    	}
    	$('#modifyRecord').XGrid('setGridParam', {
    		url:"/proxy-supplier/supplier/supplierOrganBase/supplierOrganBase/ajaxUpdateRecodList",
    		postData: {
    			"correlationId":correlationId,
    			"baseOrOrgan":baseOrOrgan
            },page:1
          
        }).trigger('reloadGrid');
    	
    }
    
    
 
    
  
	
	
	// $("[data-toggle='distpicker']").each(function(){
	// 	//省
	// 	var val1=$.trim($(this).find("select").eq(0).attr("data-value"));
	// 	if(val1 && val1 != '')
	// 	{
	// 		$(this).find("select").eq(0).val(val1);
	// 		$(this).find("select").eq(0).change();
	// 	}
	// 	//市
	// 	var val2=$.trim($(this).find("select").eq(1).attr("data-value"));
	// 	if(val1 && val1 != '')
	// 	{
	// 		$(this).find("select").eq(1).val(val2);
	// 		$(this).find("select").eq(1).change();
	// 	}
	// 	//区
	// 	var val3=$.trim($(this).find("select").eq(2).attr("data-value"));
	// 	if(val1 && val1 != '')
	// 	{
	// 		$(this).find("select").eq(2).val(val3);
	// 	}
	// });
	
	//修改记录申请明细展开收起按钮
	$("#modifyRecord").on("click",".moreBtn",function(){
	    var type=$.trim($(this).text());
	    var tr=$(this).parents("tr");
	    var innerHeiht=0;
	    if(type == '展开')
	    {
	        innerHeiht=tr.find(".mrLeft").innerHeight();
	        $(this).html('收起');
	    }else if(type == '收起'){
	        innerHeiht = 40;
	        $(this).html('展开');
	    }
	    if(innerHeiht < 40){
	        innerHeiht=40;
	    }
	    tr.find(".modifyTheRecord .itemContent").animate({
	        height:innerHeiht
	    },500)
	});

	
});


function modifyRecord(supplierBaseId,baseOrOrgan){
	 var xGridDate=[];
    $.ajax({
      	url:'/proxy-supplier/supplier/supplierOrganBase/supplierOrganBase/ajaxUpdateRecodList',
      	data:{"correlationId":supplierBaseId,"baseOrOrgan":baseOrOrgan},
      	type:"post",
      	dataType:'json',
      	success:function(data){
      		if(data.result.list.length>0){
      			var dataList = data.result.list;
      			for(var i=0;i<dataList.length;i++){
      			  var a={
      					id:dataList[i].id,
      					applyId:dataList[i].applyId, 
      					applicationTime: dataList[i].applicationTime,
      					applicationOper: dataList[i].applicationOper,
      					applicationName: dataList[i].applicationName,
      					applicationCode: dataList[i].applicationCode,
      					auditStatusName: dataList[i].auditStatusName,
      					type:dataList[i].type,
      					applicationDetail: dataList[i].applicationDetail,
      					approvalProcessId:dataList[i].approvalProcessId,
      		        }
      			   xGridDate.push(a);
      			}
          	}else{
          		xGridDate = []
          	}
      		$('#modifyRecord').XGrid('setGridParam', {
      			data:xGridDate,
                pager: '#modifyRecord_page'
            }).trigger('reloadGrid');
      	}
      });
}

/**
 * 修改记录查看流程图
 * @returns
 */
function toFlowChart(processInstaId) {
	if("null" != processInstaId  && "" != processInstaId){
		 dialog({
		        url: '/proxy-supplier/supplier/supplierOrganBase/supplierChangeRecord/toFlowChart',
		        title: '审批流程',
		        width: 1200,
		        height: 520,
		        data: {"processInstaId":processInstaId},
		        onclose: function () {
		        },
		        oniframeload: function () {
		        }
		    }).showModal();
		    return false;
	}
   
}

// window.onload = function () {
// 	let list = $('.ManufactoryList');
// 	$(list).each((index,item) => {
// 		let _name = $(item).find('input[type=hidden]').attr('name');
// 		utils.valAutocomplete("/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},_name,
// 			{data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
// 	})
// }


