﻿     var orgName =  "";
     var orgCode = "";
     var edit = null;
    // delete duplicate code gose here;
    function uniq(array){
     var temp = [];
     var index = [];
     var l = array.length;
     for(var i = 0; i < l; i++) {
         for(var j = i + 1; j < l; j++){
             if (array[i] === array[j]){
                 i++;
                 j = i;
             }
         }
         temp.push(array[i]);
         index.push(i);
     }
     console.log(index);
     return temp;
    };

    var odt =  [{    id: 0,
                 orgCode: "003",
                 orgName: "重庆小药药医药科技有限公司",
                 productCriterion: "45",
                 proportion: "30%",
                 sort: "A",
                 sortRule: "销售数量",
                 unit: "天"
                 },
             {
                id: 1,
                orgCode: "003",
                orgName: "重庆小药药医药科技有限公司",
                productCriterion: "45",
                proportion: "70%",
                sort: "B",
                sortRule: "销售数量",
                unit: "天"
             },
             {
                id: 2,
                orgCode: "003",
                orgName: "重庆小药药医药科技有限公司",
                productCriterion: "45",
                proportion: "",
                sort: "C",
                sortRule: "",
                unit: "天"

             }
             ];


     //弹框提示
     function showTips(contentText){
         utils.dialog({
             content: contentText,
             quickClose: true,
             timeout: 2000
         }).showModal();
     }


     function showDialog(titleText,contentText){
         //提示框
         utils.dialog({
             width: 180,
             height: 30,
             title: titleText,
             content: contentText,
             quickClose: false,
             okValue: '确定',
             ok: function () {}
         }).showModal();
     }



    $("#create").click(function(){

        $.ajax({
            url:'queryIsProcessed',
            type:'get',
            success:function (result) {

                if(result.code==1){
                    showTips(result.msg);

                }else{
                  $("#edit").prop("disabled","disabled");
                    edit = 1;
                    $("[name='orgCode']").val('');
                    $("#orgCode").val("");

                    dialog({
                        content: $("#create_dia"),
                        title: '新建商品采购分类管理',
                        width: 389,
                        height: 96,
                        data: 'val值', // 给modal 要传递的 的数据
                        onclose: function () {},
                        cancelValue: '取消',
                        cancel: function () {
                             $("#edit").removeAttr("disabled");
                        },
                        okValue:"确定",
                        ok: function () {

                            $("#edit").removeAttr("disabled");
                            $.ajax({
                                url:'queryOrgIsExistence',
                                type:'post',
                                data:{orgCode:$("[name='orgCode']").val()},
                                success:function (result) {
                                    console.log(result.code)
                                    if(result.code==1){
                                        var d = dialog({
                                            title: '提示',
                                            content: '该机构已存在商品采购分类管理列表！',
                                            cancelValue: '我知道了',
                                            cancel: function () {

                                            }
                                        });

                                        d.show();
                                    }else{
                                        if(orgName){
                                            var dt = odt;
                                            var lt = dt.length - 1;
                                            var id = dt[lt].id;
                                            for(var i = 0; i < 3; i++){
                                                id ++;
                                                dt[i].orgName =  orgName;
                                                dt[i].orgCode =  orgCode;
                                                dt[i].id  = id;

                                                if(i == 2){
                                                    dt[i].proportion = "";
                                                    dt[i].sortRule = "";
                                                };

                                                $('#X_Table').XGrid('addRowData',dt[i]);
                                                console.log(dt);
                                            };
                                        }else{
                                            console.log("重复了");
                                            return false;
                                        }

                                    }

                                }
                            })
                        }

                    }).show();

                }

            }
        })


    })

    $('#orgCode').Autocomplete({
        serviceUrl: '/proxy-sysmanage/sysmanage/purchaseSortManagement/queryOrg', //异步请求
        paramName: 'orgName',//查询参数，默认 query
        // dataType: 'json',
        //  lookup:, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        params:{
            isStop:0
        },
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        dataReader:{
            'list':'result',
            'value':'orgName',
            'data':'orgCode'
        },
        onSelect: function (result) {
            //选中回调
            $("[name='orgCode']").val(result.data);

            var dt =   $('#X_Table').XGrid('getRowData');
                if(dt.length < 3){
                    dt = odt;
                };

             console.log(dt);
             var arr = [];
             var nrr = [];

             for(var i = 0; i < dt.length; i++){
                arr.push(dt[i].orgName);
             };
            arr.sort();

            arr = uniq(arr);

             var  tss = result.value;
             for(var i = 0;i < arr.length ;i++){
                if(tss == arr[i]){
                 orgName = null;
                 return false;
                }else{
                      orgName = result.value;
                      orgCode = result.data;
                }
           };


        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            console.log(suggestions.length);

            if (suggestions.length <1) {

                $("[name='orgCode']").val(result.data)
                return false;
            }

        }


    })

     $('#X_Table').XGrid({
                url: 'queryPurchaseSortManagementPage',
                 postData : {
                     enable :1
                },
                colNames: ['id','分类', '机构', '商品标准流速', '单位','占比','分类规则','orgCode'],
                colModel: [{
                    name: 'id',
                    index: 'id', //索引。其和后台交互的参数为sidx
                    hidden:true

                }, {
                    name: 'sort',

                }, {
                    name: 'orgName',

                }, {
                    name: 'productCriterion',

                }, {
                    name: 'unit',

                },{
                    name: 'proportion',

                },{
                    name:'sortRule',

                },
                {
                    name: 'orgCode',
                    hidden: true,
                }
                ],
                rowNum: 0,
                rownumbers: 0,
                altRows: true, //设置为交替行表格,默认为false
                ondblClickRow: function (e, c, a, b) {
                    console.log('双击行事件', e, c, a, b);
                },
                onSelectRow: function (e, c, a, b) {
                    console.log('单机行事件', e, c, a, b);
                },
               //pager: '#grid-pager',
    });



    $("#submitAssert").click(function(){

        if(edit == 1){
            if (!validform("vX_Table").form()){
                return false;
           };
            var ttb = $('#X_Table').XGrid('getRowData');
            var data  = JSON.stringify(ttb);
            $("#edit").removeAttr("disabled");
            $("#create").removeAttr("disabled");
            $.ajax({
                url:'addPurchaseSortManagement',
                type:'post',
                data:{
                    data:data
                },
                success:function (result) {

                    if(result.code==0){
                        showTips("提交成功!")
                        edit = null;
                        $('#X_Table').trigger('reloadGrid');
                        setTimeout("location.reload()",800);

                    }if(result.code==1){
                        showTips("提交失败!")
                    }

                }

            })


        }else if(edit == 2){
            var vldate = true;
            if (!validform("vX_Tablee").form()){
                return false;
            };
            var ttb = $('#X_Tablee').XGrid('getRowData');
            for(var i = 0; i < ttb.length; i++){
                var tems = ttb[i].unit;
                var sortRule = ttb[i].sortRule;
                    tems = tems.toString();
                    sortRule = sortRule.toString();
                if(tems.length == 0 ){
                    showTips("选择单位不能为空！")
                    return false;
                };
                if(sortRule.length == 0 && (i + 1)%3 != 0){
                    showTips("分类规则不能为空！")
                    return false;
                }
            }

            for(var i = 0; i < ttb.length; i ++){
                if(ttb[i].unit == 'null'){
                    vldate = false;
                     utils.dialog({  content: '请选择单位！', quickClose: true, timeout: 1000}).showModal();

                    break;
                };

                if(ttb[i].proportion == '' && (i+1)%3 != 0){
                    vldate = false;
                     utils.dialog({content: '占比不能为空！', quickClose: true, timeout: 1000}).showModal();

                    break;
                }
                 if(ttb[i].sortRule == 'null' && (i+1)%3 != 0){
                    vldate = false;
                     utils.dialog({content: '请选择分类规则！', quickClose: true, timeout: 1000}).showModal();

                    break;
                };


            }
            if(!vldate){
                return false;
            }
            var data  = JSON.stringify(ttb);

            $("#edit").removeAttr("disabled");
            $("#create").removeAttr("disabled");
            $.ajax({
                url:'addPurchaseSortManagement',
                type:'post',
                data:{
                    data:data
                },
                success:function (result) {
                    if(result.code==0){
                        showTips("提交成功!")
                       // $('#X_Tablee').trigger('reloadGrid');
                        $('#X_Table').trigger('reloadGrid');
                        $("#edittb").css("display","none");
                        $("#originaltb").css("display","block");
                        setTimeout("location.reload()",800);
                        edit = null;
                    }if(result.code==1){
                        showTips("提交失败!")
                    }
                }


            })

        }else{

            showTips("请选择新建或者编辑!")

        }

    })

    $("#edit").click(function(){
        var flag = $("#create").attr('disabled');
        if(!flag){
            $.ajax({
                url:'queryIsProcessed',
                type:'get',
                success:function(result){
                    if(result.code==1){
                        showTips("有正在审核中的数据,不能进行编辑!");

                    }else{
                        $("#create").prop("disabled","disabled");
                        edit = 2;
                        $("#originaltb").css("display","none");
                        $("#edittb").css("display"," block");

                        $('#X_Tablee').XGrid({
                            data:  $('#X_Table').XGrid('getRowData'),
                            colNames: ['id','分类', '机构', '商品标准流速', '单位','占比','分类规则','orgCode'],
                            colModel: [{
                                name: 'id',
                                index: 'id', //索引。其和后台交互的参数为sidx
                                hidden:true

                            }, {
                                name: 'sort',

                            }, {
                                name: 'orgName',

                            }, {
                                name: 'productCriterion',
                                rowtype: "#productCriterion",

                            }, {
                                name: 'unit',
                                rowtype: "#unit",
                            },{
                                name: 'proportion',
                                rowtype: "#proportion",
                                rowEvent: proportion,

                            },{
                                name:'sortRule',
                                rowtype: "#sortRule",
                                rowEvent: sortRule,

                            },
                                {
                                    name: 'orgCode',
                                    hidden: true,
                                }
                            ],
                            rowNum: 0,
                            rownumbers: 0,
                            altRows: true, //设置为交替行表格,默认为false
                            ondblClickRow: function (e, c, a, b) {
                                console.log('双击行事件', e, c, a, b);
                            },
                            onSelectRow: function (e, c, a, b) {
                                console.log('单机行事件', e, c, a, b);
                            },
                            gridComplete: function () {
                                setTimeout(function name() {
                                    var data = $('#X_Tablee').XGrid('getRowData');
                                    data.forEach(function (item, index) {
                                        if (!item.proportion) {
                                            $('#X_Tablee #' + item['id']).find("td[row-describedby='sortRule']").css("display","none");
                                            $('#X_Tablee #' + item['id']).addClass('warnning')
                                        }
                                    });
                                }, 10);
                            },
                            //pager: '#grid-pager',
                        });
                    }
                }
            })
        }
    })

    function  proportion(cel){
        console.log(cel);
        var orgCode = cel.rowData.orgCode;
        var idx = cel.rowData.id;
        var pvl = cel.rowData.proportion;
        var pvo = parseInt(pvl);
        var pvob = 100 - pvo;

        var orgCodeb = $("#X_Tablee #"+idx).prev().find('td[row-describedby="orgCode"]').text();
        var orgCodea = $("#X_Tablee #"+idx).next().find('td[row-describedby="orgCode"]').text();

        if(orgCode &&orgCode == orgCodeb  && orgCode == orgCodea && pvo < 100){
            $("#X_Tablee #"+idx).prev().find("input[name='proportion']").val(pvob+"%");
            setTimeout(function(){

                 $("#X_Tablee #"+idx).find("input[name='proportion']").val(pvo+"%");

             }, 1000);
        }else if(orgCode && orgCode != orgCodeb  && orgCode == orgCodea   && pvo < 100){
             $("#X_Tablee #"+idx).next().find("input[name='proportion']").val(pvob+"%");
               setTimeout(function(){

                   $("#X_Tablee #"+idx).find("input[name='proportion']").val(pvo+"%");

               }, 1000);
        }else{

              setTimeout(function(){

              $("#X_Tablee #"+idx).find("input[name='proportion']").val("");
              $("#X_Tablee #"+idx).prev().find("input[name='proportion']").val("");
              $("#X_Tablee #"+idx).next().find("input[name='proportion']").val("");
          }, 1000);
        }


    }


    function  sortRule(cel){
        console.log(cel);
        var orgCode = cel.rowData.orgCode;
        var idx = cel.rowData.id;
        var pvl = cel.rowData.sortRule;



        var orgCodeb = $("#X_Tablee #"+idx).prev().find('td[row-describedby="orgCode"]').text();
        var orgCodea = $("#X_Tablee #"+idx).next().find('td[row-describedby="orgCode"]').text();

        if(orgCode &&orgCode == orgCodeb  && orgCode == orgCodea ){
            $("#X_Tablee #"+idx).prev().find("td[row-describedby='sortRule'] select  option[value='"+pvl+"']").prop("selected","selected");

        }else if(orgCode && orgCode != orgCodeb  && orgCode == orgCodea ){
                $("#X_Tablee #"+idx).next().find("td[row-describedby='sortRule'] select option[value='"+pvl+"']").prop("selected","selected");
        }else{


        }

    }


    $(function(){

        $.ajax({
            url: 'queryIsProcessed',
            type: 'get',
            success: function (result) {

                if(result.code==1){
                    initApprovalFlowChart(result.result)

                }else{
                }

            }
        })



    })

     function  initApprovalFlowChart(processId) {
         //获取审核流程数据
         $.ajax({
             type: "POST",
             url: "queryProcessHistory",
             dataType:"json",
             data:{batch:processId},
             success: function (data) {
                 if (data.code==0&&data.result!=null){
                     console.log(data.result);
                     $("#ifnohidethis").css("display","block");
                     $('.flow').html("")
                     $('.flow').process(data.result);
                 }
             },
             error: function () {}
         });
     }