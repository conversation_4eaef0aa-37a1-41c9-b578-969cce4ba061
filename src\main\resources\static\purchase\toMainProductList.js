
function timeStamp2String(time){
    var datetime = new Date();
    datetime.setTime(time);
    var year = datetime.getFullYear();
    var month = datetime.getMonth() + 1;
    var date = datetime.getDate();
    var hour = datetime.getHours();
    var minute = datetime.getMinutes();
    var second = datetime.getSeconds();
    var mseconds = datetime.getMilliseconds();
    return year + "-" + month + "-" + date;


};

// /**
//  * 订单属性
//  * */
// function addOrderAttribute() {
//     var  data = [{value:'0', name:'统筹集采'},{value:'1', name:'统筹统采'}]
//     $.each(data, function (infoIndex, info) {  //循环遍历后台传过来的json数据
//         $("#centralizedPurchaseType").append("<option value='" + info["value"] + "'>" + info["name"] + "</option>");
//         // $("#test").append("<option value='5'>测试5</option>");   //为Select追加一个Option(下拉项)
//         // $("#test").prepend("<option value='0'>测试6</option>");   //为Select插入一个Option(第一个位置)
//     });
//     // $.ajax({
//     //     url: '<%=root%>/employ/bmfwAction!getBillCompanyBilProvCdAndType',
//     //     type: 'post',
//     //     data: 'billStyle=' + billStyle + '&provCd=' + provCd,
//     //     success: function (data) {
//     //         $.each(data, function (infoIndex, info) {  //循环遍历后台传过来的json数据
//     //             $("#test").append("<option value='" + info["sex"] + "'>" + info["name"] + "</option>");
//     //             // $("#test").append("<option value='5'>测试5</option>");   //为Select追加一个Option(下拉项)
//     //             // $("#test").prepend("<option value='0'>测试6</option>");   //为Select插入一个Option(第一个位置)
//     //         });
//     //     }
//     // });
//     // $.getJSON("jsontest.json", function (data) {
//     //     $.each(data, function (infoIndex, info) {
//     //         $("#test").append("<option value='" + info["sex"] + "'>" + info["name"] + "</option>");
//     //     })
//     //     console.log(strHtml);
//     // })
// }
$(function () {
    // addOrderAttribute()
    //单据编号
    var colNameA =['序号','业务类型', '分公司', '原商品编码', '商品编码','商品名称','商品规格','生产厂家','品牌厂家','产地','区域经理','大区经理','上架时间','状态','订单属性','库存数量','可用库存','30天内售罄期'/*,'60天内售罄期','90天内售罄期'*/];
    var colModelA = [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            hidden:true ,
            hidegrid: true ,
        },
        {
            name: 'channelId',
            index: 'channelId'
        },
        {
            name: 'companyName'
        },
        {
            name: 'oldProductCode'
        },
        {
            name: 'drugCode'
        },
        {
            name: 'drugName'
        },
        {
            name: 'spec'
        } ,
        {
            name: 'manufName'
        } ,
        {
            name: 'brandManufacturersName'
        }
        ,{
            name: 'prodPlace'
        },
        {
            name: 'areaManager'
        },
        {
            name: 'districtManager'
        },
        {
            name: 'firstOnShelvesTime',
            formatter:function(xt){
                if(xt){
                    return  timeStamp2String(xt);
                }
            }
        },
        {
            name: 'status'
        },
        {
            name: 'centralizedPurchaseName',//订单属性
            index: 'centralizedPurchaseName',
        },
        {
            name: 'amountQualifieds'
        },
        {
            name: 'amountQualifiedUses'
        },
        {
            name: 'thirtyAay'
        },
        // {
        //     name: 'sixtyAay'
        // },
        // {
        //     name: 'ninetyAay'
        // },
        {
            name: 'channelId',
            index: 'channelId',
            hidden:true ,
            hidegrid: true
        }

    ],allColModelA = JSON.parse(JSON.stringify(colModelA));
    var centralizedPurchaseType= $("#centralizedPurchaseType").val();
    $('#X_Tablea').XGrid({
        url:'/proxy-purchase/purchase/common/mainProductList',
        postData: {"purchaseUser":  '',"drugCode":'',"manufName":'',"centralizedPurchaseType": centralizedPurchaseType},
        colNames: colNameA,
        colModel: colModelA,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-a',//设置翻页所在html元素名称
        selectandorder: true,
        key: "drugCode",
        // onClickRow: function (id, dom, obj, index, event) {
        //     //双击事件回调函数
        //     var selRow = obj;
        //     if (selRow) {
        //     	// searchProductDetail(selRow);
        //         $("#productCode").val(obj.drugCode);
        //     }else {
        //         utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
        //     }
        // },
    });

    $('#selectBuyDetailBtn').on('click', function () {
        var selOrder =  $('#X_Tablea').XGrid('getSeleRow');
        if(selOrder == undefined || selOrder == null){

        }else if(selOrder.length == undefined ){
            selectIdOrder = selOrder.drugCode;
        }else{
            var orderLength = selOrder.length;
            if(orderLength > 1){
                utils.dialog({content: '请选择一条查看', quickClose: true, timeout: 2000}).showModal();
                return;
            }
        }

        var purchaseUser= $("#purchaseUser").val();
        var drugCode = selOrder[0].drugCode;
        if(drugCode == undefined || drugCode == null){
            utils.dialog({content: '请选择一条查看', quickClose: true, timeout: 2000}).showModal();
        }else if(drugCode.length == undefined ){
            utils.dialog({content: '请选择一条查看', quickClose: true, timeout: 2000}).showModal();
        }
        var channelIdx = selOrder[0].channelId;
        if(channelIdx == undefined || channelIdx == null){
            utils.dialog({content: '商品业务类型异常', quickClose: true, timeout: 2000}).showModal();
        }else if(channelIdx.length == undefined ){
            utils.dialog({content: '商品业务类型异常', quickClose: true, timeout: 2000}).showModal();
        }



        var manufName =  $("#manufName").val();
        utils.dialog({
            url: '/proxy-purchase/purchase/common/toPurchaseProductBuyDetail?drugCode=' + drugCode + '&purchaseUser=' + purchaseUser+'&manufName='+manufName+'&channelId='+channelIdx,
            title: '商品购进明细',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
        }).showModal();
    })

    //查询数据，重置data
    $('#searchBtn').on('click', function () {
        // var cs = $("#toggle_wrap .active").attr("id");

        //清空下面的数据
        $('#X_Table_document_detail').XGrid('clearGridData');
        //加载左边联想的数据
        $('#X_Tablea').setGridParam({
            url: '/proxy-purchase/purchase/common/mainProductList?'+$("#searchForm").serialize(),
            postData: {a:  1 }
        }).trigger('reloadGrid');

    })


    function ObjData(key,value){
        this.Key=key;
        this.Value=value;
    }
    //重置
    $("#clearBtn").on("click", function () {
        $('#createUser').val('');
        $('#productNameKeyword').val('');
        $('#manufName').val('');
        $('#drugCode').val('');
        $('#channelId').val('');
        $('#channelId_inp').val('');
        // $('#X_Tablea').XGrid('setGridParam', {
        //     url:"/proxy-purchase/purchase/common/mainProductList",
        //     postData: {
        //         "createUser": $('#createUser').val(),
        //         "productNameKeyword": $('#productNameKeyword').val(),
        //         "manufName":$('#manufName').val()
        //     },page:1
        //
        // }).trigger('reloadGrid');
    });
    //查询数据，重置data
    $('#exportBtn').on('click', function () {
        utils.exportAstrictHandle('X_Tablea',Number($('#grid-pager-a #totalPageNum').text())).then(()=>{
            return false;
        }).catch(()=>{
            var cs = $("#toggle_wrap .active").attr("id");
            if(cs == "orderList"){
                var ck = false;
                var nameModel = "";
                addHtmlA(colNameA);
                dialog({
                    content: $("#setColA"),
                    title: '筛选列',
                    width: 706,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {
                        $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                            if ($(this).is(":checked")) {
                                nameModel += allColModelA[index+1].name + ":"+$(this).attr('name') + ","
                                // alert(nameModel);
                            }
                        });
                        if(nameModel.length == 0){
                            utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                        var sel =  $('#X_Tablea').XGrid('getSeleRow');

                        var productCodes = [];
                        if(sel != undefined && sel.length >0){
                            $.each(sel,function(i,obj){
                                productCodes.push(obj.drugCode);
                            })
                        }
                        //如果有选中的行，则导出选中的行，否则导出所有查询条件的
                        var params;
                        if (productCodes.length==0){

                            params = {
                                "nameModel": nameModel,
                                "purchaseUser": $("#purchaseUser").val(),
                                "drugCode": $("#drugCode").val(),
                                "manufName":  $("#manufName").val(),
                                "channelId": $("#channelId").val(),
                                "centralizedPurchaseType":  $("#centralizedPurchaseType").val(),
                                "productStatus": $("#productStatus").val(),
                            }
                        }else {
                            params = {"productCodes":productCodes,"nameModel": nameModel};
                        }
                        httpPost("/proxy-purchase/purchase/common/exportMainProductList", params);
                    },
                    // copy button to other dialogues
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if(!ck){
                                    $("#checkRow input").prop("checked",false);
                                    ck = true;
                                }else if(ck){
                                    $("#checkRow input").prop("checked","checked");
                                    ck = false;
                                }else{
                                    return false;
                                };
                                return false;
                            }
                        }
                    ]
                    //copy ends here
                }).showModal();

            }else{
                return;
            }
        })
    });


    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }


    $("#set_tables_rowa").click(function () {
        $('#X_Tablea').XGrid('filterTableHead');

    })
    function addHtmlA(arry) {
        if (!$('#setColA')[0]) {
            var s = '<div id="setColA" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 1; i < arry.length; i++) {
                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    var selectvlsupplier ="";
    $('#manufName').Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/purchaseOrder/getManuFactoryList', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        //lookup: ts, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.manufactoryName, data: dataItem.manufactoryId};
                })
            };
        },
        onSelect: function (result) {
            //选中回调
            selectvlsupplier = result.value;
        },
        onNoneSelect: function (params, suggestions) {
            $("#manufName").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });



    var ivkeywordOrderByNew = '';
    $('#keywordOrderByNew').Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/common/selectPurchaseOrderNos', //异步请求
        paramName: 'keywordOrderByNew',//查询参数，默认 query
        dataType: 'json',
        //lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function(response) {
            return {
                suggestions: $.map(response, function(dataItem) {
                    if((dataItem.value).length == 0 ){
                        return [];

                    };
                    return {
                        value: dataItem.value,
                        data: dataItem.data
                    };
                })
            };
        },
        onSelect: function (result) {
            ivkeywordOrderByNew = result.value;

        },
        onNoneSelect: function (params, suggestions) {
            $("#keywordOrderByNew").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });



    //商品联想搜索
    var selectvlproduct="";
    $('#productNameKeyword').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/supplier/findPageInfoByMnemonInfo', //异步请求
        paramName: 'keyword',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        zIndex: 99,
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.productName +"\xa0\xa0\xa0"+ dataItem.specifications +"\xa0\xa0\xa0"+ dataItem.manufacturerName, data: dataItem.productCode};
                })
            };
        },
        onSelect: function (result) {
            $("#drugCode").val(result.data);
            selectvlproduct = result.value;
            //$("#createUser").val(result.data);
            // console.log('选中回调')
        },
        onNoneSelect: function (params, suggestions) {
            $("#productNameKeyword").val("");
            $("#drugCode").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });






    //采购员联想查询
    var selectvl = '';
    $('#createUser').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/purchaseRefundProductOrder/getPurchaseRefundProductOrderProductUserName?pageNum=1&pageSize=5&sort=asc', //异步请求
        paramName: 'userName',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.userName, data: dataItem.id};
                })
            };
        },
        onSelect: function (result) {
            $("#purchaseUser").val(result.data);
            selectvl = result.value;
        },onNoneSelect: function (params, suggestions) {
            $("#purchaseUser").val("");
            $("#createUser").val("");
        }
    });

    //业务类型搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('0').then( res => {
            console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
        })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
        })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });


})

