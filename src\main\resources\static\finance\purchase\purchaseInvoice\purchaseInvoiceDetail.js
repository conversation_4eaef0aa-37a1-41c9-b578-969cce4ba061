$(function () {



    //重新计算金额合计
   // resetStoreInList();

    function resetStoreInList() {
        $.each(storeInList, function (index, item) {
            var stockOrderNo = item.stockOrderNo;
            item.priceTaxSum = 0;
            item.sumTaxMoney = 0;
            item.sumNoTaxMoney = 0;
            $.each(storeInDetailList, function (index, obj) {
                if (stockOrderNo === obj.purchaseOrderNo) {
                    item.priceTaxSum += parseFloat(obj.productContainTaxMoney);
                    item.sumTaxMoney += parseFloat(obj.productTaxMoney);
                    item.sumNoTaxMoney += parseFloat(obj.procutNoTaxMoney);
                }
            })
            storeInList[index].priceTaxSum = item.priceTaxSum.toFixed(2);
            storeInList[index].sumTaxMoney = item.sumTaxMoney.toFixed(2);
            storeInList[index].sumNoTaxMoney = item.sumNoTaxMoney.toFixed(2);

        })
    }

    var detailColNames = [];
    var detailColModel = [];

    if ($("#invoiceType").val() == 1){
        detailColNames = ['id', '单据日期', '采购单据号', '采购单据行号','采购单据行状态', '商品编码', '商品名称', '规格',
            '小包装单位', '生产厂家', '库存数量', '含税单价', '小包装数量','本次开票数量','已开票数量',
            '金额合计', '税额合计', '价税合计'
        ];
        detailColModel = [{
            name: 'id',
            hidden: true,
            hidegrid: true
        },
            {
                name: 'storeTime'
            }, {
                name: 'stockOrderNo'
            }, {
                name: 'billsSort'
            }, {
                name: 'ticketStatus',
                formatter: function (value) {
                    if (value != '') {
                        if (value == '0') {
                            return '未开发票';
                        } else if (value == '1') {
                            return '部分开票';
                        } else if (value == '2') {
                            return '已开票';
                        } else {
                            return '';
                        }
                    } else {
                        return '';
                    }
                },
                unformat: function (value) {
                    if (value != '') {
                        if (value == '未开发票') {
                            return '0';
                        } else if (value == '部分开票') {
                            return '1';
                        } else if (value == '已开票') {
                            return '2';
                        }
                    } else {
                        return '';
                    }
                }
            }, {
                name: 'productCode'
            }, {
                name: 'productName'
            }, {
                name: 'productSpecification'
            }, {
                name: 'productPackUnitSmall'
            }, {
                name: 'manufName'
            },{
                name: 'amountSum'
            },{
                name: 'productContainTaxPrice',
                formatter: function (val) {
                    if(val||val === 0){
                        return parseFloat(val);
                    }else {
                        return '';
                    }

                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'productPackInStoreCount'
            },
            {
                name: 'thisInvoicedCount',
                formatter: function (val) {
                    if(val){
                        return parseFloat(val).toFixed(0);
                    }else {
                        return 0;
                    }
                }
            },{
                name: 'invoicedCount',
                formatter: function (val) {
                    if(val){
                        return val;
                    }else {
                        return 0;
                    }
                }
            },{
                name: 'procutNoTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'productTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'productContainTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }];
    }else{
        detailColNames = ['id', '账单号','账单行号','账单生成日期','采购单据号','采购单行号',
            '单据日期','明细行状态', '采购订单号',
            '商品编码', '商品名称', '规格',
            '小包装单位', '生产厂家', '库存数量', '含税单价', '小包装数量','本次开票数量','已开票数量',
            '金额合计', '税额合计', '价税合计'
        ];
        detailColModel = [{
            name: 'id',
            hidden: true,
            hidegrid: true
        },
            {
                name: 'stockOrderNo'
            },
            {
                name: 'billsSort'
            },
            {
                name: 'billTime'
            },
            {
                name: 'stockNo'
            },
            {
                name: 'stockLineNo'
            },
            {
                name: 'storeTime'
            },
            {
                name: 'ticketStatus',
                formatter: function (value) {
                    if (value != '') {
                        if (value == '1') {
                            return '待确认';
                        } else if (value == '2') {
                            return '待开票';
                        } else if (value == '3') {
                            return '确认有误';
                        } else if (value == '4') {
                            return '已开票';
                        } else if (value == '5') {
                            return '部分开票';
                        } else {
                            return '';
                        }
                    } else {
                        return '';
                    }
                }
            },

            {
                name: 'purchaseOrderNo'
            },

            {
                name: 'productCode'
            }, {
                name: 'productName'
            }, {
                name: 'productSpecification'
            }, {
                name: 'productPackUnitSmall'
            }, {
                name: 'manufName'
            },{
                name: 'amountSum'
            },{
                name: 'productContainTaxPrice',
                formatter: function (val) {
                    if(val||val === 0){
                        return parseFloat(val);
                    }else {
                        return '';
                    }

                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'productPackInStoreCount'
            },
            {
                name: 'thisInvoicedCount',
                formatter: function (val) {
                    if(val){
                        return parseFloat(val).toFixed(0);
                    }else {
                        return 0;
                    }
                }
            },{
                name: 'invoicedCount',
                formatter: function (val) {
                    if(val){
                        return val;
                    }else {
                        return 0;
                    }
                }
            },{
                name: 'procutNoTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'productTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'productContainTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }];
    }

    //入库单详情
    function initDialogTable(data) {
        data = $.map(data,function (item,index) {
            item.id = item.productCode
           return item
        });
        $('#X_Table1').XGrid({
            data: data,
            colNames: detailColNames,
            colModel: detailColModel,
            rowNum: 1000,
            rownumbers: true,//是否展示序号
            altRows: true, //设置为交替行表格,默认为false
            gridComplete: function () {
                var _this = this;
                setTimeout(function () {
                    var data = $('#X_Table1').XGrid('getRowData');
                    $('#X_Table1').XGrid('addRowData', {
                        id: "合计",
                        thisInvoicedCount: totalTableX(data, "thisInvoicedCount"),
                        invoicedCount: totalTableX(data, "invoicedCount"),
                        procutNoTaxMoney: totalTableY(data, "procutNoTaxMoney"),
                        productTaxMoney: totalTableY(data, "productTaxMoney"),
                        productContainTaxMoney: totalTableY(data, "productContainTaxMoney")
                    })
                    $('#X_Table1').find('tr:last-child').attr('id', '').find('td:first-child').html('合计');
                    $('#X_Table1').unbind('click');
                }, 200)
            }
            // pager: '#grid-pager',
        });
    }
    var colNames = [];
    var colModel = [];

    if ($("#invoiceType").val() == 1){
        colNames =  ['采购单据单号','业务类型','供应商编码','供应商','移动类型','制单人','单据日期', '金额合计', '税额合计','价税合计', 'id'];
        colModel = [
            {
                name: 'stockOrderNo'
            }, {
                name: 'channelName'
            },
            {
                name: 'supplierCode'
            },
            {
                name: 'supplierName'
            }, {
                name: 'moveTypeName'
            }, {
                name: 'createUser'
            },{
                name: 'storeTime'
            }, {
                name: 'sumNoTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }

            }, {
                name: 'sumTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'priceTaxSum',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'id',
                hidden: true
            }];
        }else{
        colNames =  ['账单号','业务类型','供应商编码','供应商','账单生成日期', '金额合计', '税额合计','价税合计', 'id'];
        colModel = [
            {
                name: 'stockOrderNo'
            }, {
                name: 'channelName'
            },
            {
                name: 'supplierCode'
            },
            {
                name: 'supplierName'
            },{
                name: 'storeTime'
            }, {
                name: 'sumNoTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }

            }, {
                name: 'sumTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'priceTaxSum',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'id',
                hidden: true
            }];
    }
    //已选择入库单
    $('#X_Tablea').XGrid(
        {
            data: storeInList,
            colNames: colNames,
            colModel: colModel,
            rowNum: 1000,
            key: 'id',
            rownumbers: true,//是否展示序号
            altRows: true, //设置为交替行表格,默认为false
            ondblClickRow: function (id, dom, obj, index, event) {
                var arr = [];
                $.each(storeInDetailList, function (index, item) {
                    if (item.stockOrderNo == obj.stockOrderNo || item.purchaseOrderNo == obj.stockOrderNo ) {
                        arr.push(item);
                    }
                })

                initDialogTable(arr);
                //双击事件回调函数
                utils.dialog({
                    title: '已选采购单据明细行',
                    width: 1000,
                    content: $('#modal')
                }).showModal();

            },
            gridComplete: function () {
                setTimeout(function () {
                    totalStoreInSum();
                }, 200)
            },

        });

    //合计采购单据
    function totalStoreInSum() {
        addtotalRow($('#X_Tablea'), {
            id: "合计",
            sumNoTaxMoney: "sumNoTaxMoney",
            sumTaxMoney: "sumTaxMoney",
            priceTaxSum: "priceTaxSum"
        })
    }


    // 增加合计行
    function addtotalRow($X_Table, rowData) {

        var tem = [],
            data = $X_Table.XGrid('getRowData');

        $.each(data, function (idnex, item) {
            if (item.id) {
                tem.push(item)
            }
        })

        data = tem;

        $.each($X_Table.find('tr'), function (index, item) {
            if ($(item).find('td:first-child').html() === '合计') {
                $(item).remove();
            }
        })
        for (var key in rowData) {
            if (rowData[key] && (key !== 'id')) {
                rowData[key] = totalTableY(data, key);
            }
        }
        $X_Table.XGrid('addRowData', rowData);
        $X_Table.find('tr:last-child').attr('id', '').find('td:first-child').html('合计');
    }

    //合计采购单据明细
    function totalTableY(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat(item[colName]);
        })
        return count.toFixed(2);
    }
    function totalTableX(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat(item[colName]);
        })
        return count.toFixed(0);
    }

    //发票明细
    $('#Y_Tablea').XGrid({
        data: invoiceDetailVoList,
        colNames: [ '供应商发票号','发票日期', '发票不含税金额','发票含税金额', '发票税额','备注'],
        colModel: [
            {
            name: 'supplierInvoiceNumber',
        }, {
            name: 'invoiceDate',
        }, {
            name: 'noInvoiceTaxAmount',
        }, {
            name: 'invoiceTaxAmount',
        }, {
            name: 'tax'
        }, {
            name: 'rates',
        }],
        key: 'id',
        rowNum: 1000,
         rownumbers: true,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {
            //双击事件回调函数
            if (id == '999') return false;

        },
        gridComplete: function () {
            setTimeout(function () {
                totalTable();
            }, 200)
        }

    });


    //合计
    function totalTable() {
        var data = $('#Y_Tablea').XGrid('getRowData');
        $('#Y_Tablea').XGrid('addRowData', {
            id: "合计",
            noInvoiceTaxAmount: totalTableY(data, "noInvoiceTaxAmount"),
            invoiceTaxAmount: totalTableY(data, "invoiceTaxAmount"),
            tax: totalTableY(data, "tax")
        })
        $('#Y_Tablea').find('tr:last-child').attr('id', '').find('td:first-child').html('合计');
        $('#Y_Tablea').unbind('click');
    }



    //返回
    $('#backInvoice').bind('click', function () {
        window.location.href = "/proxy-finance/finance/purchase/invoice/toList";
    })

    //更新序号
    function refreshIndex($Table) {
        var rn = $Table.find('td[row-describedby="id"]');
        $.each(rn, function (index, item) {
            if ($(item).html() !== '合计') {
                $(item).html(index + 1);
            }

        })
    }


    function batchSearch (id) {
        var idName = id+'_batchSearch',$ele = $('#'+id),eleBox;
        if($("#"+idName).length>0){
            eleBox = $("#"+idName);
        }else {
            $('body').append('<div class="autocomplete-suggestions" id="'+idName+'"></div>');
            eleBox = $("#"+idName);
            eleBox.css({
                'position': 'absolute',
                'max-height': '300px',
                'z-index': '9999',
                'box-sizing':'border-box',
                'display': 'block',
                'fout-size': '16px'
            })
        }
        $ele.on({
            input:function () {
                fixPosition();
                var val = $(this).val(),
                    valAry = val.split(';'),
                    eleList = '';
                $.each(valAry,function (index,item) {
                    if(item){
                        eleList += '<div class="autocomplete-suggestion" data-index="'+index+'" style="padding-right: 22px">'+item+'<span class="cancel" style="float: right;margin-right: -10px;cursor: pointer;" data-value="'+item+'">X</span></span></div>'
                    }
                });
                eleList ? eleBox.html(eleList).show() : eleBox.hide();;
            },
            focus:function () {
                $(this).trigger('input')
            },
            blur:function () {
                window[idName] = setTimeout(function () {
                    eleBox.hide();
                },200)
            }
        });
        eleBox.on('click',function (e) {
            window[idName] && clearTimeout(window[idName]);
            var $this = $(e.target);
            if($this.hasClass('cancel')){
                var valAry = $ele.val().split(';');
                valAry.splice(eleBox.find('.cancel').index($this),1);
                $ele.val(valAry.join(';')).trigger('input');
            }
        });
        function fixPosition() {
            eleBox.css({
                minWidth: $ele.outerWidth(),
                top: $ele.outerHeight() + $ele.offset().top + 'px',
                left: $ele.offset().left+ 'px',
            });
        }
    }
    batchSearch('input_goodName')

    $('#l_product_btn').bind('click', function () {
        var valAry = $("#input_goodName").val().split(';');
        var seleRow =$("#X_Table1").XGrid('getRowData');

        $.each(seleRow ,function(idx,it){
            if(it.id){
                $("#X_Table1").find("tr#"+it.id).removeClass('selRow');
                $.each(valAry,function (index,item) {
                    if(it.productCode == item){
                        $("#X_Table1").find("tr#"+it.id).addClass('selRow');
                    }
                });
            }
        })

    })


})
