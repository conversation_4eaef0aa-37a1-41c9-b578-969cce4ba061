    $('#X_Tableb').XGrid({
        url:"/middle/erp2WmsMiddle/queryIntWmsCgrkBillList",
        colNames: [  '单据编号','机构id','单据序号', '单位id','日期','采购员id', '收货员','质检员','操作员','采购订单单据编号','入库类型','商品id','数量', '件数','零散数', '批号','生产日期','有效期','采购订单明细序号', '验收评定','最后更新时间', '回传标识','自增编号','自动记账标识'],
        colModel: [
            {
                name: 'djbh',
                index: 'djbh',
                width: 100
            },  {
                name: 'yzid',
                index: 'yzid',
                width: 200
            }, {
                name: 'djSort',
                index: 'djSort',
                width: 200
            }, {
                name: 'dwbh',
                index: 'dwbh',
                width: 60
            },  {
                name: 'rq',
                index: 'rq',
                width: 200
            }, {
                name: 'cgy',
                index: 'cgy',
                width: 60
            },  {
                name: 'shy',
                index: 'shy',
                width: 200
            },  {
                name: 'zjy',
                index: 'zjy',
                width: 200
            },   {
                name: 'czy',
                index: 'czy',
                width: 200
            },  {
                name: 'djbhSj',
                index: 'djbhSj',
                width: 200
            },   {
                name: 'rktype',
                index: 'rktype',
                width: 200
            },  {
                name: 'spid',
                index: 'spid',
                width: 200
            }, {
                name: 'sl',
                index: 'sl',
                width: 200
            }, {
                name: 'js',
                index: 'js',
                width: 60
            },  {
                name: 'lss',
                index: 'lss',
                width: 200
            }, {
                name: 'ph',
                index: 'ph',
                width: 60
            },  {
                name: 'rqSc',
                index: 'rqSc',
                width: 60
            }, {
                name: 'yxqz',
                index: 'yxqz',
                width: 60
            }, {
                name: 'cgddSort',
                index: 'cgddSort',
                width: 60
            },  {
                name: 'yspd',
                index: 'yspd',
                width: 60
               /* ,
                formatter: function (val) {
                    var html = "";
                    if(val == 1){
                        html = '合格';
                    }else if(val == 2){
                        html = '不合格';
                    }else if(val == 5){
                        html = '拒收';
                    }else if(val == 4){
                        html = '代管';
                    }else if(val == 5){
                        html = '拒收';
                    }else if(val == 8){
                        html = '停售';
                    }
                    return html;
                }*/
            }, {
                name: 'lastmodifytime',
                index: 'lastmodifytime',
                width: 60
            },{
                name: 'isZx',
                index: 'isZx',
                width: 200
            }, {
                name: 'recnum',
                index: 'recnum',
                width: 200
            }, {
                name: 'zidongjizhang',
                index: 'zidongjizhang',
                width: 60
            }
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        multiselect: true,//是否多选
        pager: '#grid-pager',
        rownumbers: true,
    });

    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "djbh": $("#djbh").val().trim(),
                "yzid":$("#yzid").val().trim(),
                "spid":$("#spid").val().trim(),
                "isZx":$("#isZx").val().trim(),
                "dwbh": $("#dwbh").val().trim(),
                "rq":$("#rq").val().trim(),
                "cgy":$("#cgy").val().trim(),
                "ph":$("#ph").val().trim(),
               /* "startOrderDate":$("#startOrderDate").val().trim(),
                "endOrderDate":$("#endOrderDate").val().trim(),*/
            },page:1
        }).trigger('reloadGrid');
    });

    /**
     * 开票日期
     */
    function rqDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: '',
            isShowToday: false,
            isShowClear: false,
        });
    }
    /**
     * 开票日期 开始
     */
    function startOrderDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: getMonthStartDate(),
            isShowToday: false,
            isShowClear: false,
            maxDate:'#F{$dp.$D(\'endOrderDate\')}'
        });
        $("#endOrderDate").val("");
    }

    /**
     * 开票日期 结束
     */
    function endOrderDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: getMonthStartDate(),
            isShowToday: false,
            isShowClear: false,
            minDate:'#F{$dp.$D(\'startOrderDate\')}',
            maxDate: getMaxDate('#startOrderDate')
        });
        //$("#startOrderDate").val("");
    }

    function getMaxDate(id){
        return $(id).val().split('-')[0]+'-12-31';
    }
    /**
     * 记账日期 开始
     */
    function startPostDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: getMonthStartDate(),
            isShowToday: false,
            isShowClear: false,
            maxDate:'#F{$dp.$D(\'endPostDate\')}'
        });
        $("#endPostDate").val("");
    }

    /**
     * 记账日期 结束
     */
    function endPostDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: getMonthStartDate(),
            isShowToday: false,
            isShowClear: false,
            minDate:'#F{$dp.$D(\'startPostDate\')}',
            maxDate: getMaxDate('#startPostDate')
        });
        // $("#startPostDate").val("");
    }

    /**
     * 获取当前时间，格式YYYY-MM-DD
     * @returns {string}
     */
    function getNowFormatDate() {
        var date = new Date();
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + seperator1 + month + seperator1 + strDate;
        return currentdate;
    }

    //获得本月的开始日期
    function getMonthStartDate(){
        var now = new Date();                    //当前日期
        var nowDayOfWeek = now.getDay();         //今天本周的第几天
        var nowDay = now.getDate();              //当前日
        var nowMonth = now.getMonth();           //当前月
        var nowYear = now.getYear();             //当前年
        nowYear += (nowYear < 2000) ? 1900 : 0;  //
        var monthStartDate = new Date(nowYear, nowMonth, 1);
        return formatDate(monthStartDate);
    }

    //格式化日期：yyyy-MM-dd
    function formatDate(date) {
        var myyear = date.getFullYear();
        var mymonth = date.getMonth() + 1;
        var myweekday = date.getDate();

        if (mymonth < 10) {
            mymonth = "0" + mymonth;
        }
        if (myweekday < 10) {
            myweekday = "0" + myweekday;
        }
        return (myyear + "-" + mymonth + "-" + myweekday);
    }