var win_dialog = null, initDataArr = null;
$(function () {
    win_dialog = parent.dialog.get(window);
    initDataArr=win_dialog.data.initDataArr;
    console.log(initDataArr)
})
var selArr=[];

var lockPageFlag = $("#lockPageFlag").val();
$('#X_Tableb').XGrid({
    url:"/proxy-supplier/supplier/supplierLockApprovalRecord/supplierLockOrganBaseList/"+lockPageFlag,
    postData: {
        queryFields:$("#search_vl").val(),
        auditStatus: 2
    },
    colNames: ['', '供应商编码', '供应商名称', '供应商类型', '营业执照号','','',''],
    colModel: [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        }, {
            name: 'supplierCode',
            index: 'supplierCode',
            width: 200

        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 150
        }, {
            name: 'supplierTypeName',
            index: 'supplierTypeName',
            width: 120
        }, {
            name: 'supplierBusinessNum',
            index: 'supplierBusinessNum',
            width: 150
        }, {
            name: 'isLock',
            index: 'isLock',
            width: 250,
            hidden: true
        },{
            name: 'supplierTypeId',
            index: 'supplierTypeId',
            width: 250,
            hidden: true
        },{
            name: 'orgCode',
            index: 'orgCode',
            hidden:true
        }
    ],
    viewrecords: true,//是否在浏览导航栏显示记录总数
    rownumbers: true,//是否展示序号
    multiselect: true,
    rowNum: 20,
    rowList:[20,50,100],
    
    altRows: true,//设置为交替行表格,默认为false
    pager: '#grid-pager',
    onSelectRow: function (id, dom, obj, index, event) {
        //添加已选商品
        var $tr = $(dom);
        var check=$tr.find('[row-describedby="ck"] input').prop("checked");
        getCheckData(check,obj);
    },
    gridComplete: function () {
        setTimeout(function () {
            $("#X_Tableb th:first input").hide();
            if(initDataArr.length > 0)
            {
                var arr=initDataArr;
                for(var i=0;i<arr.length;i++)
                {
                    $('#X_Tableb tr:not("first")').each(function () {
                        var id=$(this).attr("id");
                        if(id == arr[i].id)
                        {
                            $(this).find("td[row-describedby='ck'] input").prop("checked",true).trigger('input');
                        }
                    })
                }
            }
        },3)
    }
});
//全选
$('body').on('click','#grid_checked input',function () {
    let checkState = $(this).prop('checked');
    let data = [];
    if(checkState){
        data = $('#X_Tableb').getRowData();
        selArr = data;
    }else{
        selArr = [];
    }
})

//添加已选商品
$('#X_Tableb').on("change","td[row-describedby='ck'] input",function(ev){
    var check=this.checked;
    var $tr = $(this).parents('tr');
    var id=$tr.attr('id');
    var data=$('#X_Tableb').XGrid('getRowData', id);
    getCheckData(check,data);
    ev.stopPropagation();
})
// todo 搜索补全优化
$("#SearchBtn").on("click", function () {
    $('#X_Tableb').XGrid('setGridParam', {
        postData: {
            "queryFields": $("#search_vl").val(),
            "auditStatus": 2,
           // "auditStatus":$("#auditStatus").val(),
           // "maxRemainDays":$("#maxRemainDays").val(),
            //"minRemainDays":$("#minRemainDays").val()
        },page:1
    }).trigger('reloadGrid');
});
//选择按钮
$("#SelectBtn").on("click", function () {
    selArr = $('#X_Tableb').XGrid('getSeleRow');
	var organBaseIds=[];
    if(selArr.length > 0){
        for(var i=0;i<selArr.length;i++){
        	organBaseIds.push(selArr[i].id);
            selArr[i].isLock_a=selArr[i].isLock;
            selArr[i].keyId=selArr[i].id;
            selArr[i].supplierType=selArr[i].supplierTypeId;
            selArr[i].supplierOrganId=selArr[i].id;
        }
    }
   console.log(selArr)
   win_dialog.close(selArr);

    

});
//组装已选数据
function getCheckData(check,data){
    if(check)
    {
        if(!fidInArr(data.id)){
            selArr.push(data);
        }
    }else{
        for(var i=0;i<selArr.length;i++){
            if(selArr[i].id == data.id){
                selArr.splice(i,1);
                break;
            }
        }
    }
}
//数组中查找id
function fidInArr(id){
    for(var i=0;i<selArr.length;i++){
        if(selArr[i].id == id){
            return true;
        }
    }
    return false;
}






