    $('#X_Tableb').XGrid({
        url:"/middle/erp2WmsMiddle/queryWMSTransferLibraryList",
        colNames: [ '序号','商品id','数量', '老库存状态','新库存状态','老库', '新库','批号','业主内码', '生产日期','有效期至','改变时间','更新时间', '提取状态'],
        colModel: [
            {
                name: 'sp_sort',
                index: 'sp_sort',
                width: 100
            },  {
                name: 'spid',
                index: 'spid',
                width: 200
            }, {
                name: 'sl_kc',
                index: 'sl_kc',
                width: 200
            }, {
                name: 'kczt_old',
                index: 'kczt_old',
                width: 60,
                formatter: function (val) {
                    var html = "";
                    if(val == 1){
                        html = '合格(良品、正常品、可销售)';
                    }else if(val == 2){
                        html = '不合格(残品、不良品)';
                    }else if(val == 3){
                        html = '待处理';
                    }else if(val == 4){
                        html = '代管';
                    }else if(val == 5){
                        html = '拒收';
                    }else if(val == 8){
                        html = '停售';
                    }
                    return html;
                }
            },  {
                name: 'kczt_new',
                index: 'kczt_new',
                width: 200,
                formatter: function (val) {
                    var html = "";
                    if(val == 1){
                        html = '合格(良品、正常品、可销售)';
                    }else if(val == 2){
                        html = '不合格(残品、不良品)';
                    }else if(val == 3){
                        html = '待处理';
                    }else if(val == 4){
                        html = '代管';
                    }else if(val == 5){
                        html = '拒收';
                    }else if(val == 8){
                        html = '停售';
                    }
                    return html;
                }
            }, {
                name: 'kfbh_old',
                index: 'kfbh_old',
                width: 200
            }, {
                name: 'kfbh_new',
                index: 'kfbh_new',
                width: 60
            },  {
                name: 'ph',
                index: 'ph',
                width: 200
            },  {
                name: 'yzid',
                index: 'yzid',
                width: 200
            }, {
                name: 'rq_sc',
                index: 'rq_sc',
                width: 200
            }, {
                name: 'yxqz',
                index: 'yxqz',
                width: 60
            },  {
                name: 'change_time',
                index: 'change_time',
                width: 200
            }, {
                name: 'lastmodifytime',
                index: 'lastmodifytime',
                width: 60
            }, {
                name: 'is_zx',
                index: 'is_zx',
                width: 60
            }
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        multiselect: true,//是否多选
        pager: '#grid-pager',
        rownumbers: true,
    });

    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "spid": $("#spid").val().trim(),
                "yzid":$("#yzid").val().trim(),
                "is_zx":$("#is_zx").val().trim(),
                "ph":$("#ph").val().trim(),
               /* "startOrderDate":$("#startOrderDate").val().trim(),
                "endOrderDate":$("#endOrderDate").val().trim(),*/
            },page:1
        }).trigger('reloadGrid');
    });

    /**
     * 开票日期
     */
    function rqDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: '',
            isShowToday: false,
            isShowClear: false,
        });
    }
    /**
     * 开票日期 开始
     */
    function startOrderDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: getMonthStartDate(),
            isShowToday: false,
            isShowClear: false,
            maxDate:'#F{$dp.$D(\'endOrderDate\')}'
        });
        $("#endOrderDate").val("");
    }

    /**
     * 开票日期 结束
     */
    function endOrderDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: getMonthStartDate(),
            isShowToday: false,
            isShowClear: false,
            minDate:'#F{$dp.$D(\'startOrderDate\')}',
            maxDate: getMaxDate('#startOrderDate')
        });
        //$("#startOrderDate").val("");
    }

    function getMaxDate(id){
        return $(id).val().split('-')[0]+'-12-31';
    }
    /**
     * 记账日期 开始
     */
    function startPostDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: getMonthStartDate(),
            isShowToday: false,
            isShowClear: false,
            maxDate:'#F{$dp.$D(\'endPostDate\')}'
        });
        $("#endPostDate").val("");
    }

    /**
     * 记账日期 结束
     */
    function endPostDateFun() {
        WdatePicker({
            dateFmt: 'yyyy-MM-dd',
            startDate: getMonthStartDate(),
            isShowToday: false,
            isShowClear: false,
            minDate:'#F{$dp.$D(\'startPostDate\')}',
            maxDate: getMaxDate('#startPostDate')
        });
        // $("#startPostDate").val("");
    }

    /**
     * 获取当前时间，格式YYYY-MM-DD
     * @returns {string}
     */
    function getNowFormatDate() {
        var date = new Date();
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + seperator1 + month + seperator1 + strDate;
        return currentdate;
    }

    //获得本月的开始日期
    function getMonthStartDate(){
        var now = new Date();                    //当前日期
        var nowDayOfWeek = now.getDay();         //今天本周的第几天
        var nowDay = now.getDate();              //当前日
        var nowMonth = now.getMonth();           //当前月
        var nowYear = now.getYear();             //当前年
        nowYear += (nowYear < 2000) ? 1900 : 0;  //
        var monthStartDate = new Date(nowYear, nowMonth, 1);
        return formatDate(monthStartDate);
    }

    //格式化日期：yyyy-MM-dd
    function formatDate(date) {
        var myyear = date.getFullYear();
        var mymonth = date.getMonth() + 1;
        var myweekday = date.getDate();

        if (mymonth < 10) {
            mymonth = "0" + mymonth;
        }
        if (myweekday < 10) {
            myweekday = "0" + myweekday;
        }
        return (myyear + "-" + mymonth + "-" + myweekday);
    }