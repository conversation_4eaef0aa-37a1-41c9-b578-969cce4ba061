var selEl = null;
/* 合计计算 */
var totalTable = z_utils.totalTable;

$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);


    /* table_ a */
    //data
    var colName = ['id','退货原因', '销售退回单号','EC退回单号','商品编码' ,'原商品编码','商品名称', '商品规格', '生产厂家', '产地', '单位', '库房名称','batchBaseVoList', '批号', '生产日期', '有效期至', '本次退回数量','可退数量',
        '实际退货数量', '含税价', '实付价', '价税合计', '实付金额', '活动明细优惠金额', '余额抵扣明细优惠金额','退回返利金额', '税率', '税额','原活动优惠金额','原余额优惠金额','原返利优惠金额','ec订单明细id' ,'业务类型', '是否编辑'
    ];
    var colModel = [{ name: 'id',    index: 'id', hidden:true,hidegrid:true   },
        { name: 'returnReason',    index: 'returnReason',    rowtype: '#sort_e',
            formatter:function (e) {

                if(e==1){
                    return '整单拒收';
                }else if(e==2){
                    return '药品漏发';
                }else if(e==3){
                    return '药品错发';
                }else if(e==4){
                    return '药品破损';
                }else if(e==5){
                    return '效期不好';
                }else if(e==6){
                    return '批号不符';
                } else if(e==7){
                    return '税票有误';
                }else if(e==8){
                    return '无检验报告单';
                }else if(e==9){
                    return '采购价偏高';
                } else if(e==10){
                    return '实物与图片不符';
                }else if(e==11){
                    return '采购单重复';
                }else if(e==12){
                    return '商品其他质量问题';
                }else if(e==13){
                    return '拦截';
                }else if(e==14){
                    return '缺货未发';
                }else if(e==15){
                    return '召回';
                }else if(e==16){
                    return '水剂不发货';
                }else {
                    return e;
                }
            },
            rowEvent:function(row){
                var $input = $(row.e.target);
                var rowData = row.rowData;
                if ((rowData.returnReason != null || rowData.returnReason != "" )&&rowData.returnReason.length>=20){
                    utils.dialog({
                        title: '提示',
                        content: '退货原因不能超过20个字符',
                        width: '300',
                        cancel: false,
                        okValue: '确认',
                        ok: function () {

                        }
                    }).showModal();
                    $('#table_a').XGrid('setRowData',rowData.id,{ returnReason: rowData.returnReason.substring(0,20)});
                    return
                }
            }
        },
        {    name: 'salesReturnCode',    index: 'salesReturnCode', hidden:true ,hidegrid:true  },
        {    name: 'refundApplyCode',    index: 'refundApplyCode', hidden:true ,hidegrid:true  },
        {    name: 'productCode',    index: 'productCode'  },
        {    name: 'oldProductCode',    index: 'oldProductCode'  },
        {    name: 'productName',    index: 'productName'  },
        {    name: 'specifications',    index: 'specifications'  },
        {    name: 'manufacturer',    index: 'manufacturer'  },
        {    name: 'productOrigin',    index: 'productOrigin'  },
        {    name: 'productUnit',    index: 'productUnit'  },
        {    name: 'warehouseName',    index: 'warehouseName',formatter:function (e) {
                if(e==1){
                    return '合格库';
                }else if(e==2){
                    return '不合格库';
                }else if(e==3){
                    return '暂存库';
                }else{
                    return e;
                }
            },unformat:function (e) {
                if(e=='合格库'){
                    return 1;
                }else if(e=='不合格库'){
                    return 2;
                }else if(e=='暂存库'){
                    return 3;
                }else{
                    return '';
                }
            } },
        {    name: 'batchBaseVoList',    index: 'batchBaseVoList',hidden:true,hidegrid:true ,  formatter:function (result) {
                //console.log('e',e)
                if(typeof result == 'string'){
                    return result;
                }else {
                    return JSON.stringify(result);
                }

            } },
        {    name: 'batchCode',    index: 'batchCode',
            formatter:function (result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '--'
                } else {
                    return result
                }
                // var batchBaseVoList = rowData.batchBaseVoList;
                // setTimeout(function () {
                //     console.log(typeof batchBaseVoList);
                //     var ary = [];
                //     if($(id+'_'+rowData.id+' select>option').length>1)return;
                //     if(typeof batchBaseVoList == 'string'){
                //         ary = JSON.parse(batchBaseVoList);
                //     }else {
                //         ary = batchBaseVoList;
                //     }
                //     ary.forEach(function (item,index) {
                //         $(id+'_'+rowData.id+' select').append(`<option value="${item.batchCode}">${item.batchCode}</option>`);
                //     });
                // },200)
                //
                // return result
            },
            unformat: function (e) {
                return e == '--' ? '' : e
            }
            // rowEvent:function (event) {
            //     var rowData = event.rowData;
            //     var batchCode = rowData.batchCode;
            //     var batchBaseVoList = JSON.parse(rowData.batchBaseVoList);
            //     batchBaseVoList.forEach(function (item,index) {
            //         if(batchCode==item.batchCode){
            //             $('#table_a').XGrid('setRowData',rowData.id,{ productionTime: item.productionTime, periodValidity: item.periodValidity,oldactivityDiscountAmount:item.activityDiscountAmount,oldbalanceDiscountAmount:item.balanceDiscountAmount,oldamounOfRebate:item.amounOfRebate,actualReturnNumber:item.outStoreNumber});
            //             console.log(item)
            //         }
            //     });
            // }
        },
        {    name: 'productionTime',    index: 'productionTime',
            formatter: function(result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '-'
                } else {
                    return dateFormatter(result)
                }
            },
            unformat: function (e) {
                return e == '-' ? null : e
            }
        },
        {    name: 'periodValidity',    index: 'periodValidity' ,
            formatter: function(result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '-'
                } else {
                    return dateFormatter(result)
                }
            },
            unformat: function (e) {
                return e == '-' ? null : e
            }
        },
        {    name: 'returnsNumber',    index: 'returnsNumber'  },
        {    name: 'mayReturnNumber',    index: 'mayReturnNumber'  },
        {    name: 'actualReturnNumber',
            index: 'actualReturnNumber',
            // rowtype: function () {
            //     return '#text12_e'
            // },
            formatter:function (result,id,rowData) {
                console.log('result', result);
                console.log('rowData', rowData);
                if (rowData['editDetailRow']) {
                    return {
                        val: '-',
                        rowtype: ''
                    }
                } else {
                    return {
                        val: result,
                        rowtype: '#text12_e'
                    }
                }
            },
            unformat: function (e) {
                return (e.trim() == '' || e == '-') ? 0 : e
            },
            rowEvent:function(row){
                var input_id = $(row.e.target).parent().attr('id');
                var $input = $(row.e.target);
                var rowData = row.rowData;
                // if (rowData.batchCode == null || rowData.batchCode == "" ){
                //     utils.dialog({
                //         title: '提示',
                //         content: '请选择批号',
                //         width: '300',
                //         cancel: false,
                //         okValue: '确认',
                //         ok: function () {
                //
                //         }
                //     }).showModal();
                //     $input.val(0);
                //     return
                // }

                if(rowData.batchCode != null || rowData.batchCode != ""){
                    var actualReturnNumber = $input.val() // rowData.actualReturnNumber;
                    if (actualReturnNumber.length != 0 && actualReturnNumber.indexOf('0') == 0) {
                        actualReturnNumber = actualReturnNumber.substring(1)
                    }
                    var returnNumber = rowData.returnsNumber;
                    var taxPrice = rowData.taxPrice;
                    var activityDiscountAmount = rowData.oldactivityDiscountAmount;
                    var balanceDiscountAmount = rowData.oldbalanceDiscountAmount;
                    var amounOfRebate = rowData.oldamounOfRebate;
                    var payPrice  = rowData.paymentPrice;
                    if ( actualReturnNumber == '' ||  actualReturnNumber  == null){
                        actualReturnNumber = 0;
                    }
                    if ( activityDiscountAmount == '' ||  activityDiscountAmount  == null){
                        activityDiscountAmount = 0;
                    }
                    if ( balanceDiscountAmount == '' ||  balanceDiscountAmount  == null){
                        balanceDiscountAmount = 0;
                    }
                    if ( amounOfRebate == '' ||  amounOfRebate  == null){
                        amounOfRebate = 0;
                    }
                    rowData.taxAmount =  (actualReturnNumber * taxPrice).toFixed(2);
                    rowData.paymentAmount = (actualReturnNumber * payPrice).toFixed(2);
                    rowData.activityDiscountAmount = (activityDiscountAmount*(actualReturnNumber/returnNumber)).toFixed(2);
                    rowData.balanceDiscountAmount = (balanceDiscountAmount*(actualReturnNumber/returnNumber)).toFixed(2);
                    rowData.amounOfRebate = (amounOfRebate*(actualReturnNumber/returnNumber)).toFixed(2);
                    // $input.val(actualReturnNumber);
                    var rate = rowData.rate;
                    var tax = 0;
                    if (rate != '' && rate != null && rate != undefined){
                        rate = rate.replace("%" , "")
                        var notaxPrice = (taxPrice/(1+ rate/100)).toFixed(2)
                        tax = (rowData.taxAmount - notaxPrice* actualReturnNumber).toFixed(2);
                    }
                    $('#table_a').XGrid('setRowData',rowData.id,
                        {taxAmount:rowData.taxAmount,
                            paymentAmount:rowData.paymentAmount,
                            activityDiscountAmount:rowData.activityDiscountAmount,
                            balanceDiscountAmount:rowData.balanceDiscountAmount,
                            amounOfRebate:rowData.amounOfRebate,
                            rate:rate,
                            tax:tax,
                            ecOrderDetailId:rowData.ecOrderDetailId,
                            actualReturnNumber: actualReturnNumber
                        });

                    var data = $('#table_a').XGrid('getRowData');
                    var sum_ele = $('#table_a_sum .sum');
                    //console.log(sum_ele);
                    $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                    $(sum_ele[1]).text(totalTable(data, 'tax'));
                    $(sum_ele[2]).text(totalTable(data, 'paymentAmount'));
                    $('#table_a #'+rowData.id).find('[row-describedby="actualReturnNumber"] input').val("").focus().val(actualReturnNumber)
                }

            }
        },
        {    name: 'taxPrice',    index: 'taxPrice'  , formatter: priceFormatter},
        {    name: 'paymentPrice',    index: 'paymentPrice'  ,formatter: priceFormatter},
        {    name: 'taxAmount',    index: 'taxAmount' ,
            formatter: function(result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '-'
                } else {
                    return priceFormatter(result)
                }
            },
            unformat: function (e) {
                return e == '-' ? 0 : e
            }
        },
        {    name: 'paymentAmount',    index: 'paymentAmount' ,
            formatter: function(result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '-'
                } else {
                    return priceFormatter(result)
                }
            },
            unformat: function (e) {
                return e == '-' ? 0 : e
            }
        },
        {    name: 'activityDiscountAmount',    index: 'activityDiscountAmount',
            formatter: function(result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '-'
                } else {
                    return priceFormatter(result)
                }
            },
            unformat: function (e) {
                return e == '-' ? 0 : e
            }
        },
        {    name: 'balanceDiscountAmount',    index: 'balanceDiscountAmount' ,
            formatter: function(result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '-'
                } else {
                    return priceFormatter(result)
                }
            },
            unformat: function (e) {
                return e == '-' ? 0 : e
            }
        },
        {    name: 'amounOfRebate',      index: 'amounOfRebate',
            formatter: function(result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '-'
                } else {
                    return priceFormatter(result)
                }
            },
            unformat: function (e) {
                return e == '-' ? 0 : e
            }
        },
        {    name: 'rate',    index: 'rate',
            formatter: function (e) {
                if ( e != undefined && e != null ) {
                    return e+ "%"
                } else {
                    return ''
                }
            }
        },
        {    name: 'tax',    index: 'tax',
            formatter: function(result,id,rowData) {
                if (rowData['editDetailRow']) {
                    return '-'
                } else {
                    return result
                }
            },
            unformat: function (e) {
                return e == '-' ? 0 : e
            }
        },

        {    name: 'oldactivityDiscountAmount',    index: 'oldactivityDiscountAmount',hidden:true,hidegrid:true},
        {    name: 'oldbalanceDiscountAmount',    index: 'oldbalanceDiscountAmount',hidden:true,hidegrid:true },
        {    name: 'oldamounOfRebate',      index: 'oldamounOfRebate',hidden:true,hidegrid:true},
        {    name: 'ecOrderDetailId',      index: 'ecOrderDetailId' ,hidden:true,hidegrid:true},
        {    name: 'channelId',      index: 'channelId' },
        {    name: 'editDetailRow',
            index: 'editDetailRow',
            formatter: function (e) {
                if (e) {
                    return `<p class="btn_editTable"><button class="btn btn-primary btn-small">编辑</button></p>`
                } else {
                    return ''
                }
            }, unformat: function (e) {
                return e == '' ? 0 : 1;
            }
        }
    ];

    $('#batchBaseVoListTable').XGrid({
        data: [],
        colNames: ['退货原因','批号', '生产日期', '有效期至', '可退数量', '实际退货数量','含税价','实付价', '活动明细优惠金额', '余额抵扣明细优惠金额', '退回返利金额', '价税合计', '实付金额', '税额'],
        colModel: [
            { name: 'returnReason',index: 'returnReason',rowtype: '#sort_e',
                rowEvent:function(row){
                    var $input = $(row.e.target);
                    var rowData = row.rowData;
                    if ((rowData.returnReason != null || rowData.returnReason != "" )&&rowData.returnReason.length>=20){
                        utils.dialog({
                            title: '提示',
                            content: '退货原因不能超过20个字符',
                            width: '300',
                            cancel: false,
                            okValue: '确认',
                            ok: function () {

                            }
                        }).showModal();
                        $('#table_a').XGrid('setRowData',rowData.id,{ returnReason: rowData.returnReason.substring(0,20)});
                        return
                    }
                }
            },
            {
                name: 'batchCode',
                index: 'batchCode'
            },
            {
                name: 'productionTime',
                index: 'productionTime'
            },
            {
                name: 'periodValidity',
                index: 'periodValidity'
            },
            {
                name: 'mayReturnNumber',
                index: 'mayReturnNumber'
            },
            {
                name: 'actualReturnNumber',
                index: 'actualReturnNumber',
                rowtype: '#actualReturnNumberInput'
            },
            {
                name: 'taxPrice',
                index: 'taxPrice'
            },
            {
                name: 'realPayPrice',
                index: 'realPayPrice'
            },
            {
                name: 'activityDiscountAmount',
                index:'activityDiscountAmount',
            },
            {
                name: 'balanceDiscountAmount',
                index:'balanceDiscountAmount',
            },
            {
                name: 'amounOfRebate',
                index:'amounOfRebate',
            },
            {
                name: 'taxAmount',
                index:'taxAmount'
            },
            {
                name: 'paymentAmount',
                index:'paymentAmount'
            },
            {
                name: 'tax',
                index:'tax'
            }
        ],
        key: 'id',
        altRows: true, //设置为交替行表格,默认为false
        gridComplete: function () {
            setTimeout(() => {
                $(this).find('[row-describedby="returnReason"] input').prop('readonly', true)
            }, 100)
        },
    })

    $('body').on('click', '#table_a .btn_editTable', function () {
        let trId = $(this).parents('tr').attr('id');
        let productCode = $('#' + trId).find('[row-describedby="productCode"]').text(),
            productName = $('#' + trId).find('[row-describedby="productName"]').text(),
            returnsNumber = $('#' + trId).find('[row-describedby="returnsNumber"]').text(),
            returnReason = $('#' + trId).find('[row-describedby="returnReason"] input').val(),

            taxPrice = $('#' + trId).find('[row-describedby="taxPrice"]').text(),
            rate = $('#' + trId).find('[row-describedby="rate"]').text(),
            batchBaseVoList = JSON.parse($('#' + trId).find('[row-describedby="batchBaseVoList"]').text()),
            oldactivityDiscountAmount = $('#' + trId).find('[row-describedby="oldactivityDiscountAmount"]').text(),
            oldbalanceDiscountAmount = $('#' + trId).find('[row-describedby="oldbalanceDiscountAmount"]').text(),
            oldamounOfRebate = $('#' + trId).find('[row-describedby="oldamounOfRebate"]').text()

        $('#taxPrice_tag').text(taxPrice)
        $('#rate_tag').text(rate)
        $('#returnReason_tag').text(returnReason)
        $('#oldamounOfRebate_tag').text(oldamounOfRebate)
        $('#oldbalanceDiscountAmount_tag').text(oldbalanceDiscountAmount)
        $('#oldactivityDiscountAmount_tag').text(oldactivityDiscountAmount)

        $('#batchBaseVoListTable_content #productCode_tag').text(productCode)
        $('#batchBaseVoListTable_content #productName_tag').text(productName)
        $('#batchBaseVoListTable_content #returnsNumber_tag').text(returnsNumber)

        $(batchBaseVoList).each((index, item) => {
            item['returnReason'] = returnReason
        })
        $('#batchBaseVoListTable').XGrid('setGridParam', {
            data: batchBaseVoList
        }).trigger("reloadGrid");
        utils.dialog({
            title: '商品编辑',
            width: $(window).width() * 0.7,
            content: $('#batchBaseVoListTable_content'),
            button: [
                {
                    value: '保存',
                    callback: function () {
                        let actualReturnNumberSum = $('#batchBaseVoListTable').XGrid('getRowData').map(item => item['actualReturnNumber']).reduce((total, cut) => Number(cut) + Number(total))
                        if (actualReturnNumberSum != Number($('#returnsNumber_tag').text())) {
                            utils.dialog({content: '实际退货数量之和需要等于本次退回数量.', quickClose: true, timeout: 2000}).showModal()
                            return false
                        }
                        // 剔除 实际可退数量为0的 数据不要往回传
                        let returnData = $('#batchBaseVoListTable').XGrid('getRowData')
                        // returnData = returnData.filter(item => item['actualReturnNumber'])
                        $('#table_a').XGrid('setRowData',trId,{ batchBaseVoList: JSON.stringify(returnData)});
                        setTableSum()

                        this.close().remove()
                    },
                    autofocus: true
                },
                {
                    value: '取消',
                    callback: function () {}
                }
            ]
        }).showModal()
    })

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }

    function priceFormatter(e){
        if (e != null && e != '') {
            var array = e.toString().split(".");
            if (array.length == 1){
                return e.toString() + ".00"
            }else if (array.length ==2){
                var elngt2 = array[1].length;
                if (elngt2 == 1){
                    return e.toString()+"0"
                }else {
                    return e;
                }
            }else {
                return e;
            }
        }else {
            return e;
        }


    }

    $('#table_a').XGrid({
        url:"/proxy-order/order/orderReturn/orderReturnController/getProductList?salesReturnCode=" + $("#salesReturnCode").val(),
        colNames: colName,
        colModel: colModel,
        selectandorder: false,
        key: 'id',
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function () {
            setTimeout(function (param) {
                /* 合计写入 */
                setTableSum()
                // var data = $('#table_a').XGrid('getRowData');
                // let _data = data
                // $(_data).each((index, item) => {
                //     let batchBaseVoList = JSON.parse(item['batchBaseVoList'])
                //     let taxAmount_num_Ary = batchBaseVoList.map(item => Number(item['taxAmount']))
                //     let tax_num_num_Ary = batchBaseVoList.map(item => Number(item['tax']))
                //     let paymentAmount_num_Ary = batchBaseVoList.map(item => Number(item['paymentAmount']))
                //     let taxAmount_num = taxAmount_num_Ary.reduce((total, cur) => Number(total) + Number(item['taxAmount']),0)
                //     let tax_num = tax_num_num_Ary.reduce((total, cur) => Number(total) + Number(item['tax']), 0)
                //     let paymentAmount_num = paymentAmount_num_Ary.reduce((total, cur) => Number(total) + Number(item['paymentAmount']), 0)
                //     item['taxAmount'] = taxAmount_num
                //     item['tax'] = tax_num
                //     item['paymentAmount'] = paymentAmount_num
                // })
                // var sum_ele = $('#table_a_sum .sum');
                // //console.log(sum_ele);
                // $(sum_ele[0]).text(totalTable(_data, 'taxAmount'));
                // $(sum_ele[1]).text(totalTable(_data, 'tax'));
                // $(sum_ele[2]).text(totalTable(_data, 'paymentAmount'));
            }, 200)
        },
        onSelectRow: function (id, dom, obj, index, event) {
            /*  var inp_salesOutOrderCode = $('#inp_salesOutOrderCode').val();
              var productCode = obj.productCode;
              var ts1 = [{value: 'Andorra',data: '123',abc: 'AD'},
                  {value: 'Zimbabwe',data: '123',abc: 'AD'},
                  {value: '2Andorra',data: '345',abc: 'AD'}];

              selEl = $('#text8_e_'+id);
              var html = '';
              for(var i = 0; i < ts1.length; i++){
                  html += '<option value="'+ts1[i].data+'" data-val="'
                      +ts1[i].value+'"  data-val2="'+ts1[i].abc+'">'
                      +ts1[i].data
                      +'</option>'
              }
              $('#text8_e_'+id).find('select').html(html);
              // $.ajax({
              //     url:'/orderReturn/orderReturnController/getBatchCode',
              //     data:{
              //         salesOutOrderCode:inp_salesOutOrderCode,
              //         productCode:productCode
              //     },
              //     success:function (res) {
              //         console.log(res)
              //
              //     }
              // })





              // $(selEl).on('change',function () {
              //     console.log('11111111111')
              // })*/
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            //console.log(id, dom, obj, index, event)
        }
    });

    //
    $(selEl).on('change',function () {
        console.log('1111111111')
    })

    /* 提交审核 */
    $('#sub_check').on('click', function () {
        var form_data = $('#form_a').serializeToJSON();
        let sum_ele = $('#table_a_sum .sum');
        form_data.taxAmount = $(sum_ele[0]).text()
        form_data.tax = $(sum_ele[1]).text()
        form_data.paymentAmount = $(sum_ele[2]).text()
        var table_data = $('#table_a').XGrid('getRowData');
        var formData = JSON.stringify(form_data);
        // table_data = table_data.map(function (item,index) {
        //     delete item.batchBaseVoList;
        //     return item
        // });
        var tableData = JSON.stringify(table_data);
        var  checkDate = true;
        let wrap_table_data = table_data.filter(item => !item['editDetailRow'])
        var sum_data = [];
        for(var i=0;i<wrap_table_data.length;i++){
            var flag = false;
            sum_data = sum_data.map(function (item,index) {
                if(item.productCode == wrap_table_data[i].productCode){
                    item.actualReturnNumber = Number(item.actualReturnNumber)+Number(wrap_table_data[i].actualReturnNumber);
                    flag = true;
                }
                return item
            });
            if(!flag){
                sum_data.push({productCode:wrap_table_data[i].productCode,actualReturnNumber:wrap_table_data[i].actualReturnNumber,returnsNumber:wrap_table_data[i].returnsNumber});
            }
            if(wrap_table_data[i].batchCode==''){
                utils.dialog({
                    title: '温馨提示',
                    content: '存在未选择的批号',
                    okValue: '确定',
                    ok: function () {},
                    cancelValue: '取消',
                    cancel: function () {}
                }).showModal()
                checkDate = false;
                return;
            }



        }
        sum_data.forEach(function (item,index) {
            if (!item.actualReturnNumber) {
                utils.dialog({
                    title: '温馨提示',
                    content: '请输入商品编号（'+item.productCode+'）的实际退货数量',
                    okValue: '确定',
                    ok: function () {},
                    cancelValue: '取消',
                    cancel: function () {}
                }).showModal()
                checkDate = false;
                return;
            }
            /*if(Number(item.returnsNumber)!=Number(item.actualReturnNumber)){
                utils.dialog({
                    title: '温馨提示',
                    content: '商品编号（'+item.productCode+'）的实际退回数量必须等于本次退回数量',
                    okValue: '确定',
                    ok: function () {},
                    cancelValue: '取消',
                    cancel: function () {}
                }).showModal()
                checkDate = false;
                return;
            }*/

        });
        // 数据提交校验
        // 某商品实际退货数量>可退数量
        let validAry1 = wrap_table_data.filter(item => Number(item['actualReturnNumber']) > Number(item['mayReturnNumber']))
        // 某商品实际退货数量<0
        let validAry2 = wrap_table_data.filter(item => Number(item['actualReturnNumber']) < 0)
        // 某商品实际退货数量之和 不等于 本次退回数量
        let validAry3 = wrap_table_data.filter(item => Number(item['actualReturnNumber']) != Number(item['returnsNumber']))
        let validProductAry1 = validAry1.map(item => item['productCode']),
            validProductAry2 = validAry2.map(item => item['productCode']),
            validProductAry3 = validAry3.map(item => item['productCode'])
        let msg1 = '', msg2 = '', msg3 = ''
        $(validProductAry1).each((index, item) => {
            msg1 += `商品编码：${item}的实际退货数量必须小于等于可退数量 \n`
        })
        $(validProductAry2).each((index, item) => {
            msg2 += `商品编码：${item}的实际退货数量必须大于0 \n`
        })
        // $(validProductAry3).each((index, item) => {
        //     msg3 += `商品编码：${item}的实际退货数量之和需要等于本次退回数量 \n`
        // })
        if (validProductAry1.length > 0) {
            utils.dialog({
                title: '提示',
                content: msg1,
                okValue: '确定',
                ok: function () {}
            }).showModal()
            return false
        }
        if (validProductAry2.length > 0) {
            utils.dialog({
                title: '提示',
                content: msg2,
                okValue: '确定',
                ok: function () {}
            }).showModal()
            return false
        }
        // if (validProductAry3.length > 0) {
        //     utils.dialog({
        //         title: '提示',
        //         content: msg3,
        //         okValue: '确定',
        //         ok: function () {}
        //     }).showModal()
        //     return false
        // }
        if(checkDate){
            console.log(form_data);
            console.log(table_data);
            for(var i=0;i<table_data.length;i++){
                var rate = table_data[i].rate
                if (rate){
                    table_data[i].rate =  rate.replace("%","");
                }else {
                    table_data[i].rate = 0;
                }
            }
            $(table_data).each((index, item) => {
                item['batchBaseVoList'] = JSON.parse(item['batchBaseVoList'])
                item['batchBaseVoList'] = item['batchBaseVoList'].filter(ite => Number(ite['actualReturnNumber']))
            })
            var detailArray = JSON.stringify(table_data) // .replace(/\\/g, '');
            parent.showLoading();
            $.ajax({
                url: "/proxy-order/order/orderReturn/orderReturnController/v2/submitSave",
                type:'post',
                dataType:'json',
                data: {tableData:detailArray, formData:formData},
                beforeSend: function(){
                    parent.showLoading({hideTime: 60000})
                },
                success: function(data){
                    parent.hideLoading();
                    if(data.code == 0){
                        utils.dialog({
                            title: '温馨提示',
                            content: '  保存成功   ',
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }

                        }).showModal()
                    }else{
                        utils.dialog({
                            title: '温馨提示',
                            content: data.msg,
                            okValue: '确定',
                            ok: function () {
                                return;
                            }

                        }).showModal()
                    }
                },
                complete: function () {
                    parent.hideLoading()
                }
            });
        }
    });

    /* 返回 */
    $('#goback').on('click', function () {
        utils.dialog({
            title: '温馨提示',
            content: '返回后当前页面数据将丢失，是否继续',
            okValue: '确定',
            ok: function () {
                utils.closeTab();
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal()
    });

    /* 退货原因下拉框同步 */
    $('#returnReason').on('change',function () {
        var val = $(this).val();
        var data = $('#table_a').XGrid('getRowData');
        console.log(data);
        data = data.forEach(function (item,index) {
            $('#table_a').XGrid('setRowData', item.id,{returnReason:val});
        });
        console.log(val,data);
    });

    /* 复制行 */
    $('#cloneRoW').on('click', function () {
        var id = 1;
        var data = $('#table_a').XGrid('getSeleRow');
        if (!data) {
            utils.dialog({
                title: "提示",
                content: "请先选中一行数据"
            }).showModal();
            return
        }
        if (data.length&&data.length>1) {
            utils.dialog({
                title: "提示",
                content: "只能单行复制"
            }).showModal();
            return
            /*$.each(data, function (index, item) {
                item.id = Number(data[data.length-1].id)+index+1;
              $('#table_a').XGrid('addRowData', item, "last");
            })*/
        } else {
            var all_data = $('#table_a').XGrid('getRowData');
            var detail = data[0];
            var batchBaseVoList = JSON.parse(detail.batchBaseVoList);
            var this_data = [];
            all_data.forEach(function (item,index) {
                if(item.productCode==detail.productCode){
                    this_data.push(item);
                }
            });
            //判断同一商品行数是否小于该商品批号数量
            if(this_data.length<batchBaseVoList.length){
                detail.id = Number(all_data[all_data.length-1].id)+1;
                var rate = detail.rate;
                if (rate != undefined && rate != null && rate != ''){
                    detail.rate = rate.replace("%","");
                }

                $('#table_a').XGrid('addRowData', detail, "last");
            }else {
                utils.dialog({
                    title: "提示",
                    content: "该商品已经达到批号数量限制"
                }).showModal();
            }
        }
    });

    /* 删除行 */
    $('#removeRoW').on('click', function () {
        var data = $('#table_a').XGrid('getSeleRow');
        if (!data) {
            utils.dialog({
                title: "提示",
                content: "请先选中一行数据"
            }).showModal();
            return
        }
        //删除二次确认
        utils.dialog({
            title: "提示",
            content: "确认删除当前选中行？",
            okValue: '确定',
            ok: function () {
                if (data.length) {
                    $.each(data, function (index, item) {
                        $('#table_a').XGrid('delRowData', item.id);
                    })
                } else {
                    $('#table_a').XGrid('delRowData', data.id);
                }
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    });

    //退回按钮禁用逻辑
    resetDisabledBtnSendBack();
    function resetDisabledBtnSendBack(){
        if($('#hiddenIsReturnBack').val() == '1'){
            $('#btnSendBack').prop('disabled',true);
        }else{
            $('#btnSendBack').prop('disabled',false);
        }
    }
    //退回
    let checkedReasonList = [];//退回原因选择项
    $('#btnSendBack').on('click',function(){
        utils.dialog({
            title: '退货单退回',
            width: 520,//$(window).width() * 0.5,
            content: $('#returnReceiveEditSendBackDialog'),
            onshow: function(){},
            button: [
                {
                    value: '确定',
                    callback: function () {
                        asyncHandleSendBack().then((confirmMsg)=>{
                            $('#btnSendBack').prop('disabled',true);//禁用退回按钮
                            utils.dialog({
                                title: '温馨提示',
                                width: 320,
                                content: confirmMsg,
                                okValue: '确定',
                                ok: function () {
                                    //清空输入的值
                                    $("#reason_check_all").prop("checked",false);
                                    $('input[name="sendBackReasonCheck"]').prop("checked",false);
                                    checkedReasonList = [];
                                    //dialog关闭
                                    this.close().remove();
                                    //tab关闭
                                    //utils.closeTab();
                                }
                            }).showModal()
                        }).catch((errMsg)=>{
                            utils.dialog({
                                title: '提示',
                                width: 320,
                                content: errMsg,
                                okValue: '确定',
                                ok: function () {}
                            }).showModal()
                        })
                    },
                    autofocus: true
                },
                {
                    value: '取消',
                    callback: function () {}
                }
            ]
        }).showModal()
    })
    //退回异步操作
    function asyncHandleSendBack(){
        return new Promise((resolve,reject)=>{
            //退回原因必填项非空
            if(!checkedReasonList.length){
                reject('请选择退回原因');
                return;
            }
            //退回备注必填项非空
            let sendBackRemark = $('#sendBackRemark').val();
            if(!sendBackRemark.trim().length){
                reject('请输入退回备注');
                return;
            }
            //提交
            parent.showLoading();
            $.ajax({
                url: "/proxy-order/order/orderReturn/orderReturnController/handReturnBackSave",
                type:'post',
                dataType:'json',
                data: {
                    returnBackReason: checkedReasonList.join('、'),
                    returnBackComment: sendBackRemark,
                    salesReturnCode: $("#salesReturnCode").val(),
                    ecRefundRequestCode: $("#ecRefundRequestCode").val()
                },
                beforeSend: function(){
                    parent.showLoading({hideTime: 60000})
                },
                success: function(data){
                    parent.hideLoading();
                    if(data.code == 0){
                        resolve('处理成功')
                    }else{
                        reject(data.msg || '处理失败，请稍后重试！')
                    }
                },
                error:function(error){
                    reject('内部出错，请稍后重试！')
                },
                complete: function () {
                    parent.hideLoading()
                }
            });
        })
    }
    //退回原因全选
    $('#reason_check_all').on('click',function(){
        var isCheckedAll = $('#reason_check_all').is(':checked');
        $('input[name="sendBackReasonCheck"]').prop("checked",isCheckedAll);
        if (isCheckedAll) {
            checkedReasonList = [];
            $('input[name="sendBackReasonCheck"]').each(function () {
                checkedReasonList.push(this.value)
            })
        } else {
            $('input[name="sendBackReasonCheck"]').each(function () {
                checkedReasonList = [];
            })
        }
        console.log(checkedReasonList)
    })
    //退回原因单选
    $('input[name="sendBackReasonCheck"]').on('click',function(){
        $("#reason_check_all").prop("checked", $('input[name="sendBackReasonCheck"]').length === $("input[name='sendBackReasonCheck']:checked").length ? true : false);
        if ($(this).is(':checked')) {
            checkedReasonList.push($(this).val())
        }else{
            checkedReasonList.splice(checkedReasonList.indexOf($(this).val()), 1)
        }
        console.log(checkedReasonList)
    })
})

function setTableSum() {
    var data = $('#table_a').XGrid('getRowData');
    let _data = data
    // $(_data).each((index, item) => {
    //     let batchBaseVoList = JSON.parse(item['batchBaseVoList'])
    //     let taxAmount_num_Ary = batchBaseVoList.map(item => Number(item['taxAmount']))
    //     let tax_num_num_Ary = batchBaseVoList.map(item => Number(item['tax']))
    //     let paymentAmount_num_Ary = batchBaseVoList.map(item => Number(item['paymentAmount']))
    //     let taxAmount_num = taxAmount_num_Ary.reduce((total, cur) => Number(total) + cur,0)
    //     let tax_num = tax_num_num_Ary.reduce((total, cur) => Number(total) + cur, 0)
    //     let paymentAmount_num = paymentAmount_num_Ary.reduce((total, cur) => Number(total) + cur, 0)
    //     item['taxAmount'] = taxAmount_num
    //     item['tax'] = tax_num
    //     item['paymentAmount'] = paymentAmount_num
    // })
    var sum_ele = $('#table_a_sum .sum');
    //console.log(sum_ele);
    $(sum_ele[0]).text(totalTable(_data, 'taxAmount'));
    $(sum_ele[1]).text(totalTable(_data, 'tax'));
    $(sum_ele[2]).text(totalTable(_data, 'paymentAmount'));
}
//编辑销售退回收货单
// function  pihao_change() {
//     $(id).onchange(function () {
//         console.log('11111111111')
//     })
// }
function actualReturnNumberChange(el) {
    let thisVal  = $(el).val(),
        productCode = $('#productCode_tag').text(),
        returnsNumber = $('#returnsNumber_tag').text()
    let mayReturnNumber = $($(el).parents('tr')[0]).find('[row-describedby="mayReturnNumber"]').text()
    if (Number(thisVal) > Number(mayReturnNumber)) {
        utils.dialog({content: `“商品编码：${productCode}的实际退货数量必须小于等于可退数量`, quickClose: true, timeout: 2000}).showModal()
        $(el).val('')
        return false
    } else if (Number(thisVal) < 0) {
        utils.dialog({content: `实际退货数量必须大于0`, quickClose: true, timeout: 2000}).showModal()
        $(el).val('')
        return false
    }

    // 活动明细优惠金额
    $($(el).parents('tr')[0]).find('[row-describedby="activityDiscountAmount"]').text((Number(thisVal) / Number(returnsNumber) * Number($('#oldactivityDiscountAmount_tag').text())).toFixed(2))
    // 余额抵扣明细优惠金额
    $($(el).parents('tr')[0]).find('[row-describedby="balanceDiscountAmount"]').text((Number(thisVal) / Number(returnsNumber) * Number($('#oldbalanceDiscountAmount_tag').text())).toFixed(2))
    // 退回返利金额
    $($(el).parents('tr')[0]).find('[row-describedby="amounOfRebate"]').text((Number(thisVal) / Number(returnsNumber) * Number($('#oldamounOfRebate_tag').text())).toFixed(2))
    // 价税合计
    $($(el).parents('tr')[0]).find('[row-describedby="taxAmount"]').text((Number(thisVal) * Number($($(el).parents('tr')[0]).find('[row-describedby="taxPrice"]').text())).toFixed(2))
    // 实付金额
    $($(el).parents('tr')[0]).find('[row-describedby="paymentAmount"]').text((Number(thisVal) * Number($($(el).parents('tr')[0]).find('[row-describedby="realPayPrice"]').text())).toFixed(2))
    // 税额
    let taxAmount = $($(el).parents('tr')[0]).find('[row-describedby="taxAmount"]').text()
    let rate = $('#rate_tag').text()
    rate = rate.replace("%" , "")
    let notaxPrice = (Number($('#taxPrice_tag').text())/(1+ Number(rate)/100)).toFixed(2)
    $($(el).parents('tr')[0]).find('[row-describedby="tax"]').text((Number(taxAmount) - Number(notaxPrice * Number(thisVal))).toFixed(2))
}
