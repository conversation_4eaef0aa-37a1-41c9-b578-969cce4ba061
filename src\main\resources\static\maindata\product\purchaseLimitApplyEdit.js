$(function () {
    //tabs输入框只能输入数字限制
    $(document).on("input",".bootstrap-tagsinput input",function(){
        this.value=this.value.replace(/\D/g,'');
        $(this).attr("maxlength",15);
    });
    var checkedStatus = $("#disableStateYes").prop('checked');
    if(checkedStatus){
        $('#select_reason').css('display','block');
        $('#inp_reason').css('display','none');
        $('div[id^=disableReason]').html(selStr);
    }
    //采销限制选择时校验商品
    $(".limited").click(function(){
        var checked=this.checked;
        if(checked){
            var rowData = $('#X_Table').getRowData();
            var recordInfo=$("#recordInfo").serializeToJSON();
            var limitName = $(this).attr("name");
            //页面校验
            var result = limitedChecked(rowData,recordInfo,limitName);
            if(!result){
                checkSubmitFlag= false;
                return false;
            }
        }
    });
//新增行
    $("#addOneRow").on("click", function () {
        var recordInfo=$("#recordInfo").serializeToJSON();
        if (recordInfo.businessType==undefined){
            utils.dialog({content: '请选择业务类型', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if (recordInfo.disableState==undefined&&recordInfo.limitedProductionState==undefined&&recordInfo.limitedPinState==undefined){
            utils.dialog({content: '至少选择一种采销限制', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        var resultArr = $('#X_Table').getRowData();
        if(resultArr.length > 0)
        {
            for (var i = 0; i < resultArr.length; i++) {
                resultArr[i].id=resultArr[i].productId;
            }
        }
        var limitedPinState=null,limitedProductionState=null,disableState=null;
        var paramJson=$("#recordInfo").serializeToJSON();

        if (paramJson.disableState!=undefined){
            disableState= paramJson.disableState==0?1:0;
        }
        if (paramJson.limitedPinState!=undefined){
            limitedPinState= paramJson.limitedPinState==0?1:0;
        }
        if (paramJson.limitedProductionState!=undefined){
            limitedProductionState= paramJson.limitedProductionState==0?1:0;
        }
        // 如果停用按钮或者解除停用按钮被选中  另外两个不生效
        if(disableState!=null&&disableState!=undefined){
            limitedPinState=null;
            limitedProductionState=null;
        }else{// 如果选择的是停用外的按钮  默认停用为否
            disableState=0;
        }

        dialog({
            url: '/proxy-product/product/purchaseLimit/searchProduct',
            title: '商品列表',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.8,
            data: {
                "orgCode": $("#recordOrgCode").val(),
                "limitedPinState": limitedPinState,
                "limitedProductionState": limitedProductionState,
                "disableState": disableState,
                resultArr: resultArr
            }, // 给modal 要传递的 的数据
            onclose: function (data) {
                var data = this.returnValue;
                console.log(data)
                if (data) {
                    var rows = data.resultArr;
                    for (var i = 0; i < rows.length; i++) {
                        var id = rows[i].id;
                        if (!findInArr(id)) {
                            rows[i].productId = rows[i].id;
                            $('#X_Table').XGrid('addRowData', rows[i]);
                        }
                    }
                }
            }
        }).showModal();
    });
//列表内查询id
    function findInArr(id) {
        var arr = $('#X_Table').getRowData();
        for (var i = 0; i < arr.length; i++) {
            var ID = arr[i].productId;
            if (ID == id) {
                return true;
            }
        }
        return false;
    }
    //删除行
    $("#delOneRow").on("click", function () {
        var selectRow = $('#X_Table').XGrid('getSeleRow');
        if (!selectRow) {
            utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
        } else {
            utils.dialog({
                title:"提示",
                width:300,
                height:30,
                okValue: '确定',
                content: "确定删除此条记录?",
                ok: function () {
                    $('#X_Table').XGrid('delRowData', selectRow.id);
                    utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        }
    })
    $('#X_Table').XGrid({
        url: "/proxy-product/product/purchaseLimit/audi/details",
        postData: {
            "recordId": $("#recordId").val()
        },
        colNames: ['','商品编码', '商品名', '通用名', '型号/规格', '生产厂家','包装单位', '剂型', '限销状态', '限采状态', '停用状态','停（启）用原因'],
        colModel: [{
            name: 'productId',
            index: 'productId',
            hidden:true
        }, {
            name: 'productCode',
            index: 'productCode'
        }, {
            name: 'productName',
            index: 'productName'
        }, {
            name: 'commonName',
            index: 'commonName'
        }, {
            name: 'specifications',
            index: 'specifications'
        }, {
            name: 'manufacturerName',
            index: 'manufacturerName'
        }, {
            name: 'packingUnitValue',
            index: 'packingUnitValue'
        }, {
            name: 'dosageFormValue',
            index: 'dosageFormValue'
        }, {
            name: 'limitedPinState',
            index: 'limitedPinState',
            formatter:function(value){
                if (value==0){
                    return "否"
                }else if (value==1) {
                    return "是"
                }else {
                    return "未知"
                }
            }

        }, {
            name: 'limitedProductionState',
            index: 'limitedProductionState',
            formatter:function(value){
                if (value==0){
                    return "否"
                }else if (value==1) {
                    return "是"
                }else {
                    return "未知"
                }
            }

        }, {
            name: 'disableState',
            index: 'disableState',
            formatter:function(value){
                if (value==0){
                    return "否"
                }else if (value==1) {
                    return "是"
                }else {
                    return "未知"
                }
            }
        }, {
            name: 'disableReason',
            index: 'disableReason',
            rowtype: '#disableReason'
        }],
        rowNum: 0,
        rownumbers:true,
        altRows: true//设置为交替行表格,默认为false
    });
    // 停（启）用原因  按钮提交
    $('#btn_reason').on('click',function () {
        let reasonVal = ($('#inp_reason').css('display') != 'none') ? $('#inp_reason').val() : $('#select_reason').val();
        let inp_disableReasons = $('#X_Table [name=disableReason]');
        if (inp_disableReasons.length ==0) {
            utils.dialog({content: '请先选择商品', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        $(inp_disableReasons).each(function (index,item) {
            $(item).val(reasonVal);
        })
    })
})
/*$(document).on("keydown", ".keywordSearch", function (ev) {
    if (ev.keyCode == 13) {
        var ss = $('#X_Table').XGrid('getSeleRow');
        //var code = ev.currentTarget.value;
        var keyword=$(this).val();
        var limitedPinState='',limitedProductionState='',disableState='';
        var paramJson=$("#recordInfo").serializeToJSON();
        if (paramJson.limitedPinState!=undefined){
            limitedPinState= paramJson.limitedPinState==0?1:0;
        }
        if (paramJson.limitedProductionState!=undefined){
            limitedProductionState= paramJson.limitedProductionState==0?1:0;
        }
        if (paramJson.disableState!=undefined){
            disableState= paramJson.disableState==0?1:0;
        }
        dialog({
            url:'/proxy-product/product/adjustPrice/searchProduct',
            title: '商品列表',
            width: 1000,
            height: 650,
            data: {"limitedPinState":limitedPinState,"limitedProductionState":limitedProductionState,"disableState":disableState,"orgCode":$("#recordOrgCode").val(),"keyword":keyword},// 给modal 要传递的 的数据
            onclose: function (data) {
                if (this.returnValue) {
                    var data = this.returnValue;
                    var rows =  $('#X_Table').XGrid('getRowData');
                    //console.log(rows);
                    if (data.limitedPinState==0){
                        data.limitedPinStateValue="否";
                    }else {
                        data.limitedPinStateValue="是";
                    }
                    if (data.limitedProductionState==0){
                        data.limitedProductionStateValue="否";
                    }else {
                        data.limitedProductionStateValue="是";
                    }
                    if (data.disableState==0){
                        data.disableStateValue="否";
                    }else {
                        data.disableStateValue="是";
                    }
                    for (var i = 0; i < rows.length; i++) {
                        if (data.productCode==rows[i].productCode){
                            utils.dialog({content: '已添加该商品！', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                    }
                    data.productId=data.id;
                    data.manufacturerValue=data.manufacturerName;
                    $('#X_Table').XGrid('setRowData', ss.id, data)
                }
            },
            oniframeload: function () {
                //console.log('iframe ready')
            }
        }).showModal();
    }
    ev.stopPropagation();
});*/
var checkSubmitFlag= false;
function checkSubmit(){
    if (checkSubmitFlag==true){
        return false;
    }
    checkSubmitFlag=true;
    return true;
}
$(".submitAudit").click(function () {
    if (!checkSubmit()){
        utils.dialog({content: '正在提交。。。', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    var status=$(this).attr("status");
    var rowData = $('#X_Table').getRowData();
    var recordInfo=$("#recordInfo").serializeToJSON();
    //页面校验
    var result = limitedChecked(rowData,recordInfo);
    if(!result){
        checkSubmitFlag= false;
        return false;
    }
    if (rowData.length==0){
        utils.dialog({content: '至少选择一种商品', quickClose: true, timeout: 2000}).showModal();
        checkSubmitFlag= false;
        return false;
    }
    var purchaseLimitApprovalRecordVo={"purchaseLimitApprovalRecordVo":recordInfo};
    var purchaseLimitApprovalRecordDetailVos={"purchaseLimitApprovalRecordDetailVos":rowData};
    //拼装链接两个json对象
    var jsonData = (JSON.stringify(purchaseLimitApprovalRecordVo) + JSON.stringify(purchaseLimitApprovalRecordDetailVos)).replace(/}{/, ',');
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/editApply?statues="+status,
        data: jsonData,
        async: false,
        dataType:'json',
        contentType: "application/json",
        error: function () {
            alert("提交失败！");
            checkSubmitFlag= false;
        },
        success: function (data) {
            if (data.code==0){
                var msg="";
                if (status==0){
                    msg='保存成功';
                }else if (status==1){
                    msg='提交审核成功';
                }else {
                    msg='操作成功';
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
                checkSubmitFlag= false;
            }
        }
    });
});

let inpStr = `<input type="text" name="disableReason" maxlength="200" style="width: 200px" class="form-control" />`,
    selStr = `<select class="form-control" id="select_reason" name="disableReason" style="width: 200px">
                <option value="药监网站通报">药监网站通报</option>
                <option value="药监局检查指出">药监局检查指出</option>
                <option value="厂商要求协查">厂商要求协查</option>
            </select>`;
$(".businessType").click(function(){
    if ($(this).val()==0){
        $("#applyYes").css("display","block");
        $("#applyNo").css("display","none");
        $("#applyNo input:checkbox").removeAttr("name");
        $("#disableStateYes").attr("name","disableState");
        $("#limitedProductionStateYes").attr("name","limitedProductionState");
        $("#limitedPinStateYes").attr("name","limitedPinState");
        if ($("#disableStateYes").is(':checked')){
            $('#select_reason').css('display','block');
            $('#inp_reason').css('display','none');
            $('div[id^=disableReason]').html(selStr);
        }else{
            $('#select_reason').css('display','none');
            $('#inp_reason').css('display','block');
            $('div[id^=disableReason]').html(inpStr);
        }
        if ($("#limitedPinStateYes").is(':checked')){
            $("#limitedBatchNumberDiv").show();
            $("#noticePurchaseDiv").show();
            $("#batchNumberRestrictionDiv").show();

            $("#limitedBatchNumberDiv input:radio").attr("name","limitedBatchNumber");
            $("#noticePurchase").attr("name","noticePurchase");
            $("#batchNumberRestriction").attr("name","batchNumberRestriction");
        }else {
            $("#limitedBatchNumberDiv").hide();
            $("#noticePurchaseDiv").hide();
            $("#batchNumberRestrictionDiv").hide();

            $("#limitedBatchNumberDiv input:radio").removeAttr("name");
            $("#noticePurchase").removeAttr("name");
            $("#batchNumberRestriction").removeAttr("name");
        }
    }else {
        $("#applyYes").css("display","none");
        $("#applyNo").css("display","block");
        $("#applyYes input:checkbox").removeAttr("name");
        $("#disableStateNo").attr("name","disableState");
        $("#limitedProductionStateNo").attr("name","limitedProductionState");
        $("#limitedPinStateNo").attr("name","limitedPinState");

        $("#limitedBatchNumberDiv").hide();
        $("#noticePurchaseDiv").hide();
        $("#batchNumberRestrictionDiv").hide();

        $("#limitedBatchNumberDiv input:radio").removeAttr("name");
        $("#noticePurchase").removeAttr("name");
        $("#batchNumberRestriction").removeAttr("name");
        $('#select_reason').css('display','none');
        $('#inp_reason').css('display','block');
        $('div[id^=disableReason]').html(inpStr);
    }
})
$("#limitedPinStateYes").change(function() {
    if($(this).is(':checked')){
        $("#limitedBatchNumberDiv").show();
        $("#noticePurchaseDiv").show();
        $("#batchNumberRestrictionDiv").show();
        $("#limitedBatchNumberDiv input:radio").attr("name","limitedBatchNumber");
        $("#noticePurchase").attr("name","noticePurchase");
        $("#batchNumberRestriction").attr("name","batchNumberRestriction");
        $("input[name='limitedBatchNumber']").each(function () {
            var val=this.value;
            if(val == "1")
            {
                $(this).prop("checked",true);
            }
        });
    }else {
        $("#limitedBatchNumberDiv").hide();
        $("#noticePurchaseDiv").hide();
        $("#batchNumberRestrictionDiv").hide();

        $("#limitedBatchNumberDiv input:radio").removeAttr("name");
        $("#noticePurchase").removeAttr("name");
        $("#batchNumberRestriction").removeAttr("name");
        //取消停用的选中
        $("#disableStateYes").prop("checked",false);
        $('#select_reason').css('display','none');
        $('#inp_reason').css('display','block');
        $('div[id^=disableReason]').html(inpStr);
    }
})
$(".limitedBatchNumber").click(function(){
    if ($(this).val()==0){
        $("#batchNumberRestrictionDiv").hide();
        $("#batchNumberRestriction").removeAttr("name");
    }else {
        $("#batchNumberRestrictionDiv").show();
        $("#batchNumberRestriction").attr("name","batchNumberRestriction");
    }
})
//选中停用
$("#disableStateYes").change(function() {
    if($(this).is(':checked')){
        $("#limitedProductionStateYes").prop("checked",true);
        $("#limitedPinStateYes").prop("checked",true);
        $("#limitedPinStateYes").change();
    }
});
//限采 取消选中
$("#limitedProductionStateYes").change(function() {
    if(!$(this).is(':checked')){
        $("#disableStateYes").prop("checked",false);

        $('#select_reason').css('display','none');
        $('#inp_reason').css('display','block');
        $('div[id^=disableReason]').html(inpStr);
    }
});
$('#submitAuditAgain').on('click', function () {
    var rowData = $('#X_Table').getRowData();
    var recordInfo=$("#recordInfo").serializeToJSON();
    if (recordInfo.businessType==undefined){
        utils.dialog({content: '请选择业务类型', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    if (recordInfo.disableState==undefined&&recordInfo.limitedProductionState==undefined&&recordInfo.limitedPinState==undefined){
        utils.dialog({content: '至少选择一种采销限制', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    if (rowData.length==0){
        utils.dialog({content: '至少选择一种商品', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    //判断生产厂家是否为空，判断是否是选择商品带出
    for (var i = 0; i < rowData.length; i++) {
        if (rowData[i].manufacturerName==""){
            utils.dialog({content: '请认真填写数据', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
    }
    //申请业务类型与下方已选列表冲突判断
    if (recordInfo.businessType == 0) {
        var applyYes=[];
        $("#applyYes input[type='checkbox']:checked").each(function () {
            applyYes.push($(this).attr("name"))
        })
        for (var i = 0; i < applyYes.length; i++) {
            if (applyYes[i]=="limitedPinState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].limitedPinState=="是"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
            if (applyYes[i]=="limitedProductionState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].limitedProductionState=="是"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
            if (applyYes[i]=="disableState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].disableState=="是"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
        }
    }else {
        var applyNo=[];
        $("#applyNo input[type='checkbox']:checked").each(function () {
            applyNo.push($(this).attr("name"))
        })
        for (var i = 0; i < applyNo.length; i++) {
            if (applyNo[i]=="limitedPinState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].limitedPinState=="否"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
            if (applyNo[i]=="limitedProductionState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].limitedProductionState=="否"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
            if (applyNo[i]=="disableState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].disableState=="否"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
        }
    }
    var purchaseLimitApprovalRecordVo={"purchaseLimitApprovalRecordVo":recordInfo};
    var purchaseLimitApprovalRecordDetailVos={"purchaseLimitApprovalRecordDetailVos":rowData};
    //拼装链接两个json对象
    var jsonData = (JSON.stringify(purchaseLimitApprovalRecordVo) + JSON.stringify(purchaseLimitApprovalRecordDetailVos)).replace(/}{/, ',');
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/submitAuditAgain?taskId="+$("#taskId").val(),
        data: jsonData,
        async: false,
        dataType:'json',
        contentType: "application/json",
        error: function () {
            alert("提交失败！");
        },
        success: function (data) {
            if (data.code==0){
                utils.dialog({
                    title: "提示",
                    content: '提交审核成功！',
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
            }
        }
    });

})
//关闭申请
$('.auditPass').on('click', function () {
    var status=this.getAttribute("status");
    utils.dialog({
        title:"提示",
        width:300,
        height:30,
        okValue: '确定',
        content: "确定关闭此申请？",
        ok: function () {
            submitAudiInfo(status,"");
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
});

//关闭按钮
$("#closePage").on("click", function () {
    dialog({
        title: "提示",
        content: "是否保存草稿？",
        width: 300,
        height: 30,
        okValue: '保存',
        button: [
            {
                value: '关闭',
                callback: function () {
                    utils.closeTab();
                }
            }
        ],
        ok: function () {
            var recordInfo=$("#recordInfo").serializeToJSON();
            if (recordInfo.businessType==undefined){
                utils.dialog({content: '请选择业务类型', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            if (recordInfo.disableState==undefined&&recordInfo.limitedProductionState==undefined&&recordInfo.limitedPinState==undefined){
                utils.dialog({content: '至少选择一种采销限制', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            var rowData = $('#X_Table').getRowData();
            if (rowData.length == 0) {
                utils.dialog({content: '至少添加一种商品', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
            $("#saveCaoGao").click()
            setTimeout(function () {
                utils.closeTab();
            }, 2000)
        }
    }).showModal();
});
function submitAudiInfo(status,auditOpinion){
    var submitData ={
        "id":$("#recordId").val(),
        "statues":status,
        "auditOpinion":auditOpinion,
        "taskId":$("#taskId").val()
    } ;
    var rowData = $('#X_Table').getRowData();
    var purchaseLimitSaveVo = {
        "purchaseLimitApprovalRecordVo":submitData,
        "purchaseLimitApprovalRecordDetailVos":rowData,
    };
    resultData=JSON.stringify(purchaseLimitSaveVo);
    $.ajax({
        type: "POST",
        url: "/proxy-product/product/purchaseLimit/audi/passAudit",
        data: resultData,
        async: false,
        dataType:'json',
        contentType: "application/json",
        success: function (data) {
            if (data.code==0){
                var msg ="";
                if(status == 2){//申请人关闭流程
                    msg = "流程已关闭！";
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        // var  mid = parent.$('#nav-tab li.active a').attr('href').replace('#', '');
                        // parent.$('#mainFrameTabs').bTabsClose(mid);
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                console.log(data.result);
                utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '审核失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
$(function () {
    var workUrl="";
    if ($("#pageType").val()==1){
        workUrl="/proxy-product/product/purchaseLimit/queryTotle?key="+$("#workProcessKey").val();
    }
    if ($("#pageType").val()==4){
        workUrl="/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+$("#processId").val();
    }
    $.ajax({
        type: "POST",
        url: workUrl,
        async: false,
        error: function () {
            utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
        },
        success: function (data) {
            if (data.code==0){
                $('.flow').process(data.result);
            }else {
                utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
            }
        }
    });

    // 采销限制  停用 checkbox
    $('#disableStateYes').change(function () {
        var checkedStatus = $(this).prop('checked');
        if(checkedStatus){
            $('#select_reason').css('display','block');
            $('#inp_reason').css('display','none');
            $('div[id^=disableReason]').html(selStr);
        }else{
            $('#select_reason').css('display','none');
            $('#inp_reason').css('display','block');
            $('div[id^=disableReason]').html(inpStr);
        }
    })
})
function  limitedChecked (rowData,recordInfo,limitName) {
    if (recordInfo.businessType==undefined){
        utils.dialog({content: '请选择业务类型', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    if (recordInfo.disableState==undefined&&recordInfo.limitedProductionState==undefined&&recordInfo.limitedPinState==undefined){
        utils.dialog({content: '至少选择一种采销限制', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    //申请业务类型与下方已选列表冲突判断
    if (recordInfo.businessType == 0) {
        var applyYes=[];
        if(limitName==undefined){
            $("#applyYes input[type='checkbox']:checked").each(function () {
                applyYes.push($(this).attr("name"));
            })
        }else{
            applyYes.push(limitName);
        }
        for (var i = 0; i < applyYes.length; i++) {
            if (applyYes[i]=="limitedPinState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].limitedPinState=="是"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
            if (applyYes[i]=="limitedProductionState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].limitedProductionState=="是"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
            if (applyYes[i]=="disableState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].disableState=="是"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
        }
    }else {
        var applyNo=[];
        if(limitName==undefined) {
            $("#applyNo input[type='checkbox']:checked").each(function () {
                applyNo.push($(this).attr("name"));
            })
        }else{
            applyNo.push(limitName);
        }
        for (var i = 0; i < applyNo.length; i++) {
            if (applyNo[i]=="limitedPinState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].limitedPinState=="否"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
            if (applyNo[i]=="limitedProductionState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].limitedProductionState=="否"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
            if (applyNo[i]=="disableState"){
                for (var j = 0; j < rowData.length; j++) {
                    if (rowData[j].disableState=="否"){
                        utils.dialog({content: '已选择商品中有不可申请限制的商品，请删除商品后再修改！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                }
            }
        }
    }
    return true;
}