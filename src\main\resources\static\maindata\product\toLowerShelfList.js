$(function() {
    $('#X_Tableb').XGrid({
        url: "/proxy-product/product/lowerShelf/queryProductLowerShelf",
        colNames: ['单据id', '业务类型', '单据编号', '申请日期', '申请人ID', '申请人', '机构', '审核日期', '审核状态', 'EC审核状态', 'EC下架时间', 'EC未下架原因', '商品编号', '原商品编码', '小包装条码', '商品名称', '通用名', '商品规格', '生产厂家', '单位',
            '采购员', '销售状态', '下架原因', '原因说明', "原因备注<div style='display:inline-block;background-color: black;color: white;border-radius: 50%;width: 15px ;height: 15px;text-align: center;font-size:10px;text-align: center;'  onmouseenter='replaceEncodingTip(this," + "1" + ")' onmouseout='replaceEncodingTip(this," + "2" + ")'>?</div> ",
            '商品定位', '商品定位', '品类组审核商品定位', '销售定位', '品类组审核销售定位', '经营状态', '品类组审核经营状态', '合格品库可用库存数量',
            '最后含税进价', '最后供应商', 'APP售价', '终端建议零售价', '商品产地', '剂型', '处方分类', '批准文号',
            '一级分类', '二级分类', '三级分类', '四级分类', '应季类型'
        ],
        colModel: [{
                name: 'recordId',
                index: 'recordId',
                hidegrid: true,
                hidden: true
            }, {
                name: 'channelId',
                index: 'channelId',

                width: 100
            }, {
                name: 'applicationCode',
                index: 'applicationCode',
                width: 160
            }, {
                name: 'applicationTime',
                index: 'applicationTime',
                width: 200,
                formatter: function(value) {
                    return new Date(value).Format('yyyy-MM-dd hh:mm:ss');
                },
            }, {
                name: 'applicantId',
                index: 'applicantId',
                width: 80,
                hidegrid: true,
                hidden: true
            },
            {
                name: 'applicantName',
                index: 'applicantName',
                width: 80
            },
            {
                name: 'productOrgName',
                index: 'productOrgName',
                width: 210
            },
            {
                name: 'auditTime',
                index: 'auditTime',
                width: 200,
                formatter: function(value) {
                    if (value != null) {
                        return new Date(value).Format('yyyy-MM-dd hh:mm:ss');
                    }
                }
            },
            {
                name: 'statuesStr',
                index: 'statuesStr',
                width: 100,
                /*formatter:function(e){
                    if (e == '0') {
                        return '录入中'
                    } else if (e == '1') {
                        return '审核中'
                    }else if (e == '2') {
                        return '审核通过'
                    }else if (e == '3') {
                        return '审核不通过'
                    }
                },*/
            }, {
                name: 'ecAuditStatusStr',
                index: 'ecAuditStatusStr',
                width: 120,
            }, {
                name: 'ecLowerShelfTime',
                index: 'ecLowerShelfTime',
                width: 200,
                formatter: function(value) {
                    if (value != null) {
                        return new Date(value).Format('yyyy-MM-dd hh:mm:ss');
                    }
                },
            }, {
                name: 'ecReason',
                index: 'ecReason',
                width: 200,
            }, {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'oldProductCode',
                index: 'oldProductCode',
                width: 140
            }, {
                name: 'smallPackageBarCode',
                index: 'smallPackageBarCode',
                width: 140
            }, {
                name: 'productName',
                index: 'productName',
                width: 110
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 110
            }, {
                name: 'specifications',
                index: 'specifications',
                width: 140
            }, {
                name: 'manufacturerName',
                index: 'manufacturerName',
                width: 240
            }, {
                name: 'packingUnitVal',
                index: 'packingUnitVal',
                width: 80
            }, {
                name: 'buyerVal',
                index: 'buyerVal',
                width: 80
            }, {
                name: 'ecStatusVal',
                index: 'ecStatusVal',
                width: 80
            }, {
                name: 'lowerShelfReasonVal',
                index: 'lowerShelfReasonVal',
                width: 200
            }, {
                name: 'remarks',
                index: 'remarks',
                width: 220
            }, {
                name: 'cause',
                index: 'cause',
                width: 300,
                formatter: function(a, b, arr) {
                    if (arr.lowerShelfReason == 8) {
                        if (arr.replaceEncoding) { return arr.replaceEncoding + "  " + arr.replaceProductName + "  " + arr.replaceSpecifications + "  " + arr.replaceManufacturerName } else return ""

                    } else {
                        if (arr.reasonRemarks) {
                            return arr.reasonRemarks
                        } else {
                            return ""
                        }

                    }
                },


            },
            {
                name: 'commodityPosition',
                index: 'commodityPosition',
                hidden: true,
                hidegrid: true
            },
            {
                name: 'commodityPositionVal',
                index: 'commodityPositionVal',
                width: 120
            },
            {
                name: 'auditCommodityPositionVal',
                index: 'auditCommodityPositionVal',
                width: 170
            },
            {
                name: 'salesClassificationVal',
                index: 'salesClassificationVal',
                width: 120
            },
            {
                name: 'auditSalesClassificationVal',
                index: 'auditSalesClassificationVal',
                width: 170
            },
            {
                name: 'operatingStateVal',
                index: 'operatingStateVal',
                width: 170
            },
            {
                name: 'auditOperatingStateVal',
                index: 'auditOperatingStateVal',
                width: 170
            },
            {
                name: 'inventoryQuantity', //【库存数量】名称改为【合格品库可用库存数量】
                index: 'inventoryQuantity',
                width: 170
            },
            {
                name: 'lastIncludingTaxPurchasePrice',
                index: 'lastIncludingTaxPurchasePrice',
                width: 110
            },
            {
                name: 'lastSupplier',
                index: 'lastSupplier',
                width: 220
            },
            {
                name: 'appPrice',
                index: 'appPrice',
                formatter: function(e) {
                    if (e != null) {
                        return Number(e).toFixed(2);
                    }
                },
                width: 110
            },
            {
                name: 'terminalPrice',
                index: 'terminalPrice',
                formatter: function(e) {
                    if (e != null) {
                        return Number(e).toFixed(2);
                    }
                },
                width: 130
            },
            {
                name: 'producingArea',
                index: 'producingArea',
                width: 80
            },
            {
                name: 'dosageFormVal',
                index: 'dosageFormVal',
                width: 100
            },
            {
                name: 'prescriptionClassificationVal',
                index: 'prescriptionClassificationVal',
                width: 100
            },
            {
                name: 'approvalNumber',
                index: 'approvalNumber',
                width: 160
            },
            {
                name: 'firstCategoryVal',
                index: 'firstCategoryVal',
                width: 100
            },
            {
                name: 'secondCategoryVal',
                index: 'secondCategoryVal',
                width: 120
            },
            {
                name: 'thirdCategoryVal',
                index: 'thirdCategoryVal',
                width: 120
            },
            {
                name: 'fourCategory',
                index: 'fourCategory',
                width: 100
            },
            {
                name: 'seasonalVarietiesVal',
                index: 'seasonalVarietiesVal',
                width: 120
            }
        ],
        rowNum: 20,
        rowList: [20, 50, 100], //分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function(id, dom, obj, index, event) {
            if (obj.statuesStr == "录入中") {
                var loginUserId = $("#loginUserId").val();
                if (obj.applicantId == loginUserId) {
                    utils.openTabs("productLowerShelfEdit", "商品下架申请", "/proxy-product/product/lowerShelf/lowerShelfEdit?id=" + obj.recordId);
                } else {
                    utils.dialog({ content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000 }).showModal();
                }
            } else {
                utils.openTabs("productlowerShelfDetail", "商品下架申请详情", "/proxy-product/product/lowerShelf/lowerShelfDetail?id=" + obj.recordId);
            }
        },
        rownumbers: true,
    });

    $("#SearchBtn").on("click", function() {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "orgCode": $("#orgCode").val(),
                "applicantId": $("#id").val(),
                "statues": $("#statues").val(),
                "flag": $("#flag").val(),
                "beginTime": $("#beginTime").val(),
                "endTime": $("#endTime").val(),
                "channelId": $("#channelId").val(),
                "productCode": $("#productCode").val(),
                "ecAuditStatus": $("#ecAuditStatus").val()
            },
            page: 1
        }).trigger('reloadGrid');
    });

    // 删除草稿
    $("#deleteDraftBtn").on("click", function() {
        let selRow = $('#X_Tableb').XGrid('getSeleRow');
        var loginUserId = $("#loginUserId").val();
        var applicantId = selRow[0].applicantId;
        // 表格重新渲染 的参数
        let postData = {}
        let data = {
            lowerShelfDetailId: selRow[0].id,
            applicantId: applicantId,
            loginUserId: loginUserId,
            lowerShelfId: selRow[0].recordId
        }
        let params = {
            applicantId: applicantId,
            statusVal: selRow[0].statuesStr,
            statusName: '录入中',
            url: '/proxy-product/product/lowerShelf/delete',
            loginUserId: loginUserId
        }
        utils.deleteDraft('X_Tableb', params, data, postData);
    });

    // 导出
    $('#ExportBtn').on('click', function() {
            utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text())).then(() => {
                return false;
            }).catch(() => {
                //原始处理逻辑代码
                utils.exportTableData('X_Tableb', 'searchForm', '/proxy-product/product/lowerShelf/exportLowerList')
            });
        })
        //上架督办
    $("#supervise").on("click", function() {
        utils.openTabs("productLowerSupervise", "待下架商品督办", "/proxy-product/product/lowerShelf/toSuperviseList");
    });
    //搜索申请人
    valAutocomplete("/proxy-product/product/productFirst/queryBuyerList", { paramName: 'userNames' }, "id", { data: "id", value: "userName" });





    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function() {
            $(this).siblings("input").trigger("dblclick")
        })
        //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function() {
        utils.channelDialog('0').then(res => {
            console.log(res)
            let _str_name = '',
                _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });

});

Date.prototype.Format = function(fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */

//设置显示列
$("#setRow").click(function() {
    $('#X_Tableb').XGrid('filterTableHead', 1000);
});

function valAutocomplete(url, param, obj, resParam, select, noneSelect) {
    var resParam = Object.assign({ 'list': 'result' }, resParam);
    $("#" + obj + "Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName, //查询参数，默认 query
        params: param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader: resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果', //查无结果的提示语
        onSelect: function(result) {
            $("#" + obj).val(result.data);
            select && select(result)
        },
        onNoneSelect: function(params, suggestions) {
            console.log(params, suggestions);
            $("#" + obj).val("");
            $("#" + obj + "Val").val("");
            noneSelect && noneSelect();
        }
    });
}

let dialogShow;

function replaceEncodingTip(e, str) {

    if (str === 1) {
        dialogShow = utils.dialog({ content: "下架原因备注列，当下架原因为替换编码时展示所备注替换编码的商品信息。" })
        dialogShow.show(e)
    } else {
        dialogShow.close().remove();
    }
    return false;

}