var req_url_prefix = "/proxy-sysmanage/sysmanage/common/dict";
var common_dict_type = 3;
$(function () {
    renderData();
});

/** 渲染数据 */
function renderData() {
    $('#X_Table').XGrid({
        url: req_url_prefix + "/productFirstPositionPage",
        mtype: "POST",
        postData: {
            type: common_dict_type
        },
        // colNames: ['名称', '状态', '商品定位二级属性值', '采购业务类型', '创建人', '最后一次修改人', '创建时间', '最后一次修改时间'],   商品逻辑优化三期-取消采购业务类型展示
        colNames: ['名称', '状态', '商品定位二级属性值', '创建人', '最后一次修改人', '创建时间', '最后一次修改时间'],

        colModel: [
            {
                name: 'name'
            }, {
                name: 'statesName',
                formatter: function (e, d, rData) {
                    var val = rData.states;
                    if (0 == val) {
                        return "停用";
                    } else if (1 == val) {
                        return "启用";
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'productSecondPositionStr',
            },
            // {
            //     name: 'purchaseTypeVal',  //商品逻辑优化三期-取消采购业务类型展示
            // },
            {
                name: 'createUser',
            }, {
                name: 'updateUser',
            }, {
                name: 'createTime',
                formatter: dateFormatter
            }, {
                name: 'updateTime',
                formatter: dateFormatter
            }, {
                name: 'states',
                hidden: true
            }, {
                name: 'editFlag',
                hidden: true
            }
        ],
        rowNum: 20,
        rowList: [20, 50, 100],
        rownumbers: true,
        altRows: true,
        pager: '#grid-pager',
        ondblClickRow: function (id, dom, obj, index, event) {
        },
        onSelectRow: function (id, dom, obj, index, event) {
            updateOperateButtonEnable(obj.editFlag)
        }
    });
};

function updateOperateButtonEnable(isEnabled) {
    if (isEnabled == "true") {
        // $('#edit_btn').removeAttr('disabled')
        $('#chanage_status_btn').removeAttr('disabled')
    } else {
        // $('#edit_btn').attr('disabled','disabled')
        $('#chanage_status_btn').attr('disabled', 'disabled')
    }
}

/** 停用/启用 */
function chanageStatus(id, states) {
    if (states == 1) {
        states = 0;
    } else {
        states = 1;
    }
    $.ajax({
        url: req_url_prefix + "/chanageStatus",
        type: "POST",
        dataType: "json",
        async: false,
        data: {
            id: id,
            states: states
        },
        success: function (res) {
            if (res.code == 0 && "true" == res.result) {
                showTips("保存成功");
            } else {
                showTips("保存失败");
            }
            renderData();
        }
    });
};

/** 停用/启用 */
$('#chanage_status_btn').bind('click', function () {
    var selRow = $('#X_Table').XGrid('getSeleRow');
    if (selRow.length > 0) {
        chanageStatus(selRow[0].id, selRow[0].states);
    } else {
        utils.dialog({
            content: '没有选中任何行！',
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
});

/** 编辑 */
$('#edit_btn').bind('click', function () {
    var selRow = $('#X_Table').XGrid('getSeleRow');
    if (selRow.length > 0) {
        queryById(selRow[0].id);
        $('#add_common_name').attr('disabled', 'disabled');
        // $('#purchaseType').attr('disabled', 'disabled');
    } else {
        utils.dialog({
            content: '没有选中任何行！',
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
});

/** 新增 */
$('#add_btn').bind('click', function () {
    console.log('add--------')
    $('#add_common_name').removeAttr('disabled');
    $('#purchaseType').removeAttr('disabled');
    $.ajax({
        url: req_url_prefix + "/selectSecondPositionList",
        type: "POST",
        dataType: "json",
        async: false,
        success: function (res) {
            if (res.code == 0) {
                // 重置编辑信息
                resetForm(res.result);
                $('#secondPropCheckedOrNot label').children("input[type=radio][value='0']").prop('checked', true)
                showModel("add");
            } else {
                showTips(res.msg);
            }
        }
    });
});

/** 重置表单 */
function resetForm(productNextPositionVoList) {
    clearForm()
    if (productNextPositionVoList && productNextPositionVoList.length > 0) {
        $('#goodsSecondPropGroup').css('display', 'block')
        $('#secondPropMustSelect').css('display', 'block')
        $('#secondPropCheckedOrNot').css('display', 'block')
        productNextPositionVoList.forEach(function (item) {
            $('#goodsSecondPropGroup').append($('<label style="margin-right: 10px"><input type="checkbox" value="' + item.id + '" name="' + item.name + '" >' + item.name + '</label>'))
        })
    } else {
        $('#goodsSecondPropGroup').css('display', 'none')
        $('#secondPropMustSelect').css('display', 'none')
        $('#secondPropCheckedOrNot').css('display', 'none')
    }
    goodsSecondPropGroupListener();
    secondPropCheckedOrNotListener();
    resetPurchaseType();
}

function clearForm() {
    $("#add_common_id").val("")
    $("#add_common_name").val("")
    $('#goodsSecondPropGroup').empty();
    $('#secondPropMustSelect label').children('input[type=radio]').prop('disabled', true);
    $('#secondPropMustSelect label').children('input[type=radio]').prop('checked', false);
    $('#secondPropCheckedOrNot label').children('input[type=radio]').prop('checked', false);
    $('#purchaseType').empty();
}

function resetPurchaseType() {
    $('#purchaseType').append('<option value=' + 0 + ' selected="selected">' + '地采' + '</option>')
        .append('<option value=' + 20 + '>' + 'OEM' + '</option>').append('<option value=' + 30 + '>' + '赋能' + '</option>')
        .append('<option value=' + 11 + '>' + '集采-统谈统采' + '</option>').append('<option value=' + 12 + '>' + '集采-统谈分采' + '</option>');
    $("#table3").empty();
    $('#purchaseTypeSelect').css('display', 'block');
    $('#tableBox').css('display', 'none');
    $('#table3').css('display', 'none');
}

function goodsSecondPropGroupListener() {
    $('#goodsSecondPropGroup label').children('input[type=checkbox]').on('click', function () {
        if ($('#goodsSecondPropGroup label input[type=checkbox]:checked').length == 0) {
            $('#secondPropMustSelect label').children('input[type=radio]').prop('disabled', true)
            $('#secondPropMustSelect label').children('input[type=radio]').prop('checked', false)
            // $('#secondPropCheckedOrNot label').children('input[type=radio]').prop('disabled', true)
            // $('#secondPropCheckedOrNot label').children('input[type=radio]').prop('checked', false)
        } else if ($('#goodsSecondPropGroup label input[type=checkbox]:checked').length == 1) { // 默认选择否
            if ($('#secondPropMustSelect label').children('input[type=radio]:checked').length == 0) {
                $('#secondPropMustSelect label').children("input[type=radio][value='0']").prop('checked', true)
            }
            // if ($('#secondPropCheckedOrNot label').children('input[type=radio]:checked').length == 0) {
            //     $('#secondPropCheckedOrNot label').children("input[type=radio][value='0']").prop('checked', true)
            // }
            $('#secondPropMustSelect label').children('input[type=radio]').prop('disabled', false)
            $('#secondPropCheckedOrNot label').children('input[type=radio]').prop('disabled', false)
        }
        var selectSecondPropArray = []
        $('#goodsSecondPropGroup label input[type=checkbox]:checked').each(function (index, item) {
            selectSecondPropArray.push({ id: item.value, name: item.name });
        })
        var productPositionVoArr = [];
        var html = '';
        var selectedOption = '';
        $('#table3').find('tr').each(function () {
            $(this).find('td').each(function () {
                html += $(this).text() + ',';
                selectedOption = $(this).find('select option:selected').text();
                html += selectedOption
            })
            html += '|';
        })
        var newArr = html.split('|')
        newArr.forEach((item) => {
            var productPositionArr = item.split(',')
            productPositionVoArr.push({
                id: productPositionArr[0],
                centralizedType: productPositionArr[2], //集采签约方式，0统谈统采 1统谈分采
                purchaseType: productPositionArr[4], //采购业务类型，0地采，11集采-统谈统采，12集采-统谈分采，20OEM，30赋能
                purchaseTypeVal: productPositionArr[4]
            })
        })
        productPositionVoArr.splice(productPositionVoArr.length - 1, 1)  //table中的数据
        selectSecondPropArray.forEach((item) => {
            let isHave = true
            productPositionVoArr.forEach((n) => {
                if (n.id === item.id) {
                    isHave = false
                }
            })
            if (isHave) {
                $("#table3").append("<tr><td class='secondPropVal' style='display:none'>" + item.id + "</td><td class='secondProp'>" + item.name + "</td><td class='centralizedType'>统谈统采</td><td class='purchaseTypeVal'>" + "<select  style='width:100%; '>" + "<option value='0' selected='selected'>地采</option>" + "<option value='20'>OEM</option>" + "<option value='30'>赋能</option>" + "<option value='11'>集采-统谈统采</option>" + "<option value='12'>集采-统谈分采</option>" + "</select>" + "</td></tr>")
                    .append("<tr><td class='secondPropVal' style='display:none'>" + item.id + "</td><td class='secondProp'>" + item.name + "</td><td class='centralizedType'>统谈分采</td><td class='purchaseTypeVal'>" + "<select  style='width:100%; '>" + "<option value='0' selected='selected'>地采</option>" + "<option value='20'>OEM</option>" + "<option value='30'>赋能</option>" + "<option value='11'>集采-统谈统采</option>" + "<option value='12'>集采-统谈分采</option>" + "</select>" + "</td></tr>")
            }
        })
        if ($(this).prop('checked') == false) {
            let checkSecondProp = $(this).val();
            $('#table3').find('tr').each(function () {
                if (checkSecondProp === $($(this).children('td')[0]).text()) {
                    $(this).remove()
                }
            })
        }
    })
}

function secondPropCheckedOrNotListener() {
    $('#secondPropCheckedOrNot label').children("input[type=radio]").change(function () {
        var myValue = $('input:radio[name="secondPropCheckedOrNot"]:checked').val();
        if (myValue == 1) {
            $('#purchaseTypeSelect').css('display', 'none')
            $('#tableBox').css('display', 'block')
            $('#table3').css('display', 'block')
            var selectSecondPropArray = []
            $('#goodsSecondPropGroup label input[type=checkbox]:checked').each(function (index, item) {
                selectSecondPropArray.push({ id: item.value, name: item.name });
            })
            $("#table3").empty()
            selectSecondPropArray.forEach((item) => {
                $("#table3").append("<tr><td class='secondPropVal' style='display:none'>" + item.id + "</td><td class='secondProp'>" + item.name + "</td><td class='centralizedType'>统谈统采</td><td class='purchaseTypeVal'>" + "<select  style='width:100%; '></select>" + "</td></tr>").append("<tr><td class='secondPropVal' style='display:none'>" + item.id + "</td><td class='secondProp'>" + item.name + "</td><td class='centralizedType'>统谈分采</td><td class='purchaseTypeVal'>" + "<select  style='width:100%; '></select>" + "</td></tr>")
            })
            $("#table3").append("<tr><td class='secondPropVal' style='display:none'>" + "" + "</td><td class='secondProp'>" + "" + "</td><td class='centralizedType'>统谈统采</td><td class='purchaseTypeVal'>" + "<select  style='width:100%; '></select>" + "</td></tr>").append("<tr><td class='secondPropVal' style='display:none'>" + "" + "</td><td class='secondProp'>" + "" + "</td><td class='centralizedType'>统谈分采</td><td class='purchaseTypeVal'>" + "<select  style='width:100%; '></select>" + "</td></tr>")
            $('select').append('<option value=' + 0 + ' selected="selected">' + '地采' + '</option>')
                .append('<option value=' + 20 + '>' + 'OEM' + '</option>').append('<option value=' + 30 + '>' + '赋能' + '</option>')
                .append('<option value=' + 11 + '>' + '集采-统谈统采' + '</option>').append('<option value=' + 12 + '>' + '集采-统谈分采' + '</option>');
        } else {
            $('#purchaseTypeSelect').css('display', 'block')
            $('#tableBox').css('display', 'none')
            $('#table3').css('display', 'none')
            $("#purchaseType").empty();
            $('#purchaseType').append('<option value=' + 0 + ' selected="selected">' + '地采' + '</option>')
                .append('<option value=' + 20 + '>' + 'OEM' + '</option>').append('<option value=' + 30 + '>' + '赋能' + '</option>')
                .append('<option value=' + 11 + '>' + '集采-统谈统采' + '</option>').append('<option value=' + 12 + '>' + '集采-统谈分采' + '</option>');
        }
    })
}

$('#table3').XGrid({
    colNames: ['定位二级', '集采签约方式', '采购业务类型'],
    colModel: [{
        name: 'secondProp',
        index: 'secondProp',
    }, {
        name: 'centralizedType',
        index: 'centralizedType'
    }, {
        name: 'purchaseTypeVal',
        index: 'purchaseTypeVal'
    }],
});

// 复原编辑信息
function restoreEditInfo(res) {
    resetForm(res.result.productNextPositionVoList)
    $("#add_common_id").val(res.result.id);
    $("#add_common_name").val(res.result.name);
    if ($('#secondPropMustSelect label').children("input[type=radio][value='" + res.result.requiredFlag + "']").length > 0) {
        $('#secondPropMustSelect label').children("input[type=radio][value='" + res.result.requiredFlag + "']").prop('checked', true)
    }
    if ($('#secondPropCheckedOrNot label').children("input[type=radio][value='" + res.result.isJudgeSecond + "']").length > 0) {
        $('#secondPropCheckedOrNot label').children("input[type=radio][value='" + res.result.isJudgeSecond + "']").prop('checked', true)
    }
    res.result.productNextPositionVoList.forEach(function (value) {
        $('#goodsSecondPropGroup label').children("input[type=checkbox][value='" + value.id + "']").prop('checked', value.selectedFlag)
        if (value.selectedFlag) {
            $('#secondPropMustSelect label').children('input[type=radio]').prop('disabled', false)
            $('#secondPropCheckedOrNot label').children('input[type=radio]').prop('disabled', false)
        }
    })
    if (null == res.result.purchaseType || 'null' == res.result.purchaseType || 'undefined' == res.result.purchaseType) {
        $('#purchaseType').append('<option value=' + 0 + ' selected="selected">' + '地采' + '</option>');
    } else {
        $('#purchaseType').find('option').each(function () {
            if ($(this).val() == res.result.purchaseType) {
                $(this).attr('selected', 'selected')
            }
        })
    }
    if (res.result.isJudgeSecond && res.result.isJudgeSecond === 1) {
        $('#purchaseTypeSelect').css('display', 'none')
        $('#tableBox').css('display', 'block')
        $('#table3').css('display', 'block')
    } else if (res.result.isJudgeSecond && res.result.isJudgeSecond === 0) {
        $('#purchaseTypeSelect').css('display', 'block')
        $('#tableBox').css('display', 'none')
        $('#table3').css('display', 'none')
    }
    if (res.result.productPositionVoList) {
        res.result.productPositionVoList.forEach((item) => {
            $("#table3").append("<tr><td class='secondPropVal' style='display:none'>" + item.id + "</td><td class='secondProp'>" + item.name + "</td><td class='centralizedType'>" + item.centralizedName + "</td><td class='purchaseTypeVal'>" + "<select id='" + item.purchaseType + "'  style='width:100%; '>" + "<option value='" + item.purchaseType + "' selected='selected'>" + item.purchaseTypeVal + "</option>" + "<option value='0'>地采</option>" + "<option value='20'>OEM</option>" + "<option value='30'>赋能</option>" + "<option value='11'>集采-统谈统采</option>" + "<option value='12'>集采-统谈分采</option>" + "</select>" + "</td></tr>")
            var thisSelect = $("select[id='" + item.purchaseType + "']");
            thisSelect.find("option").each(function () {
                if ($(this).attr("value") == item.purchaseType) {
                    if ($(this).attr("selected")) {

                    } else {
                        $(this).remove()
                    }
                }
            })
        })
    }
    // 假数据模拟
    // $('#purchaseTypeSelect').css('display', 'none')
    // $('#tableBox').css('display', 'block')
    // $('#table3').css('display', 'block')
    // var productPositionList = [{
    //     id: 242, //商品定位二级
    //     name: "独家品种", //商品定位二级名称
    //     centralizedName: "统谈统采", //集采签约方式

    //     purchaseType: "20",  //采购业务类型，0地采，11集采-统谈统采，12集采-统谈分采，20OEM，30赋能
    //     purchaseTypeVal: "OEM"
    // },
    // {
    //     id: 242, //商品定位二级
    //     name: "独家品种", //商品定位二级名称
    //     centralizedName: "统谈分采", //集采签约方式

    //     purchaseType: "30",  //采购业务类型，0地采，11集采-统谈统采，12集采-统谈分采，20OEM，30赋能
    //     purchaseTypeVal: "赋能"
    // }, {
    //     id: 243, //商品定位二级
    //     name: "独家品种2", //商品定位二级名称
    //     centralizedName: "统谈统采", //集采签约方式

    //     purchaseType: "11",  //采购业务类型，0地采，11集采-统谈统采，12集采-统谈分采，20OEM，30赋能
    //     purchaseTypeVal: "集采-统谈统采"
    // },
    // {
    //     id: 243, //商品定位二级
    //     name: "独家品种2", //商品定位二级名称
    //     centralizedName: "统谈分采", //集采签约方式

    //     purchaseType: "12",  //采购业务类型，0地采，11集采-统谈统采，12集采-统谈分采，20OEM，30赋能
    //     purchaseTypeVal: "集采-统谈分采"
    // }]

    // productPositionList.forEach((item) => {
    //     $("#table3").append("<tr><td class='secondPropVal' style='display:none'>" + item.id + "</td><td class='secondProp'>" + item.name + "</td><td class='centralizedType'>" + item.centralizedName + "</td><td class='purchaseTypeVal'>" + "<select id='" + item.purchaseType + "'  style='width:100%; '>" + "<option value='" + item.purchaseType + "' selected='selected'>" + item.purchaseTypeVal + "</option>" + "<option value='0'>地采</option>" + "<option value='20'>OEM</option>" + "<option value='30'>赋能</option>" + "<option value='11'>集采-统谈统采</option>" + "<option value='12'>集采-统谈分采</option>" + "</select>" + "</td></tr>")
    //     var thisSelect = $("select[id='" + item.purchaseType + "']");
    //     console.log(thisSelect.text());
    //     thisSelect.find("option").each(function () {
    //         if ($(this).attr("value") == item.purchaseType) {
    //             console.log($(this).attr("selected"))
    //             if ($(this).attr("selected")) {

    //             } else {
    //                 $(this).remove()
    //             }
    //         }
    //     })

    // })

}

/** 查询信息 */
function queryById(id) {
    $.ajax({
        url: req_url_prefix + "/queryByProductPositionId",
        type: "POST",
        dataType: "json",
        async: false,
        data: {
            id: id,
            type: common_dict_type
        },
        success: function (res) {
            if (res.code == 0) {
                // 复现上次编辑信息
                restoreEditInfo(res)
                showModel("edit");
            } else {
                showTips(res.msg);
            }
        }
    });
};


/** 保存信息 */
function save(obj) {
    $.ajax({
        url: req_url_prefix + "/saveProductFirstPosition",
        type: "POST",
        dataType: "json",
        async: false,
        contentType: "application/json;charset=UTF-8",
        data: JSON.stringify(obj),
        success: function (res) {
            if (res.code == 0 && "true" == res.result) {
                showTips("保存成功");
            } else {
                showTips(res.msg);
            }
            renderData();
        }
    });
    resetForm();
};


/** 保存信息 */
function saveCheck() {
    var result = { passed: false };
    var name = $("#add_common_name").val();
    var regexp = /^[\u4e00-\u9fa5a-zA-Z]+$/;
    if (!regexp.test(name)) {
        showTips("格式不正确，只能输入中文或英文字母");
        return result;
    }
    result.passed = true;
    result.id = $("#add_common_id").val();
    result.name = name;
    result.type = common_dict_type;

    // 获取选中的二级属性
    var selectSecondPropArray = []
    $('#goodsSecondPropGroup label input[type=checkbox]:checked').each(function (index, item) {
        selectSecondPropArray.push({ id: item.value });
    })
    // 获取是否必选
    var selectSecondPropMust = []
    $('#secondPropMustSelect label input[type=radio]:checked').each(function (index, item) {
        selectSecondPropMust.push(item.value);
    })
    // 获取是否判断二级
    var selectedSecondPropCheckedOrNot = []
    $('#secondPropCheckedOrNot label input[type=radio]:checked').each(function (index, item) {
        selectedSecondPropCheckedOrNot.push(item.value);
    })
    var productPositionVoArr = []
    var html = '';
    var selectedOption = '';
    $('#table3').find('tr').each(function () {
        $(this).find('td').each(function () {
            html += $(this).text() + ',';
            selectedOption = $(this).find('select option:selected').text();
            html += selectedOption
        })
        html += '|';
    })
    var newArr = html.split('|')
    newArr.forEach((item) => {
        var productPositionArr = item.split(',')
        productPositionVoArr.push({
            id: productPositionArr[0],
            centralizedType: productPositionArr[2], //集采签约方式，0统谈统采 1统谈分采
            purchaseType: productPositionArr[4], //采购业务类型，0地采，11集采-统谈统采，12集采-统谈分采，20OEM，30赋能
            purchaseTypeVal: productPositionArr[4]
        })
    })
    productPositionVoArr.splice(productPositionVoArr.length - 1, 1)
    if (selectSecondPropArray.length > 0) { // 选择了二级属性，但未选择是否必选
        result.productNextPositionVoList = selectSecondPropArray
        result.requiredFlag = selectSecondPropMust[0]
    }
    result.isJudgeSecond = selectedSecondPropCheckedOrNot[0];
    if (selectedSecondPropCheckedOrNot[0] === '0') {
        result.purchaseType = $('#purchaseType').find("option:selected").val();
        result.purchaseTypeVal = $('#purchaseType').find("option:selected").text();
    } else if (selectedSecondPropCheckedOrNot[0] === '1') {
        result.productPositionVoList = [].concat(productPositionVoArr)
        result.productPositionVoList.forEach((item) => {
            if (item.centralizedType == "统谈统采") {
                item.centralizedType = 1
            } else if (item.centralizedType == "统谈分采") {
                item.centralizedType = 2
            }
            if (item.purchaseType == "地采") {
                item.purchaseType = 0
            } else if (item.purchaseType == "集采-统谈统采") {
                item.purchaseType = 11
            } else if (item.purchaseType == "集采-统谈分采") {
                item.purchaseType = 12
            } else if (item.purchaseType == "OEM") {
                item.purchaseType = 20
            } else if (item.purchaseType == "赋能") {
                item.purchaseType = 30
            }
        })
    }
    return result;
};

function getSelectPropIds() {
    $('#goodsSecondPropGroup')
}


/** 展示弹板信息 */
function showModel(flag) {
    var str = "新建";
    if ("edit" == flag) {
        str = "编辑";
    }
    utils.dialog({
        title: str + '商品定位',
        content: $('#modal'),
        width: 450,
        okValue: '确认',
        cancelValue: '取消',
        cancel: true,
        ok: function () {
            var params = saveCheck();
            if (!params.passed) {
                return false;
            }
            save(params);
        }
    }).show();
}

/** 时间格式化 */
function dateFormatter(val) {
    if (val != null && val != "") {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
    } else {
        return "";
    }
};

/** 状态格式化 */
function statesFormatter(val) {
    if (0 == val) {
        return "停用";
    } else if (1 == val) {
        return "启用";
    } else {
        return "";
    }
};

/** 弹框提示 */
function showTips(content) {
    utils.dialog({
        content: content,
        quickClose: true,
        timeout: 2000
    }).showModal();
};
