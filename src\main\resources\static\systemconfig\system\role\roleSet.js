var orgCode = "${orgCode}";
var roleId = $("#ret_roleId").val();
var roleCode = $("#ret_roleCode").val();
var roleName = "${roleName}";

if (roleName != null && roleName != "") {
	$("#roleName").attr("disabled", true);
} else {
	$("#roleName").attr("disabled", false);
}
var baseUrl = "";
var openFlag = true;
var isUseLocalData = false;

var zTreeObjSystemSet; // 系统设置

var setting = {
	check: {
		enable: true
	},
	data: {
		simpleData: {
			enable: true
		}
	},
	callback: {
		onCheck: onCheck,
	},
	view: {
		expandSpeed: "",
//		addHoverDom: addHoverDom,
		removeHoverDom: removeHoverDom,
		selectedMulti: false
	},
	edit: {
		enable: true,
		drag: {
			isCopy: false,
			isMove: false,
		},
		showRenameBtn: false,
		showRemoveBtn: false,
		removeTitle: "删除改节点",
	}
};

function backToRoleManage() {
	window.location.href = "/proxy-sysmanage/sysmanage/role/roleManage";
}

//弹框提示
function showTips(contentText) {
	utils.dialog({
		content: contentText,
		quickClose: true,
		timeout: 2000
	}).showModal();
}

function showDialog(titleText, contentText) {
	//提示框
	utils.dialog({
		width: 180,
		height: 30,
		title: titleText,
		content: contentText,
		quickClose: false,
		okValue: '确定',
		ok: function() {},
	}).showModal();
}

var menuMap = new Map();
var buttonMap = new Map();
var fieldMap = new Map();

function onCheck(e, treeId, treeNode) {
	return
}

function initTree(code) {
	$.ajax({
		type: "post",
		url: baseUrl + '/proxy-sysmanage/sysmanage/resource/querySystemPermissionTreeListNew',
		dataType: "json",
		data: {
			orgCode: orgCode,
			roleId: roleId,
			flag: "1",
			appCode: code
		},
		success: function(res) {
			if (res && res.code == 0) {
				var zTreeNodes = res.result;				
				zTreeObjSystemSet = $.fn.zTree.init($("#tree_" + code), setting, zTreeNodes);				
			} else {
				showTips("加载权限树失败");
			}
		}
	});
}

$(document).ready(function() {
	var tabsAry = ['orgTreeList'];
    // var tabsAry = ['systemSet', 'erp', 'dataCenter', 'parity'];
	// tabs 切换
	$('.nav-tabs>li').bind('click', function() {
		var $this = $(this),
			$nav_content = $('.nav-content');
		$this.addClass('active').siblings().removeClass('active');
		$nav_content.children('div').eq($this.index()).show().siblings().hide();
		$nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');
	});


	if (isUseLocalData) {
		var zTreeObj = $.fn.zTree.init($("#tree"), setting, zTreeNodes);
	} else {
		tabsAry.forEach(function(tabs, index) {
			initTree(tabs);
		});
	}	

});

function saveMenu() {
	if ($("#roleName").val() == "") {
		showTips("请输入角色名称！");
		return result;
	}
	if ($("#roleName").val().length > 30) {
		showTips("角色名称不能超过30个字符！");
		return result;
	}

	var nodes_zTreeArr = new Array();
	nodes_zTreeArr.push(zTreeObjSystemSet.getCheckedNodes(true));	

	var str = "";
	var mid, type, checkOrgCode, extCode;
	var mKey;
	menuMap.clear();

	nodes_zTreeArr.forEach(function(nodes_zTreeObj, index) {
		nodes_zTreeObj.forEach(function(nodes_zTree, index) {
			mid = nodes_zTree.id;
			type = nodes_zTree.resType;
			checkOrgCode = nodes_zTree.appCode;
			extCode = nodes_zTree.extCode;

			mKey = checkOrgCode + "-" + mid;
			menuMap.set(mKey, checkOrgCode);
		});
	});

	var menuDataArray = new Array();
	menuMap.forEach(function(value, key, map) {
		menuDataArray.push(value);
	});

	var formData = {};
	formData["menuList"] = menuDataArray;
	formData["roleCode"] = roleCode;
	formData["Name"] = $("#roleName").val();
	formData["loginOrgCode"] = orgCode;
	//等等与-1 代表是新增
	var flag = "";
	if (roleId == -1) {
		flag = 1;

	}
	$.ajax({
		type: "post",
		url: baseUrl + '/proxy-sysmanage/sysmanage/system/saveRoleOrg',
		contentType: "application/json; charset=utf-8",
		dataType: "json",
		data: JSON.stringify(formData),
		success: function(res) {
			if (res && res.code == 0) {
				$("#ret_roleId").val(res.result);
				roleId = res.result;
				showDialog("提示", "保存权限成功！");
			}
			if (res && res.code == 100) {
				showDialog("提示", res.msg);
			}
			if (!res && res.code == 0 || !res && res.code == 100) {
				showDialog("提示", "保存权限失败！");
			}
		}
	});

}


function removeHoverDom(treeId, treeNode) {
	if (!openFlag) {
		return;
	}
	if (treeNode.resType == 0 || treeNode.resType == 2) {
		return;
	} else {
		$("#diyBtn_view_" + treeNode.id).unbind().remove();
	}
};

function addHoverDom(treeId, treeNode) {
	if (!openFlag) {
		return;
	}
	if (treeNode.resType == 0 || treeNode.resType == 2) {
		return;
	} else {
		var aObj = $("#" + treeNode.tId + "_a");
		if ($("#diyBtn_view_" + treeNode.id).length > 0) return;
		var editStr =
			"<a id='diyBtn_view_" + treeNode.id + "' href='javascript:void(0);'>权限分配</a>";
		aObj.append(editStr);
		var btn0 = $("#diyBtn_view_" + treeNode.id);
		if (btn0) {
			btn0.bind("click", function() {
				viewEvent(treeNode);
			});
		}

	}
}

//--------------------------8-12

function buildButtonList(appCode, extCode, resourceId, buttonArray) {
	if (!buttonArray || buttonArray.length == 0) {
		return "";
	}
	var str = '';
	$.each(buttonArray, function(idx, obj) {
		str += '<div class="col-xs-4 col-sm-4 col-md-4">' +
			'<div class="checkbox">' +
			'<label>' +
			'<input type="checkbox" value="' + obj.id + '-' + obj.resType + '"  name="chkButton' + appCode +
			'" onchange="checkButton(this);" data-code="' + appCode + '" data-id="' + resourceId + '">' + obj.resName +
			'</label>' +
			'</div></div>';
	});
	return str;

}

function buildFieldList(appCode, extCode, resourceId, obj) {
	if (!obj || !obj.fieldsEn) {
		return '';
	}
	if (obj.fieldsEn.length == 0) {
		return '';
	}

	//--------------8-27---start
	var gridId = obj.gridId;
	//--------------8-27---end

	var fieldEnArray = obj.fieldsEn.split(",");
	var fieldCnArray = obj.fieldsCn.split(",");
	var len = fieldEnArray.length;
	if (len == 0) {
		return '';

	}
	var str = "";
	for (var i in fieldEnArray) {
		str +=
			'<div class="checkbox">' +
			'<label>' +
			'<input type="checkbox" value="' + fieldEnArray[i] + '"  name="chkField' + appCode +
			'" onchange="checkField(this);" data-code="' + appCode + '" data-id="' + resourceId + '" data-gId="' + gridId +
			'">' + fieldCnArray[i] + '</label>'

			+
			'</div>';
	}
	return str;
}

function checkButton(obj) {
	var button = $(obj);
	var appcode = button.attr("data-code");
	var resId = button.attr("data-id");

	var id = button.val();
	var key = appcode + "-" + resId;
	var buttonSet = buttonMap.get(key);
	console.log("checkButton-key=" + key);
	var value = key + '-' + id;
	console.log("checkButton-buttonSet-value=" + value);
	if (obj.checked) {
		if (!buttonSet) {
			buttonSet = new Set();
			buttonMap.set(key, buttonSet);
		}
		buttonSet.add(value);
	} else {
		if (buttonSet) {
			buttonSet.delete(value);
		}
		console.log("checkButton-unchecked--value=" + value);
	}
}

function checkField(obj) {
	var field = $(obj);
	var appcode = field.attr("data-code");
	var resId = field.attr("data-id");

	//---------8-27--add  start
	var gridId = field.attr("data-gId");
	//---------8-27--add  end

	var name = field.val();
	var key = appcode + "-" + resId + "-" + gridId; //8-27
	var fieldSet = fieldMap.get(key);
	console.log("checkField-key=" + key);
	var value = name; //8-13
	if (obj.checked) {
		if (!fieldSet) {
			fieldSet = new Set();
			fieldMap.set(key, fieldSet);
		}
		fieldSet.add(value);
	} else {
		if (fieldSet) {
			fieldSet.delete(value);
		}
	}
}

//----------8-22--add

function selectButton(appCode, extCode, resourceId) {
	var key = appCode + "-" + resourceId;
	var buttonSet = buttonMap.get(key);
	if (!buttonSet) {
		return;
	}
	var id;
	$("input[name='chkButton" + appCode + "']").each(function() {
		id = $(this).val();
		if (buttonSet.has(key + '-' + id)) {
			this.checked = true;
		}
	});
}

function selectField(appCode, extCode, resourceId, gridId) {
	var key = appCode + "-" + resourceId + "-" + gridId; //8-2
	var fieldSet = fieldMap.get(key);
	if (!fieldSet) {
		return;
	}
	var id;
	$("input[name='chkField" + appCode + "']").each(function() {
		id = $(this).val();
		if (fieldSet.has(id)) {
			this.checked = true;
		}
	});
}

