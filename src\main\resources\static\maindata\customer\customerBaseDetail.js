$(function () {
    var baseProductId=$("#baseProductId").val();
    $('div[fold=head]').fold({
        sub: 'sub'
    });

    //特殊经营范围回显
    var specialBusinessScope = $('#specialBusinessScope').val().split(',');
    $("input[name='specialBusinessScope']").each(function () {
        for(var x = 0; x < specialBusinessScope.length; x ++){
            if($(this).val() == specialBusinessScope[x]){
                $(this).attr("checked","checked");
            }
        }
    });

    //经营类别回显
    if ($('#businessCategory').val()){
        var businessCategory = $('#businessCategory').val().split(',');
        $("input[name='businessCategoryName']").each(function () {
            for(var x = 0; x < businessCategory.length; x ++){
                if($(this).val() == businessCategory[x]){
                    $(this).attr("checked","checked");
                }
            }
        });
    }

    //不可经营类别回显
    if ($('#cannotBusinessCategory').val()){
        var cannotBusinessCategory = $('#cannotBusinessCategory').val().split(',');
        $("input[name='cannotBusinessCategoryName']").each(function () {
             for(var x = 0; x < cannotBusinessCategory.length; x ++){
                 if($(this).val() == cannotBusinessCategory[x]){
                    $(this).attr("checked","checked");
                }
            }
         });
     }


    //结算方式
    $("input[name='itemValue']:not(:checked)").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').hide();
    });
    $("input[name='itemValue']:checked").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').show();
    });

    //tab切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    })

    //切换的质量保证协议
    // $('#table1').XGrid({
    //     url:'/proxy-customer/customer/customerCommonData/getCustomerQualityAgreementAll?correlationId='+$('#baseId').val()+'&type=3',
    //     colNames: ['','<i class="i-red">*</i>签订日期', '<i class="i-red">*</i>有效期至', '<i class="i-red">*</i>签订人',  '附件','附件数据'],
    //     colModel: [{
    //         name: 'id', //与反回的json数据中key值对应
    //         index: 'id', //索引。其和后台交互的参数为sidx
    //         key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    //         hidden: true
    //     }, {
    //         name: 'signedDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         },
    //         index: 'signedDate'
    //     }, {
    //         name: 'validDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         },
    //         index: 'validDate'
    //     }, {
    //         name: 'signer',
    //         index: 'signer'
    //     }, {
    //         name: 'enclosureCount',
    //         index: 'enclosureCount',
    //         formatter:function (value) {
    //             var str='无';
    //             if(value)
    //             {
    //                 var tableId = "#table1"
    //                 str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
    //             }
    //             return str;
    //         },
    //         unformat: function (e) {
    //             e=e.replace(/<[^>]+>/g,'');
    //             if(e == '无'){
    //                 e = 0;
    //             }
    //             return e;
    //         }
    //     },{
    //         name:'customerEnclosureVoList',
    //         index:'customerEnclosureVoList',
    //         hidden:true,
    //         formatter:function (value) {
    //             if(value)
    //             {
    //                 return JSON.stringify(value);
    //             }
    //             return JSON.stringify([]);
    //         }
    //     }],
    //     altRows: true,
    //     rownumbers: true,
    //     /*rowNum: 10,
    //     altRows: true, //设置为交替行表格,默认为false
    //     pager:'#table1_page'*/
    //     gridComplete: function () {
    //         setTimeout(function () {
    //             utils.setTableInpTit('#table1')
    //         },3000)
    //     }
    // });

    //客户委托书
    $('#table2').XGrid({
        url:'/proxy-customer/customer/customerCommonData/getCustomerDelegationFileAll?correlationId='+$('#baseId').val()+'&type=3',
        colNames: ['', '<i class="i-red">*</i>委托人类型','<i class="i-red">*</i>姓名',
            '<i class="i-red">*</i>电话',  '<i class="i-red">*</i>委托书有效期至','附件','附件数据'
        ],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'mandatorType',
            index: 'mandatorType',
            formatter:function(value){
                if(!value)return false;
                localBatchName(value);
            },
            rowtype: '#clientTypeId'
        },{
            name: 'delegationName',
            index: 'delegationName'
        },
        //     {
        //     name: 'delegationSex',
        //     index: 'delegationSex',
        //     formatter: function (e) {
        //         if (e == '1') {
        //             return '男'
        //         } else if (e == '2') {
        //             return '女'
        //         } else {
        //             return '未知'
        //         }
        //     }
        // },
            {
            name: 'delegationTel',
            index: 'delegationTel'
        },
        //     {
        //     name: 'delegationNum',
        //     index: 'delegationNum'
        // },
        //     {
        //     name: 'delegationAddr',
        //     index: 'delegationAddr'
        // },
        //     {
        //     name: 'delegationIdentityDate',
        //     index: 'delegationIdentityDate',
        //     formatter:function(value){
        //         var date=value;
        //         if(!value)return false;
        //         date=format(value);
        //         return date.split(' ')[0];
        //     }
        // },
            {
            name: 'validityDelegationCredential',
            index: 'validityDelegationCredential',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'enclosureCount',
            index: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#table2";
                    str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        },{
            name:'customerEnclosureVoList',
            index:'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],
        altRows: true,
        rownumbers: true,
        /*rowNum: 10,
        altRows: true
        // pager:'#table2_page'*/
        gridComplete: function () {
            setTimeout(function () {
                utils.setTableInpTit('#table2')
            },3000)
        }
    });

    //被委托人身份证
    $('#certTable').XGrid({
        url:'/proxy-customer/customer/customerCommonData/getCustomerBeProxyIdentityAll?correlationId='+$('#baseId').val()+'&type=3',
        colNames: ['', '<i class="i-red">*</i>姓名', '<i class="i-red">*</i>证件号码', '<i class="i-red">*</i>身份证有效期至','附件','附件数据'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'customerName',
            index: 'customerName'
        }, {
            name: 'identityNumber',
            index: 'identityNumber'
        }, {
            name: 'identityValidityDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            },
            index: 'identityValidityDate'
        }, {
            name: 'enclosureCount',
            index: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#certTable";
                    str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        },{
            name:'customerEnclosureVoList',
            index:'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],
        altRows: true,
        rownumbers: true,
        /*rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        pager:'#certTable_page'*/
        gridComplete: function () {
            setTimeout(function () {
                utils.setTableInpTit('#certTable')
            },3000)
        }
    });
    initTable3($('#baseId').val(),3);
    //批准文件 参数1：关联id，参数2：客户类型(首营、机构...)
    // $('#table3').XGrid({
    //     url:'/proxy-customer/customer/customerCommonData/getCustomerApprovalFileAll?correlationId='+$('#baseId').val()+'&type=3',
    //     colNames: ['', '证书类型', '证书编号', '经营范围', '发证机关', '发证日期', '有效期至', '附件','附件数据'],
    //     colModel: [{
    //         name: 'id', //与反回的json数据中key值对应
    //         index: 'id', //索引。其和后台交互的参数为sidx
    //         key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    //     }, {
    //         name: 'credentialTypeId',
    //         index: 'credentialTypeId'
    //     }, {
    //         name: 'credentialCode',
    //         index: 'credentialCode'
    //     }, {
    //         name: 'businessScope',
    //         index: 'businessScope'
    //     }, {
    //         name: 'certificationOffice',
    //         index: 'certificationOffice'
    //     }, {
    //         name: 'openingDate',
    //         index: 'openingDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'validUntil',
    //         index: 'validUntil',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'enclosureCount',
    //         index: 'enclosureCount',
    //         formatter:function (value) {
    //             var str='';
    //             if(value)
    //             {
    //                 var tableId = "#table3"
    //                 str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
    //             }
    //             return str;
    //         },
    //         unformat: function (e) {
    //             e=e.replace(/<[^>]+>/g,'');
    //             return e;
    //         }
    //     },{
    //         name:'customerEnclosureVoList',
    //         index:'customerEnclosureVoList',
    //         hidden:true,
    //         formatter:function (value) {
    //             if(value)
    //             {
    //                 return JSON.stringify(value);
    //             }
    //             return JSON.stringify([]);
    //         }
    //     }],
    //     rowNum: 10,
    //     altRows: true, //设置为交替行表格,默认为false
    //     pager:'#table3_page'
    // });

    //客户合同回显
    // $.ajax({
    //     url:'/proxy-customer/customer/customerCommonData/getCustomerContractAll?correlationId='+$('#baseId').val()+'&type=3',
    //     dataType:'json',
    //     //async:false,
    //     success:function(data){
    //         if(!data.result) return false;
    //         if(data.result.length){
    //             $(data.result).each(function(contractIndex,ontractItem){
    //                 var contractTypes = $('#contractType').find('input[type=checkbox]');
    //                 $(contractTypes).each(function(index,item){
    //                     if(ontractItem.contractType==$(this).val()){
    //                         $(this).attr("checked",'true');
    //                         if(ontractItem.customerEnclosureVoList && ontractItem.customerEnclosureVoList.length > 0){
    //                             for(var j=0;j<ontractItem.customerEnclosureVoList.length;j++)
    //                             {
    //                                 ontractItem.customerEnclosureVoList[j].type = ontractItem.contractType;
    //                             }
    //                             var arr=JSON.stringify(ontractItem.customerEnclosureVoList);
    //                             var html='<input type="hidden" data-type="'+ontractItem.contractType+'" id="contractType'+ontractItem.contractType+'" value=\''+arr+'\' />';
    //                             $("body").append(html);
    //                         }
    //                     }
    //                 });
    //             });
    //         }
    //     },
    //     error:function(){

    //     }
    // });
    //客户合同 查看附件
    // $("#qtfjView").on("click",function(){
    //     //获取type类型
    //     var imgList=[];
    //     var checkInp=$("#contractType input[type='checkbox']:checked");
    //     if(checkInp.length)
    //     {
    //         for(var i=0;i<checkInp.length;i++)
    //         {
    //             var type=checkInp.eq(i).val();
    //             var inp=$("#contractType"+type);
    //             if(inp.length)
    //             {
    //                 var imgArr=JSON.parse(inp.val());
    //                 imgList=imgList.concat(imgArr);
    //             }
    //         }
    //     }
    //     $.viewImg({
    //         fileParam:{
    //             name:'enclosureName',
    //             url:'url'
    //         },
    //         list:imgList
    //     })
    // });
//客户合同双击选项预览对应的附件
    // $('#contractType label').each(function () {
    //     $(this).dblclick(function () {
    //         var imgList=[];
    //         var type = $(this).prev().val();
    //         var inp=$("#contractType"+type);
    //         if(inp.length){
    //             var imgArr=JSON.parse(inp.val());
    //             imgList=imgList.concat(imgArr);
    //         }
    //         $.viewImg({
    //             fileParam:{
    //                 name:'enclosureName',
    //                 url:'url'
    //             },
    //             list:imgList
    //         });
    //     })
    // })
    // $('#qtfjUpload').hide();
    //
    // $("div[name='operateRows']").hide();

    $("div[name='operateRows'] div button").hide();
    // 一键下载
    $('.patchDownload').show();
    // 客户合同，查看文件
    // $('#qtfjView').show();

    //初始化地址选择
    /*$("#nature input[type='text']").attr("disabled","disabled");
     $("#busineessScopeAll").attr("disabled","disabled");*/
    //$('[data-toggle="distpicker"]').distpicker();
    // $("input[type='text']").attr("disabled","disabled");
    // $("input[type='tel']").attr("disabled","disabled");
    // $("input[type='number']").attr("disabled","disabled");
    // $("input[type='checkbox']").attr("disabled","disabled");
    // $("input[type='radio']").attr("disabled","disabled");
    // $("textarea").attr("disabled","disabled");
    // $("select").attr("disabled","disabled");

    var townFormat = function(obj,info){
        $(obj).find("select:last").html('');
        if(info['code']%1e4&&info['code']<7e5){	//是否为“区”且不是港澳台地区
            $.ajax({
                url:'/proxy-sysmanage/sysmanage/area/getNexAreaListByPcode?pCode='+info['code'],
                dataType:'json',
                //async:false,
                success:function(town){
                    $(obj).find("select:last").html('');
                    $(obj).find("select:last").append('<option value="">请选择</option>');
                    var arr=town.result;
                    if(arr && arr.length>0)
                    {
                        for(var i=0;i<arr.length;i++)
                        {
                            var code='';
                            var text='';
                            for(name in arr[i]){
                                if(name == 'code')
                                {
                                    code=arr[i][name];
                                }
                                if(name == 'name')
                                {
                                    text=arr[i][name];
                                }
                            }
                            $(obj).find("select:last").append('<option value="'+code+'">'+text+'</option>');
                        }
                        var flag=$(obj).attr("data-flag");
                        if(flag == "true")
                        {
                            var val3=$.trim($(obj).find("select:last").attr("data-value"));
                            if(val3 && val3 != '')
                            {
                                $(obj).find("select:last").val(val3);
                            }
                        }
                        $(obj).attr("data-flag","false");
                    }
                }
            });
        }
    };
    // initCitys($('#registerBox'),'registerProvinceId','registerCityId','registerDistrictId');
    // initCitys($('#storageBox'),'storageProvinceId','storageCityId','storageDistrictId');
    // initCitys($('#billingBox'),'billingProvinceId','billingCityId','billingDistrictId');
    // initCitys($('.depotList'),'billingProvinceId','billingCityId','billingDistrictId');
    //初始化四级联动
    function initCitys(obj,provinceField,cityField,areaField){
        obj.attr("data-flag","true").citys({
            dataUrl:'/proxy-sysmanage/sysmanage/area/getAreaExtStreet',
            provinceField:provinceField, //省份字段名
            cityField:cityField,         //城市字段名
            areaField:areaField,         //地区字段名
            onChange:function(obj,info){
                townFormat(obj,info);
            }
        },function(obj){
            var flag=$(obj).attr("data-flag");
            if(flag == "true")
            {
                var dataVal=$.trim($(obj).find("select:first").attr("data-value"));
                if(!dataVal || dataVal == '')return;

                $(obj).find("select:first").val(dataVal);
                $(obj).find("select:first").change();
                var dataVal1=$.trim($(obj).find("select").eq(1).attr("data-value"));
                $(obj).find("select").eq(1).val(dataVal1);
                var val1=$(obj).find("select").eq(1).val();
                if(!val1 || val1 == '')return;

                $(obj).find("select").eq(1).change();
                var dataVal2=$.trim($(obj).find("select").eq(2).attr("data-value"));
                if(!dataVal2 || dataVal2 == '')return;

                $(obj).find("select").eq(2).val(dataVal2);
                var val2=$(obj).find("select").eq(2).val();

                if(!val2 || val2 == '')return;
                $(obj).find("select").eq(2).change();
            }
        });
    }
    //修改记录
    var customerCode =  $("#custmCode").val();
    var orgCode = $("#orgCode").val();
    $('#modifyTable').XGrid({
        url:'/proxy-customer/customer/customerBaseAppl/queryCustomerChangeRecord?customerCode='+customerCode+'&orgCode='+orgCode,
        /*data: [{
            date: '2018-6-26 10:32:19',
            certificate_type:'客户停用申请',
            certificate_num: '姓名3',
            operate_range: '',
            Issuing_organ: '【是否停用】：修改前“否”，修改后“是”',
            Issuing_date: ''
        }],*/
        colNames: ['申请时间', '申请操作', '申请人', '单据编号', '申请明细', '审批流程'],
        colModel: [{
            name: 'applTimeStr', //与反回的json数据中key值对应
            index: 'date', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'applOperation',
            index: 'certificate_type'
        }, {
            name: 'applUser',
            index: 'certificate_num'
        }, {
            name: 'applicationNumber',
            index: 'operate_range'
        }, {
            name: 'applDetail',
            index: 'Issuing_organ',
            formatter:function (value,b,rowData) {

                if (value == 'customerFirstAppl') {
                    id = rowData.id;
                    return '<a href=/proxy-customer/customer/customerFirstAppl/detail?id='+ id+'>查看最初申请明细</a>';
                }else {
                    if(rowData.applicationOperation != 0){
                        var arr=value.split('；');
                        var html='<div class="modifyTheRecord">\n' +
                            '<div class="itemContent"><div class="mrLeft">';
                        for(var i=0;i<arr.length;i++)
                        {
                            html+='<p>'+arr[i]+'</p>';
                        }
                        html+='</div></div>' +
                            '\t\t<div class="mrRight">\n' +
                            '\t\t\t<a href="javascript:;" class="moreBtn">展开</a>\n' +
                            '\t\t</div>\n' +
                            '\t</div>\n';
                        return html;
                    }else{
                        value=value.replace(/&lt;/g,'<').replace(/&gt;/g,'>');
                        return value;
                    }
                }
            }
        }, {
            name: 'approvalProcessId',
            index: 'Issuing_date',
            // rowtype: '#lookBtn',
            formatter: function (value, grid, rows, state) {
                return "<a href = 'javascript:void(0)' onclick='toFlowChart("+value+")'>查看</a>";
            }
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        /*ondblClickRow: function (id,dom,obj,index,event) {
            // console.log('双击行事件', id,dom,obj,index,event);
        },
        onSelectRow: function (id,dom,obj,index,event) {
            // console.log('单机行事件',id,dom,obj,index,event);
        },*/
        pager:'#modifyTable_page',
        rownumbers: true,
        gridComplete:function () {

        }
    });

    // 注册地址
    let registerAddressSelIdObj = [
        {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvinceId',nextNodeId: 'province1'},
        {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCityId',nextNodeId: 'city1'},
        {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerDistrictId',nextNodeId: 'district1'},
        {nextNodeWrap: '#streetSel_wrap',nextNodeName: 'registerStreetId',nextNodeId: 'street1'},
    ]
    let registerPromiseArray = [];
    utils.setAllProDom('#provinceSel_wrap', registerAddressSelIdObj, '#registerBox', true,function () {
        // 注册地址有值回显
        let _registerHiddenVal = eval($('#registerAddressJson').val());
        if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
        _registerHiddenVal.splice(_registerHiddenVal.length - 1);
        $('#' + registerAddressSelIdObj[0]['nextNodeId']).prop('disabled', true);
        $('#' + registerAddressSelIdObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
        for (let i = 1; i < _registerHiddenVal.length; i++) {
            registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
        }
        Promise.all(registerPromiseArray).then(data => {
            // console.log(data)
            for (let i = 0; i < data.length; i++) {
                $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).html(data[i]);
                $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).attr('data-depth',i+2);
                $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).prop('disabled', true);
            }
        })
    });
    disable();
    // 仓库地址
    // let storgeAddressSelIdObj = [];
    // let _storgeHiddenVal = eval($('#repertoryAddressJson').val());
    // if (!_storgeHiddenVal) _storgeHiddenVal = [['','','','']];
    // let _storgeHiddenValArr =  eval($('#repertoryAddressJson').val());
    // if (!_storgeHiddenValArr) _storgeHiddenValArr = [['','','','']];
    // $(_storgeHiddenValArr).each((index,item) => {
    //     item.splice(item.length - 1);
    // });
    // let obj = distpickerHTML(_storgeHiddenValArr.length);
    // $(obj.radomInit).each((index, item) => {
    //     let _arr = [
    //         {nextNodeWrap: '#stoProvinceSel_wrap_' + item,nextNodeName: 'repertoryProvince_' + item,nextNodeId: 'repertoryProvince_' + item},
    //         {nextNodeWrap: '#stoCitySel_wrap_' + item,nextNodeName: 'repertoryCity_' + item,nextNodeId: 'repertoryCity_' + item},
    //         {nextNodeWrap: '#stoDistrictSel_wrap_' + item,nextNodeName: 'repertoryArea_' + item,nextNodeId: 'repertoryArea_' + item},
    //         {nextNodeWrap: '#stoStreetSel_wrap_' + item,nextNodeName: 'repertoryStreet_' + item,nextNodeId: 'repertoryStreet_' + item}
    //     ]
    //     storgeAddressSelIdObj.push(_arr)
    // });
    // $('#depotAddress').find('.depotList').remove();
    // $('#depotAddress').append(obj.html)
    // let storgePromiseArray = [];
    // $(obj.radomInit).each((index, item) => {
    //     utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[index], storgeAddressSelIdObj[index], '#storageBox', true,function () {
    //         $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(_storgeHiddenValArr[index][0]);
    //         $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[index][4]);
    //         $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
    //         $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=storageAddress]').prop('disabled', true);
    //         disable();
    //         for (let ind = 1; ind < _storgeHiddenValArr[0].length; ind++) {
    //             storgePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenValArr[0][ind-1]));
    //         }
    //         let allSelArr = storgeAddressSelIdObj.flat().map((item, index) => {
    //             if (index % 4 != 0) {
    //                 return item['nextNodeId']
    //             }
    //         }).filter(item => {
    //             return item
    //         });
    //         Promise.all(storgePromiseArray).then(data => {
    //             for (let i = 0; i < data.length; i++) {
    //                 $('#' + allSelArr[i]).html(data[i]);
    //                 $('#' + allSelArr[i]).attr('data-depth',i+2);
    //                 $('#' + allSelArr[i]).val(_storgeHiddenValArr[index][i + 1]);
    //                 $('#' + allSelArr[i]).prop('disabled', true);
    //             }
    //         })
    //     })
    // })

    // 收货地址
    let shippingAddressObj = [
        {nextNodeWrap: '#saProvinceSel_wrap',nextNodeName: 'province2',nextNodeId: 'province2'},
        {nextNodeWrap: '#sacitySel_wrap',nextNodeName: 'shippingAddressCityId',nextNodeId: 'shippingAddressCityId'},
        {nextNodeWrap: '#sadistrictSel_wrap',nextNodeName: 'shippingAddressDistrictId',nextNodeId: 'shippingAddressDistrictId'},
        {nextNodeWrap: '#sastreetSel_wrap',nextNodeName: 'shippingAddressStreetId',nextNodeId: 'shippingAddressStreetId'},
    ]
    let shippingAddressArray = [];
    utils.setAllProDom('#saProvinceSel_wrap', shippingAddressObj, '#shippingAddressBox',true, function () {
        // 收货地址有值回显
        let _registerHiddenVal = eval($('#shippingAddressJson').val());
        console.log('---收货地址有值回显---', _registerHiddenVal)
        if (!_registerHiddenVal) _registerHiddenVal = [['','','','']];
        if(_registerHiddenVal.length>0){
            _registerHiddenVal=_registerHiddenVal[0];
        }
        _registerHiddenVal.splice(_registerHiddenVal.length - 1);
        // $('#' + registerAddressSelIdObj[0]['nextNodeId']).prop('disabled', true);
        $('#' + shippingAddressObj[0]['nextNodeId']).prop('disabled', true);
        $('#' + shippingAddressObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
        for (let i = 1; i < _registerHiddenVal.length; i++) {
            shippingAddressArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
        }
        Promise.all(shippingAddressArray).then(data => {
            // console.log(data)
            for (let i = 0; i < data.length; i++) {
            $('#' + shippingAddressObj[i + 1]['nextNodeId']).html(data[i]);
            $('#' + shippingAddressObj[i + 1]['nextNodeId']).attr('data-depth',i+2);
            $('#' + shippingAddressObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
            $('#' + shippingAddressObj[i + 1]['nextNodeId']).prop('disabled', true);
             }
      })
    });


    // 发票邮寄地址
    // let billAddressSelIdObj = [];
    // let _billHiddenVal = eval($('#billingAddressJson').val());
    // if (!_billHiddenVal) _storgeHiddenVal = [['','','','']];
    // let _billHiddenValArr =  eval($('#billingAddressJson').val());
    // $(_billHiddenValArr).each((index,item) => {
    //     item.splice(item.length - 1);
    // });
    // if (!_billHiddenValArr) _storgeHiddenValArr = [['','','','']];
    // let billObj = billDistpickerHTML(_billHiddenValArr.length);
    // $(billObj.radomInit).each((index, item) => {
    //     let _arr = [
    //         {nextNodeWrap: '#billProvinceSel_wrap_' + item,nextNodeName: 'billingProvinceId_' + item,nextNodeId: 'billingProvinceId_' + item},
    //         {nextNodeWrap: '#billCitySel_wrap_' + item,nextNodeName: 'billingCityId_' + item,nextNodeId: 'billingCityId_' + item},
    //         {nextNodeWrap: '#billDistrictSel_wrap_' + item,nextNodeName: 'billingDistrictId_' + item,nextNodeId: 'billingDistrictId_' + item},
    //         {nextNodeWrap: '#billStreetSel_wrap_' + item,nextNodeName: 'billingStreetId_' + item,nextNodeId: 'billingStreetId_' + item}
    //     ]
    //     billAddressSelIdObj.push(_arr)
    // });
    // // $('#billAddress').find('.depotList').remove();
    // $('#billAddress').append(billObj.html)
    //
    // $(billObj.radomInit).each((index, item) => {
    //     let billPromiseArray = [];
    //     // for (let ind = 1; ind < _billHiddenValArr[index].length; ind++) {
    //         utils.setAllProDom('#billProvinceSel_wrap_' + billObj.radomInit[index], billAddressSelIdObj[index], '#billingBox',true, function () {
    //             $('#' + billAddressSelIdObj[index][0]['nextNodeId']).val(_billHiddenValArr[index][0]);
    //             $('#' + billAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').val(_billHiddenVal[index][4]);
    //             $('#' + billAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
    //             $('#' + billAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').prop('disabled', true);
    //             disable();
    //             for (let ind = 1; ind < _billHiddenValArr[0].length; ind++) {
    //                 billPromiseArray.push(utils.setAddressReturnVal(_billHiddenValArr[index][ind-1]));
    //             }
    //             let allSelArr = billAddressSelIdObj[index].flat().map((item, index) => {
    //                 if (index % 4 != 0) {
    //                     return item['nextNodeId']
    //                 }
    //             }).filter(item => {
    //                 return item
    //             });
    //             Promise.all(billPromiseArray).then(data => {
    //                 for (let i = 0; i < data.length; i++) {
    //                     $('#' + allSelArr[i]).html(data[i]);
    //                     $('#' + allSelArr[i]).attr('data-depth',i+2);
    //                     $('#' + allSelArr[i]).val(_billHiddenValArr[index][i + 1]);
    //                     $('#' + allSelArr[i]).prop('disabled', true);
    //                 }
    //             })
    //         })
    //     // }
    // })
    //修改记录申请明细展开收起按钮
    $("#modifyTable").on("click",".moreBtn",function(){
        var type=$.trim($(this).text());
        var tr=$(this).parents("tr");
        var innerHeiht=0;
        if(type == '展开')
        {
            innerHeiht=tr.find(".mrLeft").innerHeight();
            $(this).html('收起');
        }else if(type == '收起'){
            innerHeiht = 40;
            $(this).html('展开');
        }
        if(innerHeiht < 40){
            innerHeiht=40;
        }
        tr.find(".modifyTheRecord .itemContent").animate({
            height:innerHeiht
        },500)
    });

});
//获取仓库地址
// function distpickerHTML(n) {
//     var len = n ? n : 1;
//     var html = '';
//     //2018.9.4,RL,下面html中新增了非空校验,bug2386
//     let radomInit = [];
//     for (let i = 0; i < len; i++) {
//         let _int = parseInt(Math.random() * 100 + Math.random() * 1000);
//         html += `<div class="col-md-6 depotList">
// 	        <div class="input-group">
// 	            <div class="input-group-addon require"><i class="text-require">*  </i>仓库地址</div>
// 	            <div class="form-control form-inline distpicker" id="storageBox_${_int}">
// 	                <div class="row">
// 	                    <div class="form-group col-md-2" id="stoProvinceSel_wrap_${_int}">
// 	                        <select class="form-control repertoryProvinceSelect" name="repertoryProvince_${_int}" id="repertoryProvince_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="stoCitySel_wrap_${_int}">
// 	                        <select class="form-control repertoryCitySelect" name="repertoryCity_${_int}" id="repertoryCity_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="stoDistrictSel_wrap_${_int}">
// 	                        <select class="form-control repertoryAreaSelect" name="repertoryArea_${_int}" id="repertoryArea_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="stoStreetSel_wrap_${_int}">
// 	                        <select class="form-control repertoryAreaSelect" name="repertoryStreet_${_int}" id="repertoryStreet_${_int}"></select>
//                         </div>
// 	                    <div class="form-group col-md-4" style="position: initial;">
// 	                        <input type="text" class="form-control text-inp" name="repertoryDetail"/>
// 	                    </div>
// 	                </div>
// 	            </div>
// 	        </div>
// 	    </div>`;
//         radomInit.push(_int);
//     }
//     return {
//         html: html,
//         radomInit: radomInit
//     };
// }
// //获取发票邮寄地址
// function billDistpickerHTML(n) {
//     var len = n ? n : 1;
//     var html = '';
//     let radomInit = [];
//     for (let i = 0; i < len; i++) {
//         let _int = parseInt(Math.random() * 100 + Math.random() * 1000);
//         html += `<div class="col-md-6 depotList">
// 	        <div class="input-group">
// 	            <div class="input-group-addon require">发票邮寄地址</div>
// 	            <div class="form-control form-inline distpicker" id="billingBox_${_int}">
// 	                <div class="row">
// 	                    <div class="form-group col-md-2" id="billProvinceSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingProvinceId_${_int}" id="billingProvinceId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billCitySel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingCityId_${_int}" id="billingCityId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billDistrictSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingDistrictId_${_int}" id="billingDistrictId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billStreetSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingStreetId_${_int}" id="billingStreetId_${_int}"></select>
//                         </div>
// 	                    <div class="form-group col-md-4" style="position: initial;">
// 	                        <input type="text" class="form-control {validate:{ required :true}} text-inp" name="billingAddress" value=""/>
// 	                    </div>
// 	                </div>
// 	            </div>
// 	        </div>
// 	    </div>`;
//         radomInit.push(_int);
//     }
//     return {
//         html: html,
//         radomInit: radomInit
//     };
// }
// 全量数据不可编辑
function disable(){
    $("input[type='text']").attr("disabled","disabled");
    $("input[type='tel']").attr("disabled","disabled");
    $("input[type='number']").attr("disabled","disabled");
    $("input[type='checkbox']").attr("disabled","disabled");
    $("input[type='radio']").attr("disabled","disabled");
    $("textarea").attr("disabled","disabled");
    $("select").attr("disabled","disabled");
    $(".addDepot").hide();
    $(".removeDepot").hide();
    $(".rowBtn").attr("disabled","disabled");
}
/**
 * 修改记录查看流程图
 * @returns
 */
function toFlowChart(processInstaId) {
    if("null" != processInstaId  && "" != processInstaId){
        dialog({
            url: '/proxy-customer/customer/customerBaseAppl/customerChangeRecord/toFlowChart',
            title: '审批流程',
            width: 1200,
            height: 520,
            data: {"processInstaId":processInstaId},
            onclose: function () {
            },
            oniframeload: function () {
            }
        }).showModal();
        return false;
    }

}

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }

/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj, tableId) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(tableId).getRowData(parentId);
    if(data.customerEnclosureVoList)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            list:JSON.parse(data.customerEnclosureVoList)
        })
    }
}

/**
 * 加载所属经营范围对应的批准文件
 * @param simpleCode
 */
function localBatchName(simpleCode) {
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/queryClient",
        async : false,
        data:{"type":4},
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        if(simpleCode == arr[i].clientId){
                            html+='<option value="'+arr[i].clientId+'" selected="selected">'+arr[i].clientName+'</option>';
                        }else{
                            html+='<option value="'+arr[i].clientId+'">'+arr[i].clientName+'</option>';
                        }
                    }
                }
            }
            $("select[name='mandatorType']").html(html);
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

// 非独立经营证明 ，点击图片 图标，
// $('body').on('click','.fileIcon_p',function (e) {
//     let all_fileIcon_p = $('.fileIcon_p');
//     let thisIndex = $(this).parents('.fileIcon_div').attr('data-index');
//     let that = $(this);
//     $(all_fileIcon_p).each(function (index,item) {
//         if(thisIndex == index){
//             if(that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display') != 'none'){
//                 that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display','none')
//             }else{
//                 that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display','block')
//                 that.parents('.fileIcon_div').siblings().find('.fileIcon_showTag').css('display','none')
//             }
//             return false;
//         }else{
//             if(that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display') != 'none'){
//                 that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display','none')
//             }else{
//                 that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display','block')
//                 that.parents('.fileIcon_div').siblings().find('.fileIcon_showTag').css('display','none')
//             }
//             return false;
//         }
//     })
//     let pageType = $('[name=tag_forPage]').val();
//     if(pageType == 'tag_edit_page' || !$('.btn_customerFile').prop('disabled')){ // 编辑页面
//         $('.btn_fileClean').prop('disabled', false)
//     }else{
//         $('.btn_fileClean').prop('disabled', true)
//     }
// })

// 非独立经营证明 ，点击图片 图标，
$('body').on('click','.fileIcon_p',function (e) {
    let all_fileIcon_p = $('.fileIcon_p');
    let thisIndex = $(this).parents('.fileIcon_div').attr('data-index');
    let that = $(this);
    $(all_fileIcon_p).each(function (index,item) {
        if(thisIndex == index){
            if(that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display') != 'none'){
                that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display','none')
            }else{
                that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display','block')
                that.parents('.fileIcon_div').siblings().find('.fileIcon_showTag').css('display','none')
            }
            return false;
        }else{
            if(that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display') != 'none'){
                that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display','none')
            }else{
                that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display','block')
                that.parents('.fileIcon_div').siblings().find('.fileIcon_showTag').css('display','none')
            }
            return false;
        }
    })
    let pageType = $('[name=tag_forPage]').val();
    if(pageType == 'tag_edit_page' || !$('.btn_customerFile').prop('disabled')){ // 编辑页面
        $('.btn_fileClean').prop('disabled', false)
    }else{
        $('.btn_fileClean').prop('disabled', true)
    }
})
// 非独立经营证明  预览
function btn_fileView(that) {
    let urlList = [$(that).parents('.fileIcon_div').find('.fileIcon_p').attr('data-imgUrl')];
    $.viewImg({
        fileParam:{
            name:'fileName',
            url:'url'
        },
        list:urlList
    })
}
// 非独立经营证明 清空
function btn_fileClean(that) {
    $(that).parents('.fileIcon_div').remove();
}

//批准文件,质量保障协议,客户委托书,被委托人 一键下载
function patchDownload(tableId){
    downloadTableAttachFiles(tableId)
}
function downloadTableAttachFiles(tableId){
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$(tableId);
    var rowData=$table.getRowData();
    console.log(rowData)
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++)
    {
        var sel=$tr.eq(i).find("select[name='credentialTypeId'] option:selected");
        typeList.push({
            text:sel.text(),
            value:sel.val()
        });
        console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0)
        {
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
            {
                rowData[i].customerEnclosureVoList[j].type=sel.val();
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    console.log(eChoImgList)
    // downloadImg("/static/MobileToken.png") //同源图片，下载有效
    const fileUrls = []
    const fileNames = []
    eChoImgList.forEach((item,index)=>{
        if (item.url && item.url.length) {
            fileUrls.push(item.url)
            let fileName = item.enclosureName
            if (!fileName){
                fileName = index + ''
            }
            fileNames.push(fileName)
        }
    })
    downloadImg(fileUrls,fileNames)
}
//客户合同 一键下载
// TODO 待验证
function qtfjPatchDownload() {
    let eChoImgList = []
    var checkInp=$("#contractType input[type='checkbox']:checked");
    if(checkInp.length)
    {
        for(var i=0;i<checkInp.length;i++)
        {
            var type=checkInp.eq(i).val();
            var inp=$("#contractType"+type);
            if(inp.length)
            {
                var imgArr=JSON.parse(inp.val());
                eChoImgList=eChoImgList.concat(imgArr);
            }
        }
    }
    const fileUrls = []
    const fileNames = []
    eChoImgList.forEach((item,index)=>{
        if (item.url && item.url.length) {
            fileUrls.push(item.url)
            let fileName = item.enclosureName
            if (!fileName){
                fileName = index + ''
            }
            fileNames.push(fileName)
        }
    })
    downloadImg(fileUrls,fileNames)
}
function downloadImg(fileUrls,fileNames){
    if (!(fileUrls.length * fileNames.length)){
        utils.dialog({content: '暂无附件下载', quickClose: true, timeout: 2000}).showModal();
        return
    }
    window.open("/proxy-customer/customer/customerFirstAppl/downloadZip?fileUrls="+fileUrls+"&fileNames="+fileNames+"&zipFileName=导出")
}
