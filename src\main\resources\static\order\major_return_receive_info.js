$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);

    /* 合计 计算 */
    var totalTable = z_utils.totalTable;

    var colName = [ '验收结果','退货原因', '商品编码','原商品编码','商品大类', '商品名称','通用名', '商品规格', '生产厂家', '产地', '单位', '库房名称', '批号', '生产日期', '有效期至', '本次退货数量', '实际退货数量',
        '含税单价', '价税合计', '实付金额', '活动优惠金额', '余额抵扣优惠金额','退回返利金额', '税率', '税额','id'
    ];
    var colModel = [
        {   name: 'checkResult',
            index: '#checkResult',
            rowtype: '#checkResultSel'
        },
        { name: 'returnReason',index: 'returnReason',formatter:function (e) {

        if(e==1){
            return '整单拒收';
        }else if(e==2){
            return '药品漏发';
        }else if(e==3){
            return '药品错发';
        }else if(e==4){
            return '药品破损';
        }else if(e==5){
            return '效期不好';
        }else if(e==6){
            return '批号不符';
        } else if(e==7){
            return '税票有误';
        }else if(e==8){
            return '无检验报告单';
        }else if(e==9){
            return '采购价偏高';
        } else if(e==10){
            return '实物与图片不符';
        }else if(e==11){
            return '采购单重复';
        }else if(e==12){
            return '商品其他质量问题';
        }else if(e==13){
            return '拦截';
        }else if(e==14){
            return '缺货未发';
        }else if(e==15){
            return '召回';
        }else if(e==16){
            return '水剂不发货';
        }else {
            return e;
        }
    } },
        { name: 'productCode', index: 'productCode' },
        {name: 'oldProductCode', index: 'oldProductCode'},
        { name: 'drugClass', index: 'drugClass' },
        { name: 'productName', index: 'productName'},
        { name: 'commonName', index: 'commonName'},
        { name: 'specifications',index: 'specifications'},
        { name: 'manufacturer', index: 'manufacturer'},
        { name: 'productOrigin', index: 'productOrigin'},
        { name: 'productUnit',index: 'productUnit'},
        { name: 'warehouseName',index: 'warehouseName',formatter:function (e) {
            if(e==1){
                return '合格库';
            }else if(e==2){
                return '不合格库';
            }else if(e==3){
                return '暂存库';
            }else if(e==10){
                return '临时库';
            }
        } },
        { name: 'batchCode', index: 'batchCode' },
        { name: 'productionTime', index: 'productionTime',formatter:dateFormatter},
        { name: 'periodValidity',index: 'periodValidity',formatter:dateFormatter},
        { name: 'returnsNumber',index: 'returnsNumber'},
        { name: 'actualReturnNumber',index: 'actualReturnNumber'},
        { name: 'taxPrice',index: 'taxPrice',
            formatter:function(e){
                return Number(e).toFixed(2);
            }
        },
        { name: 'taxAmount',    index: 'taxAmount'  ,
            formatter:function(e){
                return Number(e).toFixed(2);
            }},
        { name: 'paymentAmount',    index: 'paymentAmount'  ,
            formatter:function(e){
                return Number(e).toFixed(2);}},
        { name: 'activityDiscountAmount',    index: 'activityDiscountAmount'  ,
            formatter:function(e){
                return Number(e).toFixed(2);
            }},
        { name: 'balanceDiscountAmount',    index: 'balanceDiscountAmount' ,
            formatter:function(e){
                return Number(e).toFixed(2);
            } },
        {      name: 'amounOfRebate',      index: 'amounOfRebate',
            formatter:function(e){
                return Number(e).toFixed(2);
            }
        },
        { name: 'rate',    index: 'rate',
            formatter: function (e) {
                if ( e != undefined && e != null ) {
                    return e +'%';
                } else {
                    return '0%'
                }
            } },
        { name: 'tax',    index: 'tax' ,
            formatter:function(e){
                return Number(e).toFixed(2);}},

        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            hidden: true,
            hidegrid: true

        }
        ];

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
    }

    $('#table_info').XGrid({
        url:'/proxy-order/order/orderReturnCheck/majorCusOrderReturnCheckController/getDetailList?salesReturnCode=' + $("#salesReturnCode").val(),
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'id',
        rowNum: 100,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        //pager: '#grid_pager_a',
        attachRow:true,
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            /* 双击单据，进入单据详情页面。若单据状态为“待处理”，双击单据跳转至编辑销售退回单页面； */
            window.location = 'major_return_receive_info.html';
            },
        gridComplete: function () {
            setTimeout(function (param) {
                /* 合计写入 */
                var data = $('#table_info').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                console.log('验收明细', data);
                $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                $(sum_ele[1]).text(totalTable(data, 'tax'));
                $(sum_ele[2]).text(totalTable(data, 'paymentAmount'));
            }, 200)
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log("单击行事件",id, dom, obj, index, event)
        }
    });

    /* 筛选列 */
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })

    /* 返回 */
    $('#goback').on('click', function () {
        utils.closeTab();
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();
        return temp;
    }

    //统一验收确认
    $('#set_all_check').on('click', function () {
        var selData = $('#table_info').XGrid("getSeleRow");
        console.log("selData",selData);
        if(!selData.length){
            utils.dialog({title: '提示',content:'请至少选择一行！',timeout:3000}).show();
            return false;
        }
        utils.dialog({
            title: '入库验收确认',
            content: $('#checkerModal'),
            width: 400,
            height: 100,
            okValue: '确认',
            ok: function () {
                console.log(this.node);
                var formdata = $(this.node).find('form').serializeToJSON();
                var selRow =  $('#table_info').XGrid('getSeleRow');
                if(selRow.length){
                    $.each(selRow, function (index, item) {
                        /*item.receiveState = formdata.receiveState;
                        item.receiveTime = formdata.receiveTime;*/
                        // $('#table_info').XGrid('setRowData',item.id, {returnReason:'4'});

                        $('#table_info').XGrid('setRowData',item.id,{checkResult:formdata.checkResult});
                    })
                }
            }
            ,
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal();
    })
})
// 完成验收
function btn_gocheck() {
    let allRowdata = $('#table_info').XGrid('getRowData');
    let bool = allRowdata.some( item => {
        return item.checkResult == 0;
    });
    if(bool){
        utils.dialog({
            title: '提示',
            content: '尚有未录入验收结果的单据，不能完成验收!',
            okValue: '确定',
            ok: function () {}
        }).showModal();
        return false
    }else{
        //ajax
        console.log('pass')

        var arr = [];
        utils.dialog({
            title: '温馨提示',
            content: '保存成功后该页面将关闭，是否确认保存？',
            okValue: '确定',
            ok: function () {
                //下面保存
                var formData = $('#form_a').serializeToJSON();
                $.ajax({
                    url: "/proxy-order/order/orderReturnCheck/majorCusOrderReturnCheckController/toCheckMajorCurReturnOrder",
                    type:'post',
                    dataType:'json',
                    data: {formData:JSON.stringify(formData), rowdata:JSON.stringify(allRowdata)},
                    beforeSend: function(){
                        parent.showLoading({hideTime: 60000})
                    },
                    success: function(res){
                        if(res){
                            if(res.code === 0){
                               /* utils.dialog({content: '验收成功！', quickClose: true, timeout: 1000}).show();
                                setTimeout(function () {
                                    utils.closeTab();
                                },1000);*/
                                utils.dialog({
                                    title: '温馨提示',
                                    content:  '验收成功！',
                                    okValue: '确定',
                                    ok: function () {
                                        utils.closeTab();
                                    }
                                }).showModal()
                            }else{
                                utils.dialog({
                                    title: '温馨提示',
                                    content: res.msg,
                                    okValue: '确定',
                                    ok: function () {
                                        utils.closeTab();
                                    }
                                }).showModal()
                            }
                        }
                    },
                    complete: function () {
                        parent.hideLoading()
                    }
                });
            }
        }).showModal()

    }
}