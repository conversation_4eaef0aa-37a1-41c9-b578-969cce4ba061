$(function () {

	var totalTable = z_utils.totalTable;
	
    var url = '/proxy-finance/finance/initBalance/findFinanceStockInitialBalance';
    var sysOrgCode = $("#sysOrgCode").val();

    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: url,
        colNames: ['初始期间', '库房', '商品大类编码', '商品大类名称', '商品编码', '商品名称', '数量', '单价', '结存金额'
        ],
        colModel: [{
            name: 'createTime',
            formatter: function (value) {
                if (value != null) {
                    return moment(value).format("YYYY-MM-DD ");
                } else {
                    return '';
                }

            }
        }, {
            name: 'storageTypeName'
        }, {
            name: 'largeCategory'
        }, {
            name: 'largeCategoryVal'
        }, {
            name: 'productCode'
        }, {
            name: 'name'
        }, {
            name: 'quantity'
        }, {
            name: 'costPrice',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        }, {
            name: 'costSum',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        }],
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        rownumbers: true,
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            var data = $(this).XGrid("getRowData");
            if (!data.length) {
                utils.dialog({
                    content: "查询无数据",
                    quickClose: true,
                    timeout: 2000
                }).show();
            }
        },
        gridComplete: function () {
             //合计行 
            var data = $(this).XGrid('getRowData');
            var sum_models = ['costSum'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        },
        pager: '#grid-pager'
    });

    //统计方法
    totalSum();

    // 查询数据
    $('#searchBtn').bind('click', function () {

        var param = $('#myform').serializeToJSON();
        var storageTypeLists = $("#storageTypeLists").val();
        if (!$.isArray(storageTypeLists) && storageTypeLists != undefined) {
            var tem = [];
            tem.push(storageTypeLists);
            storageTypeLists = tem;
        }
        console.log(storageTypeLists);
        console.log(JSON.stringify(storageTypeLists));
        //param.storageTypeLists = JSON.stringify(storageTypeLists);
        storageTypeLists = JSON.stringify(storageTypeLists);
        $('#X_Table').setGridParam({
            url: url,
            postData: {
                "storageTypeLists": storageTypeLists,
                "productCode": $("#drugCode").val(),
                "oldProductCode": $("#oldProductCode").val()
            }
        }).trigger('reloadGrid');
        //统计方法
        totalSum();
    })

    // 导出
    $('#exportBtn').bind('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            parent.showLoading({hideTime: 999999999});
            var storageTypeLists = $("#storageTypeLists").val();
            if (!$.isArray(storageTypeLists) && storageTypeLists != undefined) {
                var tem = [];
                tem.push(storageTypeLists);
                storageTypeLists = tem;
            }
            storageTypeLists = JSON.stringify(storageTypeLists);
            console.log(storageTypeLists);
            var productCode = $("#drugCode").val();
            var oldProductCode = $("#oldProductCode").val();
             parent.showLoading({hideTime: 999999999});
             var obj = {
                storageTypeLists: storageTypeLists,
                productCode: productCode,
                oldProductCode: oldProductCode
             };
            httpPost("/proxy-finance/finance/initBalance/exportExcelInitList", obj);
            parent.hideLoading();
            //验证导出数据
            // $.ajax({
            //     url: "/proxy-finance/finance/initBalance/exportExcelInitList",
            //     data: {
            //         storageTypeLists: storageTypeLists,
            //         productCode: productCode,
            //         oldProductCode: oldProductCode
            //     },
            //     type: "post",
            //     success: function (result) {
            //         //校验成功
            //         if (result.code == 0) {
            //             console.log(result.result);
            //             var url = result.result.filePath;
            //             var extfilename = result.result.extfilename;
            //             var parames = [];
            //             parames.push({name: "filePath", value: url});
            //             parames.push({name: "extfilename", value: extfilename});
            //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
            //             parent.hideLoading();
            //         } else {
            //             parent.hideLoading();
            //             utils.dialog({
            //                 content: result.msg, quickClose: true,
            //                 timeout: 3000
            //             }).showModal();
            //         }
            //     }
            // })
        })
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    // 导出
    // $('#exportBtn').bind('click', function () {
    //     var storageTypeLists = $("#storageTypeLists").val();
    //     if (!$.isArray(storageTypeLists) && storageTypeLists != undefined) {
    //         var tem = [];
    //         tem.push(storageTypeLists);
    //         storageTypeLists = tem;
    //     }
    //     storageTypeLists = JSON.stringify(storageTypeLists);
    //     console.log(storageTypeLists);
    //     var productCode = $("#drugCode").val();
    //     var parames = [];
    //     parames.push({name: "storageTypeLists", value: storageTypeLists});
    //     parames.push({name: "productCode", value: productCode});
    //     Post("/proxy-finance/finance/initBalance/exportExcelInitList", parames);
    // });


    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }


    //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    //商品名称 双击查询
    $('#productDesc').dblclick(function () {
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#productDesc').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#productDesc').val(data.productName);
                    $('#drugCode').val(data.productCode)
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    })

    //商品名称 搜索
    $('#productDesc').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + sysOrgCode, //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data);

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            // console.log(query, suggestions);

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#productDesc").val("");
        }
    });

    //统计方法
    function totalSum() {
        var storageTypeLists = $("#storageTypeLists").val();
        if(!$.isArray (storageTypeLists) && storageTypeLists != undefined){
            var tem = [];
            tem.push(storageTypeLists);
            storageTypeLists = tem;
        }
        storageTypeLists = JSON.stringify(storageTypeLists);
        console.log(storageTypeLists);
        var productCode = $("#drugCode").val();
        var oldProductCode = $("#oldProductCode").val();

        $.ajax({
            url:'/proxy-finance/finance/initBalance/totalSumStorageGoodsInit',
            type:'post',
            data:{
                storageTypeLists : storageTypeLists,
                productCode : productCode,
                oldProductCode : oldProductCode
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;
                    var totalCostSum = data.totalCostSum;
                    if(totalCostSum){
                        $("#totalCostSum").text(parseFloat(totalCostSum).formatMoney('2', '', ',', '.'));
                    }else{
                        $("#totalCostSum").text(0);
                    }

                }
            }
        })
    }


})