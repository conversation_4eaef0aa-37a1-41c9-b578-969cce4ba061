// 去掉所有input的autocomplete, 显示指定的除外

$(function () {
    $('.fold-block[fold=sub]').fold();

    var colNames = ['id','采购订单号','采购单据号','业务类型','采购单据状态', '供应商编码', '供应商', '移动类型', '制单人', '单据日期', '金额合计', '税额合计', '税率', '价税合计','未开票金额','移动类型code'],
        colModel = [
            {
                name: 'id',
                hidden:true,
                hidegrid: true
            },
            {
                name: 'orderNo'
            },
            {
                name: 'stockOrderNo'
            }, {
                name: 'channelId'
            },{
                name: 'ticketStatus',
                formatter:function(value){
                    if(value != ''){
                        if(value == '0' || value == null){
                            return '未开发票';
                        }else if(value == '1'){
                            return '部分开票';
                        }else if(value == '2'){
                            return '已开发票';
                        }else if(value == '3'){
                            return '不需开票';
                        }else{
                            return '';
                        }
                    }else{
                        return '';
                    }
                }
            }, {
                name: 'supplierCode'
            }, {
                name: 'supplierName'
            },{
                name: 'moveTypeName'
            },{
                name: 'createUser'
            },{
                name: 'storeTime'
            }, {
                name: 'sumNoTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }

            }, {
                name: 'sumTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'productEntryTax'
            }, {
                name: 'priceTaxSum',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'noTicketMoney',
                formatter: function(val,a,b,c){
                    if(!val){
                        return '0.00'
                    }else if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')) {
                        return '-' + parseFloat(val).formatMoney('2', '', ',', '.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }

                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }

            }, {
                name: 'moveTypeCode',
                hidden: true,
                hidegrid: true
            }, {
                name: 'channelId',
                hidden: true,
                hidegrid: true
            }];
    var totalTable = z_utils.totalTable;
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/purchase/AccountPayableReports/getInvoiceStoreInReportData',
        postData: {
            "starttime": $("#starttime").val(),
            "endtime": $("#endtime").val(),
            "moveTypeCode": $("#moveTypeCode").val(),
            "creStartTime": $("#creStartTime").val(),
            "creEndTime": $("#creEndTime").val()
        },
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,//是否展示序号
        altRows: true, //设置为交替行表格,默认为false
        mutltiselect: true,
        attachRow:true,
        maxheight: false,
        ondblClickRow: function (rowid, usedata) {
            var rowData = $("#X_Table").XGrid('getRowData', rowid);
            initTableDialog(rowData.moveTypeCode,rowData.stockOrderNo);
            utils.dialog({
                title: '采购单据明细行查询结果',
                content: $('#modal1'),
                width: $(window).width()*0.8

            }).showModal();
        },
        onSelectRow: function (e, c, a, b) {
            //console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            var data = $(this).XGrid("getRowData");
            /* 合计行 */
            var sum_models = ['sumNoTaxMoney','sumTaxMoney','priceTaxSum','noTicketMoney'];
            /* 合计行 */
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                // lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        },
        pager: '#grid-pager'
    });





    //入库单明细
    function initTableDialog(moveTypeCode,stockOrderNo) {
        $('#X_Table1').XGrid({

            url: '/proxy-finance/finance/purchase/invoice/findStoreInDetail',
            postData:{stockOrderNo:stockOrderNo,
                moveTypeCode:moveTypeCode},
            colNames: ['id','采购订单号','订单数量','采购单据号','业务类型','单据日期', '采购单据行号','采购单据行状态', '商品编码', '商品名称','通用名','规格','供应商','生产厂家', '小包装单位', '商品二级分类','含税单价',
                '小包装数量', '已开票数量' , '已开票金额', '未开票数量', '未开票金额',
                '金额合计', '税额合计', '价税合计','补价说明'
            ],
            colModel: [{
                name: 'id',
                hidden: true,
                hidegrid: true
            }, {
                name: 'orderNo'
            },{
                name: 'orderCount'
            },{
                name: 'stockOrderNo'
            }, {
                name: 'channelId'
            },{
                name: 'billTime'
            }, {
                name: 'billsSort'
            }, {
                name: 'ticketStatus',
                formatter:function(value){
                    if(value != ''){
                        if(value == '0'){
                            return '未开发票';
                        }else if(value == '1'){
                            return '部分开票';
                        }else if(value == '2'){
                            return '已开发票';
                        }else if(value == '3'){
                            return '不需开票';
                        }else{
                            return '';
                        }
                    }else{
                        return '';
                    }
                }
            },{
                name: 'productCode'
            },{
                name: 'productName'
            },{
                name: 'commonName'
            },{
                name: 'productSpecification'
            },{
                name: 'supplierName'
            },{
                name: 'productProduceFactory'
            },{
                name: 'productPackUnitSmall'
            },{
                name: 'secondCategory'
            },  {
                name: 'productContainTaxPrice',
                formatter: function (val) {
                    return parseFloat(val);
                }
            }, {
                name: 'productPackInStoreCount',
                formatter: function(val,a,b,c){
                    if(val == ''){
                        return '0';
                    }else if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return parseFloat(val);
                    }else{
                        return parseFloat(val);
                    }
                }
            }, {
                name: 'outInvoiceCount'
            }, {
                name: 'outInvoiceMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && val>0 && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                }
            }, {
                name: 'notInvoiceCount'
            }, {
                name: 'notInvoiceMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && val>0 && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                }
            }, {
                name: 'newProcutNoTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'newProductTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'newProductContainTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            },{
                name: 'refundPriceExplanation'
            }, {
                name: 'moveTypeCode',
                hidden: true,
                hidegrid: true
            }, {
                name: 'channelId',
                hidden: true,
                hidegrid: true
            }],
            // rowNum: 10,
            //multiselect: true,
            rownumbers: true,
            altRows: true, //设置为交替行表格,默认为false
            gridComplete: function () {
                var data = $(this).XGrid("getRowData");
                if(data == null || data.length == 0){
                    utils.dialog({
                        content:"查询无数据",
                        quickClose: true,
                        timeout: 3000
                    }).showModal();
                }
            },
            pager: '#grid-pager1',
        });
    }








    // 查询数据
    $('#searchBtn').bind('click', function () {
        var moveTypeCode = $("#moveTypeCode").val();
        var ticketStatus = $("#ticketStatus").val();
        var startTime = null;
        var endTime = null;
        var creStartTime = null;
        var creEndTime = null;
        var creatTimeCheckbox = $("#creatTimeCheckbox").prop('checked');
        if (!creatTimeCheckbox) {
            if (! $("#creStartTime").val() || !$("#creEndTime").val()) {
                utils.dialog({
                    content: "请选择数据生成时间", quickClose: true,
                    timeout: 2000
                }).showModal();
                return;
            }
            creStartTime = $("#creStartTime").val();
            creEndTime = $("#creEndTime").val();
        }else {
            if (! $("#starttime").val() || ! $("#endtime").val()) {
                utils.dialog({
                    content: "请选择单据时间", quickClose: true,
                    timeout: 2000
                }).showModal();
                return;
            }
            startTime = $("#starttime").val();
            endTime = $("#endtime").val();
        }
        var buyer = $("#buyer").val();
        var channel = $("#channelId").val();
        if(moveTypeCode.length == 0 || moveTypeCode.length == 4){
            moveTypeCode = ['-1']
        }
        if(ticketStatus == -1){
            ticketStatus = null;
        }
        if(buyer == -1){
            buyer = null;
        }
        if(channel == -1){
            channel = null;
        }
        //console.log(moveTypeCode);
        //console.log(JSON.stringify(moveTypeCode));
        //console.log(ticketStatus);
        // parent.showLoading({hideTime: *********});
        $('#X_Table').setGridParam({
            url: '/proxy-finance/finance/purchase/AccountPayableReports/getInvoiceStoreInReportData',
            postData: {
                "keyWord": $("#keyWord").val(),
                "starttime":startTime,
                "endtime":endTime,
                "creStartTime": creStartTime,
                "creEndTime": creEndTime,
                "ticketStatus": ticketStatus,
                "buyer": buyer,
                "moveTypeCode": JSON.stringify(moveTypeCode),
                "stockOrderNo": $("#stockOrderNo").val(),
                "channel":channel
            }
        }).trigger('reloadGrid');
    })

    // 导出明细
    $('#exportBtn').bind('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            parent.showLoading({hideTime: *********});
            var moveTypeCode = $("#moveTypeCode").val();
            var ticketStatus = $("#ticketStatus").val();
            var buyer = $("#buyer").val();
            var channel = $("#channelId").val();
            var startTime = null;
            var endTime = null;
            var creStartTime = null;
            var creEndTime = null;
            var creatTimeCheckbox = $("#creatTimeCheckbox").prop('checked');
            if (!creatTimeCheckbox) {
                if (!$("#creStartTime").val() || !$("#creEndTime").val()) {
                    utils.dialog({
                        content: "请选择数据生成时间", quickClose: true,
                        timeout: 2000
                    }).showModal();
                    return;
                }
                creStartTime = $("#creStartTime").val();
                creEndTime = $("#creEndTime").val();
            }else {
                if (!$("#starttime").val() || ! $("#endtime").val()) {
                    utils.dialog({
                        content: "请选择单据时间", quickClose: true,
                        timeout: 2000
                    }).showModal();
                    return;
                }
                startTime = $("#starttime").val();
                endTime = $("#endtime").val();
            }
            if(moveTypeCode.length == 0){
                moveTypeCode = ['-1']
            }
            if(ticketStatus == -1){
                ticketStatus = null;
            }
            if(buyer == -1){
                buyer = null;
            }
            if(channel == -1){
                channel = null;
            }
            var obj = {
                keyWord : $("#keyWord").val(),
                starttime : startTime,
                endtime : endTime,
                creStartTime: creStartTime,
                creEndTime: creEndTime,
                ticketStatus : ticketStatus,
                buyer : buyer,
                channelId : channel,
                moveTypeCode : JSON.stringify(moveTypeCode),
                stockOrderNo : $("#stockOrderNo").val()
            };
            httpPost("/proxy-finance/finance/purchase/AccountPayableReports/exportInvoiceStoreInDetailReport", obj);
            parent.hideLoading();

            //console.log(moveTypeCode);
            //console.log(ticketStatus);
            //console.log(buyer);
            // //验证导出数据
            // $.ajax({
            //     url : "/proxy-finance/finance/purchase/AccountPayableReports/exportInvoiceStoreInDetailReport",
            //     data:{ keyWord : $("#keyWord").val(),
            //         starttime : $("#starttime").val(),
            //         endtime : $("#endtime").val(),
            //         ticketStatus : ticketStatus,
            //         buyer : buyer,
            //         channelId : channel,
            //         moveTypeCode : JSON.stringify(moveTypeCode),
            //         stockOrderNo : $("#stockOrderNo").val()},
            //     type: "post",
            //     success:function(result){
            //         //校验成功
            //         if(result.code==0){
            //             console.log(result.result);
            //             var url = result.result.filePath;
            //             var extfilename = result.result.extfilename;
            //             var parames = [];
            //             parames.push({ name: "filePath", value: url});
            //             parames.push({ name: "extfilename", value: extfilename});
            //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
            //             parent.hideLoading();
            //         }else{
            //             parent.hideLoading();
            //             utils.dialog({content:result.msg, quickClose: true,
            //                 timeout: 3000}).showModal();
            //
            //         }
            //     }
            // })
        })
    });

    // 导出单据
    $('#exportStoreInBtn').bind('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            parent.showLoading({hideTime: *********});
            var moveTypeCode = $("#moveTypeCode").val();
            var ticketStatus = $("#ticketStatus").val();
            var buyer = $("#buyer").val();
            var channel = $("#channelId").val();
            if(moveTypeCode.length == 0){
                moveTypeCode = ['-1']
            }
            if(ticketStatus == -1){
                ticketStatus = null;
            }
            if(buyer == -1){
                buyer = null;
            }
            if(channel == -1){
                channel = null;
            }

            var startTime = null;
            var endTime = null;
            var creStartTime = null;
            var creEndTime = null;
            var creatTimeCheckbox = $("#creatTimeCheckbox").prop('checked');

            if (!creatTimeCheckbox) {
                if (!$("#creStartTime").val() || !$("#creEndTime").val()) {
                    utils.dialog({
                        content: "请选择数据生成时间", quickClose: true,
                        timeout: 2000
                    }).showModal();
                    return;
                }
                creStartTime = $("#creStartTime").val();
                creEndTime = $("#creEndTime").val();
            }else {
                if (!$("#starttime").val() || !$("#endtime").val()) {
                    utils.dialog({
                        content: "请选择单据时间", quickClose: true,
                        timeout: 2000
                    }).showModal();
                    return;
                }
                startTime = $("#starttime").val();
                endTime = $("#endtime").val();
            }
            var obj = {
                "keyWord": $("#keyWord").val(),
                "starttime": startTime,
                "endtime": endTime,
                "creStartTime": creStartTime,
                "creEndTime": creEndTime,
                "ticketStatus": ticketStatus,
                "buyer": buyer,
                "channelId" : channel,
                "moveTypeCode": JSON.stringify(moveTypeCode),
                "stockOrderNo": $("#stockOrderNo").val()
            }
            httpPost("/proxy-finance/finance/purchase/AccountPayableReports/exportInvoiceStoreInReport", obj);
            parent.hideLoading();

            //console.log(moveTypeCode);
            //console.log(ticketStatus);
            //console.log(buyer);
            // //验证导出数据
            // $.ajax({
            //     url : "/proxy-finance/finance/purchase/AccountPayableReports/exportInvoiceStoreInReport",
            //     data:{
            //         "keyWord": $("#keyWord").val(),
            //         "starttime": $("#starttime").val(),
            //         "endtime": $("#endtime").val(),
            //         "ticketStatus": ticketStatus,
            //         "buyer": buyer,
            //         "channelId" : channel,
            //         "moveTypeCode": JSON.stringify(moveTypeCode),
            //         "stockOrderNo": $("#stockOrderNo").val()
            //     },
            //     type: "post",
            //     success:function(result){
            //         //校验成功
            //         if(result.code==0){
            //             console.log(result.result);
            //             var url = result.result.filePath;
            //             var extfilename = result.result.extfilename;
            //             var parames = [];
            //             parames.push({ name: "filePath", value: url});
            //             parames.push({ name: "extfilename", value: extfilename});
            //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
            //             parent.hideLoading();
            //         }else{
            //             parent.hideLoading();
            //             utils.dialog({content:result.msg, quickClose: true,
            //                 timeout: 3000}).showModal();
            //
            //         }
            //     }
            // })
        })

    });


    // 导出,不验证
    // $('#exportBtn').bind('click', function () {
    //     var moveTypeCode = $("#moveTypeCode").val();
    //     var ticketStatus = $("#ticketStatus").val();
    //     var buyer = $("#buyer").val();
    //     if(moveTypeCode.length == 0){
    //         moveTypeCode = ['-1']
    //     }
    //     if(ticketStatus == -1){
    //         ticketStatus = null;
    //     }
    //     if(buyer == -1){
    //         buyer = null;
    //     }
    //     console.log(moveTypeCode);
    //     console.log(ticketStatus);
    //     console.log(buyer);
    //     var parames = [];
    //     parames.push({ name: "keyWord", value: $("#keyWord").val()});
    //     parames.push({ name: "starttime", value: $("#starttime").val()});
    //     parames.push({ name: "endtime", value: $("#endtime").val()});
    //     parames.push({ name: "ticketStatus", value: ticketStatus});
    //     parames.push({ name: "buyer", value: buyer});
    //     parames.push({ name: "moveTypeCode", value: JSON.stringify(moveTypeCode)});
    //     //parames.push({ name: "orderNo", value: $("#orderNo").val()});
    //     parames.push({ name: "stockOrderNo", value: $("#stockOrderNo").val()});
    //     Post("/proxy-finance/finance/purchase/AccountPayableReports/exportInvoiceStoreInReport", parames);
    //
    // });





    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }
    // 按照数据生成日期查询
    $('#creatTimeCheckbox').change(function () {
        $('.creatTimeWrap').css('display',$(this).prop('checked')?'block':'none');
        $('#creStartTime, #creEndTime').prop('disabled', $(this).prop('checked')?true:false);
    });
    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/invoice/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            $("#keyWord").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#keyWord").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#keyWord").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();
    }

    //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('1').then( res => {
            // console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        }).catch(err => {
            console.log(err);
        })
    });

})
