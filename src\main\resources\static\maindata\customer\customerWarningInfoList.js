$(function () {
    $('#table').XGrid({
        url: "/proxy-customer/customer/customerWarning/customerWarningInfoPageList",
        colNames: ['', '预警期间', '机构名称', '开始日期', '截止日期', '预警日期','客户数', '预警数', '送达率', '完成率', '质管姓名', '质管电话', '质管邮箱', '质管钉钉', '预警发送时间','效期剩余天数'],
        colModel: [
            {
                name: 'id', //与反回的json数据中key值对应
                hidden: true,
                key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            },
            {
                name: 'dateNum',
                index: 'dateNum'
            },
            {
                name: 'orgCodeName',
                index: 'orgCodeName'
            },
            {
                name: 'startTime',
                index: 'startTime',
                formatter: function (value) {
                    let date = value;
                    if (!value) return false;
                    date = utils.formatDate(value);
                    return date.split(' ')[0];
                }
            },
            {
                name: 'endTime',
                index: 'endTime',
                formatter: function (value) {
                    let date = value;
                    if (!value) return false;
                    date = utils.formatDate(value);
                    return date.split(' ')[0];
                }
            },
            {
                name: 'warningTime',
                index: 'warningTime',
                formatter: function (value) {
                    let date = value;
                    if (!value) return false;
                    date = utils.formatDate(value);
                    return date.split(' ')[0];
                }
            },
            {
                name: 'customerNum',
                index: 'customerNum'
            },
            {
                name: 'warningNum',
                index: 'warningNum'
            },
            {
                name: 'sendRateStr',
                index: 'sendRateStr'
            },
            {
                name: 'finishRateStr',
                index: 'finishRateStr'
            },
            {
                name: 'qualityName',
                index: 'qualityName'
            },
            {
                name: 'qualityPhone',
                index: 'qualityPhone'
            },
            {
                name: 'qualityMail',
                index: 'qualityMail'
            },
            {
                name: 'qualityDing',
                index: 'qualityDing'
            },
            {
                name: 'sendTime',
                index: 'sendTime',
                formatter: function (e) {
                    if (e) {
                        return utils.formatDate(e);
                    }
                },
                width: 200
            },
            {
                name: 'dueDayQuery',
                index: 'dueDayQuery'
            }
        ],
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        ondblClickRow: function (id, c, a, b) {
            utils.openTabs("purchaseRefundPriceOrderDetail", "资质预警明细", "/proxy-customer/customer/customerWarning/detail?infoId="+id);
        },
        onSelectRow: function (e, c, a, b) {},
        pager: '#grid-pager'
    });

    $("#SearchBtn").on("click", function () {
        $('#table').XGrid('setGridParam', {
            postData: {
                "dateNum": $("#dateNum option:selected").val(),
                "bdName": $("#bdName").val(),
                "customerName": $("#customerName").val(),
            },
            page: 1
        }).trigger('reloadGrid');
    });

    $("#exportBtn").on("click", function () {
        utils.exportAstrictHandle('table', Number($('#totalPageNum').text()), 1).then( () => {
            return false;
        }).catch( () => {
            utils.dialog({
                title: '提示',
                content:"数据量大的时候耗时较长，请耐心等待。",
                okValue: '确定',
                ok: function () {
                    let body = document.body;
                    let form = $(body).find('form#searchForm');
                    $(form).attr("action","/proxy-customer/customer/customerDelegationFile/exportCustomerDelegationFileExcel");
                    $(form).submit();
                },
                cancelValue: '取消',
                cancel: function () { },
            }).showModal();
        });
    });

    utils.valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},'customerName',
        {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});

    utils.valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},'BDName',
        {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
})
