$(function(){
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
})
    var urlObjectList = [];
    $('#X_Tableb').XGrid({
        url:"/proxy-product/product/zhilu/productOrga/query",
        colNames: ['商品编码', '商品名', '通用名',"商品大类", '型号/规格', '生产厂家', '批准文号', '包装单位', '剂型',"首营快照url"],
        colModel: [
             {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'productName',
                index: 'productName',
                width: 180
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 180
            },{
                name: 'largeCategoryVal',
                index: 'largeCategoryVal',
                width: 80
            }, {
                name: 'specifications',
                index: 'specifications',
                width: 200
            }, {
                name: 'manufacturerValue',
                index: 'manufacturerValue',
                width: 220
            }, {
                name: 'approvalNumber',
                index: 'approvalNumber',
                width: 180
            }, {
                name: 'packingUnitValue',
                index: 'packingUnitValue',
                width: 80
            }, {
                name: 'dosageFormValue',
                index: 'dosageFormValue',
                width: 80
            }, {
                name: 'imageUrl',
                hidden:true
            }
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        multiselect: true,//是否多选
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            utils.openTabs("zhiluProductOrgaDetail", "商品详情", "/proxy-product/product/zhilu/productOrga/toDetail?productId="+id);
        },
            onSelectRow: function (id, dom, obj, index, event) {
                console.log(id, dom, obj, index, event)
                setUrlObjectList(dom,id,obj);
            },
        rownumbers: true,
    });
    // 生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord"},"manufacturer",{data:"manufactoryId",value:"manufactoryName"});
    $("#SearchBtn").on("click", function () {
        urlObjectList = [];
        $("#productCode").val($("#productCode").val().trim());
        $("#approvalNumber").val($("#approvalNumber").val().trim());
        $("#smallPackageBarCode").val($("#smallPackageBarCode").val().trim());
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "productCode": $("#productCode").val(),
                "approvalNumber":$("#approvalNumber").val(),
                "smallPackageBarCode":$("#smallPackageBarCode").val(),
                "manufacturer":$("#manufacturer").val(),
                "largeCategory":$("#largeCategory").val(),
            },page:1
        }).trigger('reloadGrid');
    });
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    });
}
function setUrlObjectList($tr,id,rowData){
    var a=rowData;
    var fileParam = {};
    if($tr.hasClass('selRow') && a.imageUrl){
        fileParam.id = a.id;
        fileParam.name = a.productCode;
        fileParam.url = a.imageUrl;
        urlObjectList.push(fileParam);
    }else if(!$tr.hasClass('selRow')){
        $(urlObjectList).map(function (i,v) {
            if(v.id==a.id){
                urlObjectList.splice(i,1);
            }
        })
    }
}
