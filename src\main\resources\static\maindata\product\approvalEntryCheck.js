function approvalInputValidation(tableData, checkInp) {
    let resObj = {}
    const tableDataSelId = tableData.map(item => item['selectedValId'])
    if ('普通药品' == checkInp) {
        if (tableDataSelId.indexOf('3') < 0) {
            resObj = {
                message: '商品大类为“普通药品”时，必须录入《药品再注册批件》'
            }
        }
    } else if ('HZP' == checkInp) {
        if (tableDataSelId.indexOf('51') < 0) {
            resObj = {
                message: '商品所属经营范围为“化妆品”时，必须录入《化妆品生产许可证》'
            }
        } else if (tableDataSelId.filter(item => ['14','15','16','17'].indexOf(item) !== -1).length == 0) {
            resObj = {
                message: '商品所属经营范围为“化妆品”时，必须录入《国产特殊用途化妆品行政许可批件》、《国产非特殊化妆品备案登记凭证》、《进口特殊用途化妆品卫生许可批件》、《进口非特殊用途化妆品备案登记凭证》中的一个'
            }
        }
    } else if ('SP_BJSP' == checkInp) {
        if (tableDataSelId.indexOf('11') < 0) {
            resObj = {
                message: '商品所属经营范围为“保健食品”时，必须录入《保健食品备案凭证》'
            }
        } else if (tableDataSelId.indexOf('50') < 0) {
            resObj = {
                message: '商品所属经营范围为“食品”时，必须录入《食品生产许可证》'
            }
        }
    } else if ('SP_YBZSPXS' == checkInp || 'SP_TSYXYTPFSP' == checkInp || 'SP_QTYYEPFSP' == checkInp) {  // 预包装食品 || 特殊医学用途配方食品 || 其他婴幼儿配方食品
        if (tableDataSelId.indexOf('50') < 0) {
            resObj = {
                message: '商品所属经营范围为“食品”时，必须录入《食品生产许可证》'
            }
        }
    } else if (checkInp.indexOf('SLYLQX') > -1  || checkInp.indexOf('II') == 0) {
        if (tableDataSelId.indexOf('9') < 0) {
            resObj = {
                message: '商品所属经营范围为“II类医疗器械”和“III类医疗器械”时，必须录入《医疗器械注册证》'
            }
        } else if (tableDataSelId.indexOf('49') < 0) {
            resObj = {
                message: '商品所属经营范围为“II类医疗器械”和“III类医疗器械”时，必须录入《医疗器械生产企业许可证》'
            }
        }
    } else if (checkInp.indexOf('ELYLQX') >  -1 || checkInp.indexOf('II') == 0) {
        if (tableDataSelId.indexOf('9') < 0) {
            resObj = {
                message: '商品所属经营范围为“II类医疗器械”和“III类医疗器械”时，必须录入《医疗器械注册证》'
            }
        } else if (tableDataSelId.indexOf('49') < 0) {
            resObj = {
                message: '商品所属经营范围为“II类医疗器械”和“III类医疗器械”时，必须录入《医疗器械生产企业许可证》'
            }
        }
    } else if ('YLYLQX' == checkInp) {
        if (tableDataSelId.indexOf('8') < 0) {
            resObj = {
                message: '商品经营范围为“I类医疗器械”时，必须录入《第一类医疗器械备案凭证》'
            }
        } else if (tableDataSelId.indexOf('48') < 0) {
            resObj = {
                message: '商品所属经营范围为“I类医疗器械”时，必须录入《第一类医疗器械生产备案凭证》'
            }
        }
    }
    console.log('resObj', resObj);
    return resObj
}
