$(function () {
  $('div[fold=head]').fold({
    sub: 'sub'
  });

  function percentFormatter(value) {
    if (value) {
      // 临时规避浮点数陷阱 https://github.com/camsong/blog/issues/9
      return (value * 100).toFixed(2) + "%";
    } else {
      return '0%'
    }
  }

  /**
   * @param cellValue
   * @param options
   * @param rowData 当前行的数据（由 setGridParam 函数的 data 参数注入）
   *
   * 更多 API 细节：http://www.trirand.com/jqgridwiki/doku.php?id=wiki:custom_formatter
   */
  function passedNumAndRatioFormatter(cellValue, options, rowObject) {
    return renderNumAndRatioHTML(rowObject.passedNum, rowObject.passedRatio)
  }

  function nonPassedNumAndRatioFormatter(cellValue, options, rowObject) {
    return renderNumAndRatioHTML(rowObject.nonPassedNum, rowObject.nonPassedRatio)
  }


  function renderNumAndRatioHTML(firstValue, secondValue) {
    const firstSpan = "<span style='font-size: large'>" + firstValue + "</span><br/>"
    const secondSpan = "<span style='font-size: small'>" + percentFormatter(secondValue) + "</span>"
    return firstSpan + secondSpan
  }

  const colMaps = [
    {colName: '', name: 'id', hidden: true},
    {colName: '机构', name: 'orgName', index: 'orgName', width: 250},
    {colName: '统计区间', name: 'statPeriod', index: 'statPeriod', width: 180},
    {colName: '变更来源', name: 'sourceName', index: 'sourceName'},
    {colName: '<i>客户变更记录</i><i class="questa"></i>', name: 'recordNum', index: 'recordNum'},
    {colName: '<i>变更客户</i><i class="questa"></i>', name: 'customerNum', index: 'customerNum'},
    {colName: '<i>变更资质数量</i><i class="questa"></i>', name: 'credentialNum', index: 'credentialNum'},
    {colName: '<i>变更完成数<br/>/变更完成率</i><i class="questa"></i>', name: 'passedNumAndRatio', index: 'passedNumAndRatio', formatter: passedNumAndRatioFormatter,width: 200},
    {colName: '<i>变更未完成数<br/>/变更未完成率</i><i class="questa"></i>', name: 'nonPassedNumAndRatio', index: 'nonPassedNumAndRatio', formatter: nonPassedNumAndRatioFormatter, width: 200},
  ]

  // 创建表格
  $('#baseTable').XGrid({
    colNames: colMaps.map(item => {
      return item.colName
    }),
    colModel: colMaps.map(item => {
      delete item.colName
      return item
    }),
    // 设置为交替行表格,默认为false
    altRows: true,
    // 设置每页展示行数，-1 表示全部展示
    rowNum: -1,
    // 是否展示序号
    rownumbers: true
  });


  function fetchData() {
    $.ajax({
        type: 'post',
        data: $("#searchForm").serializeToJSON(),
        url: '/proxy-customer/customer/statistic/changeSummaryList',
        success: (res) => {
          const total = res.result.recordTotal
          if (total) {
            total.orgName = '合计'
            res.result.recordList.push(total)
          }
          $('#baseTable').XGrid('setGridParam', {
            data: res.result.recordList
          }).trigger('reloadGrid')
        }
      }
    )
  }

  /**字段名称解释start*/
  var questaOption
      = [
    {
      name:'recordNum',
      content:'同一个客户在同一统计区间内变更2次，变更记录为2。比如客户A 豆芽发起一次变更+1，神农再发起一次变更再+1.不包括录入中 和已关闭状态的客户变更。'
    },
    {
      name:'customerNum',
      content:'变更的客户数量。比如同一个客户在同一统计区间内变更2次，变更客户数还是为1。'
    },
    {
      name:'credentialNum',
      content:'变更记录中，发起变更的资质数量有多少个。比如，更新了当前资质，或新增了资质。'
    },
    {
      name:'passedNumAndRatio',
      content:'指的是该统计区间客户资料变更审核通过的记录数。比如客户资料变更记录数是120，审核通过是70，那变更完成数就为70'
    },
    {
      name:'nonPassedNumAndRatio',
      content:'变更未完成数=客户变更记录数-变更完成数。不包括录入中 和已关闭状态的客户变更。比如变更完成数是120，未完成的客户数 50，那变更未完成率=50/120=42%'
    }
  ];

  $("body").on({
    mouseover:function (e) {
      var item = questaOption.find(function (item) {
        return item.name === $(e.target).parent('th').attr('row-describedby');
      });
      $("body").append("<div id='div_toop'><div id='inner'>"+item.content+"</div></div>");
      $("#div_toop")
          .css({
            "top": (e.pageY - 44) + "px",
            "position": "absolute",
            "padding-left": "5px",
            "padding-right": "5px",
            "padding-top": "5px",
            "padding-bottom": "5px",
            "background-color": "lightGray",
            "left": (e.pageX - 130) + "px"
          }).show("fast");
    },
    mouseout:function () {
      $("#div_toop").remove();
    }
  },questaOption.map(function (item) { return "th[row-describedby='"+item.name+"'] .questa" }).join(','));

  /**
   * 1. 设置查询按钮点击事件
   * 2. 立即触发查询按钮的点击事件
   */
  $("#searchBtn").on("click", fetchData).trigger('click');


  /**
   * 设置导出按钮点击事件
   */
  $("#exportBtn").on("click", function () {
    const data = $('#baseTable').XGrid('getGridParam').data
    //无数据提示
    if (data.length==0){
      utils.dialog({"content":"无数据可导出","timeout":2000}).show();
      return
    }
    utils.dialog({
      title: '提示',
      content: "数据导出需要一定时间，请耐心等待。",
      okValue: '开始导出',
      cancelValue: '取消',
      ok: function () {
        // 利用 form 表单完成导出
        const form = $('#searchForm');
        // 导出接口
        $(form).attr("action", "/proxy-customer/customer/statistic/changeSummaryList/export");
        $(form).submit();
      },
      cancel: function () {
      },
    }).showModal();
  });
})
