$(function () {

    $("#table_a").trigger("reloadGrid");
    /* 日期初始化 */
    z_utils.initDate('begint', 'endt')

    /* tabs 切换 */
    z_utils.tableCut('flex');

    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    /* table_a */
    var colName = ['id','机构名称', '业务类型转移申请单号', '业务类型转移执行单号', '申请人所在部门', '申请人', '申请日期', '复核人',   '申请调整数量', '实际调整数量','备注' ];
    var colModel = [
        {
            name: 'id',
            index: 'id',
            hidden:true,
            hidegrid:true
        },
        {
            name: 'orgName',
            index: 'orgName',
            width: 260,
        },{
            name: 'applyOrderCode',
            index: 'applyOrderCode',
            width: 200,
        }, {
            name: 'orderCode',
            index: 'orderCode',
            width: 200,
        },   {
            name: 'departmentName',
            index: 'departmentName',
            hidden:true,
            hidegrid:true
        }, {
            name: 'proposer',
            index: 'proposer',
            hidden:true,
            hidegrid:true
        }, {
            name: 'createTime',
            index: 'createTime',
            formatter: dateFormatter
        }, {
            name: 'reviewer',
            index: 'reviewer',
            width: 200,
            hidden:true,
            hidegrid:true
        }, {
            name: 'applyAdjustAmount',
            index: 'applyAdjustAmount',
            width: 200,
        }, {
            name: 'actualAdjustAmount',
            index: 'actualAdjustAmount'
        }, {
            name: 'remarks',
            index: 'remarks'
        }];



    $('#table_a').XGrid({
        url:"/proxy-storage/storage/StorageOwner/execute/findList",
        postData:{startTime:$("#begint").val(),endTime:$('#endt').val()},
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'id',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            utils.openTabs("StorageOwnerExecuteInfo","业务类型转移执行单详情", "/proxy-storage/storage/StorageOwner/execute/toInfo?orderCode="+obj.orderCode,{},function () {
                $('#searchBtn').trigger('click')
            });
        },
        attachRow:true,
        gridComplete: function () {

        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    };


    // 筛选列
    $("#set_tb_rows").on('click',function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .table-box .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })


    $('.panel-title').on('click',function(){
        //alert('777')
    })



    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        //获取form数据
        var formData = $('#form_a').serializeToJSON();
        //更新表格数据
        //列表
        $('#table_a').XGrid('setGridParam', {
            url:"/proxy-storage/storage/StorageOwner/execute/findList",
            postData: formData,
            page: 1
        }).trigger("reloadGrid");
    });






/*    /!* 导出 *!/
    $('#exportRowData').on('click', function () {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            var formData = $('#form_a').serializeToJSON();
            formData["colName"] = colName;
            formData["colNameDesc"] = colNameDesc;

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            var exportLimitRow = Number($("#exportLimitRow").val());
            if (data.length <= exportLimitRow && data.length >0){
                /!*if (!data.length) {
                    data = [data];
                }*!/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                formData["selectData"] = data;
            } else {
                var rows = $('#' + tableId)[0].p.records;
                if(rows > exportLimitRow) {
                    utils.dialog({
                        title: '提示',
                        width: 200,
                        height: 40,
                        cancel: false,
                        content: '您本次导出的数据量过大（已超过'+exportLimitRow+'条），不允许导出，请缩小导出范围。',
                        okValue: '确定',
                        ok: function () {
                            //utils.closeTab();
                        }

                    }).showModal();
                    return;
                }
                data = '';
            }
            console.log(colName);

            switch (getTabInd()) {
                case 0:
                    httpPost("/proxy-storage/storage/adjustment/exportApplyList", formData);
                    break;
                case 1:
                    httpPost("/proxy-storage/storage/adjustment/exportApplyDetailList", formData);
                    break;
            }
        });
    });*/


    //获取当前tab  的下标 销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    function getTabInd() {
        for (var i = 0; i < $('.saleOrderList_tabs li').length; i++) {
            if ($('.saleOrderList_tabs li').eq(i).hasClass('active')) {
                _ind = $('.saleOrderList_tabs li').eq(i).index();
            }
        }
        return _ind;
    };

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }



    // 生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord"},"manufacturer",{data:"manufactoryId",value:"manufactoryName"});


    /**
     * 字典值
     * @param isEmpty
     * @param obj
     * @param data
     */
    function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
        var resParam=Object.assign({'list':'result'},resParam);
        $("#"+obj+"Val").Autocomplete({
            serviceUrl: url, //异步请求
            paramName: param.paramName,//查询参数，默认 query
            params:param.params || {},
            dataType: 'json',
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            dataReader:resParam,
            triggerSelectOnValidInput: false, // 必选
            showNoSuggestionNotice: true, //显示查无结果的container
            noSuggestionNotice: '查询无结果',//查无结果的提示语
            onSelect: function (result) {
                select && select(result)
                $("#manufacturer").val(result.data);
                $("#"+obj+"Val").attr("data-value",result.value);
            },
            onNoneSelect: function (params, suggestions) {
                console.log(params, suggestions);
                noneSelect && noneSelect();
                var value=$("#"+obj+"Val").val();
                if(value != $("#"+obj+"Val").attr("data-value"))
                {
                    $("#"+obj).val("");
                    $("#"+obj+"Val").val("");
                }
            }
        });
    }


    //商品名称 搜索
    $('#input_goodName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$("#orgCode").val() +'&pageSize=5', //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#productCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            //console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#productCode").val("");
            $("#input_goodName").val("");
            //$("#s_commodity").trigger("click");
            //console.log(params, suggestions);
            //console.log('没选中回调函数');
            ;
        }
    });


    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#productCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        /*var commodity_d = $(e.target).prev('input').val();
        utils.dialog({
          url: '/proxy-storage/storage/saleOrderGoodList.ftl',
          title: '商品列表',
          width: $(window).width() * 0.9,
          height: $(window).height() * 0.7,
          data: commodity_d, // 给modal 要传递的 的数据
          onclose: function () {
            if (this.returnValue) {
              var data = this.returnValue;
              $(e.target).prev('input').val(data.name);
              console.log("this.returnValue", data);
            }
          },
          oniframeload: function () {

          }
        }).showModal();
        return false;
        //alert('查询商品名称')
    */  })
})