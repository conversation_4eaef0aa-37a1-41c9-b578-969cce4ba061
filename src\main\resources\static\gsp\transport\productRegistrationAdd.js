$(function () {

    //设置日期默认值
    $("#registerTime").val(getNowFormatDate());
    $("#departureTime").val(getNowFormatDateTime());

    //获取发货地址
    $.ajax({
        method: "POST",
        url: "/proxy-order/order/orderOut/orderOut/getDeliverGoodsAddress",
        dataType: 'json',
        cache: false,
        success: function(data){
            console.log(data);
            $("#deliverAddressName").val(data.result);
        },
        error: function (message) {

        }
    });


    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        // url: '/proxy-finance/finance/initBalance/findOutAndInStorage',
        data: [],
        colNames: ['货单号', '销售出库单号', '销售订单号', '客户名称', '联系电话','出库日期','收货地址', '销售订单备注','启运温度°C'],
        colModel: [{
            name:'goodsCode',
            index:'goodsCode',
            width: 200,
            rowtype: '#goodsCode'
        }, {
            name: 'outOrderCode',
            width: 200,
            formatter:function (e) {
                if(e != '' && e != undefined){
                    return '<a href="javascript:;" url= "/proxy-order/order/orderOut/orderOutDetail/toOrderOutDetail?outOrderCode='+e+'" class="outOrderCode">'+e+'</a>';
                }else{
                    return '';
                }
                },
            unformat:function (e,rowtype,rowdata) {
                var code = e.match(/\>(\S+)(\<\/a>)$/);
                if(code&&code.length&&code[1]){
                    return code[1]
                }else {
                    return e
                }
            }
        }, {
            name:'salesOrderCode',
            index:'salesOrderCode',
        }, {
            name:'customerName',
            index:'customerName',
        }, {
            name:'receiverPhone',
            index:'receiverPhone',
        },{
            name:'outTime',
            index:'outTime',
            formatter: function(val){
                if(val){
                    return formatDate(val);
                }else {
                    return '';
                }
            }
        },{
            name: 'address',
            index:'address',
        }, {
            name: 'salesOrdersRemarks',
            index:'salesOrdersRemarks',
        }, {
            name: 'startShipmentTemperature',
            index:'startShipmentTemperature',
            rowtype: '#input'
        },{
            name: 'ecOrderCode',
            index:'ecOrderCode',
            hidden:true
        },{
            name: 'orgCode',
            index:'orgCode',
            hidden:true
        },{
            name: 'customerId',
            index:'customerId',
            hidden:true
        },{
            name: 'customerName',
            index:'customerName',
            hidden:true
        },{
            name: 'customerCode',
            index:'customerCode',
            hidden:true
        },{
            name: 'operName',
            index:'operName',
            hidden:true
        },{
            name: 'outTime',
            index:'outTime',
            hidden:true
        },{
            name: 'postingTime',
            index:'postingTime',
            hidden:true
        },{
            name: 'deleveryType',
            index:'deleveryType',
            hidden:true
        },{
            name: 'taxAmount',
            index:'taxAmount',
            hidden:true
        },{
            name: 'totalRealPayAmount',
            index:'totalRealPayAmount',
            hidden:true
        },{
            name: 'totalTaxAssess',
            index:'totalTaxAssess',
            hidden:true
        },{
            name: 'totalOutNum',
            index:'totalOutNum',
            hidden:true
        },{
            name: 'provinceId',
            index:'provinceId',
            hidden:true
        },{
            name: 'cityId',
            index:'cityId',
            hidden:true
        },{
            name: 'districtId',
            index:'districtId',
            hidden:true
        },{
            name: 'streetId',
            index:'streetId',
            hidden:true
        },{
            name: 'departmentName',
            index:'departmentName',
            hidden:true
        },{
            name: 'salesPersonName',
            index:'salesPersonName',
            hidden:true
        },{
            name: 'OperatePersonName',
            index:'OperatePersonName',
            hidden:true
        },{
            name: 'activityPreferentialAmount',
            index:'activityPreferentialAmount',
            hidden:true
        },{
            name: 'balancePreferentialAmount',
            index:'balancePreferentialAmount',
            hidden:true
        },{
            name: 'rebateAmount',
            index:'rebateAmount',
            hidden:true
        },{
            name: 'taxCostAmount',
            index:'taxCostAmount',
            hidden:true
        },{
            name: 'costAmout',
            index:'costAmout',
            hidden:true
        },{
            name: 'salesOrdersRemarks',
            index:'salesOrdersRemarks',
            hidden:true
        },{
            name: 'payType',
            index:'payType',
            hidden:true
        },{
            name: 'pcs',
            index:'pcs',
            hidden:true
        },{
            name: 'temporaryAreaName',
            index:'temporaryAreaName',
            hidden:true
        },{
            name: 'invoice',
            index:'invoice',
            hidden:true
        },{
            name: 'preferenrialAmount',
            index:'preferenrialAmount',
            hidden:true
        },{
            name: 'expressCompany',
            index:'expressCompany',
            hidden:true
        },{
            name: 'expressNo',
            index:'expressNo',
            hidden:true
        },{
            name: 'expressFed',
            index:'expressFed',
            hidden:true
        },{
            name: 'mobileType',
            index:'mobileType',
            hidden:true
        },{
            name: 'orderType',
            index:'orderType',
            hidden:true
        },{
            name: 'createUser',
            index:'createUser',
            hidden:true
        },{
            name: 'updateUser',
            index:'updateUser',
            hidden:true
        },{
            name: 'createTime',
            index:'createTime',
            hidden:true
        },{
            name: 'updateTime',
            index:'updateTime',
            hidden:true
        },{
            name: 'officeAccountant',
            index:'officeAccountant',
            hidden:true
        },{
            name: 'billType',
            index:'peerType',
            hidden:true
        },{
            name: 'billIsOpen',
            index:'billIsOpen',
            hidden:true
        },{
            name: 'drugTestReportUrl',
            index:'drugTestReportUrl',
            hidden:true
        }, {
            name: 'receiver',
            index:'receiver',
            hidden:true
        }, {
            name: 'drugTestReportUrl',
            index:'drugTestReportUrl',
            hidden:true
        }],
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        selectandorder: true,
        // attachRow:true,//合计行
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            var $ele = $(event.target);
            if ($ele.attr("class")=="outOrderCode") {
                var url = $ele.attr("url");
                utils.openTabs("order-salesOrder-toInfo","销售出库单明细",  url ,{reload:false})
            }

        },
        gridComplete: function () {
            // var data = $(this).XGrid("getRowData");
            // if(!data.length){
            //     utils.dialog({
            //         content:"查询无数据",
            //         quickClose: true,
            //         timeout: 2000
            //     }).show();
            // }
        },
        pager: '#grid-pager'
    });

    // 查询数据
    $('#searchBtn').bind('click', function () {
        var param = $('#myform').serializeToJSON();
        $('#X_Table').setGridParam({
            url: '/proxy-finance/finance/initBalance/findOutAndInStorage',
            postData: param
        }).trigger('reloadGrid');
        //统计方法
    })


    //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    });

    //承运单位 双击查询
    $('#CompanyName').on({
        dblclick:function () {
            utils.dialog({
                title: '承运单位列表',
                // url: '/orderReturn/orderReturnController/toCommodityList',
                url: '/proxy-gsp/gsp/transport/toTransportCompanyList',
                width: $(window).width() * 0.8,
                height: 500,
                data: $('#productDesc').val(), // 给modal 要传递的 的数据
                onclose: function () {
                    if (this.returnValue) {
                        $('#driverName').val('');
                        $('#truckName').val('');
                        // 回显信息
                        var data = this.returnValue;
                        $('#CompanyName').val(data.carrierName);
                        $('#companyCode').val(data.carrierCode);
                        $('#carrierCode').val(data.carrierCode);
                    }
                },
                oniframeload: function () {
                    // console.log('iframe ready')
                }
            }).showModal();
            return false;
        },
        input:function () {
            $('#driverName').val('');
            $('#truckName').val('');
        }
    });
    /*$('#CompanyName').dblclick(function () {
        utils.dialog({
            title: '承运单位列表',
            // url: '/orderReturn/orderReturnController/toCommodityList',
            url: '/proxy-gsp/gsp/transport/toTransportCompanyList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#productDesc').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    // 回显信息
                    var data = this.returnValue;
                    console.log(data)
                    $('#CompanyName').val(data.carrierName);
                    $('#companyCode').val(data.carrierCode);
                    $('#carrierCode').val(data.carrierCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    }).change(function () {
        $('#driverName').val('');
        $('#truckName').val('')
    });*/

    //司机 双击查询
    $('#driverName').dblclick(function () {
        if(!verifyCompanyName()) return false;
        utils.dialog({
            title: '司机列表',
            url: '/proxy-gsp/gsp/transport/toTransportDriverList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#companyCode').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    // 回显信息
                    var data = this.returnValue;
                    console.log(data)
                    $('#driverName').val(data.driverName);
                    $('#agentor').val(data.driverName);
                    $('#driverLicenceNo').val(data.drivingLicenseCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    });

    //运输车辆 双击查询
    $('#truckName').dblclick(function () {
        if(!verifyCompanyName()) return false;
        utils.dialog({
            title: '车辆列表',
            url: '/proxy-gsp/gsp/transport/toTransportTruckList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#companyCode').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    // 回显信息
                    var data = this.returnValue;
                    console.log(data)
                    $('#truckName').val(data.vehicleType);
                    $('#truckPlateNumber').val(data.carNumber);

                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    });

    //新增出库单
    $('#add').click(function () {
        var rowData = $("#X_Table").XGrid('getRowData');
        utils.dialog({
            title: '待运输出库单',
            url: '/proxy-gsp/gsp/transport/toTransportOutOrderList',
            width: $(window).width() * 0.8,
            height: 500,
            data: {
                orgCode: $('#orgCode').val(),
                ids: rowData.map(function (item) {
                    return item.outOrderCode
                })
            }, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    // 回显信息
                    var data = this.returnValue;
                    console.log(data)
                    if(data.length){
                        $.each(data,function (index, item) {
                            $('#X_Table').XGrid('addRowData',item)
                        })
                    }

                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    });

    //取消
    $('#cancelBtn').on('click', function () {
        utils.dialog({
            title: '温馨提示:',
            content: '取消后页面信息将丢失，是否确认取消？',
            width: 300,
            height: 50,
            okValue: '确认',
            ok: function () {
                $('#myform')[0].reset();

                //设置日期默认值
                $("#registerTime").val(getNowFormatDate());
                $("#departureTime").val(getNowFormatDateTime());

                //获取发货地址
                $.ajax({
                    method: "POST",
                    url: "/proxy-order/order/orderOut/orderOut/getDeliverGoodsAddress",
                    dataType: 'json',
                    cache: false,
                    success: function(data){
                        console.log(data);
                        $("#deliverAddressName").val(data.result);
                    },
                    error: function (message) {

                    }
                });
                $('#X_Table').XGrid('clearGridData');
            },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal();
    });

    //删除
    $('#delete').on('click', function () {
        var selData = $('#X_Table').XGrid("getSeleRow");
        if(!selData.length){
            utils.dialog({title: '提示',content:'请至少选择一行！',timeout:3000}).show();
            return false;
        }
        utils.dialog({
            title: '温馨提示:',
            content: '是否确认删除已选中的出库单？',
            width: 300,
            height: 50,
            okValue: '确认',
            ok: function () {
                var selData = $('#X_Table').XGrid('getSeleRow');
                $.each(selData, function (index, item) {
                    $('#X_Table').XGrid('delRowData', item.id);
                })
            },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal();
    });

    //确认保存
    $('#confirmBtn').on('click', function () {
        var rowdata = $('#X_Table').getRowData();
        if(!rowdata.length){
            utils.dialog({title:'提示',content:'请先增加出库单！',timeout: 3000}).show();
            return false;
        }

        $.each(rowdata, function (index, item) {
            delete item.id;
            delete item.rowid;
        })

        if(rowdata.length == 0){
            utils.dialog({
                title:'温馨提示',
                content:'请选择数据！',
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
        }
        var arr = [];
        utils.dialog({
            title: '温馨提示',
            content: '保存成功后该页面将关闭，是否确认保存？',
            okValue: '确定',
            ok: function () {
                //下面保存
                var formData = $('#myform').serializeToJSON();
                $.ajax({
                    url: "/proxy-gsp/gsp/transport/toSaveTransportOutOrderList",
                    type:'post',
                    dataType:'json',
                    data: {formData:JSON.stringify(formData), rowdata:JSON.stringify(rowdata)},
                    success: function(res){
                        if(res){
                            if(res.code === 0){
                                utils.dialog({content: '确认成功！', quickClose: true, timeout: 1000}).show();
                                setTimeout(function () {
                                    utils.closeTab();
                                },1000);
                            }else{
                                utils.dialog({
                                    title: '温馨提示',
                                    //content: data.msg,
                                    content: '<div style="max-width: 400px;max-height: 400px;overflow-y: auto">'+ res.msg +'</div>',
                                    okValue: '确定',
                                    ok: function () {
                                    }
                                }).showModal();
                                if(res.result) $("#TransportRegisterCode").val(res.result);
                            }
                        }
                    }
                });
            }
        }).showModal()
    });

    // 校验承运单位
    function verifyCompanyName() {
        if($('#CompanyName').val() == ''){
            utils.dialog({title:'提示',content:'请先选择承运单位！',timeout: 1000}).show();
            return false;
        }else {
            return true;
        }
    }

})
/**
 * 单据日期 开始
 */
function startOrderDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        maxDate:'#F{$dp.$D(\'endOrderDate\')}'
    });
    $("#endOrderDate").val("");
}

/**
 * 单据日期 结束
 */
function endOrderDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        minDate:'#F{$dp.$D(\'startOrderDate\')}',
        maxDate: getMaxDate('#startOrderDate')
    });
}

function getMaxDate(id){
    return $(id).val().split('-')[0]+'-12-31';
}

/**
 * 获取当前时间，格式YYYY-MM-DD
 * @returns {string}
 */
function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}

function getNowFormatDateTime() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    var hour = date.getHours();
    var mini = date.getMinutes();
    var sec = date.getSeconds();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    if (hour >= 1 && hour <= 9) {
        hour = "0" + hour;
    }
    if (mini >= 0 && mini <= 9) {
        mini = "0" + mini;
    }
    if (sec >= 0 && sec <= 9) {
        sec = "0" + sec;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate +  ' ' + hour+ ':'+ mini + ':'+sec;
    return currentdate;
}

//获得本月的开始日期
function getMonthStartDate(){
    var now = new Date();                    //当前日期
    var nowDayOfWeek = now.getDay();         //今天本周的第几天
    var nowDay = now.getDate();              //当前日
    var nowMonth = now.getMonth();           //当前月
    var nowYear = now.getYear();             //当前年
    nowYear += (nowYear < 2000) ? 1900 : 0;  //
    var monthStartDate = new Date(nowYear, nowMonth, 1);
    return formatDate(monthStartDate);
}

//格式化日期：yyyy-MM-dd
function formatDate(date) {
    var date = new Date(parseInt(date));
    var myyear = date.getFullYear();
    var mymonth = date.getMonth() + 1;
    var myweekday = date.getDate();

    if (mymonth < 10) {
        mymonth = "0" + mymonth;
    }
    if (myweekday < 10) {
        myweekday = "0" + myweekday;
    }
    return (myyear + "-" + mymonth + "-" + myweekday);
}


