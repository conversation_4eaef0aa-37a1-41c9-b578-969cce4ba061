$(function () {
    var myDate = new Date;
    var year = myDate.getFullYear();//获取当前年
    var yue = myDate.getMonth()+1;//获取当前月
    var date = myDate.getDate();//获取当前日
  /* 参数,页面传递的数据 */
  var url = location.search;
  var param = z_utils.parseParams(url);
  //$('#val_checkTimes').val(param.time);
    $('#val_checkTimes').val(getNowFormatDate());
    $('#orgCode').val(param.orgcode);
    //$('#departName').val(param.departName);
    if(param.checkType){
        $("#checkType").val(decodeURIComponent(param.checkType))
    }
    if(param.isZyyp){
        $("#isZyyp").val(decodeURIComponent(param.isZyyp))
    }
    if(param.checkTimes!="undefined"){
        $('#val_checkTimes').val(decodeURIComponent(param.checkTimes));
    }

    $.ajax({
        url: "/proxy-gsp/gsp/stockInCheck/queryOrg",
        type: "get",
        success: function (result) {
            result.result.forEach(function (item,index) {
                if(item.orgCode==param.orgcode){
                    $('#orgName').val(item.orgName);
                }
            })
        }
    })



  var grid_dataY = [];

  var colName = ['id','商品编码', '商品名称', '商品规格', '生产厂家', '产地', '单位', '剂型',  '库房名称', '批号', '生产日期', '有效期至', '数量','存储条件',
    '外观', '除湿', '加湿', '升温', '降温', '通风', '养护原因', '是否异常','实际养护数量', '养护结论', '质管员', '复查结果', '批准文号', '备注','隐藏列','商品大类'
  ];
  var colModel = [
      {
      name: 'id',
      index: 'id',
      hidden:true,
      hidegrid:true
  },{
      name: 'productCode',
      index: 'productCode'
  }, {
      name: 'productName',
      index: 'productName'
  }, {
      name: 'specifications',
      index: 'specifications'
  }, {
      name: 'manufacturerVal',
      index: 'manufacturerVal'
  }, {
      name: 'producingArea',
      index: 'producingArea'
  }, {
      name: 'packingUnitVal',
      index: 'packingUnitVal'
  }, {
      name: 'dosageFormVal',
      index: 'dosageFormVal'
  }, {
      name: 'wareHouse',
      index: 'wareHouse',
      formatter: function (e) {
          if (e == '1') {
              return '合格库'
          } else if (e == '2'){
              return '不合格库'
          }else if (e == '3'){
              return '暂存库'
          }else {
              return e
          }
      },
      unformat: function(value){
          if(value == '合格库'){
              return 1;
          }else if(value == '不合格库'){
              return 2;
          }else if(value == '暂存库'){
              return 3;
          }else {
              return e
          }
      }
  }, {
      name: 'productBatch',
      index: 'productBatch'
  }, {
      name: 'productDate',
      index: 'productDate',
      formatter:function (e){
          if (e&&!isNaN(e)) {
              return ToolUtil.dateFormat(e, 'yyyy-MM-dd ');
          } else {
              return e;
          }
      }

  }, {
      name: 'validateDate',
      index: 'validateDate',
      formatter:function (e){
          if (e&&!isNaN(e)) {
              return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
          } else {
              return e;
          }
      }
  }, {
      name: 'storageAmount',
      index: 'storageAmount'
  }, {
          name: 'storageConditions',
          index: 'storageConditions'
      },/*  {
      name: 'repile',
      index: 'repile',
      rowtype: '#text14_e',
  },*/ {
      name: 'appearance',
      index: 'appearance',
      width:'80',
      formatter:function (e,rowType,rowData) {
          if(rowData.appearance == '1'){
              return `<div class="checkbox"><input type="checkbox" checked ></div>`;
          }else{
              return `<div class="checkbox"><input type="checkbox"></div>`;
          }
      },
      unformat: function(e,rowtype,rowdata){
          let rowId = rowtype.rowId;
          let checkVal = $('#'+rowId).find('[row-describedby=appearance] input').prop('checked');
          if(checkVal){
              return 1;
          }else{
              return 0;
          }
      }
  }, {
      name: 'dehumidification',
      index: 'dehumidification',
      width:'80',
      formatter:function (e,rowType,rowData) {
          if(rowData.dehumidification == '1'){
              return `<div class="checkbox"><input type="checkbox" checked ></div>`;
          }else{
              return `<div class="checkbox"><input type="checkbox"></div>`;
          }
      },
      unformat: function(e,rowtype,rowdata){
          let rowId = rowtype.rowId;
          let checkVal = $('#'+rowId).find('[row-describedby=dehumidification] input').prop('checked');
          if(checkVal){
              return 1;
          }else{
              return 0;
          }
      }
  }, {
      name: 'humidification',
      index: 'humidification',
      width:'80',
      formatter:function (e,rowType,rowData) {
          if(rowData.humidification == '1'){
              return `<div class="checkbox"><input type="checkbox" checked ></div>`;
          }else{
              return `<div class="checkbox"><input type="checkbox"></div>`;
          }
      },
      unformat: function(e,rowtype,rowdata){
          let rowId = rowtype.rowId;
          let checkVal = $('#'+rowId).find('[row-describedby=humidification] input').prop('checked');
          if(checkVal){
              return 1;
          }else{
              return 0;
          }
      }
  }, {
      name: 'warming',
      index: 'warming',
      width:'80',
      formatter:function (e,rowType,rowData) {
          if(rowData.warming == '1'){
              return `<div class="checkbox"><input type="checkbox" checked ></div>`;
          }else{
              return `<div class="checkbox"><input type="checkbox"></div>`;
          }
      },
      unformat: function(e,rowtype,rowdata){
          let rowId = rowtype.rowId;
          let checkVal = $('#'+rowId).find('[row-describedby=warming] input').prop('checked');
          if(checkVal){
              return 1;
          }else{
              return 0;
          }
      }
  }, {
      name: 'cooling',
      index: 'cooling',
      width:'80',
      formatter:function (e,rowType,rowData) {
          if(rowData.cooling == '1'){
              return `<div class="checkbox"><input type="checkbox" checked ></div>`;
          }else{
              return `<div class="checkbox"><input type="checkbox"></div>`;
          }
      },
      unformat: function(e,rowtype,rowdata){
          let rowId = rowtype.rowId;
          let checkVal = $('#'+rowId).find('[row-describedby=cooling] input').prop('checked');
          if(checkVal){
              return 1;
          }else{
              return 0;
          }
      }
  }, {
      name: 'ventilate',
      index: 'ventilate',
      width:'80',
      formatter:function (e,rowType,rowData) {
          // if(rowData.ventilate == '1'){
              return `<div class="checkbox"><input type="checkbox" checked ></div>`;
          // }else{
          //     return `<div class="checkbox"><input type="checkbox"></div>`;
          // }
      },
      unformat: function(e,rowtype,rowdata){
          let rowId = rowtype.rowId;
          let checkVal = $('#'+rowId).find('[row-describedby=ventilate] input').prop('checked');
          if(checkVal){
              return 1;
          }else{
              return 0;
          }
      }
  }, {
    name: 'checkStatus',
    index: 'checkStatus',
    rowtype:'#checkStatus_e',
    formatter:function (e,rowType,rowData) {
        if((e && e.indexOf(',') >= 0)||e===' '){
            return ''
        }else {
            return e
        }
    }
  }, {
    name: 'checkCause',
    index: 'checkCause',
    rowtype: '#checkCause_e',
    rowEvent: function (row) {
      /* 是否异常，养护结论联动 */
      var data = row.rowData;
      var id = data.id;
      var options = ['', '待复查','继续销售'];
      $('#table_a').XGrid('setRowData', id, {conclusion:data.checkCause});
    }
  },{
      name: 'amount',
      index: 'amount',
      rowtype: '#amount_e',
      // rowEvent: function (row) {
      //     /* 实际养护数量 */
      //     var data = row.rowData;
      //     var id = data.id;
      //     var options = ['', '待复查','继续销售'];
      //     $('#table_a').XGrid('setRowData', id, {conclusion:data.checkCause});
      // }
  }, {
    name: 'conclusion',
    index: 'conclusion',
      formatter:function (e,rowType,rowData) {
          // if(rowData.conclusion){
          //     var options = ['', '待复查','继续销售'];
          //     return options[rowData.conclusion]
          // }else {
          //     return e?e:"继续销售"
          // }
          if(rowData.checkCause){
              var options = ['', '待复查','继续销售'];
              return options[rowData.checkCause]
          }else {
              return '继续销售'
          }
      }
  }, {
    name: 'userName',
    index: 'userName',
    hidden:true,
    hideGrid:true,
  }, {
    name: 'scalerConclusion',
    index: 'scalerConclusion',
    hidden:true,
    hideGrid:true,
  }, {
    name: 'approvalNumber',
    index: 'approvalNumber',
  }, {
    name: 'remark',
    index: 'remark',
    rowtype: '#remark_e'
  },{
      name:'hidden_status',
      index:'hidden_status',
      hidden:true,
      hidegrid:true
  },{
      name:'drugClass',
      index:'drugClass',
      hidden:true
  }];
    initTable(param.status ? decodeURIComponent(param.status):'',param.checkPlanCode);
    function initTable (status,checkPlanCode){

      //判断编辑、新增、详情
      if(status){
          if(status == '1'){
              //详情
              $("#panel-title").text('在库养护商品');
              $('body button').not('#goback').hide();
              colModel = [
                  {
                  name: 'id',
                  index: 'id',
                  hidden:true,
                  hidegrid:true
              },{
                  name: 'productCode',
                  index: 'productCode'
              }, {
                  name: 'productName',
                  index: 'productName'
              }, {
                  name: 'specifications',
                  index: 'specifications'
              }, {
                  name: 'manufacturerVal',
                  index: 'manufacturerVal'
              }, {
                  name: 'producingArea',
                  index: 'producingArea'
              }, {
                  name: 'packingUnitVal',
                  index: 'packingUnitVal'
              }, {
                  name: 'dosageFormVal',
                  index: 'dosageFormVal'
              }, {
                  name: 'wareHouse',
                  index: 'wareHouse',
                  formatter: function (e) {
                      if (e == '1') {
                          return '合格库'
                      } else if (e == '2'){
                          return '不合格库'
                      }else if (e == '3'){
                          return '暂存库'
                      }else {
                          return e
                      }
                  },
                  unformat: function(value){
                      if(value == '合格库'){
                          return 1;
                      }else if(value == '不合格库'){
                          return 2;
                      }else if(value == '暂存库'){
                          return 3;
                      }else {
                          return e
                      }
                  }
              }, {
                  name: 'productBatch',
                  index: 'productBatch'
              }, {
                  name: 'productDate',
                  index: 'productDate',
                  formatter:function (e){
                      if (e&&!isNaN(e)) {
                          return ToolUtil.dateFormat(e, 'yyyy-MM-dd ');
                      } else {
                          return e;
                      }
                  }

              }, {
                  name: 'validateDate',
                  index: 'validateDate',
                  formatter:function (e){
                      if (e&&!isNaN(e)) {
                          return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                      } else {
                          return e;
                      }
                  }
              }, {
                  name: 'storageAmount',
                  index: 'storageAmount'
              }, {
                  name: 'storageConditions',
                  index: 'storageConditions'
              },{
                  name: 'appearance',
                  index: 'appearance',
                  width:'80',
                  formatter:function (e,rowType,rowData) {
                      var val = ''
                      if (e == '1') {
                          val =  '是'
                      } else {
                          val =  '否'
                      }
                      return val
                  }
              }, {
                  name: 'dehumidification',
                  index: 'dehumidification',
                  width:'80',
                  formatter:function (e,rowType,rowData) {
                      var val = ''
                      if (e == '1') {
                          val =  '是'
                      } else {
                          val =  '否'
                      }
                      return val
                  }
              }, {
                  name: 'humidification',
                  index: 'humidification',
                  width:'80',
                  formatter:function (e,rowType,rowData) {
                      var val = ''
                      if (e == '1') {
                          val =  '是'
                      } else {
                          val =  '否'
                      }
                      return val
                  }
              }, {
                  name: 'warming',
                  index: 'warming',
                  width:'80',
                  formatter:function (e,rowType,rowData) {
                      var val = ''
                      if (e == '1') {
                          val =  '是'
                      } else {
                          val =  '否'
                      }
                      return val
                  }
              }, {
                  name: 'cooling',
                  index: 'cooling',
                  width:'80',
                  formatter:function (e,rowType,rowData) {
                      var val = ''
                      if (e == '1') {
                          val =  '是'
                      } else {
                          val =  '否'
                      }
                      return val
                  }
              }, {
                  name: 'ventilate',
                  index: 'ventilate',
                  width:'80',
                  formatter:function (e,rowType,rowData) {
                      var val = ''
                      if (e == '1') {
                          val =  '是'
                      } else {
                          val =  '否'
                      }
                      return val
                  }
              }, {
                  name: 'checkStatus',
                  index: 'checkStatus',
                  formatter:function (e,rowType,rowData) {
                      if((e && e.indexOf(',') >= 0)||e===' '){
                          return ''
                      }else {
                          return e
                      }
                  }
              }, {
                  name: 'checkCause',
                  index: 'checkCause',
                  formatter:function (e,rowType,rowData) {
                      var val = ''
                      if (e == '1') {
                          val =  '是'
                      } else if (e == '2') {
                          val =  '否'
                      }
                      return val
                  }
              },{
                  name: 'amount',
                  index: 'amount',
              }, {
                  name: 'conclusion',
                  index: 'conclusion',
                  formatter:function (e,rowType,rowData) {
                      if(rowData.checkCause){
                          var options = ['', '待复查','继续销售'];
                          return options[rowData.checkCause]
                      }else {
                          return ''
                      }
                  }
              }, {
                  name: 'userName',
                  index: 'userName',
                  hidden:true,
                  hideGrid:true,
              }, {
                  name: 'scalerConclusion',
                  index: 'scalerConclusion',
                  hidden:true,
                  hideGrid:true,
              }, {
                  name: 'approvalNumber',
                  index: 'approvalNumber',
              }, {
                  name: 'remark',
                  index: 'remark',
              },{
                  name:'hidden_status',
                  index:'hidden_status',
                  hidden:true,
                  hidegrid:true
              },{
                  name:'drugClass',
                  index:'drugClass',
                  hidden:true
              }];
          }else if(status == '2'){
              //编辑
              $("#panel-title").text('编辑在库养护商品');
              $('#extract').hide();
          }
          $('#table_a').XGrid({
              url:'/proxy-gsp/gsp/stockInCheck/getStockDatail',
               postData:{
                   checkPlanCode:checkPlanCode,
                   orgCode:$("#orgCode").val()
               },
              colNames: colName,
              colModel: colModel,
              //rownumbers: true,
              selectandorder:true,
              key: 'id',
              rowNum: 5000,
              //   pager: '#grid-pager',
              altRows: true, //设置为交替行表格,默认为false
              onSelectRow: function (id, dom, obj, index, event) {
                  console.log('单机行事件', id, dom, obj, index, event);
              },
              ondblClickRow: function (id, dom, obj, index, event) {
                  console.log('双击行事件', id, dom, obj, index, event);
              },
              gridComplete: function () {
                  setTimeout(function () {
                      $('#table_a').on('click','td .checkbox input[type=checkbox],td select,td input[name=amount],td input[name=remark]',function (e) {
                          e.stopPropagation();
                      });
                  });
              }
          });
      }else {
          //新增
          $('#table_a').XGrid({
              data:[],
              colNames: colName,
              colModel: colModel,
              //rownumbers: true,
              selectandorder:true,
              key: 'id',
              rowNum: 9999,
              //   pager: '#grid-pager',
              altRows: true, //设置为交替行表格,默认为false
              onSelectRow: function (id, dom, obj, index, event) {
                  console.log('单机行事件', id, dom, obj, index, event);
              },
              ondblClickRow: function (id, dom, obj, index, event) {
                  console.log('双击行事件', id, dom, obj, index, event);
              },
              gridComplete: function () {
                  setTimeout(function () {
                      $('#table_a').on('click','td .checkbox input[type=checkbox],td select,td input[name=amount],td input[name=remark]',function (e) {
                          e.stopPropagation();
                      });
                  });
              }
          });
      }

    }


  /* 提取养护计划 */
  $('#extract').on('click', function () {
      var checkPlanCode = $("#checkPlanCode").val();
      var _tableData = $('#table_a').getRowData();
      if(_tableData.length == 0){
          checkPlanCode = '';
      }
      if(checkPlanCode == '' && _tableData.length != 0){
          utils.dialog({
              title: '提示',
              content: '养护计划不能重复提取',
              okValue: '确定',
              ok: function(){}
          }).showModal()
          return false;
      }
      if(checkPlanCode){
          utils.dialog({
              title:"提示",
              content:"养护计划不能重复提取",
              timeout:2000
          }).show();
      }else {
          utils.dialog({
              url: '/proxy-gsp/gsp/stockInCheck/toGetPlanVo',
              title: '养护计划',
              width: $(window).width() * 0.9,
              height: $(window).height() * 0.7,
              data: $('#orgCode').val(),
              onclose: function () {
                  if (this.returnValue) {
                      var return_data = this.returnValue;
                      console.log(return_data.code);
                      console.log(return_data.data);
                      $("#checkType").val(return_data.checkType);
                      $("#checkPlanCode").val(return_data.code);
                      $("#isZyyp").val(return_data.isZyyp);

                      /*colModel.push({
                          name:'hidden_status',
                          index:'hidden_status',
                          hidden:true,
                          formatter: function (e) {
                              return return_data.hidden_status
                          }
                      })
                      colName.push('隐藏列')
                       $('#table_a').setGridParam({
                          url: '/proxy-gsp/gsp/stockInCheck/getPlanVoDeatil',
                          postData: {
                              checkPlanCode:return_data.code,
                               orgCode:return_data.data,
                          },
                           colModel : colModel,
                       }).trigger('reloadGrid');*/
                      var msg_dialog = utils.dialog({
                          content:'正在提取养护计划...'
                      }).showModal();
                      $.ajax({
                          url: "/proxy-gsp/gsp/stockInCheck/getPlanVoDeatil",
                          data:{
                              checkPlanCode:return_data.code,
                              orgCode:return_data.data,
                          },
                          type: "get",
                          success: function (res) {
                              msg_dialog.close().remove();
                              console.log(res)
                              var table_data = res.result.list;
                              if(table_data&&table_data.length){
                                  $.each(table_data,function (index,item) {
                                      item.hidden_status = "1";
                                      $("#table_a").XGrid("addRowData",item)
                                  });
                              }
                          }
                      })

                  }
              }
          }).showModal();
          return false;
      }
  });

  /* 保存 */
  $('#saveData').on('click', function () {
      //字段长度校验
      if(!validform('table_form').form()) return;
    //var form_data = $('#form_a').serializeToJSON();
    var table_data = $('#table_a').XGrid('getRowData');
    var _arr = [];
    console.log(table_data);

    if(table_data&&table_data.length!==0){
        if(!table_data.length){
            table_data = [table_data];
        }
        var msg_ary = [];
        var msg_ary2 = [];
        table_data.forEach(function (item,index) {
            if(!item.repile&&!item.appearance&&!item.dehumidification&&!item.humidification&&!item.warming&&!item.cooling&&!item.ventilate&&!item.dedusting){
                msg_ary.push(index+1);
            }
            if(!item.checkCause||item.checkCause=="0"){
                msg_ary2.push(index+1);
            }
            _arr.push(getData(item));
        });
        if(msg_ary2.length>0){
            var msg_str2 = msg_ary2.join();
            utils.dialog({
                title:"提示",
                content:"<div style='max-width: 200px;max-height: 400px;word-break:break-all;overflow-y: auto;'>第"+msg_str2+"行请先选择是否异常</div>",
                timeout:3000
            }).show();
        }else if(msg_ary.length>0){
            var msg_str = msg_ary.join();
            utils.dialog({
                title:"提示",
                content:"<div style='max-width: 200px;max-height: 400px;word-break:break-all;overflow-y: auto;'>第"+msg_str+"行请先养护商品</div>",
                timeout:3000
            }).show();
        }else {
            //养护异常原因为空提示
            var checkStatusArr = [];
            table_data.forEach(function (item,index) {
                if(item.checkStatus == ''){
                    checkStatusArr.push(index+1)
                }
            })
            if(checkStatusArr.length > 0){
                utils.dialog({
                    title:'提示',
                    content:"<div style='max-width: 200px;max-height: 400px;word-break:break-all;overflow-y: auto;'>第"+checkStatusArr.join(',')+"行的养护原因不能为空.</div>",
                    timeout:3000
                }).show()
                return false;
            }
            //是否异常为空提示
            var checkCauseArr = [];
            table_data.forEach(function (item,index) {
                if(item.checkCause == 0){
                    checkCauseArr.push(index+1)
                }
            })
            if(checkCauseArr.length > 0){
                utils.dialog({
                    title:'提示',
                    content:"<div style='max-width: 200px;max-height: 400px;word-break:break-all;overflow-y: auto;'>第"+checkCauseArr.join(',')+"行的是否异常状态不能为空.</div>",
                    timeout:3000
                }).show();
                return false;
            }
            //实际养护数量 数字比较
            var accountArr = [];
            var isnumber = [];
            table_data.forEach(function (item,index) {
                if( item.amount == ''){
                    accountArr.push(index+1)
                }else{
                    if(isNaN(item.amount)){
                        isnumber.push(index+1)
                    }
                }

            })
            if(accountArr.length > 0){
                utils.dialog({
                    title:'提示',
                    content:"<div style='max-width: 200px;max-height: 400px;word-break:break-all;overflow-y: auto;'>第"+accountArr.join(',')+"行的实际养护数量不能为空.",
                    timeout:3000
                }).show();
                return false;
            }
            if(isnumber.length >0){
                utils.dialog({
                    title:'提示',
                    content:"<div style='max-width: 200px;max-height: 400px;word-break:break-all;overflow-y: auto;'>第"+isnumber.join(',')+"行的实际养护数量只能填写数字.",
                    timeout:3000
                }).show();
                return false;
            }

            table_data.forEach(function (item,index) {
                if( Number(item.amount)> Number(item.storageAmount)){
                    accountArr.push(index+1)
                }
            })
            if(accountArr.length > 0){
                utils.dialog({
                    title:'提示',
                    content:"<div style='max-width: 200px;max-height: 400px;word-break:break-all;overflow-y: auto;'>第"+accountArr.join(',')+"行的实际养护数量不能大于数量.",
                    timeout:3000
                }).show();
                return false;
            }
            var arr_str =  JSON.stringify(_arr);
            console.log(typeof  arr_str)
            var msg_dialogs = utils.dialog({
                content:'正在保存...'
            }).showModal();
            $.ajax({
                url:'/proxy-gsp/gsp/stockInCheck/addInStockCheckDetail',
                type:'post',
                data:{
                    inStockDetailVoData:arr_str,
                    type:1
                },
                success:function (res) {
                    if(res.code==1){
                        msg_dialogs.close().remove();
                        window.location ='/proxy-gsp/gsp/stockInCheck/toStockInCheck';
                    }else{
                        setTimeout(function () {
                            utils.dialog({content: res.msg, quickClose: true, timeout: 5000}).showModal();
                            msg_dialogs.close().remove();
                            window.location ='/proxy-gsp/gsp/stockInCheck/toStockInCheck';
                        },5000)
                    }
                },
                error:function (err) {
                    console.log('err')
                    msg_dialogs.close().remove();
                    window.location ='/proxy-gsp/gsp/stockInCheck/toStockInCheck';
                }
            })
        }
    }else {
        utils.dialog({
            title:"提示",
            content:"请先添加养护商品",
            timeout:2000
        }).show();
    }
  });

  $("#saveCaoGao").click(function(){

      utils.dialog({
          width: 200,
          title: '温馨提示',
          content: '保存草稿不会完成该养护单\n' +
          '\n' +
          '\n' +
          '\n' +
          '\n' +
          '您可进入“GSP管理->在库养护”中重新编辑\n',
          okValue: '确定',
          ok: function () {

              //字段长度校验
              if(!validform('table_form').form())
                  return;
              //var form_data = $('#form_a').serializeToJSON();
              var table_data = $('#table_a').XGrid('getRowData');
              var _arr =[];
              table_data.forEach(function (item,index) {
                  _arr.push(getData(item));
              });
              if(table_data&&table_data.length!==0){
                  if(!table_data.length){
                      table_data = [table_data];
                  }
                  var isnumber = [];
                  table_data.forEach(function (item,index) {
                      if(!item.amount == ''&& isNaN(item.amount)){
                          isnumber.push(index+1)
                      }
                  })
                  if(isnumber.length >0){
                      utils.dialog({
                          title:'提示',
                          content:"<div style='max-width: 200px;max-height: 400px;word-break:break-all;overflow-y: auto;'>第"+isnumber.join(',')+"行的实际养护数量只能填写数字.",
                          timeout:3000
                      }).show();
                      return false;
                  }

                  var arr_str =  JSON.stringify(_arr);
                  console.log(typeof  arr_str)
                  var msg_dialogs = utils.dialog({
                      content:'正在保存...'
                  }).showModal();
                  $.ajax({
                      url:'/proxy-gsp/gsp/stockInCheck/addInStockCheckDetail',
                      type:'post',
                      data:{
                          inStockDetailVoData:arr_str,
                          type:2
                      },
                      success:function (res) {
                          if(res.code==1){
                              msg_dialogs.close().remove();
                              window.location ='/proxy-gsp/gsp/stockInCheck/toStockInCheck';
                          }else{
                              setTimeout(function () {
                                  utils.dialog({content: res.msg, quickClose: true, timeout: 5000}).showModal();
                                  msg_dialogs.close().remove();
                                  window.location ='/proxy-gsp/gsp/stockInCheck/toStockInCheck';
                              },5000)
                          }
                      },
                      error:function (err) {
                          console.log('err')
                          msg_dialogs.close().remove();
                          window.location ='/proxy-gsp/gsp/stockInCheck/toStockInCheck';
                      }
                  })
              }else {
                  utils.dialog({
                      title:"提示",
                      content:"请先添加养护商品",
                      timeout:2000
                  }).show();
              }

          },
          cancelValue: '取消',
          cancel: function () {
          }
      }).showModal();


  })

  /* 返回 */
  $('#goback').on('click', function () {
      var status =  param.status;
      if(typeof(status)=="undefined" || status == 2){
          utils.dialog({
              title: '温馨提示',
              content: '返回后当前页面数据将丢失，是否继续',
              okValue: '确定',
              ok: function () {
                  window.history.back(-1);
              },
              cancelValue: '取消',
              cancel: function () {}
          }).show()
      }else{
          window.history.back(-1);
      }


  });

  /* 新增 */
  $('#addRoW').on('click', function () {
     var checkPlanCode = $("#checkPlanCode").val();
      /*   if(!checkPlanCode){
            utils.dialog({
                title:"提示",
                content:"请先提取养护计划",
                timeout:2000
            }).show();
        }else {*/
          utils.dialog({
              url: '/proxy-gsp/gsp/stockInCheck/tofindProductDetail',
              title: '商品列表',
              width: $(window).width() * 0.9,
              height: $(window).height() * 0.75,
              data: {
                  orgCode: $('#orgCode').val(),
                  isZyyp: $("#isZyyp").val(),
                  checkPlanCode: $("#checkPlanCode").val()
              },
              onclose: function () {
                  if (this.returnValue) {
                      var all_data = $("#table_a").XGrid("getRowData");
                      var data = this.returnValue;
                      var table_data = $.extend(data.data_a,data.data_b);
                      table_data.productBatch = table_data.batchNum;
                      table_data.wareHouse = table_data.storageTypeName;
                      table_data.storageAmount = table_data.amount;
                      table_data.id = (all_data.length != 0)? parseFloat(all_data[all_data.length-1].id) + 1:1;
                      delete table_data.amount;
                      var flag = all_data.some(function (item,index) {
                          return item.productCode==table_data.productCode&&item.productBatch==table_data.productBatch
                      });
                      if(!flag){
                          $("#table_a").XGrid("addRowData",table_data,"last");
                      }else {
                          utils.dialog({
                              title:"提示",
                              content:"相同商品编码和批号的商品不能重复新增",
                              timeout:2000
                          }).show();
                      }

                      console.log(data);
                  }
              },
              oniframeload: function () {

              }
          }).showModal();
          return false;
     // }
  });


    //删除

    /* 删除 */
  $('#removeRoW').on('click', function () {
    var data = $('#table_a').XGrid('getSeleRow');
    if (!data) {
      var d = utils.dialog({
        title: "提示",
        content: "请先选中一行数据",
          timeout:2000
      }).showModal();
      
      setTimeout(function () {
          d.close().remove()
      },2000)
      return
    }
    if(data.hidden_status == "1"){ // 已提取的养护计划 不允许删除
        var d = utils.dialog({
            title: "提示",
            content: "已提取的养护计划不允许删除",
            timeout:2000
        }).showModal();
        return
    }
      //删除二次确认
      utils.dialog({
          title: "提示",
          content: "确认删除当前选中行？",
          okValue: '确定',
          ok: function () {
              if (data.length) {
                  $.each(data, function (index, item) {
                      $('#table_a').XGrid('delRowData', item.id);
                  })
              } else {
                  $('#table_a').XGrid('delRowData', data.id);
              }
              $.ajax({
                  url:'/proxy-gsp/gsp/stockInCheck/deleteStockInCheckDetail',
                  type:'get',
                  data:{
                      id:data.id
                  },
                  success:function (res) {
                      console.log(res)
                      var d = utils.dialog({
                          title: "提示",
                          content: "删除成功。",
                          timeout:2000
                      }).showModal();
                      setTimeout(function () {
                          d.close().remove();
                      },3000)
                  },
                  error:function (err) {
                      console.log('err')
                  }
              })
          },
          cancelValue: '取消',
          cancel: function () {}
      }).showModal();
  });
})

function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}

    function   getData(item){

        var _obj = {};
        _obj.id = item.id;
        _obj.productCode = item.productCode;
        _obj.productName = item.productName;
        _obj.amount = item.amount;
        _obj.productBatch = item.productBatch;
        _obj.wareHouse = item.wareHouse;
        _obj.repile = item.repile;
        _obj.appearance = item.appearance;
        _obj.dehumidification = item.dehumidification;
        _obj.humidification = item.humidification;
        _obj.warming = item.warming;
        _obj.cooling = item.cooling;
        _obj.ventilate = item.ventilate;
        _obj.dedusting = item.dedusting;
        _obj.checkCause = item.checkCause;
        _obj.checkStatus = item.checkStatus;
        _obj.remark = item.remark;
        _obj.checkPlanCode =$("#checkPlanCode").val();
        _obj.checkType =$("#checkType").val();
        _obj.isZyyp =$("#isZyyp").val();
        _obj.productName = item.productName;
        _obj.specifications = item.specifications;
        _obj.manufacturerVal = item.manufacturerVal;
        _obj.producingArea = item.producingArea;
        _obj.packingUnitVal = item.packingUnitVal;
        _obj.dosageFormVal = item.dosageFormVal;
        _obj.productDates = item.productDate;
        _obj.validateDates = item.validateDate;
        _obj.storageConditions = item.storageConditions;
        _obj.approvalNumber = item.approvalNumber;
        _obj.drugClass = item.drugClass;
        _obj.storageAmount = item.storageAmount;
        //假数据
        //  _obj.checkPlanCode = 'adasdsa';
        _obj.wareHouseCode = '1212';
        //  _obj.indeterminacy_size = 11;
        _obj.conclusion = 1010;
        //  _obj.scaler_userid = ;
        //  _obj.scaler_conclusion = '';
        _obj.source = 1212;
        _obj.chanplandetailid = 2131;
        //  _obj.check_type = 545;
        return _obj;

    }