.panel-default {
    background: #f5f5f5;
}

.page-header {
    padding: 0;
    margin: 0;
}

.page-header h1 {
    font-size: 30px;
    margin: 15px;
}

.empty {
    width: 100%;
    height: 34px;
    background-color: #fff;
}

.container {
    width: 100%;
    height: 100%;
    background-color: rgba(242, 242, 242, 1);
}

.container .form-group {
    width: 500px;
    margin: 50px auto;
}

.container .col-sm-2 {
    line-height: 35px;
    font-weight: normal;
    margin-bottom: 0;
    text-align: center;
}

.permissionsTree {
    width: 1500px;
    height: 600px;
    margin: 150px auto;
    border: 1px solid rgba(121, 121, 121, 1);
    border-top: none;
    border-radius: 12px;
    background: #fff;
}

.treeName {
    height: 45px;
    line-height: 45px;
    text-indent: 18px;
    background: inherit;
    background-color: rgba(153, 153, 153, 1);
    border: none;
    border-radius: 11px;
}

.permissionsTree .ztree {
    margin: 50px 0 0 80px;
}