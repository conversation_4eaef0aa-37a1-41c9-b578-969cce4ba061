.panel-default {
    background: #f5f5f5;
}

.page-header {
    padding: 0;
    margin: 0;
}

.page-header h1 {
    font-size: 30px;
    margin: 15px;
}

.empty {
    width: 100%;
    height: 34px;
    background-color: #fff;
}

.container {
    width: 100%;
    height: 100%;
    background-color: rgba(242, 242, 242, 1);
}

.cont-left {
    width: 60%;
    height: 367px;
    margin: 30px 40px 200px;
    background: inherit;
    background-color: rgba(255, 255, 255, 1);
    box-sizing: border-box;
    border: 1px solid rgba(121, 121, 121, 1);
    border-radius: 0px;
    float: left;
}

.cont-left ul {
    height: 37px;
    list-style: none;
    background: rgba(215, 215, 215, 1);
    margin: 0;
    line-height: 37px;
}

.cont-left ul li {
    border-right: 1px solid rgba(121, 121, 121, 1);
    text-align: center;
}

.cont-left ul li:last-child {
    border-right: none;
}

.cont-left .cont-title {
    height: 34px;
    margin: 0;
    line-height: 35px;
    border-bottom: 1px solid rgba(121, 121, 121, 1);
}

.cont-left .cont-review {
    margin: 0;
    line-height: 35px;
}

.cont-right {
    width: 33%;
    float: left;
    margin-top: 30px;
}

.cont-r-title {
    height: 37px;
    line-height: 37px;
    background: rgba(215, 215, 215, 1);
    margin-bottom: 0;
    text-indent: 18px;
}

.cont-right ul {
    height: 330px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(121, 121, 121, 1);
    border-top: none;
    list-style: none;
    padding: 10px 10px 0;
}

.cont-right ul li {
    height: 40px;
    line-height: 40px;
    margin-bottom: 10px;
    background: rgba(242, 242, 242, 1);
    overflow: hidden;
}

.cont-right ul li span {
    color: #169BD5;
    float: right;
    margin-right: 30px;
}