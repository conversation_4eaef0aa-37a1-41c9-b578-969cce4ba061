//设置已选商品
const dialog = parent.dialog.get(window);
$("#selectedProductIds").val(dialog.data.selectArr.join(','));
const count = dialog.data.count
const isEdit = dialog.data.isEdit;
var orgCode = $("#orgCode").val();
$('#X_Tableb').XGrid({
	url:"/proxy-product/product/productOrga/query?&orgCode="+orgCode+"&limitedPinState=0&limitedProductionState=0&disableState=0",
	colNames: ['商品编号', '商品名', '通用名', '规格', '生产厂家', '批准文号', '包装单位', '剂型', '小包装条码'],
	colModel: [
		{
			name: 'productCode',
			index: 'productCode',
			width: 200
		}, {
			name: 'productName',
			index: 'productName',
			width: 60
		}, {
			name: 'commonName',
			index: 'commonName',
			width: 150
		}, {
			name: 'specifications',
			index: 'specifications',
			width: 250
		}, {
			name: 'manufacturerValue',
			index: 'manufacturerValue',
			width: 250
		}, {
			name: 'approvalNumber',
			index: 'approvalNumber',
			width: 250
		}, {
			name: 'packingUnitValue',
			index: 'packingUnitValue',
			width: 250
		}, {
			name: 'dosageFormValue',
			index: 'dosageFormValue',
			width: 250
		}, {
			name: 'smallPackageBarCode',
			index: 'smallPackageBarCode',
			width: 250
		}
	],
	rowNum: 20,
	rowList:[20,50,100],
	multiselect: true,
	altRows: true,//设置为交替行表格,默认为false
	pager: '#grid-pager',
    postData: {
        "operatingCustomers": 0
    },
	onSelectRow: function (id, dom, obj, index, event) {
        if (isEdit != '0') {
            //添加已选商品
            var $tr = $("#X_Tableb tr#"+id);
            var check=$tr.find('[row-describedby="ck"] input').prop("checked");
            getCheckData(check,id,obj);
        }
	},
	gridComplete: function () {
		setTimeout(function () {
            $("#X_Tableb th:first input").hide();
            if(dialog.data.selRowData.length > 0){
                var arr= ( count == 0) ? dialog.data.selectedProductIds : dialog.data.selRowData;
                for(var i=0;i<arr.length;i++){
                    $('#X_Tableb tr').not(':first').each(function () { // $('#X_Tableb tr:not("first")')
                        var id= $(this).attr("id");
                        var _id = ( count == 0) ? arr[i]['id'] : arr[i]['productCode']
                        if(id == _id){
                            $(this).find("td[row-describedby='ck'] input").prop("checked",true).trigger('input');
                        }
                    })
                }
            }
            // setTableData($("#X_Tableb").XGrid('getSeleRow') instanceof Array ? $("#X_Tableb").XGrid('getSeleRow') : $("#X_Tableb").XGrid('getSeleRow') ? [$("#X_Tableb").XGrid('getSeleRow')] : []);
            if (isEdit != '1') {
                // $('input, button, select').prop('disabled', true)
                $('#isEditHTML form input,#isEditHTML form button,#isEditHTML form select').prop('disabled', true)
                $('#X_Tableb input, #X_Tableb button,  #X_Tableb select').prop('disabled', true)
                $('.btnDelete').css('display','none')
            }
        },1)
	}
});
//添加已选商品
$('#X_Tableb').on("change","td[row-describedby='ck'] input",function(ev){
    var check=this.checked;
    var $tr = $(this).parents('tr');
    var id=$tr.attr('id');
    var data=$('#X_Tableb').XGrid('getRowData', id);
    getCheckData(check,id,data);
    ev.stopPropagation();
})
$('body').on('change', '#X_Tableb #grid_checked', function (ev) {
    var check=$(this).find('input').prop('checked');
    var data=$('#X_Tableb').XGrid('getRowData');
    let selData = dialog.data.selectedProductIds
    // let selData=$('#selected_Tableb').XGrid('getRowData');
    if (check) {
        let _ids = data.map(item => Number(item['id']))
        let _data = selData.filter(item => {
            return _ids.indexOf(Number(item['id'])) < 0 }
        )
        data = data.concat(_data)
    } else {
        let _ids = data.map(item => Number(item['id']))
        let _data = selData.filter(item => {
            return _ids.indexOf(Number(item['id'])) < 0 }
        )
        console.log('_data', _data);
        data = _data
    }
    console.log('data', data);

    // let obj = {}
    // data = data.reduce((cur,next) => {
    //     obj[next.id] ? "" : obj[next.id] = true && cur.push(next);
    //     return cur;
    // },[])
    dialog.data.selectedProductIds = data
    $('#selected_Tableb').XGrid('clearGridData')
    $('#selected_Tableb').XGrid('setGridParam', {
        data
    }).trigger('reloadGrid');
    // for (let i = 0; i<data.length; i++) {
    //     getCheckData(check,data[i]['id'],data[i]);
    // }
    ev.stopPropagation();
})

function getCheckData(check,id,data){
    let selDataIds =  $('#selected_Tableb').XGrid('getRowData').map(item => item['id'])
    if(check) {
        if (selDataIds.indexOf(id) < 0) {
            $('#selected_Tableb').XGrid('addRowData', data);
        }
    }else{
        $('#selected_Tableb').XGrid('delRowData', id);
    }
}
//删除按钮事件
$('#selected_Tableb').on('click', '.btnDelete', function (e) {
    var $tr = $(this).parents('tr');
    if ($tr.length && $tr.attr('id')) {
        $('#X_Tableb #'+$tr.attr('id')).find("td[row-describedby='ck'] input").prop('checked',false).trigger('input');
        $('#selected_Tableb').XGrid('delRowData', $tr.attr('id'));
    }
    dialog.data.selectedProductIds = dialog.data.selectedProductIds.filter(item => Number(item['id']) != Number($tr.attr('id')))
    e.stopPropagation();

})
//已选择商品列表
var selectedProductIds =  $("#selectedProductIds").val();//已选择商品列表id
var xGridParam={};
if(0 == count){
    xGridParam.data= dialog.data.selectedProductIds;
}else{
    xGridParam.data = dialog.data.selRowData
    // xGridParam.url="/proxy-product/product/productOrga/query?ids="+selectedProductIds;
}
var options={
    colNames: ['商品编号', '商品名', '通用名', '规格', '生产厂家', '批准文号', '包装单位', '剂型', '小包装条码',''],
    colModel: [
        {
            name: 'productCode',
            index: 'productCode',
            width: 200
        }, {
            name: 'productName',
            index: 'productName',
            width: 60
        }, {
            name: 'commonName',
            index: 'commonName',
            width: 150
        }, {
            name: 'specifications',
            index: 'specifications',
            width: 250
        }, {
            name: 'manufacturerValue',
            index: 'manufacturerValue',
            width: 250
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width: 250
        }, {
            name: 'packingUnitValue',
            index: 'packingUnitValue',
            width: 250
        }, {
            name: 'dosageFormValue',
            index: 'dosageFormValue',
            width: 250
        }, {
            name: 'smallPackageBarCode',
            index: 'smallPackageBarCode',
            width: 250
        }, {
            name: 'Operation',
            index: 'Operation',
            rowtype: '#Operation'
        }
    ],
    rowNum: 9999,
    altRows: true,//设置为交替行表格,默认为false
    /*rownumbers: true,*/
    gridComplete: function () {
        setTimeout(function () {
            // setTableData(dialog.data.resultArr)
            // setTableData(dialog.data.selRowData)

            if (isEdit != '1') {
                //$('input, button, select').prop('disabled', true)
                $('#X_Tableb input, #X_Tableb button,  #X_Tableb select').prop('disabled', true)
                $('.btnDelete').css('display','none')
            }
        },200)
    }
};
function setTableData(resultArr){
    if(resultArr && resultArr.length>0){
        var arr = resultArr;
        for(var i=0;i<arr.length;i++){
            var d=$('#selected_Tableb').getRowData();
            if(!fidInArr(d,arr[i].id)){
                var json=arr[i];
                $('#selected_Tableb').addRowData(json);
            }
        }
    }
}
options=Object.assign(xGridParam,options);
setTimeout(function () {
    $('#selected_Tableb').XGrid(options);
}, 200)
function btn_choose() {
    dialog.close($('#selected_Tableb').XGrid('getRowData'))
}
//数组中查找id
function fidInArr(arr,id){
	for(var i=0;i<arr.length;i++)
	{
		if(typeof arr[i] == 'object')
		{
			if(arr[i].id == id)
			{
				return true;
			}
		}else{
			if(arr[i] == id)
			{
				return true;
			}
		}
	}
	return false;
}
$("#SearchBtn").on("click", function () {
	$('#X_Tableb').XGrid('setGridParam', {
		postData: {
			"productCode": $("#productCode").val(),
			"approvalNumber":$("#approvalNumber").val(),
			"smallPackageBarCode":$("#smallPackageBarCode").val(),
			"manufacturerName":$("#manufacturerName").val()

		},page:1
	}).trigger('reloadGrid');
    // if('' == selectedProductIds){
    //     $('#selected_Tableb').XGrid('setGridParam', {
    //         postData: {},page:1
    //     }).trigger('reloadGrid');
    // }else{
    //     $('#selected_Tableb').XGrid('setGridParam', {
    //         url:"/proxy-product/product/productOrga/query?ids="+selectedProductIds,
    //         postData: {},page:1
    //     }).trigger('reloadGrid');
    // }

});

