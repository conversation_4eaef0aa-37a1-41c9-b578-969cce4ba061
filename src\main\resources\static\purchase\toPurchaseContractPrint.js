var getData;
$(function () {
    /* 日期格式化 */
    //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
    function dateFormat(time, format) {
        var t = new Date(parseInt(time,10));
        var tf = function (i) {
            return (i < 10 ? '0' : '') + i;
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        });
    }



    /* 获取数据 */
    getData = function (printType,orderNo) {
        $.ajax({
            url:"/proxy-purchase/purchase/purchaseOrderProduct/purchaseOrderPrint",
            data:{
                orderNo :  orderNo
            },
            success:function(res){
                if(res.result && $.isArray(res.result.purchaseOrderProductVos)){
                    /* 重新拼装数据 */
                    var data_list = res.result.purchaseOrderProductVos;
                    var list = [], page_size = 25;
                    var list_length = Math.ceil(data_list.length/page_size);
                    for (var i = 0; i < list_length; i++) {
                        list.push(data_list.slice(i*page_size,(i+1)*page_size));
                    }
                    webRender(res.result,list,printType,page_size,list_length);
                }else {
                    utils.dialog({
                        title:'提示',
                        content:"数据异常",
                        ok:function () {}
                    }).showModal();
                }
            },
            error:function(){
                utils.dialog({
                    title:'提示',
                    content:"打印失败 ",
                    ok:function () {}
                }).showModal();
            }
        })
    }

    var excludeSpecial = function(s) {
        // 去掉转义字符
        s = s.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
        // 去掉特殊字符
        s = s.replace(/\<|\>|\"|\'|\&/g,'');
        return s;
    };

    /* 数据渲染 */
    function webRender(data,list,printType,pageSize,total) {
        var box_html = `
        <div class="header">订单号：<span class="h_num">${data.orderNo}</span></div>
        <h3 class="title">购 销 合 同</h3>
        <div class="content">
            <div class="con-item1" style="font-family: Arial, sans-serif; font-weight: bold;">甲方（供方、卖方）：<span class="con-item1-val" >${data.supplierName}</span></div>
            <div class="con-item1" style="font-family: Arial, sans-serif; font-weight: bold;">乙方（需方、买方）：<span class="con-item1-val" >${data.sysOrgName}</span></div>
            <div class="con-item2" style="font-family: Arial, sans-serif; font-weight: bold;">依据《中华人民共和国民法典》、《中华人民共和国药品管理法》及相关法律法规之规定，本着自愿、平等、互利原则，甲乙双方就以下商品的供销事宜，达成如下条款：</div>
            <div class="con-item2">一、合同标的（品名、数量、价款）（如种类较多，可将下列表格作为本订单附件）</div>`
        list.forEach(function (item,index) {
                box_html +=`
                <div class="indent1">
                    <table id="table_a_${index}"></table>
                </div>
                `;
            });
        box_html+=`
            <div class="con-item2">二、质量：甲方提供给乙方的商品质量符合国家药品标准、相关法律法规的要求，确保药品按规定赋追溯码，发往我司时已按规定核注核销，且赋码品种未被医保结算过，及甲乙双方已经签订的《质量保证协议》的要求。<span style="font-family: Arial, sans-serif; font-weight: bold;">甲方承担商品因质量问题、包装不符合规定、不合法（如回流药）、未按要求赋追溯码及其他违法违约情形而给乙方造成的一切损失，并负责对以上商品进行退换货。</span></div>
            <div class="con-item2">三、运输方式：□火车，    □汽车，     □其他：【<span class="con-item2-val"> &nbsp;  </span>】。</div>
            <div class="con-item2">四、运费承担：由甲方承担。</div>
            <div class="con-item2">五、交货地点：乙方仓库注册地。</div>
            <div class="con-item2 boxClearfix"><span style="float:left">六、到货时间：</span><div style="float:left">（1）&nbsp;本合同签订后  □ 3天   □ 7天   □ 10天  。<br>（2）乙方支付货款后  □ 3天   □ 7天   □ 10天  。</div><span> </span></div>
            <div class="con-item2">七、结算方式：□电子承兑，          □银行转账，        □其他：【<span class="con-item2-val"> &nbsp;  </span>】</div>
            <div class="con-item2 boxClearfix"><span style="float:left">八、付款时间：</span><div style="float:left">□款到发货（甲方必须是开具增值税专票的一般纳税人）<br>□货到付款，    □月结，     □实销实结，    □送二结一，    □其他：【<span class="con-item2-val"> &nbsp;  </span>】</div><span> </span></div>
            <div class="con-item2">乙方通过邮件或邮寄等形式发送给甲方的货物及奖励金的对账单，甲方应积极配合，乙方根据甲方盖财务专用章的对账单和甲方开具的增值税专用发票安排付款。</div>
            <div class="con-item2">九、发票：甲方根据双方核对一致的商品信息按照国家规定给乙方开具增值税专用发票。发票内容应包括药品通用名称、规格、单位、数量、单价、金额等，不能全部列明的，应当附税进入税控系统打印的《销货清单》，并加盖发票专用章。</div>
            <div class="con-item2">□货票同行  □乙方收到发票后7个工作日支付货款  □先款后货方式的，甲方在乙方收货后5个工作日内开具发票。</div>
            <div class="con-item2">十、收货及验收条款：乙方在收货后5个工作日内检查收到的商品是否与甲方的销售出库单相符。如发生商品件数短少、破损等，乙方有权拒收，甲方应在乙方指定时间内补齐缺失、破损的商品，由此产生的一切费用，均有甲方承担；乙方在收货的同时应当在随货同行的甲方销售出库单据上盖章确认，并将单据交给甲方。</div>
            <div class="con-item2" style="font-family: Arial, sans-serif; font-weight: bold;">十一、甲方同意乙方有权向甲方退货，甲方应当自收到乙方退货通知之日起五天内从乙方取回退货商品，并为乙方办理完毕退货手续，否则自退货通知期限届满时起视同乙方已经向甲方完成退货。同时，甲方应当向乙方返还全部退货款，或赔偿货款损失。</div>
            <div class="con-item2" style="font-family: Arial, sans-serif; font-weight: bold;">十二、廉政合规条款：甲乙双方应遵守所有适用的反贿赂、反腐败方面的法律、法规和规定。任何一方不得在日常业务活动中暗中或私下以现金、礼品、服务等形式向对方工作人员支付回扣、折扣等不当的经济利益，否则，视为商业贿赂，守约方将依法追究违约方的全部法律和经济责任。任何一方不得将合同款项划转至个人银行账户，否则视同未支付。</div>
            <div class="con-item2" style="font-family: Arial, sans-serif; font-weight: bold;">十三、违约责任：一方未按本合同约定及时履行义务，且经另一方书面催告后30天内仍不履行的，守约方有权立即解除本合同。若甲方未按照本合同约定时间交货的，每延迟一日，按照合同金额的千分之三向乙方支付违约金，延迟超过5天的，乙方有权解除本合同，并要求甲方按照合同金额的百分之二十支付违约金。</div>
            <div class="con-item2" style="font-family: Arial, sans-serif; font-weight: bold;">十四、争议解决：本合同在履行过程中发生争议，由双方当事人协商解决，协商不成，任一方可向合同签订地人民法院提起诉讼。</div>
            <div class="con-item2">十五、保证：合同双方均保证其为合法设立并有效存续的主体，有权签订并有能力履行本合同，其已获得有关的政府审批（如需）和/或其他必要的授权。如资质证件或其他资料、信息发生变更的，需在5个工作日内告知对方。如因提供的相关资质证件、资料、信息等不合法、不真实或失效造成的一切损失及法律责任全部由责任方承担，另一方因此被有关部门处罚承担连带责任或者遭受损失的，有权向责任方追责。</div>
            <div class="con-item2">十六、本合同自双方盖章之日起生效，一式肆份，双方各执贰份，具有同等法律效力。本合同签订地点：湖北省黄石市铁山区黄金山开发区百花路10号。本合同未约定的内容，按照双方已签订的《年度购销合同》执行。</div>
            <div class="con-item2">（以下无正文）</div>
            <div class="signature">
              <div  class="signature-item">
                <div style="margin-right:15px;flex: 1;">
                  <div class="s-item">甲方（盖章）：${data.supplierName||''}</div>
                  <div class="s-item">授权代表人（签字）：${data.amandatary||'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'}</div>
                  <div class="s-item">日期：</div>
                </div>
                <div style="flex: 1;">
                  <div class="s-item">乙方（盖章）：${data.sysOrgName||''}</div>
                  <div class="s-item">授权代表人（签字）：</div>
                  <div class="s-item">日期：</div>
                </div>
              </div>

            </div>
        </div>
        <div style="page-break-after:always"></div>
        `;
        // <div class="footer">1/${total+1}</div>
        // <div class="header">订单号：<span class="h_num">${data.orderNo}</span></div>
        // ${index===0 ? '<div class="table_header">附件一：合同标的（品名、数量、价款）</div>' : ''}
        // <div class="footer">${index + 2}/${total+1}</div>
        // <div style="page-break-after:always"></div>
        /* 基本结构拼装 */
    //     <div class="signature-item">
    //     <div class="s-item">甲方：${data.supplierName||''}</div>
    //     <div class="s-item">地址：${data.aaddress||''}</div>
    //     <div class="s-item">法定代表人：${data.alegalRepresentative||'&nbsp;&nbsp;&nbsp;&nbsp;'} &nbsp;&nbsp; 签约代表：${data.amandatary||'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'}</div>
    //     <div class="s-item">电话：${data.amandataryPhone||'&nbsp;&nbsp;&nbsp;&nbsp;'}</div>
    //     <div class="s-item">传真：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 邮编：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
    //     <div class="s-item">开户银行：${data.abankName||''}</div>
    //     <div class="s-item">账号：${data.abankAccount||''}</div>
    //     <div class="s-item">税号：${data.ataxRegistrationCode||''}</div>
    // </div>
    // <div class="signature-item">
    //     <div class="s-item">乙方：${data.sysOrgName||''}（盖章）</div>
    //     <div class="s-item">地址：${data.baddress||''}</div>
    //     <div class="s-item">法定代表人：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 签约代表：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
    //     <div class="s-item">电话：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
    //     <div class="s-item">传真：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 邮编：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
    //     <div class="s-item">开户银行：${data.bbankName||''}</div>
    //     <div class="s-item">账号：${data.bbankAccount||''}</div>
    //     <div class="s-item">税号：${data.btaxRegistrationCode||''}</div>
    // </div>
    // <!--<div class="signature-item">
    //     <div class="s-item">乙方：  系统自动填写    小药药医药科技有限公司（盖章）</div>
    //     <div class="s-item">地址：系统自动填写</div>
    //     <div class="s-item">法定代表人:      签约代表：</div>
    //     <div class="s-item">电话：</div>
    //     <div class="s-item">传真：            邮编：</div>
    //     <div class="s-item">开户银行：系统自动填写</div>
    //     <div class="s-item">账号：系统自动填写</div>
    //     <div class="s-item">税号：系统自动填写</div>
    // </div>-->

        $("#box").html(box_html);

        /* 表格初始化 */
        list.forEach(function (item,index) {
            $("#table_a_"+index).jqGrid({
                data: item,
                datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                height: "auto", //高度，表格高度。可为数值、百分比或'auto'

                colNames: ['品名', '生产厂商', '规格', '单位','数量', '单价', '金额','备注(批号/有效期)','行标识'],
                colModel: [
                    {
                        name: 'productName',
                        index: 'productName',
                        width: 180,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rawObject.tableRowName) {
                                return 'colspan=9 style="text-align: left;padding:0 10px;"'
                            };
                        }
                    }, {
                        name: 'productProduceFactory',
                        index: 'productProduceFactory',
                        width: 180
                    }, {
                        name: 'productSpecification',
                        index: 'productSpecification',
                        width: 130
                    },{
                        name: 'productPackUnitSmall',
                        index: 'productPackUnitSmall',
                        width: 60
                    }, {
                        name: 'productPackCountSmall',
                        index: 'productPackCountSmall',
                        width: 80
                    },   {
                        name: 'productContainTaxPrice',
                        index: 'productContainTaxPrice',
                        width: 110
                    },{
                        name: 'productContainTaxMoney',
                        index: 'productContainTaxMoney',
                        width: 140
                    },  
                    // {
                    //     name: 'productApprovalNumberExpireDate',
                    //     index: 'productApprovalNumberExpireDate',
                    //     width: 90,
                    //     formatter:function(){ return '' }
                    // }, 
                    {
                        name: 'productRemark',
                        index: 'productRemark',
                        width: 120,
                        formatter:function(){ return '' }
                    }, {
                        name: 'tableRowName',
                        index: 'tableRowName',
                        hidden: true
                    }
                ],
                shrinkToFit: true,
                rowNum: pageSize,
                gridview: true,
                gridComplete: function () {
                    var rowData = $(this).getRowData();
                    if((index+1)===total && !rowData[rowData.length-1].tableRowName) {
                        $(this).addRowData(999999, {
                            productName: "合计金额（大写）："+ data.orderPriiceTaxSumChineseNum +"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ￥："+ data.orderPriceTaxSum,
                            tableRowName: "sum"
                        }, "last");
                    }
                }
            });
        });


        if(printType==0){
            /* 打印预览 */
            utils.dialog({
                title:'预览',
                content:$('#big_box').html()
            }).showModal();
            //$("#print_box").contents().find(".ui-dialog-content").css("overflow","auto");
            window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
        }else if(printType==1){
            /* 打印 */
            $("#box").jqprint({
                globalStyles: true, //是否包含父文档的样式，默认为true
                mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                stylesheet: null, //外部样式表的URL地址，默认为null
                noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                append: null, //将内容添加到打印内容的后面
                prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                deferred: $.Deferred() //回调函数
            });
        }
    }

    // getData(1,"CGD2004006004");
});
