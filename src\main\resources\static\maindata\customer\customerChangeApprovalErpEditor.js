var baseId =  $("#baseId").val(); //主数据id
var approvalId =  $("#approvalId").val(); //申请id
var pageType = $("#pageType").val();
var processId = $("#processId").val();
var taskId = $("#taskId").val();
var approvalProcessId = $("#approvalProcessId").val();
var detailAllData={};

//经营类别
valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryClient",{paramName:'clientName', params:{"type":1}},"businessCategory",{data:"clientId",value:"clientName"});
//客户类别
valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryClient",{paramName:'clientName', params:{"type":2}},"customerType",{data:"clientId",value:"clientName"});
//经营方式
valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryClient",{paramName:'clientName', params:{"type":3}},"operationMode",{data:"clientId",value:"clientName"});

/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            if (obj == 'customerType') {
                response.result = response.result.filter(item => item.clientName != '基层医疗机构')
                return response
            }else{
                return response
            }
        },
        onSelect: function (result) {
            $("#"+obj).val(result.data);
            if(obj == "operationMode" && $('input:radio[name="threeInOne"]:checked').val()=="1"){
                if(result.value == "非营利性"){
                    var businessLicenseNum = $(".businessLicenseNum").val();
                    $(".businessLicenseNum").attr("disabled", "disabled")//组织机构代码号
                    $(".organizationCodeNumber").attr("readonly", true)//组织机构代码号
                    $(".taxRegistryNumber").attr("readonly", true)//税务登记证号
                    $(".organizationCodeNumber").val("")//组织机构代码号
                    $(".taxRegistryNumber").val("")//税务登记证号
                    $(".businessLicenseNum").val("")//税务登记证号
                    $(".taxpayerNum").val("") //纳税人识别号
                }else{
                    if($(".businessLicenseNum").prop("disabled")){
                        $(".businessLicenseNum").removeAttr("disabled");
                    }
                }
            }

            select && select(result)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            $("#"+obj).val("");
            $("#"+obj+"Val").val("");
            if(obj == "operationMode" && $('input:radio[name="threeInOne"]:checked').val()=="1"){
                console.log($(".threeEvidenceAll").val()+"aaa");
                var businessLicenseNum = $(".businessLicenseNum").val();
                $(".organizationCodeNumber").attr("readonly", true)//组织机构代码号
                $(".taxRegistryNumber").attr("readonly", true)//税务登记证号
                $(".organizationCodeNumber").val(businessLicenseNum)//组织机构代码号
                $(".taxRegistryNumber").val(businessLicenseNum)//税务登记证号
                $(".taxpayerNum").val(businessLicenseNum) //纳税人识别号
            }
            noneSelect && noneSelect();
        }
    });
}
//批准文件,质量保障协议,客户委托书,被委托人 一键下载
function patchDownload(tableId){
    downloadTableAttachFiles(tableId)
}
function downloadTableAttachFiles(tableId){
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$(tableId);
    var rowData=$table.getRowData();
    console.log(rowData)
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++)
    {
        var sel=$tr.eq(i).find("select[name='credentialTypeId'] option:selected");
        typeList.push({
            text:sel.text(),
            value:sel.val()
        });
        console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0)
        {
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
            {
                rowData[i].customerEnclosureVoList[j].type=sel.val();
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    console.log(eChoImgList)
    // downloadImg("/static/MobileToken.png") //同源图片，下载有效
    const fileUrls = []
    const fileNames = []
    eChoImgList.forEach((item,index)=>{
        if (item.url && item.url.length) {
            fileUrls.push(item.url)
            let fileName = item.enclosureName
            if (!fileName){
                fileName = index + ''
            }
            fileNames.push(fileName)
        }
    })
    downloadImg(fileUrls,fileNames)
}
//客户合同 一键下载
// TODO 待验证
// function qtfjPatchDownload() {
//     let eChoImgList = []
//     var checkInp=$("#contractType input[type='checkbox']:checked");
//     if(checkInp.length)
//     {
//         for(var i=0;i<checkInp.length;i++)
//         {
//             var type=checkInp.eq(i).val();
//             var inp=$("#contractType"+type);
//             if(inp.length)
//             {
//                 var imgArr=JSON.parse(inp.val());
//                 eChoImgList=eChoImgList.concat(imgArr);
//             }
//         }
//     }
//     const fileUrls = []
//     const fileNames = []
//     eChoImgList.forEach((item,index)=>{
//         if (item.url && item.url.length) {
//             fileUrls.push(item.url)
//             let fileName = item.enclosureName
//             if (!fileName){
//                 fileName = index + ''
//             }
//             fileNames.push(fileName)
//         }
//     })
//     downloadImg(fileUrls,fileNames)
// }
function downloadImg(fileUrls,fileNames){
    if (!(fileUrls.length * fileNames.length)){
        utils.dialog({content: '暂无附件下载', quickClose: true, timeout: 2000}).showModal();
        return
    }
    window.open("/proxy-customer/customer/customerFirstAppl/downloadZip?fileUrls="+fileUrls+"&fileNames="+fileNames+"&zipFileName=导出")
}

// 设置页面所有属性不可编辑
function disable(){
    $("input[type='text']").attr("disabled","disabled");
    $("input[type='tel']").attr("disabled","disabled");
    $("input[type='number']").attr("disabled","disabled");
    $("input[type='checkbox']").attr("disabled","disabled");
    $("input[type='radio']").attr("disabled","disabled");
    $("textarea").attr("disabled","disabled");
    $("select").attr("disabled","disabled");
    $(".addbill").hide();
    $(".removeDepot").hide();
    $(".rowBtn").attr("disabled","disabled");
    $(".ApprovalUploadeImg").attr("disabled","disabled");
}
// 设置页面所有属性可读取
function able(){
    $("input[type='text']").removeAttr("disabled");
    $("input[type='tel']").removeAttr("disabled");
    $("input[type='number']").removeAttr("disabled");
    $("input[type='checkbox']").removeAttr("disabled");
    $("input[type='radio']").removeAttr("disabled");
    $("textarea").removeAttr("disabled");
    $("select").removeAttr("disabled");
    $("#addbill").show();
    $(".removeDepot").show();
}
disable();
// //添加账单
// $("#billAddress").on("click",".addbill", function () {
//     if($("select[name^='billingProvinceId']").length >= 2){
//         utils.dialog({content: '发票邮寄地址最多只能填两个。', quickClose: true, timeout: 2000}).showModal();
//         return false;
//     }else{
//         let obj = distpickerHTML(1,1);
//         $("#billAddress").append(obj.html);
//         let billAddressSelIdObj = [
//             {nextNodeWrap: '#billProvinceSel_wrap_' +  obj.radomInit[0],nextNodeName: 'billingProvinceId_' +  obj.radomInit[0],nextNodeId: 'billingProvinceId_' +  obj.radomInit[0]},
//             {nextNodeWrap: '#billCitySel_wrap_' +  obj.radomInit[0],nextNodeName: 'billingCityId_' +  obj.radomInit[0],nextNodeId: 'billingCityId_' +  obj.radomInit[0]},
//             {nextNodeWrap: '#billDistrictSel_wrap_' +  obj.radomInit[0],nextNodeName: 'billingDistrictId_' +  obj.radomInit[0],nextNodeId: 'billingDistrictId_' +  obj.radomInit[0]},
//             {nextNodeWrap: '#billStreetSel_wrap_' +  obj.radomInit[0],nextNodeName: 'billingStreetId_' +  obj.radomInit[0],nextNodeId: 'billingStreetId_' +  obj.radomInit[0]}
//         ]
//         utils.setAllProDom('#billProvinceSel_wrap_' + obj.radomInit[0], billAddressSelIdObj, '#billingBox',true, function () {
//             $(billAddressSelIdObj[0]['nextNodeWrap']).parents('.distpicker').find('.btn').removeClass('addbill').addClass('removeDepot');
//             $(billAddressSelIdObj[0]['nextNodeWrap']).parents('.distpicker').find('.glyphicon').removeClass('glyphicon-plus').addClass('glyphicon-minus');
//             $(billAddressSelIdObj).each((index,item) => {
//                 if(index == 3) {
//                     $('#' + item['nextNodeId']).removeClass('{validate:{ required :true}}');
//                 }
//             })
//             $('#billAddress [id^=billingBox]').find('select').prop('disabled',false)
//         })
//     }
// });
// //删除发票邮寄地址
// $("#billAddress").on("click", ".removeDepot", function () {
//     $(this).parents(".billList").remove();
// });
//获取发票邮寄地址
// function distpickerHTML(n,m) {
//     var len = n ? n : 1;
//     var html = '';
//     let radomInit = [];
//     for (let i = 0; i < len; i++) {
//         let _int = parseInt(Math.random() * 100 + Math.random() * 1000);
//         html += `<div class="col-md-6 billList">
// 	        <div class="input-group">
// 	            <div class="input-group-addon require">发票邮寄地址</div>
// 	            <div class="form-control form-inline distpicker" id="billingBox_${_int}">
// 	                <div class="row">
// 	                    <div class="form-group col-md-2" id="billProvinceSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingProvinceId_${_int}" id="billingProvinceId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billCitySel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingCityId_${_int}" id="billingCityId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billDistrictSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingDistrictId_${_int}" id="billingDistrictId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billStreetSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}"  name="billingStreetId_${_int}" id="billingStreetId_${_int}"></select>
//                         </div>
// 	                    <div class="form-group col-md-3" style="position: initial;">
// 	                        <input type="text" class="form-control {validate:{ required :true}} text-inp Filter_SpaceAnd200StrLen_Class" name="billingAddress" value=""/>
// 	                    </div>
// 	                    <div class="form-group btn-box col-md-1">
// 	                        <button type="button" class="btn ${i==0  && !m ? 'addbill' : 'removeDepot'}" id="addbill">
// 	                            <span class="glyphicon ${ i==0 && !m ? 'glyphicon-plus' : 'glyphicon-minus'}" aria-hidden="true"></span>
// 	                        </button>
// 	                    </div>
// 	                </div>
// 	            </div>
// 	        </div>
// 	    </div>`;
//         radomInit.push(_int);
//     }
//     return {
//         html: html,
//         radomInit: radomInit
//     };
// }
$(function () {
    if ($('#customerCode').val() && $('#customerCode').val().indexOf('SNKH') > -1){
        $('#bigcusType').val('1')
    }
    var id = $("#customerApplId").val();
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //显示流程图
    var processInstaId=$("#processId").val();
    if(!processInstaId || processInstaId == ""){
        processInstaId=$("#approvalProcessId").val();
    }
    initApprovalFlowChart("customerInfoModifyERPApply", processInstaId);
    var rowNumber=1;
    //特殊经营范围回显
    var specialBusinessScope = $('#specialBusinessScope').val().split(',');
    $("input[name='specialBusinessScope']").each(function () {
        for(var x = 0; x < specialBusinessScope.length; x ++){
            if($(this).val() == specialBusinessScope[x]){
                $(this).attr("checked","checked");
            }
        }
    });
    //经营类别回显
    if ($('#businessCategory').val()) {
        var businessCategory = $('#businessCategory').val().split(',');
        $("input[name='businessCategoryName']").each(function () {
            for(var x = 0; x < businessCategory.length; x ++){
                if($(this).val() == businessCategory[x]){
                    $(this).attr("checked","checked");
                }
            }
        });
    }


    //不可经营类别回显
    if ($('#cannotBusinessCategory').val()) {
        var cannotBusinessCategory = $('#cannotBusinessCategory').val().split(',');
        $("input[name='cannotBusinessCategoryName']").each(function () {
            for(var x = 0; x < cannotBusinessCategory.length; x ++){
                if($(this).val() == cannotBusinessCategory[x]){
                    $(this).attr("checked","checked");
                }
            }
        });
    }


    //助记码
    $("#customerNm").on("keyup",function (ev) {
        if(ev.keyCode != 13)
        {
            var value=this.value;
            getMnemonicCode(value,'customerMnemonicCode');
        }
    });
    //结算方式
    $("input[name='parentCode']:not(:checked)").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').hide();
    });
    $("input[name='parentCode']:checked").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').show();
    });
    //tab切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    })
    $("#busineessScopeAll").attr("disabled","disabled");
    // 设置页面所有属性不可编辑
    if (pageType==0){
        $('#customerName').removeAttr('readonly').removeAttr('disabled');
        $(".ApprovalUploadeImg").attr('disabled','disabled');
        $("#changeApplyBtn").hide();
        // 修改字段结构初始化
        $.changApply_insertData({
            name: 'columnValue',
            status: 'changeStatus',
            afterValue: 'valueAfter',
            beforeValue: 'valueBefore'
        });  //按钮id：changeApplyBtn
        // 注册地址
        let registerAddressSelIdObj = [
            {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvinceId',nextNodeId: 'province1'},
            {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCityId',nextNodeId: 'city1'},
            {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerDistrictId',nextNodeId: 'district1'},
            {nextNodeWrap: '#streetSel_wrap',nextNodeName: 'registerStreetId',nextNodeId: 'street1'},
        ]
        utils.setAllProDom('#provinceSel_wrap', registerAddressSelIdObj, '#registerBox',true, function () {
            $(registerAddressSelIdObj).each((index, item ) => {
                $('#' + item['nextNodeId']).prop('disabled', true)
            })
        });
        // 仓库地址
        let storgeAddressSelIdObj = [
            {nextNodeWrap: '#stoProvinceSel_wrap',nextNodeName: 'repertoryProvince',nextNodeId: 'repertoryProvince'},
            {nextNodeWrap: '#stoCitySel_wrap',nextNodeName: 'repertoryCity',nextNodeId: 'repertoryCity'},
            {nextNodeWrap: '#stoDistrictSel_wrap',nextNodeName: 'repertoryArea',nextNodeId: 'repertoryArea'},
            {nextNodeWrap: '#stoStreetSel_wrap',nextNodeName: 'repertoryStreet',nextNodeId: 'repertoryStreet'}
        ];
        utils.setAllProDom('#stoProvinceSel_wrap', storgeAddressSelIdObj, '#storageBox', true,function () {
            $(storgeAddressSelIdObj).each((index, item ) => {
                $('#' + item['nextNodeId']).prop('disabled', true)
            })
        });

        // 收货地址
        let shippingAddressObj = [
            {nextNodeWrap: '#saProvinceSel_wrap',nextNodeName: 'province2',nextNodeId: 'province2'},
            {nextNodeWrap: '#sacitySel_wrap',nextNodeName: 'shippingAddressCityId',nextNodeId: 'shippingAddressCityId'},
            {nextNodeWrap: '#sadistrictSel_wrap',nextNodeName: 'shippingAddressDistrictId',nextNodeId: 'shippingAddressDistrictId'},
            {nextNodeWrap: '#sastreetSel_wrap',nextNodeName: 'shippingAddressStreetId',nextNodeId: 'shippingAddressStreetId'},
        ]
        let shippingAddressArray = [];
        utils.setAllProDom('#saProvinceSel_wrap', shippingAddressObj, '#shippingAddressBox',true, function () {
            $(shippingAddressObj).each((index, item ) => {
                $('#' + item['nextNodeId']).prop('disabled', true)
           })
        });

        // // 发票邮寄地址
        // let billAddressSelIdObj = [
        //     {nextNodeWrap: '#billProvinceSel_wrap',nextNodeName: 'billingProvinceId',nextNodeId: 'billingProvinceId'},
        //     {nextNodeWrap: '#billCitySel_wrap',nextNodeName: 'billingCityId',nextNodeId: 'billingCityId'},
        //     {nextNodeWrap: '#billDistrictSel_wrap',nextNodeName: 'billingDistrictId',nextNodeId: 'billingDistrictId'},
        //     {nextNodeWrap: '#billStreetSel_wrap',nextNodeName: 'billingStreetId',nextNodeId: 'billingStreetId'}
        // ];
        // utils.setAllProDom('#billProvinceSel_wrap', billAddressSelIdObj, '#billingBox', true,function () {
        //     $(billAddressSelIdObj).each((index, item ) => {
        //         $('#' + item['nextNodeId']).prop('disabled', true)
        //     })
        // });
    }else {
        // 注册地址
        let registerAddressSelIdObj = [
            {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvinceId',nextNodeId: 'province1'},
            {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCityId',nextNodeId: 'city1'},
            {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerDistrictId',nextNodeId: 'district1'},
            {nextNodeWrap: '#streetSel_wrap',nextNodeName: 'registerStreetId',nextNodeId: 'street1'},
        ];
        let registerPromiseArray = [];
        utils.setAllProDom('#provinceSel_wrap', registerAddressSelIdObj, '#registerBox',true, function () {
            // 注册地址有值回显
            let _registerHiddenVal = eval($('#registerAddressJson').val());
            if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
            _registerHiddenVal.splice(_registerHiddenVal.length - 1);
            $('#' + registerAddressSelIdObj[0]['nextNodeId']).prop('disabled', true);
            $('#' + registerAddressSelIdObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
            $('#' + registerAddressSelIdObj[0]['nextNodeId']).addClass('{validate:{ required :true}}');
            for (let i = 1; i < _registerHiddenVal.length; i++) {
                registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
            }
            Promise.all(registerPromiseArray).then(data => {
                for (let i = 0; i < data.length; i++) {
                    $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).html(data[i]);
                    $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).attr('data-depth', i+2);
                    $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                    $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).prop('disabled', true);
                }
            })
        });
        // 仓库地址
        // let storgeAddressSelIdObj = [
        //     {nextNodeWrap: '#stoProvinceSel_wrap',nextNodeName: 'repertoryProvince',nextNodeId: 'repertoryProvince'},
        //     {nextNodeWrap: '#stoCitySel_wrap',nextNodeName: 'repertoryCity',nextNodeId: 'repertoryCity'},
        //     {nextNodeWrap: '#stoDistrictSel_wrap',nextNodeName: 'repertoryArea',nextNodeId: 'repertoryArea'},
        //     {nextNodeWrap: '#stoStreetSel_wrap',nextNodeName: 'repertoryStreet',nextNodeId: 'repertoryStreet'}
        // ];
        // let storgePromiseArray = [];
        // utils.setAllProDom('#stoProvinceSel_wrap', storgeAddressSelIdObj, '#storageBox',true, function () {
        //     // 仓库地址有值回显
        //     let _storgeHiddenVal = eval($('#repertoryAddressJson').val());
        //     if (!_storgeHiddenVal) _storgeHiddenVal = ['','','','',''];
        //     $('#' + storgeAddressSelIdObj[0]['nextNodeId']).prop('disabled', true);
        //     $('#' + storgeAddressSelIdObj[0]['nextNodeId']).val(_storgeHiddenVal[0][0]);
        //     $('#' + storgeAddressSelIdObj[0]['nextNodeId']).addClass('{validate:{ required :true}}');
        //     $('#' + storgeAddressSelIdObj[0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[0][4]);
        //     for (let i = 1; i < _storgeHiddenVal[0].length; i++) {
        //         storgePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenVal[0][i-1]));
        //     }
        //     Promise.all(storgePromiseArray).then(data => {
        //         for (let i = 1; i < data.length; i++) {
        //             $('#' + storgeAddressSelIdObj[i]['nextNodeId']).html(data[i-1]);
        //             $('#' + storgeAddressSelIdObj[i]['nextNodeId']).attr('data-depth',i+1);
        //             $('#' + storgeAddressSelIdObj[i]['nextNodeId']).val(_storgeHiddenVal[0][i]);
        //             $('#' + storgeAddressSelIdObj[i]['nextNodeId']).prop('disabled', true);
        //         }
        //     }).catch(err => {
        //         console.log(err)
        //     })
        // });
        // 收货地址
        let shippingAddressObj = [
            {nextNodeWrap: '#saProvinceSel_wrap',nextNodeName: 'province2',nextNodeId: 'province2'},
            {nextNodeWrap: '#sacitySel_wrap',nextNodeName: 'shippingAddressCityId',nextNodeId: 'shippingAddressCityId'},
            {nextNodeWrap: '#sadistrictSel_wrap',nextNodeName: 'shippingAddressDistrictId',nextNodeId: 'shippingAddressDistrictId'},
            {nextNodeWrap: '#sastreetSel_wrap',nextNodeName: 'shippingAddressStreetId',nextNodeId: 'shippingAddressStreetId'},
        ]
        let shippingAddressArray = [];
        utils.setAllProDom('#saProvinceSel_wrap', shippingAddressObj, '#shippingAddressBox',true, function () {
            // 收货地址有值回显
            let _registerHiddenVal = eval($('#shippingAddressJson').val());
            if (!_registerHiddenVal) _registerHiddenVal = [['','','','']];
            if(_registerHiddenVal.length>0){
                _registerHiddenVal=_registerHiddenVal[0];
            }
            _registerHiddenVal.splice(_registerHiddenVal.length - 1);
            // $('#' + registerAddressSelIdObj[0]['nextNodeId']).prop('disabled', true);
            $('#' + shippingAddressObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
            $('#' + shippingAddressObj[0]['nextNodeId']).parents('.distpicker').find('input[type=text]')
            for (let i = 1; i < _registerHiddenVal.length; i++) {
                shippingAddressArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
            }
            Promise.all(shippingAddressArray).then(data => {
                // console.log(data)
                for (let i = 0; i < data.length; i++) {
                $('#' + shippingAddressObj[i + 1]['nextNodeId']).html(data[i]);
                $('#' + shippingAddressObj[i + 1]['nextNodeId']).attr('data-depth',i+2);
                $('#' + shippingAddressObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                $('#' + shippingAddressObj[i + 1]['nextNodeId']).prop('disabled', true);
            }
        })
            $(shippingAddressObj).each((index,item) => {
                if(index == 3) {
                $('#' + item.nextNodeId).removeClass('{validate:{ required :true}}')
            }
        })
        });




        // 发票邮寄地址
        // let billAddressSelIdObj = [];
        // let _billHiddenVal = eval($('#billingAddressJson').val());
        // if (!_billHiddenVal) _billHiddenVal = [['','','','']];
        // let _billHiddenValArr =  eval($('#billingAddressJson').val());
        // $(_billHiddenValArr).each((index,item) => {
        //     item.splice(item.length - 1);
        // });
        // if (!_billHiddenValArr) _billHiddenValArr = [['','','','']];
        //
        // let billObj = distpickerHTML(_billHiddenValArr.length);
        // $(billObj.radomInit).each((index, item) => {
        //     let _arr = [
        //         {nextNodeWrap: '#billProvinceSel_wrap_' + item,nextNodeName: 'billingProvinceId_' + item,nextNodeId: 'billingProvinceId_' + item},
        //         {nextNodeWrap: '#billCitySel_wrap_' + item,nextNodeName: 'billingCityId_' + item,nextNodeId: 'billingCityId_' + item},
        //         {nextNodeWrap: '#billDistrictSel_wrap_' + item,nextNodeName: 'billingDistrictId_' + item,nextNodeId: 'billingDistrictId_' + item},
        //         {nextNodeWrap: '#billStreetSel_wrap_' + item,nextNodeName: 'billingStreetId_' + item,nextNodeId: 'billingStreetId_' + item}
        //     ]
        //     billAddressSelIdObj.push(_arr)
        // });
        // $('#billAddress').find('.depotList').remove();
        // $("#billAddress").find('.billList').remove();
        // $('#billAddress').append(billObj.html)
        // $(billObj.radomInit).each((index, item) => {
        //     let billPromiseArray = [];
        //     for (let ind = 1; ind < _billHiddenValArr[index].length; ind++) {
        //         utils.setAllProDom('#billProvinceSel_wrap_' + billObj.radomInit[index], billAddressSelIdObj[index], '#billingBox', true,function () {
        //             $('#' + billAddressSelIdObj[index][0]['nextNodeId']).val(_billHiddenValArr[index][0]);
        //             $('#' + billAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').val(_billHiddenVal[index][4]);
        //             $('#' + billAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
        //             $('#' + billAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').prop('disabled', true);
        //             $('#' + billAddressSelIdObj[0][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').attr('changeApplyFlag', 'customerBillingAddressVoList');
        //             disable();
        //             for (let ind = 1; ind < _billHiddenValArr[0].length; ind++) {
        //                 billPromiseArray.push(utils.setAddressReturnVal(_billHiddenValArr[index][ind-1]));
        //             }
        //             let allSelArr = billAddressSelIdObj[index].flat().map((item, index) => {
        //                 if (index % 4 != 0) {
        //                     return item['nextNodeId']
        //                 }
        //             }).filter(item => {
        //                 return item
        //             });
        //             Promise.all(billPromiseArray).then(data => {
        //                 for (let i = 0; i < data.length; i++) {
        //                     $('#' + allSelArr[i]).html(data[i]);
        //                     $('#' + allSelArr[i]).attr('data-depth',i+2);
        //                     $('#' + allSelArr[i]).val(_billHiddenValArr[index][i + 1]);
        //                     $('#' + allSelArr[i]).prop('disabled', true);
        //                     if (i == 2) {
        //                         $('#' + allSelArr[i]).removeClass('{validate:{ required :true}}');
        //                     }
        //                 }
        //             })
        //         })
        //     }
        // })
        parent.showLoading({hideTime: 15000});
        $.ajax({
            url:'/proxy-customer/customer/change/approval/queryBaseAfterDetail',
            data:{"id":$("#approvalId").val()},
            type:"post",
            success:function(data){
                if(data!=null&&data!=undefined){
                    //字段修改全部
                    var changeObj = data.result.customerChangeRecordDetailVoList;
                    var obj = {};
                    var objList = {};
                    obj.customerStorageAddressVOList = {};
                    obj.customerStorageAddressVOList.changeStatus =-1;
                    obj.customerStorageAddressVOList.columnValue = 'customerStorageAddressVOList';
                    var StorageAddressListvalueAfter = {};

                    obj.registerList = {};
                    obj.registerList.changeStatus = -1;
                    obj.registerList.columnValue = 'registerList';
                    var registerListvalueAfter = {};

                    obj.customerBillingAddressVoList = {};
                    obj.customerBillingAddressVoList.changeStatus = -1;
                    obj.customerBillingAddressVoList.columnValue = 'customerBillingAddressVoList';
                    var billingAddressvalueAfter = {};
                    // 注册地址
                    var registerflag = 0;
                    var register = {
                        "columnValue":"register",
                        "valueAfter":{}
                    };
                    // 仓库地址
                    var storageflag = 0;
                    var storage = {
                        "columnValue":"storage",
                        "valueAfter":{}
                    };
                    var storageRegisterStreet = '';
                    var registerStreet = '';


                    $.each(changeObj,function (i, v) {
                        // 判断是否为批准文件
                        if(v.columnValue=="customerApprovalFileVoList"){
                            objList.customerApprovalFileVoList = {
                                "columnValue":"customerApprovalFileVoList",
                                "changeStatus":-1,
                                "valueAfter" : {}
                            };
                            if (typeof v.valueAfter == 'string') {
                                v.valueAfter = JSON.parse(v.valueAfter);
                            }
                            objList.customerApprovalFileVoList.valueAfter = v.valueAfter;
                        }else if(v.columnValue=="customerQualityAgreementVoList"){
                            // 质量保证协议
                            objList.customerQualityAgreementVoList = {
                                "columnValue":"customerQualityAgreementVoList",
                                "changeStatus":-1,
                                "valueAfter" : {}
                            };
                            if (typeof v.valueAfter == 'string') {
                                v.valueAfter = JSON.parse(v.valueAfter);
                            }
                            objList.customerQualityAgreementVoList.valueAfter = v.valueAfter;
                        }else if(v.columnValue=="customerDelegationFileVoList"){
                            // 客户委托书
                            objList.customerDelegationFileVoList = {
                                "columnValue":"customerDelegationFileVoList",
                                "changeStatus":-1,
                                "valueAfter" : {}
                            };
                            if (typeof v.valueAfter == 'string') {
                                v.valueAfter = JSON.parse(v.valueAfter);
                            }
                            // if($("#shippingSource").length&&$("#shippingSource").val()!="3"){
                            //     $("#khwtAddRow").hide();
                            //     $("#khwtRemoveRow").hide();
                            // }

                            objList.customerDelegationFileVoList.valueAfter = v.valueAfter;
                        }else if(v.columnValue=="customerBeProxyIdentityVoList"){
                            // 被委托人身份证
                            objList.customerBeProxyIdentityVoList = {
                                "columnValue":"customerBeProxyIdentityVoList",
                                "changeStatus":-1,
                                "valueAfter" : {}
                            };
                            if (typeof v.valueAfter == 'string') {
                                v.valueAfter = JSON.parse(v.valueAfter);
                            }
                            // if($("#shippingSource").length&&$("#shippingSource").val()!="3") {
                            //         $("#zlbzAddRow").hide();
                            //         $("#zlbzRemoveRow").hide();
                            // }
                            objList.customerBeProxyIdentityVoList.valueAfter = v.valueAfter;
                        }else if(v.columnValue=="customerContractVos"){
                            // 客户合同
                            objList.customerContractVos = {
                                "columnValue":"customerContractVos",
                                "changeStatus":-1,
                                "valueAfter" : {}
                            };
                            if (typeof v.valueAfter == 'string') {
                                v.valueAfter = JSON.parse(v.valueAfter);
                            }
                            objList.customerContractVos.valueAfter = v.valueAfter;
                        }else if(v.columnValue=="customerDeliveryAddressVoList"){
                            v.changeStatus=-1;
                            // 收货地址
                            if (typeof v.valueAfter == 'string') {
                                v.valueAfter = JSON.parse(v.valueAfter);
                            }
                           const customerDeliveryAddressVoList = v.valueAfter;
                            if(customerDeliveryAddressVoList.length>0&&!customerDeliveryAddressVoList[0].province2){
                                for(var i=0;i<customerDeliveryAddressVoList.length;i++){
                                    customerDeliveryAddressVoList[i].province2=customerDeliveryAddressVoList[i].billingProvinceId?customerDeliveryAddressVoList[i].billingProvinceId:customerDeliveryAddressVoList[i].repertoryProvince;
                                    customerDeliveryAddressVoList[i].shippingAddressCityId=customerDeliveryAddressVoList[i].billingCityId?customerDeliveryAddressVoList[i].billingCityId:customerDeliveryAddressVoList[i].repertoryCity;
                                    customerDeliveryAddressVoList[i].shippingAddressDistrictId=customerDeliveryAddressVoList[i].billingDistrictId?customerDeliveryAddressVoList[i].billingDistrictId:customerDeliveryAddressVoList[i].repertoryArea;
                                    customerDeliveryAddressVoList[i].shippingAddressStreetId=customerDeliveryAddressVoList[i].billingStreetId?customerDeliveryAddressVoList[i].billingStreetId:customerDeliveryAddressVoList[i].registerStreet;
                                    customerDeliveryAddressVoList[i].shippingAddressInput=customerDeliveryAddressVoList[i].billingAddress?customerDeliveryAddressVoList[i].billingAddress:customerDeliveryAddressVoList[i].repertoryDetail;
                                }
                            }

                            af = v.afterText;
                            obj.customerDeliveryAddressVoList={};
                            obj.customerDeliveryAddressVoList.changeStatus = -1;
                            obj.customerDeliveryAddressVoList.columnValue = 'customerDeliveryAddressVoList';
                            obj.customerDeliveryAddressVoList.valueAfter = customerDeliveryAddressVoList;
                            obj.customerDeliveryAddressVoList.afterText = af;

                        } else if(v.columnValue=="customerBillingAddressVoList"){
                            v.changeStatus=-1;
                            // 发票邮寄地址
                            if (typeof v.valueAfter == 'string') {
                                v.valueAfter = JSON.parse(v.valueAfter);
                            }
                            billingAddressvalueAfter = v.valueAfter;
                            af = v.afterText;
                            obj.customerBillingAddressVoList.valueAfter = billingAddressvalueAfter;
                            obj.customerBillingAddressVoList.afterText = af;
                        }else if(v.columnValue=="customerExtendItemVoList"){
                            var items = data.result.customerExtendItemVoList;
                            var temp = [];
                            // 付款方式结算方式
                            obj.customerExtendItemVoList = {
                                "columnValue":"customerExtendItemVoList",
                                "changeStatus":-1,
                                "valueAfter" : {}
                            };
                            if (typeof v.valueAfter == 'string') {
                                v.valueAfter = JSON.parse(v.valueAfter);
                            }
                            for(var i=0;i<items.length;i++){
                                if(items[i].parentCode==0){
                                    var item = {
                                        parentCode:items[i].code
                                    };
                                    temp.push(item);
                                }else{
                                    temp.push(items[i]);
                                }

                            }
                            obj.customerExtendItemVoList.valueAfter = temp;
                        }else if(v.columnValue=="registerCityId"){
                            registerflag = 1;
                            register.valueAfter.registerCityId = v.valueAfter;
                            registerListvalueAfter.registerCity = v.valueAfter;
                        }else if(v.columnValue=="registerAreaId"){
                            registerflag = 1;
                            register.valueAfter.registerAreaId = v.valueAfter;
                            registerListvalueAfter.registerArea = v.valueAfter;
                        }else if(v.columnValue=="registerStreetId"){
                            registerflag = 1;
                            registerStreet = v.afterText;
                            register.valueAfter.registerStreetId = v.valueAfter;
                            registerListvalueAfter.registerStreet = v.valueAfter;
                        }else if(v.columnValue=="registerAddress"){
                            registerflag = 1;
                            register.valueAfter.registerAddress = v.valueAfter;
                            registerListvalueAfter.registerDetail = v.valueAfter;
                        }else if(v.columnValue=="registerProvinceId"){
                            registerflag = 1;
                            register.valueAfter.registerProvinceId = v.valueAfter;
                            registerListvalueAfter.registerProvince = v.valueAfter;
                        }else if(v.columnValue=="storageProvinceId"){
                            storageflag = 1;
                            register.valueAfter.storageProvinceId = v.valueAfter;
                            StorageAddressListvalueAfter.repertoryProvince = v.valueAfter;
                        }else if(v.columnValue=="storageCityId"){
                            storageflag = 1;
                            register.valueAfter.storageCityId = v.valueAfter;
                            v.changeStatus=-1;
                            StorageAddressListvalueAfter.repertoryCity = v.valueAfter;
                        }else if(v.columnValue=="storageDistrictId"){
                            storageflag = 1;
                            register.valueAfter.storageDistrictId = v.valueAfter;
                            StorageAddressListvalueAfter.storageDistrictId = v.valueAfter;
                        }else if(v.columnValue=="storageStreetId"){
                            storageflag = 1;
                            storageRegisterStreet = v.afterText;
                            register.valueAfter.storageStreetId = v.valueAfter;
                            StorageAddressListvalueAfter.registerStreet = v.valueAfter;
                        }else if(v.columnValue=="storageAddress"){
                            storageflag = 1;
                            register.valueAfter.storageAddress = v.valueAfter;
                            StorageAddressListvalueAfter.repertoryDetail = v.valueAfter;
                        }else if(v.columnValue=="storageAreaId"){
                            storageflag = 1;
                            register.valueAfter.storageAreaId = v.valueAfter;
                            StorageAddressListvalueAfter.repertoryArea = v.valueAfter;
                        }else{
                            v.changeStatus = -1;
                            obj[v.columnValue]=v;
                        }
                    });
                    obj.customerStorageAddressVOList.valueAfter = StorageAddressListvalueAfter;
                    obj.customerStorageAddressVOList.afterText = storageRegisterStreet;
                    obj.registerList.valueAfter = registerListvalueAfter;
                    obj.registerList.afterText = registerStreet;
                    if ($.isEmptyObject(obj.customerStorageAddressVOList.valueAfter)) {
                        delete obj.customerStorageAddressVOList;
                    }
                    if ($.isEmptyObject(obj.registerList.valueAfter)) {
                        delete obj.registerList;
                    }
                    if ($.isEmptyObject(obj.customerBillingAddressVoList.valueAfter)) {
                        delete obj.customerBillingAddressVoList;
                    }
                    if(registerflag==1){
                        delete obj.registerProvinceId;
                        delete obj.storageCityId;
                        delete obj.storageDistrictId;
                        delete obj.storageStreetId;
                        delete obj.storageAddress;
                        storage.changeStatus=-1;
                        obj.storage=register;
                    }
                    if(storageflag==1){
                        delete obj.storageProvinceId;
                        delete obj.registerCityId;
                        delete obj.registerDistrictId;
                        delete obj.registerStreetId;
                        delete obj.registerAddress;
                        register.changeStatus=-1;
                        obj.register=register;
                    }
                    //编辑页面
                    if (pageType==1||pageType==4||pageType==30){
                        // 修改字段结构初始化
                        $.changApply_insertData();  //按钮id：changeApplyBtn
                    }
                    //审核页面
                    if (pageType==2||pageType==3){
                        var objView = obj;
                        $.extend(objView,objList);
                        setTimeout(function () {
                            $.changeApply_selectData(obj);
                        });
                    }
                    detailAllData=JSON.parse(JSON.stringify(objList));
                    window.changeApply=obj;
                    window.changeApplyList=objList;
                    window.changeApplyBak=JSON.parse(JSON.stringify(obj));
                    window.changeApplyListBak=JSON.parse(JSON.stringify(objList));
                }
            },
            complete:function(){
                parent.hideLoading();
            }
        })
    }

    if(baseId==null||baseId==''){
        //批准文件
        xGridTable3({
            id: "",
            credentialTypeId: "",
            credentialCode:"",
            customerBusinessScopeVoList: [],
            certificationOffice: "",
            openingDate: "",
            validUntil: "",
            enclosureCount: "",
            customerEnclosureVoList:[]
        });
        //质量保证协议
        // xGridTable1({
        //     id: "",
        //     signedDate: "",
        //     validDate:"",
        //     signer: "",
        //     enclosureCount: "",
        //     customerEnclosureVoList:[]
        // });
        //客户委托书
        xGridTable3({
            id: "",
            delegationCredentialCode: "",
            delegationName:"",
            delegationSex: "",
            delegationTel: "",
            delegationNum: "",
            delegationAddr: "",
            delegationIdentityDate: "",
            validityDelegationCredential:"",
            authorityZone: "",
            authorityScope: "",
            enclosureCount: "",
            customerEnclosureVoList:[]
        });
        //被委托人身份证
        xGridCertTable({
            id: "",
            customerName: "",
            identityNumber:"",
            identityValidityDate: "",
            enclosureCount: "",
            customerEnclosureVoList:[]
        });
        //客户合同

    }else{
        //批准文件
        initTable3($("#baseId").val(),3);
        //质量保证协议
        // initTable1($("#baseId").val(),3);
        //客户委托书
        initTable2($("#baseId").val(),3);
        //被委托人身份证
        initCertTable($("#baseId").val(),3);
        //客户合同
        // contract($("#baseId").val(),3);
    }
    $("input[name='specialBusinessScope']").change(function() {
        let specialBusinessScopeVal = $(this).val();
        /**
         * RM 2019-07-23
         */
        if ($("input[name='specialBusinessScope']:checked").length > 0) {
            // 版本一
            if (specialBusinessScopeVal == '12') { // 操作 无特殊属性时
                $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').not(':last').prop('checked', false);
            } else {
                $('#specialBusinessScope').parent().find('[name=specialBusinessScope]:last').prop({'checked': false,'disabled': false });// 无特殊属性 不允许选
            }
            // 版本二
            // if (specialBusinessScopeVal == '12') { // 操作 无特殊属性时
            //     $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').not(':last').prop('disabled', true);
            // } else {
            //     $('#specialBusinessScope').parent().find('[name=specialBusinessScope]:last').prop('disabled', true); // 无特殊属性 不允许选
            //     $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').not(':last').prop('disabled', false);
            // }
        } else {
            // 没有选中项时 全部放开允许点击
            $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').prop('disabled', false);
        }
        /*----------2019-07-23 END------------------*/
    })

});
//点击放大镜触发
$(document).on("click", "#customerBtn", function (ev) {
    search(ev);
});
//回车触发
$(document).on("keydown", "#customerName", function (ev) {
    if(ev.keyCode==13){
        search(ev);
    }
});
function search(ev){
    parent.showLoading();
    var supplierName = $("#supplierName").val();
    utils.dialog({
        title: '客户列表',
        url: '/proxy-customer/customer/customerBaseAppl/toCustomerListForChange',
        width: $(window).width() * 0.8,
        height: 600,
        data: supplierName, // 给modal 要传递的 的数据
        onclose: function () {
            if (this.returnValue) {
                var data = this.returnValue;
                $.ajax({
                    url:'/proxy-customer/customer/customerBaseAppl/queryOrganBaseById',
                    data:{"id":data.id},
                    type:"post",
                    dataType:'json',
                    success:function(data){
                        console.log(data)
                        // 清空变更属性
                        window.changeApply = {};//变更obj
                        window.changeApply_ = {};//变更临时obj
                        window.changeApplyList = {};//变更obj-list
                        window.changeApplyList_ = {};//变更临时obj-list
                        var vo = data.result;
                        var customerBaseVo = vo.baseVo;
                        var customerDeliveryAddressVoList=vo.customerDeliveryAddressVoList;
                        $("#baseId").val(customerBaseVo.id);
                        //格式化时间
                        var recordDate = vo.baseVo.recordDate;
                        var validUntil = vo.baseVo.validUntil;
                        if (recordDate != undefined){
                            vo.baseVo.recordDate =  new Date(recordDate).Format('yyyy-MM-dd');
                        }
                        if (validUntil != undefined){
                            vo.baseVo.validUntil = new Date(validUntil).Format('yyyy-MM-dd');
                        }
                        loadData(vo.baseVo,$("#base"));
                        if (vo.baseVo['customerCode'].indexOf('SNKH') > -1){
                            $('#bigcusType').val('1')
                        }
                        able();
                        $("[name='openingBank']").val(customerBaseVo.bankName);
                        $("[name='bankAccountName']").val(customerBaseVo.accountName);
                        $("[name='billingCompanyName']").val(customerBaseVo.billingCompanyName);
                        $("[name='businessLicenseAddress']").val(customerBaseVo.businessLicenseAddress);
                       if(customerDeliveryAddressVoList&&customerDeliveryAddressVoList.length>0){
                           $("[name='shippingAddressInput']").val(customerDeliveryAddressVoList[0].billingAddress);
                       }

                        // 注册地址
                        let registerAddressSelIdObj = [
                            [
                                {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvinceId',nextNodeId: 'province1'},
                                {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCityId',nextNodeId: 'city1'},
                                {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerDistrictId',nextNodeId: 'district1'},
                                {nextNodeWrap: '#streetSel_wrap',nextNodeName: 'registerStreetId',nextNodeId: 'street1'},
                            ]
                        ];
                        let registerPromiseArray = [];
                        let _registerHiddenVal = [customerBaseVo.registerProvinceId, customerBaseVo.registerCityId, customerBaseVo.registerDistrictId, customerBaseVo.registerStreetId];
                        $('#' + registerAddressSelIdObj[0][0]['nextNodeId']).val(_registerHiddenVal[0]);
                        $('#' + registerAddressSelIdObj[0][0]['nextNodeId']).addClass('{validate:{ required :true}}');
                        $('#' + registerAddressSelIdObj[0][0]['nextNodeId']).attr('data-value', _registerHiddenVal[0]);
                        for (let i = 1; i < _registerHiddenVal.length; i++) {
                            registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
                        };
                        Promise.all(registerPromiseArray).then(data => {
                            for (let i = 0; i < data.length; i++) {
                                $('#' + registerAddressSelIdObj[0][i+1]['nextNodeId']).html(data[i]);
                                $('#' + registerAddressSelIdObj[0][i+1]['nextNodeId']).attr('data-depth', i+2);
                                $('#' + registerAddressSelIdObj[0][i+1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                                $('#' + registerAddressSelIdObj[0][i+1]['nextNodeId']).prop('disabled', true);
                                if (i == 2) {
                                    $('#' + registerAddressSelIdObj[0][i+1]['nextNodeId']).removeClass('{validate:{ required :true}}');
                                }
                            }
                        })
                        // 仓库地址
                        let storgeAddressSelIdObj = [
                            [
                                {nextNodeWrap: '#stoProvinceSel_wrap',nextNodeName: 'repertoryProvince',nextNodeId: 'repertoryProvince'},
                                {nextNodeWrap: '#stoCitySel_wrap',nextNodeName: 'repertoryCity',nextNodeId: 'repertoryCity'},
                                {nextNodeWrap: '#stoDistrictSel_wrap',nextNodeName: 'repertoryArea',nextNodeId: 'repertoryArea'},
                                {nextNodeWrap: '#stoStreetSel_wrap',nextNodeName: 'repertoryStreet',nextNodeId: 'repertoryStreet'}
                            ]
                        ];
                        let storgePromiseArray = [];
                        let _storgeHiddenVal = [customerBaseVo.storageProvinceId, customerBaseVo.storageCityId, customerBaseVo.storageDistrictId, customerBaseVo.storageStreetId];
                        $('#' + storgeAddressSelIdObj[0][0]['nextNodeId']).val(_storgeHiddenVal[0]);
                        $('#' + storgeAddressSelIdObj[0][0]['nextNodeId']).attr('data-value', _storgeHiddenVal[0]);
                        $('#' + storgeAddressSelIdObj[0][0]['nextNodeId']).addClass('{validate:{ required :true}}');
                        for (let i = 1; i < _storgeHiddenVal.length; i++) {
                            storgePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenVal[i-1]));
                        };
                        let allSelArr = storgeAddressSelIdObj.flat().map((item, index) => {
                            if (index % 4 != 0) {
                                return item['nextNodeId']
                            }
                        }).filter(item => {
                            return item
                        });
                        Promise.all(storgePromiseArray).then(data => {
                            for (let i = 0; i < data.length; i++) {
                                $('#' + allSelArr[i]).html(data[i]);
                                $('#' + allSelArr[i]).attr('data-depth',i+2);
                                $('#' + allSelArr[i]).val(_storgeHiddenVal[i + 1]);
                                $('#' + allSelArr[i]).prop('disabled', true);
                                if (i == 2) {
                                    $('#' + allSelArr[i]).removeClass('{validate:{ required :true}}');
                                }
                            }
                        })

                        // 收货地址
                        let shippingAddressObj = [
                            {nextNodeWrap: '#saProvinceSel_wrap',nextNodeName: 'province2',nextNodeId: 'province2'},
                            {nextNodeWrap: '#sacitySel_wrap',nextNodeName: 'shippingAddressCityId',nextNodeId: 'shippingAddressCityId'},
                            {nextNodeWrap: '#sadistrictSel_wrap',nextNodeName: 'shippingAddressDistrictId',nextNodeId: 'shippingAddressDistrictId'},
                            {nextNodeWrap: '#sastreetSel_wrap',nextNodeName: 'shippingAddressStreetId',nextNodeId: 'shippingAddressStreetId'},
                        ]
                        let shippingAddressArray = [];
                        utils.setAllProDom('#saProvinceSel_wrap', shippingAddressObj, '#shippingAddressBox',true, function () {
                            // 收货地址有值回显
                            let _registerHiddenVal = customerDeliveryAddressVoList.map(item => {
                                return [item.billingProvinceId, item.billingCityId, item.billingDistrictId, item.billingStreetId,item.billingAddress];
                            });

                            if (!_registerHiddenVal) _registerHiddenVal = [['','','','']];
                            if(_registerHiddenVal.length>0){
                                _registerHiddenVal=JSON.parse(JSON.stringify(_registerHiddenVal[0]));
                            }
                            _registerHiddenVal.splice(_registerHiddenVal.length - 1);
                            // $('#' + registerAddressSelIdObj[0]['nextNodeId']).prop('disabled', true);
                            $('#' + shippingAddressObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
                            $('#' + shippingAddressObj[0]['nextNodeId']).parents('.distpicker').find('input[type=text]')
                            for (let i = 1; i < _registerHiddenVal.length; i++) {
                                shippingAddressArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
                            }
                            Promise.all(shippingAddressArray).then(data => {
                                // console.log(data)
                                for (let i = 0; i < data.length; i++) {
                                $('#' + shippingAddressObj[i + 1]['nextNodeId']).html(data[i]);
                                $('#' + shippingAddressObj[i + 1]['nextNodeId']).attr('data-depth',i+2);
                                $('#' + shippingAddressObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                                $('#' + shippingAddressObj[i + 1]['nextNodeId']).prop('disabled', true);
                                if (i == 2) {
                                    $('#' + shippingAddressObj[i+1]['nextNodeId']).removeClass('{validate:{ required :true}}');
                                }
                            }
                        })
                            $(shippingAddressObj).each((index,item) => {
                                if(index == 3) {
                                $('#' + item.nextNodeId).removeClass('{validate:{ required :true}}')
                            }
                        })
                        });

                        // 发票邮寄地址
                        // var billingList=vo.customerBillingAddressVoList;
                        // var html='';
                        // if(null !=billingList && billingList.length){
                        //     var len = billingList.length;
                        //     $("#billAddress").find('.billList').remove();
                        //     var obj = distpickerHTML(len);
                        //     let billAddressSelIdObj = [];
                        //     let _billHiddenValArr = billingList.map(item => {
                        //         return [item.billingProvinceId, item.billingCityId, item.billingDistrictId, item.billingStreetId]
                        //     });
                        //     let _storgeHiddenVal = billingList.map(item => {
                        //         return [item.billingProvinceId, item.billingCityId, item.billingDistrictId, item.billingStreetId,item.billingAddress]
                        //     });
                        //     $(obj.radomInit).each((index, item) => {
                        //         let _arr = [
                        //             {nextNodeWrap: '#billProvinceSel_wrap_' + item,nextNodeName: 'billingProvinceId_' + item,nextNodeId: 'billingProvinceId_' + item},
                        //             {nextNodeWrap: '#billCitySel_wrap_' + item,nextNodeName: 'billingCityId_' + item,nextNodeId: 'billingCityId_' + item},
                        //             {nextNodeWrap: '#billDistrictSel_wrap_' + item,nextNodeName: 'billingDistrictId_' + item,nextNodeId: 'billingDistrictId_' + item},
                        //             {nextNodeWrap: '#billStreetSel_wrap_' + item,nextNodeName: 'billingStreetId_' + item,nextNodeId: 'billingStreetId_' + item}
                        //         ]
                        //         billAddressSelIdObj.push(_arr)
                        //     });
                        //     $('#billAddress').html(obj.html)
                        //     $(obj.radomInit).each((index, item) => {
                        //         let billPromiseArray = [];
                        //         for (let ind = 1; ind < _billHiddenValArr[index].length; ind++) {
                        //             utils.setAllProDom('#billProvinceSel_wrap_' + obj.radomInit[index], billAddressSelIdObj[index], '#billingBox',true, function () {
                        //                 $('#' + billAddressSelIdObj[index][0]['nextNodeId']).val(_billHiddenValArr[index][0]);
                        //                 $('#' + billAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').val(_storgeHiddenVal[index][4]);
                        //                 $('#' + billAddressSelIdObj[0][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').attr('changeApplyFlag', 'customerBillingAddressVoList');
                        //                 $('#' + billAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
                        //                 $('#' + billAddressSelIdObj[index][0]['nextNodeId']).addClass('{validate:{ required :true}}');
                        //                 $('#' + billAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').prop('disabled', true);
                        //                 $('#' + billAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('.btn').css('display','none');
                        //                 disable();
                        //                 for (let ind = 1; ind < _billHiddenValArr[0].length; ind++) {
                        //                     billPromiseArray.push(utils.setAddressReturnVal(_billHiddenValArr[index][ind-1]));
                        //                 }
                        //                 let allSelArr = billAddressSelIdObj[index].flat().map((item, index) => {
                        //                     if (index % 4 != 0) {
                        //                         return item['nextNodeId']
                        //                     }
                        //                 }).filter(item => {
                        //                     return item
                        //                 });
                        //                 Promise.all(billPromiseArray).then(data => {
                        //                     for (let i = 0; i < data.length; i++) {
                        //                         $('#' + allSelArr[i]).html(data[i]);
                        //                         $('#' + allSelArr[i]).attr('data-depth',i+2);
                        //                         $('#' + allSelArr[i]).val(_billHiddenValArr[index][i + 1]);
                        //                         $('#' + allSelArr[i]).prop('disabled', true);
                        //                         if (i == 2) {
                        //                             $('#' + allSelArr[i]).removeClass('{validate:{ required :true}}');
                        //                         }
                        //                     }
                        //                 })
                        //             })
                        //         }
                        //     })
                        //
                        // }else{
                        //     // html=billDistpickerHTML();
                        //     // 先移除 billList div ，避免重复
                        //     $("#billAddress").find('.billList').remove();
                        //     let obj = distpickerHTML();
                        //     $("#billAddress").html(obj.html);
                        //     // 补全 distpickerHTML 函数中遗漏的 红色 * 标志
                        //     $('#billAddress .input-group-addon.require').prepend('<i class="text-require">*</i>')
                        //     let billAddressSelIdObj = [
                        //         {nextNodeWrap: '#billProvinceSel_wrap_' + obj.radomInit[0],nextNodeName: 'billingProvinceId_' + obj.radomInit[0],nextNodeId: 'billingProvinceId_' + obj.radomInit[0]},
                        //         {nextNodeWrap: '#billCitySel_wrap_' + obj.radomInit[0],nextNodeName: 'billingCityId_' + obj.radomInit[0],nextNodeId: 'billingCityId_' + obj.radomInit[0]},
                        //         {nextNodeWrap: '#billDistrictSel_wrap_' + obj.radomInit[0],nextNodeName: 'billingDistrictId_' + obj.radomInit[0],nextNodeId: 'billingDistrictId_' + obj.radomInit[0]},
                        //         {nextNodeWrap: '#billStreetSel_wrap_' + obj.radomInit[0],nextNodeName: 'billingStreetId_' + obj.radomInit[0],nextNodeId: 'billingStreetId_' + obj.radomInit[0]}
                        //     ]
                        //     utils.setAllProDom('#billProvinceSel_wrap_' + obj.radomInit[0], billAddressSelIdObj, '#billingBox', true,function () {
                        //         $('#' + billAddressSelIdObj[0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').attr('changeApplyFlag', 'customerBillingAddressVoList');
                        //         disable();
                        //     });
                        // }
                        // if(html != ''){
                        //     $("#billAddress").html(html);
                        // }

                        /**
                         * 当引用了一条数据，并且点了资料变更announce后，此时，三个点的小圆圈已经都显示，这个时候客户名称引用一条新的数据，。
                         * 回显之后发票邮寄地址的小圆圈不显示。
                         * 下面加了if会后判断发票邮寄地址的前一个节点元素，是否显示小圆圈，如果显示说明当前处于资料变更的状态下，
                         * 此时回显完毕后给发票邮寄地址后面也追加一个小圆圈。
                         * 为什么没有圆圈是因为之前操作发票邮寄地址的html 内容被清空重置了、所以追加。
                         */
                        // if($("#billAddress").prev().find('.yulanInput').css('display')){
                        //     var _htm = '<i class="yulan yulanInput yulanInput_after" style="position:absolute;top: 8px;right:-17px; display: block;"></i><i changeapplybtn="" class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>'
                        //     $("#billAddress").find('.billList').eq(0).append(_htm)
                        // }

                        // if(billingList.length > 0){
                        //     $(".billList").each(function(index){
                        //         $(this).find("select[name='billingProvinceId']").attr("data-value",billingList[index].billingProvinceId);
                        //         $(this).find("select[name='billingCityId']").attr("data-value",billingList[index].billingCityId);
                        //         $(this).find("select[name='billingDistrictId']").attr("data-value",billingList[index].billingDistrictId);
                        //         $(this).find("select[name='billingStreetId']").attr("data-value",billingList[index].billingStreetId);
                        //         $(this).find("input[name='billingAddress']").val(billingList[index].billingAddress);
                        //
                        //         initCitys($(this),'billingProvinceId','billingCityId','billingDistrictId');
                        //     });
                        // }

                        /*$("input[name='parentCode']:not(:checked)").each(function() {
                            $(this).parent().parent().parent().children().not('.checkbox').hide();
                        });*/

                        if(vo.customerExtendItemVoList != null && vo.customerExtendItemVoList.length > 0){
                            for(var x = 0; x < vo.customerExtendItemVoList.length; x ++){
                                var item = vo.customerExtendItemVoList[x];
                                var code = item.code;
                                var itemVoList = vo.customerExtendItemVoList[x].itemVoList;
                                $('input[name="parentCode"][value='+code+']').removeAttr("checked");
                                if(item.value==1){
                                    $('input[name="parentCode"][value='+code+']').prop("checked","checked");
                                }
                                if(itemVoList){
                                    for(var y=0; y<itemVoList.length;y++){
                                        var childItem = itemVoList[y];
                                        var childCode = childItem.code;
                                        $('input[name="code"][value='+childCode+']').next().val(childItem.value);
                                    }
                                }
                            }
                        }
                        balanceTypeCheck();
                        // 批准文件
                        initTable3(customerBaseVo.id,3);
                        // 质量保证协议
                        // initTable1(customerBaseVo.id,3);
                        // 客户委托书
                        initTable2(customerBaseVo.id,3);
                        // 被委托人身份证
                        initCertTable(customerBaseVo.id,3);
                        //客户合同回显
                        // contract(customerBaseVo.id,3);
                        //特殊经营范围回显
                        var specialBusinessScope = customerBaseVo.specialBusinessScope.split(',');
                        $("input[name='specialBusinessScope']").each(function () {
                            $(this).removeAttr("checked");
                            for(var x = 0; x < specialBusinessScope.length; x ++){
                                if($(this).val() == specialBusinessScope[x]){
                                    $(this).prop("checked","checked");
                                }
                            }
                        });
                        //经营类别回显
                        var businessCategory = [];
                        if (customerBaseVo.businessCategory && customerBaseVo.businessCategory.indexOf(",")>0){
                            businessCategory = customerBaseVo.businessCategory.split(',');
                        } else if (customerBaseVo.businessCategory) {
                            businessCategory.push(customerBaseVo.businessCategory);
                        }
                        $("input[name='businessCategoryName']").each(function () {
                            $(this).prop("checked",false);
                        });
                        if (businessCategory.length>0) {
                            $("input[name='businessCategoryName']").each(function () {
                                for(var x = 0; x < businessCategory.length; x ++){
                                    if($(this).val() == businessCategory[x]){
                                        $(this).prop("checked","checked");
                                    }
                                }
                            });
                        } else {
                            $("input[name='businessCategoryName']").each(function () {
                                $(this).prop("checked",false);
                            });
                        }
                        //不可经营类别回显 wgf
                        var cannotBusinessCategory = [];
                        if (customerBaseVo.cannotBusinessCategory && customerBaseVo.cannotBusinessCategory.indexOf(",")>0){
                            cannotBusinessCategory = customerBaseVo.cannotBusinessCategory.split(',');
                        } else if (customerBaseVo.cannotBusinessCategory) {
                            cannotBusinessCategory.push(customerBaseVo.cannotBusinessCategory);
                        }
                        $("input[name='cannotBusinessCategoryName']").each(function () {
                            $(this).prop("checked",false);
                        });

                        if (cannotBusinessCategory.length>0) {
                            $("input[name='cannotBusinessCategoryName']").each(function () {
                                for(var x = 0; x < cannotBusinessCategory.length; x ++){
                                    if($(this).val() == cannotBusinessCategory[x]){
                                        $(this).prop("checked","checked");
                                    }
                                }
                            });
                        } else {
                            $("input[name='cannotBusinessCategoryName']").each(function () {
                                $(this).prop("checked",false);
                            });
                        }

                        disable();
                        $.clear_changeApply();
                        $("#changeApplyBtn").show();

                        /**
                         * 开票信息  非独立经营证明
                         */
                        let return_customerFile = data.result.customerEnclosureVoList;
                        let customerFileHtml = '';
                        let curimgLen = $('.fileIcon_div')
                        if(return_customerFile.length > 0){
                            for(let i = 0, len = return_customerFile.length; i< len; i++ ){
                                customerFileHtml +=  `
                                    <div class="fileIcon_div" data-index="`+((curimgLen.length == 0)?'0':'1')+`">
                                        <p class="btn btn-info btn-xs fileIcon_p" data-imgUrl="`+return_customerFile[i].url+`" style="position: relative; margin-right: 10px;">
                                            <span class="glyphicon glyphicon-picture"></span>
                                        </p>
                                        <div class="btn-group fileIcon_showTag" style="position: absolute; top:-23px ; left: -27px;width: 72px; height: 22px; display: none;">
                                            <button type="button" class="btn btn-default btn-xs btn_fileView" onclick="btn_fileView(this)">预览</button>
                                            <button type="button" class="btn btn-default btn-xs btn_fileClean" onclick="btn_fileClean(this)">清空</button>
                                        </div>
                                    </div>
                                `
                            };
                            $('.btn_customerFile').parents('.form-control').find('.fileIcon_p').remove();
                            $('.btn_customerFile').prev().before(customerFileHtml)
                        }
                        $('[name=billingCompanyName]').val(data.result.baseVo.billingCompanyName);
                        $('[name=taxpayerNum]').val(data.result.baseVo.taxpayerNum);
                        $('[name=billingOpeningBank]').val(data.result.baseVo.billingOpeningBank);
                        $('[name=billingBankAccount]').val(data.result.baseVo.billingBankAccount);
                        $('[name=businessLicenseAddress]').val(data.result.baseVo.businessLicenseAddress);
                        $('[name=billingPhone]').val(data.result.baseVo.billingPhone);
                        $(":radio[name='invoiceType'][value='" + data.result.baseVo.invoiceType + "']").prop("checked", "checked");
                        $(":radio[name='isIndependent'][value='" + data.result.baseVo.isIndependent + "']").prop("checked", "checked");
                        //当 数据加载进来发票类型 为增值税专用发票的时候 需要给营业执照地址和电话添加必填校验
                        if(data.result.baseVo.invoiceType == '2'){
                            $('[name=businessLicenseAddress]')['addClass']('{validate:{ required :true}}');
                            $('[name=businessLicenseAddress]').prev().html('<i class="text-require">*  </i>营业执照地址');
                            $('[name=billingPhone]').prev().html('<i class="text-require">*  </i>电话');
                            $('[name=billingPhone]').removeClass().addClass('form-control {validate:{required :true, isTel :true}}');
                        }
                        //当 页面加载进来
                        //当发票类型为 【增值税专用发票】或 【【增值税电子专用发票时】或【专票】时，基础属性中的【开户银行】，【开户户名】，【银行账号】为必填项，同时 开票信息中的 【开户银行】【银行账号】也为必填项。
                        //当发票类型为 非【增值税专用发票】或 【增值税电子专用发票时】时，基础属性中的【开户银行】，【开户户名】，【银行账号】为非必填项，同时 开票信息中的 【开户银行】【银行账号】也为非必填项。
                        if ($('#billInfo_form [name=invoiceType]:checked').val() == '1' || $('#billInfo_form [name=invoiceType]:checked').val() == '3' || $('#billInfo_form [name=invoiceType]:checked').val() == '5') {
                            $('[name=openingBank]').attr("class", "form-control {validate:{ }}")
                            $('[name=openingBank]').prev().html('开户银行')
                            $('[name=bankAccountName]').attr("class", "form-control {validate:{ }}")
                            $('[name=bankAccountName]').prev().html('开户户名')
                            $('[name=bankAccount]').attr("class", "form-control {validate:{ }}")
                            $('[name=bankAccount]').prev().html('银行账号')
                            $('[name=billingOpeningBank]').attr("class", "form-control {validate:{ }}")
                            $('[name=billingOpeningBank]').prev().html('开户银行')
                            $('[name=billingBankAccount]').attr("class", "form-control {validate:{ }}")
                            $('[name=billingBankAccount]').prev().html('银行账号')
                        } else {
                            $('[name=openingBank]').attr("class", "form-control {validate:{ required :true }}")
                            $('[name=openingBank]').prev().html('<i class="text-require">*  </i>开户银行')
                            $('[name=bankAccountName]').attr("class", "form-control {validate:{ required :true}}")
                            $('[name=bankAccountName]').prev().html('<i class="text-require">*  </i>开户户名')
                            $('[name=bankAccount]').attr("class", "form-control {validate:{ required :true}}")
                            $('[name=bankAccount]').prev().html('<i class="text-require">*  </i>银行账号')
                            $('[name=billingOpeningBank]').attr("class", "form-control {validate:{ required :true}}")
                            $('[name=billingOpeningBank]').prev().html('<i class="text-require">*  </i>开户银行')
                            $('[name=billingBankAccount]').attr("class", "form-control {validate:{ required :true}}")
                            $('[name=billingBankAccount]').prev().html('<i class="text-require">*  </i>银行账号')
                        }

                    },
                    complete:function(){
                        parent.hideLoading();
                    }
                })
            }
        },
        oniframeload: function () {}
    }).showModal();
    ev.stopPropagation();
}
/**
 * 对页面中表单元素进行赋值
 * @param json 必传 eg:{"a":1,"b":2}
 * @param jq对象 表示:从当前对象下找对应的元素进行赋值
 * */
function loadData(json,el) {
    var obj = json;
    var key, value, tagName, type, arr, thisVal,$el;
    for (x in obj) {
        key = x;
        value = obj[x];
        if(el)
        {
            $el=el.find("[name='" + key + "']");
        }else{
            $el=$("[name='" + key + "']");
        }
        $el.each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).attr('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    for (var i = 0; i < arr.length; i++) {
                        if (thisVal == arr[i]) {
                            $(this).attr('checked', true);
                            break;
                        }
                    }
                } else {
                    $(this).val(value);
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
            }
        });
    }
}
function getMnemonicCode(str,id){
    $.ajax({
        url:'/proxy-product/product/productFirst/getMnemonicCode?name='+str,
        type:'get',
        dataType:'json',
        async : false,
        success:function(data){
            if(data.code == 0)
            {
                $("#"+id).val(data.result);
            }
        }
    })
}
/**
 * 客户首营 customerFirstApplVo;
 * 批准文件 customerApprovalFileVoList;
 * 质量保证协议 customerQualityAgreementVoList;
 * 客户委托书 customerDelegationFileVoList;
 * 被委托人身份证 customerBeProxyIdentityVoList;
 * 客户合同 customerContractVos;
 * 发票邮寄地址 customerBillingAddressVoList;
 * 结算方式 customerExtendItemVoList;
 */
function pushData() {
    var customerChangeApprovalRecordInfoVo = {};
    /*var customerBaseVo = $("#customerApplVo").serializeToJSON();
    delete customerBaseVo.applicationDate;
    delete customerBaseVo.applicationNumber;
    delete customerBaseVo.applicant;
    delete customerBaseVo.applicantName;
    delete customerBaseVo.orgCodeName;
    delete customerBaseVo.openingBank;
    delete customerBaseVo.bankAccountName;
    delete customerBaseVo.customerExtendItemVoList;
    delete customerBaseVo.value;
    delete customerBaseVo.code;
    delete customerBaseVo.specialBusinessScope;
    customerBaseVo.accountName = $("input[name='bankAccountName']").val();

    if (customerBaseVo.specialBusinessScope && customerBaseVo.specialBusinessScope.indexOf(",") != -1){
        customerBaseVo.specialBusinessScope = customerBaseVo.specialBusinessScope.join(",");
    }
    var ss = JSON.stringify(customerBaseVo);*/
    // customerChangeApprovalRecordInfoVo.customerBaseVo = customerBaseVo;
    //变更申请基础属性
    var customerChangeApprovalRecordVo = $("#customerApplVo").serializeToJSON(); // 基础属性 form
    var billInfo_formVo = $('#billInfo_form').serializeToJSON(); // 开票信息 form
    customerChangeApprovalRecordVo = Object.assign(customerChangeApprovalRecordVo,billInfo_formVo);
    customerChangeApprovalRecordVo.applicationNumber=$("input[name='applicationNumber']").val();
    //customerChangeApprovalRecordVo.applicationDate=$("input[name='applicationDate']").val();
    customerChangeApprovalRecordVo.applicant=$("input[name='applicant']").val();
    customerChangeApprovalRecordVo.baseId=$("input[id='baseId']").val();
    customerChangeApprovalRecordVo.id=$("input[id='approvalId']").val();
    customerChangeApprovalRecordVo.approvalProcessId=$("input[id='approvalProcessId']").val();
    customerChangeApprovalRecordVo.auditStatus=$("#auditStatus").val();
    //数组变成字符串

    if(customerChangeApprovalRecordVo.specialBusinessScope == undefined) {
        customerChangeApprovalRecordVo.specialBusinessScope = "";
    }
    var specialBusinessScope = customerChangeApprovalRecordVo.specialBusinessScope.toString();
    //重新赋值
    customerChangeApprovalRecordVo.specialBusinessScope = specialBusinessScope;
    delete customerChangeApprovalRecordVo.businessPatternName;
    delete customerChangeApprovalRecordVo.billingProvinceId;
    delete customerChangeApprovalRecordVo.billingCityId;
    delete customerChangeApprovalRecordVo.billingDistrictId;
    delete customerChangeApprovalRecordVo.billingStreetId;
    delete customerChangeApprovalRecordVo.billingAddress;
    delete customerChangeApprovalRecordVo.customerExtendItemVoList;
    delete customerChangeApprovalRecordVo.parentCode;
    delete customerChangeApprovalRecordVo.value;
    delete customerChangeApprovalRecordVo.code;
    delete customerChangeApprovalRecordVo.customerTypeName;
    delete customerChangeApprovalRecordVo.businessCategoryName;
    delete customerChangeApprovalRecordVo.cannotBusinessCategoryName;

    delete window.changeApply.undefined
    //修改后数据拼装
    var changeApplyTemp = JSON.parse(JSON.stringify(window.changeApply));
    var customerChangeRecordDetailVoList = [];
    $.each(changeApplyTemp,function (c, v) {
        // 随货同行地址暨文本收货地址
        if(c =="accompanyAddress"){
            v.beforeText=v.valueBefore
        } else
        // 注册地址
        if(c=="registerList"){
            // 注册地址修改
            var after = v.valueAfter;
            var before = v.valueBefore;
            var changeStatus = v.changeStatus;
            var registerProvince = {
                "columnName":"注册地址省",
                "columnValue":"registerProvinceId",
                "valueAfter":after.registerProvince,
                "changeStatus":changeStatus
            };
            var registerCity = {
                "columnName":"注册地址市",
                "columnValue":"registerCityId",
                "valueAfter":after.registerCity,
                "changeStatus":changeStatus
            };
            var registerArea = {
                "columnName":"注册地址区",
                "columnValue":"registerAreaId",
                "valueAfter":after.registerArea,
                "changeStatus":changeStatus
            };
            var registerStreet = {
                "columnName":"注册地址街道",
                "columnValue":"registerStreetId",
                "valueAfter":after.registerStreet,
                "changeStatus":changeStatus
            };
            var registerDetail = {
                "columnName":"注册地址详细",
                "columnValue":"registerAddress",
                "valueAfter":after.registerDetail,
                "changeStatus":changeStatus,
                "afterText":after.registerDetail
            };
            if(before){
                registerProvince.valueBefore = before.registerProvince;
                registerCity.valueBefore = before.registerCity;
                registerArea.valueBefore = before.registerArea;
                registerStreet.valueBefore = before.registerStreet;
                registerDetail.valueBefore = before.registerDetail;
            }
            registerDetail.beforeText = $("input[name='registerAddress']").val();
            customerChangeRecordDetailVoList.push(registerProvince);
            customerChangeRecordDetailVoList.push(registerCity);
            customerChangeRecordDetailVoList.push(registerArea);
            customerChangeRecordDetailVoList.push(registerStreet);
            customerChangeRecordDetailVoList.push(registerDetail);
            return true;
        }else if(c=="customerDeliveryAddressVoList"){
            // 收货地址修改
                var temp = changeApplyTemp.customerDeliveryAddressVoList.valueAfter;
                if (typeof temp == 'string') {
                    temp = JSON.parse(temp);
                    if (typeof temp == 'string') {
                        temp = JSON.parse(temp);
                    }
                }
                if(Array.isArray(temp)&&temp.length>0){
                    temp=temp[0];
                }
                var customerDeliveryAddressVoList=[];
                if (temp){
                            var addrVo = {};
                            addrVo.billingProvinceId = temp.province2;
                            addrVo.billingCityId = temp.shippingAddressCityId;
                            addrVo.billingDistrictId = temp.shippingAddressDistrictId;
                            if(/.*[\u4e00-\u9fa5]+.*$/.test(temp.shippingAddressStreetId)){
                                addrVo.billingStreetId = '';
                            } else {
                                addrVo.billingStreetId = temp.shippingAddressStreetId;
                            }
                            addrVo.billingAddress = temp.shippingAddressInput;
                            addrVo.addressType=2;
                       customerDeliveryAddressVoList.push(addrVo);

                    function buildAddressArr(value) {
                        /**
                         * 服务端返回的 valueAfter 可能是 List(1)，此处对 List 进行拆解
                         */
                        if (value.length && value.length>0){
                            value = value[0]
                        }
                        const resultArr = []
                        resultArr.push(ChineseDistricts[86][value.province2]);
                        resultArr.push(ChineseDistricts[value.province2][value.shippingAddressCityId]);
                        resultArr.push((ChineseDistricts[value.shippingAddressCityId][value.shippingAddressDistrictId])?ChineseDistricts[value.shippingAddressCityId][value.shippingAddressDistrictId]:ChineseDistricts[value.province2][value.shippingAddressDistrictId]);
                        /***
                         * 鉴于项目中对 JS 省市区字典文件的街道信息不全，且长期未进行维护。
                         * 对于街道信息，通过 AJAX 方式获取。
                         */
                        if (value.shippingAddressStreetId){
                            $.ajax({
                                type: 'get',
                                url: '/proxy-sysmanage/sysmanage/area/getNexAreaListByPcode?pCode=' + value.shippingAddressDistrictId,
                                async: false,
                                success: function (response) {
                                    if (response.code == 0){
                                        response.result.filter(item => item.code == value.shippingAddressStreetId).map(item => {
                                          resultArr.push(item.name)
                                        })
                                    }
                                }
                            })
                        }
                        resultArr.push(value.shippingAddressInput);
                        return resultArr
                    }

                    /**
                     * 从页面 DOM 获取变更前收货地址
                     */
                    const customerDeliveryAddressDetailElement = $('[changeapplyflag=customerDeliveryAddressVoList]')
                    let beforeTextArr = []
                    customerDeliveryAddressDetailElement.parents('div.row').eq(0).find('select').find('option:selected').each((index,value)=> {
                        beforeTextArr.push(value.text)
                    })
                    beforeTextArr.push(customerDeliveryAddressDetailElement.val())

                    var billingAddressVoList = {
                            "columnName":"收货地址",
                            "columnValue":"customerDeliveryAddressVoList",
                            "changeStatus":changeApplyTemp.customerDeliveryAddressVoList.changeStatus,
                            "valueAfter":retrunAfterVal(changeApplyTemp.customerDeliveryAddressVoList.valueAfter),
                            "valueBefore":retrunAfterVal(changeApplyTemp.customerDeliveryAddressVoList.valueBefore),
                            "afterText":buildAddressArr(v.valueAfter).join('-'),
                            "beforeText":beforeTextArr.join('-')
                        };
                        function retrunAfterVal(_thisdata) {
                            var _objs={};
                            var _boole=false;
                            if(!_thisdata){
                                return _thisdata;
                            }
                            _thisdata=JSON.parse(JSON.stringify(_thisdata));
                            if(Array.isArray(_thisdata)){
                                _thisdata=_thisdata[0];
                            }
                            if(_thisdata.hasOwnProperty("province2")){
                                _objs.repertoryProvince=_thisdata.province2;
                                _boole=true;
                            }
                            if(_thisdata.hasOwnProperty("shippingAddressCityId")){
                                _objs.repertoryCity=_thisdata.shippingAddressCityId;
                                _boole=true;
                            }
                            if(_thisdata.hasOwnProperty("shippingAddressDistrictId")){
                                _objs.repertoryArea=_thisdata.shippingAddressDistrictId;
                                _boole=true;
                            }
                            if(_thisdata.hasOwnProperty("shippingAddressStreetId")){
                                _objs.registerStreet=_thisdata.shippingAddressStreetId;
                                _boole=true;
                            }
                            if(_thisdata.hasOwnProperty("shippingAddressInput")||thisdata.hasOwnProperty("billingAddress")){
                                _objs.repertoryDetail=_thisdata.shippingAddressInput;
                            }
                            return _boole?[_objs]:[_thisdata];
                        }
                    customerChangeRecordDetailVoList.push(billingAddressVoList);
                    customerChangeApprovalRecordInfoVo.customerDeliveryAddressVoList = customerDeliveryAddressVoList;
                    // 字段变更表删除修改前后字段
                    delete changeApplyTemp.customerDeliveryAddressVoList.valueBefore;
                    delete changeApplyTemp.customerDeliveryAddressVoList.valueAfter;
                }
                return true;
        } else if(c=="customerStorageAddressVOList"){
            // 仓库地址
            var after = v.valueAfter;
            var before = v.valueBefore;
            var changeStatus = v.changeStatus;
            var storageProvince = {
                "columnName":"仓库地址省",
                "columnValue":"storageProvinceId",
                "valueAfter":after.repertoryProvince,
                "changeStatus":changeStatus
            };
            var storageCity = {
                "columnName":"仓库地址市",
                "columnValue":"storageCityId",
                "valueAfter":after.repertoryCity,
                "changeStatus":changeStatus
            };
            var storageArea = {
                "columnName":"仓库地址区",
                "columnValue":"storageAreaId",
                "valueAfter":after.repertoryArea,
                "changeStatus":changeStatus
            };
            var storageStreet = {
                "columnName":"仓库地址街道",
                "columnValue":"storageStreetId",
                "valueAfter":after.registerStreet,
                "changeStatus":changeStatus
            };
            var storageDetail = {
                "columnName":"仓库地址详情",
                "columnValue":"storageAddress",
                "valueAfter":after.repertoryDetail,
                "changeStatus":changeStatus
            };
            if(before){
                storageProvince.valueBefore=before.repertoryProvince;
                storageCity.valueBefore=before.repertoryCity;
                storageArea.valueBefore=before.registerList;
                storageStreet.valueBefore=before.registerStreet;
                storageDetail.valueBefore=before.repertoryDetail;
            }
            customerChangeRecordDetailVoList.push(storageProvince);
            customerChangeRecordDetailVoList.push(storageArea);
            customerChangeRecordDetailVoList.push(storageCity);
            customerChangeRecordDetailVoList.push(storageDetail);
            customerChangeRecordDetailVoList.push(storageStreet);
            return true;
        }
        if (c == 'customerStorageAddressList') {
            return true;
        }
        // 发票邮寄地址
        if(c=="customerBillingAddressVoList") {
            // 修改后的账单集合
            /**
             * RM 2018-10-15
             * 如果进入录入状态下的详情页，没有修改，直接点提交审核的话是 没有valueAfter属性的，提交时就会报错
             */
            var temp = changeApplyTemp.customerBillingAddressVoList.valueAfter;
            if (typeof temp == 'string') {
                temp = JSON.parse(temp);
                if (typeof temp == 'string') {
                    temp = JSON.parse(temp);
                }
            }
            var customerBillingAddressVoList=[];
            if (temp){
                if(temp.length>0){
                    for(var i=0;i<temp.length;i++){
                        var addrVo = {};
                        var addr = temp[i];
                        addrVo.billingProvinceId = addr.repertoryProvince;
                        addrVo.billingCityId = addr.repertoryCity;
                        addrVo.billingDistrictId = addr.repertoryArea;
                        if(/.*[\u4e00-\u9fa5]+.*$/.test(addr.registerStreet)){
                            addrVo.billingStreetId = '';
                        } else {
                            addrVo.billingStreetId = addr.registerStreet;
                        }
                        addrVo.billingAddress = addr.repertoryDetail;
                        addrVo.addressType=1;
                        customerBillingAddressVoList.push(addrVo);
                    }
                    var billingAddressVoList = {
                        "columnName":"发票邮寄地址",
                        "columnValue":"customerBillingAddressVoList",
                        "changeStatus":changeApplyTemp.customerBillingAddressVoList.changeStatus,
                        "valueAfter":JSON.stringify(changeApplyTemp.customerBillingAddressVoList.valueAfter),
                        "valueBefore":JSON.stringify(changeApplyTemp.customerBillingAddressVoList.valueBefore),
                    };
                    customerChangeRecordDetailVoList.push(billingAddressVoList);
                }
                customerChangeApprovalRecordInfoVo.customerBillingAddressVoList = customerBillingAddressVoList;
                // 字段变更表删除修改前后字段
                delete changeApplyTemp.customerBillingAddressVoList.valueBefore;
                delete changeApplyTemp.customerBillingAddressVoList.valueAfter;
            }
            return true;
        }else
        // 结算方式
        if(c=="customerExtendItemVoList"){
            // 修改后的结算方式
            var customerExtendItemVoList = changeApplyTemp.customerExtendItemVoList.valueAfter;
            customerChangeApprovalRecordInfoVo.customerExtendItemVoList = [];
            var temp = [];
            if(customerExtendItemVoList){
                for(var i=0;i<customerExtendItemVoList.length;i++){
                    var bean = customerExtendItemVoList[i];
                    if(!customerExtendItemVoList[i].code ){
                        customerExtendItemVoList[i].code=customerExtendItemVoList[i].parentCode;
                        customerExtendItemVoList[i].parentCode=0;
                    }else{
                        temp.push(customerExtendItemVoList[i].parentCode);
                    }
                    customerChangeApprovalRecordInfoVo.customerExtendItemVoList.push(bean);
                }
                temp = uniq(temp);
                for(var i=0;i<temp.length;i++){
                    var code = {
                        "parentCode":0,
                        "code":temp[i]
                    }
                    customerChangeApprovalRecordInfoVo.customerExtendItemVoList.push(code);
                }
                var extendItemVOList = {
                    "columnName":"结算方式",
                    "columnValue":"customerExtendItemVoList",
                    "changeStatus":changeApplyTemp.customerExtendItemVoList.changeStatus,
                    "valueAfter":JSON.stringify(changeApplyTemp.customerExtendItemVoList.valueAfter),
                    "valueBefore":JSON.stringify(changeApplyTemp.customerExtendItemVoList.valueBefore)
                };
                customerChangeRecordDetailVoList.push(extendItemVOList);
            }
            // 字段变更表删除修改前后字段
            delete changeApplyTemp.customerExtendItemVoList.valueBefore;
            delete changeApplyTemp.customerExtendItemVoList.valueAfter;
            return true;
        }
        function uniq(array){
            var temp = []; //一个新的临时数组
            for(var i = 0; i < array.length; i++){
                if(temp.indexOf(array[i]) == -1){
                    temp.push(array[i]);
                }
            }
            return temp;
        }
        if (c == 'register') {
            //注册地址修改
            return true;
        }
        customerChangeRecordDetailVoList.push(v);
    });
    customerChangeApprovalRecordInfoVo.customerChangeApprovalRecordVo = customerChangeApprovalRecordVo;
    // table等特殊数据
    var changeApplyListTemp = JSON.parse(JSON.stringify(window.changeApplyList));
    // 批准文件 supplierApprovalFileList:table3,
    if(changeApplyListTemp.customerApprovalFileVoList) {
        var table3 = changeApplyListTemp.customerApprovalFileVoList.valueAfter;
        if (changeApplyListTemp.customerApprovalFileVoList.changeStatus != 0 && table3 && table3.length > 0) {
            for(var i=0;i<table3.length;i++){
                if (table3[i].scopeofoperationVo && typeof table3[i].scopeofoperationVo == 'string'){
                    table3[i].scopeofoperationVo = JSON.parse(table3[i].scopeofoperationVo);
                }
                var ss = table3[i].scopeofoperationVo;
                if (ss == "") {
                    table3[i].scopeofoperationVo = [];
                }
                if($("#shippingSource").length&&$("#shippingSource").val()!=3){
                   if(detailAllData.customerApprovalFileVoList&&detailAllData.customerApprovalFileVoList.valueAfter&&detailAllData.customerApprovalFileVoList.valueAfter.length>0){
                        if(detailAllData.customerApprovalFileVoList.valueAfter.length==table3.length){
                            table3[i].changeStatus=detailAllData.customerApprovalFileVoList.valueAfter[i].changeStatus||0;
                            changeApplyListTemp.customerApprovalFileVoList.valueAfter[i].changeStatus=detailAllData.customerApprovalFileVoList.valueAfter[i].changeStatus||0
                        }
                   }
                }
                // delete table3[i].id;
                // delete table3[i].scopeofoperationVo;
                //处理经营范围数据
                var scopeList=table3[i].customerBusinessScopeVoList;
                if(scopeList && scopeList.length > 0){
                    var approvalScopeList=[];
                    var scopeJson={};
                    for(var x=0;x<scopeList.length;x++)
                    {
                        if(scopeList[x].name != '经营范围'){
                            scopeJson={};
                            scopeJson.businessScopeCode=(scopeList[x].id != undefined)?scopeList[x].id:scopeList[x].businessScopeCode;
                            approvalScopeList.push(scopeJson);
                        }
                    }
                    table3[i].customerBusinessScopeVoList=approvalScopeList;
                }
                if(typeof table3[i].customerEnclosureVoList == 'string')
                {
                    table3[i].customerEnclosureVoList=JSON.parse(table3[i].customerEnclosureVoList);
                    if(typeof table3[i].customerEnclosureVoList == 'string')
                    {
                        table3[i].customerEnclosureVoList=JSON.parse(table3[i].customerEnclosureVoList);
                        if(typeof table3[i].customerEnclosureVoList == 'string')
                        {
                            table3[i].customerEnclosureVoList=JSON.parse(table3[i].customerEnclosureVoList);
                        }
                    }
                }
                if (table3[i].customerEnclosureVoList){
                    for(var j=0;j<table3[i].customerEnclosureVoList.length;j++)
                    {
                        delete table3[i].customerEnclosureVoList[j].type
                    }
                }

            }
            customerChangeApprovalRecordInfoVo.customerApprovalFileVoList = table3;
            if($("#shippingSource").length&&$("#shippingSource").val()==3){
                customerChangeApprovalRecordInfoVo.customerChangeApprovalRecordVo.changeType=1;
            }
            var approvalFileVoList = {
                "columnName":"批准文件",
                "columnValue":"customerApprovalFileVoList",
                "changeStatus":changeApplyListTemp.customerApprovalFileVoList.changeStatus,
                "valueAfter":JSON.stringify(changeApplyListTemp.customerApprovalFileVoList.valueAfter),
                "valueBefore":JSON.stringify(changeApplyListTemp.customerApprovalFileVoList.valueBefore)
            };
            customerChangeRecordDetailVoList.push(approvalFileVoList);
        }
        // 字段变更表删除修改前后字段
        delete changeApplyListTemp.customerApprovalFileVoList.valueBefore;
        delete changeApplyListTemp.customerApprovalFileVoList.valueAfter;
    }
    //被委托人身份证customerBeProxyIdentityVoList:table4
    if (changeApplyListTemp.customerBeProxyIdentityVoList) {
        var table4 = changeApplyListTemp.customerBeProxyIdentityVoList.valueAfter;
        if(table4 && table4.length > 0){
            if (changeApplyListTemp.customerBeProxyIdentityVoList.changeStatus != 0){
                if (table4){
                    for(var i=0;i<table4.length;i++){
                        if (typeof table4[i].customerEnclosureVoList == 'string') {
                            table4[i].customerEnclosureVoList = JSON.parse(table4[i].customerEnclosureVoList);
                            if (typeof table4[i].customerEnclosureVoList == 'string') {
                                table4[i].customerEnclosureVoList = JSON.parse(table4[i].customerEnclosureVoList);
                            }
                        }
                        if (table4[i].customerEnclosureVoList && table4[i].customerEnclosureVoList.length > 0){
                            for (var j = 0; j<table4[i].customerEnclosureVoList.length; j++) {
                                table4[i].customerEnclosureVoList[j].enclosureType = 5;
                                if (table4[i].customerEnclosureVoList[j].type) {
                                    delete table4[i].customerEnclosureVoList[j].type;
                                }
                                if (table4[i].customerEnclosureVoList[j].lineNum) {
                                    delete table4[i].customerEnclosureVoList[j].lineNum;
                                }
                            }
                        }
                        if($("#shippingSource").length&&$("#shippingSource").val()!=3){
                            if(detailAllData.customerBeProxyIdentityVoList&&detailAllData.customerBeProxyIdentityVoList.valueAfter&&detailAllData.customerBeProxyIdentityVoList.valueAfter.length>0){
                                if(detailAllData.customerBeProxyIdentityVoList.valueAfter.length==table4.length){
                                    table4[i].changeStatus=detailAllData.customerBeProxyIdentityVoList.valueAfter[i].changeStatus||0;
                                    changeApplyListTemp.customerBeProxyIdentityVoList.valueAfter[i].changeStatus=detailAllData.customerBeProxyIdentityVoList.valueAfter[i].changeStatus||0;
                                }
                            }
                        }
                    }
                }
                customerChangeApprovalRecordInfoVo.customerBeProxyIdentityVoList = table4;
                if($("#shippingSource").length&&$("#shippingSource").val()==3){
                    customerChangeApprovalRecordInfoVo.customerChangeApprovalRecordVo.changeType=1;
                }
                var beProxyIdentityVoList = {
                    "columnName":"被委托人身份证",
                    "columnValue":"customerBeProxyIdentityVoList",
                    "changeStatus":changeApplyListTemp.customerBeProxyIdentityVoList.changeStatus,
                    "valueAfter":JSON.stringify(changeApplyListTemp.customerBeProxyIdentityVoList.valueAfter),
                    "valueBefore":JSON.stringify(changeApplyListTemp.customerBeProxyIdentityVoList.valueBefore)
                };
                customerChangeRecordDetailVoList.push(beProxyIdentityVoList);
            }
        }else{
            parent.hideLoading();
            utils.dialog({
                title: '提示',
                content: '被委托人身份证信息不能为空.',
                okValue: '确定',
                ok: function () {}
            }).showModal();
            disable();
            return false;
        }
        // 字段变更表删除修改前后字段
        delete changeApplyListTemp.customerBeProxyIdentityVoList.valueBefore;
        delete changeApplyListTemp.customerBeProxyIdentityVoList.valueAfter;
    }
    //客户委托书customerDelegationFileVoList:table5
    if (changeApplyListTemp.customerDelegationFileVoList) {
        var table5 = changeApplyListTemp.customerDelegationFileVoList.valueAfter;
        if (changeApplyListTemp.customerDelegationFileVoList.changeStatus != 0 && table5 && table5.length > 0){
            for(var i=0;i<table5.length;i++){
                var customerEnclosureVoList = table5[i].customerEnclosureVoList;
                if (typeof customerEnclosureVoList == 'string'){
                    customerEnclosureVoList = JSON.parse(customerEnclosureVoList);
                    if (typeof customerEnclosureVoList == 'string') {
                        customerEnclosureVoList = JSON.parse(customerEnclosureVoList);
                    }
                    table5[i].customerEnclosureVoList = customerEnclosureVoList;
                    if (table5[i].customerEnclosureVoList && table5[i].customerEnclosureVoList.length > 0){
                        for (var j = 0;j<table5[i].customerEnclosureVoList.length; j++){
                            if (table5[i].customerEnclosureVoList[j].type) {
                                delete table5[i].customerEnclosureVoList[j].type;
                            }
                            if (table5[i].customerEnclosureVoList[j].lineNum) {
                                delete table5[i].customerEnclosureVoList[j].lineNum;
                            }
                            table5[i].customerEnclosureVoList[j].enclosureType = 2;
                            if($("#shippingSource").length&&$("#shippingSource").val()!=3){
                                if(detailAllData.customerDelegationFileVoList&&detailAllData.customerDelegationFileVoList.valueAfter&&detailAllData.customerDelegationFileVoList.valueAfter.length>0){
                                    if(detailAllData.customerDelegationFileVoList.valueAfter.length==table5.length){
                                        table5[i].changeStatus=detailAllData.customerDelegationFileVoList.valueAfter[i].changeStatus||0;
                                        changeApplyListTemp.customerDelegationFileVoList.valueAfter[i].changeStatus=detailAllData.customerDelegationFileVoList.valueAfter[i].changeStatus||0;
                                    }
                                }
                            }

                        }

                    }
                }

                /*for(var j=0;j<customerEnclosureVoList.length;j++)
                {
                    delete table5[i].customerEnclosureVoList[j].type
                }*/
            }
            customerChangeApprovalRecordInfoVo.customerDelegationFileVoList = table5;
            if($("#shippingSource").length&&$("#shippingSource").val()==3){
                customerChangeApprovalRecordInfoVo.customerChangeApprovalRecordVo.changeType=1;
            }
            var delegationFileVoList = {
                "columnName":"客户委托书",
                "columnValue":"customerDelegationFileVoList",
                "changeStatus":changeApplyListTemp.customerDelegationFileVoList.changeStatus,
                "valueAfter":JSON.stringify(changeApplyListTemp.customerDelegationFileVoList.valueAfter),
                "valueBefore":JSON.stringify(changeApplyListTemp.customerDelegationFileVoList.valueBefore)
            };
            customerChangeRecordDetailVoList.push(delegationFileVoList);
        }else{
            parent.hideLoading();
            utils.dialog({
                title: '提示',
                content: '客户委托书信息不能为空.',
                okValue: '确定',
                ok: function () {}
            }).showModal()
            disable();
            return false;
        }
        // 字段变更表删除修改前后字段
        delete changeApplyListTemp.customerDelegationFileVoList.valueBefore;
        delete changeApplyListTemp.customerDelegationFileVoList.valueAfter;
    }
    //质量保障协议customerQualityAgreementVoList
    // if (changeApplyListTemp.customerQualityAgreementVoList) {
    //     var table6 = changeApplyListTemp.customerQualityAgreementVoList.valueAfter;
    //
    //     if (changeApplyListTemp.customerQualityAgreementVoList.changeStatus != 0 && table6 ){
    //         for(var i=0;i<table6.length;i++){
    //             var ad = table6[i].customerEnclosureVoList;
    //             if (table6[i].customerEnclosureVoList && typeof table6[i].customerEnclosureVoList == 'string') {
    //                 table6[i].customerEnclosureVoList = JSON.parse(ad);
    //             }
    //             if (ad == '[]'){
    //                 delete table6[i].customerEnclosureVoList;
    //             }
    //             if (table6[i].customerEnclosureVoList && table6[i].customerEnclosureVoList.length > 0){
    //                 for (var j = 0; j<table6[i].customerEnclosureVoList.length; j++ ){
    //                     if (table6[i].customerEnclosureVoList[j].type) {
    //                         delete table6[i].customerEnclosureVoList[j].type;
    //                     }
    //                     if (table6[i].customerEnclosureVoList[j].lineNum) {
    //                         delete table6[i].customerEnclosureVoList[j].lineNum;
    //                     }
    //                     table6[i].customerEnclosureVoList[j].enclosureType = 1;
    //                 }
    //                 if ($("#shippingSource").length && $("#shippingSource").val() != 3) {
    //                     if (detailAllData.customerQualityAgreementVoList && detailAllData.customerQualityAgreementVoList.valueAfter && detailAllData.customerQualityAgreementVoList.valueAfter.length > 0) {
    //                         if (detailAllData.customerQualityAgreementVoList.valueAfter.length == table6.length) {
    //                             table6[i].changeStatus = detailAllData.customerQualityAgreementVoList.valueAfter[i].changeStatus || 0;
    //                             changeApplyListTemp.customerQualityAgreementVoList.valueAfter[i].changeStatus = detailAllData.customerQualityAgreementVoList.valueAfter[i].changeStatus || 0;
    //                         }
    //                     }
    //                 }
    //
    //             }
    //         }
    //         customerChangeApprovalRecordInfoVo.customerQualityAgreementVoList = table6;
    //         if($("#shippingSource").length&&$("#shippingSource").val()==3){
    //             customerChangeApprovalRecordInfoVo.customerChangeApprovalRecordVo.changeType=1;
    //         }
    //         var qualityAgreementVoList = {
    //             "columnName":"质量保证协议",
    //             "columnValue":"customerQualityAgreementVoList",
    //             "changeStatus":changeApplyListTemp.customerQualityAgreementVoList.changeStatus,
    //             "valueAfter":JSON.stringify(changeApplyListTemp.customerQualityAgreementVoList.valueAfter),
    //             "valueBefore":JSON.stringify(changeApplyListTemp.customerQualityAgreementVoList.valueBefore)
    //         };
    //         customerChangeRecordDetailVoList.push(qualityAgreementVoList);
    //     }
    //     // 字段变更表删除修改前后字段
    //     delete changeApplyListTemp.customerQualityAgreementVoList.valueBefore;
    //     delete changeApplyListTemp.customerQualityAgreementVoList.valueAfter;
    // }
    //客户合同 customerContractVos
    // if (changeApplyListTemp.customerContractVos) {
    //     var table7 = changeApplyListTemp.customerContractVos.valueAfter;
    //     if (changeApplyListTemp.customerContractVos.changeStatus != 0){
    //         if (table7) {
    //             for (var i=0;i < table7.length;i++){
    //                 if (table7[i].certificateType) {
    //                     table7[i].customerEnclosureVoList = [];
    //                     table7[i].contractType = table7[i].certificateType;
    //                     delete table7[i].certificateType;
    //                 }
    //                 if (table7[i].enclosureList && table7[i].enclosureList.length > 0){
    //                     for (var j = 0; j<table7[i].enclosureList.length; j++){
    //                         if (table7[i].enclosureList[j].type) {
    //                             delete table7[i].enclosureList[j].type;
    //                         }
    //                         if (table7[i].enclosureList[j].lineNum) {
    //                             delete table7[i].enclosureList[j].lineNum;
    //                         }
    //                         table7[i].enclosureList[j].enclosureType = 6;
    //                         table7[i].customerEnclosureVoList.push(table7[i].enclosureList[j]);
    //                     }
    //                 }
    //                 delete table7[i].enclosureList;
    //             }
    //         }
    //         customerChangeApprovalRecordInfoVo.customerContractVos = table7;
    //         if($("#shippingSource").length&&$("#shippingSource").val()==3){
    //             customerChangeApprovalRecordInfoVo.customerChangeApprovalRecordVo.changeType=1;
    //         }
    //         var contractVos = {
    //             "columnName":"客户合同",
    //             "columnValue":"customerContractVos",
    //             "changeStatus":changeApplyListTemp.customerContractVos.changeStatus,
    //             "valueAfter":JSON.stringify(changeApplyListTemp.customerContractVos.valueAfter),
    //             "valueBefore":JSON.stringify(changeApplyListTemp.customerContractVos.valueBefore)
    //         };
    //         customerChangeRecordDetailVoList.push(contractVos);
    //     }
    //     // 字段变更表删除修改前后字段
    //     delete changeApplyListTemp.customerContractVos.valueBefore;
    //     delete changeApplyListTemp.customerContractVos.valueAfter;
    // }

    if (customerChangeRecordDetailVoList && customerChangeRecordDetailVoList.length > 0) {
        for (var i = 0; i < customerChangeRecordDetailVoList.length; i++){
            if (typeof customerChangeRecordDetailVoList[i].valueAfter != 'string'){
                customerChangeRecordDetailVoList[i].valueAfter = JSON.stringify(customerChangeRecordDetailVoList[i].valueAfter);
            }
            if (typeof customerChangeRecordDetailVoList[i].valueBefore != 'string'){
                customerChangeRecordDetailVoList[i].valueBefore = JSON.stringify(customerChangeRecordDetailVoList[i].valueBefore);
            }
        }
    }

    if (customerChangeRecordDetailVoList && customerChangeRecordDetailVoList.length > 0){
        var n = customerChangeRecordDetailVoList.length;
        for (var i=0; i<n; i++){
            var detail = customerChangeRecordDetailVoList[i];
            if (typeof(detail) == "undefined" || detail == null){
                break;
            }
            var beforeText = '';
            if(detail.columnValue == 'isIndependent'){ // 是否独立核算
                beforeText = $("input[name=isIndependent]:checked").val();
                if(beforeText == '1'){
                    detail.beforeText = '是';
                }else if(beforeText == '0'){
                    detail.beforeText = '否';
                }
                detail.columnName = '是否独立核算';
            }
            if(detail.columnValue == 'billingCompanyName'){ // 公司名称
                beforeText = $("input[name=billingCompanyName]").val();
                detail.beforeText = beforeText;
                detail.columnName = '发票信息公司名称';
            }
            if(detail.columnValue == 'taxpayerNum'){ // 纳税人识别号
                beforeText = $("input[name=taxpayerNum]").val();
                detail.beforeText = beforeText;
                detail.columnName = '纳税人识别号';
            }
            if(detail.columnValue == 'billingOpeningBank'){ // 开户银行
                beforeText = $("input[name=billingOpeningBank]").val();
                detail.beforeText = beforeText;
                detail.columnName = '发票信息开户银行';
            }
            if(detail.columnValue == 'billingBankAccount'){ // 银行账号
                beforeText = $("input[name=billingBankAccount]").val();
                detail.beforeText = beforeText;
                detail.columnName = '发票信息银行账号';
            }
            if(detail.columnValue == 'businessLicenseAddress'){ // 营业执照地址
                beforeText = $("input[name=businessLicenseAddress]").val();
                detail.beforeText = beforeText;
                detail.columnName = '发票信息营业执照地址';
            }
            if(detail.columnValue == 'billingPhone'){ // 电话
                beforeText = $("input[name=billingPhone]").val();
                detail.beforeText = beforeText;
                detail.columnName = '发票信息电话';
            }
            if (detail.columnValue == 'customerType') {
                beforeText = $("input[id='customerTypeName']").val();
                detail.beforeText = beforeText;
                detail.columnName = '客户类别';
            } else if (detail.columnValue == 'businessCategoryName') {
                detail.columnName = '经营类别';
                detail.beforeText = $("#businessCategoryName").val();
                if(detail.beforeText==''){
                    detail.valueBefore = '';
                }else if ($.trim(detail.valueBefore) == '非处方药甲类') {
                    detail.valueBefore = '2';
                }else if ($.trim(detail.valueBefore) == '处方药') {
                    detail.valueBefore = '1';
                }else if ($.trim(detail.valueBefore) == '非处方药乙类') {
                    detail.valueBefore = '3';
                }else if ($.trim(detail.valueBefore) == '中药饮片') {
                    detail.valueBefore = '4';
                }
                if ($.trim(detail.valueAfter) == '处方药') {
                    detail.valueAfter = '1';
                } else if ($.trim(detail.valueAfter) == '非处方药甲类'){
                    detail.valueAfter = '2';
                } else if ($.trim(detail.valueAfter) == '非处方药乙类'){
                    detail.valueAfter = '3';
                } else if ($.trim(detail.valueAfter) == '中药饮片'){
                    detail.valueAfter = '4';
                }
                //增加不可经营类别  wgf20200313
            }  else if (detail.columnValue == 'cannotBusinessCategoryName') {
                detail.columnName = '不可经营类别';
                detail.beforeText = $("#cannotBusinessCategoryName").val();
                if(detail.beforeText==''){
                    detail.valueBefore = '';
                }else if ($.trim(detail.valueBefore) == '抗肿瘤治疗药') {
                    detail.valueBefore = '1';
                }else if ($.trim(detail.valueBefore) == '限制使用抗菌药') {
                    detail.valueBefore = '2';
                }else if ($.trim(detail.valueBefore) == '特殊使用抗菌药') {
                    detail.valueBefore = '3';
                }else if ($.trim(detail.valueBefore) == '含特殊药品复方制剂') {
                    detail.valueBefore = '4';
                }else if ($.trim(detail.valueBefore) == '冷藏冷冻药品') {
                    detail.valueBefore = '5';
                }else if ($.trim(detail.valueBefore) == '注射剂') {
                    detail.valueBefore = '6';
                }else if ($.trim(detail.valueBefore) == '处方药') {
                    detail.valueBefore = '7';
                }else if ($.trim(detail.valueBefore) == '甲类OTC') {
                    detail.valueBefore = '8';
                }else if ($.trim(detail.valueBefore) == '乙类OTC') {
                    detail.valueBefore = '9';
                }else if($.trim(detail.valueBefore) == 'β-内酰胺类注射剂') {
                    detail.valueBefore = '10';
                }else if($.trim(detail.valueBefore) == '抗生素类处方药') {
                    detail.valueBefore = '11';
                }

                if ($.trim(detail.valueAfter) == '抗肿瘤治疗药') {
                    detail.valueAfter = '1';
                }else if ($.trim(detail.valueAfter) == '限制使用抗菌药') {
                    detail.valueAfter = '2';
                }else if ($.trim(detail.valueAfter) == '特殊使用抗菌药') {
                    detail.valueAfter = '3';
                }else if ($.trim(detail.valueAfter) == '含特殊药品复方制剂') {
                    detail.valueAfter = '4';
                }else if ($.trim(detail.valueAfter) == '冷藏冷冻药品') {
                    detail.valueAfter = '5';
                }else if ($.trim(detail.valueAfter) == '注射剂') {
                    detail.valueAfter = '6';
                }else if ($.trim(detail.valueAfter) == '处方药') {
                    detail.valueAfter = '7';
                }else if ($.trim(detail.valueAfter) == '甲类OTC') {
                    detail.valueAfter = '8';
                }else if ($.trim(detail.valueAfter) == '乙类OTC') {
                    detail.valueAfter = '9';
                }else if($.trim(detail.valueAfter) == 'β-内酰胺类注射剂') {
                    detail.valueAfter = '10';
                }else if($.trim(detail.valueAfter) == '抗生素类处方药') {
                    detail.valueAfter = '11';
                }

            } else if (detail.columnValue == 'businessPattern') {
                beforeText = $("#businessPatternName").val();
                detail.beforeText = beforeText;
            } else if (detail.columnValue == 'organizationCodeNumber') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            } else if (detail.columnValue == 'invoiceYn') {
                if (detail.valueAfter == '0'){
                    beforeText = '是';
                } else {
                    beforeText = '否';
                }
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'responsiblePersons') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'salesman') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'salesmanTel') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'legalPerson') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'openingBank') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'bankAccount') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'bankAccountName') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'taxRegistryNumber') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'customerExtendItemVoList') {
                detail.afterText = dealCustomerExtendItemVoList(detail.valueAfter);
                detail.beforeText = dealCustomerExtendItemVoList(detail.valueBefore);
            }else if (detail.columnValue == 'businessLicenseNum') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'certificationOffice') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'businessLicenseAddress') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
                detail.columnName = '营业执照地址';
            }else if (detail.columnValue == 'recordDate') {
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if (detail.columnValue == 'threeInOne') {
                if (detail.valueBefore == '0'){
                    beforeText = '是';
                    detail.afterText = '否';
                } else {
                    beforeText = '否';
                    detail.afterText = '是';
                }
                detail.beforeText = beforeText;
            }else if(detail.columnValue == 'storageAddress'){
                detail.beforeText = detail.valueBefore;
                detail.afterText = detail.valueAfter;
            }else if(detail.columnValue == 'remark'){
                detail.beforeText = detail.valueBefore;
            }else if(detail.columnValue == 'validUntil'){
                detail.beforeText = detail.valueBefore;
            }else if(detail.columnValue == 'customerName'){
                detail.beforeText = detail.valueBefore;
            }else if(detail.columnValue == 'packagingRequirement'){
                if (detail.valueBefore == '1' && detail.valueAfter == '2') {
                    detail.beforeText = '纸箱';
                    detail.afterText = '周转箱';
                } else if (detail.valueBefore == '2' && detail.valueAfter == '1'){
                    detail.beforeText = '周转箱';
                    detail.afterText = '纸箱';
                } else if (detail.valueBefore == '1' && detail.valueAfter == '1'){
                    detail.beforeText = '纸箱';
                    detail.afterText = '纸箱';
                } else if (detail.valueBefore == '2' && detail.valueAfter == '2'){
                    detail.beforeText = '周转箱';
                    detail.afterText = '周转箱';
                }
            }else if(detail.columnValue == 'batchNumberRequirement'){
                if (detail.valueBefore == '1' && detail.valueAfter == '2') {
                    detail.beforeText = '单一批号';
                    detail.afterText = '无要求';
                } else if (detail.valueBefore == '2' && detail.valueAfter == '1'){
                    detail.beforeText = '无要求';
                    detail.afterText = '单一批号';
                } else if (detail.valueBefore == '1' && detail.valueAfter == '1'){
                    detail.beforeText = '单一批号';
                    detail.afterText = '单一批号';
                } else if (detail.valueBefore == '2' && detail.valueAfter == '2'){
                    detail.beforeText = '无要求';
                    detail.afterText = '无要求';
                }
            }else if(detail.columnValue == 'register'){
                customerChangeRecordDetailVoList.splice(i,1);
            }else if(detail.columnValue == 'specialBusinessScope'){
                var before = detail.valueBefore;
                beforeText = dealSpecialBusinessScope(before);
                detail.beforeText = beforeText.substring(0,beforeText.length-1);
                var afterText = dealSpecialBusinessScope(detail.valueAfter);
                detail.afterText = afterText.substring(0,afterText.length-1);
            }else if(detail.columnValue == 'invoiceType'){
                detail.columnName = '发票类型';
                if (detail.valueBefore == '1'){
                    beforeText = '电子普通发票';
                }else if(detail.valueBefore == '2'){
                    beforeText = '增值税专用发票';
                } else if(detail.valueBefore == '3'){
                    beforeText = '纸质普通发票';
                }else if(detail.valueBefore == '4'){
                    beforeText = '增值税电子专用发票';
                }
                detail.beforeText = beforeText;
                if (detail.valueAfter == '1'){
                    detail.afterText = '电子普通发票';
                } else if (detail.valueAfter == '2') {
                    detail.afterText = '增值税专用发票';
                } else if (detail.valueAfter == '3'){
                    detail.afterText = '纸质普通发票';
                }else if(detail.valueAfter == '4'){
                    detail.afterText = '增值税电子专用发票';
                }
            }else if (detail.columnValue == 'nature'){
                detail.columnName = '客户性质';
                if (detail.valueBefore == '0'){
                    beforeText = '';
                }else if(detail.valueBefore == '1'){
                    beforeText = '一般纳税人';
                } else if(detail.valueBefore == '2'){
                    beforeText = '小规模纳税人';
                } else if(detail.valueBefore == '3'){
                    beforeText = '其他';
                }
                detail.beforeText = beforeText;
                if (detail.valueAfter == '0'){
                    detail.afterText = '';
                } else if (detail.valueAfter == '1') {
                    detail.afterText = '一般纳税人';
                } else if (detail.valueAfter == '2'){
                    detail.afterText = '小规模纳税人';
                } else if (detail.valueAfter == '3'){
                    detail.afterText = '其他';
                }
            }else if (detail.columnValue == 'disabledYn') {
                if (detail.valueBefore == '0'){
                    beforeText = '否';
                }else {
                    beforeText = '是';
                }
                detail.beforeText = beforeText;
                if (detail.valueAfter == '0') {
                    detail.afterText = '否';
                } else {
                    detail.afterText = '是';
                }
            }else if (detail.columnValue == 'num') {
                beforeText = $("input[name=num]").val();
                detail.beforeText = beforeText;
                detail.columnName = '档案编号';
            }else if (detail.columnValue == 'remarkss') {
                beforeText = $("input[name=remarkss]").val();
                detail.beforeText = beforeText;
                detail.columnName = '质管备注';
            }else if(detail.columnValue == 'bdName'){
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }else if(detail.columnValue == 'bdTel'){
                beforeText = detail.valueBefore;
                detail.beforeText = beforeText;
            }
        }
    }
    customerChangeApprovalRecordInfoVo.id = $("input[id='taskId']").val();
    customerChangeApprovalRecordInfoVo.processId = $("input[id='processId']").val();
    customerChangeApprovalRecordInfoVo.edit = $("input[id='edit']").val();
    customerChangeApprovalRecordInfoVo.customerChangeRecordDetailVoList = customerChangeRecordDetailVoList;
    if(changeApplyTemp['customerEnclosureVoList'] && changeApplyTemp['customerEnclosureVoList']['valueAfter'] ){ // && JSON.parse(changeApplyTemp['customerEnclosureVoList']['valueAfter']).length > 0
        customerChangeApprovalRecordInfoVo.customerEnclosureVoList = JSON.parse(changeApplyTemp.customerEnclosureVoList.valueAfter)
    }else{ // 非独立经营证明 没有修改的时候从页面取值
        let fileIcons = $('.fileIcon_p');let _arr = [];
        if(fileIcons.length > 0){
            $(fileIcons).each(function (index,item) {
                let _obj = {};
                _obj.enclosureName = '非独立经营证明'+(index+1)
                _obj.url = $(item).attr('data-imgurl');
                _arr.push(_obj)
            })
            customerChangeApprovalRecordInfoVo.customerEnclosureVoList = _arr;
        }else {
            customerChangeApprovalRecordInfoVo.customerEnclosureVoList = [];
        }

    }
    var da = JSON.stringify(customerChangeApprovalRecordInfoVo);
    return customerChangeApprovalRecordInfoVo;
}
//处理特殊属性
function dealSpecialBusinessScope(a){
    var befores;
    if (a.indexOf(",") != -1){
        befores = a.split(",");
    } else {
        befores = new Array();
        befores[0] = a;
    }
    var beforeText = '';
    for (var j=0; j<befores.length; j++){
        if (befores[j] == '1') {
            beforeText += '一般纳税人资格证明,';
        } else if (befores[j] == '2') {
            beforeText += '可经营抗生素,';
        } else if (befores[j] == '3') {
            beforeText += '可经营注射剂,';
        } else if (befores[j] == '4') {
            beforeText += '可经营终止妊娠,';
        } else if (befores[j] == '5') {
            beforeText += '可经营特殊管理药品,';
        } else if (befores[j] == '6') {
            beforeText += '可经营含兴奋剂,';
        } else if (befores[j] == '7') {
            beforeText += '可经营抗肿瘤治疗药商品,';
        } else if (befores[j] == '8') {
            beforeText += '麻醉药品,';
        } else if (befores[j] == '9') {
            beforeText += '精神药品,';
        } else if (befores[j] == '10') {
            beforeText += '医疗用毒性药品,';
        } else if (befores[j] == '12') {
            beforeText += '无特殊属性,';
        }
    }
    return beforeText;
}
function dealCustomerExtendItemVoList(a){
    var afterText = '';
    var text = '';
    if ($.isEmptyObject(a)){
        return "";
    }
    if (typeof a == 'string' ){
        a = JSON.parse(a);
    }
    if (a.length > 0){
        for (var i = 0; i<a.length; i++){
            if (a[i].parentCode == '1001'){
                text += '财务合账,';
            }else if (a[i].parentCode == '1002') {
                text += '账期,';
            }
            if (a[i].code && a[i].code == '1002001'){
                afterText += '结算周期'+a[i].value + '天,';
            } else if (a[i].code && a[i].code == '1002002') {
                afterText += '结算日'+a[i].value + '日,';
            } else if (a[i].code && a[i].code == '1002003') {
                afterText += '支付日'+a[i].value + '日,';
            }
        }
    }
    afterText = text + afterText;
    afterText = afterText.substring(0, afterText.length-1);
    return afterText;
}
//经营方式
$("#businessPatternName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:3},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    onSelect: function (result) {
        $('#businessPattern').val(result.data);
    },
    onNoneSelect: function (params, suggestions) {
        $('#businessPattern').val("");
        $('#businessPatternName').val("");
    }
});
//经营类别
$("#businessCategoryName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:1},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    onSelect: function (result) {
        $('#businessCategory').val(result.data);
    },
    onNoneSelect: function (params, suggestions) {
        $('#businessCategory').val("");
        $('#businessCategoryName').val("");
    }
});
//客户类别
$("#customerTypeName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:2},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    transformResult: function (response) {
        response.result = response.result.filter(item => item.clientName != '基层医疗机构')
        return response
    },
    onSelect: function (result) {
        $('#customerType').val(result.data);
    },
    onNoneSelect: function (params, suggestions) {
        $('#customerType').val("");
        $('#customerTypeName').val("");
    }
});
function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }
//结算方式
function balanceTypeCheck(){
    $("input[name='parentCode']:not(:checked)").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').hide();
        $(this).parent().parent().parent().children().not('.checkbox').find('input[name="value"]').val('');
    });
    $("input[name='parentCode']:checked").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').show();
    });
}
/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj, tableId) {
    var parentId=$(obj).parents("tr").attr("id");
    //var data=$(tableId).getRowData(parentId);
    /**
     * RM 2018-09-29
     * 之前取data 值的写法，会把修改前的table 的数据拿到，然后在弹窗里查看图片的时候 数量显示的是修改后的值， 但是查看图片只能看到修改前的图片
     */
    var data=$(obj).parents("table").getRowData(parentId);
    if(data.customerEnclosureVoList)
    {
        if(typeof data.customerEnclosureVoList != 'string'){
            data.enclosureList=JSON.stringify(data.enclosureList);
        }
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            list:JSON.parse(data.customerEnclosureVoList)
        })
    }
}
// 联系人电话校验
function checkSalesmanTel(telList, auditStatus) {
    let finalTelAry = telList.map(item => item.tels)
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '/proxy-customer/customer/customerBaseAppl/validateUserMobile?mobiles=' + finalTelAry.toString(),
            type: 'get',
            success: function (res) {
                if (res.code == 0) {
                    // 对返回数据操作判断是否有不满足数据，如果有 ，返回false
                    let list = res.result;
                    let checkres = list.filter(item => item.flag)
                    if (auditStatus==2 || auditStatus == 9) {
                        // 大客户的 不用判断
                        if(($('[name=customerCode]').val() || $('#customerCode').val()) && $('#bigcusType').val() == '1'){
                            reject()
                        }else{
                            if(checkres.length == 0) {
                                reject()
                            }else{
                                //  筛出 不允许的手机号
                                let tels  =  checkres.map(item => item.mobile);
                                let tag = ''
                                for (let i = 0; i< tels.length; i++){
                                    for (let j = 0; j<telList.length; j++){
                                        if (tels[i] == telList[j]['tels']) {
                                            tag =  telList[j]['tag'];
                                            break;
                                        }
                                    }
                                }
                                resolve(tag);
                            }
                        }
                    }else {
                        reject()
                    }
                }else{
                    if ($('#bigcusType').val() == '1'){
                        reject()
                    } else{
                        if (res.result){
                            resolve(res.msg)
                        } else{
                            reject()
                        }
                    }
                }
            }
        })
    })
}
//新增的ajax
function insert(infoVo){
    var auditStatus = infoVo.customerChangeApprovalRecordVo.auditStatus;
    // 莫名其妙代码
    // utils.dialog({content: successMsg, timeout: 2000}).showModal();

    var successMsg = "保存成功！";
    // 草稿
    if(auditStatus==2){
        successMsg = "提交审核成功";
    }
    let salesmanTelList = []; // 所有联系人的电话
    var _tableBoole;
    var _tableCardBoole;
    _tableBoole= $("#table2").XGrid('getRowData');
    _tableCardBoole= $("#certTable").XGrid('getRowData');
    _tableBoole=_tableBoole.length<=0;
    _tableCardBoole=_tableCardBoole.length<=0;
    infoVo.baseDelegationFileEmpty=_tableBoole;
    infoVo.baseBeProxyFileEmpty=_tableCardBoole;
    if (window.changeApply && changeApply['salesmanTel'] && changeApply['salesmanTel']['afterText']){
        salesmanTelList.push({'tag': 'salesmanTel', 'tels':changeApply['salesmanTel']['afterText']})
    } else{
        salesmanTelList.push({'tag': 'salesmanTel', 'tels':$('[name=salesmanTel]').val()})
    }
    // 客户委托书
    let allTable2Tel = [];
    if (window.changeApplyList && changeApplyList['customerDelegationFileVoList'] && changeApplyList['customerDelegationFileVoList']['valueAfter']) {
        let  ary = changeApplyList['customerDelegationFileVoList']['valueAfter'];
        allTable2Tel = ary.map(item => {
            return {'tag': 'table', 'tels':item.delegationTel}
        });
    }else{
        let allTable2Data  = $('#table2').XGrid('getRowData');
        allTable2Tel = allTable2Data.map(item => {
            return {'tag': 'table', 'tels':item.delegationTel}
        });
    }
    // salesmanTelList = salesmanTelList.concat(allTable2Tel);
    // //
    // checkSalesmanTel(salesmanTelList,auditStatus).then((msg) => {
    //     if (msg){
    //         parent.hideLoading();
    //         if(msg == 'table'){
    //             disable();
    //             utils.dialog({content: '客户委托书中的电话不允许录入我司员工手机号', quickClose: true, timeout: 2000}).showModal();
    //             return false;
    //         }else{
    //             disable();
    //             utils.dialog({content: msg, quickClose: true, timeout: 2000}).showModal();
    //             return false;
    //         }
    //     }
    // }).catch( () => {
        console.log('电话验证通过')
        parent.showLoading({hideTime: 15000});
        $.ajax({
            url:'/proxy-customer/customer/change/approval/baseChangeSave',
            data:JSON.stringify(infoVo),
            type:"post",
            dataType:'json',
            contentType: "application/json",

            success:function(data){
                var status = data.result.status;
                var taskStatus = data.result.taskStatus;
                if(status!=0){
                    if(typeof(taskStatus) != "undefined"){
                        if(taskStatus==true){
                            utils.dialog({content: successMsg, timeout: 2000}).showModal();
                            setTimeout(function(){
                                utils.closeTab();
                            },2000)
                        }else{// 工作流失败
                            utils.dialog({
                                title: "提示",
                                content: data.result.taskMsg + "，已暂时保存为草稿",
                                width: 300,
                                height: 30,
                                okValue: '确定',
                                ok: function () {
                                    $('#table').XGrid('setGridParam', {
                                        postData: {
                                            "orgCode": $("#orgCode").val(),
                                            "applicationNumber":$("#applicationNumber").val(),
                                            "customerCode":$("#customerCode").val(),
                                            "auditStatus":$("#auditStatus").val()
                                        },page:1
                                    }).trigger('reloadGrid');

                                    utils.closeTab();
                                }
                            }).showModal();
                            /*utils.dialog({content: data.result.taskMsg+"，保存为草稿", timeout: 2000}).showModal();*/
                        }
                    }else{
                        utils.dialog({content: '保存成功', timeout: 2000}).showModal();
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)
                    }
                }else {// 保存失败
                    utils.dialog({content: data.result.msg, timeout: 2000}).showModal();
                }
            },
            error:function(){
                utils.dialog({content: '保存失败', timeout: 2000}).showModal();
            },
            complete:function(){
                parent.hideLoading();
            }
        });
    // })

}
// 更新的ajax
function update(infoVo,dateFrom){
    var auditStatus = infoVo.customerChangeApprovalRecordVo.auditStatus;
    var successMsg = "保存成功！";
    var _tableBoole;
    var _tableCardBoole;
    _tableBoole= $("#table2").XGrid('getRowData');
    _tableCardBoole= $("#certTable").XGrid('getRowData');
    _tableBoole=_tableBoole.length<=0;
    _tableCardBoole=_tableCardBoole.length<=0;
    infoVo.baseDelegationFileEmpty=_tableBoole;
    infoVo.baseBeProxyFileEmpty=_tableCardBoole;
    // 草稿
    if(auditStatus!=1&&auditStatus!=4){
        successMsg = "提交审核成功";
    }
    let salesmanTelList = []; // 所有联系人的电话
    if (window.changeApply && changeApply['salesmanTel'] && changeApply['salesmanTel']['afterText']){
        salesmanTelList.push({'tag': 'salesmanTel', 'tels':changeApply['salesmanTel']['afterText']})
    } else{
        salesmanTelList.push({'tag': 'salesmanTel', 'tels':$('[name=salesmanTel]').val()})
    }
    // 客户委托书
    let allTable2Tel = [];
    if (window.changeApplyList && changeApplyList['customerDelegationFileVoList'] && changeApplyList['customerDelegationFileVoList']['valueAfter']) {
        let  ary = changeApplyList['customerDelegationFileVoList']['valueAfter'];
        allTable2Tel = ary.map(item => {
            return {'tag': 'table', 'tels':item.delegationTel}
        });
    }else{
        let allTable2Data  = $('#table2').XGrid('getRowData');
        allTable2Tel = allTable2Data.map(item => {
            return {'tag': 'table', 'tels':item.delegationTel}
        });
    }

    // salesmanTelList = salesmanTelList.concat(allTable2Tel);
    // //
    // checkSalesmanTel(salesmanTelList,auditStatus).then((msg) => {
    //     if (msg){
    //         parent.hideLoading();
    //         if (msg == 'salesmanTel') {
    //             disable();
    //             utils.dialog({content: '联系人手机号不允许录入我司员工手机号', quickClose: true, timeout: 2000}).showModal();
    //             return false;
    //         }else if(msg == 'table'){
    //             disable();
    //             utils.dialog({content: '客户委托书中的电话不允许录入我司员工手机号', quickClose: true, timeout: 2000}).showModal();
    //             return false;
    //         }else{
    //             disable();
    //             utils.dialog({content: msg, quickClose: true, timeout: 2000}).showModal();
    //             return false;
    //         }
    //     }
    // }).catch( () => {
         var  _url="";
        if(dateFrom=="Audit"){
            _url="/proxy-customer/customer/change/approval/passBaseChangeApproval";
        }else{
            _url="/proxy-customer/customer/change/approval/updateBaseChangeApproval";
        }
        parent.showLoading({hideTime: 15000});
        $.ajax({
            url:_url,
            data:JSON.stringify(infoVo),
            type:"post",
            dataType:'json',
            contentType: "application/json",

            success:function(data){
                var status = data.result.status;
                var taskStatus = data.result.taskStatus;
                if(status!=0){
                    if(typeof(taskStatus) != "undefined"){
                        if(taskStatus==true){
                            utils.dialog({content: successMsg,timeout: 2000}).showModal();
                            /*if(dateFrom=='againAssert'){
                                setTimeout(function(){
                                    var  mid = parent.$('#nav-tab li.active a').attr('href').replace('#', '');
                                    parent.$('#mainFrameTabs').bTabsClose(mid);
                                },2000)
                            }else{*/
                            setTimeout(function(){
                                utils.closeTab();
                            },2000)

                        }else{// 任务流失败
                            utils.dialog({
                                title: "提示",
                                content: data.result.taskMsg + "，已暂时保存为草稿",
                                width: 300,
                                height: 30,
                                okValue: '确定',
                                ok: function () {
                                    utils.closeTab();
                                }
                            }).showModal();
                        }
                    }else{
                        utils.dialog({content: '保存成功', timeout: 2000}).showModal();
                        /*if(dateFrom=='againAssert'){
                            setTimeout(function(){
                                var  mid = parent.$('#nav-tab li.active a').attr('href').replace('#', '');
                                parent.$('#mainFrameTabs').bTabsClose(mid);
                            },2000)
                        }else{*/
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)

                    }
                }else{// 保存失败
                    utils.dialog({content: data.result.msg, timeout: 2000}).showModal();
                }
            },
            error:function(){
                utils.dialog({content: '保存失败', timeout: 2000}).showModal();
            },
            complete:function(){
                parent.hideLoading();
            }
        });
    // })

}
// 通过ajax
function passAjax(status){
    var _tableBoole;
    var _tableCardBoole;
    _tableBoole= $("#table2").XGrid('getRowData');
    _tableCardBoole= $("#certTable").XGrid('getRowData');
    _tableBoole=_tableBoole.length<=0;
    _tableCardBoole=_tableCardBoole.length<=0;
    var processVO = {
        customerProcessVO: {
            "auditOpinion": $("#auditOpinion").val(),
            "taskId": taskId,
            "id": approvalId,
        },
        customerChangeApprovalRecordVo:{
            source:$("#shippingSource").val()
        },
        baseDelegationFileEmpty:_tableBoole,
        baseBeProxyFileEmpty:_tableCardBoole
    };
    if(pageType==8||pageType==30){
        // 再次提交审核弹窗
        dialogAgain("审核通过","是否确认提交审核",status)
        return false;
    }
    parent.showLoading({hideTime: 15000});
    $.ajax({
        type:"post",
        url: "/proxy-customer/customer/change/approval/passBaseChangeApproval",
        async : false,
        data:JSON.stringify(processVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(taskStatus==true){
                utils.dialog({content: '恭喜审核通过！', timeout: 2000}).showModal();
                setTimeout(function(){
                    utils.closeTab();
                },2000)
            }else {// 任务流失败
                utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '保存失败', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 不通过ajax
function noPassAjax(){
    if(!$("#auditOpinion").val()){
        parent.hideLoading();
        utils.dialog({content: '审批意见不能为空！', timeout: 2000}).showModal();
        return false;
    }
    var processVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":taskId,
        "id":approvalId,
        "auditStatus":returnWorkflowData($("#auditStatus").val(),false),
        "source":$("#shippingSource").val()
    };
    parent.showLoading({hideTime: 15000});
    $.ajax({
        type:"post",
        url: "/proxy-customer/customer/change/approval/noPassBaseChangeApproval",
        async : false,
        data:JSON.stringify(processVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(taskStatus==true){
                utils.dialog({content: '驳回成功！', timeout: 2000}).showModal();
                setTimeout(function(){
                    utils.closeTab();
                },2000)
            }else {// 任务流失败
                utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '驳回失败！', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
// 撤销
function withdraw(){
    var processVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":taskId,
        "id":approvalId
    };
    parent.showLoading({hideTime: 15000});
    $.ajax({
        type:"post",
        url: "/proxy-customer/customer/change/approval/withdrawBaseChangeApproval",
        async : false,
        data:JSON.stringify(processVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(status!=0){
                if(typeof(taskStatus) != "undefined"){
                    if(taskStatus==true){
                        utils.dialog({content: '撤销成功！', timeout: 2000}).showModal();
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)
                    }else{// 任务流启动失败
                        utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
                    }

                }
            }else {// 保存失败
                utils.dialog({content: data.result.msg, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '撤销失败！', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
//关闭按钮
$("#closePage").on("click", function () {
    var d =  dialog({
        title: "提示",
        content: "是否保存草稿？",
        width:300,
        height:30,
        okValue: '保存草稿',
        ok: function () {
            $("#saveRowData").click();
            d.close().remove();
            return false;
        },
        button:[
            {
                value:'关闭',
                callback:function(){
                    utils.closeTab();
                }
            }
        ]
    }).showModal();
});
/**
 * dialog_nature_checked()
 * 1）客户首营页面增加字段：【客户性质】：下拉选择，字典值为：“一般纳税人/小规模纳税人/其他”共三项。
 * 由质管审核时填写，默认值为空，控制必填。
 *（2）控制逻辑修改：当【客户性质】=“一般纳税人”、“小规模纳税人”
 * 并且 【是否三证合一】=“否”时，控制【税务登记证号】必填。提交时提示并禁止提交。
 * 不满足这一条件时，不控制必填。
 */
function dialog_nature_checked() {
    $('[name=taxRegistryNumber]').prev('div').html('<i class="text-require">*  </i>税务登记账号')
    utils.dialog({
        title: '提示',
        content: '当【客户性质】=“一般纳税人”、“小规模纳税人”，并且【是否三证合一】=“否”时，税务登记证号必填.',
        okValue: '确定',
        ok: function () {}
    }).showModal();
}
//保存草稿
$("#saveRowData").on("click",function(){
    if(utils.allSavaBtnsHidden()){
        return false;
    }
    let modify_nature = window.changeApply && window.changeApply['nature'], // 客户性质
        modify_threeInOne = window.changeApply && window.changeApply['threeInOne'], // 三证合一
        modify_taxRegistryNumber = window.changeApply && window.changeApply['taxRegistryNumber']; // 税务登记号

    let modify_natureSubVal = (window.changeApply && window.changeApply['nature'])? window.changeApply['nature']['valueAfter']:$('[name=nature]').val();
    if(!modify_natureSubVal){
        utils.dialog({
            title: '提示',
            content: '客户性质为必填项.',
            okValue: '确定',
            ok: function () {}
        }).showModal();
        return false;
    }

    if(modify_nature){ // 客户性质 有修改
        if(modify_threeInOne){ // 三证合一有修改
            if(window.changeApply['nature']['valueAfter'] != '3' && window.changeApply['threeInOne']['valueAfter'] == 0){
                if(modify_taxRegistryNumber){ // 税务登记号 有修改
                    if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                        dialog_nature_checked();
                        return false;
                    }else{
                        $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                    }
                }else{
                    if($('[name=taxRegistryNumber]').val() == ''){
                        dialog_nature_checked();
                        return false;
                    }else{
                        $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                    }
                }
            }
        }else{ // 三证合一没有修改
            if($('[name=threeInOne]:checked').val() == 0){// 没修改时三证合一为否的时候
                if(window.changeApply['nature'] && window.changeApply['nature']['valueAfter'] != '3'){//客户性质值改为一般纳税人或小规模纳税人
                    if(modify_taxRegistryNumber){ // 税务登记号 有修改
                        if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                            dialog_nature_checked();
                            return false;
                        }else{
                            $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                        }
                    }else{
                        if($('[name=taxRegistryNumber]').val() == ''){
                            dialog_nature_checked();
                            return false;
                        }else{
                            $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                        }
                    }
                }
            }
        }
    }else{ // 客户性质  没修改
        if($('#customerNature').val() != 3){
            if(modify_threeInOne){ // 三证合一有修改
                if(window.changeApply['threeInOne']['valueAfter'] == 0){
                    if(modify_taxRegistryNumber){ // 税务登记号 有修改
                        // 客户性质当前值不为3的时候应该 税务登记号必须有值
                        if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){
                            if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }else{
                        if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){
                            if($('[name=taxRegistryNumber]').val() == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }
                }
            }else{ // 三证合一没有修改
                if($('[name=threeInOne]:checked').val() == 0){
                    if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){ // 客户性质没改，三证合一没改，获取当前页面值然后判断税务登记是否必填
                        if(modify_taxRegistryNumber){ // 税务登记号 有修改
                            if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }else{
                            if($('[name=taxRegistryNumber]').val() == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }
                }
            }
        }
    }
    console.log('客户性质验证通过')
    // return false;
    able();
    parent.showLoading({hideTime: 60000});
    if(!$("#baseId").val()){
        disable();
        utils.dialog({content: '请选择要修改的客户，才能继续保存', quickClose: true, timeout: 2000}).showModal();
        $('#customerName').removeAttr('readonly').removeAttr('disabled');
        parent.hideLoading();
    }else{
        // 状态 审核中
        var infoVo = pushData();
        infoVo.customerChangeApprovalRecordVo.auditStatus = 1;
        infoVo.customerChangeApprovalRecordVo.baseId =$("#baseId").val(); //基础属性id
        if(pageType==0){
            // 草稿 新增
            insert(infoVo);
        }else if(pageType==1){
            // 草稿再次编辑
            infoVo.customerChangeApprovalRecordVo.id =$("#approvalId").val();
            update(infoVo,"saveRowData");
        }
        disable();
    }
});
//客户资料变更--当客户性质为“一般纳税人\小规模纳税人”，三证合一为否，税务登记证为空时，点击提交，应做拦截并做提示
function fun_checkNature() {

}
//提交审核
$("#submitAssert").on("click",function(){
    // 校验客户名称与公司名称是否一致
    if(!validateCustomerName()) {
        utils.dialog({
            title: '提示',
            content: '客户名称与发票信息中的公司名称不一致，确认要提交吗？',
            okValue: '确定',
            ok: function () {
                submitAction();
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    }else {
        submitAction();
    }
});
function submitAction(){
    if(utils.allSavaBtnsHidden()){
        return false;
    }
    let modify_nature = window.changeApply && window.changeApply['nature'], // 客户性质
        modify_threeInOne = window.changeApply && window.changeApply['threeInOne'], // 三证合一
        modify_taxRegistryNumber = window.changeApply && window.changeApply['taxRegistryNumber']; // 税务登记号
    let modify_natureSubVal = (window.changeApply && window.changeApply['nature'])? window.changeApply['nature']['valueAfter']:$('[name=nature]').val();
    if(!modify_natureSubVal){
        utils.dialog({
            title: '提示',
            content: '客户性质为必填项.',
            okValue: '确定',
            ok: function () {}
        }).showModal();
        return false;
    }
    if(modify_nature){ // 客户性质 有修改
        if(modify_threeInOne){ // 三证合一有修改
            if(window.changeApply['nature']['valueAfter'] != '3' && window.changeApply['threeInOne']['valueAfter'] == 0){
                if(modify_taxRegistryNumber){ // 税务登记号 有修改
                    if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                        dialog_nature_checked();
                        return false;
                    }else{
                        $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                    }
                }else{
                    if($('[name=taxRegistryNumber]').val() == ''){
                        dialog_nature_checked();
                        return false;
                    }else{
                        $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                    }
                }
            }
        }else{ // 三证合一没有修改
            if($('[name=threeInOne]:checked').val() == 0){// 没修改时三证合一为否的时候
                if(window.changeApply['nature'] && window.changeApply['nature']['valueAfter'] != '3'){//客户性质值改为一般纳税人或小规模纳税人
                    if(modify_taxRegistryNumber){ // 税务登记号 有修改
                        if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                            dialog_nature_checked();
                            return false;
                        }else{
                            $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                        }
                    }else{
                        if($('[name=taxRegistryNumber]').val() == ''){
                            dialog_nature_checked();
                            return false;
                        }else{
                            $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                        }
                    }
                }
            }
        }
    }else{ // 客户性质  没修改
        if($('#customerNature').val() != 3){
            if(modify_threeInOne){ // 三证合一有修改
                if(window.changeApply['threeInOne']['valueAfter'] == 0){
                    if(modify_taxRegistryNumber){ // 税务登记号 有修改
                        // 客户性质当前值不为3的时候应该 税务登记号必须有值
                        if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){
                            if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }else{
                        if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){
                            if($('[name=taxRegistryNumber]').val() == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }
                }
            }else{ // 三证合一没有修改
                if($('[name=threeInOne]:checked').val() == 0){
                    if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){ // 客户性质没改，三证合一没改，获取当前页面值然后判断税务登记是否必填
                        if(modify_taxRegistryNumber){ // 税务登记号 有修改
                            if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }else{
                            if($('[name=taxRegistryNumber]').val() == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }
                }
            }
        }
    }
    console.log('客户性质验证通过')
    // return false;
    able();
    parent.showLoading({hideTime: 60000});
    if(!$("#baseId").val()){
        disable();
        utils.dialog({content: '请选择要修改的客户，才能继续保存', quickClose: true, timeout: 2000}).showModal();
        $('#customerName').removeAttr('readonly').removeAttr('disabled');
        parent.hideLoading();
    }else{
        // 状态 审核中
        var infoVo = pushData();
        if(typeof infoVo == 'boolean'){
            return false
        }else{
            if(infoVo.customerChangeRecordDetailVoList.length<1){
                utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
                parent.hideLoading();
                disable();
                return false;
            };
            // if(checkValEmpty()){
            //     parent.hideLoading();
            //     utils.dialog({
            //         title: '提示',
            //         content: '当是否独立核算的提交值为否时，纳税人识别号或者开户银行或者银行账号,或者营业执照地址或者电话不能为空。',
            //         okValue: '确定',
            //         ok: function () {}
            //     }).showModal();
            //     disable();
            //     return false;
            // }
            // if(anotherCheckValEmpty()){
            //     parent.hideLoading();
            //     utils.dialog({
            //         title: '提示',
            //         content: '当是否独立核算的提交值为是，发票类型的值为增值税专用发票时，纳税人识别号或者开户银行或者银行账号不能为空。',
            //         okValue: '确定',
            //         ok: function () {}
            //     }).showModal();
            //     disable();
            //     return false;
            // }
            // 提交审核时当 是否独立核算为否时，判断非独立经营证明中是否有数据
            // 资料变更时，判断 是否独立核算 是否有修改，有修改的话修改后的值为是的时候不做检验
            let isIndependentSubVal = (window.changeApply && window.changeApply['isIndependent'])?window.changeApply['isIndependent']['valueAfter']:$('[name=isIndependent]:checked').val();
            if(isIndependentSubVal == '1'){ // 是否独立核算 选中值为是时。判断上下文对应的值是否相等
                let returnBool = checkChangeSyncDataSame();
                if(!returnBool){
                    parent.hideLoading();
                    disable();
                    return false;
                };
            }else if(isIndependentSubVal == '0' && window.changeApply['isIndependent']){
                //if($('[name=isIndependent]:checked').val() == '0' || (window.changeApply && window.changeApply['isIndependent'] && window.changeApply['isIndependent']['valueAfter'] == '0')){
                //if($('.fileIcon_p').length < 1){
                if((window.changeApply && !window.changeApply['customerEnclosureVoList'] ) || (window.changeApply['customerEnclosureVoList'] && JSON.parse(window.changeApply['customerEnclosureVoList']['valueAfter']).length < 1)){
                    parent.hideLoading();
                    utils.dialog({
                        title: '提示',
                        content: '非独立经营证明附件未作修改，不允许审核通过！',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                    disable();
                    return false;
                }
                if(!window.changeApply['billingCompanyName']){ //  && $('#billInfo_form [name=isIndependent]:checked').val() == '0'
                    parent.hideLoading();
                    utils.dialog({
                        title: '提示',
                        content: '是否独立核算修改为否的时候，公司名称必须做修改.',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                    disable();
                    return false;
                }
            }

            //
            if(validBillInfoForm_fun()){
                parent.hideLoading();
                disable();
                return false;
            };

            infoVo.customerChangeApprovalRecordVo.auditStatus = 2;
            infoVo.customerChangeApprovalRecordVo.baseId =$("#baseId").val(); //基础属性id
            if(pageType==0) {
                // 草稿 新增
                insert(infoVo);
            }else if(pageType==1){
                // 草稿再次编辑
                infoVo.customerChangeApprovalRecordVo.id =$("#approvalId").val();
                update(infoVo,"submitAssert");
            }
            disable();
        }
    }
}
// 审核意见
function dialogData(title,status){
    $('#auditOpinion').val('');
    utils.dialog({
        title:title,
        content: $('#container'),
        okValue: '确定',
        ok: function () {
            parent.showLoading({hideTime: 60000});
            if(status=="pass"){
                passAjax("Audit");

            }else if(status=="noPass" ){
                noPassAjax();
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}
// 驳回编辑
function dialogAgain(title,con,status){
    parent.showLoading({hideTime: 60000});
    // 状态 审核中
    var infoVo = pushData();
    if(typeof infoVo == 'boolean'){
        return false
    }else{
        if(infoVo.customerChangeRecordDetailVoList.length<1){
            utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
            disable();
            return false;
        }
        if(status != 'withdraw'){ // 撤销审核的时候不校验
            // 是否独立核算 选中值为是时。判断上下文对应的值是否相等
            let isIndependentVal = (window.changeApply && window.changeApply['isIndependent'])? window.changeApply['isIndependent']['valueAfter']:$('#billInfo_form [name=isIndependent]:checked').val();
            if(isIndependentVal == '1'){ // 是否独立核算 选中值为是时。判断上下文对应的值是否相等
                let returnBool = checkChangeSyncDataSame();
                if(!returnBool){
                    parent.hideLoading();
                    disable();
                    return false;
                };
            }
            if(isIndependentVal == '0'  && window.changeApply['isIndependent']){ // 是否独立核算 提交值为否时，判断必填值
                if(!infoVo.customerEnclosureVoList || (infoVo.customerEnclosureVoList && infoVo.customerEnclosureVoList.length == 0 && status != 'withdraw')){ // 再次审核的时候
                    parent.hideLoading();
                    utils.dialog({
                        title: '提示',
                        content: '非独立经营证明附件未上传，不允许审核通过！',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                    disable();
                    return false;
                }

                // 当是否独立 核算修改为否的时候，公司名称必须做修改。同步的改变后面三个的值
                if(!window.changeApply['billingCompanyName']){ // 如果 公司名称没有做修改
                    parent.hideLoading();
                    utils.dialog({
                        title: '提示',
                        content: '是否独立核算修改为否的时候，公司名称必须做修改.',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                    disable();
                    return false;
                }
            }
            if(validBillInfoForm_fun()){
                parent.hideLoading();
                disable();
                return false;
            };
        }

        parent.hideLoading();
        utils.dialog({
            title:title,
            content: con?con:$('#alertAgain'),
            width:300,
            height:30,
            okValue: '确定',
            ok: function () {
                if(status=="pass"||status=="Audit"){
                    able();
                    var _tableBoole;
                    var _tableCardBoole;
                    _tableBoole= $("#table2").XGrid('getRowData');
                    _tableCardBoole= $("#certTable").XGrid('getRowData');
                    _tableBoole=_tableBoole.length<=0;
                    _tableCardBoole=_tableCardBoole.length<=0;
                    infoVo.baseDelegationFileEmpty=_tableBoole;
                    infoVo.baseBeProxyFileEmpty=_tableCardBoole;
                    infoVo.customerChangeApprovalRecordVo.auditStatus = returnWorkflowData(infoVo.customerChangeApprovalRecordVo.auditStatus ,true);
                    infoVo.customerChangeApprovalRecordVo.id =$("#approvalId").val();
                    infoVo.customerChangeApprovalRecordVo.baseId =$("#baseId").val(); //基础属性id
                    infoVo.customerChangeApprovalRecordVo.source=$("#shippingSource").val();
                    infoVo.customerChangeApprovalRecordVo.paperRecyclStatus=$("#paperRecyclStatus").val();
                    var processVo = {
                        "auditOpinion":$("#auditOpinion").val(),
                        "taskId":taskId,
                        "id":approvalId
                    };
                    infoVo.customerProcessVO = processVo;
                    update(infoVo,status);
                }else if(status=="withdraw"){
                    withdraw();
                }
                disable();
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    }
}
//通过
$("#pass").on("click",function(){
    // 校验客户名称与公司名称是否一致
    if(!validateCustomerName()) {
        utils.dialog({
            title: '提示',
            content: '客户名称与发票信息中的公司名称不一致，确认要审核通过吗？',
            okValue: '确定',
            ok: function () {
                auditPassAction();
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    }else {
        auditPassAction();
    }
});
function auditPassAction(){
    var title = "审核通过";
    var status = "pass";
    $('#container').find('.text-require').css('display','none')
    $("#container textarea").removeAttr("disabled");
    // 审批意见弹窗
    dialogData(title,status);
}
//不通过
$("#noPass").on("click",function(){
    var title = "审核不通过";
    var status = "noPass";
    $('#container').find('.text-require').css('display','inline-block')
    $("#container textarea").removeAttr("disabled");
    // 审批意见弹窗
    dialogData(title,status);
});
//再次提交
$("#againAssert").on("click",function() {
    // 校验客户名称与公司名称是否一致
    if(!validateCustomerName()) {
        utils.dialog({
            title: '提示',
            content: '客户名称与发票信息中的公司名称不一致，确认要审核通过吗？',
            okValue: '确定',
            ok: function () {
                againAssertAction();
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    }else {
        againAssertAction();
    }
});
function againAssertAction(){
    if(utils.allSavaBtnsHidden()){
        return false;
    }
    var title = "再次提交审核";
    var contant = "确定重新提交申请？";
    var status = "pass";
    let modify_nature = window.changeApply && window.changeApply['nature'], // 客户性质
        modify_threeInOne = window.changeApply && window.changeApply['threeInOne'], // 三证合一
        modify_taxRegistryNumber = window.changeApply && window.changeApply['taxRegistryNumber']; // 税务登记号

    let modify_natureSubVal = (window.changeApply && window.changeApply['nature'])? window.changeApply['nature']['valueAfter']:$('[name=nature]').val();
    if(!modify_natureSubVal){
        utils.dialog({
            title: '提示',
            content: '客户性质为必填项.',
            okValue: '确定',
            ok: function () {}
        }).showModal();
        return false;
    }

    if(modify_nature){ // 客户性质 有修改
        if(modify_threeInOne){ // 三证合一有修改
            if(window.changeApply['nature']['valueAfter'] != '3' && window.changeApply['threeInOne']['valueAfter'] == 0){ // 客户性质值改为一般纳税人或小规模纳税人，三证合一为否的时候
                if(modify_taxRegistryNumber){ // 税务登记号 有修改
                    if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                        dialog_nature_checked();
                        return false;
                    }else{
                        $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                    }
                }else{
                    if($('[name=taxRegistryNumber]').val() == ''){
                        dialog_nature_checked();
                        return false;
                    }else{
                        $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                    }
                }
            }
        }else{ // 三证合一没有修改
            if($('[name=threeInOne]:checked').val() == 0){// 没修改时三证合一为否的时候
                if(window.changeApply['nature'] && window.changeApply['nature']['valueAfter'] != '3'){//客户性质值改为一般纳税人或小规模纳税人
                    if(modify_taxRegistryNumber){ // 税务登记号 有修改
                        if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                            dialog_nature_checked();
                            return false;
                        }else{
                            $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                        }
                    }else{
                        if($('[name=taxRegistryNumber]').val() == ''){
                            dialog_nature_checked();
                            return false;
                        }else{
                            $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                        }
                    }
                }
            }
        }
    }else{ // 客户性质  没修改
        if($('#customerNature').val() != 3){
            if(modify_threeInOne){ // 三证合一有修改
                if(window.changeApply['threeInOne']['valueAfter'] == 0){
                    if(modify_taxRegistryNumber){ // 税务登记号 有修改
                        // 客户性质当前值不为3的时候应该 税务登记号必须有值
                        if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){
                            if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }else{
                        if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){
                            if($('[name=taxRegistryNumber]').val() == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }
                }
            }else{ // 三证合一没有修改
                if($('[name=threeInOne]:checked').val() == 0){
                    if($('[name=nature]').val() != '3' && $('[name=nature]').val() != '0'){ // 客户性质没改，三证合一没改，获取当前页面值然后判断税务登记是否必填
                        if(modify_taxRegistryNumber){ // 税务登记号 有修改
                            if(window.changeApply['taxRegistryNumber']['valueAfter'] == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }else{
                            if($('[name=taxRegistryNumber]').val() == ''){
                                dialog_nature_checked();
                                return false;
                            }else{
                                $('[name=taxRegistryNumber]').prev('div').html('税务登记账号')
                            }
                        }
                    }
                }
            }
        }
    }
    console.log('客户性质验证通过')
    // 再次提交审核弹窗
    dialogAgain(title,contant,status)
}
// 撤销
$("#withdraw").on("click",function(){
    var title = "关闭审核";
    var contant = "确定关闭此申请？";
    var status = "withdraw";
    // 撤销弹窗
    dialogAgain(title,contant,status);
});
// 关闭
$("#close").on("click",function(){
    utils.closeTab();
});

//客户委托书，远程身份证号重复校验提示
$("#table2").on("blur","input[name=delegationNum]",function (e) {
    var val = $(e.target).val();
    if(val){
        $.ajax({
            url:"/proxy-customer/customer/customerDelegationFile/checkoutExist",
            data:{delegationNum:val},
            type:'get',
            success:function (res) {
                console.log('身份证',res)
                if(res.length!=0){
                    utils.dialog({content: '身份证号已在【'+res[0].customerCode+'】使用，不可重复使用', quickClose: true, timeout: 2000}).showModal();
                }
            }
        })
    }
});


//自定义格式化时间格式
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

/*
/********审核工作流节点处理***/
function returnWorkflowData(val,boole){
    if($("#shippingSource").val()&&$("#shippingSource").val()!="3"){
        switch (String(val)){
            case "2":
                return boole?11:10;
            case "11":
                return boole?21:20;
            case "21":
                return boole?3:30
            case "20":
                return boole?11:10;
            case "30":
                return boole?21:20;
            case "10":
                return 2;
            default :
                return 2;
        }
    }else{
        if(val==10){
            return 2;
        }else{
            return boole?3:10;
        }

    }

}

function oneKeyCopyCerts() {
    const delegationFiles = $('#table2').getRowData()
    // 如果存在客户委托书，
    if (delegationFiles.length){
        const certTable = $('#certTable')
        // 遍历客户委托书，依次复制到被委托人身份证
        delegationFiles.forEach(lastDelegationFile=>{
            const certName = certTable.find('tr:last').find('input[name=customerName]').val()
            const certIdNo =  certTable.find('tr:last').find('input[name=identityNumber]').val()
            const certDate = certTable.find('tr:last').find('input[name=identityValidityDate]').val()
            // 若最后一行中 姓名，证件号码，身份证有效期 均有值，则新增一行进行插入。
            // 若最后一行中 姓名，证件好吗，身份证有效期 的值均为空，则不新增行，直接在最后一行的基础上修改。
            if (certName &&  certIdNo && certDate){
                addRow('#certTable')
            }
            certTable.find('tr:last').find('input[name=customerName]').val(lastDelegationFile.delegationName)
            certTable.find('tr:last').find('input[name=identityNumber]').val(lastDelegationFile.delegationNum)
            certTable.find('tr:last').find('input[name=identityValidityDate]').val(lastDelegationFile.delegationIdentityDate)
        })
    }else {
        utils.dialog({content: '暂无可用证件信息', quickClose: true, timeout: 2000}).showModal();
    }
}

// 校验基础属性中的客户名称 与 开票信息中的公司名称是否一致
function validateCustomerName(){
    // 变更后
    const changedCustomerName = window.changeApply.customerName
    const changedBillingCompanyName = window.changeApply.billingCompanyName
    // 变更前
    const customerName = $("[name=customerName]").val()
    const billingCompanyName = $("[name=billingCompanyName]").val()
    let result = true
    // 若 客户名称 与 公司名称 均未变更，则判断页面中这两个输入框的值是否一致
    if (!changedCustomerName && !changedBillingCompanyName) {
        result = customerName == billingCompanyName
    }
    // 若客户名称未变化，但公司名称发生变化。则比对页面中的 客户名称 字段与变更后的 公司名称 字段是否一致。
    if (!changedCustomerName && changedBillingCompanyName) {
        result = customerName == changedBillingCompanyName.valueAfter
    }
    // 若公司名称未变化，但客户名称发生变化。则比对页面中的 公司名称 字段与变更后的 客户名称 字段是否一致。
    if (changedCustomerName && !changedBillingCompanyName) {
        result = changedCustomerName.valueAfter == billingCompanyName
    }
    // 若 客户名称 与 公司名称 均发生变化，则这两个字段变更后的值是否一致
    if (changedCustomerName && changedBillingCompanyName) {
        result = changedCustomerName.valueAfter == changedBillingCompanyName.valueAfter
    }
    return result
}
