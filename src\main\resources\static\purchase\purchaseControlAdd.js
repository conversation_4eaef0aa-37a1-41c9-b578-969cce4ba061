$(function () {
    $('#X_Tablea').XGrid({
        // url: '/proxy-purchase/purchase/productSupplier/queryProductSupplierPageList',
        data:[],
        colNames: ['id','机构名称','机构编码','神农商品编码','商品名称','指定供应商编码','供应商名称','备注'/*,'操作人','操作时间'*/],
        colModel: [
            {
                name: 'id',
                index: 'id',
                hidden: true,
                hidegrid: true
            },
            {
                name: 'orgName',
                index: 'orgName',
                /*rowtype: "#orgName_select"*/
            },
            {
                name: 'orgCode',
                index: 'orgCode',
                hidden: true,
                hidegrid: true
            },
            {
                name: 'productCode',
                index: 'productCode',
                /*rowtype: "#orgName_input"*/
            },
            {
                name: 'productName',
                index: 'productName'
            },
            {
                name: 'supplierCode',
                index: 'supplierCode',
                formatter: function(val){
                    if(val != null && val == "-1"){
                        return '';
                    }else {
                        return val;
                    }
                }
            },
            {
                name: 'supplierName',
                index: 'supplierName'
            },
            {
                name: 'remark',
                index: 'remark',
                /*rowtype: "#remark_input"*/
            }
         /*   ,{
                name: 'updateUser',
                index: 'updateUser'
            },
            {
                name: 'updateTime',
                index: 'updateTime',
                formatter: function (e) {
                    if (e != null && e != "") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }*/
        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        //pager: '#grid_page',//设置翻页所在html元素名称
        selectandorder: true,
        rowList: [20, 50, 100],
        // attachRow:true,
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件订单', obj);
        },
        gridComplete: function () {}
    })

})


//下载模板
$("#downloadBtn").click(function () {
    window.open("/proxy-purchase/purchase/productSupplier/downloadTemplate")
})



// 导入之前检验
$("#batchAdd").click(function () {
    utils.dialog({
        url: '/proxy-purchase/purchase/productSupplier/fileupload',
        title: '批量导入',
        width: 600,
        height: 500,
        onclose: function () {
            var data = this.returnValue;
            console.log("data",data);
            if (data && data.length > 0) {
                var rowData = $('#X_Tablea').XGrid('getRowData')
                if(rowData&&rowData.some(function (row,key) {
                    return data.some(function (item) {
                        return row.productCode === item.productCode && row.supplierCode === item.supplierCode && row.orgCode === item.orgCode
                    })
                })){
                    utils.dialog({content: '数据重复，导入失败！', quickClose: true, timeout: 3000}).show();
                }else{
                    data.forEach(function (item,key) {
                        let rowNumber = $('#X_Tablea').find("tr").not(":first").length + 1;
                        item.id = rowNumber + key;
                        $('#X_Tablea').XGrid('addRowData', item);
                    })
                }
            }
        }
    }).showModal();
})

//保存
$("#saveBtn").click(function (e) {
    utils.dialog({
        title: '提示',
        content: '<div style="max-height: 300px;overflow-y: auto;">确认后，将会修改数据，是否确定操作？</div>',
        okValue: '确定',
        ok: function () {
            saveInfo();
        },
        cancelValue: '返回',
        cancel: function () {
            return;
        },
    }).showModal();


});

function saveInfo(){
    parent.showLoading();
    var tableData = $('#X_Tablea').XGrid('getRowData');
    $.ajax({
        method: "POST",
        url: "/proxy-purchase/purchase/productSupplier/save",
        data: {
            "ppsData": JSON.stringify(tableData)
        },
        dataType: 'json',
        cache: false,
    }).done(function (data) {
        parent.hideLoading();
        if(data == null){
            utils.dialog({content: "保存异常！", quickClose: true, timeout: 4000}).showModal();
        }else {
            utils.dialog({content: data.msg, quickClose: true, timeout: 4000}).showModal();
            $('#X_Tablea').setGridParam({
            }).trigger('reloadGrid');
        }
    });
}


// 新增
$('#addBtn').click(function () {
    var rowData = $('#X_Tablea').XGrid('getRowData');
    utils.dialog({
        title: '新增',
        url: '/proxy-purchase/purchase/productSupplier/toBounced',
        width: $(window).width() * 0.4 > 600 ? $(window).width() * 0.4 : 600,
        height: $(window).height() * 0.6,
        data: {rowData:rowData},
        onclose: function () {
            if (this.returnValue){
                var productCode = this.returnValue.productCode;
                var productName = this.returnValue.productName;
                var supplierCode = this.returnValue.supplierCode;
                var supplierName = this.returnValue.supplierName;
                var remark = this.returnValue.remark;
                let rowNumber = $('#X_Tablea').find("tr").not(":first").length + 1;
                let addList = this.returnValue.org.forEach(function (item,key) {
                    $('#X_Tablea').XGrid('addRowData', {
                        id: rowNumber + key,
                        orgName: item.orgName,
                        orgCode: item.orgCode,
                        productCode: productCode,
                        productName: productName,
                        supplierCode: supplierCode,
                        supplierName: supplierName,
                        remark: remark
                    });
                });
            }
        }
    }).showModal();
    /*let rowNumber = $('#X_Tablea').find("tr").not(":first").length + 1;
    $('#X_Tablea').XGrid('addRowData', {id:rowNumber});
    $('#productCode_input_'+'rowNumber input[name=productCode]').Autocomplete({
        serviceUrl:'/proxy-product/product/productBase/query', //异步请求
        paramName: 'productCode',
        params: {
            pageNum: 1,
            pageSize: 5
        },
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            if(response.code == 0){
                if(response.result){
                    return {
                        suggestions: $.map(response.result.list, function (dataItem) {
                            return {value: dataItem.productCode+"\xa0\xa0\xa0"+dataItem.productName,productCode:dataItem.productCode,productName:dataItem.productName};
                        })
                    };
                }

            }
        },
        onSelect: function (result) {
            $('#X_Tablea').XGrid('setRowData',rowNumber, {productCode:result.productCode,productName:result.productName});
        },
        onNoneSelect: function (params, suggestions) {
            $('#X_Tablea').XGrid('setRowData',rowNumber, {productCode:result.productCode,productName:result.productName});
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    })

    $('#supplierCode_input_'+'rowNumber input[name=productCode]').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/supplier/getSuppliserKeyInfo', //异步请求
        paramName: 'keyword',
        params: {
            pageNum: 1,
            pageSize: 5
        },
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        maxWidth: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.SupplierName +"\xa0\xa0\xa0"+ dataItem.supplierCode, supplierCode: dataItem.supplierCode,supplierName:dataItem.supplierCode};
                })
            };
        },
        onSelect: function (result) {
            var supplierCode = result.supplierCode;
            var supplierName = result.supplierName;
            $("#supplierCodeVal").val(supplierCode);
            $("#supplierNameVal").val(supplierName);
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierCodeVal").val("");
            $("#supplierNameVal").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });*/
});

//删除
$('#deleteBtn').click(function () {
    var selRow = $('#X_Tablea').XGrid('getSeleRow');
    if (!selRow||!selRow.length) {
        utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 3000}).showModal();
        return false;
    }
    $.each(selRow,function (index,item) {
        $('#X_Tablea').XGrid('delRowData', item.id);
    });
});

$("#setBtn").click(function () {
    $('#X_Tablea').XGrid('filterTableHead');
});


