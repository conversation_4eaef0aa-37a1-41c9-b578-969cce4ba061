$(function () {
    let selfEdit = $('#selfEdit').val(), editAddress = $('#editAddress').val(), _has = ($('#selfEdit').length > 0);
    // 注册地址
    let addressSelIdObj = [
        {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvince',nextNodeId: 'province1'},
        {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCity',nextNodeId: 'registerCity'},
        {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerArea',nextNodeId: 'district1'}
    ];
    let registerPromiseArray = [];
    utils.setAllProDom('#provinceSel_wrap', addressSelIdObj, '#registerBox',true, function () {
        // 注册地址有值回显
        let _registerHiddenVal = eval($('#registerAddressJson').val());
        if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
        _registerHiddenVal.splice(_registerHiddenVal.length - 1);
        if (_has) {
            if ( selfEdit != 'Y' || editAddress != 'true') {
                $('#' + addressSelIdObj[0]['nextNodeId']).prop('disabled', true);
            }else{
                if ($('#resentBtn').css('display') == 'inline-block' || $('#saveRowData').css('display') == 'inline-block') { //  没有办法了才加这么个if这么写。 逻辑已经找不回来了。。。后面接手的大神多担待！！！
                    if (!$('[name=accountName]').prop('readonly')){
                        $('#registerBox select,#registerBox input').removeAttr('disabled readonly');
                    }
                }else{
                    $('#registerBox select,#registerBox input').prop('readonly',true);
                }
            }
        }
        $('#' + addressSelIdObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
        for (let i = 1; i < _registerHiddenVal.length; i++) {
            registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
        }
        Promise.all(registerPromiseArray).then(data => {
            for (let i = 0; i < data.length; i++) {
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).html(data[i]);
                $('#' + addressSelIdObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                if (_has) {
                    if (selfEdit != 'Y' || editAddress != 'true') {
                        $('#' + addressSelIdObj[i + 1]['nextNodeId']).prop('disabled', true);
                    }else{
                        if ($('#resentBtn').css('display') == 'inline-block' || $('#saveRowData').css('display') == 'inline-block') {
                            if (!$('[name=accountName]').prop('readonly')){
                                $('#registerBox select,#registerBox input').removeAttr('disabled readonly');
                            }
                        }else{
                            $('#registerBox select,#registerBox input').prop('readonly',true);
                        }
                    }
                }
            }
        })
    });

    // 仓库地址
    let storgeAddressSelIdObj = []
    let _storgeHiddenVal = eval($('#repertoryAddressJson').val());
    if (!_storgeHiddenVal) _storgeHiddenVal = [['','','','']];
    let _storgeHiddenValArr =  eval($('#repertoryAddressJson').val());
    if (!_storgeHiddenValArr) _storgeHiddenValArr = [['','','','']];
    $(_storgeHiddenValArr).each((index,item) => {
        item.splice(item.length - 1);
    });
    let obj = distpickerHTML(_storgeHiddenValArr.length);
    $(obj.radomInit).each((index, item) => {
        let _arr = [
            {nextNodeWrap: '#stoProvinceSel_wrap_' + item,nextNodeName: 'repertoryProvince_' + item,nextNodeId: 'repertoryProvince_' + item},
            {nextNodeWrap: '#stoCitySel_wrap_' + item,nextNodeName: 'repertoryCity_' + item,nextNodeId: 'repertoryCity_' + item},
            {nextNodeWrap: '#stoDistrictSel_wrap_' + item,nextNodeName: 'repertoryArea_' + item,nextNodeId: 'repertoryArea_' + item}
        ]
        storgeAddressSelIdObj.push(_arr)
    });
    $('#depotAddress').html(obj.html)
    $(obj.radomInit).each((index, item) => {
        let storagePromiseArray = [];
        utils.setAllProDom('#stoProvinceSel_wrap_' + obj.radomInit[index], storgeAddressSelIdObj[index], '#storageBox_' + obj.radomInit[index], true,function () {
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).val(_storgeHiddenValArr[index][0]);
            $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[index][3]);
            if (_has) {
                if (selfEdit != 'Y' || editAddress != 'true') {
                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).prop('disabled', true);
                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').prop('disabled', true);
                    $('#' + storgeAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('.btn').css('display','none');
                }else{
                    if ($('#resentBtn').css('display') == 'inline-block' || $('#saveRowData').css('display') == 'inline-block') {
                        if (!$('[name=accountName]').prop('readonly')){
                            $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
                            $('#depotAddress .btn').css('display','inline-block');
                        }
                    }
                }
            }
            for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
                storagePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenValArr[index][ind-1]));
            }
            let allSelArr = storgeAddressSelIdObj[index].flat().map((item, index) => {
                if (index != 0) {
                    return item['nextNodeId']
                }
            }).filter(item => {
                return item
            });
            Promise.all(storagePromiseArray).then(data => {
                for (let i = 0; i < data.length; i++) {
                    $('#' + allSelArr[i]).html(data[i]);
                    $('#' + allSelArr[i]).val(_storgeHiddenValArr[index][i + 1]);
                    if (_has) {
                        if (selfEdit != 'Y' || editAddress != 'true') {
                            $('#' + allSelArr[i]).prop('disabled', true);
                        }else{
                            if ($('#resentBtn').css('display') == 'inline-block' || $('#saveRowData').css('display') == 'inline-block') {
                                if (!$('[name=accountName]').prop('readonly')){
                                    $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
                                    $('#depotAddress .btn').css('display','inline-block');
                                }
                            }
                        }
                    }
                }
                let addReturnPageFlag= $('#addReturnPageFlag').val();
                if (addReturnPageFlag == 'rturnAddPage') {
                    $('#registerAddress select,#registerAddress input').removeAttr('disabled readonly');
                    $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
                    $('#depotAddress .btn').css('display','inline-block');
                }
            })
        })
    })
})
