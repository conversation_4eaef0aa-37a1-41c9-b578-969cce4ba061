$(function() {
		   //查看页面不能显示按钮
			$('.toggle-btn').hide();
			//输入框不可用
			$('input[type="text"]').attr('disabled','');
//			//发票状态
//	        var invoiceStatus = $("#invoiceStatus").val();
//	        //发票详情
//	        var invoiceDetailVoList = $("#invoiceDetailVoList").val();
//	        //入库单列表
//	        var storeInList = $("#storeInList").val();
//	        //入库单详情
//	        var storeInDetailList = $("#storeInDetailList").val();
				
			
			//入库单详情
			 function initDialogTable(data){
				 $('#X_Table1').XGrid({
	                    data: data,
	                    // url: 'http://www.baidu.com?param='+param,
	              colNames: ['id', '单据日期', '采购单据号', '采购单据行号', '商品编码', '商品名称', '规格', '小包装单位', '含税单价', '含税金额',
	                        '小包装数量',
	                        '金额合计', '税额合计', '价税合计'
	               ],
                colModel: [
                	{
                        name: 'id',
                        hidden: true
                    },
                    {
                        name: 'storeTime'
                    }, {
                        name: 'stockOrderNo'
                    }, {
                        name: 'billsSort'
                    },{
                        name: 'productCode'
                    },{
                        name: 'productName'
                    },{
                        name: 'productSpecification'
                    },{
                        name: 'productPackUnitSmall'
                    }, {
                        name: 'productContainTaxPrice'
                    },{
                        name: 'productContainTaxMoney'
                    },{
                        name: 'productPackInStoreCount'
                    },{
                        name: 'procutNoTaxMoney'
                    },{
                        name: 'productTaxMoney'
                    },{
                        name: 'productContainTaxMoney'
                    }],
	                    // rowNum: 10,
	                    rownumbers: true,//是否展示序号
	                    altRows: true, //设置为交替行表格,默认为false
	                    gridComplete: function () {
	                        console.log('*********');
	                    }
	                    // pager: '#grid-pager',
	                });
			 }

			//已选择入库单
			$('#X_Tablea').XGrid(
					{
						data: storeInList,
						colNames: ['供应商', '供应商编码', '采购单据单号', '单据日期',
			                '制单人', '金额合计', '税额合计', '税率', '价税合计', 'id'],
						colModel : [ {
							name : 'supplierName'

						},{
							name : 'supplierCode',
							hidden:true

						}, {
							name : 'stockOrderNo'
						}, {
							name : 'storeTime'

						}, {
							name : 'createUser'

						}, {
							name : 'sumNoTaxMoney'

						}, {
							name : 'sumTaxMoney'

						}, {
							name : 'productEntryTax',
	                        formatter: function (value) {
	                            if (!value) {
	                                return '';
	                            } else {
	                                return parseFloat(value) + '%';
	                            }
	                        }
						}, {
							name : 'priceTaxSum'
						},{
							name : 'id',
							hidden : true

						}],
						rowNum : 10,
						rownumbers: true,//是否展示序号
						key : 'id',
						altRows : true, //设置为交替行表格,默认为false
						ondblClickRow: function (id, dom, obj, index, event) {
								var arr = [];
								$.each(storeInDetailList,function(index,item){
									if(item.stockOrderNo == obj.stockOrderNo){
										arr.push(item);
									}
								})
							
								initDialogTable(arr);
			                    //双击事件回调函数
			                    utils.dialog({
			                        title: '已选采购单据明细行',
			                        width: 1000,
			                        content: $('#modal')
			                    }).showModal();

			             },
			             gridComplete: function () {
			                    setTimeout(function () {
			                    	totalStoreInSum();
			                    }, 200)
			                },

					});

			 function totalStoreInSum() {

	                addtotalRow($('#X_Tablea'), {
	                	id: "合计",
	                    sumNoTaxMoney: "sumNoTaxMoney",
	                    sumTaxMoney: "sumTaxMoney",
	                    priceTaxSum: "priceTaxSum"
	                })
	            }
			

		    // 增加合计行
            function addtotalRow($X_Table, rowData) {

                var tem = [],
                    data = $X_Table.XGrid('getRowData');

                $.each(data, function (idnex, item) {
                    if (item.id) {
                        tem.push(item)
                    }
                })

                data = tem;

                $.each($X_Table.find('tr'), function (index, item) {
                    if ($(item).find('td:first-child').html() === '合计') {
                        $(item).remove();
                    }
                })
                for (var key in rowData) {
                    if (rowData[key] && (key !== 'id')) {
                        rowData[key] = totalTableY(data, key);
                    }
                }
                $X_Table.XGrid('addRowData', rowData);
                $X_Table.find('tr:last-child').attr('id', '').find('td:first-child').html('合计');
            }

            //合计
            function totalTableY(data, colName) {
                var count = 0;
                $.each(data, function (index, item) {
                    count += parseFloat(item[colName]);
                })
                return count.toFixed(2);
            }
		    
            
            //发票明细
            $('#Y_Tablea').XGrid({
                data : invoiceDetailVoList,
                colNames: ['序号', '<span class="danger">*</span> 供应商发票号',
                    '<span class="danger">*</span> 发票日期', '<span class="danger">*</span> 发票不含税金额',
                    '<span class="danger">*</span> 发票含税金额', '发票税额', '税率', '备注'
                ],
                colModel: [{
                    name: 'id',
                    hidden:true
                }, {
                    name: 'supplierInvoiceNumber'
                }, {
                    name: 'invoiceDate'
                }, {
                    name: 'noInvoiceTaxAmount',
                    formatter:function(value){
                    	if(value != ''){
                    		return parseFloat(value).toFixed(2);
                    	}else{
                    		return '0.00';
                    	}
                   }
                }, {
                    name: 'invoiceTaxAmount',
                    formatter:function(value){
                    	if(value != ''){
                    		return parseFloat(value).toFixed(2);
                    	}else{
                    		return '0.00';
                    	}
                   }
                }, {
                    name: 'tax',
                    formatter:function(value){
                    	if(value != ''){
                    		return parseFloat(value).toFixed(2);
                    	}else{
                    		return '0.00';
                    	}
                   }
                }, {
                    name: 'taxRate',
                    formatter:function(value){
                    	if(!value) {
                    		return '';
                    	}else{
                    		return value+'%';
                    	}
                    }
                }, {
                    name: 'rates'
                }],
                key: 'id',
                rowNum: 1000,
                // rownumbers: true,
                altRows: true, //设置为交替行表格,默认为false
                ondblClickRow: function (id, dom, obj, index, event) {
                    //双击事件回调函数
                    if(id == '999') return false;
                    $('#Y_Tablea').XGrid('editRow', id);

                },
                gridComplete: function(){
                    setTimeout(function(){
                        totalTable();
                    },200)
                }

            });

            // 行内计算发票税额和税率
            function rowEvent(etype) {
                console.log(etype.e.target, 'text改变时触发的事件');
                $target = $(etype.e.target);
                //发票含税金额
                var invoiceTaxAmount = parseFloat($.trim(data.invoiceTaxAmount).replace(/,/g, ''));
                //发票不含税金额
                var noInvoiceTaxAmount = parseFloat($.trim(data.noInvoiceTaxAmount).replace(/,/g, ''));
                //税额
                tax = invoiceTaxAmount - noInvoiceTaxAmount;
                //税率 = 税额/发票不含税金额
                taxRate = tax / noInvoiceTaxAmount;
                taxRate = (taxRate * 100).toFixed(2) + '%';
                if (tax < 0 || Number.isNaN(tax)) {
                	tax = '';
                	taxRate = '';
                }
                $target.closest('tr').find('td[row-describedby="tax"]').html(tax);
                $target.closest('tr').find('td[row-describedby="taxRate"]').html(taxRate);
            }

            //合计
            function totalTable() {
            	 $.each($('#Y_Tablea').find('tr'), function (index, item) {
                     if ($(item).find('td:first-child').html() === '合计') {
                         $(item).remove();
                     }
                 })
                var data = $('#Y_Tablea').XGrid('getRowData');
               
                $('#Y_Tablea').XGrid('addRowData', {
                    id: '999',
                    noInvoiceTaxAmount: totalSum(data, 'noInvoiceTaxAmount'),
                    invoiceTaxAmount: totalSum(data, 'invoiceTaxAmount'),
                    tax: totalSum(data, 'tax')
                });
                //$('#Y_Tablea tr:last-child').attr('id','');
                $('#Y_Tablea tr:last-child td:first-child').html('合计');
                $('#Y_Tablea tr:last-child').find('td:eq(2)').html('');
            }

            //发票明细合计
            function totalSum(data, colName) {
                if( $('#Y_Tablea tr:last-child td:first-child').html() == '合计'){
                    $('#Y_Tablea tr:last-child').remove();
                }
                var count = 0;
                $.each(data, function (index, item) {
                	count += parseFloat($.trim(item[colName]).replace(/,/g,''));
                })
                return count.toFixed(2);
            }
            
            
            //删除选中行
            $('#deleRow').click(function (param) {

                var selRow = $('#Y_Tablea').XGrid('getSeleRow');
                if (selRow) {
                    //删除二次确认
                    utils.dialog({
                        title: "提示",
                        content: "确认删除当前选中行？",
                        okValue: '确定',
                        ok: function () {
                            $('#Y_Tablea').XGrid('delRowData', selRow.id);
                            totalTable();
                        },
                        cancelValue: '取消',
                        cancel: function () {}
                    }).showModal();
                } else {
                    utils.dialog({
                        content: '没有选中任何行！',
                        quickClose: true,
                        timeout: 2000
                    }).showModal();
                }
            })

            //新增一行
            $('#addRow').on('click', function () {

                id = $('#Y_Tablea tr').length - 1;
                if (id > 50) {
                    utils.dialog({content: '最多可添加50行!', quickClose: true, timeout: 2000}).show();
                    return false;
                }

                $('#Y_Tablea').XGrid('addRowData', {
                    id: id
                }, 'before', '999');

                $('#Y_Tablea').XGrid('editRow', id);

            })

            //保存行
            $('#saveRow').on('click', function () {
                var selRow = $('#Y_Tablea').XGrid('getSeleRow');
                if (selRow) {
                    if (!validInput(selRow)) return false;
                    $('#Y_Tablea').XGrid('saveRow', selRow.id);
                    totalTable();
                    
                  //获取发票合计金额
		            var totalInvoiceData = $('#Y_Tablea').XGrid('getRowData').pop();
		            var totalStoreInData = $('#X_Tablea').XGrid('getRowData').pop();
		            
		            //赋值
		            //发票税额合计(发票汇总税额)
		            $("#totalInvoiceValue").val(totalInvoiceData.tax);
		            //发票价税合计(发票汇总含税总额)
		            $("#totalInvoiceTax").val(totalInvoiceData.invoiceTaxAmount);
		            //发票不含税金额
		            $("#noTotalTaxAmount").val(totalInvoiceData.noInvoiceTaxAmount);
		            //货值差 发票不含税金额合计-入库单金额合计
		            var valueDifference = totalInvoiceData.noInvoiceTaxAmount - totalStoreInData.sumNoTaxMoney;
                    $("#valueDifference").val(parseFloat(valueDifference).formatMoney('2','',',','.'));
                } else {
                    utils.dialog({
                        content: '没有选中任何行！',
                        quickClose: true,
                        timeout: 2000
                    }).showModal();
                }
            })

             // 校验保存数据
           function validInput(data) {
               var isValid = true;
               if (data.supplierInvoiceNumber == '') {
                   utils.dialog({
                       content: '供应商发票号不能为空！',
                       quickClose: true,
                       timeout: 2000
                   }).showModal();
                   isValid = false;
                   return false;
               }
               if (data.invoiceDate == '') {
                   utils.dialog({
                       content: '请选择发票日期！',
                       quickClose: true,
                       timeout: 2000
                   }).showModal();
                   isValid = false;
                   return false;
               }
               if (data.noInvoiceTaxAmount == '') {
                   utils.dialog({
                       content: '发票不含税金额不能为空！',
                       quickClose: true,
                       timeout: 2000
                   }).showModal();
                   isValid = false;
                   return false;
               }
               if (data.invoiceTaxAmount == '') {
                   utils.dialog({
                       content: '发票含税金额不能为空！',
                       quickClose: true,
                       timeout: 2000
                   }).showModal();
                   isValid = false;
                   return false;
               }
               return isValid;
           }
			
			
			//返回
			$('#backInvoice').bind('click', function () {
				 //弹框
				utils.dialog({
		            content: '确定返回？',
		            quickClose: false,
		            okValue: '确定',
		            cancelValue: '取消',
		            ok: function () {
		            	window.location.href="/proxy-finance/finance/purchase/invoice/toList";
		            },
		            cancel: true
		        }).showModal();
			})
			
			//保存
			$('#saveInvoice').bind('click', function () {
				 //校验
				 if validform("formTable").form() && (validform("invoiceForm").form()) {
                    //发票明细校验
                     var flag = true;
                     if($('#Y_Tablea tr').length == 2){
                         utils.dialog({content: '无发票明细,请添加', quickClose: true, timeout: 2000 }).show();
                         flag = false;
                     }
                     if(!flag) return false;
                     
					   var invoiceNo = $("#invoiceNo").val();
					   var invoiceStatus = 1;
					   saveInvoice(invoiceNo,invoiceStatus);
				   } 
			      return false;
				
			})
			
			//过账，过账时校验汇总不含税金额差额
			$('#outInvoice').bind('click', function () {
			//汇总不含税金额差额，校验 小于10，小于发票不含税金额的1%
			  var valueDifference = parseFloat($('#valueDifference').val());
			  var noTotalTaxAmount =  parseFloat($("#noTotalTaxAmount").val());
			  var bills = valueDifference/noTotalTaxAmount;
				 //校验
				 if (validform("formTable").form() && validform("invoiceForm").form()) {
				  //发票明细校验
                     var flag = true;
                     if($('#Y_Tablea tr').length == 2){
                         utils.dialog({content: '无发票明细,请添加', quickClose: true, timeout: 2000 }).show();
                         flag = false;
                     }
                     if(!flag) return false;
				 
					  if(Math.abs(valueDifference) < 10 && Math.abs(bills) < 0.01 ){
					       var invoiceNo = $("#invoiceNo").val();
						   var invoiceStatus = 2;
						   saveInvoice(invoiceNo,invoiceStatus);
					  }else{
					   utils.dialog({content:'过账差额超过范围，请使用财务高级权限用户进行“超级过账”',quickClose:true,timeout:2000}).show();
					   return false;
					  }
				 } 
			    return false;
				
			})
		    //作废
			$('#feiInvoice').bind('click', function () {
				 //校验
				 if ( validform("formTable").form() && validform("invoiceForm").form()) {
				   //发票明细校验
                     var flag = true;
                     if($('#Y_Tablea tr').length == 2){
                         utils.dialog({content: '无发票明细,请添加', quickClose: true, timeout: 2000 }).show();
                         flag = false;
                     }
                     if(!flag) return false;
                     
					 var invoiceNo = $("#invoiceNo").val();
					   var invoiceStatus = 3;
					   saveInvoice(invoiceNo,invoiceStatus); 
				    }
			        return false;
				
			})
			
			function saveInvoice(invoiceNo,invoiceStatus){
				//发票表单数据
				var invoiceFormdata = $('#invoiceForm').serializeToJSON();
				//发票明细表单数据
				//var invoiceDetailFormdata = $('#invoiceDetailForm').serializeToJSON(),
				var invoiceDetailFormdata = $('#Y_Tablea').XGrid('getRowData');
                //如果表格下边有合计行
                invoiceDetailFormdata.pop();
                
                $.each(storeInList,function(index,item){
					delete item.id;
				})
				$.each(invoiceDetailFormdata,function(index,item){
					delete item.id;
					item.taxRate = item.taxRate.split('%').join('');
				})
                
				console.log("发票数据",invoiceFormdata);
				console.log("发票明细",invoiceDetailFormdata);
				console.log("入库单列表",storeInList);
				console.log("入库单详情",storeInDetailList);
			
			
				//保存
				 $.ajax({
		                url:'/proxy-finance/finance/purchase/invoice/tosaveInvoice',
		                type:'post',
		                data:{storeInList:JSON.stringify(storeInList),
		                	  storeInDetailList:JSON.stringify(storeInDetailList),
		                	  invoiceInfo:JSON.stringify(invoiceFormdata),
		                	  invoiceDetail:JSON.stringify(invoiceDetailFormdata),
		                	  invoiceNo:invoiceNo,
		                	  invoiceStatus:invoiceStatus
		                	},
		                success:function(result){
		                    utils.dialog({content:result.msg, quickClose: true, 
		                        timeout: 3000}).showModal();
		                	if(result.result.invoiceNo != null){
		                		$("#invoiceNo").val(result.result.invoiceNo);
		                	}
		                 //   $('#Y_Tablea').XGrid('editRow', id);
		                },
		                error:function (error) {
		                    console.log(error)
		                }
		            })
		    }

		})