$(function() {

    var dialog = parent.dialog.get(window);
    $('#X_Table').XGrid({
        url: '/proxy-purchase/purchase/PriceOrderProduct/selectPurchaseStorePriceOrderProductDataByRefund?stockOrderNo='+$("#stockOrderNo").val()+"&productCode="+$("#productCode").val(),
        colNames: ['id','退补价订单编号','商品编号','商品名称','商品规格','小包装单位','生产批号','生产日期','有效期至','库存数量','生产厂家','新含税单价','冲价数量','新税率','新税额','含税差价','APP售价','创建日期'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                hidden:true
            },
            //商品编号
            {
                name: 'orderRefundPriceNo',
            },
            //商品编号
            {
                name: 'productCode',
            },
            {//商品名称
                name: 'productName',
            },
            {//商品规格
                name: 'productSpecification',
            },
            {//小包装单位
                name: 'productPackUnitSmall',
            },
            {//批号
                name: 'productApprovalNumber',
                hidden:true
            },
            {//生产日期
                name: 'productProduceDate',
                hidden:true
            },
            {//有效期至
                name: 'productExpireDate',
                hidden:true
            },

            {//商品入库数量
                name: 'productPackInStoreCount',
            },
            {//商品生产厂家
                name: 'productProduceFactory',
            },
            {//新含税单价
                name: 'newTaxPrice',
            },
            {//冲价数量
                name: 'productPunchPriceCount',
            },
            {//新税率
                name: 'newTaxRate',
                hidden:true
            },
            {//新税额
                name: 'newTaxMoney',
                hidden:true
            },
            {//含税差价
                name: 'taxPriceDiffer',
            },
            {//APP售价
                name: 'productAppContainTaxPrice',
                hidden:true
            },
            {
                name: 'createTimeFormat',
                index: 'createTimeFormat'
            }

        ],
        key: 'id',
        rowNum: 0,
        rownumbers: true,//是否展示序号
        multiselect: true,
        disableRow: dialog.data,
        //rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false

        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            this.returnValue = obj;
            dialog.close(obj);
            return obj;
        },
        //pager: '#grid-pager',
    });

});