function initDate(param) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    var month = new_date.getMonth() + 1;
    var date = new_date.getDate();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    //console.log(new_date, year, month, date);
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#beginTime').val(year + '-' + month + '-01');
    $('#endTime').val(year + '-' + month + '-' + date);
}
$($("#checkPlanFrom .row").get(1)).find("input,select").attr('disabled',true);
window.curIndex = 0;
// tabs 切换
$('.historyPlanTabs>li').on('click', function () {
    var $this = $(this), $nav_content = $('.nav-content');
    window.curIndex = $this.index();
    $this.addClass('active').siblings().removeClass('active');
    $nav_content.children('.panel-body').eq($this.index()).addClass('active').siblings().removeClass('active');
    $nav_content.children('.panel-body').eq($this.index()).css("display","flex").siblings().css("display","none");
    $("#btn_toggle").children("button").eq($this.index()).show().siblings().hide();
    if(window.curIndex == 1){
        $("#productCode").prop("disabled",false);
        $("#productName").prop("disabled",false);
        $("#drugClass").prop("disabled",false);
        $("#batchName").prop("disabled",false);
        $("#manuFactory").prop("disabled",false);
        //  $("#moveType").prop("disabled",false);
    }else{
        $("#productCode").prop("disabled",true);
        $("#productName").prop("disabled",true);
        $("#drugClass").prop("disabled",true);
        $("#batchName").prop("disabled",true);
        $("#manuFactory").prop("disabled",true);
    }
})


$(function () {
    initDate()
    initDrugClass()
    $('#X_Table').XGrid({
        url:'/proxy-gsp/gsp/checkPlan/queryHistoryChekPlan',
        colNames: ['养护记录单据编号', '日期', '机构名称', '部门名称', '养护员', '养护类别', '单据状态', '备注'],
        postData:{
            orgCode:$("#orgValue").val(),
            startTime:$("#beginTime").val(),
            endTime:$("#endTime").val()
        },
        colModel: [
            {
                name: 'checkPlanCode',
                index: 'checkPlanCode',//索引。其和后台交互的参数为sidx
                key: true
            }, {
                name: 'checkTimes',
                index: 'checkTimes',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'orgName',
                index: 'orgName'

            }, {
                name: 'depaName',
                index: 'depaName'
            }, {
                name: 'userName',
                index: 'userName'
            }, {
                name: 'checkTypeName',
                index: 'checkTypeName'

            }, {
                name: 'statusName',
                index: 'statusName'

            }, {
                name: 'rate',
                index: 'rate'
            }

        ],
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true,//设置为交替行表格,默认为false
        //rownumbers: true,//是否展示序号，多选
        selectandorder:true,
        ondblClickRow: function (e, c, a, b) {

         /*   $("#checkPlanCode").val(a.checkPlanCode)
            $("#orgName").val(a.orgName);
            $("#deptName").val(a.depaName);
            $("#userName").val(a.userName);

            var el = document.querySelector('#checkPlanInfo_Table_wrap');//html元素
            utils.dialog({
                title:'历史养护记录详情',
                content: el,
                width:$(window).width()*0.8,
                height:$(window).height()*0.8

            }).showModal();*/
        },
        onSelectRow: function (e, c, a, b) {

        },
        pager: '#grid-pager',
    });
    $('#checkPlanInfo_Table').XGrid({
        url:'/proxy-gsp/gsp/stockInCheck/queryHistoryinfo',
        colNames: ["养护计划单据编号","商品编码", "商品名称","商品大类", "商品规格 ", "生产厂家", "单位", "剂型", "库房名称","批号","实际养护数量","养护类别","生产日期","有效期","养护完成日期","近效期天数","养护周期","养护状态","备注"],
        postData:{
            startTime:$("#beginTime").val(),
            endTime:$("#endTime").val(),
            status:1
        },
        colModel: [
            {
                name: 'checkPlanCode',
                index: 'checkPlanCode',
                width:'250'
            },
            {
                name: 'productCode',
                index: 'productCode',//索引。其和后台交互的参数为sidx
                key: true
            }, {
                name: 'productName',
                index: 'productName'

            }, {
                name: 'drugClass',
                index: 'drugClass',

            }, {
                name: 'specifications',
                index: 'specifications'
            }, {
                name: 'manufacturerVal',
                index: 'manufacturerVal'
            }, {
                name: 'packingUnitVal',
                index: 'packingUnitVal'
            },
            {
                name: 'dosageFormVal',
                index: 'dosageFormVal',

            }, {
                name: 'wareHouseName',
                index: 'wareHouseName'


            }, {
                name: 'productBatch',
                index: 'productBatch'
            },{
                name: 'amount',
                index: 'amount'
            }, {
                name: 'checkTypeName',
                index: 'checkTypeName'
            }, {
                name: 'productDate',
                index: 'productDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'validateDate',
                index: 'validateDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'dateTime',
                index: 'dateTime'
            }, {
                name: 'neareffect',
                index: 'neareffect'
            }, {
                name: 'checkcycle',
                index: 'checkcycle'
            }, {
                name: 'documentStatus',
                index: 'documentStatus'
            }, {
                name: 'remark',
                index: 'remark'
            }
        ],
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true,//设置为交替行表格,默认为false
        //rownumbers: true,//是否展示序号，多选
        selectandorder:true,
        ondblClickRow: function (e, c, a, b) {


        },
        onSelectRow: function (e, c, a, b) {

        },
        pager: '#checkPlanInfo_Table-pager',
    });


    $.ajax({
        url: "/proxy-gsp/gsp/checkPlan/queryOrg",
        type: "get",
        success: function (result) {
            var  options  =""
            for (var i =0;i<result.result.length;i++) {
                options+=" <option value="+result.result[i].orgCode+" >"+result.result[i].orgName+"</option>"
            }
            $("#orgcode").html(options)
            $("#orgcode").val($('#orgValue').val())
            var  orgValue  =   $("#orgcode").val();
            initOrgCode($('#orgValue').val())
            
        }

    })

})






    function initDrugClass(){
        $.ajax({
            url:"/proxy-sysmanage/sysmanage/dict/querycommonnotpage",
            type:"POST",
            data:{type:7},
            success:function(result){

                var str = "<option value=''>全部</option>";
                var list = result.result;
                for (let i = 0; i < list.length ; i++) {
                     str += "<option value = "+list[i].id+">"+list[i].name+"</option>>"
                }
                $("#drugClass").html(str);
            }
        })
    }

//机构名称
function initOrgCode(val) {
    $.ajax({
        url: "/proxy-gsp/gsp/checkPlan/varietyCount",
        type: "post",
        async:true,
        data:{orgCode:val,startTime:$("#beginTime").val(),endTime:$("#endTime").val(),checkType:$("#checkType").val(),userName:$("#chckUser").val()},
        success: function (result) {
            $('#pointVarietiesNum').text(result.pointCode)
            $('#commonVarietiesNum').text(result.commonCode)
            $('#pointBatchNum').text(result.pointBatch)
            $('#commonBatchNum').text(result.commonBatch)

        }
    })
}

    //查询
    function btn_search() {

        var tableId = $('#nav_content .active .table-box .XGridBody table').attr('id');
        if(tableId == "X_Table"){

            var  orgValue  =   $("#orgValue").val();
            initOrgCode(orgValue)
            $('#X_Table').setGridParam({
                url: '/proxy-gsp/gsp/checkPlan/queryHistoryChekPlan',
                postData: {
                    startTime:$("#beginTime").val(),
                    endTime:$("#endTime").val(),
                    orgCode:$("#orgValue").val(),
                    rate:$("#remark").val(),
                    checkType:$("#checkType").val(),
                    userName:$("#chckUser").val()
                }
            }).trigger('reloadGrid');
        }

        if(tableId == "checkPlanInfo_Table"){
            $('#checkPlanInfo_Table').setGridParam({
                url: '/proxy-gsp/gsp/stockInCheck/queryHistoryinfo',
                postData: {
                    productCodeJq:$("#productCode").val(),
                    productName:$("#productName").val().replace(/^\s*|\s*$/g,""),
                    drugClass:$("#drugClass").val(),
                    productBatch:$("#batchName").val(),
                    manufacturerVal:$("#manuFactory").val(),
                    startTime:$("#beginTime").val(),
                    endTime:$("#endTime").val(),
                    checkName:$("#chckUser").val(),
                    checkType:$("#checkType").val(),
                    status:1


                }
            }).trigger('reloadGrid');

        }
        }



    $("#set_tb_rows").click(function () {
        var id = $("#nav_content .active .XGridBody table").attr('id');
        $('#'+id).XGrid('filterTableHead');
    });


function btn_output() {
    var tableId = $('#nav_content .active .table-box .XGridBody table').attr('id');
    z_utils.exportTable(tableId, function (that) {
        //需要导出项
        var colName = [];
        var colNameDesc = [];
        $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
            if ($(this).prop("checked")) {
                colName.push($(this).attr('name'))
                colNameDesc.push($(this).parent().text());
            }
        });

        //获取form数据
        var formData = $('#checkPlanFrom').serializeToJSON();
        formData["colName"] = colName;
        formData["colNameDesc"] = colNameDesc;

        var pageId = '';
        if(tableId == "X_Table"){
            pageId = 'grid-pager';
        } if(tableId == "checkPlanInfo_Table"){        
            pageId = 'checkPlanInfo_Table-pager';
        }
        var len = Number($('#' + pageId +'  #totalPageNum').text());
        //获取当前选中项
        var data = $('#' + tableId).XGrid('getSeleRow');
        if (data) {
            if (data.length && data.length >0) {
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                len = data.length;
                data = JSON.stringify(data);
                formData["selectData"] = data;
            }
        } else {
            //如果一个也没选就导出全部
            // data = $('#' + tableId).XGrid('getRowData');
            data = '';
        }
        if(tableId == "X_Table"){
        	utils.exportAstrictHandle('X_Table', len, 1).then( () => {
                return false;
            }).catch( () => {
            	httpPost("/proxy-gsp/gsp/checkPlan/excelHistory", formData);
            });            
        }if(tableId == "checkPlanInfo_Table"){
        	utils.exportAstrictHandle('checkPlanInfo_Table', len, 1).then( () => {
                return false;
            }).catch( () => {
            	httpPost("/proxy-gsp/gsp/checkPlan/excelHistoryInfo", formData);
            });             
        }
    });
};
    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
}



/*
function btn_output(){

    window.location = '/checkPlan/excelHistory?orgCode='+$("#orgValue").val()+'&startTime='+$("#beginTime").val()+'&endTime='+$("#endTime").val()+'&checkType='+$("#checkType").val()


}*/
