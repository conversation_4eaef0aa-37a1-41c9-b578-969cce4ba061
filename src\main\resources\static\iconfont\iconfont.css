
@font-face {font-family: "font_family";
  src: url('iconfont.eot?t=1534130423901'); /* IE9*/
  src: url('iconfont.eot?t=1534130423901#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff'),
  url('iconfont.ttf?t=1534130423901') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('iconfont.svg?t=1534130423901#font_family') format('svg'); /* iOS 4.1- */
}

.font_family {
  font-family:"font_family" !important;
  font-size:16px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-CombinedShape:before { content: "\e60e"; }

.icon-CombinedShape-:before { content: "\e60f"; }

.icon-Group-:before { content: "\e605"; }

