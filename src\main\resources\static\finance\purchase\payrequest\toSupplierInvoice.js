$(function () {
    var totalTable = z_utils.totalTable;

    $('.fold-block[fold=sub]').fold();

    var colNames = ['ERP采购发票号', '供应商发票号','发票状态', '发票税额', '发票含税金额', '创建日期', '供应商发票日期','过账日期','供应商编号','供应商名称','备注'],
        colModel = [{
            name: 'invoiceNo'

        }, {
            name: 'supplierInvoiceNumber',
            formatter: function (val) {
                return '<span class="hover-title" title="'+ val +'">'+val+'</span>';
            }
        }, {
            name: 'invoiceStatusName'

        }, {
            name: 'totalInvoiceValue',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }

        }, {
            name: 'totalInvoiceTax',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }

        }, {
            name: 'createTime',
            formatter:function(value){
                return moment(value).format("YYYY-MM-DD");
            }

        }, {
            name: 'invoiceCreateDate',
            formatter:function(value){
                return moment(value).format("YYYY-MM-DD");
            }


        }, {
            name: 'outDate',
            formatter:function(value){
                return moment(value).format("YYYY-MM-DD");
            }


        }, {
            name: 'supplierNumber'

        }, {
            name: 'supplierName'

        },{
            name: 'rates'

        }];

    var transferiemCheckbox = $("#transferiemCheckbox").prop('checked');
    console.log("transferiemCheckbox="+ transferiemCheckbox);
    var starttime = null;
    var endtime = null;
    var invoiceStatus = null;
    var transferitemStarttime = null;
    var transferitemsEndtime = null;
    if(transferiemCheckbox) {
        transferitemStarttime = $("#transferitemStarttime").val();
        transferitemsEndtime = $("#transferitemsEndtime").val();
    } else {
        starttime = $("#starttime").val();
        endtime = $("#endtime").val();
        invoiceStatus = $("#invoiceStatus").val();
    }
    //设置table高度
    //utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierInvoiceReport',
        postData: {
            "keyWord": $("#keyWord").val(),
            "startTime": starttime,
            "endTime": endtime,
            "invoiceStatus": invoiceStatus,
            "invoiceNo": $("#invoiceNo").val(),
            "supplierInvoiceNumber": $("#supplierInvoiceNumber").val(),
            "invoiceType": $("#invoiceType").val(),
            "transferitemStartTime" : transferitemStarttime,
            "transferitemsEndTime" : transferitemsEndtime
        },
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,//是否展示序号
        altRows: true, //设置为交替行表格,默认为false
        mutltiselect: true,
        attachRow:true,
        maxheight:false,
        ondblClickRow: function (rowid, usedata) {
            var rowData = $("#X_Table").XGrid('getRowData', rowid);
            $.extend(rowData, {
                id: rowid
            });
            // dialog.close(rowData);
            // dialog.remove();
        },
        onSelectRow: function (e, c, a, b) {
            //console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['totalInvoiceValue','totalInvoiceTax'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                // lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
        },
        pager: '#grid-pager'
    });

    //统计
    totalSum();

    // 查询数据
    $('#searchBtn').bind('click', function () {
        var transferiemCheckbox = $("#transferiemCheckbox").prop('checked');
        console.log("transferiemCheckbox="+ transferiemCheckbox);
        var starttime = null;
        var endtime = null;
        var invoiceStatus = null;
        var transferitemStarttime = null;
        var transferitemsEndtime = null;
        if(transferiemCheckbox) {
            transferitemStarttime = $("#transferitemStarttime").val();
            transferitemsEndtime = $("#transferitemsEndtime").val();
        } else {
            starttime = $("#starttime").val();
            endtime = $("#endtime").val();
            invoiceStatus = $("#invoiceStatus").val();
        }
        $('#X_Table').setGridParam({
            url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierInvoiceReport',
            postData: {
                "keyWord": $("#keyWord").val(),
                "startTime": starttime,
                "endTime": endtime,
                "invoiceStatus": invoiceStatus,
                "invoiceNo": $("#invoiceNo").val(),
                "supplierInvoiceNumber": $("#supplierInvoiceNumber").val(),
                "invoiceType": $("#invoiceType").val(),
                "transferitemStartTime" : transferitemStarttime,
                "transferitemsEndTime" : transferitemsEndtime
            }
        }).trigger('reloadGrid');
        //统计
        totalSum();
    })

    // 导出
    // $('#exportBtn').bind('click', function () {
    //
    //     LayTool.confirm("确认是否需要导出", {icon: 1, title: '提示'}
    //         , function (index) {
    //             top.layer.close(index);
    //             top.layer.msg('Excel数据处理中，请稍后...', {
    //                 icon: 16,
    //                 shade:[0.1, '#fff'],
    //                 time:false
    //             });
    //
    //             //验证导出数据
    //             $.ajax({
    //                 url : "/proxy-finance/finance/purchase/AccountPayableReports/checkExportInvoiceInfoReport",
    //                 data:{ keyWord : $("#keyWord").val(),
    //                     starttime : $("#starttime").val(),
    //                     endtime : $("#endtime").val(),
    //                     invoiceStatus : $("#invoiceStatus").val(),
    //                     invoiceNo : $("#invoiceNo").val(),
    //                     supplierInvoiceNumber : $("#supplierInvoiceNumber").val()},
    //                 type: "post",
    //                 success:function(result){
    //                     //校验成功
    //                     if(result.code==0){
    //                         console.log(result.result);
    //                         //window.location.href =result.result;
    //                         openWin(result.result);
    //                     }else{
    //                         top.layer.closeAll();
    //                         top.layer.msg("导出excel发生错误");
    //                     }
    //                 },
    //                 error : function(jqXHR, textStatus, errorThrown) {
    //                     top.layer.closeAll();
    //                     top.layer.msg("导出excel发生错误");
    //                 }
    //             })
    //
    //
    //         });
    // });

    // 导出
    $('#exportBtn').bind('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            var transferiemCheckbox = $("#transferiemCheckbox").prop('checked');
            console.log("transferiemCheckbox="+ transferiemCheckbox);
            var starttime = null;
            var endtime = null;
            var invoiceStatus = null;
            var transferitemStarttime = null;
            var transferitemsEndtime = null;
            if(transferiemCheckbox) {
                transferitemStarttime = $("#transferitemStarttime").val();
                transferitemsEndtime = $("#transferitemsEndtime").val();
            } else {
                starttime = $("#starttime").val();
                endtime = $("#endtime").val();
                invoiceStatus = $("#invoiceStatus").val();
            }
            parent.showLoading({hideTime: 999999999});
            var obj = {
                keyWord : $("#keyWord").val(),
                startTime : starttime,
                endTime : endtime,
                invoiceStatus : invoiceStatus,
                invoiceNo : $("#invoiceNo").val(),
                supplierInvoiceNumber : $("#supplierInvoiceNumber").val(),
                invoiceType : $("#invoiceType").val(),
                transferitemStartTime : transferitemStarttime,
                transferitemsEndTime : transferitemsEndtime
            };
            httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportSupplierInvoiceReport", obj);
            parent.hideLoading();
            //验证导出数据
            // $.ajax({
            //     url : "/proxy-finance/finance/purchase/payrequestinfo/exportSupplierInvoiceReport",
            //     data:{
            //         keyWord : $("#keyWord").val(),
            //         startTime : starttime,
            //         endTime : endtime,
            //         invoiceStatus : invoiceStatus,
            //         invoiceNo : $("#invoiceNo").val(),
            //         supplierInvoiceNumber : $("#supplierInvoiceNumber").val(),
            //         invoiceType : $("#invoiceType").val(),
            //         transferitemStartTime : transferitemStarttime,
            //         transferitemsEndTime : transferitemsEndtime
            //     },
            //     type: "post",
            //     success:function(result){
            //         //校验成功
            //         if(result.code==0){
            //             console.log(result.result);
            //             var url = result.result.filePath;
            //             var extfilename = result.result.extfilename;
            //             var parames = [];
            //             parames.push({ name: "filePath", value: url});
            //             parames.push({ name: "extfilename", value: extfilename});
            //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
            //             parent.hideLoading();
            //         }else{
            //             parent.hideLoading();
            //             utils.dialog({content:result.msg, quickClose: true,
            //                 timeout: 3000}).showModal();
            //
            //         }
            //     }
            // })
        })
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }


    // //导出，不验证
    // $('#exportBtn').bind('click', function () {
    //                 var parames = [];
    //                 parames.push({ name: "keyWord", value: $("#keyWord").val()});
    //                 parames.push({ name: "starttime", value: $("#starttime").val()});
    //                 parames.push({ name: "endtime", value: $("#endtime").val()});
    //                 parames.push({ name: "invoiceStatus", value: $("#invoiceStatus").val()});
    //                 parames.push({ name: "invoiceNo", value: $("#invoiceNo").val()});
    //                 parames.push({ name: "supplierInvoiceNumber", value: $("#supplierInvoiceNumber").val()});
    //                 Post("/proxy-finance/finance/purchase/AccountPayableReports/exportInvoiceInfoReport", parames);
    // });


    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }
    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/invoice/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result);
            $("#keyWord").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);;
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {

            $("#keyWord").val("");
            $("#supplierName").val("");

        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    // 按照过账日期查询
    $('#transferiemCheckbox').change(function () {
        $('.transferitemsWrap').css('display',$(this).prop('checked')?'block':'none');
        $('#starttime, #endtime, #invoiceStatus').prop('disabled', $(this).prop('checked')?true:false)
    });

    //放大镜查询
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });
    //供应商双击查询
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#keyWord").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }



    $("#btn_export").click(function () {
        LayTool.confirm("确认是否需要导出", {icon: 1, title: '提示'}
            , function (index) {
                // var startCreateTime = $("#txt_search_startCreateTime").val();
                // var endCreateTime = $("#txt_search_endCreateTime").val();
                // if (startCreateTime) {
                //     var dateDiff = ToolUtil.differentDays(startCreateTime, endCreateTime, 1);
                //     if (dateDiff > 31) {
                //         LayTool.alert("可能造成服务器资源紧张,筛选时间段受限31天");
                //         return ;
                //     }
                // }
                top.layer.close(index);
                top.layer.msg('Excel数据处理中，请稍后...', {
                    icon: 16,
                    shade:[0.1, '#fff'],
                    time:false
                });


            });
    });

    //统计方法
    function totalSum() {
        var transferiemCheckbox = $("#transferiemCheckbox").prop('checked');
        console.log("transferiemCheckbox="+ transferiemCheckbox);
        var starttime = null;
        var endtime = null;
        var invoiceStatus = null;
        var transferitemStarttime = null;
        var transferitemsEndtime = null;
        if(transferiemCheckbox) {
            transferitemStarttime = $("#transferitemStarttime").val();
            transferitemsEndtime = $("#transferitemsEndtime").val();
        } else {
            starttime = $("#starttime").val();
            endtime = $("#endtime").val();
            invoiceStatus = $("#invoiceStatus").val();
        }
        $.ajax({
            url:'/proxy-finance/finance/purchase/payrequestinfo/selectSupplierInvoiceTotal',
            type:'post',
            data:{
                keyWord : $("#keyWord").val(),
                startTime : starttime,
                endTime : endtime,
                invoiceStatus : invoiceStatus,
                invoiceNo : $("#invoiceNo").val(),
                supplierInvoiceNumber : $("#supplierInvoiceNumber").val(),
                invoiceType : $("#invoiceType").val(),
                transferitemStartTime : transferitemStarttime,
                transferitemsEndTime : transferitemsEndtime
            },
            dataType:'json',
            success:function (result) {
                if(result.code == 0){
                    var static = result.result;
                    // $("#totalNoTaxAmount").text(Number(static.totalNoTaxAmount).toFixed(2));
                    $("#totalTax").text(parseFloat(static.totalTax).formatMoney('2', '', ',', '.'));
                    $("#totalAmount").text(parseFloat(static.totalAmount).formatMoney('2', '', ',', '.'));
                    // $("#totalTaxDifference").text(Number(static.totalTaxDifference).toFixed(2));
                }
            }
        })
    }

})
