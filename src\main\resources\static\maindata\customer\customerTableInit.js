var idArr=[];//存放经营范围已选中id
var _socreTable=false; //特殊需求首营变更专用
//批准文件
function initTable3(supplierBaseId,type,$table){
    idArr=[];
    $.ajax({
        url:'/proxy-customer/customer/customerCommonData/getCustomerApprovalFileAll',
        data:{"correlationId":supplierBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            // console.log(3)
            // console.log(data);
            handleData(data,$table);
        }
    });
}
function handleData(data,$table,scoreTable) {
    _socreTable=scoreTable?true:false
    var xGridData=[];
    if($table){
        window.idArr_=[];
    }
    if(data.result.list!=null&& data.result.list.length>0){
        var dataList = data.result.list;
        for(var i=0;i<dataList.length;i++){
            /**
             * 树形，重点
             */
            var zTreeNodes=[];
            if(dataList[i].scopeofoperationVo&&dataList[i].scopeofoperationVo!="null"&&dataList[i].scopeofoperationVo.length>0&&dataList[i].scopeofoperationVo!="[]"){
                zTreeNodes = [{
                    id: 0,
                    name: "经营范围",
                    // open: true,//展开
                    children:[]
                }]
            }
            /**
             * RM 2018-09-25
             * 客户管理 客户变更详情 资料变更  批准文件经营范围 修改后 查看时 表格中树形 回显
             * 因为 保存数据时， 属性名好多都不统一， 不想去修改前面的，万一出状况。所有 又写了这么一个if代码块。
             */
            if(dataList[i].scopeofoperationVo && dataList[i].scopeofoperationVo.length > 0){
                var scopeofoperationVo = dataList[i].scopeofoperationVo;
                //  console.log(scopeofoperationVo)
                /**
                 * 树形，重点
                 */
                scopeofoperationVo = typeof scopeofoperationVo=="string"? JSON.parse(scopeofoperationVo):scopeofoperationVo;
                $(scopeofoperationVo).each(function(index,item){
                    var _obj = {};
                    _obj.id = item.scopeId;
                    _obj.name = item.scopeName;
                    _obj.children = [];
                    $(item.children).each(function(cindex,citem){
                        var _obj_child = {};
                        _obj_child.id = citem.scopeId;
                        _obj_child.name = citem.scopeName;
                        _obj.children.push(_obj_child)
                    })
                    zTreeNodes[0].children.push(_obj)
                })
            }

            if (zTreeNodes){
                function getNodeData(n){
                    if (n && n.children){
                        let el = n.children;
                        if(el.length > 0){
                            for(let i = 0; i<el.length; i++){
                                if(el[i].children && el[i].children.length > 0){
                                    getNodeData(el[i])
                                }else{
                                    el[i].isParent = false
                                }
                            }
                        }
                    }
                }
                getNodeData(zTreeNodes[0]);
            }
            let certJson={};
            certJson[dataList[i].credentialTypeId]=dataList[i].customerBusinessScopeVoList;
            //idArr.push(certJson);
            /**
             * 树形，重点
             */
            if($table){
                idArr_.push(certJson);
            }else{
                idArr.push(certJson);
            }
            // console.log(dataList[i].credentialTypeId)
            var a={
                id: dataList[i].id,
                changeStatus: dataList[i].changeStatus,
                credentialTypeId: dataList[i].credentialTypeId,
                credentialCode:dataList[i].credentialCode,
                customerBusinessScopeVoList: zTreeNodes,
                certificationOffice: dataList[i].certificationOffice,
                openingDate: dataList[i].openingDate,
                validUntil: dataList[i].validUntil,
                enclosureCount: dataList[i].enclosureCount,
                customerEnclosureVoList:typeof (dataList[i].customerEnclosureVoList)=="string"?JSON.parse(dataList[i].customerEnclosureVoList):dataList[i].customerEnclosureVoList,
                /**
                 * 树形，重点
                 */
                scopeofoperationVo:typeof(dataList[i].scopeofoperationVo)=="string"?dataList[i].scopeofoperationVo:JSON.stringify(dataList[i].scopeofoperationVo)
            }
            xGridData.push(a);
        }
    }
    if(xGridData.length<1){
        xGridData={
            id: 1,
            credentialTypeId: "",
            credentialCode:"",
            customerBusinessScopeVoList: [],
            certificationOffice: "",
            openingDate: "",
            validUntil: "",
            enclosureCount: "",
            customerEnclosureVoList:[]
        }
    }
    xGridTable3(xGridData,$table);
    /**
     * RM 2018-10-07
     * 客户资料变更 编辑查看 树形禁用
     */
    setTimeout(function () {setDisabledZtree(true,$('#changeApplyTable'))},500)
}
function xGridTable3(xGridData,$table) {
    $table=$table|| $('#table3');
    /**
     * RM 2018-10-11
     * 重新引入供应商名称之后，需要清空批准文件表格，否则，上一条供应商的附件还会保存在表格中，
     * 当新数据加载完后，虽然附件数量为无，但是实际表格隐藏域中可能存在数据，，
     * 然后上传附件的时候附件个数和变革中显示的数量对应不上
     */
    $table.XGrid('clearGridData');
    $table.XGrid('setGridParam',{data:xGridData}).trigger('reloadGrid');
}
let isChangshaStorage = ($('[name=orgCode]').val() == '007');
console.log('isChangshaStorage  ' + isChangshaStorage);
let table3ColNames = null, table3ColModel = null;
if(false){ //  长沙仓的 产品 又要放开了  2019-11-21
    $('#pzwjUplode').css('display','none')
    table3ColNames = ['', '<i class="i-red">*</i>证书类型', '<i class="i-red">*</i>证书编号', '<i class="i-red">*</i>经营范围',
        '<i class="i-red">*</i>发证日期', '<i class="i-red">*</i>有效期至'];
    table3ColModel = [
        {
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true, //与反回的json数据中key值对应
        },
        {
            name: 'credentialTypeId',
            rowtype: '#credentialTypeId',
            rowEvent: function (etype, c, d) {
                var credentialId=etype.rowData.credentialTypeId;
                // console.log(credentialId,etype.rowData.id)
                getZtreeData(credentialId,etype.rowData.id);
            }
        },
        {
            name: 'credentialCode',
            rowtype: '#credentialCode'
        },
        {
            name: 'customerBusinessScopeVoList',
            rowtype: '#operScopeZtree',
            rowEvent: function (etype) {
                initBaseDataBuseScope();//初始化经营范围内容
            }
        },
        {
            name: 'openingDate',
            rowtype: '#fcertDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        },
        {
            name: 'validUntil',
            rowtype: '#zcertDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        },
        /**
         * 树形，重点
         */
        {
            name: 'scopeofoperationVo',
            hidden:true,
        },
        {
            name:"changeStatus",
            hidden:true
        }
    ]
}else{
    table3ColNames = ['', '<i class="i-red">*</i>证书类型', '<i class="i-red">*</i>证书编号', '<i class="i-red">*</i>经营范围',
        '<i class="i-red">*</i>发证日期', '<i class="i-red">*</i>有效期至', '附件','附件数据','操作'];
    table3ColModel = [{
        name: 'id', //与反回的json数据中key值对应
        index: 'id', //索引。其和后台交互的参数为sidx
        key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        hidden: true, //与反回的json数据中key值对应
    }, {
        name: 'credentialTypeId',
        rowtype: '#credentialTypeId',
        rowEvent: function (etype, c, d) {
            var credentialId=etype.rowData.credentialTypeId;
            // console.log(credentialId,etype.rowData.id)
            getZtreeData(credentialId,etype.rowData.id);
        }
    }, {
        name: 'credentialCode',
        rowtype: '#credentialCode'
    }, {
        name: 'customerBusinessScopeVoList',
        rowtype: '#operScopeZtree',
        rowEvent: function (etype) {
            initBaseDataBuseScope();//初始化经营范围内容
        }
    }, {
        name: 'openingDate',
        rowtype: '#fcertDate',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'validUntil',
        rowtype: '#zcertDate',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'enclosureCount',
        formatter:function (value) {
            var str='无';
            if(value)
            {
                var tableId = "#table3";
                str ='<a href="javascript:;" onclick="showImg(this,\''+tableId+'\');">'+value+'</a>';

            }
            return str;
        },
        unformat: function (e) {
            e=e.replace(/<[^>]+>/g,'');
            if(e == '无'){
                e = 0;
            }
            return e;
        }
    }, {
        name: 'customerEnclosureVoList',
        hidden:true,
        formatter:function (value) {
            if(value){
                return typeof(value)=="string"?value:JSON.stringify(value);
            }
            return JSON.stringify([]);
        }
    }, {
        name:"UploadeImg",
        formatter:function (val,objs,data) {
            console.log(val,objs,data);
            var tableId = "#table3";
            return   '<button  type="button" class="btn btn-info ApprovalUploadeImg" data-id="'+data.id+'" disabled="disabled" onclick="clickAddImgUploaderImg(this)">上传附件</button>';
        }
    },
        /**
         * 树形，重点
         */
        {
            name: 'scopeofoperationVo',
            hidden:true,
        },
        {
            name:"changeStatus",
            hidden:true
        }
    ]
}
$('#table3').XGrid({
    data: [],
    colNames: table3ColNames,
    colModel: table3ColModel,
    rownumbers: true,
    /*rowNum: 10,
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_page3',*/
    gridComplete: function () {
        /**
         * 树形，重点
         */
        var $this=$(this);
        const rowDatas = $this.getRowData()
        if(rowDatas.length<=0) return;
        setTimeout(function () {
            var $this_tr=$this.find('tr');
            //$("#table3 tr").each(function (index) {
            $this_tr.each(function (index,value) {
                if(index != 0){
                    const rowData = rowDatas[index-1]
                    // 若 changeStatus 等于 1，表示该行有修改，将其背景颜色改为淡红色
                    // 仅在客户资质变更页面的批准文件变更数据中，对变更项进行特殊标识。
                    if ($('#customerApplVo') && $this.attr('id') === 'changeApplyTable' && rowData.changeStatus && rowData.changeStatus == 1){
                        $(this).css('background','rgb(253, 229, 229)')
                    }
                    var credentialId=$(this).find("td[row-describedby='credentialTypeId'] select option:selected").val();
                    var zTreeId=$(this).find(".operScopeZtree").attr("id");
                    var zTree = $.fn.zTree.getZTreeObj(zTreeId);
                    var changeAry=idArr;
                    //如果为查阅变更，则赋对应的变更数据
                    if($(this).closest('table').prop('id')=='changeApplyTable'||_socreTable){
                        changeAry=idArr_;
                    }
                    for(var i=0;i<changeAry.length;i++){
                        var item=changeAry[i];
                        for(var n in item){
                            if(n == credentialId){
                                var aList=item[n];
                                if(aList && aList.length > 0){
                                    for(var s=0;s<aList.length;s++){
                                        var treeNode=zTree.getNodeByParam( "id",Number(aList[s].businessScopeCode || aList[s].id));
                                        if(treeNode)
                                            zTree.checkNode(treeNode, true );
                                        if($(this).closest('table').prop('id')!='changeApplyTable'){
                                            initBaseDataBuseScope();
                                        }else{
                                            setTimeout(function(){setDisabledZtree(true,$('#changeApplyTable'))});
                                        }
                                    }
                                    if(zTree.getNodes()&&zTree.getNodes().length>0&&zTree.getNodes()[0].children.length >0){
                                        if(zTree.getNodes()[0].children[0].children.length > 0){
                                            if(zTree.getNodes()[0].children[0].children.length == aList.length){
                                                zTree.checkAllNodes(true);
                                            }
                                        }else{
                                            if(zTree.getNodes()&&zTree.getNodes().length>0&&zTree.getNodes()[0].children.length == aList.length){
                                                zTree.checkAllNodes(true);
                                            }
                                        }
                                        // if(zTree.getNodes()[0].children[0].children.length == aList.length || zTree.getNodes()[0].children.length == aList.length){
                                        //     zTree.checkAllNodes(true);
                                        // }else{
                                        //
                                        // }
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            })
            _socreTable=false;
            //设置不可更改 0不可编辑 1可编辑
            if($('#isEdit').val() == '0') {
                setDisabledZtree(true);
            }
            // $this.find('[row-describedby="supplierApprovalFileBusinessScopeVOList"] span.node_name').filter(function (i, v) {
            //     if ($(v).html() == 'undefined') {
            //         $(v).closest('li').remove()
            //     }
            // })
        },500)
    }
});
function clickAddImgUploaderImg(that){
    var rowData;
    var typeList=[];
    var eChoImgList=[];
    var $table=$("#table3");
    var thisID=$(that).data("id");
    rowData=$("#table3").getRowData(thisID);
    if(rowData&&rowData.customerEnclosureVoList&&rowData.customerEnclosureVoList!="null"&&rowData.customerEnclosureVoList.length>0){
        rowData.customerEnclosureVoList=JSON.parse(rowData.customerEnclosureVoList);
        if(rowData.customerEnclosureVoList.length>0){
            for(var i=0;i<rowData.customerEnclosureVoList.length;i++){
                rowData.customerEnclosureVoList[i].type=$(that).parent().parent().find("select[name=credentialTypeId] option:selected").val();
                rowData.customerEnclosureVoList[i].lineNum =$(that).data("id");
            }
        }
        typeList.push({
            text: $(that).parent().parent().find("select[name=credentialTypeId] option:selected").text(),
            value:$(that).parent().parent().find("select[name=credentialTypeId] option:selected").val(),
            lineNum: $(that).data("id")
        });
        $(that).parent().parent().find("select[name=credentialTypeId] option:selected").text();
        eChoImgList = eChoImgList.concat(rowData.customerEnclosureVoList);
        $(that).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                // console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id&&id==$(that).data("id")){
                            $table.setRowData(id,{'customerEnclosureVoList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                var l = [];
                for(let i = 0; i<data.length; i++){
                    var list = data[i];
                    //data.splice()
                    if(rowData.credentialTypeId == data[i].type && $(that).data("id")== data[i].lineNum){
                        l.push(list);
                    }
                }
                console.log(l);
                var trId=thisID;
                $table.setRowData(trId,{'customerEnclosureVoList':l});
                $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
            }
        });
    }
}
// 批准文件 类型 重复选择校验
$("#table3").on("mousedown",'select[name="credentialTypeId"]',function () {
    /**
     * RM 2018-10-11
     * 批准文件 证书类型 选中的不再被禁用
     */
    //selCannotRepeatChoice(this,'table3','credentialTypeId');
})
//批量上传  批准文件
$("#pzwjUplode").on("click", function () {
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$('#table3');
    var rowData=$table.getRowData();
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++){
        var sel=$tr.eq(i).find("select[name='credentialTypeId'] option:selected");
        typeList.push({
            text:sel.text(),
            value:sel.val(),
            lineNum: i
        });
        // console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0){
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++){
                rowData[i].customerEnclosureVoList[j].type=sel.val();
                rowData[i].customerEnclosureVoList[j].lineNum =i;
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    $(this).upLoad({
        typeList:typeList,//格式[{text:xxx,value:xxx}]
        eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
        fileParam:{
            name:'enclosureName',
            url:'url'
        },
        urlBack: function (data) {
            //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
            // console.log(data)
            if($.isEmptyObject(data)){
                $table.find("tr").each(function () {
                    var id=$(this).attr('id');
                    if(id){
                        $table.setRowData(id,{'customerEnclosureVoList':[]});
                        $table.setRowData(id,{'enclosureCount':''});
                    }
                })
                return false;
            }
            var listArr = [];
            for(let j=0;j<rowData.length;j++){
                var l = [];
                for(let i = 0; i<data.length; i++){
                    var list = data[i];
                    //data.splice()
                    if(rowData[j].credentialTypeId == data[i].type && j == data[i].lineNum){
                        l.push(list);
                    }
                }
                var trId=$table.find("tr").eq(j+1).attr('id');
                $table.setRowData(trId,{'customerEnclosureVoList':l});
                $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
            }
        }
    });
});
//质量保证协议
function initTable1(supplierBaseId,type,$table){
    var xGridData=[];
    $.ajax({
        url:'/proxy-customer/customer/customerCommonData/getCustomerQualityAgreementAll',
        data:{"correlationId":supplierBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            xGridData = data.result.list;
            if(xGridData.length<1){
                xGridData={
                    id: "",
                    signedDate: "",
                    validDate:"",
                    signer: "",
                    enclosureCount: "",
                    customerEnclosureVoList:[]
                }
            }
            xGridTable1(xGridData,$table);
        }
    });
}
function xGridTable1(xGridData,$table){
    $table=$table|| $('#table1');
    $table.XGrid('setGridParam',{data:xGridData}).trigger('reloadGrid');
}
$('#table1').XGrid({
    data: [],
    colNames: ['', '<i class="i-red">*</i>签订日期', '<i class="i-red">*</i>有效期至', '<i class="i-red">*</i>签订人', '附件','附件数据'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        index: 'id', //索引。其和后台交互的参数为sidx
        key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        hidden: true
    }, {
        name: 'signedDate',
        index: 'signedDate',
        rowtype: '#signedDate',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'validDate',
        index: 'validDate',
        rowtype: '#validDate',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'signer',
        index: 'signer',
        rowtype: '#signer'
    }, {
        name: 'enclosureCount',
        index: 'enclosureCount',
        formatter:function (value) {
            var str='无';
            if(value)
            {
                var tableId = "#table1"
                str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
            }
            return str;
        },
        unformat: function (e) {
            e=e.replace(/<[^>]+>/g,'');
            if(e == '无'){
                e = 0;
            }
            return e;
        }
    },{
        name:'customerEnclosureVoList',
        index:'customerEnclosureVoList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return typeof(value)=="string"?value:JSON.stringify(value);
            }
            return JSON.stringify([]);
        }
    },        {
        name:"changeStatus",
        hidden:true
    }
    ],
    altRows: true,
    rownumbers: true/*, //设置为交替行表格,默认为false
         ondblClickRow: function (id,dom,obj,index,event) {
         console.log('双击行事件', id,dom,obj,index,event);
         },
         onSelectRow: function (id,dom,obj,index,event) {
         console.log('单机行事件',id,dom,obj,index,event);
         },*/
    //pager:'#table1_page'
});
//批量上传 质量保证协议
$("#zlbzUpload").on("click", function () {
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$('#table1');
    var rowData=$table.getRowData();
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++)
    {
        var sel=$tr.eq(i).find("input[name='signer']");
        typeList.push({
            text:sel.val(),
            value:sel.val()
        });
        // console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0)
        {
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
            {
                rowData[i].customerEnclosureVoList[j].type=sel.val();
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    $(this).upLoad({
        typeList:typeList,//格式[{text:xxx,value:xxx}]
        eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
        fileParam:{
            name:'enclosureName',
            url:'url'
        },
        urlBack: function (data) {
            //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
            //console.log(data)
            if($.isEmptyObject(data)){
                $table.find("tr").each(function () {
                    var id=$(this).attr('id');
                    if(id){
                        $table.setRowData(id,{'customerEnclosureVoList':[]});
                        $table.setRowData(id,{'enclosureCount':''});
                    }
                })
                return false;
            }
            var listArr = [];
            for(let j=0;j<rowData.length;j++){
                var l = [];
                for(let i = 0; i<data.length; i++){
                    var list = data[i];
                    //data.splice()
                    if(rowData[j].signer == data[i].type){
                        l.push(list);
                    }
                }
                var trId=$table.find("tr").eq(j+1).attr('id');
                $table.setRowData(trId,{'customerEnclosureVoList':l});
                $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
            }
        }
    });
});
//客户委托书
function initTable2(supplierBaseId,type,$table){
    var xGridData=[];
    $.ajax({
        url:'/proxy-customer/customer/customerCommonData/getCustomerDelegationFileAll',
        data:{"correlationId":supplierBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            xGridData = data.result.list;
            if(xGridData.length<1){
                xGridData={
                    id: "",
                    mandatorType: "",
                    delegationCredentialCode: "",
                    delegationName:"",
                    delegationSex: "",
                    delegationTel: "",
                    delegationNum: "",
                    delegationAddr: "",
                    delegationIdentityDate: "",
                    validityDelegationCredential:"",
                    authorityZone:"",
                    authorityScope:"",
                    enclosureCount:"",
                    customerEnclosureVoList:[],
                }
            }
            xGridTable2(xGridData,$table);
        }
    });
}
function xGridTable2(xGridData,$table){
    $table=$table|| $('#table2');
    $table.XGrid('setGridParam',{data:xGridData}).trigger('reloadGrid');
}
$('#table2').XGrid({
    data: [],
    colNames: ['', '<i class="i-red">*</i>委托人类型', '<i class="i-red">*</i>姓名',  '<i class="i-red">*</i>电话',
        '<i class="i-red">*</i>委托书有效期至', '附件','附件数据'
    ],
    // colNames: ['', '<i class="i-red">*</i>委托人类型', '<i class="i-red">*</i>姓名', '<i class="i-red">*</i>性别', '<i class="i-red">*</i>电话', '<i class="i-red">*</i>证件号码',
    //     '<i class="i-red">*</i>身份证有效期至', '<i class="i-red">*</i>委托书有效期至', '附件','附件数据'
    // ],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        index: 'id', //索引。其和后台交互的参数为sidx
        key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        hidden: true
    }, {
        name: 'mandatorType',
        index: 'mandatorType',
        rowtype: '#clientTypeId',
        formatter:function(value){
            if(!value)return false;
            var valueName = loadMandatorType(value);
            return valueName;
        }
    },  {
        name: 'delegationName',
        index: 'delegationName',
        rowtype: '#delegationName'
    }
    //     {
    //     name: 'delegationSex',
    //     index: 'delegationSex',
    //     rowtype: '#entrustSex'
    // }
    , {
        name: 'delegationTel',
        index: 'delegationTel',
        rowtype: '#delegationTel'
    },
    //     {
    //     name: 'delegationNum',
    //     index: 'delegationNum',
    //     rowtype: '#delegationNum'
    // },{
    //     name: 'delegationIdentityDate',
    //     index: 'delegationIdentityDate',
    //     rowtype: '#delegationIdentityDate',
    //     formatter:function(value){
    //         var date=value;
    //         if(!value)return false;
    //         date=format(value);
    //         return date.split(' ')[0];
    //     }
    // },
        {
        name: 'validityDelegationCredential',
        index: 'validityDelegationCredential',
        rowtype: '#validityDelegationCredential',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'enclosureCount',
        index: 'enclosureCount',
        formatter:function (value) {
            var str='无';
            if(value)
            {
                var tableId = "#table2";
                str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
            }
            return str;
        },
        unformat: function (e) {
            e=e.replace(/<[^>]+>/g,'');
            if(e == '无'){
                e = 0;
            }
            return e;
        }
    },{
        name:'customerEnclosureVoList',
        index:'customerEnclosureVoList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return typeof(value)=="string"?value:JSON.stringify(value);
            }
            return JSON.stringify([]);
        }
    },
        {
            name:"changeStatus",
            hidden:true
        }],
    altRows: true,
    rownumbers: true/*, //设置为交替行表格,默认为false
         ondblClickRow: function (id,dom,obj,index,event) {
         console.log('双击行事件', id,dom,obj,index,event);
         },
         onSelectRow: function (id,dom,obj,index,event) {
         console.log('单机行事件', id,dom,obj,index,event);
         },*/
    //pager:'#table2_page'
});
//批量上传 客户委托书
$("#knwtUplode").on("click", function () {
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$('#table2');
    var rowData=$table.getRowData();
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++)
    {
        var sel=$tr.eq(i).find("input[name='delegationName']");
        typeList.push({
            text:sel.val(),
            value:sel.val()
        });
        //console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0)
        {
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
            {
                rowData[i].customerEnclosureVoList[j].type=sel.val();
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    $(this).upLoad({
        typeList:typeList,//格式[{text:xxx,value:xxx}]
        eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
        fileParam:{
            name:'enclosureName',
            url:'url'
        },
        urlBack: function (data) {
            //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
            //console.log(data)
            if($.isEmptyObject(data)){
                $table.find("tr").each(function () {
                    var id=$(this).attr('id');
                    if(id){
                        $table.setRowData(id,{'customerEnclosureVoList':[]});
                        $table.setRowData(id,{'enclosureCount':''});
                    }
                })
                return false;
            }
            /**
             * RM 2018-09-29
             */
            var listArr = [];
            for(let j=0;j<rowData.length;j++){
                var l = [];
                for(let i = 0; i<data.length; i++){
                    var list = data[i];
                    //data.splice()
                    if(rowData[j].delegationName == data[i].type){
                        l.push(list);
                    }
                }
                var trId=$table.find("tr").eq(j+1).attr('id');
                $table.setRowData(trId,{'customerEnclosureVoList':l});
                $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
            }

        }
    });
});

//被委托人身份证
function initCertTable(supplierBaseId,type,$table){
    var xGridData=[];
    $.ajax({
        url:'/proxy-customer/customer/customerCommonData/getCustomerBeProxyIdentityAll',
        data:{"correlationId":supplierBaseId,"type":type},
        type:"post",
        dataType:'json',
        success:function(data){
            xGridData = data.result.list;
            if(xGridData.length<1){
                xGridData={
                    id: "",
                    customerName: "",
                    identityNumber:"",
                    identityValidityDate: "",
                    enclosureCount: "",
                    customerEnclosureVoList: [],
                }
            }
            xGridCertTable(xGridData,$table);
        }
    });
}
function xGridCertTable(xGridData,$table){
    $table=$table|| $('#certTable');
    $table.XGrid('setGridParam',{data:xGridData}).trigger('reloadGrid');
}
$('#certTable').XGrid({
    data:[],
    colNames: ['', '<i class="i-red">*</i>姓名', '<i class="i-red">*</i>证件号码', '<i class="i-red">*</i>身份证有效期至', '附件','附件数据'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        index: 'id', //索引。其和后台交互的参数为sidx
        key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        hidden: true
    }, {
        name: 'customerName',
        index: 'customerName',
        rowtype :'#customerName1'
    }, {
        name: 'identityNumber',
        index: 'identityNumber',
        rowtype :'#identityNumber'
    }, {
        name: 'identityValidityDate',
        index: 'identityValidityDate',
        rowtype :'#identityValidityDate',
        formatter:function(value){
            var date=value;
            if(!value)return false;
            date=format(value);
            return date.split(' ')[0];
        }
    }, {
        name: 'enclosureCount',
        index: 'enclosureCount',
        formatter:function (value) {
            var str='无';
            if(value)
            {
                var tableId = "#certTable";
                str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
            }
            return str;
        },
        unformat: function (e) {
            e=e.replace(/<[^>]+>/g,'');
            if(e == '无'){
                e = 0;
            }
            return e;
        }
    },{
        name:'customerEnclosureVoList',
        index:'customerEnclosureVoList',
        hidden:true,
        formatter:function (value) {
            if(value)
            {
                return typeof(value)=="string"?value:JSON.stringify(value);
            }
            return JSON.stringify([]);
        }
    }, {
            name:"changeStatus",
            hidden:true
        }],
    altRows: true,
    rownumbers: true/*, //设置为交替行表格,默认为false
         ondblClickRow: function (id,dom,obj,index,event) {
         console.log('双击行事件', id,dom,obj,index,event);
         },
         onSelectRow: function (id,dom,obj,index,event) {
         console.log('单机行事件', id,dom,obj,index,event);
         },*/
    //pager:'#certTable_page'
});
//批量上传 委托人身份证
$("#bwtrUplode").on("click", function () {
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$('#certTable');
    var rowData=$table.getRowData();
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++)
    {
        var sel=$tr.eq(i).find("input[name='customerName']");
        typeList.push({
            text:sel.val(),
            value:sel.val()
        });
        //console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0)
        {
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
            {
                rowData[i].customerEnclosureVoList[j].type=sel.val();
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    $(this).upLoad({
        typeList:typeList,//格式[{text:xxx,value:xxx}]
        eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
        fileParam:{
            name:'enclosureName',
            url:'url'
        },
        urlBack: function (data) {
            //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
            //console.log(data)
            if($.isEmptyObject(data)){
                $table.find("tr").each(function () {
                    var id=$(this).attr('id');
                    if(id){
                        $table.setRowData(id,{'customerEnclosureVoList':[]});
                        $table.setRowData(id,{'enclosureCount':''});
                    }
                })
                return false;
            }
            // for(var name in data)
            // {
            //     var list=data[name];
            //     for(var i=0;i<rowData.length;i++)
            //     {
            //         if(rowData[i].customerName == name)
            //         {
            //             var trId=$table.find("tr").eq(i+1).attr('id');
            //             $table.setRowData(trId,{'customerEnclosureVoList':list});//存储时只能存字符串，取值时用JSON.parse转换
            //             $table.setRowData(trId,{'enclosureCount':(list.length == 0?'':list.length)});
            //             break;
            //         }
            //     }
            // }
            var listArr = [];
            for(let j=0;j<rowData.length;j++){
                var l = [];
                for(let i = 0; i<data.length; i++){
                    var list = data[i];
                    //data.splice()
                    if(rowData[j].customerName == data[i].type){
                        l.push(list);
                    }
                }
                var trId=$table.find("tr").eq(j+1).attr('id');
                $table.setRowData(trId,{'customerEnclosureVoList':l});
                $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
            }
        }
    });
});

//客户合同回显
function contract(id,type){
    $.ajax({
        url:'/proxy-customer/customer/customerCommonData/getCustomerContractAll?correlationId='+id+'&type='+type,
        dataType:'json',
        //async:false,
        success:function(data){
            if(!data.result) return false;
            if(data.result.length){
                $(data.result).each(function(contractIndex,ontractItem){
                    var contractTypes = $('#contractType').find('input[type=checkbox]');
                    $(contractTypes).each(function(index,item){
                        if(ontractItem.contractType==$(this).val()){
                            $(this).attr("checked",'true');
                            if(ontractItem.customerEnclosureVoList && ontractItem.customerEnclosureVoList.length > 0){
                                for(var j=0;j<ontractItem.customerEnclosureVoList.length;j++)
                                {
                                    ontractItem.customerEnclosureVoList[j].type = ontractItem.contractType;
                                }
                                var arr=JSON.stringify(ontractItem.customerEnclosureVoList);
                                var html='<input type="hidden" data-type="'+ontractItem.contractType+'" id="contractType'+ontractItem.contractType+'" value=\''+arr+'\' />';
                                $("body").append(html);
                            }
                        }
                    });
                });
            }
        },
        error:function(){

        }
    });
}

//批量上传 客户合同
$("#qtfjUpload").on("click", function() {
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var checkLen=$("#contractType input[type='checkbox']:checked").length;
    var inpdata =null,inpdataArr = [], inpadataFinalArr = [];
    if($('input[id^=newcontractType]').length > 0){
        inpdata = $('input[id^=newcontractType]')
    }else{
        inpdata = $('input[id^=contractType]')
    }
    $(inpdata).each(function (index,item) {
        inpdataArr.push(JSON.parse(item.value));
    })
    for(var i = 0; i<inpdataArr.length; i++ ){
        for(var j = 0; j<inpdataArr[i].length; j++){
            inpadataFinalArr.push(inpdataArr[i][j])
        }
    }
    $("#contractType label").each(function(){
        if(checkLen < 1 && inpadataFinalArr.length <1){  // 情况1
            var text=$.trim($(this).text());
            var v=$(this).prev("input").val();
            typeList.push({
                text:text,
                value:v
            })
        }else if(checkLen > 0 && inpadataFinalArr.length <1 ){ // 情况2
            var checked=$(this).prev('input[type=checkbox]')[0].checked; //$(this).find("input[type='checkbox']").get(0).checked;
            if(checked){
                var text=$.trim($(this).text());
                var v=$(this).prev("input").val();
                typeList.push({
                    text:text,
                    value:v
                })
            }
        }else if(checkLen > 0 && inpadataFinalArr.length > 0){ // 情况3
            var text=$.trim($(this).text());
            var v=$(this).prev("input").val();
            typeList.push({
                text:text,
                value:v
            })
            if($("input[id^='newcontractType']").length > 0){
                var newOther = $("input[id='newcontractType"+v+"']");
                var imgArr = [], newImgArr = [];
                for(var i = 0; i<newOther.length; i++){
                    var str = $(newOther[i]).val();
                    str = JSON.parse(str)
                    imgArr = imgArr.concat(str);
                }
                var type = $("input[id='newcontractType"+v+"']").attr('data-type');
                for(var i = 0; i<imgArr.length; i++){
                    imgArr[i].type = type
                }
                eChoImgList = eChoImgList.concat(imgArr);
            }else{
                if($("input[id='contractType"+v+"']").length > 0){
                    var newOther = $("input[id='contractType"+v+"']");
                    var imgArr = [], newImgArr = [];
                    for(var i = 0; i<newOther.length; i++){
                        var str = $(newOther[i]).val();
                        str = JSON.parse(str)
                        imgArr = imgArr.concat(str);
                    }
                    var type = $("input[id='contractType"+v+"']").attr('data-type');
                    for(var i = 0; i<imgArr.length; i++){
                        imgArr[i].type = type
                    }
                    eChoImgList = eChoImgList.concat(imgArr);
                }
            }
        }else{  //情况4：去掉了勾选，但还有包含附件。弹层下拉框显示所有的checkbox选项、
            var text=$.trim($(this).text());
            var v=$(this).prev("input").val();
            typeList.push({
                text:text,
                value:v
            })
            if($("input[id^='newcontractType']").length > 0){
                var newOther = $("input[id='newcontractType"+v+"']");
                var imgArr = [], newImgArr = [];
                for(var i = 0; i<newOther.length; i++){
                    var str = $(newOther[i]).val();
                    str = JSON.parse(str)
                    imgArr = imgArr.concat(str);
                }
                var type = $("input[id='newcontractType"+v+"']").attr('data-type');
                for(var i = 0; i<imgArr.length; i++){
                    imgArr[i].type = type
                }
                eChoImgList = eChoImgList.concat(imgArr);
            }else{
                if($("input[id='contractType"+v+"']").length > 0){
                    var newOther = $("input[id='contractType"+v+"']");
                    var imgArr = [], newImgArr = [];
                    for(var i = 0; i<newOther.length; i++){
                        var str = $(newOther[i]).val();
                        str = JSON.parse(str)
                        imgArr = imgArr.concat(str);
                    }
                    var type = $("input[id='contractType"+v+"']").attr('data-type');
                    for(var i = 0; i<imgArr.length; i++){
                        imgArr[i].type = type
                    }
                    eChoImgList = eChoImgList.concat(imgArr);
                }
            }
        }
    });
    $(this).upLoad({
        typeList:typeList,//格式[{text:xxx,value:xxx}]
        eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
        fileParam:{
            name:'enclosureName',
            url:'url'
        },
        urlBack: function (data) {
            //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
            //存放数据
            //files
            //console.log(data)
            var inp = $('#contractType input')
            var html='';
            $('input[id^=newcontractType]').remove();
            for(var name in data){
                //var list=data[name];
                var list = [];
                list.push(data[name])
                if(list.length > 0){
                    var arr=JSON.stringify(list);
                    //html='<input type="hidden" data-type="'+name+'" id="contractType'+name+'" value=\''+arr+'\' />';
                    html='<input type="hidden" data-type="'+data[name].type+'" id="newcontractType'+data[name].type+'" value=\''+arr+'\' />';
                    $("body").append(html);
                }
            }

            var AllInpHide = $('input[id^=newcontractType]');
            if(AllInpHide.length > 0){ //  如果存在附件，再去设置checkbox的回显。
                for(var i = 0; i<inp.length; i++){
                    inp[i].checked = false;
                    for(var j = 0; j<AllInpHide.length; j++){
                        if(inp[i].value == AllInpHide[j].getAttribute('data-type')){
                            inp[i].checked = true
                        }
                    }
                }
            }else{ //  如果不存在附件，或者说附件被删除，设置checkbox的 全部未选中。
                for(var i = 0; i<inp.length; i++){
                    inp[i].checked = false;
                }
            }
        }
    });
});
//客户合同 查看附件
$("#qtfjView").on("click",function(){
    //获取type类型
    var imgList=[];
    var checkInp=$("#contractType input[type='checkbox']:checked");
    if(checkInp.length) {
        var id_name = ($('input[id^=contractType]').length > 0) ? 'contractType' : 'newcontractType';
        var inp = $("input[id^=contractType]");
        for (let i = 0; i < inp.length; i++) {
            var val = $(inp[i]).val();
            var imgArr = JSON.parse(val);
            imgList = imgList.concat(imgArr);
        }
    }
    $.viewImg({
        fileParam:{
            name:'enclosureName',
            url:'url'
        },
        list:imgList
    })
});

function chkDis(obj,zTree){

    zTree.setChkDisabled(zTree.getNodeByParam( "id",obj.id), true);
    var children=obj.children;
    if(children && children.length > 0)
    {
        for(var i=0;i<children.length;i++)
        {
            //console.log(children[i].id)
            zTree.setChkDisabled(zTree.getNodeByParam( "id",children[i].id), true);
            chkDis(children[i],zTree);
        }
    }
}
//新增行
function addRow(tableId){
    var rowNumber=$(tableId).find("tr").not("first").length+1;

    if(tableId == '#table3'){ // 批准文件
        $('.PZWJ_wrap_div').find('input,select,checkbox').removeAttr('disabled');
    }
    if(tableId == '#table1'){ // 质量保证协议
        $('.ZLBZXY_wrap_div').find('input,select,checkbox').removeAttr('disabled');
    }
    if(tableId == '#table2'){ // 客户委托书
        $('.KHWTS_wrap_div').find('input,select,checkbox').removeAttr('disabled');

    }
    if(tableId == '#certTable'){ // 被委托人身份证
        $('.BWTRSFZ_wrap_div').find('input,select,checkbox').removeAttr('disabled');
    }
    // if(tableId == '#table1'){ // 客户合同
    //     $('.ZLBZXY_wrap_div').find('input,select,checkbox').removeAttr('disabled');
    // }

    $(tableId).addRowData({id:rowNumber})
    if(tableId == '#table2'){

        var opt = $(tableId).find('tr:last').find('select[name=mandatorType] option');
        var defVal = null;
        $(opt).each(function(index,item){
            if($(item).text() == '采购委托书'){
                defVal = $(item).val();
            }
        })
        $(tableId).find('tr:last').find('select[name=mandatorType]').find('option[value="'+defVal+'"]').attr("selected",true)


    }
    // setTimeout(function () {
    //
    // },1000)
    if(tableId == '#table3'){ // 批准文件
        $(".ApprovalUploadeImg").removeAttr('disabled');
    }
}

//删除行
function deleRow(tableId){
    var selectRow = $(tableId).XGrid('getSeleRow');
    if (!selectRow) {
        utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
    } else {
        $(tableId).XGrid('delRowData', selectRow.id);
        if(tableId == "#table3"){
            initBaseDataBuseScope();
        }
        utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
    }
}
//请求字典获取经营范围树数据
/**
 * 树形，重点
 */
function getZtreeData(credentialId,id){
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/listbycredentialidAndorgcode",
        async : true,
        data:{
            credentialId:credentialId,
            orgCode:'001'
        },
        dataType:"json",
        success: function (data) {
            //console.log(isInit);
            var zTreeNodes = [{
                id: 0,
                name: "经营范围",
                //open: true,//展开
                children:[]
            }]
            // var isInit = $('#isInit').val();
            // if(isInit){
            //     zTreeNodes[0].checked=true;
            // }
            if(!data )return false;
            if(data.result && data.result.length > 0){
                $(data.result).each(function(index,item){
                    var _obj = {};
                    _obj.id = item.scopeId;
                    _obj.name = item.scopeName;
                    _obj.children = [];
                    // if(isInit){
                    //     _obj.checked=true;
                    // }
                    $(item.children).each(function(cindex,citem){
                        var _obj_child = {};
                        _obj_child.id = citem.scopeId;
                        _obj_child.name = citem.scopeName;
                        // if(isInit){
                        //     _obj_child.checked=true;
                        // }
                        _obj.children.push(_obj_child)
                    })
                    zTreeNodes[0].children.push(_obj)
                })
                for(var i of zTreeNodes[0].children){
                    if(i.children.length == 0){
                        delete  i['children']
                    }
                }
                $('#table3').XGrid('setRowData', id,{customerBusinessScopeVoList:zTreeNodes,scopeofoperationVo:JSON.stringify(data.result)});
            }else{
                $('#table3').XGrid('setRowData', id,{customerBusinessScopeVoList:'',scopeofoperationVo:''});
            }
            //$('#isInit').val(0);
            //console.log(zTreeNodes)
            //$('#table3').XGrid('setRowData', id,{customerBusinessScopeVoList:zTreeNodes});

            var zTreeId=$('#table3 #'+id).find(".operScopeZtree").attr("id");
            var zTree = $.fn.zTree.getZTreeObj(zTreeId);
            zTree.refresh();
            for(var i=0;i<idArr.length;i++){
                var item=idArr[i];
                for(var n in item){
                    if(n == credentialId){
                        var aList=item[n];
                        if(aList && aList.length > 0){
                            for(var s=0;s<aList.length;s++){
                                zTree.checkNode( zTree.getNodeByParam( "id",Number(aList[s].businessScopeCode)), true );
                            }
                        }
                    }
                }
            }

        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
};

//初始化经营范围内容
/**
 * initBaseDataBuseScope() 此方法需要在有效期截止日期WdatePicker内调用 eg:<input type="text" placeholder="有效期" onclick="WdatePicker({onpicked:function(dp){initBaseDataBuseScope();}})"  name="validityDate"/>
 *
 * utils.operateRange 拼装数据
 *
 * @param XGrid 数据 $('#table3').XGrid('getRowData')
 *
 * @param 经营范围文件数当前列字段名 scopeOfBusiness
 *
 * @param 有效期截止字段  validityDate
 * */
function initBaseDataBuseScope(){
    var htmlAry = [], idAry = [];
    $.each(utils.operateRange($('#table3').XGrid('getRowData'),'customerBusinessScopeVoList','validUntil'), function (i, v) {
        if (v.status) {
            htmlAry.push('<font style="color: red;">' + v.name + '</font>');
        } else {
            htmlAry.push('<font>' + v.name + '</font>');
        }
        idAry.push(v.id);
    })
    //console.log(htmlAry.join(','))
    $('#baseDataBuseScope').html(htmlAry.join(','));
}


// 初始化仓库地址
function initDistpicker(){
    $('[data-toggle="distpicker"]').each(function(){
        //省
        var val1=$.trim($(this).find("select").eq(0).attr("data-value"));
        if(val1 && val1 != ''){
            $(this).find("select").eq(0).val(val1);
            if($(this).find("select").eq(3).attr('oldStreetName')) {
                $(this).find("select").eq(0).change();
            }
        }else{
            $(this).find("select").eq(0).val(val1);
        }
        //市
        var val2=$.trim($(this).find("select").eq(1).attr("data-value"));
        if(val1 && val1 != ''){
            $(this).find("select").eq(1).val(val2);
            $(this).find("select").eq(1).change();
        }else{
            $(this).find("select").eq(1).val(val2);
        }
        //区
        var val3=$.trim($(this).find("select").eq(2).attr("data-value"));
        if(val1 && val1 != ''){
            $(this).find("select").eq(2).val(val3);
            // 20191012 ------------
            $(this).find("select").eq(2).change();
        }else{
            $(this).find("select").eq(2).val(val3);
        }
        //街道
        if($(this).find("select").eq(3).attr('oldStreetName')){
            var val4=$.trim($(this).find("select").eq(3).attr("data-value"));
            if(val1 && val1 != ''){
                $(this).find("select").eq(3).val(val4);
                // 20191012 ------------ 客户基础资料变更保存后，注册地址重新初始化会将option清空
                // $(this).find("select").eq(3).html('<option selected>'+$(this).find("select").eq(3).attr('oldStreetName')+'</option>');
            }else{
                $(this).find("select").eq(3).val(val4);
            }
        }

    });
}

//初始化显示隐藏付款、结算方式输入框
function initPaymentSettlement(){
    $(".parentCode").each(function(){
        var checked=this.checked;
        if(checked)
        {
            $(this).parents('.paymentSettlement').find('.childCode').show();
        }else{
            $(this).parents('.paymentSettlement').find('.childCode').hide();
        }
    })
}
//付款、结算方式点击复选框显示输入框
$(document).on("change",".parentCode",function(){
    var checked=this.checked;
    if(checked)
    {
        $(this).parents('.paymentSettlement').find('.childCode').show();
    }else{
        $(this).parents('.paymentSettlement').find('.childCode').hide();
    }
})
/**
 * 禁止批准文件内经营范围树可选
 *@param obj  Object 树节点对象
 * @param zTree Object 树对象
 * @param checked  boolean 是否禁止选则  true为禁止
 * */
function chkDis(obj,zTree,checked){
    if(!obj) return;
    zTree.setChkDisabled(zTree.getNodeByParam( "id",obj.id), checked);
    var children=obj.children;
    if(children && children.length > 0)
    {
        for(var i=0;i<children.length;i++)
        {
            zTree.setChkDisabled(zTree.getNodeByParam( "id",children[i].id), checked);
            chkDis(children[i],zTree,checked);
        }
    }
}
/**
 * 设置 批准文件内经营范围文件树不可选
 * @param checked boolean  true为禁止选择
 * */
function setDisabledZtree(checked,$table){
    $table=  $table || $("#table3");
    $table.find('tr').each(function (index) {
        if (index != 0) {
            var zTreeId = $(this).find(".operScopeZtree").attr("id");
            var zTree = $.fn.zTree.getZTreeObj(zTreeId);
            var nodes=zTree.getNodes();
            chkDis(nodes[0],zTree,checked);
        }
    })
}

$(function () {
    //客户合同  双击选项预览对应的附件
    $('#contractType label').each(function () {
        $(this).dblclick(function () {
            var imgList=[];
            var type = $(this).prev().val();

            var inp= $("input[id^='newcontractType']");
            if(inp.length){
                if(inp.length > 1){
                    for(var i = 0; i<inp.length; i++){
                        if($(inp[i]).attr('data-type') == type){
                            var imgArr=JSON.parse($(inp[i]).val());
                            imgList=imgList.concat(imgArr);
                        }
                    }
                }else{
                    var imgArr=JSON.parse(inp.val());
                    imgList=imgList.concat(imgArr);
                }
            }else{
                var inp= $("input[id^='contractType']");
                if(inp.length){
                    if(inp.length > 1){
                        for(var i = 0; i<inp.length; i++){
                            if($(inp[i]).attr('data-type') == type){
                                var imgArr=JSON.parse($(inp[i]).val());
                                imgList=imgList.concat(imgArr);
                            }
                        }
                    }else{
                        var imgArr=JSON.parse(inp.val());
                        imgList=imgList.concat(imgArr);
                    }
                }
            }
            $.viewImg({
                fileParam:{
                    name:'enclosureName',
                    url:'url'
                },
                list:imgList
            });
        })
    })
})
//查看变更数据的图片
$('body').on('click','#id_dataChangeDiv span',function(){
    var  oldFile = window.changeApplyList.customerContractVos.valueBefore;
    var  newFile = window.changeApplyList.customerContractVos.valueAfter;
    var newArr = [], oldFileArr = [], newFileArr = [], oldFileNameArr = [];
    for(var i = 0; i< newFile.length; i++){
        var a  = newFile[i];
        var len = 0, param = '';
        len = (a.customerEnclosureVoList)?a.customerEnclosureVoList.length:a.enclosureList.length;
        param = (a.customerEnclosureVoList)?'customerEnclosureVoList':'enclosureList';
        for(var j = 0; j<len; j++){
            var newObj = {};
            if(a[param][j]){
                newObj.type = (a.contractType)?a.contractType:a.certificateType;
                newObj.enclosureName = (a.customerEnclosureVoList)?a.customerEnclosureVoList[j].enclosureName:a.enclosureList[j].enclosureName;
                newObj.url = (a.customerEnclosureVoList)?a.customerEnclosureVoList[j].url:a.enclosureList[j].url;
                newFileArr.push(newObj);
            }else{
                break
            }
        }
    }

    var type = $(this).prev('input').val()?$(this).prev('input').val():$(this).attr('data-val');
    if(newFileArr.length > 0){
        var demoArr= [];
        for(let i = 0; i<newFileArr.length; i++){
            if(newFileArr[i].type == type){
                demoArr.push(newFileArr[i])
            }
        }
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            list:demoArr
        })
        return false;
    }else{
        utils.dialog({
            title:'提示',
            content:'没有对应类型的附件。',
            okValue:'确定',
            ok:function () {}
        }).showModal();
        return false;
    }
});

/**
 * 加载委托人类型
 * @param simpleCode
 */
function loadMandatorType(simpleCode) {
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/queryClient",
        async : false,
        data:{"type":4},
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        if(simpleCode == arr[i].clientId){
                            html+='<option value="'+arr[i].clientId+'" selected="selected">'+arr[i].clientName+'</option>';
                        }else{
                            html+='<option value="'+arr[i].clientId+'">'+arr[i].clientName+'</option>';
                        }
                    }
                }
            }
            $("select[name='mandatorType']:last").html(html);
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}




