$('div[fold=head]').fold({
    sub: 'sub'
});
var recordId = $("#recordId").val();
$('#X_Table').XGrid({
    url:"/proxy-product/product/upperShelf/queryProductUpperShelfDetail?approvalRecordId="+recordId,
    colNames: ['机构', '业务类型' ,'商品编号','原商品编码','小包装条码','商品名称', '通用名', '商品规格', '生产厂家', '单位',
        '经营状态','上次下架时间','上次下架原因','上次下架备注','上次下架附件',
        '申请商品定位一级','品类策略审核商品定位一级', '申请商品定位二级', '品类策略审核商品定位二级', '申请集采签约方式', '品类策略审核集采签约方式',
        '是否专供', 'APP销售价是否维价','终端零售价是否维价', '应季类型','建议终端售价', '价格管理部审核建议终端售价', '建议APP售价',
        '价格管理部最终定价', '建议智鹿总部采购价', '价格管理部审核智鹿总部采购价','建议连锁APP售价', "建议连锁APP阶梯售价<div style='display:inline-block;background-color: black;color: white;border-radius: 50%;width: 15px ;height: 15px;text-align: center;font-size:10px;text-align: center;'  onmouseenter='replaceEncodingTip(this," + "1" + ")' onmouseout='replaceEncodingTip(this," + "2" + ")'>?</div> ",'价格管理部审核连锁APP售价', '价格管理部审核连锁APP阶梯售价','建议荷叶大药房采购价','价格管理部审核荷叶大药房采购价','连锁票面毛利率','票面毛利率','终端毛利率', '备注', '采购员','销售状态','库存数量','最后含税进价','最后入库时间','最后供应商', '商品产地','剂型', '处方分类', '批准文号',
        '存储条件','底价', '一级分类', '二级分类', '三级分类', '四级分类','爆款标识'],
    colModel: [{
        name: 'orgName',
        index: 'orgName',
        width:210
    },  {
        name: 'channelId',
        index: 'channelId',
        width:100
    }, {
        name: 'productCode',
        index: 'productCode',
        width:100
    },  {
        name: 'oldProductCode',
        index: 'oldProductCode',
        width:140
    },  {
        name: 'smallPackageBarCode',
        index: 'smallPackageBarCode',
        width:130
    },  {
        name: 'productName',
        index: 'productName',
        width:110
    },  {
        name: 'commonName',
        index: 'commonName',
        width:110
    },  {
        name: 'specifications',
        index: 'specifications',
        width:140
    },  {
        name: 'manufacturerVal',
        index: 'manufacturerVal',
        width:240
    },  {
        name: 'packingUnitVal',
        index: 'packingUnitVal',
        width:80
    },{
        name: 'operatingStateVal',
        index: 'operatingStateVal',
        width: 80
    }, {
        name: 'lastLowerShelfTime',
        index: 'lastLowerShelfTime',
        width: 200,
        formatter: function (value) {
            if (value) {
                return new Date(value).Format('yyyy-MM-dd');
            }
        }
    }, {
        name: 'lastLowerShelfReason',
        index: 'lastLowerShelfReason',
        width: 200
    }, {
        name: 'lastLowerShelfRemark',
        index: 'lastLowerShelfRemark',
        width: 150
    }, {
        name: 'lastLowerShelfEnclosureUrl',
        index: 'lastLowerShelfEnclosureUrl',
        formatter: function (val,rowType,rowData) {
            if(val) {
                return `<a href="javascript:;" data-url="${val}" class="file_a">📎</a>`;
            }else {
                return '';
            }
        },
        unformat: function (val,rowModel,ele) {
            if($(ele).find('a.file_a').length > 0) {
                return $(ele).find('a.file_a').attr('data-url');
            }else {
                return '';
            }
        }
    },{
        name: 'commodityPositionVal',
        index: 'commodityPositionVal',
        width:150
    },  {
        name: 'auditCommodityPositionVal',
        index: 'auditCommodityPositionVal',
        width:200
    }, {
        name: 'secondCommodityPositionVal',
        index: 'secondCommodityPositionVal',
        width:150
    }, {
        name: 'auditSecondCommodityPositionVal',
        index: 'auditSecondCommodityPositionVal',
        width:200
    }, {
        name: 'purchaseContractModeVal',
        index: 'purchaseContractModeVal',
        width:150
    }, {
        name: 'auditPurchaseContractModeVal',
        index: 'auditPurchaseContractModeVal',
        width:200
    }, {
        name: 'exclusiveYn',
        index: 'exclusiveYn',
        hidden:true,
        hidegrid:true,
        width:80
    }, {
        name: 'dimensionSalesPriceYnVal',
        index: 'dimensionSalesPriceYnVal',
        width:180
    }, {
        name: 'dimensionTerminalPriceYnVal',
        index: 'dimensionTerminalPriceYnVal',
        width:150
    }, {
        name: 'seasonalVarietiesVal',
        index: 'seasonalVarietiesVal',
        width:100
    }, {
        name: 'terminalPrice',
        index: 'terminalPrice',
        width:130
    },  {
        name: 'auditTerminalPrice',
        index: 'auditTerminalPrice',
        width:220
    },{
        name: 'appPrice',
        index: 'appPrice',
        width:220
    }, {
        name: 'auditAppPrice',
        index: 'auditAppPrice',
        width:220
    },{
        name: 'zhiluPrice',
        index: 'zhiluPrice',
        width:150
    }, {
        name: 'auditZhiluPrice',
        index: 'auditZhiluPrice',
        width:220
    },{
        name: 'chainGuidePrice',
        index: 'chainGuidePrice',
        formatter: function (e) {
            if(e!=null) {
                return Number(e).toFixed(2);
            }
        },
        width:140
    },{
        name: 'ladderPrice',
        index: 'ladderPrice',
        // formatter: function (e) {
        //     if(e!=null) {
        //         return Number(e).toFixed(2);
        //     }
        // },
        width: 160
    },{
        name: 'auditChainGuidePrice',
        index: 'auditChainGuidePrice',
        formatter: function (e) {
            if(e!=null) {
                return Number(e).toFixed(2);
            }
        },
        width:220
    },
    {
        name: 'auditLadderPrice',
        index: 'auditLadderPrice',
        formatter: function(e,rowType,rowData) {
            if (rowData.ladderPrice != null) {
                return rowData.ladderPrice;
            }
        },
        width: 250
    },{
        name: 'heyePrice',
        index: 'heyePrice',
        width:180
    }, {
        name: 'auditHeyePrice',
        index: 'auditHeyePrice',
        width:240
    },{
        name: 'chainParGrossMargin',
        index: 'chainParGrossMargin',
        width:130
    }, {
        name: 'parGrossMargin',
        index: 'parGrossMargin',
        width:120
    }, {
        name: 'terminalGrossMargin',
        index: 'terminalGrossMargin',
        width:120
    }, {
        name: 'remarks',
        index: 'remarks',
        width:180
    }, {
        name: 'buyerVal',
        index: 'buyerVal',
        width:80
    }, {
        name: 'ecProductStatus',
        index: 'ecProductStatus',
        width:80
    }, {
        name: 'inventoryQuantity',
        index: 'inventoryQuantity',
        width:80
    }, {
        name: 'lastIncludingTaxPurchasePrice',
        index: 'lastIncludingTaxPurchasePrice',
        width:110
    }, {
        name: 'lastStorageTime',
        index: 'lastStorageTime',
        formatter:function(value){
            if(value){
                return new Date(value).Format('yyyy-MM-dd');
            }
        },
        width:200
    }, {
        name: 'lastSupplier',
        index: 'lastSupplier',
        width:220
    },{
        name: 'producingArea',
        index: 'producingArea',
        width:80
    }, {
        name: 'dosageFormVal',
        index: 'dosageFormVal',
        width:100
    }, {
        name: 'prescriptionClassificationVal',
        index: 'prescriptionClassificationVal',
        width:100
    }, {
        name: 'approvalNumber',
        index: 'approvalNumber',
        width:160
    }, {
        name: 'storageConditionsVal',
        index: 'storageConditionsVal',
        width:80
    }, {
        name: 'floorPrice',
        index: 'floorPrice',
        width:80
    },{
        name: 'firstCategoryVal',
        index: 'firstCategoryVal',
        width:100
    }, {
        name: 'secondCategoryVal',
        index: 'secondCategoryVal',
        width:120
    }, {
        name: 'thirdCategoryVal',
        index: 'thirdCategoryVal',
        width:120
    }, {
        name: 'fourCategoryVal',
        index: 'fourCategoryVal',
        width:100
    },{
        name:'inVogue',
        index:'inVogue',
        hidden:true
    }],
    rowNum: 20,
    rownumbers: true,
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {
        //console.log('双击行事件', id, dom, obj, index, event);
    },
    onSelectRow: function (id, dom, obj, index, event) {
        //console.log('单机行事件', id, dom, obj, index, event);
    }
});
//时间格式化
function dateFormatter(inputTime) {
    if (inputTime == null || inputTime == "")
        return "";
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    var minute = date.getMinutes();
    var second = date.getSeconds();
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d;
    // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
}
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}
//设置显示列
$("#setRow").click(function () {
    $('#X_Table').XGrid('filterTableHead',1000);
});
//流程图显示
$.ajax({
    type: "POST",
    url: "/proxy-product/product/purchaseLimit/queryTotle?key="+ $("#workProcessKey").val()+"&processInstaId="+$("#taskId").val(),
    async: false,
    success: function (data) {
        if (data.code==0&&data.result!=null){
            console.log(data.result);
            $('.flow').html("")
            $('.flow').process(data.result);
        }
    },
    error: function () {}
});

$("#X_Table").on('click','.file_a',function (e) {
    var url = $(e.target).attr('data-url');
    var content=`<div class="changeApplyItem" style="line-height: 50px;text-align: center;">
                    <a style="flex: 1;" href="${url}" target="_blank" class="download">查看下载</a>
                </div>`;
    utils.dialog({
        align: 'top',
        width: 130,
        height: 50,
        padding: 2,
        content:content,
        quickClose: true
    }).show(this);
});
let dialogShow;

function replaceEncodingTip(e, str) {

    if (str === 1) {
        dialogShow = utils.dialog({ content: "请按如下格式填写阶梯价:100,1.2;200,1.1  (用英文逗号和分号隔开)，阶梯价格须小于默认连锁APP售价"})
        dialogShow.show(e)
    } else {
        dialogShow.close().remove();
    }
    return false;

}