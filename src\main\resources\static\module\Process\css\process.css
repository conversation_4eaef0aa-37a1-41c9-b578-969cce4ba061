.content-ovf {
    overflow: unset;
}

.process-container {
    list-style: none;
    zoom: 1;
    width: max-content;

}

.process-step {
    float: left;
    width: 35px;
    height: 35px;
    position: relative;
    margin-right: 120px;
    text-align: center;
    margin-bottom: 100px;
}

.step-content {
    line-height: normal;
    color: #999;
    font-size: 12px;
    white-space: nowrap;
    margin-top: 40px;
    position: absolute;
    top: 0px;
}

.process-step>span {
    display: block;
    width: 35px;
    height: 35px;
    line-height: 35px;
    color: #999;
    font-size: 14px;
}

.track-3 {
    width: 98px;
    height: 23px;
    position: absolute;
    right: -110px;
    top: 30%;
    margin-top: -3px;
    background: url(../imgs/line-2.1.png) center center no-repeat;
}

.track-4 {
    width: 98px;
    height: 23px;
    position: absolute;
    right: -110px;
    top: 30%;
    margin-top: -3px;
    background: url(../imgs/line-2.2.png) center center no-repeat;
}

.track-2 {
    width: 98px;
    height: 6px;
    position: absolute;
    right: -110px;
    top: 50%;
    margin-top: -3px;
    background: url(../imgs/line-1.2.png) center center no-repeat;
}

.track-1 {
    width: 98px;
    height: 6px;
    position: absolute;
    right: -110px;
    top: 50%;
    margin-top: -3px;
    background: url(../imgs/line-1.1.png) center center no-repeat;
}

.step-last>.line {
    display: none;
}

.step-tips {
    position: absolute;
    width: 140px;
    left: 0px;
    top: 55px;
    padding-bottom: 3px;
    text-align: left;
    font-size: 12px;
    z-index: 1;
    background: #fff;
}

.tips-more {
    position: absolute;
    right: 20px;
    top: 40px;
    color: #2DB7F5;
    cursor: pointer;
    z-index: 5;
}

.tips-modal {
    width: 332px;
    height: auto !important;
    height: 322px;
    max-height: 322px;
    overflow: hidden;
    display: none;
    z-index: 666;
}

.tipsModal,
.tipsModal .ui-dialog-arrow-a,
.tipsModal .ui-dialog-arrow-b {
    border: 0px !important;
    box-shadow: 0px 1px 10px 2px rgba(0, 0, 0, .23);
}


.tipsModal .close {
    font-style: normal;
    font-size: 20px;
    width: 10px;
    height: 10px;
    color: #999;
    position: absolute;
    top: 3px;
    right: 20px;
    line-height: normal;
}

.tipsModal .more_cont {
    width: 292px;
    height: auto;
    margin: 20px auto;
    position: relative;
}

.child {
    display: none;
}

.more_cont:first-of-type {
    margin-top: 20px;
}

.tips-modal>.more_cont>P {
    padding-left: 16px;
    line-height: 18px;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
}

.node-line {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: url(../imgs/node.png)center center no-repeat;
    position: absolute;
    left: -10px;
    top: 4px;
}

.node-line:after {
    content: "";
    width: 2px;
    height: 100px;
    background: #E9E9E9;
    border-radius: 8px;
    position: absolute;
    top: 8px;
    left: 3px;
}

.step-tips p,
.more_cont p {
    color: #999;
    line-height: 16px;
    margin: 3px 0;
    font-size: 12px;
}

.main-tips::-webkit-scrollbar {
    width: 6px !important;
    height: 10px !important;
}

.step-tips>p:nth-of-type(3) {
    display: block;
    width: 50px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.step-tips>p:nth-of-type(3):hover {
    width: 72px;
    height: auto;
    z-index: 10;
    background: #fff;
    white-space: unset;
    position: relative;
}

.status-notStart {
    background: url(../imgs/notStart.png) center center no-repeat;
}

.status-pending {
    background: url(../imgs/pending.png) center center no-repeat;
}

.status-pending>span {
    color: #fff;
}

.status-success {
    background: url(../imgs/success.png) center center no-repeat;
}

.status-return {
    background: url(../imgs/return.png) center center no-repeat;
}

.status-close {
    background: url(../imgs/close.png) center center no-repeat;
}

.status-success>span,
.status-return>span,
.status-close>span {
    display: none;
}

/* ::-webkit-scrollbar {
    滚动条整体样式
    width: 8px;
    高宽分别对应横竖滚动条的尺寸
    height: 6px;
} */
::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #D0D3D8;
}

::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background: #EDEDED;
}