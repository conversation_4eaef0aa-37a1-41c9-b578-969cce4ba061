$(function () {
    // 付款方式切换
    $('[name=parentCode]').on('change', function() {
        let checkedInp = $('[name=parentCode]:checked')
        let checkedVal  = []
        for(let i = 0; i<checkedInp.length; i++ ){
            checkedVal.push($(checkedInp[i]).val())
        }
        const baseVal = ['1010', '1003', '1004']
        let intersection = baseVal.filter(function (val) { return checkedVal.indexOf(val) > -1 })
        $('.paymentNode').css('display',intersection.length != 0 ? 'block' : 'none')
        $('.paymentNode').find('.childCode').css('display','flex')
        $('.paymentNode').find('input[type=checkbox]').prop('checked',intersection.length != 0 ? true : false )
    })
    //
    $('body').on('input', '.paymentNodeValRang',function () {
        if ($(this).prev().val() == '1011001' || $(this).prev().val() == '1012001') {

            let v = $(this).val();
            v = v.replace(/[^\d.]/g, '')
            $(this).val(v);
            if(v.indexOf('-') >= 0){
                v = v.replace(/-/g,'');
                $(this).val(v);
            }
            if(v.indexOf('.') == 0){
                v = v.replace('.','');
                $(this).val(v);
            }
            if(v.indexOf('0') == 0){
                if (v.length == 1) { // 0000000
                    $(this).val('0');
                } else {
                    if(v.indexOf('0.') == 0){
                    } else {
                        $(this).val(v.substring(1));
                    }
                }
            }
            if(v.split('.').length > 2){
                let  lastIndex = v.lastIndexOf('.')
                v = v.substring(0,lastIndex) + v.substring(lastIndex + 1)
                $(this).val(v);
            }
            if (Number(v) < 0)  $(this).val(0);
            if (Number(v) > 100)  $(this).val(100);
        }
    })
})