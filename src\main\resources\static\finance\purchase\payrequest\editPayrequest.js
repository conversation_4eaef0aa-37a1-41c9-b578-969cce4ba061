let sbankName = ''
var supplierCodeStatic;
$(function () {
    //动态设置页面高度
    $('.content').css('height', $(window).height());
    /**
     * 获取款项类型
     */
    var fundTypes = JSON.parse($('#fundTypes').val());
    var imageType = ['png','jpg','pdf','xls','xlsx','doc','docx'];
    var advancePaymentAccount = {
        "prepaymentAmount": 0,
        "occupyAmount": 0,
        "availableAmount": 0,
        "boolNoDeductionSupplier": false
    };
    function getFundName(code) {
        var result = '';
        if (fundTypes) {
            $.each(fundTypes, function (index, item) {
                if (item.code == code) {
                    result = item.name;
                }
            })
        }
        return result;
    }
    var tabsTrue = [false, false];
    // tabs 切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $('.nav-content');
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass(
            'active');
        if ($(this).index() == '0') {
            //选择订单
            $('#isPrepay').val('C01');
            $('#isPrepayStr').val(getFundName('C01'));
            if (tabsTrue[0]) {
                loadOrderTabNext('');
            } else {
                tabsTrue[0] = true;
                loadOrderTab(""); // 第一次渲染
            }
            $('#applayAmount').val("");
        } else {
            //选择发票
            $('#isPrepay').val('C02');
            $('#isPrepayStr').val(getFundName('C02'));
            $('#applayAmount').val("");
            if (tabsTrue[1]) {
                loadInvoiceTabNext("");
            } else {
                tabsTrue[1] = true;
                loadInvoiceTab("");; // 第一次渲染
            }
        }
    })

    window.isFirst = true;

    $.ajax({
        method: "POST",
        url: "/proxy-finance/finance/purchase/payrequestinfo/getPayrequestInfo",
        data: {
            "billNo": $("#billNo").val(),
        },
        dataType: 'json',
        cache: false
    }).done(function (data) {
        data.result.expectPayTime = moment(data.result.expectPayTime).format('YYYY-MM-DD');
        supplierCodeStatic = data.result.supplierNo;
        getAdvancePaymentAccount(supplierCodeStatic);
        $('#myform').JSONToform(data.result);
        $('#basicInfo').JSONToform(data.result);
        getBankAmount(data.result.supplierNo);
        getPayableType(data.result.supplierNo, '#payableType', data.result.payableType,{acceptanceRebate:data.result.acceptRebate,bankTransferRebate:data.result.transferRebate})
        // $('#isPrepay').val(data.result.isPrepay);
        $('#isKa').val(data.result.isKa);
        $('#isPrepay').val(data.result.isPrepay);
        $('#isPrepayStr').val(getFundName(data.result.isPrepay));
        $("#remarks").text(data.result.remarks);
        $('#applayAmount').val(data.result.applayAmount ? data.result.applayAmount.formatMoney('2', '', ',', '.') : '0.00');
        $('#prepaymentAmountDeduction').val(data.result.prepaymentAmountDeduction ? data.result.prepaymentAmountDeduction.formatMoney('2', '', ',', '.') : '0.00');
        $('#actualPaymentAmount').val(data.result.actualPaymentAmount ? data.result.actualPaymentAmount.formatMoney('2', '', ',', '.') : '0.00');
        $('#prepaymentAvailableAmount').val(data.result.prepaymentAvailableAmount ? data.result.prepaymentAvailableAmount.formatMoney('2', '', ',', '.') : '0.00');
        // changeDeduAmount(data.result.applayAmount ? data.result.applayAmount : 0);
        $('#payableBalance').val(data.result.payableBalance ? data.result.payableBalance.formatMoney('2', '', ',', '.') : '0.00');
        $('#businessPayBalance').val(data.result.businessPayBalance ? data.result.businessPayBalance.formatMoney('2', '', ',', '.') : '0.00');
        $('#acceptRebate').val(data.result.acceptRebate ? data.result.acceptRebate.formatMoney('2', '', ',', '.') : '0.00');
        $('#transferRebate').val(data.result.transferRebate ? data.result.transferRebate.formatMoney('2', '', ',', '.') : '0.00');
        $("#e_lastPayTime").val(data.result.lastPayTime);
        $('#ticketDeduction').val(data.result.ticketDeduction?data.result.ticketDeduction.formatMoney('2', '', ',', '.') : '0.00');
        $('#otherDeductions').val(data.result.otherDeductions?data.result.otherDeductions.formatMoney('2', '', ',', '.') : '0.00');
        //$('#workerFlowType').val(data.result.workerFlowType+'');
        $('#docAttr').val(data.result.docAttr);
        $('#sourceDept').val(data.result.sourceDept);
        // if(data.result.isCollect == '1') {
        //   $(".isVistable").css('display','none')
        // }
        if(data.result.settlementModes==1){
            $("#settlementDiv").css("display","block")
            $("#e_settlementModes").attr("checked","checked");
            $("#e_settlementModes").val("1");
            $("#e_settlementInterval").val(data.result.settlementInterval);
            $("#e_settlementDay").val(data.result.settlementDay);
            $("#e_paymentDay").val(data.result.paymentDay);
        }else{
            $("#e_settlementModes").attr("checked",false);
            $("#settlementDiv").css("display","none");
            $("#e_settlementModes").val("2");
        }
// 展示附件
        let fileList = data.result.payrequestAccessoryVos;
        if (fileList && fileList.length != 0){
            let str = '';
            $(fileList).each( (index,item) => {
                str += `<div data-url="`+item.enclosureUrl+`" style="display: inline-block; padding: 10px 20px 10px 10px; position: relative;">
                                        <span style=" position: absolute;width: 15px;height: 15px;top: 0;right: 0;z-index: 1;cursor: pointer;" onclick="btn_delFile(this)"><img src="/proxy-sysmanage/static/images/close.png"></span>
                                        <a href='javascript:;' onclick='uploadFile(this)' data-url="`+item.enclosureUrl+`" data-name="`+item.enclosureName+`">`+item.enclosureName+`</a></div>`
                // <a href='`+item.enclosureUrl+`' target="_blank" download="`+item.enclosureName+`">`+item.enclosureName+`</a></div>`
            });
            $('.uploadFiles_div').append(str);
            $('.uploadFiles_div').css('display','block')
        }
        if (data.result.isPrepay == 'C02') {
            //应付
            $('.nav-tabs>li:eq(0)').removeClass('active');
            $('.nav-tabs>li:eq(1)').addClass('active');
            $('.nav-content>.panel-body:eq(0)').hide();
            $('.nav-content>.panel-body:eq(1)').show();
            tabsTrue[1] = true;
            loadInvoiceTab(data.result.billNo);
        } else {
            tabsTrue[0] = true;
            loadOrderTab(data.result.billNo);
        }
        if(data.result.supplierNo){
            getReportAmount(data.result.supplierNo,data.result.orgName,data.result.businessPayBalance);
        }
    });

    function getReportAmount(supplierNo,orgCode,businessPayBalance) {
        $("#sumInvoicedMoney").val("");
        //$("#businessPayBalance").val("");
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/findSupplierReportAmount",
            data: {
                supplierNo: supplierNo,
                orgCode:orgCode
            },
            dataType: 'json',
            cache: false,
        }).done(function (data) {
            console.log("获取累计发票金额:"+data);
            $("#sumInvoicedMoney").val(parseFloat(data.result.sumInvoicedMoney).formatMoney('2', '', ',', '.'));
            //$("#businessPayBalance").val(parseFloat(data.result.businessPayBalance).formatMoney('2', '', ',', '.'));
            $("#addUpPurchaseAmount").val(parseFloat(data.result.addUpPurchaseAmount).formatMoney('2', '', ',', '.'));
            getStorageReportAmount(supplierNo,orgCode,businessPayBalance);

        });
    }
    function getStorageReportAmount(supplierNo,orgCode,businessPayBalance) {
        $("#storageMoney").val("");
        $("#payableStorageScale").val("");
        $("#badStorageMoney").val("");

        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/findStorageReportAmount",
            data: {
                supplierNo: supplierNo,
                orgCode:orgCode,
                businessPayBalance:businessPayBalance
            },
            dataType: 'json',
            cache: false,
        }).done(function (data) {
            console.log("获取库存金额:"+data);
            $("#storageMoney").val(parseFloat(data.result.storageMoney).formatMoney('2', '', ',', '.'));
            //应付库存比=应付余额（采购）/库存金额*100%；不能整除的情况，百分比小数点后保留两位小数；库存金额为0时,显示 '-'
            if(data.result.storageMoney == '0'||data.result.storageMoney=='0.00' || data.result.storageMoney == ''){
                $("#payableStorageScale").val("-");
            }else{
                $("#payableStorageScale").val(data.result.payableStorageScale);
            }
            $("#badStorageMoney").val(parseFloat(data.result.badStorageMoney).formatMoney('2', '', ',', '.'));
        });
    }
    window.isFirst = true;

    //供应商查询
    $('#keyWord').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            $("#supplierNo").val(result.data);
            $("#keyWord").val(result.value).attr('oldvalue',result.value);
            $("#supplierName").val(result.value);
            $("#deliveryman").val(result.delivery);
            $("#telno").val(result.deliveryPhone);
            getBalance(result.data,result.id);
            window.isSelect = true;
            $('#searchOrderBtn').click();
            $('#searchInvoiceBtn').click();
            $("#accountName").val("");
            $("#bankAccount").val("");
            $("#bankName").val("");
            getPayableType(result.data, '#payableType')
            //  $("#searchOrderBtn").trigger("click");
            //  $("#searchInvoiceBtn").trigger("click");
            // return false;
            //选中回调
            // alert('You selected: ' + result.value + ', ' + result.data + ',' + result.name);
            console.log(result)
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName',
            delivery: 'delivery',
            deliveryPhone: 'deliveryPhone',
            id: 'id'
        },
        onNoneSelect: function (params, suggestions) {
            //没选中
            window.isSelect = false;
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    //放大镜查询
    $('#keyWord').on({
        dblclick: function (e) {
            supplierdDalog($("#keyWord").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#keyWord").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#keyWord").attr('oldvalue'))
    });

    //供应商双击查询
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=2',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    $('#keyWord').removeAttr('save');
                    var data = this.returnValue;
                    $("#supplierNo").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                    $("#deliveryman").val(data.delivery);
                    $("#telno").val(data.deliveryPhone);
                    $("#keyWord").val(data.supplierName);
                    getBalance(data.supplierCode,data.id);
                    $('#searchOrderBtn').click();
                    $('#searchInvoiceBtn').click();
                    $("#accountName").val("");
                    $("#bankAccount").val("");
                    $("#bankName").val("");
                    window.isSelect = true;
                    console.log(data)
                    //$("#input_custorm").val(data.customerName)
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }



    /**
     * 查询供应商余额
     * @param supplierNo
     */
    function getBalance(supplierNo,id) {
        getBankAmount(supplierNo);
        $("#e_settlementInterval").val("");
        $("#e_settlementDay").val("");
        $("#e_paymentDay").val("");
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/getSupplierBalance",
            data: {
                supplierNo: supplierNo,
                supplierId : id
            },
            dataType: 'json',
            cache: false,
        }).done(function (data) {
            console.log(data);
            $("#payableBalance").val(parseFloat(data.result.balance).formatMoney('2', '', ',', '.'));
            $("#businessPayBalance").val(parseFloat(data.result.businessPayBalance).formatMoney('2', '', ',', '.'));
            $("#e_lastPayTime").val(data.result.lastPayTime);
            if(data.result.settlementModes==1){
                $("#settlementDiv").css("display","block")
                $("#e_settlementModes").prop("checked",true);
                $("#e_settlementModes").val("1");
                $("#e_settlementInterval").val(data.result.settlementInterval);
                $("#e_settlementDay").val(data.result.settlementDay);
                $("#e_paymentDay").val(data.result.paymentDay);
            }else{
                $("#e_settlementModes").prop("checked",false);
                $("#settlementDiv").css("display","none");
                $("#e_settlementModes").val("2");
            }
            //显示提交按钮
            // $('#submitBtn').show();
        });
    }

    function getBankAmount(supplierNo) {
        //$("#accountName").val("");
        //$("#bankAccount").val("");
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/findSupplierBankAmount",
            data: {
                supplierNo: supplierNo
            },
            dataType: 'json',
            cache: false,
        }).done(function (data) {
            console.log(data);
            if(data.result.length){
                var ts = '<option value="">请选择</option>';
                for(var i = 0; i < data.result.length;i++){
                    ts += '<option data-bankName="'+data.result[i].bankName+'"+  value="'+data.result[i].accountName +'_'+data.result[i].bankAccount+'_'+data.result[i].bankName+'">'+data.result[i].bankName+' '+data.result[i].bankAccount+'</option>';
                }
                $("#sbankName").empty().append(ts);
                sbankName = ts
                $("#sbankName").val($("#accountName").val()+"_"+$("#bankAccount").val()+"_"+$("#bankName").val());
            }

        });
    }

    $("#sbankName").change(function(){
        var name_count = $(this).val();

        var tarr = name_count.split("_");
        console.log(tarr);
        $("#accountName").val(tarr[0]);
        $("#bankAccount").val(tarr[1]);
        $("#bankName").val(tarr[2]);

    })

    /////////////////////////////加载订单数据////////////////////////////////////////////////////////////
    function loadOrderTabNext(billNo) {
        var url;
        if (!billNo) {
            url = "/proxy-finance/finance/purchase/payrequestinfo/loadPurchaseOrder"
        } else {
            url = '/proxy-finance/finance/purchase/payrequestinfo/selectOrderByPayReqNo?reqNo=' + billNo
        }
        $('#X_Tablea').XGrid('setGridParam', {
            url: url,
            postData: {
                supplierNo: $("#supplierNo").val(),
                startTime: $("#o_beginTime").val(),
                endTime: $("#o_endTime").val(),
                billNo: $("#orderNo").val(),
                page: 1
            },
            rowList: [20, 50, 100] //分页条数下拉选择
        }).trigger('reloadGrid');
    }
    function loadOrderTab(billNo) {
        var colNames = ['隱藏列', '隐藏列', '单据类型', '单据编号','业务类型', '供应商编号', '供应商名称', '送货方式', '是否预付', '送货人', '送货人电话', '供应商仓库地址',
            '预计到货时间', '金额合计',
            '税额合计', '税价合计','本次申请金额','累计申请金额'
        ];
        var colModel = [
            {
                name: 'id',
                hidden: true
            },{
                name: 'isInit',
                hidden:true
            },
            {
                name: 'billType',
                index: 'billType'
            },
            {
                name: 'billNo',
                index: 'billNo'
            }, {
                name: 'channelName'
            },
            {
                name: 'supplierNo',
                index: 'supplierNo'
            },
            {
                name: 'supplierName',
                index: 'supplierName'
            },
            {
                name: 'deliveryMethod',
                index: 'deliveryMethod',
                formatter: function (val) {
                    if (val == "0") {
                        return "厢式送货";
                    } else if (val == "1") {
                        return "冷藏车";
                    } else if (val == "2") {
                        return "保温车";
                    } else if (val == "3") {
                        return "冷藏箱";
                    } else if (val == "4") {
                        return "其他封闭式车辆"
                    } else {
                        return ""
                    }
                },
                unformat: function (val) {
                    if (val == "厢式送货") {
                        return "0";
                    } else if (val == "冷藏车") {
                        return "1";
                    } else if (val == "保温车") {
                        return "2";
                    } else if (val == "冷藏箱") {
                        return "3";
                    } else if (val == "其他封闭式车辆") {
                        return "4"
                    }

                }
            }, {
                name: 'isPrepay',
                index: 'isPrepay',
                width: 250,
                sortable: false,
                editable: true,
                formatter: function (val) {
                    if (val == "1") {
                        return "是"
                    } else if (val == "0") {
                        return "否"
                    } else {
                        return "";
                    }
                },
                unformat: function (val) {
                    if (val == "是") {
                        return "1"
                    } else if (val == "否") {
                        return "0"
                    } else {
                        return "";
                    }
                }
            },
            {
                name: 'deliveryman',
                index: 'deliveryman'
            },
            {
                name: 'deliveryTel',
                index: 'deliveryTel'
            }, {
                name: 'storeAddress',
                index: 'storeAddress'
            }, {
                name: 'expectArrivalTime',
                index: 'expectArrivalTime',
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return '';
                    }
                }
            }, {
                name: 'amount',
                index: 'amount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'taxAmount',
                index: 'taxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'taxLimitAmount',
                index: 'taxLimitAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'thisTimeApplayAmount',
                rowtype: '#money_black',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                rowEvent: function (etype) {
                    console.log(etype);
                    var totals = getTotalMoney($('#X_Tablea'), 'thisTimeApplayAmount'),
                        totalsRow = getRowDataTotalMoney($('#X_Tablea'), 'thisTimeApplayAmount');
                    $('#X_Tablea').find("tr[nosele=true]").find("td[row-describedby='thisTimeApplayAmount']").text(parseFloat(totals).formatMoney('2', '', ',', '.'));
                    $('#applayAmount').val(parseFloat(totals).formatMoney('2', '', ',', '.'));
                    changeDeduAmount(parseFloat(totals));
                }
            }, {
                name: 'totalApplayAmount',
                index: 'totalApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'channelId',
                hidden: true,
                hidegrid: true
            }
        ];
        var url;
        if (!billNo) {

            url = "/proxy-finance/finance/purchase/payrequestinfo/loadPurchaseOrder"
        } else {
            url = '/proxy-finance/finance/purchase/payrequestinfo/selectOrderByPayReqNo?reqNo=' + billNo
        }
        $('#X_Tablea').XGrid({
            url: url,
            colNames: colNames,
            colModel: colModel,
            rowNum: 100,
            rownumbers: true,
            altRows: true,//设置为交替行表格,默认为false
            multiselect: true,
            attachRow:true,
            postData: {
                supplierNo: $("#supplierNo").val(),
                startTime: $("#o_beginTime").val(),
                endTime: $("#o_endTime").val(),
                billNo: $("#orderNo").val(),
            },
            //rownumbers:true,
            // selectandorder: true,
            onSelectRow: function (id, dom, obj, index, event) {
                //计算申请总额
                var totals = getTotalMoney($('#X_Tablea'), 'thisTimeApplayAmount'),
                    totalsRow = getRowDataTotalMoney($('#X_Tablea'), 'thisTimeApplayAmount');
                $('#X_Tablea').find("tr[nosele=true]").find("td[row-describedby='thisTimeApplayAmount']").text(parseFloat(totals).formatMoney('2', '', ',', '.'));
                $('#applayAmount').val(parseFloat(totals).formatMoney('2', '', ',', '.'));
                changeDeduAmount(parseFloat(totals))
                if ($('#' + id).attr('clickselrow')) {
                    var chindrenTaxLimitAmount = $('#' + id).find('[row-describedby="taxLimitAmount"]').text().replace(/,/g, '');
                    if (Number(chindrenTaxLimitAmount) >= 0) $(dom[0]).find('input[type="text"]').removeAttr('disabled');
                } else {
                    $(dom[0]).find('input[type="text"]').attr('disabled', 'disabled');
                }
            },
            ondblClickRow: function (id, dom, obj, index, event) {
                //console.log('双击行事件', obj);
                this.returnValue = obj;
                // console.log(obj)
                // window.location.href = "/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseOrderNO=" + obj.orderNo+"&supplierBaseId="+"supplierBaseId";
                //utils.openTabs('financePurchaseOrderDetail','采购订单详情','/proxy-finance/finance/purchase/payrequestinfo/toOrderDetail?purchaseOrderNo='+obj.billNo+'&type=1',{reload:false});
                if(obj.billNo) {
                    parent.parent.openTabs('financePurchaseOrderDetail', '采购订单详情', '/proxy-finance/finance/purchase/payrequestinfo/toOrderDetail?purchaseOrderNo=' + obj.billNo + '&type=1', {reload: false});
                }
                return obj;
            },
            pager: '#grid_pager1',
            gridComplete: function () {
                var _this = $(this);
                if (!billNo && !_this.XGrid('getRowData').length) {
                    utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
                    return false;
                }
                var data = $('#X_Tablea').XGrid('getRowData');
                var sum_models = ['amount','taxAmount','taxLimitAmount','thisTimeApplayAmount','totalApplayAmount'];
                var lastRowEle = $(this).find("tr[nosele=true]");
                lastRowEle.find("td:first-child").text('合计');
                sum_models.forEach(function (item,index) {
                    lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
                });
                if (window.isFirst) {
                    $('#X_Tablea input[type=checkbox]').prop('checked', true).trigger('input');
                    data.map(function (item) {
                        if (Number(item.taxLimitAmount) >= 0) {
                            $('#' + item.id).find('input[type="text"]').removeAttr('disabled');
                        }
                    });
                    window.isFirst = false;
                }
            }
        });

    }

    // 查询
    $('#searchOrderBtn').bind('click', function (param) {
        if ($('#supplierName').val() == '') {
            utils.dialog({content: '请选择供应商！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        $('#X_Tablea').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/payrequestinfo/loadPurchaseOrder',
            postData: {
                supplierNo: $("#supplierNo").val(),
                startTime: $("#o_beginTime").val(),
                endTime: $("#o_endTime").val(),
                billNo: $("#orderNo").val(),
                page: 1
            },
            rowList: [20, 50, 100] //分页条数下拉选择
        }).trigger('reloadGrid');
    })

//////////////////////////////////加载发票数据////////////////////////////////////////////////////////////////////
    function loadInvoiceTabNext(billNo) {
        var url;
        if (!billNo) {
            url = "/proxy-finance/finance/purchase/payrequestinfo/getPayreqInvoice";
        } else {
            url = '/proxy-finance/finance/purchase/payrequestinfo/selectInvoiceByPayReqNo?billNo=' + billNo;
        }
        $('#Y_Tablea').XGrid('setGridParam', {
            url: url,
            postData: {
                invoiceNo: $("#invoiceNo").val(),
                startTime: $("#beginTime").val(),
                endTime: $("#endTime").val(),
                supplierNo: $("#supplierNo").val()
            },
            rowList: [20, 50, 100] //分页条数下拉选择
        }).trigger('reloadGrid');
    }
    function loadInvoiceTab(billNo) {
        var colNames1 = ['隐藏列','隐藏列', 'ERP发票编码', '开票日期', '供应商编号', '供应商名称', '发票不含税金额合计', '发票税额合计', '发票价税合计', '未核销金额','本次申请金额','累计申请金额','最早可付款日期'];
        var colModel1 = [
            {
                name: 'id',
                hidden: true
            },{
                name: 'isInit',
                hidden:true
            },
            {
                name: 'invoiceNo'
            }, {
                name: 'invoiceCreateDate',
                index: 'invoiceCreateDate',
                formatter: function (value) {
                    if (!value) {
                        return '';
                    } else {
                        return utils.formatDate(value);
                    }
                }
            }, {
                name: 'supplierNo'
            }, {
                name: 'supplierName'
            }, {
                name: 'noTotalTaxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'totalInvoiceValue',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'totalInvoiceTax',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'unwriteoffMoney',
                index: 'unwriteoffMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'thisTimeApplayAmount',
                rowtype: '#money_black',
                rowEvent: function (etype) {
                    console.log(etype);
                    var totals = getTotalMoney($('#Y_Tablea'), 'thisTimeApplayAmount'),
                        totalsRow = getRowDataTotalMoney($('#Y_Tablea'), 'thisTimeApplayAmount');
                    $('#Y_Tablea').find("tr[nosele=true]").find("td[row-describedby='thisTimeApplayAmount']").text(parseFloat(totals).formatMoney('2', '', ',', '.'));
                    $('#applayAmount').val(parseFloat(totals).formatMoney('2', '', ',', '.'));
                    changeDeduAmount(parseFloat(totals));
                }
            }, {
                name: 'totalApplayAmount',
                index: 'totalApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'earliestPayTime',
                index: 'earliestPayTime'
            }];
        var url;
        if (!billNo) {
            url = "/proxy-finance/finance/purchase/payrequestinfo/getPayreqInvoice";
        } else {
            url = '/proxy-finance/finance/purchase/payrequestinfo/selectInvoiceByPayReqNo?billNo=' + billNo;
        }
        $('#Y_Tablea').XGrid({
            url: url,
            colNames: colNames1,
            colModel: colModel1,
            rowNum: 100,
            rownumbers: true,
            altRows: true, //设置为交替行表格,默认为false
            multiselect: true,
            pager: '#grid_pager2',
            attachRow:true,
            ondblClickRow: function (e, c, a, b) {
                console.log('双击行事件', e, c, a, b);
                var billNo = a.invoiceNo;
                var isInit = a.isInit;
                showDetail(billNo,isInit);
            },
            postData: {
                invoiceNo: $("#invoiceNo").val(),
                startTime: $("#beginTime").val(),
                endTime: $("#endTime").val(),
                supplierNo: $("#supplierNo").val()
            },
            onSelectRow: function (id, dom, obj, index, event) {
                //选中事件
                //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
                //  $('#basicInfo').JSONToform(obj);

                console.log(id, dom, obj, index, event)
                //计算申请总额
                var totals = getTotalMoney($('#Y_Tablea'), 'thisTimeApplayAmount'),
                    totalsRow = getRowDataTotalMoney($('#Y_Tablea'), 'thisTimeApplayAmount');
                $('#Y_Tablea').find("tr[nosele=true]").find("td[row-describedby='thisTimeApplayAmount']").text(parseFloat(totals).formatMoney('2', '', ',', '.'));
                $('#applayAmount').val(parseFloat(totals).formatMoney('2', '', ',', '.'));
                changeDeduAmount(parseFloat(totals));
                if ($('#' + id).attr('clickselrow')) {
                    var chindrenTaxLimitAmount = $('#' + id).find('[row-describedby="totalInvoiceTax"]').text().replace(/,/g, '');
                    if (Number(chindrenTaxLimitAmount) >= 0) $(dom[0]).find('input[type="text"]').removeAttr('disabled');
                } else {
                    $(dom[0]).find('input[type="text"]').attr('disabled', 'disabled');
                }
            },
            gridComplete: function () {
                queryIsGrayOfOrgForPaymentRequest().then((isGray)=>{
                    if(!isGray){
                        colModel1.forEach(colModelItem=>{
                            if(['earliestPayTime'].includes(colModelItem.name)){
                                colModelItem.hidden = true
                            }
                        })
                        $('#Y_Tablea').setGridParam({
                            colModel: colModel1
                        });
                    }
                })
                var _this = $(this);
                if (!billNo && !_this.XGrid('getRowData').length) {
                    utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
                    return false;
                }
                var data = $('#Y_Tablea').XGrid('getRowData');
                var sum_models = ['noTotalTaxAmount','totalInvoiceValue','totalInvoiceTax','unwriteoffMoney','thisTimeApplayAmount','totalApplayAmount'];
                var lastRowEle = $(this).find("tr[nosele=true]");
                lastRowEle.find("td:first-child").text('合计');
                sum_models.forEach(function (item,index) {
                    lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
                });
                if (window.isFirst) {
                    data.map(function (item) {
                        if (Number(item.totalInvoiceTax) >= 0) {
                            $('#' + item.id).find('input[type="text"]').removeAttr('disabled');
                        }
                    });
                    $('#Y_Tablea input[type=checkbox]').prop('checked', true).trigger('input');
                    window.isFirst = false;
                }
            },
        });

    }

    //查询是否灰度环境
    function queryIsGrayOfOrgForPaymentRequest(){
        return new Promise((resolve)=>{
            $.ajax({
                url: "/proxy-finance/finance/purchase/invoice/isGrayOfOrgForPaymentRequest",
                type: "get",
                dataType: 'json',
                success: function (result) {
                    if(result.code === 0){
                        resolve(true)//灰度中
                    }else{
                        resolve(false)//非灰度中
                    }
                },
                error: function(err){
                    resolve(false)//非灰度中
                }
            })
        })
    }

    // 查询
    $('#searchInvoiceBtn').bind('click', function (param) {
        if ($('#supplierName').val() == '') {
            utils.dialog({content: '请选择供应商！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        $('#Y_Tablea').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/payrequestinfo/getPayreqInvoice',
            postData: {
                invoiceNo: $("#invoiceNo").val(),
                startTime: $("#beginTime").val(),
                endTime: $("#endTime").val(),
                supplierNo: $("#supplierNo").val(),
                page: 1
            },
            rowList: [20, 50, 100] //分页条数下拉选择
        }).trigger('reloadGrid');
    })


    //合计
    function totalTable(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            var num = item[colName].replace(/,/g, '');
            count += parseFloat(num);
        })
        return count.toFixed(2);

    }

    //保存按钮绑定
    $("#saveBtn").click(function () {

        var tempArr = [],
            orderData = $('#X_Tablea').XGrid('getSeleRow'),
            invoiceData = $('#Y_Tablea').XGrid('getSeleRow');

        var tabIndex = $(".nav li").index($(".nav .active"));
        if (tabIndex == 1) {
            orderData = undefined;
        } else {
            invoiceData = undefined;
        }
        if (orderData && !orderData.length) {
            tempArr.push(orderData);
            orderData = tempArr;
        }

        if (invoiceData && !invoiceData.length) {
            tempArr.push(invoiceData);
            invoiceData = tempArr;
        }
        var fileArr = [];
        let allFiles = $('.uploadFiles_div>div');
        if(allFiles.length > 0){
            $(allFiles).each( (index,item ) => {
                let obj = {};
                obj.enclosureUrl = $(item).attr('data-url');
                obj.enclosureName = $(item).find('a').text();
                fileArr.push(obj)
            });
        }
        console.log(fileArr)
        console.log(orderData);

        if($('#Y_Tablea').find('[type="text"]').hasClass('activeRed')){
            utils.dialog({content: '请修改申请金额！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        //return false;
        var ttx = $("#basicInfo").serializeToJSON();
        ttx.applayAmount = ttx.applayAmount.replace(/,/g, '');
        ttx.payableBalance = ttx.payableBalance.replace(/,/g, '');
        ttx.businessPayBalance = ttx.businessPayBalance.replace(/,/g, '');
        ttx.settlementModes =$("#e_settlementModes").val();
        ttx.actualPaymentAmount = ttx.actualPaymentAmount.replace(/,/g, '');
        ttx.prepaymentAvailableAmount = ttx.prepaymentAvailableAmount.replace(/,/g, '');
        ttx.prepaymentAmountDeduction = ttx.prepaymentAmountDeduction.replace(/,/g, '');
        ttx.otherDeductions = ttx.otherDeductions.replace(/,/g, '');
        ttx.ticketDeduction = ttx.ticketDeduction.replace(/,/g, '');
        if (ttx.applayAmount < 0) {
            utils.dialog({content: '申请金额不能为负数！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#payableType').val() == '') {
            utils.dialog({content: '请选择支付方式！', quickClose: true, timeout: 2000}).show();
            return false;
        }

        if ($('#expectPayTime').val() == '') {
            utils.dialog({content: '请选择期望支付日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#bankName').val() == '') {
            utils.dialog({content: '开户银行不能为空！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        var data = $("#myform").serializeToJSON();
        // var obj = $("#basicInfo").serializeToJSON();
        var objDemand = $.extend({}, data, ttx);

        $('#X_Tablea').on('change', 'input[type="checkbox"]', function () {
            if ($(this).prop('checked')) {
                var rowId = $(this).closest('tr').attr('id');
                var obj = $('#X_Tablea').XGrid('getRowData', rowId);
                $('#basicInfo').JSONToform(obj);
            }
        })
        var flag;
        if ($("#isPrepay").val() == "C01") {
            var flag = orderData;
        } else {
            flag = invoiceData;
        }

        if (!flag) {
            utils.dialog({content: '请选择订单或发票数据！', quickClose: true, timeout: 2000}).show();
            return false;
        }

        if(typeof(invoiceData) != "undefined"){
            if(invoiceData || invoiceData.length>0){
                $.ajax({
                    method: "POST",
                    async:true,
                    url: "/proxy-finance/finance/purchase/payrequestinfo/checkInvoiceNo",
                    data: {
                        "payrequestInvoiceDetailVoData": JSON.stringify(invoiceData),
                    },
                    dataType: 'json',
                    cache: false,
                }).done(function (data) {
                    if (data.code == 1) {
                        utils.dialog({content: data.result+"已经核销，不允许创建付款申请", quickClose: true, timeout: 2000}).show();
                        return false;
                    }else{
                        savePayRequest(orderData,invoiceData,objDemand,fileArr)
                    }

                });
            }
        }else{
            var tipsNum = 1;
            if (orderData) {
                if (orderData && orderData.length && orderData.length > 0) {
                    orderData.map(function (item) {
                        item.thisTimeApplayAmount = item.thisTimeApplayAmount.replace(/,/g, '');
                        item.taxLimitAmount = item.taxLimitAmount.replace(/,/g, '');
                        if (item.taxLimitAmount >0 && item.thisTimeApplayAmount <=0 ) {
                            $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                            utils.dialog({content: '申请金额不能小于等于0！', quickClose: true, timeout: 2000}).show();
                            tipsNum++;
                        }
                    })
                } else if (orderData.thisTimeApplayAmount) {
                    orderData.thisTimeApplayAmount = orderData.thisTimeApplayAmount.replace(/,/g, '');
                    orderData.taxLimitAmount = orderData.taxLimitAmount.replace(/,/g, '');
                    if (orderData.taxLimitAmount > 0 && orderData.thisTimeApplayAmount <=0 ) {
                        $('#' + orderData.id).find('[type="text"]').addClass('activeRed').val('');
                        utils.dialog({content: '申请金额不能小于等于0！', quickClose: true, timeout: 2000}).show();
                        tipsNum++;
                    }
                }
                if (tipsNum === 1) savePayRequest(orderData,invoiceData,objDemand,fileArr);
            }


        }

    })


    // 关键词查询
    $('#searchSupplierBtn').bind('click', function () {
        if (!window.isSelect) {
            utils.dialog({content: '查询无数据！', quickClose: true, timeout: 2000}).show();
            $('#X_Tablea').XGrid('clearGridData');
            $('#Y_Tablea').XGrid('clearGridData');
            $("#supplierNo").val('');
            $("#supplierName").val('');
            $("#keyWord").val('');
            $("#deliveryman").val('');
            $("#telno").val('');
            return false;
        }
        if ($("#isPrepay").val() === "C01") {
            $('#X_Tablea').XGrid('clearGridData');
            $("#searchOrderBtn").trigger("click");
        } else {
            $('#Y_Tablea').XGrid('clearGridData');
            $("#searchInvoiceBtn").trigger("click");
        }
    });

    //提交
    $("#submitBtn").click(function () {
        var tempArr = [],
            orderData = $('#X_Tablea').XGrid('getSeleRow'),
            invoiceData = $('#Y_Tablea').XGrid('getSeleRow');

        var tabIndex = $(".nav li").index($(".nav .active"));
        if (tabIndex == 1) {
            orderData = undefined;
        } else {
            invoiceData = undefined;
        }
        if (orderData && !orderData.length) {
            tempArr.push(orderData);
            orderData = tempArr;
        }

        if (invoiceData && !invoiceData.length) {
            tempArr.push(invoiceData);
            invoiceData = tempArr;
        }
        if(orderData) {
            var isStop = submitTabled('X_Tablea');
            if(isStop) {
                if(typeof isStop =='string'&&isStop == 'stop'){
                    return
                }else if(isStop instanceof Array &&isStop.includes('stop')){
                    return
                }
            }
        }
        if(invoiceData) {
            var isStop = submitTabled('Y_Tablea');
            if(isStop) {
                if(typeof isStop =='string'&&isStop == 'stop'){
                    return
                }else if(isStop instanceof Array &&isStop.includes('stop')){
                    return
                }
            }
        }

        var fileArr = [];
        let allFiles = $('.uploadFiles_div>div');
        if(allFiles.length > 0){
            $(allFiles).each( (index,item ) => {
                let obj = {};
                obj.enclosureUrl = $(item).attr('data-url');
                obj.enclosureName = $(item).find('a').text();
                fileArr.push(obj)
            });
        }
        console.log(fileArr)
        console.log(orderData);
        $('#X_Tablea').on('change', 'input[type="checkbox"]', function () {
            if ($(this).prop('checked')) {
                var rowId = $(this).closest('tr').attr('id');
                var obj = $('#X_Tablea').XGrid('getRowData', rowId);
                $('#basicInfo').JSONToform(obj);
            }
        })

        //return false;
        var ttx = $("#basicInfo").serializeToJSON();
        var data = $("#myform").serializeToJSON();
        ttx.applayAmount = ttx.applayAmount.replace(/,/g, '');
        ttx.payableBalance = ttx.payableBalance.replace(/,/g, '');
        ttx.businessPayBalance = ttx.businessPayBalance.replace(/,/g, '');
        ttx.settlementModes =$("#e_settlementModes").val();
        ttx.actualPaymentAmount = ttx.actualPaymentAmount.replace(/,/g, '');
        ttx.prepaymentAvailableAmount = ttx.prepaymentAvailableAmount.replace(/,/g, '');
        ttx.prepaymentAmountDeduction = ttx.prepaymentAmountDeduction.replace(/,/g, '');
        ttx.otherDeductions = ttx.otherDeductions.replace(/,/g, '');
        ttx.ticketDeduction = ttx.ticketDeduction.replace(/,/g, '');
        if (ttx.applayAmount < 0) {
            utils.dialog({content: '申请金额不能小于0！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#payableType').val() == '') {
            utils.dialog({content: '请选择支付方式！', quickClose: true, timeout: 2000}).show();
            return false;
        }

        if ($('#expectPayTime').val() == '') {
            utils.dialog({content: '请选择期望支付日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#bankName').val() == '') {
            utils.dialog({content: '开户银行不能为空！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        // var obj = $("#basicInfo").serializeToJSON();
        var objDemand = $.extend({}, data, ttx);
        var flag;
        if ($("#isPrepay").val() == "C01") {
            var flag = orderData;
        } else {
            flag = invoiceData;
        }
        if (!flag) {
            utils.dialog({content: '请选择订单或发票数据！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        // parent.showLoading();
        console.log(invoiceData, orderData);
        $('#submitBtn').attr('disabled',"true");
        if(typeof(invoiceData) != "undefined"){
            if(invoiceData || invoiceData.length>0){
                $.ajax({
                    method: "POST",
                    async:true,
                    url: "/proxy-finance/finance/purchase/payrequestinfo/checkInvoiceNo",
                    data: {
                        "payrequestInvoiceDetailVoData": JSON.stringify(invoiceData),
                    },
                    dataType: 'json',
                    cache: false,
                }).done(function (data) {
                    if (data.code == 1) {
                        utils.dialog({content: data.result+"已经核销，不允许创建付款申请", quickClose: true, timeout: 2000}).show();
                        $('#submitBtn').removeAttr("disabled");
                        return false;
                    }else{
                        submitPayRequest(orderData,invoiceData,objDemand,fileArr)
                    }

                });
            }
        }else{
            var tipsNum = 1;
            if (orderData) {
                if (orderData && orderData.length && orderData.length > 0) {
                    orderData.map(function (item) {
                        item.thisTimeApplayAmount = item.thisTimeApplayAmount.replace(/,/g, '');
                        item.taxLimitAmount = item.taxLimitAmount.replace(/,/g, '');
                        if (item.taxLimitAmount >0 && item.thisTimeApplayAmount <=0 ) {
                            $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                            utils.dialog({content: '申请金额不能小于等于0！', quickClose: true, timeout: 2000}).show();
                            tipsNum++;
                        }
                    })
                } else if (orderData.thisTimeApplayAmount) {
                    orderData.thisTimeApplayAmount = orderData.thisTimeApplayAmount.replace(/,/g, '');
                    orderData.taxLimitAmount = orderData.taxLimitAmount.replace(/,/g, '');
                    if (orderData.taxLimitAmount > 0 && orderData.thisTimeApplayAmount <=0 ) {
                        $('#' + orderData.id).find('[type="text"]').addClass('activeRed').val('');
                        utils.dialog({content: '申请金额不能小于等于0！', quickClose: true, timeout: 2000}).show();
                        tipsNum++;
                    }
                }
            }
            tipsNum === 1 ? submitPayRequest(orderData,invoiceData,objDemand,fileArr) : $('#submitBtn').removeAttr("disabled");
        }
        // parent.hideLoading();

        /*$.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/submitPayrequest",
            data: {
                "PayrequestOrderDetailVoData": JSON.stringify(orderData),
                "PayrequestInvoiceDetailVoData": JSON.stringify(invoiceData),
                "PurchasePayrequestInfoVo": JSON.stringify(objDemand)
            },
            dataType: 'json',
            cache: false
        }).done(function (data) {
            if (data.code == 0) {
                utils.dialog({content: '提交成功', quickClose: true, timeout: 2000}).show();
                setTimeout(function () {
                    var dialog = parent.dialog.get(window);
                    dialog.close();
                }, 500);
            } else {
                $('#submitBtn').removeAttr("disabled");
                utils.dialog({content: data.result.msg, quickClose: true, timeout: 2000}).show();
            }
        });*/
    });
    function submitTabled(id) {
        var selectRow = $('#'+id).XGrid('getSeleRow');
        var nums = {
            'X_Tablea': {
                'end': 'taxLimitAmount',
                'total': 'taxLimitAmount'
            },
            'Y_Tablea': {
                'end': 'totalInvoiceTax',
                'total': 'totalInvoiceTax'
            }
        }
        if (selectRow) {
            if (selectRow.length > 0) {
                var stopList = selectRow.map(function (item) {
                    if (Number(item[nums[id].end]) >= 0 && Number(item.thisTimeApplayAmount) === 0) {
                        $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                        utils.dialog({content: '申请金额不能等于0,不能为空', quickClose: true, timeout: 3000}).show();
                        return 'stop';
                    }
                    if (Number(item[nums[id].end]) >= 0) {
                        if (Number(item.thisTimeApplayAmount) < 0) {
                            $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                            utils.dialog({content: '申请金额不能小于0', quickClose: true, timeout: 3000}).show();
                            return 'stop';
                        } else {
                            let isAmount = Number(item.thisTimeApplayAmount) + Number(item.totalApplayAmount) > Number(item[nums[id].total]);
                            if (isAmount) {
                                $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                                utils.dialog({content: '金额不符合要求<br/>金额+累计申请金额不能大于价税合计', quickClose: true, timeout: 3000}).show();
                                return 'stop';
                            } else {
                                $('#' + item.id).find('[type="text"]').removeClass('activeRed')
                            }
                        }
                    }
                })
                return stopList
            } else {
                if (Number(selectRow[nums[id].end]) >= 0) {
                    if (Number(selectRow[nums[id].end]) > 0 && Number(selectRow.thisTimeApplayAmount) === 0) {
                        $('#' + selectRow.id).find('[type="text"]').addClass('activeRed').val('');
                        utils.dialog({content: '申请金额不能等于0,不能为空', quickClose: true, timeout: 3000}).show();
                        return 'stop';
                    }
                    if (Number(selectRow.thisTimeApplayAmount) < 0) {
                        $('#' + selectRow.id).find('[type="text"]').addClass('activeRed').val('');
                        utils.dialog({content: '申请金额不能小于0', quickClose: true, timeout: 3000}).show();
                        return 'stop';
                    } else {
                        let isAmount = Number(selectRow.thisTimeApplayAmount) + Number(selectRow.totalApplayAmount) > Number(selectRow[nums[id].total]);
                        if (isAmount) {
                            $('#' + selectRow.id).find('[type="text"]').addClass('activeRed').val('');
                            utils.dialog({content: '金额不符合要求<br/>金额+累计申请金额不能大于价税合计', quickClose: true, timeout: 3000}).show();
                            return 'stop';
                        } else {
                            $('#' + selectRow.id).find('[type="text"]').removeClass('activeRed')
                        }
                    }
                }
            }
        }
    }
    // 返回
    var dialog = parent.dialog.get(window);
    $('#return').bind('click', function () {
        utils.dialog({
            title: '提示',
            content: '返回后数据将丢失，是否继续？',
            width: 200,
            okValue: '确认',
            ok: function () {
                // 关闭弹窗
                dialog.close();
            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).showModal();
        return false;

    })


    //计算申请总额
    var tableList = ['X_Tablea', 'Y_Tablea'];
    var typeListCheckbox = ['taxLimitAmount', 'totalInvoiceTax'];
    $('.nav-content').on('change', '#grid_checked input[type="checkbox"]', function () {
        var tabIndex = $(".nav li").index($(".nav .active"));
        if ($(this).prop('checked')) {
            checkboxGrid(tableList[tabIndex], typeListCheckbox[tabIndex]);
        } else {
            $('#' + tableList[tabIndex]).find('input[type="text"]').attr('disabled', 'disabled');
        }
        var orderTotal = getTotalMoney($('#X_Tablea'), 'thisTimeApplayAmount'),
            billTotal = getTotalMoney($('#Y_Tablea'), 'thisTimeApplayAmount'),
            result = parseFloat(orderTotal) + parseFloat(billTotal);
        $('#applayAmount').val(result.formatMoney('2', '', ',', '.'));
        changeDeduAmount(result);
    });
    $('.nav-content').on('change', ':not("#grid_checked") input[type="checkbox"]', function () {
        var tabIndex = $(".nav li").index($(".nav .active"));
        if ($(this).prop('checked')) {
            var nums = $(this).parent().siblings('td[row-describedby='+ typeListCheckbox[tabIndex] +']').text().replace(/,/g, '');
            if (Number(nums) >=0 ) $(this).parent().siblings('td[row-describedby="thisTimeApplayAmount"]').find('input[type="text"]').removeAttr('disabled')
        } else {
            $('#' + tableList[tabIndex]).find('input[type="text"]').attr('disabled', 'disabled')
        }
        var orderTotal = getTotalMoney($('#X_Tablea'), 'thisTimeApplayAmount'),
            billTotal = getTotalMoney($('#Y_Tablea'), 'thisTimeApplayAmount'),
            result = parseFloat(orderTotal) + parseFloat(billTotal);
        $('#applayAmount').val(result.formatMoney('2', '', ',', '.'));
        changeDeduAmount(result);
    });
    // 按钮的全选
    function checkboxGrid($table, tabIndex) {
        var selData = $('#' + $table).XGrid('getRowData');
        if (selData) {
            if (selData.length) {
                $.each(selData, function (index, item) {
                    if (item[tabIndex] > 0) $('#' + item.id).find('input[type="text"]').removeAttr('disabled');
                });
            }
        }
    }

    function getTotalMoney($table, colName) {
        var result = 0,
            selData = $table.XGrid('getSeleRow');
        if (selData) {
            if (selData.length) {
                $.each(selData, function (index, item) {
                    var itemNum = item[colName].replace(/,/g, '');
                    result += parseFloat(itemNum);
                })
                return result;
            } else {
                var itemNum = selData[colName].replace(/,/g, '');
                return itemNum;
            }
        } else {
            return 0;
        }
    }
    function getRowDataTotalMoney($table, colName) {
        var result = 0,
            selData = $table.XGrid('getRowData');
        if (selData) {
            if (selData.length) {
                $.each(selData, function (index, item) {
                    result += parseFloat(item[colName]);
                })
                return result;
            } else {
                return selData[colName];
            }
        } else {
            return 0;
        }
    }
    function showDetail(billNo,isInit) {
        var title = "";
        var url = "";
        var height = 0;
        title = "采购发票详情";
        url = '/proxy-finance/finance/purchase/payrequestinfo/detailInvoiceInfo?invoiceNo=' + billNo+'&isInit='+isInit;
        if(isInit == '1'){
            utils.dialog({
                title: title,
                url: url,
                width: $(window).width() * 0.8,
                data: 'val值', // 给modal 要传递的 的数据
                onclose: function () {
                    $('iframe').remove();
                }
            }).show();
        }else {
            utils.dialog({
                title: title,
                url: url,
                width: $(window).width() * 0.8,
                height: $(window).height() * 0.8,
                data: 'val值', // 给modal 要传递的 的数据
                onclose: function () {
                    $('iframe').remove();
                }
            }).show();
        }

    }

    function savePayRequest(orderData,invoiceData,objDemand,fileArr){
        if(!objDemand.payableType){
            utils.dialog({
                content: "请选择支付方式", quickClose: true,
                timeout: 2000
            }).showModal();
            return
        }
        if(objDemand.ticketDeduction == ''){
            objDemand.ticketDeduction = "0.00"
        }
        if(objDemand.otherDeductions == ''){
            objDemand.otherDeductions = "0.00"
        }
        var actualPaymentAmount = !isNaN(parseFloat(objDemand.actualPaymentAmount)) ? 0 : parseFloat(objDemand.actualPaymentAmount);
        var prepaymentAmountDeduction = !isNaN(parseFloat(objDemand.prepaymentAmountDeduction)) ? 0 : parseFloat(objDemand.prepaymentAmountDeduction);
        var ticketDeduction = !isNaN(parseFloat(objDemand.ticketDeduction)) ? 0 : parseFloat(objDemand.ticketDeduction);
        var otherDeductions = !isNaN(parseFloat(objDemand.otherDeductions)) ? 0 : parseFloat(objDemand.otherDeductions);
        var applayAmount = !isNaN(parseFloat(objDemand.applayAmount)) ? 0 : parseFloat(objDemand.applayAmount);
        if(add(add(add(actualPaymentAmount,prepaymentAmountDeduction),ticketDeduction),otherDeductions) != applayAmount){
            utils.dialog({
                content: "申请总额-本次预付余额抵扣-票折抵扣-其他抵扣必须大于等于0.00，请修改票折抵扣/其他抵扣", quickClose: true,
                timeout: 2000
            }).showModal();
            $('#submitBtn').removeAttr("disabled");
            return
        }

        if(!objDemand.accountName || !objDemand.bankName || !objDemand.bankAccount) {
            utils.dialog({
                content: "请重新选择开户银行", quickClose: true,
                timeout: 2000
            }).showModal();
            return
        }
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/editPayrequest",
            data: {
                "PayrequestOrderDetailVoData": JSON.stringify(orderData),
                "PayrequestInvoiceDetailVoData": JSON.stringify(invoiceData),
                "PurchasePayrequestInfoVo": JSON.stringify(objDemand),
                "payrequestAccessoryData":JSON.stringify(fileArr),
            },
            dataType: 'json',
            cache: false,
        }).done(function (data) {
            if (data.code == 0) {
                utils.dialog({
                    content: "保存成功", quickClose: true,
                    timeout: 2000
                }).showModal();
            } else {
                utils.dialog({
                    content: data.result, quickClose: true,
                    timeout: 2000
                }).showModal();
            }
        });
    }

    function submitPayRequest(orderData,invoiceData,objDemand,fileArr){
        if(!objDemand.payableType){
            utils.dialog({
                content: "请选择支付方式", quickClose: true,
                timeout: 2000
            }).showModal();
            $('#submitBtn').removeAttr("disabled");
            return
        }
        if(objDemand.ticketDeduction == ''){
            objDemand.ticketDeduction = "0.00"
        }
        if(objDemand.otherDeductions == ''){
            objDemand.otherDeductions = "0.00"
        }
        var actualPaymentAmount = !isNaN(parseFloat(objDemand.actualPaymentAmount)) ? 0 : parseFloat(objDemand.actualPaymentAmount);
        var prepaymentAmountDeduction = !isNaN(parseFloat(objDemand.prepaymentAmountDeduction)) ? 0 : parseFloat(objDemand.prepaymentAmountDeduction);
        var ticketDeduction = !isNaN(parseFloat(objDemand.ticketDeduction)) ? 0 : parseFloat(objDemand.ticketDeduction);
        var otherDeductions = !isNaN(parseFloat(objDemand.otherDeductions)) ? 0 : parseFloat(objDemand.otherDeductions);
        var applayAmount = !isNaN(parseFloat(objDemand.applayAmount)) ? 0 : parseFloat(objDemand.applayAmount);
        if(add(add(add(actualPaymentAmount,prepaymentAmountDeduction),ticketDeduction),otherDeductions) != applayAmount){
            utils.dialog({
                content: "申请总额-本次预付余额抵扣-票折抵扣-其他抵扣必须大于等于0.00，请修改票折抵扣/其他抵扣", quickClose: true,
                timeout: 2000
            }).showModal();
            $('#submitBtn').removeAttr("disabled");
            return
        }
        if(!objDemand.accountName || !objDemand.bankName || !objDemand.bankAccount) {
            utils.dialog({
                content: "请重新选择开户银行", quickClose: true,
                timeout: 2000
            }).showModal();
            $('#submitBtn').removeAttr("disabled");
            return
        }

        let allFiles = $('.uploadFiles_div>div');
        if($("#isPrepay").val() === "C01" && allFiles.length == 0){

            var htmls = '<div>'
                + '\n 申请预付款必须上传我司已盖章的预付款合同，请添加合同附件！\n' +'<br/>'
                + '\n 注意：若预付款合同暂未走完盖章流程，可提供线下特批截图，地采-省区邮件特批截图，集采-集采负责人特批截图 \n';
            htmls +=
                '</div>';

            utils.dialog({
                title: "提示",
                content: '<div style="max-height: 100px;overflow-y: auto;font-size:120%;">' + htmls + '</div>',
                height: 100,
                okValue: '确定',
                ok: function () {
                }
            }).showModal();
            $('#submitBtn').removeAttr("disabled");
            return;
        }

        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/submitPayrequest",
            data: {
                "PayrequestOrderDetailVoData": JSON.stringify(orderData),
                "PayrequestInvoiceDetailVoData": JSON.stringify(invoiceData),
                "PurchasePayrequestInfoVo": JSON.stringify(objDemand),
                "payrequestAccessoryData":JSON.stringify(fileArr),
            },
            dataType: 'json',
            cache: false
        }).done(function (data) {
            if (data.code == 0) {
                utils.dialog({content: data.result.msg, quickClose: true, timeout: 2000}).show();
                setTimeout(function () {
                    var dialog = parent.dialog.get(window);
                    dialog.close();
                }, 500);
            } else if (data.code == 2){
                utils.dialog({
                    title: '票折抵扣/其他抵扣金额校验',
                    content: data.result,
                    okValue: '确定',
                    ok: function () {
                        // 重置预付可用余额
                        // getAdvancePaymentAccount($('#supplierNo').val());
                        changeDeduAmount(objDemand.applayAmount);
                    }
                }).showModal();
            } else {
                $('#submitBtn').removeAttr("disabled");
                utils.dialog({content: data.result.msg, quickClose: true, timeout: 2000}).show();
            }
        });
    }

    function checkInvoiceNo(invoiceData){
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/checkInvoiceNo",
            data: {
                "payrequestInvoiceDetailVoData": JSON.stringify(invoiceData),
            },
            dataType: 'json',
            cache: false,
        }).done(function (data) {
            if (data.code == 1) {
                utils.dialog({content: data.result+"", quickClose: true, timeout: 2000}).show();
                return false;
            }else{

            }

        });
    }


//期望支付日期
    $('#expectPayTime').bind('click', function () {
        WdatePicker({
            minDate: getNowDate()
        })
    })

    function getNowDate() {
        return moment().format('YYYY-MM-DD');
    }
    // 申请金额校验
    $('#Y_Tablea, #X_Tablea').on('click',"input[type='text']",function (e) {
        e.preventDefault();
        e.stopPropagation();
    });
    $('#Y_Tablea').on('blur',"input[type='text']", function () {
        if (isNaN(Number($(this).val()))){
            $(this).addClass('activeRed').val('');
            utils.dialog({content: '金额不符合要求', quickClose: true, timeout: 3000}).show();
            return false;
        }
        return blurTabled('Y_Tablea');
    });
    $('#X_Tablea').on('blur',"input[type='text']", function () {
        if (isNaN(Number($(this).val()))){
            $(this).addClass('activeRed').val('');
            utils.dialog({content: '金额不符合要求', quickClose: true, timeout: 3000}).show();
            return false;
        }
        return blurTabled('X_Tablea');
    });
    function blurTabled(id) {
        var selectRow = $('#'+id).XGrid('getSeleRow');
        console.log(selectRow)
        var nums = {
            'X_Tablea': {
                'end': 'taxLimitAmount',
                'total': 'taxLimitAmount'
            },
            'Y_Tablea': {
                'end': 'totalInvoiceTax',
                'total': 'totalInvoiceTax'
            }
        }
        if (selectRow) {
            if (selectRow.length > 0) {
                selectRow.map(function (item) {
                    console.log(item);
                    if (Number(item[nums[id].end]) > 0 && Number(item.thisTimeApplayAmount) === 0) {
                        $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                        utils.dialog({content: '申请金额不能等于0,不能为空', quickClose: true, timeout: 3000}).show();
                        return ;
                    }
                    if (Number(item[nums[id].end]) > 0) {
                        if (Number(item.thisTimeApplayAmount) < 0) {
                            $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                            utils.dialog({content: '申请金额不能小于0', quickClose: true, timeout: 3000}).show();
                        } else {
                            let isAmount = Number(item.thisTimeApplayAmount) + Number(item.totalApplayAmount) > Number(item[nums[id].total]);
                            if (isAmount) {
                                $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                                utils.dialog({content: '金额不符合要求<br/>金额+累计申请金额不能大于价税合计', quickClose: true, timeout: 3000}).show();
                            } else {
                                $('#' + item.id).find('[type="text"]').removeClass('activeRed')
                            }
                        }
                    }
                    // else {
                    //     let isAmount = Math.abs(Number(item.thisTimeApplayAmount) + Number(item.totalApplayAmount)) > Math.abs(Number(item[nums[id].total]));
                    //     if (isAmount) {
                    //         $('#' + item.id).find('[type="text"]').addClass('activeRed').val('');
                    //         utils.dialog({content: '金额不符合要求<br/>金额+累计申请金额绝对值不能大于价税合计的绝对值', quickClose: true, timeout: 3000}).show();
                    //     } else {
                    //         $('#' + item.id).find('[type="text"]').removeClass('activeRed')
                    //     }
                    // }
                })
            } else {
                if (Number(selectRow[nums[id].end]) >= 0) {
                    if (Number(selectRow[nums[id].end]) > 0 && Number(selectRow.thisTimeApplayAmount) === 0) {
                        $('#' + selectRow.id).find('[type="text"]').addClass('activeRed').val('');
                        utils.dialog({content: '申请金额不能等于0,不能为空', quickClose: true, timeout: 3000}).show();
                        return ;
                    }
                    if (Number(selectRow.thisTimeApplayAmount) < 0) {
                        $('#' + selectRow.id).find('[type="text"]').addClass('activeRed').val('');
                        utils.dialog({content: '申请金额不能小于0', quickClose: true, timeout: 3000}).show();
                    } else {
                        let isAmount = Number(selectRow.thisTimeApplayAmount) + Number(selectRow.totalApplayAmount) > Number(selectRow[nums[id].total]);
                        if (isAmount) {
                            $('#' + selectRow.id).find('[type="text"]').addClass('activeRed').val('');
                            utils.dialog({content: '金额不符合要求<br/>金额+累计申请金额不能大于价税合计', quickClose: true, timeout: 3000}).show();
                        } else {
                            $('#' + selectRow.id).find('[type="text"]').removeClass('activeRed')
                        }
                    }
                }
                // else {
                //     let isAmount = Math.abs(Number(selectRow.thisTimeApplayAmount) + Number(selectRow.totalApplayAmount)) > Math.abs(Number(selectRow.taxLimitAmount));
                //     if (isAmount) {
                //         $('#' + selectRow.id).find('[type="text"]').addClass('activeRed').val('');
                //         utils.dialog({content: '金额不符合要求<br/>金额+累计申请金额绝对值不能大于价税合计的绝对值', quickClose: true, timeout: 3000}).show();
                //     } else {
                //         $('#' + selectRow.id).find('[type="text"]').removeClass('activeRed')
                //     }
                // }
            }
        }
    }

    $("#upload_btn").on('click', function () {
        let len = $('.uploadFiles_div>div').length;
        if(len<10){
            utils.dialog({
                title: '上传附件',
                width: 700,
                height:120,
                overflow: 'hidden',
                content: $("#upload_dialog").css({height: '120px', 'overflow-y': 'hidden'}),
                okValue: '确定',
                cancelValue: '取消',
                onshow: function () {
                    $("").appendTo($("#previewBox").show().find("ul"));

                    $("#previewBox").find("li").remove();
                    $("#btnUpload").removeAttr("disabled");
                    //  valAutocomplete("/drug/Report/Detatil/queryBatchNumByCode",{paramName:'productBatchNo',params:{"productCode":$("input[name='productCode']").val(),"startTime":$("input[name='startDate']").val(),"endTime":$("input[name='endDate']").val()}},"batch",{data:"batchNum",value:"batchNum"});

                },
                ok: function () {
                    UploadFileFn(function (list, filename) {
                        //previewUpload(list, filename);
                    });
                    //保存按钮回调
                    //   return submitDrugTestReport();
                },
                cancel: function () {
                },
                onclose: function () {

                },
            }).showModal();
        }else{
            utils.dialog({
                title: '提示',
                content: '最多支持传10个附件。',
                okValue: '确定',
                ok: function () {}
            }).showModal();
            return false;
        }
    });

    var prevUploadFileName = "";//上一次上传的文件名
    $("#btnUpload").on("change", function () {
        $('#filePath').val($(this).val())
        var fileName = this.files[0].name;
        var fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLocaleLowerCase();
        if(imageType.indexOf(fileType) == -1){
            utils.dialog({content: "请上传正确格式的文件！如图片：jpg、png、以及pdf。", quickClose: false, timeout: 2000}).showModal();
            return false;
        }
        var maxsize = 5 * 1024 * 1024; //5M
        var size = this.files[0].size; //图片大小
        if (size > maxsize) {
//                alert('图片过大，单张图片大小不超过2M');
            utils.dialog({content: "文件过大，文件大小不能超过5M", quickClose: false, timeout: 2000}).showModal();
            $("#btnUpload, #filePath").val('');
            return false;
        }

        /* if($(".upload-file-box").find("li").length >= 15){
             utils.dialog({content: "最多只能上传15张附件", quickClose: false, timeout: 2000}).showModal();
             return false;
         }*/

        //是否选择了文件
        if (!fileName) {
            utils.dialog({
                title: "提示",
                width: 200,
                content: "请选择文件",
                okValue: "确定",
                ok: function () {
                }
            }).showModal();
            return false;
        }

        // UploadFileFn(function (list, filename) {
        //     previewUpload(list, filename);
        // });
        // }
    });


    function UploadFileFn(cb) {
        var file = $("#btnUpload")[0].files[0];
        var fileName = file.name;
        var fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLocaleLowerCase();
        if(imageType.indexOf(fileType) == -1){
            utils.dialog({content: "请上传正确格式的文件！如图片：jpg、png、以及pdf。", quickClose: false, timeout: 2000}).showModal();
            return false;
        }
        var maxsize = 5 * 1024 * 1024; //5M
        var size = file.size; //图片大小
        if (size > maxsize) {
//                alert('图片过大，单张图片大小不超过2M');
            utils.dialog({content: "文件过大，文件大小不能超过5M", quickClose: false, timeout: 2000}).showModal();

            return false;
        }
        if (!file) {
            utils.dialog({
                title: "提示",
                width: 200,
                content: "请选择文件",
                okValue: "确定",
                ok: function () {
                }
            }).showModal();
            return false;
        }

        var formData = new FormData();
        formData.append("files", file);
        formData.append("name", file.name);
        // formData.files = file
        // formData.name = file.name

        var loading = utils.dialog({
            title: '上传中',
            fixed: true,
            width: 200,
            quickClose: false,
            cancel: false
        }).showModal();

        $.ajax({
            url: '/proxy-finance/finance/purchase/payrequestinfo/upload',
            type: 'POST',
            async: false,
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function () {
                console.log("正在上传");
            },
            success: function (data) {
                loading.close();
                if (data.code == 0) {
                    prevUploadFileName = file.name;
                    cb(data.result[0], prevUploadFileName);
                    utils.dialog({content: "上传成功", quickClose: false, timeout: 2000}).showModal();

                } else if (data.code == 1) {
                    utils.dialog({content: "上传成功", quickClose: false, timeout: 2000}).showModal();
                } else {
                    utils.dialog({content: "上传失败", quickClose: true, timeout: 2000}).showModal();
                }
                // 展示附件
                let fileList = data.result;
                if (fileList && fileList.length != 0){
                    let str = '';
                    $(fileList).each( (index,item) => {
                        str += `<div data-url="`+item.enclosureUrl+`" style="display: inline-block; padding: 10px 20px 10px 10px; position: relative;">
                                        <span style=" position: absolute;width: 15px;height: 15px;top: 0;right: 0;z-index: 1;cursor: pointer;" onclick="btn_delFile(this)"><img src="/proxy-sysmanage/static/images/close.png"></span>
                                        <a href='javascript:;' onclick='uploadFile(this)' data-url="`+item.enclosureUrl+`" data-name="`+item.enclosureName+`">`+item.enclosureName+`</a></div>`
                        // <a href='`+item.enclosureUrl+`' target="_blank" download="`+item.enclosureName+`">`+item.enclosureName+`</a></div>`
                    });
                    $('.uploadFiles_div').append(str);
                    $('.uploadFiles_div').css('display','block')
                }
            }, error: function () {
                loading.close();
                utils.dialog({content: "上传失败", quickClose: true, timeout: 2000}).showModal();
            }, complete: function () {
                $("#btnUpload").val("");
                $('#filePath').val("");
            }
        });
    }

    $('body').on('input', '.zeroto100',function () {
        let v = $(this).val();
        v = v.replace(/[^\d.]/g, '')
        $(this).val(v);
        if(v.indexOf('-') >= 0){
            v = v.replace(/-/g,'');
            $(this).val(v);
        }
        if(v.indexOf('.') == 0){
            v = v.replace('.','');
            $(this).val(v);
        }
        if(v.indexOf('0') == 0){
            if (v.length == 1) { // 0000000
                $(this).val('0');
            } else {
                if(v.indexOf('0.') == 0){
                } else {
                    $(this).val(v.substring(1));
                }
            }
        }
        if(v.split('.').length > 2){
            let  lastIndex = v.lastIndexOf('.')
            v = v.substring(0,lastIndex) + v.substring(lastIndex + 1)
            $(this).val(v);
        }
        if (Number(v) < 0)  $(this).val(0);
        if (Number(v) > 100)  $(this).val(100);
    })

    function getAdvancePaymentAccount(supplierCode) {
        var param = {"supplierNo":supplierCode};
        $.ajax({
            data: JSON.stringify(param),
            method: "POST",
            url: "/proxy-finance/finance/purchase/supplierAdvancePayment/queryAccountInfo",
            dataType: 'json',
            contentType:"application/json",
            cache: false,
        }).done(function (data) {
            if(data){
                // advancePaymentAccount.prepaymentAmount = data.prepaymentAmount;
                // advancePaymentAccount.occupyAmount = data.occupyAmount;
                advancePaymentAccount.availableAmount = data.availableAmount;
                advancePaymentAccount.boolNoDeductionSupplier= data.boolNoDeductionSupplier;
            }
            // $('#prepaymentAmount').val(advancePaymentAccount.prepaymentAmount.formatMoney('2', '', ',', '.'));
            // $('#occupyAmount').val(advancePaymentAccount.occupyAmount.formatMoney('2', '', ',', '.'));
            $('#prepaymentAvailableAmount').val(advancePaymentAccount.availableAmount.formatMoney('2', '', ',', '.'));
            // if(prepaymentAmountDeduction)
            var applayAmount = $("#applayAmount").val();
            applayAmount = (applayAmount== '' ? 0: Number(applayAmount.replace(/,/g, '')));
            changeDeduAmount(applayAmount);
        })
    }

    function changeDeduAmount(applyAmount){
        // getAdvancePaymentAccount(supplierCodeStatic);
        console.log("金额变化了");
        var zero = 0;
        var deductAmount=0;
        var ticketDeduction = $("#ticketDeduction").val();
        var otherDeductions = $("#otherDeductions").val();
        ticketDeduction = (ticketDeduction== '' ? 0: Number(ticketDeduction.replace(/,/g, '')));
        otherDeductions = (otherDeductions== '' ? 0: Number(otherDeductions.replace(/,/g, '')));
        console.log(ticketDeduction);
        console.log(otherDeductions);
        $("#actualPaymentAmount").val($('#applayAmount').val());
        $("#prepaymentAmountDeduction").val(zero.formatMoney('2', '', ',', '.'));
        //如果是不抵扣供应商 抵扣金额=0
        if (advancePaymentAccount.boolNoDeductionSupplier) {
            deductAmount=0;
            //如果是正常供应商
        }else {
            //预付可用余额>0
            if(advancePaymentAccount.availableAmount > 0){
                if(advancePaymentAccount.availableAmount >=  applyAmount){
                    //可用 >= 申请 抵扣金额 = 申请
                    deductAmount = applyAmount;
                }else{
                    //可用 < 申请 抵扣金额 = 可用
                    deductAmount = advancePaymentAccount.availableAmount
                }
                //预付可用余额<=0,则“本次预付款余额抵扣”=0
            }else {
                deductAmount=0;
            }
        }
        // 最后校验：票折抵扣+其他抵扣<=申请总额-本次预付余额抵扣
        var paymentAmount = dcmSub(applyAmount, deductAmount);
        console.log(paymentAmount);

        // 申请总额-本次预付余额抵扣-票折抵扣-其他抵扣
        // var s = dcmSub(applyAmount, deeuctAmount);
        $("#actualPaymentAmount").val(dcmSub(paymentAmount,(ticketDeduction+otherDeductions)).formatMoney('2', '', ',', '.'));
        $('#prepaymentAmountDeduction').val(deductAmount.formatMoney('2', '', ',', '.'))
        $("#ticketDeduction").removeClass('activeRed');
        $("#otherDeductions").removeClass('activeRed');

        if (paymentAmount < (ticketDeduction+otherDeductions)){
            utils.dialog({content: '票折抵扣+其他抵扣必须小于等于申请总额-本次预付余额抵扣。', quickClose: true, timeout: 3000}).show();
            $("#ticketDeduction").addClass('activeRed').val('');
            $("#otherDeductions").addClass('activeRed').val('');
            return;
        }
    }


    function dcmSub(arg1,arg2){
        var r1,r2,m;
        try{r1=arg1.toString().split(".")[1].length;}catch(e){r1=0;}
        try{r2=arg2.toString().split(".")[1].length;}catch(e){r2=0;}
        m=Math.pow(10,Math.max(r1,r2));
        return (accMul(arg1,m)-accMul(arg2,m))/m;
    }

    function accMul(arg1,arg2){
        var m=0,s1=arg1.toString(),s2=arg2.toString();
        try{m+=s1.split(".")[1].length}catch(e){}
        try{m+=s2.split(".")[1].length}catch(e){}
        return Number(s1.replace(".",""))*Number(s2.replace(".",""))/Math.pow(10,m)
    }

    function add(value1, value2) {
        var r1, r2, m;
        try {
            r1 = value1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = value2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        m = Math.pow(10, Math.max(r1, r2));
        return (value1 * m + value2 * m) / m;
    }

    // 预览
    $("#preview_btn").on('click', function (){
        var fileArrPreview = [];
        let allFiles = $('.uploadFiles_div>div');
        if(allFiles.length > 0) {
            $(allFiles).each(function(index, item) {
                let obj = {};
                obj.url = $(item).attr('data-url');
                obj.name = $(item).find('a').text();
                fileArrPreview.push(obj)
            });
            $.viewImg({
                fileParam:{
                    name:'name',
                    url:'url'
                },
                list:fileArrPreview
            })
        }
    })

    $('#ticketDeduction').blur(function () {
        // 确认是否有申请总额
        var applayAmount = $('#applayAmount').val()
        console.log(applayAmount);
        if (applayAmount == ''){
            utils.dialog({content: '请先确认本次申请总额', quickClose: true, timeout: 3000}).show();
            $(this).addClass('activeRed').val('');
            return;
        }
        applayAmount = applayAmount.replace(/,/g, '');
        // 校验 非数字格式不校验
        var money = Number($(this).val());
        if (isNaN(money)){
            // 非数字格式看是否是金额展示格式
            money = Number($(this).val().replace(/,/g, ''));
            // 如果还不是，提示重新输入
            if (isNaN(money)){
                utils.dialog({content: '请输入正确金额', quickClose: true, timeout: 3000}).show();
                $(this).addClass('activeRed').val('');
                return;
            }
        }
        if(money< 0){
            utils.dialog({content: '仅可输入大于0.00的金额值', quickClose: true, timeout: 3000}).show();
            $(this).addClass('activeRed').val('');
            return;
        }else if(money > 999999999.99){
            utils.dialog({content: '仅可输入小于999999999.99的金额值', quickClose: true, timeout: 3000}).show();
            $(this).addClass('activeRed').val('');
            return;
        }
        $('#ticketDeduction').val(money.formatMoney('2', '', ',', '.'));
        // 修改完成，调用changeDeduAmount 进行校验并计算出本次实付金额
        changeDeduAmount(applayAmount);
    });

    $('#otherDeductions').blur(function () {
        // 确认是否有申请总额
        var applayAmount = $('#applayAmount').val()
        console.log(applayAmount);
        if (applayAmount == ''){
            utils.dialog({content: '请先确认本次申请总额', quickClose: true, timeout: 3000}).show();
            $(this).addClass('activeRed').val('');
            return;
        }
        applayAmount = applayAmount.replace(/,/g, '');
        // 校验 非数字格式不校验
        var money = Number($(this).val());
        if (isNaN(money)){
            // 非数字格式看是否是金额展示格式
            money = Number($(this).val().replace(/,/g, ''));
            // 如果还不是，提示重新输入
            if (isNaN(money)){
                utils.dialog({content: '请输入正确金额', quickClose: true, timeout: 3000}).show();
                $(this).addClass('activeRed').val('');
                return;
            }
        }
        if(money< 0){
            utils.dialog({content: '仅可输入大于0.00的金额值', quickClose: true, timeout: 3000}).show();
            $(this).addClass('activeRed').val('');
            return;
        }else if(money > 999999999.99){
            utils.dialog({content: '仅可输入小于999999999.99的金额值', quickClose: true, timeout: 3000}).show();
            $(this).addClass('activeRed').val('');
            return;
        }
        $('#otherDeductions').val(money.formatMoney('2', '', ',', '.'));
        // 修改完成，调用changeDeduAmount 进行校验并计算出本次实付金额
        changeDeduAmount(applayAmount);
    });
})


function btn_delFile(el) {
    utils.dialog({
        title: "提示",
        width: 200,
        content: "确定要删除此文件?",
        okValue: "确定",
        ok: function () {
            let len = $(el).parents('.uploadFiles_div').find('div').length;
            $(el).parent().remove();
            if((len-1) == 0){
                $('.uploadFiles_div').css('display','none')
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();


}
// 下载附件
function uploadFile(that) {
    var a=[];
    var urls = $(that).attr('data-url');
    var names = $(that).attr('data-name');
    // var fileArr = [];
    // let allFiles = $('.uploadFiles_div>div');
    // if(allFiles.length > 0) {
    //     $(allFiles).each(function(index, item) {
    //         let obj = {};
    //         obj.url = $(item).attr('data-url');
    //         obj.name = $(item).find('a').text();
    //         fileArr.push(obj)
    //     });
    //
    // }
    //
    // $.viewImg({
    //     fileParam:{
    //         name:'name',
    //         url:'url'
    //     },
    //     list:fileArr
    // })
    downloadImg(urls,names)
    // downloadImg(urls,names)
    //  a.push('<form style="display: none" method="post">');
    //  a.push('<input name="file" value = ' + urls + '>');
    //  a.push('<input name="name" value = '+ names + '>');
    //  a.push('</form>');
    //
    //  var $eleForm = $(a.join(''));
    // // $eleForm.attr("action", "/proxy-finance/finance/purchase/payrequestinfo/download");
    //  $eleForm.attr("action", urls);
    //  $eleForm.attr("target","");
    //  $eleForm.attr("method","get");//设置请求类型
    //  $(document.body).append($eleForm);
    //  // 提交表单，实现下载
    //  $eleForm.submit();
    //  $eleForm.remove();
}
function downloadImg(src,name){
    var x=new XMLHttpRequest();
    //禁止浏览器缓存；否则会报跨域的错误
    x.open("GET", src+'?t='+new Date().getTime(), true);
    x.responseType = 'blob';
    x.onload=function(e){
        var url = window.URL.createObjectURL(x.response)
        var a = document.createElement('a');
        a.href = url
        a.download = name
        a.click()
    }
    x.send();
}
// 支付方式 查詢回显
function getPayableType(supplierCode, tagNode, selVal,bankInfo) {
    let otherObj = {}
    $.ajax({
        url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierPayType?supplierCode='+ supplierCode,
        type: 'get',
        success: (res) => {
            console.log('res', res);
            let _Str = '<option value="">请选择</option>'
            if (res.code == 0) {
                res.result.payTypeVOList.forEach(item => _Str += `<option value="${item.code}">${item.name}</option>`)
                otherObj = {
                    acceptanceRebate: bankInfo ? bankInfo.acceptanceRebate: res.result.acceptanceRebate,
                    bankTransferRebate: bankInfo ? bankInfo.bankTransferRebate:  res.result.bankTransferRebate,
                    // acceptanceRebate:  res.result.acceptanceRebate,
                    // bankTransferRebate: res.result.bankTransferRebate,
                    accountName: res.result.accountName,
                    bankAccount: res.result.bankAccount,
                    bankName: res.result.bankName
                }
            }
            $(tagNode).empty().html(_Str)
            $(tagNode).attr('data-otherObj', JSON.stringify(otherObj))
            $('#payableType').val(selVal)
            payableTypeChange('#payableType', 0)
        },
        error: (err) => {
            console.log('err', err);
        }
    })
}
//  支付方式切换
function payableTypeChange(el,type) {
    console.log('type', type);
    $('#accept_transfer_Rebate').css('display', ($(el).val() == '112' || $(el).val() == '114' || $(el).val() == '115') ? 'block' : 'none')
    const otherObj = JSON.parse($(el).attr('data-otherObj'))
    if ($(el).val() == '112' || $(el).val() == '114' || $(el).val() == '115') {
        $('#sbankName').empty().html(`<option value="">请选择</option><option value="${otherObj.accountName+"_"+otherObj.bankAccount+"_"+otherObj.bankName}" ${type == 0 ? 'selected' : ''} >${otherObj.bankName}</option>`)
        $('#accountName').val(type == 0 ? otherObj.accountName : '')
        $('#bankAccount').val( type == 0 ? otherObj.bankAccount : '')
        $('#acceptRebate').val( type == 0 ? otherObj.acceptanceRebate : '')
        $('#transferRebate').val( type == 0 ? otherObj.bankTransferRebate : '')
    } else {
        if (type == 1) {
            $('#sbankName').empty().html(sbankName)
            $('#accountName').val('')
            $('#bankAccount').val('')
            $('#acceptRebate').val('')
            $('#transferRebate').val('')
        } else {
            // $('#sbankName').empty().html(sbankName)
            // // $('#accountName').val('')
            // // $('#bankAccount').val('')
            // $("#sbankName").change()
            // $('#acceptRebate').val('')
            // $('#transferRebate').val('')
        }

    }
}
// //是否集采切换是否OEM
// $("#isCollect").on('change',function() {
//     let num = $("#isCollect").val()
//     if(num == 1) {
//         $(".isVistable").css('display','none')
//         $('#workerFlowType').val('0')
//     }else {
//         $('#workerFlowType').val('0')
//         $(".isVistable").css('display','block')
//     }
// })
// 发起部门切换
$("#sourceDept").on('change', function () {
    console.log('sourceDept change2')
    let num = $("#sourceDept").val()
    if (num == 1) { // 当发起部门为省区，则订单属性可选地采、集采-统谈分采、集采-统谈统采；默认值是地采；
        $(".isSecond").css('display','inherit')
        $(".isThird").css('display','inherit')
        $(".isFirst").css('display','none')
        $('#docAttr').val('1')
    }  else if (num == 2) { // 当发起部门为集采中心，则订单属性是集采-统谈统采；
        $(".isFirst").css('display','inherit')
        $(".isThird").css('display','inherit')
        $(".isSecond").css('display','none')
        $('#docAttr').val('3')
    } else if (num == 3) { // 当发起部门为OEM，则订单属性为OEM；
        $(".isFirst").css('display','inherit')
        $(".isSecond").css('display','inherit')
        $(".isThird").css('display','none')
        $('#docAttr').val('5')
    } else { // 全部都情况
        $(".isFirst").css('display','inherit')
        $(".isSecond").css('display','inherit')
        $(".isThird").css('display','inherit')
        $('#docAttr').val('')
    }
})