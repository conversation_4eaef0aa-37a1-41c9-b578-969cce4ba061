var parent_dialog = parent.dialog.get(window);
// // var parent_dialog = parent.dialog.get(window.dialog);
var keyword=parent_dialog.data;
let supportMultiple = parent_dialog.data.supportMultiple,
    orgCode = parent_dialog.data.orgCode,
    productCode = parent_dialog.data.productCode; //  业务类型列表是否支持多选，  1： 支持  0： 不支持
status = ''
$(function () {
    $("#searchChannel_btn").attr('data-status',status)
    let options = {
        url:'/common/channel/listPage',
        postData: {
            status
        },
        colNames: ['业务类型编码', '业务类型','类别','是否启用'],
        colModel: [
            {
                name: 'channelCode',
                index: 'channelCode',
                hidden:true,
                width: 50
            },
            {
                name: 'channelName',
                index: 'channelName',
                width: 60
            },
            {
                name: 'parentCode',
                index: 'parentCode',
                width: 60,
                formatter: function (e) {
                    if (e == '1') {
                        return '药帮忙'
                    }else if (e == '2') {
                        return '宜块钱'
                    }else if (e == '3') {
                        return '控销'
                    }
                },
                unformat: function (e) {
                    if (e == '药帮忙') {
                        return '1';
                    } else if (e == '宜块钱') {
                        return '2';
                    } else if (e == '控销') {
                        return '3';
                    }
                }
            },
            {
                name: 'status',
                index: 'status',
                hidden:true,
                width: 50
            }
        ],
        key: 'channelCode',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#channel-pager',
    }
    supportMultiple == '1'? options['selectandorder'] = true : options['rownumbers'] = true;
    // let channelDiaNode = dialog.get('channelDia').node;
    $('#channel_table').XGrid(options);


    //查询
    $("#searchChannel_btn").on("click", function () {
        $('#channel_table').XGrid('setGridParam', {
            postData: {
                "parentCode": $('#channelType option:selected').val(),
                "channelName":$("#channelName").val().trim(),
                "status": $(this).attr('data-status')
            },page:1
        }).trigger('reloadGrid');
    });

    // //选择
    $("#chooseChannel_btn").on("click", function () {
        let mult = $(this).attr('data-mult');
        let finalRow = [];
        let channelType = $('#channelType option:selected').val();
        let selRow = $('#channel_table').XGrid('getSeleRow');
        /* begin   需求临时变动  注掉下面ifelse  增加临时代码*/
        // if (channelType == '' || channelType == '3') {
        //     if (mult == '1') { // 多选
        //         finalRow = selRow.length == 0 ?  $('#channel_table').XGrid('getRowData') : selRow;
        //     }else{
        //         finalRow = selRow;
        //     }
        // }else{ // 药帮忙 宜块钱
        //     finalRow = selRow;
        // }
        finalRow = selRow; // 临时代码
        /*end  需求临时变动， 需求改回去时，临时代码删掉 */
        //
        // if (finalRow.length == 0){
        //     utils.dialog({content: '请先选中数据.', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }
        /*  需求变动，临时注掉 */
        // if (finalRow.length == $('#channel_table').XGrid('getRowData').length){
        //     if (channelType == '' || channelType == '3') {
        //         finalRow = [
        //             {
        //                 channelName: channelType == '' ? '全部' : '控销全部',
        //                 channelCode: channelType == '' ? '99999' : '88888'
        //             }
        //         ]
        //     }
        // }
        parent_dialog.close(finalRow).remove();
    })
})
