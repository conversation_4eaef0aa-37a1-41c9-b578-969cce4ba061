$(function () {


    $("#setDayNum").on("click", function () {
        let dayNum = $("#dayNum").val();
        let orgCode = $("#orgCode").val();
        $.ajax({
            type: 'post',
            url: '/proxy-customer/customer/customerWarning/customerWarningDayNumSet',
            data: {
                dayNum,
                orgCode
            },
            success: function (res) {
                if (res.code == 0){
                    utils.dialog({content: res.result, quickClose: true,timeout: 3000}).showModal();
                }else{
                    utils.dialog({content: res.msg, quickClose: true,timeout: 3000}).showModal();
                    return
                }
            }
        })
    });
})
