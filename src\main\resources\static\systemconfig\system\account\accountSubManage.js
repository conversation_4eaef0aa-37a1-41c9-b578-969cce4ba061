 var queryUserListUrl="/proxy-sysmanage/sysmanage/system/queryUserListByItAdmin";    
    //初始化查询
    $(function () {
        //角色表
        $('#X_Table').XGrid({
            url:queryUserListUrl,
            postData:[],
            colNames: ['', '账号', '姓名', '手机', '邮箱', '职位', '角色', '部门', '状态'],
            colModel: [
            	 {
                     name: 'id',
                     index: 'id',//索引。其和后台交互的参数为sidx
                     key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                     width: 100,
                     hidden:true,
                     hidegrid: true
                 }, 
                {
                    name: 'account',
                    index: 'account'
                },
                {
                    name: 'userName',
                    index: 'userName'
                }, {
                    name: 'mobile',
                    index: 'mobile'
                }, {
                    name: 'email',
                    index: 'email'
                }, {
                    name: 'postName',
                    index: 'postName'
                }, {
                    name: 'roleNames',
                    index: 'roleNames'
                }, {
                    name: 'dptName',
                    index: 'dptName'
                }, {
                    name: 'userStatusName',
                    index: 'userStatusName'
                }
            ],
            rowNum: 20,
            rowList:[20,50,100],
            altRows: true, //设置为交替行表格,默认为false,
            rownumbers: true,
            key: 'id',            
            ondblClickRow: function (e, c, a, b) {
                console.log('双击行事件', e, c, a, b);
            },
            onSelectRow: function (e, c, a, b) {
                console.log('单机行事件', e, c, a, b);
            },
            pager: '#grid-pager'
        });        

    });  


  //查询数据，重置data
    $('#seachData').on('click', function () {
        searchData();
    });
    
    //验证新建用户，需要的数据
    function saveCheck(){
        var result={passed:false};
        var userId=$("#userId").val();        
        var erpOrgListArr = $("#add_erp_orgList").val();      

        if(!$.isArray(erpOrgListArr)){
            showTips("请选择机构！");
            return result;
        } else if(erpOrgListArr.length < 1){
            showTips("请选择机构！");
            return result;
        }
        var erpOrgListParam = "";
        $(erpOrgListArr).each(function(i,item){
        	erpOrgListParam += item + ",";
        });

        result.passed = true;
        result.data={
        	userId: userId, orgCodes: erpOrgListParam
        }

        return result;
    }
 
    // 打开弹窗
    function saveData(){
        var titleStr="编辑";
        utils.dialog({
            title: titleStr + '账号',
            content: $('.container'),
            okValue: '确定',
            ok: function () {
                //验证需要添加的数据
                var result = saveCheck();
                if(!result.passed){
                    return false;
                }
                var b = false;               
                $.ajax({
                    url: "/proxy-sysmanage/sysmanage/system/saveUserOrg",
                    type: "post",
                    async: false,
                    data: result.data,
                    success: function(res){
                        if(res.code == 0){
                            b = true;                            
                            showTips(titleStr + "账号成功！");                            
                            searchData();
                        }else if(res.code == 100){
                            b=false;
                            showTips(res.msg);
                        }
                        else{
                            showDialog("提示", res.msg);
                            b = false;
                        }
                    }
                });
                return b;
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    }
    
    // 页面编辑
    function editData(obj){
        var _this = $(obj);
        var userId = _this.closest('tr').attr('id');

       // resetForm();
        console.log("-----editData--userId="+userId);

        $.ajax({
            type:"get",
            url:'/proxy-sysmanage/sysmanage/system/queryUserById',
            dataType: "json",
            data:{userId:userId},
            success: function(res) {
                if(res&&res.code==0){
                    var resultStr=JSON.stringify(res);
                    var user=res.result;
                    console.log(user.id);
                    //赋值
                    $("#userId").val(user.id);
                    $("#add_name").val(user.userName);
                    $("#add_mobile").val(user.mobile);
                    $("#add_email").val(user.email);
                    $("#add_account").val(user.account);                    
                    $("#add_employeeNumber").val(user.employeeNumber);
                    $("#add_dptName").val(user.dptName);
                    $("#add_postName").val(user.postName);
                    $("#add_roleNames").val(user.roleNames);
                    $("#add_account").attr("disabled",true);
                    if(user.employeeNumber != null && user.employeeNumber != "") {
                        $("#add_employeeNumber").attr("disabled",true);
                    }
                    if(user.orgCode){
                        $("#add_erp_orgList").val(user.orgCode);
                        $("#add_erp_orgList").attr("disabled",true);

                    }else{
                        $("#add_erp_orgList").val("-1");
                    }
                    $("#channelCode").val(user.channelCode);
                    $("#channelName").val(user.channelName); 
                    
                    
                    
                    $(".bigCustomerFlag").each(function() { 
                    	var bigCustomerFlag = $(this).val(); 
                    	if(bigCustomerFlag == user.bigCustomerFlag) {	                			
                			$(this).prop("checked", true);            			
                		}   	
                    });  
                    queryOrgList(user.erpOrgCodes);
                    saveData();
                }else{
                    showTips("查询用户信息失败！");
                }
            }
        });
    }

    //验证查询参数
    function searchCheck(){
        var result={passed:false};
        var searchName=$("#searchName").val();
        var searchMobile=$("#searchMobile").val();
        var searchUserStatus = $("#searchStatusList").children('option:selected').val();
        var searchRoleId=$("#roleList").children('option:selected').val();
        var searchPost=$("#query_postList").val();
        var deptCodeVal = $("#deptList").children('option:selected').val(); 

        if(searchUserStatus ==""){
            searchUserStatus = "-1";
        }

        if(searchRoleId == ""){
            searchRoleId = "-1";
        }
             
        if(deptCodeVal==""){
        	deptCodesStr = "-1";
        }

        result.passed=true;
        
        result.data={userName:searchName,mobile:searchMobile,
            userStatus:searchUserStatus,
            deptCode:deptCodeVal,
            roleId:searchRoleId,postCode:searchPost};

        return result;
    }

    function searchData(){
        var result = searchCheck();
        if(!result.passed){
            return;
        }

        $("#X_Table").setGridParam({
            url:queryUserListUrl,
            postData: result.data
        }).trigger('reloadGrid');
    }  
    
    // 新增||编辑页面&筛选条件-选择机构
    // flag: 1查询、2新增
    function orgChangeNew(obj,user,flag){                 
        var deptCode = $("#deptList").children('option:selected').val();   
        if(!deptCode){
        	deptCode="";
        }        
          
       // 联动岗位
       quertPostListByOrgCode('', deptCode, flag);
       // 联动角色
//        queryRoleListByOrgCode('', deptCode, flag);
       
    }
    
  //添加||编辑-机构联动部门
    function quertDeptListByOrgCode(orgCodesStr,deptCode,flag){
        $.ajax({
            url:"/proxy-sysmanage/sysmanage/dept/quertDeptListByOrgCode",
            data:{orgCode:orgCodesStr},
            type:"get",
            success:function(data){
                var dptoptions = "<option value='-1'>--请选择--</option>";
                if(data.code==0){
                    if(data.result){
                        $.each(data.result, function(i, dpt){
                            dptoptions+="<option value='"+dpt.dptCode+"'>"+dpt.dptName+"</option>";
                        });
                    }
                    if(flag==1){
                        $("#dptList").html(dptoptions);
                        if(deptCode!=""&&deptCode!="-1"){
                            $("#dptList").val(deptCode);
                        }
                    }
                }else{
                    console.log("quertDeptListByOrgCode-->获取数据失败！");
                    if(flag==1){
                        $("#dptList").html(dptoptions);
                    }else if(flag==2){
                        $("#add_dptList").html(dptoptions);
                    }
                }
            }
        });
    }
  
    function quertPostListByOrgCode(orgCodesStr,deptCode,flag){
    	  $.ajax({
              url:"/proxy-sysmanage/sysmanage/post/queryPostListByDept",
              type: "get",
              data:{deptCode:deptCode},
              success:function(res){
                  var postoptions = "<option value='-1'>--请选择--</option>";
                  if(res&&res.code == 0){
                      if(res.result){
                          $.each(res.result, function(i, post){
                              postoptions+="<option value='"+post.postCode+"'>"+post.postName+"</option>";
                          });
                      }
                  }
                  $("#query_postList").html(postoptions);                  
              }
          });
    }

    //查询条件 选择机构联动角色
    function queryRoleListByOrgCode(orgCode, deptCode, flag){
//     	console.log("deptCode=" + deptCode);
        $.ajax({
            url:"/proxy-sysmanage/sysmanage/role/queryRoleListByParam",
            data:{orgCode:orgCode, deptCode:deptCode},
            dataType: 'json',
    		type: 'post',
            success:function(data){
                var roleoptions = "<option value='-1'>--请选择--</option>";
                if(data.code==0){
                    if(flag == 1) {
                        if(data.result){
                            $.each(data.result, function(i, role){
                                roleoptions+="<option value='"+role.id+"'>"+role.name+"</option>";
                            });
                            $("#roleList").html(roleoptions);
                        }
                    }
                }else{
                    console.log("queryRoleListByOrgCode-->获取数据失败！");
                }
            }
        });
    }
    
     /* 导出 */
    $('#exportData').on('click', function () {
        var tableId = "X_Table";
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });
            var len = Number($('#grid-pager #totalPageNum').text());
            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                /*  if (!data.length) {
                      data = [data];
                  }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                len = data.length;
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            var dptCodeVal = $("#deptList").children('option:selected').val();          
            var dptCodeStr=dptCodeVal;            
            
            var postCodeVal = $("#query_postList").children('option:selected').val();
            var postCodeStr=postCodeVal;            
            
            if(postCodeStr==""){
            	postCodeStr="-1";
            }
            
            var roleCodeVal = $("#roleList").children('option:selected').val();          
            var roleCodeStr=roleCodeVal;            
            
            if(roleCodeStr==""){
            	roleCodeStr="-1";
            }
            
            var userStatusVal = $("#searchStatusList").children('option:selected').val();          
            var userStatusStr=userStatusVal;            
            
            if(userStatusStr==""){
            	userStatusStr="-1";
            }
            
            var obj = {
            		userName:$("#searchName").val(),
            		mobile:$("#searchMobile").val(),
            		userStatus:userStatusStr,
            		postCode:postCodeStr,
            		roleId: roleCodeStr,
            		deptCode: dptCodeStr,
                	selectData: data,
                	colNames: colName,
               		colNameDesc: colNameDesc
            }
           // obj["nameModel"] = nameModel;
         // 是否超出限制    
		    utils.exportAstrictHandle('X_Table', len, 1).then( () => {
		        return false;
		    }).catch( () => {
		    	httpPost("/proxy-sysmanage/sysmanage/system/exportUserListByItAdminList", obj);
		    });
        });
    });
    
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    
    //查询条件 选择机构
    function queryOrgList(orgCodes){
        $.ajax({
            url:"/proxy-sysmanage/sysmanage/org/getOrgList",
            data:{},
            type:"get",
            success:function(data){
                var roleoptions = "<option value='-1'>--请选择--</option>";
                if(data.code==0){                    
                    //角色
                    var add_orgList_val = $("#add_erp_orgList");
                    if(data.result){
                        var htm = '';
                        $.each(data.result, function(i, org){
                            htm += "<option value='"+org.orgCode+"'>"+ org.orgName + "</option>";
                        });
                        add_orgList_val.html("").append(htm);
                        console.log(orgCodes);
                        if(orgCodes) {
                            $('#add_erp_orgList').val(orgCodes.split(","));
                        }
                    }
                    $('#add_erp_orgList').selectpicker('refresh');                    
                }else{
                    console.log("queryRoleListByOrgCode-->获取数据失败！");
                }
            }
        });
    }
    
    function showDialog(titleText,contentText){
        //提示框
        utils.dialog({
            width: 180,
            height: 30,
            title: titleText,
            content: contentText,
            quickClose: false,
            okValue: '确定',
            ok: function () {}
        }).showModal();
    }
    
    //弹框提示
    function showTips(contentText){
        utils.dialog({
            content: contentText,
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
    
    // 初始化下拉多选控件
    $('#add_erp_orgList').selectpicker({
        actionsBox: true,
        selectAllText: '选择全部',
        deselectAllText: '取消全选'
    })
    
    queryRoleListByOrgCode('', '', 1);