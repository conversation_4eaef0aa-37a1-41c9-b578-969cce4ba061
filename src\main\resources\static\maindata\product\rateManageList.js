$(function () {

    $('#X_Tableb').XGrid({
        url:"/proxy-product/product/baseApproval/listData?applyType=2",
        colNames: ['ID','申请日期', '机构', '申请人ID', '申请人', '单据编号', '商品编码', '商品名称','商品大类', '通用名', '生产厂家', '批准文号', '包装单位', '剂型', '审核状态'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true
            }, {
                name: 'applicationTime',
                index: 'applicationTime',
               formatter:function(value){
                    return new Date(value).Format('yyyy-MM-dd');
                },
                width: 100
            }, {
                name: 'orgCodeValue',
                index: 'orgCodeValue',
                width: 210
            }, {
                name: 'applicantId',
                index: 'applicantId',
                hidden:true
            }, {
                name: 'applicantValue',
                index: 'applicantValue',
                width: 100
            }, {
                name: 'applicationCode',
                index: 'applicationCode',
                width: 160
            }, {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'productName',
                index: 'productName',
                width: 200
            }, {
                name: 'largeCategoryVal',
                index: 'largeCategoryVal',
                width: 80
            },{
                name: 'commonName',
                index: 'commonName',
                width: 200
            }, {
                name: 'manufacturerValue',
                index: 'manufacturerValue',
                width: 220
            }, {
                name: 'approvalNumber',
                index: 'approvalNumber',
                width: 180
            }, {
                name: 'packingUnitValue',
                index: 'packingUnitValue',
                width: 80
            }, {
                name: 'dosageFormValue',
                index: 'dosageFormValue',
                width: 80
            },{
                name: 'statues',
                index: 'statues',
                formatter:function(value){
                    if (value==0){
                        return "录入中"
                    }else if (value==1){
                        return "审核中"
                    }else if (value==2){
                        return "审核通过"
                    }else if (value==3){
                        return "审核不通过"
                    }},
                width: 100}
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        ondblClickRow: function (id,dom,obj,index,event) {
            if(obj.statues=="录入中"){
                var loginUserId = $("#loginUserId").val();
                if(obj.applicantId==loginUserId){
                    utils.openTabs("rateManageApplyEdit", "商品涉税字段变更申请", "/proxy-product/product/baseApproval/editRateManage?recordId="+id);
                }else{
                    utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                }
            }else {
                utils.openTabs("rateManageApplyDetail", "商品涉税字段变更申请详情", "/proxy-product/product/baseApproval/rateManageDetail?businessId="+id);
            }
        },
        pager: '#grid-pager'
    });

    $("#SearchBtn").on("click", function () {
        $("#keyword").val($("#keyword").val().trim());
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "orgCode": $("#orgCode").val(),
                "keyword":$("#keyword").val(),
                "statues":$("#statues").val(),
                "largeCategory":$("#largeCategory").val()
            },page:1
        }).trigger('reloadGrid');
    });

    $("#exportBtn").on("click", function () {

//全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
        utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text())).then( () => {
            return false;
        }).catch( () => {
        //原始处理逻辑代码
            var ck = true;
        // copy this parameter and the below buttons
        let html = $('#exportHtml').html();
        let d = dialog({
            title: '请选择导出字段',
            width:400,
            content: html,
            okValue: '确定',
            ok: function () {
                this.title('提交中…');
                return false;
            },
            cancelValue: '取消',
            cancel: function () { },
            // copy button to other dialogues
            button: [
                {
                    id: 'chooseAll',
                    value: '全选',
                    callback: function () {
                        //debugger;
                        if(!ck){
                            $(".exportItem input").removeAttr("checked");
                            ck = true;
                        }else if(ck){
                            $(".exportItem input").prop("checked","checked");
                            ck = false;
                        }else{
                            return false;
                        };
                        return false;
                    }
                }
            ]
            //copy ends here

        });
        d.showModal();
        });
    });

})
var orgCode=$("#loginOrgCode").val();
if(orgCode=='001'){
    $.ajax({
        url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode="+orgCode,
        async : false,
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].orgCode+'">'+arr[i].orgName+'</option>';
                    }
                }
            }
            $("#orgCode").html(html);
        },
        error:function () {
        }
    });
}
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}