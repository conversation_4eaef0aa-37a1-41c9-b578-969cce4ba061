.box,.file_table{
    font-family: '宋体';
    font-size: 13px;
}

.box {
    width: 718px;
    height: 1040px;
    /* border: 1px solid #000;
    box-sizing: border-box; */
}

.box .header {
    font-size: 21px;
    font-weight: bold;
    text-align: center;
    height: 30px;
    line-height: 30px;
}

.box .content {
    margin-top: 30px;
}

.file_about {
    height: 40px;
    line-height: 40px;
}

.file_about span {
    display: inline-block;
    width: 32.5%;
}

.file_table {
    width: 100%;
    table-layout: fixed;
    word-break: break-all;
    border-collapse: collapse;
}

.file_table td {
    height: 40px;
    vertical-align: middle;
    text-align: center;
    border-top: 1px solid #000;
    border-left: 1px solid #000;
}
.file_table td .td_con{
    height: 40px;
    overflow: hidden;
}

.file_table tr:last-child td {
    border-bottom: 1px solid #000;
}

.file_table tr td:last-child {
    border-right: 1px solid #000;
}