$(function () {
    $('#Table').XGrid({
        url: '/proxy-purchase/purchase/orgAllocatingParameters/selectOrgAllocatingParametersData',
        colNames: ['调出机构','机构编码','可调百分比上限（地采）','调拨单价是否可改（地采）','可调百分比上限（集采）','调拨单价是否可改（集采）','canAdjustPriceChangeCopy','jcCanAdjustPriceChangeCopy','更新人','更新时间'],
        colModel: [
            {
                name: 'orgName',
                index: 'orgName'
            },
            {
                name: 'orgCode',
                index: 'orgCode',
                hidden: true,
                hidegrid: true
            },
            {
                name: 'adjustPercentageLimit',
                index: 'adjustPercentageLimit',
                width: 200,
                rowtype: '#adjustPercentageLimitInput',
                formatter: (e) => {
                    return e + '%'
                }
            },
            {
                name: 'canAdjustPriceChange',
                index: 'canAdjustPriceChange',
                width: 200,
                formatter: (result,id,rowData) => {
                    return {
                        rowtype:  `<div>
                                        <label class="radio-inline">
                                            <input type="radio" name="canAdjustPriceChange_${rowData.id}" disabled value="1"> Y
                                        </label>
                                        <label class="radio-inline">
                                            <input type="radio" name="canAdjustPriceChange_${rowData.id}" disabled  value="0"> N
                                        </label>
                                    </div>`
                    }
                }
            },
            {
                name: 'jcAdjustPercentageLimit',
                index: 'jcAdjustPercentageLimit',
                width: 200,
                rowtype: '#adjustPercentageLimitInput',
                formatter: (e) => {
                    return e + '%'
                }
            },
            {
                name: 'jcCanAdjustPriceChange',
                index: 'jcCanAdjustPriceChange',
                width: 200,
                formatter: (result,id,rowData) => {
                    return {
                        rowtype:  `<div>
                                        <label class="radio-inline">
                                            <input type="radio" name="jcCanAdjustPriceChange_${rowData.id}" disabled value="1"> Y
                                        </label>
                                        <label class="radio-inline">
                                            <input type="radio" name="jcCanAdjustPriceChange_${rowData.id}" disabled  value="0"> N
                                        </label>
                                    </div>`
                    }
                }
            },
            {
                name: 'canAdjustPriceChangeCopy',
                index: 'canAdjustPriceChangeCopy',
                hidden: true
            },
            {
                name: 'jcCanAdjustPriceChangeCopy',
                index: 'jcCanAdjustPriceChangeCopy',
                hidden: true
            },
            {
                name: 'updateUserName',
                index: 'updateUserName'
            },
            {
                name: 'updateTime',
                index: 'updateTime',
                formatter: function (e) {
                    if (e != null && e != "") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }
        ],
        rownumbers: true,
        rowNum: 99999,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid_page',//设置翻页所在html元素名称
        rowList: [20, 50, 100],
        // attachRow:true,
        gridComplete: function () {
            let rowData = $("#Table").XGrid('getRowData');
            if(rowData && rowData.length){
                rowData.forEach(function (item) {
                    $('#Table #'+item.id+' [name=canAdjustPriceChange_'+item.id+'][value='+item['canAdjustPriceChangeCopy']+']').prop('checked',true)
                    $('#Table #'+item.id+' [name=jcCanAdjustPriceChange_'+item.id+'][value='+item['jcCanAdjustPriceChangeCopy']+']').prop('checked',true)

                })
            }
        }
    })
    // 修改
    $('#updateBtn').on('click', function () {
        let selRow = $('#Table').XGrid('getSeleRow');
        if (selRow.length == 0) {
            utils.dialog({content: '请先选择要修改的内容.', quickClose: true, timeout: 2000}).showModal();
            return false
        }
        $('#updateBtn').prop('disabled', true)
        $('#saveBtn').prop('disabled', false)
        $('#saveBtn').attr('data-selRowId', selRow[0]['id'])
        $('#Table #'+ selRow[0]['id']).find('[row-describedby="adjustPercentageLimit"] input').prop('disabled', false)
        $('#Table #'+ selRow[0]['id']).find('[row-describedby="canAdjustPriceChange"] input').prop('disabled', false)
        $('#Table #'+ selRow[0]['id']).find('[row-describedby="adjustPercentageLimit"] input').val(selRow[0]['adjustPercentageLimit'].substring(0, selRow[0]['adjustPercentageLimit'].length -1 ))


        $('#Table #'+ selRow[0]['id']).find('[row-describedby="jcAdjustPercentageLimit"] input').prop('disabled', false)
        $('#Table #'+ selRow[0]['id']).find('[row-describedby="jcCanAdjustPriceChange"] input').prop('disabled', false)
        $('#Table #'+ selRow[0]['id']).find('[row-describedby="jcAdjustPercentageLimit"] input').val(selRow[0]['jcAdjustPercentageLimit'].substring(0, selRow[0]['jcAdjustPercentageLimit'].length -1 ))

    })

    // 保存
    $('#saveBtn').on('click', function () {
        let selRowId = $(this).attr('data-selRowId')
        let selRowData =  $('#Table').XGrid('getRowData', selRowId);
        $.ajax({
            url: '/proxy-purchase/purchase/orgAllocatingParameters/update',
            method: 'post',
            data: {
                id: selRowId,
                adjustPercentageLimit: selRowData['adjustPercentageLimit'],
                canAdjustPriceChange: selRowData['canAdjustPriceChange']['canAdjustPriceChange_'+selRowId],
                jcAdjustPercentageLimit: selRowData['jcAdjustPercentageLimit'],
                jcCanAdjustPriceChange: selRowData['jcCanAdjustPriceChange']['jcCanAdjustPriceChange_'+selRowId]
            },
            beforeSend: function(){
              parent.showLoading()
            },
            success: (res) => {
                if (res.code === 0) {
                    $('#Table').setGridParam({
                        url: '/proxy-purchase/purchase/orgAllocatingParameters/selectOrgAllocatingParametersData',
                    }).trigger('reloadGrid');
                }
            },
            complete: () => {
                parent.hideLoading()
                $('#saveBtn').attr('data-selRowId', '')
                $('#updateBtn').prop('disabled', false)
                $('#saveBtn').prop('disabled', true)
            }
        })
    })

    // 可调百分比上限
    $('body').on('input', 'input[name=adjustPercentageLimit]',function () {
        let val = $(this).val()
        val = val.replace(/\D/g,'')
        if(Number(val) === 0) {
            val = 0
        } else {
            if(val.charAt(0) == 0) {
                val = val.substring(1)
            }
        }
        if(val > 100) {
            val = 100
        }
        $(this).val(val)
    })
    // 可调百分比上限
    $('body').on('input', 'input[name=jcAdjustPercentageLimit]',function () {
        let val = $(this).val()
        val = val.replace(/\D/g,'')
        if(Number(val) === 0) {
            val = 0
        } else {
            if(val.charAt(0) == 0) {
                val = val.substring(1)
            }
        }
        if(val > 100) {
            val = 100
        }
        $(this).val(val)
    })
})
