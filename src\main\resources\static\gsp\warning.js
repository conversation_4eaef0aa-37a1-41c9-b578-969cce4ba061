var minToMAX = true;
$(function () {
    $('#Warning_Table').XGrid({
        url:"/proxy-gsp/gsp/checkPlan/effectiveWarningPage",
        //data:res.result.list,
        colNames: ['商品编号', '商品名称', '商品规格', '生产厂家', '商品产地','业务类型',
            '批号', '生产日期', '有效期至', '近效期天数', '库房名称', '库存数量'],
        postData:{
            orgCode:$("#orgNum").val(),
            channelIds:$("#channelId").val(),
            sort:'asc'
        },
        colModel: [
            {
                name: 'drugCode',
                index: 'drugCode',
            }
            , {
                name: 'productName',
                index: 'productName'
            }, {
                name: 'specifications',
                index: 'specifications'

            }, {
                name: 'manufacturerName',
                index: 'manufacturerName'
            }, {
                name: 'producingArea',
                index: 'producingArea'
            },{
                name: 'channelId',
                index: 'channelId'
            },{
                name: 'batchNum',
                index: 'batchNum'
            }, {
                name: 'productDate',
                index: 'productDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'validateDate',
                index: 'validateDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'effectiveTime',
                index: 'effectiveTime'
            }, {
                name: 'storageTypeName',
                index: 'storageTypeName'
            }, {
                name: 'amount',
                index: 'amount'
            }, {
                name: 'id',
                index: 'id',
                hidden:true
            }, {
                name: 'warningType',
                index: 'warningType',
                hidden:true,
                formatter: function (e, c, rowData) {
                    if (e) {
                        var id = rowData.id;
                        setTimeout(function () {
                            if (e === 3) {
                                //console.log('11111111111')
                                $("#" + id).css({
                                    "background": "#FFF9DD",
                                });
                            }if(e === 1) {
                                //console.log('22222222')
                                $("#" + id).css({
                                    "background": "#FDE5E5",
                                });
                            }else if(e === 2){
                                $("#" + id).css({
                                    "background": "#fce1b8",
                                });
                            }
                        }, 0);
                        //console.log('-----------  ' + e)
                        return e
                    }else{
                        var id = rowData.id;
                        setTimeout(function () {
                            $("#"+id).css({
                                "background": "#FFF9DD",
                            });
                        },0)
                        console.log('++++++  ' + e)
                        return '0';
                    }
                },
                rowEvent: function (etype) {
                    //initBaseDataBuseScope();//初始化经营范围内容

                }
            }
        ],
        key:'id',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号，多选
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
    });

    //近效期天数
    $('.content').on('click','table th:nth-child(10)',function () {
        $('#Warning_Table').XGrid('setGridParam', {
            url:"/proxy-gsp/gsp/checkPlan/effectiveWarningPage",
            postData:{
                orgCode:$("#orgNum").val(),
                channelIds:$("#channelId").val(),
                sort:minToMAX?'desc':'asc'
            },
        }).trigger('reloadGrid');
        minToMAX = minToMAX?false:true;
    })


    //业务类型搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })


    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('1').then( res => {
            console.log(res)
        let _str_name = '', _str_code = '';
        let _str_arr = res.map(item => {
            return item.channelCode;
    })
        _str_name = _str_arr.join(',');

        let _str_code_arr = res.map(item => {
            return item.channelCode;
    })
        _str_code = _str_code_arr.join(',')
        $('#channelId_inp').val(_str_name)
        $('#channelId').val(_str_code)
    })
    });

})
function compare(propertyName) {
    return function(object1, object2) {
        var value1 = Number(object1[propertyName]); // 转换成数字类型比较
        var value2 = Number(object2[propertyName]);
        if (value2 < value1) {
            return 1;
        } else if (value2 > value1) {
            return -1;
        } else {
            return 0;
        }
    }
}
function  excelWarning() {
	utils.exportAstrictHandle('Warning_Table', Number($('#totalPageNum').text()), 1).then( () => {
        return false;
    }).catch( () => {
    	window.location = '/proxy-gsp/gsp/checkPlan/excelWarning?orgCode='+$("#orgNum").val()+'&sort='+(minToMAX?'asc':'desc')+'&channelIds='+$("#channelId").val();
    });    
    

}

//查询
function btn_search() {

    $('#Warning_Table').setGridParam({
        url: '/proxy-gsp/gsp/checkPlan/effectiveWarningPage',
        postData: {
            orgCode:$("#orgNum").val(),
            channelIds:$("#channelId").val(),
            sort:'asc'
        }
    }).trigger('reloadGrid');
}
