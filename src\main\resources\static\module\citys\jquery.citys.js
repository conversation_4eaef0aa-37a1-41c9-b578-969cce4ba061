/**
 * jquery.citys.js 1.0
 * http://jquerywidget.com
 */
;(function (factory) {
    if (typeof define === "function" && (define.amd || define.cmd) && !jQuery) {
        // AMD或CMD
        define([ "jquery" ],factory);
    } else if (typeof module === 'object' && module.exports) {
        // Node/CommonJS
        module.exports = function( root, jQuery ) {
            if ( jQuery === undefined ) {
                if ( typeof window !== 'undefined' ) {
                    jQuery = require('jquery');
                } else {
                    jQuery = require('jquery')(root);
                }
            }
            factory(jQuery);
            return jQuery;
        };
    } else {
        //Browser globals
        factory(jQuery);
    }
}(function ($) {
    $.support.cors = true;
    $.fn.citys = function(parameter,getApi) {
        if(typeof parameter == 'function'){ //重载
            getApi = parameter;
            parameter = {};
        }else{
            parameter = parameter || {};
            getApi = getApi||function(){};
        }
        var defaults = {
            dataUrl:'',     //数据库地址
            dataType:'json',          //数据库类型:'json'或'jsonp'
            provinceField:'province', //省份字段名
            cityField:'city',         //城市字段名
            areaField:'area',         //地区字段名
            streetFieId:'street',        //街道字段名
            valueType:'code',         //下拉框值的类型,code行政区划代码,name地名
            code:0,                   //地区编码
            province:'',               //省份,可以为地区编码或者名称
            city:'',                   //城市,可以为地区编码或者名称
            area:'',                   //地区,可以为地区编码或者名称
            required: false,           //是否必须选一个
            nodata: 'hidden',         //当无数据时的表现形式:'hidden'隐藏,'disabled'禁用,为空不做任何处理
            onChange:function(){}     //地区切换时触发,回调函数传入地区数据
        };
        var options = $.extend({}, defaults, parameter);
        return this.each(function() {
            //对象定义
            var _api = {};
            var flag=false;
            var $this = $(this);
            var $province = $this.find('select[name="'+options.provinceField+'"]'),
                $city = $this.find('select[name="'+options.cityField+'"]'),
                $area = $this.find('select[name="'+options.areaField+'"]'),
                $street = $this.find('select[name="'+options.streetFieId+'"]');
            $.ajax({
                url:options.dataUrl,
                type:'GET',
                crossDomain: true,
                dataType:options.dataType,
                jsonpCallback:'jsonp_location',
                success:function(data){
                    var province,city,area,hasCity;
                    if(options.code){   //如果设置地区编码，则忽略单独设置的信息
                        var c = options.code - options.code%1e4;
                        if(data[c]){
                            options.province = c;
                        }
                        c = options.code - (options.code%1e4 ? options.code%1e2 : options.code);
                        if(data[c]){
                            options.city = c;
                        }
                        c = options.code%1e2 ? options.code : 0;
                        if(data[c]){
                            options.area = c;
                        }
                    }
                    var updateData = function(){
                        province = {},city={},area={};
                        hasCity = false;       //判断是非有地级城市
                        for(var code in data){
                            if(!(code%1e4)){     //获取所有的省级行政单位
                                province[code]=data[code];
                                if(options.required&&!options.province){
                                    if(options.city&&!(options.city%1e4)){  //省未填，并判断为直辖市
                                        options.province = options.city;
                                    }else{
                                        options.province = code;
                                    }
                                }else if(data[code] == options.province){
                                    options.province = isNaN(options.province)?code:options.province;
                                }
                            }else{
                                var p = code - options.province;
                                if(options.province&&p>0&&p<1e4){    //同省的城市或地区
                                    if(!(code%100)){
                                        hasCity = true;
                                        city[code]=data[code];

                                        if(data[code] == options.city){
                                            options.city = isNaN(options.city)?code:options.city;
                                        }
                                    }else if(p>9000&&code != 419001){
                                        //省直辖县级行政单位 (暂定419001是济源市的第三级)
                                        city[code] = data[code];
                                        if(data[code] == options.city){
                                            options.city = isNaN(options.city)?code:options.city;
                                        }
                                    }
                                    if(hasCity){                  //非直辖市
                                        var c = code-options.city;
                                        // if(options.city&&c>0&&c<100){     //同个城市的地区
                                        //     area[code]=data[code];
                                        //     if(data[code] == options.area){
                                        //         options.area = isNaN(options.area)?code:options.area;
                                        //     }
                                        // }
                                        /**
                                         * RM  2019-01-04
                                         * 上面是旧的代码，对于重庆 不适用
                                         * 因为之前的比较 是取小于100 的值
                                         * 但是重庆下面区级 有c 值大于100的 好多少数民族的县
                                         * 而上面的方法直接把这些县都过滤掉了
                                         * 所以下面先这么针对性的改了
                                         * 150 是取得最接近的值
                                         *  500100: {
                                              500101: '万州区',
                                              500102: '涪陵区',
                                              500235: '云阳县',
                                              500236: '奉节县',
                                              500237: '巫山县',
                                              500238: '巫溪县',
                                              500240: '石柱土家族自治县',
                                              500241: '秀山土家族苗族自治县',
                                              500242: '酉阳土家族苗族自治县',
                                              500243: '彭水苗族土家族自治县'
                                              }
                                         500100  表示的是重庆市，按照上面的算法 ，后面几个自治县的编码减去重庆市的编码
                                         都大于100，所以这几个自治县的数据都被过滤掉
                                         */
                                        if(options.city == 500100 &&c>0&&c<150){
                                            area[code]=data[code];
                                            if(data[code] == options.area){
                                                options.area = isNaN(options.area)?code:options.area;
                                            }
                                        }else if(options.city&&c>0&&c<100){     //同个城市的地区
                                            area[code]=data[code];
                                            if(data[code] == options.area){
                                                options.area = isNaN(options.area)?code:options.area;
                                            }
                                        }else if( c == '0'){
                                            // 湖北  仙桃市   潜江市  天门市 神农架林区
                                            // 等于0  是因为  自定义的编码
                                            let arr = ['429900','429800','429700','429600']
                                            if(arr.indexOf(code) > -1){
                                                area[code] = data[code];
                                                if(data[code] == options.area){
                                                    options.area = isNaN(options.area)?code:options.area;
                                                }
                                            }
                                        }
                                    }else{
                                        area[code]=data[code];            //直辖市
                                        if(data[code] == options.area){
                                            options.area = isNaN(options.area)?code:options.area;
                                        }
                                    }
                                }
                            }
                        }
                        /**
                         * 湖北  仙桃市   潜江市  天门市 神农架林区
                         *  因为这几个是省级直辖市，下属没有区，所以自定义了区级编码
                         *   ['429900','429800','429700','429600']
                         *   在渲染市级的时候用的自定义的数据， 渲染区级的时候用的正规数据，
                         *   所以需要在市级的下拉框中删除，要不页面看着重复显示，
                         */
                        delete city[429004]
                        delete city[429005]
                        delete city[429006]
                        delete city[429021]
                    };
                    var format = {
                        province:function(){
                            $province.empty();
                            if(!options.required){
                                $province.append('<option value=""> - 请选择 - </option>');
                            }
                            for(var i in province){
                                $province.append('<option value="'+(options.valueType=='code'?i:province[i])+'" data-code="'+i+'">'+province[i]+'</option>');
                            }
                            if(options.province){
                                var value = options.valueType=='code'?options.province:province[options.province];
                                $province.val(value);
                            }
                            this.city();
                        },
                        city:function(){
                            $city.empty();

                            // $city.css('display','');
                            if(!options.required){
                                $city.append('<option value=""> - 请选择 - </option>');
                            }
                            for(var i in city){
                                $city.append('<option value="'+(options.valueType=='code'?i:city[i])+'" data-code="'+i+'">'+city[i]+'</option>');
                            }
                            if(options.city){
                                var value = options.valueType=='code'?options.city:city[options.city];
                                $city.val(value);
                            }else if(options.area){
                                var value = options.valueType=='code'?options.area:city[options.area];
                                $city.val(value);
                            }
                            this.area();
                        },
                        area:function(){
                            $area.empty();
                            if(!options.required){
                                $area.append('<option value=""> - 请选择 - </option>');
                            }
                            for(var i in area){

                                $area.append('<option value="'+(options.valueType=='code'?i:area[i])+'" data-code="'+i+'">'+area[i]+'</option>');
                            }
                            if(options.area){
                                var value = options.valueType=='code'?options.area:area[options.area];
                                $area.val(value);
                            }
                            this.street()
                        },
                        street:function(){
                            $street.empty();
                            if(!options.required){
                                $street.append('<option value=""> - 请选择 - </option>');
                            }
                        },
                    };
                    //获取当前地理信息
                    _api.getInfo = function(){
                        var status = {
                            direct:!hasCity,
                            province:data[options.province]||'',
                            city:data[options.city]||'',
                            area:data[options.area]||'',
                            code:options.area||options.city||options.province
                        };
                        return status;
                    };
                    //事件绑定
                    $province.on('change',function(){
                        options.province = $(this).find('option:selected').data('code')||0; //选中节点的区划代码
                        options.city = 0;
                        options.area = 0;
                        updateData();
                        format.city();
                        /* 清空街道数据 */
                        //options.onChange($this,_api.getInfo());
                    });
                    $city.on('change',function(){
                        options.city = $(this).find('option:selected').data('code')||0; //选中节点的区划代码
                        options.area = 0;
                        updateData();
                        format.area();
                        flag=true;
                        //options.onChange($this,_api.getInfo());
                    });
                    $area.unbind().on('change',function(){
                        options.area = $(this).find('option:selected').data('code')||0; //选中节点的区划代码
                        options.onChange($this,_api.getInfo());
                    });
                    //初始化
                    updateData();
                    format.province();
                    if(options.code){
                        options.onChange($this,_api.getInfo());
                    }
                    getApi($this,_api);
                }
            });
        });
    };
}));
