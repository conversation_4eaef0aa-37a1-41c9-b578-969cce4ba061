.form-horizontal {
    width: 400px;
    margin: 100px auto;
}

.form-horizontal .control-label {
    text-align: center;
    font-weight: normal;
}

.form-group {
    margin-top: 35px;
    position: relative;
}

.err {
    position: absolute;
    top: 40px;
}

.formtips {
    position: absolute;
}

.form-control {
    width: 313px;
}

.btn.btn-default.dropdown-toggle {
    width: 100%;
    text-align: left;
}

.btn .caret,
.btn-group>.btn:first-child {
    margin-left: 200px;
}

.dropdown-menu {
    width: 295px;
}

.login {
    width: 130px;
}

.col-sm-offset-2 {
    margin-left: 30px;
    margin-top: 16px;
    color: #169BD5;
    cursor: pointer;
}

.col-sm-2 {
    padding: 0;
}

#phone {
    position: relative;
}

#reg_mescode_btn, .verifyCodeBox {
    position: absolute;
    left: 475px;
}

#undefined_select.j_select {
    width: 295px;
}

#undefined_current_select {
    height: 34px;
    border-radius: 4px;
}

#undefined_select ul li:hover {
    background: #169BD5;
}

#undefined_select ul li.current_item {
    background: #169BD5;
}

/*****蹇樿瀵嗙爜******/

.page-header {
    padding-bottom: 0;
    border-bottom: none;
}

h3 {
    font-family: 'Arial Negreta', 'Arial Normal', 'Arial';
    font-weight: 700;
    font-style: normal;
    font-size: 20px;
}

h3 small {
    float: right;
    color: #0000FF;
    border-bottom: 1px solid#0000FF;
}