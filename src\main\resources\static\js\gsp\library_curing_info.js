$(function () {
  /* 参数,页面传递的数据 */
  var url = location.search;
  var param = z_utils.parseParams(url);
  console.log(url, param);

  /* 填入初始数据 */
  $('#form_a').JSONToform({
    val_a: '举个栗子'
  })

  /* 合计计算 */
  var totalTable = z_utils.totalTable;

  /* table_a */
  //data
  var grid_dataY = [{
    text1: "1",
    text2: "",
    text3: "",
    text4: "",
    text5: "",
    text6: "",
    text7: "",
    text8: "",
    text9: "",
    text10: "",
    text11: "",
    text12: "2",
    text13: "",
    text14: "",
    text15: "4",
    text16: "6",
    text17: "",
    text18: "",
    text19: "",
    text20: "",
    text21: "",
    text22: "",
    text23: "",
    text24: "",
    text25: "",
    text26: "",
    text27: "",
    text28: "",
    text29: "",
    text30: "",
  }];
  for (let i = 0; i < 2; i++) {
    grid_dataY.push(grid_dataY[0]);
  }
  var colName = ['商品编码', '商品名称', '商品规格', '生产厂家', '产地', '单位', '剂型', '计量规格', '库房名称', '批号', '生产日期', '有效期至', '数量',
    '翻垛', '外观', '除湿', '加湿', '升温', '降温', '通风', '除尘', '养护原因', '养护结果', '不确定数量', '养护结论', '复查员', '复查结果', '批准文号', '灭菌文号', '备注'
  ];
  var colModel = [{
    name: 'text1',
    index: 'text1'
  }, {
    name: 'text2',
    index: 'text2'
  }, {
    name: 'text3',
    index: 'text3'
  }, {
    name: 'text4',
    index: 'text4'
  }, {
    name: 'text5',
    index: 'text5'
  }, {
    name: 'text6',
    index: 'text6'
  }, {
    name: 'text7',
    index: 'text7'
  }, {
    name: 'text8',
    index: 'text8',
  }, {
    name: 'text9',
    index: 'text9'
  }, {
    name: 'text10',
    index: 'text10'
  }, {
    name: 'text11',
    index: 'text11'
  }, {
    name: 'text12',
    index: 'text12'
  }, {
    name: 'text13',
    index: 'text13'
  }, {
    name: 'text14',
    index: 'text14',
    rowtype: '#text14_e',
  }, {
    name: 'text15',
    index: 'text15',
    rowtype: '#text14_e',
  }, {
    name: 'text16',
    index: 'text16',
    rowtype: '#text14_e',
  }, {
    name: 'text17',
    index: 'text17',
    rowtype: '#text14_e',
  }, {
    name: 'text18',
    index: 'text18',
    rowtype: '#text14_e',
  }, {
    name: 'text19',
    index: 'text19',
    rowtype: '#text14_e',
  }, {
    name: 'text20',
    index: 'text20',
    rowtype: '#text14_e',
  }, {
    name: 'text21',
    index: 'text21',
    rowtype: '#text14_e',
  }, {
    name: 'text22',
    index: 'text22'
  }, {
    name: 'text23',
    index: 'text23'
  }, {
    name: 'text24',
    index: 'text24'
  }, {
    name: 'text25',
    index: 'text25'
  }, {
    name: 'text26',
    index: 'text26'
  }, {
    name: 'text27',
    index: 'text27'
  }, {
    name: 'text28',
    index: 'text28'
  }, {
    name: 'text29',
    index: 'text29'
  }, {
    name: 'text30',
    index: 'text30'
  }];
  $('#table_a').XGrid({
    data: grid_dataY,
    colNames: colName,
    colModel: colModel,
    key: 'text1',
    rowNum: 999,
    rownumbers: true,
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {
 
    },
    gridComplete: function () {},
    onSelectRow: function (id, dom, obj, index, event) {
      //选中事件
      //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
      //console.log(id, dom, obj, index, event)
    }
  });


  /* 筛选列 */
  $("#set_tb_rows").click(function () {
    //获取当前显示表格
    var tableId = $('#nav_content .active table').attr('id');
    $('#' + tableId).XGrid('filterTableHead');
  })

  /* 编辑 */
  //判断是否为已驳回状态
  var indent_type = '已驳回';
  if (indent_type == '已驳回') {
    $('#editRowData').show();
  }
  $('#editRowData').on('click', function () {
    var data = $('#table_a').XGrid('getSeleRow');
    if (!data) {
      utils.dialog({
        title: "提示",
        content: "请先选中一行数据"
      }).show();
      return
    }
    window.location = 'return_receive_edit.html';
  });

  /* 返回 */
  $('#goback').on('click', function () {
    window.history.back(-1);
  });

})