$(function () {
  /* 获取dialog上层实例 */
  var dialog = parent.dialog.get(window);
  if (dialog) {
    var dialog_data = dialog.data;
    $('#search_vl').val(dialog_data)
  }

    var colName = ['仓库名称', '批号', '生产日期', '有效期至', '库存数量'];
    var colModel = [{
        name: 'storageTypeName',
        index: 'storageTypeName',
    }, {
        name: 'batchNum',
        index: 'batchNum',

    }, {
        name: 'productDate',
        index: 'productDate',
        formatter:function (e){
        if (e != null && e !="") {
            return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
        } else {
            return "";
        }
    }

    }, {
        name: 'validateDate',
        index: 'validateDate',
        formatter:function (e){
        if (e != null && e !="") {
            return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
        } else {
            return "";
        }
    }

    }, {
        name: 'amount',
        index: 'amount',

    }];
    $('#table_b').XGrid({
        data: [],
        colNames: colName,
        colModel:colModel ,
        rowNum: 2,
        key:"batchNum",
        rownumbers: true,
        altRows: true, //设置为交替行表格,默认为false
        //pager: '#grid_pager_n',
    });
  $('#table_a').XGrid({

    url:'/proxy-gsp/gsp/stockInCheck/findProductDetail',
    postData:{
          orgCode :dialog_data.orgCode
      },
    colNames: ['商品编码', '商品名称', '商品规格', '生产厂家', '商品产地', '单位', '批准文号','存储条件','商品大类'],
    colModel: [{
      name: 'productCode',
      index: 'productCode',
    }, {
      name: 'productName',
      index: 'productName',

    }, {
      name: 'specifications',
      index: 'specifications',

    }, {
      name: 'manufacturerVal',
      index: 'manufacturerVal',

    }, {
      name: 'producingArea',
      index: 'producingArea',

    }, {
      name: 'packingUnitVal',
      index: 'packingUnitVal',

    }, {
      name: 'approvalNumber',
      index: 'approvalNumber',

    }, {
        name: 'storageConditions',
        index: 'storageConditions',

    }, {
        name: 'dosageFormVal',
        index: 'dosageFormVal',
        hidden:true

    }, {
        name: 'drugClass',
        index: 'drugClass',
        hidden:true

    }],
      rowNum: 20,
      rowList:[20,50,100],
    key: "productCode",
    rownumbers: true,
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_pager_a',
    onSelectRow: function (id, dom, obj, index, event) {
        var sele_data = $(this).XGrid("getSeleRow");
        var isZyyp = dialog_data.isZyyp;

        if(dialog_data.checkPlanCode != ''){
            if(sele_data){
                if(isZyyp == '1' &&  !(sele_data.drugClass == 201)){
                    utils.dialog({content: '您提取的养护计划单为“中药”，请在该页面选择中药进行维护', timeout: 3000, quickClose: true,}).showModal();
                    return false;
                }
                if(isZyyp == '0' &&  sele_data.drugClass == 201){
                    utils.dialog({content: '您提取的养护计划单为“普通药品”，请在该页面选择普通药品进行维护', timeout: 3000, quickClose: true,}).showModal();
                    return false;
                }
            }
        }
        var productCode = obj.productCode;
        var orgCode = dialog_data.orgCode;
        $('#table_b').XGrid('clearGridData');
        if(sele_data){
            $('#table_b').XGrid('setGridParam', {
                url: '/proxy-gsp/gsp/stockInCheck/queryProductMessage',
                postData:{
                    drugCode:productCode,
                    orgCode :orgCode
                }
            }).trigger("reloadGrid");
        }
    },
    gridComplete:function () {
        $('#table_b').XGrid('clearGridData');
    }
  });

  /* table_b */


  /* 查询 */
  $('#search').on('click', function () {
      $('#table_a').setGridParam({
     // url: 'http://www.baidu.com?name=' + $('#a').val(),
        url:'/proxy-gsp/gsp/stockInCheck/findProductDetail',
      postData: {
          productName:$("#productName").val(),
          orgCode :dialog_data.orgCode
      }
    }).trigger('reloadGrid');
  })

  /* 确定 */
  $('#sub').on('click', function () {
    var data_a = $('#table_a').XGrid('getSeleRow');
    var data_b = $('#table_b').XGrid('getSeleRow');
    if(data_a&&data_b){
      var obj = {
        data_a:data_a,
        data_b:data_b
      }
      dialog.close(obj);
    }
    console.log(data_a,data_b);
  })



  // 去掉所有input的autocomplete, 显示指定的除外
  /* $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete',
    'off'); */

  /* 模糊搜索 */
  var countries = [{
      value: 'Andorra',
      data: 'AD'
    },
    {
      value: 'Zimbabwe',
      data: 'ZZ'
    }
  ];

  $('#search_vl').Autocomplete({
    serviceUrl: 'http://localhost/x.json', //异步请求
    // paramName: 'query111',//查询参数，默认 query
    dataType: 'json',
    //lookup: countries, //监听数据 value显示文本，data为option的值
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    /*  dataReader:{
          'value':'manufactoryName',
          'data':'manufactoryId',
           'xxt':"name"
      },*/
    triggerSelectOnValidInput: false, // 必选
    transformResult: function (response) {
      return {
        suggestions: $.each(response, function (idnex, dataItem) {
          return {
            value: dataItem,
            data: dataItem
          };
        })
      };
    },
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function (result) {
      //选中回调
      alert('You selected: ' + result.value + ', ' + result.data + ',' + result.xxt);
      // console.log('选中回调')
    },
    onSearchStart: function (params) {
      // console.log('检索开始回调', params)
    },
    onSearchComplete: function (query, suggestions) {
      //匹配结果后回调
      // console.log(query, suggestions);
      // if (suggestions.length < 1) {
      //     utils.dialog({
      //         title: '查询无结果',
      //         content: '是否新增生产厂家？',
      //         width: '300',
      //         okValue: '确认',
      //         ok: function () {
      //             this.title('提交中…');
      //             return false;
      //         },
      //         cancelValue: '取消',
      //         cancel: function () {
      //             $('input').val('')
      //         }
      //     }).show();
      // }
    },
    onSearchError: function (query, jqXHR, textStatus, errorThrown) {
      //查询失败回调
      console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function (container) {
      // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function (params, suggestions) {
      console.log(params, suggestions);
      console.log('没选中回调函数');
    }
  });


})