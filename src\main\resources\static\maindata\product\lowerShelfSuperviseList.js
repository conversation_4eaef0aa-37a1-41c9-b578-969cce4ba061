var colNames=['阶段状态','机构编码','机构','业务类型','原商品id','商品编号', '原商品编码', '小包装条码', '商品名称', '通用名', '商品规格', '生产厂家', '单位',
    '采购员','销售状态','库存数量','商品定位','销售定位', '最后含税进价', '最后供应商', 'APP售价', '终端建议零售价', '商品产地', '剂型',
    '处方分类', '批准文号', '一级分类', '二级分类', '三级分类', '四级分类', '应季类型'];
var colModel= [
    {
        name: 'stageStateVal',
        index: 'stageStateVal',
    }, {
        name: 'orgCode',
        index: 'orgCode',
        hidden: true,
        hidegrid:true
    }, {
        name: 'orgName',
        index: 'orgName',
        width:210
    }, {
        name: 'channelId',
        index: 'channelId',
        width:100
    },{
        name: 'oldProductId',
        index: 'oldProductId',
        hidden: true,
        hidegrid:true
    },{
        name: 'productCode',
        index: 'productCode',
        width:100
    }, {
        name: 'oldProductCode',
        index: 'oldProductCode',
        width:140
    }, {
        name: 'smallPackageBarCode',
        index: 'smallPackageBarCode',
        width:140
    }, {
        name: 'productName',
        index: 'productName',
        width:110
    }, {
        name: 'commonName',
        index: 'commonName',
        width:110
    }, {
        name: 'specifications',
        index: 'specifications',
        width:140
    }, {
        name: 'manufacturerName',
        index: 'manufacturerName',
        width:240
    }, {
        name: 'packingUnitVal',
        index: 'packingUnitVal',
        width:80
    }, {
        name: 'buyerVal',
        index: 'buyerVal',
        width:80
    }, {
        name: 'ecStatusVal',
        index: 'ecStatusVal',
        width:80
    },{
        name: 'inventoryQuantity',
        index: 'inventoryQuantity',
        width:80
    },{
        name: 'commodityPositionVal',
        index: 'commodityPositionVal',
        width:120
    },{
        name: 'salesClassification',
        index: 'salesClassification',
        width:120
    },{
        name: 'lastIncludingTaxPurchasePrice',
        index: 'lastIncludingTaxPurchasePrice',
        width:110
    }, {
        name: 'lastSupplier',
        index: 'lastSupplier',
        width:220
    }, {
        name: 'appPrice',
        index: 'appPrice',
        formatter: function (e) {
            if(e!=null) {
                return Number(e).toFixed(2);
            }
        },
        width:110
    }, {
        name: 'terminalPrice',
        index: 'terminalPrice',
        formatter: function (e) {
            if(e!=null) {
                return Number(e).toFixed(2);
            }
        },
        width:130
    }, {
        name: 'producingArea',
        index: 'producingArea',
        width:80
    }, {
        name: 'dosageFormVal',
        index: 'dosageFormVal',
        width:100
    }, {
        name: 'prescriptionClassificationVal',
        index: 'prescriptionClassificationVal',
        width:100
    }, {
        name: 'approvalNumber',
        index: 'approvalNumber',
        width:160
    }, {
        name: 'firstCategoryVal',
        index: 'firstCategoryVal',
        width:100
    }, {
        name: 'secondCategoryVal',
        index: 'secondCategoryVal',
        width:120
    }, {
        name: 'thirdCategoryVal',
        index: 'thirdCategoryVal',
        width:120
    }, {
        name: 'fourCategory',
        index: 'fourCategory',
        width:100
    }, {
        name: 'seasonalVarietiesVal',
        index: 'seasonalVarietiesVal',
        width:120
    }];
$('#X_Table').XGrid({
    url: '/proxy-product/product/lowerShelf/querySuperviseProduct',
    postData: {
        "orgCode": $("#orgCode").val()
    },
    colNames: colNames,
    colModel: colModel,
    datasouce: 'json',
    rowNum: 20,
    altRows: true,//设置为交替行表格,默认为false
    rownumbers: true,
    rowList: [20, 50,100], //分页条数下拉选择
    onSelectRow: function (id, dom, obj, index, event) {
    },
    pager: '#grid-pager'
});
//查询数据，重置data
$('#SearchBtn').on('click', function () {
    var orgCode = $("#orgCode").val();
    if (orgCode==undefined||orgCode==''){
        utils.dialog({content: '请选择机构！', quickClose: true, timeout: 2000}).showModal();
        return false;
    }

    $('#X_Table').setGridParam({
        url: '/proxy-product/product/lowerShelf/querySuperviseProduct',
        postData: {
            "orgCode": $("#orgCode").find("option:selected").val(),
            "stageState": $("#stageState").find("option:selected").val(),
            "channelId": $("#channelId").val()
        }
    }).trigger('reloadGrid');
})
// 导出
$("#exportBtn").on("click", function () {
    utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text())).then( () => {
        return false;
    }).catch( () => {
        //原始处理逻辑代码
        var orgCode = $("#orgCode").val();
        if (orgCode==undefined||orgCode==''){
            utils.dialog({content: '请选择机构后再导出！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        utils.dialog({
            title: '提示',
            content:"数据量大的时候耗时较长，请耐心等待。",
            okValue: '确定',
            ok: function () {
                //parent.showLoading()
                var body = document.body;
                var form = $(body).find('form#searchForm');
                $(form).attr("action","/proxy-product/product/lowerShelf/exportSuperviseProduct");
                $(form).submit();
                // setTimeout(function () {
                //     parent.hideLoading()
                // },2000)
            },
            cancelValue: '取消',
            cancel: function () { },
        }).showModal();
    })
});

//商品搜索图标
$(document).on("click", ".glyphicon-search", function () {
    $(this).siblings("input").trigger("dblclick")
})
//业务类型 输入框双击 弹出业务类型列表
$("#channelId_inp").dblclick(function () {
    utils.channelDialog('0').then( res => {
        console.log(res)
        let _str_name = '', _str_code = '';
        let _str_arr = res.map(item => {
            return item.channelName;
        })
        _str_name = _str_arr.join(',');

        let _str_code_arr = res.map(item => {
            return item.channelCode;
        })
        _str_code = _str_code_arr.join(',')
        $('#channelId_inp').val(_str_name)
        $('#channelId').val(_str_code)
    })
});


//时间格式化
function dateFormatter(inputTime) {
    if (inputTime == null || inputTime == "")
        return "";
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    var minute = date.getMinutes();
    var second = date.getSeconds();
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d;
    // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
}
//设置显示列
$("#setRow").click(function () {
    $('#X_Table').XGrid('filterTableHead',800);
});