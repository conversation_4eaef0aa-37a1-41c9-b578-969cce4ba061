$(function () {
    var baseProductId=$("#baseProductId").val();
    $('div[fold=head]').fold({
        sub: 'sub'
    });
//显示流程图
    var processInstaId=$("#processId").val();
    if(!processInstaId || processInstaId == ""){
        processInstaId=$("#approvalProcessId").val();
    }
    initApprovalFlowChart("customerFirstCampApply", processInstaId);

    $("input[name='itemValue']:not(:checked)").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').hide();
    });
    $("input[name='itemValue']:checked").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').show();
    });

    // //是否三证合一 变化
    // $(".threeEvidenceAll").on("change",function(){
    //     var $this = $(this);
    //     var threeEvidenceAll = $this.val();
    //     checkThreeInOne(threeEvidenceAll);
    // });
    //是否三证合一 变化
    $("input[name='businessLicenseNum']").on("blur",function(){
        checkThreeInOne($('input:radio[name="threeInOne"]:checked').val());
    });
    function checkThreeInOne(threeEvidenceAll){
        if("1"==threeEvidenceAll){//是三证合一
            if($("#operationModeVal").val()=="非营利性"){
                $(".businessLicenseNum").attr("disabled","disabled");
                $(".businessLicenseNum").val("");
            }
            $(".organizationCodeNumber").attr("readonly", true)//组织机构代码号
            $(".taxRegistryNumber").attr("readonly", true)//税务登记证号
        }else{
            if($(".businessLicenseNum").prop("disabled")){
                $(".businessLicenseNum").removeAttr("disabled");
            }
            $(".organizationCodeNumber").attr("readonly", false)//组织机构代码号
            $(".taxRegistryNumber").attr("readonly", false)//税务登记证号
        }
        var businessLicenseNum = $(".businessLicenseNum").val();
        $(".organizationCodeNumber").val(businessLicenseNum)//组织机构代码号
        $(".taxRegistryNumber").val(businessLicenseNum)//税务登记证号
        $(".taxpayerNum").val(businessLicenseNum)//纳税人识别号
    }
    //点击特殊属性返显证书类型
    $("input[name='specialBusinessScope']").change(function() {
        let specialBusinessScopeVal = $(this).val();
        /**
         * RM 2019-07-23
         */
        if($("input[name='specialBusinessScope']:checked").length > 0){
            // 版本一
            if (specialBusinessScopeVal == '12') { // 操作 无特殊属性时
                $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').not(':last').prop('checked', false);
            } else {
                $('#specialBusinessScope').parent().find('[name=specialBusinessScope]:last').prop({'checked': false,'disabled': false });// 无特殊属性 不允许选
            }
            // 版本二
            // if(specialBusinessScopeVal == '12'  ){ // 操作 无特殊属性时
            //     $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').not(':last').prop('disabled',true);
            // }else{
            //     $('#specialBusinessScope').parent().find('[name=specialBusinessScope]:last').prop('disabled',true); // 无特殊属性 不允许选
            //     $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').not(':last').prop('disabled',false);
            // }
        }else{
            // 没有选中项时 全部放开允许点击
            $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').prop('disabled',false);
        }
        /*----------2019-07-23 END------------------*/
        if($(this).prop("checked")){
            $.ajax({
                type:"post",
                url: "/proxy-customer/customer/customerFirstAppl/listByCustomerSpecialAttrAndType",
                async : false,
                data:{"type":2,"customerSpecialAttr":specialBusinessScopeVal},
                dataType:"json",
                success: function (data) {
                    if(data.code == 0)
                    {
                        var arr =data.result;
                        if(arr!=null){
                            var credentialTypeIds = [];
                            if($("select[name='credentialTypeId']").length > 0){
                                $("select[name='credentialTypeId']").each(function(){
                                    credentialTypeIds.push($(this).val());
                                });
                            }
                            for(var i=0;i<arr.length;i++)
                            {
                                if($.inArray(arr[i].batchId+"",credentialTypeIds) == -1){
                                    addRow('#table3');
                                    var id = $('#table3').find('tr:last').attr('id');
                                    $('#isInit').val(1);
                                    $('#table3').find('tr:last').find("select[name='credentialTypeId']").val(arr[i].batchId);
                                    var batchId = arr[i].batchId;
                                        console.log(batchId,id,"aaa");
                                        getZtreeData(batchId,id);
                                }
                            }
                        }
                    }
                },
                error:function () {
                    utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
                }
            });
        }else{
            $.ajax({
                type:"post",
                url: "/proxy-customer/customer/customerFirstAppl/listByCustomerSpecialAttrAndType",
                async : false,
                data:{"type":2,"customerSpecialAttr":specialBusinessScopeVal},
                dataType:"json",
                success: function (data) {
                    if(data.code == 0)
                    {
                        var arr =data.result;
                        if(arr!=null){
                            var specialBusinessScopes = [];
                            for(var x = 0; x < $("input[name='specialBusinessScope']:checked").length; x ++){
                                specialBusinessScopes.push($("input[name='specialBusinessScope']:checked")[x].value);
                            }
                            //第一步获取要删除的证书
                            for(var i=0;i<arr.length;i++)
                            {
                                $('#table3').find("select[name='credentialTypeId']").each(function(){
                                    if($(this).val() == arr[i].batchId+"" && $(this).find("option:selected").attr("data-value") != ""){
                                        var specialBusinessScopeIds = $(this).find("option:selected").attr("data-value").split(",");
                                        var check = 0;
                                        for(var j = 0; j < specialBusinessScopeIds.length; j ++){
                                            if($.inArray(specialBusinessScopeIds[j],specialBusinessScopes) != -1){
                                                check ++;
                                            }
                                        }
                                        if(check==0){
                                            $("#table3").XGrid('delRowData',$(this).parents("tr").attr("id"));
                                            initBaseDataBuseScope();
                                        }
                                    }
                                });
                            }
                        }
                    }
                },
                error:function () {
                    utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
                }
            });
        }
    });
    //特殊经营范围回显
    var specialBusinessScope = $('#specialBusinessScope').val().split(',');
    /**
     *  RM 2019-07-23
     */
    // 版本一
    if(specialBusinessScope.indexOf('12') < 0) {// 无特殊属性未被选中
        $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').not(':last').prop('checked', false);
    } else {
        $('#specialBusinessScope').parent().find('[name=specialBusinessScope]:last').prop({'checked': false,'disabled': false });// 无特殊属性 不允许选
    }
    // 版本二
    // if(specialBusinessScope.indexOf('12') < 0){// 无特殊属性未被选中
    //     $('#specialBusinessScope').parent().find('[name=specialBusinessScope]:last').prop('disabled',true); // 无特殊属性 不允许选
    // }else{
    //     $('#specialBusinessScope').parent().find('[name=specialBusinessScope]').not(':last').prop('disabled',true); // 别的都不允许选
    // }
    /*---------2019-07-23 END-------------*/
    $("input[name='specialBusinessScope']").each(function () {
        for(var x = 0; x < specialBusinessScope.length; x ++){
            if($(this).val() == specialBusinessScope[x]){
                $(this).prop("checked",true);
            }
        }
    });


    //经营类别回显
    if ($('#businessCategory').val()){
        var businessCategory = $('#businessCategory').val().split(',');
        $("input[name='businessCategoryName']").each(function () {
            for(var x = 0; x < businessCategory.length; x ++){
                if($(this).val() == businessCategory[x]){
                    $(this).attr("checked","checked");
                }
            }
        });
    }
    //如果没有 不可经营类别回显 ，默认写死 4，5
    // if ($('#cannotBusinessCategory').val()){
    // } else {
    //     $('#cannotBusinessCategory').val('4,5');
    // }
    if ($('#cannotBusinessCategory').val()) {
        var cannotBusinessCategory = $('#cannotBusinessCategory').val().split(',');
        $("input[name='cannotBusinessCategoryName']").each(function () {
            for(var x = 0; x < cannotBusinessCategory.length; x ++){
                if($(this).val() == cannotBusinessCategory[x]){
                    $(this).attr("checked","checked");
                }
            }
        });
    }


    //特殊属性联动经营范围

    //委托人类型设置不可重复选择
    $("#table2").on("mousedown",'select[name="mandatorType"]',function () {
        //selCannotRepeatChoice(this,'table2','mandatorType');
    })
    //批准文件证书类型设置不可重复选择
    $("#table3").on("mousedown",'select[name="credentialTypeId"]',function () {
        //selCannotRepeatChoice(this,'table3','credentialTypeId');
    })
    //助记码
    $("#customerName").on("blur",function (ev) {
        if(ev.keyCode != 13)
        {
            var value=this.value;
            getMnemonicCode(value,'customerMnemonicCode');
        }
    })

    //经营类别
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryClient",{paramName:'clientName', params:{"type":1}},"businessCategory",{data:"clientId",value:"clientName"});
    //客户类别
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryClient",{paramName:'clientName', params:{"type":2}},"customerType",{data:"clientId",value:"clientName"});
    //经营方式
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryClient",{paramName:'clientName', params:{"type":3}},"operationMode",{data:"clientId",value:"clientName"});
    //委托人类型


    //添加发票邮寄地址
    // $("#billAddress").on("click",".addbill", function () {
    //     if($("select[name^='billingProvinceId']").length >= 2){
    //         utils.dialog({content: '发票邮寄地址最多只能填两个。', quickClose: true, timeout: 2000}).showModal();
    //         return false;
    //     }else{
    //         var obj = distpickerHTML(1,1);
    //         $("#billAddress").append(obj.html);
    //         let billAddressSelIdObj = [
    //             {nextNodeWrap: '#billProvinceSel_wrap_'+ obj.radomInit,nextNodeName: 'billingProvinceId_'+ obj.radomInit,nextNodeId: 'billingProvinceId_'+ obj.radomInit},
    //             {nextNodeWrap: '#billCitySel_wrap_'+ obj.radomInit,nextNodeName: 'billingCityId_'+ obj.radomInit,nextNodeId: 'billingCityId_'+ obj.radomInit},
    //             {nextNodeWrap: '#billDistrictSel_wrap_'+ obj.radomInit,nextNodeName: 'billingDistrictId_'+ obj.radomInit,nextNodeId: 'billingDistrictId_'+ obj.radomInit},
    //             {nextNodeWrap: '#billStreetSel_wrap_'+ obj.radomInit,nextNodeName: 'billingStreetId_'+ obj.radomInit,nextNodeId: 'billingStreetId_'+ obj.radomInit},
    //         ]
    //         utils.setAllProDom('#billProvinceSel_wrap_' + obj.radomInit, billAddressSelIdObj, '#billingBox', true,function () {
    //             $(billAddressSelIdObj).each((index,item) => {
    //                 if(index == 3) {
    //                     $('#' + item.nextNodeId).removeClass('{validate:{ required :true}}')
    //                 }
    //             });
    //             $('#billAddress [id^=billingBox]').find('select').prop('disabled',false)
    //         });
    //     }
    // });
    // //删除发票邮寄地址
    // $("#billAddress").on("click", ".removeDepot", function () {
    //     $(this).parents(".billList").remove();
    // });

    //tab切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    })
    //切换的质量保证协议
    // $('#table1').XGrid({
    //     url:'/proxy-customer/customer/customerCommonData/getCustomerQualityAgreementAll?correlationId='+$('#customerApplId').val()+'&type='+$('#custType').val(),
    //     colNames: ['', '<i class="i-red">*</i>签订日期', '<i class="i-red">*</i>有效期至', '<i class="i-red">*</i>签订人', '附件','附件数据'],
    //     colModel: [{
    //         name: 'id', //与反回的json数据中key值对应
    //         index: 'id', //索引。其和后台交互的参数为sidx
    //         key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    //         hidden: true
    //     }, {
    //         name: 'signedDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         },
    //         index: 'signedDate',
    //         rowtype: '#grid_date'
    //     }, {
    //         name: 'validDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         },
    //         index: 'validDate',
    //         rowtype: '#effective_date'
    //     }, {
    //         name: 'signer',
    //         index: 'signer',
    //         rowtype: '#signer'
    //     }, {
    //         name: 'enclosureCount',
    //         index: 'enclosureCount',
    //         formatter:function (value) {
    //             var str='无';
    //             if(value)
    //             {
    //                 var tableId = "#table1"
    //                 str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
    //             }
    //             return str;
    //         },
    //         unformat: function (e) {
    //             e=e.replace(/<[^>]+>/g,'');
    //             if(e == '无'){
    //                 e = 0;
    //             }
    //             return e;
    //         }
    //     },{
    //         name:'customerEnclosureVoList',
    //         index:'customerEnclosureVoList',
    //         hidden:true,
    //         formatter:function (value) {
    //             if(value)
    //             {
    //                 return JSON.stringify(value);
    //             }
    //             return JSON.stringify([]);
    //         }
    //     }],
    //     altRows: true,
    //     rownumbers: true/*,
    //     rowNum: 10,
    //     altRows: true, //设置为交替行表格,默认为false
    //     ondblClickRow: function (id,dom,obj,index,event) {
    //      console.log('双击行事件', id,dom,obj,index,event);
    //      },
    //      onSelectRow: function (id,dom,obj,index,event) {
    //      console.log('单机行事件', id,dom,obj,index,event);
    //      },
    //     pager:'#table1_page'*/
    // });
    //批量上传 质量保证协议
    // $("#zlbzUpload").on("click", function () {
    //     //获取type类型
    //     var typeList=[];
    //     var eChoImgList=[];
    //     var $table=$('#table1');
    //     var rowData=$table.getRowData();
    //     var $tr=$table.find("tr").not(":first");
    //     for(var i=0;i<$tr.length;i++)
    //     {
    //         var sel=$tr.eq(i).find("input[name='signer']");
    //         typeList.push({
    //             text:sel.val(),
    //             value:sel.val()
    //         });
    //         console.log(sel.val())
    //         //添加已存在附件
    //         if(rowData[i].customerEnclosureVoList.length > 0)
    //         {
    //             rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
    //             for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
    //             {
    //                 rowData[i].customerEnclosureVoList[j].type=sel.val();
    //             }
    //             eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
    //         }
    //     }
    //     $(this).upLoad({
    //         typeList:typeList,//格式[{text:xxx,value:xxx}]
    //         eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
    //         fileParam:{
    //             name:'enclosureName',
    //             url:'url'
    //         },
    //         urlBack: function (data) {
    //             //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
    //             console.log(data)
    //             if($.isEmptyObject(data)){
    //                 $table.find("tr").each(function () {
    //                     var id=$(this).attr('id');
    //                     if(id){
    //                         $table.setRowData(id,{'customerEnclosureVoList':[]});
    //                         $table.setRowData(id,{'enclosureCount':''});
    //                     }
    //                 })
    //                 return false;
    //             }
    //             var listArr = [];
    //             for(let j=0;j<rowData.length;j++){
    //                 var l = [];
    //                 for(let i = 0; i<data.length; i++){
    //                     var list = data[i];
    //                     //data.splice()
    //                     if(rowData[j].signer == data[i].type){
    //                         l.push(list);
    //                     }
    //                 }
    //                 var trId=$table.find("tr").eq(j+1).attr('id');
    //                 $table.setRowData(trId,{'customerEnclosureVoList':l});
    //                 $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
    //             }
    //         }
    //     });
    // });

    //客户委托书
    $('#table2').XGrid({
        url:'/proxy-customer/customer/customerCommonData/getCustomerDelegationFileAll?correlationId='+$('#customerApplId').val()+'&type='+$('#custType').val(),
        // colNames: ['', '<i class="i-red">*</i>委托人类型', '<i class="i-red">*</i>姓名', '<i class="i-red">*</i>性别', '<i class="i-red">*</i>电话', '<i class="i-red">*</i>证件号码',
        //      '<i class="i-red">*</i>身份证有效期至', '<i class="i-red">*</i>委托书有效期至', '附件','附件数据'
        // ],
        colNames: ['', '<i class="i-red">*</i>委托人类型', '<i class="i-red">*</i>姓名',
            '<i class="i-red">*</i>电话', '<i class="i-red">*</i>委托书有效期至', '附件','附件数据'
        ],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'mandatorType',
            index: 'mandatorType',
            rowtype: '#clientTypeId'
        },
            {
            name: 'delegationName',
            index: 'delegationName',
            rowtype: '#delegationName'
        },
        //     {
        //     name: 'delegationSex',
        //     index: 'delegationSex',
        //     rowtype: '#delegationSex'
        // },
            {
            name: 'delegationTel',
            index: 'delegationTel',
            rowtype: '#delegationTel'
        },
        //     {
        //     name: 'delegationNum',
        //     index: 'delegationNum',
        //     rowtype: '#delegationNum'
        // },
        //     {
        //     name: 'delegationAddr',
        //     index: 'delegationAddr',
        //     rowtype: '#delegationAddr'
        // },
        //     {
        //     name: 'delegationIdentityDate',
        //     index: 'delegationIdentityDate',
        //     formatter:function(value){
        //         var date=value;
        //         if(!value)return false;
        //         date=format(value);
        //         return date.split(' ')[0];
        //     },
        //     rowtype: '#delegationIdentityDate'
        // },
            {
            name: 'validityDelegationCredential',
            index: 'validityDelegationCredential',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            },
            rowtype: '#validityDelegationCredential'
        }, {
            name: 'enclosureCount',
            index: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#table2";
                    str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        },{
            name:'customerEnclosureVoList',
            index:'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],
        altRows: true,
        rownumbers: true
        // pager:'#table2_page'
    });

    //客户委托书，远程身份证号重复校验提示
    $("#table2").on("blur","input[name=delegationNum]",function (e) {
        var val = $(e.target).val();
        if(val){
            $.ajax({
                url:"/proxy-customer/customer/customerDelegationFile/checkoutExist",
                data:{delegationNum:val},
                type:'get',
                success:function (res) {
                    console.log('身份证',res)
                    if(res.length!=0){
                        utils.dialog({content: '身份证号已在【'+res[0].customerCode+'】使用，不可重复使用', quickClose: true, timeout: 2000}).showModal();
                    }
                }
            })
        }
    });

    //批量上传 客户委托书
    $("#knwtUplode").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#table2');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("input[name='delegationName']");
            typeList.push({
                text:sel.val(),
                value:sel.val(),
                lineNum: i
            });
            console.log(sel.val())
            //添加已存在附件
            if(rowData[i].customerEnclosureVoList.length > 0)
            {
                rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
                for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
                {
                    rowData[i].customerEnclosureVoList[j].type=sel.val();
                }
                eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
            }
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'customerEnclosureVoList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData[j].delegationName == data[i].type && j == data[i].lineNum){
                            l.push(list);
                        }
                    }
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    $table.setRowData(trId,{'customerEnclosureVoList':l});
                    $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
                }
                /*for(var name in data)
                {
                    var list=data[name];
                    for(var i=0;i<rowData.length;i++)
                    {
                        if(rowData[i].mandatorType == name)
                        {
                            var trId=$table.find("tr").eq(i+1).attr('id');
                            $table.setRowData(trId,{'customerEnclosureVoList':list});//存储时只能存字符串，取值时用JSON.parse转换
                            $table.setRowData(trId,{'enclosureCount':(list.length == 0?'':list.length)});
                            break;
                        }
                    }
                }*/
            }
        });
    });

    //被委托人身份证
    $('#certTable').XGrid({
        url:'/proxy-customer/customer/customerCommonData/getCustomerBeProxyIdentityAll?correlationId='+$('#customerApplId').val()+'&type='+$('#custType').val(),
        colNames: ['', '<i class="i-red">*</i>姓名', '<i class="i-red">*</i>证件号码', '<i class="i-red">*</i>身份证有效期至', '附件','附件数据'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'customerName',
            index: 'customerName',
            rowtype: '#mandataryCustomerName'
        }, {
            name: 'identityNumber',
            index: 'identityNumber',
            rowtype: '#identityNumber'
        }, {
            name: 'identityValidityDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            },
            index: 'identityValidityDate',
            rowtype: '#identityValidityDate'
        }, {
            name: 'enclosureCount',
            index: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#certTable";
                    str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        },{
            name:'customerEnclosureVoList',
            index:'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],
        altRows: true,
        rownumbers: true/*,
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id,dom,obj,index,event) {
         console.log('双击行事件', id,dom,obj,index,event);
         },
         onSelectRow: function (id,dom,obj,index,event) {
         console.log('单机行事件', id,dom,obj,index,event);
         },
        pager:'#certTable_page'*/
    });
    //批量上传 委托人身份证
    $("#bwtrUplode").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#certTable');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("input[name='customerName']");
            typeList.push({
                text:sel.val(),
                value:sel.val()
            });
            console.log(sel.val())
            //添加已存在附件
            if(rowData[i].customerEnclosureVoList.length > 0)
            {
                rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
                for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
                {
                    rowData[i].customerEnclosureVoList[j].type=sel.val();
                }
                eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
            }
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'customerEnclosureVoList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData[j].customerName == data[i].type){
                            l.push(list);
                        }
                    }
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    $table.setRowData(trId,{'customerEnclosureVoList':l});
                    $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
                }
                /*for(var name in data)
                {
                    var list=data[name];
                    for(var i=0;i<rowData.length;i++)
                    {
                        if(rowData[i].customerName == name)
                        {
                            var trId=$table.find("tr").eq(i+1).attr('id');
                            $table.setRowData(trId,{'customerEnclosureVoList':list});//存储时只能存字符串，取值时用JSON.parse转换
                            $table.setRowData(trId,{'enclosureCount':(list.length == 0?'':list.length)});
                            break;
                        }
                    }
                }*/
            }
        });
    });
    initTable3($('#customerApplId').val(),$('#custType').val());
    $(".ApprovalUploadeImg").removeAttr('disabled');
    //批量上传  批准文件
    $("#pzwjUplode").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#table3');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("select[name='credentialTypeId'] option:selected");
            typeList.push({
                text:sel.text(),
                value:sel.val(),
                lineNum: i
            });
            console.log(sel.val())
            //添加已存在附件
            if(rowData[i].customerEnclosureVoList.length > 0){
                rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
                for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++){
                    rowData[i].customerEnclosureVoList[j].type=sel.val();
                    rowData[i].customerEnclosureVoList[j].lineNum = i;
                }
                eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
            }
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'customerEnclosureVoList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }

                var listArr = [];
                for(let j=0;j<rowData.length;j++){
                    var l = [];
                    for(let i = 0; i<data.length; i++){
                        var list = data[i];
                        //data.splice()
                        if(rowData[j].credentialTypeId == data[i].type && j == data[i].lineNum){
                            l.push(list);
                        }
                    }
                    var trId=$table.find("tr").eq(j+1).attr('id');
                    $table.setRowData(trId,{'customerEnclosureVoList':l});
                    $table.setRowData(trId,{'enclosureCount':(l.length == 0?'':l.length)});
                }
                /*for(var name in data)
                {
                    var list=data[name];
                    for(var i=0;i<rowData.length;i++)
                    {
                        if(rowData[i].credentialTypeId == name)
                        {
                            var trId=$table.find("tr").eq(i+1).attr('id');
                            $table.setRowData(trId,{'customerEnclosureVoList':list});//存储时只能存字符串，取值时用JSON.parse转换
                            $table.setRowData(trId,{'enclosureCount':(list.length == 0?'':list.length)});
                            break;
                        }
                    }
                }*/
            }
        });
    });

    //客户合同回显
    // $.ajax({
    //     url:'/proxy-customer/customer/customerCommonData/getCustomerContractAll?correlationId='+$('#customerApplId').val()+'&type='+$('#custType').val(),
    //     dataType:'json',
    //     //async:false,
    //     success:function(data){
    //         if(!data.result) return false;
    //         if(data.result.length){
    //             $(data.result).each(function(contractIndex,ontractItem){
    //                 var contractTypes = $('#contractType').find('input[type=checkbox]');
    //                 $(contractTypes).each(function(index,item){
    //                     if(ontractItem.contractType==$(this).val()){
    //                         $(this).attr("checked",'true');
    //                         if(ontractItem.customerEnclosureVoList && ontractItem.customerEnclosureVoList.length > 0){
    //                             for(var j=0;j<ontractItem.customerEnclosureVoList.length;j++)
    //                             {
    //                                 ontractItem.customerEnclosureVoList[j].type = ontractItem.contractType;
    //                             }
    //                             var arr=JSON.stringify(ontractItem.customerEnclosureVoList);
    //                             var html='<input type="hidden" data-type="'+ontractItem.contractType+'" id="contractType'+ontractItem.contractType+'" value=\''+arr+'\' />';
    //                             $("body").append(html);
    //                         }
    //                     }
    //                 });
    //             });
    //         }
    //     },
    //     error:function(){
    //
    //     }
    // });
    // //批量上传 客户合同
    // $("#qtfjUpload").on("click", function() {
    //     //获取type类型
    //     var typeList=[];
    //     var eChoImgList=[];
    //     /*
    //     * 操作流程：
    //     * 点击批量管理附件按钮时候、先判断 条件1：checkbox 是否有选中，条件2： 是否弹窗里已经含有附件。
    //     * 情况1： 如果没勾选checkbox。 也没有包含附件，。 打开弹窗后下拉框option 显示所有的checkbox 项；
    //     * 情况2： 如果checkbox有勾选，但是没有包含附件， 碳层下拉框只显示选中的checkbox项；
    //     * 情况3：既有勾选，也有包含附件。弹层下拉框显示所有的checkbox选项、
    //     * */
    //     var checkLen=$("#contractType input[type='checkbox']:checked").length;
    //     var inpdata = $('input[id^=contractType]'),inpdataArr = [], inpadataFinalArr = [];
    //     $(inpdata).each(function (index,item) {
    //         inpdataArr.push(JSON.parse(item.value));
    //     })
    //     for(var i = 0; i<inpdataArr.length; i++ ){
    //         for(var j = 0; j<inpdataArr[i].length; j++){
    //             inpadataFinalArr.push(inpdataArr[i][j])
    //         }
    //     }
    //     $("#contractType label").each(function(){
    //         if(checkLen < 1 && inpadataFinalArr.length <1){  // 情况1
    //             var text=$.trim($(this).text());
    //             var v=$(this).prev("input").val();
    //             typeList.push({
    //                 text:text,
    //                 value:v
    //             })
    //         }else if(checkLen > 0 && inpadataFinalArr.length <1 ){ // 情况2
    //             var checked=$(this).prev('input[type=checkbox]')[0].checked; //$(this).find("input[type='checkbox']").get(0).checked;
    //             if(checked){
    //                 var text=$.trim($(this).text());
    //                 var v=$(this).prev("input").val();
    //                 typeList.push({
    //                     text:text,
    //                     value:v
    //                 })
    //             }
    //         }else if(checkLen > 0 && inpadataFinalArr.length > 0){ // 情况3
    //             var text=$.trim($(this).text());
    //             var v=$(this).prev("input").val();
    //             typeList.push({
    //                 text:text,
    //                 value:v
    //             })
    //             if($("input[id='contractType"+v+"']").length > 0){
    //                 var imgArr=$("input[id='contractType"+v+"']").val();
    //                 //console.log(imgArr,JSON.parse(imgArr))
    //                 eChoImgList = eChoImgList.concat(JSON.parse(imgArr));
    //             }
    //         }else{  //情况4：去掉了勾选，但还有包含附件。弹层下拉框显示所有的checkbox选项、
    //             var text=$.trim($(this).text());
    //             var v=$(this).prev("input").val();
    //             typeList.push({
    //                 text:text,
    //                 value:v
    //             })
    //             if($("input[id='contractType"+v+"']").length > 0){
    //                 var imgArr=$("input[id='contractType"+v+"']").val();
    //                 //console.log(imgArr,JSON.parse(imgArr))
    //                 eChoImgList = eChoImgList.concat(JSON.parse(imgArr));
    //             }
    //         }
    //     });
    //     //console.log(typeList)
    //     //console.log(eChoImgList)
    //     $(this).upLoad({
    //         typeList:typeList,//格式[{text:xxx,value:xxx}]
    //         eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
    //         fileParam:{
    //             name:'enclosureName',
    //             url:'url'
    //         },
    //         urlBack: function (data) {
    //             //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
    //             //存放数据
    //             //files
    //             console.log(data)
    //             var inp = $('#contractType input')
    //             var html='';
    //             $('input[id^=contractType]').remove();
    //
    //             $("input[name='contractType']").each(function(){
    //                 var dataValue = $(this).val();
    //                 var list = [];
    //                 for(var name in data){
    //                     if(data[name].type == dataValue){
    //                         list.push(data[name]);
    //                     }
    //
    //                 }
    //                 if(list.length > 0){
    //                     var arr=JSON.stringify(list);
    //                     //html='<input type="hidden" data-type="'+name+'" id="newother'+name+'" value=\''+arr+'\' />';
    //                     html='<input type="hidden" data-type="'+dataValue+'" id="contractType'+dataValue+'" value=\''+arr+'\' />';
    //                     $("body").append(html);
    //                 }
    //             });
    //             var AllInpHide = $('input[id^=contractType]');
    //             if(AllInpHide.length > 0){ //  如果存在附件，再去设置checkbox的回显。
    //                 for(var i = 0; i<inp.length; i++){
    //                     inp[i].checked = false;
    //                     for(var j = 0; j<AllInpHide.length; j++){
    //                         if(inp[i].value == AllInpHide[j].getAttribute('data-type')){
    //                             inp[i].checked = true
    //                         }
    //                     }
    //                 }
    //             }else{ //  如果不存在附件，或者说附件被删除，设置checkbox的 全部未选中。
    //                 for(var i = 0; i<inp.length; i++){
    //                     inp[i].checked = false;
    //                 }
    //             }
    //         }
    //     });
    // });
    // //客户合同 查看附件
    // $("#qtfjView").on("click",function(){
    //     //获取type类型
    //     var imgList=[];
    //     var checkInp=$("#contractType input[type='checkbox']:checked");
    //     if(checkInp.length)
    //     {
    //         for(var i=0;i<checkInp.length;i++)
    //         {
    //             var type=checkInp.eq(i).val();
    //             var inp=$("#contractType"+type);
    //             if(inp.length)
    //             {
    //                 var imgArr=JSON.parse(inp.val());
    //                 imgList=imgList.concat(imgArr);
    //             }
    //         }
    //     }
    //     $.viewImg({
    //         fileParam:{
    //             name:'enclosureName',
    //             url:'url'
    //         },
    //         list:imgList
    //     })
    // });
    //
    // //客户合同双击选项预览对应的附件
    // $('#contractType label').each(function () {
    //     $(this).dblclick(function () {
    //         var imgList=[];
    //         var type = $(this).prev().val();
    //         var inp=$("#contractType"+type);
    //         if(inp.length){
    //             var imgArr=JSON.parse(inp.val());
    //             imgList=imgList.concat(imgArr);
    //         }
    //         $.viewImg({
    //             fileParam:{
    //                 name:'enclosureName',
    //                 url:'url'
    //             },
    //             list:imgList
    //         });
    //     })
    // })

    $("#busineessScopeAll").attr("disabled","disabled");

    //审核通过
    $("#saveRowData").on("click", function () {
        //$("#statues").val(0);
        getSavedData();
    });
    // 注册地址
    let edit = $('#edit').val()
    // 注册地址
    let registerAddressSelIdObj = [
        {nextNodeWrap: '#provinceSel_wrap',nextNodeName: 'registerProvinceId',nextNodeId: 'province1'},
        {nextNodeWrap: '#citySel_wrap',nextNodeName: 'registerCityId',nextNodeId: 'city1'},
        {nextNodeWrap: '#districtSel_wrap',nextNodeName: 'registerDistrictId',nextNodeId: 'district1'},
        {nextNodeWrap: '#streetSel_wrap',nextNodeName: 'registerStreetId',nextNodeId: 'street1'},
    ]
    let registerPromiseArray = [];
    utils.setAllProDom('#provinceSel_wrap', registerAddressSelIdObj, '#registerBox',true, function () {
        // 注册地址有值回显
        let _registerHiddenVal = eval($('#registerAddressJson').val());
        if (!_registerHiddenVal) _registerHiddenVal = ['','','','',''];
        // _registerHiddenVal.splice(_registerHiddenVal.length - 1);
        // $('#' + registerAddressSelIdObj[0]['nextNodeId']).prop('disabled', true);
        $('#' + registerAddressSelIdObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
        // $('#' + registerAddressSelIdObj[0]['nextNodeId']).parents('.distpicker').find('input[type=text]').attr('maxLength', '50')
        $('#' + registerAddressSelIdObj[0]['nextNodeId']).parents('.distpicker').find('[name=registerAddress]').val(_registerHiddenVal[4]);
        for (let i = 1; i < _registerHiddenVal.length-1; i++) {
            registerPromiseArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
        }
        Promise.all(registerPromiseArray).then(data => {
            // console.log(data)
            for (let i = 0; i < data.length; i++) {
                $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).html(data[i]);
                $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).attr('data-depth',i+2);
                $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
                if(edit != '1') {
                    $('#' + registerAddressSelIdObj[i + 1]['nextNodeId']).prop('disabled', true);
                }else{
                    $('#registerBox select,#registerBox input').removeAttr('disabled readonly');
                }
            }
        })
        $(registerAddressSelIdObj).each((index,item) => {
            if(index == 3) {
                $('#' + item.nextNodeId).removeClass('{validate:{ required :true}}')
            }
        })
    });


    // 仓库地址
    // let storgeAddressSelIdObj = [
    //     {nextNodeWrap: '#stoProvinceSel_wrap',nextNodeName: 'repertoryProvince',nextNodeId: 'repertoryProvince'},
    //     {nextNodeWrap: '#stoCitySel_wrap',nextNodeName: 'repertoryCity',nextNodeId: 'repertoryCity'},
    //     {nextNodeWrap: '#stoDistrictSel_wrap',nextNodeName: 'repertoryArea',nextNodeId: 'repertoryArea'},
    //     {nextNodeWrap: '#stoStreetSel_wrap',nextNodeName: 'repertoryStreet',nextNodeId: 'repertoryStreet'}
    // ];
    // let storgePromiseArray = [];
    // utils.setAllProDom('#stoProvinceSel_wrap', storgeAddressSelIdObj, '#storageBox', true,function () {
    //     // 仓库地址有值回显
    //     let _storgeHiddenVal = eval($('#repertoryAddressJson').val());
    //     if (!_storgeHiddenVal) _storgeHiddenVal = ['','','','',''];
    //     $('#' + storgeAddressSelIdObj[0]['nextNodeId']).val(_storgeHiddenVal[0][0]);
    //     $('#' + storgeAddressSelIdObj[0]['nextNodeId']).addClass('{validate:{ required :true}}');
    //     $('#' + storgeAddressSelIdObj[0]['nextNodeId']).parents('.distpicker').find('[name=repertoryDetail]').val(_storgeHiddenVal[0][4]);
    //     for (let i = 1; i < _storgeHiddenVal[0].length; i++) {
    //         storgePromiseArray.push(utils.setAddressReturnVal(_storgeHiddenVal[0][i-1]));
    //     }
    //     Promise.all(storgePromiseArray).then(data => {
    //         for (let i = 1; i < data.length; i++) {
    //             $('#' + storgeAddressSelIdObj[i]['nextNodeId']).html(data[i-1]);
    //             $('#' + storgeAddressSelIdObj[i]['nextNodeId']).attr('data-depth',i+1);
    //             $('#' + storgeAddressSelIdObj[i]['nextNodeId']).val(_storgeHiddenVal[0][i]);
    //             if(edit == '1') {
    //                 $('#depotAddress select,#depotAddress input').removeAttr('disabled readonly');
    //                 $('#depotAddress .btn').css('display','inline-block');
    //             }
    //         }
    //     }).catch(err => {
    //         console.log(err)
    //     });
    //     $(storgeAddressSelIdObj).each((index,item) => {
    //         if (index == 3) {
    //             $('#' + item.nextNodeId).removeClass('{validate:{ required :true}}')
    //         }
    //     })
    // });

    edit = $('#edit').val()
    // 收货地址
    let shippingAddressObj = [
        {nextNodeWrap: '#saProvinceSel_wrap',nextNodeName: 'province2',nextNodeId: 'province2'},
        {nextNodeWrap: '#sacitySel_wrap',nextNodeName: 'shippingAddressCityId',nextNodeId: 'shippingAddressCityId'},
        {nextNodeWrap: '#sadistrictSel_wrap',nextNodeName: 'shippingAddressDistrictId',nextNodeId: 'shippingAddressDistrictId'},
        {nextNodeWrap: '#sastreetSel_wrap',nextNodeName: 'shippingAddressStreetId',nextNodeId: 'shippingAddressStreetId'},
    ]
    let shippingAddressArray = [];
    utils.setAllProDom('#saProvinceSel_wrap', shippingAddressObj, '#shippingAddressBox',true, function () {
        // 收货地址有值回显
        let _registerHiddenVal = eval($('#shippingAddressJson').val());
        if (!_registerHiddenVal) _registerHiddenVal = [['','','','']];;
        if(_registerHiddenVal.length>0){
            _registerHiddenVal=_registerHiddenVal[0];
        }
        console.log(_registerHiddenVal);
        _registerHiddenVal.splice(_registerHiddenVal.length - 1);
        // $('#' + registerAddressSelIdObj[0]['nextNodeId']).prop('disabled', true);
        $('#' + shippingAddressObj[0]['nextNodeId']).val(_registerHiddenVal[0]);
        // $('#' + shippingAddressObj[0]['nextNodeId']).parents('.distpicker').find('input[type=text]').attr('maxLength', '50')
        for (let i = 1; i < _registerHiddenVal.length; i++) {
            shippingAddressArray.push(utils.setAddressReturnVal(_registerHiddenVal[i - 1]));
        }
        Promise.all(shippingAddressArray).then(data => {
            // console.log(data)
            for (let i = 0; i < data.length; i++) {
            $('#' + shippingAddressObj[i + 1]['nextNodeId']).html(data[i]);
            $('#' + shippingAddressObj[i + 1]['nextNodeId']).attr('data-depth',i+2);
            $('#' + shippingAddressObj[i + 1]['nextNodeId']).val(_registerHiddenVal[i + 1]);
            if(edit != '1') {
                $('#' + shippingAddressObj[i + 1]['nextNodeId']).prop('disabled', true);
            }else{
                $('#saProvinceSel_wrap select,#shippingAddressBox input').removeAttr('disabled readonly');
                $('#' + shippingAddressObj[i + 1]['nextNodeId']).removeAttr('disabled', true);
            }
        }
    })
        $(shippingAddressObj).each((index,item) => {
            $('#' + item.nextNodeId).removeClass('{validate:{ required :true}}')
    })
    });
    $('#auditOpinion').removeAttr("disabled");

    // 发票邮寄地址
    // let billAddressSelIdObj = [];
    // let _billHiddenVal = eval($('#billingAddressJson').val());
    // if (!_billHiddenVal) _billHiddenVal = [['','','','']];
    // let _billHiddenValArr =  eval($('#billingAddressJson').val());
    // if (!_billHiddenValArr) _billHiddenValArr = [['','','','']];
    // $(_billHiddenValArr).each((index,item) => {
    //     item.splice(item.length - 1);
    // });
    // let billObj = distpickerHTML(_billHiddenValArr.length);
    // $(billObj.radomInit).each((index, item) => {
    //     let _arr = [
    //         {nextNodeWrap: '#billProvinceSel_wrap_' + item,nextNodeName: 'billingProvinceId_' + item,nextNodeId: 'billingProvinceId_' + item},
    //         {nextNodeWrap: '#billCitySel_wrap_' + item,nextNodeName: 'billingCityId_' + item,nextNodeId: 'billingCityId_' + item},
    //         {nextNodeWrap: '#billDistrictSel_wrap_' + item,nextNodeName: 'billingDistrictId_' + item,nextNodeId: 'billingDistrictId_' + item},
    //         {nextNodeWrap: '#billStreetSel_wrap_' + item,nextNodeName: 'billingStreetId_' + item,nextNodeId: 'billingStreetId_' + item}
    //     ]
    //     billAddressSelIdObj.push(_arr)
    // });
    // $('#billAddress').find('.billList').remove();
    // $('#billAddress').append(billObj.html)
    //
    // $(billObj.radomInit).each((index, item) => {
    //     let billPromiseArray = [];
    //     utils.setAllProDom('#billProvinceSel_wrap_' + billObj.radomInit[index], billAddressSelIdObj[index], '#billingBox_'+ billObj.radomInit[index], true, function () {
    //         $('#' + billAddressSelIdObj[index][0]['nextNodeId']).val(_billHiddenValArr[index][0]);
    //         $('#' + billAddressSelIdObj[index][0]['nextNodeId']).parents('.distpicker').find('[name=billingAddress]').val(_billHiddenVal[index][4]);
    //         $('#auditOpinion').removeAttr("disabled");
    //         for (let ind = 1; ind < _billHiddenValArr[index].length; ind++) {
    //             billPromiseArray.push(utils.setAddressReturnVal(_billHiddenValArr[index][ind-1]));
    //         }
    //         let allSelArr = billAddressSelIdObj[index].flat().map((item, index) => {
    //             if (index != 0) {
    //                 return item['nextNodeId']
    //             }
    //         }).filter(item => {
    //             return item
    //         });
    //         Promise.all(billPromiseArray).then(data => {
    //             for (let i = 0; i < data.length; i++) {
    //                 $('#' + allSelArr[i]).html(data[i]);
    //                 $('#' + allSelArr[i]).attr('data-depth',i+2);
    //                 $('#' + allSelArr[i]).val(_billHiddenValArr[index][i + 1]);
    //                 if(edit == '1') {
    //                     $('.billList select,.billList input').removeAttr('disabled readonly');
    //                     $('.billList .btn').css('display','inline-block');
    //                 }
    //             }
    //         })
    //         $(billAddressSelIdObj).each((index,item) => {
    //             $(item).each((ind,ite) => {
    //                 if (ind == 3) {
    //                     $('#' + ite.nextNodeId).removeClass('{validate:{ required :true}}')
    //                 }
    //             })
    //         })
    //     })
    // })



    var townFormat = function(obj,info){
        $(obj).find("select:last").html('');
        if(info['code']%1e4&&info['code']<7e5){	//是否为“区”且不是港澳台地区
            $.ajax({
                url:'/proxy-sysmanage/sysmanage/area/getNexAreaListByPcode?pCode='+info['code'],
                dataType:'json',
                //async:false,
                success:function(town){
                    $(obj).find("select:last").html('');
                    $(obj).find("select:last").append('<option value="">请选择</option>');
                    var arr=town.result;
                    if(arr && arr.length>0)
                    {
                        for(var i=0;i<arr.length;i++)
                        {
                            var code='';
                            var text='';
                            for(name in arr[i]){
                                if(name == 'code')
                                {
                                    code=arr[i][name];
                                }
                                if(name == 'name')
                                {
                                    text=arr[i][name];
                                }
                            }
                            $(obj).find("select:last").append('<option value="'+code+'">'+text+'</option>');
                        }
                        var flag=$(obj).attr("data-flag");
                        if(flag == "true")
                        {
                            var val3=$.trim($(obj).find("select:last").attr("data-value"));
                            if(val3 && val3 != '')
                            {
                                $(obj).find("select:last").val(val3);
                            }
                        }
                        $(obj).attr("data-flag","false");
                    }
                }
            });
        }
    };
    // initCitys($('#registerBox'),'registerProvinceId','registerCityId','registerDistrictId');
    // initCitys($('#storageBox'),'storageProvinceId','storageCityId','storageDistrictId');
    // initCitys($('#billingBox'),'billingProvinceId','billingCityId','billingDistrictId');
    // initCitys($('.depotList'),'billingProvinceId','billingCityId','billingDistrictId');
    //初始化四级联动
    function initCitys(obj,provinceField,cityField,areaField){
        obj.attr("data-flag","true").citys({
            dataUrl:'/proxy-sysmanage/sysmanage/area/getAreaExtStreet',
            provinceField:provinceField, //省份字段名
            cityField:cityField,         //城市字段名
            areaField:areaField,         //地区字段名
            onChange:function(obj,info){
                townFormat(obj,info);
            }
        },function(obj){
            $(obj).find("select:last").html('<option value="">请选择</option>');
            var flag=$(obj).attr("data-flag");
            if(flag == "true")
            {
                var dataVal=$.trim($(obj).find("select:first").attr("data-value"));
                if(!dataVal || dataVal == '')return;

                $(obj).find("select:first").val(dataVal);
                $(obj).find("select:first").change();
                var dataVal1=$.trim($(obj).find("select").eq(1).attr("data-value"));
                $(obj).find("select").eq(1).val(dataVal1);
                var val1=$(obj).find("select").eq(1).val();
                if(!val1 || val1 == '')return;

                $(obj).find("select").eq(1).change();
                var dataVal2=$.trim($(obj).find("select").eq(2).attr("data-value"));
                if(!dataVal2 || dataVal2 == '')return;

                $(obj).find("select").eq(2).val(dataVal2);
                var val2=$(obj).find("select").eq(2).val();

                if(!val2 || val2 == '')return;
                $(obj).find("select").eq(2).change();
            }
        });
    }
});
/**
 * 客户首营申请标志
 * @type {boolean}
 */
window.customerFirstPageLoading = true
$(function () {
    //当页面加载完成 2000 毫秒后，移除 客户首营申请标志
    setTimeout(function () {
        delete window.customerFirstPageLoading
    },2000)
})
$('[name=shippingAddressInput]').on('change',function () {
    if ($('#customerApplType').val() == 6){
        const receiveAddressProvince = $('#province2').val()?$('#province2 option:selected').text():""
        const receiveAddressCity = $('#shippingAddressCityId').val() ? $('#shippingAddressCityId option:selected').text() :""
        const receiveAddressDistrict = $('#shippingAddressDistrictId').val()?$('#shippingAddressDistrictId option:selected').text():""
        const receiveAddressStreet = $('#shippingAddressStreetId').val()?$('#shippingAddressStreetId option:selected').text():""
        const receiveAddressInput = $('input[name=shippingAddressInput]').val()
        const receiveAddress = receiveAddressProvince + receiveAddressCity + receiveAddressDistrict + receiveAddressStreet + receiveAddressInput
        $('input[name=accompanyAddress]').val(receiveAddress)
    }
})

/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showValue(obj,data) {
    var key =$("#"+obj).val();
    for(var i=0;i<data.length;i++){
        if(data[i].data==key){
            $("#"+obj+"Val").val(data[i].value);
        }
    }
}

/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            if (obj == 'customerType') {
                response.result = response.result.filter(item => item.clientName != '基层医疗机构')
                return response
            }else{
                return response
            }
        },
        onSelect: function (result) {
            $("#"+obj).val(result.data);
            if(obj == "operationMode" && $('input:radio[name="threeInOne"]:checked').val()=="1"){
                if(result.value == "非营利性"){
                    var businessLicenseNum = $(".businessLicenseNum").val();
                    $(".businessLicenseNum").attr("disabled", "disabled")//组织机构代码号
                    $(".organizationCodeNumber").attr("readonly", true)//组织机构代码号
                    $(".taxRegistryNumber").attr("readonly", true)//税务登记证号
                    $(".organizationCodeNumber").val("")//组织机构代码号
                    $(".taxRegistryNumber").val("")//税务登记证号
                    $(".businessLicenseNum").val("")//税务登记证号
                }else{
                    if($(".businessLicenseNum").prop("disabled")){
                        $(".businessLicenseNum").removeAttr("disabled");
                    }
                }
            }

            select && select(result)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            $("#"+obj).val("");
            $("#"+obj+"Val").val("");
            if(obj == "operationMode" && $('input:radio[name="threeInOne"]:checked').val()=="1"){
                console.log($(".threeEvidenceAll").val()+"aaa");
                var businessLicenseNum = $(".businessLicenseNum").val();
                $(".organizationCodeNumber").attr("readonly", true)//组织机构代码号
                $(".taxRegistryNumber").attr("readonly", true)//税务登记证号
                $(".organizationCodeNumber").val(businessLicenseNum)//组织机构代码号
                $(".taxRegistryNumber").val(businessLicenseNum)//税务登记证号
            }
            noneSelect && noneSelect();
        }
    });
}

//获取发票邮寄地址
// function distpickerHTML(n,m){
//    var len = n ? n : 1;
//     var html = '';
//     let radomInit = [];
//     for (let i = 0; i < len; i++) {
//         let _int = parseInt(Math.random() * 100 + Math.random() * 1000);
//         html += `<div class="col-md-6 billList">
// 	        <div class="input-group">
// 	            <div class="input-group-addon require">发票邮寄地址</div>
// 	            <div class="form-control form-inline distpicker" id="billingBox_${_int}">
// 	                <div class="row">
// 	                    <div class="form-group col-md-2" id="billProvinceSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingProvinceId_${_int}" id="billingProvinceId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billCitySel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingCityId_${_int}" id="billingCityId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billDistrictSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" name="billingDistrictId_${_int}" id="billingDistrictId_${_int}"></select>
// 	                    </div>
// 	                    <div class="form-group col-md-2" id="billStreetSel_wrap_${_int}">
// 	                        <select class="form-control {validate:{ required :true}}" changeApplyFlag="customerBillingAddressVoList" name="billingStreetId_${_int}" id="billingStreetId_${_int}"></select>
//                         </div>
// 	                    <div class="form-group col-md-3" style="position: initial;">
// 	                        <input type="text" class="form-control {validate:{ required :true}} text-inp Filter_SpaceAnd200StrLen_Class" name="billingAddress" value=""/>
// 	                    </div>
// 	                    <div class="form-group btn-box col-md-1">
// 	                        <button type="button" class="btn ${i==0  &&  !m  ? 'addbill' : 'removeDepot'}" id="addbill">
// 	                            <span class="glyphicon ${ i==0 && !m ? 'glyphicon-plus' : 'glyphicon-minus'}" aria-hidden="true"></span>
// 	                        </button>
// 	                    </div>
// 	                </div>
// 	            </div>
// 	        </div>
// 	    </div>`;
//         radomInit.push(_int);
//     }
//     return {
//         html: html,
//         radomInit: radomInit
//     };
// }

function getMnemonicCode(str,id){
    $.ajax({
        url:'/proxy-product/product/productFirst/getMnemonicCode?name='+str,
        type:'get',
        dataType:'json',
        async : false,
        success:function(data){
            if(data.code == 0)
            {
                $("#"+id).val(data.result);
            }
        }
    })
}

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }


/**
 * 获取提交数据
 * @returns {string}
 */
function getSavedData() {
    /**
     * 客户首营 customerFirstApplVo;
     *批准文件 customerApprovalFileVoList;
     * 质量保证协议 customerQualityAgreementVoList;
     * 客户委托书 customerDelegationFileVoList;
     * 被委托人身份证 customerBeProxyIdentityVoList;
     * 客户合同 customerContractVos;
     * 发票邮寄地址 customerBillingAddressVoList;
     * 结算方式 customerExtendItemVoList;
     * 批号、包装 customerBatchNumberList
     *
     */
    //变更申请基础属性
    $("input[name='disabledYn']").removeAttr("disabled");
    var customerFirstApplVo=$("#customerApplVo").serializeToJSON();
    /*----------*/
    customerFirstApplVo['storageProvinceId'] = customerFirstApplVo['repertoryProvince'];
    customerFirstApplVo['storageCityId'] = customerFirstApplVo['repertoryCity'];
    customerFirstApplVo['storageDistrictId'] = customerFirstApplVo['repertoryArea'];
    customerFirstApplVo['storageStreetId'] = customerFirstApplVo['repertoryStreet'];
    customerFirstApplVo['storageAddress'] = customerFirstApplVo['repertoryDetail'];
    /*----------*/
    //去除经营类别
    // if (Array.isArray(customerFirstApplVo.businessCategoryName)) {
    //     customerFirstApplVo.businessCategoryName = customerFirstApplVo.businessCategoryName.join(",");
    // }
    if (Array.isArray(customerFirstApplVo.cannotBusinessCategoryName)) {
        customerFirstApplVo.cannotBusinessCategoryName = customerFirstApplVo.cannotBusinessCategoryName.join(",");
    }
    customerFirstApplVo.businessCategory = customerFirstApplVo.businessCategoryName;
    customerFirstApplVo.cannotBusinessCategory = customerFirstApplVo.cannotBusinessCategoryName;
    if ($('#customerApplType').val() == '6') {
        customerFirstApplVo.applicationNumber = $("input[name='applicationNumber']").val();
    }
    //var customerBatchNumberVo=$("#customerBatchNumber").serializeToJSON(); // 批号、包装  form数据
    $("input[name='disabledYn']").prop("disabled","disabled");

    let bill_customerNameState = false;
    if($('input[name="billingCompanyName"]').prop('disabled')){
        bill_customerNameState = true;
        $("input[name='billingCompanyName']").removeAttr("disabled");
    }
    $("input[name='taxpayerNum'],input[name='billingOpeningBank'],input[name='billingBankAccount']").removeAttr("disabled");
    let billInfoFormVo = $("#billInfo_form").serializeToJSON(); // 开票信息
    if(bill_customerNameState){
        $("input[name='billingCompanyName']").prop("disabled","disabled");
    }
    $("input[name='taxpayerNum'], input[name='billingOpeningBank'],input[name='billingBankAccount']").prop("disabled","disabled");

    //数组变成字符串

    if (customerFirstApplVo.specialBusinessScope == undefined){
        customerFirstApplVo.specialBusinessScope="";
    }
    var specialBusinessScope =  customerFirstApplVo.specialBusinessScope.toString();
    //重新赋值
    customerFirstApplVo.specialBusinessScope = specialBusinessScope;
    customerFirstApplVo.batchNumberRequirement = customerFirstApplVo.batchNumberRequirement; //
    customerFirstApplVo.packagingRequirement = customerFirstApplVo.packagingRequirement; //
    //开票信息  数据
    customerFirstApplVo.isIndependent = billInfoFormVo.isIndependent
    customerFirstApplVo.taxpayerNum = billInfoFormVo.taxpayerNum
    customerFirstApplVo.billingBankAccount = billInfoFormVo.billingBankAccount
    customerFirstApplVo.billingOpeningBank = billInfoFormVo.billingOpeningBank
    customerFirstApplVo.billingCompanyName = billInfoFormVo.billingCompanyName
    customerFirstApplVo.billingPhone = billInfoFormVo.billingPhone;
    customerFirstApplVo.businessLicenseAddress = billInfoFormVo.businessLicenseAddress;
    customerFirstApplVo.invoiceType = billInfoFormVo.invoiceType;

    var customerBillingAddressVoList = [];
    $('.billList').each(function(){
        var provinceId = $(this).find('select[name^=billingProvinceId] option:selected').val();
        var cityId = $(this).find('select[name^=billingCityId] option:selected').val();
        var districtId = $(this).find('select[name^=billingDistrictId] option:selected').val();
        var streetId = $(this).find('select[name^=billingStreetId] option:selected').val();
        var address = $(this).find('input[name=billingAddress]').val();
        var customerBillingAddressVo = {"billingProvinceId":""+provinceId+"","billingCityId":""+cityId +"","billingDistrictId":""+districtId+"","billingStreetId":""+streetId+"","billingAddress":""+address+"",addressType:1};
        customerBillingAddressVoList.push(customerBillingAddressVo)
    });
    var shippingAddress=[];
    $('#shippingAddress').each(function(){
        var saProvinceId = $(this).find('select[name^=province2] option:selected').val();
        var saCityId = $(this).find('select[name^=shippingAddressCityId] option:selected').val();
        var saDistrictId = $(this).find('select[name^=shippingAddressDistrictId] option:selected').val();
        var saStreetId = $(this).find('select[name^=shippingAddressStreetId] option:selected').val();
        var saAddress = $(this).find('input[name=shippingAddressInput]').val();
        var saCustomerBillingAddressVo = {"billingProvinceId":""+saProvinceId+"","billingCityId":""+saCityId +"","billingDistrictId":""+saDistrictId+"","billingStreetId":""+saStreetId+"","billingAddress":""+saAddress+"",addressType:2};
        shippingAddress.push(saCustomerBillingAddressVo)
    });

    // var table1 = $("#table1").getRowData();
    // for(var i=0;i<table1.length;i++)
    // {
    //     if(table1[i].customerEnclosureVoList)
    //     {
    //         var customerEnclosureVoList = JSON.parse(table1[i].customerEnclosureVoList);
    //         for(var j=0;j<customerEnclosureVoList.length;j++)
    //         {
    //             customerEnclosureVoList[j].type = 0;
    //         }
    //         table1[i].customerEnclosureVoList=customerEnclosureVoList;
    //     }
    // }
    //被委托人身份证
    var certTable = $("#certTable").getRowData();
    for(var i=0;i<certTable.length;i++)
    {
        if(certTable[i].customerEnclosureVoList)
        {
            var customerEnclosureVoList = JSON.parse(certTable[i].customerEnclosureVoList);
            for(var j=0;j<customerEnclosureVoList.length;j++)
            {
                customerEnclosureVoList[j].type = 0;
            }
            certTable[i].customerEnclosureVoList=customerEnclosureVoList;
        }
    }
    //批准文件
    var table3 = $("#table3").getRowData();
    for(var i=0;i<table3.length;i++) {
        if(table3[i].customerEnclosureVoList) {
            var customerEnclosureVoList = JSON.parse(table3[i].customerEnclosureVoList);
            for(var j=0;j<customerEnclosureVoList.length;j++) {
                customerEnclosureVoList[j].type = 0;
            }
            table3[i].customerEnclosureVoList=customerEnclosureVoList;
        }
        if (table3[i].customerBusinessScopeVoList){
            var selectBusinessScopeVoList=[];
            for (var j=0; j<table3[i].customerBusinessScopeVoList.length;j++){
                var customerBusinessScopeVo = {};
                if (0 != table3[i].customerBusinessScopeVoList[j].id){
                    customerBusinessScopeVo.businessScopeCode = table3[i].customerBusinessScopeVoList[j].id;
                    selectBusinessScopeVoList.push(customerBusinessScopeVo);
                }
            }
            table3[i].customerBusinessScopeVoList = selectBusinessScopeVoList;
        }
    }
    //客户委托书
    var table2 = $("#table2").getRowData();
    for(var i=0;i<table2.length;i++)
    {
        if(table2[i].customerEnclosureVoList)
        {
            var customerEnclosureVoList = JSON.parse(table2[i].customerEnclosureVoList);
            for(var j=0;j<customerEnclosureVoList.length;j++)
            {
                customerEnclosureVoList[j].type = 0;
            }
            table2[i].customerEnclosureVoList=customerEnclosureVoList;
        }
    }
    //客户合同 customerContractVos
    // var customerContractVos=[];
    // $("#contractType input[type='checkbox']").each(function(){
    //     var checked=this.checked;
    //     if(checked)
    //     {
    //         var v=$(this).val();
    //         var json={};
    //         var imgList=[];
    //         var fLen=$("input[id='contractType"+v+"']").length;
    //         json.contractType=v;
    //         if(fLen > 0){
    //             imgList=JSON.parse($("input[id='contractType"+v+"']").val())
    //             json.customerEnclosureVoList=imgList;
    //             for(var j=0;j<json.customerEnclosureVoList.length;j++)
    //             {
    //                 delete json.customerEnclosureVoList[j].type
    //             }
    //         }else{
    //             json.customerEnclosureVoList=[];
    //         }
    //         customerContractVos.push(json);
    //     }
    // });
    //结算方式 customerExtendItemVoList
    var customerExtendItemVoList=[];
    $(".settlement").each(function(){
        var parentInp=$(this).find("input[name='itemValue']");
        if(parentInp.is(":checked"))
        {
            //var parentCode=parentInp.attr('code');
            var json={};
            var parentCodeValue=parentInp.val();
            json['parentCode']=0;
            json['code']=parentCodeValue;
            json['value']=1;
            customerExtendItemVoList.push(json);
            $(this).find(".childCode").each(function(index){
                var json1={};
                var cCode=$(this).find("input[name='code']");
                //var cCodeName=cCode.attr('name');
                var cCodeValue=cCode.val();
                var cValue=$(this).find("input[name='itemValue1']").val();
                //var cValueName=cValue.attr('name');
                //var cValueValue=cValue.val();
                if($.trim(cValue) != '')
                {
                    json1['parentCode']=parentCodeValue;
                    json1['code']=cCodeValue;
                    json1['value']=cValue;
                    customerExtendItemVoList.push(json1);
                }
            });
        }
    });
    let kaipiaoPicArr = [];
    let smallArr = billInfoFormVo.customerEnclosureVoList.split(',');
    $(smallArr).each(function (index,item) {
        //if(item != ''){
            let kaipiaoPicObj = {};
            kaipiaoPicObj.enclosureName = '非独立经营证明'+(index + 1)
            kaipiaoPicObj.url = item;
            kaipiaoPicArr.push(kaipiaoPicObj)
        //}
    })
    var customerFirstApplDetailVo={
        customerFirstApplVo:customerFirstApplVo,
        customerEnclosureVoList: kaipiaoPicArr,
        customerApprovalFileVoList:table3,
        // customerQualityAgreementVoList:table1,
        customerDelegationFileVoList:table2,
        customerBeProxyIdentityVoList:certTable,
        customerBillingAddressVoList:customerBillingAddressVoList,
        // customerContractVos:customerContractVos,
        customerExtendItemVoList:customerExtendItemVoList,
        customerDeliveryAddressVoList:shippingAddress
    }
    return customerFirstApplDetailVo;
}


function initFileSelected() {

}

function balanceTypeCheck(){
    $("input[name='itemValue']:not(:checked)").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').hide();
        $(this).parent().parent().parent().children().not('.checkbox').find('input[name="itemValue1"]').val('');
    });
    $("input[name='itemValue']:checked").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').show();
    });
}

/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj, tableId) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(tableId).getRowData(parentId);
    if(data.customerEnclosureVoList)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            list:JSON.parse(data.customerEnclosureVoList)
        })
    }
}

/**
 * 加载委托人类型
 * @param simpleCode
 */
function localBatchName(simpleCode) {
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/queryClient",
        async : false,
        data:{"type":4},
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        if(simpleCode == arr[i].clientId){
                            html+='<option value="'+arr[i].clientId+'" selected="selected">'+arr[i].clientName+'</option>';
                        }else{
                            html+='<option value="'+arr[i].clientId+'">'+arr[i].clientName+'</option>';
                        }
                    }
                }
            }
            $("select[name='mandatorType']").html(html);
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

//邓涛0915，增加动态设置最小日期限制
$('#validUntilInp').on('click',function () {

});

function minValue(el){
    // var startTime=$('#'+el).val(),options={};
    // if (startTime < getToday()) {
        options.minDate = '%y-%M-%d';
    // } else {
    //     options.minDate ='#F{$dp.$D(\'' + el + '\',{d:1})}';
    // }
    WdatePicker(options);
}

$("#withdraw").on("click",function(){
    var title = "关闭审核";
    var contant = "确定关闭此申请？";
    var status = "withdraw";
    // 撤销弹窗
    dialogAgain(title,contant,status);
});
// 驳回编辑
function dialogAgain(title,con,status){
    parent.showLoading({hideTime: 60000});
    // 状态 审核中
    if(status != 'withdraw'){ // 撤销审核的时候不校验
        // 是否独立核算 选中值为是时。判断上下文对应的值是否相等
        let isIndependentVal = (window.changeApply && window.changeApply['isIndependent'])? window.changeApply['isIndependent']['valueAfter']:$('#billInfo_form [name=isIndependent]:checked').val();
        if(isIndependentVal == '1'){ // 是否独立核算 选中值为是时。判断上下文对应的值是否相等
            let returnBool = checkChangeSyncDataSame();
            if(!returnBool){
                parent.hideLoading();
                disable();
                return false;
            };
        }
        if(isIndependentVal == '0'  && window.changeApply['isIndependent']){ // 是否独立核算 提交值为否时，判断必填值
            if(!infoVo.customerEnclosureVoList || (infoVo.customerEnclosureVoList && infoVo.customerEnclosureVoList.length == 0 && status != 'withdraw')){ // 再次审核的时候
                parent.hideLoading();
                utils.dialog({
                    title: '提示',
                    content: '非独立经营证明附件未上传，不允许审核通过！',
                    okValue: '确定',
                    ok: function () {}
                }).showModal();
                disable();
                return false;
            }

            // 当是否独立 核算修改为否的时候，公司名称必须做修改。同步的改变后面三个的值
            if(!window.changeApply['billingCompanyName']){ // 如果 公司名称没有做修改
                parent.hideLoading();
                utils.dialog({
                    title: '提示',
                    content: '是否独立核算修改为否的时候，公司名称必须做修改.',
                    okValue: '确定',
                    ok: function () {}
                }).showModal();
                disable();
                return false;
            }
        }
        if(validBillInfoForm_fun()){
            parent.hideLoading();
            disable();
            return false;
        };
    }

    parent.hideLoading();
    utils.dialog({
        title:title,
        content: con?con:$('#alertAgain'),
        width:300,
        height:30,
        okValue: '确定',
        ok: function () {
            if(status=="pass"){
                able();
                infoVo.customerChangeApprovalRecordVo.auditStatus = 2;
                infoVo.customerChangeApprovalRecordVo.id =$("#approvalId").val();
                infoVo.customerChangeApprovalRecordVo.baseId =$("#baseId").val(); //基础属性id
                var processVo = {
                    "auditOpinion":$("#auditOpinion").val(),
                    "taskId":taskId,
                    "id":approvalId
                };
                infoVo.customerProcessVO = processVo;
                update(infoVo,"againAssert");
            }else if(status=="withdraw"){
                withdraw();
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();
}
// 撤销
function withdraw(){
    var processVO = {
        "auditOpinion":$("#auditOpinion").val(),
        "taskId":$("#taskId").val(),
        "id":$("#id").val(),
        "workProcessKey":"customerFirstErp"
    };
    $.ajax({
        type:"post",
        url: "/proxy-customer/customer/change/approval/withdrawBaseChangeApproval",
        async : false,
        data:JSON.stringify(processVO),
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var status = data.result.status;
            var taskStatus = data.result.taskStatus;
            if(status!=0){
                if(typeof(taskStatus) != "undefined"){
                    if(taskStatus==true){
                        utils.dialog({content: '撤销成功！', timeout: 2000}).showModal();
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)
                    }else{// 任务流启动失败
                        utils.dialog({content: data.result.taskMsg, timeout: 2000}).showModal();
                    }

                }
            }else {// 保存失败
                utils.dialog({content: data.result.msg, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '撤销失败！', timeout: 2000}).showModal();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}


//批准文件,质量保障协议,客户委托书,被委托人 一键下载
function patchDownload(tableId){
    downloadTableAttachFiles(tableId)
}
function downloadTableAttachFiles(tableId){
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$(tableId);
    var rowData=$table.getRowData();
    console.log(rowData)
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++)
    {
        var sel=$tr.eq(i).find("select[name='credentialTypeId'] option:selected");
        typeList.push({
            text:sel.text(),
            value:sel.val()
        });
        console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0)
        {
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
            {
                rowData[i].customerEnclosureVoList[j].type=sel.val();
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    console.log(eChoImgList)
    // downloadImg("/static/MobileToken.png") //同源图片，下载有效
    const fileUrls = []
    const fileNames = []
    eChoImgList.forEach((item,index)=>{
        if (item.url && item.url.length) {
            fileUrls.push(item.url)
            let fileName = item.enclosureName
            if (!fileName){
                fileName = index + ''
            }
            fileNames.push(fileName)
        }
    })
    downloadImg(fileUrls,fileNames)
}
//客户合同 一键下载
// TODO 待验证
function qtfjPatchDownload() {
    let eChoImgList = []
    var checkInp=$("#contractType input[type='checkbox']:checked");
    if(checkInp.length)
    {
        for(var i=0;i<checkInp.length;i++)
        {
            var type=checkInp.eq(i).val();
            var inp=$("#contractType"+type);
            if(inp.length)
            {
                var imgArr=JSON.parse(inp.val());
                eChoImgList=eChoImgList.concat(imgArr);
            }
        }
    }
    const fileUrls = []
    const fileNames = []
    eChoImgList.forEach((item,index)=>{
        if (item.url && item.url.length) {
            fileUrls.push(item.url)
            let fileName = item.enclosureName
            if (!fileName){
                fileName = index + ''
            }
            fileNames.push(fileName)
        }
    })
    downloadImg(fileUrls,fileNames)
}
function downloadImg(fileUrls,fileNames){
    if (!(fileUrls.length * fileNames.length)){
        utils.dialog({content: '暂无附件下载', quickClose: true, timeout: 2000}).showModal();
        return
    }
    window.open("/proxy-customer/customer/customerFirstAppl/downloadZip?fileUrls="+fileUrls+"&fileNames="+fileNames+"&zipFileName=导出")
}

function oneKeyCopyCerts() {
    const delegationFiles = $('#table2').getRowData()
    // 如果存在客户委托书，
    if (delegationFiles.length){
        const certTable = $('#certTable')
        // 遍历客户委托书，依次复制到被委托人身份证
        delegationFiles.forEach(lastDelegationFile=>{
            const certName = certTable.find('tr:last').find('input[name=customerName]').val()
            const certIdNo =  certTable.find('tr:last').find('input[name=identityNumber]').val()
            const certDate = certTable.find('tr:last').find('input[name=identityValidityDate]').val()
            // 若最后一行中 姓名，证件号码，身份证有效期 均有值，则新增一行进行插入。
            // 若最后一行中 姓名，证件好吗，身份证有效期 的值均为空，则不新增行，直接在最后一行的基础上修改。
            if (certName && certIdNo && certDate){
                addRow('#certTable')
            }
            certTable.find('tr:last').find('input[name=customerName]').val(lastDelegationFile.delegationName)
            certTable.find('tr:last').find('input[name=identityNumber]').val(lastDelegationFile.delegationNum)
            certTable.find('tr:last').find('input[name=identityValidityDate]').val(lastDelegationFile.delegationIdentityDate)
        })
    }else {
        utils.dialog({content: '暂无可用证件信息', quickClose: true, timeout: 2000}).showModal();
    }
}
