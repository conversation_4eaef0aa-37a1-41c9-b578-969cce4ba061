/*时间戳转字符串*/
function timestamp2String(timestamp){
    if (timestamp==0 || timestamp == null)return '';
    // 1.时间戳转date对象
    let date = new Date(timestamp);
    // 2.调用api获取年月日
    let year = date.getFullYear().toString();
    // 月份从0开始，需要+1
    let month = (date.getMonth()+1).toString().padStart(2,'0');
    // debugger
    let day = date.getDate().toString().padStart(2,'0');
    // 3.拼接年月日
    return year+'-'+month+'-'+day;
}

$(function () {

    $('div[fold=head]').fold({
        sub: 'sub'
    });

    /**
    * 表格模型数据
    * */
    const colMaps = [
        {colName: '', name: 'baseId', hidden: true},
        {colName: '客户编码', name: 'customerCode', index: 'customerCode', width: 180},
        {colName: '客户名称', name: 'customerName', index: 'customerName', width: 300},
        {colName: '客户类别', name: 'customerTypeName', index: 'customerTypeName', width: 180},
        {colName: '营业执照号', name: 'businessLicenseNum', index: 'businessLicenseNum', width: 220},
        {colName: '客户联系人', name: 'salesman', index: 'salesman', width: 180},
        {colName: '证书类型', name: 'credentialTypeName', index: 'credentialTypeName', width: 220},
        {colName: '证书编号', name: 'credentialCode', index: 'credentialCode', width: 180},
        {colName: '发证日期', name: 'issueDate', index: 'issueDate', formatter: timestamp2String ,width: 180},
        {colName: '有效期至', name: 'expireDate', index: 'expireDate',formatter: timestamp2String, width: 180},
        {colName: '剩余效期', name: 'remainingValidityPeriod', index: 'remainingValidityPeriod', width: 180},
        {colName: '<i>效期标识</i><i class="questa"></i>', name: 'validityPeriodFlagName', index: 'validityPeriodFlagName', width: 180}
    ]

    if ($('#orgType').val()==1){
        colMaps.splice(1,0,{colName: '机构', name: 'orgName', index: 'orgName', width: 250})
    }



    /**
     * 表头初始化
     * */
    $('#baseTable').XGrid({
        url:'/proxy-customer/customer/customerQualificationSearch/pageList',
        mtype: 'POST',
        postData: $("#searchForm").serializeToJSON(),
        colNames: colMaps.map(item => {
            return item.colName
        }),
        colModel: colMaps.map(item => {
            delete item.colName
            return item
        }),
        data: [],
        // 设置每页展示行数，-1 表示全部展示
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        // 设置为交替行表格,默认为false
        altRows: true,
        // 分页
        pager: '#grid-pager',
        // 是否展示序号
        rownumbers: true,
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件', id, dom, obj, index, event);
            utils.openTabs("customerBaseDetail", "机构客户详情", "/proxy-customer/customer/customerBaseAppl/detail?id="+obj.baseId);
            // location.href='/proxy-customer/customer/customerBaseAppl/detail?id='+id
        },
    });


    /**字段名称悬浮解释*/
    var questaOption
        = [
        {
            name:'validityPeriodFlagName',
            content:'即将过期是指剩余效期【0天<=资质剩余效期<=30天】, 已过期是指剩余效期【资质剩余效期<=-1天】, 正常是指剩余效期【资质剩余效期>30天】'
        }
    ];

    /**
     * 表头悬浮提示样式*/
    $("body").on({
        mouseover:function (e) {
            var item = questaOption.find(function (item) {
                return item.name === $(e.target).parent('th').attr('row-describedby');
            });
            $("body").append("<div id='div_toop'><div id='inner'>"+item.content+"</div></div>");
            $("#div_toop")
                .css({
                    "top": (e.pageY - 44) + "px",
                    "position": "absolute",
                    "padding-left": "5px",
                    "padding-right": "5px",
                    "padding-top": "5px",
                    "padding-bottom": "5px",
                    "background-color": "lightGray",
                    "left": (e.pageX - 130) + "px"
                }).show("fast");
        },
        mouseout:function () {
            $("#div_toop").remove();
        }
    },questaOption.map(function (item) { return "th[row-describedby='"+item.name+"'] .questa" }).join(','));


    /**
     *  设置查询按钮点击事件
     */
    $("#searchBtn").on("click", function () {
        $('#baseTable').XGrid('setGridParam', {
            postData: $('#searchForm').serializeToJSON(),
            page:1
        }).trigger('reloadGrid');
    });


    /**
     * 设置导出按钮点击事件
     */
    $("#exportBtn").on("click", function () {
        const table = $('#baseTable').XGrid('getGridParam');
        const maxExportSize = 100000
        if (table.records>maxExportSize)
        {
            utils.dialog({"content":"导出条数超过限制10万条，请选择合适的条件，减少导出条数","timeout":2000}).show();
            return;;
        }
        const data = table.data
        //无数据提示
        if (data.length==0){
            utils.dialog({"content":"无数据可导出","timeout":2000}).show();
            return
        }
        utils.dialog({
            title: '提示',
            content: "数据导出需要一定时间，请耐心等待。",
            okValue: '开始导出',
            cancelValue: '取消',
            ok: function () {
                // 利用 form 表单完成导出
                const form = $('#searchForm');
                // 导出接口
                $(form).attr("action", "/proxy-customer/customer/customerQualificationSearch/exportExcel");
                $(form).submit();
            },
            cancel: function () {
            },
        }).showModal();
    });
})
