$(function () {
  /* 获取dialog上层实例 */
  var dialog = parent.dialog.get(window);
  if (dialog) {
    var dialog_data = dialog.data;
    $('#search_vl').val(dialog_data.productCode)
  }



  /* table_b */
  $('#table_b').XGrid({
    url:'/proxy-storage/storage/adjustment/findBatchProductPageInfo',
    postData: {
      productCode: dialog_data.productCode,
        channelId: dialog_data.channelId
    },
    colNames: ['id','库房名称', '批号', '生产日期', '有效期至', '库存数量', '成本价','是否可以编辑','库别','业务类型'],
    colModel: [{
        name: 'id',
        index: 'id',
        hidden:true

    }, {
        name: 'storeName',
        index: 'storeName',
        formatter: function (e) {
            if (e == '1') {
                return '合格库'
            } else if (e == '2') {
                return '不合格库'
            }else if (e == '3') {
                return '暂存库'
            }else if(e == "9"){
                return "盘点库";
            } else {
                return ''
            }
        }
    }, {
        name: 'batchCode',
        index: 'batchCode'

    }, {
        name: 'productDateStr',
        index: 'productDateStr'

    }, {
        name: 'validateDateStr',
        index: 'validateDateStr'

    }, {
        name: 'batchStockNum',
        index: 'batchStockNum'

    },{
        name: 'costPrice',
        index: 'costPrice',
        hidden:true

    },
        {
            name: 'canEdit',
            index: 'canEdit',
            hidden:true
        },

        {
            name: 'storageType',
            index: 'storageType',
            hidden:true
        },
        {
            name: 'channelId',
            index: 'channelId',
        }


        ],
    rowNum: 0,
    selectandorder: false,
    key:"id",
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {
        this.returnValue = obj;
        dialog.close(obj);
        return obj;
    }

  });

  /* 查询 */
  $('#search').on('click', function () {
    $('#table_a').setGridParam({
      url: '/proxy-storage/storage/adjustment/getProductList',
      postData: {
        param:  $('#search_vl').val()
      }
    }).trigger('reloadGrid');
  })

  /* 确定 */
  $('#sub').on('click', function () {
    //var data_a = $('#table_a').XGrid('getSeleRow');
    var data_b = $('#table_b').XGrid('getSeleRow');
    if(data_b){
        dialog.close(data_b);
    }
  })



  // 去掉所有input的autocomplete, 显示指定的除外
  /* $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete',
    'off'); */

  /* 模糊搜索 */
  var countries = [{
      value: 'Andorra',
      data: 'AD'
    },
    {
      value: 'Zimbabwe',
      data: 'ZZ'
    }
  ];

  $('#search_vl').Autocomplete({
    serviceUrl: 'http://localhost/x.json', //异步请求
    // paramName: 'query111',//查询参数，默认 query
    dataType: 'json',
    //lookup: countries, //监听数据 value显示文本，data为option的值
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    /*  dataReader:{
          'value':'manufactoryName',
          'data':'manufactoryId',
           'xxt':"name"
      },*/
    triggerSelectOnValidInput: false, // 必选
    transformResult: function (response) {
      return {
        suggestions: $.each(response, function (idnex, dataItem) {
          return {
            value: dataItem,
            data: dataItem
          };
        })
      };
    },
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function (result) {
      //选中回调
      alert('You selected: ' + result.value + ', ' + result.data + ',' + result.xxt);
      // console.log('选中回调')
    },
    onSearchStart: function (params) {
      // console.log('检索开始回调', params)
    },
    onSearchComplete: function (query, suggestions) {
      //匹配结果后回调
      // console.log(query, suggestions);
      // if (suggestions.length < 1) {
      //     utils.dialog({
      //         title: '查询无结果',
      //         content: '是否新增生产厂家？',
      //         width: '300',
      //         okValue: '确认',
      //         ok: function () {
      //             this.title('提交中…');
      //             return false;
      //         },
      //         cancelValue: '取消',
      //         cancel: function () {
      //             $('input').val('')
      //         }
      //     }).show();
      // }
    },
    onSearchError: function (query, jqXHR, textStatus, errorThrown) {
      //查询失败回调
      console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function (container) {
      // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function (params, suggestions) {
      console.log(params, suggestions);
      console.log('没选中回调函数');
    }
  });


})