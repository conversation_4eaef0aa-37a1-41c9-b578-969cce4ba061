
$(function () {

    
    $("#exportBtn").on("click", function () {
        utils.exportAstrictHandle('X_Tableb',
            Number($('#totalPageNum').text()),1).then(()=>{
            return false;
        }).catch(()=>{
//全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = true;
            // copy this parameter and the below buttons
            var html = $('#exportHtml').html();
            var d = dialog({
                title: '请选择导出字段',
                width:820,
                height:400,
                content: html,
                okValue: '确定',
                ok: function () {
                    this.title('提交中…');
                    var arr=[];
                    for(var i=0;i<$(".exportItem").length;i++)
                    {
                        $(".exportItem").eq(i).find('dd input[type="checkbox"]').each(function () {
                            var checked=this.checked;
                            if(checked){
                                arr.push($.trim($(this).val()))
                            }
                        })
                    }
                    if(arr.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    var exportAttribute= arr.join(',');
                    $("#exportAttribute").val(exportAttribute);
                    $("#searchForm").attr("action","/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/exportExcel");
                    $("#searchForm").submit();
                },
                cancelValue: '取消',
                cancel: function () { },
// copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $(".exportItem input").removeAttr("checked");
                                ck = true;
                            }else if(ck){
                                $(".exportItem input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here

            });
            d.showModal();




        })



    });
    
    $(document).on("change",".exportItem dt input",function () {
        var checked=this.checked;
        if(checked)
        {
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',false);
        }
    })
    
    
  
})