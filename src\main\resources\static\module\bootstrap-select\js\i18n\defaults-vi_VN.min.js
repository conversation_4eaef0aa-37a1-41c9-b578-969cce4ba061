/*!
 * Bootstrap-select v1.12.4 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2017 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(a){return b(a)}):"object"==typeof module&&module.exports?module.exports=b(require("jquery")):b(a.jQuery)}(this,function(a){!function(a){a.fn.selectpicker.defaults={noneSelectedText:"Chưa chọn",noneResultsText:"Không có kết quả cho {0}",countSelectedText:function(a,b){return"{0} mục đã chọn"},maxOptionsText:function(a,b){return["Không thể chọn (giớ<PERSON> hạn {n} mục)","<PERSON><PERSON><PERSON><PERSON> thể chọn (giớ<PERSON> hạn {n} mục)"]},selectAllText:"Chọn tất cả",deselectAllText:"Bỏ chọn",multipleSeparator:", "}}(a)});