var win_dialog = null, initDataArr = null;
$(function () {
    win_dialog = parent.dialog.get(window);
    isLock=win_dialog.data.isLock;
    initDataArr = win_dialog.data.initDataArr
    console.log(isLock)
})
var selArr=[];

var businessType = $('#businessType').val();
    $('#X_Tableb').XGrid({
        url:"/proxy-customer/customer/customerLock/toSearchCustomerList?businessType="+businessType,
        colNames: ['', '客户编码', '客户名称', '客户类型', '联系人', '联系电话', '营业执照号'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true ,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden:true
        }, {
            name: 'customerCode',
            index: 'customerCode'
        }, {
            name: 'customerName',
            index: 'customerName',
            width:330
        }, {
            name: 'customerTypeName',
            index: 'customerTypeName',
            width:330
        },{
            name: 'salesman',
            index: 'salesman',
            width:100
        }, {
            name: 'salesmanTel',
            index: 'salesmanTel',
            width:100
        }, {
            name: 'businessLicenseNum',
            index: 'businessLicenseNum',
            width:140
        }, {
            name: 'customerMnemonicCode',
            index: 'customerMnemonicCode',
            width:140,
            hidden:true
        }, {
            name: 'orgCode',
            index: 'orgCode',
            width:140,
            hidden:true
        }, {
            name: 'isLock',
            index: 'isLock',
            width:140,
            hidden:true
        },{
            name: 'baseId',
            hidden: true
        },{
            name: 'disabledYn',
            hidden: true
        }
        ],
        viewrecords: true,//是否在浏览导航栏显示记录总数
        rownumbers: true,//是否展示序号
        multiselect: true,
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        gridComplete: function () {
            setTimeout(function () {
                let allRowData = $("#X_Tableb").getRowData();
                if(allRowData.length == 0){
                    utils.dialog({
                        title: '提示',
                        content: '暂无数据',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                }

                $("#X_Tableb th:first input").hide();
                    if(initDataArr.length > 0){
                        var arr=initDataArr;
                        for(var i=0;i<arr.length;i++){
                            $('#X_Tableb tr:not("first")').each(function () {
                                var id=$(this).attr("id");
                                if(id == arr[i].baseId || id == arr[i].id){ // 因为后端返回值有的有baseID有的没有，
                                    $(this).find("td[row-describedby='ck'] input").prop("checked",true).trigger('input');
                                }
                            })
                        }
                    }
            },200)
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //添加已选客户
            var $tr = $(dom);
            var check=$tr.find('[row-describedby="ck"] input').prop("checked");
            getCheckData(check,obj);
        },
        pager: '#grid-pager'
    });
    //全选
    $('body').on('click','#grid_checked input',function () {
        let checkState = $(this).prop('checked');
        let data = [];
        if(checkState){
            data = $('#X_Tableb').getRowData();
            selArr = data;
        }else{
            selArr = [];
        }
    });

//添加已选客户
$('#X_Tableb').on("change","td[row-describedby='ck'] input",function(ev){
    var check=this.checked;
    var $tr = $(this).parents('tr');
    var id=$tr.attr('id');
    var data=$('#X_Tableb').XGrid('getRowData', id);
    getCheckData(check,data);
    ev.stopPropagation();
});
// todo 搜索补全优化
$("#SearchBtn").on("click", function () {
    $('#X_Tableb').XGrid('setGridParam', {
        postData: {
            "customerCode": $("#search_vl").val(),
            "businessType": businessType,
            // "auditStatus":$("#auditStatus").val(),
            // "maxRemainDays":$("#maxRemainDays").val(),
            //"minRemainDays":$("#minRemainDays").val()
        },page:1
    }).trigger('reloadGrid');
});
//选择按钮
$("#SelectBtn").on("click", function () {
    selArr = $('#X_Tableb').XGrid('getSeleRow');
    var baseIds=[];
    if(selArr.length > 0){
        for(var i=0;i<selArr.length;i++){
            baseIds.push(selArr[i].id);
            selArr[i].isLock=isLock;
            selArr[i].id=selArr[i].id;
            selArr[i].customerType=selArr[i].customerTypeName;
            selArr[i].businessType=businessType^1;
            selArr[i].disabledType=selArr[i].disabledYn^1;
        }
    }
    console.log(selArr);
    win_dialog.close(selArr);
    //若主数据停用  且机构数据已是停用状态 则机构数据不可再启用
    // if(organBaseIds.length>0){
    //     $.ajax({
    //         type: "POST",
    //         url: "/proxy-customer/customer/customerLock/supplierDisableApprovalRecord/validateDisableState",
    //         async: false,
    //         data:{organBaseIds:JSON.stringify(organBaseIds)},
    //         success: function (data) {
    //             if (data.code){
    //                 if(data.resultList && data.resultList.length>0){
    //                     var msg = "供应商名称为：";
    //                     data.resultList.forEach(function(e){
    //                         msg +=  e.supplier_name+","
    //                     });
    //                     msg = msg.substr(0, msg.length-1) +"的基础属性已停用，该供应商不可再启用";
    //                     utils.dialog({
    //                         title: '提示',
    //                         width:300,
    //                         height:30,
    //                         content: msg,
    //                         okValue: '确定',
    //                         ok: function () {
    //                         }
    //                     }).showModal();
    //                 }else{
    //                     win_dialog.close(selArr);
    //                 }
    //             }else{
    //                 utils.dialog({content: data.msg, quickClose: true, timeout: 2000}).showModal();
    //             }
    //         },
    //         error: function () {}
    //     });
    // }else{
    //     win_dialog.close(selArr);
    // }

});
//组装已选数据
function getCheckData(check,data){
    if(check)
    {
        if(!fidInArr(data.id)){
            selArr.push(data);
        }
    }else{
        for(var i=0;i<selArr.length;i++){
            if(selArr[i].id == data.id){
                selArr.splice(i,1);
                break;
            }
        }
    }
}
//数组中查找id
function fidInArr(id){
    for(var i=0;i<selArr.length;i++){
        if(selArr[i].id == id){
            return true;
        }
    }
    return false;
}
