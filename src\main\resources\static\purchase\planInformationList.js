function timeStamp2String(time){
    var datetime = new Date();
    datetime.setTime(time);
    var year = datetime.getFullYear();
    var month = datetime.getMonth() + 1;
    var date = datetime.getDate();
    var hour = datetime.getHours();
    var minute = datetime.getMinutes();
    var second = datetime.getSeconds();
    var mseconds = datetime.getMilliseconds();
    return year + "-" + month + "-" + date;

};
var menuFlag=$("#menuFlag").val();
if(menuFlag==1||menuFlag==2){
    $.ajax({
        url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode=001",
        async : false,
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].orgCode+'">'+arr[i].orgName+'</option>';
                    }
                }
            }
            $("#orgCodes").html(html);
        },
        error:function () {
        }
    });
}

function change_channelID(that) {
    // _channeld = $(that).val();
    addOrderAttribute(that)
}
/**
 * 订单属性
 * */
function addOrderAttribute(type) {
    $("#centralizedPurchaseTypePop").empty()
    $.ajax({
        url: '/proxy-purchase/purchase/orderAttribute/dict/queryListByType',
        type: 'get',
        data: { type:type,
            orgStatus: 1,
        },
        success: function (data) {
            $.each(data.result, function (infoIndex, info) {  //循环遍历后台传过来的json数据
                $("#centralizedPurchaseTypePop").append("<option value='" + info["code"] + "'>" + info["name"] + "</option>");
            });
            $('#centralizedPurchaseTypePop').selectpicker('val', '');
            $('#centralizedPurchaseTypePop').selectpicker('refresh');
        }
    });
}
$(function () {
    var date1=new Date();
    var curMonth = date1.getMonth();
    curMonth = (curMonth == 0)?'12':curMonth;
    var lastMonth = ((curMonth - 1) == 0) ? '12': (curMonth - 1);
    var lastLastMonth = ((lastMonth - 1) == 0) ? '12': (lastMonth - 1);
    //单据编号

    var colNames =['业务类型','机构', '原商品编码', '商品编码','商品名称','首选供应商','规格','生产厂家','条形码','批准文号','中包装','件包装','批件效期','状态','订单属性','药帮忙售价','最后入库价',
    '最近两个月最低价','最后供应商','最低价供应商','TOP排名',lastLastMonth+ '月总销量', lastMonth+ '月总销量', curMonth+ '月总销量','<i>60天/日均销量</i><i class="questa"></i>',
        '<i>30天/日均销量</i><i class="questa"></i>','<i>15天/日均销量</i><i class="questa"></i>','<i>7天/日均销量</i><i class="questa"></i>','<i>商品流速</i><i class="questa"></i>',
        '可销天数','可用库存','合格库数量','在途数量','<i>安全库存</i><i class="questa"></i>','<i>库存上限</i><i class="questa"></i>',
		'30天内售罄天数','60天内售罄天数','30天内下架天数','60天内下架天数','<i>30天净日均销量</i><i class="questa"></i>','<i>60天净日均销量</i><i class="questa"></i>',
		'计划检查','商品定位','采购员','大区经理', '付款情况', '建议采购量','是否新品','备货状态','更新时间','品类经理'];
    var colModel = [
        {
            name: 'channelId',
            index: 'channelId',
        },
		{
            name: 'sysOrgName'
        },
        {
            name: 'originalProductCode'
        },
        {
            name: 'productCode'
        },
        {
            name: 'productName'
        },
        {
            name: 'firstSuppName',
            formatter:function (e,row,data) {
                if (menuFlag == 0 ){
                    return `
                        <div style="display: flex; align-items: center;">
                            <p class="updateFirstSupplier_p" style="width: 80px; display: inline-block; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${data.firstSuppName}</p>
                            <input type="button" class="btn btn-sm btn-info updateFirstSupplier_btn" style="display: inline-block;" value="修改"
                                data-id="${data.id}"
                                data-channelId="${data.channelId}"
                                data-channelName="${data.channelName}"
                                data-productCode="${data.productCode}"
                                data-productName="${data.productName}"
                                data-specifications="${data.specifications}"
                                data-manufName="${data.manufName}"
                                data-firstSuppCode="${data.firstSuppCode}"
                                data-firstSuppName="${data.firstSuppName}"
                            ></input>
                        </div>
                `
                }
                return `
                        ${data.firstSuppName}
                `
                // return '<button type="button" class="btn btn-info updateFirstSupplier_btn" >修改</button>' + data.firstSuppName;
            }
        },
        {
            name: 'specifications'
        },
        {
            name: 'manufName'
        } ,
        {
            name: 'smallPackageBarCode'
        } ,
        {
            name: 'approvalNumber'
        }
        ,{
            name: 'mediumPackageNumber'
        },
        {
            name: 'piecePackageNumber'
        },
        {
            name: 'approvalIndate'
        },
        {
            name: 'ecStatus'
        },
        {   //是否集采
            name: 'centralizedPurchaseName',
            type: 'centralizedPurchaseName'
        },
        {
            name: 'appPrice',
            formatter: function (e) {
                    if(e == null){
                        return 0.00;
                    }
                  return toDecima(e)
            }
        },
        {
            name: 'lastTaxPrice',
            formatter: function (e) {
                if(e == null){
                    return 0.00;
                }
                return  toDecima(e)
            }
        },
        {
            name: 'lastLowestTaxPriceStr'
        },
        {
            name: 'lastSuppName'
        },
        {
            name: 'lowestSupplierName'
        },
        {
            name: 'top'
        },
        {
            name: 'threeMonthStr'
        },
        {
            name: 'twoMonthStr'
        },
        {
            name: 'oneMonthStr'
        },
        {
            name: 'sixtySaleVolume'
        },
        {
            name: 'thirtySaleVolume'
        },
        {
            name: 'fifteenSaleVolume'
        },
        {
            name: 'sevenSaleVolume'
        },
        {
            name: 'productStrength'
        },
        {
            name: 'salesDay'
        },
        {
            name: 'hegekeyongkucun'
        },
        {
            name: 'hegekucun'
        },
        {
            name: 'zaitu'
        },
        {
            name: 'safetyStockAmount'
        },
        {
            name: 'upperLimitStockAmount'
        },

		{
            name: 'thirtySaleoutDays'
        },
		{
            name: 'sixtySaleoutDays'
        },
		{
            name: 'thirtyLowershelfDays'
        },
		{
            name: 'sixtyLowershelfDays'
        },
		{
            name: 'thirtyAverageDailySales'
        },
		{
            name: 'sixtyAverageDailySales'
        },

        {
            name: 'planCheck'
        },
        {
            name: 'commodityPosition'
        },
        {
            name: 'buyerName'
        },
        {
            name: 'regionalManagerName',
            hidden : true
        }, {
            name: 'payType'
        }, {
            name: 'recommendedPurchNumber'
        },{
            name: 'isNew',
            formatter: function (val){
                if (val =="1"){
                    return "新品";
                }else if(val =="0"){
                    return "非新品";
                }else{
                    return "";
                }
            }
        },{
            name: 'choiceStatus',
            formatter: function (val){
                if (val ==6){
                    return "近期无销售";
                }else if (val ==5){
                    return "超高库存";
                }else if(val ==4){
                    return "高库存";
                }else if(val ==3){
                    return "正常";
                }else if(val ==2){
                    return "需补货";
                }else if(val ==1){
                    return "即将断货";
                }else if(val ==0){
                    return "断货";
                }else{
                    return "";
                }
            }
        },{
            name: 'updateTime',
            formatter: function (value) {
                return moment(value).format("YYYY-MM-DD ");
            }
        },{
            name: 'categoryUserName'
        }, {
            name: 'centralizedPurchaseType',// 集采属性
            index: 'centralizedPurchaseType',
            hidden: true,
            hidegrid: true
        }
    ],allColModelA = JSON.parse(JSON.stringify(colModel));
    //初始化加载
    $('#X_Tablea').XGrid({
        url:'/proxy-purchase/purchase/planInformation/getPlanInformationData',
        postData: {
            "buyerCode": $("#buyerCode").val(),
            "originalProductCode": $("#originalProductCode").val(),
            "productCode": $("#productCode").val(),
            "manufCode": $("#manufCode").val(),
            "supplierCode": $("#supplierCode").val(),
            "channelId":$("#channelId").val(),
            "isNew":$("#isNew").val(),
            "firstSupplierCode": $("#firstSupplierCode").val(),
            "centralizedPurchaseType": $("#centralizedPurchaseType").val(),
            "categoryUserCode": $("#categoryUserCode").val(),
            "menuFlag": $("#menuFlag").val(),
            "orgCode": $("#orgCodes").val()

        },
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-a',//设置翻页所在html元素名称
        rownumbers: true,
        key: "id",
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        gridComplete: function () {
            var data = $(this).XGrid("getRowData");
            if (!data.length) {
                utils.dialog({
                    content: "查询无数据",
                    quickClose: true,
                    timeout: 2000
                }).show();
            }
            //初始化头部提示
            initQuesta();
            //按钮权限
            isBuyer();
        }
    });

    function toDecima(x) {
        var f = parseFloat(x);
        if (isNaN(f)) {
            return 0.00;
        }
        var f = Math.round(x*100)/100;
        var s = f.toString();
        var rs = s.indexOf('.');
        if (rs < 0) {
            rs = s.length;
            s += '.';
        }
        while (s.length <= rs + 2) {
            s += '0';
        }
        return s;
    }

    function initQuesta() {
        var questaOption = [{
            th:'sixtySaleVolume',
            title:'（60天销售出库-60天销售入库）/60',
            top:44,
            left:190
        },{
            th:'thirtySaleVolume',
            title:'（30天销售出库-30天销售入库）/30',
            top:44,
            left:190
        },{
            th:'fifteenSaleVolume',
            title:'（15天销售出库-15天销售入库）/15',
            top:44,
            left:190
        },{
            th:'sevenSaleVolume',
            title:'（7天销售出库-7天销售入库）/7',
            top:44,
            left:190
        },{
            th:'productStrength',
            title:'A类：上月销售金额＞3W；<br/>B类：1W＜上月销售金额≤3W；<br/>' +
                'C类：3K＜上月销售金额≤1W；<br/>D类：上月销售金额＜3K；' ,
            top:100,
            left:190
        },{
            th:'safetyStockAmount',
            title:'安全库存天数*日均销售数量（取前30天日均销售数量）',
            top:44,
            left:190
        },{
            th:'upperLimitStockAmount',
            title:'库存上限天数*日均销售数量（取前30天日均销售数量）',
            top:44,
            left:190
        },{
            th:'thirtyAverageDailySales',
            title:'已扣减售罄和下架天数',
            top:44,
            left:190
        },{
            th:'sixtyAverageDailySales',
            title:'已扣减售罄和下架天数',
            top:44,
            left:190
        }];
        $.each(questaOption,function (index,item) {
            $('.XGridHead').delegate('th[row-describedby='+item.th+'] .questa',{
                mouseover:function (e) {
                    $("body").append("<div id='div_toop'><div id='inner'>"+item.title+"</div></div>");
                    $("#div_toop")
                        .css({
                            "top": (e.pageY - parseFloat(item.top)) + "px",
                            "position": "absolute",
                            "left": (e.pageX - parseFloat(item.top)) + "px"
                        }).show("fast");
                },
                mouseout:function () {
                    $("#div_toop").remove();
                },
                click:function () {
                   // alert('88')
                }
            })
        });
    }
    // 业务类型切换
    $('#channelIdDetail').change(function(that){
        $('#lastSupplierCode, #lastSupplierName, #simpleCode, #supplierId').val('');

    })
    //生成采购订单
    $('#generatePurchaseOrder').on('click',function(){
        $('#channelIdDetail').val('1');
        $('#channelIdDetail_inp').val('YBM');
        $('#generate_purchase_order_firstSupplierCode, #generate_purchase_order_firstSupplierName, #simpleCode, #supplierId').val('');
        $('#generatePurchaseChoiceStatus').selectpicker('val', ['0','1','2']);
        var vl = document.querySelector('#generate_purchase_order');
        let caigoudan = utils.dialog({
            title: '生成采购订单',
            content: vl,
            width: 470,
            height:200,
            button:
                [
                    {
                        value: '下一步',
                        callback: function () {
                            //首选供应商code
                            let firstSupplierCode = $('#generate_purchase_order_firstSupplierCode').val();
                            //业务类型
                            let channelIdDetail = $('#channelIdDetail').val();
                            //备货状态
                            let purchaseChoiceStatus = $("#generatePurchaseChoiceStatus").val();
                            // 是否集采 1：是 0：否
                            let centralizedPurchaseType = $("#centralizedPurchaseTypePop").val();
                            console.log("firstSupplierCode:"+firstSupplierCode);
                            if(firstSupplierCode == null || firstSupplierCode == ""){
                                let selectSupplierStr = "供应商不能为空";
                                utils.dialog({content: selectSupplierStr, quickClose: true, timeout: 6000}).showModal();
                                return false;
                            }
                            if(channelIdDetail == null || channelIdDetail == ""){
                                let selectSupplierStr = "业务类型不能为空";
                                utils.dialog({content: selectSupplierStr, quickClose: true, timeout: 6000}).showModal();
                                return false;
                            }
                            if(centralizedPurchaseType == null || centralizedPurchaseType == ""){
                                let selectSupplierStr = "订单属性不能为空";
                                utils.dialog({content: selectSupplierStr, quickClose: true, timeout: 6000}).showModal();
                                return false;
                            }
                            let formData = new FormData();
                            formData.append("supplierCode",firstSupplierCode);
                            //对供应商进行验证
                            $.ajax({
                                url : '/proxy-purchase/purchase/planInformation/supplierVerify',
                                type : 'POST',
                                data : formData,
                                processData : false,
                                contentType : false,
                                beforeSend:function(){
                                    console.log("正在进行供应商验证，请稍候");
                                    parent.showLoading();
                                },
                                success : function(data) {
                                    parent.hideLoading();
                                    if(data.code==0){
                                        console.log(data.result);
                                        let colNamesDetail =['隐藏id','状态','最近转单', '商品编码', '商品名称','规格','生产厂家','建议采购','最近购价','可用量','日均销量','订单属性'];
                                        let colModelDetail =[
                                            {
                                                name: 'id',
                                                index: 'id',
                                                hidden:true ,
                                                hidegrid: true ,
                                                width:30
                                            },
                                            {
                                                name: 'choiceStatus',
                                                index: 'choiceStatus',
                                                width:60,
                                                formatter: function (val){
                                                    if (val ==5){
                                                        return "超高库存";
                                                    }else if(val ==4){
                                                        return "高库存";
                                                    }else if(val ==3){
                                                        return "正常";
                                                    }else if(val ==2){
                                                        return "需补货";
                                                    }else if(val ==1){
                                                        return "即将断货";
                                                    }else if(val ==0){
                                                        return "断货";
                                                    }else if(val ==6){
                                                        return "近期无销售";
                                                    }else{
                                                        return "";
                                                    }
                                                }
                                            },
                                            {
                                                name: 'recentPurchaseTime',
                                                width: 90,
                                                formatter: function (value) {
                                                    if(value){
                                                        return moment(value).format("YYYY/MM/DD HH:mm");
                                                    }else{
                                                        return "";
                                                    }
                                                }
                                            },
                                            {
                                                name: 'productCode',
                                                width:100,
                                            },
                                            {
                                                name: 'productName',
                                                width:130
                                            },
                                            {
                                                name: 'specifications',
                                                width: 110
                                            },
                                            {
                                                name: 'manufName',
                                                width: 90
                                            },
                                            {
                                                name: 'recommendedPurchNumber',
                                                width: 90
                                            },
                                            {
                                                name: 'lastTaxPrice',
                                                width: 90,
                                                formatter: function (e) {
                                                    if(e == null){
                                                        return 0.00;
                                                    }
                                                    return  toDecima(e)
                                                }
                                            },
                                            {
                                                name: 'hegekeyongkucun',
                                                width: 70
                                            },
                                            {
                                                name: 'sixtyAverageDailySales',
                                                width: 70
                                            },{
                                                name: 'centralizedPurchaseName',//是否集采
                                                index: 'centralizedPurchaseName',
                                                hidden:true ,
                                            }
                                            ,{
                                                name: 'centralizedPurchaseType',//是否集采
                                                index: 'centralizedPurchaseType',
                                                hidden:true ,
                                            }

                                        ];
                                        //生成采购订单
                                        $('#X_Tablea_Detail').XGrid({
                                            url:'/proxy-purchase/purchase/planInformation/getPlanInformationData',
                                            postData: {
                                                "firstSupplierCode": $("#generate_purchase_order_firstSupplierCode").val(),
                                                "choiceStatus":JSON.stringify(purchaseChoiceStatus),
                                                "channelId":$("#channelIdDetail").val(),
                                                "centralizedPurchaseType":centralizedPurchaseType,
                                                "flage" : "recommended_purch_number",
                                                "menuFlag": $("#menuFlag").val(),
                                                "orgCode": $("#orgCodes").val()
                                            },
                                            colNames: colNamesDetail,
                                            colModel: colModelDetail,
                                            rowNum: 500,
                                            //rowList: [500],
                                            altRows: true,//设置为交替行表格,默认为false
                                            //attachRow:true,
                                            selectandorder: true,
                                            pager: '#grid-pager-a-detail',//设置翻页所在html元素名称
                                            //rownumbers: true,
                                            key: "id",
                                            ondblClickRow: function (e, c, a, b) {
                                                console.log('双击行事件', e, c, a, b);
                                            },
                                            gridComplete: function () {
                                                var data = $(this).XGrid("getRowData");
                                                if (!data.length) {
                                                    utils.dialog({
                                                        content: "无对应商品行",
                                                        quickClose: true,
                                                        timeout: 2000
                                                    }).show();
                                                }
                                                setTimeout(function () {
                                                    $('#generate_purchase_order_detail .XGridHead tr').find('input[type=checkbox]').click()
                                                },0)
                                            }
                                        });
                                        let v2 = document.querySelector('#generate_purchase_order_detail');
                                        $('#generate_purchase_order_supplierNameDetail').val($('#generate_purchase_order_firstSupplierName').val());
                                        $('#generate_purchase_order_firstSupplierCodeDetail').val(firstSupplierCode);
                                        utils.dialog({
                                            title: '计划表生成采购订单商品明细',
                                            width: $(window).width() * 0.95,
                                            content: v2,
                                            /*cancelValue: '取消',
                                            cancel: true,*/
                                        }).showModal();

                                    }else{
                                        let errDialog = utils.dialog({
                                            title: '提示',
                                            content: data.msg,
                                            okValue: '确定',
                                            ok: function () {
                                                errDialog.close().remove()
                                            }
                                        }).showModal();
                                    }
                                },
                                error:function () {
                                    utils.dialog({content: '供应商验证异常，请重试！', quickClose: true, timeout: 2000}).showModal();
                                }
                            });
                            return  false;
                        },
                        autofocus: true
                    }
                ],
        }).showModal();
    });
    $('body').on('click','#generate_purchase_order_firstSupplierCodeIcon',function (e) {
        dialog({
            url: '/proxy-purchase/purchase/purchaseOrder/toSupplier',
            title: '供应商列表',
            width: $(window).width() * 0.9,
            height: '550px',
            data: $(e.target).val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var obj = this.returnValue;
                    $("#generate_purchase_order_firstSupplierCode").val(obj.supplierCode);
                    $("#supplierId").val(obj.id);
                    $("#generate_purchase_order_firstSupplierName").val(obj.supplierName);
                    $.ajax({
                        type: "POST",
                        url: "/proxy-purchase/purchase/supplier/getForSupplierVoList4ByPurchaseOrder?id=" + obj.id,
                        async: false,
                        error: function () {
                        },
                        success: function (data) {
                            let selectoverStr = "";
                            let boolean = false;
                            //付款方式
                            if (data.supplierPaymentVoList != "" && data.supplierPaymentVoList != null) {
                                obj.supplierPaymentVoList = JSON.stringify(data.supplierPaymentVoList);
                            } else {
                                obj.supplierPaymentVoList = "";
                            }
                            //结算方式
                            if (data.supplierSettlementVoList != "" && data.supplierSettlementVoList != null) {
                                obj.supplierSettlementVoList = JSON.stringify(data.supplierSettlementVoList);
                            } else {
                                obj.supplierSettlementVoList = "";
                            }
                            //仓库地址
                            if (data.listSupplyStoreAddress != "" && data.listSupplyStoreAddress != null) {
                                obj.listSupplyStoreAddress = JSON.stringify(data.listSupplyStoreAddress);
                            } else {
                                obj.listSupplyStoreAddress = "";
                            }

                            //经营访问
                            if (data.simpleCodeList != null && data.simpleCodeList != "") {
                                var ary = [];
                                $.each(data.simpleCodeList, function (i, v) {
                                    ary.push(v);
                                });
                                obj.simpleCodeList = ary.join(',');
                            } else {
                                obj.simpleCodeList = "";
                            }
                            //经营范围
                            $("#simpleCode").val(obj.simpleCodeList);
                            //不足一个月
                            if (data.overOneMouthList != null && data.overOneMouthList != "") {
                                var ary = [];
                                $.each(data.overOneMouthList, function (i, v) {
                                    ary.push(v);
                                });
                                obj.overOneMouthList = ary.join(',');
                            } else {
                                obj.overOneMouthList = "";
                            }
                            //过期证件
                            if (data.overDueList != null && data.overDueList != "") {
                                var ary = [];
                                $.each(data.overDueList, function (i, v) {
                                    ary.push(v);
                                });
                                obj.overDueList = ary.join(',');
                            } else {
                                obj.overDueList = "";
                            }

                            if (obj.overDueList != "") {
                                selectoverStr = "该供应商{" + obj.supplierCode + "}的{" + obj.overDueList + "}资质已过期!<br/>";
                                boolean = true;
                            }
                            if (obj.overOneMouthList != "") {
                                selectoverStr += "该供应商{" + obj.supplierCode + "}的{" + obj.overOneMouthList + "}资质即将过期!";
                            }
                            if (selectoverStr != "") {
                                utils.dialog({content: selectoverStr, quickClose: true, timeout: 6000}).showModal();
                            }

                            //订单类型
                           /* if (data.itemType != null && data.itemType != '') {
                                obj.itemType = data.itemType
                            }
                            //是否可修改金额
                            if (data.ifUpdatePrice != null && data.ifUpdatePrice != '') {
                                obj.ifUpdatePrice = data.ifUpdatePrice
                            }
                            //数量百分比
                            if (data.adjustPercentageLimit != null && data.adjustPercentageLimit != '') {
                                obj.adjustPercentageLimit = data.adjustPercentageLimit
                            }*/
                        }
                    });
                }else {
                    $("#generate_purchase_order_firstSupplierCode").val('');
                    $("#supplierId").val('');
                    $("#generate_purchase_order_firstSupplierName").val('');
                }
            }
        }).showModal();
    })
    /*$('body').on({
        dblclick: function (e) {
            commodity_search_dia($(e.target).attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$(e.target).attr('oldvalue')){
                commodity_search_dia()
            }
        }
    }).siblings('.glyphicon-search').on("click", function (e) {
        commodity_search_dia($(e.target).attr('oldvalue'))
        e.stopPropagation();

    });*/

    //转采购订单校验
    $('#convertToPurchaseOrder').on('click',function () {
        let rowDt =  $('#X_Tablea_Detail').XGrid('getSeleRow');
        let simpleCode = $('#simpleCode').val();
        let supplierId = $('#supplierId').val();
        //let supplierName = $('#supplierNameDetail').val();
        //let supplierCode = $('#lastSupplierCode').val();
        let supplierName = $('#generate_purchase_order_supplierNameDetail').val();
        let supplierCode = $('#generate_purchase_order_firstSupplierCodeDetail').val();
        let channelId = $('#channelIdDetail').val();
        let channelName = $('#channelIdDetail_inp').val();
        let centralizedPurchaseType = $("#centralizedPurchaseTypePop").val();

        //判断是否有选中
        if(rowDt.length>0) {
            var selectProductCode ="";
            if(rowDt == undefined || rowDt == null){

            }else if(rowDt.length == undefined ){
                selectProductCode = rowDt.productCode;
            }else{
                $.each(rowDt,function(i,obj){
                    selectProductCode = selectProductCode + obj.productCode +",";
                });
            }
            console.log(selectProductCode);
            let formData = new FormData();
            formData.append("selectProductCode",selectProductCode);
            //formData.append("supplierId",supplierId);
            formData.append("simpleCode",simpleCode);
            formData.append("supplierCode",supplierCode);
            formData.append("supplierName",supplierName);
            formData.append("channelId",channelId);
            formData.append("channelName",channelName);
            formData.append("centralizedPurchaseType",centralizedPurchaseType);
            $.ajax({
                url : '/proxy-purchase/purchase/purchaseOrder/generatePurchaseOrder',
                type : 'POST',
                data : formData,
                processData : false,
                contentType : false,
                beforeSend:function(){
                    console.log("正在进行，请稍候");
                },
                success : function(data) {
                    if(data.code==0){
                        console.log(data.result);
                        let url = "/proxy-purchase/purchase/purchaseOrder/toAdd?productCodeList="+JSON.stringify(data.result).replace(/\"/g,'')+"&supplierId="+supplierId+"&supplierCode="+supplierCode+"&channelId="+channelId+"&channelName="+channelName+"&supplierName="+supplierName+"&fromWhere=planInfo&centralizedPurchaseType="+centralizedPurchaseType;
                        parent.$('#mainFrameTabs').bTabsAdd("toLis", "新建采购订单", url);
                        // parent.$('#mainFrameTabs').bTabsAdd("toLis", "新建采购订单", url);
                    }else {
                        utils.dialog({
                            title: '提示',
                            content: '<div class="totalWrap" style="height:100%;">' +
                                '<div class="totalContent" style="overflow-y:scroll;height: inherit;">'+data.msg+'</div>' +
                                '<textarea id="copyInput" class="btn btn-info" style="float: right;margin-top: -966px;"  >'+data.msg+'</textarea> '+
                                '</div>',
                            width: 360,
                            height:150,
                            okValue: '确认',
                            ok: function () {

                            },
                            statusbar:'<button type="button" class="btn btn-info" id="copyBtn" style="float: right;margin-top: -6px;">复制信息</button>'
                        }).showModal();
                        $("#copyBtn").on("click",function () {
                            $("#copyInput").select(); // 选择对象
                            let flag = document.execCommand("Copy","false",null); // 执行浏览器复制命令
                            if(flag) utils.dialog({content: '复制成功！', quickClose: true, timeout: 2000}).show();
                        })
                    }
                },
                error:function () {
                    /*utils.dialog({
                        title: '提示',
                        content: '<div class="totalWrap" style="height:100%;">' +
                            '      <div class="totalContent">转采购订单失败</div>' +
                            '</div>',
                        width: 360,
                        height:150,
                        okValue: '确认',
                        ok: function () {
                            location.reload()
                        }
                    }).showModal();*/
                    utils.dialog({content: '转采购订单失败！', quickClose: true, timeout: 4000}).showModal();
                    return false;
                }
            });

        }else{
            utils.dialog({title: '提示',content: '请先选中商品行', quickClose: true, timeout: 4000}).showModal();
            return false;

        }
    });

    $('#generate_purchase_order_firstSupplierCode').Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/supplier/loadSupplierData4PurchaseOrderProxy',
        paramName: 'keyword',
        params: {
            pageNum: 1,
            pageSize: 5
        },
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (obj) {
            $("#generate_purchase_order_firstSupplierCode").val(obj.data);
            $("#supplierId").val(obj.id);
            $("#generate_purchase_order_firstSupplierName").val(obj.value);
            obj.supplierCode = obj.data;
            obj.supplierName = obj.value;
            $.ajax({
                type: "POST",
                url: "/proxy-purchase/purchase/supplier/getForSupplierVoList4ByPurchaseOrder?id=" + obj.id,
                async: false,
                error: function () {
                },
                success: function (data) {
                    let selectoverStr = "";
                    let boolean = false;
                    //付款方式
                    if (data.supplierPaymentVoList != "" && data.supplierPaymentVoList != null) {
                        obj.supplierPaymentVoList = JSON.stringify(data.supplierPaymentVoList);
                    } else {
                        obj.supplierPaymentVoList = "";
                    }
                    //结算方式
                    if (data.supplierSettlementVoList != "" && data.supplierSettlementVoList != null) {
                        obj.supplierSettlementVoList = JSON.stringify(data.supplierSettlementVoList);
                    } else {
                        obj.supplierSettlementVoList = "";
                    }
                    //仓库地址
                    if (data.listSupplyStoreAddress != "" && data.listSupplyStoreAddress != null) {
                        obj.listSupplyStoreAddress = JSON.stringify(data.listSupplyStoreAddress);
                    } else {
                        obj.listSupplyStoreAddress = "";
                    }

                    //经营访问
                    if (data.simpleCodeList != null && data.simpleCodeList != "") {
                        var ary = [];
                        $.each(data.simpleCodeList, function (i, v) {
                            ary.push(v);
                        });
                        obj.simpleCodeList = ary.join(',');
                    } else {
                        obj.simpleCodeList = "";
                    }
                    //经营范围
                    $("#simpleCode").val(obj.simpleCodeList);
                    //不足一个月
                    if (data.overOneMouthList != null && data.overOneMouthList != "") {
                        var ary = [];
                        $.each(data.overOneMouthList, function (i, v) {
                            ary.push(v);
                        });
                        obj.overOneMouthList = ary.join(',');
                    } else {
                        obj.overOneMouthList = "";
                    }
                    //过期证件
                    if (data.overDueList != null && data.overDueList != "") {
                        var ary = [];
                        $.each(data.overDueList, function (i, v) {
                            ary.push(v);
                        });
                        obj.overDueList = ary.join(',');
                    } else {
                        obj.overDueList = "";
                    }

                    if (obj.overDueList != "") {
                        selectoverStr = "该供应商{" + obj.supplierCode + "}的{" + obj.overDueList + "}资质已过期!<br/>";
                        boolean = true;
                    }
                    if (obj.overOneMouthList != "") {
                        selectoverStr += "该供应商{" + obj.supplierCode + "}的{" + obj.overOneMouthList + "}资质即将过期!";
                    }
                    if (selectoverStr != "") {
                        utils.dialog({content: selectoverStr, quickClose: true, timeout: 6000}).showModal();
                    }

                    //订单类型
                    /*if (data.itemType != null && data.itemType != '') {
                        obj.itemType = data.itemType
                    }
                    //是否可修改金额
                    if (data.ifUpdatePrice != null && data.ifUpdatePrice != '') {
                        obj.ifUpdatePrice = data.ifUpdatePrice
                    }
                    //数量百分比
                    if (data.adjustPercentageLimit != null && data.adjustPercentageLimit != '') {
                        obj.adjustPercentageLimit = data.adjustPercentageLimit
                    }*/
                }
            });
        },
        dataReader: {
            list: 'result', //结果集，不写返回结果为数组
            listChild: 'list',
            data: 'supplierCode',
            value: 'supplierName',
            id: 'id',
            supplyBussinessUserName: "supplyBussinessUserName",
            supplyBussinessUserId: "supplyBussinessUserId",
            supplyBussinessUserMobile: "supplyBussinessUserMobile",
            supplierPaymentVoList: "supplierPaymentVoList",
            supplierSettlementVoList: "supplierSettlementVoList",
            listSupplyStoreAddress: "listSupplyStoreAddress",
            supplyOperateId: "supplyOperateId",
            arrivalPeriod: "arrivalPeriod",
            overOneMouthList: "overOneMouthList",
            overDueList: "overDueList",
            simpleCodeList: "simpleCodeList"

        }
        /*,
        onNoneSelect: function (params, suggestions) {
            $("#lastSupplierId").val("");
            $("#supplierName").val("");
            setTimeout(function () {
                $("#supplierName").attr('oldvalue', '');
            }, 200)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }*/
    });

    //查询数据，重置data
    $('#searchBtn').on('click', function () {
        var choiceStatus = $("#choiceStatus").val();
        $('#X_Tablea').setGridParam({
            url: '/proxy-purchase/purchase/planInformation/getPlanInformationData',
            postData: {
                "buyerCode": $("#buyerCode").val(),
                "originalProductCode": $("#originalProductCode").val(),
                "productCode": $("#productCode").val(),
                "manufCode": $("#manufCode").val(),
                "supplierCode": $("#supplierCode").val(),
                "channelId":$("#channelId").val(),
                "isNew":$("#isNew").val(),
                "choiceStatus":JSON.stringify(choiceStatus),
                "firstSupplierCode": $("#firstSupplierCode").val(),
                "centralizedPurchaseType": $("#centralizedPurchaseType").val(),
                "categoryUserCode": $("#categoryUserCode").val(),
                "menuFlag": $("#menuFlag").val(),
                "orgCode": $("#orgCodes").val()
            }
        }).trigger('reloadGrid');
        //按钮权限
        isBuyer();
    });


    function ObjData(key,value){
        this.Key=key;
        this.Value=value;
    }
    //重置
    $("#clearBtn").on("click", function () {
        $('#buyerCode').val('');
        $('#buyerName').val('');
        $('#originalProductCode').val('');
        $('#productCode').val('');
        $('#productName').val('');
        $('#manufCode').val('');
        $('#manufName').val('');
        $('#supplierCode').val('');
        $('#supplierName').val('');
        $('#firstSupplierCode').val('');
        $('#firstSupplierName').val('');
        $("#isNew").val('');
        // document.getElementById("choiceStatus").options.selectedIndex = ''; //回到初始状态
        $("#choiceStatus").selectpicker('val',['noneSelectedText']);
        $("#choiceStatus").selectpicker('refresh');//下拉框进行重置刷新
        $('#channelId').val('');
        $('#channelId_inp').val('');
        $('#categoryUserName').val('');
        $('#categoryUserCode').val('');
        $("#orgCodes").val('');
        $("#orgCodes").selectpicker('refresh');//下拉框进行重置刷新
    });

    var colNameA =['业务类型','机构','原商品编码', '商品编码','商品名称','首选供应商','规格','生产厂家','条形码','批准文号','中包装','件包装','批件效期','状态','订单属性','药帮忙售价','最后入库价',
        '最近两个月最低价','最后供应商','最低价供应商','TOP排名',lastLastMonth+ '月总销量', lastMonth+ '月总销量', curMonth+ '月总销量','60天/日均销量','30天/日均销量','15天/日均销量','7天/日均销量','商品流速',
        '可销天数','可用库存','合格库数量','在途数量','安全库存','库存上限',
		'30天内售罄天数','60天内售罄天数','30天内下架天数','60天内下架天数','30天净日均销量','60天净日均销量',
		'计划检查','商品定位','采购员','大区经理', '付款情况', '建议采购量','是否新品','备货状态','更新时间',"品类经理"];

    //导出
    $('#exportBtn').on('click', function () {
        utils.exportAstrictHandle('X_Tablea',Number($('#grid-pager-a #totalPageNum').text()),1).then(()=>{
            return false;
    }).catch(()=> {
            var cs = $("#toggle_wrap .active").attr("id");
            if(cs == "orderList"){
                //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                var ck = false;
                // copy this parameter and the below buttons
                //选择导出列
                var nameModel = "";
                addHtmlA(colNameA);
                dialog({
                    content: $("#setCol"),
                    title: '筛选列',
                    width: $(window).width() * 0.55,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {
                        var newColName = [], newColModel = [];
                        var colName = [];
                        var colNameDesc = [];
                        $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                            if ($(this).is(":checked")) {
                                nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                                colName.push(allColModelA[index].name);
                                colNameDesc.push($(this).attr('name'));
                            }
                        });
                        if(nameModel.length == 0){
                            utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                        parent.showLoading({hideTime: 999999999});
                        data = {
                            nameModel: nameModel,
                            buyerCode: $("#buyerCode").val(),
                            originalProductCode: $("#originalProductCode").val(),
                            productCode: $("#productCode").val(),
                            manufCode: $("#manufCode").val(),
                            supplierCode: $("#supplierCode").val(),
                            isNew:$("#isNew").val(),
                            choiceStatus:$("#choiceStatus").val(),
                            centralizedPurchaseType:$("#centralizedPurchaseType").val(),
                            firstSupplierCode: $("#firstSupplierCode").val()
                        };
                        var colNames= colName.join(",")
                        var colNameDescs= colNameDesc.join(",")
                        var choiceStatuses = $("#choiceStatus").val();
                        var choiceStr = "";
                        if(choiceStatuses != 'undefined' && choiceStatuses != null && choiceStatuses != '' && choiceStatuses.length != 0){
                            choiceStr =  choiceStatuses.join(",");
                        }

                        var formData2 = {
                            moduleName: 'purchasePlanInfo',
                            taskCode: '1008',
                            colName: colNames,
                            colNameDesc: colNameDescs,
                            fileName: '计划信息表',
                            exportParams: {
                                buyerCode: $("#buyerCode").val(),
                                originalProductCode: $("#originalProductCode").val(),
                                productCode: $("#productCode").val(),
                                manufCode: $("#manufCode").val(),
                                supplierCode: $("#supplierCode").val(),
                                isNew:$("#isNew").val(),
                                choiceStatus:choiceStr,
                                centralizedPurchaseType:$("#centralizedPurchaseType").val(),
                                firstSupplierCode: $("#firstSupplierCode").val(),
                                categoryUserCode: $("#categoryUserCode").val(),
                                menuFlag: $("#menuFlag").val(),
                                orgCode: $("#orgCodes").val()
                            }
                        };
                         if(typeof sel == "undefined" || sel == null || sel == "") {
                             utils.dialog({
                                 title: '温馨提示',
                                 content: '导出预估条数为'+Number($('#grid-pager-a #totalPageNum').text())+'最大导出30000条，导出任务提交成功后页面将关闭，是否确认导出？',
                                 okValue: '确定',
                                 ok: function () {
                                     $.ajax({
                                         url: "/proxy-purchase/purchase/commonExport/commonCommitExportTask",
                                         type: 'post',
                                         dataType: 'json',
                                         data: {
                                             "data":JSON.stringify(formData2)
                                         },
                                         success: function (res) {
                                             if (res) {
                                                 if (res.code === 0) {
                                                     utils.dialog({
                                                         title: '温馨提示',
                                                         content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                                         okValue: '确定',
                                                         ok: function () {

                                                         }
                                                     }).showModal()
                                                 } else {
                                                     utils.dialog({
                                                         title: '温馨提示',
                                                         content: res.msg,
                                                         okValue: '确定',
                                                         ok: function () {

                                                         }
                                                     }).showModal()
                                                 }
                                             }
                                         }
                                     });
                                 }
                             }).showModal()
                        }else {
                            httpPost("/proxy-purchase/purchase/planInformation/exportExcelPlanInformationReport", data);
                        }
                        parent.hideLoading();
                    },
                    // copy button to other dialogues
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                if(!ck){
                                    $("#checkRow input").prop("checked",false);
                                    ck = true;
                                }else if(ck){
                                    $("#checkRow input").prop("checked","checked");
                                    ck = false;
                                }else{
                                    return false;
                                };
                                return false;
                            }
                        }
                    ]
                    //copy ends here
                }).showModal();

            }else{
                return;
            }
        })
    });

    // 设置显示列列，集成到 xgrid.js 里了
    $("#set_tables_rowa").click(function () {
        $('#X_Tablea').XGrid('filterTableHead');

    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none; padding-left: 2.4rem;">'
                + '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">'
                    + '            <div class="checkbox">'
                    + '                <label>'
                    + `                    <input style="margin-right: 10px" ${arry[i]==='大区经理'?'':'checked'} type="checkbox" name="`
                    + arry[i] + '">' + arry[i] + '                </label>'
                    + '            </div>' + '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }
    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    function Post(URL, PARAMTERS) {
        // 创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        // 如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        // 添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        // 提交数据
        temp_form.submit();
    }


    //生产厂家
    var isManufSelected='';
    $('#manufName').Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/purchaseOrder/getManuFactoryList', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        //lookup: ts, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.manufactoryName, data: dataItem.manufactoryId};
                })
            };
        },
        onSelect: function (result) {
            $("#manufName").val(result.value);
            $("#manufCode").val(result.data);
            isManufSelected = result.value;
        },onNoneSelect: function (params, suggestions) {
            $("#manufName").val("");
            $("#manufCode").val("");
    }
    });
    //未选中生产厂家信息时，清空值
  /*  $("#manufName").blur(function(){
        var ivlsupplierCode = $(this).val();
        if(isManufSelected != ivlsupplierCode){
            $("#manufName").val("");
            $("#manufCode").val("");
        }
    })*/

    //采购员联想查询
    var isBuySelected = '';
    $('#buyerName').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/purchaseRefundProductOrder/getPurchaseRefundProductOrderProductUserName?pageNum=1&pageSize=5&sort=asc', //异步请求
        paramName: 'userName',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.userName, data: dataItem.id};
                })
            };
        },
        onSelect: function (result) {
            $("#buyerCode").val(result.data);
            $("#buyerName").val(result.value);
            isBuySelected = result.value;
        },onNoneSelect: function (params, suggestions) {
            $("#buyerName").val("");
            $("#buyerCode").val("");
        }
    });
    //品类员联想查询
    var isBuySelectedForCategoryUserName = '';
    $('#categoryUserName').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/purchaseRefundProductOrder/getCategoryUserUserName?pageNum=1&pageSize=5&sort=asc', //异步请求
        paramName: 'userName',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.userName, data: dataItem.id};
                })
            };
        },
        onSelect: function (result) {
            $("#categoryUserCode").val(result.data);
            $("#categoryUserName").val(result.value);
            isBuySelectedForCategoryUserName = result.value;
        },onNoneSelect: function (params, suggestions) {
            $("#categoryUserName").val("");
            $("#categoryUserCode").val("");
        }
    });

    //未选中采购员联想信息时，清空值
  /*  $("#buyerName").blur(function(){
        var ivl = $(this).val();
        if(ivl != isBuySelected){
            $("#buyerName").val("");
            $("#buyerCode").val("");
        }
    });*/


    //商品联想搜索
    var isProductSeleted="";
    $('#productName').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/supplier/findPageInfoByMnemonInfo', //异步请求
        paramName: 'keyword',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.productName +"\xa0\xa0\xa0"+ dataItem.specifications +"\xa0\xa0\xa0"+ dataItem.manufacturerName, data: dataItem.productCode};
                })
            };
        },
        onSelect: function (result) {
            $("#productCode").val(result.data);
            $("#productName").val(result.value);
            isProductSeleted = result.value;

        },
        onNoneSelect: function (params, suggestions) {
            $("#productName").val("");
            $("#productCode").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });


    //未选中商品联想信息时，清空值
   /* $("#productName").blur(function(){
        var ivlproduct = $(this).val();
        if(isProductSeleted != ivlproduct){
            $("#productName").val("");
            $("#productCode").val("");
        }
    });*/


    $('#supplierName').on({
        dblclick: function (e) {
            commodity_search_dia($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                commodity_search_dia()
            }
        }
    }).siblings('.glyphicon-search').on("click", function (e) {
        commodity_search_dia($("#supplierName").attr('oldvalue'))
        e.stopPropagation();
    });


    function commodity_search_dia(val) {
        dialog({
            url: '/proxy-purchase/purchase/purchaseOrder/toSupplierPage',
            title: '供应商列表',
            width: $(window).width() * 0.9,
            height: '550px',
            data: val, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#supplierCode").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                }else {
                    $("#supplierCode").val("");
                    $("#supplierName").val("");
                }
            }
        }).showModal();
    };


    //供应商联想搜索
    var isSupplierSeleted="";
    $('#supplierName').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/supplier/getNewOrganBaseListByMnemonInfo', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        autoSelectFirst: true,
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.supplierName, data: dataItem.supplierCode};
                })
            };
        },
        onSelect: function (result) {
            $("#supplierCode").val(result.data);
            $("#supplierName").val(result.value);
            isSupplierSeleted = result.value;
            $("#supplierName").attr('oldvalue',result.value);

        }, onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#supplierCode").val("");
            setTimeout(function () {
                $("#supplierName").attr('oldvalue','');
            },200)
        }

    });


    //未选中商品联想信息时，清空值
   /* $("#supplierName").blur(function(){
        var ivlproduct = $(this).val();
        if(isSupplierSeleted != ivlproduct){
            $("#supplierName").val("");
            $("#supplierCode").val("");
        }
    });*/

    $('#firstSupplierName').on({
        dblclick: function (e) {
            commodity_search_dia_first_supplier($("#firstSupplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#firstSupplierName").attr('oldvalue')){
                commodity_search_dia_first_supplier()
            }
        }
    }).siblings('.glyphicon-search').on("click", function (e) {
        commodity_search_dia_first_supplier($("#firstSupplierName").attr('oldvalue'))
        e.stopPropagation();
    });


    function commodity_search_dia_first_supplier(val) {
        dialog({
            url: '/proxy-purchase/purchase/purchaseOrder/toSupplierPage',
            title: '供应商列表',
            width: $(window).width() * 0.9,
            height: '550px',
            data: val, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#firstSupplierCode").val(data.supplierCode);
                    $("#firstSupplierName").val(data.supplierName);
                }else {
                    $("#firstSupplierCode").val("");
                    $("#firstSupplierName").val("");
                }
            }
        }).showModal();
    };


    //供应商联想搜索
    var isSupplierSeleted="";
    $('#firstSupplierName').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/supplier/getNewOrganBaseListByMnemonInfo', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        autoSelectFirst: true,
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.supplierName, data: dataItem.supplierCode};
                })
            };
        },
        onSelect: function (result) {
            $("#firstSupplierCode").val(result.data);
            $("#firstSupplierName").val(result.value);
            isSupplierSeleted = result.value;
            $("#firstSupplierName").attr('oldvalue',result.value);

        }, onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onNoneSelect: function (params, suggestions) {
            $("#firstSupplierName").val("");
            $("#firstSupplierCode").val("");
            setTimeout(function () {
                $("#firstSupplierName").attr('oldvalue','');
            },200)
        }

    });

    //首选供应商按钮权限
    function isBuyer() {
        if($("#isBuyer").val()=="true"){
            $(".updateFirstSupplier_btn").attr("style","display:block;");
            $(".updateFirstSupplier_p").attr("width: 80px; display: inline-block; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;");
        }else {
            $(".updateFirstSupplier_btn").attr("style","display:none;");
            $(".updateFirstSupplier_p").attr("style","width:100px;");
        }
    }

    //修改首选供应商
    $('body').on('click','.updateFirstSupplier_btn',function(){
        $('#updateFirstSupp_id').val($(this).attr('data-id'));
        $('#updateFirstSupp_channelId').val($(this).attr('data-channelId'));
        $('#updateFirstSupp_channelName').val($(this).attr('data-channelName'));
        $('#updateFirstSupp_productCode').val($(this).attr('data-productCode'));
        $('#updateFirstSupp_productName').val($(this).attr('data-productName'));
        $('#updateFirstSupp_specifications').val($(this).attr('data-specifications'));
        $('#updateFirstSupp_manufName').val($(this).attr('data-manufName'));
        $('#updateFirstSupp_firstSuppCode').val($(this).attr('data-firstSuppCode'));
        $('#updateFirstSupp_firstSuppName').val($(this).attr('data-firstSuppName'));
        var vl = document.querySelector('#update_first_supplier');
        utils.dialog({
            title: '修改首选供应商',
            content: vl,
            width: 470,
            height:270,
            button:
                [
                    {
                        value: '取消',
                        cancel: true
                    },
                    {
                        value: '确认',
                        callback: function () {
                            // id
                            let id = $('#updateFirstSupp_id').val();
                            //首选供应商code
                            let firstSuppCode = $('#updateFirstSupp_firstSuppCode').val();
                            //首选供应商code
                            let firstSuppName = $('#updateFirstSupp_firstSuppName').val();
                            if(firstSuppCode == null || firstSuppCode == "" || firstSuppName == null || firstSuppName == ""){
                                let selectSupplierStr = "首选供应商不能为空";
                                utils.dialog({content: selectSupplierStr, quickClose: true, timeout: 6000}).showModal();
                                return false;
                            }
                            //修改首选供应商
                            $.ajax({
                                url : '/proxy-purchase/purchase/planInformation/updateFirstSupplier?id='+id+'&supplierCode='+firstSuppCode+'&supplierName='+firstSuppName,
                                processData : false,
                                contentType : false,
                                beforeSend:function(){
                                    console.log("正在进行，请稍候");
                                },
                                success : function(data) {
                                    if(data.code==0){
                                        utils.dialog({content: data.result, quickClose: true, timeout: 4000}).showModal();
                                        $("#searchBtn").trigger("click");
                                    }else {
                                        utils.dialog({content: data.msg, quickClose: true, timeout: 6000}).showModal();
                                        return false;
                                    }
                                },
                                error:function () {
                                    utils.dialog({content: '修改失败！', quickClose: true, timeout: 4000}).showModal();
                                    return false;
                                }
                            });
                        },
                        autofocus: true
                    }
                ],
        }).showModal();

        //供应商联想搜索
        var isSupplierSeleted="";
        $('#updateFirstSupp_firstSuppName').Autocomplete({
            serviceUrl: '/proxy-purchase/purchase/planInformation/getFirstSupplierVoList?'+'productCode='+$("#updateFirstSupp_productCode").val()+'&channelId='+$("#updateFirstSupp_channelId").val(),
            paramName:'keyword',
            dataType: 'json',
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            triggerSelectOnValidInput: false, // 必选
            showNoSuggestionNotice: true,
            noSuggestionNotice: '查询无结果',
            autoSelectFirst: true,
            transformResult: function (response) {
                return {
                    suggestions: $.map(response, function (dataItem) {
                        return {value: dataItem.supplierName, data: dataItem.supplierCode};
                    })
                };
            },
            onSelect: function (result) {
                $("#updateFirstSupp_firstSuppCode").val(result.data);
                $("#updateFirstSupp_firstSuppName").val(result.value);
                isSupplierSeleted = result.value;
                $("#updateFirstSupp_firstSuppName").attr('oldvalue',result.value);

            }, onSearchComplete: function (query, suggestions) {
                var $ele = $(this);
                if(suggestions && suggestions.length === 0){
                    $ele.attr('oldvalue','');
                }else {
                    $ele.attr('oldvalue',$ele.val())
                }
                if(!$ele.is(":focus")){
                    $ele.Autocomplete('hide');
                }
            },
            onNoneSelect: function (params, suggestions) {
                $("#updateFirstSupp_firstSuppName").val("");
                $("#updateFirstSupp_firstSuppCode").val("");
                setTimeout(function () {
                    $("#updateFirstSupp_firstSuppName").attr('oldvalue','');
                },200)
            }

        });
    });

    $('#updateFirstSupp_firstSuppName').on({
            dblclick: function (e) {
                commodity_search_dia_update_first_supplier($("#updateFirstSupp_firstSuppName").val(),
                    $("#updateFirstSupp_productCode").val(),$("#updateFirstSupp_channelId").val())
            },
            keyup: function (e) {
                if(e.keyCode === 13 && !$("#updateFirstSupp_firstSuppName").attr('oldvalue')){
                    commodity_search_dia_update_first_supplier()
                }
            }
        }).siblings('.glyphicon-search').on("click", function (e) {
            commodity_search_dia_update_first_supplier($("#updateFirstSupp_firstSuppName").val(),
                $("#updateFirstSupp_productCode").val(),$("#updateFirstSupp_channelId").val())
            e.stopPropagation();
    });


    function commodity_search_dia_update_first_supplier(keyword,productCode,channelId) {
        dialog({
            url: '/proxy-purchase/purchase/planInformation/toSupplierPage?keyword='+keyword+'&productCode='+productCode+'&channelId='+channelId,
            title: '供应商列表',
            width: $(window).width() * 0.9,
            height: '550px',
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#updateFirstSupp_firstSuppCode").val(data.supplierCode);
                    $("#updateFirstSupp_firstSuppName").val(data.supplierName);
                }else {
                    $("#updateFirstSupp_firstSuppCode").val("");
                    $("#updateFirstSupp_firstSuppName").val("");
                }
            }
        }).showModal();
    };



    //业务类型搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('0').then( res => {
            console.log(res)
            let _str_name = '', _str_code = '',_str_val, _str_parentCode = '';
            let _str_arr = res.map(item => {
                return item.channelName;
        })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
        })
            _str_code = _str_code_arr.join(',')
            let _str_val_arr = res.map(item => {
                return item.channelValue;
        })
        _str_val =   _str_val_arr.join(',');
            let _str_parentCode_arr = res.map(item => {
                return item.parentCode;
            });
            _str_parentCode = _str_parentCode_arr.join(',');
        $('#channelId_inp').val(_str_val)
        $('#channelId').val(_str_code)
            change_channelID(_str_parentCode)
        })
    });

    //业务类型 输入框双击 弹出业务类型列表
    $("#channelIdDetail_inp").dblclick(function () {
        utils.channelDialog('0', 1).then( res => {
            console.log(res)
           let _str_name = '', _str_code = '',_str_val, _str_parentCode = '';
            let _str_arr = res.map(item => {
                return item.channelName;
        })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
        })
            _str_code = _str_code_arr.join(',')
            let _str_val_arr = res.map(item => {
                return item.channelValue;
        })
        _str_val =   _str_val_arr.join(',');
            let _str_parentCode_arr = res.map(item => {
                return item.parentCode;
            });
            _str_parentCode = _str_parentCode_arr.join(',');
        $('#channelIdDetail_inp').val(_str_val)
         $('#channelIdDetail').val(_str_code)
            change_channelID(_str_parentCode)
        })
    });

})

function  btn_output_list(){
    // var cs = $("#toggle_wrap .active").attr("id");
    var taskCode = "1008";
    var moduleName = "purchasePlanInfo";
    utils.dialog({
        title: '导出列表',
        url: '/proxy-purchase/purchase/commonExport/toExportList?moduleName='+moduleName+'&taskCode='+taskCode,
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
}

