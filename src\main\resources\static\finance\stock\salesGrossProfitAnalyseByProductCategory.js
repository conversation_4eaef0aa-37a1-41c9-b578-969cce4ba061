$(function () {

    //设置日期默认值
    $("#startOrderDate").val(getMonthStartDate());
    $("#endOrderDate").val(getNowFormatDate());

    //合计行方法
    var totalTable = z_utils.totalTable;
    var totalTablea = z_utils.totalTablea;

    //渲染商品大类下拉列表
    initProductCategoriesSelect();

    var param = $('#myform').serializeToJSON();
    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/initBalance/summarySalesGrossProfitAnalyse',
        postData: param,
        colNames: [ '商品大类编码', '商品大类名称', '数量','优惠前销售金额(不含税)','优惠前毛利','优惠前毛利率', '不含税单价', '销售金额(不含税)','销售成本','毛利金额','毛利率','返利金额'
        ],
        colModel: [ {
            name: 'drugClass'
        }, {
            name: 'largeCategoryName'
        }, {
            name: 'quantity'
        },{
            name: 'orderNoTaxAmount',
            width:200,
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        },{
            name: 'beforeProfit',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        },{
            name: 'beforeInterestRate',
            formatter: function (val) {
                if(!val){
                    return '0.00%';
                }else{
                    return val;
                }
            }
        }, {
            name: 'noTaxPrice',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'notaxSum',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'sumOut',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'profit',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'interestRate',
            formatter: function (val) {
                if(!val){
                    return '0.00%';
                }else{
                    return val;
                }
            }
        },{
            name: 'orderRebateAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }],
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,//合计行
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            var data = $(this).XGrid("getRowData");
            if(!data.length){
                utils.dialog({
                    content:"查询无数据",
                    quickClose: true,
                    timeout: 2000
                }).show();
            }
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['quantity','notaxSum','sumOut'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                if(item == 'quantity'){
                    lastRowEle.find("td[row-describedby="+item+"]").text(totalTablea(data,item));
                }else{

                    lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
                }
                lastRowEle.find("td[row-describedby="+item+"]").prop("title","");
            });
        },
        pager: '#grid-pager',
        rownumbers: true,
    });
    //统计方法
    totalSum();


    $("#searchBtn").on("click", function () {
        var param = $('#myform').serializeToJSON();
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/initBalance/summarySalesGrossProfitAnalyse',
            postData: param
        }).trigger('reloadGrid');
        //统计方法
        totalSum();
    });

    // 筛选列，集成到 xgrid.js 里了
    $("#set_tb_rows").click(function () {
        $('#X_Table').XGrid('filterTableHead');
    })

    $('#exportBtn').bind('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {

            var obj = {
                startOrderDate :$("#startOrderDate").val(),
                endOrderDate : $("#endOrderDate").val(),
                summaryType : $("#summaryType").val(),
                drugClass :$("#drugClass").val()
            }
            httpPost("/proxy-finance/finance/initBalance/exportExcelSalesGrossProfitAnalyseSummary", obj);
            //原始处理逻辑代码
            // parent.showLoading({hideTime: 999999999});
            // $.ajax({
            //     url : "/proxy-finance/finance/initBalance/exportExcelSalesGrossProfitAnalyseSummary",
            //     data:{
            //         startOrderDate :$("#startOrderDate").val(),
            //         endOrderDate : $("#endOrderDate").val(),
            //         summaryType : $("#summaryType").val(),
            //         drugClass :$("#drugClass").val()
            //     },
            //     type: "post",
            //     success:function(result){
            //         //校验成功
            //         if(result.code==0){
            //             console.log(result.result);
            //             var url = result.result.filePath;
            //             var extfilename = result.result.extfilename;
            //             var parames = [];
            //             parames.push({ name: "filePath", value: url});
            //             parames.push({ name: "extfilename", value: extfilename});
            //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
            //             parent.hideLoading();
            //         }else{
            //             parent.hideLoading();
            //             utils.dialog({content:result.msg, quickClose: true,
            //                 timeout: 3000}).showModal();
            //         }
            //     }
            // })
        })
    });

    // // 导出
    // $('#exportBtn').bind('click', function () {
    // 	var parames = [];
    //     parames.push({ name: "startDate", value: $("#startDate").val()});
    //     parames.push({ name: "endDate", value: $("#endDate").val()});
    //     parames.push({ name: "summaryType", value: $("#summaryType").val()});
    //     parames.push({ name: "drugClass", value: $("#drugClass").val()});
    //
    //     Post("/proxy-finance/finance/initBalance/exportExcelSalesGrossProfitAnalyseSummary", parames);
    // });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }

    //统计方法
    function totalSum() {
        var param = $('#myform').serializeToJSON();
        $.ajax({
            url:'/proxy-finance/finance/initBalance/totalSumSalesGrossProfitAnalyse',
            type:'post',
            data:param,
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;
                    var shuliang = data.shuliang;
                    var chengben = data.chengben;
                    var xiaoshoujine = data.maolixiaoshoujine;
                    $("#shuliang").text(shuliang);
                    $("#chengben").text(parseFloat(chengben).formatMoney('2', '', ',', '.'));
                    $("#xiaoshoujine").text(parseFloat(xiaoshoujine).formatMoney('2', '', ',', '.'));
                }
            }
        })
    }
})


/**
 * 渲染商品大类下拉列表
 */
function initProductCategoriesSelectTemplate(data) {
    var html = template('productType-tmp', {list: data});
    document.getElementById('productType-div').innerHTML = html;
}

/**
 * 获取商品大类
 */
function initProductCategoriesSelect() {
    $.post("/proxy-sysmanage/sysmanage/dict/querycommonnotpage", { type: 7 },
        function(data){
            console.log(data.result);
            if (data.code == 0) {
                var _data = data.result;
                initProductCategoriesSelectTemplate(_data);
            }
        }, "json");

}


/**
 * 开始日期
 */
function startDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        //startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        maxDate:'#F{$dp.$D(\'endOrderDate\')}'
    });
}

/**
 * 结束日期
 */
function endDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        //startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        minDate:'#F{$dp.$D(\'startOrderDate\')}'
    });
}

/**
 * 获取当前时间，格式YYYY-MM-DD
 * @returns {string}
 */
function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}

//获得本月的开始日期
function getMonthStartDate(){
    var now = new Date();                    //当前日期
    var nowDayOfWeek = now.getDay();         //今天本周的第几天
    var nowDay = now.getDate();              //当前日
    var nowMonth = now.getMonth();           //当前月
    var nowYear = now.getYear();             //当前年
    nowYear += (nowYear < 2000) ? 1900 : 0;  //
    var monthStartDate = new Date(nowYear, nowMonth, 1);
    return formatDate(monthStartDate);
}

//格式化日期：yyyy-MM-dd
function formatDate(date) {
    var myyear = date.getFullYear();
    var mymonth = date.getMonth() + 1;
    var myweekday = date.getDate();

    if (mymonth < 10) {
        mymonth = "0" + mymonth;
    }
    if (myweekday < 10) {
        myweekday = "0" + myweekday;
    }
    return (myyear + "-" + mymonth + "-" + myweekday);
}