let colName = [],colModel = [];
/* 合计计算  */
var totalTable = z_utils.totalTable;
$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    if($('#searchBtnDetail').length > 0 && $('#searchBtnDetail').css('display') != 'none'){
        $('#searchBtn').css('display','none')
    }

    /* 日期初始化 */
    z_utils.initDate('begint', 'endt')


    var drugClassType = ""

    /*查询页面总计*/
    getTotalNum (0);
    /* table_a */
    //data
    var grid_dataY = [];
    var g_item = {
        text1: "2",
        text2: "",
        text3: "",
        text4: "",
        text5: "",


        text6: "",
        text7: "",
        text8: "",
        text9: "",
        text10: "",
        text11: "",
        text12: "2",
        text13: "",
        text14: "",
        text15: "4",
        text16: "6",
        text17: "",
        text18: "",
        text19: "",
        text20: "",
    };
    var g_item1 = {
        text1: "1",
        text2: "",
        text3: "",
        text4: "",
        text5: "",
        text6: "",
        text7: "",
        text8: "",
        text9: "",
        text10: "",
        text11: "",
        text12: "2",
        text13: "",
        text14: "",
        text15: "4",
        text16: "6",
        text17: "",
        text18: "",
        text19: "待处理",
        text20: ""
    };
    for (var i = 0; i < 20; i++) {
        if (i === 0) {
            grid_dataY.push(g_item1);
        } else {
            grid_dataY.push(g_item);
        }
    }
    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d + ' '+ h+':'+minute+':'+second;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }


    //账页   tab 切换
    $('.leder_tabs>li').on('click', function () {
        var $this = $(this), $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        if ($this.index() != 2) {
            sessionStorage.setItem('leder_ind', '-1');
        }
        $('#table_a').XGrid('clearGridData');
        initTable($this.index())
        getTotalNum ($this.index());
        drugClassType = $this.index();
        //$nav_content.children('div').eq($this.index()).show().siblings().hide();
        // $nav_content.children('div').eq($this.index()).css('display', 'flex').siblings().hide();
        // $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');
    });



    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    colName = ['id', '移动类型', '单据编号', '商品大类', '商品编码','原商品编码', '商品名称','通用名', '行号','生产厂家','规格', '所属经营范围', '单据时间', '过账时间', '往来单位编号', '往来单位名称', '部门名称',
        '职员名称', '库房名称','业务类型', '批号', '生产日期', '有效期至', '账页日期' ,'灭菌批号', '入库数量', '不含税入库单价', '不含税入库金额', '出库数量', '不含税出库单价', '不含税出库金额', '批号结存数量',
        '批号结存金额', '总结存数量', '总结存金额', '总结存单价','税率','含税成本单价','含税成本金额','组织机构', '操作员','备注'
    ];

    //, '不含税收入金额 '
    colModel = [{
        name: 'id',
        index: 'id',
        key: true,
        hidden: true,
        hidegrid: true
    }, {
        name: 'moveTypeName',
        index: 'moveTypeName'
    }, {
        name: 'orderCode',
        index: 'orderCode',
        width: 200
    }, {
        name: 'drugClassName',
        index: 'drugClassName'
    }, {
        name: 'drugCode',
        index: 'drugCode'
    }, 	{
        name: 'oldProductCode',
        index: 'oldProductCode'
    },
        {
            name: 'drugName',
            index: 'drugName',
            width: 220
        }, {
            name: 'commonName',
            index: 'commonName'
        },{
            name: 'seqNum',
            index: 'seqNum'
        },{
            name: 'manufName',
            index: 'manufName'
        },{
            name: 'specifications',
            index: 'specifications'
        },{
            name: 'scopeOfOperation',
            index: 'scopeOfOperation'
        },

        {
            name: 'orderDate',
            index: 'orderDate'
        }, {
            name: 'postDate',
            index: 'postDate'
        }, {
            name: 'intercourseNum',
            index: 'intercourseNum'
        }, {
            name: 'companyName',
            index: 'companyName',
            width: 220
        }, {
            name: 'departmentName',
            index: 'departmentName'
        }, {
            name: 'employeeName',
            index: 'employeeName'
        }, {
            name: 'storageTypeName',
            index: 'storageTypeName'
            ,formatter:function (e) {
                if(!re.test(e)){
                    return e;
                }
                var result = "";
                $.each(jsonStore,function(idx,item){
                    if (drugClassType){
                        if (drugClassType == '139'){
                            result = "二精专用库";
                        }else if (drugClassType == '140'){
                            result = "蛋肽专用库";
                        }
                    }else if(item.numCode == e){
                        result = item.name;
                        return false;
                    }


                });
                return result;
            }
        }, {
            name: 'channelId',
            index: 'channelId'
        },{
            name: 'batchNum',
            index: 'batchNum'
        }, {
            name: 'productDate',
            index: 'productDate'
        }, {
            name: 'validateDate',
            index: 'validateDate'
        }, {
            name: 'createTime',
            index: 'createTime',
            formatter: dateFormatter

        }, {
            name: 'sterilizeBatchNum',
            index: 'sterilizeBatchNum'
        }, {
            name: 'amountIn',
            index: 'amountIn',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'priceIn',
            index: 'priceIn',
            formatter: function (e, a, b, c) {
                if (b.moveType == "105") {
                    return "--";
                }
                return Number(e).toFixed(2);
            }
        }, {
            name: 'notaxSumIn',
            index: 'notaxSumIn',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'amountOut',
            index: 'amountOut',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'priceOut',
            index: 'priceOut',
            formatter: function (e, a, b, c) {
            	if (b.moveType == "107"||b.moveType == "111") {
                    return "--";
                }
                return Number(e).toFixed(2);
            }
        }, {
            name: 'notaxSumOut',
            index: 'notaxSumOut',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'batchInventoryAmount',
            index: 'batchInventoryAmount',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'batchInventorySum',
            index: 'batchInventorySum',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'inventoryAmount',
            index: 'inventoryAmount',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'inventorySum',
            index: 'inventorySum',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'inventoryPrice',
            index: 'inventoryPrice',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }},
        //  {
        //     name: 'notaxSumInome',
        //     index: 'notaxSumInome',
        //     hidden: true,
        //     formatter: function (e, a, b, c) {
        //         if (b.moveType != "201" && b.moveType != "203") {
        //             return "--";
        //         }
        //         return Number(e).toFixed(2);
        //     }
        // },
        {
            name: 'taxRate',
            index: 'taxRate',
        },{
            name:'taxCostPrice',
            index:'taxCostPrice'
        },{
            name: 'taxCostAmount',
            index: 'taxCostAmount',
            formatter: function (e, a, b, c) {
                if (e =="" && e !== 0) {
                    return "--";
                }
                var amount = Math.abs(Number(e));
                return amount.toFixed(2);
            }
        },{
            name: 'organization',
            index: 'organization'
        }, {
            name: 'operator',
            index: 'operator',
            formatter: function (e) {
                if (e=='1'){
                    return '';
                }else {
                    return e;
                }
            }
        },{
            name: 'remarks',
            index: 'remarks'
        }];
    $('#table_a').XGrid({
        url: "/proxy-storage/storage/ledger/listStorageLedger?startDate=" + $('#begint').val() + "&endDate=" + $('#endt').val(),
        colNames: colName,
        colModel: colModel,
        // rownumbers: true,//是否展示序号
        selectandorder: true,
        key: 'id',
        // rowNum: 20,
        // rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function (res) {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            // var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut','inventoryAmount','inventorySum'];
            var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
            $('#totalTableNum').text($('#totalPageNum').text()+'条')
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            //console.log(id, dom, obj, index, event)
        }
    });


    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        $('#table_a').XGrid('filterTableHead');
    })

    /* 商品名称 搜索提示（只显示5条） */
    var ts1 = [{
        value: 'Andorra',
        data: 'AD'
    },
        {
            value: 'Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '2Andorra',
            data: 'AD'
        },
        {
            value: '2Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '3Andorra',
            data: 'AD'
        },
        {
            value: '3Zimbabwe',
            data: 'ZZ'
        }
    ];
    $('#input_goodName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + $("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#input_goodName").val("");
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });


    //获取当前tab  的下标
    function getTabInd() {
        for (var i = 0; i < $('.leder_tabs li').length; i++) {
            if ($('.leder_tabs li').eq(i).hasClass('active')) {
                _ind = $('.leder_tabs li').eq(i).index();
            }
        }
        return _ind;
    }


    /* 查询 */
    $('#searchBtn').on('click', function () {
        var tabInd = getTabInd();
        initTable(tabInd)
        getTotalNum (tabInd);
    });
    function initTable(index){
        var  url = "/proxy-storage/storage/ledger/listStorageLedger";
        switch (index) {
            case 0:
                $("#scopeOfOperation").val("")
                break;
            case 1:
                $("#scopeOfOperation").val("DELJSYP")
                break;
            case 2:
                $("#scopeOfOperation").val("DBTHZJTLJS")
                break;
        }
        //获取form数据
        var data = $('#form_a').serializeToJSON();


        console.log(data);
        //更新表格数据
        var $table_id = $('#nav_content .active .XGridBody table').attr('id');
        if ($table_id == 'table_a') {
            //列表
            $('#table_a').XGrid('setGridParam', {
                url: url,
                postData: {
                    drugCode: $('#drugCode').val(),
                    batchNum: $('#batchNum').val(),
                    startDate: $('#begint').val(),
                    endDate: $('#endt').val(),
                    moveType: $("#moveType").find("option:selected").val(),
                    drugClass:$("#drugClass").find("option:selected").val(),
                    orderCode: $("#orderCode").val(),
                    storageType: $("#storageType").val(),
                    oldProductCode:$("#oldProductCode").val(),
                    manufName: $("#manufName").val(),
                    productCode: $("#productCode").val(),
                    companyName: $("#companyName").val(),
                    scopeOfOperation:$("#scopeOfOperation").val(),
                    channelCode:$("#channelCode").val()
                }
            }).trigger("reloadGrid");
        }
    }

    /* 详情查询 */
    $('#searchBtnDetail').on('click', function () {
        var tabInd = getTabInd();
        initTableDetail(tabInd)
        getTotalNum (tabInd);
    });
    function initTableDetail(index){
        var  url = "/proxy-storage/storage/ledger/listStorageLedgerDetail";
        var drugClass = "";
        switch (index) {
            case 0:
                $("#drugClass").attr("disabled",false)
                $("#storageType").attr("disabled",false)
                drugClass = $("#drugClass").find("option:selected").val();
                break;
            case 1:
                $("#drugClass").val("");
                $("#drugClass").attr("disabled",true)
                $("#storageType").val("");
                $("#storageType").attr("disabled",true)
                drugClass = "139"
                break;
            case 2:
                $("#drugClass").val("");
                $("#drugClass").attr("disabled",true)
                $("#storageType").val("");
                $("#storageType").attr("disabled",true)
                drugClass = "140"
                break;
        }
        //获取form数据
        var data = $('#form_a').serializeToJSON();


        console.log(data);
        //更新表格数据
        var $table_id = $('#nav_content .active .XGridBody table').attr('id');
        if ($table_id == 'table_a') {
            //列表
            $('#table_a').XGrid('setGridParam', {
                url: url,
                postData: {
                    drugCode: $('#drugCode').val(),
                    batchNum: $('#batchNum').val(),
                    startDate: $('#begint').val(),
                    endDate: $('#endt').val(),
                    moveType: $("#moveType").find("option:selected").val(),
                    drugClass:drugClass,
                    orderCode: $("#orderCode").val(),
                    storageType: $("#storageType").val(),
                    oldProductCode:$("#oldProductCode").val(),
                    manufName: $("#manufName").val(),
                    productCode: $("#productCode").val(),
                    companyName: $("#companyName").val(),
                    channelCode: $("#channelCode").val()
                }
            }).trigger("reloadGrid");
        }
    }

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#table_a').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                /*  if (!data.length) {
                      data = [data];
                  }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        var value=item[val];
                        if(('priceIn'==val||'priceOut'==val||'taxCostAmount'==val)&&'--'==value){
                            value='0.00'
                        }
                        new_item[val] = value;
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            console.log(colName);

            //获取form数据
            var formData = {
                drugCode: $('#drugCode').val(),
                batchNum: $('#batchNum').val(),
                startDate: $('#begint').val(),
                endDate: $('#endt').val(),
                moveType: $("#moveType").find("option:selected").val(),
                drugClass: $("#drugClass").find("option:selected").val(),
                orderCode: $("#orderCode").val(),
                colName: colName,
                colNameDesc: colNameDesc,
                selectData: data,
                storageType: $("#storageType").val(),
                oldProductCode:$('#oldProductCode').val(),
                manufName: $("#manufName").val(),
                productCode: $("#productCode").val(),
                companyName: $("#companyName").val(),
                scopeOfOperation:$("#scopeOfOperation").val(),
                channelCode: $("#channelCode").val()
            }
            var colNames= colName.join(",")
            var colNameDescs= colNameDesc.join(",")
            console.log(colNames);
            var formData2 = {
                moduleName: 'storageLedger',
                taskCode: '1004',
                colName: colNames,
                colNameDesc: colNameDescs,
                fileName: '商品账页',
                exportParams: {
                    drugCode: $('#drugCode').val(),
                    batchNum: $('#batchNum').val(),
                    startDate: $('#begint').val(),
                    endDate: $('#endt').val(),
                    moveType: $("#moveType").find("option:selected").val(),
                    drugClass: $("#drugClass").find("option:selected").val(),
                    orderCode: $("#orderCode").val(),
                    storageType: $("#storageType").val(),
                    oldProductCode:$('#oldProductCode').val(),
                    manufName: $("#manufName").val(),
                    productCode: $("#productCode").val(),
                    companyName: $("#companyName").val(),
                    scopeOfOperation:$("#scopeOfOperation").val(),
                    channelCode: $("#channelCode").val()
                }
            };
            if(typeof data == "undefined" || data == null || data == "") {
                utils.dialog({
                    title: '温馨提示',
                    content: '导出任务提交成功后页面将关闭，是否确认导出？',
                    okValue: '确定',
                    ok: function () {
                        $.ajax({
                            url: "/proxy-storage/storage/commonExport/commonCommitExportTask",
                            type: 'post',
                            dataType: 'json',
                            data: {
                                "data":JSON.stringify(formData2)
                            },
                            success: function (res) {
                                if (res) {
                                    if (res.code === 0) {
                                        utils.dialog({
                                            title: '温馨提示',
                                            content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                            okValue: '确定',
                                            ok: function () {

                                            }
                                        }).showModal()
                                    } else {
                                        utils.dialog({
                                            title: '温馨提示',
                                            content: data.msg,
                                            okValue: '确定',
                                            ok: function () {

                                            }
                                        }).showModal()
                                    }
                                }
                            }
                        });
                    }
                }).showModal()
            }else{
                httpPost("/proxy-storage/storage/ledger/exportListStorageLedger", formData);
            }
        });
    });

    /* 导出 */
    $('#exportRowDataDetail').on('click', function () {
        var tableId = $('#table_a').attr('id');
        var index = getTabInd();
        var drugClass = "";
        switch (index) {
            case 0:
                drugClass = $("#drugClass").find("option:selected").val();
                break;
            case 1:
                drugClass = "139"
                break;
            case 2:
                drugClass = "140"
                break;
        }
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                /*  if (!data.length) {
                      data = [data];
                  }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        var value=item[val];
                        if(('priceIn'==val||'priceOut'==val||'taxCostAmount'==val)&&'--'==value){
                            value='0.00'
                        }
                        new_item[val] = value;
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            console.log(colName);

            //获取form数据
            var formData = {
                drugCode: $('#drugCode').val(),
                batchNum: $('#batchNum').val(),
                startDate: $('#begint').val(),
                endDate: $('#endt').val(),
                moveType: $("#moveType").find("option:selected").val(),
                drugClass: drugClass,
                orderCode: $("#orderCode").val(),
                colName: colName,
                colNameDesc: colNameDesc,
                selectData: data,
                storageType: $("#storageType").val(),
                oldProductCode:$('#oldProductCode').val(),
                manufName: $("#manufName").val(),
                productCode: $("#productCode").val(),
                companyName: $("#companyName").val(),
                channelCode: $("#channelCode").val()
            }
            httpPost("/proxy-storage/storage/ledger/exportListStorageLedgerDetail", formData);
        });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#drugCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })

    //业务类型搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })


    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('1').then( res => {
            // console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelCode;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelCode').val(_str_code)
        })
    });
})
//查询总合计数目
function getTotalNum (index) {
    //加载总数量
    $.ajax({
        url: '/proxy-storage/storage/ledger/selectStorageLedgerViewStatisticsTotal',
        dataType: 'json',
        timeout: 10000, //6000
        data:{
            drugCode: $('#drugCode').val(),
            batchNum: $('#batchNum').val(),
            startDate: $('#begint').val(),
            endDate: $('#endt').val(),
            moveType: $("#moveType").find("option:selected").val(),
            drugClass: $("#drugClass").find("option:selected").val(),
            orderCode: $("#orderCode").val(),
            storageType: $("#storageType").val(),
            oldProductCode:$("#oldProductCode").val(),
            manufName: $("#manufName").val(),
            productCode: $("#productCode").val(),
            companyName: $("#companyName").val(),
            scopeOfOperation:$("#scopeOfOperation").val(),
            channelCode: $("#channelCode").val()
        },
        success: function (data) {
            // alert(data.code);
            if (data.code==0){
                var static = data.result;
                $("#totalAmountIn").text(Number(static.totalAmountIn).toFixed(2));
                $("#totalNotaxSumIn").text(Number(static.totalNotaxSumIn).toFixed(2));
                $("#totalAmountOut").text(Number(static.totalAmountOut).toFixed(2));
                $("#totalNotaxSumOut").text(Number(static.totalNotaxSumOut).toFixed(2));
                // $("#totalInventoryAmount").text(Number(static.totalInventoryAmount).toFixed(2));
                // $("#totalInventorySum").text(Number(static.totalInventorySum).toFixed(2));
            }else {
                $("#totalAmountIn").text('0.00');
                $("#totalNotaxSumIn").text("0.00");
                $("#totalAmountOut").text("0.00");
                $("#totalNotaxSumOut").text("0.00");
                $("#totalTableNum").text('0条');
                // $("#totalInventoryAmount").text("0.00");
                // $("#totalInventorySum").text("0.00");
            }
        },
        error: function () {
            $("#totalAmountIn").text('0.00');
            $("#totalNotaxSumIn").text("0.00");
            $("#totalAmountOut").text("0.00");
            $("#totalNotaxSumOut").text("0.00");
            $("#totalTableNum").text($('#totalPageNum').text() + '条');
            // $("#totalInventoryAmount").text("0.00");
            // $("#totalInventorySum").text("0.00");
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

function  btn_output_list(){
    utils.dialog({
        title: '导出列表',
        url: '/proxy-storage/storage/commonExport/toExportList?moduleName=storageLedger&taskCode=1004',
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
}

//加载更多
function btn_getMore() {
    parent.showLoading()
    let rowdata = $('#table_a').XGrid('getRowData'),
        lastId = rowdata.length ? rowdata[rowdata.length-1].id: null;
    if(lastId){
        $.ajax({
            type: 'get',
            url: '/proxy-storage/storage/ledger/listStorageLedger?startDate='
                + $('#begint').val()
                + "&endDate=" + $('#endt').val()
                + "&batchNum=" + $('#batchNum').val()
                + "&moveType=" + $("#moveType").find("option:selected").val()
                + "&drugClass=" + $("#drugClass").find("option:selected").val()
                + "&orderCode=" + $("#orderCode").val()
                + "&storageType=" + $("#storageType").val()
                + "&oldProductCode=" + $("#oldProductCode").val()
                + "&manufName=" + $("#manufName").val()
                + "&productCode=" + $("#productCode").val()
                + "&companyName=" + $("#companyName").val()
                + "&drugCode=" + $("#drugCode").val()
                + "&scopeOfOperation=" + $("#scopeOfOperation").val()
                + "&channelCode=" + $("#channelCode").val()
                + "&pageNum=" +  $('#pg_grid_pager_a').find('input[type=number]').val() + 1
                + "&pageSize=20"
                +'&lastId=' + lastId,
            success: function (res) {
                if(res.result.list.length){
                    let newArr =[];
                    newArr = rowdata.concat(res.result.list);
                    $('#table_a').XGrid('clearGridData');
                    var heji_tr = $('#table_a tr[nosele=true]').clone();
                    $('#table_a tr[nosele=true]').remove()
                    for(let i = 0; i<newArr.length; i++){
                        $('#table_a').XGrid('addRowData',newArr[i])
                    }
                    var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
                    heji_tr.find("td:first-child").text('合计');
                    sum_models.forEach(function (item,index) {
                        heji_tr.find("td[row-describedby="+item+"]").text(totalTable(newArr,item))
                    });
                    $("#totalTableNum").text(Number(res.result.total)+'条');
                    $('#table_a tbody').append(heji_tr)

                }else {
                    utils.dialog({
                        title: '提示',
                        content: '没有更多数据了！',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                }
            },
            error: function (err) {
                console.log(err)
            },
            complete:function () {
                parent.hideLoading();
            }

        })
    }else{
        utils.dialog({
            title: '提示',
            content: '无查询结果！',
            okValue: '确定',
            ok: function () {}
        }).showModal();
    }
}
