    var urlObjectList = [];
    var dueDayMax = $('#dueDayMax').val();
    if(dueDayMax){
        dueDayMax = "?dueDayMax="+dueDayMax;
    }
    //创建列表
    $('#baseTable').XGrid({
        url:'/proxy-customer/customer/customerBaseAppl/customerEnclourePageList',
        colNames: ['', '客户类型', '客户编码', '客户名称', '营业执照号', '联系人', '证书名称', '证书编码', '发证机关', '发证日期','有效期至','剩余效期'],
        postData: $('#searchForm').serializeToJSON(),page:1,
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            hidden: true
        }, {
            name: 'customerType',
            index: 'customerType'
        },{
            name: 'customerCode',
            index: 'customerCode'
        }, {
            name: 'customerName',
            index: 'customerName'
        }, {
            name: 'businessLicenseNum',
            index: 'businessLicenseNum'
        }, {
            name: 'salesman',
            index: 'salesman'
        },{
            name: 'credentialTypeId',
            index: 'credentialTypeId'
        }, {
            name: 'credentialCode',
            index: 'credentialCode'
        }, {
            name: 'certificationOffice',
            index: 'certificationOffice'
        }, {
            name: 'openingDate',
            index: 'openingDate',
            formatter:function(value){
                var date=value;
                if(typeof(date) == "undefined" || !value)return '';
                date=format(value);
                return date.split(' ')[0];
            }
        },{
            name: 'validUntil',
            index: 'validUntil',
            formatter:function(value){
                var date=value;
                if(typeof(date) == "undefined" || !value)return '';
                date=format(value);
                return date.split(' ')[0];
            }
        },{
            name: 'dueDay',
            index: 'dueDay'
        }],
        rowNum: 20,
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        rowList: [20,50,100],//分页条数下拉选择
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件', id, dom, obj, index, event);
            utils.openTabs("customerBaseDetail", "机构客户详情", "/proxy-customer/customer/customerBaseAppl/detail?id="+id);
            // location.href='/proxy-customer/customer/customerBaseAppl/detail?id='+id
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //console.log(id, dom, obj, index, event)
            setUrlObjectList(dom,id,obj);
        },
        pager: '#grid-pager'
    });
    $("#searchBtn").on("click", function () {
        urlObjectList=[];
        $('#baseTable').XGrid('setGridParam', {
            postData: $('#searchForm').serializeToJSON(),page:1
        }).trigger('reloadGrid');
    });
    $(document).on('change','#grid_checked input',function () {
        var $tr=$(this).parents('tr');
        var rowData=$("#baseTable").getRowData($tr.attr('id'));
        var id=$tr.attr('id');
        setUrlObjectList($tr,id,rowData);
        //var checked=this.checked;
        // urlObjectList=[];
        // if(checked){
        //     var selRow=$('#baseTable').XGrid('getSeleRow');
        //     if(selRow && selRow.length > 0){
        //
        //         for(var i=0;i<selRow.length;i++){
        //             if(selRow[i].snapshootUrl != ''){
        //                 var fileParam={};
        //                 fileParam.id = selRow[i].id;
        //                 fileParam.name = selRow[i].customerCode;
        //                 fileParam.url = selRow[i].snapshootUrl;
        //                 urlObjectList.push(fileParam);
        //             }
        //         }
        //     }
        // }else{
        //     urlObjectList=[];
        // }
    })
    $(document).on("change",".exportItem dt input",function () {
        var checked=this.checked;
        if(checked)
        {
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',false);
        }
    });
    $(document).on("change",".exportItem dd input[type='checkbox']",function () {
        var inpLen=$(this).parents("dd").find("input[type='checkbox']").length;
        var checkLen=$(this).parents("dd").find("input[type='checkbox']:checked").length;
        if(inpLen == checkLen)
        {
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',false);
        }
    });
    //首营审批快照
    $('#snapshootBtn').on('click', function () {
        var len=$("#baseTable").XGrid('getSeleRow');
        if(!len || len.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'请先从列表中选择一条数据',
                okValue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        utils.dialog({
            align: 'top',
            width: 90,
            height: 50,
            padding: 8,
            content:'<div class="changeApplyItem formApprovalBlock"><div class="cSelect">预览</div><div class="cDown">下载</div></div>',
            quickClose: true
        }).show(this);
    });
    // $("#baseTable").on('change','td[row-describedby="ck"] input',function(){
    //     var $tr=$(this).parents('tr');
    //     var rowData=$("#baseTable").getRowData($tr.attr('id'));
    //     var id=$tr.attr('id');
    //     setUrlObjectList($tr,id,rowData);
    // })

    $('body').on('click', '.cSelect', function () {
        if(urlObjectList.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'没有可预览附件',
                okVlue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        $.viewImg({
            fileParam:{
                name:'name',
                url:'url'
            },
            list:urlObjectList
        })
    })

    $('body').on('click', '.cDown', function () {
        if(urlObjectList.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'没有可下载的附件',
                okVlue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        //批量下载
        var a=[];
        a.push('<form style="display: none" method="post">');
        urlObjectList.forEach(function(item) {
            console.log(item);
            a.push('<input name="file" value = ' + item.url + '>');
            a.push('<input name="name" value = '+ item.name + '>');
        });
        a.push('</form>');

        var $eleForm = $(a.join(''));
        $eleForm.attr("action", "/proxy-sysmanage/upload/downloadZip");
        //$eleForm.attr("action", "http://**************:8080/proxy-customer/customer/upload/download?filepath=G2/M00/00/01/Cgo001tPH3OAXX8_AABEWCsL8kc354.png&name=''");
        $(document.body).append($eleForm);
        //提交表单，实现下载
        $eleForm.submit();
    })
    function setUrlObjectList($tr,id,rowData){
        var a=rowData;
        var fileParam = {};
        if($tr.hasClass('selRow') && a.snapshootUrl){
            fileParam.id = a.id;
            fileParam.name = a.customerCode;
            fileParam.url = a.snapshootUrl;
            urlObjectList.push(fileParam);
        }else if(!$tr.hasClass('selRow')){
            $(urlObjectList).map(function (i,v) {
                if(v.id==a.id){
                    urlObjectList.splice(i,1);
                }
            })
        }
    }

    function format(shijianchuo)
    {
//shijianchuo是整数，否则要parseInt转换
        var time = new Date(shijianchuo);
        var y = time.getFullYear();
        var m = time.getMonth()+1;
        var d = time.getDate();
        var h = time.getHours();
        var mm = time.getMinutes();
        var s = time.getSeconds();
        return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
    }

    function add0(m){return m<10?'0'+m:m }

$("#exportBtn").on("click", function () {
    //wgf判断条数wgf20200225
    utils.exportAstrictHandle('baseTable', Number($('#totalPageNum').text()),1).then( () => {
        return false;
    }).catch( () => {
        const params = $("#searchForm").serializeToJSON();
        var formData2 = {
            moduleName: 'customer',
            taskCode: '1064',
            colName: '',
            colNameDesc: '',
            fileName: '客户资质',
            exportParams:params
        };
        utils.dialog({
            title: '温馨提示',
            content: '导出任务提交成功后页面将关闭，是否确认导出？',
            okValue: '确定',
            ok: function () {
                $.ajax({
                    url: "/proxy-customer/customer/customerBaseAppl/commonCommitExportTask",
                    type: 'post',
                    dataType: 'json',
                    data: {
                        "data":JSON.stringify(formData2)
                    },
                    success: function (res) {
                        if (res) {
                            if (res.code === 0) {
                                utils.dialog({
                                    title: '温馨提示',
                                    content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                    okValue: '确定',
                                    ok: function () {

                                    }
                                }).showModal()
                            } else {
                                utils.dialog({
                                    title: '温馨提示',
                                    content: data.msg,
                                    okValue: '确定',
                                    ok: function () {

                                    }
                                }).showModal()
                            }
                        }
                    }
                });
            }
        }).showModal()
        // utils.dialog({
        //     title: '提示',
        //     content:"数据量大的时候耗时较长，请耐心等待。",
        //     okValue: '确定',
        //     ok: function () {
        //         //parent.showLoading()
        //         var body = document.body;
        //         var form = $(body).find('form#searchForm');
        //         $(form).attr("action","/proxy-customer/customer/customerBaseAppl/exportCustomerEncloureExcel");
        //         $(form).submit();
        //         // setTimeout(function () {
        //         //     parent.hideLoading()
        //         // },2000)
        //     },
        //     cancelValue: '取消',
        //     cancel: function () { },
        // }).showModal();

    });

});
function  btn_output_list(){
    utils.dialog({
        title: '导出列表',
        url: '/proxy-customer/customer/customerBaseAppl/toExportList?moduleName=customer&taskCode=1064',
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
}
