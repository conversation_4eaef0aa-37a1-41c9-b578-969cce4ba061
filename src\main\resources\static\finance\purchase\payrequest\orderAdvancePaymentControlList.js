$(function () {
    var totalTable = z_utils.totalTable;
    var colNames = ['供应商编号', '供应商名称',  '采购订单号', '订单创建日期', '订单付款日期', '订单金额', '预付金额', '占用金额','核销金额','释放金额','入库金额', '退货金额', '已开票金额', '核销/释放时间', '关闭后退货释放时间','占用时长（天）','订单状态'];
    var colModel = [
        {
            name: 'supplierNo',
            index: 'supplierNo',
            width: 150
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 150
        }, {
            name: 'purchaseOrderNo',
            index: 'purchaseOrderNo',
            width: 150
        }, {
            name: 'orderCreateTimeStr',
            index: 'orderCreateTimeStr',
            width: 150
        }, {
            name: 'orderPaymentTimeStr',
            index: 'orderPaymentTimeStr',
            width: 150
        }, {
            name: 'orderAmount',
            index: 'orderAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        },{
            name: 'prepaymentAmount',
            index: 'prepaymentAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 100
        }, {
            name: 'occupyAmount',
            index: 'occupyAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 100
        }, {
            name: 'writeoffAmount',
            index: 'writeoffAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 100
        }, {
            name: 'releaseAmount',
            index: 'releaseAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 100
        },{
            name: 'warehousingAmount',
            index: 'warehousingAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150

        }, {
            name: 'returnAmount',
            index: 'returnAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        },{
            name:'invoicedAmount',
            index:'invoicedAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        },{
            name:'orderReleaseTimeStr',
            index:'orderReleaseTimeStr',
            width: 150
        },{
            name:'orderCloseReleaseTimeStr',
            index:'orderCloseReleaseTimeStr',
            width: 200
        }
        ,{
            name:'occupyDuration',
            index:'occupyDuration',
        }
        ,{
            name:'purchaseOrderStatusStr',
            index:'purchaseOrderStatusStr',
            width: 150
        }

    ];

    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/orderAdvancePaymentControl/getOrderAdvancePaymentControlList',
        // url: 'http://localhost:8080/account/find',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        attachRow:true,
        postData: {
            keyword: $("#keyword").val(),
            purchaseOrderNo:$("#purchaseOrderNo").val(),
            starttime:$("#starttime").val(),
            endtime:$("#endtime").val(),
            occupyVal:occupyVal()
        },
        ondblClickRow: function (id, dom, obj, index, event) {
            //this.returnValue = obj;
            //  window.location.href = "/proxy-finance/finance/purchase/payrequestinfo/detailPayrequestPage?billNo="+obj.billNo+"&status=1";
            //showDetail(obj.billNo);
            //return obj;
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            var _this = $(this);
            setTimeout(function () {
                if(_this.XGrid('getRowData').length === 0){
                    utils.dialog({content:'查询无数据',quickClose:true,timeout:2000}).show();
                }
            },200);
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            var sumOrderAmount = ['orderAmount'];
            sumOrderAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sumprepaymentAmount = ['prepaymentAmount'];
            sumprepaymentAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sumoccupyAmount = ['occupyAmount'];
            sumoccupyAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sumwriteoffAmount = ['writeoffAmount'];
            sumwriteoffAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sumreleaseAmount = ['releaseAmount'];
            sumreleaseAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sumwarehousingAmount = ['warehousingAmount'];
            sumwarehousingAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sumReturnAmount = ['returnAmount'];
            sumReturnAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var suminvoicedAmount = ['invoicedAmount'];
            suminvoicedAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });

        },
        pager: '#grid-pager'
    });



    $("#searchBtn").on("click", function () {
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/orderAdvancePaymentControl/getOrderAdvancePaymentControlList',
            postData: {
                keyword: $("#keyword").val(),
                purchaseOrderNo:$("#purchaseOrderNo").val(),
                starttime:$("#starttime").val(),
                endtime:$("#endtime").val(),
                occupyVal:occupyVal()
            }
        }).trigger('reloadGrid');
        totalPayrequestSum();
    });

    function occupyVal() {
        if ($("#occupyValcheckbox").prop('checked')) {
            return  "1";
        } else {
            return  "0";
        }
    }

    totalPayrequestSum();
    var allColModelA = JSON.parse(JSON.stringify(colModel));
    //导出
    $('#exportBtn').on('click', function () {

        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            addHtmlA(colNames);
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    var obj ={};

                    //obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    obj["nameModel"] = nameModel;
                    httpPost("/proxy-finance/finance/purchase/orderAdvancePaymentControl/exportOrderAdvancePaymentControlList", obj);
                },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })


    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none;     padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }

        var input1 = document.createElement("input");
        input1.name = "occupyVal";
        input1.value = occupyVal();
        temp.appendChild(input1);

        var input2 = document.createElement("input");
        input2.name = "keyword";
        input2.value = $("#keyword").val();
        temp.appendChild(input2);

        var input3 = document.createElement("input");
        input3.name = "purchaseOrderNo";
        input3.value = $("#purchaseOrderNo").val();
        temp.appendChild(input3);

        var input4 = document.createElement("input");
        input4.name = "starttime";
        input4.value = $("#starttime").val();
        temp.appendChild(input4);
        var input5 = document.createElement("input");
        input5.name = "endtime";
        input5.value = $("#endtime").val();
        temp.appendChild(input5);
        document.body.appendChild(temp);

        temp.submit();

        return temp;
    }

    //关键字模糊查询
    $('#keyword1').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result)
            $("#keyword1").val(result.value).attr('oldvalue',result.value);
            $("#keyword").val(result.data);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {

            $("#keyword").val("");
            $("#keyword1").val("");

        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });


    //放大镜查询
    $('#keyword1').on({
        dblclick: function (e) {
            supplierdDalog($("#keyword1").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#keyword1").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#keyword1").attr('oldvalue'))
    });
    //供应商双击查询
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '',  // 给modal 要传递的 的数据
            onclose: function () {
                $('#keyword1').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#keyword1").val(data.supplierName);
                    $("#keyword").val(data.supplierCode);
                }else{
                	$("#keyword").val('');
                    $("#keyword1").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }



    function totalPayrequestSum() {
        $.ajax({
            url:'/proxy-finance/finance/purchase/orderAdvancePaymentControl/getOrderAdvancePaymentControlTotalMoney',
            type:'post',
            data:{
                keyword: $("#keyword").val(),
                purchaseOrderNo:$("#purchaseOrderNo").val(),
                starttime:$("#starttime").val(),
                endtime:$("#endtime").val(),
                occupyVal:occupyVal()
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;
                    if (null == data) {
                        $("#sumprepaymentAmount").text(0);
                        $("#sumoccupyAmount").text(0);
                        $("#sumwriteoffAmount").text(0);
                        $("#sumreleaseAmount").text(0);
                        $("#sumwarehousingAmount").text(0);
                        $("#suminvoicedAmount").text(0);
                    } else {
                        if (data.sumPrepaymentAmount) {
                            $("#sumprepaymentAmount").text(parseFloat(data.sumPrepaymentAmount).formatMoney('2', '', ',' ,'.'));
                            // $("#sumApplayAmount").text(data.sumApplayAmount.toFixed(2));
                        } else {
                            $("#sumprepaymentAmount").text(0);
                        }
                        if (data.sumOccupyAmount) {
                            $("#sumoccupyAmount").text(parseFloat(data.sumOccupyAmount).formatMoney('2', '', ',' ,'.'));
                        } else {
                            $("#sumoccupyAmount").text(0);
                        }
                        if (data.sumWriteoffAmount) {
                            $("#sumwriteoffAmount").text(parseFloat(data.sumWriteoffAmount).formatMoney('2', '', ',' ,'.'));
                        } else {
                            $("#sumwriteoffAmount").text(0);
                        }
                        if (data.sumReleaseAmount) {
                            $("#sumreleaseAmount").text(parseFloat(data.sumReleaseAmount).formatMoney('2', '', ',' ,'.'));
                            // $("#sumApplayAmount").text(data.sumApplayAmount.toFixed(2));
                        } else {
                            $("#sumreleaseAmount").text(0);
                        }
                        if (data.sumWarehousingAmount) {
                            $("#sumwarehousingAmount").text(parseFloat(data.sumWarehousingAmount).formatMoney('2', '', ',' ,'.'));
                        } else {
                            $("#sumwarehousingAmount").text(0);
                        }
                        if (data.sumInvoicedAmount) {
                            $("#suminvoicedAmount").text(parseFloat(data.sumInvoicedAmount).formatMoney('2', '', ',' ,'.'));
                        } else {
                            $("#suminvoicedAmount").text(0);
                        }
                    }

                }
            }
        });
    }




})