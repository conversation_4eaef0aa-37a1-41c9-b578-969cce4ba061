var _dialog = null;
$(function () {
    var dialog = parent.dialog.get(window);
    _dialog = parent.dialog.get(window);
    $('#X_Tableb').XGrid({
        data: [],
        colNames: ['','', ' <i style="color:red;margin-right:4px;">*</i>供应商编码', '供应商名称',' <i style="color:red;margin-right:4px;">*</i>商品编码','商品名称','通用名', '规格/型号', '生产厂家', '供应商经营范围', '商品经营范围'],
        colModel: [
            {
                name: 'id',
                index: 'id',
                hidden: true
            },
            {
                name: 'idval',
                index: 'idval',
                hidden: true
            },
            {
                name: 'supplierCode',
                index: 'supplierCode',
                width: 200,
                rowtype: '#supplierCode_inp'

            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 200
            }, {
                name: 'productCode',
                index: 'productCode',
                width: 150,
                rowtype: '#productCode_inp'
            }, {
                name: 'productName',
                index: 'productName',
                width: 250
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 250
            }, {
                name: 'specifications',
                index: 'specifications',
                width: 250
            }, {
                name: 'manufacturerName',
                index: 'manufacturerName',
                width: 250
            }, {
                name: 'scopeCodeList',// 供应商 经营范围
                index: 'scopeCodeList',
                width: 250,
                hidden: true
            }, {
                name: 'scopeOfOperation', // shangpin  经营范围
                index: 'scopeOfOperation',
                width: 250,
                hidden: true
            }
        ],
        // rowNum: 20,
        // rowList:[20,50,100],
        rownumbers: true,//是否展示序号
        // altRows: true,//设置为交替行表格,默认为false
        // pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
           // dialog.close(obj);
        }
    });
    //增行
    $("#addRowBtn").on("click", function () {
        addRow()
    });
    //删行
    $("#delRowBtn").on("click", function () {
        var selObj = $('#X_Tableb').XGrid("getSeleRow");
        if(selObj.length){
            $('#X_Tableb').XGrid("delRowData",selObj[0].id);
            utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
        }else{
            utils.dialog({"content":"请选择删除行！","timeout":2000}).showModal();
        }
    });
    //取消
    $("#cancleBtn").on("click", function () {
        _dialog.close();
    });
    //点击确定  验证保存的数据唯一性 商品编码+供应商编码  如果数据重复 则展示位红色
    $("#confirmBtn").on("click", function () {
        let selectRow =  $('#X_Tableb').XGrid('getRowData');
        let supplierGoodsCatalogueVOList = [];
        if (selectRow) {
            if (!Array.isArray(selectRow)) {
                selectRow = [selectRow]
            }
            let subdata = selectRow.filter(item => item.productCode == '');
            if (subdata.length != 0){
                utils.dialog({content: '完善信息！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        }else{
            utils.dialog({content: '请选择行！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        supplierGoodsCatalogueVOList = selectRow.map(item => {
            return {supplierCode: item.supplierCode,productCode:item.productCode}
        })
        console.log(supplierGoodsCatalogueVOList);
        if(supplierGoodsCatalogueVOList.length>50){
            utils.dialog({content: '最多只能保存50条数据！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if (supplierGoodsCatalogueVOList.length == 0) {
            utils.dialog({content: '请添加供货目录！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        $.ajax({
            url: '/proxy-supplier/supplier/supplierGoodsCatalogue/getBySupCodeListAndProCodeList',
            data:JSON.stringify(supplierGoodsCatalogueVOList),
            type:"post",
            dataType:'json',
            contentType: "application/json",
            success: function (data) {
               // alert("判断商品编码+供应商编码重复")
                if (data.result.length != 0){
                    let resCodeStrArr = data.result.map(item => item.supplierCode+item.productCode);
                    let allRowData = $('#X_Tableb').XGrid('getRowData'),
                        allRowDataStrArr = allRowData.map(item => item.supplierCode+item.productCode) ;
                    let indArr = [];
                    $(allRowDataStrArr).each((index,item) => {
                        if (resCodeStrArr.indexOf(item) > -1){
                            indArr.push(index)
                        }
                    })
                    for (let i = 0 ; i <indArr.length ; i++){
                        $('#X_Tableb tr').not(':first').eq(i).css('color','red')
                    }
                    if (indArr.length > 0){
                        utils.dialog({
                            title: '提示 ',
                            content: '红色标注的数据行，商品编码+供应商编码存在重复数据，是否继续？',
                            okValue: '继续',
                            ok: function () {
                                saveDataHandle()
                            },
                            cancelValue: '否',
                            cancel: function () {}
                        }).showModal()
                    }
                }else{
                    saveDataHandle()
                }
            },
            error: function (err) {
                console.log(err);
                utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
            }
        });
    });
    //点击确定保存数据   supplierCode productCode
    $("#saveBtn").on("click", function () {
        saveDataHandle();
    });
})
function addRow() {
    var rowNumber = $('#X_Tableb').find("tr").not(":first").length + 1;
    //$('#X_Tableb').addRowData({id: rowNumber})
    let rowData = {id:rowNumber}
    $('#X_Tableb').XGrid('addRowData', rowData);
}
function saveDataHandle() {
    let selectRow =  $('#X_Tableb').XGrid('getRowData');
    let supplierGoodsCatalogueVOList = [];
    if (selectRow) {
        if (!Array.isArray(selectRow)) {
            selectRow = [selectRow]
        }
    }else{
        utils.dialog({content: '请选择行！', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    supplierGoodsCatalogueVOList = selectRow.map(item => {
        return {supplierCode: item.supplierCode,supplierName: item.supplierName,productCode:item.productCode}
    })
    console.log(supplierGoodsCatalogueVOList);
    if(supplierGoodsCatalogueVOList.length>50){
        utils.dialog({content: '最多只能保存50条数据！', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    $.ajax({
        url: '/proxy-supplier/supplier/supplierGoodsCatalogue/insertSupplierGoodsCatalogue',
        data:JSON.stringify(supplierGoodsCatalogueVOList),
        type:"post",
        dataType:'json',
        contentType: "application/json",
        success: function (data) {
            if("0"==data.code){
                utils.dialog({content: "保存成功", quickClose: true, timeout: 2000}).showModal();
            }else{
                utils.dialog({content: data.msg, quickClose: true, timeout: 2000}).showModal();
            }
        },
        error: function (err) {
            console.log(err)
            utils.dialog({content: '请求失败！', quickClose: true, timeout: 2000}).showModal();
        },
        complete: function () {
            setTimeout(function () {
                _dialog.close();
            },3000)
        }
    });
}
function btn_searchSupplier(el) {
    searchSupplier(el)
}
// 搜索供应商
function searchSupplier(el){
    var supplierName = $(el).val();
    utils.dialog({
        url: '/proxy-supplier/supplier/supplierGoodsCatalogue/toSupplierCatalogueLis',//弹框页面请求地址
        title: '搜索供应商',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: supplierName, // 给modal 要传递的 的数据   后期也不打开
        onclose:function(){
            if(this.returnValue){
                var data = this.returnValue;
                $(el).parents('tr').find('[row-describedby="supplierCode"] input').val(data.supplierCode)
                $(el).parents('tr').find('[row-describedby="supplierName"]').text(data.supplierName)
                $(el).parents('tr').find('[row-describedby="scopeCodeList"]').text(JSON.stringify(data.scopeCodeList));
                //判断商品的经营范围(scopeOfOperation  需判断是否包含英文逗号 若包含 则切割，若不包含，则直接比较)是否在供应商的经营范围(List<String>  scopeCodeList)中
            }
        }
    }).showModal();
}
function btn_searchPdroduct(el) {
    let a = $(el).parents('tr').find('[row-describedby="supplierCode"] input').val()
    if(a){
        searchPdroduct(el)
    }else{
        utils.dialog({content: '请选择供应商', quickClose: true, timeout:2000}).showModal()
        return false;
    }
}
// 搜索商品
function searchPdroduct(el){
    var orgCode = $('#orgCode').val();
    let selData = {
        supplierCode: $(el).parents('tr').find('[row-describedby="supplierCode"] input').val(),
        supplierName: $(el).parents('tr').find('[row-describedby="supplierName"]').text(),
        scopeCodeList: $(el).parents('tr').find('[row-describedby="scopeCodeList"]').text(),
    }
    utils.dialog({
        url: '/proxy-supplier/supplier/supplierGoodsCatalogue/toProductCatalogueList?compareFlag=true',//弹框页面请求地址
        title: '搜索商品',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            orgCode: orgCode,
            compareFlag:true,//是否比较供应商的经营范围和商品的经营范围 false 不比较
            supportMultiple: $(el).parents('tr').index() == $('#X_Tableb').find("tr").not(":first").length ? true: false,
            scopeCodeList: $(el).parents('tr').find('[row-describedby="scopeCodeList"]').text()
        },
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                if (Array.isArray(data)) {
                    for (let i = 1; i<data.length; i++){
                        addRow()
                    }
                    $(el).parents('tr').find('[row-describedby="supplierCode"] input').val()
                    $(el).parents('tr').find('[row-describedby="supplierName"]').text()
                    var selObj = $('#X_Tableb').XGrid("getSeleRow");
                    getnewdata(data.length).then( arr => {
                        $(arr).each((index,item) => {
                            $('#'+item['id']).find('[row-describedby="productCode"] input').val(data[index].productCode);
                            $('#'+item['id']).find('[row-describedby="productName"]').text(data[index].productName);
                            $('#'+item['id']).find('[row-describedby="scopeOfOperation"]').text(data[index].scopeOfOperation);
                            $('#'+item['id']).find('[row-describedby="commonName"]').text(data[index].commonName);
                            $('#'+item['id']).find('[row-describedby="specifications"]').text(data[index].specifications);
                            $('#'+item['id']).find('[row-describedby="manufacturerName"]').text(data[index].manufacturerName);
                            $('#'+item['id']).find('[row-describedby="supplierCode"] input').val(selData.supplierCode);
                            $('#'+item['id']).find('[row-describedby="supplierName"]').text(selData.supplierName);
                            $('#'+item['id']).find('[row-describedby="scopeCodeList"]').text(selData.scopeCodeList);
                            $('#'+item['id']).find('[row-describedby="supplierCode"] .glyphicon-search').css('display','none');
                        })
                    }).catch(err => {
                        console.log(err)
                    })
                }else{
                    $(el).parents('tr').find('[row-describedby="productCode"] input').val(data.productCode);
                    $(el).parents('tr').find('[row-describedby="productName"]').text(data.productName);
                    $(el).parents('tr').find('[row-describedby="scopeOfOperation"]').text(data.scopeOfOperation);
                    $(el).parents('tr').find('[row-describedby="commonName"]').text(data.commonName);
                    $(el).parents('tr').find('[row-describedby="specifications"]').text(data.specifications);
                    $(el).parents('tr').find('[row-describedby="manufacturerName"]').text(data.manufacturerName);
                    $(el).parents('tr').find('[row-describedby="supplierCode"] .glyphicon-search').css('display','none');

                }
            }
        }
    }).showModal();
}

function getnewdata(len) {
    return new Promise( (resolve, reject) => {
        let newRowData = $('#X_Tableb').XGrid('getRowData');
        resolve(newRowData.splice(-len))
    })
}