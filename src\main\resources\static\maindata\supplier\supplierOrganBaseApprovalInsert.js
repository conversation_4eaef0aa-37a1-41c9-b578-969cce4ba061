var supplierBaseId = $("#supplierBaseId").val(); //基础属性id
var supplierOrganBaseId = $("#supplierOrganBaseId").val(); //运营属性id
var approvalId = $("#approvalId").val(); //申请id
var pageType = $("#pageType").val();
var processId = $("#processId").val();
var taskId = $("#taskId").val();
var approvalProcessId = $("#approvalProcessId").val();
var oldsettleMultipleDateList = {};
var settleMultipleDateList = {};
var settleMultipleDateListObj = {};

if (approvalId && approvalId !== "") {
  apiGetOldFirstOperate();
  apiGetFirstOperate(approvalId, "edit");
}

//获取仓库地址
function distpickerHTML(n, m) {
  var len = n ? n : 1;
  var html = "";
  let radomInit = [];
  for (let i = 0; i < len; i++) {
    let _int = parseInt(Math.random() * 100 + Math.random() * 1000);
    html += `<div class="col-md-6 depotList">
	        <div class="input-group">
	            <div class="input-group-addon require"><i class="text-require">*  </i>仓库地址</div>
	            <div class="form-control form-inline distpicker" id="storageBox_${_int}">
	                <div class="row">
	                    <div class="form-group col-md-2" id="stoProvinceSel_wrap_${_int}">
	                        <select class="form-control repertoryProvinceSelect" name="repertoryProvince_${_int}" id="repertoryProvince_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-2" id="stoCitySel_wrap_${_int}">
	                        <select class="form-control repertoryCitySelect" name="repertoryCity_${_int}" id="repertoryCity_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-2" id="stoDistrictSel_wrap_${_int}">
	                        <select class="form-control repertoryAreaSelect" name="repertoryArea_${_int}" id="repertoryArea_${_int}"></select>
	                    </div>
	                    <div class="form-group col-md-5" style="position: initial;">
	                        <input type="text" class="form-control repertoryDetailSelect text-inp Filter_SpaceAndFiveStrLen_Class" name="repertoryDetail"/>
	                    </div>
	                    <div class="form-group btn-box col-md-1">
	                        <button type="button" class="btn ${
                            i == 0 && !m ? "addDepot" : "removeDepot"
                          }">
	                            <span class="glyphicon ${
                                i == 0 && !m
                                  ? "glyphicon-plus"
                                  : "glyphicon-minus"
                              }" aria-hidden="true"></span>
	                        </button>
	                    </div>
	                </div>
	            </div>
	        </div>
	    </div>`;
    radomInit.push(_int);
  }
  return {
    html: html,
    radomInit: radomInit,
  };
}

// 仓库地址 回填
/*function returnHTML(n){
    var html = '';
    for(var i=0;i<n;i++){
        if(i==0){
            html += '<div class="depotList">\
	        <div class="input-group">\
	            <div class="input-group-addon require">仓库地址</div>\
	            <div data-toggle="distpicker" class="form-control form-inline distpicker">\
	                <div class="row">\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control {validate:{required:true}}" name="repertoryProvince"></select>\
	                    </div>\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control {validate:{required:true}}" name="repertoryCity"></select>\
	                    </div>\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control {validate:{required:true}}" name="repertoryArea"></select>\
	                    </div>\
	                    <div class="form-group col-md-4">\
	                        <input type="text"  class="form-control text-inp Filter_SpaceAndFiveStrLen_Class {validate:{required:true}}" name="repertoryDetail"/>\
	                    </div>\
	                    <div class="form-group btn-box col-md-1">\
                            <button type="button" class="btn" id="addDepot">\
                                <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>\
                            </button>\
                        </div>\
	                </div>\
	            </div>\
	        </div>\
	    </div>';
        }else{
            html += '<div class="col-md-6 depotList">\
	        <div class="input-group">\
	            <div class="input-group-addon require">仓库地址</div>\
	            <div data-toggle="distpicker" class="form-control form-inline distpicker">\
	                <div class="row">\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control {validate:{required:true}}" name="repertoryProvince"></select>\
	                    </div>\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control {validate:{required:true}}" name="repertoryCity"></select>\
	                    </div>\
	                    <div class="form-group col-md-2">\
	                        <select class="form-control {validate:{required:true}}" name="repertoryArea"></select>\
	                    </div>\
	                    <div class="form-group col-md-4">\
	                        <input type="text" class="form-control text-inp Filter_SpaceAndFiveStrLen_Class {validate:{required:true}}" name="repertoryDetail"/>\
	                    </div>\
	                    <div class="form-group btn-box col-md-1">\
	                        <button type="button" class="btn removeDepot">\
	                            <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>\
	                        </button>\
	                    </div>\
	                </div>\
	            </div>\
	        </div>\
	    </div>';
        }
    }
}*/
// // 仓库地址 回填
// function returnHTML(n){
//     var html = '';
//     for(var i=0;i<n;i++){
//         if(i==0){
//             html += '<div class="depotList">\
// 	        <div class="input-group">\
// 	            <div class="input-group-addon require"><i class="text-require">*  </i>仓库地址</div>\
// 	            <div data-toggle="distpicker" class="form-control form-inline distpicker">\
// 	                <div class="row">\
// 	                    <div class="form-group col-md-2">\
// 	                        <select class="form-control {validate:{required:true}}" name="repertoryProvince"></select>\
// 	                    </div>\
// 	                    <div class="form-group col-md-2">\
// 	                        <select class="form-control {validate:{required:true}}" name="repertoryCity"></select>\
// 	                    </div>\
// 	                    <div class="form-group col-md-2">\
// 	                        <select class="form-control {validate:{required:true}}" name="repertoryArea"></select>\
// 	                    </div>\
// 	                    <div class="form-group col-md-4">\
// 	                        <input type="text"  class="form-control text-inp Filter_SpaceAndFiveStrLen_Class {validate:{required:true}}" name="repertoryDetail"/>\
// 	                    </div>\
// 	                    <div class="form-group btn-box col-md-1">\
//                             <button type="button" class="btn" id="addDepot">\
//                                 <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>\
//                             </button>\
//                         </div>\
// 	                </div>\
// 	            </div>\
// 	        </div>\
// 	    </div>';
//         }else{
//             html += '<div class="col-md-6 depotList">\
// 	        <div class="input-group">\
// 	            <div class="input-group-addon require"><i class="text-require">*  </i>仓库地址</div>\
// 	            <div data-toggle="distpicker" class="form-control form-inline distpicker">\
// 	                <div class="row">\
// 	                    <div class="form-group col-md-2">\
// 	                        <select class="form-control {validate:{required:true}}" name="repertoryProvince"></select>\
// 	                    </div>\
// 	                    <div class="form-group col-md-2">\
// 	                        <select class="form-control {validate:{required:true}}" name="repertoryCity"></select>\
// 	                    </div>\
// 	                    <div class="form-group col-md-2">\
// 	                        <select class="form-control {validate:{required:true}}" name="repertoryArea"></select>\
// 	                    </div>\
// 	                    <div class="form-group col-md-4">\
// 	                        <input type="text" class="form-control text-inp Filter_SpaceAndFiveStrLen_Class {validate:{required:true}}" name="repertoryDetail"/>\
// 	                    </div>\
// 	                    <div class="form-group btn-box col-md-1">\
// 	                        <button type="button" class="btn removeDepot">\
// 	                            <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>\
// 	                        </button>\
// 	                    </div>\
// 	                </div>\
// 	            </div>\
// 	        </div>\
// 	    </div>';
//         }
//     }
//     return html;
// }
// // 初始化仓库地址
// function initDistpicker(){
//     $('[data-toggle="distpicker"]').each(function(){
//         //省
//         var val1=$.trim($(this).find("select").eq(0).attr("data-value"));
//         if(val1 && val1 != '')
//         {
//             $(this).find("select").eq(0).val(val1);
//             $(this).find("select").eq(0).change();
//         }
//         //市
//         var val2=$.trim($(this).find("select").eq(1).attr("data-value"));
//         if(val1 && val1 != '')
//         {
//             $(this).find("select").eq(1).val(val2);
//             $(this).find("select").eq(1).change();
//         }
//         //区
//         var val3=$.trim($(this).find("select").eq(2).attr("data-value"));
//         if(val1 && val1 != '')
//         {
//             $(this).find("select").eq(2).val(val3);
//         }
//     });
// }
// 全量数据不可编辑
function disable() {
  $("input[type='text']").attr("disabled", "disabled");
  $("input[type='tel']").attr("disabled", "disabled");
  $("input[type='number']").attr("disabled", "disabled");
  $("input[type='checkbox']").attr("disabled", "disabled");
  $("input[type='radio']").attr("disabled", "disabled");
  $("textarea").attr("disabled", "disabled");
  $("select").attr("disabled", "disabled");
  $("#addDepot").hide();
  $("#btn_addManufacturer").hide();
  $(".addDepot").hide();
  $(".removeDepot").hide();
  $(".rowBtn").attr("disabled", "disabled");
}
// 全量数据可编辑
function able() {
  $("input[type='text']").removeAttr("disabled");
  $("input[type='tel']").removeAttr("disabled");
  $("input[type='number']").removeAttr("disabled");
  $("input[type='checkbox']").removeAttr("disabled");
  $("input[type='radio']").removeAttr("disabled");
  $("textarea").removeAttr("disabled");
  $("select").removeAttr("disabled");
  $(".addDepot").show();
  $("#addDepot").show();
  $("#btn_addManufacturer").show();
  $(".removeDepot").show();
}
disable();

function checkMoreTimeInput() {
  let vi = true;
  var balanceStyle_ckeck = $(".class_balanceStyle input[name=parentCode]");
  $(balanceStyle_ckeck).each(function (index, item) {
    var a = $(item).parent().parent().siblings().find(".childName");
    $(a).each(function (ind, ite) {
      if ($(ite).text() == "结算日" || $(ite).text() == "支付日") {
        var b = $(ite).parent().siblings()[1];
        if (!b) {
          b = $(ite).parent().siblings()[0];
        }
        if ($(b).parent().hasClass("displaynone") !== true && $(b).parent().css('display') !== 'none') {
          if (b.value && parseFloat(b.value) > parseFloat(b.dataset.max)) {
            vi = false;
          }

          if (!b.value || b.value === "") {
            vi = false;
          }
        }
      }
    });
  });
  return vi;
}

function getMultipleDateList() {
  let objsArr = [];
  Object.keys(settleMultipleDateListObj).forEach((key) => {
    let objArr = settleMultipleDateListObj[key];
    objArr.forEach((item) => {
      if (
        item.settlementCycle === "" ||
        item.settlementDate === "" ||
        item.paymentDay === ""
      ) {
        return;
      }
      objsArr.push(item);
    });
  });
  return objsArr;
}

function addSettleMultiple(code) {
  if (code !== "2002" && code !== "2003") {
    return;
  }
  $(".checkbox" + code)
    .parent()
    .find(".addSettleMultipleDateList")
    .addClass(code);
  settleMultipleDateList[code.toString()] = settleMultipleDateList[
    code.toString()
  ]
    ? settleMultipleDateList[code.toString()]
    : [];
}

function clearSettleMultiple(code) {
  if (code !== "2002" && code !== "2003") {
    return;
  }
  checkInitAddBtn();
}
function removeAllChilds(code) {
  if (code !== "2002" && code !== "2003") {
    return;
  }
  Object.keys(settleMultipleDateList).forEach((key) => {
    let el = settleMultipleDateList[key];
    for (let index = 0; index < el.length; index++) {
      const element = el[index];
      let rowClass = ".list" + code + element.index;
      $(rowClass).remove();
    }
  });
  delete settleMultipleDateList[code];
  getsettleMultipleDateListObj();
}

function clickFunctions() {
  $(".2002")
    .off()
    .on("click", function () {
      if (!settleMultipleDateList["2002"]) {
        settleMultipleDateList["2002"] = [];
      }
      var length2002 = settleMultipleDateList["2002"].length;
      var index =
        length2002 > 0
          ? settleMultipleDateList["2002"][length2002 - 1].index + 1
          : 0;
      if (length2002 > 3) {
        return;
      }
      addhtml("2002", index);

      settleMultipleDateList["2002"].push({
        settlementCycle: "",
        settlementDate: "",
        paymentDay: "",
        index: index,
        settleMethod: 2002,
      });

      $(".minuteMultipleDateList2002" + index)
        .off()
        .on("click", function (val) {
          console.log(val);
          $(".list2002" + index).remove();
          settleMultipleDateList["2002"] = settleMultipleDateList[
            "2002"
          ].filter((item) => {
            return item.index !== index;
          });
        });
    });

  $(".2003")
    .off()
    .on("click", function () {
      if (!settleMultipleDateList["2003"]) {
        settleMultipleDateList["2003"] = [];
      }
      var length2003 = settleMultipleDateList["2003"].length;
      var index =
        length2003 > 0
          ? settleMultipleDateList["2003"][length2003 - 1].index + 1
          : 0;
      if (length2003 > 3) {
        return;
      }
      addhtml("2003", index);
      settleMultipleDateList["2003"].push({
        settlementCycle: "",
        settlementDate: "",
        paymentDay: "",
        index: index,
        settleMethod: 2003,
      });

      $(".minuteMultipleDateList2003" + index)
        .off()
        .on("click", function (val) {
          console.log(val);
          $(".list2003" + index).remove();
          settleMultipleDateList["2003"] = settleMultipleDateList[
            "2003"
          ].filter((item) => {
            return item.index !== index;
          });
        });
    });
}

function checkSameInput(obj, datas) {
  if (!datas || datas.length === 0) {
    return true;
  }
  let objctArr =
    obj.supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList.filter(
      (item) => {
        return item.parentCode === "2002" || item.parentCode === "2003";
      }
    );
  objectDatas = [
    {
      paymentDay: "",
      settleMethod: "2002",
      settlementCycle: "",
      settlementDate: "",
    },
    {
      paymentDay: "",
      settleMethod: "2003",
      settlementCycle: "",
      settlementDate: "",
    },
  ];
  for (let index = 0; index < objctArr.length; index++) {
    const element = objctArr[index];
    if (element.code === "2002001") {
      objectDatas[0].settlementCycle = element.value;
    }
    if (element.code === "2002002") {
      objectDatas[0].settlementDate = element.value;
    }
    if (element.code === "2002003") {
      objectDatas[0].paymentDay = element.value;
    }
    if (element.code === "2003001") {
      objectDatas[1].settlementCycle = element.value;
    }
    if (element.code === "2003002") {
      objectDatas[1].settlementDate = element.value;
    }
    if (element.code === "2003003") {
      objectDatas[1].paymentDay = element.value;
    }
  }

  let allDatas = objectDatas.concat(datas);
  for (let index = 0; index < allDatas.length; index++) {
    const element = allDatas[index];
    let settlementCycles = allDatas.filter((item) => {
      return (
        item.settleMethod.toString() === element.settleMethod.toString() &&
        item.settlementCycle === element.settlementCycle &&
        item.settlementDate === element.settlementDate &&
        item.paymentDay === element.paymentDay
      );
    });
    if (settlementCycles.length > 1) {
      return false;
    }
  }
  return true;
}

//添加html
function addhtml(code, index) {
  var space =
    "<div style='width:100%;display:flex;flex-direction:row;' class='list" +
    code +
    index +
    "'><div class='col-md-2'></div>";
  var html =
    "<div class='col-md-3 col-xs-4'><span class='cyclechildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>结算周期</span></div><input class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class cyclecode' type='number' placeholder='天' name='cyclecode'>天</span></div>";
  var html1 =
    "<div class='col-md-3 col-xs-4'><span class='datechildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>结算日</span></div><input class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class datecode' type='number' placeholder='日' name='datecode' data-max='31'>日</span></div>";
  var html2 =
    "<div class='col-md-3 col-xs-4'><span class='daychildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>支付日</span></div><input class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class daycode' type='number' placeholder='日' name='daycode' data-max='31'>日</span></div>";
  var end =
    "<div class='minuteMultipleDateList" + code + index + "'>-</div></div>";
  let classs = "." + code;
  $(classs)
    .parent()
    .append(space + html + html1 + html2 + end);
}

//初始化详情的数据
function apiGetFirstOperate(baseid) {
  let rqType = "OPERATE_ATTRIBUTE_CHANG";
  let baseId = baseid;
  $.ajax({
    url:
      "/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/new-add-settle-multiple-date?type=" +
      rqType +
      "&fid=" +
      baseId,
    type: "get",
    dataType: "json",
    success: function (data) {
      const { code, msg, result } = data;
      if (code === 0) {
        settleMultipleDateListObj = {};
        settleMultipleDateListObj = result ? result : {};
        // getsettleMultipleDateListObj();
      } else {
      }
    },
    error: function () {},
  });
}

//初始化详情的旧数据
function apiGetOldFirstOperate() {
  let rqType = "OPERATE_ATTRIBUTE";
  let baseId = $("#supplierOrganBaseId").val();
  $.ajax({
    url:
      "/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/new-add-settle-multiple-date?type=" +
      rqType +
      "&fid=" +
      baseId,
    type: "get",
    dataType: "json",
    success: function (data) {
      const { code, msg, result } = data;
      if (code === 0) {
        settleMultipleDateList = result;
        oldsettleMultipleDateList = JSON.parse(JSON.stringify(result));
        Object.keys(settleMultipleDateList).forEach((key) => {
          console.log(key);
          settleMultipleDateList[key.toString()].forEach((item, index) => {
            item["index"] = index;
            addDetailhtml(key, index, item);
          });
          let classs = ".checkbox" + key;
          $(classs)
            .parent()
            .find(".addSettleMultipleDateList")
            .addClass(key.toString());
        });
        checkInitAddBtn();
        clickFunctions();
      } else {
      }
    },
    error: function () {},
  });
}

function addDisabled(code) {
  $(".checkbox" + code)
    .parent()
    .find(".addSettleMultipleDateList")
    .addClass("displaynone");
  if (!settleMultipleDateList[code.toString()]) return;
  settleMultipleDateList[code.toString()].forEach((item) => {
    let classstring = ".list" + code + item.index;
    let minuClss = ".minuteMultipleDateList" + code + item.index;
    $(classstring).find(".cyclecode").attr("disabled", "disabled");
    $(classstring).find(".datecode").attr("disabled", "disabled");
    $(classstring).find(".daycode").attr("disabled", "disabled");
    $(classstring).find(minuClss).addClass("displaynone");
  });
}

function clearDisabled(code) {
  checkInitAddBtn();
  if (!settleMultipleDateList[code.toString()]) return;
  if (!settleMultipleDateList) {
    return;
  }
  settleMultipleDateList[code.toString()].forEach((item) => {
    let classstring = ".list" + code + item.index;
    let minuClss = ".minuteMultipleDateList" + code + item.index;
    $(classstring).find(".cyclecode").removeAttr("disabled");
    $(classstring).find(".datecode").removeAttr("disabled");
    $(classstring).find(".daycode").removeAttr("disabled");
    $(classstring).find(minuClss).removeClass("displaynone");
  });
}

function getsettleMultipleDateListObj() {
  settleMultipleDateListObj = {};
  let obj = {};
  Object.keys(settleMultipleDateList).forEach((key) => {
    let objArr = settleMultipleDateList[key];
    let objsArr = [];
    objArr.forEach((item) => {
      let obj = {
        settlementCycle: "",
        settlementDate: "",
        paymentDay: "",
        settleMethod: "",
      };
      let classes = ".list" + item.settleMethod + item.index;
      obj.settleMethod = item.settleMethod;
      obj["settlementCycle"] = $(classes).find(".cyclecode").val();
      obj["settlementDate"] = $(classes).find(".datecode").val();
      obj["paymentDay"] = $(classes).find(".daycode").val();
      objsArr.push(obj);
    });
    obj[key] = objsArr;
  });
  settleMultipleDateListObj = obj;
}

function setOldDetail() {
  getsettleMultipleDateListObj();
  Object.keys(settleMultipleDateList).forEach((key) => {
    let classs = ".checkbox" + key;
    settleMultipleDateList[key.toString()].forEach((item, index) => {
      let listClass = ".list" + key + item.index;
      $(classs).parent().find(listClass).remove();
    });
  });
  Object.keys(oldsettleMultipleDateList).forEach((key) => {
    let classs = ".checkbox" + key;
    oldsettleMultipleDateList[key.toString()].forEach((item, index) => {
      item["index"] = index;
      addDetailhtml(key, index, item);
    });

    $(classs)
      .parent()
      .find(".addSettleMultipleDateList")
      .addClass(key.toString());
  });
  settleMultipleDateList = JSON.parse(
    JSON.stringify(oldsettleMultipleDateList)
  );
  clickFunctions();
  checkInitAddBtnHid();
}

function checkInitAddBtn() {
  let codes = ["2002", "2003"];
  for (let index = 0; index < codes.length; index++) {
      const element = codes[index];
      if (settleMultipleDateList[element.toString()] && settleMultipleDateList[element.toString()].length>0) {
      let classs = ".checkbox" + element;
      let disabled =
        $(classs).find(".parentCode").attr("disabled") === undefined
          ? false
          : true;
      let value =
        $(classs).find(".parentCode").val() === "2002" ||
        $(classs).find(".parentCode").val() === "2003"
          ? true
          : false;
      if (disabled === false && value) {
        $(classs)
          .parent()
          .find(".addSettleMultipleDateList")
          .removeClass("displaynone");
      }
    }
  }
}

function checkInitAddBtnHid() {
  let codes = ["2002", "2003"];
  for (let index = 0; index < codes.length; index++) {
    const element = codes[index];
    let classs = ".checkbox" + element;
    $(classs)
      .parent()
      .find(".addSettleMultipleDateList")
      .addClass("displaynone");
  }
}

//添加html
function addDetailhtml(code, index, detail) {
  let classs = ".checkbox" + code;
  let disabled =
    $(classs).find(".parentCode").attr("disabled") === undefined ? false : true;
  let redOnlyString = disabled ? "disabled='disabled'" : "";
  var space =
    "<div style='width:100%;display:flex;flex-direction:row;' class='list" +
    code +
    index +
    "'><div class='col-md-2'></div>";
  var html =
    "<div class='col-md-3 col-xs-4'><span class='cyclechildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>结算周期</span></div><input " +
    redOnlyString +
    " value='" +
    detail.settlementCycle +
    "' class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class cyclecode' type='number' placeholder='天' name='cyclecode'>天</span></div>";
  var html1 =
    "<div class='col-md-3 col-xs-4'><span class='datechildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>结算日</span></div><input " +
    redOnlyString +
    " value='" +
    detail.settlementDate +
    "' class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class datecode' type='number' placeholder='日' name='datecode' data-max='31'>日</span></div>";
  var html2 =
    "<div class='col-md-3 col-xs-4'><span class='daychildCode'> <div class='col-md-6 col-xs-5'><span class='childName'>支付日</span></div><input " +
    redOnlyString +
    " value='" +
    detail.paymentDay +
    "' class='col-md-4 cValue col-xs-3 Filter_SpaceAndStrLen_Class daycode' type='number' placeholder='日' name='daycode' data-max='31'>日</span></div>";
  var end = disabled
    ? "<div class='displaynone minuteMultipleDateList" +
      code +
      index +
      "'>-</div></div>"
    : "<div class='minuteMultipleDateList" + code + index + "'>-</div></div>";
  $(classs)
    .parent()
    .append(space + html + html1 + html2 + end);

  disabled === false &&
    $(classs)
      .parent()
      .find(".addSettleMultipleDateList")
      .removeClass("displaynone");

  $(".minuteMultipleDateList" + code + index)
    .off()
    .on("click", function (val) {
      $(".list" + code + index).remove();
      settleMultipleDateList[code.toString()] = settleMultipleDateList[
        code.toString()
      ].filter((item) => {
        return item.index !== index;
      });
    });
}
$(function () {
  // 注册地址
  let addressSelIdObj = [
    {
      nextNodeWrap: "#provinceSel_wrap",
      nextNodeName: "registerProvince",
      nextNodeId: "province1",
    },
    {
      nextNodeWrap: "#citySel_wrap",
      nextNodeName: "registerCity",
      nextNodeId: "registerCity",
    },
    {
      nextNodeWrap: "#districtSel_wrap",
      nextNodeName: "registerArea",
      nextNodeId: "district1",
    },
  ];
  let registerPromiseArray = [];
  utils.setAllProDom(
    "#provinceSel_wrap",
    addressSelIdObj,
    "#registerBox",
    true,
    function () {
      // 注册地址有值回显
      let _registerHiddenVal = eval($("#registerAddressJson").val());
      if (!_registerHiddenVal) _registerHiddenVal = ["", "", "", ""];
      _registerHiddenVal.splice(_registerHiddenVal.length - 1);
      $("#" + addressSelIdObj[0]["nextNodeId"]).prop("disabled", true);
      $("#" + addressSelIdObj[0]["nextNodeId"]).val(_registerHiddenVal[0]);
      for (let i = 1; i < _registerHiddenVal.length; i++) {
        registerPromiseArray.push(
          utils.setAddressReturnVal(_registerHiddenVal[i - 1])
        );
      }
      Promise.all(registerPromiseArray).then((data) => {
        console.log(data);
        for (let i = 0; i < data.length; i++) {
          $("#" + addressSelIdObj[i + 1]["nextNodeId"]).html(data[i]);
          $("#" + addressSelIdObj[i + 1]["nextNodeId"]).val(
            _registerHiddenVal[i + 1]
          );
          $("#" + addressSelIdObj[i + 1]["nextNodeId"]).prop("disabled", true);
        }
      });
      disable();
    }
  );
  // 仓库地址
  let storgeAddressSelIdObj = [];
  let _storgeHiddenVal = eval($("#repertoryAddressJson").val());
  if (!_storgeHiddenVal) _storgeHiddenVal = [["", "", "", ""]];
  let _storgeHiddenValArr = eval($("#repertoryAddressJson").val());
  if (!_storgeHiddenValArr) _storgeHiddenValArr = [["", "", "", ""]];
  $(_storgeHiddenValArr).each((index, item) => {
    item.splice(item.length - 1);
  });
  let obj = distpickerHTML(_storgeHiddenValArr.length);
  $(obj.radomInit).each((index, item) => {
    let _arr = [
      {
        nextNodeWrap: "#stoProvinceSel_wrap_" + item,
        nextNodeName: "repertoryProvince_" + item,
        nextNodeId: "repertoryProvince_" + item,
      },
      {
        nextNodeWrap: "#stoCitySel_wrap_" + item,
        nextNodeName: "repertoryCity_" + item,
        nextNodeId: "repertoryCity_" + item,
      },
      {
        nextNodeWrap: "#stoDistrictSel_wrap_" + item,
        nextNodeName: "repertoryArea_" + item,
        nextNodeId: "repertoryArea_" + item,
      },
    ];
    storgeAddressSelIdObj.push(_arr);
  });
  $("#depotAddress").html(obj.html);
  $(obj.radomInit).each((index, item) => {
    let storagePromiseArray = [];
    utils.setAllProDom(
      "#stoProvinceSel_wrap_" + obj.radomInit[index],
      storgeAddressSelIdObj[index],
      "#storageBox_" + obj.radomInit[index],
      true,
      function () {
        $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"]).val(
          _storgeHiddenValArr[index][0]
        );
        $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
          .parents(".distpicker")
          .find("[name=repertoryDetail]")
          .val(_storgeHiddenVal[index][3]);
        $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"]).prop(
          "disabled",
          true
        );
        for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
          storagePromiseArray.push(
            utils.setAddressReturnVal(_storgeHiddenValArr[index][ind - 1])
          );
        }
        let allSelArr = storgeAddressSelIdObj[index]
          .flat()
          .map((item, index) => {
            if (index != 0) {
              return item["nextNodeId"];
            }
          })
          .filter((item) => {
            return item;
          });
        Promise.all(storagePromiseArray).then((data) => {
          for (let i = 0; i < data.length; i++) {
            $("#" + allSelArr[i]).html(data[i]);
            $("#" + allSelArr[i]).val(_storgeHiddenValArr[index][i + 1]);
            $("#" + allSelArr[i]).prop("disabled", true);
          }
        });
        disable();
      }
    );
  });
});

// 如果是新增页 或者草稿
if (pageType == 0 || pageType == 1) {
  // 能重新选择商品
} else {
  $("#supplierBtn").hide();
}
// 如果是驳回编辑页或者草稿页  资料变更按钮显示
if (pageType == 1 || pageType == 4) {
  $("#changeApplyBtn").show();
}

//质量保证协议上传附件
upLoadFile("#zlbzUpload", "#table1", "signDate");
//客户委托书上传附件
upLoadFile("#khwtUpload", "#table2", "proxyOderNo");
// 审核流程
function initApprovalFlowChart(url) {
  //获取审核流程数据
  $.ajax({
    type: "POST",
    url: url,
    async: false,
    success: function (data) {
      if (data.code == 0 && data.result != null) {
        console.log(data.result);
        $(".flow").process(data.result);
      }
    },
    error: function () {},
  });
}

// 修改后数据拼装
if (pageType == 0) {
  disable();
  $("#supplierName").removeAttr("readonly").removeAttr("disabled");
  $("#changeApplyBtn").hide();
  // 修改字段结构初始化
  $.changApply_insertData({
    name: "columnValue",
    status: "changeStatus",
    afterValue: "valueAfter",
    beforeValue: "valueBefore",
  }); //按钮id：changeApplyBtn
  var url = "/proxy-product/product/purchaseLimit/queryTotle?key=supplierUpdate";
  initApprovalFlowChart(url);
} else {
  //编辑页面
  /*if (pageType==1||pageType==4){
        disable();
    }
    //审核页面
    if (pageType==2||pageType==3){
        disable();
    }*/
  // 审核页和驳回编辑页
  if (pageType == 3 || pageType == 4) {
    var url = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId=" + processId;
    initApprovalFlowChart(url);
  }
  // 草稿页
  if (pageType == 1) {
    var url = "/proxy-product/product/purchaseLimit/queryTotle?key=supplierUpdate";
    initApprovalFlowChart(url);
  }
  // 详情页
  if (pageType == 2) {
    var url =
      "/proxy-product/product/purchaseLimit/queryTotle?processInstaId=" + approvalProcessId;
    initApprovalFlowChart(url);
  }
  $.ajax({
    url: "/proxy-supplier/supplier/supplierOrganBaseChangeApproval/querySupplierOrganBaseAfterDetail",
    data: { id: approvalId },
    type: "post",
    success: function (data) {
      if (data != null && data != undefined) {
        //字段修改全部
        var changeObj = data.result.supplierOrganBaseChangeRecordVOList;
        var obj = {};
        var objList = {};
        /*if(data.result.supplierOrganBaseApprovalRecordVO.supplierExtendTypeVOList){
                    var list = data.result.supplierOrganBaseApprovalRecordVO.supplierExtendTypeVOList;
                    for (i=0;i<list.length;i++){
                        var meth = list[i];
                        if(meth.type==1){
                            pay.push(meth);
                        } else{
                            settle.push(meth);
                        }
                    }
                }*/
        $.each(changeObj, function (i, v) {
          // 判断是否为质量保证协议
          if (v.columnValue == "supplierQualityAgreementList") {
            objList.supplierQualityAgreementList = {
              columnValue: "supplierQualityAgreementList",
              valueAfter: {},
              changeStatus: -1,
            };
            objList.supplierQualityAgreementList.valueAfter =
              data.result.supplierQualityAgreementList;
          } else if (v.columnValue == "supplierClientProxyOrderList") {
            // 判断是否为客户委托书
            objList.supplierClientProxyOrderList = {
              columnValue: "supplierClientProxyOrderList",
              valueAfter: {},
              changeStatus: -1,
            };
            objList.supplierClientProxyOrderList.valueAfter =
              data.result.supplierClientProxyOrderList;
          } else if (v.columnValue == "supplierApprovalFileList") {
            // 判断是否为批准文件
            objList.supplierApprovalFileList = {
              columnValue: "supplierApprovalFileList",
              valueAfter: {},
              changeStatus: -1,
            };
            objList.supplierApprovalFileList.valueAfter =
              data.result.supplierApprovalFileList;
          } else if (v.columnValue == "supplierYearReportList") {
            // 年度报告
            objList.supplierYearReportList = {
              columnValue: "supplierYearReportList",
              valueAfter: {},
              changeStatus: -1,
            };
            objList.supplierYearReportList.valueAfter =
              data.result.supplierYearReportList;
          } else if (v.columnValue == "supplierOtherFileList") {
            // 其他附件
            objList.supplierOtherFileList = {
              columnValue: "supplierOtherFileList",
              valueAfter: {},
              changeStatus: -1,
            };
            objList.supplierOtherFileList.valueAfter =
              data.result.supplierOtherFileList;
          } else if (v.columnValue == "supplierExtendItemVOList") {
            // 付款方式结算方式
            obj.supplierExtendItemVOList = {
              columnValue: "supplierExtendItemVOList",
              valueAfter: {},
              changeStatus: -1,
            };
            obj.supplierExtendItemVOList.valueAfter =
              data.result.supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList;
          } else {
            v.changeStatus = -1;
            obj[v.columnValue] = v;
          }
        });
        //编辑页面
        if (pageType == 1 || pageType == 4) {
          // 修改字段结构初始化
          $.changApply_insertData(); //按钮id：changeApplyBtn
        }
        //审核页面和详情页
        if (pageType == 2 || pageType == 3) {
          var objView = obj;
          $.extend(objView, objList);
          $.changeApply_selectData(obj);
        }
        window.changeApply = obj;
        window.changeApplyList = objList;
        window.changeApplyBak = JSON.parse(JSON.stringify(obj));
        window.changeApplyListBak = JSON.parse(JSON.stringify(objList));
      }
    },
  });
}

//type 2：主数据变更 3：运营属性变更
if (supplierOrganBaseId == null || supplierOrganBaseId == "") {
  // 质量保证协议
  xGridTable1({
    id: "",
    signDate: "",
    validDate: "",
    signName: "",
    enclosureCount: "",
    enclosureList: [],
  });
  // 客户委托书
  xGridTable2({
    id: "",
    proxyOderNo: "",
    mandataryName: "",
    mandatarySex: "",
    mandataryTel: "",
    mandataryCertificateNumber: "",
    mandataryAddress: "",
    identityValidDate: "",
    proxyValidDate: "",
    authorityType: "",
    enclosureCount: "",
    supplierClientProxyProductVOList: [],
    supplierClientProxyBusinessScopeVOList: [],
    enclosureList: [],
    //2018.9.3 15:36,RL,新增剂型,begin
    supplierClientProxyTypeVOList: [],
    // 2018.9.3 15:36,RL,新增剂型,end
  });
  //批准文件
  xGridTable3({
    id: "",
    certificateId: "",
    certificateNum: "",
    supplierApprovalFileBusinessScopeVOList: [],
    certificationOffice: "",
    certificationDate: "",
    validityDate: "",
    enclosureCount: "",
    enclosureList: [],
  });
  //年度报告
  xGridTable4(
    (xGridData = {
      id: "",
      reportDate: "",
      manageAbnormal: "",
      administrativePenalty: "",
      enclosureCount: "",
      enclosureList: [],
    })
  );
} else {
  // 质量保证协议
  initTable1(supplierOrganBaseId, 1);
  // 客户委托书
  initTable2(supplierOrganBaseId, 1);
  // 批准文件经营范围
  table3(supplierOrganBaseId);
  //批准文件
  initTable3(supplierOrganBaseId, 0);
  //年度报告table初始化
  initTable4(supplierOrganBaseId, 0);
  // 其他附件
  initOtherFile(supplierOrganBaseId, 0);
}
// 批准文件 经营范围填充
function table3(supplierBaseId) {
  $.ajax({
    url: "/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getApprovalFileList",
    data: { correlationId: supplierBaseId, type: 0 },
    type: "post",
    dataType: "json",
    success: function (data) {
      console.log(data);
      var approvalFileList = data.result.list;
      for (var i = 0; i < approvalFileList.length; i++) {
        var approvalFile = approvalFileList[i];
        // 日期转换
        if (approvalFile.validityDate != null) {
          approvalFile.validityDate = FormatAllDate(approvalFile.validityDate);
        }

        var ids = approvalFile.supplierApprovalFileBusinessScopeVOList;
        var names = approvalFile.scopeofoperationVo;
        for (var j = 0; j < ids.length; j++) {
          for (var k = 0; k < names.length; k++) {
            if (ids[j].businessScopeCode == names[k].scopeId) {
              ids[j].id = ids[j].businessScopeCode;
              ids[j].name = names[k].scopeName;
              continue;
            }
            if (names[k].children != null) {
              var childrens = names[k].children;
              for (var p = 0; p < childrens.length; p++) {
                if (ids[j].businessScopeCode == childrens[p].scopeId) {
                  ids[j].id = ids[j].businessScopeCode;
                  ids[j].name = childrens[p].scopeName;
                }
              }
            }
          }
        }
      }
      var htmlAry = [],
        idAry = [];
      $.each(
        utils.operateRange(
          approvalFileList,
          "supplierApprovalFileBusinessScopeVOList",
          "validityDate"
        ),
        function (i, v) {
          if (v.status) {
            htmlAry.push('<font style="color: red;">' + v.name + "</font>");
          } else {
            htmlAry.push(v.name);
          }
          idAry.push(v.id);
        }
      );
      //console.log(htmlAry.join(','))
      $("#baseDataBuseScope").html(htmlAry.join(","));
    },
  });
}

// string转日期
function FormatAllDate(sDate) {
  var date = new Date(sDate);
  var seperator1 = "-";
  var month = date.getMonth() + 1;
  var strDate = date.getDate();
  //月
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  //日
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  //格式化后日期为：yyyy-MM-dd HH:mm:ss
  var currentdate =
    date.getFullYear() + seperator1 + month + seperator1 + strDate;
  return currentdate;
}

//客户委托书被委托人发生变化
$("#table2").on("keyup", ".mandataryName:first", function (ev) {
  var mandataryName = $.trim($(this).val()); //选中的值
  if (mandataryName != "") {
    $("input[name='mandatary']").val(mandataryName);
  }
});

//客户委托书被委托人电话发生变化
$("#table2").on("keyup", ".mandataryTel:first", function (ev) {
  var mandataryTel = $.trim($(this).val()); //选中的值
  $("input[name='mandataryPhone']").val(mandataryTel);
});
//初始化地址选择
// $('[data-toggle="distpicker"]').distpicker();
// initDistpicker();
//添加仓库
$("#depotAddress").on("click", ".addDepot", function () {
  let obj = distpickerHTML(1, 1);
  $("#depotAddress").append(obj.html);
  let storgeAddressSelIdObj = [
    {
      nextNodeWrap: "#stoProvinceSel_wrap_" + obj.radomInit[0],
      nextNodeName: "repertoryProvince_" + obj.radomInit[0],
      nextNodeId: "repertoryProvince_" + obj.radomInit[0],
    },
    {
      nextNodeWrap: "#stoCitySel_wrap_" + obj.radomInit[0],
      nextNodeName: "repertoryCity_" + obj.radomInit[0],
      nextNodeId: "repertoryCity_" + obj.radomInit[0],
    },
    {
      nextNodeWrap: "#stoDistrictSel_wrap_" + obj.radomInit[0],
      nextNodeName: "repertoryArea_" + obj.radomInit[0],
      nextNodeId: "repertoryArea_" + obj.radomInit[0],
    },
  ];
  utils.setAllProDom(
    "#stoProvinceSel_wrap_" + obj.radomInit[0],
    storgeAddressSelIdObj,
    "#storageBox_" + obj.radomInit[0],
    true,
    function () {
      $(storgeAddressSelIdObj[0]["nextNodeWrap"])
        .parents(".distpicker")
        .find(".btn")
        .removeClass("addDepot")
        .addClass("removeDepot");
      $(storgeAddressSelIdObj[0]["nextNodeWrap"])
        .parents(".distpicker")
        .find(".glyphicon")
        .removeClass("glyphicon-plus")
        .addClass("glyphicon-minus");
    }
  );
});
//删除仓库地址
$("#depotAddress").on("click", ".removeDepot", function () {
  $(this).parents(".depotList").remove();
});

//点击放大镜触发
$(document).on("click", "#supplierBtn", function (ev) {
  search(ev);
});
//回车触发
$(document).on("keydown", "#supplierName", function (ev) {
  if (ev.keyCode == 13) {
    search(ev);
  }
});
// 模糊搜索供应商名称
function search(ev) {
  // 再次修改草稿页和新增页
  if (pageType == 0 || pageType == 1) {
    var supplierName = $("#supplierName").val();
    dialog({
      url: "/proxy-supplier/supplier/supplierOrganBaseChangeApproval/toSearchList", //弹框页面请求地址
      title: "搜索供应商",
      width: 1000,
      height: 650,
      data: supplierName, // 给modal 要传递的 的数据
      onclose: function () {
        if (this.returnValue) {
          var data = this.returnValue;
          $.ajax({
            url: "/proxy-supplier/supplier/supplierOrganBaseChangeApproval/queryOrganBaseById",
            data: { id: data.id },
            type: "post",
            dataType: "json",
            success: function (data) {
              // 清空变更属性
              window.changeApply = {}; //变更obj
              window.changeApply_ = {}; //变更临时obj
              window.changeApplyList = {}; //变更obj-list
              window.changeApplyList_ = {}; //变更临时obj-list
              var vo = data.result;
              loadData(vo.supplierBase, $("#base"));
              loadData(vo.supplierOrganBase, $("#operat"));
              // 注册地址
              let addressSelIdObj = [
                [
                  {
                    nextNodeWrap: "#provinceSel_wrap",
                    nextNodeName: "registerProvince",
                    nextNodeId: "province1",
                  },
                  {
                    nextNodeWrap: "#citySel_wrap",
                    nextNodeName: "registerCity",
                    nextNodeId: "registerCity",
                  },
                  {
                    nextNodeWrap: "#districtSel_wrap",
                    nextNodeName: "registerArea",
                    nextNodeId: "district1",
                  },
                ],
              ];
              let registerPromiseArray = [];
              let _registerHiddenVal = [
                vo.supplierBase.registerProvince,
                vo.supplierBase.registerCity,
                vo.supplierBase.registerArea,
              ];
              $("#" + addressSelIdObj[0][0]["nextNodeId"]).val(
                _registerHiddenVal[0]
              );
              $("#" + addressSelIdObj[0][0]["nextNodeId"]).attr(
                "data-value",
                _registerHiddenVal[0]
              );
              for (let i = 1; i < _registerHiddenVal.length; i++) {
                registerPromiseArray.push(
                  utils.setAddressReturnVal(_registerHiddenVal[i - 1])
                );
              }
              Promise.all(registerPromiseArray).then((data) => {
                console.log(data);
                for (let i = 0; i < data.length; i++) {
                  $("#" + addressSelIdObj[0][i + 1]["nextNodeId"]).html(
                    data[i]
                  );
                  $("#" + addressSelIdObj[0][i + 1]["nextNodeId"]).val(
                    _registerHiddenVal[i + 1]
                  );
                  $("#" + addressSelIdObj[0][i + 1]["nextNodeId"]).prop(
                    "disabled",
                    true
                  );
                }
              });

              var AddressVOList =
                vo.supplierBase.supplierRepertoryAddressVOList;
              if (null != AddressVOList) {
                var len = AddressVOList.length;
                $("#depotAddress").find(".depotList").remove();
                var obj = distpickerHTML(len);
                let storgeAddressSelIdObj = [];
                let _storgeHiddenValArr = AddressVOList.map((item) => {
                  return [
                    item.repertoryProvince,
                    item.repertoryCity,
                    item.repertoryArea,
                  ];
                });
                let _storgeHiddenVal = AddressVOList.map((item) => {
                  return [
                    item.repertoryProvince,
                    item.repertoryCity,
                    item.repertoryArea,
                    item.repertoryDetail,
                  ];
                });
                $(obj.radomInit).each((index, item) => {
                  let _arr = [
                    {
                      nextNodeWrap: "#stoProvinceSel_wrap_" + item,
                      nextNodeName: "repertoryProvince_" + item,
                      nextNodeId: "repertoryProvince_" + item,
                    },
                    {
                      nextNodeWrap: "#stoCitySel_wrap_" + item,
                      nextNodeName: "repertoryCity_" + item,
                      nextNodeId: "repertoryCity_" + item,
                    },
                    {
                      nextNodeWrap: "#stoDistrictSel_wrap_" + item,
                      nextNodeName: "repertoryArea_" + item,
                      nextNodeId: "repertoryArea_" + item,
                    },
                  ];
                  storgeAddressSelIdObj.push(_arr);
                });
                $("#depotAddress").html(obj.html);
                $(obj.radomInit).each((index, item) => {
                  let storagePromiseArray = [];
                  utils.setAllProDom(
                    "#stoProvinceSel_wrap_" + obj.radomInit[index],
                    storgeAddressSelIdObj[index],
                    "#storageBox_" + obj.radomInit[index],
                    true,
                    function () {
                      $(
                        "#" + storgeAddressSelIdObj[index][0]["nextNodeId"]
                      ).val(_storgeHiddenValArr[index][0]);
                      $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find("[name=repertoryDetail]")
                        .val(_storgeHiddenVal[index][3]);
                      disable();
                      for (
                        let ind = 1;
                        ind < _storgeHiddenValArr[index].length;
                        ind++
                      ) {
                        storagePromiseArray.push(
                          utils.setAddressReturnVal(
                            _storgeHiddenValArr[index][ind - 1]
                          )
                        );
                      }
                      let allSelArr = storgeAddressSelIdObj[index]
                        .flat()
                        .map((item, index) => {
                          if (index % 3 != 0) {
                            return item["nextNodeId"];
                          }
                        })
                        .filter((item) => {
                          return item;
                        });
                      Promise.all(storagePromiseArray).then((data) => {
                        for (let i = 0; i < data.length; i++) {
                          $("#" + allSelArr[i]).html(data[i]);
                          $("#" + allSelArr[i]).val(
                            _storgeHiddenValArr[index][i + 1]
                          );
                          $("#" + allSelArr[i]).prop("disabled", true);
                        }
                      });
                    }
                  );
                });
              } else {
                let obj = distpickerHTML();
                $("#depotAddress").html(obj.html);
                let storgeAddressSelIdObj = [
                  {
                    nextNodeWrap: "#stoProvinceSel_wrap_" + obj.radomInit[0],
                    nextNodeName: "repertoryProvince_" + obj.radomInit[0],
                    nextNodeId: "repertoryProvince_" + obj.radomInit[0],
                  },
                  {
                    nextNodeWrap: "#stoCitySel_wrap_" + obj.radomInit[0],
                    nextNodeName: "repertoryCity_" + obj.radomInit[0],
                    nextNodeId: "repertoryCity_" + obj.radomInit[0],
                  },
                  {
                    nextNodeWrap: "#stoDistrictSel_wrap_" + obj.radomInit[0],
                    nextNodeName: "repertoryArea_" + obj.radomInit[0],
                    nextNodeId: "repertoryArea_" + obj.radomInit[0],
                  },
                ];
                utils.setAllProDom(
                  "#stoProvinceSel_wrap_" + obj.radomInit[0],
                  storgeAddressSelIdObj,
                  "#storageBox_" + obj.radomInit[0],
                  true,
                  function () {
                    $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
                      .parents(".distpicker")
                      .find("[name=repertoryDetail]")
                      .attr(
                        "changeApplyFlag",
                        "supplierRepertoryAddressVOList"
                      );
                    disable();
                  }
                );
              }

              // $('[data-toggle="distpicker"]').distpicker();
              //initDistpicker();
              // 对应生产厂商
              let ManufactoryVOList = vo.supplierBase.supplierManufactoryVOList,
                ManufactoryHtml = "";
              if (ManufactoryVOList.length != 0) {
                let len = ManufactoryVOList.length;
                for (let i = 0; i < len; i++) {
                  ManufactoryHtml += `<div class="col-md-6 ManufactoryList">
                                                        <div class="input-group">
                                                            <div class="input-group-addon ">对应生产厂商</div>
                                                            <div class="form-control form-inline distpicker">
                                                                <div>
                                                                    <div class="form-group col-md-11">
                                                                        <input type="hidden" id="Manufactory${i}" value="${
                    ManufactoryVOList[i]["manufactoryId"]
                  }"  name="Manufactory${i}"/>
                                                                        <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class ${
                                                                          vo
                                                                            .supplierBase[
                                                                            "supplierTypeId"
                                                                          ] ==
                                                                          "55"
                                                                            ? ""
                                                                            : ""
                                                                        }" value="${
                    ManufactoryVOList[i]["manufactoryName"]
                  }"   id="Manufactory${i}Val" name="Manufactory${i}Val"  disabled="disabled" />
                                                                    </div>
                                                                   <!-- <div class="form-group btn-box col-md-1">  {validate:{ required :true}}
                                                                        <button type="button" class="btn ${
                                                                          i == 0
                                                                            ? "btn_addManufacturer"
                                                                            : "btn_removeManufacturer"
                                                                        } ">
                                                                            <span class="glyphicon ${
                                                                              i ==
                                                                              0
                                                                                ? "glyphicon-plus"
                                                                                : "glyphicon-minus"
                                                                            }" aria-hidden="true" ></span>
                                                                        </button>
                                                                    </div>-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>`;
                }
                $("#manufacturer_row").html(ManufactoryHtml);
              } else {
                // 没有回传值，页面结构不动
              }

              var extendItemList =
                vo.supplierOrganBase.supplierExtendItemVOList;
              $(".paymentSettlement [name=parentCode]").prop("checked", false);
              // console.log(extendItemList)
              var boolExtendFlag = false;
              if (extendItemList.length > 0) {
                for (var i = 0; i < extendItemList.length; i++) {
                  var parentCode = extendItemList[i].parentCode;
                  var code = extendItemList[i].code;
                  if (parentCode == 0) {
                    parentCode = code;
                  }
                  var parent = $('.parentCode[value="' + parentCode + '"]');
                  if (!parent.is(":checked")) {
                    parent.prop("checked", true);
                  }
                  if (code && code != "") {
                    var value = extendItemList[i].value;
                    var cCode = parent
                      .parents(".paymentSettlement")
                      .find('.cCode[value="' + code + '"]');
                    var cValue = cCode.parent(".childCode").find(".cValue");
                    cValue.val(value);
                  }
                  //当选中纸质银行，3月，6月 付款方式显示承兑返点,转账返点，银行，户名，账户
                  if (
                    parentCode == 1010 ||
                    parentCode == 1003 ||
                    parentCode == 1004
                  ) {
                    boolExtendFlag = true;
                  }
                }
                if (boolExtendFlag) {
                  $(".paymentNode").css("display", "block");
                } else {
                  $(".paymentNode").css("display", "none");
                }
              }
              initPaymentSettlement();

              $("#supplierBaseId").val(vo.supplierBase.id);
              $("#supplierOrganBaseId").val(vo.supplierOrganBase.id);
              $("#organBaseApplicationCode").val(data.applicationCode);
              // 客户委托书
              initTable1(vo.supplierOrganBase.id, 1);
              initTable2(vo.supplierOrganBase.id, 1);
              //供应商类别关联经营范围
              baseDataBuseScopeOrScopes();
              // 批准文件经营范围
              // table3(vo.supplierBase.id);
              table3(vo.supplierOrganBase.id);
              //批准文件
              initTable3(vo.supplierOrganBase.id, 0);
              //年度报告table初始化
              initTable4(vo.supplierOrganBase.id, 0);
              // 其他附件
              initOtherFile(vo.supplierOrganBase.id, 0);
              disable();
              $("#changeApplyBtn").show();
              $.clear_changeApply();
              apiGetOldFirstOperate();
            },
            error: function () {},
          });
        }
        disable();
        if (!$("#supplierBaseId").val()) {
          $("#supplierName").removeAttr("readonly").removeAttr("disabled");
        }
      },
    }).showModal();
    ev.stopPropagation();
  }
}

// //初始化地址选择
// $('[data-toggle="distpicker"]').distpicker();
// //添加仓库
// $(".addDepot").on("click", function () {
//     var html = '<div class="col-md-6 depotList">\
//                 <div class="input-group">\
//                     <div class="input-group-addon">仓库地址</div>\
//                     <div data-toggle="distpicker" class="form-control form-inline distpicker">\
//                         <div class="row">\
//                             <div class="form-group col-md-2">\
//                                 <select class="form-control" name="repertoryProvince"></select>\
//                             </div>\
//                             <div class="form-group col-md-2">\
//                                 <select class="form-control" name="repertoryCity"></select>\
//                             </div>\
//                             <div class="form-group col-md-2">\
//                                 <select class="form-control" name="repertoryArea"></select>\
//                             </div>\
//                             <div class="form-group col-md-4">\
//                                 <input type="text" class="form-control text-inp" name="repertoryDetail"/>\
//                             </div>\
//                             <div class="form-group btn-box col-md-1">\
//                                 <button type="button" class="btn removeDepot">\
//                                     <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>\
//                                 </button>\
//                             </div>\
//                         </div>\
//                     </div>\
//                 </div>\
//             </div>';
//     $("#depotAddress").append(html);
//     $('[data-toggle="distpicker"]').distpicker();
// });
// //删除仓库地址
// $("#depotAddress").on("click", ".removeDepot", function () {
//     $(this).parents(".depotList").remove();
// });
//批量上传
$("#appBatch").on("click", function () {});
//拼装数据
function pushData() {
  //运营属性修改
  var supplierOrganBaseApprovalRecordVO = $(
    "#supplierOrganBaseApprovalRecordVO"
  ).serializeToJSON();
  supplierOrganBaseApprovalRecordVO.baseId = $("#supplierBaseId").val(); //基础属性id
  supplierOrganBaseApprovalRecordVO.organBaseId = $(
    "#supplierOrganBaseId"
  ).val(); //运营属性id

  supplierOrganBaseApprovalRecordVO.orgCode = $("input[name='orgCode']").val();
  supplierOrganBaseApprovalRecordVO.supplierCode = $(
    "input[name='supplierCode']"
  ).val();
  supplierOrganBaseApprovalRecordVO.applicantId = $(
    "input[name='applicantId']"
  ).val();
  supplierOrganBaseApprovalRecordVO.applicationTime = $(
    "input[name='applicationTime']"
  ).val();
  supplierOrganBaseApprovalRecordVO.applicationCode = $(
    "input[name='applicationCode']"
  ).val();

  delete supplierOrganBaseApprovalRecordVO.parentCode;
  delete supplierOrganBaseApprovalRecordVO.code;
  delete supplierOrganBaseApprovalRecordVO.value;
  delete supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList;
  var supplierAllDataVO = {};

  //运营属性 付款、结算部分
  /* var supplierExtendItemVoList=[];
    $(".paymentSettlement").each(function(){
      var parentInp=$(this).find(".parentCode");
      if(parentInp.is(":checked"))
      {
          var parentCode=parentInp.attr('name');
          var parentCodeVal=parentInp.val();
          $(this).find(".childCode").each(function(index){
              var json={};
              var cCode=$(this).find(".cCode");
              var cCodeName=cCode.attr('name');
              var cCodeValue=cCode.val();
              var cValue=$(this).find(".cValue");
              var cValueName=cValue.attr('name');
              var cValueValue=cValue.val();
              if($.trim(cValueValue) != '')
              {
                  json[parentCode]=parentCodeVal;
                  json[cCodeName]=cCodeValue;
                  json[cValueName]=cValueValue;
                  supplierExtendItemVoList.push(json);
              }
          });
      }

    });
    supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList = supplierExtendItemVoList;*/
  //修改后数据拼装
  var changeApplyTemp = JSON.parse(JSON.stringify(window.changeApply));
  var supplierOrganBaseChangeRecordVOList = [];
  $.each(changeApplyTemp, function (c, v) {
    if (c == "supplierExtendItemVOList") {
      // 修改后的结算方式付款方式
      var supplierExtendItemVOList =
        changeApplyTemp.supplierExtendItemVOList.valueAfter;
      supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList = [];
      if (supplierExtendItemVOList) {
        for (var i = 0; i < supplierExtendItemVOList.length; i++) {
          var bean = supplierExtendItemVOList[i];
          if (
            !supplierExtendItemVOList[i].code ||
            supplierExtendItemVOList[i].code == undefined
          ) {
            supplierExtendItemVOList[i].code =
              supplierExtendItemVOList[i].parentCode;
            supplierExtendItemVOList[i].parentCode = 0;
          }
          supplierOrganBaseApprovalRecordVO.supplierExtendItemVOList.push(bean);
        }
      }
      // 字段变更表删除修改前后字段
      delete changeApplyTemp.supplierExtendItemVOList.valueBefore;
      delete changeApplyTemp.supplierExtendItemVOList.valueAfter;
    }
    supplierOrganBaseChangeRecordVOList.push(v);
  });
  // table等特殊数据
  var changeApplyListTemp = JSON.parse(JSON.stringify(window.changeApplyList));
  // 结算方式 付款方式

  if (changeApplyListTemp.supplierQualityAgreementList) {
    if (changeApplyListTemp.supplierQualityAgreementList.changeStatus != 0) {
      var table1 = changeApplyListTemp.supplierQualityAgreementList.valueAfter;
      for (var i = 0; i < table1.length; i++) {
        delete table1[i].id;
        if (typeof table1[i].enclosureList == "string") {
          table1[i].enclosureList = JSON.parse(table1[i].enclosureList);
        }
        for (var j = 0; j < table1[i].enclosureList.length; j++) {
          delete table1[i].enclosureList[j].type;
        }
      }
      supplierAllDataVO.supplierQualityAgreementList = table1;
    }
    var quality = {
      columnValue: "supplierQualityAgreementList",
      changeStatus:
        changeApplyListTemp.supplierQualityAgreementList.changeStatus,
    };
    supplierOrganBaseChangeRecordVOList.push(quality);
  }
  // 客户委托书
  if (changeApplyListTemp.supplierClientProxyOrderList) {
    if (changeApplyListTemp.supplierClientProxyOrderList.changeStatus != 0) {
      var table2 = changeApplyListTemp.supplierClientProxyOrderList.valueAfter;
      //客户委托书
      for (var i = 0; i < table2.length; i++) {
        delete table2[i].id;
        delete table2[i].authrange;
        if (
          null == table2[i].supplierClientProxyProductVOList ||
          table2[i].supplierClientProxyProductVOList.length < 1
        ) {
          table2[i].supplierClientProxyProductVOList = [];
        }
        if (typeof table2[i].enclosureList == "string") {
          table2[i].enclosureList = JSON.parse(table2[i].enclosureList);
        }
        for (var j = 0; j < table2[i].enclosureList.length; j++) {
          delete table2[i].enclosureList[j].type;
        }
        if (
          typeof table2[i].supplierClientProxyBusinessScopeVOList == "string"
        ) {
          table2[i].supplierClientProxyBusinessScopeVOList = JSON.parse(
            table2[i].supplierClientProxyBusinessScopeVOList
          );
        }
        if (
          typeof table2[i].supplierClientProxyBusinessScopeVOList == "string"
        ) {
          table2[i].supplierClientProxyTypeVOList = JSON.parse(
            table2[i].supplierClientProxyTypeVOList
          );
        }
      }
      supplierAllDataVO.supplierClientProxyOrderList = table2;
    }
    var proxy = {
      columnValue: "supplierClientProxyOrderList",
      changeStatus:
        changeApplyListTemp.supplierClientProxyOrderList.changeStatus,
    };
    supplierOrganBaseChangeRecordVOList.push(proxy);
  }

  // 批准文件 supplierApprovalFileList:table3,
  if (changeApplyListTemp.supplierApprovalFileList) {
    var table3 = changeApplyListTemp.supplierApprovalFileList.valueAfter;
    if (changeApplyListTemp.supplierApprovalFileList.changeStatus != 0) {
      for (var i = 0; i < table3.length; i++) {
        delete table3[i].id;
        delete table3[i].scopeofoperationVo;
        //处理经营范围数据
        var scopeList = table3[i].supplierApprovalFileBusinessScopeVOList;
        if (scopeList && scopeList.length > 0) {
          var approvalScopeList = [];
          var scopeJson = {};
          for (var x = 0; x < scopeList.length; x++) {
            if (scopeList[x].name != "经营范围") {
              scopeJson = {};
              scopeJson.businessScopeCode = scopeList[x].id;
              approvalScopeList.push(scopeJson);
            }
          }
          table3[i].supplierApprovalFileBusinessScopeVOList = approvalScopeList;
        }
        if (typeof table3[i].enclosureList == "string") {
          table3[i].enclosureList = JSON.parse(table3[i].enclosureList);
        }
        for (var j = 0; j < table3[i].enclosureList.length; j++) {
          delete table3[i].enclosureList[j].type;
        }
      }
      supplierAllDataVO.supplierApprovalFileList = table3;
    }
    var approvalFile = {
      columnValue: "supplierApprovalFileList",
      changeStatus: changeApplyListTemp.supplierApprovalFileList.changeStatus,
    };
    supplierOrganBaseChangeRecordVOList.push(approvalFile);
  }
  // 年度报告 supplierYearReportList:table4,
  if (changeApplyListTemp.supplierYearReportList) {
    var table4 = changeApplyListTemp.supplierYearReportList.valueAfter;
    if (changeApplyListTemp.supplierYearReportList.changeStatus != 0) {
      for (var i = 0; i < table4.length; i++) {
        delete table4[i].id;
        for (var j = 0; j < table4[i].enclosureList.length; j++) {
          delete table4[i].enclosureList[j].type;
        }
      }
      supplierAllDataVO.supplierYearReportList = table4;
    }
    var yearReport = {
      columnValue: "supplierYearReportList",
      changeStatus: changeApplyListTemp.supplierYearReportList.changeStatus,
    };
    supplierOrganBaseChangeRecordVOList.push(yearReport);
  }
  // 其他附件
  //supplierOtherFileList:otherFilesArr,
  if (changeApplyListTemp.supplierOtherFileList) {
    var supplierOtherFileList =
      changeApplyListTemp.supplierOtherFileList.valueAfter;
    if (changeApplyListTemp.supplierOtherFileList.changeStatus != 0) {
      supplierAllDataVO.supplierOtherFileList = supplierOtherFileList;
    }
    var otherFile = {
      columnValue: "supplierOtherFileList",
      changeStatus: changeApplyListTemp.supplierOtherFileList.changeStatus,
    };
    supplierOrganBaseChangeRecordVOList.push(otherFile);
  }

  supplierAllDataVO.supplierOrganBaseChangeRecordVOList =
    supplierOrganBaseChangeRecordVOList;
  supplierAllDataVO.supplierOrganBaseApprovalRecordVO =
    supplierOrganBaseApprovalRecordVO;

  let supplierSettleMultipleDateVOList = getMultipleDateList();
  supplierAllDataVO.supplierOrganBaseApprovalRecordVO[
    "supplierSettleMultipleDateVOList"
  ] = supplierSettleMultipleDateVOList;

  console.log(supplierAllDataVO);
  return supplierAllDataVO;
}
function changeData() {
  var changeApply = window.changeApply;
  var ary = [];
  $.each(changeApply, function (c, v) {
    ary.push(v);
  });
  return ary;
}

//新增的ajax
function insert(supplierAllDataVO) {
  var auditStatus =
    supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus;
  //检查
  var validated =validatorForm(auditStatus,supplierAllDataVO.supplierOrganBaseApprovalRecordVO,supplierAllDataVO.supplierOrganBaseChangeRecordVOList); 
  if(!validated){
    return false;
  }
  var successMsg = "保存成功！";
  // 提交审核
  if (auditStatus == 1) {
    successMsg = "恭喜提交审核成功！";
  }
  $.ajax({
    url: "/proxy-supplier/supplier/supplierOrganBaseChangeApproval/organBaseChangeSave",
    data: JSON.stringify(supplierAllDataVO),
    type: "post",
    dataType: "json",
    contentType: "application/json",

    success: function (data) {
      var status = data.result.status;
      var taskStatus = data.result.taskStatus;
      if (status != 0) {
        if (typeof taskStatus != "undefined") {
          if (taskStatus == true) {
            resetWindow();
            utils.dialog({ content: successMsg, timeout: 2000 }).showModal();
            setTimeout(function () {
              utils.closeTab();
            }, 2000);
          } else {
            // 任务流失败
            utils
              .dialog({
                title: "提示",
                content: data.result.taskMsg + "，已暂时保存为草稿",
                width: 300,
                height: 30,
                okValue: "确定",
                ok: function () {
                  utils.closeTab();
                },
              })
              .showModal();
          }
        } else {
          resetWindow();
          utils.dialog({ content: "保存成功", timeout: 2000 }).showModal();
          setTimeout(function () {
            utils.closeTab();
          }, 2000);
        }
      } else {
        resetWindow();
        // 保存失败
        utils.dialog({ content: data.result.msg, timeout: 2000 }).showModal();
      }
    },
    error: function () {
      resetWindow();
      utils.dialog({ content: "保存失败", timeout: 2000 }).showModal();
    },
    complete: function () {
      resetWindow();
      parent.hideLoading();
    },
  });
}
// 更新的ajax
function update(supplierAllDataVO, dataFrom) {
  var auditStatus =
    supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus;
    //检查
  var validated =validatorForm(auditStatus,supplierAllDataVO.supplierOrganBaseApprovalRecordVO,supplierAllDataVO.supplierOrganBaseChangeRecordVOList); 
  if(!validated){
    return false;
  }
  var successMsg = "保存成功！";
  // 提交审核
  if (auditStatus == 1) {
    successMsg = "恭喜提交审核成功！";
  }
  $.ajax({
    url: "/proxy-supplier/supplier/supplierOrganBaseChangeApproval/organBaseChangeUpdate",
    data: JSON.stringify(supplierAllDataVO),
    type: "post",
    dataType: "json",
    contentType: "application/json",
    success: function (data) {
      var status = data.result.status;
      var taskStatus = data.result.taskStatus;
      if (status != 0) {
        if (typeof taskStatus != "undefined") {
          if (taskStatus == true) {
            resetWindow();
            utils.dialog({ content: successMsg, timeout: 2000 }).showModal();
            /*if(dataFrom=='againAssert'){
                            setTimeout(function(){
                                location.href='/process/gTaskList';
                            },2000)
                        }else{*/
            setTimeout(function () {
              utils.closeTab();
            }, 2000);
          } else {
            // 任务流失败
            utils
              .dialog({
                title: "提示",
                content: data.result.taskMsg + "，已暂时保存为草稿",
                width: 300,
                height: 30,
                okValue: "确定",
                ok: function () {
                  utils.closeTab();
                },
              })
              .showModal();
          }
        } else {
          resetWindow();
          utils.dialog({ content: "保存成功", timeout: 2000 }).showModal();
          /*if(dataFrom=='againAssert'){
                        setTimeout(function(){
                            utils.closeTab();
                        },2000)
                    }else{*/
          setTimeout(function () {
            utils.closeTab();
          }, 2000);
        }
      } else {
        resetWindow();
        // 保存失败
        utils.dialog({ content: data.result.msg, timeout: 2000 }).showModal();
      }
    },
    error: function () {
      resetWindow();
      utils.dialog({ content: "保存失败", timeout: 2000 }).showModal();
    },
    complete: function () {
      resetWindow();
      parent.hideLoading();
    },
  });
}
// 通过ajax
function passAjax() {
  var supplierProcessVO = {
    auditOpinion: $("#auditOpinion").val(),
    taskId: taskId,
    id: approvalId,
  };
  $.ajax({
    type: "post",
    url: "/proxy-supplier/supplier/supplierOrganBaseChangeApproval/passOrganBaseChangeApproval",
    async: false,
    data: JSON.stringify(supplierProcessVO),
    dataType: "json",
    contentType: "application/json",
    success: function (data) {
      var status = data.result.status;
      var taskStatus = data.result.taskStatus;
      if (taskStatus == true) {
        utils.dialog({ content: "恭喜审核通过！", timeout: 2000 }).showModal();
        setTimeout(function () {
          utils.closeTab();
        }, 2000);
      } else {
        // 任务流失败
        utils
          .dialog({ content: data.result.taskMsg, timeout: 2000 })
          .showModal();
      }
    },
    error: function () {
      utils.dialog({ content: "保存失败", timeout: 2000 }).showModal();
    },
    complete: function () {
      parent.hideLoading();
    },
  });
}
// 不通过ajax
function noPassAjax() {
  /*if(!$("#auditOpinion").val()){
        parent.hideLoading();
        utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
        return false;
    }*/
  var supplierProcessVO = {
    auditOpinion: $("#auditOpinion").val(),
    taskId: taskId,
    id: approvalId,
  };
  $.ajax({
    type: "post",
    url: "/proxy-supplier/supplier/supplierOrganBaseChangeApproval/noPassOrganBaseChangeApproval",
    async: false,
    data: JSON.stringify(supplierProcessVO),
    dataType: "json",
    contentType: "application/json",
    success: function (data) {
      var status = data.result.status;
      var taskStatus = data.result.taskStatus;
      if (taskStatus == true) {
        utils.dialog({ content: "驳回成功！", timeout: 2000 }).showModal();
        setTimeout(function () {
          utils.closeTab();
        }, 2000);
      } else {
        // 任务流失败
        utils
          .dialog({ content: data.result.taskMsg, timeout: 2000 })
          .showModal();
      }
    },
    error: function () {
      utils.dialog({ content: "驳回失败！", timeout: 2000 }).showModal();
    },
    complete: function () {
      parent.hideLoading();
    },
  });
}
// 撤销
function withdraw() {
  var supplierProcessVO = {
    auditOpinion: $("#auditOpinion").val(),
    taskId: taskId,
    id: approvalId,
  };
  $.ajax({
    type: "post",
    url: "/proxy-supplier/supplier/supplierOrganBaseChangeApproval/organBaseChangeWithdraw",
    async: false,
    data: JSON.stringify(supplierProcessVO),
    dataType: "json",
    contentType: "application/json",
    success: function (data) {
      var status = data.result.status;
      var taskStatus = data.result.taskStatus;
      if (status != 0) {
        if (typeof taskStatus != "undefined") {
          if (taskStatus == true) {
            utils
              .dialog({
                title: "提示",
                content: "流程已关闭",
                width: 300,
                height: 30,
                okValue: "确定",
                ok: function () {
                  utils.closeTab();
                },
              })
              .showModal();
          } else {
            // 任务流失败
            utils
              .dialog({ content: data.result.taskMsg, timeout: 2000 })
              .showModal();
          }
        } else {
          utils
            .dialog({ content: "未知错误！", quickClose: true, timeout: 2000 })
            .showModal();
          setTimeout(function () {
            utils.closeTab();
          }, 2000);
        }
      } else {
        // 保存失败
        utils
          .dialog({ content: data.result.msg, quickClose: true, timeout: 2000 })
          .showModal();
      }
    },
    error: function () {
      utils
        .dialog({ content: "撤销失败！", quickClose: true, timeout: 2000 })
        .showModal();
    },
    complete: function () {
      parent.hideLoading();
    },
  });
}

//重置window对象
function resetWindow(){
  window.myContactPerson = false;
  window.myContactMessage1 = false;
  window.myContactMessage2 = false;
}

//关闭按钮
$("#closePage").on("click", function () {
  var d = dialog({
    title: "提示",
    content: "是否保存草稿？",
    width: 300,
    height: 30,
    okValue: "保存草稿",
    ok: function () {
      $("#saveRowData").click();
      d.close().remove();
      return false;
    },
    button: [
      {
        value: "关闭",
        callback: function () {
          resetWindow();
          // var mid = parent
          //   .$("#nav-tab li.active a")
          //   .attr("href")
          //   .replace("#", "");
          // parent.$("#mainFrameTabs").bTabsClose(mid);
          utils.closeTab();
        },
      },
    ],
  }).showModal();
});
//保存草稿
$("#saveRowData").on("click", function () {
  if (utils.allSavaBtnsHidden()) {
    return false;
  }
  able();
  parent.showLoading({ hideTime: 90000 });
  if (!$("#supplierOrganBaseId").val()) {
    disable();
    utils
      .dialog({
        content: "请选择要修改的供应商，才能继续保存",
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    $("#supplierName").removeAttr("readonly").removeAttr("disabled");
    parent.hideLoading();
  } else {
    // 状态 审核中
    var supplierAllDataVO = pushData();
    let checkSameMultiple = checkSameInput(
      supplierAllDataVO,
      supplierAllDataVO.supplierOrganBaseApprovalRecordVO
        .supplierSettleMultipleDateVOList
    );
    if (checkSameMultiple == false) {
      utils
        .dialog({
          content:
            "对账账期/实销实结里已存在相同的结算时间信息，请勿重复录入！",
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      disable();
      return false;
    }
    supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus = 0;
    if (pageType == 0) {
      // 草稿 新增
      //        	if($('#supplierTypeId').val() == '55'){
      //        		if(window.changeApply && !window.changeApply['scopes']){
      //            		parent.hideLoading();
      //            		utils.dialog({
      //            			title:'提示',
      //            			content: '生产/经营范围不能为空',
      //            			okValue: '确定',
      //            			ok: function(){}
      //            		}).showModal();
      //            		disable();
      //            		return false;
      //            	}
      //        	}
      insert(supplierAllDataVO);
    } else if (pageType == 1) {
      // 草稿再次编辑
      supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =
        $("#approvalId").val();
      update(supplierAllDataVO, "saveRowData");
    }
    disable();
  }
});
//提交审核
$("#submitAssert").on("click", function () {
  if (utils.allSavaBtnsHidden()) {
    return false;
  }
  able();
  parent.showLoading({ hideTime: 90000 });
  if (!$("#supplierOrganBaseId").val()) {
    disable();
    utils
      .dialog({
        content: "请选择要修改的供应商，才能继续保存",
        quickClose: true,
        timeout: 2000,
      })
      .showModal();
    $("#supplierName").removeAttr("readonly").removeAttr("disabled");
    parent.hideLoading();
  } else {
    // 状态 审核中sds
    var supplierAllDataVO = pushData();
    let checkSameMultiple = checkSameInput(
      supplierAllDataVO,
      supplierAllDataVO.supplierOrganBaseApprovalRecordVO
        .supplierSettleMultipleDateVOList
    );
    if (checkSameMultiple == false) {
      utils
        .dialog({
          content:
            "对账账期/实销实结里已存在相同的结算时间信息，请勿重复录入！",
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      disable();
      return false;
    }
    if (supplierAllDataVO.supplierOrganBaseChangeRecordVOList.length < 1) {
      utils
        .dialog({
          content: "请在进行过修改后，再提交审核",
          quickClose: true,
          timeout: 2000,
        })
        .showModal();
      parent.hideLoading();
      disable();
      return false;
    }
    supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus = 1;
    var supplierBase = {
      supplierName: $("#supplierName").val(),
    };
    supplierAllDataVO.supplierBase = supplierBase;
    if ($("#supplierTypeId").val() == "55") {
      if (
        window.changeApply &&
        window.changeApply["scopes"] &&
        window.changeApply["scopes"]["afterValue"] == ""
      ) {
        parent.hideLoading();
        utils
          .dialog({
            title: "提示",
            content: "生产/经营范围不能为空",
            okValue: "确定",
            ok: function () {},
          })
          .showModal();
        disable();
        return false;
      }
    }
    if (pageType == 0) {
      // 草稿 新增
      insert(supplierAllDataVO);
    } else if (pageType == 1) {
      // 草稿再次编辑
      supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =
        $("#approvalId").val();
      update(supplierAllDataVO, "submitAssert");
    }
    disable();
  }
});
//验证新数据
function validateNew(newData,key){
  for(var i=0;i<newData.length;i++){
    if(newData[i].columnValue==key){
      return true;
    }
  }
  return false;
}
//表单验证
function validatorForm(status,formData,newData) {
  //提交审核
  if(status==1){
      //业务对接人
      if (formData.contactPerson=="") {
        if(!validateNew(newData,'contactPerson')){
          utils
          .dialog({ content: '业务对接人不能为空', quickClose: true, timeout: 2000 })
          .showModal();
          return false;
        }
      }
      if (formData.contactMessage1=="") {
        if(!validateNew(newData,'contactMessage1')){
          utils
          .dialog({ content: '联系方式一不能为空', quickClose: true, timeout: 2000 })
          .showModal();
          return false;
        }
      } else {
        if(formData.contactType1==1){
          var PhoneResult = utils.validatorPhone(formData.contactMessage1);
            if (!PhoneResult) {
              utils
              .dialog({ content: '联系方式一:手机号格式不正确', quickClose: true, timeout: 2000 })
              .showModal();
              return false;
          }
        }
      }
      if (formData.contactMessage2!="" && formData.contactType2==1) {
        var PhoneResult = utils.validatorPhone(formData.contactMessage2);
        if (!PhoneResult) {
              utils
              .dialog({ content: '联系方式二:手机号格式不正确', quickClose: true, timeout: 2000 })
              .showModal();
              return false;
          }
      }
  }
  return true;
}
// 审核意见
function dialogData(title, status) {
  utils
    .dialog({
      title: title,
      content: $("#container"),
      okValue: "确定",
      ok: function () {
        parent.showLoading({ hideTime: 90000 });
        if (status == "pass") {
          passAjax();
        } else if (status == "noPass") {
          if ($("#auditOpinion").val() == "") {
            parent.hideLoading();
            utils
              .dialog({
                content: "审批意见不能为空!",
                quickClose: true,
                timeout: 2000,
              })
              .showModal();
            return false;
          }
          noPassAjax();
        }
      },
      cancelValue: "取消",
      cancel: function () {},
    })
    .showModal();
}
// 驳回编辑
function dialogAgain(title, content, status) {
  utils
    .dialog({
      title: title,
      width: 300,
      height: 30,
      content: content,
      okValue: "确定",
      ok: function () {
        parent.showLoading({ hideTime: 90000 });
        if (status == "pass") {
          able();
          // 状态 审核中
          var supplierAllDataVO = pushData();
          let checkSameMultiple = checkSameInput(
            supplierAllDataVO,
            supplierAllDataVO.supplierOrganBaseApprovalRecordVO
              .supplierSettleMultipleDateVOList
          );
          if (checkSameMultiple == false) {
            utils
              .dialog({
                content:
                  "对账账期/实销实结里已存在相同的结算时间信息，请勿重复录入！",
                quickClose: true,
                timeout: 2000,
              })
              .showModal();
            disable();
            return false;
          }
          if (
            supplierAllDataVO.supplierOrganBaseChangeRecordVOList.length < 1
          ) {
            utils
              .dialog({
                content: "请在进行过修改后，再提交审核",
                quickClose: true,
                timeout: 2000,
              })
              .showModal();
            parent.hideLoading();
            disable();
            return false;
          }
          supplierAllDataVO.supplierOrganBaseApprovalRecordVO.auditStatus = 1;
          supplierAllDataVO.supplierOrganBaseApprovalRecordVO.id =
            $("#approvalId").val();
          var supplierBase = {
            supplierName: $("#supplierName").val(),
          };
          supplierAllDataVO.supplierBase = supplierBase;
          var supplierProcessVO = {
            auditOpinion: $("#auditOpinion").val(),
            taskId: taskId,
            id: $("#approvalId").val(),
          };
          supplierAllDataVO.supplierProcessVO = supplierProcessVO;
          update(supplierAllDataVO, "againAssert");
        } else if (status == "withdraw") {
          withdraw();
        }
      },
      cancelValue: "取消",
      cancel: function () {},
    })
    .showModal();
}

//通过
$("#pass").on("click", function () {
  var title = "审核通过";
  var status = "pass";
  $("#container textarea").removeAttr("disabled");
  $("#opinion").hide();
  if ($("#supplierTypeId").val() == "55") {
    if (
      window.changeApply &&
      window.changeApply["scopes"] &&
      window.changeApply["scopes"]["afterValue"] == ""
    ) {
      parent.hideLoading();
      utils
        .dialog({
          title: "提示",
          content: "生产/经营范围不能为空",
          okValue: "确定",
          ok: function () {},
        })
        .showModal();
      disable();
      return false;
    }
  }
  // 审批意见弹窗
  dialogData(title, status);
});
//不通过
$("#noPass").on("click", function () {
  var title = "审核不通过";
  var status = "noPass";
  $("#container textarea").removeAttr("disabled");
  $("#opinion").show();
  // 审批意见弹窗
  dialogData(title, status);
});
//再次提交
$("#againAssert").on("click", function () {
  var title = "重新提交";
  var contant = "确定重新提交申请？";
  var status = "pass";
  $("#container textarea").removeAttr("disabled");
  // 再次提交审核弹窗
  dialogAgain(title, contant, status);
});
// 撤销
$("#withdraw").on("click", function () {
  var title = "关闭审核";
  var contant = "确定关闭审核？";
  var status = "withdraw";
  // 撤销弹窗
  dialogAgain(title, contant, status);
});
// 关闭
$("#close").on("click", function () {
  utils.closeTab();
});

//添加对应生产厂商
$("body").on("click", ".btn_addManufacturer", function () {
  var obj = ManufactoryHTML();
  $("#manufacturer_row").append(obj.html);
  let _id = "Manufactory" + obj.radomInt;
  utils.valAutocomplete(
    "/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",
    { paramName: "keyWord", params: { isStop: 0 } },
    _id,
    {
      data: "manufactoryId",
      value: "manufactoryName",
      brandfactoryName: "brandfactoryName",
      brandfactoryId: "brandfactoryId",
      address: "address",
    }
  );
});
//删除对应生产厂商
$("body").on("click", ".btn_removeManufacturer", function () {
  $(this).parents(".ManufactoryList").remove();
});

initQuesta()

//初始化hover提示
function initQuesta() {
  const questaOption = 
    {
      th: 'field2', //审批价差幅度,
      title: `供应商邮箱地址，用于收货异常通知到供应商`,
      width: 460,
      height: 80,
    }
  $('#questaI').mouseover(function (e) {
    $('body').append(`
                    <div id='div_tooltips'>
                        <style>
                            #div_tooltips:after{
                                content: "";
                                width: 0;
                                height: 0;
                                position: absolute;
                                left: ${questaOption.width / 2 - 10}px;
                                bottom: -10px;
                                border-left: solid 10px transparent;
                                border-top: solid 10px white;
                                border-right: solid 10px transparent;
                            }
                        </style>
                        <div id='inner_tooltips'>${questaOption.title}</div>
                    </div>
                `);
    $('#div_tooltips')
      .css({
        boxSizing: 'border-box',
        width: questaOption.width + 'px',
        height: questaOption.height + 'px',
        padding: '10px',
        zIndex: 9999,
        backgroundColor: '#ffffff',
        border: '1px solid #c4c4c4',
        position: 'absolute',
        top: $(e.target).offset().top - questaOption.height - 10 + 'px',
        left: $(e.target).offset().left + 5 - questaOption.width / 2 + 'px',
      })
      .show('fast');
  });

  $('#questaI').mouseout(function (e) {
    $('#div_tooltips').remove();
  });
}

// $(function () {
//     utils.valAutocomplete("/dict/querymanufactorynotpage",{paramName:'keyWord', params:{"isStop":0}},'Manufactory',
//         {data:"manufactoryId",value:"manufactoryName",brandfactoryName:"brandfactoryName",brandfactoryId:"brandfactoryId",address:"address"});
// })
function ManufactoryHTML(n) {
  let len = n ? n : 1;
  let html = "",
    obj = {};
  let radomInt = parseInt(Math.random() * 100) + parseInt(Math.random() * 10);
  let supplierTypeIdVal = $("#supplierTypeIdVal").val();
  for (let i = 0; i < len; i++) {
    html += `<div class="col-md-6 ManufactoryList">
                    <div class="input-group">
                        <div class="input-group-addon ">${
                          supplierTypeIdVal == "生产"
                            ? '<i class="text-require">*  </i>'
                            : ""
                        }对应生产厂商</div>
                        <div class="form-control form-inline distpicker">
                            <div>
                                <div class="form-group col-md-11">
                                    <input type="hidden" id="Manufactory${radomInt}"  name="Manufactory${radomInt}"/>
                                    <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class ${
                                      supplierTypeIdVal == "生产" ? "" : ""
                                    }"  id="Manufactory${radomInt}Val" name="Manufactory${radomInt}Val"  />
                                </div>
                                <div class="form-group btn-box col-md-1">
                                    <button type="button" class="btn btn_removeManufacturer">
                                        <span class="glyphicon glyphicon-minus" aria-hidden="true" ></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`; // {validate:{ required :true}}
  }
  obj = {
    html: html,
    radomInt: radomInt,
  };
  return obj;
}

//
function checkEdit() {
  let _bool =
    window.changeApply &&
    window.changeApply["supplierTypeId"] &&
    window.changeApply["supplierTypeId"]["valueAfter"] == "55";
  _bool = _bool || $("#supplierTypeId").val() == "55";
  return _bool;
}
