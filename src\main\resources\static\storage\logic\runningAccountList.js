let colName = [],colModel = [];
/* 合计计算  */
// var totalTable = z_utils.totalTable;
var tbIdx = '';
$(function () {
    $('.nav-tabs>li').on('click', function (){
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    });

    //页面初始化加载table
    // initTable(101);
    $('#inAcc').on('click','a',function(){
        // loadTabFrame($(this).attr("href"),$(this).attr("rel"));
        tbIdx='101';
        initTable(tbIdx);
        /*查询页面总计*/
        getTotalNum (tbIdx);
    });

    $('#outAcc').on('click','a',function(){
        // loadTabFrame($(this).attr("href"),$(this).attr("rel"));
        tbIdx='203';
        initTable(tbIdx);

        /*查询页面总计*/
        getTotalNum (tbIdx);
    });

    $('#allAcc').on('click','a',function(){
        // loadTabFrame($(this).attr("href"),$(this).attr("rel"));
        tbIdx=''
        initTable(tbIdx);
        /*查询页面总计*/
        getTotalNum (tbIdx);
    });

    $('div[fold=head]').fold({
        sub: 'sub'
    });

    /* 日期初始化 */
    z_utils.initDate('postTime', 'validateDate')
    getTotalNum (tbIdx);
    //tab 切换
    // $('.leder_tabs>li').on('click', function () {
    //     var $this = $(this), $nav_content = $this.parent().next();
    //     $this.addClass('active').siblings().removeClass('active');
    //     if ($this.index() != 2) {
    //         sessionStorage.setItem('leder_ind', '-1');
    //     }
    //     $('#table_a').XGrid('clearGridData');
    //     initTable(moveType)
    //     getTotalNum ($this.index());
    //     drugClassType = $this.index();
    // });



    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    colNames = ['移动类型','单据编号','原单据编号','逻辑批次号','商品大类','原商品编码','商品编码','商品名称','通用名','生产厂家','规格','单据时间','过账时间','往来单位编号','往来单位名称','部门名称','职员名称',
        '业务类型','批号','生产日期','有效期至','灭菌批号','入库数量','入库金额','出库数量','剩余数量','备注'];

    //, '不含税收入金额 '
    colModel = [
        {
            name: 'moveType',
            index: 'moveType'
        }, {
            name: 'orderCode',
            index: 'orderCode',
            width: 200
        }, {
            name: 'originalOrderCode',
            index: 'originalOrderCode',
            width: 200
        }, {
            name: 'logicBatch',
            index: 'logicBatch',
            width: 250
        }, {
            name: 'drugClass',
            index: 'drugClass'
        },
        {
            name: 'originalDrugCode',
            index: 'originalDrugCode'
        }, {
            name: 'drugCode',
            index: 'drugCode'
        },{
            name: 'drugName',
            index: 'drugName'
        },{
            name: 'commonName',
            index: 'commonName'
        }, {
            name: 'manufacturerName',
            index: 'manufacturerName',
            width: 260
        },{
            name: 'productSterilization',
            index: 'productSterilization',
            width: 250
        },{
            name: 'orderTime',
            index: 'orderTime'
        },{
            name: 'postTime',
            index: 'postTime'
        }, {
            name: 'intercourseNum',
            index: 'intercourseNum'
        }, {
            name: 'intercourseName',
            index: 'intercourseName',
            width: 260
        }, {
            name: 'departmentName',
            index: 'departmentName'
        },  {
            name: 'operator',
            index: 'operator'
        }, {
            name: 'channelId',
            index: 'channelId'
        }, {
            name: 'batchNum',
            index: 'batchNum'
        }, {
            name: 'productDate',
            index: 'productDate'
        },{
            name: 'validateDate',
            index: 'validateDate'
        }, {
            name: 'sterilizationBatchNum',
            index: 'sterilizationBatchNum'
        }, {
            name: 'intoAmount',
            index: 'intoAmount',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'intoSum',
            index: 'intoSum',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'outAmount',
            index: 'outAmount',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }, {
            name: 'nowAmount',
            index: 'nowAmount',
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        },{
            name: 'remark',
            index: 'remark'
        }
    ];
    $('#table_a').XGrid({
        url: "/proxy-storage/storage/logic/queryRunningAccountList",
        colNames: colNames,
        colModel: colModel,
        // rownumbers: true,//是否展示序号
        selectandorder: true,
        key: 'id',
        // rowNum: 20,
        // rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function (res) {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            // var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut','inventoryAmount','inventorySum'];
            var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
            // var lastRowEle = $(this).find("tr[nosele=true]");
            // lastRowEle.find("td:first-child").text('合计');
            // sum_models.forEach(function (item,index) {
            //     lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            // });
            $('#totalTableNum').text($('#totalPageNum').text()+'条')
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            //console.log(id, dom, obj, index, event)
        }
    });


    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        $('#table_a').XGrid('filterTableHead');
    })

    /* 商品名称 搜索提示（只显示5条） */
    var ts1 = [{
        value: 'Andorra',
        data: 'AD'
    },
        {
            value: 'Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '2Andorra',
            data: 'AD'
        },
        {
            value: '2Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '3Andorra',
            data: 'AD'
        },
        {
            value: '3Zimbabwe',
            data: 'ZZ'
        }
    ];
    $('#input_goodName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + $("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#input_goodName").val("");
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });


    //获取当前tab  的下标
    function getTabInd() {
        for (var i = 0; i < $('.nav-tabs li').length; i++) {
            if ($('.nav-tabs li').eq(i).hasClass('active')) {
                _ind = $('.nav-tabs li').eq(i).index();
            }
        }
        return _ind;
    }


    /* 查询 */
    $('#searchBtn').on('click', function () {
        //var moveType;
        tab =  $('.nav-tabs>li[class=active]').attr("id");
        if(tab=='inAcc'){
            tbIdx ='101';
        }else if (tab=='outAcc'){
            tbIdx ='203';
        }else{
            //  全部
            tbIdx ='';
        }
        initTable(tbIdx)
        getTotalNum (tbIdx);
    });
    function initTable(tbIdx){
        $('#table_a').XGrid({
            url: "/proxy-storage/storage/logic/queryRunningAccountList",
            postData: {
                moveType:$('#moveType').val(),
                drugCode:$('#drugCode').val(),
                productName:$('#productName').val(),
                oldProductCode:$('#oldProductCode').val(),
                batchNum:$('#batchNum').val(),
                logicBatch:$('#logicBatch').val(),
                intercourseName:$('#intercourseName').val(),
                intoOrderCode:$('#intoOrderCode').val(),
                drugType:$("#drugType").find("option:selected").val(),
                orderCode:$('#orderCode').val(),
                postTime:$("#postTime").val(),
                validateDate:$("#validateDate").val(),
                manufacturerName:$('#manufacturerName').val(),
                tbIdx:tbIdx,
                channelId:$("#channelId").find("option:selected").val()
            },
            colNames: colNames,
            colModel: colModel,
            altRows: true, //设置为交替行表格,默认为false
            pager: '#grid_pager_a',
            selectandorder: true,//是否展示序号，多选
            attachRow:true,
            ondblClickRow: function (id, dom, obj, index, event) {

            },
            onSelectRow: function (id, dom, obj, index, event) {
            },
            gridComplete: function (res) {
                var data = $(this).XGrid('getRowData');
                // var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut','inventoryAmount','inventorySum'];
                var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
                // var lastRowEle = $(this).find("tr[nosele=true]");
                // lastRowEle.find("td:first-child").text('合计');
                // sum_models.forEach(function (item,index) {
                //     lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                // });
                $('#totalTableNum').text($('#totalPageNum').text()+'条')
            },
        });
    };

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#table_a').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                // data = data.map(function (item, key) {
                //     var new_item = {};
                //     colName.forEach(function (val, index) {
                //         var value=item[val];
                //         if(('priceIn'==val||'priceOut'==val||'taxCostAmount'==val)&&'--'==value){
                //             value='0.00'
                //         }
                //         new_item[val] = value;
                //     })
                //     return new_item
                // })
                data = JSON.stringify(data);
            } else {
                data = '';
            }
            console.log(colName);
            //获取form数据
            var formData = {
                colName:colName,
                colNameDesc:colNameDesc,
                selectData:data,
                moveType:$('#moveType').val(),
                drugCode:$('#drugCode').val(),
                productName:$('#productName').val(),
                oldProductCode:$('#oldProductCode').val(),
                batchNum:$('#batchNum').val(),
                logicBatch:$('#logicBatch').val(),
                intercourseName:$('#intercourseName').val(),
                drugType:$("#drugType").find("option:selected").val(),
                manufacturerName:$('#manufacturerName').val(),
                orderCode:$('#orderCode').val(),
                postTime:$("#postTime").val(),
                validateDate:$("#validateDate").val(),
                tbIdx:tbIdx,
                channelId:$("#channelId").find("option:selected").val(),
            }
            var colNames= colName.join(",")
            var colNameDescs= colNameDesc.join(",")
            var formData2 = {
                moduleName: 'logicFlow',
                taskCode: '1005',
                colName: colNames,
                colNameDesc: colNameDescs,
                fileName: '逻辑批次库存流水',
                exportParams: {
                    moveType:$('#moveType').val(),
                    drugCode:$('#drugCode').val(),
                    productName:$('#productName').val(),
                    oldProductCode:$('#oldProductCode').val(),
                    batchNum:$('#batchNum').val(),
                    logicBatch:$('#logicBatch').val(),
                    intercourseName:$('#intercourseName').val(),
                    drugType:$("#drugType").find("option:selected").val(),
                    manufacturerName:$('#manufacturerName').val(),
                    orderCode:$('#orderCode').val(),
                    postTime:$("#postTime").val(),
                    validateDate:$("#validateDate").val(),
                    tbIdx:tbIdx,
                    channelId:$("#channelId").find("option:selected").val()
                }
            };
            if(typeof data == "undefined" || data == null || data == "") {
                utils.dialog({
                    title: '温馨提示',
                    content: '导出任务提交成功后页面将关闭，是否确认导出？',
                    okValue: '确定',
                    ok: function () {
                        $.ajax({
                            url: "/proxy-storage/storage/commonExport/commonCommitExportTask",
                            type: 'post',
                            dataType: 'json',
                            data: {
                                "data":JSON.stringify(formData2)
                            },
                            success: function (res) {
                                if (res) {
                                    if (res.code === 0) {
                                        utils.dialog({
                                            title: '温馨提示',
                                            content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                            okValue: '确定',
                                            ok: function () {

                                            }
                                        }).showModal()
                                    } else {
                                        utils.dialog({
                                            title: '温馨提示',
                                            content: data.msg,
                                            okValue: '确定',
                                            ok: function () {

                                            }
                                        }).showModal()
                                    }
                                }
                            }
                        });
                    }
                }).showModal()
            }else{
                httpPost("/proxy-storage/storage/logic/queryRunningAccounExport", formData);
            }
        });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#drugCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })
})
//查询总合计数目
function getTotalNum (tbIdx) {
    //加载总数量
    $.ajax({
        url: '/proxy-storage/storage/logic/selectStorageFlowViewStatisticsTotal',
        dataType: 'json',
        timeout: 10000, //6000
        data:{
            moveType:$('#moveType').val(),
            drugCode:$('#drugCode').val(),
            productName:$('#productName').val(),
            oldProductCode:$('#oldProductCode').val(),
            batchNum:$('#batchNum').val(),
            logicBatch:$('#logicBatch').val(),
            intercourseName:$('#intercourseName').val(),
            intoOrderCode:$('#intoOrderCode').val(),
            drugType:$("#drugType").find("option:selected").val(),
            manufacturerName:$('#manufacturerName').val(),
            orderCode:$('#orderCode').val(),
            postTime:$("#postTime").val(),
            validateDate:$("#validateDate").val(),
            tbIdx:tbIdx,
            channelId:$("#channelId").find("option:selected").val()
        },
        success: function (data) {
            // alert(data.code);
            if (data.code==0){
                var static = data.result;
                $("#totalAmountIn").text(Number(static.totalAmountIn).toFixed(2));
                $("#totalAmountOut").text(Number(static.totalAmountOut).toFixed(2));
            }else {
                $("#totalAmountIn").text('0.00');
                $("#totalAmountOut").text("0.00");
            }
        },
        error: function () {
            $("#totalAmountIn").text('0.00');
            $("#totalAmountOut").text("0.00");
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

function  btn_output_list(){
    utils.dialog({
        title: '导出列表',
        url: '/proxy-storage/storage/commonExport/toExportList?moduleName=logicFlow&taskCode=1005',
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
}

//加载更多
function btn_getMore() {
    var moveType;
    tab =  $('.nav-tabs>li[class=active]').attr("id");
    if(tab=='inAcc'){
        moveType ='101';
    }else if (tab=='outAcc'){
        moveType ='203';
    }else{
        //  全部
        moveType ='';
    }
    parent.showLoading()
    let rowdata = $('#table_a').XGrid('getRowData'),
        lastId = rowdata.length ? rowdata[rowdata.length-1].id: null;
    if(lastId){
        $.ajax({
            type: 'post',
            url: '/proxy-storage/storage/logic/queryRunningAccountList',
            data: {
                moveType:$('#moveType').val(),
                drugCode:$('#drugCode').val(),
                productName:$('#productName').val(),
                oldProductCode:$('#oldProductCode').val(),
                batchNum:$('#batchNum').val(),
                logicBatch:$('#logicBatch').val(),
                intercourseName:$('#intercourseName').val(),
                drugType:$("#drugType").find("option:selected").val(),
                manufacturerName:$('#manufacturerName').val(),
                channelId:$("#channelId").find("option:selected").val(),
                orderCode:$('#orderCode').val(),
                postTime:$("#postTime").val(),
                validateDate:$("#validateDate").val(),
                lastId:lastId,
                tbIdx:tbIdx,
                pageNum:$('#pg_grid_pager_a').find('input[type=number]').val() + 1,
                pageSize:20,
            },
            success: function (res) {
                if(res.result.list.length){
                    let newArr =[];
                    newArr = rowdata.concat(res.result.list);
                    $('#table_a').XGrid('clearGridData');
                    // var heji_tr = $('#table_a tr[nosele=true]').clone();
                    $('#table_a tr[nosele=true]').remove()
                    for(let i = 0; i<newArr.length; i++){
                        $('#table_a').XGrid('addRowData',newArr[i])
                    }
                    var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
                    // heji_tr.find("td:first-child").text('合计');
                    // sum_models.forEach(function (item,index) {
                    //     heji_tr.find("td[row-describedby="+item+"]").text(totalTable(newArr,item))
                    // });
                    $("#totalTableNum").text(Number(res.result.total)+'条');
                    // $('#table_a tbody').append(heji_tr)
                }else {
                    utils.dialog({
                        title: '提示',
                        content: '没有更多数据了！',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                }
            },
            error: function (err) {
                console.log(err)
            },
            complete:function () {
                parent.hideLoading();
            }

        })
    }else{
        utils.dialog({
            title: '提示',
            content: '无查询结果！',
            okValue: '确定',
            ok: function () {}
        }).showModal();
    }
}
