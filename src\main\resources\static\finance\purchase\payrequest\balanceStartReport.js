$(function () {
    /* 合计计算 */
    var totalTable = z_utils.totalTable;




    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            //console.log(result);
            $("#supplierNo").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#supplierNo").val("");

        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商双击查询
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#supplierName").val(data.supplierName);
                    $("#supplierNo").val(data.supplierCode);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }


    var colNames = ['初始化日期','供应商编码', '供应商名称', '期初余额'],
        colModel = [
            {
                name: 'createTime',
                index: 'createTime',
                width: 200,//宽度
                formatter:function (value){
                    if(value){
                        return moment(value).format('YYYY-MM-DD');
                    }else{
                        return ''
                    }
                }
            },
            {
                name: 'supplierNo',
                index: 'supplierNo',
                width: 200,//宽度
            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 60
            },
            {
                name: 'balance',
                index: 'balance',
                width: 150,
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }
        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierStartBalance',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        attachRow:true,
        postData: {
        	supplierNo: $("#supplierNo").val(),
            supplierName:$("#supplierName").val(),
            keyWord : $("#keyWord").val(),
            startDate: $("#beginTime").val(),
            endDate: $("#endTime").val()
        },
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件', e, c, a, b);
            this.returnValue = obj;
            // console.log(obj)
            //window.location.href = "/proxy-finance/finance/purchase/payrequestinfo/toDetail?payReqNO=" + obj.billNo;
            return obj;
        },
        onSelectRow: function (e, c, a, b) {
            //console.log('单机行事件', e, c, a, b);
        },

        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['balance'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
        },
        pager: '#grid-pager'
    });
    //调用统计方法
    totalSum();

    $("#searchBtn").on("click", function () {
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierStartBalance',
            postData: {
            	supplierNo: $("#supplierNo").val(),
                supplierName:$("#supplierName").val()
            }
        }).trigger('reloadGrid');
        //调用统计方法
        totalSum();
    });

    //设置显示列
    $("#setBtn").on("click", function () {
        $('#X_Table').XGrid('filterTableHead');
    });


    //导出
    $('#exportBtn').on('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            addHtmlA(colNames);
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    // var obj = $("#searchForm").serializeToJSON();
                    var obj = {
                        supplierNo: $("#supplierNo").val(),
                        supplierName:$("#supplierName").val()

                    }
                    // obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    obj["nameModel"] = nameModel;
                    httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportSupplierStartBalance", obj);
                },
    // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })
    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none; padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    //统计方法
    function totalSum() {
        $.ajax({
            url:'/proxy-finance/finance/purchase/payrequestinfo/totalSumSupplierStartBalance',
            type:'post',
            data:{
                supplierNo: $("#supplierNo").val(),
                supplierName:$("#supplierName").val()
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var static = result.result;
                   $("#totalCount").text(static.totalCount);
                    $("#totalBalance").text(Number(static.totalBalance).toFixed(2));
                }
            }
        })
    }
})