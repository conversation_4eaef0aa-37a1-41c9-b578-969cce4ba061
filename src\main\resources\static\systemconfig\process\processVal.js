/**
 * 获取工作流的名称
 * @param key
 */
function getProcessName(key, obj) {
    var name = "";
    switch (key) {
        case "storageOwnerTransferApply":
            name = "业务类型转移申请";
            break;
        case "purchasePriceListOffer":
            name = "采购价目表报价";
            break;
        case "purchasePriceListModify":
            name = "采购价目表报变更";
            break;
        case "purchasePriceListCancel":
            name = "采购价目表报作废";
            break;
        case "dictPurchaseSortManagement":
            name = "商品采购分类管理申请";
            break;
        case "purchaseOrderA":
        case "purchaseOrderB":
        case "majorCustomerPurchaseOrderApply":
        case "keyrCustomerPurchaseOrder":
        case "purchaseInternalTransaction":
        case "focusPurchaseInternalTransaction":
        case "focusPurchaseOrder":
        case "purchaseOrdeOffLine":
        case "newFocusPurchaseInternalTransaction":
        case "orgOEMPurchaseOrder":
        case "focusPurchaseInternalTransactionTTTC":
        case "purOrderTransferFM":
            name = "采购订单申请";
            if (obj.number.indexOf('JGJHD') != -1) {
                name = "采购计划单申请";
            }
            break;
        case "focusPurchaseInternalTransactionGroup":
        case "OEMInternalTransaction":
            name = "调拨计划单";
            break;
        case "focusPurchaseOrderGroup":
        case "enablePurchaseOrder":
        case "OEMPurchaseOrder":
            name = "采购计划单";
            break;
        case "purchaseRetrieveHead":
            name = "退补价计划单";
            break;
        case "purchasePlanOrder":
            name = "华润采购直联订单申请";
            break;
        case "purchaseReturn":
        case "purchaseReturnOffLine":
        case "unqualified_purchaseReturn":
        case "orgOEMReturnOrder":
        case "unqualified_orgOEMReturnOrder":
            name = "采购退货申请";
            break;
        case "purchaseRetrieve":
        case "purchaseRetrieveOffLine":
            name = "采购退补价申请";
            break;
        case "purchasewayOrder":
        case "keyrCustomerPurchasewayOrder":
        case "purchasewayOrderOffLine":
            name = "采购订单关闭申请";
            break;
        case "majorCustomerPurchaseOrderReturnApply":
            name = "采购大客户退货申请";
            break;
        case "focusPurchaseReturnGroup":
        case "enableReturnOrder":
        case "OEMReturnOrder":
        case "unqualified_focusPurchaseReturnGroup":
        case "unqualified_enableReturnOrder":
        case "unqualified_OEMReturnOrder":
            name = "退货计划单";
            break;
        case "orderSalesReturn":
            name = "销售退回申请";
            break;
        case "salesReturnOrder":
            name = "销售退货收货审批";
            break;
        case "orderCommodityProfitAndLoss":
            name = "商品损溢申请";
            break;
        case "orderAccountRegulation":
            name = "调账单申请";
            break;
        case "orderDamaged":
            name = "不合格品报损申请";
            break;
        case "orderDisDrugsToDestroy":
            name = "不合格药品销毁申请";
            break;
        case "unqualifiedDestructionWmsApply":
            name = "不合格品销毁申请-普罗格";
            break;
        case "unqualifiedReportLossWmsApply":
            name = "不合格品报损申请-普罗格";
            break;
        case "storageProductLossApply":
            name = "损益申请单";
            break;
        case "storageProductSpillApply":
            name = "库存商品损溢申请";
            break;
        case "productFirst":
        case "productFirstIgnore":
        case "majorCustomerProductFirstIgnore":
            name = "商品首营申请";
            break;
        case "productUpdate":
            name = "商品资料变更申请";
            break;
        case "productPropUpdate":
        case "productPropUpdateHead":
            name = "商品运营属性变更申请";
            break;
        case "productPriceUpdate":
        case "productPriceUpdateHead":
        case "focusProductPriceUpdate":
        case "priceAdjust0":
        case "priceAdjust1":
            name = "商品调价申请";
            break;
        case "focusProductPriceUpdateHead":
            name = "集采商品调价申请";
            break;
        case "productBidPrice":
            name = "商品标价申请";
            break;
        case "productLimit":
            name = "商品停用申请";
            break;
        case "productFactoryUpdate":
            name = "商品包装管理";
            break;
        case "productUpdates":
            name = "商品上架申请";
            break;
        case "focusProductUpdates":
            name = "集采商品上架申请";
            break;
        case "productOffLoading":
        case "productOffLoadingHead":
            name = "商品下架申请";
            break;
        case "focusProductOffLoading":
            name = "集采商品下架申请";
            break;
        case "productTaxRelatedChangeApply":
            name = "商品涉税变更申请";
            break;
        case "productQualityAttrChange":
            name = "商品质管属性变更申请";
            break;
        case "productManagePropertyApply":
        case "productManagePropertyHeadApply":
            name = "商品管理属性变更申请";
            break;
        case "productCuringPropertyApply":
            name = "商品养护属性变更申请";
            break;
        case "productSuggTermPriceAdjm":
        case "productSuggTermPriceAdjmHead":
        case "terminalPriceAdjust":
            name = "商品建议终端售价调整申请";
            break;
        case "supplierFirstOne":
        case "supplierFirstTwo":
        case "majorCustomerSupplierFirstOne":
            name = "供应商首营申请";
            break;
        case "supplierChange":
            name = "供应商资料变更申请";
            break;
        case "supplierUpdate":
            name = "供应商运营属性变更申请";
            break;
        case "supplierStopTwo":
        case "supplierStopOne":
        case "supplierFinancialAudit":
            name = "供应商停用申请";
            break;
        case "supplierLockingErpApply":
            name = "供应商锁定申请";
            break;
        case "supplierQualificationChange":
            name = "供应商资质变更申请";
            break;
        case "customerFirstErp":
        case "customerFirstCampApply":
        case "customerFirst2crm":
        case "majorCustomerCustomerFirst":
        case "customerFirst2Self":
        case "platformCustomerFirst2Crm":
        case "platformCustomerFirst2Self":
        case "erpAutoCustomerFirst2Self":
            name = "客户首营申请";
            break;
        case "customerInfoModifyApply":
        case "customerInfoModifyERPApply":
        case "customerInfoModify2crm":
        case "customerInfoModify2Self":
        case "platformCustomerInfoModify2Crm":
        case "platformCustomerInfoModify2Self":
        case "platformCustomerInfoModifyERPApply":
        case "erpAutoCustomerInfoModify2Self":
            name = "客户资料变更申请";
            break;
        case "customerStopUseQCApply":
        case "customerStopErpApply":
        case "platformCustomerStopErpApply":
        case "erpAutoCustomerStopApply":
            name = "客户停用申请";
            break;
        case "customerLockingErpApply":
            name = "客户锁定申请";
            break;
        case "purchaseOrderAdvance":
        case "majorCustomerPurchaseOrderAdvance":
        case "focusPurchaseOrderAdvance":
        case "oemPurchasePayRequest":
        case "provincePrePaymentCenterFlow":
        case "provincePrePaymentDisperseFlow":
        case "centerPrePaymentFlow":
        case "keyCustomerPurchaseOrderAdvance":
        case "provincePrePaymentDisperseFlowExcess":
        case "provincePrePaymentCenterFlowExcess":
        case "centerPrePaymentFlowExcess":
        case "provincePaymentRejectFlow":
            name = "采购订单付款申请";
            break;
        case "purchaseOrderUnadvance":
        case "majorCustomerPurchaseOrderUnadvance":
        case "focusPurchaseOrderUnadvance":
            //        case "oemPurchasePayRequest":
        case "provincePaymentDisperseFlow":
        case "provincePaymentCenterFlow":
        case "centerPaymentFlow":
        case "keyCustomerPurchaseOrderUnadvance":
            name = "采购发票付款申请";
            break;
        case "carrierApply":
            name = "承运单位申请";
            break;

        case 'orderInternalTransaction': //被下面两个替换
        case 'orderInternalLocalPurchaseAllocate':
        case 'orderInternalCentralPurchaseAllocate':
        case 'orderInternalCentralTotalPurchaseAllocate':
        case 'orderInternalCentralPartPurchaseAllocate':
        case 'orderSalesOrder':
            name = '销售订单申请'; //销售内部调拨单
            break;
        case 'focusProductFirst':
            name = '商品集中首营申请';
            break;
        case 'oemFocusProductFirst':
            name = 'OEM商品集中首营申请';
            break;
        case 'brandFacturerAdd':
            name = '品牌厂家新增申请';
            break;
        case 'brandFacturerUpdate':
            name = '品牌厂家状态变更申请';
            break;
        case 'gatherRelationship':
            name = '归拢关系维护申请';
            break;
        case 'manufacturerAdd':
            name = '生产厂家新增申请';
            break;
        case 'manufacturerUpdate':
            name = '生产厂家状态变更申请';
            break;
        case 'salesControl':
            name = '控销单审核';
            break;
        case 'productGroupSalesArea':
            name = '商品销售域配置集团申请';
            break;
        case 'productOrgSalesArea':
            name = '商品销售域配置分公司申请';
            break;
        case 'pbillFiDepositApply':
            name = '返利预存申请单';
            break;
        case 'pbillFiDepositKeepAccountApply':
            name = '返利预存下账申请单';
            break;
        case 'pbillFiRebateKeepAccountApply':
            name = '返利下账申请单';
            break;
        case 'productReview':
            name = '商品评审';
            break;
        case 'productIntroduce':
            name = '新品引入';
            break;
        case 'pbillFiRebateKeepAccountShareApply':
            name = '商品实收分摊申请';
            break;
        case 'pbillFiDepositBackApply':
            name = '返利预存反冲申请单';
            break;
        case 'pbillFiDepositKeepAccountBackApply':
            name = '返利预存下账反冲申请单';
            break;
        case 'pbillFiRebateKeepAccountBackApply':
            name = '返利下账反冲申请单';
            break;
        case 'advance_pay_request':
            name = '提前付款申请';
            break;
        case 'purchaseOrderZl':
            name = '荷叶智慧采购订单智鹿';
            break;
        case 'purchaseOrderHy':
            name = '荷叶智慧采购订单荷叶';
            break;
        case 'purchaseOrderZy':
            name = '荷叶智慧采购订单自营';
            break;
        case 'purchasePlanOrderZl':
            name = '荷叶智慧采购计划单智鹿';
            break;
        case 'purchasePlanOrderHy':
            name = '荷叶智慧采购计划单荷叶';
            break;
        case 'purchasePlanOrderZy':
            name = '荷叶智慧采购计划单自营';
            break;
        default:
            name = '不存在';
            break;
    }
    return name;
}

/**
 * 获取工作流审核跳转页面url
 * @param key
 */
function getProcessUrl(key, obj) {
    var url = '';
    var number = obj.number
    switch (key) {
        case 'storageOwnerTransferApply':
            url = '/proxy-storage/storage/StorageOwner/apply/toInfo?orderCode=' + number + '&type=1';//审批
            break;
        case 'purchasePriceListOffer':
            url = '/#/stock/priceList/quotePrice/detail';
            break;
        case 'purchasePriceListModify':
            url = '/#/stock/priceList/modify/detail';
            break;
        case 'purchasePriceListCancel':
            url = '/#/stock/priceList/destroy/detail';
            break;
        case 'dictPurchaseSortManagement':
            // 商品采购分类管理申请
            url = '/proxy-sysmanage/sysmanage/purchaseSortManagement/processedDetailToList';
            break;
        case 'purchaseOrderA':
        case 'purchaseOrderB':
        case 'majorCustomerPurchaseOrderApply':
        case 'keyrCustomerPurchaseOrder':
        case 'purchaseInternalTransaction':
        case 'focusPurchaseInternalTransaction':
        case 'focusPurchaseOrder':
        case 'purchaseOrdeOffLine':
        case 'newFocusPurchaseInternalTransaction':
        case 'orgOEMPurchaseOrder':
        case 'focusPurchaseInternalTransactionTTTC':
        case 'purchaseOrderZl':
        case 'purchaseOrderHy':
        case 'purchaseOrderZy':
        case 'purOrderTransferFM':
            
            url = '/proxy-purchase/purchase/purchaseOrder/toProcessApproveDetail';
            break;
        case 'focusPurchaseInternalTransactionGroup':
        case 'OEMInternalTransaction':
            url = '/proxy-purchase/purchase/allocatingPlanOrder/toOrdeProcessApproveDetail';
            break;
        case 'purchasePlanOrder':
            url = '/proxy-purchase/purchase/purchasePlanOrder/toProcessApproveDetail';
            break;
        case 'focusPurchaseOrderGroup':
        case 'enablePurchaseOrder':
        case 'OEMPurchaseOrder':
            // 采购计划单
            url = '/proxy-purchase/purchase/groupPlanOrder/toProcessApproveDetail';

            break;
        case 'focusPurchaseReturnGroup':
        case 'enableReturnOrder':
        case 'OEMReturnOrder':
        case 'unqualified_focusPurchaseReturnGroup':
        case 'unqualified_enableReturnOrder':
        case 'unqualified_OEMReturnOrder':
            // 退货计划单
            url = '/proxy-purchase/purchase/groupRefundPlanOrder/toProcessApproveDetail';
            break;
        case 'purchaseRetrieveHead':
            // 退补价计划单
            url = '/proxy-purchase/purchase/groupPlanRefundPriceOrder/toProcessApproveDetail';
            break;
        case 'purchaseReturn':
        case 'purchaseReturnOffLine':
        case 'unqualified_purchaseReturn':
        case 'orgOEMReturnOrder':
        case 'unqualified_orgOEMReturnOrder':
            // 采购退货申请
            url = '/proxy-purchase/purchase/purchaseRefundProductOrder/toProcessApproveDetail';
            break;
        case 'purchaseRetrieve':
        case 'purchaseRetrieveOffLine':
            // 采购退补价申请
            url = '/proxy-purchase/purchase/purchaseRefundPriceOrder/toProcessApproveDetail';
            break;
        case 'purchasewayOrder':
        case 'keyrCustomerPurchasewayOrder':
        case 'purchasewayOrderOffLine':
            // 采购订单关闭申请
            url = '/proxy-purchase/purchase/purchaseOrder/toWayOrdeProcessApproveDetail';
            break;
        case 'majorCustomerPurchaseOrderReturnApply':
            // 大客户采购退货申请
            url = '/proxy-purchase/purchase/purchaseRefundProductOrder/toProcessApproveDetail';
            break;
        case 'orderSalesReturn':
            // 销售退回申请
            url = '/proxy-order/order/orderReturn/orderReturnController/toApproval';
            break;
        case 'salesReturnOrder':
            // 销售退货收货审批
            url = '/proxy-order/order/orderReturn/orderReturnController/detail?salesReturnCode=' + number + '&type=1';
            break;
        case 'orderCommodityProfitAndLoss':
            // 商品损溢申请
            url = '/proxy-storage/storage/lossApply/lossApplyController/toApproval';
            break;
        case 'orderAccountRegulation':
            // 调账单申请
            url = '/proxy-storage/storage/adjustment/toApprove';
            break;
        case 'orderDisDrugsToDestroy':
            // 不合格药品销毁申请
            url = '/proxy-storage/storage/unqualifiedDestory/toAudit';
            break;
        case 'orderDamaged':
            // 不合格品报损申请
            url = '/proxy-storage/storage/unqualifiedLoss/unqualifiedLossController/toApproval';
            break;
        case 'unqualifiedDestructionWmsApply':
            // 不合格品销毁申请-WMS
            url = '/proxy-storage/storage/unqualifiedDestructionApply/toApply';
            break;
        case 'unqualifiedReportLossWmsApply':
            // 不合格品报损申请-WMS
            url = '/proxy-storage/storage/loss/toLossVerifyPage';
            break;
        case 'storageProductLossApply':
            // 库存商品报损申请
            url = '/proxy-storage/storage/lossApply/lossApplyController/toApproval';
            break;
        case 'storageProductSpillApply':
            // 库存商品报溢申请
            url = '/proxy-storage/storage/lossApply/lossApplyController/toApproval';
            break;
        case 'productFirst':
        case 'productFirstIgnore':
        case 'majorCustomerProductFirstIgnore':
            // 商品首营申请
            url = '/proxy-product/product/productFirst/toApplyDetail';
            break;
        case 'productUpdate':
            // 商品资料变更申请
            url = '/proxy-product/product/baseApproval/auditBaseApproval';
            break;
        case 'productPropUpdate':
        case 'productPropUpdateHead':
            // 商品运营属性变更申请
            url = '/proxy-product/product/orgApproval/auditOrgApproval';
            break;
        case 'productPriceUpdate':
        case 'productPriceUpdateHead':
        case 'focusProductPriceUpdate':
        case 'focusProductPriceUpdateHead':
        case 'priceAdjust0':
        case 'priceAdjust1':
            // 商品调价申请(priceAdjust0为地采,priceAdjust1为集采)







            url = '/proxy-product/product/adjustPrice/audiAdjustPrice';
            break;
        case 'productBidPrice':
            // 商品标价申请
            url = '/proxy-product/product/adjustPrice/audiAdjustPrice';
            break;
        case 'productLimit':
            // 商品停用申请
            url = '/proxy-product/product/purchaseLimit/audit/toAudit';
            break;
        case 'productFactoryUpdate':
            // 商品包装属性修改
            url = '/proxy-product/product/orgApproval/auditPackingManage';
            break;
        case 'productUpdates':
        case 'focusProductUpdates':
            // 商品上架申请
            url = '/proxy-product/product/upperShelf/toAudit';
            break;
        case 'productOffLoading':
        case 'productOffLoadingHead':
        case 'focusProductOffLoading':
            // 商品下架申请
            url = '/proxy-product/product/lowerShelf/toAudit';
            break;
        case 'productTaxRelatedChangeApply':
            // 商品涉税变更申请
            url = '/proxy-product/product/baseApproval/auditRateManage';
            break;
        case 'productQualityAttrChange':
            // 商品质管属性变更申请
            url = '/proxy-product/product/quality/audit';
            break;
        case 'productManagePropertyApply':
        case 'productManagePropertyHeadApply':
            // 商品管理属性变更申请
            url = '/proxy-product/product/orgApproval/auditProductManage';
            break;
        case 'productCuringPropertyApply':
            // 商品养护属性变更申请
            url = '/proxy-product/product/orgApproval/auditProductCuring';
            break;
        case 'productSuggTermPriceAdjm':
        case 'productSuggTermPriceAdjmHead':
        case 'terminalPriceAdjust':
            // 商品建议终端售价调整申请
            url = '/proxy-product/product/adjustPrice/audiAdjustPrice';
            break;
        case 'supplierFirstOne':
        case 'supplierFirstTwo':
        case 'majorCustomerSupplierFirstOne':
            // 供应商首营申请
            url = '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/detail/first';
            break;
        case 'supplierChange':
            // 供应商资料变更申请
            url = '/proxy-supplier/supplier/supplierBaseChangeApproval/toSupplierBaseChange';
            break;
        case 'supplierUpdate':
            // 供应商运营属性变更申请
            url =
                '/proxy-supplier/supplier/supplierOrganBaseChangeApproval/toSupplierOrganBaseChange';
            break;
        case 'supplierStopTwo':
        case 'supplierFinancialAudit':
        case 'supplierStopOne':
            // 供应商停用申请
            url =
                '/proxy-supplier/supplier/supplierDisableApprovalRecord/supplierDisableApprovalExamine';
            break;
        case 'supplierLockingErpApply':
            // 供应商锁定申请
            url = '/proxy-supplier/supplier/supplierLockApprovalRecord/supplierLockApprovalExamine';
            break;
        case 'supplierQualificationChange':
            // 供应商资质变更申请
            url =
                '/proxy-supplier/supplier/supplierQualificationChangeController/toSupplierQualificationChange';
            break;
            // 平台客户审核流程
        case 'platformCustomerFirst2Crm':
        case 'platformCustomerFirst2Self':
            url = '/#/supply/platfromCustomer/customerAuditDetail';
            break;
            // 平台客户变更审核流程
        case 'platformCustomerInfoModify2Crm':
        case 'platformCustomerInfoModify2Self':
        case 'platformCustomerInfoModifyERPApply':
            url = '/#/supply/platfromCustomer/customerManageDetail';
            break;
            // 平台客户停用审核流程
        case 'platformCustomerStopErpApply':
            url = '/#/supply/platfromCustomer/customerStopUsingDetail';
            break;
            // 客户首营申请
        case 'customerFirstCampApply':
        case 'customerFirstErp':
        case 'customerFirst2crm':
        case 'majorCustomerCustomerFirst':
        case 'customerFirst2Self':
            url = '/proxy-customer/customer/customerFirstAppl/detail';
            break;
            // 客户资料变更申请
        case 'customerInfoModifyApply':
            url = '/proxy-customer/customer/change/approval/detail';
            break;
            // 客户资料变更申请-ERP
        case 'customerInfoModifyERPApply':
        case 'customerInfoModify2crm':
        case 'customerInfoModify2Self':
            url = '/proxy-customer/customer/change/approval/toCustomerBaseChange';
            break;
            // 客户停用申请
        case 'customerStopUseQCApply':
        case 'customerStopErpApply':
            url = '/proxy-customer/customer/disuse/toDetailedSkip';
            break;
            // 客户锁定申请
        case 'customerLockingErpApply':
            url = '/proxy-customer/customer/customerLock/customerLockApprovalExamine';
            break;
        case 'purchaseOrderAdvance':
        case 'purchaseOrderUnadvance':
        case 'majorCustomerPurchaseOrderAdvance':
        case 'majorCustomerPurchaseOrderUnadvance':
        case 'focusPurchaseOrderAdvance':
        case 'oemPurchasePayRequest':
        case 'focusPurchaseOrderUnadvance':
        case 'keyCustomerPurchaseOrderAdvance':
        case 'keyCustomerPurchaseOrderUnadvance':
        case 'provincePrePaymentCenterFlow':
        case 'provincePrePaymentDisperseFlow':
        case 'centerPrePaymentFlow':
        case 'provincePaymentDisperseFlow':
        case 'provincePaymentCenterFlow':
        case 'centerPaymentFlow':
        case "provincePrePaymentDisperseFlowExcess":
        case "provincePrePaymentCenterFlowExcess":
        case "centerPrePaymentFlowExcess":
        case "provincePaymentRejectFlow":
            // 采购订单付款申请
            url = '/#/finance/purchase/newrequestV3';
            break;
        case 'carrierApply':
            // 新增承运单位申请
            url = '/proxy-gsp/gsp/transport/toCarrierApprove';
            break;
        case 'orderInternalTransaction': //被下面两个替换
        case 'orderInternalLocalPurchaseAllocate':
        case 'orderInternalCentralPurchaseAllocate':
        case 'orderInternalCentralTotalPurchaseAllocate':
        case 'orderInternalCentralPartPurchaseAllocate':
            // 销售内部调拨单
            url = '/proxy-order/order/salesOrder/toApproval';
            break;
        case 'orderSalesOrder':
            // 销售内部订单
            url =
                '/proxy-order/order/salesOrder/toInfo?ECOrderCode=' + obj.businessId;
            break; 
        case 'focusProductFirst':
            // 商品集采首营申请
            url = '/proxy-product/product/productFirstJiCai/toApplyDetail';
            break;
        case 'oemFocusProductFirst':
            // OEM商品集采首营申请
            url = '/proxy-product/product/productFirstJiCai/toApplyDetail';
            break;
        case 'brandFacturerAdd':
        case 'brandFacturerUpdate':
            // 品牌厂家新增申请
            // 品牌厂家状态变更申请
            url = '/#/supply/monufactor/brandmanufacturers/approvaldetails';
            break;
        case 'manufacturerAdd':
        case 'manufacturerUpdate':
            // 生产厂家新增申请
            // 生产厂家状态变更申请
            url = '/#/supply/monufactor/manufacturer/approvaldetails';
            break;
        case 'gatherRelationship':
            // 归拢关系维护申请
            url = '/#/supply/monufactor/relationship/approvaldetails';
            break;
        case 'salesControl':
            // 销控审批流程
            url = '/#/supply/product/saleControl/audit';
            if (obj.edit == 1 && obj.close == 1) {
                url = '/#/supply/product/saleControl/modify';
            }
            break;
        case 'productGroupSalesArea':
            url = '/#/supply/goods/goodsSellApprove';
            break;
        case 'productOrgSalesArea':
            url = '/#/supply/goods/goodsSellApprove';
            break;
        case 'pbillFiDepositApply':
            url = '/#/supply/rebateBill/rebatePrestoreApprove?from=taskCenterApprove';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiDepositKeepAccountApply':
            url = '/#/supply/rebateBill/approveRebateBill?from=taskCenterApprove';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiDepositBackApply':
            // 返利预存反冲申请单
            url = '/#/supply/rebateBill/rebatePrestoreApprove?from=taskCenterApprove';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiRebateKeepAccountApply':
            url = '/#/supply/rebateBill/approveRebateBill?from=taskCenterApprove';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiDepositKeepAccountBackApply':
            // 返利预存下账反冲申请单
            url = '/#/supply/rebateBill/approveRebateBill?from=taskCenterApprove';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiRebateKeepAccountBackApply':
            // 返利下账反冲申请单
            url = '/#/supply/rebateBill/approveRebateBill?from=taskCenterApprove';
            url = url + '&id=' + obj.businessId;
            break;
        case 'productReview':
            // 商品评审申请
            url =
                '/#/supply/mechanism/commodityReviewExamine?businessId=' +
                obj.businessId;
            if (obj.edit == 1) {
                url =
                    '/#/supply/mechanism/commodityReviewResubmit?businessId=' +
                    obj.businessId;
            }
            break;
        case 'productIntroduce':
            // 新品引入
            url = '/#/supply/goodsImport/goodsImportApply?from=check';
            if (obj.edit == 1) {
                url = '/#/supply/goodsImport/goodsImportApply?from=edit';
            }
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiRebateKeepAccountShareApply':
            // 商品实收分摊申请
            url =
                '/#/supply/rebateBill/groupReceiptAllocationApprove?from=taskCenterApprove';
            url = url + '&id=' + obj.businessId;
            break;
        case 'advance_pay_request':
            // 提前付款申请
            url = '/#/finance/purchase/advancePayrequestCheck?from=taskCenterApprove';
            url = url + '&earlierPayrequestOrderNo=' + obj.number + '&approve=true';
            break;
        default:
            url = '';
            break;
    }
    if (
        key === 'platformCustomerFirst2Crm' ||
        key === 'platformCustomerFirst2Self' ||
        key === 'platformCustomerStopErpApply'
    ) {
        url =
            url +
            '?businessId=' +
            obj.businessId +
            '&taskId=' +
            obj.taskId +
            '&processId=' +
            obj.approvalProcessId +
            '&edit=' +
            obj.edit +
            '&close=' +
            obj.close +
            '&agree=' +
            obj.agreed +
            '&reject=' +
            obj.reject +
            '&report=' +
            obj.report +
            '&flag=1';
    } else if (
        key === 'platformCustomerInfoModifyERPApply' ||
        key === 'platformCustomerInfoModify2Self' ||
        key === 'platformCustomerInfoModify2Crm'
    ) {
        url =
            url +
            '?businessId=' +
            obj.businessId +
            '&taskId=' +
            obj.taskId +
            '&processId=' +
            obj.approvalProcessId +
            '&edit=' +
            obj.edit +
            '&close=' +
            obj.close +
            '&agree=' +
            obj.agreed +
            '&reject=' +
            obj.reject +
            '&report=' +
            obj.report +
            '&flag=1' +
            '&applicationNumber=' +
            obj.number;
    } else if (
        key === 'pbillFiDepositApply' ||
        key === 'pbillFiDepositKeepAccountApply' ||
        key === 'pbillFiRebateKeepAccountApply' ||
        key === 'productIntroduce' ||
        key === 'pbillFiRebateKeepAccountShareApply' ||
        key === 'pbillFiDepositBackApply' ||
        key === 'pbillFiDepositKeepAccountBackApply' ||
        key === 'pbillFiRebateKeepAccountBackApply' ||
        key === 'advance_pay_request'
    ) {
        url =
            url +
            '&businessId=' +
            obj.businessId +
            '&taskId=' +
            obj.taskId +
            '&processId=' +
            obj.approvalProcessId +
            '&edit=' +
            obj.edit +
            '&close=' +
            obj.close +
            '&agree=' +
            obj.agreed +
            '&reject=' +
            obj.reject +
            '&report=' +
            obj.report;
    } else if (key === 'productReview') {
        url = url + '&taskId=' + obj.taskId;
    } else if (obj.number.indexOf("JGJHD") != -1) {
        url = '/#/supply/purchasePlan/purchasePlanDetail?orderPlanNo=' + obj.number + "&pageType=1"
    } else if(key === 'purchaseOrderAdvance' || key === 'purchaseOrderUnadvance' || key === 'majorCustomerPurchaseOrderAdvance' || 
            key === 'majorCustomerPurchaseOrderUnadvance' || key === 'focusPurchaseOrderAdvance' || key === 'oemPurchasePayRequest'
            || key === 'focusPurchaseOrderUnadvance' || key === 'keyCustomerPurchaseOrderAdvance' || key === 'keyCustomerPurchaseOrderUnadvance'
            || key === 'provincePrePaymentCenterFlow' || key === 'provincePrePaymentDisperseFlow' || key === 'centerPrePaymentFlow' 
            || key === 'provincePaymentDisperseFlow' || key === 'provincePaymentCenterFlow' || key === 'centerPaymentFlow'
            || key === 'provincePrePaymentDisperseFlowExcess' || key === 'provincePrePaymentCenterFlowExcess' || key === 'centerPrePaymentFlowExcess' || key === 'provincePaymentRejectFlow'){
                url = url + '?billNo=' + obj.number + '&type=2&processId=' + obj.approvalProcessId + '&taskId=' + obj.taskId
    }else if(key === 'orderSalesOrder'){
        url = url + '&taskId=' +
            obj.taskId +
            '&processId=' +
            obj.approvalProcessId +
            '&edit=' +
            obj.edit +
            '&close=' +
            obj.close +
            '&agree=' +
            obj.agreed +
            '&reject=' +
            obj.reject +
            '&report=' +
            obj.report;
    }else{
        url =
            url +
            '?businessId=' +
            obj.businessId +
            '&taskId=' +
            obj.taskId +
            '&processId=' +
            obj.approvalProcessId +
            '&edit=' +
            obj.edit +
            '&close=' +
            obj.close +
            '&agree=' +
            obj.agreed +
            '&reject=' +
            obj.reject +
            '&report=' +
            obj.report;
    }

    // 价目表页面需要额外增加参数
    if (
        key === 'purchasePriceListOffer' ||
        key === 'purchasePriceListModify' ||
        key === 'purchasePriceListCancel' ||
        key === 'salesControl'
    ) {
        url = url + '&itemNo=' + obj.number; // 价目表单据号
        url = url + '&isFromProcess=true'; // 是否来源于审批流
    }
    return url;
}

/**
 * 获取工作流查看跳转页面url
 * @param key
 */
function getProcessLookUrl(key, obj) {
    var url = '';
    var businessId = obj.businessId;
    var number = obj.number
    switch (key) {
        case 'storageOwnerTransferApply':
            url = '/proxy-storage/storage/StorageOwner/apply/toInfo?orderCode=' + number + '&type=2';//审批
            break;
        case 'purchasePriceListOffer':
            url = '/#/stock/priceList/quotePrice/detail';
            url =
                url +
                '?businessId=' +
                obj.businessId +
                '&taskId=' +
                obj.taskId +
                '&processId=' +
                obj.approvalProcessId +
                '&itemNo=' +
                obj.number +
                '&isFromProcess=true';
            break;
        case 'purchasePriceListModify':
            url = '/#/stock/priceList/modify/detail';
            url =
                url +
                '?businessId=' +
                obj.businessId +
                '&taskId=' +
                obj.taskId +
                '&processId=' +
                obj.approvalProcessId +
                '&itemNo=' +
                obj.number +
                '&isFromProcess=true';
            break;
        case 'purchasePriceListCancel':
            url = '/#/stock/priceList/destroy/detail';
            url =
                url +
                '?businessId=' +
                obj.businessId +
                '&taskId=' +
                obj.taskId +
                '&processId=' +
                obj.approvalProcessId +
                '&itemNo=' +
                obj.number +
                '&isFromProcess=true';
            break;
        case 'dictPurchaseSortManagement':
            // 商品采购分类管理申请
            url =
                '/proxy-sysmanage/sysmanage/purchaseSortManagement/processedDetailToList?businessId=' +
                obj.businessId +
                '&processId=' +
                obj.approvalProcessId +
                '&close=0&agree=0&reject=0';
            break;
        case 'purchaseOrderA':
        case 'purchaseOrderB':
        case 'majorCustomerPurchaseOrderApply':
        case 'keyrCustomerPurchaseOrder':
        case 'purchaseInternalTransaction':
        case 'focusPurchaseInternalTransaction':
        case 'focusPurchaseOrder':
        case 'purchaseOrdeOffLine':
        case 'newFocusPurchaseInternalTransaction':
        case 'orgOEMPurchaseOrder':
        case 'focusPurchaseInternalTransactionTTTC':
        case 'purchasePlanOrderZy':
        case 'purchasePlanOrderHy':
        case 'purchasePlanOrderZl':
        case 'purchaseOrderZl':
        case 'purchaseOrderHy':
        case 'purchaseOrderZy':
        case 'purOrderTransferFM':
            // 采购订单申请

            url =
                '/proxy-purchase/purchase/purchaseOrder/toDetail?purchaseId=' + businessId + '&type=0';
            if (number.indexOf('JGJHD') != -1) {
                url = '/#/supply/purchasePlan/purchasePlanDetail?orderPlanNo=' + number
            }
            break;
        // case 'purPlanOrder312TransferFrom315OEM':
        //     url = '/#/supply/purchasePlan/purchasePlanDetail?orderPlanNo=' + number
        //     break;
        case 'focusPurchaseInternalTransactionGroup':
        case 'OEMInternalTransaction':
            // 调拨计划单
            url =
                '/proxy-purchase/purchase/allocatingPlanOrder/toOrdeProcessApproveDetail?businessId=' +
                businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'focusPurchaseOrderGroup':
        case 'enablePurchaseOrder':
        case 'OEMPurchaseOrder':
            // 采购计划单
            url =
                '/proxy-purchase/purchase/groupPlanOrder/toProcessApproveDetail?businessId=' +
                businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'focusPurchaseReturnGroup':
        case 'enableReturnOrder':
        case 'OEMReturnOrder':
        case 'unqualified_focusPurchaseReturnGroup':
        case 'unqualified_enableReturnOrder':
        case 'unqualified_OEMReturnOrder':
            // 退货计划单
            url =
                '/proxy-purchase/purchase/groupRefundPlanOrder/toProcessApproveDetail?businessId=' +
                businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'purchaseRetrieveHead':
            // 退补价计划单
            url =
                '/proxy-purchase/purchase/groupPlanRefundPriceOrder/toProcessApproveDetail?businessId=' +
                businessId +
                '&type=0';
            break;
        case 'purchasePlanOrder':
            // 华润采购直联订单
            url =
                '/proxy-purchase/purchase/purchasePlanOrder/toDetail?purchaseId=' +
                businessId +
                '&type=0';
            break;
        case 'purchaseReturn':
        case 'purchaseReturnOffLine':
        case 'unqualified_purchaseReturn':
        case 'orgOEMReturnOrder':
        case 'unqualified_orgOEMReturnOrder':
            // 采购退货申请
            url =
                '/proxy-purchase/purchase/purchaseRefundProductOrder/toDetail?purchaseId=' +
                businessId +
                '&type=0&refundType=0';
            break;
        case 'purchaseRetrieve':
        case 'purchaseRetrieveOffLine':
            // 采购退补价申请
            url =
                '/proxy-purchase/purchase/purchaseRefundPriceOrder/toDetail?purchaseId=' +
                businessId +
                '&type=0';
            break;
        case 'purchasewayOrder':
        case 'keyrCustomerPurchasewayOrder':
        case 'purchasewayOrderOffLine':
            // 采购订单关闭申请
            url =
                '/proxy-purchase/purchase/purchaseOrder/toWayOrdeDetail?purchaseId=' +
                businessId +
                '&type=0';
            break;
        case 'majorCustomerPurchaseOrderReturnApply':
            // 大客户采购退货申请
            url =
                '/proxy-purchase/purchase/purchaseRefundProductOrder/toDetail?purchaseId=' +
                businessId +
                '&type=0&refundType=1';
            break;
        case 'orderSalesReturn':
            // 销售退回申请
            url =
                '/proxy-order/order/orderReturn/orderReturnController/detail?salesReturnCode=' +
                businessId;
            break;
        case 'salesReturnOrder':
            // 销售退回申请
            url =
                '/proxy-order/order/orderReturn/orderReturnController/detail?salesReturnCode=' +
                businessId;
            break;
        case 'orderCommodityProfitAndLoss':
            // 商品损溢申请
            url =
                '/proxy-storage/storage/lossApply/lossApplyController/toProfitAndLossApplyDetailList?damageRequestCode=' +
                businessId;
            break;
        case 'orderDisDrugsToDestroy':
            // 不合格药品销毁申请
            url =
                '/proxy-storage/storage/unqualifiedDestory/toUnqualifiedDestoryDetailList?damageDestroyCode=' +
                businessId;
            break;
        case 'orderDamaged':
            // 不合格品报损申请
            url =
                '/proxy-storage/storage/unqualifiedLoss/unqualifiedLossController/toUnqualifiedLossDetailListcheck?damageRequestCode=' +
                businessId;
            break;
        case 'orderAccountRegulation':
            // 调账单申请
            url = '/proxy-storage/storage/adjustment/toInfo?id=' + businessId;
            break;
        case 'unqualifiedDestructionWmsApply':
            // 不合格品销毁申请-WMS
            url =
                '/proxy-storage/storage/unqualifiedDestructionApply/findDetil?unqualifiedDestructionApplyCode=' +
                businessId;
            break;
        case 'unqualifiedReportLossWmsApply':
            // 不合格品报损申请-WMS
            url = '/proxy-storage/storage/loss/toOrderRequestDetailList?businessId=' + businessId;
            break;
        case 'storageProductLossApply':
            // 库存商品报损申请
            url =
                '/proxy-storage/storage/lossApply/lossApplyController/toProfitAndLossApplyDetailList?damageRequestCode=' +
                businessId;
            break;
        case 'storageProductSpillApply':
            // 库存商品报溢申请
            url =
                '/proxy-storage/storage/lossApply/lossApplyController/toProfitAndLossApplyDetailList?damageRequestCode=' +
                businessId;
            break;
        case 'productFirst':
        case 'productFirstIgnore':
        case 'majorCustomerProductFirstIgnore':
            // 首营商品申请
            url = '/proxy-product/product/productFirst/toDetail?productId=' + businessId;
            break;
        case 'productUpdate':
            // 商品资料变更申请
            url = '/proxy-product/product/baseApproval/baseApprovalDetail?businessId=' + businessId;
            break;
        case 'productPropUpdate':
        case 'productPropUpdateHead':
            // 商品运营属性变更申请
            url = '/proxy-product/product/orgApproval/orgApprovalDetail?businessId=' + businessId;
            break;
        case 'productPriceUpdate':
        case 'productPriceUpdateHead':
        case 'focusProductPriceUpdate':
        case 'focusProductPriceUpdateHead':
        case 'priceAdjust0':
        case 'priceAdjust1':
            // 商品调价申请






            url = '/proxy-product/product/adjustPrice/adjustPriceDetail?businessId=' + businessId;
            break;
        case 'productBidPrice':
            // 商品标价申请
            url = '/proxy-product/product/adjustPrice/adjustPriceDetail?businessId=' + businessId;
            break;
        case 'productLimit':
            // 商品停用申请
            url = '/proxy-product/product/purchaseLimit/audit/toDetail?businessId=' + businessId;
            break;
        case 'productFactoryUpdate':
            // 商品包装属性修改
            url = '/proxy-product/product/orgApproval/packingManageDetail?businessId=' + businessId;
            break;
        case 'productUpdates':
        case 'focusProductUpdates':
            // 商品上架申请
            url = '/proxy-product/product/upperShelf/toUpperShelfDetail?id=' + businessId;
            break;
        case 'productOffLoading':
        case 'productOffLoadingHead':
        case 'focusProductOffLoading':
            // 商品下架申请
            url = '/proxy-product/product/lowerShelf/lowerShelfDetail?id=' + businessId;
            break;
        case 'productTaxRelatedChangeApply':
            // 商品涉税变更申请
            url = '/proxy-product/product/quality/detail?businessId=' + businessId;
            break;
        case 'productQualityAttrChange':
            // 商品质管属性变更申请
            url = '/proxy-product/product/quality/detail?businessId=' + businessId;
            break;
        case 'productManagePropertyApply':
        case 'productManagePropertyHeadApply':
            // 商品管理属性变更申请
            url = '/proxy-product/product/orgApproval/productManageDetail?businessId=' + businessId;
            break;
        case 'productCuringPropertyApply':
            // 商品养护属性变更申请
            url = '/proxy-product/product/orgApproval/productCuringDetail?businessId=' + businessId;
            break;
        case 'productSuggTermPriceAdjm':
        case 'productSuggTermPriceAdjmHead':
        case 'terminalPriceAdjust':
            // 商品建议终端售价调整申请
            url = '/proxy-product/product/adjustPrice/adjustPriceDetail?businessId=' + businessId;
            break;
        case 'supplierFirstOne':
        case 'supplierFirstTwo':
        case 'majorCustomerSupplierFirstOne':
            // 供应商首营申请
            url =
                '/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/detail/first?businessId=' +
                businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'supplierChange':
            // 供应商资料变更申请
            url =
                '/proxy-supplier/supplier/supplierBaseChangeApproval/querySupplierBaseChangeApprovalDetail?id=' +
                businessId;
            break;
        case 'supplierUpdate':
            // 供应商运营属性变更申请
            url =
                '/proxy-supplier/supplier/supplierOrganBaseChangeApproval/querySupplierOrganBaseChangeApprovalDetail?id=' +
                businessId;
            break;
        case 'supplierStopTwo':
        case 'supplierFinancialAudit':
        case 'supplierStopOne':
            // 供应商停用申请
            url =
                '/proxy-supplier/supplier/supplierDisableApprovalRecord/querySupplierDisableApprovalRecord?id=' +
                businessId;
            break;
        case 'supplierLockingErpApply':
            // 供应商锁定申请
            url =
                '/proxy-supplier/supplier/supplierLockApprovalRecord/querySupplierLockApprovalRecord/acPage?id=' +
                businessId;
            break;
        case 'supplierQualificationChange':
            // 供应商资质变更申请
            url =
                '/proxy-supplier/supplier/supplierQualificationChangeController/querySupplierQualificationChangeDetail?id=' +
                businessId;
            break;
            // 平台客户审核流程
        case 'platformCustomerFirst2Crm':
        case 'platformCustomerFirst2Self':
            url = '/#/supply/platfromCustomer/customerAuditDetail';
            break;
            // 平台客户变更审核流程
        case 'platformCustomerInfoModify2Crm':
        case 'platformCustomerInfoModify2Self':
        case 'platformCustomerInfoModifyERPApply':
            url = '/#/supply/platfromCustomer/customerManageDetail';
            break;
            // 平台客户停用审核流程
        case 'platformCustomerStopErpApply':
            url = '/#/supply/platfromCustomer/customerStopUsingDetail';
            break;
        case 'customerFirstCampApply':
        case 'customerFirstErp':
        case 'customerFirst2crm':
        case 'majorCustomerCustomerFirst':
        case 'customerFirst2Self':
        case 'erpAutoCustomerFirst2Self':
            // 客户首营申请
            url = '/proxy-customer/customer/customerFirstAppl/detail?businessId=' + businessId;
            break;
        case 'customerInfoModifyApply':
            // 客户资料变更申请
            url = '/proxy-customer/customer/change/approval/detail?businessId=' + businessId;
            break;
        case 'customerInfoModifyERPApply':
        case 'customerInfoModify2crm':
        case 'customerInfoModify2Self':
        case 'erpAutoCustomerInfoModify2Self':
            // 客户资料变更申请-ERP
            url =
                '/proxy-customer/customer/change/approval/queryBaseChangeApprovalDetail?id=' +
                businessId;
            break;
        case 'customerStopUseQCApply':
        case 'customerStopErpApply':
        case 'erpAutoCustomerStopApply':
            // 客户停用申请
            url = '/proxy-customer/customer/disuse/toDetailedSkip?id=' + businessId;
            break;
        case 'customerLockingErpApply':
            // 客户锁定申请
            url =
                '/proxy-customer/customer/customerLock/customerLockApprovalExamine?id=' + businessId;
            break;
        case 'purchaseOrderAdvance':
        case 'purchaseOrderUnadvance':
        case 'majorCustomerPurchaseOrderAdvance':
        case 'majorCustomerPurchaseOrderUnadvance':
        case 'focusPurchaseOrderAdvance':
        case 'oemPurchasePayRequest':
        case 'focusPurchaseOrderUnadvance':
        case 'keyCustomerPurchaseOrderAdvance':
        case 'provincePaymentDisperseFlow':
        case 'provincePaymentCenterFlow':
        case 'centerPaymentFlow':
        case 'provincePrePaymentCenterFlow':
        case 'provincePrePaymentDisperseFlow':
        case 'centerPrePaymentFlow':
        case 'keyCustomerPurchaseOrderUnadvance':
        case "provincePrePaymentDisperseFlowExcess":
        case "provincePrePaymentCenterFlowExcess":
        case "centerPrePaymentFlowExcess":
        case "provincePaymentRejectFlow":
            // 采购订单付款申请
            url =
                '/#/finance/purchase/newrequestV3?billNo=' +
                obj.number + '&type=2';
            break;
        case 'carrierApply':
            // 新增承运单位
            url = '/proxy-gsp/gsp/transport/toCarrierDetail?carrierCode=' + businessId;
            break;
        case 'orderInternalTransaction': //被下面两个替换
        case 'orderInternalLocalPurchaseAllocate':
        case 'orderInternalCentralPurchaseAllocate':
        case 'orderInternalCentralTotalPurchaseAllocate':
        case 'orderInternalCentralPartPurchaseAllocate':
        case 'orderSalesOrder':
            // 销售内部订单
            url =
                '/proxy-order/order/salesOrder/toInfo?ECOrderCode=' +
                businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'focusProductFirst':
            // 集采首营商品申请
            url = '/proxy-product/product/productFirstJiCai/toDetail?productId=' + businessId;
            break;
        case 'oemFocusProductFirst':
            // 集采首营商品申请
            url = '/proxy-product/product/productFirstJiCai/toDetail?productId=' + businessId;
            break;
        case 'brandFacturerAdd':
        case 'brandFacturerUpdate':
            // 品牌厂家新增申请
            // 品牌厂家状态变更申请
            url =
                '/#/supply/monufactor/brandmanufacturers/approvaldetails?businessId=' +
                obj.businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'manufacturerAdd':
        case 'manufacturerUpdate':
            // 生产厂家新增申请
            // 生产厂家状态变更申请
            url =
                '/#/supply/monufactor/manufacturer/approvaldetails?businessId=' +
                obj.businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'gatherRelationship':
            // 归拢关系维护申请
            url =
                '/#/supply/monufactor/relationship/approvaldetails?businessId=' +
                obj.businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'salesControl':
            // 销控审批流程
            url =
                '/#/supply/product/saleControl/detail?businessId=' +
                obj.businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'productGroupSalesArea':
            url =
                '/#/supply/goods/goodsSellApprove?businessId=' +
                obj.businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'productOrgSalesArea':
            url =
                '/#/supply/goods/goodsSellApprove?businessId=' +
                obj.businessId +
                '&processId=' +
                obj.approvalProcessId;
            break;
        case 'pbillFiDepositApply':
            url = '/#/supply/rebateBill/rebatePrestoreDetail?from=taskCenterDetail';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiDepositBackApply':
            // 返利预存反冲申请单
            url = '/#/supply/rebateBill/rebatePrestoreDetail?from=taskCenterDetail';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiDepositKeepAccountApply':
            url = '/#/supply/rebateBill/detailRebateBill?from=taskCenterDetail';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiDepositKeepAccountBackApply':
            // 返利预存下账反冲申请单
            url = '/#/supply/rebateBill/detailRebateBill?from=taskCenterDetail';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiRebateKeepAccountBackApply':
            // 返利下账反冲申请单
            url = '/#/supply/rebateBill/detailRebateBill?from=taskCenterDetail';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiRebateKeepAccountApply':
            url = '/#/supply/rebateBill/detailRebateBill?from=taskCenterDetail';
            url = url + '&id=' + obj.businessId;
            break;
        case 'productReview':
            // 商品评审申请
            url =
                '/#/supply/mechanism/commodityReviewDetails?businessId=' +
                obj.businessId;
            break;
        case 'productIntroduce':
            // 新品引入详情
            url = '/#/supply/goodsImport/goodsImportApply?from=detail';
            url = url + '&id=' + obj.businessId;
            break;
        case 'pbillFiRebateKeepAccountShareApply':
            // 商品实收分摊申请
            url =
                '/#/supply/rebateBill/groupReceiptAllocationDetail?from=taskCenterDetail';
            url = url + '&id=' + obj.businessId;
            break;
        case 'advance_pay_request':
            // 提前付款申请
            url = '/#/finance/purchase/advancePayrequestCheck?from=taskCenterDetail';
            url = url + '&earlierPayrequestOrderNo=' + obj.number + '&approve=true';
            break;
        default:
            url = '';
            break;
    }

    if (
        key === 'platformCustomerFirst2Crm' ||
        key === 'platformCustomerFirst2Self' ||
        key === 'platformCustomerStopErpApply'
    ) {
        url =
            url +
            '?businessId=' +
            obj.businessId +
            '&taskId=' +
            obj.taskId +
            '&processId=' +
            obj.approvalProcessId +
            '&edit=' +
            obj.edit +
            '&close=' +
            obj.close +
            '&agree=' +
            obj.agreed +
            '&reject=' +
            obj.reject +
            '&report=' +
            obj.report +
            '&flag=0';
    } else if (
        key === 'platformCustomerInfoModifyERPApply' ||
        key === 'platformCustomerInfoModify2Self' ||
        key === 'platformCustomerInfoModify2Crm' ||
        key === 'pbillFiDepositBackApply' ||
        key === 'pbillFiDepositKeepAccountBackApply' ||
        key === 'pbillFiRebateKeepAccountBackApply'
    ) {
        url =
            url +
            '?businessId=' +
            obj.businessId +
            '&taskId=' +
            obj.taskId +
            '&processId=' +
            obj.approvalProcessId +
            '&edit=' +
            obj.edit +
            '&close=' +
            obj.close +
            '&agree=' +
            obj.agreed +
            '&reject=' +
            obj.reject +
            '&report=' +
            obj.report +
            '&flag=0' +
            '&applicationNumber=' +
            obj.number;
    }
    return url;
}
/**
 * 
 * @param { string } approvalProcessKey_hidden 
 * @param { Object } rData 
 * @param { "look" | "look_copy" | "apply" | "claim" } type 
 * @returns { Promise<{ status: boolean, url: '' }> }
 */
function getProcessUrlSpecial(approvalProcessKey_hidden, rData, type) {
    //调价新老单据判断
    return new Promise((resolve, reject) => {
        if (['productPriceUpdate',
                'productPriceUpdateHead',
                'focusProductPriceUpdate',
                'focusProductPriceUpdateHead',
                'priceAdjust0',
                'priceAdjust1'
            ].includes(approvalProcessKey_hidden)) {
            $.ajax({
                url: `/proxy-product/product/adjustPrice/audi/details?pageNum=1&pageSize=0&sort=asc&recordId=${rData.businessId}`,
                type: "GET",
                success: function(res) {
                    if (res.result && res.result.list && res.result.list.length > 0) {
                        if (res.result.list[0].newFlg == 1) {
                            //新单据
                            let url = "";
                            if (type == "look" || type == "look_copy") {
                                url = `/#/supply/product/priceAdjustment/detail?businessId=${rData.businessId}`
                            } else {
                                url = `/#/supply/product/priceAdjustment/verify?businessId=${rData.businessId}&taskId=${rData.taskId}&processId=${rData.approvalProcessId}&edit=${rData.edit}&close=${rData.close}&agree=${rData.agree}&reject=${rData.reject}&report=${rData.report}`;
                            }
                            resolve({ status: true, url: url })
                        } else {
                            resolve({ status: false, url: '' })
                        }
                    } else {
                        resolve({ status: false, url: '' })
                    }
                },
                error: function() {
                    resolve({ status: false, url: '' })
                }
            })
        } else {
            resolve({ status: false, url: '' })
        }
    })
}