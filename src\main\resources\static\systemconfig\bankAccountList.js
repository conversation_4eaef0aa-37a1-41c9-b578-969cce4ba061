    $(function () {
        $('#X_Table').XGrid({
			url : "/proxy-sysmanage/sysmanage/bankAccount/listPage",
			mtype : "POST",
			postData : {
				status : $("#bank_account_status").val(),
				orgCode : $("#bank_account_con_org").val()
			},
            colNames: ['银行名称', '银行账号', '所属机构', '状态', '操作人', '操作时间'],
            colModel: [
            {
                name: 'bankName'
            }, {
                name: 'bankAccount'
            }, {
                name: 'bankOrgName'
            }, {
                name: 'status',
                formatter: function (e, d, rData) {
                	if ("true" == String(rData.status)) {
                		return "生效";
					} else {
						return "失效";
					}
                }
            }, {
                name: 'updateUser',
            }, {
                name: 'updateTime',
                formatter : dateFormatter
            }, {
				name : 'bankAccountId',
				hidden: true,
                formatter: function (e, d, rData) {
					return rData.id;
                }
			}, {
				name : 'orgCode',
				hidden: true,
                formatter: function (e, d, rData) {
					return rData.orgCode;
                }
			}, {
				name : 'bank_status',
				hidden: true,
                formatter: function (e, d, rData) {
					return rData.status;
                }
			} ],
            rowNum: 20,
            rowList:[20,50,100],
            rownumbers: true,
            altRows: true, //设置为交替行表格,默认为false
            pager: '#grid-pager',
            ondblClickRow: function (id, dom, obj, index, event) {
                //双击事件回调函数
            },
            onSelectRow: function (id, dom, obj, index, event) {
                //选中事件
                //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            }
        });
    });
    
// 查询按钮事件
$("#searchBtn").click(function() {
	$('#X_Table').XGrid('setGridParam', {
		url : "/proxy-sysmanage/sysmanage/bankAccount/listPage",
		mtype : "POST",
		postData : {
			status : $("#bank_account_status").val(),
			orgCode : $("#bank_account_con_org").val()
		}
	}).trigger('reloadGrid');
});

// 修改
$('#editBtn').bind('click', function () {
	// 检测是否能够修改所属机构
	$("#bank_account").removeAttr("disabled");
	$("#bank_org").removeAttr("disabled");
	var selRow = $('#X_Table').XGrid('getSeleRow');
	$('#hideInput').show();
    if (selRow) {
    	$("#bank_name").val(selRow.bankName);
    	$("#bank_account").val(selRow.bankAccount);
    	$("#bank_status").val(selRow.bank_status);
    	$("#bank_org").val(selRow.orgCode);
    	if (checkBankAccountIsUse(selRow)) {
    		$("#bank_account").attr("disabled", true);
    		$("#bank_org").attr("disabled", true);
    	}
        utils.dialog({
            title: '修改银行账号',
            content: $('#modal'),
            width: 450,
            okValue: '确认',
            cancelValue: '取消',
            cancel: true,
            ok: function () {
                if (!validform("myForm").form()) {
                    return false;
                }
                if ($("#bank_name").val() == '') {
                    utils.dialog({
                        content: '不能为空',
                        quickClose: true,
                        timeout: 2000
                    }).showModal();
                    return false;
                }
                var reg = /^[a-zA-Z0-9\u4e00-\u9fa5-_]+$/;
                if (!reg.test($("#bank_name").val())) {
                    utils.dialog({
                        content: '特殊字符仅支持“-”，“_”',
                        quickClose: true,
                        timeout: 2000
                    }).showModal();
                    return false;
                }
                $.ajax({
                	url : "/proxy-sysmanage/sysmanage/bankAccount/saveBank",
                    type: "POST",
                    data: {
                    	id: selRow.id,
                    	bankName: $("#bank_name").val(),
                    	bankAccount: $("#bank_account").val(),
                    	status: $("#bank_status").val()
                    },
                    success: function (result) {
                    	if (result.result) {
    						// 刷新当前列表
                    		$('#X_Table').trigger('reloadGrid');
                    	    $("#bank_name").val("");
                    	    $("#bank_account").val("");
                    	    $("#bank_status").val("");
                    	    $("#bank_org").val("-1");
                    	    $("#bank_org").removeAttr("disabled");
                    	    $("#bank_account").removeAttr("disabled");
    					} else {
    						utils.dialog({content: result.msg, quickClose: true, timeout: 2000}).showModal();
    					}
                    }
                });
            }
        }).show();
    } else {
        utils.dialog({
            content: '没有选中任何行！',
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
});

// 新增
$('#addBtn').bind('click', function () {
	$('#hideInput').hide();
    $("#bank_name").val("");
    $("#bank_account").val("");
    $("#bank_status").val("");
    $("#bank_org").val("-1");
    $("#bank_org").removeAttr("disabled");
    $("#bank_account").removeAttr("disabled");
    utils.dialog({
        title: '银行账号设置',
        content: $('#modal'),
        width: 450,
        okValue: '确认',
        cancelValue: '取消',
        cancel: true,
        ok: function () {
            if (!validform("myForm").form()) {
                return false;
            }
            if ($("#bank_name").val() == '') {
                utils.dialog({
                    content: '不能为空',
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                return false;
            }
            if ($("#bank_org").val() == '-1') {
            	utils.dialog({
            		content: '所属机构不能为空',
            		quickClose: true,
            		timeout: 2000
            	}).showModal();
            	return false;
            }
            var reg = /^[a-zA-Z0-9\u4e00-\u9fa5-_]+$/;
            if (!reg.test($("#bank_name").val())) {
                utils.dialog({
                    content: '特殊字符仅支持“-”，“_”',
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                return false;
            }
            $.ajax({
            	url : "/proxy-sysmanage/sysmanage/bankAccount/saveBank",
                type: "POST",
                data: {
                	bankName: $("#bank_name").val(),
                	bankAccount: $("#bank_account").val(),
                	orgCode: $("#bank_org").val()
                },
                success: function (result) {
                	if (result.code == 0) {
						// 刷新当前列表
                		$('#X_Table').trigger('reloadGrid');
                	    $("#bank_name").val("");
                	    $("#bank_account").val("");
                	    $("#bank_status").val("");
                	    $("#bank_org").val("-1");
					} else {
						utils.dialog({content: result.msg, quickClose: true, timeout: 2000}).showModal();
					}
                }
            });
        }
    }).show();
});      
        
 // 时间格式化
function dateFormatter(val) {
	if (val != null && val != "") {
		return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
	} else {
		return "";
	}
};

// 检测账号的所属机构是否能够更改
function checkBankAccountIsUse(obj) {
	var bank_account = obj.bankAccount;
	var ret_obj = null;
    $.ajax({
    	url : "/proxy-sysmanage/sysmanage/bankAccount/checkBankAccountIsUse",
        type: "POST",
        async: false,
        data: {
        	bankAccount: bank_account
        },
        success: function (result) {
        	ret_obj = result.result;
        }
    });
	return ret_obj;
};