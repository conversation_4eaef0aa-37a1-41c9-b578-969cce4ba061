$(function () {

    // tabs 切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $('.nav-content');

        var selRow = $('#X_Table').XGrid('getSeleRow');
        if ($(this).index() == '0') {
            //先判断已选采购单据明细，本次开票数量为 [1，小包装数量-已开票数量] 区间的整数
            var seleAry = $('#X_Table1').XGrid('getSeleRow');
            seleAry = seleAry?[].concat(seleAry):[];
            var errorAry = [];
            var str ="商品编号为:";
            $.each(seleAry,function (key,item) {
                if(item.moveTypeCode !="105" && item.moveTypeCode !="107"){
                    if(!(0 < item.thisInvoicedCount && item.thisInvoicedCount <= (item.productPackInStoreCount - item.invoicedCount) && item.thisInvoicedCount%1 === 0)){
                        str += '<br/>' + item.productCode+","+item.productName;
                        errorAry.push(item.productCode)
                    }
                }

            });
            str +="<br/>的商品,本次开票数量有误,请修改.<br/><span style='color:#a7a6a6'>（注意:本次开票数量为大于0且小于等于'小包装数量-已开票数量'的整数）</span>";
            if(errorAry.length > 0){
                utils.dialog({
                    title:"提示",
                    width:300,
                    content:'<div style="max-height: 500px;overflow-y: auto">'+str+'</div>',
                    okValue: '确定',
                    ok: function () {

                    }
                }).showModal();
                return false;
            } else {
                countTicketMoney(selRow);
            }
        } else {
            $("#productStatus").val(1);
            $("#input_goodName").val("");
            initDetailTableDialog(selRow);
        }

        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass(
            'active');
    });

    //dialog显示入库单明细
    function initDetailTableDialog(selRow) {
        var tempArr =[] ;
        if (selRow) {
            selRow = [].concat(selRow);
            tempArr = selRow.map(function (item,key) {
                return {
                    moveTypeCode:item.moveTypeCode,
                    stockOrderNo:item.stockOrderNo,
                    productEntryTax:item.productEntryTax,
                }
            })
        }
        $('#X_Table1').setGridParam({
            url: '/proxy-finance/finance/purchase/invoice/findNewStoreInDetail',
            mtype:'POST',
            postData: {
                storeInListStr:JSON.stringify(tempArr),
                "drugCode": $("#input_goodName").val(),
                "searchType": $("#productStatus").val()
            },
        }).trigger('reloadGrid');
    }

    //计算剩余本次开票金额
    function countTicketMoney (selRow) {
        //当前选中的单据
        selRow = selRow?[].concat(selRow):[];
        //明细表数据
        var tableRow1 = $('#X_Table1').XGrid('getSeleRow');
        tableRow1 = tableRow1?[].concat(tableRow1):[];
        //明细本次开票合计计算
        var count = {};
        $.each(tableRow1,function (key,item) {
            if(count[item.stockOrderNo]){
                count[item.stockOrderNo] += (item.productContainTaxMoney - 0)
            }else {
                count[item.stockOrderNo] = (item.productContainTaxMoney - 0)
            }
        });
        //更改单据列表本次开票数
        $.each(selRow,function (key,item) {
            if(count[item.stockOrderNo]){
                $('#X_Table').XGrid('setRowData',item.id,{thisTicketMoney:count[item.stockOrderNo]});
            }else {
                $('#X_Table').XGrid('setRowData',item.id,{thisTicketMoney:'0.00'})
            }
        });
    }

    // 采购入库单列表
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/purchase/invoice/findStoreIn',
        postData: {
            "starttime": $("#starttime").val(),
            "endtime": $("#endtime").val(),
            "moveTypeCode": $("#moveTypeCode").val()
        },
        colNames: ['id','采购订单号',  '采购单据号','业务类型', '采购单据状态', '供应商编码', '供应商', '移动类型', '制单人', '单据日期','本次开票金额','未开票金额', '金额合计', '税额合计', '税率', '价税合计', '移动类型code'],
        colModel: [
            {
                name: 'id',
                hidden: true,
                hidegrid: true
            },{
                name: 'orderNo'
            }, {
                name: 'stockOrderNo'
            }, {
                name: 'channelName'
            }, {
                name: 'ticketStatus',
                formatter: function (value) {
                    if (value) {
                        if (value == '0' || value == null) {
                            return '未开发票';
                        } else if (value == '1') {
                            return '部分开票';
                        } else if (value == '2') {
                            return '已开票';
                        } else {
                            return '';
                        }
                    } else {
                        return '';
                    }
                },
                unformat: function (value) {
                    if (value) {
                        if (value == '未开发票') {
                            return '0';
                        } else if (value == '部分开票') {
                            return '1';
                        } else if (value == '已开票') {
                            return '2';
                        }
                    } else {
                        return '';
                    }
                }
            }, {
                name: 'supplierCode'
            }, {
                name: 'supplierName'
            }, {
                name: 'moveTypeName'
            }, {
                name: 'createUser'
            }, {
                name: 'storeTime'
            }, {
                name: 'thisTicketMoney',
                formatter: function(val,a,b,c){
                    if(!val){
                        return '0.00'
                    }else if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')) {
                        return '-' + parseFloat(val).formatMoney('2', '', ',', '.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }

                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }

            }, {
                name: 'noTicketMoney',
                formatter: function(val,a,b,c){
                    if(!val){
                        return '0.00'
                    }else if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')) {
                        return '-' + parseFloat(val).formatMoney('2', '', ',', '.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }

                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }

            }, {
                name: 'sumNoTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }

            }, {
                name: 'sumTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'productEntryTax'
            }, {
                name: 'priceTaxSum',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'moveTypeCode',
                hidden: true,
                hidegrid: true
            }, {
                name: 'channelId',
                hidden: true,
                hidegrid: true
            }],

        rowNum: 20,
        rowList: [20, 50, 100],
        rownumbers: true,//是否展示序号
        altRows: true, //设置为交替行表格,默认为false
        // key: 'id',
        multiselect: true,
        ondblClickRow: function (e, c, a, b) {
            $("#input_goodName").val('');
            initTableDialog(a.moveTypeCode, a.stockOrderNo,a.productEntryTax);
            utils.dialog({
                title: '选择采购单据明细行',
                width: 1000,
                content: $('#modal1'),
                okValue: '确定',
                ok: function () {
                    //var seleRow1 = $('#X_Table').XGrid('getSeleRow');
                    var seleRow = $('#X_Table1').XGrid('getSeleRow'),
                        tableRow2 = $('#X_Table2').XGrid('getRowData'),
                        tableRow3 = $('#X_Table3').XGrid('getRowData'),
                        seleAry = [].concat(seleRow?seleRow:[]);

                    if (seleAry.length > 0) {
                        /*if (seleRow.length) {
                            seleAry = seleRow
                        } else {
                            seleAry.push(seleRow)
                        }*/
                        //先判断已选采购单据明细，本次开票数量为 [1，小包装数量-已开票数量] 区间的整数
                        var errorAry = [];
                        var str ="商品编号为:";
                        $.each(seleAry,function (key,item) {
                           if(item.moveTypeCode !="105" && item.moveTypeCode !="107"){
                               if(!(0 < item.thisInvoicedCount && item.thisInvoicedCount <= (item.productPackInStoreCount - item.invoicedCount) && item.thisInvoicedCount%1 === 0)){
                                   str += '<br/>' + item.productCode+","+item.productName;
                                   errorAry.push(item.productCode)
                               }
                           }

                        });
                        str +="<br/>的商品,本次开票数量有误,请修改.<br/><span style='color:#a7a6a6'>（注意:本次开票数量为大于0且小于等于'小包装数量-已开票数量'的整数）</span>";
                        if(errorAry.length > 0){
                            utils.dialog({
                                title:"提示",
                                width:300,
                                content:'<div style="max-height: 500px;overflow-y: auto">'+str+'</div>',
                                okValue: '确定',
                                ok: function () {

                                }
                            }).showModal();
                            return false;
                        }

                        var flag1 = true;
                        $.each(seleAry,function (key,item) {
                            var flag2 = true;
                            $.each(tableRow2,function (index,val) {
                                if(item.stockOrderNo == val.stockOrderNo){
                                    flag1 = false;
                                }
                            });
                            $.each(tableRow3,function (index,val) {
                                if(item.stockOrderNo == val.stockOrderNo&&item.billsSort == val.billsSort){
                                    flag2 = false;
                                }
                            })
                            if(flag2){
                                //已选采购单据明细 单据号相同时行号必须唯一
                                $('#X_Table3').XGrid('addRowData', item);
                            }
                        });
                        if(flag1){
                            //已选单据 采购单据号 唯一
                            $('#X_Table2').XGrid('addRowData', a);
                        }
                        addtotalRow($('#X_Table'), {
                            noTicketMoney:"noTicketMoney",
                            sumNoTaxMoney: "sumNoTaxMoney",
                            sumTaxMoney: "sumTaxMoney",
                            priceTaxSum: "priceTaxSum"
                        });
                        addtotalRow($('#X_Table2'), {
                            sumNoTaxMoney: "sumNoTaxMoney",
                            sumTaxMoney: "sumTaxMoney",
                            priceTaxSum: "priceTaxSum"
                        });
                        addtotalRow($('#X_Table3'), {
                            procutNoTaxMoney: "procutNoTaxMoney",
                            productTaxMoney: "productTaxMoney",
                            productContainTaxMoney: "productContainTaxMoney"
                        });
                    }else {
                        utils.dialog({
                            content:"请选择需要移入的明细",
                            quickClose: true,
                            timeout: 2000
                        }).showModal();
                        return false;
                    }

                },
                cancelValue: '取消',
                cancel: function () {
                }
            }).showModal();
        },
        onSelectRow: function (e, c, a, b) {
            getSelTotalData();
        },
        gridComplete: function () {
            var data = $(this).XGrid("getRowData");
            if (!data.length) {
                utils.dialog({
                    content: "查询无数据",
                    quickClose: true,
                    timeout: 2000
                }).showModal();
            }
            setTimeout(function () {
                addtotalRow($('#X_Table'), {
                    noTicketMoney:"noTicketMoney",
                    sumNoTaxMoney: "sumNoTaxMoney",
                    sumTaxMoney: "sumTaxMoney",
                    priceTaxSum: "priceTaxSum"
                });
            }, 200)
            //如果是从发票录入返回的 就加载缓存"http://localhost:8080/proxy-finance/finance/purchase/invoice/toInvoice"
            if(document.referrer.indexOf('/proxy-finance/finance/purchase/invoice/toEditInvoice')>-1){
                dataBuffer()
            }
        },
        pager: '#grid-pager'
    });


    //查询数据，重置data
    $('#searchBtn').on('click', function () {
        var moveTypeCode = $("#moveTypeCode").val() ? $("#moveTypeCode").val() : [];
        var ticketStatus = $("#ticketStatus").val();
        if (moveTypeCode.length == 0 || moveTypeCode.length == 4) {
            moveTypeCode = ['-1']
        }
        if (ticketStatus == -1) {
            ticketStatus = null;
        }
        console.log(moveTypeCode);
        console.log(ticketStatus);
        //校验
        if (validform("myform").form()) {
            $('#X_Table').setGridParam({
                url: '/proxy-finance/finance/purchase/invoice/findStoreIn',
                postData: {
                    "keyWord": $("#keyWord").val(),
                    "starttime": $("#starttime").val(),
                    "endtime": $("#endtime").val(),
                    "ticketStatus": $("#ticketStatus").val(),
                    "moveTypeCode": JSON.stringify(moveTypeCode),
                    "orderNo": $("#orderNo").val(),
                    "stockOrderNo": $("#stockOrderNo").val()
                }
            }).trigger('reloadGrid');
        } else {//验证不通过
            utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }


    })

    //更新序号
    function refreshIndex($Table) {
        var rn = $Table.find('td[row-describedby="rn"]');
        $.each(rn, function (index, item) {
            $(item).html(index + 1);
        })
    }

    //先初始化入库单明细
    $('#X_Table1').XGrid({
        data:[],
        colNames: ['id','采购订单号','订单数量', '单据日期', '采购单据号', '真正单据号', '采购单据行号', '采购单据行状态', '商品编码', '商品名称', '规格', '小包装单位', '含税单价',
            '小包装数量','本次开票数量','已开票数量',
            '金额合计', '税额合计', '价税合计'
        ],
        colModel: [{
            name: 'id',
            hidden: true,
            hidegrid: true
        },{
            name: 'purchaseOrderNo'
        },
            {
                name: 'orderCount'
            },
            {
                name: 'storeTime'
            }, {
                name: 'stockOrderNo'
            },  {
                name: 'billsSort'
            }, {
                name: 'ticketStatus',
                formatter: function (value) {
                    if (value != '') {
                        if (value == '0') {
                            return '未开发票';
                        } else if (value == '1') {
                            return '部分开票';
                        } else if (value == '2') {
                            return '已开票';
                        } else {
                            return '';
                        }
                    } else {
                        return '';
                    }
                },
                unformat: function (value) {
                    if (value != '') {
                        if (value == '未开发票') {
                            return '0';
                        } else if (value == '部分开票') {
                            return '1';
                        } else if (value == '已开票') {
                            return '2';
                        }
                    } else {
                        return '';
                    }
                }
            }, {
                name: 'productCode'
            }, {
                name: 'productName'
            }, {
                name: 'productSpecification'
            }, {
                name: 'productPackUnitSmall'
            }, {
                name: 'productContainTaxPrice',
                formatter: function (val) {
                    return parseFloat(val);
                }
            }, {
                name: 'productPackInStoreCount',
                /*formatter: function(val,a,b,c){
                    if(val == ''){
                        return '0';
                    }else if(!val){
                        return '';
                    }else if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val);
                    }else{
                        return parseFloat(val);
                    }
                }*/
            },
             {
                 name: 'thisInvoicedCount',
                 rowtype: "#thisInvoicedCount_input",
                 formatter: function(val,a,obj,c){
                     if(obj.moveTypeCode == 105 || obj.moveTypeCode == 107){
                         setTimeout(function () {
                             $("#thisInvoicedCount_input_" + obj.id + " input").attr('disabled',true)
                         })
                     }
                     if(val){
                         return val
                     }else {
                         return 0
                     }
                 },
                 unformat: function (val) {
                     if(val){
                         return val
                     }else {
                         return 0
                     }
                 },
                 rowEvent: function (row) {
                     var procutNoTaxMoney,productTaxMoney,productContainTaxMoney,moveCode,rowData = row.rowData;
                     if(rowData.moveTypeCode ==103 || rowData.moveTypeCode ==107){
                         if(rowData.ticketStatus ==1 && (parseFloat(rowData.thisInvoicedCount)+parseFloat(rowData.invoicedCount)) == rowData.productPackInStoreCount){
                             productContainTaxMoney = rowData.newProductContainTaxMoney - rowData.invoicedCount * rowData.productContainTaxPrice *-1
                         }else{
                             //价税合计
                             productContainTaxMoney = parseFloat(rowData.thisInvoicedCount * rowData.productContainTaxPrice *-1);
                         }
                         //金额合计
                         procutNoTaxMoney = parseFloat(productContainTaxMoney / (1 + parseFloat(rowData.productEntryTax)));
                         //税额合计
                         productTaxMoney = parseFloat(procutNoTaxMoney * parseFloat(rowData.productEntryTax));
                     }else{
                         if(rowData.ticketStatus ==1 && (parseFloat(rowData.thisInvoicedCount)+parseFloat(rowData.invoicedCount)) == rowData.productPackInStoreCount){
                             productContainTaxMoney = rowData.newProductContainTaxMoney - rowData.invoicedCount * rowData.productContainTaxPrice;
                         }else{
                             //价税合计
                             productContainTaxMoney = parseFloat(rowData.thisInvoicedCount * rowData.productContainTaxPrice);
                         }
                         //金额合计
                         procutNoTaxMoney = parseFloat(productContainTaxMoney / (1 + parseFloat(rowData.productEntryTax)));
                         //税额合计
                         productTaxMoney = parseFloat(procutNoTaxMoney * parseFloat(rowData.productEntryTax));
                     }


                     $(this).XGrid('setRowData',rowData.id,{productContainTaxMoney:productContainTaxMoney,procutNoTaxMoney:procutNoTaxMoney,productTaxMoney:productTaxMoney});
                     countTotal();
                 }
            },{
                name: 'invoicedCount'
            },
            {
                name: 'procutNoTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'productTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'productContainTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'moveTypeCode',
                hidden: true,
                hidegrid: true
            }, {
                name: 'productEntryTax',
                hidden: true,
                hidegrid: true
            }, {
                name: 'newProductContainTaxMoney',
                hidden: true

            }],
        rowNum: 99999999,
        multiselect: true,
        rownumbers: true,
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function (id, dom, obj, index, event) {
            // $(this).XGrid("setRowData",id,{thisInvoicedCount:parseFloat(obj.productPackInStoreCount - obj.invoicedCount)});
            countTotal();
        },
        gridComplete: function () {
            var data = $(this).XGrid("getRowData");
            if (!data.length && $(this)[0].p.datasouce !== 'local') {
                utils.dialog({
                    content: "查询无数据",
                    quickClose: true,
                    timeout: 2000
                }).show();
            }
            //默认全选
            $(this).find('#grid_checked input').trigger('click');
            countTotal()
        },
        pager: '#grid-pager1'
    });
    $('#X_Table1').on('change','#grid_checked input[type=checkbox],tr td:first-child input[type=checkbox]',function () {
        countTotal()
    })
    function countTotal() {
        var selData =  $('#X_Table1').XGrid('getSeleRow'),
            totalAry = [0,0,0];//已选金额 已选税额 已选价税合计
        if(selData){
            if(selData.length>0){
                $.each(selData,function (index,item) {
                    totalAry[0] += (item.procutNoTaxMoney - 0);
                    totalAry[1] += (item.productTaxMoney - 0);
                    totalAry[2] += (item.productContainTaxMoney - 0);
                })
            }else {
                totalAry[0] += (selData.procutNoTaxMoney - 0);
                totalAry[1] += (selData.productTaxMoney - 0);
                totalAry[2] += (selData.productContainTaxMoney - 0);
            }
        }
        $("#X_Table1_selectTotal i.total").each(function (index,item) {
            $(item).text(totalAry[index].toFixed(2))
        })
    }
    //dialog显示入库单明细
    function initTableDialog(moveTypeCode, stockOrderNo,productEntryTax) {
        $("#s_stockOrderNo").val(stockOrderNo);
        $("#s_moveTypeCode").val(moveTypeCode);
        $("#s_productEntryTax").val(productEntryTax);
        $("#s_ticketStatus").val(0);
        $('#X_Table1').setGridParam({
            url: '/proxy-finance/finance/purchase/invoice/findStoreInDetail',
            postData: {
                stockOrderNo: stockOrderNo,
                moveTypeCode: moveTypeCode,
                productEntryTax:productEntryTax,
                ticketStatus: 0,
                drugCode:''
            },
        }).trigger('reloadGrid');
    }



    // 增加合计行
    function addtotalRow($X_Table, param) {

        $.each($X_Table.find('tr'), function (index, item) {
            if ($(item).find('td:first-child').html() === '合计') {
                $(item).remove();
            }
        })

        var data = $X_Table.XGrid('getRowData');

        for (var key in param) {
            param[key] = totalTable(data, key);
        }

        var obj = $.extend({
            id: '999999'
        }, param);


        refreshIndex($X_Table);
        $X_Table.XGrid('addRowData', obj);
        $X_Table.find('tr#999999 td').each(function (index,item) {
            if(!param[$(item).attr('row-describedby')]){
                $(item).empty();
            }
        });
       /* $X_Table.find('tr:last-child td').empty();
        $X_Table.XGrid('setRowData', '999', param);*/
        $X_Table.find('tr#999999').attr('id', '').find('td:first-child').html('合计');
    }

    //合计
    function totalTable(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat(item[colName]);
        })
        return count.toFixed(2);
    }

    //选择移入
    /*$('#selectInBtn').bind('click', function () {

        if (!isSelect()) return false;

        var selRow = $('#X_Table').XGrid('getSeleRow');
        var param = [];
        if (selRow) {
            if (selRow.length) {
                var supplierCode = selRow[0].supplierCode;
                var flag = true;
                $.each(selRow, function (index, item) {
                    if(supplierCode!= item.supplierCode){
                        utils.dialog({content: '不能选择多个供应商单据', quickClose: true, timeout: 2000}).show();
                        flag = false;
                        return false;
                    }
                })
                if(!flag) return false;
               // refreshSpanSumTotal(selRow,"minus");
                $.each(selRow, function (index, item) {
                    $('#X_Table').XGrid('delRowData', item.id);
                    $('#X_Table2').XGrid('addRowData', item, 'last');
                    param.push(item.stockOrderNo + ',' + item.moveTypeCode);
                })

            } else {
               // refreshSpanSumTotal(selRow,"minus");
                $('#X_Table').XGrid('delRowData', selRow.id);
                $('#X_Table2').XGrid('addRowData', selRow, 'last');
                param.push(selRow.stockOrderNo + ',' + selRow.moveTypeCode);

            }

            //批量查询明细
            $.ajax({
                url: '/proxy-finance/finance/purchase/invoice/findStoreInDetailList',
                method: 'POST',
                data: {stockOrderNoList: JSON.stringify(param)},
                dataType: 'json',
                success: function (data) {
                    if (data && data.result && data.result.length) {
                        $.each(data.result, function (index, item) {
                            $('#X_Table3').XGrid('addRowData', item);
                        })
                        addtotalRow($('#X_Table3'), {
                            procutNoTaxMoney: "procutNoTaxMoney",
                            productTaxMoney: "productTaxMoney",
                            productContainTaxMoney: "productContainTaxMoney"
                        });
                    }
                }
            })
            addtotalRow($('#X_Table'), {
                noTicketMoney:"noTicketMoney",
                sumNoTaxMoney: "sumNoTaxMoney",
                sumTaxMoney: "sumTaxMoney",
                priceTaxSum: "priceTaxSum"
            });
            addtotalRow($('#X_Table2'), {
                sumNoTaxMoney: "sumNoTaxMoney",
                sumTaxMoney: "sumTaxMoney",
                priceTaxSum: "priceTaxSum"
            });
            $('.selectTotal_Div').css('display','none');
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }
    })*/
    $('#selectInBtn').bind('click', function () {
        var tabIndex = $("#nav-tag li").index($("#nav-tag li.active")),
            selRow = $('#X_Table').XGrid('getSeleRow'),
            seleRow1 = $('#X_Table1').XGrid('getSeleRow'),
            tableRow1 = $('#X_Table1').XGrid('getRowData'),
            tableRow2 = $('#X_Table2').XGrid('getRowData'),
            tableRow3 = $('#X_Table3').XGrid('getRowData');

        seleRow1 = [].concat(seleRow1?seleRow1:[]);
        selRow = [].concat(selRow?selRow:[]);



        if(tabIndex === 0){
            if (!isSelect()) return false;
            var param = [];
            if (selRow) {
                selRow = [].concat(selRow);
                var supplierCode = selRow[0].supplierCode;
                var flag = false;
                $.each(selRow, function (index, item) {
                    if(supplierCode !== item.supplierCode && index !== 0){
                        flag = true;
                    }
                    $.each(tableRow2,function (key,val) {
                        if(val.supplierCode && val.supplierCode !== item.supplierCode){
                            flag = true;
                        }
                    })
                });
                if(flag){
                    utils.dialog({content: '不能选择多个供应商单据', quickClose: true, timeout: 2000}).show();
                    return false;
                }

                selRow.forEach(function (item,key) {
                    var isSame =  tableRow2.some(function (val,index) {
                        return item.stockOrderNo === val.stockOrderNo
                    }) 　
                    param.push(item.stockOrderNo + ',' + item.moveTypeCode);

                    !isSame &&  $('#X_Table').XGrid('delRowData', item.id);
                    !isSame &&  $('#X_Table2').XGrid('addRowData', item);
                });


                seleRow1.forEach(function (item,key) {
                    selRow.some(function (val,index) {
                        return item.stockOrderNo === val.stockOrderNo
                    }) && $('#X_Table3').XGrid('addRowData', item);
                });

                if(param.length){
                    //批量查询明细
                    $.ajax({
                        url: '/proxy-finance/finance/purchase/invoice/findStoreInDetailList',
                        method: 'POST',
                        data: {stockOrderNoList: JSON.stringify(param)},
                        dataType: 'json',
                        success: function (data) {
                            if (data && data.result && data.result.length) {
                                $.each(data.result, function (index, item) {
                                    $('#X_Table3').XGrid('addRowData', item);
                                })
                                addtotalRow($('#X_Table3'), {
                                    procutNoTaxMoney: "procutNoTaxMoney",
                                    productTaxMoney: "productTaxMoney",
                                    productContainTaxMoney: "productContainTaxMoney"
                                });
                            }
                        }
                    });
                }
                addtotalRow($('#X_Table'), {
                    noTicketMoney:"noTicketMoney",
                    sumNoTaxMoney: "sumNoTaxMoney",
                    sumTaxMoney: "sumTaxMoney",
                    priceTaxSum: "priceTaxSum"
                });
                addtotalRow($('#X_Table2'), {
                    sumNoTaxMoney: "sumNoTaxMoney",
                    sumTaxMoney: "sumTaxMoney",
                    priceTaxSum: "priceTaxSum"
                });
                addtotalRow($('#X_Table3'), {
                    procutNoTaxMoney: "procutNoTaxMoney",
                    productTaxMoney: "productTaxMoney",
                    productContainTaxMoney: "productContainTaxMoney"
                });
                $('.selectTotal_Div').css('display','none');
            } else {
                utils.dialog({
                    content: '没有选中任何行！',
                    quickClose: true,
                    timeout: 2000
                }).showModal();
            }
        }else {
            if (seleRow1.length > 0) {
                //先判断已选采购单据明细，本次开票数量为 [1，小包装数量-已开票数量] 区间的整数
                var errorAry = [];
                var str ="商品编号为:";
                $.each(seleRow1,function (key,item) {
                    if(item.moveTypeCode !="105" && item.moveTypeCode !="107"){
                        if(!(0 < item.thisInvoicedCount && item.thisInvoicedCount <= (item.productPackInStoreCount - item.invoicedCount) && item.thisInvoicedCount%1 === 0)){
                            str += '<br/>' + item.productCode+","+item.productName;
                            errorAry.push(item.productCode)
                        }
                    }
                });
                str +="<br/>的商品,本次开票数量有误,请修改.<br/><span style='color:#a7a6a6'>（注意:本次开票数量为大于0且小于等于'小包装数量-已开票数量'的整数）</span>";
                if(errorAry.length > 0){
                    utils.dialog({
                        title:"提示",
                        width:300,
                        content:'<div style="max-height: 500px;overflow-y: auto">'+str+'</div>',
                        okValue: '确定',
                        ok: function () {

                        }
                    }).showModal();
                    return false;
                }

                var seleRow_f = selRow.filter(function (item,key) {
                    return seleRow1.some(function (val,index) {
                        return val.stockOrderNo === item.stockOrderNo
                    })
                });
                var supplierCode_flag = true;
                if(seleRow_f.length){
                    var seleRow_f_supplierCode = seleRow_f[0].supplierCode;
                    supplierCode_flag = seleRow_f.every(function (item,key) {
                        return seleRow_f_supplierCode === item.supplierCode
                    }) && tableRow2.every(function (item,key) {
                        return !item.supplierCode || seleRow_f_supplierCode === item.supplierCode
                    })
                }else {
                    supplierCode_flag = false
                }
                if(!supplierCode_flag){
                    utils.dialog({content: '不能选择多个供应商单据', quickClose: true, timeout: 2000}).show();
                    return false;
                }
                seleRow_f.forEach(function (item,index) {
                    tableRow2.some(function (val,index) {
                        return val.stockOrderNo === item.stockOrderNo
                    }) || $('#X_Table2').XGrid('addRowData', item);
                });
                seleRow1.forEach(function (item,index) {
                    tableRow3.some(function (val,index) {
                        return val.stockOrderNo === item.stockOrderNo && item.billsSort === val.billsSort
                    }) || $('#X_Table3').XGrid('addRowData', item);
                });

                addtotalRow($('#X_Table'), {
                    noTicketMoney:"noTicketMoney",
                    sumNoTaxMoney: "sumNoTaxMoney",
                    sumTaxMoney: "sumTaxMoney",
                    priceTaxSum: "priceTaxSum"
                });
                addtotalRow($('#X_Table2'), {
                    sumNoTaxMoney: "sumNoTaxMoney",
                    sumTaxMoney: "sumTaxMoney",
                    priceTaxSum: "priceTaxSum"
                });
                addtotalRow($('#X_Table3'), {
                    procutNoTaxMoney: "procutNoTaxMoney",
                    productTaxMoney: "productTaxMoney",
                    productContainTaxMoney: "productContainTaxMoney"
                });
            }else {
                utils.dialog({
                    content:"请选择需要移入的明细",
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                return false;
            }
        }

    })

    // 采购单据列表  全选checkbox，单选checkbox
    $('#X_Table').on('change','#grid_checked input[type=checkbox],tr td:first-child input[type=checkbox]',function () {
        getSelTotalData()
    })
    // 采购单据列表  选中项 数据统计
    function getSelTotalData() {
        let selData =  $('#X_Table').XGrid('getSeleRow'),
            sumNoTaxMoneyArr = [], sumNoTaxMoneyStr = 0,
            sumTaxMoneyArr = [], sumTaxMoneyStr = 0,
            priceTaxSumArr = [], priceTaxSumStr = 0;
        if(selData && !Array.isArray(selData)){
            selData = [selData];
        }
        if(selData && selData.length > 0){
            $('.selectTotal_Div').css('display','block');
            sumNoTaxMoneyArr = Array.from(selData).map(function (item,index) {
                return item.sumNoTaxMoney
            });
            sumTaxMoneyArr = Array.from(selData).map(function (item,index) {
                return item.sumTaxMoney
            });
            priceTaxSumArr = Array.from(selData).map(function (item,index) {
                return item.priceTaxSum
            });
            sumNoTaxMoneyStr = sumNoTaxMoneyArr.reduce((prev, cur, index, arr)=> {
                return Number(prev) + Number(cur);
        });
            sumTaxMoneyStr = sumTaxMoneyArr.reduce((prev, cur, index, arr)=> {
                return Number(prev) + Number(cur);
        });
            priceTaxSumStr = priceTaxSumArr.reduce((prev, cur, index, arr)=> {
                return Number(prev) + Number(cur);
        });
            let totalStr =  `
                    <div class="selectTotal_Div"><span>已选金额:` + Number(sumNoTaxMoneyStr).toFixed(2) + `</span>
                    <span>已选税额:` + Number(sumTaxMoneyStr).toFixed(2) + `</span>
                    <span>已选价税合计:` + Number(priceTaxSumStr).toFixed(2) + `</span></div>`;
            $('.selectTotal_Div').remove();
            $('.totalSumClassBiz').before(totalStr);
        }else{
            $('.selectTotal_Div').remove();
        }
    }
    function isSelect() {
        var flag = true,
            arrA = [],
            arrB = [],
            a = $('#X_Table').XGrid('getSeleRow'),
            b = $('#X_Table2').XGrid('getRowData');
        b.pop();
        if (a) {
            if (a.length) {
                $.each(a, function (index, item) {
                    arrA.push(item.id)
                })
            } else {
                arrA.push(a.id)
            }

        }

        if (b.length) {
            $.each(b, function (index, item) {
                arrB.push(item.id)
            })
        }
        // console.log(arrA, arrB)
        //判断arrB中是否包含arrA中的订单

        if (b.length > 0) {
            $.each(arrB, function (indexB, itemB) {
                $.each(arrA, function (indexA, itemA) {
                    // console.log(itemB,itemA)
                    if (itemB == itemA) {
                        utils.dialog({
                            content:"此订单已经移入",
                            quickClose: true,
                            timeout: 2000
                        }).showModal();
                        flag = false;
                    }
                })
            })
        }

        return flag;

    }

    function isSumSelect() {
        var flag = true,
            arrA = [],
            arrB = [],
            a = $('#X_Table').XGrid('getSeleRow'),
            b = $('#X_Table2').XGrid('getRowData');
        b.pop();
        if (a) {
            if (a.length) {
                $.each(a, function (index, item) {
                    arrA.push(item.id)
                })
            } else {
                arrA.push(a.id)
            }

        }

        if (b.length) {
            $.each(b, function (index, item) {
                arrB.push(item.id)
            })
        }
        // console.log(arrA, arrB)
        //判断arrB中是否包含arrA中的订单

        if (b.length > 0) {
            $.each(arrB, function (indexB, itemB) {
                $.each(arrA, function (indexA, itemA) {
                    // console.log(itemB,itemA)
                    if (itemB == itemA) {

                        flag = false;
                    }
                })
            })
        }

        return flag;

    }

    //刷新总计
    function refreshSpanSumTotal(selRow,minit){

        var span = $(".totalSumClassBiz span");
        var span1 = span[0].innerText;
        var span2 = span[1].innerText;
        var span3 = span[2].innerText;
        var span4 = span[3].innerText;
        var span1str =span1.split(":");
        var span2str =span2.split(":");
        var span3str =span3.split(":");
        var span4str =span4.split(":");
        var span1num = parseFloat(span1str[1]);
        var span2num =parseFloat(span2str[1]);
        var span3num = parseFloat(span3str[1]);
        var span4num = parseFloat(span4str[1]);
       var selRow1 = $('#X_Table2').XGrid('getRowData');
        if(minit=="minus"){
            if (!isSumSelect()) return false;
            if(selRow){
                if (selRow.length) { //多行
                    $.each(selRow, function (index, item) {
                         span1num -= parseFloat(item.sumNoTaxMoney);
                        span2num -= parseFloat(item.sumTaxMoney);
                        span3num -= parseFloat(item.priceTaxSum);
                        span4num -= parseFloat(item.noTicketMoney);
                    });
                }else{  //单行
                    span1num -= parseFloat(selRow.sumNoTaxMoney);
                    span2num -= parseFloat(selRow.sumTaxMoney);
                    span3num -= parseFloat(selRow.priceTaxSum);
                    span4num -= parseFloat(selRow.noTicketMoney);

                }
            }
        }else {
            if(selRow){
                if (selRow.length) { //多行
                    $.each(selRow, function (index, item) {
                        span1num += parseFloat(item.sumNoTaxMoney);
                        span2num += parseFloat(item.sumTaxMoney);
                        span3num += parseFloat(item.priceTaxSum);
                        span4num += parseFloat(item.noTicketMoney);
                    });
                }else{  //单行
                    span1num += parseFloat(selRow.sumNoTaxMoney);
                    span2num += parseFloat(selRow.sumTaxMoney);
                    span3num += parseFloat(selRow.priceTaxSum);
                    span4num += parseFloat(selRow.noTicketMoney);

                }
            }
        }

        span[0].innerText=span1str[0]+":"+parseFloat(span1num).toFixed(2);
        span[1].innerText=span2str[0]+":"+parseFloat(span2num).toFixed(2);
        span[2].innerText=span3str[0]+":"+parseFloat(span3num).toFixed(2);
        span[3].innerText=span4str[0]+":"+parseFloat(span4num).toFixed(2);


    }

    //选择移出
    $('#selectOutBtn').bind('click', function () {

        var selRow3 = $('#X_Table3').XGrid('getRowData'),
            selRow1 = $('#X_Table').XGrid('getRowData'),
            selRow = $('#X_Table2').XGrid('getSeleRow');
        var param = [];
        if (selRow) {
            if (selRow.length) {
                $.each(selRow, function (index, item) {
                    var $checkboxEle = $('#X_Table2').find('tr#'+item.id+' input[type=checkbox]');
                    if($checkboxEle.prop("checked")){
                        $checkboxEle.trigger('click');
                    }
                    $('#X_Table2').XGrid('delRowData', item.id);
                    if(selRow1&&selRow1.length>0){
                        var flag = selRow1.some(function (val,key) {
                            return val.stockOrderNo==item.stockOrderNo
                        });
                        if(!flag) $('#X_Table').XGrid('addRowData', item);
                    }else {
                        $('#X_Table').XGrid('addRowData', item);
                    }
                    $.each(selRow3, function (index, obj) {
                        if(obj.stockOrderNo == item.stockOrderNo){
                            $('#X_Table3').XGrid('delRowData', obj.id);
                        }
                    })
                })

            } else {
                var $checkboxEle = $('#X_Table2').find('tr#'+selRow.id+' input[type=checkbox]');
                if($checkboxEle.prop("checked")){
                    $checkboxEle.trigger('click');
                }
                $('#X_Table2').XGrid('delRowData', selRow.id);
                if(selRow1&&selRow1.length>0){
                    var flag = selRow1.some(function (val,key) {
                        return val.stockOrderNo==selRow.stockOrderNo
                    });
                    if(!flag) $('#X_Table').XGrid('addRowData', selRow);
                }else {
                    $('#X_Table').XGrid('addRowData', selRow);
                }
                $.each(selRow3, function (index, obj) {
                    if(obj.stockOrderNo == selRow.stockOrderNo){
                        $('#X_Table3').XGrid('delRowData', obj.id);
                    }

                })
            }

            addtotalRow($('#X_Table'), {
                noTicketMoney:"noTicketMoney",
                sumNoTaxMoney: "sumNoTaxMoney",
                sumTaxMoney: "sumTaxMoney",
                priceTaxSum: "priceTaxSum"
            });
            addtotalRow($('#X_Table2'), {
                sumNoTaxMoney: "sumNoTaxMoney",
                sumTaxMoney: "sumTaxMoney",
                priceTaxSum: "priceTaxSum"
            });
            addtotalRow($('#X_Table3'), {
                procutNoTaxMoney: "procutNoTaxMoney",
                productTaxMoney: "productTaxMoney",
                productContainTaxMoney: "productContainTaxMoney"
            });
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }
    })


    //已选择入库单
    $('#X_Table2').XGrid({
        // url: 'http://www.baidu.com',
        data: storeInList,
        colNames: ['id', '采购单据号','业务类型', '采购单据状态', '供应商编码', '供应商', '移动类型', '制单人', '单据日期', '金额合计', '税额合计', '税率', '价税合计', '移动类型code'],
        colModel: [
            {
                name: 'id',
                hidden: true,
                hidegrid: true
            }, {
                name: 'stockOrderNo'
            }, {
                name: 'channelName'
            }, {
                name: 'ticketStatus',
                formatter: function (value) {
                    if (value != '') {
                        if (value == '0' || value == null) {
                            return '未开发票';
                        } else if (value == '1') {
                            return '部分开票';
                        } else if (value == '2') {
                            return '已开票';
                        }
                    } else {
                        return '';
                    }
                },
                unformat: function (value) {
                    if (value != '') {
                        if (value == '未开发票') {
                            return '0';
                        } else if (value == '部分开票') {
                            return '1';
                        } else if (value == '已开票') {
                            return '2';
                        } else {
                            return '';
                        }
                    } else {
                        return '';
                    }
                }
            }, {
                name: 'supplierCode'
            }, {
                name: 'supplierName'
            }, {
                name: 'moveTypeName'
            }, {
                name: 'createUser'
            }, {
                name: 'storeTime'
            }, {
                name: 'sumNoTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'sumTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'productEntryTax'
            }, {
                name: 'priceTaxSum',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'moveTypeCode',
                hidden: true,
                hidegrid: true
            },{
                name: 'orderNo',
                hidden: true,
                hidegrid: true
            }, {
                name: 'noTicketMoney',
                hidden: true,
                hidegrid: true,
                formatter: function(val,a,b,c){
                    if(!val){
                        return '0.00'
                    }else if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')) {
                        return '-' + parseFloat(val).formatMoney('2', '', ',', '.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }

                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'channelId',
                hidden: true,
                hidegrid: true
            }],
         rowNum: 9999999,
        altRows: true, //设置为交替行表格,默认为false
        multiselect: true, // 全选
        rownumbers: true,
        //selectandorder: true,
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {




        }
        // pager: '#grid-pager2',
    });
    addtotalRow($('#X_Table2'), {
        sumNoTaxMoney: "sumNoTaxMoney",
        sumTaxMoney: "sumTaxMoney",
        priceTaxSum: "priceTaxSum"
    });
    //已选入库单明细
    $('#X_Table3').XGrid({
        // url: 'http://www.baidu.com',
        data: storeInDetailList,
        colNames: ['id', '单据日期', '采购单据号', '真正单据号', '采购单据行号','采购单据行状态', '商品编码', '商品名称','通用名助记码','通用名', '规格', '小包装单位', '含税单价',
            '小包装数量','本次开票数量','已开票数量',
            '金额合计', '税额合计', '价税合计'
        ],
        colModel: [{
            name: 'id',
            hidden: true,
            hidegrid: true
        },
            {
                name: 'storeTime'
            }, {
                name: 'stockOrderNo'
            }, {
                name: 'purchaseOrderNo',
                hidden: true
            }, {
                name: 'billsSort'
            },{
                name: 'ticketStatus',
                formatter: function (value) {
                    if (value != '') {
                        if (value == '0' || value == null) {
                            return '未开发票';
                        } else if (value == '1') {
                            return '部分开票';
                        } else if (value == '2') {
                            return '已开票';
                        }
                    } else {
                        return '';
                    }
                },
                unformat: function (value) {
                    if (value != '') {
                        if (value == '未开发票') {
                            return '0';
                        } else if (value == '部分开票') {
                            return '1';
                        } else if (value == '已开票') {
                            return '2';
                        } else {
                            return '';
                        }
                    } else {
                        return '';
                    }
                }
            }, {
                name: 'productCode'
            }, {
                name: 'productName'
            }, {
                name: 'commonNameMnemonicCode'
            }, {
                name: 'commonName'
            }, {
                name: 'productSpecification'
            }, {
                name: 'productPackUnitSmall'
            }, {
                name: 'productContainTaxPrice',
                formatter: function (val) {
                    return parseFloat(val);
                }
            }, {
                name: 'productPackInStoreCount',
                /*formatter: function(val,a,b,c){
                    if(!val){
                        return '0';
                    }else if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val);
                    }else{
                        return parseFloat(val);
                    }
                }*/
            },{
                name: 'thisInvoicedCount',
                formatter: function(val,a,obj,c){
                    if(!val){
                        return parseFloat(obj.productPackInStoreCount - obj.invoicedCount).toFixed(0);
                    }else {
                        return parseFloat(val).toFixed(0)
                    }
                },
            },{
                name: 'invoicedCount',
                formatter: function(val,a,obj,c){
                    if(val){
                        return val;
                    }else {
                        return "0";
                    }
                },
            }, {
                name: 'procutNoTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'productTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'productContainTaxMoney',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'moveTypeCode',
                hidden: true,
                hidegrid: true
            }],
        key: 'id',
         rowNum: 99999999,
        rownumbers: true,
        multiselect: true, // 全选
        altRows: true, //设置为交替行表格,默认为false
        gridComplete: function () {

        },
        // pager: '#grid-pager3',

    });
    addtotalRow($('#X_Table3'), {
        procutNoTaxMoney: "procutNoTaxMoney",
        productTaxMoney: "productTaxMoney",
        productContainTaxMoney: "productContainTaxMoney"
    });

    //跳转页面
    $('#toDetail').bind('click', function () {
        var data2 = $('#X_Table2').XGrid('getRowData');

        //如果表格下边有合计行
        data2.pop();
        var data3 = [].concat($('#X_Table3').XGrid('getRowData'));
        //判断有没有选择入库单，没有，不能跳转页面
        if (data2.length == 0 && data3.length == 0) {
            utils.dialog({content: '请选择入库单！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if(data3.length&&(data3[data3.length-1].id == '' || data3[data3.length-1].id == 999)) {
            data3.pop()
        }
        $.each(data3, function (index, item) {
            delete item.id;
        });

        if (data2.length) {
            var supplierCode = data2[0].supplierCode;
            var flag = true;
            $.each(data2, function (index, item) {
                if(supplierCode!= item.supplierCode){
                    utils.dialog({content: '不能选择多个供应商单据', quickClose: true, timeout: 2000}).show();
                    flag = false;
                    return false;
                }
            })
            if(!flag) return false;
        }

        var parames = [];
        parames.push({name: "purChaseInvoiceStoreInVoList", value: JSON.stringify(data2)});
        parames.push({name: "purChaseInvoiceStoreInProductDetailVoList", value: JSON.stringify(data3)});
        parames.push({name:"invoiceNo",value:$("#invoice_no").val()});
        //进行发票录入的时候添加缓存
        var X_Table2_data = $('#X_Table2').XGrid('getRowData');
        var X_Table3_data = $('#X_Table3').XGrid('getRowData');
        //去掉合计行
        if(X_Table2_data&&X_Table2_data.length>1&&X_Table2_data[X_Table2_data.length-1].id==999){
            X_Table2_data.pop();
        }
        //去掉合计行
        if(X_Table3_data&&X_Table3_data.length>1&&X_Table3_data[X_Table3_data.length-1].id==999){
            X_Table3_data.pop();
        }
        var dataBuffer = {
            searchParams:{
                keyWord: $("#keyWord").val(),
                starttime: $("#starttime").val(),
                endtime: $("#endtime").val(),
                ticketStatus: $("#ticketStatus").val(),
                moveTypeCode: $("#moveTypeCode").val(),
                orderNo: $("#orderNo").val(),
                stockOrderNo: $("#stockOrderNo").val(),
                supplierName: $("#supplierName").val()
            },
            X_Table2:[],
            X_Table3:[]
        };
        X_Table2_data.length?dataBuffer.X_Table2 = X_Table2_data:dataBuffer.X_Table2.push(X_Table2_data);
        X_Table3_data.length?dataBuffer.X_Table3 = X_Table3_data:dataBuffer.X_Table3.push(X_Table3_data);
        parent.financePurchaseInvoiceToEdit = dataBuffer;
        Post("/proxy-finance/finance/purchase/invoice/toEditInvoice", parames);

    })

    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }

    //退出
    $('#backInvoice').bind('click', function () {
        //弹框
        utils.dialog({
            title: '提示',
            content: '确定返回？',
            width: 200,
            okValue: '确定',
            cancelValue: '取消',
            ok: function () {
                utils.closeTab();
                // window.location.href = "/proxy-finance/finance/purchase/invoice/toList";
            },
            cancel: true
        }).showModal();
    })

    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/invoice/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result);
            $("#keyWord").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#keyWord").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });


    //放大镜查询
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=2',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#keyWord").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName).attr('oldvalue',data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('').attr('oldvalue','');
                }
            }
        }).showModal();
    }


    //加载缓存
    function dataBuffer() {
        var data = parent.financePurchaseInvoiceToEdit;
        if(!data) return false
        //查询条件
        var searchParams = data.searchParams;
        $("#keyWord").val(searchParams.keyWord);
        $("#starttime").val(searchParams.starttime);
        $("#endtime").val(searchParams.endtime);
        $("#ticketStatus").val(searchParams.ticketStatus);
        $("#moveTypeCode").val(searchParams.moveTypeCode);
        $("#orderNo").val(searchParams.orderNo);
        $("#stockOrderNo").val(searchParams.stockOrderNo);
        $("#supplierName").val(searchParams.supplierName);
        if(searchParams.supplierName) {
            $("#searchBtn").trigger('click');
        }
        //已选单据
        if(data.X_Table2.length&&(data.X_Table2[data.X_Table2.length-1].id == '' || data.X_Table2[data.X_Table2.length-1].id == 999)){
            data.X_Table2.pop()
        }
        $('#X_Table2').setGridParam({
            data:data.X_Table2
        }).trigger('reloadGrid');
        addtotalRow($('#X_Table2'), {
            sumNoTaxMoney: "sumNoTaxMoney",
            sumTaxMoney: "sumTaxMoney",
            priceTaxSum: "priceTaxSum"
        });
        //已选明细
        if(data.X_Table3.length&&(data.X_Table3[data.X_Table3.length-1].id == '' || data.X_Table3[data.X_Table2.length-1].id == 999)){
            data.X_Table3.pop()
        }
        $('#X_Table3').setGridParam({
            data:data.X_Table3
        }).trigger('reloadGrid');
        addtotalRow($('#X_Table3'), {
            procutNoTaxMoney: "procutNoTaxMoney",
            productTaxMoney: "productTaxMoney",
            productContainTaxMoney: "productContainTaxMoney"
        });
        parent.financePurchaseInvoiceToEdit = null;
    }
    $('#s_product_btn').on('click', function (){
        var selRow = $('#X_Table').XGrid('getSeleRow');
        var tempArr = [];
        if (selRow) {
            selRow = [].concat(selRow);
            tempArr = selRow.map(function (item,key) {
                return {
                    moveTypeCode:item.moveTypeCode,
                    stockOrderNo:item.stockOrderNo,
                    productEntryTax:item.productEntryTax,
                }
            })
        }
            $('#X_Table1').setGridParam({
                url: '/proxy-finance/finance/purchase/invoice/findNewStoreInDetail',
                postData: {
                    storeInListStr:JSON.stringify(selRow),
                    "drugCode": $("#input_goodName").val(),
                    "searchType": $("#productStatus").val()
                }
            }).trigger('reloadGrid');
    });

    function batchSearch (id) {
        var idName = id+'_batchSearch',$ele = $('#'+id),eleBox;
        if($("#"+idName).length>0){
            eleBox = $("#"+idName);
        }else {
            $('body').append('<div class="autocomplete-suggestions" id="'+idName+'"></div>');
            eleBox = $("#"+idName);
            eleBox.css({
                'position': 'absolute',
                'max-height': '300px',
                'z-index': '9999',
                'box-sizing':'border-box',
                'display': 'block',
                'fout-size': '16px'
            })
        }
        $ele.on({
            input:function () {
                fixPosition();
                var val = $(this).val(),
                    valAry = val.split(';'),
                    eleList = '',
                    seleLength = $(this)[0].selectionStart;
                //console.log(seleLength);
                $.each(valAry,function (index,item) {
                    if(item){
                        var bg = '#fff';
                        if(seleLength === +seleLength){
                            if(seleLength > 0){
                                seleLength = seleLength - item.length - 1;
                            }
                            if(seleLength <= 0){
                                bg = '#66AFE9';
                                seleLength = false;
                            }
                        }
                        eleList += '<div class="autocomplete-suggestion" data-index="'+index+'" style="padding-right: 22px;background:'+bg+'">'+item+'<span class="cancel" style="float: right;margin-right: -10px;cursor: pointer;" data-value="'+item+'">X</span></span></div>'
                    }
                });
                eleList ? eleBox.html(eleList).show() : eleBox.hide();
            },
            focus:function () {
                $(this).trigger('input')
            },
            click:function () {
                $(this).trigger('input')
            },
            keyup:function (e) {
                //console.log(e.keyCode);
                if(e.keyCode>=37 && e.keyCode<=40){
                    $(this).trigger('input')
                }
            },
            blur:function () {
                window[idName] = setTimeout(function () {
                    eleBox.hide();
                },200)
            }
        });
        eleBox.on('click',function (e) {
            window[idName] && clearTimeout(window[idName]);
            var $this = $(e.target);
            if($this.hasClass('cancel')){
                var valAry = $ele.val().split(';');
                valAry.splice(eleBox.find('.cancel').index($this),1);
                $ele.val(valAry.join(';')).trigger('input');
            }
        });
        function fixPosition() {
            eleBox.css({
                minWidth: $ele.outerWidth(),
                top: $ele.outerHeight() + $ele.offset().top + 'px',
                left: $ele.offset().left+ 'px',
            });
        }
    }
    batchSearch('input_goodName');
    $("#input_goodName").on('keyup',function (e) {
        if(e.keyCode === 13){
            $('#X_Table1').setGridParam({
                url: '/proxy-finance/finance/purchase/invoice/findStoreInDetail',
                postData: {
                    "stockOrderNo": $("#s_stockOrderNo").val(),
                    "moveTypeCode": $("#s_moveTypeCode").val(),
                    "ticketStatus": $("#s_ticketStatus").val(),
                    "drugCode": $("#input_goodName").val()
                }
            }).trigger('reloadGrid');
        }
    })

    //选择移出明细行
    $('#selectInfoOutBtn').bind('click', function () {

        var rowData2 = $('#X_Table2').XGrid('getRowData'),
            rowData1 = $('#X_Table').XGrid('getRowData'),
            selRow3 = $('#X_Table3').XGrid('getSeleRow');
        var selOrder = [];//选择的明细行采购单据集合
        if (selRow3) {
            selRow3 = [].concat(selRow3);
            $.each(selRow3, function (index, item) {
                var $checkboxEle = $('#X_Table2').find('tr#'+item.id+' input[type=checkbox]');
                if($checkboxEle.prop("checked")){
                    $checkboxEle.trigger('click');
                }
                $('#X_Table3').XGrid('delRowData', item.id);
                if(selOrder.indexOf(item.stockOrderNo) === -1){
                    selOrder.push(item.stockOrderNo)
                }
            });
            var rowData3 = $('#X_Table3').XGrid('getRowData');
            $.each(selOrder, function (index, item) {
                var flag = rowData3.find(function (val,key) {
                    return val.stockOrderNo === item;
                });
                if(!flag){
                    $.each(rowData2,function (key, val) {
                        if(item === val.stockOrderNo){
                            $('#X_Table2').XGrid('delRowData', val.id);
                            if(rowData1&&rowData1.length>0){
                                var flag1 = rowData1.some(function (val1,key1) {
                                    return val1.stockOrderNo === val.stockOrderNo
                                });
                                if(!flag1) $('#X_Table').XGrid('addRowData', val);
                            }else {
                                $('#X_Table').XGrid('addRowData', val);
                            }
                        }
                    });
                }
            });

            addtotalRow($('#X_Table'), {
                noTicketMoney:"noTicketMoney",
                sumNoTaxMoney: "sumNoTaxMoney",
                sumTaxMoney: "sumTaxMoney",
                priceTaxSum: "priceTaxSum"
            });
            addtotalRow($('#X_Table2'), {
                sumNoTaxMoney: "sumNoTaxMoney",
                sumTaxMoney: "sumTaxMoney",
                priceTaxSum: "priceTaxSum"
            });
            addtotalRow($('#X_Table3'), {
                procutNoTaxMoney: "procutNoTaxMoney",
                productTaxMoney: "productTaxMoney",
                productContainTaxMoney: "productContainTaxMoney"
            });
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }
    })
})
