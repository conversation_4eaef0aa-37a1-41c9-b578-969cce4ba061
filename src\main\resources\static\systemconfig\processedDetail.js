$('#X_Table').XGrid({
    url: 'queryCompleteTask',
    postData : {
        batch : $("#batch").val()
    },
    colNames: ['id','分类', '机构', '商品标准流速', '单位','占比','分类规则','orgCode'],
    colModel: [{
        name: 'id',
        index: 'id', //索引。其和后台交互的参数为sidx
        hidden:true

    }, {
        name: 'sort',

    }, {
        name: 'orgName',

    }, {
        name: 'productCriterion',

    }, {
        name: 'unit',

    },{
        name: 'proportion',

    },{
        name:'sortRule',
    },
        {
            name: 'orgCode',
            hidden: true,
        }
    ],
    rowNum: 0,
    rownumbers: 0,
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (e, c, a, b) {
        console.log('双击行事件', e, c, a, b);
    },
    onSelectRow: function (e, c, a, b) {
        console.log('单机行事件', e, c, a, b);
    },
    //pager: '#grid-pager',
});


//弹框提示
function showTips(contentText){
    utils.dialog({
        content: contentText,
        quickClose: true,
        timeout: 2000
    }).showModal();
}


function showDialog(titleText,contentText){
    //提示框
    utils.dialog({
        width: 180,
        height: 30,
        title: titleText,
        content: contentText,
        quickClose: false,
        okValue: '确定',
        ok: function () {}
    }).showModal();
}

$(function(){


    initApprovalFlowChart($("#processId").val())

    function  initApprovalFlowChart(processId) {
        //获取审核流程数据
        $.ajax({
            type: "POST",
            url: "queryProcessHistory",
            dataType:"json",
            data:{batch:processId},
            success: function (data) {
                if (data.code==0&&data.result!=null){
                    console.log(data.result);
                    $('.flow').html("")
                    $('.flow').process(data.result);
                }
            },
            error: function () {}
        });
    }


//审批通过
    $("#completeTask").click(function(){

        var batch = $("#batch").val();
        var taskId = $("#taskId").val();
        utils.dialog({
            title: '审核通过',
            content: $('#check_pass_msg'),
            okValue: '确定',
            ok: function () {
                if(!validform('check_pass_form').form()) return false;
                $.ajax({
                    url:'completeTask',
                    type:'post',
                    data:{businessId:batch,taskId:taskId,comment:$("#remark").val()},
                    success:function(result){
                        if(result.code==0){
                            showDialog("提示","审核成功!")
                            $('#X_Table').trigger('reloadGrid');
                        }if(result.code==1){
                            showDialog("提示","审核失败!")
                        }

                    }
                })
            },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal()








    })


// 审批驳回
    $("#commitProcess").click(function(){



        utils.dialog({
            title: '审核不通过',
            content: $('#check_pass_msg'),
            okValue: '确定',
            ok: function () {
                if(!validform('check_pass_form').form()) return false;
                $.ajax({
                    url:'commitProcess',
                    type:'post',
                    data:{batch:$("#batch").val(),taskId:$("#taskId").val(),comment:$("#remark").val()},
                    success:function(result){
                        if(result.code==0){
                            showDialog("提示","驳关成功!")
                            $('#X_Table').trigger('reloadGrid');
                        }if(result.code==1){
                            showDialog("提示","驳关失败!")
                        }

                    }
                })
            },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal()





    })


})



