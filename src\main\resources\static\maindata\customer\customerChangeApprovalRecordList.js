$(function () {
    //创建客户资料变更管理列表
    $('#table').XGrid({
        url: "/proxy-customer/customer/change/approval/query",
        postData: $("#searchForm").serializeToJSON(),
        // colNames: ['', '申请日期', '机构', '申请人', '单据编号', '客户编码', '客户名称', '客户类别',
        //     '是否资质变更','变更来源', '审核状态','审核时间','纸质回收状态','操作','来源','审核状态id',],
        colNames: ['', '申请日期', '机构', '申请人', '单据编号', '客户编码', '客户名称', '客户类别',
            '是否资质变更', '变更来源', '审核状态', '审核时间', '来源', '审核状态id',],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            hidden: true,
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'applicationDate',
            index: 'applicationDate',
            formatter: function (value) {
                var date = value;
                if (!value) return false;
                date = format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'orgCodeName',
            index: 'orgCodeName'
        }, {
            name: 'applicantName',
            index: 'applicantName'
        }, {
            name: 'applicationNumber',
            index: 'applicationNumber'
        }, {
            name: 'customerCode',
            index: 'customerCode'
        }, {
            name: 'customerName',
            index: 'customerName'
        }, {
            name: 'customerTypeName',
            index: 'customerTypeName'
        }, {
            name: 'changeType',
            index: 'changeType',
            formatter: function (e) {
                if (e == "1") {
                    return "是";
                } else {
                    return "否";
                }
            }
        },
        {
            name: 'source',
            index: 'source',
            formatter: function (e) {
                switch (String(e)) {
                    case "1":
                        return 'EC'
                    case "2":
                        return '豆芽'
                    case "3":
                        return '神农'
                    case "4":
                        return '药帮帮'
                }
            }
        },
        {
            name: 'auditStatus',
            index: 'auditStatus',
            formatter: function (e) {
                switch (String(e)) {
                    case "1":
                        return '录入中'
                    case "2":
                        return '审核中'
                    case "11":
                        return '一审通过'
                    case "10":
                        return '一审不通过'
                    case "21":
                        return '二审通过'
                    case "20":
                        return '二审不通过'
                    case "31":
                        return '三审通过'
                    case "30":
                        return '三审不通过'
                    case "40":
                        return '四审不通过'
                    case "3":
                        return '审核通过'
                    case "4":
                        return '已关闭'
                }
            }
        },
        {
            name: 'auditTime',
            index: 'auditTime',
            formatter: function (value) {
                var date = value;
                if (!value) return false;
                date = format(value);
                return date.split(' ')[0];
            }
        },
        // {
        //     name: 'paperRecyclStatus',
        //     index: 'paperRecyclStatus',
        //     formatter:function(e, d, rData){
        //         if (rData['changeType'] != 1) {
        //             return ''
        //         }else {
        //             switch (String(e)){
        //                 case "10":
        //                     return "资质不合格"
        //                 case "11":
        //                     return "资质已回收"
        //                 case "0":
        //                     return "资质未回收"
        //             }
        //         }
        //     }
        // },
        // {
        //     name:"computer",
        //     formatter: function (e, d, rData) {
        //         console.log(rData.paperRecyclStatus,rData.auditStatus);
        //         if (rData['changeType'] != 1) {
        //             return ''
        //         }else {
        //             if(rData&&rData.paperRecyclStatus!=11&&rData.auditStatus&&rData.auditStatus==3){
        //                 return  "<a href='javascript:;' onclick='qualificationRecoveryClick("+JSON.stringify(rData)+")'>资质回收</a>"
        //             } else {
        //                 return  "<span  style='color: #cccccc'>资质回收</span>"
        //             }
        //         }
        //     }
        // },
        {
            name: 'source',
            index: 'source',
            hidden: true
        },
        {
            name: 'orgCode',
            index: 'orgCode',
            hidden: true
        },
        {
            name: 'auditStatus',
            hidden: true
        },
        {
            name: 'applicant',
            index: 'applicant',
            hidden: true
        }
        ],
        viewrecords: true,//是否在浏览导航栏显示记录总数
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        gridComplete: function (id, dom, obj, index, event) {
            _tablePageNum = $(this).getGridParam().page
        },
        ondblClickRow: function (id, dom, obj, index, event) {

            if (obj.auditStatus == 1) {
                var loginUserId = $("#loginUserId").val();
                if (obj.applicant != loginUserId) {
                    utils.dialog({ content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
            }


            //console.log('双击行事件', id, dom, obj, index, event);

            utils.openTabs("customerChangeApprovalRecordDetail", "客户变更详情", "/proxy-customer/customer/change/approval/queryBaseChangeApprovalDetail?id=" + id + "&auditStatus=" + obj.auditStatus);

            // location.href='/proxy-customer/customer/change/approval/detail?id='+id
        },
        onSelectRow: function (id, dom, obj, index, event, obj) {
            //console.log('单机行事件', id, dom, obj, index, event, obj);
        },
        pager: '#grid-pager'
    });

    //删除
    $('#delete').on('click', function () {
        var selData = $('#table').XGrid("getSeleRow");
        if (!selData.length) {
            utils.dialog({ title: '提示', content: '请至少选择一行！', timeout: 3000 }).show();
            return false;
        }
        $.each(selData, function (index, item) {
            if ($('#loginUserId').val() != item.applicant) {
                utils.dialog({ title: '温馨提示:', content: '只允许删除自己录入的单据！', timeout: 3000 }).show();
                return false;
            }
            if (item.auditStatus == 1) { // 审核中
                utils.dialog({
                    title: '温馨提示:',
                    content: '确定要删除选中的单据吗？',
                    width: 300,
                    height: 50,
                    okValue: '【是】',
                    ok: function () {
                        $.ajax({
                            type: "GET",
                            url: "/proxy-customer/customer/change/approval/updateCustomerChanage?applicationNumber=" + item.applicationNumber,
                            async: false,
                            success: function (data) {
                                if (data.code == 0 && data.result != null) {
                                    console.log(data.result);
                                    console.log("更新 删除成功");
                                    // window.location.reload();
                                    //刷新列表
                                    $('#table').XGrid('setGridParam', {
                                        postData: {
                                            "orgCode": $("#orgCode").val(),
                                            "applicationNumber": $("#applicationNumber").val(),
                                            "customerCode": $("#customerCode").val(),
                                            "auditStatus": $("#auditStatus").val(),
                                            "paperRecyclStatus": $("#paperRecyclStatus").val(),
                                            "source": $("#source").val()
                                        }, page: 1
                                    }).trigger('reloadGrid');
                                }
                            },
                            error: function (data) {
                                if (data.code == 1 && data.result != null) {
                                    utils.dialog({ title: '温馨提示:', content: '只允许删除自己录入的单据！', timeout: 3000 }).show();
                                    return false;
                                }
                            }
                        });
                    },
                    cancelValue: '【否】',
                    cancel: function () {
                    }
                }).showModal();

            } else {
                utils.dialog({ title: '温馨提示:', content: '只允许删除录入中的单据！', timeout: 3000 }).show();
                return false;
            }
        });
    });





    $("#SearchBtn").on("click", function () {
        $('#table').XGrid('setGridParam', {
            postData: $("#searchForm").serializeToJSON(), page: 1
        }).trigger('reloadGrid');
    });
    $("#exportBtn").on("click", function () {
        const startTime = $("#startTime").val()
        const endTime = $("#endTime").val()
        if (!startTime || startTime.length === 0) {
            utils.dialog({ content: '请输入开始日期', quickClose: true, timeout: 2000 }).showModal();
            return
        }
        if (!endTime || endTime.length === 0) {
            utils.dialog({ content: '请输入截止日期', quickClose: true, timeout: 2000 }).showModal();
            return;
        }
        // IMPORTANT 当前页面内部的导出数量限制，与全局限制无关。
        const totalPageNum = Number($('#totalPageNum').text());
        const limitNum = Number($('#exportLimit').val());
        if (totalPageNum > limitNum) {
            const tips = "您本次导出的数据量过大（已超过" + limitNum + "条），不允许导出，请缩小导出范围。";
            utils.dialog({ content: tips, quickClose: true, timeout: 2000 }).showModal()
            return
        }
        //wgf判断条数wgf20200225
        // utils.exportAstrictHandle('table', Number($('#totalPageNum').text()), 1).then(() => {
        //     return false;
        // }).catch(() => {

        //     //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
        //     var ck = true;
        //     // copy this parameter and the below buttons
        //     var html = $('#exportHtml').html();
        //     var d = dialog({
        //         title: '请选择导出字段',
        //         width: 820,
        //         height: 460,
        //         content: html,
        //         okValue: '确定',
        //         ok: function () {
        //             this.title('提交中…');
        //             var arr = [];
        //             for (var i = 0; i < $("div[i=content] .exportItem").length; i++) {
        //                 $("div[i=content] .exportItem").eq(i).find('dd input[type="checkbox"]').each(function () {
        //                     var checked = this.checked;
        //                     if (checked) {
        //                         arr.push($.trim($(this).val()))
        //                     }
        //                 })
        //             }
        //             if (arr.length == 0) {
        //                 utils.dialog({ content: '请选择后导出', quickClose: true, timeout: 2000 }).showModal();
        //                 return false;
        //             }
        //             var exportAttribute = arr.join(',');
        //             // $("#exportAttribute").val(exportAttribute);
        //             // $("#searchForm").attr("action","/proxy-customer/customer/change/approval/exportExcel");
        //             // $("#searchForm").submit();
                    const params = $("#searchForm").serializeToJSON();
                    var formData2 = {
                        moduleName: 'customer',
                        taskCode: '1063',
                        colName: '50,51,52,53,160,54,55,56,57,58,60,61,62,69,70,71,72,73,82,84,85,88,89,96,148',
                        colNameDesc: '',
                        fileName: '客户资料变更管理',
                        exportParams:params
                    };
                    utils.dialog({
                        title: '温馨提示',
                        content: '导出任务提交成功后页面将关闭，是否确认导出？',
                        okValue: '确定',
                        ok: function () {
                            $.ajax({
                                url: "/proxy-customer/customer/customerBaseAppl/commonCommitExportTask",
                                type: 'post',
                                dataType: 'json',
                                data: {
                                    "data":JSON.stringify(formData2)
                                },
                                success: function (res) {
                                    if (res) {
                                        if (res.code === 0) {
                                            utils.dialog({
                                                title: '温馨提示',
                                                content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                                okValue: '确定',
                                                ok: function () {
    
                                                }
                                            }).showModal()
                                        } else {
                                            utils.dialog({
                                                title: '温馨提示',
                                                content: data.msg,
                                                okValue: '确定',
                                                ok: function () {
    
                                                }
                                            }).showModal()
                                        }
                                    }
                                }
                            });
                        }
                    }).showModal()
        //             // httpPost("/proxy-customer/customer/change/approval/exportExcel", params)

        //         },
        //         cancelValue: '取消',
        //         cancel: function () { },
        //         // copy button to other dialogues
        //         button: [
        //             {
        //                 id: 'chooseAll',
        //                 value: '全选',
        //                 callback: function () {
        //                     //debugger;
        //                     if (!ck) {
        //                         $(".exportItem input").removeAttr("checked");
        //                         ck = true;
        //                     } else if (ck) {
        //                         $(".exportItem input").prop("checked", "checked");
        //                         ck = false;
        //                     } else {
        //                         return false;
        //                     };
        //                     return false;
        //                 }
        //             }
        //         ]
        //         //copy ends here
        //     });
        //     d.showModal();
        // });


    });

})
$(document).on('change', '#grid_checked input', function () {
    var $tr = $(this).parents('tr');
    var rowData = $("#baseTable").getRowData($tr.attr('id'));
    var id = $tr.attr('id');
    setUrlObjectList($tr, id, rowData);
    //var checked=this.checked;
    // urlObjectList=[];
    // if(checked){
    //     var selRow=$('#baseTable').XGrid('getSeleRow');
    //     if(selRow && selRow.length > 0){
    //
    //         for(var i=0;i<selRow.length;i++){
    //             if(selRow[i].snapshootUrl != ''){
    //                 var fileParam={};
    //                 fileParam.id = selRow[i].id;
    //                 fileParam.name = selRow[i].customerCode;
    //                 fileParam.url = selRow[i].snapshootUrl;
    //                 urlObjectList.push(fileParam);
    //             }
    //         }
    //     }
    // }else{
    //     urlObjectList=[];
    // }
})
$(document).on("change", ".exportItem dt input", function () {
    var checked = this.checked;
    if (checked) {
        $(this).parents("dl").find("dd input[type='checkbox']").prop('checked', true);
    } else {
        $(this).parents("dl").find("dd input[type='checkbox']").prop('checked', false);
    }
});
$(document).on("change", ".exportItem dd input[type='checkbox']", function () {
    var inpLen = $(this).parents("dd").find("input[type='checkbox']").length;
    var checkLen = $(this).parents("dd").find("input[type='checkbox']:checked").length;
    if (inpLen == checkLen) {
        $(this).parents("dl").find("dt input[type='checkbox']").prop('checked', true);
    } else {
        $(this).parents("dl").find("dt input[type='checkbox']").prop('checked', false);
    }
});
function httpPost(URL, PARAMS) {
    var temp = document.createElement("form");
    temp.action = URL;
    temp.method = "post";
    temp.style.display = "none";

    for (var x in PARAMS) {
        var opt = document.createElement("textarea");
        opt.name = x;
        opt.value = PARAMS[x];
        temp.appendChild(opt);
    }
    document.body.appendChild(temp);
    temp.submit();

    return temp;
}
function qualificationRecoveryClick(_data) {
    // copy this parameter and the below buttons
    // _data=JSON.parse(_data);
    var _textVal = "";
    var html = $('#paperRecyclingDilog').html();
    $("#aperRecyclingTextAreas").val('');
    $.ajax({
        url: '/proxy-customer/customer/paperRecycle/queryRefuedReason',
        type: 'POST',
        dataType: 'json',
        contentType: 'application/json',
        timeout: 60000, //6000
        data: JSON.stringify({ type: 4, applicationNumber: _data.applicationNumber }),
        success: function (data) {
            if (data && data.code == 0) {
                if (data.result.refusedList && data.result.refusedList.length > 0) {
                    _textVal = data.result.refusedList[0].refusedDesc;
                    $("#aperRecyclingTextAreas").val(_textVal);
                } else {
                    $("#aperRecyclingTextAreas").val("");
                }
            }
        },
        error: function () {

        }
    });
    var sendData;
    var d = dialog({
        title: '纸质资质确认是否回收?',
        width: 500,
        height: 200,
        content: html,
        okValue: '确定',
        ok: function () {
            sendData = {
                type: 4,
                applicationNumber: _data.applicationNumber,
                applicant: _data.applicant,
                orgCode: _data.orgCode,
                customerCode: _data.customerCode,
                paperRecyclStatus: Number($("input[name='paperRecyclingRadio']:checked").val()),
                refusedList: [],
            }
            if (sendData.paperRecyclStatus == 10) {
                if (!$("#aperRecyclingTextAreas").val()) {
                    tankuang.dialog({
                        content: "内容为必填项",
                        quickClose: true,
                        timeout: 2000,
                    }).showModal();
                    return false;
                }
                sendData.refusedList.push({ credentialTypeId: "other", refusedCode: "other", refusedDesc: $("#aperRecyclingTextAreas").val() });
            }
            sendData = JSON.stringify(sendData);
            $.ajax({
                url: '/proxy-customer/customer/paperRecycle/submit',
                type: 'POST',
                dataType: 'json',
                contentType: 'application/json',
                timeout: 60000, //6000
                data: sendData,
                success: function (data) {
                    if (data && data.code == 0) {
                        d.close().remove();
                        $('#table').XGrid('setGridParam', {
                            postData: {
                                "orgCode": $("#orgCode option:selected").val(),
                                "applicationNumber": $("#applicationNumber").val(),
                                "customerCode": $("#customerCode").val(),
                                "auditStatus": $("#auditStatus").val(),
                                "paperRecyclStatus": $("#paperRecyclStatus").val(),
                                "source": $("#source").val()
                            }
                        }).trigger('reloadGrid', _tablePageNum);
                    }
                    tankuang.dialog({
                        content: data.msg,
                        quickClose: true,
                        timeout: 1000,
                    }).showModal();
                },
                error: function () {
                    tankuang.dialog({
                        content: "修改失败",
                        quickClose: true,
                        timeout: 2000,
                    }).showModal();
                }
            });
            return false;
        },
        cancelValue: '取消',
        cancel: function () { },
        //copy ends here
    });
    d.showModal();
    // $("input[name='paperRecyclingRadio'][value='11']").attr("checked",true);
    if (_data && _data.paperRecyclStatus && _data.paperRecyclStatus == 10) {
        $("#aperRecyclingTextAreas").val(_textVal);
        $("#aperRecyclingTextAreas").show();
        $("input[name='paperRecyclingRadio'][value='10']").attr("checked", true);
    } else {
        $("#aperRecyclingTextAreas").hide();
        $("input[name='paperRecyclingRadio'][value='11']").attr("checked", true);
    }
    $("input[name='paperRecyclingRadio']").off("change").on("change", function () {
        if ($("input[name='paperRecyclingRadio']:checked").val() == 10) {
            $("#aperRecyclingTextAreas").show();
        } else {
            $("#aperRecyclingTextAreas").hide();
        }
    })
}
var tankuang = {
    dialog: function (objParams) {
        //http://aui.github.io/artDialog/doc/index.html
        if (window.dialog && typeof window.dialog == 'function') {
            console.log('window.dialog');
        } else if (parent.dialog && typeof parent.dialog == 'function') {
            console.log('parent.dialog');
            window.dialog = parent.dialog;
        } else {
            console.log(CONFIG.STR.NOT_FIND);
            alert(CONFIG.STR.NOT_FIND + ':dialog');
            return false;
        }

        var defaultObj = {};
        $.extend(objParams, defaultObj);
        var d = dialog(objParams);
        if (objParams.timeout) {
            setTimeout(function () {
                d.close().remove();
                if (objParams.callBack) {
                    objParams.callBack()
                }
            }, objParams.timeout);
        }

        return d;

    }

}
function format(shijianchuo) {
    //shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth() + 1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y + '-' + add0(m) + '-' + add0(d) + ' ' + add0(h) + ':' + add0(mm) + ':' + add0(s);
}
function add0(m) { return m < 10 ? '0' + m : m }
function  btn_output_list(){
    utils.dialog({
        title: '导出列表',
        url: '/proxy-customer/customer/customerBaseAppl/toExportList?moduleName=customer&taskCode=1063',
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
}