$(function () {

    /* 合计计算  */
    var totalTable = z_utils.totalTable;

    var colName = ['商品编码','原商品编码','商品大类', '商品名称','通用名', '商品规格', '生产厂家', '产地', '单位', '库房名称', '批号', '生产日期', '有效期至', '入库数量',
        '含税单价', '价税合计', '实付金额', '活动优惠金额', '余额抵扣优惠金额', '退回返利金额', '税率', '税额'];
    var colModel = [{name: 'productCode', index: 'productCode',},
        {name: 'oldProductCode', index: 'oldProductCode'},
        {name: 'drugClass', index: 'drugClass'},
        {name: 'productName', index: 'productName'},
        { name: 'commonName', index: 'commonName'},
        {name: 'specifications', index: 'specifications'},
        {name: 'manufacturer', index: 'manufacturer'},
        {name: 'productOrigin', index: 'productOrigin'},
        {name: 'productUnit', index: 'productUnit'},
        {name: 'warehouseName', index: 'warehouseName'},
        {name: 'batchCode', index: 'batchCode'},
        {name: 'productionTime', index: 'productionTime', formatter: dateFormatter},
        {name: 'periodValidity', index: 'periodValidity', formatter: dateFormatter},
        {name: 'storageNumber', index: 'storageNumber'},
        {
            name: 'taxPrice', index: 'taxPrice',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1) {
                        return e.toString() + ".00"
                    } else if (array.length == 2) {
                        var elngt2 = array[1].length;
                        if (elngt2 == 1) {
                            return e.toString() + "0"
                        } else {
                            return e;
                        }
                    } else {
                        return e;
                    }
                } else {
                    return e;
                }
            }
        },
        {
            name: 'taxAmount', index: 'taxAmount',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1) {
                        return e.toString() + ".00"
                    } else if (array.length == 2) {
                        var elngt2 = array[1].length;
                        if (elngt2 == 1) {
                            return e.toString() + "0"
                        } else {
                            return e;
                        }
                    } else {
                        return e;
                    }
                } else {
                    return e;
                }
            }
        },
        {
            name: 'paymentAmount', index: 'paymentAmount',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1) {
                        return e.toString() + ".00"
                    } else if (array.length == 2) {
                        var elngt2 = array[1].length;
                        if (elngt2 == 1) {
                            return e.toString() + "0"
                        } else {
                            return e;
                        }
                    } else {
                        return e;
                    }
                } else {
                    return e;
                }
            }
        },
        {
            name: 'activityDiscountAmount', index: 'activityDiscountAmount',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1) {
                        return e.toString() + ".00"
                    } else if (array.length == 2) {
                        var elngt2 = array[1].length;
                        if (elngt2 == 1) {
                            return e.toString() + "0"
                        } else {
                            return e;
                        }
                    } else {
                        return e;
                    }
                } else {
                    return e;
                }
            }
        },
        {
            name: 'balanceDiscountAmount', index: 'balanceDiscountAmount',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1) {
                        return e.toString() + ".00"
                    } else if (array.length == 2) {
                        var elngt2 = array[1].length;
                        if (elngt2 == 1) {
                            return e.toString() + "0"
                        } else {
                            return e;
                        }
                    } else {
                        return e;
                    }
                } else {
                    return e;
                }
            }
        },
        {
            name: 'amountOfRebate', index: 'amountOfRebate',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1) {
                        return e.toString() + ".00"
                    } else if (array.length == 2) {
                        var elngt2 = array[1].length;
                        if (elngt2 == 1) {
                            return e.toString() + "0"
                        } else {
                            return e;
                        }
                    } else {
                        return e;
                    }
                } else {
                    return e;
                }
            }
        },
        {
            name: 'rate', index: 'rate',
            formatter: function (e) {
                if (e != undefined && e != null) {
                    return e + '%';
                } else {
                    return '0%'
                }
            }
        },
        {name: 'tax', index: 'tax'}
    ];
    $('#table_a').XGrid({
        url: '/proxy-order/order/orderReturnStorage/orderReturnStorageController/getReturnSalesProductListBase?refundRequestStorageCode=' + $("#refundRequestStorageCode").val(),
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'sort',
        rowNum: 100,
        rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        gridComplete: function () {
            setTimeout(function (param) {
                /* 合计写入 */
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                $(sum_ele[1]).text(totalTable(data, 'tax'));
                $(sum_ele[2]).text(totalTable(data, 'paymentAmount'));
            }, 200)
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });


    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    };

    function dateFormatter(inputTime) {
        if (inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }
    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        var datao = {};
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            datao.refundRequestStorageCode = $("#refundRequestStorageCode").val();
            datao.colName = colName;
            datao.colNameDesc = colNameDesc;

            httpPost("/proxy-order/order/orderReturnStorage/orderReturnStorageController/exportSalesOrderReturnStorageVDetailList", datao);

        });
    });
})//