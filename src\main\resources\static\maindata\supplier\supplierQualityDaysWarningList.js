$(function () {

    $('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierOrganBaseFile/querySupplierQualityDaysWarning",
        colNames: ["供应商类型",'供应商编码', '供应商名称', '营业执照号', '采购员', '签订日期','签订人','有效期至','剩余效期'],
        colModel: [
            {
                name: 'supplierTypeIdVal',
                index: 'supplierTypeIdVal',
                width: 100
            },{
                name: 'supplierCode',
                index: 'supplierCode',
                width: 250
            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 250
            }, {
                name: 'supplierBusinessNum',
                index: 'supplierBusinessNum',
                width: 250
            },{
                name: 'buyerIdVal',
                index: 'buyerIdVal',
                width: 140
            },{
                name: 'signDate',
                index: 'signDate',
                width: 180
            },{
                name: 'signName',
                index: 'signName',
                width: 160
            },{
                name: 'validDate',
                index: 'validDate',
                width: 100
            }, {
                name: 'remainDays',
                index: 'remainDays',
                width: 200,
                formatter:function (value,a,rowData) {
                        var id=rowData.id;
                        console.log(id);

                        setTimeout(function () {
                            if(0 < value && value < 180){
                                $("#"+id).css({
                                    "background":"#FFF9DD",
                                });
                            }else if(value <= 0){
                                $("#"+id).css({
                                    "background":"#FDE5E5",
                                });
                            }else if(value!=0&&!value){
                                $("#"+id).css({
                                    "background":"#FDE5E5",
                                });
                                value = "";
                            }
                        },0);

                        return value;
                }
            }
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        rownumbers: true
    });
    //搜索申请人
    valAutocomplete("/proxy-supplier/supplier/supplierOrganBaseFile/queryBuyerList",{paramName:'userNames'},"id",{data:"id",value:"userName"});

    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "supplierTypeId": $("#supplierTypeId").val(),
                "supplierCode":$("#supplierCode").val(),
                "minDay":$("#minDay").val(),
                "maxDay":$("#maxDay").val(),
                "buyerId":$("#id").val()
            },page:1
        }).trigger('reloadGrid');
    });

    $("#exportBtn").on("click", function () {
        utils.exportAstrictHandle('X_Tableb',
            Number($('#totalPageNum').text())).then(()=>{
            return false;
        }).catch(()=>{
            utils.dialog({
                title: '提示',
                content:"数据量大的时候耗时较长，请耐心等待。",
                okValue: '确定',
                ok: function () {
                    //parent.showLoading()
                    var body = document.body;
                    var form = $(body).find('form#searchForm');
                    $(form).attr("action","/proxy-supplier/supplier/supplierOrganBaseFile/exportQualityWarningExcel");
                    $(form).submit();
                    // setTimeout(function () {
                    //     parent.hideLoading()
                    // },2000)
                },
                cancelValue: '取消',
                cancel: function () { },
            }).showModal();
        })
    });
    $(document).on("change",".exportItem dt input",function () {
        var checked=this.checked;
        if(checked)
        {
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',false);
        }
    })
    $(document).on("change",".exportItem dd input[type='checkbox']",function () {
        var inpLen=$(this).parents("dd").find("input[type='checkbox']").length;
        var checkLen=$(this).parents("dd").find("input[type='checkbox']:checked").length;
        if(inpLen == checkLen)
        {
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',false);
        }
    })

})
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    });
}