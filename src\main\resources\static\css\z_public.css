html,
    body {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .content {
      height: 100%;
      display: flex;
      flex-direction: column;
      /* overflow: hidden; */
    }

    .content .panel:last-child {
        margin-bottom: 0;
    }

    .con-body {
      margin-bottom: 0 !important;
      flex: 1;
      /* 随便写的，实现自适应 */
      height: 100px;
      display: flex;
      flex-direction: column;
    }

    .nav-title {
      padding-bottom: 0;
    }

    /* 自适应表格 */
    .nav-content {
      flex: 1;
      /* 随便写的，实现自适应 */
      height: 100px;
    }

    .nav-content .panel-body {
      height: 100%;
      display: none;
      flex-direction: column;
      padding: 0 15px;
    }

    .nav-content>div.active {
      display: flex;
    }

    .nav-content .panel-body .panel-footer {
      margin: 0 -15px;
    }
    .nav-content .table-box {
        flex: 1;
        width: 100%;
        overflow: auto;
    }

    /* 固定高度表格 */
    .nav-content2 .panel-body {
        padding: 0 15px;
    }
    .nav-content2 .table-box{
        height: 500px;
    }
    .nav-content2 .panel-footer{
        margin: 0 -15px;
    }
    .nav-content2 .panel-footer .row{
        margin-left: 0;
        margin-right: 0;
    }

    #order_info {
      border-top: 1px solid #ddd;
    }

    #order_info .input-group {
      margin-bottom: 0;
    }

    #order_info .input-group .input-group-addon {
      width: 135px !important;
    }

    #order_info .input-group .form-control {
      width: calc(100% - 140px) !important;
      border: none;
      background: none;
      box-shadow: none;
    }