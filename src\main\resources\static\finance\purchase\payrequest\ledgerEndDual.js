$(function () {


    var colNames = ['公司代码', '公司名称', '当前期间', '月结后期间', '月结'],
        colModel = [
            {
                name: 'companyNo',
            }, {
                name: 'companyName'
            },
            {
                name: 'period',
                formatter:function (value){
                    if(value){
                        return moment(value).format('YYYY年M月');
                    }else{
                        return '';
                    }
                }
            },
            {
                name: 'endDateStr'
            }, {
                name: 'status',
                formatter: function(val) {
                    if(val !== 2){
                        return '已经月结';
                    }else{
                        return {rowtype: '#grid_block'};
                    }

                }

            }
        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/payrequestinfo/findLedgerEndDual',
        // url: 'http://localhost:8080/account/find',
        colNames: colNames,
        colModel: colModel,
        rowNum: 10,
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        postData: {

        },
        ondblClickRow: function (id, dom, obj, index, event) {
            this.returnValue = obj;
            // console.log(obj)
            //window.location.href = "/proxy-finance/finance/purchase/payrequestinfo/toDetail?payReqNO=" + obj.billNo;
            return obj;
        },
        onSelectRow: function (e, c, a, b) {
           // console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            var _this = $(this);
            setTimeout(function () {
                if(_this.XGrid('getRowData').length === 0){
                    utils.dialog({content:'查询无数据',quickClose:true,timeout:2000}).show();
                }
            },200)
        }
      //  pager: '#grid-pager',
    });

    //点击月结
    $('#X_Table').on('click', '.opreater', function () {
        var id = $(this).closest('tr').attr('id');
        var period = $('#X_Table').XGrid('getRowData',id).period;
        var year = period.split('年')[0];
        var month = period.split('年')[1].split('月')[0];
        var perTime = year+'-'+month;

        var now = moment(new Date().getTime()).format('YYYY-MM');
        var isBefore = moment(perTime).isBefore(now);

       //var text = $(this).closest('tr').prev('tr').find('td:last-child').html();
        if(!isBefore){
            utils.dialog({content:'不能对当月的期间月结！',quickClose: true,timeout:2000}).show();
            return false;
        }




        $.ajax({
            url:'/proxy-finance/finance/purchase/payrequestinfo/editLedgerEndDual',
            type: 'post',
            data: {ledgerId:id},
            success: function (result) {
                utils.dialog({content:'月结成功！',quickClose: true,timeout:2000}).show();
                setTimeout(function () {
                    $('#X_Table').trigger('reloadGrid');
                }, 500);

            },
            error: function (error) {
                alert(error.msg);
            }
        })
        console.log('123')
    })

})