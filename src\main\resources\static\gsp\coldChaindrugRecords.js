$(function () {

    var totalTable = z_utils.totalTable;
    var imageType = ['png', 'jpg', 'pdf'];


    $("#X_Table").XGrid({
        url: "/proxy-gsp/gsp/coldChainTransport/toDrugRecordsList",
        postData:{
            startTime: $("#starttime").val(),
            endTime: $("#endTime").val(),
        },
        colNames: ["id","销售出库单号", "销售订单号", "运输员", "联系方式", "车牌号", "运输在途温度", "承运单位", "启运日期", "启运温度",
            "发货日期", "发货地址", "收货单位","收货地址","收货人","货单号","药品件数","运输方式","委托经办人","到货时间","到货温度"],

        colModel: [
            {
                name: 'id',
                index: 'id',
                key: true,
                hidden:true,
                hidegrid: true
            },
            {
            name: "saleOutOrderNo"
        }, {
            name: "saleOrderNo"
        }, {
            name: "transporter"
        }, {
            name: "telPhone"
        }, {
            name: "busNum"
        }, {
            name: "transTemperature"
        }, {
            name: "carrierOrg"
        }, {
            name: "departDate"
        }, {
            name: "departTemperature"
        }, {
                name: "sendDate",
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                }
            }, {
                name: "sendAddress"
            }, {
                name: "receiveOrg"
            }, {
                name: "receiveAddress"
            },  {
                name: "receiver"
            }, {
                name: "invoiceNo"
            }, {
                name: "drugNum"
            }, {
                name: "transportWay"
            }, {
                name: "operator"
            }, {
                name: "arriveDate"
            }, {
                name: "arriveTemperature"
            }],
        altRows: true,
       // rownumbers: true,
        rowNum: 20,
        rowList: [20, 50, 100],
        pager: "#grid-pager",
        attachRow:true,
        selectandorder: true,//是否展示序号，多选
        onSelectRow: function (id, dom, obj) {

        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['drugNum'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
        },
        onPaging: function (page) {
            $("#X_Table").XGrid("clearGridData");
        },
    });


    /* 查询 */
    $('#search_btn').on('click', function () {
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-gsp/gsp/coldChainTransport/toDrugRecordsList',
            postData: {
                startTime: $("#starttime").val(),
                endTime: $("#endTime").val(),
                orderNo: $("#order_no").val()
            }
        }).trigger('reloadGrid');
        $("#X_Table").XGrid("clearGridData");
    });

    /**
     * 设置显示列
     */
    // 筛选列
    $("#setBtn").click(function () {
        //获取当前显示表格

        $('#X_Table').XGrid('filterTableHead');
    })
    /**
     * 下载模板
     */
    $('#downTemplete').on('click', function () {
        window.open("/proxy-gsp/gsp/coldChainTransport/downTemplate")
    });
    var colModel = [
        {
            name: "saleOutOrderNo"
        }, {
            name: "saleOrderNo"
        }, {
            name: "transporter"
        }, {
            name: "telPhone"
        }, {
            name: "busNum"
        }, {
            name: "transTemperature"
        }, {
            name: "carrierOrg"
        }, {
            name: "departDate"
        }, {
            name: "departTemperature"
        }, {
            name: "sendDate"
        }, {
            name: "sendAddress"
        }, {
            name: "receiveOrg"
        }, {
            name: "receiveAddress"
        }, {
            name: "receiver"
        }, {
            name: "invoiceNo"
        }, {
            name: "drugNum"
        }, {
            name: "transportWay"
        }, {
            name: "operator"
        }, {
            name: "arriveDate"
        }, {
            name: "arriveTemperature"
        }];
    var allColModelA = JSON.parse(JSON.stringify(colModel));
    //导入
    $("#implBtn").on("click", function () {
        dialog({
            url: '/proxy-gsp/gsp/coldChainTransport/toFileupload',
            title: '批量导入',
            width: 600,
            height: 300,
        /*    onclose: function (data) {
                if (this.returnValue) {
                    var dataArray = this.returnValue;
                    var rows = $('#X_Table').XGrid('getRowData');
                    for (var x = 0; x < dataArray.length; x++) {
                        for (var i = 0; i < rows.length; i++) {
                            if (dataArray[x].productId == rows[i].productId) {
                                $('#X_Table').XGrid('delRowData', dataArray[x].productId);
                            }
                        }
                    }
                    for (var x = 0; x < dataArray.length; x++) {
                        $('#X_Table').XGrid('addRowData', {id: x + 1}, 'last');
                        $('#X_Table').XGrid('setRowData', x + 1, dataArray[x]);
                    }
                }
            }*/
        }).showModal();
    });

    /* 导出 */
    $('#exportBtn').on('click', function () {
        var tableId = "X_Table";
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            var len = Number($('#totalPageNum').text());
            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                /*  if (!data.length) {
                      data = [data];
                  }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                len = data.length;
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            console.log(colName);

            var obj = {
                startTime: $("#starttime").val(),
                endTime: $("#endTime").val(),
                orderNo: $("#order_no").val(),
                selectData: data,
                colName: colName,
                colNameDesc: colNameDesc
            }
           // obj["nameModel"] = nameModel;                   
            // 是否超出限制            
            utils.exportAstrictHandle('X_Table', len, 1).then( () => {
	            return false;
	        }).catch( () => {
	        	httpPost("/proxy-gsp/gsp/coldChainTransport/exportDrugRecordsList", obj);
	        }); 
        });
    });


    //导出
    /*$('#exportBtn').on('click', function () {

        //第一个选项卡
        //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
        var ck = false;
        // copy this parameter and the below buttons
        var nameModel = "";
        var exportColNames = ["销售出库单号", "销售订单号", "运输员", "联系方式", "车牌号", "运输在途温度", "承运单位", "启运日期", "启运温度",
            "发货日期", "发货地址", "收货单位","收货地址","收货人","货单号","药品件数","运输方式","委托经办人","到货时间","到货温度"
        ];
        addHtmlA(exportColNames);
        dialog({
            content: $("#setCol1"),
            title: '筛选列',
            width: $(window).width() * 0.4,
            data: 'val值',
            cancelValue: '取消',
            cancel: true,
            okValue: '导出',
            ok: function () {
                var newColName = [], newColModel = [];
                $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                    if ($(this).is(":checked")) {
                        nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                    }
                });

                if(nameModel.length == 0){
                    utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }

                var obj = {
                    startTime: $("#starttime").val(),
                    endTime: $("#endTime").val(),
                    orderNo: $("#order_no").val()

                }
                // obj["pageNum"] = "1";
                // obj["pageSize"] = "1000000";
                obj["nameModel"] = nameModel;
                httpPost("/coldChainTransport/exportDrugRecordsList", obj);
            },
// copy button to other dialogues
            button: [
                {
                    id: 'chooseAll',
                    value: '全选',
                    callback: function () {
                        //debugger;
                        if(!ck){
                            $("#checkRow input").prop("checked",false);
                            ck = true;
                        }else if(ck){
                            $("#checkRow input").prop("checked","checked");
                            ck = false;
                        }else{
                            return false;
                        };
                        return false;
                    }
                }
            ]
            //copy ends here

        }).showModal();



    });*/

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol1" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }



})








