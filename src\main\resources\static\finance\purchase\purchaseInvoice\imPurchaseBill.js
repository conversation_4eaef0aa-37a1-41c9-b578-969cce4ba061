$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    /**
     * 下载模板
     */
    $('#downTemplete').on('click', function () {
        window.open("/proxy-finance/finance/purchase/invoice/downTemplate")
    });
    $("#exportBatchNo").val("");
   // $("#exportBatchNo").val("FPIM200209000006");
    //导入
    $("#implBtn").on("click", function () {

        if ($('#keyWord').val() == '') {
            utils.dialog({content: '请选择供应商名称！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        // $("#exportBatchNo").val('123456');
        utils.dialog({
            url: '/proxy-finance/finance/purchase/invoice/toFileupload',
            title: '批量导入',
            width: 600,
            height: 300,
            // cancelValue: '取消',
            // cancel: true,
            // okValue: '确认',
            // ok: function () {
            //     doUpload(fileval);
            // },
            onclose: function () {
                if (this.returnValue) {
                    console.log(this.returnValue)
                    doUpload(this.returnValue);
                }
            }

        }).showModal();
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/invoice/findImStoreInDetailList',
            datatype:'json',
            postData: {
                "exportBatchNo": $('#exportBatchNo').val()
            },
            page: 1
        }).trigger("reloadGrid");
    });

    var totalTable = z_utils.totalTable;

    var d;
    var colNames = ['<span class="danger">*</span> 采购单据号', '<span class="danger">*</span> 采购单据行号','<span class="danger">*</span> 商品编码',
            '商品名称', '<span class="danger">*</span> 本次开票数量', 'id'],
        colModel = [
            {
                name: 'receiptNo',
                sortable: false
            },
            {
                name: 'receiptLineNo'
            },
            {
                name: 'productCode',
            },
            {
                name: 'productName'
            },
            {
                name: 'thisInvoicedCount'
            },
            {
                name: 'exportBatchNo',
                hidden: true
            }
            , {
                name: 'id',
                hidden: true,
                hidegrid: true
            }
        ];

    var $X_Table = $('#X_Table');
    window.isFirst = true;

    // 设置table高度
    utils.setTableHeight('X_Table');
    $X_Table.XGrid({
        url: '/proxy-finance/finance/purchase/invoice/findImStoreInDetailList',
        datatype:'json',
        postData: {
            "exportBatchNo": $('#exportBatchNo').val()
        },
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        multiselect: false,
        attachRow:true,
        maxheight: false,
        onSelectRow: function (e, c, a, b) {
            console.log('单击行事件', e, c, a, b);
        },

        gridComplete: function (data) {
            var _this = $(this);
            // setTimeout(function () {
            //     if (_this.XGrid('getRowData').length === 0) {
            //         utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
            //     }
            // }, 200);
            dataBuffer();
        }

    });

    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/invoice/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result);
            $("#keyWord").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#keyWord").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    //放大镜查询
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

//供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#keyWord").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();
    }


    $('#checkBtn').bind('click', function () {

        //校验是否已导入采购明细，如未导入，则toast提示：“请导入采购明细数据

        if ($('#X_Table').XGrid('getRowData').length === 0) {
            utils.dialog({content: '请导入采购明细数据', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#keyWord').val() == '') {
            utils.dialog({content: '请选择供应商名称！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        //加载总数量
        $.ajax({
            url: '/proxy-finance/finance/purchase/invoice/validateImportStoreInDetail',
            dataType: 'json',
            // timeout: 8000, //6000
            data:{"imBatchNo":$('#exportBatchNo').val(),
                "supplierNo":$("#keyWord").val()
            },
            success: function (data) {
                utils.dialog({content: '数据校验完毕', quickClose: true, timeout: 2000}).show();

                // alert(data.code);
                if (data.code==0){
                    $('#X_Table3').XGrid('setGridParam', {
                        url: '/proxy-finance/finance/purchase/invoice/findImStoreInDetailList',
                        postData: {
                            "exportBatchNo": $('#exportBatchNo').val()
                        },

                        page: 1
                    }).trigger("reloadGrid");


                }
            },
            error: function () {

                utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
            }
        });

    });


    var colNames3 =['id', '校验结果', '采购单据号',  '采购单据行号','采购单据状态', '供应商编码', '供应商名称', '商品编码', '商品名称', '规格', '小包装单位', '含税单价',
            '小包装数量','本次开票数量','已开票数量',
            '金额合计', '税额合计', '价税合计', '校验结果说明'
        ],
        colModel3 = [
            {
                name: 'id',
                hidden: true
            },
            {
                name: 'checkResultStatus',
                formatter: function (value) {
                    if (value == '0') {
                        return '通过';
                    } else if (value == '1') {
                        return '<span class="danger">不通过</span>';
                    } else {
                        return '';
                    }
                },
                unformat: function (value) {
                    if (value == '通过') {
                        return '0';
                    } else if (value == '<span class="danger">不通过</span>') {
                        return '1';
                    } else {
                        return '';
                    }
                }
            },
            {
                name: 'receiptNo'
            },
            {
                name: 'receiptLineNo'
            },{
                name: 'ticketStatus',
                formatter: function (value) {
                    if (value != '') {
                        if (value == '0' || value == null) {
                            return '未开发票';
                        } else if (value == '1') {
                            return '部分开票';
                        } else if (value == '2') {
                            return '已开票';
                        } else if (value == '3') {
                        return '不需开票';

                    } else {
                        return '';
                    }
                    }
                },
                unformat: function (value) {
                    if (value != '') {
                        if (value == '未开发票') {
                            return '0';
                        } else if (value == '部分开票') {
                            return '1';
                        } else if (value == '已开票') {
                            return '2';
                        } else {
                            return '';
                        }
                    } else {
                        return '';
                    }
                }
            },
            {
                name: 'supplierNo'
            }, {
                name: 'supplierName'
            },
            {
                name: 'productCode'
            }, {
                name: 'productName'
            }, {
                name: 'specification'
            }, {
                name: 'amallPackingUnit'
            }, {
                name: 'priceUnit'
                /* formatter: function (val) {
                     return parseFloat(val).formatMoney('2', '', ',' ,'.');
                 },
                 unformat: function (val) {
                     return val.replace(/,/g ,'');
                 }*/
            }, {
                name: 'smallPackageQuantity',
                /*formatter: function(val,a,b,c){
                    if(!val){
                        return '0';
                    }else if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val);
                    }else{
                        return parseFloat(val);
                    }
                }*/
            },{
                name: 'thisInvoicedCount',
                formatter: function(val,a,obj,c){
                    if(!val){
                        return parseFloat(obj.productPackInStoreCount - obj.invoicedCount)
                    }else {
                        return val
                    }
                }
            },{
                name: 'invoicedCount',
                formatter: function(val,a,obj,c){
                    if(val){
                        return val;
                    }else {
                        return "0";
                    }
                }
            }, {
                name: 'aggregateAmount',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.toString().includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'totalAmount',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.toString().includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'totalTaxPrice',
                formatter: function(val,a,b,c){
                    if((b.moveTypeCode == 103 || b.moveTypeCode == 107) && !val.toString().includes('-') && (val != '0.00')){
                        return '-'+parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }else{
                        return parseFloat(val).formatMoney('2', '', ',' ,'.');
                    }
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'checkStatus'
            }, {
                name: 'exportBatchNo',
                hidden: true,
                hidegrid: true
            }];

    var $X_Table3 = $('#X_Table3');
    // 设置table高度
    utils.setTableHeight('X_Table3');
    $X_Table3.XGrid({
        url: '/proxy-finance/finance/purchase/invoice/findImStoreInDetailList',
        type: "post",
        colNames: colNames3,
        colModel: colModel3,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        // multiselect: true,
        attachRow:false,
        maxheight: false,
        rownumWidth: 30,
        multiselect:true,
        shrinkToFit:false,  //设置为true时，列数充满表格，当列数较多时，只会缩小列宽，并不会出现滚动条，需要将属性设置为false
        onSelectRow: function (e, c, a, b) {
            console.log('单击行事件', e, c, a, b);
        },
        gridComplete: function () {
            var data = $(this).XGrid('getRowData');
            $(data).each((index,item) => {
                if (item.checkResultStatus == '0') {
                $('#X_Table3 #' + item.id).find('input[type=checkbox]').click();
            }else{
                $('#X_Table3 #' + item.id).find('input[type=checkbox]').prop('disabled',true);
                $('#X_Table3 #' + item.id).attr('nosele',true);
            }
        })


            // var _this = $(this);
            // setTimeout(function () {
            //     if (_this.XGrid('getRowData').length === 0) {
            //         utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
            //     }
            // }, 200);

        }

    });
    //跳转页面
    $('#toDetail').bind('click', function () {
        //校验是否存在校验通过数据，如存在，则跳转采购发票录入详情页面，否则toast提示：请选择校验通过采购单据明细数据

        var data3 = [].concat($('#X_Table3').XGrid('getSeleRow'));
        console.log(data3);
        //判断有没有选择入库单，没有，不能跳转页面
        if (data3.length == 0) {
            utils.dialog({content: '请选择校验通过采购单据明细数据！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        var arr = new Array();

        $.each(data3, function (index, item) {
            arr.push($(this).attr("id"));
        });
        console.log("arr"+arr);
        var parames = [];
        parames.push({name: "storeInDetailImportVoList", value: JSON.stringify(arr)});
        parames.push({name: "turnTime", value: $("#turnTime").val()});
        var dataBuffer = {
            keyWord : $('#keyWord').val(),
            supplierName:$("#supplierName").val(),
            exportBatchNo:$('#exportBatchNo').val()
        };
        parent.financeImpPurchaseInvoiceToAdd = dataBuffer;
        console.log(parames);
        console.log(parent.financeImpPurchaseInvoiceToAdd);
        Post("/proxy-finance/finance/purchase/invoice/toInvoice", parames);

    })

    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }

    //加载缓存
    function dataBuffer() {
        var data = parent.financeImpPurchaseInvoiceToAdd;
        if (!data) return false
        console.log("缓存"+data);
        //查询条件
        var exportBatchNo = data.exportBatchNo;
        var keyWord = data.keyWord;
        var supplierName = data.supplierName;

        $("#exportBatchNo").val(exportBatchNo);
        $("#keyWord").val(keyWord);
        $("#supplierName").val(supplierName);

        parent.financeImpPurchaseInvoiceToAdd=null;
        if (keyWord && exportBatchNo) {
            $('#X_Table').XGrid('setGridParam', {
                url: '/proxy-finance/finance/purchase/invoice/findImStoreInDetailList',
                postData: {
                    "exportBatchNo": exportBatchNo
                },
                page: 1
            }).trigger("reloadGrid");
            setTimeout(function(){
                // 能获得点击后的状态
                $("#checkBtn").trigger('click');
            },2000);


        }
    }

    function doUpload(dom) {
        var name = dom.value;
        if(name == ''){
            dialog({
                title:"提示",
                width:200,
                height:70,
                content:'请选择文件',
                okValue: '确定',
                ok: function () {
                }
            }).showModal();
            return false;
        }
        var formData = new FormData();
        formData.append("file",dom.files[0]);
        formData.append("name",name);
        $('#uploadBtn').attr('disabled',"true");

        $.ajax({
            url : '/proxy-finance/finance/purchase/invoice/upLoadStoreInDetailExcel',
            type : 'POST',
            data : formData,
            processData : false,
            contentType : false,
            beforeSend:function(){
                console.log("正在进行，请稍候");
            },
            success : function(data) {
                $('#uploadBtn').removeAttr("disabled");
                console.log(data);
                if(data.code==0){
                    utils.dialog({
                        title: '提示',
                        content: '<div class="totalWrap" style="height:100%;">' +
                        '      <div class="totalContent">导入数据成功</div>' +
                        '</div>',
                        width: 360,
                        height:150,
                        cancelValue: '取消',
                        cancel: function () {
                        },
                        okValue: '确认',
                        ok: function () {
                            $("#exportBatchNo").val(data.result.imBatchNo);
                            //  parent.window.location.href = '/coldChainTransport/toDrugRecords';
                            $('#X_Table').XGrid('setGridParam', {
                                url: '/proxy-finance/finance/purchase/invoice/findImStoreInDetailList',
                                datatype:'json',
                                postData: {
                                    "exportBatchNo": $("#exportBatchNo").val()
                                },
                                page: 1
                            }).trigger("reloadGrid");
                           /* setTimeout(function () {
                                // parent.window.location.href = '/coldChainTransport/toDrugRecords';
                                var dialog = parent.dialog.get(window);
                                dialog.close();
                            }, 500);*/
                            return;
                        }
                    }).showModal();
                    // utils.dialog({content:"导入数据成功", quickClose: true, timeout: 2000}).showModal();


                }else {
                    utils.dialog({
                        title: '提示',
                        content: '<div class="totalWrap" style="height:100%;">' +
                        '      <div class="totalContent" style="overflow-y:scroll;height: inherit;">'+data.result.errorMsg+'</div>' +
                        '</div>',
                        width: 360,
                        height:100,
                        cancelValue: '取消',
                        cancel: function () {
                        },
                        okValue: '确认',
                        ok: function () {
                        }
                    }).showModal();
                    return;
                }
            },error:function () {
                $('#uploadBtn').removeAttr("disabled");
                utils.dialog({
                    title: '提示',
                    content: '<div class="totalWrap" style="height:100%;">' +
                    '      <div class="totalContent">导入数据失败</div>' +
                    '</div>',
                    width: 360,
                    height:150,
                    cancelValue: '取消',
                    cancel: function () {
                    },
                    okValue: '确认',
                    ok: function () {
                        parent.window.location.href = '/coldChainTransport/toDrugRecords';
                    }
                }).showModal();
                //  utils.dialog({content:"上传失败", quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
})
