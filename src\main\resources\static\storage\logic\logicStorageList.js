let colName = [],colModel = [];
/* 合计计算  */
var totalTable = z_utils.totalTable;
$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    setPrescriptionClassificationVal();
    /* 日期初始化 */
    z_utils.initDate('begint', 'endt')
    getTotalNum();

    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    colNames = ['逻辑批次号','入库单号','采购入库单号','商品大类','原商品编码','商品编码','条码','标准库ID','商品名称','通用名','商品规格','剂型','单位','商品产地','批号','中包装数量',
        '业务类型','生产厂家','生产日期','有效期至','灭菌批号','库存数量','不含税入库单价','供应商名称','入库日期','在库时长（天）','批准文号','存储条件','商品定位描述',
        '销售贡献分类','厂商定位','处方分类','品类一级分类','品类二级分类','品类三级分类'];

    //, '不含税收入金额 '
    colModel = [
        {
            name: 'logicBatch',
            index: 'logicBatch',
            width: 250
        }, {
            name: 'intoOrderCode',
            index: 'intoOrderCode',
            width: 200
        }, {
            name: 'purchaseOrder',
            index: 'purchaseOrder',
            width: 200
        }, {
            name: 'drugClassName',
            index: 'drugClassName'
        },
        {
            name: 'originalDrugCode',
            index: 'originalDrugCode'
        }, {
            name: 'drugCode',
            index: 'drugCode'
        }, {
            name: 'barCode',
            index: 'barCode'
        },{
            name: 'standardProductId',
            index: 'standardProductId'
        },{
            name: 'drugName',
            index: 'drugName'
        }, {
            name: 'commonName',
            index: 'commonName'
        },{
            name: 'productSterilization',
            index: 'productSterilization',
            width: 250
        },{
            name: 'dosageName',
            index: 'dosageName'
        },{
            name: 'packagingUnit',
            index: 'packagingUnit'
        }, {
            name: 'producingArea',
            index: 'producingArea'
        }, {
            name: 'batchNum',
            index: 'batchNum'
        }, {
            name: 'middlePackageNum',
            index: 'middlePackageNum'
        }, {
            name: 'channelId',
            index: 'channelId'
        },{
            name: 'manufacturerName',
            index: 'manufacturerName',
            width: 260
        },{
            name: 'productDate',
            index: 'productDate',
            formatter: dateFormatter
        },{
            name: 'validateDate',
            index: 'validateDate',
            formatter: dateFormatter
        }, {
            name: 'sterilizationBatchNum',
            index: 'sterilizationBatchNum',
        }, {
            name: 'surplusAmount',
            index: 'surplusAmount',
            formatter:function(e){
                return Number(e).toFixed(2);
            }
        }, {
            name: 'intoPrice',
            index: 'intoPrice',
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 260
        }, {
            name: 'intoTime',
            index: 'intoTime',
            formatter: dateFormatter
        }, {
            name: 'lengthOfLibrary',
            index: 'lengthOfLibrary'
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width: 180
        }, {
            name: 'storageConditions',
            index: 'storageConditions'
        }, {
            name: 'commodityPosition',
            index: 'commodityPosition'
        }, {
            name: 'salesContribution',
            index: 'salesContribution'
        }, {
            name: 'manufacturerPosition',
            index: 'manufacturerPosition'
        }, {
            name: 'prescriptionClassificationName',
            index: 'prescriptionClassificationName'
        }, {
            name: 'oneClassification',
            index: 'oneClassification'
        }, {
            name: 'twoClassification',
            index: 'twoClassification'
        }, {
            name: 'thirdClassification',
            index: 'thirdClassification'
        }
    ];
    $('#table_a').XGrid({
        url: "/proxy-storage/storage/logic/queryList",
        colNames: colNames,
        colModel: colModel,
        // rownumbers: true,//是否展示序号
        selectandorder: true,
        key: 'id',
        // rowNum: 20,
        // rowList: [20, 50, 100],
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function (res) {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            // var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut','inventoryAmount','inventorySum'];
            var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
            $('#totalTableNum').text($('#totalPageNum').text()+'条')
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            //console.log(id, dom, obj, index, event)
        }
    });


    // 筛选列
    $('#set_tb_rows').click(function () {
        //获取当前显示表格
        $('#table_a').XGrid('filterTableHead');
    })



    //获取当前tab  的下标
    function getTabInd() {
        for (var i = 0; i < $('.nav-tabs li').length; i++) {
            if ($('.nav-tabs li').eq(i).hasClass('active')) {
                _ind = $('.nav-tabs li').eq(i).index();
            }
        }
        return _ind;
    }


    /* 查询 */
    $('#searchBtn').on('click', function () {
        initTable()
        getTotalNum();
    });

    // 设置处方分类下拉内容
    function setPrescriptionClassificationVal(){
        $.ajax({
            url: '/proxy-sysmanage/sysmanage/dict/querycommonnotpage?type=6&CommonName=',
            type: 'get',
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    var list=data.result;
                    var str='<option value="">&nbsp;全部</option>';
                    list.forEach(function(item,index){
                        str +='<option value="'+item.id+'">&nbsp;'+item.name+'</option>'
                    })
                    $('#prescriptionClassification').html(str)
                } else {
                    utils.dialog({
                        title: '提示',
                        width: '200px',
                        content: '请求失败',
                        okValue: '确定',
                        ok: function () {
                        }
                    }).showModal();
                }
            },
            error: function () {
                $('#' + fileBtn).val('');
                d.close().remove();
            }
        })
    }

    function initTable(){
        $('#table_a').XGrid({
            url: "/proxy-storage/storage/logic/queryList",
            postData: {
                drugCode:$('#drugCode').val(),
                productName:$('#productName').val(),
                oldProductCode:$('#oldProductCode').val(),
                batchNum:$('#batchNum').val(),
                logicBatch:$('#logicBatch').val(),
                supplierName:$('#supplierName').val(),
                intoOrderCode:$('#intoOrderCode').val(),
                drugType:$("#drugType").find("option:selected").val(),
                manufacturerName:$('#manufacturerName').val(),
                dosageForm:$("#dosageForm").find("option:selected").val(),
                channelId:$("#channelId").find("option:selected").val(),
                prescriptionClassification:$("#prescriptionClassification").find("option:selected").val()
            },
            colNames: colNames,
            colModel: colModel,
            altRows: true, //设置为交替行表格,默认为false
            pager: '#grid_pager_a',
            selectandorder: true,//是否展示序号，多选
            attachRow:true,
            ondblClickRow: function (id, dom, obj, index, event) {

            },
            onSelectRow: function (id, dom, obj, index, event) {
            },
            gridComplete: function (res) {
                var data = $(this).XGrid('getRowData');
                // var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut','inventoryAmount','inventorySum'];
                var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
                var lastRowEle = $(this).find("tr[nosele=true]");
                lastRowEle.find("td:first-child").text('合计');
                sum_models.forEach(function (item,index) {
                    lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                });
                $('#totalTableNum').text($('#totalPageNum').text()+'条')
            },
        });
    };

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#table_a').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });
            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
            } else {
                //如果一个也没选就导出全部
                data='';
            }
            //获取form数据
            var formData = {
                colName:colName,
                colNameDesc:colNameDesc,
                selectData:data,
                approvalNumber:$("#approvalNumber").val(),
                drugCode:$('#drugCode').val(),
                productName:$('#productName').val(),
                oldProductCode:$('#oldProductCode').val(),
                batchNum:$('#batchNum').val(),
                logicBatch:$('#logicBatch').val(),
                supplierName:$('#supplierName').val(),
                intoOrderCode:$('#intoOrderCode').val(),
                drugType:$("#drugType").find("option:selected").val(),
                manufacturerName:$('#manufacturerName').val(),
                dosageForm:$("#dosageForm").find("option:selected").val(),
                channelId:$("#channelId").find("option:selected").val(),
                prescriptionClassification:$("#prescriptionClassification").find("option:selected").val()
            }
            var colHeader= colNameDesc.join(",")
            var colName= colName.join(",")
            var formData2 = {
                moduleName: 'storage',
                taskCode: '1001',
                selectData:data,
                colHeader: colHeader,
                colName: colName,
                fileName: '逻辑批次库存导出',
                approvalNumber:$("#approvalNumber").val(),
                drugCode:$('#drugCode').val(),
                productName:$('#productName').val(),
                oldProductCode:$('#oldProductCode').val(),
                batchNum:$('#batchNum').val(),
                logicBatch:$('#logicBatch').val(),
                supplierName:$('#supplierName').val(),
                intoOrderCode:$('#intoOrderCode').val(),
                drugType:$("#drugType").find("option:selected").val(),
                manufacturerName:$('#manufacturerName').val(),
                dosageForm:$("#dosageForm").find("option:selected").val(),
                channelId:$("#channelId").find("option:selected").val(),
                prescriptionClassification:$("#prescriptionClassification").find("option:selected").val()
            };
            httpPost("/proxy-storage/storage/logic/queryExport",formData2);
        });
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#drugCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })
})
//查询总合计数目
function getTotalNum () {
    //加载总数量
    $.ajax({
        url: '/proxy-storage/storage/logic/logicStorageStatisticsTotal',
        dataType: 'json',
        timeout: 10000, //6000
        data:{
            drugCode:$('#drugCode').val(),
            productName:$('#productName').val(),
            approvalNumber:$("#approvalNumber").val(),
            oldProductCode:$('#oldProductCode').val(),
            batchNum:$('#batchNum').val(),
            logicBatch:$('#logicBatch').val(),
            supplierName:$('#supplierName').val(),
            intoOrderCode:$('#intoOrderCode').val(),
            drugType:$("#drugType").find("option:selected").val(),
            manufacturerName:$('#manufacturerName').val(),
            dosageForm:$("#dosageForm").find("option:selected").val(),
            channelId:$("#channelId").find("option:selected").val(),
            prescriptionClassification:$("#prescriptionClassification").find("option:selected").val()
        },
        success: function (data) {
            if (data.code==0){
                $("#totalStockNum").text(data.result.totalStockNum);
                $("#totalProductNum").text(data.result.totalProductNum);
            }else {
                $("#totalStockNum").text("0.00");
                $("#totalProductNum").text("0.00");
            }
        },
        error: function () {
            $("#totalStockNum").text("0.00");
            $("#totalProductNum").text("0.00");
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

function  btn_output_list(){
    utils.dialog({
        title: '导出列表',
        url: '',
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
}

function dateFormatter(inputTime) {
    if (inputTime == null || inputTime == "")
        return "";
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    var minute = date.getMinutes();
    var second = date.getSeconds();
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d;
    // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
}

//加载更多
function btn_getMore() {
    parent.showLoading()
    let rowdata = $('#table_a').XGrid('getRowData'),
        lastId = rowdata.length ? rowdata[rowdata.length-1].id: null;
    if(lastId){
        $.ajax({
            type: 'post',
            url: '/proxy-storage/storage/logic/queryList',
            data: {
                drugCode:$('#drugCode').val(),
                productName:$('#productName').val(),
                oldProductCode:$('#oldProductCode').val(),
                batchNum:$('#batchNum').val(),
                logicBatch:$('#logicBatch').val(),
                supplierName:$('#supplierName').val(),
                intoOrderCode:$('#intoOrderCode').val(),
                drugType:$("#drugType").find("option:selected").val(),
                manufacturerName:$('#manufacturerName').val(),
                dosageForm:$("#dosageForm").find("option:selected").val(),
                channelId:$("#channelId").find("option:selected").val(),
                prescriptionClassification:$("#prescriptionClassification").find("option:selected").val(),
                lastId:lastId,
                pageNum:$('#pg_grid_pager_a').find('input[type=number]').val() + 1,
                pageSize:20,
            },
            success: function (res) {
                if(res.result.list.length){
                    let newArr =[];
                    newArr = rowdata.concat(res.result.list);
                    $('#table_a').XGrid('clearGridData');
                    $('#table_a tr[nosele=true]').remove()
                    for(let i = 0; i<newArr.length; i++){
                        $('#table_a').XGrid('addRowData',newArr[i])
                    }
                    // var heji_tr = $('#table_a tr[nosele=true]').clone();

                    // var sum_models = ['amountIn','notaxSumIn','amountOut','notaxSumOut'];
                    // heji_tr.find("td:first-child").text('合计');
                    // sum_models.forEach(function (item,index) {
                    //     heji_tr.find("td[row-describedby="+item+"]").text(totalTable(newArr,item))
                    // });
                    // $("#totalTableNum").text(Number(res.result.total)+'条');
                    // $('#table_a tbody').append(heji_tr)
                }else {
                    utils.dialog({
                        title: '提示',
                        content: '没有更多数据了！',
                        okValue: '确定',
                        ok: function () {}
                    }).showModal();
                }
            },
            error: function (err) {
                console.log(err)
            },
            complete:function () {
                parent.hideLoading();
            }

        })
    }else{
        utils.dialog({
            title: '提示',
            content: '无查询结果！',
            okValue: '确定',
            ok: function () {}
        }).showModal();
    }
}
