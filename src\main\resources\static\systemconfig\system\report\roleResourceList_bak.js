// 请求基本路径
var base_req_url = "/proxy-sysmanage/sysmanage/roleResouceCtr";

// 进入即执行
$(function() {
	queryRoleList();
	// 显示样式
	if (isEmpty($("#sys_dept").val())) {
		$("#sys_dept").attr("disabled", "disabled");
	}
});

// 获取角色
function queryRoleList(deptCode) {
	if(deptCode == null) {
		deptCode = $('#sys_dept').val();
	}
	$.ajax({
		url: base_req_url + "/queryRoleList",
		data: {
			deptCode: deptCode
		},
		dataType: 'json',
		type: 'post',
		success: function(data) {
			if (data.code == 0) {
				var roleList = data.result;
				var opHtml = '';
				if (roleList != null && roleList.length > 0) {
					var html = "";
					$.each(roleList, function(index, role) {
						html += "<option value='" + role.id + "'>" + role.name + "</option>";
					});
					opHtml = html;
				}
				if (opHtml == '') {
					opHtml = '<option value="-1">无角色</option>';
					$("#sys_role").attr("disabled", "disabled");
				} else {
					$("#sys_role").removeAttr("disabled");
				}
				$("#sys_role").html(opHtml);
				renderData();
			} else {
				utils.dialog({
					content: result.msg,
					quickClose: true,
					timeout: 2000
				}).showModal();
			}
		}
	});
};

// 绑定部门change事件
$("#sys_dept").change(function() {
	queryRoleList($("#sys_dept").val());
});

//绑定查询click事件
$("#searchBtn").click(function() {
	renderData();
});

//绑定导出click事件
$("#sys_export").click(function() {
	exportRoleResourceReport(1);
});

//绑定导出所有click事件
$("#sys_export_all").click(function() {
	exportRoleResourceReport();
});

//导出Excel
function exportRoleResourceReport(exportType) {
	var appCode = $("#sys_org_app_code").val();
	var roleId = $("#sys_role").val();
	if (isEmpty(exportType)) {
		roleId = null;
	}
	 
    if (isEmpty(exportType)) {
    	$.ajax({
    		url: base_req_url + "/roleResourceReportTotalCount",
    		data: {
    			appCode: appCode,
    			roleId: roleId
    		},
    		dataType: 'json',
    		type: 'post',
    		success: function(data) {
    			var msg = data.msg;
    			var totalCount = data.result;
    			// 是否超出限制    
    		    utils.exportAstrictHandle('role_resource_table', totalCount, 1).then( () => {
    		        return false;
    		    }).catch( () => {
    		    	window.location.href = base_req_url + "/exportRoleResourceReport?appCode=" + appCode;
    		    });	
//    			if (data.code == 0) {				
//    				if(!overLimit) {
//    					window.location.href = base_req_url + "/exportRoleResourceReport?appCode=" + appCode;
//    				} 
//    			}
//    			if(data.code != 0 || overLimit) {
//    				utils.dialog({content: msg, quickClose: true, timeout: 4000}).showModal();
//    			}
    		}
    	});		
	} else {		
		// 是否超出限制    
	    utils.exportAstrictHandle('role_resource_table', Number($('#totalPageNum').text()), 1).then( () => {
	        return false;
	    }).catch( () => {
	    	window.location.href = base_req_url + "/exportRoleResourceReport?appCode=" + appCode + "&roleId=" + roleId;
	    });		
	}	
}

//判断字符是否为空的方法
function isEmpty(obj){
    if(typeof obj == "undefined" || obj == null || obj == ""){
        return true;
    }else{
        return false;
    }
}

// 渲染表格
function renderData(roleId) {
	if (roleId == null) {
		roleId = $("#sys_role").val();
	};
	var orgAppCode = $("#sys_org_app_code").val();
	$('#role_resource_table').XGrid({
		url: base_req_url + "/queryRoleResourceReportListPage",
		mtype: "POST",
		postData: {
			roleId: roleId,
			orgAppCode: orgAppCode
		},
		colNames: ['机构', '部门', '角色', '模块', '菜单权限', '菜单对应操作权限', '角色拥有操作权限'],
		colModel: [{
			name: 'orgName'
		}, {
			name: 'deptName'
		}, {
			name: 'roleName'
		}, {
			name: 'resourceModuleName'
		}, {
			name: 'resourceMenuName',
		}, {
			name: 'resourceBtnName',
		}, {
			name: 'roleResourceBtnName'
		}],
		rowNum: 20,
		rowList: [20, 50, 100],
		rownumbers: true,
		altRows: true,
		pager: '#grid-pager',
		ondblClickRow: function(id, dom, obj, index, event) {
		},
		onSelectRow: function(id, dom, obj, index, event) {
		}
	});
};
