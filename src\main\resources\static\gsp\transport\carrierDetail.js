$(function(){
    $('div[fold=head]').fold({sub: 'sub'});
	var carrierCode = $('#carrierCode').val();	
	//附件管理
    $('#table1').XGrid({
    	data: [],    	
        colNames: ['','证件类型','最后上传日期', '最后操作员', '查看附件', '附件地址集合'],
        colModel: [{
            name: 'id',
            index: 'id',
            key: true,
            hidden: true
        }, {
            name: 'licenseType',
            index: 'licenseType',
            rowtype:'#licenseType'
        }, {
            name: 'uploadDate',
            index: 'uploadDate',
            rowtype:'#uploadDate'
        }, {
            name: 'createUserName',
            index: 'createUserName',
            rowtype:'#createUserName'
        }, {
            name: 'licensePhotoCount',
            index: 'licensePhotoCount',
            formatter: function (value) {
	          var str = '无';
	          if (value) {
	              str = '<a href="javascript:;" onclick="showImg(this);">' + value + '</a>';
	          }
	          return str;
		    },
		    unformat: function (e) {
	          e = e.replace(/<[^>]+>/g, '');
	          if (e == '无') {
	              e = 0;
	          }
	          return e;
		    }       	
        }, {
            name: 'licensePhotoList',
            index: 'licensePhotoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],        
        rowNum: 100,
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_page3',
        gridComplete: function () {
        	
        }
    }); 
    //运输员信息
    $('#table2').XGrid({
        data: [],
        colNames: ['序号', '姓名', '性别标识', '性别', '驾驶证号','驾照类型', '维护日期'],
        colModel: [{
            name: 'id',
            index: 'id',
            key: true,
            hidden: true
        }, {
            name: 'driverName',
            index: 'driverName'
        }, {
            name: 'sex',
            index: 'sex',
            hidden: true
        }, {
            name: 'sexValue',
            index: 'sexValue'
        }, {
            name: 'drivingLicenseCode',
            index: 'drivingLicenseCode'
        },{
            name: 'drivingLicenseType',
            index: 'drivingLicenseType'
        }, {
            name: 'maintainDate',
            index: 'maintainDate'
//            	,
//            formatter:function (e){
//    	        if (e != null && e !="") {
//    	            return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
//    	        } else {
//    	            return "";
//    	        }
//    	    }
        }],
        rowNum: 100,
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_page4',
        gridComplete: function () {

        }
    });
    //运输车辆信息
    $('#table3').XGrid({
        data: [],
        colNames: ['', '车牌号', '车辆类型', '维护日期'],
        colModel: [{
            name: 'id',
            index: 'id',
            key: true,
            hidden: true
        }, {
            name: 'carNumber',
            index: 'carNumber'
        }, {
            name: 'vehicleType',
            index: 'vehicleType'
        }, {
            name: 'maintainDate',
            index: 'maintainDate'
//            	,
//            formatter:function (e){
//    	        if (e != null && e !="") {
//    	            return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
//    	        } else {
//    	            return "";
//    	        }
//    	    }
        }],
        rowNum: 100,
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_page4',
        gridComplete: function () {

        }
    });
    // 返回
    $("#close").on("click",function(){
        utils.closeTab();
    });
  
    //审核通过
    $('#auditApproval').on('click',function(){     	
    	utils.dialog({
            title: '审核通过',
            content: $('#tg_dialog'),
            okValue: '确定',
            width: 500,
            height: 260,
            ok: function () {
            	var approveComment = $("#approveComment").val();
            	if(!isNotNull(approveComment)) {		
            		utils.dialog({content: '审核留言不能为空', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}
//            	console.log(carrierCode +','+ $("#approveComment").val());
            	$.ajax({            
                    type: "POST",                    
                    url: "/proxy-gsp/gsp/transport/carrierApprove",
                    data: {
                    	carrierCode:carrierCode,
                    	comment:$("#approveComment").val(),
                    	taskId:$("#taskId").val(),
                    	processId:$("#processId").val(),
                    	edit:$("#edit").val(),
                    	close:$("#close").val(),
                    	agree:$("#agree").val(),
                    	reject:$("#reject").val(),
                    	report:$("#report").val()
                    },
                    success: function (result) {            	
                        if (result.code == 0) { 
                        	utils.dialog({content: '审核通过！', timeout: 2000}).showModal();
		                    setTimeout(function(){
		                        utils.closeTab();
		                    },2000)                        	
                        } else {
                        	utils.dialog({content: '审核异常！', quickClose: true, timeout: 4000}).showModal();
                        };
                    },
                    error : function() {
                    	utils.dialog({content: '系统出错了', quickClose: true, timeout: 4000}).showModal();
                    }
                });            	
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    	$("#approveComment").val('');        
    })
    //驳回
    $('#reject').on('click',function(){
        utils.dialog({
            title: '审核驳回',
            content: $('#bh_dialog'),
            okValue: '确定',
            width: 500,
            height: 260,
            ok: function () {
//            	console.log(carrierCode +','+ $("#rejectComment").val());
            	var rejectComment = $("#rejectComment").val();
            	if(!isNotNull(rejectComment)) {		
            		utils.dialog({content: '审核留言不能为空', quickClose: true, timeout: 2000}).showModal();
            		return false;
            	}
            	$.ajax({            
                    type: "POST",                    
                    url: "/proxy-gsp/gsp/transport/carrierReject",
                    data: {
                    	carrierCode:carrierCode,
                    	comment:$("#rejectComment").val(),
                    	taskId:$("#taskId").val(),
                    	processId:$("#processId").val(),
                    	edit:$("#edit").val(),
                    	close:$("#close").val(),
                    	agree:$("#agree").val(),
                    	reject:$("#reject").val(),
                    	report:$("#report").val()
                    },
                    success: function (result) {            	
                        if (result.code == 0) {
                        	utils.dialog({content: '审核驳回成功！', timeout: 2000}).showModal();
		                    setTimeout(function(){
		                        utils.closeTab();
		                    },2000)
                        } else {
                        	utils.dialog({content: '审核驳回异常！', quickClose: true, timeout: 4000}).showModal();
                        };
                    },
                    error : function() {
                    	utils.dialog({content: '系统出错了！', quickClose: true, timeout: 4000}).showModal();
                    }
                });            	
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
        $("#rejectComment").val('');        
    })  
    
     //关闭按钮
    $("#approvalClose").on("click", function () {
        utils.dialog({
            title: "提示",
            content: "关闭后该流程信息将丢失，无法重新审核，是否确认关闭？",
            width:300,
            height:30,
            okValue: '确定',
            ok: function () {
            	$.ajax({            
                    type: "POST",                    
                    url: "/proxy-gsp/gsp/transport/approvalClose",
                    data: {
                    	carrierCode:$('#carrierCode').val(),
                    	comment:$("#approveComment").val(),
                    	taskId:$("#taskId").val(),
                    	processId:$("#processId").val(),
                    	edit:$("#edit").val(),
                    	close:$("#close").val(),
                    	agree:$("#agree").val(),
                    	reject:$("#reject").val(),
                    	report:$("#report").val()
                    },
                    success: function (result) {            	
                        if (result.code == 0) {  
                        	utils.dialog({content: '审核关闭成功！', timeout: 2000}).showModal();
		                    setTimeout(function(){
		                        utils.closeTab();
		                    },2000)
                        } else {
                        	utils.dialog({content: '审核关闭异常！', quickClose: true, timeout: 4000}).showModal();                        	
                        };
                    },
                    error : function() {
                    	utils.dialog({content: '系统出错了', quickClose: true, timeout: 4000}).showModal();                    	
                    }
                });                 
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    });
   
    //批准文件切换
    $('.nav-tabs>li').on('click', function (){
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    });
    
    /* 编辑 */
    $('#editRowData').on('click', function () {    	  		
		var carrierCode = $("#carrierCode").val();
//	    		console.log('carrierCode='+carrierCode)
		utils.openTabs("carrierlistEdit","编辑承运单位", "/proxy-gsp/gsp/transport/toCarrierEdit?carrierCode="+carrierCode
				+ "&taskId="+$("#taskId").val()+"&processId="+$("#processId").val()+"&edit="+$("#edit").val()
				+"&close="+$("#close").val()+"&agree="+$("#agree").val()+"&reject="+$("#reject").val()
				+"&report="+$("#report").val());     	
    })
  
    var sources= new Array();
    sources = $("#sourceHidden").val().split(",");
   // console.log(sources);
    $(".source").each(function(){
    	var source = $(this).val();    	
    	for(var i = 0; i < sources.length; i++) {  		
    		if(source == sources[i]) {
//    			console.log(source +','+ sources[i]);
    			$(this).attr("checked",true);    			
    			break;
    		}
    	}    	   	
    });
    
    var qualityHidden = $("#qualityHidden").val();   
    $("#quality").find("option[value='" + qualityHidden + "']").attr("selected",true);
    var thermalControl = $("#transportQualityHidden").val();   
    $("#transportQuality").find("option[value='" + thermalControl + "']").attr("selected",true);
    var thermalControl = $("#thermalControlHidden").val();   
    $("#thermalControl").find("option[value='" + thermalControl + "']").attr("selected",true);
 
    if(licenseList != null && licenseList.length >0 && licenseList[0] != null) {
		addRowDataTable1('#table1', licenseList);
	}
	if(driverList != null && driverList.length >0 && driverList[0] != null) {
		addRowDataTable2('#table2', driverList);
	}
	if(vehicleList != null && vehicleList.length >0 && vehicleList[0] != null) {
		addRowDataTable3('#table3', vehicleList);
	}
    
    initApprovalFlowChart($("#processId").val());
})

function addRowDataTable1(table, list){	
//	console.log(list);
	$.each(list, function (index, item) {
		console.log('item.licenseType='+item.licenseType);
		$('#table1').XGrid('addRowData',{
			licenseType:item.licenseType,
			uploadDate:item.uploadDate,
			createUserName:item.createUserName,
			licensePhotoCount:item.licensePhotoCount,
			licensePhotoList:item.licensePhotoList			
		});
	})    
}

function addRowDataTable2(table, list){	
//	console.log(list);
	$.each(list, function (index, item) {		
		$('#table2').XGrid('addRowData',{			
			driverName: item.driverName,  
			sex: item.sex, 
			sexValue: item.sexValue, 
			drivingLicenseCode: item.drivingLicenseCode,           
			drivingLicenseType: item.drivingLicenseType,          
			maintainDate: item.maintainDate          
		});
	})    
}

function addRowDataTable3(table, list){	
	console.log( list);
	$.each(list, function (index, item) {		
		$('#table3').XGrid('addRowData',{			
			carNumber: item.carNumber,
			vehicleType: item.vehicleType,
			maintainDate: item.maintainDate
		});
	})    
}

//获取审核流程数据
function  initApprovalFlowChart(processId) {
    $.ajax({
    	url: "/proxy-gsp/gsp/transport/carrier/queryFlowChart",
        type: "POST",
        dataType:"JSON",
        data:{
        	processInstaId:processId
        },
        success: function (data) {
            if (data.code==0&&data.result!=null){
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}

//附件详情
function showImg(obj) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(obj).parents("table").getRowData(parentId);
   
//    console.log('data2=='+JSON.stringify(data.licensePhotoList));    
//    console.log('data2=='+data.licensePhotoList.length);    
    if(data.licensePhotoList.length > 0)
    {
        $.viewImg({
            fileParam:{
                name:'name',
                url:'url'
            },
            list:data.licensePhotoList
        })
    }
}

function isNotNull(value) {
    if (value != "" && value != null && value != undefined ) {
        return true;
    } else {
    	return false;
    }
    
}