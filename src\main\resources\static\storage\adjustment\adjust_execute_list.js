$(function () {
    getQulifiledNum ();
    // 时间格式化
    function formatDateTime(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }

    /* 日期初始化 */
    z_utils.initDate('begint', 'endt')

    /* tabs 切换 */
    z_utils.tableCut('flex');

    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    /* table_a */
    //data
    var grid_dataY = [{
        text1: "1",
        text2: "",
        text3: "",
        text4: "",
        text5: "",
        text6: "",
        text7: "",
        text8: "",
        text9: "",
    }];
    for (let i = 0; i < 20; i++) {
        grid_dataY.push(grid_dataY[0]);
    }
    var colName = ['机构名称', '调账执行单号', '调账申请单号','部门名称', '执行人','执行日期', '过账日期', '不含税成本金额', '移动类型'];
    var colModel = [{
        name: 'orgName',
        index: 'orgName',
        width: 260,
    }, {
        name: 'adjustmentExecuteCode',
        index: 'adjustmentExecuteCode',
        width: 200,
    }, {
        name: 'adjustmentApplyCode',
        index: 'adjustmentApplyCode',
        width: 200,
        formatter:function (e) {
            if(e != undefined && e != null && e != ''){
                return '<a href="javascript:;"  url= "/proxy-storage/storage/adjustment/toInfo?adjustmentApplyCode='+e+'"     class="to_apply">'+e+'</a>';
            }else {
                return "";
            }

        },
        unformat:function (e,rowtype,rowdata) {
            var code = e.match(/\>(\S+)(\<\/a>)$/);
            if(code&&code.length&&code[1]){
                return code[1]
            }else {
                return e
            }
        }

    }, {
        name: 'departmentName',
        index: 'departmentName',
        hidden:true, hidegrid:true
    }, {
        name: 'createUser',
        index: 'createUser'
    }, {
        name: 'executeTime',
        index: 'executeTime',
        formatter: function (e) {
            //console.log(e)
            return formatDateTime(e);
        }
    }, {
        name: 'postingTime',
        index: 'postingTime',
        formatter: function (e) {
            //console.log(e)
            return formatDateTime(e);
        }
    }, {
        name: 'costAmount',
        index: 'costAmount'
    }, {
        name: 'mobileTypeDesc',
        index: 'mobileTypeDesc'
    }];
    $('#table_a').XGrid({
        // data: grid_dataY,
        url: "/proxy-storage/storage/adjustmentexecute/findList",
        colNames: colName,
        colModel: colModel,
        selectandorder: true,
        key: 'adjustmentExecuteCode',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            console.log('双击行事件', obj);
            utils.openTabs("adjustExecutionInfo","调账执行单详情", '/proxy-storage/storage/adjustmentexecute/toExecInfoList?adjustmentExecuteCode=' + id,{},function () {
                $('#searchBtn').trigger('click')
            });
            /* 双击单据，进入单据详情页面。若单据状态为“待处理”，双击单据跳转至编辑销售退回单页面； */
            // window.location = '/proxy-storage/storage/adjustmentexecute/toExecInfoList?adjustmentExecuteCode=' + id;
        },
        attachRow:true,
        gridComplete: function () {
            /*setTimeout(function (param) {
              /!* 合计写入 *!/
              var data = $('#table_a').XGrid('getRowData');
              var sum_ele = $('#table_a_sum .sum');
              //console.log(sum_ele);
              $(sum_ele[0]).text(totalTable(data,'text12'));
              $(sum_ele[1]).text(totalTable(data,'text15'));
              $(sum_ele[2]).text(totalTable(data,'text16'));
            }, 200)*/
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['costAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(z_utils.totalTable(data,item))
            });
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)

            var $ele = $(event.target);
            if ($ele.attr("class")=="to_apply") {
                var data = $ele.attr("url");
                utils.openTabs("adjustInfo","调账申请单详情", data);
            }
        }
    });


    /* table_b */
    var grid_datax = [{
        text1: "XSTH180620000001",
        text2: "1.1",
        text3: "",
        text4: "",
        text5: "",
        text6: "",
        text7: "",
        text8: "",
        text9: "",
        text10: "",
        text11: "",
        text12: "",
        text13: "",
        text14: "",
        text15: "",
        text16: "",
        text17: "",
        text18: "",
        text19: "",
        text20: "",
        text21: "9",
        text22: "",
        text23: "",
        text24: "6",
        text25: "5",
        text26: "4",
        text27: "",
        text28: "",
        text29: "1",
    }];
    for (let i = 0; i < 20; i++) {
        grid_datax.push(grid_datax[0]);
    }
/*    $('#table_b').XGrid({
        // data: grid_datax,
        url: "/proxy-storage/storage/adjustmentexecute/findDetailList",
        colNames: ['id','调账执行单号', '行号', '执行日期',  '商品编码', '条码','商品大类', '商品名称', '商品规格', '生产厂家', '产地',
            '单位', '库房名称', '批号', '生产日期', '有效期至', '调账数量', '不含税成本单价', '不含税成本金额'
        ],
        colModel: [{
            name: 'id',
            index: 'id',
            key:true,
            hidden:true,
            hidegrid:true
        },{
            name: 'adjustmentExecuteCode',
            index: 'adjustmentExecuteCode'
        }, {
            name: 'sortNo',
            index: 'sortNo'
        }, {
            name: 'performTime',
            index: 'performTime',
            formatter: function (e) {
                //console.log(e)
                return formatDateTime(e);
            }
        }, {
            name: 'productCode',
            index: 'productCode'
        }, {
            name: 'smallPackageBarCode',
            index: 'smallPackageBarCode'
        }, {
            name: 'drugClass',
            index: 'drugClass'
        }, {
            name: 'productName',
            index: 'productName'
        }, {
            name: 'productSpec',
            index: 'productSpec'
        }, {
            name: 'productmManufacturer',
            index: 'productmManufacturer'
        }, {
            name: 'producingArea',
            index: 'producingArea'
        }, {
            name: 'producingPackingUnit',
            index: 'producingPackingUnit'
        }, {
            name: 'storeName',
            index: 'storeName',
            formatter: function (e) {
                if (e == '1') {
                    return '合格库'
                } else if (e == '2') {
                    return '不合格库'
                }else if (e == '3') {
                    return '暂存库'
                }else {
                    return e;
                }
            }
        }, {
            name: 'batchCode',
            index: 'batchCode'
        }, {
            name: 'manufactureTime',
            index: 'manufactureTime',
            formatter: function (e) {
                //console.log(e)
                return formatDateTime(e);
            }
        }, {
            name: 'expiryTime',
            index: 'expiryTime',
            formatter: function (e) {
                //console.log(e)
                return e==null?"":e;
            }
        }, {
            name: 'adjustmentNumber',
            index: 'adjustmentNumber'
        }, {
            name: 'costPrice',
            index: 'costPrice'
        }, {
            name: 'costAmount',
            index: 'costAmount'
        }],
        rowNum: 20,
        rowList:[20,50,100],
        key: 'id',
        selectandorder: true,
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_b',
        gridComplete: function () {
            /!*setTimeout(function (param) {
              /!* 合计写入 *!/
              var data = $('#table_b').XGrid('getRowData');
              var sum_ele = $('#table_b_sum .sum');
              //console.log(sum_ele);
              $(sum_ele[0]).text(totalTable(data,'text21'));
              $(sum_ele[1]).text(totalTable(data,'text24'));
              $(sum_ele[2]).text(totalTable(data,'text25'));
              $(sum_ele[3]).text(totalTable(data,'text26'));
              $(sum_ele[4]).text(totalTable(data,'text29'));
            }, 200)*!/
        },
    });*/

    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .table-box .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })



    /* 商品名称 搜索提示（只显示5条） */
    var ts1 = [{
        value: 'Andorra',
        data: 'AD'
    },
        {
            value: 'Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '2Andorra',
            data: 'AD'
        },
        {
            value: '2Zimbabwe',
            data: 'ZZ'
        },
        {
            value: '3Andorra',
            data: 'AD'
        },
        {
            value: '3Zimbabwe',
            data: 'ZZ'
        }
    ];
    $('#commodity').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList', //异步请求
        paramName: 'param',//查询参数，默认 query
        params: {orgCode:orgCode},
        dataType: 'json',
        lookup: ts1, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '没有找到相关商品', //查无结果的提示语
        transformResult: function(response) {
            return {
                suggestions: $.map(response.result.list, function(dataItem) {
                    return { value: dataItem.productName, data: dataItem.productCode };
                })
            };
        },
        showNoSuggestionNotice: true, //显示查无结果的container
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#input_product_hidden").val(result.data);
            // alert('You selected: ' + result.value + ', ' + result.data);
            // console.log('选中回调')
        },
        onSearchStart: function (params) {
            console.log('检索开始回调', params)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
            $('#input_product_hidden').val('');
            $('#commodity').val('');
            ;
        }
        // tabDisabled: true,
    });

    /* 查询 */
    $('#searchBtn').on('click', function (e) {
        var formData = $("#form_a").serializeToJSON();
        //获取form数据
        // var data = $('#form_a').serializeToJSON();
        // console.log(data);
        //更新表格数据
        var $table_id = $('#nav_content .active table').attr('id');
        // if ($table_id == 'table_a') {
        //列表
        $('#table_a').XGrid('setGridParam', {
            url: "/proxy-storage/storage/adjustmentexecute/findList",
            postData: formData,
            // data: grid_dataY,
            colNames: colName,
            colModel: colModel,
            rowNum: 20,
            rowList:[20,50,100],
            altRows: true, //设置为交替行表格,默认为false
            pager: '#grid_pager_a',
        }).trigger("reloadGrid");
        // } else {
        //详情
       /* $('#table_b').XGrid('setGridParam', {
            // data: grid_datax,
            url: "/proxy-storage/storage/adjustmentexecute/findDetailList",
            postData: formData,
            //colNames: colName,
            //colModel: colModel,
            //rowNum: 10,
            altRows: true, //设置为交替行表格,默认为false
            pager: '#grid_pager_b',
        }).trigger("reloadGrid");*/
        // }
        getQulifiledNum ();
    });

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });

            //获取form数据
            var formData = $('#form_a').serializeToJSON();
            formData["colName"] = colName;
            formData["colNameDesc"] = colNameDesc;

            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            var exportLimitRow = Number($("#exportLimitRow").val());
            if (data.length <= exportLimitRow && data.length > 0) {
                /* if (!data.length) {
                     data = [data];
                 }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                data = JSON.stringify(data);
                formData["selectData"] = data;
            } else {
                var rows = $('#' + tableId)[0].p.records;
                if(rows > exportLimitRow) {
                    utils.dialog({
                        title: '提示',
                        width: 200,
                        height: 40,
                        cancel: false,
                        content: '您本次导出的数据量过大（已超过'+exportLimitRow+'条），不允许导出，请缩小导出范围。',
                        okValue: '确定',
                        ok: function () {
                            //utils.closeTab();
                        }

                    }).showModal();
                    return;
                }
                data = '';
            }
            console.log(colName);

            switch (getTabInd()) {
                case 0:
                    httpPost("/proxy-storage/storage/adjustmentexecute/exportExecuteList", formData);
                    break;
                case 1:
                    httpPost("/proxy-storage/storage/adjustmentexecute/exportExecuteDetailList", formData);
                    break;
            }
        });
    });

    //获取当前tab  的下标 销售出库单列表 、 销售出库单商品明细 、 异常出库处理
    function getTabInd() {
        for (var i = 0; i < $('.saleOrderList_tabs li').length; i++) {
            if ($('.saleOrderList_tabs li').eq(i).hasClass('active')) {
                _ind = $('.saleOrderList_tabs li').eq(i).index();
            }
        }
        return _ind;
    };

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    /* 商品名称查询 */
    /*$('#s_commodity').on('click', function (e) {
        var commodity_d = $(e.target).prev('input').val();
        utils.dialog({
            url: '/orderReturn/orderReturnController/toCommodityList',
            title: '商品列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            data: commodity_d, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $(e.target).prev('input').val(data.name);
                    console.log("this.returnValue", data);
                }
            },
            oniframeload: function () {

            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })*/

    //商品名称 双击查询
    $('#s_commodity').click(function () {
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            // url: 'toProductList',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.8,
            data: $("#input_goodName").val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#commodity").val(data.productName);
                    $("#input_product_hidden").val(data.productCode);
                }
                $('iframe').remove();
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    })
    function getQulifiledNum () {
        //获取form数据
        var formData = $("#form_a").serializeToJSON();
        //加载总数量
        $.ajax({
            url: '/proxy-storage/storage/adjustmentexecute/findTotalCostAmount',
            dataType: 'json',
            timeout: 8000,
            data:formData,
            success: function (data) {
                if (data.code==0){
                    var result = data.result;
                    if(result!=null) {
                        if(result.totalCostAmount!=null){
                            $("#totalCostAmount").text(Number(result.totalCostAmount).toFixed(2));
                        }else {
                            $("#totalCostAmount").text('0.00');
                        }
                        $("#totalCount").text(result.totalCount);
                    }else {
                        $("#totalCostAmount").text('0.00');
                        $("#totalCount").text(0);
                    }
                }
            },
            error: function () {
                $("#totalCostAmount").text('0.00');
                $("#totalCount").text(0);
                utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
            }
        });
    }
})