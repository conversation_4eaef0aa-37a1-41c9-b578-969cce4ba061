$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);


    /* 审批流 */
    /**
     * 流程图显示
     */
        //根据流程实例 ID加载流程图
    var processInstaId=$("#processId").val();
    initApprovalFlowChart(processInstaId);
    function  initApprovalFlowChart(processInstaId) {
        //获取审核流程数据
        $.ajax({
            type: "POST",
            url: "/proxy-order/order/orderReturn/orderReturnController/queryTotle?processInstaId="+processInstaId,
            async: false,
            success: function (data) {
                if (data.code==0&&data.result!=null){
                    console.log(data.result);
                    $('.flow').html("")
                    $('.flow').process(data.result);
                }
            },
            error: function () {}
        });
    }

    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    var colName = ['退货原因', '商品编码','原商品编码','商品大类', '商品名称', '商品规格', '生产厂家', '产地', '单位', '库房名称', '批号', '生产日期', '有效期至', '本次退回数量','实际退回数量','含税价',  '价税合计', '实付金额', '活动明细优惠金额', '余额抵扣明细优惠金额', '退回返利金额', '税率', '税额'
    ];
    var colModel =[{ name: 'returnReason',index: 'returnReason',formatter:function (e) {
            if(e==1){
                return '整单拒收';
            }else if(e==2){
                return '药品漏发';
            }else if(e==3){
                return '药品错发';
            }else if(e==4){
                return '药品破损';
            }else if(e==5){
                return '效期不好';
            }else if(e==6){
                return '批号不符';
            } else if(e==7){
                return '税票有误';
            }else if(e==8){
                return '无检验报告单';
            }else if(e==9){
                return '采购价偏高';
            } else if(e==10){
                return '实物与图片不符';
            }else if(e==11){
                return '采购单重复';
            }else if(e==12){
                return '商品其他质量问题';
            }else if(e==13){
                return '拦截';
            }else if(e==14){
                return '缺货未发';
            }else if(e==15){
                return '召回';
            }else if(e==16){
                return '水剂不发货';
            }
        }},
        { name: 'productCode', index: 'productCode' },
        { name: 'oldProductCode', index: 'oldProductCode' },
        { name: 'drugClass', index: 'drugClass'},
        { name: 'productName', index: 'productName'},
        { name: 'specifications',index: 'specifications'},
        { name: 'manufacturer', index: 'manufacturer'},
        { name: 'productOrigin', index: 'productOrigin'},
        { name: 'productUnit',index: 'productUnit'},
        { name: 'warehouseName',index: 'warehouseName',formatter:function (e) {
                if(e==1){
                    return '合格库';
                }else if(e==2){
                    return '不合格库';
                }else if(e==3){
                    return '暂存库';
                }else{
                    return '暂存库';
                }
            }},
        { name: 'batchCode', index: 'batchCode' },
        { name: 'productionTime', index: 'productionTime',formatter:dateFormatter},
        { name: 'periodValidity',index: 'periodValidity',formatter:dateFormatter},
        { name: 'returnsNumber',index: 'returnsNumber'},
        { name: 'actualReturnNumber',index: 'actualReturnNumber'},
        { name: 'taxPrice',index: 'taxPrice',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            }},
        { name: 'taxAmount',    index: 'taxAmount' ,
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            } },
        { name: 'paymentAmount',    index: 'paymentAmount'  ,
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            }},
        { name: 'activityDiscountAmount',    index: 'activityDiscountAmount',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            }  },
        { name: 'balanceDiscountAmount',    index: 'balanceDiscountAmount',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            }  },
        {      name: 'amounOfRebate',      index: 'amounOfRebate',
            formatter: function (e) {
                if (e != null && e != '') {
                    var array = e.toString().split(".");
                    if (array.length == 1){
                        return e.toString() + ".00"
                    }else if (array.length ==2){
                        var elngt2 = array[1].length;
                        if (elngt2 == 1){
                            return e.toString()+"0"
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }else {
                    return e;
                }
            }
        },
        { name: 'rate',    index: 'rate',  formatter: function (e) {
                if ( e != undefined && e != '' && e != null ) {
                    return e+ "%"
                } else {
                    return '0%'
                }
            }   },
        { name: 'tax',    index: 'tax'  }];
    $('#table_a').XGrid({
        url:"/proxy-order/order/orderReturn/orderReturnController/getProductList?salesReturnCode=" + $("#salesReturnCode").val(),
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'sort',
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {
        },
        gridComplete: function () {
            setTimeout(function (param) {
                /* 合计写入 */
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                $(sum_ele[1]).text(totalTable(data, 'tax'));
                $(sum_ele[2]).text(totalTable(data, 'paymentAmount'));
            }, 200)
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
        }
    });


    // 筛选列
    $("#set_tb_rows").click(function () {
        //获取当前显示表格
        var tableId = $('#nav_content .active .XGridBody table').attr('id');
        $('#' + tableId).XGrid('filterTableHead');
    })

    /* 审核通过*/
    $('#check_pass').on('click', function () {
        utils.dialog({
            title: '温馨提示',
            content: $('#check_pass_msg'),
            okValue: '确定',
            ok: function () {
                // var form_data = $('#form_a').serializeToJSON();
                // var table_data = $('#table_a').XGrid('getRowData');
                // console.log(form_data, table_data);
                $.ajax({
                    type: "POST",
                    url: "/proxy-order/order/orderReturn/orderReturnController/rejectAudit?auditType=1&salesReturnCode="+$("#businessId").val()+"&taskId="+$("#taskId").val()+"&remark="+$("#remark").val()+"&auditId="+$("#processId").val(),
                    async: false,
                    success: function (data) {
                        if(data.code == 0){
                            utils.dialog({
                                title: '温馨提示',
                                content: '审核成功',
                                okValue: '确定',
                                ok: function () {
                                    utils.closeTab();
                                },
                                cancelValue: '取消',
                                cancel: function () {}
                            }).show()
                        }else{
                            utils.dialog({
                                title: '温馨提示',
                                content: data.msg,
                                okValue: '确定',
                                ok: function () {
                                    return;
                                }

                            }).show()
                        }
                    },
                    error: function () {}
                });
            },
            cancelValue: '取消',
            cancel: function () {}
        }).show()
    });

    /* 审核驳回 */
    $('#check_nopass').on('click', function () {
        utils.dialog({
            title: '温馨提示',
            content: $('#check_nopass_msg'),
            okValue: '确定',
            ok: function () {
                if($("#remarks").val() != null && $("#remarks").val() != ''){
                    $.ajax({
                        type: "POST",
                        url: "/proxy-order/order/orderReturn/orderReturnController/rejectAudit?auditType=0&salesReturnCode="+$("#businessId").val()+"&taskId="+$("#taskId").val()+"&remark="+$("#remarks").val()+"&auditId="+$("#processId").val(),
                        async: false,
                        success: function (data) {
                            if(data=='false'){
                                utils.dialog({
                                    title: '温馨提示',
                                    content: '驳回失败',
                                    okValue: '确定',
                                    ok: function () {
                                        return ;
                                    },
                                    cancelValue: '取消',
                                    cancel: function () {}
                                }).show()
                            }else{
                                utils.dialog({
                                    title: '温馨提示',
                                    content: '驳回成功',
                                    okValue: '确定',
                                    ok: function () {
                                        window.location="/proxy-order/order/orderReturn/orderReturnController/jumpOrderReturnList";
                                    },
                                    cancelValue: '取消',
                                    cancel: function () {}
                                }).show()
                            }
                        },
                        error: function () {}
                    });
                }else{
                    utils.dialog({
                        title: '温馨提示',
                        content: '请填写驳回原因',
                        okValue: '确定',
                        ok: function () {
                        },
                        cancelValue: '取消',
                        cancel: function () {}
                    }).show()
                }
            },
            cancelValue: '取消',
            cancel: function () {}
        }).show()
    });

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }

    /* 返回 */
    $('#goback').on('click', function () {
        utils.closeTab();
    });
})//