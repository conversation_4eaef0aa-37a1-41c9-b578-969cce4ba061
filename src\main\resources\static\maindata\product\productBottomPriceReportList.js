$(function () {
  
  //商品搜索图标
  $(document).on("click", ".glyphicon-search", function () {
    $(this).siblings("input").trigger("dblclick");
  });
  var orgCode = $("#loginOrgCode").val();
  if (orgCode == "001") {
    $.ajax({
      url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode=" + orgCode,
      async: false,
      dataType: "json",
      success: function (data) {
        var html = '<option value="">请选择</option>';
        if (data.code == 0) {
          var arr = data.result;
          if (arr != null) {
            for (var i = 0; i < arr.length; i++) {
              html +=
                '<option value="' +
                arr[i].orgCode +
                '">' +
                arr[i].orgName +
                "</option>";
            }
          }
        }
        $("#orgCode").html(html);
      },
      error: function () {},
    });
    $("#orgCode").on('change',function(){
        $("#buyerVal").val("");
        $("#buyer").val("");
        initBuyer();
    });
  }
});
initBuyer();

function initBuyer() {
  valAutocomplete("/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode",{paramName:'userName',params:{"orgCode":$("#orgCode").val()}},"buyer",{data:"id",value:"userName"});
}
let manufacturerId = "";
showCommodityPosition();
var urlObjectList = [];
var colNames = [
  "机构名称",
  "业务类型",
  "SKU编码",
  "商品编码",
  "商品原编码",
  "商品名称",
  "通用名",
  "商品规格",
  "生产厂家",
  "采购员",
  "最近一次入库价",
  "<i class='questa'></i>C1加权平均价",
  "<i class='questa'></i>C2底价（招商）",
  "<i class='questa'></i>C3底价（年度）",
  "<i class='questa'></i>C4底价（年度&招商）",
  "<i class='questa'></i>虚拟C3底价",
  "<i class='questa'></i>招商单品返利",
  "<i class='questa'></i>年度单品返利",
  "<i class='questa'></i>虚拟单品返利",
  "返利风险提醒",
  "招商返公式",
  "年度返公式",
  "虚拟返公式",
  "<i class='questa'></i>C1毛利",
  "<i class='questa'></i>C2毛利",
  "<i class='questa'></i>C3毛利",
  "<i class='questa'></i>C3虚拟毛利",
  "<i class='questa'></i>C4毛利",
  "协议供货价",
  "是否底价供货",
  "商品定位一级",
  "商品定位二级",
  "采购业务类型",
  "电商商品状态",
  "最后一次入库时间",
  "一级分类",
  "二级分类",
  "三级分类",
  "四级分类",
  "五级分类",
  "六级分类",
];
var allColModelA = [
  {
    name: "orgName",
    index: "orgName",
    width: 220,
  },
  {
    name: "channelId",
    index: "channelId",
    width: 100,
  },
  {
    name: "skuCode",
    index: "skuCode",
    width: 180,
  },
  {
    name: "productCode",
    index: "productCode",
    width: 180,
  },
  {
    name: "oldProductCode",
    index: "oldProductCode",
    width: 180,
  },
  {
    name: "productName",
    index: "productName",
    width: 180,
  },
  {
    name: "commonName",
    index: "commonName",
    width: 180,
  },
  {
    name: "specifications",
    index: "specifications",
    width: 200,
  },
  {
    name: "manufacturerName",
    index: "manufacturerName",
    width: 220,
  },
  {  //采购员
    name: "buyerName",
    index: "buyerName",
    width: 220,
  },
  {
    name: "lastPurchasePrice",
    index: "lastPurchasePrice",
    width: 180,
  },
  { //C1加权平均价
    name: "rankAvgPrice",
    index: "rankAvgPrice",
    width: 130,
  },
  { //C2底价（招商）
    name: "actBottomPrice",
    index: "actBottomPrice",
    width: 140,
  },
  { //C3底价（年度）
    name: "bottomPrice",
    index: "bottomPrice",
    width: 140,
  },
  { //C4底价（年度&招商）
    name: "proActBottomPrice",
    index: "proActBottomPrice",
    width: 180,
  },
  { //虚拟C3底价
    name: "c3VirtualBottomPrice",
    index: "c3VirtualBottomPrice",
    width: 112,
  },
  { //招商单品返利
    name: "actSingleSkuRebate",
    index: "actSingleSkuRebate",
    width: 130,
  },
  { //年度单品返利
    name: "singleSkuRebate",
    index: "singleSkuRebate",
    width: 130,
  },
  { //虚拟单品返利
    name: "virtualSingleSkuRebate",
    index: "virtualSingleSkuRebate",
    width: 130,
  },
  { //返利风险提醒
    name: "newSingleSkuRebateTips",
    index: "newSingleSkuRebateTips",
    width: 280,
  },
  { //招商返公式
    name: "newActivitySingleSkuRebateCalWay",
    index: "newActivitySingleSkuRebateCalWay",
    width: 130,
  },
  { //年度返公式
    name: "newSingleSkuRebateCalWay",
    index: "newSingleSkuRebateCalWay",
    width: 150,
  },
  { //虚拟返公式
    name: "newVirtualSingleSkuRebateCalWay",
    index: "newVirtualSingleSkuRebateCalWay",
    width: 130,
  },
  { //C1毛利
    name: "c1GrossProfitStr",
    index: "c1GrossProfitStr",
    width: 100,
  },
  { //C2毛利
    name: "c2GrossProfitStr",
    index: "c2GrossProfitStr",
    width: 100,
  },
  { //C3毛利
    name: "c3GrossProfitStr",
    index: "c3GrossProfitStr",
    width: 100,
  },
  { //C3虚拟毛利
    name: "c3VirtualGrossProfitStr",
    index: "c3VirtualGrossProfitStr",
    width: 112,
  },
  { //C4毛利
    name: "c4GrossProfitStr",
    index: "c4GrossProfitStr",
    width: 100,
  },
  {
    name: "supplyTaxPrice",
    index: "supplyTaxPrice",
    width: 100,
  },
  {
    name: "bottomPriceSupplyText",
    index: "bottomPriceSupplyText",
    width: 180,
  },
  {
    name: "commodityPositionVal",
    index: "commodityPositionVal",
    width: 120,
  },
  {
    name: "secondCommodityPositionVal",
    index: "secondCommodityPositionVal",
    width: 120,
  },
  {
    name: "purchaseProductTypeVal",
    index: "purchaseProductTypeVal",
    width: 120,
  },
  {
    name: "ecStatusVal",
    index: "ecStatusVal",
    width: 120,
  },
  {
    name: "lastInStorageTime",
    index: "lastInStorageTime",
    width: 120,
  },
  {
    name: "firstCategoryVal",
    index: "firstCategoryVal",
    width: 120,
  },
  {
    name: "secondCategoryVal",
    index: "secondCategoryVal",
    width: 120,
  },
  {
    name: "thirdCategoryVal",
    index: "thirdCategoryVal",
    width: 120,
  },
  {
    name: "fourCategoryVal",
    index: "fourCategoryVal",
    width: 120,
  },
  {
    name: "fiveCategoryVal",
    index: "fiveCategoryVal",
    width: 120,
  },
  {
    name: "sixCategoryVal",
    index: "sixCategoryVal",
    width: 120,
  },
];
//初始化table头部hover提示
setTimeout(() => {
  initQuesta();
}, 1000);
//初始化table头部hover提示
function initQuesta() {
  const questaOption = [
    {
      th: 'rankAvgPrice', //C1加权平均价
      title: `C1加权平均价取自财务加权成本价`,
      width: 460,
      height: 80,
    },
    {
      th: 'actBottomPrice', //C2底价（招商）
      title: `C2底价（招商）=C1加权平均价-招商单品返利`,
      width: 460,
      height: 80,
    },
    {
      th: 'bottomPrice', //C3底价（年度）
      title: `C3底价 (年度) =C1加权平均价-渠道均摊年度单品返利`,
      width: 460,
      height: 80,
    },
    {
      th: 'proActBottomPrice', //C4底价（年度&招商）
      title: `C4底价 (年度&招商) =C1加权平均价-渠道均摊招商单品返利-渠道均摊年度单品返利-渠道均摊虚拟单品返利`,
      width: 460,
      height: 80,
    },
    {
      th: 'actSingleSkuRebate', //招商单品返利
      title: `招商单品返利只取当前生效的招商活动协议（剔除活动类型为套餐、满减打包、满折打包、商品券打包、叠加券打包的），如同时存在拼团和其他类型招商活动协议，只取拼团活动单品返利`,
      width: 460,
      height: 80,
    },
    {
      th: 'singleSkuRebate', //年度单品返利
      title: `年度单品返利只取当前生效且存在结存库存的计提单品返利(剔除固定金额返利协议、商业供货全系列品种协议)，且只使用协议开始时间到当前时间的协议期内的平均购进单价/平均出库价核算单品返利，按协议渠道平摊至当前库内所有库存，多个协议返利时叠加计算`,
      width: 460,
      height: 80,
    },
    {
      th: 'virtualSingleSkuRebate', //虚拟单品返利
      title: `虚拟单品返利只取当前生效且存在结存库存的虚拟协议下计提单品返利，使用虚拟协议开始时间到当前时间的协议期内的平均购进单价/平均出库价核算单品返利，按协议渠道平摊至当前库内所有库存，多个协议返利时叠加计算`,
      width: 460,
      height: 80,
    },
    {
      th: 'c1GrossProfitStr', //C1毛利
      title: `C1毛利=(netgmv含税 - 销售含税成本金额（加权成本含税）)/netgmv含税*100% `,
      width: 460,
      height: 80,
    },
    {
      th: 'c2GrossProfitStr', //C2毛利
      title: `C2毛利=(netgmv含税 - 销售含税成本金额（加权成本含税）+招商返利含税金额)/netgmv含税*100% `,
      width: 460,
      height: 80,
    },
    {
      th: 'c3GrossProfitStr', //C3毛利
      title: `C3毛利=(netgmv含税 - 销售含税成本金额（加权成本含税）+年度计提含税返利金额 （ 包含临时返利金额）)/netgmv含税*100% `,
      width: 460,
      height: 80,
    },
    {
      th: 'c4GrossProfitStr', //C4毛利
      title: `C4毛利=（Netgmv含税 - 销售含税成本金额（加权成本含税） + 年度计提含税返利金额 （ 包含临时返利金额）+招商返利含税金额）netgmv含税*100% `,
      width: 460,
      height: 80,
    },
    {
      th: 'c3VirtualGrossProfitStr', //C3虚拟毛利
      title: `计算逻辑与C3毛利保持一致，仅计算虚拟协议的底价毛利`,
      width: 460,
      height: 80,
    },
    {
      th: 'c3VirtualBottomPrice', //虚拟C3底价
      title: `计算逻辑与C3底价（年度）保持一致，仅计算虚拟底价`,
      width: 460,
      height: 80,
    },
  ];
  eachTipView(questaOption);
}
function eachTipView(arr) {
  $.each(arr, function (index, item) {
    $('.XGridHead').delegate('th[row-describedby=' + item.th + '] .questa', {
      mouseover: function (e) {
        console.log(item.th,"绑定的事件")
        $('body').append(`
                      <div id='div_tooltips'>
                          <style>
                              #div_tooltips:after{
                                  content: "";
                                  width: 0;
                                  height: 0;
                                  position: absolute;
                                  left: ${item.width / 2 - 10}px;
                                  bottom: -10px;
                                  border-left: solid 10px transparent;
                                  border-top: solid 10px white;
                                  border-right: solid 10px transparent;
                              }
                          </style>
                          <div id='inner_tooltips'>${item.title}</div>
                      </div>
                  `);
        $('#div_tooltips')
          .css({
            boxSizing: 'border-box',
            width: item.width + 'px',
            height: item.height + 'px',
            padding: '10px',
            zIndex: 9999,
            backgroundColor: '#ffffff',
            border: '1px solid #c4c4c4',
            position: 'absolute',
            top: $(e.target).offset().top - item.height - 10 + 'px',
            left: $(e.target).offset().left + 5 - item.width / 2 + 'px',
          })
          .show('fast');
      },
      mouseout: function () {
        $('#div_tooltips').remove();
      },
      click: function () {},
    });
  });
}
$("#X_Tableb").XGrid({
  url: "/proxy-product/product/productBottomPriceReport/listBottomPriceReportPage",
  colNames: colNames,
  colModel: allColModelA,
  rowNum: 20,
  rowList: [20, 50, 100], //分页条数下拉选择
  altRows: true, //设置为交替行表格,默认为false
  rownumbers: true, //是否展示序号
  // multiselect: true,//是否多选
  pager: "#grid-pager",

  onSelectRow: function (id, dom, obj, index, event) {
    console.log(id, dom, obj, index, event);
    setUrlObjectList(dom, id, obj);
  },
  // rownumbers: true,
});
$("#SearchBtn").on("click", function () {
let commodit = $("#commodityPositionList").val() ? $("#commodityPositionList").val().join(",") : '';
  urlObjectList = [];
  $("#productCode").val($("#productCode").val());
  $("#X_Tableb")
    .XGrid("setGridParam", {
      postData: {
        productCode: $("#productCode").val(),
        orgCode: $("#orgCode").val(),
        commodityPositionList: commodit,
        sixCategoryVal: $("#sixCategoryVal").val(),
        purchaseProductType: $("#purchaseProductType").val(),
        manufacturer: manufacturerId,
        buyerId: $("#buyer").val(),
      },
      page: 1,
    })
    .trigger("reloadGrid");
});
function addHtmlA(arry) {
  if (!$("#setCol")[0]) {
    var s =
      '<div id="setCol" style="display: none; padding-left: 2.4rem;">' +
      '    <div class="row" id="checkRow">';

    for (var i = 0; i < arry.length; i++) {
      s +=
        '<div class="col-md-3">' +
        '            <div class="checkbox">' +
        "                <label>" +
        '                    <input style="margin-right: 10px" checked type="checkbox" name="' +
        arry[i] +
        '">' +
        arry[i] +
        "                </label>" +
        "            </div>" +
        "        </div>";
    }

    s += "</div></div>";
    $("body").append(s);
  }
}
$("#exportBtn").on("click", function () {
  var nameModel = "";
  addHtmlA(colNames);
  dialog({
    content: $("#setCol"),
    title: "筛选列",
    width: $(window).width() * 0.55,
    data: "val值",
    cancelValue: "取消",
    cancel: true,
    okValue: "导出",
    ok: function () {
      var newColName = [],
        newColModel = [];
      var colName = [];
      var colNameDesc = [];
      $(this.node)
        .find('#checkRow input[type="checkbox"]')
        .each(function (index) {
          if ($(this).is(":checked")) {
            nameModel +=
              allColModelA[index].name + ":" + $(this).attr("name") + ",";
            colName.push(allColModelA[index].name);
            colNameDesc.push($(this).attr("name"));
          }
        });
      if (nameModel.length == 0) {
        utils
          .dialog({ content: "请选择后导出", quickClose: true, timeout: 2000 })
          .showModal();
        return false;
      }
      parent.showLoading({ hideTime: 999999999 });
      let commodit = $("#commodityPositionList").val() ? $("#commodityPositionList").val().join(",") : '';
      var reqData = {
        moduleName: "productBottom",
        taskCode: "1019",
        selectOrgCode: true,
      };
      for(let i=0;i<colNameDesc.length;i++){
        colNameDesc[i] = colNameDesc[i].replace(/<[^>]*>/g, '');
      }
      reqData.colNameDesc = colNameDesc.join(",");
      reqData.colName = colName.join(",");
      reqData.fileName = "商品底价毛利报表";
      reqData.exportParams = {
        orgCode: $("#orgCode").val(),
        productCode: $("#productCode").val(),
        commodityPositionList: commodit,
        sixCategoryVal: $("#sixCategoryVal").val(),
        purchaseProductType: $("#purchaseProductType").val(),
        manufacturer: manufacturerId,
        buyerId: $("#buyer").val(),
      };
      console.log(reqData);
      utils
        .dialog({
          title: "温馨提示",
          content: "导出任务提交成功后页面将关闭，是否确认导出？",
          okValue: "确定",
          ok: function () {
            $.ajax({
              url: "/proxy-product/product/commonExport/commonCommitExportTask",
              type: "post",
              dataType: "json",
              data: {
                data: JSON.stringify(reqData),
              },
              success: function (res) {
                if (res) {
                  if (res.code === 0) {
                    utils
                      .dialog({
                        title: "温馨提示",
                        content:
                          "导出任务提交成功,稍后请点击导出列表进行下载...",
                        okValue: "确定",
                        ok: function () {},
                      })
                      .showModal();
                  } else {
                    utils
                      .dialog({
                        title: "温馨提示",
                        content: res.msg,
                        okValue: "确定",
                        ok: function () {},
                      })
                      .showModal();
                  }
                }
              },
            });
          },
        })
        .showModal();
      parent.hideLoading();
    },
    // copy button to other dialogues
    button: [
      {
        id: "chooseAll",
        value: "全选",
        callback: function () {
          if (!ck) {
            $("#checkRow input").prop("checked", false);
            ck = true;
          } else if (ck) {
            $("#checkRow input").prop("checked", "checked");
            ck = false;
          } else {
            return false;
          }
          return false;
        },
      },
    ],
    //copy ends here
  }).showModal();
});

// 生产厂家
var resParam = Object.assign(
  { list: "result" },
  { data: "manufactoryId", value: "manufactoryName" }
);
$("#manufacturer").Autocomplete({
  serviceUrl: "/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage", //异步请求
  paramName: "keyWord", //查询参数，默认 query
  params: {},
  dataType: "json",
  minChars: "0", //触发自动匹配的最小字符数
  maxHeight: "300", //默认300高度
  dataReader: resParam,
  triggerSelectOnValidInput: false, // 必选
  showNoSuggestionNotice: true, //显示查无结果的container
  noSuggestionNotice: "查询无结果", //查无结果的提示语
  onSelect: function (result) {
    manufacturerId = result.data;
    $("#manufacturer").val(result.value);
  },
  onNoneSelect: function (params, suggestions) {
    $("#manufacturer").val("");
    manufacturerId = "";
  },
});

//设置显示列
$("#setRow").click(function () {
  $("#X_Tableb").XGrid("filterTableHead", 1000);
});

function setUrlObjectList($tr, id, rowData) {
  var a = rowData;
  var fileParam = {};
  if ($tr.hasClass("selRow") && a.imageUrl) {
    fileParam.id = a.id;
    fileParam.name = a.productCode;
    fileParam.url = a.imageUrl;
    urlObjectList.push(fileParam);
  } else if (!$tr.hasClass("selRow")) {
    $(urlObjectList).map(function (i, v) {
      if (v.id == a.id) {
        urlObjectList.splice(i, 1);
      }
    });
  }
}

// 展示商品定位一级
function showCommodityPosition() {
  $.ajax({
    type: "get",
    url: "/proxy-product/product/dict/common/queryCommon?type=3",
    async: false,
    dataType: "json",
    success: function (data) {
      if (data.result && data.result.length) {
        let options = "";
        for (let index = 0; index < data.result.length; index++) {
          const element = data.result[index];
          let optionStr =
            '<option value="' + element.id + '">' + element.name + "</option>";
          options += optionStr;
        }
        $("#commodityPositionList").empty().append(options);
      }
    },
  });
}
function btn_output_list() {
  // var cs = $("#toggle_wrap .active").attr("id");
  var taskCode = "1019";
  var moduleName = "productBottom";
  let commodit = $("#commodityPositionList").val() ? $("#commodityPositionList").val().join(",") : '';
  utils
    .dialog({
      title: "导出列表",
      url:
        "/proxy-product/product/commonExport/toExportList?moduleName=" +
        moduleName +
        "&taskCode=" +
        taskCode +
        "&commodityPositionList=" +
        commodit +
        "&sixCategoryVal=" +
        $("#sixCategoryVal").val() +
        "&purchaseProductType=" +
        $("#purchaseProductType").val() +
        "&manufacturer=" +
        manufacturerId,
      width: $(window).width() * 0.8,
      height: 600,
      // data: , // 给modal 要传递的 的数据
      onclose: function () {},
      oniframeload: function () {
        // console.log('iframe ready')
      },
    })
    .showModal();
  return false;
}
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
  var resParam=Object.assign({'list':'result'},resParam);
  $("#"+obj+"Val").Autocomplete({
      serviceUrl: url, //异步请求
      paramName: param.paramName,//查询参数，默认 query
      params:param.params || {},
      dataType: 'json',
      minChars: '0', //触发自动匹配的最小字符数
      maxHeight: '300', //默认300高度
      dataReader:resParam,
      triggerSelectOnValidInput: false, // 必选
      showNoSuggestionNotice: true, //显示查无结果的container
      noSuggestionNotice: '查询无结果',//查无结果的提示语
      onSelect: function (result) {
          select && select(result)
          $("#"+obj).val(result.data);
          $("#"+obj+"Val").attr("data-value",result.value);
      },
      onNoneSelect: function (params, suggestions) {
          console.log(params, suggestions);
          noneSelect && noneSelect();
          var value=$("#"+obj+"Val").val();
          if(value != $("#"+obj+"Val").attr("data-value"))
          {
              $("#"+obj).val("");
              $("#"+obj+"Val").val("");
          }
      }
  });
}