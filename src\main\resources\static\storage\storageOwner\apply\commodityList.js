$(function () {
    /* 获取dialog上层实例 */
    var dialog = parent.dialog.get(window);
    if (dialog) {
        var dialog_data = dialog.data;
        $('#search_vl').val(dialog_data)
    }



    $('#table_a').XGrid({
        url: '/proxy-storage/storage/adjustment/getProductList',
        /* postData:{
          name:dialog_data
        }, */
        colNames: ['id','商品大类','商品编码','原商品编码', '商品名称', '商品规格', '生产厂家','生产厂家编码', '产地', '单位', '批准文号','业务类型','业务类型id'],
        colModel: [{
            name: 'id',
            index: 'id',
            hidden:true,
            hidegrid:true
        }, {
            name:'largeCategoryVal',
            index:'largeCategoryVal',
            hidden:true
        },
            { name: 'productCode',
                index: 'productCode'
            },{
                name: 'oldProductCode',
                index: 'oldProductCode',
                hidden:true

            },{ name: 'productName',
                index: 'productName'
            }, {name: 'specifications',
                index: 'specifications'},
            {name: 'manufacturerVal',
                index: 'manufacturerVal' },
            {name: 'manufacturer',
                index: 'manufacturer' },

            {name: 'producingArea',
                index: 'producingArea'},
            {name: 'packingUnitVal',index: 'packingUnitVal'   },
            {    name: 'approvalNumber',     index: 'approvalNumber'   },
            {    name: 'channelVal',     index: 'channelVal'  },
            {    name: 'channelId',     index: 'channelId' , hidden:true, hidegrid:true }

            ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        key:'id',
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function (id, dom, obj, index, event) {
            $('#table_b').setGridParam({
                url: '/proxy-storage/storage/adjustment/findBatchProductPageInfo',
                postData: {
                    productCode: obj.productCode,
                    channelId:obj.channelId

                }
            }).trigger('reloadGrid');
        },
        pager: '#grid_pager_a'
    });

    var jsonStore = eval($('#strStore').val());
    var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

    /* table_b */
    $('#table_b').XGrid({
        colNames: ['id','库房名称', '批号', '生产日期', '有效期至', '库存数量', '成本价','是否可以编辑','库别','业务类型'],
        selectandorder: false,
        key: 'id',
        colModel: [{
            name: 'id',
            index: 'id',
            hidden:true

        },{
            name: 'storeName',
            index: 'storeName',
            formatter: function (e) {
                if(!re.test(e)){
                    return e;
                }
                var result = "";
                $.each(jsonStore,function(idx,item){
                    if(item.numCode == e){
                        result = item.name;
                        return false;
                    }
                });
                return result;
            }
        }, {
            name: 'batchCode',
            index: 'batchCode'

        }, {
            name: 'productDateStr',
            index: 'productDateStr'

        }, {
            name: 'validateDateStr',
            index: 'validateDateStr'

        }, {
            name: 'batchStockNum',
            index: 'batchStockNum'

        },{
            name: 'costPrice',
            index: 'costPrice',
            hidden:true

        },
            {
                name: 'canEdit',
                index: 'canEdit',
                hidden:true
            },

            {
                name: 'storageType',
                index: 'storageType',
                hidden:true
            },
            {
                name: 'channelId',
                index: 'channelId',
                hidden:true
            }

        ],
        rowNum: 0,
        key:'id',
        altRows: true, //设置为交替行表格,默认为false
        onSelectRow: function (id, dom, obj, index, event) {
            var productData = $('#table_a').XGrid('getSeleRow');
            if (productData == undefined  || productData == '' ){
                utils.dialog({
                    title: '提示',
                    content: '请选择商品',
                    width: '300',
                    cancel: false,
                    okValue: '确认',
                    ok: function () {
                    }
                }).showModal();
                return;
            }

            if (productData.channelId =='162'){
                utils.dialog({
                    title: '提示',
                    content: '业务类型162为线下渠道,不能进行货主转移',
                    width: '300',
                    cancel: false,
                    okValue: '确认',
                    ok: function () {
                    }
                }).showModal();
                return;
            }




            var batchData =  $('#table_b').XGrid('getRowData',id);
            var data = {}
            data.product = productData;
            data.batchData = batchData;
            dialog.close(data);
        }

    });

    /* 查询 */
    $('#search').on('click', function () {
        $('#table_a').setGridParam({
            url: '/proxy-storage/storage/adjustment/getProductList',
            postData: {
                param:  $('#search_vl').val()
            }
        }).trigger('reloadGrid');
    })

    /* 确定 */
    $('#sub').on('click', function () {
        var productDataArray = $('#table_a').XGrid('getSeleRow');
        var batchDataArray =  $('#table_b').XGrid('getSeleRow');
        if (productDataArray == undefined || productDataArray == "" || !batchDataArray){
            utils.dialog({
                title: '提示',
                content: '请选择商品',
                width: '300',
                cancel: false,
                okValue: '确认',
                ok: function () {

                }
            }).showModal();

            return;
        }

        var obj = {
            product:productDataArray,
            batchData:batchDataArray
        }
        dialog.close(obj);


    })


})