  //var orgCode = "${orgCode!''}";
   var orgCodes="${orgCodes!''}";
   var roleName="${roleName!''}";
   console.log("roleManage-->load start:orgCodes="+orgCodes);
   var queryRoleListUrl="/proxy-sysmanage/sysmanage/role/queryRoleListByItAdmin";
   function showDialog(titleText,contentText){
        //提示框
        utils.dialog({
            width: 180,
            height: 30,
            title: titleText,
            content: contentText,
            quickClose: false,
            okValue: '确定',
            ok: function () {},
        }).showModal();
    }
    
    function showDialogWithCallback(titleText,contentText,callback){
        //提示框
        utils.dialog({
            width: 180,
            height: 30,
            title: titleText,
            content: contentText,
            quickClose: false,
            okValue: '确定',
            ok: callback,
        }).showModal();
    }
    
        
    //弹框提示
    function showTips(contentText){
        utils.dialog({
            content: contentText,
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
    

    
    function searchData(){
        var result = searchCheck();
        if(!result.passed){
            return;
        }
        console.log("seachData-->click--data:"+JSON.stringify(result));
        $("#X_Table").setGridParam({
            url: queryRoleListUrl,
            postData: result.data
        }).trigger('reloadGrid');
    }
    
    $(function () {
    	var deptCode = $("#dptList option:selected").val();
    	var roleName = $("#name").val();
        //角色表
        $('#X_Table').XGrid({
            url: queryRoleListUrl,
            postData:{deptCode:deptCode, roleName:roleName},
            colNames: ['部门', '角色编号', '角色名'],
            colModel: [{
                name: 'dptName',
                index: 'dptName'
            },  {
                name: 'roleCode',
                index: 'roleCode'
            },  {
                name: 'name',
                index: 'name'
            }
            ],
            rowNum: 20,
            rowList:[20,50,100],
            altRows: true, //设置为交替行表格,默认为false
            rownumbers: true,
            ondblClickRow: function (e, c, a, b) {
                console.log('双击行事件', e, c, a, b);
            }, 
             onSelectRow: function (e, c, a, b) {
                console.log('单机行事件', e, c, a, b);
            }, 
            pager: '#grid-pager'
        });
        
        //查询数据，重置data
        $('#searchData').on('click', function () {
            searchData();
        });        
    });   
    
    //验证查询参数
    function searchCheck(){
        var result={passed:false};
        var name = $("#name").val();        

        
        var dptCodeVal = $("#dptList").children('option:selected').val();
       
        var dptCodeStr=dptCodeVal;
                
        if(dptCodeVal==""){
            dptCodeStr="-1";
        }

        name=$.trim(name);

        result.passed=true;


        result.data={deptCode:dptCodeStr, roleName:name};

        return result;
     }
    
    function initDeptList() {
        var dptoptions = "<option value='-1'>--请选择--</option>";
        $("#dptList").html(dptoptions);
        queryDeptByOrgCodes();        
     }

    function queryDeptByOrgCodes(){
        $.ajax({
            url:"/proxy-sysmanage/sysmanage/dept/getDeptListByOrgCodes",
            data:{},
            type:"get",
            success:function(data){
                var dptoptions = "<option value='-1'>--请选择--</option>";
                if(data.code==0){
                    if(data.result){
                         $.each(data.result, function(i, dpt){  
                            dptoptions+="<option value='"+dpt.dptCode+"'>"+dpt.dptName+"</option>";
                         });
                    }
                    $("#dptList").html(dptoptions);
                    //选中下拉列表
                    //$("#dptList").val(user.dptCode);
                }else{
                    console.log("getDeptListByOrgCodes-->获取数据失败！");
                    $("#dptList").html(dptoptions); 
                }
            }
        });
    }
    
    /* 导出 */
    $('#exportData').on('click', function () {
        var tableId = "X_Table";
        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });
            var len = Number($('#grid-pager #totalPageNum').text());
            //获取当前选中项
            var data = $('#' + tableId).XGrid('getSeleRow');
            if (data && data.length && data.length >0) {
                /*  if (!data.length) {
                      data = [data];
                  }*/
                data = data.map(function (item, key) {
                    var new_item = {};
                    colName.forEach(function (val, index) {
                        new_item[val] = item[val]
                    })
                    return new_item
                })
                len = data.length;
                data = JSON.stringify(data);
                // formData["selectData"] = data;
            } else {
                //如果一个也没选就导出全部
                // data = $('#' + tableId).XGrid('getRowData');
                data = '';
            }
            var dptCodeVal = $("#dptList").children('option:selected').val();          
            var dptCodeStr=dptCodeVal;            
            
            if(dptCodeVal==""){
                dptCodeStr="-1";
            }
            var obj = {
            		roleName: $("#name").val(),
            		deptCode: dptCodeStr,
                	selectData: data,
                	colNames: colName,
               		colNameDesc: colNameDesc
            }
           // obj["nameModel"] = nameModel;
         // 是否超出限制    
		    utils.exportAstrictHandle('X_Table', len, 1).then( () => {
		        return false;
		    }).catch( () => {
		    	httpPost("/proxy-sysmanage/sysmanage/role/exportRoleList", obj);            
		    });
        });
    });
    
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    initDeptList();