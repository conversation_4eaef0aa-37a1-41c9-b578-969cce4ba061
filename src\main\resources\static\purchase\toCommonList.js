function timeStamp2String(time){
    var datetime = new Date();
    datetime.setTime(time);
    var year = datetime.getFullYear();
    var month = datetime.getMonth() + 1;
    var date = datetime.getDate();
    var hour = datetime.getHours();
    var minute = datetime.getMinutes();
    var second = datetime.getSeconds();
    var mseconds = datetime.getMilliseconds();
    return year + "-" + month + "-" + date;


};
function timeStampShiFenMiao(shijianchuo)
{
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }


$(function () {


    $('div[fold=head]').fold({
        sub: 'sub'
    });
    //商品联想搜索
    var selectvlproduct="";
    $('#productNameKeyword').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/supplier/findPageInfoByMnemonInfo', //异步请求
        paramName: 'keyword',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.productName +"\xa0\xa0\xa0"+ dataItem.specifications +"\xa0\xa0\xa0"+ dataItem.manufacturerName, data: dataItem.productCode};
                })
            };
        },
        onSelect: function (result) {
            $("#productCode").val(result.data);
            selectvlproduct = result.value;
            //$("#createUser").val(result.data);
            // console.log('选中回调')
        },onNoneSelect: function (params, suggestions) {
            $("#productNameKeyword").val("");
            $("#productCode").val("");
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });




    // 开始时间与结束时间初始化显示
    var date=new Date();
    var year=date.getFullYear();
    var mouth=date.getMonth() + 1;
    var dates=date.getDate();
    if(mouth < 10){
        mouth = "0" + mouth;
    }
    if(dates < 10){
        dates = "0" + dates;
    }

    $('#startDate').val(''+year+ '-' +mouth+ '-' +'01');
    $('#endDate').val(''+year+ '-' +mouth+ '-' + dates);
    // tabs 切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),  $nav_content = $('.nav-content');
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).css('display', 'block').siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');
        $("#btn_toggle").children("button[text='设置显示列']").eq($this.index()).show().siblings().hide();
        tabAbout()
        $('#productProduceFactory_group').css('display',$this.index() != 0 ? 'block': 'none')
    });
    tabAbout();
    function tabAbout() {
        var text = $('#toggle_wrap li.active a').text();
        if(text == '单据列表'){
            $('.con-body').show();
            $('#batch_group').hide();
            $('#common_kubie').hide();
            $('#createBegin').show();
            $('#createEnd').show();
            $('#fp_search').show();
        }else if(text == '商品明细'){
            $('.con-body').hide();
            $('#batch_group').show();
            $('#common_kubie').show();
            $('#createBegin').show();
            $('#createEnd').show();
            $('#fp_search').show();
        }else if(text == '商品汇总'){
            $('.con-body').hide();
            $('#batch_group').hide();
            $('#common_kubie').hide();
            $('#createBegin').hide();
            $('#createEnd').hide();
            $('#fp_search').hide();
        }
    }
    var userFlag = $('#jiTuanUserFlag').val();
    var colNameA =[];
    var colModelA =[];
    var colNameB =[];
    var colModelB =[];
    var colNameC =[];
    var colModelC =[];
    //集团账号
    if (userFlag=='1') {
        var colNameA =['id','业务类型','摘要', '单据编号', '供应商编号', '供应商名称','含税金额','单据日期','返利冲价含税金额','单品冲价含税金额','优品冲价含税金额','单价录错含税金额','备注','机构'];
        var colModelA = [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                hidden:true ,
                hidegrid: true ,
            },
            {
                name: 'channelId'
            },

            {
                name: 'moveTypeName'
            },
            {
                name: 'orderNo'
            },
            {
                name: 'supplierCode'
            },
            {
                name: 'supplierName'
            },
            {
                name: 'priceTaxSum'
            } ,
            {
                name: 'createTime'
            } ,
            {
                name: 'rebatePriceContainTax'
            }
            ,{
                name: 'singlePriceContainTax'
            },
            {
                name: 'superiorPriceContainTax'
            },
            {
                name: 'recordingPriceContainTaxs'
            },
            {
                name: 'remark'
            },
            {
                name: 'orgName',//机构

            }

        ],allColModelA = JSON.parse(JSON.stringify(colModelA));
        colNameB = [ 'id','业务类型','摘要','单据编号','单据日期','商品编号', '商品名称', '单位','数量','含税单价', '含税金额','不含税单价','不含税金额','税额','生产批号','生产厂家','规格','产地','生产日期','有效期至', '供应商名称','华润采购直联出库数量','原含税单价','含税差价','库别','数据生成时间','机构'];
        colModelB =[
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                hidden:true ,
                hidegrid: true ,
            },
            {
                name: 'channelId'
            },

            {
                name: 'moveTypeName',
            },
            {
                name: 'orderNo', //入库日期
            },
            {
                name: 'orderTime', //单据日期
            },
            {
                name: 'productCode',//商品编号
            },
            {
                name: 'productName',//商品名称
            }
            ,
            {
                name: 'productPackUnitSmall',//单位
            } ,
            {
                name:'realNum',//数量
            } ,
            {
                name: 'productContainTaxPrice', //含税单价
            }
            ,
            {
                name: 'productContainTaxMoney',//含税金额
            },

            {
                name: 'productNoTaxPrice', //不含税单价
            },
            {
                name: 'procutNoTaxMoney'//不含税金额
            },
            {
                name: 'productTaxMoney',//税额
            },
            {
                name: 'productApprovalNumber' //生产批号
            } ,
            {
                name: 'productProduceFactory' //生产厂家
            } ,
            {
                name: 'productSpecification' //规格
            }
            ,
            {
                name: 'productOriginPlace',//商品产地
            } ,
            {
                name: 'productProduceDate',//生产日期
            }
            ,{
                name: 'productExpireDate', //有效期
            },
            {
                name: 'supplierName',//供应商名称
            },
            {
                name: 'planNum',//计划数量
            },
            {
                name: 'originTaxPrice',//原含税单价
            },
            {
                name: 'taxPriceDiffer',//含税差价
            },
            {
                name: 'checkEvaluateName',//库别

            },
            {
                name: 'createTime',//数据生成时间
                formatter:function(xt){
                    if(xt){
                        return  timeStampShiFenMiao(xt);
                    }
                }
            },
            {
                name: 'orgName',//机构

            }

        ], allColModelB = JSON.parse(JSON.stringify(colModelB));
        var colNameC = ['id','业务类型','商品编号', '商品名称', '单位','数量', '含税金额','税额','生产厂家','规格','产地','机构'];
        var colModelC =[
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                hidden:true ,
                hidegrid: true ,
            },
            {
                name: 'channelId'
            },

            {
                name: 'productCode',//商品编号
            },
            {
                name: 'productName',//商品名称
            }
            ,
            {
                name: 'productPackUnitSmall',//单位
            } ,
            {
                name:'productNum',//数量
            } ,
            {
                name: 'productContainTaxMoney', //含税金额
            }
            ,
            {
                name: 'productTaxMoney',//税额
            },

            {
                name: 'productProduceFactory', //不含税单价
            },
            {
                name: 'productSpecification'//不含税金额
            },
            {
                name: 'productOriginPlace',//税额
            },
            {
                name: 'orgName',
            }


        ],allColModelC = JSON.parse(JSON.stringify(colModelC));
    } else {
        var colNameA =['id','业务类型','摘要', '单据编号', '供应商编号', '供应商名称','含税金额','单据日期','返利冲价含税金额','单品冲价含税金额','优品冲价含税金额','单价录错含税金额','备注'];
        var colModelA = [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                hidden:true ,
                hidegrid: true ,
            },
            {
                name: 'channelId'
            },

            {
                name: 'moveTypeName'
            },
            {
                name: 'orderNo'
            },
            {
                name: 'supplierCode'
            },
            {
                name: 'supplierName'
            },
            {
                name: 'priceTaxSum'
            } ,
            {
                name: 'createTime'
            } ,
            {
                name: 'rebatePriceContainTax'
            }
            ,{
                name: 'singlePriceContainTax'
            },
            {
                name: 'superiorPriceContainTax'
            },
            {
                name: 'recordingPriceContainTaxs'
            },
            {
                name: 'remark'
            }

        ],allColModelA = JSON.parse(JSON.stringify(colModelA));
        colNameB = [ 'id','业务类型','摘要','单据编号','单据日期','商品编号', '商品名称', '单位','数量','含税单价', '含税金额','不含税单价','不含税金额','税额','生产批号','生产厂家','规格','产地','生产日期','有效期至', '供应商名称','华润采购直联出库数量','原含税单价','含税差价','库别','数据生成时间'];
        colModelB =[
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                hidden:true ,
                hidegrid: true ,
            },
            {
                name: 'channelId'
            },

            {
                name: 'moveTypeName',
            },
            {
                name: 'orderNo', //入库日期
            },
            {
                name: 'orderTime', //单据日期
            },
            {
                name: 'productCode',//商品编号
            },
            {
                name: 'productName',//商品名称
            }
            ,
            {
                name: 'productPackUnitSmall',//单位
            } ,
            {
                name:'realNum',//数量
            } ,
            {
                name: 'productContainTaxPrice', //含税单价
            }
            ,
            {
                name: 'productContainTaxMoney',//含税金额
            },

            {
                name: 'productNoTaxPrice', //不含税单价
            },
            {
                name: 'procutNoTaxMoney'//不含税金额
            },
            {
                name: 'productTaxMoney',//税额
            },
            {
                name: 'productApprovalNumber' //生产批号
            } ,
            {
                name: 'productProduceFactory' //生产厂家
            } ,
            {
                name: 'productSpecification' //规格
            }
            ,
            {
                name: 'productOriginPlace',//商品产地
            } ,
            {
                name: 'productProduceDate',//生产日期
            }
            ,{
                name: 'productExpireDate', //有效期
            },
            {
                name: 'supplierName',//供应商名称
            },
            {
                name: 'planNum',//计划数量
            },
            {
                name: 'originTaxPrice',//原含税单价
            },
            {
                name: 'taxPriceDiffer',//含税差价
            },
            {
                name: 'checkEvaluateName',//库别

            },
            {
                name: 'createTime',//数据生成时间
                formatter:function(xt){
                    if(xt){
                        return  timeStampShiFenMiao(xt);
                    }
                }
            }

        ], allColModelB = JSON.parse(JSON.stringify(colModelB));


        var colNameC = [ 'id','业务类型','商品编号', '商品名称', '单位','数量', '含税金额','税额','生产厂家','规格','产地'];
        var colModelC =[
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                hidden:true ,
                hidegrid: true ,
            },
            {
                name: 'channelId'
            },

            {
                name: 'productCode',//商品编号
            },
            {
                name: 'productName',//商品名称
            }
            ,
            {
                name: 'productPackUnitSmall',//单位
            } ,
            {
                name:'productNum',//数量
            } ,
            {
                name: 'productContainTaxMoney', //含税金额
            }
            ,
            {
                name: 'productTaxMoney',//税额
            },

            {
                name: 'productProduceFactory', //不含税单价
            },
            {
                name: 'productSpecification'//不含税金额
            },
            {
                name: 'productOriginPlace',//税额
            }
        ],allColModelC = JSON.parse(JSON.stringify(colModelC));
    }




    var grid_datax = [
        {id: "1", type: "Atest", pay: "1000", name: "abc", text: "ccc",text1:"300",text2:"300s",text3:"双方都",text4:"发生",text5:"232",text6:"32",text7:"2323s",text8:"fw"},
        {id: "2", type: "Etest", pay: "1000", name: "abc", text: "ccc",text1:"300",text2:"300s",text3:"双方都",text4:"发生",text5:"232",text6:"32",text7:"2323s",text8:"fw"}
    ];
    var grid_dataY = [
        {id: "3", type: "Btest", pay: "1000", name: "abc", text: "ccc",text1:"300",text2:"300s",text3:"双方都",text4:"发生",text5:"232",text6:"32",text7:"2323s",text8:"fw"},
        {id: "4", type: "Ctest", pay: "1000", name: "abc", text: "ccc",text1:"300",text2:"300s",text3:"双方都",text4:"发生",text5:"232",text6:"32",text7:"2323s",text8:"fw"}
    ];

    function storenum (cellV){
        /*if(cellV != undefined && cellV!=""){
            cellV = (parseInt(cellV));
        }*/
        return cellV;
    };
    //获取采购类型
    var moveTypeCodes = "";
    var obj=document.getElementsByName('specialAttributes'); //选择所有name="'test'"的对象，返回数组
    //取到对象数组后，我们来循环检测它是不是被选中
    for(var i=0; i<obj.length; i++){
        if(obj[i].checked)
            moveTypeCodes+=obj[i].value+','; //如果选中，将value添加到变量s中
    }

    $('#X_Tablea').XGrid({
        // url:'/proxy-purchase/purchase/common/selectPurchaseCommonData',
        // postData: {"orderNo":  '',"startDate":'',"endDate":'',
        //            "supplierCode":'',
        //             "moveTypeCodes":moveTypeCodes},
        data: [],
        colNames: ['业务类型','摘要', '单据编号', '供应商编号', '供应商名称','含税金额','单据日期','返利冲价含税金额','单品冲价含税金额','优品冲价含税金额','单价录错含税金额','备注','机构'],

        colModel: [
            {
                name: 'channelId',
                index: 'channelId'
            },{
                name: 'moveTypeName'
            },
            {
                name: 'orderNo'
            },
            {
                name: 'supplierCode'
            },
            {
                name: 'supplierName'
            },
            {
                name: 'priceTaxSum'
            } ,
            {
                name: 'createTime',
                formatter:function(xt){
                    if(xt){
                        return  timeStamp2String(xt);
                    }
                }
            } ,
            {
                name: 'rebatePriceContainTax'
            }
            ,{
                name: 'singlePriceContainTax'
            },
            {
                name: 'superiorPriceContainTax'
            },
            {
                name: 'recordingPriceContainTaxs',
                formatter:function(xt){
                    if(xt == '0'){
                        return  "0.00";
                    }else{
                        return xt;
                    }
                }
            },
            {
                name: 'remark'
            },
            {
                name: 'orgName',
                hidden: userFlag=='1'?false:true,
                hidegrid: userFlag=='1'?false:true
            },
            {
                name: 'moveTypeCode',
                hidden:true ,
                hidegrid: true ,
            }

        ],
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-a',//设置翻页所在html元素名称
        selectandorder: true,
        key: "orderNo",
        ondblClickRow: function (id, dom, obj, index, event) {
            //双击事件回调函数
            var selRow = obj;
            if (selRow) {
                documentDetail_search(selRow);
            }else {
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }
        }
    });
    var userFlag = $('#jiTuanUserFlag').val();
    var mingxiArr=[];
    var mingxiZiDuanArr=[];

    mingxiArr = ['业务类型','摘要','单据编号','单据日期','商品编号', '商品名称', '单位','数量','含税单价', '含税金额','不含税单价','不含税金额','税额','生产批号','生产厂家','规格','产地','生产日期','有效期至', '供应商名称','华润采购直联出库数量','原含税单价','含税差价','库别','数据生成时间','机构'];

    $('#X_Tableb').XGrid({
        // url:'/proxy-purchase/purchase/common/selectPurchaseCommonProductData',
        // postData: {"orderNo":  '',"startDate":'',"endDate":'',
        //     "supplierCode":'',
        //     "moveTypeCodes":moveTypeCodes},
        data: [],
        colNames: mingxiArr,
        colModel: [
            {
                name: 'channelId',
                index: 'channelId'
            },{
                name: 'moveTypeName',
            },
            {
                name: 'orderNo', //入库日期
            },
            {
                name: 'orderTime', //单据日期
            },
            {
                name: 'productCode',//商品编号
            },
            {
                name: 'productName',//商品名称
            }
            ,
            {
                name: 'productPackUnitSmall',//单位
            } ,
            {
                name:'realNum',//数量
            } ,
            {
                name: 'productContainTaxPrice', //含税单价
            }
            ,
            {
                name: 'productContainTaxMoney',//含税金额
            },

            {
                name: 'productNoTaxPrice', //不含税单价
            },
            {
                name: 'procutNoTaxMoney'//不含税金额
            },
            {
                name: 'productTaxMoney',//税额
            },
            {
                name: 'productApprovalNumber' //生产批号
            } ,
            {
                name: 'productProduceFactory' //生产厂家
            } ,
            {
                name: 'productSpecification' //规格
            }
            ,
            {
                name: 'productOriginPlace',//商品产地
            }  ,
            {
                name: 'productProduceDate',//生产日期
            }
            ,{
                name: 'productExpireDate', //有效期
            },
            {
                name: 'supplierName',//供应商名称
            },
            {
                name: 'planNum',//计划数量
            },
            {
                name: 'originTaxPrice',//原含税单价
            },
            {
                name: 'taxPriceDiffer',//含税差价
            },
            {
                name: 'checkEvaluateName',//库别

            },
            {
                name: 'createTime',//数据生成时间
                formatter:function(xt){
                    if(xt){
                        return  timeStampShiFenMiao(xt);
                    }
                }
            },
            {
                name: 'orgName',//机构
                hidden: userFlag=='1'?false:true,
                hidegrid: userFlag=='1'?false:true
            }
        ],
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-b',//设置翻页所在html元素名称
        selectandorder: true, //是否展示序号，多选
    });



    $('#X_Tablec').XGrid({
        // url:'/proxy-purchase/purchase/common/selectPurchaseCommonSummaryData',
        // postData: {"flag":  1 },
        data: [],
        colNames: ['业务类型','商品编号', '商品名称', '单位','数量', '含税金额','税额','生产厂家','规格','产地','机构'],
        colModel: [
            {
                name: 'channelId',
                index: 'channelId'
            },{
                name: 'productCode',//商品编号
            },
            {
                name: 'productName',//商品名称
            }
            ,
            {
                name: 'productPackUnitSmall',//单位
            } ,
            {
                name:'productNum',//数量
            } ,
            {
                name: 'productContainTaxMoney', //含税金额
            }
            ,
            {
                name: 'productTaxMoney',//税额
            },

            {
                name: 'productProduceFactory', //不含税单价
            },
            {
                name: 'productSpecification'//不含税金额
            },
            {
                name: 'productOriginPlace',//税额
            },
            {
                name: 'orgName',
                hidden: userFlag=='1'?false:true,
                hidegrid: userFlag=='1'?false:true
            }

        ],
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-c',//设置翻页所在html元素名称
        selectandorder: true, //是否展示序号，多选
    });
    //查询数据，重置data
    $('#searchBtn').on('click', function () {
        var cs = $("#toggle_wrap .active").attr("id");
        moveTypeCodes = "";
        var obj=document.getElementsByName('specialAttributes'); //选择所有name="'test'"的对象，返回数组
        //取到对象数组后，我们来循环检测它是不是被选中
        for(var i=0; i<obj.length; i++){
            if(obj[i].checked)
                moveTypeCodes+=obj[i].value+','; //如果选中，将value添加到变量s中
        }

        if(moveTypeCodes == ""){
            utils.dialog({content: '没有选中采购类型！', quickClose: true, timeout: 2000}).showModal();
            // alert("请选择采购类型！");
            return false;
        }else{
            moveTypeCodes = moveTypeCodes.substring(0,moveTypeCodes.length-1);
        }

        $("#moveTypeCodes").val(moveTypeCodes);
        if(cs == "orderList"){
            //清空下面的数据
            $('#X_Table_document_detail').XGrid('clearGridData');
            // alert("左边的");
            //加载左边联想的数据
            $('#X_Tablea').setGridParam({
                url: '/proxy-purchase/purchase/common/selectPurchaseCommonData?'+$("#searchForm").serialize(),
                postData: {a:  1 }
            }).trigger('reloadGrid');
        }else if(cs == "productList") {
            // alert("右边的");
            //加载右边联想的数据
            $('#X_Tableb').setGridParam({
                url: '/proxy-purchase/purchase/common/selectPurchaseCommonProductData?' + $("#searchForm").serialize(),
                postData: {a: 1}
            }).trigger('reloadGrid');
        }else if(cs == "productTotal"){
            //判断是否选中一个采购类型
            var strs = moveTypeCodes.split(",");
            if(strs.length > 1){
                utils.dialog({content: '只能选中一个采购类型！', quickClose: true, timeout: 2000}).showModal();
                // alert("只能选中一个采购类型");
                return false;
            }
            $('#X_Tablec').setGridParam({
                url: '/proxy-purchase/purchase/common/selectPurchaseCommonSummaryData?' + $("#searchForm").serialize(),
                postData: {a: 1}
            }).trigger('reloadGrid');
        }else{
            return;
        }

    })


    function ObjData(key,value){
        this.Key=key;
        this.Value=value;
    }
    //查询数据，重置data
    $('#exportBtn').on('click', function () {
            var cs = $("#toggle_wrap .active").attr("id");
            if(cs == "orderList"){
                utils.exportAstrictHandle('X_Tablea',Number($('#grid-pager-a #totalPageNum').text())).then(()=>{
                    return false;
            }).catch(()=> {
                var ck = false;
                var nameModel = "";
                addHtmlA(colNameA);
                dialog({
                    content: $("#setColA"),
                    title: '筛选列',
                    width: 706,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {
                        $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                            if ($(this).is(":checked")) {
                                nameModel += allColModelA[index+1].name + ":"+$(this).attr('name') + ","
                            }
                        });
                        if(nameModel.length == 0){
                            utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                        var sel =  $('#X_Tablea').XGrid('getSeleRow');
                        var selectOrderNos = [];
                        if(sel != undefined && sel.length >0){
                            $.each(sel,function(i,obj){
                                selectOrderNos.push(obj.orderNo);
                            })
                        }
                        //如果有选中的行，则导出选中的行，否则导出所有查询条件的
                        var params;
                        if (selectOrderNos.length==0){
                            moveTypeCodes ="";
                            //获取采购类别
                            var obj=document.getElementsByName('specialAttributes'); //选择所有name="'test'"的对象，返回数组
                            //取到对象数组后，我们来循环检测它是不是被选中
                            for(var i=0; i<obj.length; i++){
                                if(obj[i].checked)
                                    moveTypeCodes+=obj[i].value+','; //如果选中，将value添加到变量s中
                            }
                            if(moveTypeCodes == ""){
                                // alert("请选择采购类型！");
                                utils.dialog({content: '没有选中采购类型！', quickClose: true, timeout: 2000}).showModal();
                                return false;
                            }
                            var channelId=$("#channelId").val();
                            $("#moveTypeCodes").val(moveTypeCodes);
                            params = {
                                "nameModel": nameModel,
                                "orderNo": $("#keywordOrderByNew").val(),
                                "startDate": $("#startDate").val(),
                                "endDate":  $("#endDate").val(),
                                "supplierCode":  $("#supplierCode").val(),
                                "moveTypeCodes":  $("#moveTypeCodes").val(),
                                "productCode": $("#productCode").val(),
                                "channelId":channelId,
                                "orgCode": $("#orgCode").val(),
                            }
                        }else {
                            params = {"selectOrderNos":selectOrderNos.toString(),"nameModel": nameModel};
                        }
                        httpPost("/proxy-purchase/purchase/common/exportPurchaseCommonData", params);
                    },
                    // copy button to other dialogues
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if(!ck){
                                    $("#checkRow input").prop("checked",false);
                                    ck = true;
                                }else if(ck){
                                    $("#checkRow input").prop("checked","checked");
                                    ck = false;
                                }else{
                                    return false;
                                };
                                return false;
                            }
                        }
                    ]
                    //copy ends here
                }).showModal();
            })
            }else if(cs == "productList"){
                // alert("右边的");
                //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                utils.exportAstrictHandle('X_Tableb',Number($('#grid-pager-b #totalPageNum').text())).then(()=>{
                    return false;
            }).catch(()=> {
                var ck = false;
                // copy this parameter and the below buttons
                var rowDt  = $('#X_Tableb').XGrid('getSeleRow');

                var nameModel = "";
                addHtmlB(colNameB);
                dialog({
                    content: $("#setColB"),
                    title: '筛选列',
                    width: 706,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {

                        var newColName = [], newColModel = [];
                        $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                            if ($(this).is(":checked")) {
                                // newColName.push($(this).attr('name'));
                                // newColModel.push(allColModelB[index]);
                                nameModel += allColModelB[index+1].name + ":"+$(this).attr('name') + ","
                            }
                        });
                        if(nameModel.length == 0){
                            utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                        var sel =  $('#X_Tableb').XGrid('getSeleRow');
                        var selectOrderNos = [];
                        var selectIds = [];
                        if(sel != undefined && sel.length >0){
                            $.each(sel,function(i,obj){
                                selectOrderNos.push(obj.orderNo);
                                selectIds.push(obj.id);
                            })
                        }

                        //如果有选中的行，则导出选中的行，否则导出所有查询条件的
                        var params;
                        if (selectOrderNos.length==0){
                            moveTypeCodes ="";
                            //获取采购类别
                            var obj=document.getElementsByName('specialAttributes'); //选择所有name="'test'"的对象，返回数组
                            //取到对象数组后，我们来循环检测它是不是被选中
                            for(var i=0; i<obj.length; i++){
                                if(obj[i].checked)
                                    moveTypeCodes+=obj[i].value+','; //如果选中，将value添加到变量s中
                            }
                            if(moveTypeCodes == ""){
                                // alert("请选择采购类型！");
                                utils.dialog({content: '没有选中采购类型！', quickClose: true, timeout: 2000}).showModal();
                                return false;
                            }
                            $("#moveTypeCodes").val(moveTypeCodes);
                            params = {
                                "nameModel": nameModel,
                                "orderNo": $("#keywordOrderByNew").val(),
                                "startDate": $("#startDate").val(),
                                "endDate":  $("#endDate").val(),
                                "supplierCode":  $("#supplierCode").val(),
                                "moveTypeCodes":  $("#moveTypeCodes").val(),
                                "productCode": $("#productCode").val(),

                                "checkEvaluate":  $("#checkEvaluate").val(),
                                "createTimeBegin":  $("#createTimeBegin").val(),
                                "createTimeEnd":  $("#createTimeEnd").val(),
                                "orgCode": $("#orgCode").val(),
                                "channelId":$("#channelId").val(),
                                "productProduceFactory":$("#productProduceFactory").val()
                            }
                        }else {
                            params = {"selectOrderNos":selectOrderNos.toString(),"selectIds":selectIds.toString(),
                                "nameModel": nameModel};
                        }
                        httpPost("/proxy-purchase/purchase/common/exportPurchaseCommonProductData", params);
                    },
                    // copy button to other dialogues
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if(!ck){
                                    $("#checkRow input").prop("checked",false);
                                    ck = true;
                                }else if(ck){
                                    $("#checkRow input").prop("checked","checked");
                                    ck = false;
                                }else{
                                    return false;
                                };
                                return false;
                            }
                        }
                    ]
                    //copy ends here
                }).showModal();
            })

            }else if(cs == "productTotal") {
                // alert("右边的");
                utils.exportAstrictHandle('X_Tablec',Number($('#grid-pager-c #totalPageNum').text())).then(()=>{
                    return false;
            }).catch(()=> {
                moveTypeCodes = "";
                //获取采购类别
                var obj = document.getElementsByName('specialAttributes'); //选择所有name="'test'"的对象，返回数组
                //取到对象数组后，我们来循环检测它是不是被选中
                for (var i = 0; i < obj.length; i++) {
                    if (obj[i].checked)
                        moveTypeCodes += obj[i].value + ','; //如果选中，将value添加到变量s中
                }
                if (moveTypeCodes == "") {
                    // alert("请选择采购类型！");
                    utils.dialog({content: '没有选中采购类型！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }else{
                    moveTypeCodes = moveTypeCodes.substring(0,moveTypeCodes.length-1);
                }

                //判断是否选中一个采购类型
                var strs = moveTypeCodes.split(",");

                if(strs.length > 1){
                    utils.dialog({content: '只能选中一个采购类型！', quickClose: true, timeout: 2000}).showModal();
                    // alert("只能选中一个采购类型");
                    return false;
                }

                //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                var ck = false;
                // copy this parameter and the below buttons
                var rowDt = $('#X_Tablec').XGrid('getSeleRow');

                var nameModel = "";
                addHtmlC(colNameC);
                dialog({
                    content: $("#setColC"),
                    title: '筛选列',
                    width: 706,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {

                        var newColName = [], newColModel = [];
                        $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                            if ($(this).is(":checked")) {
                                nameModel += allColModelC[index + 1].name + ":" + $(this).attr('name') + ","
                            }
                        });
                        if (nameModel.length == 0) {
                            utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                        var sel = $('#X_Tablec').XGrid('getSeleRow');
                        var selectOrderNos = [];
                        if (sel != undefined && sel.length > 0) {
                            $.each(sel, function (i, obj) {
                                selectOrderNos.push(obj.id);
                            })
                        }
                        // alert(selectOrderNos);
                        // return false;
                        //如果有选中的行，则导出选中的行，否则导出所有查询条件的
                        var params;
                        if (selectOrderNos.length == 0) {

                            $("#moveTypeCodes").val(moveTypeCodes);
                            params = {
                                "nameModel": nameModel,
                                "orderNo": $("#keywordOrderByNew").val(),
                                "startDate": $("#startDate").val(),
                                "endDate": $("#endDate").val(),
                                "supplierCode": $("#supplierCode").val(),
                                "moveTypeCodes": $("#moveTypeCodes").val(),
                                "productCode": $("#productCode").val(),
                                "channelId":$("#channelId").val(),
                                "orgCode": $("#orgCode").val(),
                                "productProduceFactory":$("#productProduceFactory").val()
                            }
                        } else {
                            params = {"selectOrderNos": selectOrderNos.toString(), "nameModel": nameModel,"moveTypeCodes": $("#moveTypeCodes").val()};
                        }
                        httpPost("/proxy-purchase/purchase/common/exportPurchaseCommonSummaryData", params);
                    },
                    // copy button to other dialogues
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if (!ck) {
                                    $("#checkRow input").prop("checked", false);
                                    ck = true;
                                } else if (ck) {
                                    $("#checkRow input").prop("checked", "checked");
                                    ck = false;
                                } else {
                                    return false;
                                }

                                return false;
                            }
                        }
                    ]
                    //copy ends here
                }).showModal();
            })
            }
            else{
                return;
            }
    })


    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }


    $("#set_tables_rowa").click(function () {
        let _curIndex = $('#toggle_wrap li.active').index();
        let tableIdArr = ['X_Tablea','X_Tableb','X_Tablec'];
        $('#'+tableIdArr[_curIndex]).XGrid('filterTableHead');

    })
    function addHtmlA(arry) {
        if (!$('#setColA')[0]) {
            var s = '<div id="setColA" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 1; i < arry.length; i++) {
                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //筛选列
    $("#set_tables_rowb").click(function () {
        let _curIndex = $('#toggle_wrap li.active').index();
        let tableIdArr = ['X_Tablea','X_Tableb','X_Tablec'];
        $('#'+tableIdArr[_curIndex]).XGrid('filterTableHead');

    })
    function addHtmlB(arry) {
        if (!$('#setColB')[0]) {
            var s = '<div id="setColB" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 1; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    function addHtmlC(arry) {
        if (!$('#setColC')[0]) {
            var s = '<div id="setColC" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 1; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    $("#set_tables_rowc").click(function () {
        $('#X_Tablec').XGrid('filterTableHead');

    })


    var ts =  '/proxy-purchase/purchase/supplier/getNewOrganBaseListByMnemonInfo';
    autoc();
    var sv = '';

    var selectvlsupplier ="";
    function autoc() {
        $('#factory_aotuname').Autocomplete({
            serviceUrl: ts, //异步请求
            paramName: 'name',//查询参数，默认 query
            dataType: 'json',
            //lookup: ts, //监听数据 value显示文本，data为option的值
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            showNoSuggestionNotice: true,
            noSuggestionNotice: '查询无结果',
            triggerSelectOnValidInput: false, // 必选
            autoSelectFirst: true,
            transformResult: function (response) {
                return {
                    suggestions: $.map(response, function (dataItem) {
                        switch (sv) {
                            case "productBrandManufacturers":
                                return {value: dataItem.brandfactoryName, data: dataItem.brandfactoryId};
                                break;
                            case "productProduceFactory":
                                return {value: dataItem.manufactoryName, data: dataItem.manufactoryId};
                                break;
                            default:
                                return {value: dataItem.supplierName, data: dataItem.supplierCode};
                        }
                    })
                };
            } , onSearchComplete: function (query, suggestions) {
                var $ele = $(this);
                if(suggestions && suggestions.length === 0){
                    $ele.attr('oldvalue','');
                }else {
                    $ele.attr('oldvalue',$ele.val())
                }
                if(!$ele.is(":focus")){
                    $ele.Autocomplete('hide');
                }
            },
            onSelect: function (result) {
                $("#factory_aotuname").attr('oldvalue',result.value);
                //选中回调
                $("#supplier_autocomplete").attr("selectId", result.data);
                $("#supplierCode").val(result.data);
                selectvlsupplier = result.value;
            }, onNoneSelect: function (params, suggestions) {
                $("#supplierCode").val("");
                $("#factory_aotuname").val("");
                setTimeout(function () {
                    $("#factory_aotuname").attr('oldvalue','');
                },200)
            }

        });

    }

    $('#factory_aotuname').on({
        dblclick: function (e) {
            commodity_search_dia($("#factory_aotuname").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#factory_aotuname").attr('oldvalue')){
                commodity_search_dia()
            }
        }
    }).siblings('.glyphicon-search').on("click", function (e) {
        commodity_search_dia($("#factory_aotuname").attr('oldvalue'))
        e.stopPropagation();
    });


    function commodity_search_dia(val) {
        dialog({
            url: '/proxy-purchase/purchase/purchaseOrder/toSupplierPage',
            title: '供应商列表',
            width: $(window).width() * 0.9,
            height: '550px',
            data: val, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#supplierCode").val(data.supplierCode);
                    $("#factory_aotuname").val(data.supplierName);
                }else {
                    $("#supplierCode").val("");
                    $("#factory_aotuname").val("");
                }
            }
        }).showModal();
    };


    var ivkeywordOrderByNew = '';
    $('#keywordOrderByNew').Autocomplete({
        serviceUrl: '/proxy-purchase/purchase/common/selectPurchaseOrderNos', //异步请求
        paramName: 'keywordOrderByNew',//查询参数，默认 query
        dataType: 'json',
        //lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function(response) {
            return {
                suggestions: $.map(response, function(dataItem) {
                    if((dataItem.value).length == 0 ){
                        return [];

                    };
                    return {
                        value: dataItem.value,
                        data: dataItem.data
                    };
                })
            };
        },

        onSelect: function (result) {
            ivkeywordOrderByNew = result.value;

        },onNoneSelect: function (params, suggestions) {
            $("#keywordOrderByNew").val("");
        }

    });





    $("#X_Tableb").on("click",".iahref",function(){
        var ih = $(this).attr("ahref");
        utils.openTabs('purchase_price_management','退补价详情',ih);
    })






    $("#endStoreTime").blur(function(){
        tm();

    })

    $("#startStoreTime").blur(function(){
        xm();
    })

    function tm() {

        var beginTime = $("#startStoreTime").val();
        var endTime = $("#endStoreTime").val();
        if (!beginTime ) {
            return false;
        }
        beginTime = new Date(beginTime.replace(/-/g, "/"));
        endTime = new Date(endTime.replace(/-/g, "/"));

        var days = endTime.getTime() - beginTime.getTime();
        time = parseInt(days / (1000 * 60 * 60 * 24));


        if (time < 0) {
            utils.dialog({content: '开始天数，不能大于结束天数', quickClose: true, timeout: 2000}).showModal();
            $("#endStoreTime").val("");
            return false;
        }

    }

    function xm() {

        var beginTime = $("#startStoreTime").val();
        var endTime = $("#endStoreTime").val();
        if (endTime) {
            //console.log("ssf")

            beginTime = new Date(beginTime.replace(/-/g, "/"));
            endTime = new Date(endTime.replace(/-/g, "/"));

            var days = endTime.getTime() - beginTime.getTime();
            time = parseInt(days / (1000 * 60 * 60 * 24));


            if (time < 0) {
                utils.dialog({content: '开始天数，不能大于结束天数', quickClose: true, timeout: 2000}).showModal();
                $("#startStoreTime").val("");
                return false;
            }
        }else{
            return false;

        }
    }
    // 单据明细
    documentDetail();

    //业务类型搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('0').then( res => {
            console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
        })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
        })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });
})


// 双击查询
function documentDetail_search(obj) {
    $('#X_Table_document_detail').setGridParam({
        url: "/proxy-purchase/purchase/common/selectPurchaseCommonProductData",
        postData: {
            "orderNo": obj.orderNo,
            "startDate":'',
            "endDate":'',
            "supplierCode":'',
            "moveTypeCodes":obj.moveTypeCode
        }
    }).trigger('reloadGrid');
}

// 单据明细
function documentDetail() {
    console.log("进入单据列表明细了……");
    $('#X_Table_document_detail').XGrid({
        data: [],
        colNames: ['业务类型','单据编号', '入库单编号', '商品编号','商品名称','规格','单位','数量','生产批号','生产厂家','含税金额', '原含税单价', '含税差价'],
        colModel: [
            {
                name: 'channelId',
            },{
                name: 'orderNo'
            },
            {
                name: 'stockOrderNo'
            },
            {
                name: 'productCode'
            },
            {
                name: 'productName'
            },
            {
                name: 'productSpecification'
            },
            {
                name: 'productPackUnitSmall'
            } ,
            {
                name: 'realNum'
            } ,
            {
                name: 'productApprovalNumber'
            } ,
            {
                name: 'productProduceFactory'
            }
            ,{
                name: 'productContainTaxMoney'
            },
            {
                name: 'originTaxPrice'
            },
            {
                name: 'taxPriceDiffer'
            }
        ],
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-document-detail',//设置翻页所在html元素名称
        selectandorder: false, //是否展示序号，多选
    });
}

//发票查询
$('#fp_search').on('click',function () {
    var text = $('#toggle_wrap li.active a').text(),selectRow = {};
    if(text === '单据列表'){
        selectRow =  $('#X_Tablea').XGrid('getSeleRow');
    }else if(text === '商品明细'){
        selectRow =  $('#X_Tableb').XGrid('getSeleRow');
    }
    if(selectRow&&selectRow.length){
        if(selectRow.length > 1){
            utils.dialog({content: '只能选择一条数据', quickClose: true, timeout: 2000}).showModal();
        }else {
            console.log("selectRow====:"+selectRow[0].orderNo);
            var orderNo=selectRow[0].orderNo;

            utils.ajax({"itemNo": orderNo}, "/proxy-purchase/purchase/common/queryFinanceTicketInfo", "POST", successCallback);

            function successCallback(data) {
                if (data.code == 0) {
                    utils.openTabs("scm_finance_invoice","采购发票3.0", "/#/finance/purchaseInvoiceNew?invoiceNos="+data.result);

                } else {
                    var msg=data.msg;
                    utils.dialog({content: msg, quickClose: true, timeout: 3000}).showModal();
                }

            }


        }
    }else {
        utils.dialog({content: '请先选择一条数据', quickClose: true, timeout: 2000}).showModal();
    }
});
