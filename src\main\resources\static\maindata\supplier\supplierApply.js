$(function () {
    //tab切换
    $('div[fold=head]').fold({
        sub: 'sub'
    });
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    })

    //切换的质量保证协议
    $('#table1').XGrid({
        data: [{
            id: '1',
            Signdata: {
                test: ''
            },
            vld: {
                test: ''
            },
            Signedperson: {
                signPerson: ''
            },
            enclosure: 1
        }],
        colNames: ['', '签订日期', '有效期至', '签订人', '附件'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'Signdata',
            index: 'Signdata',
            rowtype: '#grid_BeginDate'
        }, {
            name: 'vld',
            index: 'vld',
            rowtype: '#grid_endDate'
        }, {
            name: 'Signedperson',
            index: 'Signedperson',
            rowtype: '#grid_person'
        }, {
            name: 'enclosure',
            index: 'enclosure'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid_page1'
    });
    //客户委托书
    $('#table2').XGrid({
        data: [{
            id: '1',
            proxy: {},
            entrustment: {},
            sex: {},
            phone: {},
            idcard: {},
            address: {},
            idcard_vld: {},
            proxy_vld: {},
            authtype: {},
            authrange: {
                test: ''
            },
            enclosure: ''
        }],
        colNames: ['', '委托书编号', '被委托人', '性别', '电话', '证件号码', '地址', '身份证有效期至', '委托书有效期至', '授权类型',
            '授权范围', '附件'
        ],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'proxy',
            index: 'proxy',
            rowtype: '#entrustCode'
        }, {
            name: 'entrustment',
            index: 'entrustment',
            rowtype: '#entrustPerson'
        }, {
            name: 'sex',
            index: 'sex',
            rowtype: '#sexP'
        }, {
            name: 'phone',
            index: 'phone',
            rowtype: '#phoneP'
        }, {
            name: 'idcard',
            index: 'idcard',
            rowtype: '#crteP'
        }, {
            name: 'address',
            index: 'address',
            rowtype: '#posP'
        }, {
            name: 'idcard_vld',
            index: 'idcard_vld',
            rowtype: '#crteTerm'
        }, {
            name: 'proxy_vld',
            index: 'proxy_vld',
            rowtype: '#entrustTerm'
        }, {
            name: 'authtype',
            index: 'authtype',
            rowtype: '#grantType'
        }, {
            name: 'authrange',
            index: 'authrange',
            rowtype: '#commodity'
        }, {
            name: 'enclosure',
            index: 'enclosure'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid_page2'
    });
    //批准文件
    $('#table3').XGrid({
        data: [{
            id: '1',
            certificate_type: {},
            certificate_num: '',
            operate_range: {},
            Issuing_organ: '',
            Issuing_date: {},
            vld: {},
            enclosure: ''
        }],
        colNames: ['', '证书类型', '证书编号', '经营范围', '发证机关', '发证日期', '有效期至', '附件'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'certificate_type',
            index: 'certificate_type',
            rowtype: '#certType'
        }, {
            name: 'certificate_num',
            index: 'certificate_num'
        }, {
            name: 'operate_range',
            index: 'operate_range',
            rowtype: '#operScopeZtree'
        }, {
            name: 'Issuing_organ',
            index: 'Issuing_organ'
        }, {
            name: 'Issuing_date',
            index: 'Issuing_date',
            rowtype: '#fCrte'
        }, {
            name: 'vld',
            index: 'vld',
            rowtype: '#docTerm'
        }, {
            name: 'enclosure',
            index: 'enclosure'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid_page3'
    });
    //年度报告
    $('#table4').XGrid({
        data: [{
            id: '1',
            Report_year: {},
            abnormal: {},
            sanction: {},
            enclosure: ''
        }],
        colNames: ['', '报告年份', '是否经营异常', '是否有行政处罚', '附件'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'Report_year',
            index: 'Report_year',
            rowtype: '#reportYear'
        }, {
            name: 'abnormal',
            index: 'abnormal',
            rowtype: '#isAbnormal'
        }, {
            name: 'sanction',
            index: 'sanction',
            rowtype: '#isPunish'
        }, {
            name: 'enclosure',
            index: 'enclosure'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid_page4'
    });


    //模态框弹出内容
    let $Product_Grid = $('#Product_list');
    let $Product_Grid1 = $('#Selecte_list');
    $('.commodity').on('click', function (ev) {
        var el = document.querySelector('#myModal');
        utils.dialog({
            width: 1103,
            title: '选择商品',
            content: el,
            okValue: '确定',
            ok: function () {
                $Product_Grid.XGrid('commodity', $(this.node).find('form').serializeToJSON());
            },
            cancelValue: '取消',
            cancel: function () { }
        }).showModal();
        ev.stopPropagation();
    });
    /**************模态框的: 商品列表***************/
    $('#Product_list').XGrid({
        data: [{
            id: '',
            commodity_code: '',
            commodity_name: '',
            common_name: '',
            spec: '',
            manufacturer: '',
            Approval_num: '',
            packing_nit: '',
            Dosage_form: '',
            bar_code: ''
        }],
        colNames: ['', '商品编码', '商品名', '通用名', '规格', '生产厂家', '批准文号', '包装单位', '剂型', '小包装条码'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'commodity_code',
            index: 'commodity_code'
        }, {
            name: 'commodity_name',
            index: 'commodity_name'
        }, {
            name: 'common_name',
            index: 'common_name'
        }, {
            name: 'spec',
            index: 'spec'
        }, {
            name: 'manufacturer',
            index: 'manufacturer'
        }, {
            name: 'Approval_num',
            index: 'Approval_num'
        }, {
            name: 'packing_nit',
            index: 'packing_nit'
        }, {
            name: 'Dosage_form',
            index: 'Dosage_form'
        }, {
            name: 'bar_code',
            index: 'bar_code'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#product_list_page'
    });
    /**************模态框的: 已选商品列表***************/
    $('#Selecte_list').XGrid({
        data: [{
            id: '',
            commodity_code: '',
            commodity_name: '',
            common_name: '',
            spec: '',
            manufacturer: '',
            Approval_num: '',
            packing_nit: '',
            Dosage_form: '',
            bar_code: ''
        }],
        colNames: ['', '商品编码', '商品名', '通用名', '规格', '生产厂家', '批准文号', '包装单位', '剂型', ''],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        }, {
            name: 'commodity_code',
            index: 'commodity_code'
        }, {
            name: 'commodity_name',
            index: 'commodity_name'
        }, {
            name: 'common_name',
            index: 'common_name'
        }, {
            name: 'spec',
            index: 'spec'
        }, {
            name: 'manufacturer',
            index: 'manufacturer'
        }, {
            name: 'Approval_num',
            index: 'Approval_num'
        }, {
            name: 'packing_nit',
            index: 'packing_nit'
        }, {
            name: 'Dosage_form',
            index: 'Dosage_form'
        }, {
            name: 'bar_code',
            index: 'bar_code'
        }],
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#selecte_list_page'
    });
    //经营范围树
    var zTreeObj;
    var zTreeNodes = [{
        id: 0,
        name: "全国",
        // open: true,//展开
        children: [{
            id: 1,
            name: "湖北",
            children: [{
                id: 11,
                name: "武汉"
            },
            {
                id: 12,
                name: "宜昌"
            },
            {
                id: 13,
                name: "荆州"
            },
            {
                id: 14,
                name: "孝感"
            }
            ]
        },
        {
            id: 2,
            name: "湖南",
            // checked:true  //默认选中
        },
        {
            id: 3,
            name: "河南"
        }
        ]
    }];
    var setting = {
        callback: {
            onClick: zTreeOnClick,
        },
        check: {
            enable: true, //显示勾选框  默认不显示
        },
    };
    zTreeObj = $.fn.zTree.init($(".operScopeZtree"), setting, zTreeNodes);
    //选中事件
    function zTreeOnClick(event, treeId, treeNode) {
        console.log(event, treeId, treeNode);
        event.cancelBubble=true;
        grid_data = [];
        grid_data = [{
            id: "1",
            type: "2",
            pay: "3",
            name: treeNode.name,
            text: "5"
        }]
    };
    //初始化地址选择
    $('[data-toggle="distpicker"]').distpicker();
    //添加仓库
    $(".addDepot").on("click", function () {
        var html = '<div class="col-md-6 depotList">\
                    <div class="input-group">\
                        <div class="input-group-addon">仓库地址</div>\
                        <div data-toggle="distpicker" class="form-control form-inline distpicker">\
                            <div class="row">\
                                <div class="form-group col-md-2">\
                                    <select class="form-control" name="repertoryProvince"></select>\
                                </div>\
                                <div class="form-group col-md-2">\
                                    <select class="form-control" name="repertoryCity"></select>\
                                </div>\
                                <div class="form-group col-md-2">\
                                    <select class="form-control" name="repertoryArea"></select>\
                                </div>\
                                <div class="form-group col-md-4">\
                                    <input type="text" class="form-control text-inp" name="repertoryDetail"/>\
                                </div>\
                                <div class="form-group btn-box col-md-1">\
                                    <button type="button" class="btn removeDepot">\
                                        <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>\
                                    </button>\
                                </div>\
                            </div>\
                        </div>\
                    </div>\
                </div>';
        $("#depotAddress").append(html);
        $('[data-toggle="distpicker"]').distpicker();
    });
    //删除仓库地址
    $("#depotAddress").on("click", ".removeDepot", function () {
        $(this).parents(".depotList").remove();
    });
    //批量上传
    $("#appBatch").on("click", function () {
        var selRow = $('#table3').XGrid('getSeleRow');
        if (true) {
            $(this).upLoad({
                tableId: '#table3',
                urlBack: function (urlList) {
                    var picture = {};//存放图片数据

                    var type = '';//图片type值
                    var html = '';
                    for(var i=0;i<urlList.length;i++)
                    {
                        type=urlList[i].type;
                        if(picture[type])
                        {
                            //对已存在type类型图片进行去重合并
                            picture[type]=Array.from(new Set(picture[type].concat(urlList[i].url)));
                        }else{
                            picture[type]=[urlList[i].url];
                        }
                    }
                    console.log(picture)
                    for(var name in picture)
                    {
                        var inp=$('input[data-type="'+name+'"]');
                        if(inp.length > 0)
                        {
                            inp.val(JSON.stringify(picture[name]));
                        }else{
                            var str='<input type="hidden" data-type="'+name+'" value=\''+JSON.stringify(picture[name])+'\' />';
                            $("body").append(str);
                        }
                    }
                }
            });
            //出发插件内点击事件，弹出窗口
            $(this).click();
        }
    });
    //附件预览
    $("td[row-describedby='enclosure']").on("click",function(){
        $.viewImg({
            type:'GXZY'
        })
    });
    $("#saveRowData").on("click",function(){
        var arr=[];
        //组合仓库数据
        $(".depotList").each(function(index){
            var se = $(this).find("select");
            var inp = $(this).find(".text-inp");
            var repertoryProvince = se.eq(0).find("option:selected").val();
            var repertoryCity = se.eq(1).find("option:selected").val();
            var repertoryArea = se.eq(2).find("option:selected").val();
            var repertoryDetail = inp.val();
            arr[index]={
                repertoryProvince:repertoryProvince,
                repertoryCity:repertoryCity,
                repertoryArea:repertoryArea,
                repertoryDetail:repertoryDetail
            }
        });
        //运营属性
        var supplierOrganBaseDTO=$("#supplierOrganBaseDTO").serializeToJSON();
        //基础属性
        var supplierBaseDTO=$("#supplierBaseDTO").serializeToJSON();
        var j3=$("#yyForm").serializeToJSON();
        Object.assign(supplierOrganBaseDTO, j3);//基础属性与运营属性合并
        //删除基础属性内的仓库数据，仓库数据单独获取
        delete supplierBaseDTO.repertoryProvince;
        delete supplierBaseDTO.repertoryCity;
        delete supplierBaseDTO.repertoryArea;
        delete supplierBaseDTO.repertoryDetail;

        delete supplierOrganBaseDTO.parentCode;
        delete supplierOrganBaseDTO.code;
        delete supplierOrganBaseDTO.value;

        //运营属性 付款、结算部分
        var supplierExtendItemVoList=[];
        //parentCode/childCode/{parentCode:1,code:xxx,value:1}
        $(".paymentSettlement").each(function(){
            var json={};
            var parentInp=$(this).find(".parentCode");
            var parentCode=parentInp.attr('name');
            var parentCodeVal=parentInp.val();
            json[parentCode]=parentCodeVal;
            $(this).find(".childCode").each(function(index){
                var cCode=$(this).find(".cCode");
                var cCodeName=cCode.attr('name');
                var cCodeValue=cCode.val();
                var cValue=$(this).find(".cValue");
                var cValueName=cValue.attr('name');
                var cValueValue=cValue.val();
                json[cCodeName]=cCodeValue;
                json[cValueName]=cValueValue;
            });
            supplierExtendItemVoList.push(json);
        });
        supplierOrganBaseDTO.supplierExtendItemVoList = supplierExtendItemVoList;
        supplierBaseDTO.supplierRepertoryAddressDTOList=arr;
        //表格数据

        var table1=$("#table1").getRowData();
        var table2=$("#table2").getRowData();
        var table3=$("#table3").getRowData();
        var table4=$("#table4").getRowData();
        for(var i=0;i<table2.length;i++){
            table2[i].supplierClientProxyBusinessScopeList=[
                {businessScopeCode:'经营范围'}
            ];
            table2[i].supplierClientProxyProductList=[
                {productCode :'商品编码'}
            ];
        }
        var json={
            supplierOrganBaseDTO:supplierOrganBaseDTO,
            supplierBaseDTO:supplierBaseDTO,
            supplierQualityAgreementList:table1,
            supplierClientProxyOrderList:table2,
            supplierApprovalFileList:table3,
            supplierYearReportList:table4,
            supplierOtherFileList:[]
        }
        console.log(json);
    });
})
/**/
