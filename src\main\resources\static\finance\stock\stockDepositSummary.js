
//获取各种统计总数
function getStatisticsSums() {
    var formData = $('#myform').serializeToJSON();
    //加载总数量
    $.ajax({
        url: '/proxy-finance/finance/initBalance/getStatisticsSums',
        dataType: 'json',
        timeout: 8000,
        data:formData,
        success: function (data) {
            if (data.code==0){
                var numResult = data.result;
                if(null != numResult){
                    $("#initMoneySum").text(parseFloat(numResult.initMoneySum).formatMoney('2', '', ',', '.'));
                    $("#sumInSum").text(parseFloat(numResult.sumInSum).formatMoney('2', '', ',', '.'));
                    $("#panYingMoneySum").text(parseFloat(numResult.panYingMoneySum).formatMoney('2', '', ',', '.'));
                    $("#panKuiMoneySum").text(parseFloat(numResult.panKuiMoneySum).formatMoney('2', '', ',', '.'));
                    $("#sumOutSum").text(parseFloat(numResult.sumOutSum).formatMoney('2', '', ',', '.'));
                    $("#jiecunMoneySum").text(parseFloat(numResult.jiecunMoneySum).formatMoney('2', '', ',', '.'));
                }else {
                    $("#initMoneySum").text("0.00");
                    $("#sumInSum").text("0.00");
                    $("#panYingMoneySum").text("0.00");
                    $("#panKuiMoneySum").text("0.00");
                    $("#sumOutSum").text("0.00");
                    $("#jiecunMoneySum").text("0.00");
                }
            }
        },
        error: function () {
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}
$(function () {
    /* 合计计算 */
    var totalTable = z_utils.totalTable;
    var totalTablea = z_utils.totalTablea;
    //初始化商品大类
    initProductCategoriesSelect();
    //仓库
    initStorageTypeSelect();

    //设置table高度
    utils.setTableHeight('X_Table');

    getStatisticsSums();
    var param = $('#myform').serializeToJSON();
    $('#X_Table').XGrid({
            url: '/proxy-finance/finance/initBalance/findStockDepositSummary',
             postData: param,
            colNames: [ '开始月份','结束月份', '商品大类编码', '商品大类描述', '库房', '期初数量', '期初金额',
                '入库数量', '入库金额', '盘盈数量', '盘盈金额', '盘亏数量', '盘亏金额', '出库数量', '出库金额', '结存数量', '结存金额'
            ],
            colModel: [
                {
                    name: 'startTime',
                }, {
                    name: 'endTime',
                },
                {
                name: 'drugClass',
            }, {
                name: 'drugClassName',
            }, {
                name: 'storageName',
            }, {
                name: 'initNumber',
            }, {
                name: 'initMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'amountIn',
            }, {
                name: 'sumIn',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'panYing',
            }, {
                name: 'panYingMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'panKui',
            }, {
                name: 'panKuiMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'amountOut',
            }, {
                name: 'sumOut',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'jiecunNumber',
            }, {
                name: 'jiecunMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }],
        rownumbers: true,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
        gridComplete: function () {
            if (!$(this).XGrid('getRowData').length) {
                utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
            }
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['initNumber','initMoney','amountIn','sumIn','panYing','panYingMoney','panKui','panKuiMoney','amountOut','sumOut','jiecunNumber','jiecunMoney'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                if(item=="initNumber"||item=="amountIn"||item=="panYing"||item=="panKui"||item=="amountOut"||item=="jiecunNumber"){
                    lastRowEle.find("td[row-describedby="+item+"]").text(totalTablea(data,item));
                }else {
                    lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
                }
            });
        }
    });

    $("#searchBtn").on("click", function () {
        var param = $('#myform').serializeToJSON();
        console.log(param)
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/initBalance/findStockDepositSummary',
            postData: param
        }).trigger('reloadGrid');
        getStatisticsSums();
    });

    // 筛选列，集成到 xgrid.js 里了
    $("#set_tb_rows").click(function () {
        $('#X_Table').XGrid('filterTableHead');
    })


    // 导出
    $('#exportBtn').bind('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            var parames = [];
            parames.push({ name: "startMonth", value:  $("#startMonth").val()});
            parames.push({ name: "endMonth", value: $("#endMonth").val()});
            parames.push({ name: "drugClass", value: $("#productType").val()});
            parames.push({ name: "storageType", value: $("#storageType").val()});

            var param = $('#myform').serializeToJSON();
            // 导出
            Post("/proxy-finance/finance/initBalance/exportExcelStockDepositSummary", parames);

            // var storeNome=$("#storeNome").val();
            //  location.href="/proxy-finance/finance/initBalance/exportExcelStockDepositSummary";
        })
    });

    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
      //  $(temp_form).remove();
    }

})

/*
function yearFun() {
    var year = getYear();
    WdatePicker({
        dateFmt:'yyyy',
        minDate:'2018',
        maxDate: year
    })
}
function startMonthFun() {
    WdatePicker({
        dateFmt:'MM',
        maxDate:'#F{$dp.$D(\'endMonth\')}'
    })
}
function endMonthFun() {
    WdatePicker({
        dateFmt:'MM',
        minDate:'#F{$dp.$D(\'startMonth\')}',
        maxDate: '%y-%M'
    })
}
*/

//  开始年月
function startTime() {
    WdatePicker({
        dateFmt: 'yyyy-MM',
        maxDate:'#F{$dp.$D(\'endMonth\')}'
    })
}

//  结束年月
function endTime() {
    WdatePicker({
        dateFmt: 'yyyy-MM',
        minDate: '#F{$dp.$D(\'startMonth\')}',
    })
}

/**
 * 渲染仓库下拉列表
 */
function initStorageTypeSelectTemplate(data) {
    var html = template('storageType-tmp', {list: data});
    document.getElementById('storageType-div').innerHTML = html;
}

/**
 * 获取仓库
 */
function initStorageTypeSelect() {
    $.post("/proxy-sysmanage/sysmanage/dict/querycommonnotpage", {type: 10},
        function(data){
            console.log(data.result);
            if (data.code == 0) {
                var _data = data.result;
                initStorageTypeSelectTemplate(_data);
            }
        }, "json");
}



/**
 * 渲染商品大类下拉列表
 */
function initProductCategoriesSelectTemplate(data) {
    var html = template('productType-tmp', {list: data});
    document.getElementById('productType-div').innerHTML = html;
}

/**
 * 获取商品大类
 */
function initProductCategoriesSelect() {
    $.post("/proxy-sysmanage/sysmanage/dict/querycommonnotpage", { type: 7 },
        function(data){
            console.log(data.result);
            if (data.code == 0) {
                var _data = data.result;
                initProductCategoriesSelectTemplate(_data);
            }
        }, "json");
}


/**
 * 获取 2018 到 当前你那份数组
 * @returns {Array}
 */
/*function yearList() {
    var yearArr = [];
    for (var i = 2018; i < getYear(); i++){
        yearArr.push(i);
    }
    return yearArr;
}*/

/**
 * 获取当前年份
 */
function getYear() {
    var date = new Date();
    var year = date.getFullYear() + 10;
    return year;
}
