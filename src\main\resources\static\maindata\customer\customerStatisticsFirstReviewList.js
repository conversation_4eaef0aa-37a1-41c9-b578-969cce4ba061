$(function () {
  $('div[fold=head]').fold({
    sub: 'sub'
  });

  function percentFormatter(value) {
    if (value) {
      // 临时规避浮点数陷阱 https://github.com/camsong/blog/issues/9
      return (value * 100).toFixed(2) + "%";
    } else {
      return '0%'
    }
  }

  /** 秒转时分秒汉字 */
  function secondToDate(s) {
    let days = Math.floor(s / (24 * 3600)); //计算出小时数
    let leave1 = s % (24 * 3600); //计算天数后剩余的毫秒数
    let hours = Math.floor(leave1 / (3600)); //计算相差分钟数
    let leave2 = leave1 % (3600); //计算小时数后剩余的毫秒数
    let minutes = Math.floor(leave2 / (60)); //计算相差秒数
    let leave3 = leave2 % (60); //计算分钟数后剩余的毫秒数
    let seconds = Math.round(leave3);
    return `<span ${(days * 24 + hours) > 24 ? 'style="color: red;"' : ''}>${(days * 24 + hours) > 0 ? days * 24 + hours + "小时" : ""}${minutes > 0 ? minutes + "分" : ""}${seconds + "秒"}</span>`;
  }

  /**
   * @param cellValue
   * @param options
   * @param rowData 当前行的数据（由 setGridParam 函数的 data 参数注入）
   *
   * 更多 API 细节：http://www.trirand.com/jqgridwiki/doku.php?id=wiki:custom_formatter
   */
  function passedNumAndRatioFormatter(cellValue, options, rowObject) {
    return renderFieldHTML(rowObject.passedNum, percentFormatter(rowObject.passedRatio))
  }

  function nonPassedNumAndRatioFormatter(cellValue, options, rowObject) {
    return renderFieldHTML(rowObject.nonPassedNum, percentFormatter(rowObject.nonPassedRatio))
  }

  function firstPassedNumAndRatioFormatter(cellValue, options, rowObject) {
    return renderFieldHTML(percentFormatter(rowObject.firstPassedRatio), secondToDate(rowObject.firstAvgCostTime))
  }

  function secondPassedNumAndRatioFormatter(cellValue, options, rowObject) {
    return renderFieldHTML(percentFormatter(rowObject.secondPassedRatio), secondToDate(rowObject.secondAvgCostTime))
  }

  function thirdPassedNumAndRatioFormatter(cellValue, options, rowObject) {
    return renderFieldHTML(percentFormatter(rowObject.thirdPassedRatio), secondToDate(rowObject.thirdAvgCostTime))
  }

  function fourthPassedNumAndRatioFormatter(cellValue, options, rowObject) {
    return renderFieldHTML(percentFormatter(rowObject.fourthPassedRatio), secondToDate(rowObject.fourthAvgCostTime))
  }

  function renderFieldHTML(firstValue, secondValue) {
    const firstSpan = "<span style='font-size: large'>" + firstValue + "</span><br/>"
    const secondSpan = "<span style='font-size: small'>" + secondValue + "</span>"
    return firstSpan + secondSpan
  }


  const colMaps = [
    {colName: '', name: 'id', hidden: true},
    {colName: '机构', name: 'orgName', index: 'orgName', width: 240},
    {colName: '统计区间', name: 'statPeriod', index: 'statPeriod', width: 180},
    {colName: '首营客户数', name: 'customerNum', index: 'customerNum'},
    {colName: '首营客户资质数', name: 'credentialNum', index: 'credentialNum'},
    {colName: '首营完成数/首营完成率', name: 'passedNumAndRatio', index: 'passedNumAndRatio', formatter: passedNumAndRatioFormatter, width: 200},
    {colName: '一审通过率/一审平均时长', name: 'firstPassedRatioAndAvgTime', index: 'firstPassedRatioAndAvgTime', formatter: firstPassedNumAndRatioFormatter, width: 200},
    {colName: '二审通过率/二️审平均时长', name: 'secondPassedRatioAndAvgTime', index: 'secondPassedRatioAndAvgTime', formatter: secondPassedNumAndRatioFormatter, width: 200},
    {colName: '三审通过率/三审平均时长', name: 'thirdPassedRatioAndAvgTime', index: 'thirdPassedRatioAndAvgTime', formatter: thirdPassedNumAndRatioFormatter, width: 200},
    {colName: '四审通过率/四审平均时长', name: 'fourthPassedRatioAndAvgTime', index: 'fourthPassedRatioAndAvgTime', formatter: fourthPassedNumAndRatioFormatter, width: 200},
    {colName: '首营未完成数/首营未完成率', name: 'nonPassedNumAndRatio', index: 'nonPassedNumAndRatio', formatter: nonPassedNumAndRatioFormatter, width: 200},
  ]

  // 创建表格
  $('#baseTable').XGrid({
    colNames: colMaps.map(item => {
      return item.colName
    }),
    colModel: colMaps.map(item => {
      delete item.colName
      return item
    }),
    // 设置为交替行表格,默认为false
    altRows: true,
    // 设置每页展示行数，-1 表示全部展示
    rowNum: -1,
    // 是否展示序号
    rownumbers: true
  });


  function fetchData() {
    $.ajax({
        type: 'post',
        data: $("#searchForm").serializeToJSON(),
        url: '/proxy-customer/customer/statistic/firstReviewList',
        success: (res) => {
          const total = res.result.recordTotal
          if (total) {
            total.orgName = '合计'
            res.result.recordList.push(total)
          }
          $('#baseTable').XGrid('setGridParam', {
            data: res.result.recordList
          }).trigger('reloadGrid')
        }
      }
    )
  }

  /**
   * 1. 设置查询按钮点击事件
   * 2. 立即触发查询按钮的点击事件
   */
  $("#searchBtn").on("click", fetchData).trigger('click');


  /**
   * 设置导出按钮点击事件
   */
  $("#exportBtn").on("click", function () {
    const data = $('#baseTable').XGrid('getGridParam').data
    //无数据提示
    if (data.length==0){
      utils.dialog({"content":"无数据可导出","timeout":2000}).show();
      return
    }
    utils.dialog({
      title: '提示',
      content: "数据导出需要一定时间，请耐心等待。",
      okValue: '开始导出',
      cancelValue: '取消',
      ok: function () {
        // 利用 form 表单完成导出
        const form = $('#searchForm');
        // 导出接口
        $(form).attr("action", "/proxy-customer/customer/statistic/firstReviewList/export");
        $(form).submit();
      },
      cancel: function () {
      },
    }).showModal();
  });
})
