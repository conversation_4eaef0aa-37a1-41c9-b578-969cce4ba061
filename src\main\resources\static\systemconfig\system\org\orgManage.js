
	$('#busiScopeList').selectpicker({
	    actionsBox: true,
	    selectAllText: '选择全部',
	    deselectAllText: '取消全选'
	})
    //var loginOrgCode = "${orgCode!''}";
    var loginOrgCode = "001";
    var zTreeObj;
    var tree_id = 1;
    var zTreeNodes;
    var setting = {
        check: {
            enable: false //显示勾选框  默认不显示
        },
        callback: {
            onClick: zTreeOnClick, //选中事件
            // beforeRemove: beforeRemove,//删除事件
            // beforeRename: beforeRename,//修改事件
        },
        view: {
            // addDiyDom: addDiyDom  //设置按钮
            expandSpeed: "",
            addHoverDom: addHoverDom, //新增
            removeHoverDom: removeHoverDom,
            selectedMulti: false,
            showIcon: false//8-16
        },
        data: {
            simpleData: {
                enable: true
            }
        },
        /*key: {
            title: "code"
        },*/
        edit: {
            //enable: true, //是否可编辑
            enable: false, //是否可编辑
            drag: {
                isCopy: false, //拖拽复制
                isMove: false, //拖拽移动
            },
            showRenameBtn: false, //编辑按钮
            showRemoveBtn: false, //删除按钮
            removeTitle: "删除改节点", //删除按钮上的提示信息
        }
    };
   
    function removeHoverDom(treeId, treeNode) {
        if(treeNode.level==0){
            $("#diyBtn_manager_" + treeNode.id).unbind().remove();
            $("#diyBtn_postmanager_" + treeNode.id).unbind().remove();
        }else{
            $("#diyBtn_edit_" + treeNode.id).unbind().remove();
            $("#diyBtn_dele_" + treeNode.id).unbind().remove();
            $("#diyBtn_manager_" + treeNode.id).unbind().remove();
            $("#diyBtn_postmanager_" + treeNode.id).unbind().remove();
        }

    };
    
    function addHoverDom(treeId, treeNode) {
        var code=treeNode.code;
        console.log("----addHoverDom---code:"+code);
        if(treeNode.level==0){
            //显示添加下级
            /*
            var aObj = $("#" + treeNode.tId + "_a");
            if ($("#diyBtn_add_" + treeNode.id).length > 0) return;
    
            var editStr = "<a href='javascript:;' id='diyBtn_add_" + treeNode.id + "' > 编辑</a>" +
            aObj.append(editStr);
            var btn1 = $("#diyBtn_add_" + treeNode.id);
    
            if (btn1) {
                btn1.bind("click", function () {
                    addEvent(treeNode);
                });
            }
            */

            var aObj = $("#" + treeNode.tId + "_a");
            if ($("#diyBtn_manager_" + treeNode.id).length > 0) return;
            if ($("#diyBtn_postmanager_" + treeNode.id).length > 0) return;
    
//            var editStr = "<a id='diyBtn_manager_" + treeNode.id + "' href='deptManage?orgCode="+code+"'> 部门管理</a>"
//                +"<a id='diyBtn_postmanager_" + treeNode.id + "' href='postManage?orgCode="+code+"' > 职位管理</a>";
//            aObj.append(editStr);
            
        }else{
            var aObj = $("#" + treeNode.tId + "_a");
            if ($("#diyBtn_edit" + treeNode.id).length > 0) return;
            if ($("#diyBtn_dele_" + treeNode.id).length > 0) return;
            if ($("#diyBtn_manager_" + treeNode.id).length > 0) return;
            if ($("#diyBtn_postmanager_" + treeNode.id).length > 0) return;
            
            var editStr = "<a href='javascript:;' id='diyBtn_edit_" + treeNode.id + "' > 编辑</a>" +
            "<a id='diyBtn_dele_" + treeNode.id + "' > 删除</a>";
    
//            var editStr = "<a href='javascript:;' id='diyBtn_edit_" + treeNode.id + "' > 编辑</a>" +
//                "<a id='diyBtn_dele_" + treeNode.id + "' > 删除</a>" +
//                "<a id='diyBtn_manager_" + treeNode.id + "' href='deptManage?orgCode="+code+"'> 部门管理</a>"
//                +"<a id='diyBtn_postmanager_" + treeNode.id + "' href='postManage?orgCode="+code+"' > 职位管理</a>";
            aObj.append(editStr);
            
            var btn1 = $("#diyBtn_edit_" + treeNode.id),
                btn2 = $("#diyBtn_dele_" + treeNode.id);
    
            if (btn1) {
                btn1.bind("click", function (event) {
                    event.stopPropagation();
                    editEvent(treeNode);
                });
            }
            if (btn2) {
                btn2.bind("click", function (event) {
                    event.stopPropagation();
                    delEvent(treeNode);
                });
            }
        }
        

    }

    //-----------------------修改
    //编辑的回调函数
    function editEvent(treeNode) {
        var id = treeNode.id;
        var name = treeNode.name;
        console.log("-----editEvent--id="+id);
        zTreeObj.selectNode(treeNode);

        $("#divEdit").show();
        $("#divInfo").hide();

        $.ajax({  
            type:"get",  
            url:'/proxy-sysmanage/sysmanage/org/queryOrgById',
            dataType: "json", 
            data:{orgId:id},
            success: function(res) {  
                if(res&&res.code==0){
                	var businessScope = res.result.businessScope;
                    var resultStr=JSON.stringify(res);
                    console.log("queryOrgById-->result");
                    console.log(resultStr);
                    
                    $("#orgId").val(res.result.id);
                    $("#parentId").val(res.result.pid);
                    $("#orgCode").val(res.result.orgCode);
                    $("#orgName").val(res.result.orgName);//
                    $("#orgTypeList").val(res.result.orgType);
                    $("#orgAddress").val(res.result.address);
                    $("#orgStatusList").val(res.result.enable);
                    //$("#adminList").val(res.result.operator);
                    $("#txtUserId").val(res.result.operator);
                    $("#txtUserName").val(res.result.userName);
                    
                    if(businessScope&&businessScope!=""){
                    	$('#busiScopeList').val(businessScope.split(","));
                    	$('#busiScopeList').selectpicker('refresh');
                    }else{
                    	$('#busiScopeList').selectpicker({
                    	    actionsBox: true,
                    	    selectAllText: '选择全部',
                    	    deselectAllText: '取消全选'
                    	})
                    }
                    if(res.result.provinceCode&&res.result.provinceCode!=""){
                        $("#provinceList").val(res.result.provinceCode);
                    }else{
                        $("#provinceList").val("-1");
                    }
                    
                    //设置机构编码只读
                    //$("#orgCode").attr("readonly","readonly");
                }else{
                    showTips("查询机构信息失败！");
                }
            }
        });
        
    }
    //(删除的提示框)回调函数
    function delEvent(treeNode) {
        //根节点不能删除判断
        if(treeNode.level==0){
            showDialog("提示","不能删除根节点");
            return;
        }
        var id = treeNode.id;
        var code=treeNode.code;
        console.log("---delEvent--id="+id+",code="+code);
        zTreeObj.selectNode(treeNode);
        //$("#divEdit").hide();
        //$("#divInfo").hide();

        utils.dialog({
            content: '确定删除此机构？',
            quickClose: false,
            okValue: '确定',
            cancelValue: '取消',
            ok: function () {
                //---------------add start
                $.ajax({  
                    type:"get",
                    url:'/proxy-sysmanage/sysmanage/org/deleteOrgById',
                    dataType: "json", 
                    data:{orgId:id,orgCode:code},
                    success: function(res) {  
                        var resultStr=JSON.stringify(res);
                        console.log("deleteOrgById-->res:"+resultStr);
                        if(res){
                            if(res.code==0){
                                var zTree = $.fn.zTree.getZTreeObj("tree");
                                zTree.removeNode(treeNode, true);
                                /*
                                //判断删除节点是否正在编辑，如果是则清空表单
                                var orgId=parseInt($("#orgId").val());
                                console.log("---delEvent--正在编辑orgId="+orgId);
                                if(id==orgId){
                                    console.log("删除节点是否正在编辑,开始清空表单....");
                                    resetForm();
                                }
                                */
                                showTips("删除成功！");
                                //删除后隐藏编辑和详情
                                $("#divEdit").hide();
                                $("#divInfo").hide();
                            }else if(res.code==100){
                                //showDialog("提示","该机构下存在部门，不能删除！");
                                showTips(res.msg);
                            }else{
                                showTips(res.msg);
                            }
                        }else{
                            showTips("网络错误！");
                        }
                    }
                });
                //---------------add end
            },
            cancel: true
        }).showModal();
    }
    
    //选中事件
    function zTreeOnClick(event, treeId, treeNode) {
        console.log("-----zTreeOnClick--treeId="+treeId);//treeId=tree
        console.log(treeNode);
        //$("#parentId").val(treeNode.id);

        showInfo(treeNode);

    };
    
    function showDialog(titleText,contentText){
        //提示框
        utils.dialog({
            width: 180,
            height: 30,
            title: titleText,
            content: contentText,
            quickClose: false,
            okValue: '确定',
            ok: function () {},
        }).showModal();
    }
    
    function showTips(contentText){
        utils.dialog({
            content: contentText,
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
    
    
    function setAutocomplete(url,param,resParam) {
        var resParam=Object.assign({'list':'result'},resParam);
        $("#txtUserName").Autocomplete({
            serviceUrl: url, //异步请求
            paramName: param.paramName,//查询参数，默认 query
            params:param.params || {},
            dataType: 'json',
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            dataReader:resParam,
            triggerSelectOnValidInput: false, // 必选
            showNoSuggestionNotice: true, //显示查无结果的container
            noSuggestionNotice: '查询无结果',//查无结果的提示语
            onSelect: function (result) {
                //console.log("onSelect--->"+JSON.stringify(result));
                $("#txtUserName").val(result.value);
                $("#txtUserId").val(result.data);
                //select && select(result)
            },
            onNoneSelect: function (params, suggestions) {
                //console.log(params, suggestions);
                $("#txtUserName").val("");
                $("#txtUserId").val("");
                //noneSelect && noneSelect();
            }
        });
    }

    $(document).ready(function () {
        //初始化树
        initTree(false);
        
        //初始化省份列表
        $.ajax({
             type: "post",
             url: "/proxy-sysmanage/sysmanage/area/getProvince",
             //data: {},
             dataType: "JSON",
             success: function (res) {
                 if(res&&res.code==0){
                     var str="";
                     $.each(res.result, function (i, item) {
                         str+="<option value='"+item.code+"'>"+item.name+"</option>";
                     });
                     $("#provinceList").append(str);
                 }else{
                    alert("获取省份数据失败！");
                 }
                
             },
             error: function () { showTips("初始化省份下拉列表失败."); }
         });
        
        //文本框联想控件
        setAutocomplete("/proxy-sysmanage/sysmanage/system/queryUserListByName",
        {paramName:'userName', params:{"orgCode":loginOrgCode}},{data:"id",value:"userNameAccount"});
        

        //新建
        $('#newSet').click(function () {
        	$('#busiScopeList').selectpicker('refresh');
            //自动生成机构编码
            //setOrgCode();
        
            /*
            var zTree = $.fn.zTree.getZTreeObj("tree");
            var nodes = zTree.getSelectedNodes();
            if (nodes.length == 0) {
                showDialog("提示","请先选择一个节点");
                return false;
            }
            var treeNode = nodes[0];
            console.log("newSet click-->select treeNode.id="+ treeNode.id);
            */

            $("#divEdit").show();
            $("#divInfo").hide();
            var node = zTreeObj.getNodesByFilter(function (node) { return node.level == 0 }, true); 
            //console.log(node);
            zTreeObj.selectNode(node);

            resetForm();
            
            //移除机构编码只读
            //$("#orgCode").removeAttr("readonly");
            setOrgCode();
            
        });
        
        
    });
    
    //自动生成机构编码
    function setOrgCode(){
        $.ajax({  
                type:"get",  
                url:'/proxy-sysmanage/sysmanage/org/queryLatestOrgCode',
                dataType: "json", 
                //data:{orgCode:txtOrgCode},
                success: function(res) {  
                    var resultStr=JSON.stringify(res);
                    console.log("queryLatestOrgCode-->"+resultStr);
                    if(res&&res.code==0){
                        $("#orgCode").val(res.result);
                    }
                }
        });
    }
    
    function initTree(flag){
         $.ajax({  
            type:"post",  
            url:'/proxy-sysmanage/sysmanage/org/queryOrgList',
            dataType: "json", 
            //data:{},
            success: function(res) {  
                var resultStr=JSON.stringify(res);
                console.log("initTree-->res:"+resultStr);
                if(res&&res.code==0){
                    zTreeNodes=res.result;
                    zTreeObj = $.fn.zTree.init($("#tree"), setting, zTreeNodes);
                    //返回一个根节点 
                    var node = zTreeObj.getNodesByFilter(function (node) { return node.level == 0 }, true); 
                    console.log(node);
                    console.log("initTree-->parentId="+node.id);
                    $("#parentId").val(node.id);
                    if(flag){
                       zTreeObj.selectNode(node);
                    }
                    //zTree根节点默认打开
                    //treeObj.expandAll(true);
                }else{
                    showTips("加载机构树失败");
                }
            }
        });
    }
    
    function resetForm(){
        $("#orgId").val("0");
        $("#orgCode").val("");
        $("#orgName").val("");//
        $("#orgTypeList").val("-1");
        $("#orgAddress").val("");
        $("#orgStatusList").val("-1");
        //$("#adminList").val("-1");
        $("#busiScopeList").val("-1");
        $("#provinceList").val("-1");
        
        $("#txtUserName").val("");
        $("#txtUserId").val("");
        
        //console.log("resetForm-->current parentId="+$("#parentId").val());
        
        //移除机构编码只读
        //$("#orgCode").removeAttr("readonly");
    }
    
    function checkForm(){
        var result={passed:false};
        var orgId=$("#orgId").val();
        var parentId=$("#parentId").val();
        var orgCode=$("#orgCode").val();
        var orgName=$("#orgName").val();//
        var orgType=$("#orgTypeList").children('option:selected').val();
        var orgAddress=$("#orgAddress").val();
        var orgStatus=$("#orgStatusList").children('option:selected').val();
        //var adminId=$("#adminList").children('option:selected').val();
        var busiScopeArr = $("#busiScopeList").val();
        
        var province_code=$("#provinceList").children('option:selected').val();
        var province_name=$("#provinceList").children('option:selected').text();
        
        var adminUserId=$("#txtUserId").val();
        
        if(orgCode==""){
            showTips("企业编码不能为空！");
            return result;
        }
        
        orgName=$.trim(orgName);
        if(orgName.length==0){
            showTips("企业名称不能为空！");
            return result;
        }
        
        if(orgName.length>100){
            showTips("企业名称不能超过100个字符！");
            return result;
        }
        
        if(orgType=="-1"){
            showTips("请选择企业类型！");
            return result;
        }
        
        orgAddress=$.trim(orgAddress);
        /*
        if(orgAddress==""){
            showTips("请填写企业地址！");
            return result;
        }
        */
        
        if(orgAddress.length>200){
            showTips("企业地址不能超过200个字符！");
            return result;
        }
        
        if(orgStatus=="-1"){
            showTips("请选择企业状态！");
            return result;
        }
        
        if(!province_code||province_code=="-1"){
            showTips("请选择省份！");
            return result;
        }
        
        /*
        if(adminId=="-1"){
            showTips("请选择管理员账号！");
            return result;
        }
        */
        
        if(adminUserId==""){
            showTips("请选择IT管理员！");
            return result;
        }
        
        if(!$.isArray(busiScopeArr)){
            showTips("请选择经营范围！");
            return result;
        } else if(busiScopeArr.length<1){
            showTips("请选择经营范围！");
            return result;
        }
        var busiScopeParam = "";
        $(busiScopeArr).each(function(i,item){
        	busiScopeParam += item + ",";
        });

        result.passed=true;
        result.data={
                id:orgId,pid:parentId,
                orgCode:orgCode, orgName:orgName,orgType:orgType,
                enable:orgStatus,operator:adminUserId,
                businessScope:busiScopeParam,provinceCode:province_code,
                provinceName:province_name,address:orgAddress};
        return result;
    }
    
    function save(){
        var zTree = $.fn.zTree.getZTreeObj("tree");
        var nodes = zTree.getSelectedNodes();
        if (nodes.length == 0) {
            //提示框
            showDialog('提示','请先选择一个节点');
            return;
        }
        var treeNode = nodes[0];
        console.log("save-->select treeNode.id="+ treeNode.id);
        
        var result=checkForm();
        console.log("---checkForm-->result:");
        console.log(JSON.stringify(result));
        
        if(!result.passed){
            return;
        }
        
        if(result.data.operator==""){
            result.data.operator="0";
        }
        
        var id=parseInt(result.data.id);
        var isAdd=false;
        if(id==0){
            isAdd=true;
            //新增操作重新设置pid
            result.data.pid=treeNode.id;
            console.log("新增操作:set pid="+result.data.pid);
        }
        
        //saveSuccess(result.data,zTree,treeNode,isAdd,t_id+1);
        
         $.ajax({  
            type:"post",  
            url:'/proxy-sysmanage/sysmanage/org/saveOrg',
            dataType: "json", 
            data:result.data,
            success: function(res) {
                var resultStr=JSON.stringify(res);
                console.log("saveOrg-->res:"+resultStr);
                if(res.code==0){
	                $('#busiScopeList').val([]);
	                $('#busiScopeList').selectpicker('refresh');
                    //新增操作需要返回主键id
                    var newId=parseInt(res.result);
                    saveSuccess(result.data,zTree,treeNode,isAdd,newId);
                }else if(res.code==100){
                    showDialog("提示",res.msg);
                    initTree(true);//刷新机构树
                    setOrgCode();
                }else{
                    showDialog("提示","保存失败！请重试.");
                    setOrgCode();
                }
            }
        });
        
    }
    
    function saveSuccess(formData,zTree,treeNode,isAdd,newId){
        //保存成功提示框
        showDialog('保存','保存成功');
        
        resetForm();
        
        if(!isAdd){
            //编辑操作刷新节点名称
            treeNode.name=formData.orgName;
            zTree.updateNode(treeNode);
            return;
        }
        
        //---新增树节点
        if (treeNode) {
            console.log("saveSuccess--1,pId="+treeNode.id);
            zTree.addNodes(treeNode, {id:newId, pId:treeNode.id, isParent:false, name:formData.orgName,code:formData.orgCode});
        } else {
            console.log("saveSuccess--2,pid=0");
            zTree.addNodes(null, {id:newId, pId:0, isParent:false, name:formData.orgName,code:formData.orgCode});
        }
    }

    function showInfo(treeNode){
        if(treeNode.level==0){
            $("#divEdit").hide();
            $("#divInfo").hide();
            return ;
        }
        var  id = treeNode.id;
        //var name = treeNode.name;
        console.log("-----showInfo--id="+id);
        //zTreeObj.selectNode(treeNode);

        $("#divEdit").hide();
        $("#divInfo").show();
        $.ajax({  
            type:"get",  
            url:'/proxy-sysmanage/sysmanage/org/queryOrgInfoById',
            dataType: "json", 
            data:{orgId:id},
            success: function(res) {  
                if(res&&res.code==0){
                    var resultStr=JSON.stringify(res);
                    console.log("queryOrgInfoById-->result");
                    console.log(resultStr);
                    
                    $("#labelOrgCode").text(res.result.orgCode);
                    $("#labelOrgName").text(res.result.orgName);
                    $("#labelOrgType").text(res.result.orgTypeName);
                    $("#labelOrgStatus").text(res.result.enableName);
                    if(res.result.address){
                        $("#labelAdress").text(res.result.address);
                    }else{
                        $("#labelAdress").text(" ");
                    }

                    $("#labelProvince").text(res.result.provinceName);
                    $("#labelBusinessScope").text(res.result.businessScopeName);
                    $("#labelAdmin").text(res.result.userName);
                }else{
                    showTips("查询机构信息失败！");
                }
            }
        });
    }