//打开modal框
window.console = window.console || {
        log: function() {}
}

function doUpload() {
    var formData = new FormData();
    var name = $("#upfile").val();
    formData.append("file",$("#upfile")[0].files[0]);
    formData.append("name",name);

    var loading = dialog({
        title: '加载中',
        fixed: true,
        width: 200,
        quickClose: false,
        cancel: false
    }).showModal();

    $.ajax({
        url : '/proxy-product/product/productFirst/upLoadProductFirstExcel',
        type : 'POST',
        async : false,
        data : formData,
        processData : false,
        contentType : false,
        beforeSend:function(){
            console.log("正在进行，请稍候");
        },
        success : function(data) {
            loading.close();
            console.log(data);
            if(data.code==0){
                var d=dialog({
                    title:"提示",
                    content:data.result.msg,
                    okValue: '确定提交',
                    ok: function () {
                         window.open("/proxy-product/product/productFirst/upLoadProductData?fileName="+data.result.fileName);
                    },
                    cancelValue: '下载问题明细',
                    cancel: function () {
                        window.open("/proxy-product/product/productFirst/downLoadData?fileName="+data.result.fileName);
                        d.close().remove();
                    }
                }).showModal();
            }else {
                alert('上传失败');
                return;
            }
        },error:function () {
            loading.close();
            alert('上传失败');
        }
    });
}