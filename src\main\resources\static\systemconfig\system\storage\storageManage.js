function() {

	//--------add start
	        var orgCode='${orgCode!""}';
	        var orgName='${orgName!""}';
	        var provinceCode='${provinceCode!""}';
	        //--------add end
	        var zTreeObj;
	        var zTreeObjErp;
	        var zTreeNodesErp;
	        var zTreeNodes;
	        
	        var setting = {
	            check: {
	                enable: false //显示勾选框  默认不显示
	            },
	            callback: {
	                onClick: zTreeOnClick, //选中事件
	            },
	            view: {
	                expandSpeed: " ",
	                addHoverDom: addHoverDom, //新增
	                removeHoverDom: removeHoverDom,
	                selectedMulti: false,
	                showIcon: false
	            },
	            data: {
	                simpleData: {
	                    enable: true
	                }
	            },
	            edit: {
	                enable: true, //是否可编辑
	                drag: {
	                    isCopy: false, //拖拽复制
	                    isMove: false, //拖拽移动
	                },
	                showRenameBtn: false, //编辑按钮
	                showRemoveBtn: false, //删除按钮
	                removeTitle: "删除该节点", //删除按钮上的提示信息
	            }
	        };

	        var settingOrg = {
	            check: {
	                enable: true //显示勾选框  默认不显示
	            },
	            callback: {
	                onCheck: onCheck
	            },
	            view: {
	                expandSpeed: " ",
	                selectedMulti: false,
	            },
	            data: {
	                simpleData: {
	                    enable: true
	                }
	            },
	            edit: {
	                enable: true, //是否可编辑
	                drag: {
	                    isCopy: false, //拖拽复制
	                    isMove: false, //拖拽移动
	                },
	                showRenameBtn: false, //编辑按钮
	                showRemoveBtn: false, //删除按钮
	                removeTitle: "删除该节点", //删除按钮上的提示信息
	            }
	        };

	        function onCheck(e, treeId, treeNode) {
	            
	        }

	        //选中事件
	        function zTreeOnClick(event, treeId, treeNode) {
	            console.log("-----zTreeOnClick--id="+treeNode.id);
	            console.log(treeNode);
	        };

	        function removeHoverDom(treeId, treeNode) {
	            if (treeNode.level == 0) {
	                $("#diyBtn_next_" + treeNode.id).unbind().remove();
	            } else {
	                $("#diyBtn_edit_" + treeNode.id).unbind().remove();
	                $("#diyBtn_dele_" + treeNode.id).unbind().remove();
	                $("#diyBtn_next_" + treeNode.id).unbind().remove();
	            }
	        };

	        function addHoverDom(treeId, treeNode) {
	            var aObj = $("#" + treeNode.tId + "_a");
	            if (treeNode.level == 0) {

	                if ($("#diyBtn_next_" + treeNode.id).length > 0) return;
	                var editStr = "<a id='diyBtn_next_" + treeNode.id + "' href='javascript:void(0);'>添加仓库</a>";
	                aObj.append(editStr);

	                var btn3 = $("#diyBtn_next_" + treeNode.id);
	                if (btn3) {
	                    btn3.bind("click", function (event) {
	                        event.stopPropagation();
	                        addEvent(treeNode);
	                    });
	                }
	                return ;

	            } else {
	                if ($("#diyBtn_edit"  + treeNode.id).length > 0) return;
//	                 if ($("#diyBtn_dele_" + treeNode.id).length > 0) return;
	                if ($("#diyBtn_next_" + treeNode.id).length > 0) return;

	                var status=treeNode.enable;
	                var str="";
	                var enabelVal=-1;
	                if(status==0){
	                    str="启用";
	                    enabelVal=1;
	                } if(status==1){
	                    str="停用";
	                    enabelVal=0;
	                }

	                var editStr = "<a href='javascript:void(0);' id='diyBtn_edit_" + treeNode.id + "' > 编辑</a>" +
//	                     "<a id='diyBtn_dele_" + treeNode.id + "' > 删除</a>" +
	                    "<a id='diyBtn_next_" + treeNode.id + "' > "+str+"</a>";
	                aObj.append(editStr);

	                //绑定按钮事件 编辑 - 删除 - 启用

	                var btn1 = $("#diyBtn_edit_" + treeNode.id),
//	                     btn2 = $("#diyBtn_dele_" + treeNode.id),
	                    btn3 = $("#diyBtn_next_" + treeNode.id);

	                if (btn1) {
	                    btn1.bind("click", function () {
	                        editEvent(treeNode);
	                    });
	                }
	                /* if (btn2) {
	                    btn2.bind("click", function () {
	                        delEvent(treeNode);
	                    });
	                } */
	                if (btn3) {
	                    btn3.bind("click", function () {
	                        enableEvent(treeNode,enabelVal,str);
	                    });
	                }
	            }


	        }

	         //-----------------------修改
	        function setAutocomplete(url,param,resParam) {
	            var resParam=Object.assign({'list':'result'},resParam);
	            $("#adminName").Autocomplete({
	                serviceUrl: url, //异步请求
	                paramName: param.paramName,//查询参数，默认 query
	                params:param.params || {},
	                dataType: 'json',
	                minChars: '0', //触发自动匹配的最小字符数
	                maxHeight: '300', //默认300高度
	                dataReader:resParam,
	                triggerSelectOnValidInput: false, // 必选
	                showNoSuggestionNotice: true, //显示查无结果的container
	                noSuggestionNotice: '查询无结果',//查无结果的提示语
	                onSelect: function (result) {
	                    $("#adminName").val(result.value);
	                    $("#adminId").val(result.data);
	                },
	                onNoneSelect: function (params, suggestions) {
	                    $("#adminName").val("");
	                    $("#adminId").val("");
	                }
	            });
	        }
	        
	        function showDialog(titleText,contentText){
	            //提示框
	            utils.dialog({
	                width: 180,
	                height: 30,
	                title: titleText,
	                content: contentText,
	                quickClose: false,
	                okValue: '确定',
	                ok: function () {},
	            }).showModal();
	        }
	        
	        function showTips(contentText){
	            utils.dialog({
	                content: contentText,
	                quickClose: true,
	                timeout: 2000
	            }).showModal();
	        }

	        $(document).ready(function () {
	            initTree(true);
	            loadOrgTree("");
	            //文本框联想控件
	            setAutocomplete("/system/queryUserListByName",
	            {paramName:'userName', params:{"orgCode":'001'}},{data:"id",value:"userName"});

	        });

	        function initTree(flag){
	            $.ajax({  
	                type:"post",  
	                url:'/system/queryStorageListByOrgCode',
	                dataType: "json", 
	                data:{orgCode:orgCode},
	                success: function(res) {  
	                    var resultStr=JSON.stringify(res);
	                    console.log("initTree-->res:"+resultStr);
	                    if(res&&res.code==0){
	                        zTreeNodes=res.result;
	                        zTreeObj = $.fn.zTree.init($("#tree"), setting, zTreeNodes);
	                        //返回一个根节点 
	                        var node = zTreeObj.getNodesByFilter(function (node) { return node.level == 0 }, true); 
	                        console.log("initTree-->parentId="+node.id);
	                        $("#parentId").val(node.id);
	                        if(flag){
	                           zTreeObj.selectNode(node);
	                        }
	                    }else{
	                        showTips("加载仓库树失败");
	                    }
	                }
	            });

	           
	        }
	            
	        function backToOrg(){
	            window.location.href="/system/orgManage";
	        }
	        
	        function resetForm(){
	            $("#parentId").val("0");

	            $("#storageId").val("0");
	            $("#storageName").val("");
	            $("#province").val("");
	            $("#city").val("");
	            $("#district").val("");
	            $("#addressDetail").val("");

	            $("#adminId").val("0");
	            $("#adminName").val("");
	            zTreeObjErp.checkAllNodes(false);
	        }
	        
	        function checkForm(){
	            var result={passed:false};

	            var parentId=$("#parentId").val();
	            
	            var storageId=$("#storageId").val();
	            var storageName=$("#storageName").val();
	            var provinceCode=$("#province").children('option:selected').val();
	            var provinceName=$("#province").children('option:selected').text();
	            var cityCode=$("#city").children('option:selected').val();
	            var cityName=$("#city").children('option:selected').text();
	            var districtCode=$("#district").children('option:selected').val();
	            var districtName=$("#district").children('option:selected').text();
	            var addressDetail=$("#addressDetail").val();
	            var adminId=$("#adminId").val();
	            var adminName=$("#adminName").val();

	            var erpCodeStr="";

	            storageName=$.trim(storageName);
	            if(storageName.length==0){
	                showTips("仓库名称不能为空！");
	                return result;
	            }
	            if(storageName.length>50){
	                showTips("仓库名称不能超过50个字符！");
	                return result;
	            }

	            if(provinceCode==""){
	                showTips("请选择省！");
	                return result;
	            }

	            if(cityCode==""){
	                showTips("请选择市！");
	                return result;
	            }

	            if(districtCode==""){
	                showTips("请选择区！");
	                return result;
	            }
	            
	            if(addressDetail.length==0){
	                showTips("仓库详细地址不能为空！");
	                return result;
	            }

	            if(addressDetail.length>100){
	                showTips("仓库详细地址不能超过100个字符！");
	                return result;
	            }

	            if(adminId==""||adminId=="0"){
	                showTips("请选择IT管理员！");
	                return result;
	            }
	            if(adminName==""){
	                showTips("请选择IT管理员！");
	                return result;
	            }

	            var nodes = zTreeObjErp.getCheckedNodes(true);
	            for(var i=0;i<nodes.length;i++){
	                erpCodeStr+=nodes[i].code+",";
	            }

	            if(erpCodeStr.length>0){
	                erpCodeStr=erpCodeStr.substring(0,erpCodeStr.length-1);
	            }


	            result.passed=true;
	            result.data={
	                    id:storageId,pid:parentId,
	                    orgCode:orgCode,storageCode:"",
	                    storageName:storageName,enable:1,
	                    provinceCode:provinceCode,provinceName:provinceName,cityCode:cityCode,cityName:cityName,
	                    areaCode:districtCode,areaName:districtName,adminId:adminId,erpCodes:erpCodeStr,
	                    address:addressDetail};
	            return result;

	        }

	         function save(){
	            var zTree = $.fn.zTree.getZTreeObj("tree");
	            var nodes = zTree.getSelectedNodes();
	            if (nodes.length == 0) {
	                showDialog('提示','请先选择一个节点');
	                return;
	            }
	            var treeNode = nodes[0];
	            console.log("save-->select treeNode.id="+ treeNode.id);
	            
	            var result=checkForm();
	            console.log("---checkForm-->result:");
	            console.log(JSON.stringify(result));
	            
	            if(!result.passed){
	                return;
	            }
	            
	            var id=parseInt(result.data.id);
	            var isAdd=false;
	            if(id==0){
	                isAdd=true;
	                //新增操作重新设置pid
	                result.data.pid=treeNode.id;
	                console.log("新增操作:set pid="+result.data.pid);
	            }
	             $.ajax({  
	                type:"post",  
	                url:'/system/saveStorage',
	                dataType: "json", 
	                data:result.data,
	                success: function(res) {  
	                    var resultStr=JSON.stringify(res);
	                    console.log("saveStorage-->res:"+resultStr);
	                    if(res.code==0){
	                        //新增操作需要返回主键id
	                        var newId=parseInt(res.result);
	                        saveSuccess(result.data,zTree,treeNode,isAdd,newId);
	                    }else if(res.code==100){
	                        showDialog("提示",res.msg);
	                    }else{
	                        showDialog("提示","保存失败！请重试.");
	                    }
	                }
	            });
	        }

	        function saveSuccess(formData,zTree,treeNode,isAdd,newId){
	            showDialog('保存','保存成功');
	            resetForm();
	            initTree(true);
	        }

	        /*function showInfo(treeNode){
	            if(treeNode.level==0){
	                $("#divEdit").hide();
	                $("#divInfo").hide();
	                return ;
	            }
	            var name = treeNode.name;
	            var id = treeNode.id;
	            console.log("-----showInfo--id="+id);

	            $("#divEdit").hide();
	            $("#divInfo").show();

	            $.ajax({  
	                type:"get",  
	                url:'/system/queryStorageById',
	                dataType: "json", 
	                data:{deptId:id},
	                success: function(res) {  
	                    if(res&&res.code==0){
	                        var resultStr=JSON.stringify(res);
	                        console.log("queryStorageById-->result");
	                        console.log(resultStr);
	                        
	                        $("#labelDptCode").text(res.result.dptCode);
	                        $("#labelDptName").text(res.result.dptName);
	                        $("#labelDptDesc").text(res.result.remarks);
	                    }else{
	                        showTips("查询仓库信息失败！");
	                    }
	                }
	            });

	        }
	        */

	         function addEvent(treeNode) {
	            console.log("-----addEvent--id="+treeNode.id);
	            var name = treeNode.name;
	            var id = treeNode.id;
	            resetForm();

	            zTreeObj.selectNode(treeNode);

	            $("#divEdit").show();
	            $("#divInfo").hide();

	            //setDeptCode();
	        }


	        //编辑的回调函数
	        function editEvent(treeNode) {
	            var name = treeNode.name;
	            var id = treeNode.id;
	            console.log("-----editEvent--id="+id);

	            zTreeObj.selectNode(treeNode);
	            $("#divEdit").show();
	            $("#divInfo").hide();

	            $.ajax({  
	                type:"get",  
	                url:'/system/queryStorageById',
	                dataType: "json", 
	                data:{"id":id},
	                success: function(res) {  
	                    if(res&&res.code==0){
	                        var resultStr=JSON.stringify(res);
	                        console.log("queryStorageById-->result");
	                        console.log(resultStr);

	                        $("#storageId").val(res.result.id);
	                        $("#parentId").val(res.result.pid);//

	                        $("#storageName").val(res.result.storageName);
	                        $("#province").val(res.result.provinceCode).trigger('change.distpicker');;
	                        $("#city").val(res.result.cityCode).trigger('change.distpicker');;
	                        $("#district").val(res.result.areaCode);
	                        $("#addressDetail").val(res.result.address);

	                        $("#adminId").val(res.result.adminId);
	                        $("#adminName").val(res.result.adminName);

	                        var orgCodes=res.result.erpCodes;
	                        $("#orgCodes").val(orgCodes);//UPDATE

	                        loadOrgTree(orgCodes);

	                    }else{
	                        showTips("查询仓库信息失败！");
	                    }
	                }
	            });
	        }
	        
	        //删除回调函数
	        /* function delEvent(treeNode) {
	            var id = treeNode.id;
	            console.log("---delEvent--id="+id);
	            //根节点不能删除判断
	            if(treeNode.level==0){
	                showDialog("提示","不能删除根节点");
	                return;
	            }

	            zTreeObj.selectNode(treeNode);
	            //$("#divEdit").hide();
	            //$("#divInfo").hide();

	            utils.dialog({
	                content: '确定删除此仓库？',
	                quickClose: false,
	                okValue: '确定',
	                cancelValue: '取消',
	                ok: function () {
	                    //---------------add start
	                    $.ajax({  
	                        type:"get",
	                        url:'/system/deleteStorageById',
	                        dataType: "json", 
	                        data:{"id":id},
	                        success: function(res) {  
	                            var resultStr=JSON.stringify(res);
	                            console.log("deleteStorageById-->res:"+resultStr);
	                            if(res&&res.code==0){
	                                var zTree = $.fn.zTree.getZTreeObj("tree");
	                                zTree.removeNode(treeNode, true);
	                                showTips("删除成功！");
	                                $("#divEdit").hide();
	                                $("#divInfo").hide();
	                            }else if(res.code==100){
	                                showTips(res.msg);
	                            }else{
	                                showTips("删除失败！");
	                            }
	                        }
	                    });
	                    //---------------add end
	                },
	                cancel: true
	            }).showModal();

	        } */

	        function enableEvent(treeNode,enableVal,str) {
	            console.log("-----addEvent--id="+treeNode.id+",enableVal="+enableVal);
	            var name = treeNode.name;
	            var id = treeNode.id;
	            $.ajax({
	                url:"/system/disOrEnableStorage",
	                data:{id:id,enable:enableVal},
	                type:"get",
	                success:function(res){
	                    if(res.code==0){
	                        initTree(true);
	                        showTips(res.msg);
	                    }else{
	                        showTips(str+"失败！");
	                    }
	                }
	            });

	        }
	        function changeArea(obj){
	            var provinceName=$("#province").children('option:selected').text();
	            var cityName=$("#city").children('option:selected').text();
	            var districtName=$("#district").children('option:selected').text();
	            $("#addressDetail").val(provinceName+cityName+districtName);
	        }

	        function loadOrgTree(orgCodes){
	            $.ajax({  
	                type:"post",  
	                url:'/system/queryStorageOrgTreeList',
	                dataType: "json", 
	                data:{"orgCodes":orgCodes},
	                success: function(res) {  
	                    var resultStr=JSON.stringify(res);
	                    console.log("initTree-->res:"+resultStr);
	                    if(res&&res.code==0){
	                        zTreeNodesErp=res.result;
	                        zTreeObjErp = $.fn.zTree.init($("#treeOrg"), settingOrg, zTreeNodesErp);
	                    }else{
	                        showTips("加载机构树失败");
	                    }
	                }
	            });
	        }

}
