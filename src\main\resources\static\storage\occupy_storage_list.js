$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });

    /* 日期初始化 */
    z_utils.initDate('begint', 'endt')

  /* table_a */
  //data
  var grid_dataY = [];
  var g_item = {
    text1: "2",
    text2: "",
    text3: "",
    text4: "",
    text5: "",
    text6: "",
    text7: "",
    text8: "",
    text9: "",
    text10: "",
  };
  var g_item1 = {
    text1: "1",
    text2: "",
    text3: "",
    text4: "",
    text5: "",
    text6: "",
    text7: "",
    text8: "",
    text9: "",
    text10: "",
  };
  for (var i = 0; i < 20; i++) {
    if (i === 0) {
      grid_dataY.push(g_item1);
    } else {
      grid_dataY.push(g_item);
    }
  }


  var jsonStore = eval($('#jsonStore').val());
  var re = /^[0-9]+.?[0-9]*$/; //判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/

  var colName = ['单据类型', '单据编号','单据日期', '商品编码','原商品编码', '商品名称', '通用名','商品规格', '生产厂家', '产地', '库房名称', '业务类型',  '占用数量','商品批号'];
  var colModel = [{
    name: 'orderTypeName',
    index: 'orderTypeName'
  }, {
    name: 'orderCode',
    index: 'orderCode',
      width:220
  }, {
    name: 'orderDate',
    index: 'orderDate'
  }, {
    name: 'drugCode',
    index: 'drugCode'
  }, {
      name: 'oldProductCode',
      index: 'oldProductCode'
  }, {
    name: 'drugName',
    index: 'drugName',
    width:220
  }, {
      name: 'commonName',
      index: 'commonName'
  },{
    name: 'spec',
    index: 'spec'
  }, {
    name: 'manufName',
    index: 'manufName'
  }, {
    name: 'prodPlace',
    index: 'prodPlace'
  }, {
    name: 'storageTypeName',
    index: 'storageTypeName'
      ,formatter:function (e) {
        if(!re.test(e)){
            return e;
        }
          var result = "";
          $.each(jsonStore,function(idx,item){
              if(item.numCode == e){
                  result = item.name;
                  return false;
              }
          });
          return result;
    }
  },{
      name: 'channelId',
      index: 'channelId'
  },


      {
    name: 'amount',
    index: 'amount',
    formatter:function(e){
    	return Number(e).toFixed(2);
    }
  },{
      name:'batchNum',
      index:'batchNum',
      hidden:true
  }];
  $('#table_a').XGrid({
    url: "/proxy-storage/storage/occupy/listStorageOccupy",
      postData:{
          drugCode:$('#drugCode').val(),
          orderCode:$("#orderCode").val(),
          startDate:$('#begint').val(),
          endDate:$('#endt').val(),
          orderType:$("#val_d").find("option:selected").val(),
          oldProductCode:$("#oldProductCode").val(),
          storageType:$("#val_c").find("option:selected").val(),
          channelId:$("#channelId").val()
      },
    colNames: colName,
    colModel: colModel,
    selectandorder: true,
    key: 'id',
      rowNum: 20,
      rowList:[20,50,100],
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_pager_a',
    ondblClickRow: function (id, dom, obj, index, event) {

    },
    gridComplete: function () {

    },
    onSelectRow: function (id, dom, obj, index, event) {
      //选中事件
      //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
      //console.log(id, dom, obj, index, event)
    }
  });


  // 筛选列
  $("#set_tb_rows").click(function () {
    //获取当前显示表格
    $('#table_a').XGrid('filterTableHead');
  })

  /* 商品名称 搜索提示（只显示5条） */
  var ts1 = [{
      value: 'Andorra',
      data: 'AD'
    },
    {
      value: 'Zimbabwe',
      data: 'ZZ'
    },
    {
      value: '2Andorra',
      data: 'AD'
    },
    {
      value: '2Zimbabwe',
      data: 'ZZ'
    },
    {
      value: '3Andorra',
      data: 'AD'
    },
    {
      value: '3Zimbabwe',
      data: 'ZZ'
    }
  ];
  $('#commodity').Autocomplete({
    // serviceUrl: 'data.json', //异步请求
    // paramName: 'query111',//查询参数，默认 query
    // dataType: 'json',
    lookup: ts1, //监听数据 value显示文本，data为option的值
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    triggerSelectOnValidInput: false, // 必选
    // multi: true, //多选要和delimiter一起使用
    // delimiter: ',',
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '没有找到相关商品', //查无结果的提示语
    // tabDisabled: true,
  });

  /* 查询 */
  $('#searchBtn').on('click', function (e) {
    //获取form数据
    var data = $('#form_a').serializeToJSON();
    console.log(data);
    //更新表格数据
    var $table_id = $('#nav_content .active .XGridBody table').attr('id');
    if ($table_id == 'table_a') {
      //列表
      $('#table_a').XGrid('setGridParam', {
    	  url: "/proxy-storage/storage/occupy/listStorageOccupy",
    	  postData: {
    		drugCode:$('#drugCode').val(),
              orderCode:$("#orderCode").val(),
              startDate:$('#begint').val(),
              endDate:$('#endt').val(),
              orderType:$("#val_d").find("option:selected").val(),
              oldProductCode:$("#oldProductCode").val(),
              storageType:$("#val_c").find("option:selected").val(),
              batchNum:$('#batchNum').val(),
              drugName:$('#drugName').val(),
              channelId:$("#channelId").val()
	      }
      }).trigger("reloadGrid");
    }
  });

  /* 导出 */
  $('#exportRowData').on('click', function () {
      var tableId = $('#table_a').attr('id');
      z_utils.exportTable(tableId, function (that) {
          //需要导出项
          var colName = [];
          var colNameDesc = [];
          $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
              if ($(this).prop("checked")) {
                  colName.push($(this).attr('name'))
                  colNameDesc.push($(this).parent().text());
              }
          });
          //获取当前选中项
          var data = $('#' + tableId).XGrid('getSeleRow');
          var exportLimitRow = Number($("#exportLimitRow").val());
          if (data.length <= exportLimitRow && data.length >0) {
              /* if (!data.length) {
                   data = [data];
               }*/
              data = data.map(function (item, key) {
                  var new_item = {};
                  colName.forEach(function (val, index) {
                      new_item[val] = item[val]
                  })
                  return new_item
              })
              data = JSON.stringify(data);
              // formData["selectData"] = data;
          } else {
              var rows = $('#' + tableId)[0].p.records;
              if(rows > exportLimitRow) {
                  utils.dialog({
                      title: '提示',
                      width: 200,
                      height: 40,
                      cancel: false,
                      content: '您本次导出的数据量过大（已超过'+exportLimitRow+'条），不允许导出，请缩小导出范围。',
                      okValue: '确定',
                      ok: function () {
                          //utils.closeTab();
                      }

                  }).showModal();
                  return;
              }
              data='';
          }
          console.log(colName);

          //获取form数据
          var formData = {
              drugCode:$('#drugCode').val(),
              orderCode:$("#orderCode").val(),
              startDate:$('#begint').val(),
              endDate:$('#endt').val(),
              orderType:$("#val_d").find("option:selected").val(),
              colName:colName,
              colNameDesc:colNameDesc,
              selectData:data,
              oldProductCode:$('#oldProductCode').val(),
              storageType:$("#val_c").find("option:selected").val(),
              drugName:$('#drugName').val(),
              channelId:$("#channelId").val()
          }
          httpPost("/proxy-storage/storage/occupy/exportListStorageOccupy", formData);
      });
  });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

  /* 商品名称查询 */
  $('#s_commodity').on('click', function (e) {
	  var input_goodName = $("#input_goodName").val();
	  //商品名称 双击查询
	  utils.dialog({
          title: '商品列表',
          url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
          width: $(window).width() * 0.8,
          height: 600,
          data: input_goodName, // 给modal 要传递的 的数据
          onclose: function () {
              if (this.returnValue) {
                  var data = this.returnValue;
                  console.log(data)
                  $('#input_goodName').val(data.productName);
                  $('#drugCode').val(data.productCode);
                  $('#manufName').val(data.manufacturerVal);
                  $('#spec').val(data.specifications);
              }
          },
          oniframeload: function () {
              // console.log('iframe ready')
          }
      }).showModal();
      return false;
    /*var commodity_d = $(e.target).prev('input').val();
    utils.dialog({
      url: '/proxy-storage/storage/saleOrderGoodList.ftl',
      title: '商品列表',
      width: $(window).width() * 0.9,
      height: $(window).height() * 0.7,
      data: commodity_d, // 给modal 要传递的 的数据
      onclose: function () {
        if (this.returnValue) {
          var data = this.returnValue;
          $(e.target).prev('input').val(data.name);
          console.log("this.returnValue", data);
        }
      },
      oniframeload: function () {

      }
    }).showModal();
    return false;
    //alert('查询商品名称')
*/  })

	//业务类型搜索图标
	$(document).on("click", ".glyphicon-search", function () {
	    $(this).siblings("input").trigger("dblclick")
	})


	//业务类型 输入框双击 弹出业务类型列表
	$("#channelId_inp").dblclick(function () {
	    utils.channelDialog('1').then( res => {
	        console.log(res)
	    let _str_name = '', _str_code = '';
	    let _str_arr = res.map(item => {
	        return item.channelCode;
	})
	    _str_name = _str_arr.join(',');

	    let _str_code_arr = res.map(item => {
	        return item.channelCode;
	})
	    _str_code = _str_code_arr.join(',')
	    $('#channelId_inp').val(_str_name)
	    $('#channelId').val(_str_code)
	})
	});


})

 //商品名称 搜索
$('#input_goodName').Autocomplete({
    serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$("#orgCode").val(), //异步请求
    paramName: 'param',//查询参数，默认
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    transformResult: function (response) {
        return {
            suggestions: $.map(response.result.list, function (dataItem) {
                return {
                    value: dataItem.productName,
                    data: dataItem.productCode,
                    manufacturerVal: dataItem.manufacturerVal,
                    specifications: dataItem.specifications,
                };
            })
        };
    },
    triggerSelectOnValidInput: false, // 必选
    // multi: true, //多选要和delimiter一起使用
    // delimiter: ',',
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function (result) {
        //选中回调
        $("#drugCode").val(result.data)
        $("#manufName").val(result.manufacturerVal);
        $("#spec").val(result.specifications);

    },
    onSearchStart: function (params) {
        // console.log('检索开始回调', params)
    },
    onSearchComplete: function (query, suggestions) {

    },
    onSearchError: function (query, jqXHR, textStatus, errorThrown) {
        //查询失败回调
        console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function (container) {
        // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function (params, suggestions) {
        $("#drugCode").val("");
        $("#input_goodName").val("");
        console.log(params, suggestions);
        console.log('没选中回调函数');
        if ($('#input_goodName').val()==''){
            $('#manufName').val('');
            $('#spec').val('');
        }
    }
});

function ClearInfo(event) {
    if (event.currentTarget.value == "") {
        $('#manufName').val('');
        $('#spec').val('');
    }
}

