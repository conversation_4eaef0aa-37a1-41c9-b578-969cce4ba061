$(function () {
    // 生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord"},"manufacturer",{data:"manufactoryId",value:"manufactoryName"});
    valAutocomplete("/proxy-product/product/productFirst/queryBuyerList",{paramName:'userNames'},"applicantId",{data:"id",value:"userName"});
    valAutocomplete("/proxy-product/product/productFirstJiCai/queryBuyerList", { paramName: 'userNames'}, "buyer", { data: "id", value: "userName" });
    $('#X_Tableb').XGrid({
        url:"/proxy-product/product/quality/list?applyType=3",
        colNames: ['ID','申请日期', '机构', '申请人ID', '申请人', '单据编号', '商品编码', '商品名称', '通用名','商品大类', '生产厂家', '型号/规格', '采购员', '商品定位', '审核状态','审核时间'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden:true
            }, {
                name: 'applicationTime',
                index: 'applicationTime',
                formatter:function(value){
                    return new Date(value).Format('yyyy-MM-dd');
                },
                width: 100
            }, {
                name: 'orgCode',
                index: 'orgCode',
                width: 210
            }, {
                name: 'applicantId',
                index: 'applicantId',
                hidden:true
            }, {
                name: 'applicantValue',
                index: 'applicantValue',
                width: 100
            }, {
                name: 'applicationCode',
                index: 'applicationCode',
                width: 160
            }, {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'productName',
                index: 'productName',
                width: 180
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 180
            },  {
                name: 'largeCategoryVal',
                index: 'largeCategoryVal',
                width: 80
            }, {
                name: 'manufacturerValue',
                index: 'manufacturerValue',
                width: 220
            }, {
                name: 'specifications',
                index: 'specifications',
                width: 200
            }, {
                name: 'buyerValue',
                index: 'buyerValue',
                width: 100
            }, {
                name: 'commodityPositionValue',
                index: 'commodityPositionValue',
                width: 160
            },{
                name: 'statues',
                index: 'statues',
                formatter:function(value){
                    if (value==0){
                        return "录入中"
                    }else if (value==1){
                        return "审核中"
                    }else if (value==2){
                        return "审核通过"
                    }else if (value==3){
                        return "审核不通过"
                    }},
                width: 100},{
                name: 'auditTimeValue',
                index: 'auditTimeValue',
                width: 100
            }
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            if(obj.statues=="录入中"){
                var loginUserId = $("#loginUserId").val();
                if(obj.applicantId==loginUserId){
                    utils.openTabs("productQualityEdit", "商品质管属性变更申请", "/proxy-product/product/quality/edit?recordId="+id);
                }else{
                    utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                }
            }else {
                utils.openTabs("productQualityDetail", "商品质管申请详情", "/proxy-product/product/quality/detail?businessId="+id);
            }
        }
    });

    $("#SearchBtn").on("click", function () {
        $("#keyword").val($("#keyword").val().trim());
        $("#approvalNumber").val($("#approvalNumber").val().trim());
        $("#smallPackageBarCode").val($("#smallPackageBarCode").val().trim());
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "orgCode": $("#orgCode").val(),
                "keyword":$("#keyword").val(),
                "statues":$("#statues").val(),
                "approvalNumber":$("#approvalNumber").val(),
                "manufacturer":$("#manufacturer").val(),
                "smallPackageBarCode":$("#smallPackageBarCode").val(),
                "largeCategory":$("#largeCategory").val(),
                "applyType":1,
                "applicationTimeStart":$("#applicationTimeStart").val(),
                "applicationTimeEnd":$("#applicationTimeEnd").val(),
                "auditTimeStart":$("#auditTimeStart").val(),
                "auditTimeEnd":$("#auditTimeEnd").val(),
                "applicantId":$("#applicantId").val(),
                "buyer":$("#buyer").val(),
            },page:1
        }).trigger('reloadGrid');
    });

    // 删除草稿
    $("#deleteDraftBtn").on("click", function () {
        let selRow = $('#X_Tableb').XGrid('getSeleRow');
        var loginUserId =$("#loginUserId").val();
        var applicantId=selRow[0].applicantId;
        let postData = {
        }
        let data = {
            orgApprovalId: selRow[0].id,
            applicantId:applicantId,
            loginUserId:loginUserId
        }
        // productId：  删除草稿需要传递的参数
        let params = {
            applicantId:applicantId,
            statusVal: selRow[0].statues,
            statusName: '录入中',
            url:'/proxy-product/product/orgApproval/delete',
            loginUserId:loginUserId
        }
        utils.deleteDraft('X_Tableb', params, data, postData);

    });

    $("#exportBtn").on("click", function () {
//全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
        var ck = true;
        // copy this parameter and the below buttons
        let html = $('#exportHtml').html();
        let d = dialog({
            title: '请选择导出字段',
            width:400,
            content: html,
            okValue: '确定',
            ok: function () {
                this.title('提交中…');
                return false;
            },
            cancelValue: '取消',
            cancel: function () { },
// copy button to other dialogues
            button: [
                {
                    id: 'chooseAll',
                    value: '全选',
                    callback: function () {
                        //debugger;
                        if(!ck){
                            $(".exportItem input").removeAttr("checked");
                            ck = true;
                        }else if(ck){
                            $(".exportItem input").prop("checked","checked");
                            ck = false;
                        }else{
                            return false;
                        };
                        return false;
                    }
                }
            ]
            //copy ends here
        });
        d.showModal();
    });

})

var orgCode=$("#loginOrgCode").val();
if(orgCode=='001'){
    $.ajax({
        url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode="+orgCode,
        async : false,
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].orgCode+'">'+arr[i].orgName+'</option>';
                    }
                }
            }
            $("#orgCode").html(html);
        },
        error:function () {
        }
    });
}

/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
        },
        onNoneSelect: function (params, suggestions) {
            //console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    });
}
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}