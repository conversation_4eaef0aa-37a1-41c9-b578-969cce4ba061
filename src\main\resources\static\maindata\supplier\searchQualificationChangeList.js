$(function () {
    var dialog = parent.dialog.get(window);
    var keyword=dialog.data;
    $("#search_vl").val(keyword);
    $('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierQualificationChangeController/ajaxSupplierOrganBaseList",
        postData: {
            "supplierName":$("#search_vl").val().trim(),
            "auditStatus":2,
            "disableState":0
        },
        colNames: ['', '供应商编码', '供应商名称', '供应商类型', '业务员', '业务员电话', '营业执照号'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                hidden:true
            }, {
                name: 'supplierCode',
                index: 'supplierCode',
                width: 200

            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 60
            }, {
                name: 'supplierTypeName',
                index: 'supplierTypeName',
                width: 150
            }, {
                name: 'delivery',
                index: 'delivery',
                width: 250
            }, {
                name: 'deliveryPhone',
                index: 'deliveryPhone',
                width: 250
            }, {
                name: 'supplierBusinessNum',
                index: 'supplierBusinessNum',
                width: 250
            }, {
                name: 'disableState',
                index: 'disableState',
                width: 250,
                hidden: true
            },{
                name: 'supplierTypeId',
                index: 'supplierTypeId',
                width: 250,
                hidden: true
            }


        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,//是否展示序号
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
           dialog.close(obj);

        }
    });
    // todo 搜索补全优化
    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "supplierName":'',
                "queryFields": $("#search_vl").val().trim(),
                "auditStatus":2,
                "disableState":0
            },page:1
        }).trigger('reloadGrid');
    });
    $("#SelectBtn").on("click", function () {
        var selectRow =  $('#X_Tableb').XGrid('getSeleRow');
        if (!selectRow){
            utils.dialog({content: '请选择行！', quickClose: true, timeout: 2000}).showModal();
        }else {
            dialog.close(selectRow);
        }
    });
})