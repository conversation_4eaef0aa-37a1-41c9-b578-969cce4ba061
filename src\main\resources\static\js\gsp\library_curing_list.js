$(function () {
  /* table_a */
  //data
  var grid_dataY = [];
  var g_item = {
    text1: "2",
    text2: "",
    text3: "",
    text4: "",
    text5: "",
    text6: "",
    text7: "",
    text8: "",
  };
  var g_item1 = {
    text1: "1",
    text2: "",
    text3: "",
    text4: "",
    text5: "",
    text6: "",
    text7: "",
    text8: "",
  };
  for (let i = 0; i < 20; i++) {

    if (i === 0) {
      grid_dataY.push(g_item1);
    } else {
      grid_dataY.push(g_item);
    }
  }
  var colName = ['养护记录单据编号', '日期', '机构名称', '部门名称', '养护员', '养护类别', '在库养护状态', '备注'];
  var colModel = [{
    name: 'text1',
    index: 'text1'
  }, {
    name: 'text2',
    index: 'text2'
  }, {
    name: 'text3',
    index: 'text3'
  }, {
    name: 'text4',
    index: 'text4'
  }, {
    name: 'text5',
    index: 'text5'
  }, {
    name: 'text6',
    index: 'text6'
  }, {
    name: 'text7',
    index: 'text7'
  }, {
    name: 'text8',
    index: 'text8'
  }];
  $('#table_a').XGrid({
    data: grid_dataY,
    colNames: colName,
    colModel: colModel,
    rownumbers: true,
    key: 'text1',
    rowNum: 10,
    altRows: true, //设置为交替行表格,默认为false
    pager: '#grid_pager_a',
    ondblClickRow: function (id, dom, obj, index, event) {
      //跳转详情页
      z_utils.openPage('library_curing_info.html',{id:555,sum:666});
    },
    gridComplete: function () {},
    onSelectRow: function (id, dom, obj, index, event) {
      //选中事件
      //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
      //console.log(id, dom, obj, index, event)
    }
  });

  // 筛选列
  $("#set_tb_rows").click(function () {
    //获取当前显示表格
    var tableId = $('#nav_content .active table').attr('id');
    $('#' + tableId).XGrid('filterTableHead');
  })

  /* 查询 */
  $('#searchBtn').on('click', function (e) {
    //获取form数据
    var data = $('#form_a').serializeToJSON();
    console.log(data);
    //更新表格数据
    $('#table_a').XGrid('setGridParam', {
      data: grid_dataY,
      colNames: colName,
      colModel: colModel,
      rowNum: 10,
      altRows: true, //设置为交替行表格,默认为false
      pager: '#grid_pager_a',
    }).trigger("reloadGrid");
  });

  /* 新增 */
  $('#addRowData').on('click', function () {
    window.location = 'library_curing_add.html';
  });

  /* 导出 */
  $('#exportRowData').on('click', function () {
    var tableId = $('#nav_content .active table').attr('id');
    z_utils.exportTable(tableId, function (that) {
      //需要导出项
      var colName = [];
      $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
        if ($(this).prop("checked")) {
          colName.push($(this).attr('name'))
        }
      });
      //获取所有项
      var data = $('#' + tableId).XGrid('getRowData');
      data = data.map(function (item, key) {
        var new_item = {};
        colName.forEach(function (val, index) {
          new_item[val] = item[val]
        })
        return new_item
      })
      console.log(colName, data);
    });
  });
})