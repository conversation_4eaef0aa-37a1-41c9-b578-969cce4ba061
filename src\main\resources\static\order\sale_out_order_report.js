$(function () {
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })

    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('1').then( res => {
            console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });

    initDate();
    $('#input_goodName').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + $("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#input_goodName").val("");
            console.log(params, suggestions);
            console.log('没选中回调函数');
        }
    });

    /* 商品名称查询 */
    $('#s_commodity').on('click', function (e) {
        var input_goodName = $("#input_goodName").val();
        //商品名称 双击查询
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 600,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#input_goodName').val(data.productName);
                    $('#drugCode').val(data.productCode);
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
        //alert('查询商品名称')
    })

    // 开始日期
    $("#d4311").focus(function(){
        return WdatePicker({
            startDate:'%y-%M-01 00:00:00',
            dateFmt:'yyyy-MM-dd HH:mm:ss',
            autoPickDate:true,
            maxDate:'#F{$dp.$D(\'d4312\')||\'%y-%M-%d 23:59:59\'}'
        })
    })
    //截止日期
    $("#d4312").focus(function(){
        return WdatePicker({
            startDate:'%y-%M-%d 00:00:00',
            dateFmt:'yyyy-MM-dd HH:mm:ss',
            autoPickDate:true,
            minDate:'#F{$dp.$D(\'d4311\')}',
            maxDate:endMaxDate('d4311')
        })
    })
    function endMaxDate(id) {
        var startTime = $('#' + id).val();
        var currentDate = moment().format("YYYY-MM-DD")
        //客户筛选项是否有值
        if($('#input_custorm_hidden').val()){
            return currentDate
        }else{
            var lastDate = moment(startTime).add('3', 'month').format("YYYY-MM-DD")
            let diff = moment(lastDate).diff(moment(currentDate))
            if(diff>=0) {
                return '%y-%M-%d 23:59:59'
            }else{
                return lastDate;
            }
        }
    }

    //客户名称双击查询
    $("#btnSearch_custorm").click(function () {
        utils.dialog({
            title: '客户列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCustomerList',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.8,
            data: $('#input_custorm').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    //console.log(data)
                    $('#input_custorm').val(data.customerName);
                    //$('#customerCode').val(data.customerCode);
                    $('#input_custorm_hidden').val(data.customerCode);
                }
                $('iframe').remove();
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    })
    //客户名称搜索
    $('#input_custorm').Autocomplete({
        serviceUrl: '/proxy-customer/customer/customerBaseAppl/pageList', //异步请求
        paramName: 'customerCode',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function(response) {
            return {
                suggestions: $.map(response.result.list, function(dataItem) {
                    return { value: dataItem.customerName, data: dataItem.customerCode };
                })
            };
        },
        onSelect: function (result) {
            //选中回调
            $('#input_custorm').val(result.value);
            $("#input_custorm_hidden").val(result.data);
        },
        onSearchStart: function (params) {
        },
        onSearchComplete: function (query, suggestions) {
        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
            $("#input_custorm").val('');
            $("#input_custorm_hidden").val('');
        }
    });
    // tabs 切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),$nav_content = $('.nav-content');
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).css('display', 'flex').siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');
        $("#btn_toggle").children("button").eq($this.index()).show().siblings().hide();
    });
    //订单明细
    // var colNameA = ['id', '销售订单号', 'ERP开票单号', '销售出库单号', '客户编号', '客户名称', '商品编号', '原商品编码', '商品名称', '商品大类', '商品规格', '生产厂家', '商品产地', '单位','商品定位',
    //     '库房名称', '批号', '生产日期', '有效期至','出库日期', '出库数量', '含税单价', '价税合计', '活动优惠金额', '余额抵扣优惠', '返利金额', '税率', '税额', '不含税成本单价', '不含税成本金额', '价差毛利','业务类型'];
    var colNameA = ['id', '销售订单号', 'ERP开票单号', '销售出库单号', '客户编号', '客户名称', '商品编号', '原商品编码', '商品名称', '商品大类', '商品规格', '生产厂家', '商品产地', '单位','商品定位',
        '库房名称', '批号', '生产日期', '有效期至','出库日期', '出库数量', '含税单价', '价税合计', '活动优惠金额', '余额抵扣优惠', '返利金额', '税率', '税额', '业务类型'];
    // var colModelA = [
    //     {name: 'id', index: 'id', hidden: true, hidegrid: true},
    //     {name: 'ecOrderCode',width:240}, {name: 'salesOrderCode'}, {name: 'salesOutOrderCode',width:240}, {name: 'customerCode'}, {name: 'customerName'}, {name: 'productCode'}, {name: 'oldProductCode'}, {name: 'productName'}, {name: 'drugClassName'}, {name: 'productSpec'}, {name: 'productmManufacturer'}, {name: 'producingArea'}, {name: 'producingPackingUnit'},{name: 'commodityPosition'},
    //     {name: 'storeName'}, {name: 'batchCode'}, {name: 'manufactureTime', formatter:function(value){return new Date(value).Format('yyyy-MM-dd');}}, {name: 'expiryTime'}, {name: 'outTime', formatter:function(value){return new Date(value).Format('yyyy-MM-dd');}}, {name: 'outStoreNumber'}, {name: 'productTaxPrice'}, {name: 'taxAmount'}, {name: 'activityPreferentialAmount'}, {name: 'balancePreferentialAmount'}, {name: 'rebateAmount'}, {name: 'taxRateStr'}, {name: 'taxAssess'}, {name: 'costPrice'},
    //     {name: 'costAmount'}, {name: 'subProfit'},{name: 'channelId'}
    // ], allColModelA = JSON.parse(JSON.stringify(colModelA));
    var colModelA = [
        {name: 'id', index: 'id', hidden: true, hidegrid: true},
        {name: 'ecOrderCode',width:240}, {name: 'salesOrderCode'}, {name: 'salesOutOrderCode',width:240}, {name: 'customerCode'}, {name: 'customerName'}, {name: 'productCode'}, {name: 'oldProductCode'}, {name: 'productName'}, {name: 'drugClassName'}, {name: 'productSpec'}, {name: 'productmManufacturer'}, {name: 'producingArea'}, {name: 'producingPackingUnit'},{name: 'commodityPosition'},
        {name: 'storeName'}, {name: 'batchCode'}, {name: 'manufactureTime', formatter:function(value){return new Date(value).Format('yyyy-MM-dd');}}, {name: 'expiryTime'}, {name: 'outTime', formatter:function(value){return new Date(value).Format('yyyy-MM-dd');}}, {name: 'outStoreNumber'}, {name: 'productTaxPrice'}, {name: 'taxAmount'}, {name: 'activityPreferentialAmount'}, {name: 'balancePreferentialAmount'}, {name: 'rebateAmount'}, {name: 'taxRateStr'}, {name: 'taxAssess'},
        {name: 'channelId'}
    ], allColModelA = JSON.parse(JSON.stringify(colModelA));
    //商品汇总
    // var colNameB = ['id','商品编号', '原商品编码', '商品名称', '商品大类', '商品规格', '生产厂家', '单位', '商品定位','出库数量', '价税合计', '活动优惠金额', '余额抵扣优惠', '返利金额', '税额', '不含税成本金额', '价差毛利'];
    var colNameB = ['id','商品编号', '原商品编码', '商品名称', '商品大类', '商品规格', '生产厂家', '单位', '商品定位','出库数量', '价税合计', '活动优惠金额', '余额抵扣优惠', '返利金额', '税额'];
    // var colModelB = [
    //     {name: 'id', index: 'id', hidden: true, hidegrid: true},
    //     {name: 'productCode'}, {name: 'oldProductCode'}, {name: 'productName'}, {name: 'drugClassName'}, {name: 'productSpec'}, {name: 'productmManufacturer'},{name: 'producingPackingUnit'},{name: 'commodityPosition'},
    //     {name: 'outStoreNumber'}, {name: 'taxAmount'}, {name: 'activityPreferentialAmount'}, {name: 'balancePreferentialAmount'}, {name: 'rebateAmount'}, {name: 'taxAssess'}, {name: 'costAmount'}, {name: 'subProfit'}
    // ], allColModelB = JSON.parse(JSON.stringify(colModelB));
    var colModelB = [
        {name: 'id', index: 'id', hidden: true, hidegrid: true},
        {name: 'productCode'}, {name: 'oldProductCode'}, {name: 'productName'}, {name: 'drugClassName'}, {name: 'productSpec'}, {name: 'productmManufacturer'},{name: 'producingPackingUnit'},{name: 'commodityPosition'},
        {name: 'outStoreNumber'}, {name: 'taxAmount'}, {name: 'activityPreferentialAmount'}, {name: 'balancePreferentialAmount'}, {name: 'rebateAmount'}, {name: 'taxAssess'}
    ], allColModelB = JSON.parse(JSON.stringify(colModelB));
    //客户汇总
    // var colNameC = ['id','客户编号', '客户名称', '商品编号', '原商品编码', '商品名称', '商品大类', '商品规格', '生产厂家', '单位', '出库数量', '价税合计', '活动优惠金额', '余额抵扣优惠', '返利金额', '税额', '不含税成本金额', '价差毛利'];
    var colNameC = ['id','客户编号', '客户名称', '商品编号', '原商品编码', '商品名称', '商品大类', '商品规格', '生产厂家', '单位', '出库数量', '价税合计', '活动优惠金额', '余额抵扣优惠', '返利金额', '税额'];
    // var colModelC = [
    //     {name: 'id', index: 'id', hidden: true, hidegrid: true},
    //     {name: 'customerCode'}, {name: 'customerName'},
    //     {name: 'productCode'}, {name: 'oldProductCode'}, {name: 'productName'}, {name: 'drugClass'}, {name: 'productSpec'}, {name: 'productmManufacturer'}, {name: 'producingPackingUnit'},
    //     {name: 'outStoreNumber'}, {name: 'taxAmount'}, {name: 'activityPreferentialAmount'}, {name: 'balancePreferentialAmount'}, {name: 'rebateAmount'}, {name: 'taxAssess'}, {name: 'costAmount'}, {name: 'subProfit'}
    // ], allColModelC = JSON.parse(JSON.stringify(colModelC));
    var colModelC = [
        {name: 'id', index: 'id', hidden: true, hidegrid: true},
        {name: 'customerCode'}, {name: 'customerName'},
        {name: 'productCode'}, {name: 'oldProductCode'}, {name: 'productName'}, {name: 'drugClass'}, {name: 'productSpec'}, {name: 'productmManufacturer'}, {name: 'producingPackingUnit'},
        {name: 'outStoreNumber'}, {name: 'taxAmount'}, {name: 'activityPreferentialAmount'}, {name: 'balancePreferentialAmount'}, {name: 'rebateAmount'}, {name: 'taxAssess'}
    ], allColModelC = JSON.parse(JSON.stringify(colModelC));

    $('#X_Tablea').XGrid({
        url: '/proxy-order/order/orderOut/orderOutReport/findProductList',
        postData: $("#saleOrderForm").serializeToJSON(),
        colNames: colNameA,
        colModel: colModelA,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-a',//设置翻页所在html元素名称
        selectandorder: true
    });

    $('#X_Tableb').XGrid({
        //url:'/orderOut/orderOutReport/statisticsOrderGroupByGood',
        //postData: $("#saleOrderForm").serializeToJSON(),
        data: [],
        colNames: colNameB,
        colModel: colModelB,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-b',//设置翻页所在html元素名称
        selectandorder: true, //是否展示序号，多选
    });


    $('#X_Tablec').XGrid({
        //url:'/orderOut/orderOutReport/orderGroupByCustomer',
        //postData: $("#saleOrderForm").serializeToJSON(),
        data: [],
        colNames: colNameC,
        colModel: colModelC,
        rowNum: 20,
        rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager-c',//设置翻页所在html元素名称
        selectandorder: true, //是否展示序号，多选
    });
    //查询数据，重置data
    $('#searchBtn').on('click', function () {
        if (!outgoingTypeSelect()){
            utils.dialog({content: '至少选择一种出库类型！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        var type = $("#toggle_wrap .active").attr("type");
        if (type == 0) {
            $('#X_Tablea').setGridParam({
                url: '/proxy-order/order/orderOut/orderOutReport/findProductList?'+$("#saleOrderForm").serialize(),
                postData: {}
            }).trigger('reloadGrid');
        } else if (type == 1) {
            $('#X_Tableb').setGridParam({
                url: '/proxy-order/order/orderOut/orderOutReport/statisticsOrderGroupByGood?'+$("#saleOrderForm").serialize(),
                postData: {}
            }).trigger('reloadGrid');
        } else if (type == 2) {
            $('#X_Tablec').setGridParam({
                url: '/proxy-order/order/orderOut/orderOutReport/orderGroupByCustomer?'+$("#saleOrderForm").serialize(),
                postData: {}
            }).trigger('reloadGrid');
        } else {
            return;
        }

    })


    //查询数据，重置data
    $('#exportBtn').on('click', function () {
        $.ajax({
            url: "/proxy-order/order/common/tryAcquire",
            type: "post",
            async:false,
            success: function (result) {
                if (!result){
                    utils.dialog({content: '当前导出人数过多,请稍后2分钟后重新尝试导出！', quickClose: true, timeout: 2000}).showModal();
                    return;
                }else {
                    extracted();
                }
            }
        })
    })
    function extracted() {
        let _curTabIndex = $('.nav-tabs>li.active').index();
        let tableIdAry = ['X_Tablea', 'X_Tableb', 'X_Tablec'],
            pagerIdAry = ['pg_grid-pager-a', 'pg_grid-pager-b',
                'pg_grid-pager-c'];
        utils.exportAstrictHandle(tableIdAry[_curTabIndex], Number(
            $('#' + pagerIdAry[_curTabIndex]
                + ' #totalPageNum').text())).then(
            () => {
                return false;
            }).catch(() => {
            if (!outgoingTypeSelect()) {
                utils.dialog({
                    content: '至少选择一种出库类型！',
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                return false;
            }
            var begin = $("#d4311").val();
            var end = $("#d4312").val();
            ;
            if (begin == "" || end == "") {
                utils.dialog({
                    content: '请输入查询开始时间和结束时间！',
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                return false;
            }
            var type = $("#toggle_wrap .active").attr("type");
            if (type == 0) {
                var ck = false;
                var nameModel = "";
                addHtmlA(colNameA);
                dialog({
                    content: $("#setColA"),
                    title: '筛选列',
                    width: 706,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {
                        $(this.node).find(
                            '#checkRow input[type="checkbox"]').each(
                            function (index) {
                                if ($(this).is(":checked")) {
                                    nameModel += allColModelA[index
                                        + 1].name
                                        + ":" + $(this).attr('name') + ","
                                }
                            });
                        if (nameModel.length == 0) {
                            utils.dialog({
                                content: '请选择后导出',
                                quickClose: true,
                                timeout: 2000
                            }).showModal();
                            return false;
                        }
                        var sel = $('#X_Tablea').XGrid('getSeleRow');
                        //如果有选中的行，则导出选中的行，否则导出所有查询条件的
                        var params;
                        if (sel.length == 0) {
                            var formData = $(
                                "#saleOrderForm").serializeToJSON();
                            params = $.extend(formData,
                                {"nameModel": nameModel})
                        } else {
                            var formData = $(
                                "#saleOrderForm").serializeToJSON();
                            var selectData = JSON.stringify(sel);
                            params = $.extend(formData,
                                {"nameModel": nameModel},
                                {"selectData": selectData})
                        }
                        httpPost(
                            "/proxy-order/order/orderOut/orderOutReport/exportProductDetail",
                            params);
                    },
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if (!ck) {
                                    $("#checkRow input").prop("checked",
                                        false);
                                    ck = true;
                                } else if (ck) {
                                    $("#checkRow input").prop("checked",
                                        "checked");
                                    ck = false;
                                } else {
                                    return false;
                                }
                                ;
                                return false;
                            }
                        }
                    ]
                }).showModal();

            } else if (type == 1) {
                //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                var ck = false;
                var nameModel = "";
                addHtmlB(colNameB);
                dialog({
                    content: $("#setColB"),
                    title: '筛选列',
                    width: 706,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {
                        var newColName = [], newColModel = [];
                        $(this.node).find(
                            '#checkRow input[type="checkbox"]').each(
                            function (index) {
                                if ($(this).is(":checked")) {
                                    nameModel += allColModelB[index
                                        + 1].name
                                        + ":" + $(this).attr('name') + ","
                                }
                            });
                        if (nameModel.length == 0) {
                            utils.dialog({
                                content: '请选择后导出',
                                quickClose: true,
                                timeout: 2000
                            }).showModal();
                            return false;
                        }
                        var sel = $('#X_Tableb').XGrid('getSeleRow');
                        //如果有选中的行，则导出选中的行，否则导出所有查询条件的
                        var params;
                        if (sel.length == 0) {
                            var formData = $(
                                "#saleOrderForm").serializeToJSON();
                            params = $.extend(formData,
                                {"nameModel": nameModel})
                        } else {
                            var formData = $(
                                "#saleOrderForm").serializeToJSON();
                            var selectData = JSON.stringify(sel);
                            params = $.extend(formData,
                                {"nameModel": nameModel},
                                {"selectData": selectData})
                        }
                        httpPost(
                            "/proxy-order/order/orderOut/orderOutReport/exportOrderDataGroupbyGood",
                            params);
                    },
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if (!ck) {
                                    $("#checkRow input").prop("checked",
                                        false);
                                    ck = true;
                                } else if (ck) {
                                    $("#checkRow input").prop("checked",
                                        "checked");
                                    ck = false;
                                } else {
                                    return false;
                                }
                                ;
                                return false;
                            }
                        }
                    ]
                }).showModal();

            } else if (type == 2) {
                //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
                var ck = false;
                var nameModel = "";
                addHtmlC(colNameC);
                dialog({
                    content: $("#setColC"),
                    title: '筛选列',
                    width: 706,
                    data: 'val值',
                    cancelValue: '取消',
                    cancel: true,
                    okValue: '导出',
                    ok: function () {

                        var newColName = [], newColModel = [];
                        $(this.node).find(
                            '#checkRow input[type="checkbox"]').each(
                            function (index) {
                                if ($(this).is(":checked")) {
                                    nameModel += allColModelC[index
                                        + 1].name
                                        + ":" + $(this).attr('name') + ","
                                }
                            });
                        if (nameModel.length == 0) {
                            utils.dialog({
                                content: '请选择后导出',
                                quickClose: true,
                                timeout: 2000
                            }).showModal();
                            return false;
                        }
                        var sel = $('#X_Tablec').XGrid('getSeleRow');
                        //如果有选中的行，则导出选中的行，否则导出所有查询条件的
                        var params;
                        if (sel.length == 0) {
                            var formData = $(
                                "#saleOrderForm").serializeToJSON();
                            params = $.extend(formData,
                                {"nameModel": nameModel})
                        } else {
                            var formData = $(
                                "#saleOrderForm").serializeToJSON();
                            var selectData = JSON.stringify(sel);
                            params = $.extend(formData,
                                {"nameModel": nameModel},
                                {"selectData": selectData})
                        }
                        httpPost(
                            "/proxy-order/order/orderOut/orderOutReport/exportSalesOrderGroupByCustomerListSum",
                            params);
                    },
                    button: [
                        {
                            id: 'chooseAll',
                            value: '全选',
                            callback: function () {
                                //debugger;
                                if (!ck) {
                                    $("#checkRow input").prop("checked",
                                        false);
                                    ck = true;
                                } else if (ck) {
                                    $("#checkRow input").prop("checked",
                                        "checked");
                                    ck = false;
                                } else {
                                    return false;
                                }
                                ;
                                return false;
                            }
                        }
                    ]
                }).showModal();
            } else {
                return;
            }
        });
    }


    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }


    $("#set_tables_rowa").click(function () {
        $('#X_Tablea').XGrid('filterTableHead');

    })

    function addHtmlA(arry) {
        if (!$('#setColA')[0]) {
            var s = '<div id="setColA" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 1; i < arry.length; i++) {
                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //筛选列
    $("#set_tables_rowb").click(function () {
        $('#X_Tableb').XGrid('filterTableHead');

    })

    function addHtmlB(arry) {
        if (!$('#setColB')[0]) {
            var s = '<div id="setColB" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 1; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    function addHtmlC(arry) {
        if (!$('#setColC')[0]) {
            var s = '<div id="setColC" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 1; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }
            s += '</div></div>';
            $("body").append(s);
        }
    }

    $("#set_tables_rowc").click(function () {
        $('#X_Tablec').XGrid('filterTableHead');
    })

    //  序号添加title
    $('body').on('mouseover','td[row-describedby="ck"]',function () {
        $(this).attr('title',$(this).text())
    })
})
//判断复选框至少选择一个
function outgoingTypeSelect() {
    var flag = false;
    var checked=$("input[name='outgoingType']:checked");
    if(checked.length!=0){
       flag = true;
    }
    return flag;
}
function initDate(param) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    var month = new_date.getMonth() + 1;
    var date = new_date.getDate();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#d4311').val(year + '-' + month + '-01 00:00:00');
    $('#d4312').val(year + '-' + month + '-' + date + ' 23:59:59');
}
//控制是否打印筛选条件
$('input[name=outgoingType]').on('change',function () {
   var ele = $('input[name=outgoingType]:checked');
   if(ele.length==1&&ele.val()==1){
       $('#print').removeAttr('disabled')
       $('#printDiv').show();
   }else {
       $('#print').attr('disabled',true)
       $('#printDiv').hide();
   }
});
$('input[name=outgoingType]').trigger('change');

Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}
