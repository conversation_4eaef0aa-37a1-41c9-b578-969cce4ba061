const equals = (a, b) => {
  // a = JSON.parse(JSON.stringify(a))
  // delete a.changeStatus
  // b = JSON.parse(JSON.stringify(b))
  // delete b.changeStatus
  // debugger
  if (a === b) return true;
  if (!a || !b || (typeof a !== "object" && typeof b !== "object"))
    return a === b;
  if (a.prototype !== b.prototype) return false;
  if (a instanceof Array) {
    a = a.map(function (val, index) {
      return { id: val.id, checked: val.checked };
    });
    b = b.map(function (val, index) {
      return { id: val.id, checked: val.checked };
    });
  }
  var keys = Object.keys(a);
  if (keys.length !== Object.keys(b).length) return false;
  return keys.every((k) => equals(a[k], b[k]));
};
$.extend({
  //变更申请，新增变更
  changApply_insertData: function (obj) {
    var varObj = {
      name: "columnValue",
      beforeText: "beforeText",
      beforeValue: "valueBefore",
      afterValue: "valueAfter",
      afterText: "afterText",
      status: "changeStatus",
    };

    $.extend(varObj, obj);

    window.changeApply = {}; //变更obj
    window.changeApply_ = {}; //变更临时obj
    window.changeApplyList = {}; //变更obj-list
    window.changeApplyList_ = {}; //变更临时obj-list

    $.addTelListener();

    //点击资料变更
    $("#changeApplyBtn").on("click", function () {
      var $d = $("[changeApplyFlag]"),  //点击资料变更后所有具备该属性的元素都进行处理
        width_64 = 0,
        width_83 = 0;
      var w = $d.eq(0).parents(".row").width() - 20;

      //tabs相关
      $d.each(function (i, v) {
        if ($(v).siblings("i.yulanInput,i.changeApplyBtn").length <= 0) {
          if ($(v).is("a")) {
            //如果是tabs切换
            $(v).css("padding-right", "25px");
            $(v).after(
              '<i class="yulan"></i><i changeApplyBtn class="changeApplyBtn yulanBtn"></i>'
            );
          } else {
            if ($(v).prop("type") == "radio") {
              $(v).parent(".checkbox").css("position", "initial");
              //var left = $(v).parents('div.text-center').innerWidth() * 0.5 + 43;
              $(v).after(
                '<i class="yulan yulanInput" style="top: 8px;right:-18px"></i><i changeApplyBtn class="changeApplyBtn"  style="top: 8px;right:-18px"></i>'
              );
            } else if ($(v).prop("type") == "checkbox") {
              $(v).parent(".checkbox").css("position", "initial");
              //var left = ($(v).parents('.checkbox').offset().left - $(v).parents('.form-control').offset().left) + $(v).parents('.checkbox').width();
              $(v).after(
                '<i class="yulan yulanInput" style="position:absolute;top: 8px;right:-18px"></i><i changeApplyBtn class="changeApplyBtn"  style="position:absolute;top: 8px;right:-18px"></i>'
              );
            } else {
              //仓库地址
              // if ($(v).attr('changeapplyflag') == 'supplierRepertoryAddressVOList') {
              //     $(v).parent('.form-group').css('position', 'initial');
              // }
              // //普通文本
              // $(v).after('<i class="yulan yulanInput" style="position:absolute;top: 8px;right:-17px"></i><i changeApplyBtn class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>');
              /**
               * RM 018-10-06
               */
              //仓库地址
              if (
                $(v).attr("changeapplyflag") == "supplierRepertoryAddressVOList"
              ) {
                $(v).parent(".form-group").css("position", "initial");
              }
              //收货地址
              if (
                $(v).attr("changeapplyflag") == "customerDeliveryAddressVoList"
              ) {
                //WN：20200827 点击编辑时，临时保存原街道地址
                window.deliveryAddressBeforeStreetText = $(
                  "[changeapplyflag=customerDeliveryAddressVoList]"
                )
                  .parents("div.row")
                  .eq(0)
                  .find("select")
                  .eq(3)
                  .find("option:selected")
                  .text();
                $(v).parent(".form-group").css("position", "initial");
              }
              //对应生产厂商
              if ($(v).attr("changeapplyflag") == "supplierManufactoryVOList") {
                $(v).parent(".form-group").css("position", "initial");
              }
              //账单地址
              if (
                $(v).attr("changeapplyflag") == "customerBillingAddressVoList"
              ) {
                //客户变更详情账单地址
                if (
                  changeApply.customerBillingAddressVoList &&
                  changeApply.customerBillingAddressVoList.valueAfter &&
                  changeApply.customerBillingAddressVoList.valueAfter.length > 0
                ) {
                  $(v).after(
                    '<i class="yulan yulanInput yulanInput_after" style="position:absolute;top: 8px;right:-17px; display: block;"></i><i changeApplyBtn class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>'
                  );
                } else {
                  $(v).after(
                    '<i class="yulan yulanInput" style="position:absolute;top: 8px;right:-17px; display: block;"></i><i changeApplyBtn class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>'
                  );
                }
              } else if (
                $(v).attr("changeapplyflag") == "customerStorageAddressVOList"
              ) {
                if (
                  changeApply.customerStorageAddressVOList &&
                  changeApply.customerStorageAddressVOList.valueAfter
                ) {
                  $(v).after(
                    '<i class="yulan yulanInput yulanInput_after" style="position:absolute;top: 8px;right:-17px; display: block;"></i><i changeApplyBtn class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>'
                  );
                } else {
                  $(v).after(
                    '<i class="yulan yulanInput" style="position:absolute;top: 8px;right:-17px; display: block;"></i><i changeApplyBtn class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>'
                  );
                }
              } else {
                //普通文本
                $(v).after(
                  '<i class="yulan yulanInput" style="position:absolute;top: 8px;right:-17px"></i><i changeApplyBtn class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>'
                );
              }
            }
            //$(v).closest('.input-group').parents('.row').css('width', w + 'px');
            $(v).parents(".panel-body").find(".row").css("width", "100%");
          }
          $(v).siblings("i.yulanInput").fadeIn();
        }
      });

      //$('[changeapplyflag="registerList"]').parent('.form-group').css('width', '23.5%');
      $('[changeapplyflag="registerList"]')
        .parents(".distpicker")
        .css("overflow", "initial");

      $('[changeapplyflag="depotList"]')
        .parent(".form-group")
        .css("width", "29%");
      $('[changeapplyflag="depotList"]')
        .parents(".distpicker")
        .css("overflow", "initial");

      $("body").after(
        $("<style></style>").text(
          ".input-group > .pull-left-width-64{width:calc(95% - 125px) !important;float: left !important;margin-left: 10px;}"
        )
      );
      $("body").after(
        $("<style></style>").text(
          ".input-group > .pull-left-width-83{width:calc(97.4% - 125px) !important;float: left !important;margin-left: 10px;}"
        )
      );

      if ($('[name="threeEvidenceAll"]:checked').val() == "1") {
        $('[name="organizationCode"]').parent().find("i").hide();
        $('[name="taxRegistrationCode"]').parent().find("i").hide();
      }

      $.changeApply_updateIcon(window.changeApply, varObj);
    });

    $("body")
      .on("click", ".yulan", function () {  //打开操作弹窗 
        /*展开操作框*/
        window.changeApply_d = dialog({
          align:
            $(this).offset().left > $("body").width() * 0.85 ? "left" : "top",
          width: 130,
          height: 55,
          padding: 6,
          content: $(".changeApplyItem"),
          quickClose: true,
        }).show(this);
        window.changeApply_e = this; //储存对应节点
      })
      .on("click", ".cEdit", function () {  //点击编辑
        /**
         * 每点击一个编辑按钮的时候就判断当前页面有没有没保存的数据，有的话就提示先保存 再去编辑下一条数据，
         * 要不然当多条数据有依赖关系的时候，会造成数据混乱
         */
        let changeApplyBtns = $(".changeApplyBtn"),
          editState = false;
        var _that = this;
        $(changeApplyBtns).each(function (index, item) {
          if ($(item).css("display") != "none") {
            editState = true;
            return editState;
          }
        });
        if (editState) {
          utils
            .dialog({
              title: "提示",
              content: "请先保存修改的数据再进行编辑操作.",
              okValue: "确定",
              ok: function () {},
            })
            .showModal();
          return false;
        }
        /*点击编辑*/
        var $t = $(window.changeApply_e); //找到对应节点
        var selObj;
        /***客户类别**/
        if (
          $("#shippingSource").length &&
          $("#shippingSource").val() != 3 &&
          $t.prev("input").attr("name") == "customerTypeName"
        ) {
          utils
            .dialog({
              title: "提示",
              content: "客户类别不支持编辑修改",
              okValue: "确定",
              ok: function () {},
            })
            .showModal();
          return false;
        }
        /**
         * 开票信息
         */
        if ($t.parents("form").attr("id") == "billInfo_form") {
          // 开票信息
          let prevNameArr = ["billingOpeningBank", "billingBankAccount"], // 敲个空格
            thisTagName = $t.prev("input").attr("name"),
            btn_customerFileTagName = $t.prev().hasClass("btn_customerFile"); // 非独立经营证明 节点
          if ($t.parents("form").attr("supportEdit") != "true") {
            if (prevNameArr.indexOf(thisTagName) >= 0) {
              utils
                .dialog({
                  title: "提示",
                  content: "不支持编辑修改",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
          }

          // 仅神农新增用户可以变更发票类型
          var customerCode = $("#customerCode").val();
          if (
            thisTagName === "invoiceType" &&
            customerCode.indexOf("SNKH") < 0
          ) {
            utils
              .dialog({
                title: "提示",
                content: "不支持编辑修改",
                okValue: "确定",
                ok: function () {},
              })
              .showModal();
            return false;
          }

          // 如果  是否独立核算的值有修改，并且修改后的值为 是， 公司名称等不支持编辑
          // 如果，是否独立核算的值没有修改, 并且初始值 当前为是是， 公司名称等不支持编辑
          /**
           * RM 2019-07-19  上面注释为旧的需求。现有新的需求
           * 具体需求见 首营时 提交审核的注释
           */
          if (
            window.changeApply["isIndependent"] &&
            window.changeApply["isIndependent"]["valueAfter"] == "1"
          ) {
            if (btn_customerFileTagName) {
              // 公司名称或者非独立经营证明   // 2019-07-19  thisTagName == 'billingCompanyName' ||
              utils
                .dialog({
                  title: "提示",
                  content: "不支持编辑修改",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
          } else if (
            window.changeApply["isIndependent"] &&
            window.changeApply["isIndependent"]["valueAfter"] == "0"
          ) {
            // 否的时候  支持图片上传
          } else {
            let _val = $("[name=isIndependent]:checked").val();
            if (_val == "1" && btn_customerFileTagName) {
              // 公司名称 或者  非独立经营证明 // 2019-07-19 ( _val == '1' && thisTagName == 'billingCompanyName') ||
              utils
                .dialog({
                  title: "提示",
                  content: "不支持编辑修改",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
          }
          if ($t.parent().find("[name=customerEnclosureVoList]").length > 0) {
            // 非独立经营证明
            let tags = $t.parents(".row").find(".fileIcon_p");
            let baseImgArr = [];
            if (tags.length > 0) {
              $(tags).each(function (index, item) {
                let kaipiaoPicObj = {};
                kaipiaoPicObj.enclosureName = "非独立经营证明" + (index + 1);
                kaipiaoPicObj.url = $(item).attr("data-imgurl");
                baseImgArr.push(kaipiaoPicObj);
              });
            }
            selObj = {
              [varObj.name]: "customerEnclosureVoList", //字段名
              [varObj.beforeValue]: JSON.stringify(baseImgArr), //改前值
            };
            window.changeApply_["customerEnclosureVoList"] = selObj;

            if (
              window.changeApply["isIndependent"] &&
              window.changeApply["isIndependent"]["valueAfter"] == "0"
            ) {
              $t.parent().find("button").prop("disabled", false);
            } else {
              let _val = $("[name=isIndependent]:checked").val();
              if (_val == "0") {
                $t.parent().find("button").prop("disabled", false);
              }
            }
          }
        }

        /**
         * 对应生产厂商
         */
        if ($t.prev().attr("changeapplyflag") == "supplierManufactoryVOList") {
          let _bool = checkEdit();
          if (!_bool) {
            utils
              .dialog({
                title: "提示",
                content: "不支持编辑修改",
                okValue: "确定",
                ok: function () {},
              })
              .showModal();
            return false;
          }
        }

        // 营业执照号
        if ($t.parents("form").attr("id") == "customerApplVo") {
          // 客户资料变更基础属性
          let prevNameArr = ["businessLicenseNum", "taxRegistryNumber"],
            thisTagName = $t.prev("input").attr("name");
          // if(prevNameArr.indexOf(thisTagName) >= 0 && (window.changeApply && window.changeApply['nature'] &&  window.changeApply['nature']['valueAfter'] == '3')){
          //     utils.dialog({
          //         title: "提示",
          //         content: '当客户性质选中项为其他时，不支持编辑修改',
          //         okValue: '确定',
          //         ok: function () {}
          //     }).showModal()
          //     return false;
          // }else{
          //     if(!window.changeApply || !window.changeApply['nature']){
          //         if(prevNameArr.indexOf(thisTagName) >= 0  && $('[name=nature]').val() == '3'){
          //             utils.dialog({
          //                 title: "提示",
          //                 content: '当客户性质选中项为其他时，不支持编辑修改',
          //                 okValue: '确定',
          //                 ok: function () {}
          //             }).showModal()
          //             return false;
          //         }
          //     }else{
          //         if(prevNameArr.indexOf(thisTagName) >= 0  && window.changeApply['nature']['valueAfter']  == '3'){
          //             utils.dialog({
          //                 title: "提示",
          //                 content: '当客户性质选中项为其他时，不支持编辑修改',
          //                 okValue: '确定',
          //                 ok: function () {}
          //             }).showModal()
          //             return false;
          //         }
          //     }
          // };
        }

        if ($t.prev("input").attr("name") == "maintenancePeriod") {
          utils
            .dialog({
              title: "提示",
              content: "不支持编辑修改",
              okValue: "确定",
              ok: function () {},
            })
            .showModal();
          return false;
        }

        // $t.parent().find('div').find('span[data-role=remove]').css('display','block')
        // $t.parent().find('div').find('button').css('display','block')
        $t.parent()
          .find("div")
          .find("span[data-role=remove]")
          .removeClass("disNone")
          .addClass("disBlock");
        $t.parent()
          .find("div")
          .find("button")
          .removeClass("disNone")
          .addClass("disBlock");
        $t.parent()
          .find("div")
          .find("input")
          .removeClass("disNone")
          .addClass("disBlock");
        /**
         * 下面三元 写法是针对 客户资料的账单地址
         */
        var $t_flag =
          $t.siblings("[changeapplyflag]").length > 0
            ? $t.siblings("[changeapplyflag]")
            : $t.parent("div").find("[changeapplyflag]"); //标记元素
        window.changeApply_d.close(); //关闭对应dialog
        $t.hide(); //隐藏三个点
        $t.siblings("[changeApplyBtn]").show(); //展示保存按钮

        console.log($t_flag.prop("type"));
        var $t_type =
          $t_flag.prop("type") == "number" ||
          $t_flag.prop("type") == "select-one"
            ? "text"
            : $t_flag.prop("type");
        switch ($t_type) {
          case "radio": //为单选
            $t.siblings("input[type=radio]").removeAttr("readonly"); //解除禁用
            $t.siblings("input[type=radio]").removeAttr("disabled"); //解除禁用
            var $t_flag_radio = $t.siblings("input[type=radio]:checked");
            selObj = {
              [varObj.name]: $t_flag.prop("name"), //字段名
              [varObj.beforeValue]: $t_flag_radio.val(), //改前值
            };

            //从 变更对象 添加该属性
            window.changeApply_[$t_flag.prop("name")] = selObj;
            break;
          case "checkbox": //为复选
            // 商品管理模块 对特殊属性的高值 和易发错 的状态持久禁用，无论什么情况下。
            let checkboxs = $t.siblings("input[type=checkbox]");
            $(checkboxs).each(function (index, item) {
              if (!$(item).attr("data-disable")) {
                $(item).removeAttr("readonly disabled");
              }
            });
            //$t.siblings('input[type=checkbox]').removeAttr('readonly'); //解除禁用
            //$t.siblings('input[type=checkbox]').removeAttr('disabled'); //解除禁用
            var $t_flag_checkbox = $t.siblings("input[type=checkbox]:checked"),
              $t_checked_ary = [];
            $t_flag_checkbox.each(function (i, v) {
              v.checked ? $t_checked_ary.push($(v).val()) : "";
            });
            /**
             *  RM 2019-07-23
             *  特殊属性： 新增 无特殊属性选项
             *  当无特殊属性被选中时，，别的项不允许选择，当别的项有选中时，无特殊属性项不允许选择
             *  下面代码为版本二的代码. 具体版本二是啥操作，请在客户模块下 搜索关键字 [版本二]
             */
            // if($t_flag.parent().find('[name=specialBusinessScope]').length > 0 ){
            //     if($t_checked_ary.indexOf('12') < 0){ // 无特殊属性未被选中
            //         $t_flag.parent().find('[name=specialBusinessScope]:last').prop('disabled',true); // 不允许选
            //     }else{
            //         $t_flag.parent().find('[name=specialBusinessScope]').not(':last').prop('disabled',true); // 别的都不允许选
            //     }
            // }
            /*------------2019-07-23    END-----------------------*/
            selObj = {
              [varObj.name]: $t_flag.prop("name"), //字段名
              [varObj.beforeValue]: $t_checked_ary.join(","), //改后值
            };

            //从 变更对象 添加该属性
            window.changeApply_[$t_flag.prop("name")] = selObj;
            break;
          case "textarea": //为文本域
            $t_flag.removeAttr("readonly"); //解除禁用
            $t_flag.removeAttr("disabled"); //解除禁用
            $t_flag.parent().find("textarea").removeAttr("readonly");
            // var $t_flag_hidden = $('#' + $t_flag.attr('changeapplyflag'));
            selObj = {
              [varObj.name]: $t_flag.prop("name"), //字段名
              [varObj.beforeValue]: $t_flag.val(), //改前值
            };
            //从 变更对象 添加该属性
            window.changeApply_[$t_flag.prop("name")] = selObj;
            break;
          case "text": //为文本
           
            // 开票信息的公司名称输入框 点击编辑按钮的时候，输入框依然还是禁用，只是显示放大镜图标。支持弹窗
            if ($t_flag.parents("form").attr("id") == "billInfo_form") {
              if ($t_flag.attr("name") == "billingCompanyName") {
                $t_flag.parent().find("#bill_customerBtn").remove();
                if (
                  window.changeApply &&
                  window.changeApply["isIndependent"] &&
                  window.changeApply["isIndependent"]["valueAfter"] == 0
                ) {
                  $t_flag
                    .parent()
                    .append(
                      `<i class="glyphicon glyphicon-search" id="bill_customerBtn"></i>`
                    );
                  $t_flag.prop("disabled", false);
                } else if ($("[name=isIndependent]:checked").val() == 0) {
                  $t_flag
                    .parent()
                    .append(
                      `<i class="glyphicon glyphicon-search" id="bill_customerBtn"></i>`
                    );
                  $t_flag.prop("disabled", false);
                } else {
                  $t_flag.removeAttr("readonly  disabled"); // 2019-07-19
                }
              } else {
                $t_flag
                  .siblings("input[type=number],input[type=text]")
                  .removeAttr("readonly");
                $t_flag
                  .siblings("input[type=number],input[type=text]")
                  .removeAttr("disabled");
                $t_flag.removeAttr("readonly"); //解除禁用
                $t_flag.removeAttr("disabled"); //解除禁用
                $t_flag.prev().find("input").removeAttr("readonly");
              }
            } else {
              $t_flag
                .siblings("input[type=number],input[type=text]")
                .removeAttr("readonly");
              $t_flag
                .siblings("input[type=number],input[type=text]")
                .removeAttr("disabled");
              $t_flag.removeAttr("readonly"); //解除禁用
              $t_flag.removeAttr("disabled"); //解除禁用
              $t_flag.prev().find("input").removeAttr("readonly");
            }

            var $t_flag_hidden = $("#" + $t_flag.attr("changeapplyflag"));

            //客户变更详情
            var cusAddress = $t_flag
              .parents("#base")
              .find('div[data-toggle="distpicker"]');
            if (cusAddress.length > 0) {
              $(cusAddress).each(function (index, item) {
                var oldStreetName = $(item)
                  .find("select")
                  .eq(3)
                  .find("option:selected")
                  .text();
                $(item)
                  .find("select")
                  .eq(3)
                  .attr("oldStreetName", oldStreetName);
              });
            }

            //如果ID是隐藏域
            if ($t_flag.attr("changeapplyflag") && $t_flag_hidden.length) {
              selObj = {
                [varObj.name]: $t_flag_hidden.prop("name"),
                [varObj.beforeValue]: $t_flag_hidden.val(),
                afterText:
                  $t_flag.prop("type") == "select-one"
                    ? $t_flag.find("option:checked").text()
                    : $t_flag.val(),
              };
            } else if (
              $t_flag.attr("changeapplyflag") == "registerList" ||
              $t_flag.attr("changeapplyflag") == "register"
            ) {
              //如果为注册列表                                               //  客户资料变更管理 详情 的注册地址的这个属性值为 register
              var $t_flag_selects = $t_flag
                .parents("div.row")
                .eq(0)
                .find("select"); //  RM： 添加下标0 的作用是为了，当页面中当前行中 有两个地址类输入框时，会取到当前页面行的所有下拉框，而本意是为了只拿到当前操作地址的输入框的父级就行
              $t_flag_selects.removeAttr("readonly"); //解除禁用
              $t_flag_selects.removeAttr("disabled"); //解除禁用

              selObj = {
                [varObj.name]: "registerList",
                [varObj.beforeValue]: {
                  registerProvince: $t_flag_selects.eq(0).val(),
                  registerCity: $t_flag_selects.eq(1).val(),
                  registerArea: $t_flag_selects.eq(2).val(),
                  registerStreet: $t_flag_selects.eq(3).val(), // 街道下拉框
                  registerDetail: $t_flag.val(),
                },
              };
              //修改前街道存储
              $t_flag_selects
                .eq(3)
                .attr(
                  "oldStreetName",
                  $t_flag_selects.eq(3).find("option:selected").text()
                );
            } else if (
              $t_flag.attr("changeapplyflag") == "customerStorageAddressVOList"
            ) {
              //如果客户资料 仓库地址列表
              var $t_flag_selects = $t_flag
                .parents("div.row")
                .eq(0)
                .find("select");
              $t_flag_selects.removeAttr("readonly"); //解除禁用
              $t_flag_selects.removeAttr("disabled"); //解除禁用
              selObj = {
                [varObj.name]: "customerStorageAddressVOList", //customerStorageAddressList,
                [varObj.beforeValue]: {
                  repertoryProvince: $t_flag_selects.eq(0).val(),
                  repertoryCity: $t_flag_selects.eq(1).val(),
                  repertoryArea: $t_flag_selects.eq(2).val(),
                  registerStreet: $t_flag_selects.eq(3).val(), // 街道下拉框
                  repertoryDetail: $t_flag.val(),
                },
              };
              //修改前街道存储
              $t_flag_selects
                .eq(3)
                .attr(
                  "oldStreetName",
                  $t_flag_selects.eq(3).find("option:selected").text()
                );
            } else if (
              $t_flag.attr("changeapplyflag") == "customerDeliveryAddressVoList"
            ) {
              //收货地址

              let shippingAddressObj = [
                {
                  nextNodeWrap: "#saProvinceSel_wrap",
                  nextNodeName: "province2",
                  nextNodeId: "province2",
                },
                {
                  nextNodeWrap: "#sacitySel_wrap",
                  nextNodeName: "shippingAddressCityId",
                  nextNodeId: "shippingAddressCityId",
                },
                {
                  nextNodeWrap: "#sadistrictSel_wrap",
                  nextNodeName: "shippingAddressDistrictId",
                  nextNodeId: "shippingAddressDistrictId",
                },
                {
                  nextNodeWrap: "#sastreetSel_wrap",
                  nextNodeName: "shippingAddressStreetId",
                  nextNodeId: "shippingAddressStreetId",
                },
              ];
              var $t_flag_selects = $t_flag
                .parents("div.row")
                .eq(0)
                .find("select"); //  RM： 添加下标0 的作用是为了，当页面中当前行中 有两个地址类输入框时，会取到当前页面行的所有下拉框，而本意是为了只拿到当前操作地址的输入框的父级就行
              $t_flag_selects.removeAttr("readonly"); //解除禁用
              $t_flag_selects.removeAttr("disabled"); //解除禁用

              selObj = {
                [varObj.name]: "customerDeliveryAddressVoList",
                [varObj.beforeValue]: {
                  province2: $t_flag_selects.eq(0).val(),
                  shippingAddressCityId: $t_flag_selects.eq(1).val(),
                  shippingAddressDistrictId: $t_flag_selects.eq(2).val(),
                  shippingAddressStreetId: $t_flag_selects.eq(3).val(), // 街道下拉框
                  shippingAddressInput: $t_flag.val(),
                },
              };
              //修改前街道存储
              $t_flag_selects
                .eq(3)
                .attr(
                  "oldStreetName",
                  $t_flag_selects.eq(3).find("option:selected").text()
                );
            } else if (
              $t_flag.attr("changeapplyflag") ==
              "supplierRepertoryAddressVOList"
            ) {
              //如果仓库地址列表
              var arr = [];
              //组合仓库数据
              $(".depotList").each(function (index) {
                var se = $(this).find("select");
                var inp = $(this).find(".text-inp");
                var repertoryProvince = se.eq(0).find("option:selected").val();
                var repertoryCity = se.eq(1).find("option:selected").val();
                var repertoryArea = se.eq(2).find("option:selected").val();
                var repertoryDetail = inp.val();
                arr[index] = {
                  repertoryProvince: repertoryProvince,
                  repertoryCity: repertoryCity,
                  repertoryArea: repertoryArea,
                  repertoryDetail: repertoryDetail,
                };

                se.removeAttr("readonly").removeAttr("disabled"); //解除禁用
                inp.removeAttr("readonly").removeAttr("disabled"); //解除禁用

                //修改前街道存储
                if (se.eq(3).find("option:selected").length > 0) {
                  se.eq(3).attr(
                    "oldStreetName",
                    se.eq(3).find("option:selected").text()
                  );
                } else {
                  se.eq(2).attr(
                    "oldStreetName",
                    se.eq(2).find("option:selected").text()
                  );
                }
                //se.eq(3).attr('oldStreetName',(se.eq(3).find("option:selected").length > 0?se.eq(3).find("option:selected").text():se.eq(2).find("option:selected").text()));
              });
              $(".addDepot").show();
              $(".removeDepot").show();
              selObj = {
                [varObj.name]: "supplierRepertoryAddressVOList",
                [varObj.beforeValue]: arr,
              };
            } else if (
              $t_flag.attr("changeapplyflag") == "customerBillingAddressVoList"
            ) {
              //如果账单  地址列表
              var arr = [];
              //组合仓库数据
              //var billLists = $t_flag.parents('div.row').eq(0).parents('.billList');
              var billLists = $t_flag.parents("#billAddress").find(".billList");
              if (billLists.length > 0) {
                $(billLists).each(function (index) {
                  var se = $(this).find("select");
                  var inp = $(this).find(".text-inp");
                  var repertoryProvince = se
                    .eq(0)
                    .find("option:selected")
                    .val();
                  var repertoryCity = se.eq(1).find("option:selected").val();
                  var repertoryArea = se.eq(2).find("option:selected").val();
                  var registerStreet = se.eq(3).find("option:selected").val();
                  var repertoryDetail = inp.val();
                  arr[index] = {
                    repertoryProvince: repertoryProvince,
                    repertoryCity: repertoryCity,
                    repertoryArea: repertoryArea,
                    registerStreet: registerStreet,
                    repertoryDetail: repertoryDetail,
                  };

                  se.removeAttr("readonly").removeAttr("disabled"); //解除禁用
                  inp.removeAttr("readonly").removeAttr("disabled"); //解除禁用

                  //修改前街道存储
                  //se.eq(3).attr('oldStreetName',se.eq(3).find("option:selected").text());
                  /**
                   * RM 2018-11-09
                   * 给账单地址的所有下拉框的值存起来，因为，有的时候第四个下拉框的值或者不回显，或者没数据
                   * 然后再保存回显或者详情回显的时候哦，只能显示省份下拉框的值，测试就会提bug
                   */
                  se.eq(0).attr(
                    "oldStreetName",
                    se.eq(0).find("option:selected").text()
                  );
                  se.eq(1).attr(
                    "oldStreetName",
                    se.eq(1).find("option:selected").text()
                  );
                  se.eq(2).attr(
                    "oldStreetName",
                    se.eq(2).find("option:selected").text()
                  );
                  se.eq(3).attr(
                    "oldStreetName",
                    se.eq(3).find("option:selected").text()
                  );
                  se.eq(0).attr(
                    "oldStreetVal",
                    se.eq(0).find("option:selected").val()
                  );
                  se.eq(1).attr(
                    "oldStreetVal",
                    se.eq(1).find("option:selected").val()
                  );
                  se.eq(2).attr(
                    "oldStreetVal",
                    se.eq(2).find("option:selected").val()
                  );
                  se.eq(3).attr(
                    "oldStreetVal",
                    se.eq(3).find("option:selected").val()
                  );
                });
              }

              $(".addbill").show();
              $(".removeDepot").show();
              selObj = {
                [varObj.name]: "customerBillingAddressVoList",
                [varObj.beforeValue]: arr,
              };
            } else if (
              $t_flag.attr("changeapplyflag") == "supplierManufactoryVOList"
            ) {
              //如果对应生产厂商
              // if(checkEdit()){
              //     $t_flag.parents('#manufacturer_row').find('.ManufactoryList').find('.input-group-addon').html('<i class="text-require">*  </i>对应生产厂商');
              //     $t_flag.parents('#manufacturer_row').find('.ManufactoryList').find('input[type=text]').addClass(' {validate:{ required :true}}')
              // }
              var ManufactoryListArr = [];
              //组合对应生产厂商数据
              var ManufactoryList = $t_flag
                .parents("#manufacturer_row")
                .find(".ManufactoryList");
              if (ManufactoryList.length > 0) {
                $(ManufactoryList).each((index, item) => {
                  let hiddenInpVal = $(item)
                    .find("input[name^=Manufactory]")
                    .val();
                  let inpVal = $(item).find("input[type=text]").val();
                  ManufactoryListArr[index] = {
                    manufactoryId: hiddenInpVal,
                    manufactoryName: inpVal,
                  };
                  $(item)
                    .find("input[name^=Manufactory]")
                    .removeAttr("readonly")
                    .removeAttr("disabled"); //解除禁用
                  $(item)
                    .find("input[type=text]")
                    .removeAttr("readonly")
                    .removeAttr("disabled"); //解除禁用
                  $(item)
                    .find("input[type=text]")
                    .attr(
                      "oldManufactoryName",
                      $(item).find("input[type=text]").val()
                    );
                  $(item)
                    .find("input[type=text]")
                    .attr(
                      "oldManufactoryId",
                      $(item).find("input[name^=Manufactory]").val()
                    );
                });
              }

              $(".btn_addManufacturer").show();
              $(".btn_removeManufacturer").show();
              selObj = {
                [varObj.name]: "supplierManufactoryVOList",
                [varObj.beforeValue]: ManufactoryListArr,
              };
            } else if (
              $t_flag.attr("changeapplyflag") == "supplierExtendItemVOList"
            ) {
              //如果付款方式

              //付款、结算部分
              var supplierExtendItemVoList = [];
              $(".paymentSettlement").each(function () {
                //var parentInp = $(this).find(".parentCode");
                var parentInp =
                  $(this).find(".parentCode").length > 0
                    ? $(this).find(".parentCode")
                    : $(this).find("input[type=checkbox]");
                if (parentInp.is(":checked")) {
                  var parentCode = parentInp.attr("name");
                  var parentCodeVal = parentInp.val();
                  if ($(this).find(".childCode").length < 1) {
                    supplierExtendItemVoList.push({
                      [parentCode]: parentCodeVal,
                    });
                  }
                  $(this)
                    .find(".childCode")
                    .each(function (index) {
                      var json = {};
                      var cCode = $(this).find(".cCode");
                      var cCodeName = cCode.attr("name");
                      var cCodeValue = cCode.val();
                      var cValue = $(this).find(".cValue");
                      var cValueName = cValue.attr("name");
                      var cValueValue = cValue.val();
                      if ($.trim(cValueValue) != "") {
                        json[parentCode] = parentCodeVal;
                        json[cCodeName] = cCodeValue;
                        json[cValueName] = cValueValue;
                        supplierExtendItemVoList.push(json);
                      }
                    });
                }
              });

              selObj = {
                [varObj.name]: "supplierExtendItemVOList",
                [varObj.beforeValue]: supplierExtendItemVoList,
                afterText: "",
              };
              $(".parentCode").removeAttr("disabled");
              $(".childCode").find(".cValue").removeAttr("disabled");
              clearDisabled("2002");
              clearDisabled("2003");
            } else if (
              $t_flag.attr("changeapplyflag") == "customerExtendItemVoList"
            ) {
              //如果付款方式  客户资料变更
              var customerExtendItemVoList = [];
              $(".paymentSettlement").each(function () {
                //var parentInp = $(this).find(".parentCode");
                var parentInp =
                  $(this).find(".parentCode").length > 0
                    ? $(this).find(".parentCode")
                    : $(this).find("input[type=checkbox]");
                if (parentInp.is(":checked")) {
                  var parentCode = parentInp.attr("name");
                  var parentCodeVal = parentInp.val();
                  if ($(this).find(".childCode").length < 1) {
                    customerExtendItemVoList.push({
                      [parentCode]: parentCodeVal,
                    });
                  }
                  $(this)
                    .find(".childCode")
                    .each(function (index) {
                      var json = {};
                      var cCode = $(this).find(".cCode");
                      var cCodeName = cCode.attr("name");
                      var cCodeValue = cCode.val();
                      var cValue = $(this).find(".cValue");
                      var cValueName = cValue.attr("name");
                      var cValueValue = cValue.val();
                      if ($.trim(cValueValue) != "") {
                        json[parentCode] = parentCodeVal;
                        json[cCodeName] = cCodeValue;
                        json[cValueName] = cValueValue;
                        customerExtendItemVoList.push(json);
                      }
                    });
                }
              });

              selObj = {
                [varObj.name]: "customerExtendItemVoList",
                [varObj.beforeValue]: customerExtendItemVoList,
                afterText: "",
              };
              $(".parentCode").removeAttr("disabled");
              $(".childCode").find(".cValue").removeAttr("disabled");
              addDisabled("2002");
              addDisabled("2003");
            } else {
              if ($t_flag.attr("data-role")) {
                //如果是标签文本，保存编辑需加控制
                $t_flag.tagsinput("operable");
                $t_flag
                  .siblings()
                  .find("span[data-role=remove]")
                  .removeClass("disNone")
                  .addClass("disBlock");
              }
              //针对多个输入框的情况
              let baseVal = [];
              if (
                $t_flag.prev().is("input[type=number]") ||
                $t_flag.prev().is("input[type=text]")
              ) {
                let inputNodes =
                  $t_flag.parent().find("input[type=number]").length > 0
                    ? $t_flag.parent().find("input[type=number]")
                    : $t_flag.parent().find("input[type=text]");
                baseVal = Array.from(inputNodes).map(function (item, index) {
                  return $(item).val();
                });
              }
              selObj = {
                [varObj.name]: $t_flag.prop("name"),
                [varObj.beforeValue]:
                  baseVal.length > 0 ? baseVal.join() : $t_flag.val(),
                afterText:
                  $t_flag.prop("type") == "select-one"
                    ? $t_flag.find("option:checked").text()
                    : baseVal.length > 0
                    ? baseVal.join()
                    : $t_flag.val(),
              };
            }

            //select
            if($t_flag.parents(".tel-wrap").find("select")){
              $t_flag.parents(".tel-wrap").find("select").removeAttr("disabled");
              if($t_flag.parents(".tel-wrap").find("select").val()==""){
                $t_flag.parents(".tel-wrap").find(".tel-part").attr("disabled","disabled");
              }
            }

            //从 变更对象 添加该属性
            window.changeApply_[selObj[[varObj.name]]] = selObj;
            break;
          case "": //为tabs切换
            if ($t_flag.is("a")) {
              var $t = $(window.changeApply_e); //找到对应节点
              //var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
              var $t_flagData; //标记元素
              var tableData;
              if ($("#sourceIds")) {
                console.log($("#sourceIds").attr("data-id"));
                $t_flagData =
                  $t.siblings("[changeapplyflag]").length > 0
                    ? $t.siblings("[changeapplyflag]")
                    : $t.parent("div").find("[changeapplyflag]");
                tableData = window.changeApplyList[$t_flagData.prop("name")];
              }
              var $t_ul = $t.parents("ul.nav");
              var $t_ul_nav_content = $t_ul.next(".nav-content");
              var $t_table = $t_ul_nav_content.find("table.XGridUI:visible");

              $t.siblings("input[type=radio]");

              if ($t_ul.length) {
                $t_flag.css("padding-right", "38px"); //改变tabs宽度
              } else {
                $t_ul_nav_content = $t_flag
                  .parents(".panel-heading")
                  .next(".panel-body")
                  .find(".nav-content");
                $t_table = $t_ul_nav_content.find("table.XGridUI:visible");
              }

              if ($t_table.length) {
                //如果tbs里是Grid或其他情况
                if ($t_flag.prop("name") != "customerApprovalFileVoList") {
                  // WN: 20201013 除客户变更页面外，还原至 20200901 改动前逻辑
                  selObj = {
                    [varObj.name]: $t_flag.prop("name"),
                    [varObj.beforeValue]: $("#" + $t_table.prop("id")).XGrid(
                      "getRowData"
                    ),
                  };
                }

                $t_ul_nav_content.find("button").removeAttr("disabled");

                if ($t_table.attr("id") == "X_Table_Channel") {
                  // 业务类型属性
                  let rowdata = $t_table.XGrid("getRowData"),
                    allTrs = $t_table.find("tr").not(":first");
                  $(rowdata).each((index, item) => {
                    if (item.effectiveState == "1") {
                      // 有效时可以编辑
                      $(allTrs[index])
                        .find("input,select")
                        .not("[name=effectiveState]")
                        .removeAttr("disabled");
                      if (item["dimensionSalesPriceYn"] == "0") {
                        $(allTrs[index])
                          .find("input[name=pharmacyType]")
                          .attr("disabled", true);
                      }
                    }
                  });
                } else {
                  $t_table.find("input,select").removeAttr("disabled");
                }
                if ($t_flag.prop("name") == "supplierClientProxyOrderList") {
                  $("#isEdit").val(1);
                } else if (
                  $t_flag.prop("name") == "supplierApprovalFileList" ||
                  $t_flag.prop("name") == "customerApprovalFileVoList"
                ) {
                  setDisabledZtree(false);
                }
                if ($("#toggleSuppleentalEditing").length) {
                  $("#toggleSuppleentalEditing").attr("disabled", "disabled");
                }
                // WN: 20201013 添加限定范围，以下代码仅客户资质变更页面内生效
                if ($t_flag.prop("name") == "customerApprovalFileVoList") {
                  // WN: 20200901 将 selObj 的赋值放到数据操作之后，避免 table 中的部分字段因 disable 而取不到值。
                  selObj = {
                    [varObj.name]: $t_flag.prop("name"),
                    [varObj.beforeValue]: $("#" + $t_table.prop("id")).XGrid(
                      "getRowData"
                    ),
                  };
                }
              } else {
                //tabs中上传附件
                var otherFilesArr = [];
                var $t_checkbox = $t_ul_nav_content.find(
                  'input[type="checkbox"]:visible'
                );
                if ($("#otherFileBox").length > 0) {
                  $("#otherFileBox input[type='checkbox']").each(function () {
                    var checked = this.checked;
                    if (checked) {
                      var v = $(this).val();
                      var json = {};
                      var imgList = [];
                      var fLen = $("input[id='other" + v + "']").length;
                      json.certificateType = v;
                      if (fLen > 0) {
                        imgList = JSON.parse(
                          $("input[id='other" + v + "']").val()
                        );
                        json.enclosureList = imgList;
                        for (var j = 0; j < json.enclosureList.length; j++) {
                          delete json.enclosureList[j].type;
                        }
                      } else {
                        json.enclosureList = [];
                      }
                      otherFilesArr.push(json);
                    }
                  });
                }
                if ($("#contractType").length > 0) {
                  $("#contractType input[type='checkbox']").each(function () {
                    var checked = this.checked;
                    if (checked) {
                      var v = $(this).val();
                      var json = {};
                      var imgList = [];
                      var fLen = $("input[id='contractType" + v + "']").length;
                      json.certificateType = v;
                      if (fLen > 0) {
                        imgList = JSON.parse(
                          $("input[id='contractType" + v + "']").val()
                        );
                        json.enclosureList = imgList;
                        for (var j = 0; j < json.enclosureList.length; j++) {
                          delete json.enclosureList[j].type;
                        }
                      } else {
                        json.enclosureList = [];
                      }
                      otherFilesArr.push(json);
                    }
                  });
                }
                $t_checkbox.removeAttr("disabled");
                $("#qtfjUpload").removeAttr("disabled");

                selObj = {
                  [varObj.name]: $t_flag.prop("name"),
                  [varObj.beforeValue]: otherFilesArr,
                };
              }
            }
            if ($t_table.find(".ApprovalUploadeImg").length > 0) {
              $t_table.find(".ApprovalUploadeImg").removeAttr("disabled");
            }
            //从 变更对象 添加该属性
            window.changeApplyList_[$t_flag.prop("name")] = selObj;

            /****添加特殊处理 EC 豆芽数据***/
            // if($("#shippingSource").length&&$("#shippingSource").val()!=3){
            if ($t_flag.prop("name") == "customerApprovalFileVoList") {
              if (
                window.changeApplyList.customerApprovalFileVoList &&
                window.changeApplyList.customerApprovalFileVoList.valueAfter
              ) {
                var fileData = {
                  result: {
                    list: JSON.parse(
                      JSON.stringify(
                        window.changeApplyList.customerApprovalFileVoList
                          .valueAfter
                      )
                    ),
                  },
                };
                handleData(fileData, $("#table3"), true);
                setTimeout(function () {
                  $t_table.find("input,select").removeAttr("disabled");
                  $t_table
                    .find("[name=credentialTypeId]")
                    .attr("disabled", "disabled");
                  setDisabledZtree(false);
                }, 1500);
              }
            }
            if ($t_flag.prop("name") == "customerDelegationFileVoList") {
              if (
                window.changeApplyList.customerDelegationFileVoList &&
                window.changeApplyList.customerDelegationFileVoList.valueAfter
              ) {
                $("#table2").XGrid("clearGridData").trigger("reloadGrid");
                xGridTable2(
                  JSON.parse(
                    JSON.stringify(
                      window.changeApplyList.customerDelegationFileVoList
                        .valueAfter
                    )
                  ),
                  $("#table2")
                );
                $t_table.find("input,select").removeAttr("disabled");
              }
            }
            if ($t_flag.prop("name") == "customerBeProxyIdentityVoList") {
              if (
                window.changeApplyList.customerBeProxyIdentityVoList &&
                window.changeApplyList.customerBeProxyIdentityVoList.valueAfter
              ) {
                $("#certTable").XGrid("clearGridData").trigger("reloadGrid");
                xGridCertTable(
                  JSON.parse(
                    JSON.stringify(
                      window.changeApplyList.customerBeProxyIdentityVoList
                        .valueAfter
                    )
                  ),
                  $("#certTable")
                );
                $t_table.find("input,select").removeAttr("disabled");
              }
            }
            if ($t_flag.prop("name") == "customerQualityAgreementVoList") {
              // window.changeApplyList.customerBeProxyIdentityVoList&&
              if (
                window.changeApplyList.customerQualityAgreementVoList &&
                window.changeApplyList.customerQualityAgreementVoList.valueAfter
              ) {
                $("#table1").XGrid("clearGridData").trigger("reloadGrid");
                xGridTable1(
                  JSON.parse(
                    JSON.stringify(
                      window.changeApplyList.customerQualityAgreementVoList
                        .valueAfter
                    )
                  ),
                  $("#table1")
                );
                $t_table.find("input,select").removeAttr("disabled");
              }
            }
            // }
            break;
          case "button":
            // 开票信息
            if ($t_flag.parents("form").attr("id") == "billInfo_form") {
              $t_flag.removeAttr("readonly"); //解除禁用
              $t_flag.removeAttr("disabled"); //解除禁用
              let tags = $t_flag.parents(".row").find(".fileIcon_p");
              let kaipiaoPicArr = [];
              $(tags).each(function (index, item) {
                let kaipiaoPicObj = {};
                kaipiaoPicObj.enclosureName = "非独立经营证明" + (index + 1);
                kaipiaoPicObj.url = $(item).attr("data-imgurl");
                kaipiaoPicArr.push(kaipiaoPicObj);
              });

              selObj = {
                [varObj.name]: "customerEnclosureVoList", //字段名
                [varObj.beforeValue]: JSON.stringify(kaipiaoPicArr), //改前值
              };
              //从 变更对象 添加该属性
              window.changeApply_["customerEnclosureVoList"] = selObj;
            }
            break;
        }
      })
      .on("click", ".changeApplyBtn", function () {   //点击保存图标
        debugger;
        /*数据保存*/
        var $t = $(this),
          selObj;
        // 2018-09-14 RM
        //数据保存时将 X 号，+ 号隐藏
        $t.parent()
          .find("div")
          .find("span[data-role=remove]")
          .removeClass("disBlock")
          .addClass("disNone");
        $t.parent()
          .find("div")
          .find("button")
          .removeClass("disBlock")
          .addClass("disNone");
        var new_table1RowData;
        if ($t.parent().find("div").find("input").prev("span").length > 0) {
          $t.parent()
            .find("div")
            .find("input")
            .removeClass("disBlock")
            .addClass("disNone");
        }
        if ($t.length && $t.parents("form").attr("id") == "customerOtherInfo") {
          if (
            $("#table2").length &&
            $t.parent().find("a").prop("name") == "customerDelegationFileVoList"
          ) {
            new_table1RowData = $("#table2").XGrid("getRowData");
            if (new_table1RowData && new_table1RowData.length <= 0) {
              utils
                .dialog({
                  title: "提示",
                  content: "客户委托书不能为空",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
            // else if(new_table1RowData&&new_table1RowData.length>=2){
            //         utils.dialog({
            //             title: '提示',
            //             content: '客户委托书有且只能存在一条记录',
            //             okValue: '确定',
            //             ok: function () {
            //             }
            //         }).showModal();
            //         return false;
            // }
          }
          if (
            $("#certTable").length &&
            $t.parent().find("a").prop("name") ==
              "customerBeProxyIdentityVoList"
          ) {
            new_table1RowData = $("#certTable").XGrid("getRowData");
            if (new_table1RowData && new_table1RowData.length <= 0) {
              utils
                .dialog({
                  title: "提示",
                  content: "被委托人身份证不能为空",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
            // else  if(new_table1RowData&&new_table1RowData.length>=2){
            //     utils.dialog({
            //         title: '提示',
            //         content: '被委托人身份证有且只能存在一条记录',
            //         okValue: '确定',
            //         ok: function () {
            //         }
            //     }).showModal();
            //     return false;
            // }
          }
        }
        //检查手机号
        // var _val = $t.parents(".row").find(".tel-label").val();
        // if(_val=="a"){
        //   if($t.parents(".row").find(".tel-label").hasClass("require")){
        //     $t.siblings(".tel-part").removeClass().addClass("form-control Filter_SpaceAndStrLen_Class tel-part {validate:{ required :true, isTel:true}}");
        //   } else {
        //     $t.siblings(".tel-part").addClass("{validate:{ isTel:true}}");
        //   }
        // }
        var _val = $t.parents(".tel-wrap").find(".tel-label").val();
        //必填项
        if($t.parents(".tel-wrap").find(".tel-label").hasClass("require")){
          if(_val==""){
            utils
                  .dialog({
                    title: "提示",
                    content: "请选择联系方式",
                  })
                  .showModal();
                  return false;
          }
        }
        if (
          $t.parents("form").attr("id") == "supplierOrganBaseApprovalRecordVO"
        ) {
          if (
            $t.parent().find("input[changeapplyflag=supplierExtendItemVOList]")
              .length
          ) {
            var c = $(".class_payStyle input[name=parentCode]:checked");
            var b = $(".class_balanceStyle input[name=parentCode]:checked");
            if (c.length < 1 || b.length < 1) {
              utils
                .dialog({
                  title: "提示",
                  content: "付款方式和结算方式请至少各选择一项",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }

            // 付款方式。结算方式，当checkbox 被选中时在给相应的文本框添加 检验。否则， 如果全部校验的话 表单检验无法通过
            var payStyle_ckeck = $(".class_payStyle input[name=parentCode]");
            $(payStyle_ckeck).each(function (index, item) {
              if ($(item).val() != "1015") {
                // 承兑开户账户: 1015
                $(item)
                  .parent()
                  .siblings()
                  .find("input[type=number]")
                  [$(item).is(":checked") ? "addClass" : "removeClass"](
                    "  { validate:{ floattwoAndMax :true, required :true,}}"
                  );
                $(item)
                  .parent()
                  .siblings()
                  .find("input[type=text]")
                  [$(item).is(":checked") ? "addClass" : "removeClass"](
                    "  {validate:{ required :true,}}"
                  );
              } else {
                $(item)
                  .parent()
                  .siblings()
                  .find("input[type=number]")
                  [$(item).is(":checked") ? "addClass" : "removeClass"](
                    "  { validate:{ required :true,}}"
                  );
                $(item)
                  .parent()
                  .siblings()
                  .find("input[type=text]")
                  [$(item).is(":checked") ? "addClass" : "removeClass"](
                    "  {validate:{ required :true,}}"
                  );
              }

              $(item)
                .parent()
                .siblings()
                .find("input[type=number]")
                [$(item).is(":checked") ? "removeClass" : "addClass"]("ignore"); //未选中时忽略校验
              $(item)
                .parent()
                .siblings()
                .find("input[type=text]")
                [$(item).is(":checked") ? "removeClass" : "addClass"]("ignore");
            });
            var balanceStyle_ckeck = $(
              ".class_balanceStyle input[name=parentCode]"
            );
            let vis = checkMoreTimeInput();
              if (!vis) {
                utils
                  .dialog({
                    content: "对账账期/实效实结结算时间录入存在问题，请核实后重新提交！",
                    quickClose: true,
                    timeout: 2000,
                  })
                  .showModal();
                return false;
              }
            $(balanceStyle_ckeck).each(function (index, item) {
              var a = $(item).parent().parent().siblings().find(".childName");
              $(a).each(function (ind, ite) {
                if ($(ite).text() == "结算日" || $(ite).text() == "支付日") {
                  var b = $(ite).parent().siblings()[1];
                  $(b)[$(item).is(":checked") ? "addClass" : "removeClass"](
                    "  { validate:{ isMinMax:true, required :true,}}"
                  );
                  $(b)[$(item).is(":checked") ? "removeClass" : "addClass"](
                    "ignore"
                  ); //未选中时忽略校验
                } else if (
                  $(ite).text() == "压批次数" ||
                  $(ite).text() == "授信金额"
                ) {
                  var b = $(ite).parent().siblings()[1];
                  $(b)[$(item).is(":checked") ? "addClass" : "removeClass"](
                    "  { validate:{ integer :true, required :true,}}"
                  );
                  $(b)[$(item).is(":checked") ? "removeClass" : "addClass"](
                    "ignore"
                  );
                } else {
                  var b = $(ite).parent().siblings()[1];
                  $(b)[$(item).is(":checked") ? "addClass" : "removeClass"](
                    "  { validate:{ integer :true, required :true,}}"
                  );
                  $(b)[$(item).is(":checked") ? "removeClass" : "addClass"](
                    "ignore"
                  );
                }
              });
            });
            var balanceStyle_ckeck = $(
              ".class_balanceStyle input[name=parentCode]"
            );

            if (!validform("supplierOrganBaseApprovalRecordVO").form()) {
              return false;
            }
          }
        }

        //商品资料变更申请
        if (
          $t.parents(".panel-heading").next().find("form").attr("id") ==
          "approvalFileVo_01"
        ) {
          var rowData = $("#X_Table").XGrid("getRowData");
          if (rowData.length > 0) {
            var allSel = $("#X_Table").find("select");
            $.each(allSel, function (i, v) {
              if ($(v).val() != 0) {
                var arr = $(v).parents("td").nextAll().find("input");
                $.each(arr, function (ind, ele) {
                  if (ind != 1) {
                    // 核准内容不是必填项
                    $(ele)[$(v).val() != "0" ? "addClass" : "removeClass"](
                      "{validate:{ required :true}}"
                    );
                  }
                });
              } else {
                var arr = $(v).parents("td").nextAll().find("input");
                $.each(arr, function (ind, ele) {
                  if (ind != 1) {
                    // 核准内容不是必填项
                    $(ele)["removeClass"]("{validate:{ required :true}}");
                  } else {
                    $(ele)["addClass"]("ignore");
                  }
                });
              }
            });
            if (!validform("approvalFileVo_01").form()) {
              return false;
            }
          }
        }
        // 商品质管属性变更申请
        if ($t.siblings("[changeapplyflag]").prop("name") == "approvalFile") {
          let resStatus = inputValidate();
          if (!resStatus) return false;
        }
        /**
         * RM 2018-10-24
         * 商品首营申请和商品资料变更申请两个页面，
         * 当商品大类选择中药饮片时，基础属性中以下字段为必填项：
         * 商品编码、通用名、规格、包装单位、剂型、生产厂商、品牌厂家、通用名助记码、存储条件、质量标准、产地、
         * 一二三级分类、商品大类、所属经营范围、限销状态、限采状态、停用状态、是否税务优惠、税务分类编码、进项税率、
         * 销项税率，其他字段为非必填；
         * 选择中药饮片时，小包装条码和处方分类也必填
         *
         * 商品大类不为中药饮片时，必填项不变
         */
        //1.商品资料变更申请
        var $SPDL_falg =
          $t.siblings("[changeapplyflag]").length > 0
            ? $t.siblings("[changeapplyflag]")
            : $t.parent("div").find("[changeapplyflag]");
        // var isZYYP_text = false;

        var largeCategoryVal = $("#largeCategoryVal").attr("data-value"),
          scopeOfOperationVal = $("#scopeOfOperationVal").attr("data-value");
        var is_ZYYP = largeCategoryVal == "中药饮片",
          true_zYYP = scopeOfOperationVal == "中药饮片";
        $("input[name=qualityStandard]")[is_ZYYP ? "addClass" : "removeClass"](
          " {validate:{ required :true}}"
        ); // 质量标准
        $("input[name=qualityStandard]")[is_ZYYP ? "removeClass" : "addClass"](
          "ignore"
        ); //当 商品大类非中药饮片时，忽略 质量标准的校验

        if ($SPDL_falg.prop("name") == "producingArea") {
          // 产地
          if (
            (is_ZYYP || true_zYYP) &&
            $("input[name=producingArea]").prev("div").find("span").length < 1
          ) {
            utils
              .dialog({
                title: "提示",
                content: "产地不能为空",
                width: 300,
                height: 30,
                okValue: "确定",
                ok: function () {},
              })
              .showModal();
            return false;
          }
        }

        if ($SPDL_falg.prop("name") == "smallPackageBarCode") {
          // 小包装条码
          if (
            is_ZYYP &&
            $("input[name=smallPackageBarCode]").prev("div").find("span")
              .length < 1
          ) {
            utils
              .dialog({
                title: "提示",
                content: "小包装条码不能为空",
                width: 300,
                height: 30,
                okValue: "确定",
                ok: function () {},
              })
              .showModal();
            return false;
          }
        }

        //var is_ZYYP = ($SPDL_falg.attr('data-value') == '中药饮片');
        //var largeCategoryVal = $('#largeCategoryVal').attr('data-value');
        //var is_ZYYP = true;// (largeCategoryVal == '中药饮片');
        //if($SPDL_falg.attr('changeapplyflag') =='largeCategory'){ // 商品大类
        // 要忽略校验的ID   ,有效期,'有效期'      ,'养护周期'
        var id_ignoreArr = ["indate", "indateType", "maintenancePeriod"];
        // 要忽略校验的NAME       //批准文号      ,标准库ID          ,是否委托
        var inpName_ignoreArr = [
          "approvalNumber",
          "standardProductId",
          "entrustmentProduction",
        ];
        add_remove_require(id_ignoreArr, "TYPE_ID");
        add_remove_require(inpName_ignoreArr, "TYPE_NAME");
        //}

        function add_remove_require(arr, arrType) {
          $(arr).each(function (i, v) {
            if (arrType == "TYPE_ID") {
              $("#" + v)[is_ZYYP ? "addClass" : "removeClass"]("ignore");
            } else {
              $("input[name=" + v + "]")[is_ZYYP ? "addClass" : "removeClass"](
                "ignore"
              );
            }
          });
        }
        if (is_ZYYP) {
          $("input[name=indate]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .find("i")
            .remove(); // 有效期
          $("#maintenancePeriod")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .find("i")
            .remove(); // 养护周期
          $("input[name=approvalNumber]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .find("i")
            .remove(); // 批准文号
          $("input[name=standardProductId]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .find("i")
            .remove(); // 标准库ID
          $("input[name=entrustmentProduction]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .find("i")
            .remove(); //是否委托生产
          $("input[name=producingArea]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html('<i class="text-require">*  </i>产地'); // 产地
          $("input[name=qualityStandard]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html('<i class="text-require">*  </i>质量标准'); // 质量标准
        } else {
          $("input[name=indate]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html('<i class="text-require">*  </i>有效期'); // 有效期
          $("#maintenancePeriod")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html('<i class="text-require">*  </i>养护周期'); // 养护周期
          $("input[name=approvalNumber]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html('<i class="text-require">*  </i>批准文号'); // 批准文号
          $("input[name=standardProductId]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html('<i class="text-require">*  </i>标准库ID'); // 标准库ID
          $("input[name=entrustmentProduction]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html('<i class="text-require">*  </i>是否委托生产'); //是否委托生产
          $("input[name=producingArea]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html("产地"); // 产地
          $("input[name=qualityStandard]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html("质量标准"); // 质量标准
        }
        if (true_zYYP) {
          $("input[name=producingArea]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html('<i class="text-require">*  </i>产地'); // 产地
        } else {
          $("input[name=producingArea]")
            .parents(".input-group")
            .find("div")
            .eq(0)
            .html("产地"); // 产地
        }

        //2. 商品首營申请的校验  代码，在 productApply.js

        /**
         * RM 2019-01-23
         * 商品大类为  药食同源   食品  中药饮片  中药材  保健食品时，进项税率，销项税率的radio项有四个，显示的选项 需要切换
         */
        // 进项税率是否正在编辑状态
        let editState_entryTaxRate = $("#id_entryTaxRate")
          .find(".changeApplyBtn")
          .css("display");
        // 销项税率是否正在编辑状态
        let editState_salesRate = $("#id_salesRate")
          .find(".changeApplyBtn")
          .css("display");
        let hasFourRadioLab = [
          "药食同源",
          "食品",
          "中药饮片",
          "中药材",
          "保健食品",
        ];
        let hasFourRadioVal = ["91", "48", "51", "50", "49"];
        let finalVal_largeCategory = changeApply["largeCategory"]
          ? changeApply["largeCategory"]["afterText"]
          : largeCategoryVal;
        let beforeText_largeCategory = "";
        beforeText_largeCategory = changeApply["largeCategory"]
          ? changeApply["largeCategory"]["changeBefore"]
          : $("#largeCategoryVal").prev().val();
        let bool = hasFourRadioLab.some((item) => {
          return item == finalVal_largeCategory; //
        });
        let beforeBool = hasFourRadioVal.some((item) => {
          return item == beforeText_largeCategory; //
        });
        if (bool) {
          // 如果当前商品大类选中项为这几项 需要显示 10% 的选项
          //  if(editState_entryTaxRate != 'none'){ // 进项税率 编辑状态
          //      $('#id_entryTaxRate').find('.productrate').show()
          //  }else{
          //      $('#id_entryTaxRate').find('.productrate').hide()
          //  }
          //  if(editState_salesRate != 'none'){ // 销项税率 编辑状态
          //      $('#id_salesRate').find('.productrate').show()
          //  }else{
          //      $('#id_salesRate').find('.productrate').hide()
          // }
          //  if(beforeBool){
          //      $('#id_entryTaxRate').find('.productrate').show()
          //      $('#id_salesRate').find('.productrate').show()
          //  }else{
          //      $('#id_entryTaxRate').find('.productrate').hide()
          //      $('#id_salesRate').find('.productrate').hide()
          //  }
        } else {
          // 如果当前商品大类选中项不为这几项，需要隐藏掉10% 的选项
          if (editState_entryTaxRate != "none") {
            //当前进项税率 又处于编辑状态 设置16%项为默认选中项
            $("#id_entryTaxRate").find(".productrate").hide();
            if (
              $("#id_entryTaxRate").find("[name=entryTaxRate]:checked").val() ==
              ""
            ) {
              $("#id_entryTaxRate")
                .find("[name=entryTaxRate]")
                .eq(-1)
                .prop("checked", true);
            }
          }
          if (editState_salesRate != "none") {
            // 销项税率 编辑状态 设置16%项为默认选中项
            $("#id_salesRate").find(".productrate").hide();
            if (
              $("#id_salesRate").find("[name=entryTaxRate]:checked").val() == ""
            ) {
              $("#id_salesRate")
                .find("[name=entryTaxRate]")
                .eq(-1)
                .prop("checked", true);
            }
          }
        }
        if (beforeBool) {
          // 修改前的值为这五项，点击保存后 需要将10%显示
          if ($t.parent().find("[name=largeCategory]").length < 1) {
            $("#id_entryTaxRate").find(".productrate").show();
            $("#id_salesRate").find(".productrate").show();
          }
        } else {
          if ($t.parent().find("[name=largeCategory]").length < 1) {
            $("#id_entryTaxRate").find(".productrate").hide();
            $("#id_salesRate").find(".productrate").hide();
          }
        }
        //END

        //供应商资料变更申请
        if ($t.parents("form").attr("id") == "form_pzwj") {
          var rowData3 = $("#table3").XGrid("getRowData");
          var rowData4 = $("#table4").XGrid("getRowData");
          if (rowData3.length > 0 || rowData4.length > 0) {
            if (!validform("form_pzwj").form()) {
              return false;
            }
            //校验重复
            var valDiffOpt = ["报告年份"];
            var signDateArr = [],
              proxyOderNoArr = [];
            var bigArr = [],
              intFlag = 0;
            var table4RowData = $("#table4").XGrid("getRowData");
            if (table4RowData.length > 0) {
              for (var item of table4RowData) {
                proxyOderNoArr.push(item.reportDate);
              }
              bigArr.push(proxyOderNoArr);
            }
            //开始验证每一项都不能有重复项
            var flag = false;
            if (bigArr.length > 0) {
              for (var i = 0; i < bigArr.length; i++) {
                for (var j = 0; j < bigArr[i].length; j++) {
                  var temp = bigArr[i][j];
                  var count = 0;
                  if (bigArr[i].length > 1) {
                    //判斷当前数据有几条，如果只有一条就没啥可比较的，肯定不会有重复项。直接返回 就行
                    for (var k = 0; k < bigArr[i].length; k++) {
                      var temp2 = bigArr[i][k];
                      if (temp == temp2) {
                        count++;
                        intFlag = i;
                      }
                    }
                    //由于中间又一次会跟自己本身比较所有这里要判断count>=2
                    if (count >= 2) {
                      utils
                        .dialog({
                          title: "提示",
                          content: valDiffOpt[intFlag] + "有重复值存在！！！",
                          okValue: "确定",
                          ok: function () {},
                        })
                        .showModal();
                      return flag;
                    }
                  }
                }
              }
            }
          }
        }
        //供应商运营属性变更申请 质量保证协议 客户委托书
        if ($t.parents("form").attr("id") == "form_zlbzxy_khwts") {
          var rowData1 = $("#table1").XGrid("getRowData");
          var rowData2 = $("#table2").XGrid("getRowData");
          var rowData3 = $("#table3").XGrid("getRowData");
          if (rowData1.length > 0 || rowData2.length > 0) {
            if (!validform("form_zlbzxy_khwts").form()) {
              return false;
            }
            //校验重复
            var valDiffOpt = ["签订日期", "委托书编号"];
            var signDateArr = [],
              proxyOderNoArr = [];
            var bigArr = [],
              intFlag = 0;
            var table1RowData = $("#table1").XGrid("getRowData");
            var table2RowData = $("#table2").XGrid("getRowData");
            if (table1RowData.length > 0) {
              for (var item of table1RowData) {
                signDateArr.push(item.signDate);
              }
              bigArr.push(signDateArr);
            }
            if (table2RowData.length > 0) {
              for (var item of table2RowData) {
                proxyOderNoArr.push(item.proxyOderNo);
              }
              bigArr.push(proxyOderNoArr);
            }
            //开始验证每一项都不能有重复项
            var flag = false;
            if (bigArr.length > 0) {
              for (var i = 0; i < bigArr.length; i++) {
                for (var j = 0; j < bigArr[i].length; j++) {
                  var temp = bigArr[i][j];
                  var count = 0;
                  if (bigArr[i].length > 1) {
                    //判斷当前数据有几条，如果只有一条就没啥可比较的，肯定不会有重复项。直接返回 就行
                    for (var k = 0; k < bigArr[i].length; k++) {
                      var temp2 = bigArr[i][k];
                      if (temp == temp2) {
                        count++;
                        intFlag = i;
                      }
                    }
                    //由于中间又一次会跟自己本身比较所有这里要判断count>=2
                    if (count >= 2) {
                      utils
                        .dialog({
                          title: "提示",
                          content: valDiffOpt[intFlag] + "有重复值存在！！！",
                          okValue: "确定",
                          ok: function () {},
                        })
                        .showModal();
                      return flag;
                    }
                  }
                }
              }
            }
            /**
             * 20200114
             * 线上紧急需求，药品经营许可证和GSP
             */
            // 批准文件-证书类型绑定校验
            // var certificateOption = [
            //     {
            //         certificateIds:[30,32],
            //         msg:'《药品经营许可证》与《GSP证书》必须同时录入'
            //     },
            //     {
            //         certificateIds:[29,31],
            //         msg:'《药品生产许可证》与《GMP证书》必须同时录入'
            //     }
            // ];
            // var certificateMsg = '';
            // certificateOption.forEach(function (item) {
            //     if(rowData3.some(function (row) {
            //         return item.certificateIds[0] == row.certificateId;
            //     }) !== rowData3.some(function (row) {
            //         return item.certificateIds[1] == row.certificateId;
            //     })){
            //         certificateMsg = item.msg;
            //     }
            // });
            // if(certificateMsg){
            //     utils.dialog({
            //         title: '提示',
            //         content: certificateMsg,
            //         okValue: "确定",
            //         ok: function () {}
            //     }).showModal();
            //     return false;
            // }
          }
        }

        //客户资料变更  资料存档  保存时 form校验
        if ($t.parents("form").attr("id") == "customerOtherInfo") {
          if (!validform("customerOtherInfo").form()) {
            return false;
          }
        }
        // 商品运营属性变更  业务类型属性  保存时 form校验
        if ($t.parents("div").attr("id") == "id_productChannel") {
          if (!validform("productChannelVo_01").form()) {
            return false;
          }
        }

        /**
         * 下面三元 写法是针对 客户资料的账单地址
         * 和 供应商资料变更
         */
        var $t_flag =
          $t.siblings("[changeapplyflag]").length > 0
            ? $t.siblings("[changeapplyflag]")
            : $t.parent("div").find("[changeapplyflag]"); //标记元素
        var supplierApprovalFileListFlag;
        if ($t_flag.prop("name") == "supplierApprovalFileList") {
          var $t_ul_nav_content = $t_flag.closest("ul").next(".nav-content");
          var $t_table = $t_ul_nav_content.find("table.XGridUI:visible");
          $.each($t_table.XGrid("getRowData"), function (i, v) {
            if (v.scopeofoperationVo != "null") {
              if (
                v.scopeofoperationVo &&
                v.supplierApprovalFileBusinessScopeVOList.length <= 0
              ) {
                supplierApprovalFileListFlag =
                  "第" + (i + 1) + "行经营范围未选";
                return;
              }
            }
          });
        }
        if ($t_flag.prop("name") == "customerApprovalFileVoList") {
          var $t_ul_nav_content = $t_flag.closest("ul").next(".nav-content");
          var $t_table = $t_ul_nav_content.find("table.XGridUI:visible");
          let alltrs = $t_table.find("tr").not(":eq(0)"); //表格中当前所有的行
          let tdTitles = [
            "证书类型",
            "证书编号",
            "发证机关",
            "发证日期",
            "有效期至",
          ];
          let tdWords = [
            "credentialTypeId",
            "credentialCode",
            "certificationOffice",
            "certificationDate",
            "validityDate",
          ];
          let _arr = [];
          $(alltrs).each(function (index, item) {
            for (let i = 0, len = tdTitles.length; i < len; i++) {
              if (
                alltrs
                  .eq(index)
                  .find("[name=" + tdWords[i] + "]")
                  .val() == ""
              ) {
                supplierApprovalFileListFlag =
                  "第" + (index + 1) + "行" + tdTitles[i] + "的值不能为空.";
                return;
              }
            }
            if (
              _arr.indexOf(
                alltrs.eq(index).find("[name=credentialTypeId]").val()
              ) != -1
            ) {
              supplierApprovalFileListFlag = "证书类型重复";
              return;
            } else {
              _arr.push(alltrs.eq(index).find("[name=credentialTypeId]").val());
            }
          });
          $.each($t_table.XGrid("getRowData"), function (i, v) {
            /**
             *客户变更详情 的GSP证书 没有经营范围，不做判断
             */
            if (v.scopeofoperationVo == "[]") {
              v.scopeofoperationVo = "";
            }
            if (v.scopeofoperationVo != "null") {
              if (
                v.scopeofoperationVo &&
                v.customerBusinessScopeVoList.length <= 0
              ) {
                supplierApprovalFileListFlag =
                  "第" + (i + 1) + "行经营范围未选";
                return;
              }
            }
          });
          /**
           * 20200114
           * 线上紧急需求，药品经营许可证和GSP
           */
          // 批准文件-证书类型绑定校验
          // var rowData3 = $('#table3').XGrid('getRowData');
          // var credentialTypeOption = [
          //     {
          //         credentialTypeId:[21,22],
          //         msg:'《药品经营许可证》与《GSP证书》必须同时录入'
          //     }
          // ];
          // var credentialTypeMsg = '';
          // credentialTypeOption.forEach(function (item) {
          //     if(rowData3.some(function (row) {
          //         return item.credentialTypeId[0] == row.credentialTypeId;
          //     }) !== rowData3.some(function (row) {
          //         return item.credentialTypeId[1] == row.credentialTypeId;
          //     })){
          //         credentialTypeMsg = item.msg;
          //     }
          // });
          // if(credentialTypeMsg){
          //     utils.dialog({
          //         title: '提示',
          //         content: credentialTypeMsg,
          //         okValue: "确定",
          //         ok: function () {}
          //     }).showModal();
          //     return false;
          // }
        }
        if (supplierApprovalFileListFlag) {
          utils
            .dialog({
              content: supplierApprovalFileListFlag,
              timeout: 2500,
              quickClose: true,
            })
            .show();
          return;
        }
        /**
         *fb
         */
        if ($t_flag.prop("name") == "nature") {
          if ($t_flag.val() != "3") {
            $("[name=businessLicenseNum]").removeClass("ignore");
            $("[name=businessLicenseNum]")
              .prev()
              .html('<i class="text-require">*  </i>营业执照号'); //
            $("[name=businessLicenseNum]").removeClass(
              "{validate:{ num_word_or_blank : true}}"
            );
            $("[name=businessLicenseNum]").addClass(
              "{validate:{ required :true, num_word: true}}"
            );
            $("[name=businessLicenseNum]").metadata()["validate"] = {
              required: true,
              num_word_or_blank: true,
            };
          } else {
            // $('[name=businessLicenseNum]').addClass('ignore');
            $("[name=businessLicenseNum]").prev().html("营业执照号"); // <i class="text-require">*  </i>
            $("[name=businessLicenseNum]").removeClass(
              "{validate:{ required :true, num_word_or_blank : true}}"
            );
            $("[name=businessLicenseNum]").addClass(
              "{validate:{ num_word_or_blank : true}}"
            );
            $("[name=businessLicenseNum]").metadata()["validate"] = {
              num_word_or_blank: true,
            };
            if (window.changeApply) {
              if (window.changeApply["businessLicenseNum"]) {
                window.changeApply["businessLicenseNum"]["valueAfter"] = "";
                window.changeApply["businessLicenseNum"]["afterText"] = "";
              } else {
                $("[name=businessLicenseNum]")
                  .next("i")
                  .addClass("yulanInput_after");
                window.changeApply["businessLicenseNum"] = {
                  afterText: "",
                  changeStatus: "1",
                  columnValue: "businessLicenseNum",
                  valueAfter: "",
                  valueBefore: $("[name=businessLicenseNum]").val(),
                };
              }
              if (window.changeApply["taxRegistryNumber"]) {
                window.changeApply["taxRegistryNumber"]["valueAfter"] = "";
                window.changeApply["taxRegistryNumber"]["afterText"] = "";
              } else {
                $("[name=taxRegistryNumber]")
                  .next("i")
                  .addClass("yulanInput_after");
                window.changeApply["taxRegistryNumber"] = {
                  afterText: "",
                  changeStatus: "1",
                  columnValue: "taxRegistryNumber",
                  valueAfter: "",
                  valueBefore: $("[name=taxRegistryNumber]").val(),
                };
              }
            }
          }
        }

        //2018.9.6,RL,bug2427,begin
        //客户委托书,授权范围的校验
        if ($t_flag.prop("name") == "supplierClientProxyOrderList") {
          // var supplierClientProxyOrderListFlag;
          // if(table2RowData && table2RowData.length){
          //     $.each(table2RowData,function(i,v){
          //         if((v.authorityType=="1" && v.supplierClientProxyBusinessScopeVOList && v.supplierClientProxyBusinessScopeVOList!='[]' && v.supplierClientProxyBusinessScopeVOList.length) || (v.authorityType=="0" && v.supplierClientProxyProductVOList && v.supplierClientProxyProductVOList!='[]' && v.supplierClientProxyProductVOList.length) || (v.authorityType=="2" && v.supplierClientProxyTypeVOList && v.supplierClientProxyTypeVOList!='[]' && v.supplierClientProxyTypeVOList.length)){
          //
          //         }else{
          //             supplierClientProxyOrderListFlag='第'+(i+1)+'行授权范围未选';
          //             return false;
          //         }
          //     });
          //
          //     if(supplierClientProxyOrderListFlag){
          //         utils.dialog({
          //             content:supplierClientProxyOrderListFlag,
          //             timeout: 2500,
          //             quickClose: true,
          //         }).show();
          //         return;
          //     }
          // }
          if ($("#supplierTypeId").val() != "58") {
            let len = $("#table2").XGrid("getRowData").length;
            if (len == 0) {
              utils
                .dialog({
                  title: "提示",
                  content: "客户委托书不能为空.",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
          }
          if ($("#table2").XGrid("getRowData").length != 0) {
            let _status = checkAuthorityType();
            if (!_status) {
              utils
                .dialog({
                  title: "提示",
                  content: "同一个供应商，授权类型只能存在一种",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
          }
        }
        //2018.9.6,RL,bug2427,end

        /**
         * 客户资料变更 结算方式
         * @type {string}
         */
        if ($t.parents("form").attr("id") == "customerApplVo") {
          if ($t.parent().text().indexOf("结算方式") > 0) {
            var c = $(".paymentSettlement input[name=parentCode]:checked");
            if (c.length < 1) {
              utils
                .dialog({
                  title: "提示",
                  content: "财务合账和账期请至少各选择一项",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }

            var childCodeInpsValFlag = false;
            if (c.next().text()) {
              var childCodeInps = $(c)
                .parents(".paymentSettlement")
                .find("input[type=number]");
              $(childCodeInps).each(function (index, item) {
                if ($(item).val() == "") {
                  childCodeInpsValFlag = true;
                  utils
                    .dialog({
                      title: "提示",
                      content: "账期字段不能为空.",
                      okValue: "确定",
                      ok: function () {},
                    })
                    .showModal();
                  return false;
                }
              });
            }
            if (childCodeInpsValFlag) {
              return false;
            }
          }

          // 客户性质
          if ($t.parent().find("select").attr("id") == "customerNature") {
            if ($("#customerNature").val() == 0) {
              utils
                .dialog({
                  title: "提示",
                  content: "客户性质请选择有效值.",
                  okValue: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
          }
        }

        var $t_type =
          $t_flag.prop("type") == "number" ||
          $t_flag.prop("type") == "select-one"
            ? "text"
            : $t_flag.prop("type");
        switch ($t_type) {
          case "radio": //为单选
            console.log("单选");
            $t.siblings("input[type=radio]").attr("readonly", "readonly"); //禁用
            $t.siblings("input[type=radio]").attr("disabled", "disabled"); //禁用
            var $t_flag_radio = $t.siblings("input[type=radio]:checked");
            if ($t.parents("form").attr("id") == "customerApplVo") {
              // 客户资料变更 发票类型判断
              if (!$t_flag_radio.val()) {
                var title = $t
                  .parents(".input-group")
                  .find(".require")
                  .text()
                  .substring(1)
                  .trim();
                utils
                  .dialog({
                    content: title + "保存值不能为空",
                    timeout: 2000,
                    quickClose: true,
                  })
                  .show();
                return false;
              }
              // 客户资料变更
              var $customer_yyzzh = $('input[name="businessLicenseNum"]'), //营业执照号
                $customer_zzjgdmzh = $('input[name="organizationCodeNumber"]'), //组织机构代码证号
                $customer_swdjzh = $('input[name="taxRegistryNumber"]'), //税务登记证号
                $customer_sfszhy = $('input[name="threeInOne"]:checked'); //是否三证合一， 获取当前选中的项的值

              if (
                $t_flag_radio.val() == "1" &&
                $t_flag_radio.attr("name") != "invoiceType"
              ) {
                $.changeApply_ThreeInone(
                  $customer_yyzzh,
                  $customer_zzjgdmzh,
                  $customer_swdjzh,
                  $t_flag_radio.val(),
                  varObj,
                  "threeInOne"
                );
              }
              //$('[name="' + $t_flag.prop('name') + '"][value="' + window.changeApply_[$t_flag.prop('name')][varObj.beforeValue] + '"]').prop('checked', 'checked').trigger('input'); //回显改前值
            }
            if ($t.parents("form").attr("id") == "billInfo_form") {
              // 开票信息 发票类型保存的时候 对营业执照地址和电话回显是否需要必填
              if (
                $t_flag.prop("name") == "invoiceType" &&
                $t_flag_radio.val() == "1"
              ) {
                // 发票类型 准备 修改为电子普通发票。
                // 这个时候 需要回显 营业执照地址和电话为必填
                $("#billInfo_form [name=businessLicenseAddress]")["addClass"](
                  "{validate:{ required :true}}"
                );
                $("#billInfo_form [name=businessLicenseAddress]")
                  .prev()
                  .html('<i class="text-require">*  </i>营业执照地址');
                $("#billInfo_form [name=billingPhone]")
                  .prev()
                  .html('<i class="text-require">*  </i>电话');
                $("#billInfo_form [name=billingPhone]")
                  .removeClass()
                  .addClass(
                    "form-control {validate:{required :true, isTel :true}}"
                  );
              } else if (
                $t_flag.prop("name") == "invoiceType" &&
                $t_flag_radio.val() == "2"
              ) {
                $("#billInfo_form [name=businessLicenseAddress]")[
                  "removeClass"
                ]("{validate:{ required :true}}");
                $("#billInfo_form [name=businessLicenseAddress]")
                  .prev()
                  .html("营业执照地址");
                $("#billInfo_form [name=billingPhone]").prev().html("电话");
                $("#billInfo_form [name=billingPhone]")
                  .removeClass()
                  .addClass("form-control {validate:{isTel :true}}");
              }
              // 是否独立核算的值修改 保存的时候。当保存的值为是的时候，需要清空非独立经营证明的附件值，因为是的时候非独立经营证明是不支持上传的
              if ($t_flag.prop("name") == "isIndependent") {
                // && $t_flag_radio.val() == '1'
                $t_flag.parents("form").removeAttr("supportEdit");
                $("[name=isIndependent]").removeAttr("readonly,disabled");
                $("[name=isIndependent]").eq(1).click();
                $("[name=isIndependent]").eq(0).click();
                disable();
                if (
                  window.changeApply &&
                  window.changeApply["customerEnclosureVoList"]
                ) {
                  //delete window.changeApply['customerEnclosureVoList'];
                  window.changeApply["customerEnclosureVoList"]["afterText"] =
                    "[]";
                  window.changeApply["customerEnclosureVoList"]["valueAfter"] =
                    "[]";
                  let before_fileIcons =
                    window.changeApply["customerEnclosureVoList"][
                      "valueBefore"
                    ];
                  if (JSON.parse(before_fileIcons).length > 0) {
                    let _arr = JSON.parse(before_fileIcons);
                    let ss = "";
                    for (let i = 0; i < _arr.length; i++) {
                      ss +=
                        `
                                                <div class="fileIcon_div" data-index="` +
                        (i + 1) +
                        `">
                                                    <p class="btn btn-info btn-xs fileIcon_p" data-imgurl="` +
                        _arr[i].url +
                        `" style="position: relative; margin-right: 10px;">
                                                        <span class="glyphicon glyphicon-picture"></span>
                                                    </p>
                                                    <div class="btn-group fileIcon_showTag" style="position: absolute; top:-23px ; left: -27px; width: 72px; height: 22px;  display: none;">
                                                        <button type="button" class="btn btn-default btn-xs btn_fileView" onclick="btn_fileView(this)">预览</button>
                                                        <button type="button" class="btn btn-default btn-xs btn_fileClean" disabled>清空</button>
                                                    </div>
                                                </div>
                                            `;
                    }
                    $(".btn_customerFile").prev().before(ss);
                  }
                  if (
                    window.changeApply &&
                    window.changeApply["isIndependent"] &&
                    window.changeApply["isIndependent"]["valueBefore"] == "1"
                  ) {
                    $("#billInfo_form [name=customerEnclosureVoList]")
                      .parents(".input-group")
                      .find(".yulan")
                      .removeClass("yulanInput_after");
                  }
                }
              }
            }
            if ($t_flag.prop("name") == "keyConservationCategories") {
              let val = $("[name=keyConservationCategories]:checked").val();
              if (val == "1") {
                window.changeApply["maintenancePeriod"] = {
                  [varObj.name]: "maintenancePeriod",
                  [varObj.afterValue]: "30",
                  [varObj.afterText]: "30",
                  changeBefore: "90",
                  [varObj.status]: "1",
                };
              } else {
                window.changeApply["maintenancePeriod"] = {
                  [varObj.name]: "maintenancePeriod",
                  [varObj.afterValue]: "90",
                  [varObj.afterText]: "90",
                  changeBefore: "30",
                  [varObj.status]: "1",
                };
              }
              $("[name=maintenancePeriod]")
                .next(".yulanInput")
                .addClass("yulanInput_after");
            }
            selObj = {
              [varObj.name]: $t_flag.prop("name"),
              [varObj.afterValue]: $t_flag_radio.val(),
              afterText: $t_flag_radio.nextAll("label").eq(0).text(),
              [varObj.status]: "1",
            };
            if (
              $t_flag_radio.val() == "1" &&
              $t_flag_radio.attr("name") != "invoiceType" &&
              $t_flag_radio.attr("name") != "invoiceYn"
            ) {
              $.changeApply_ThreeInone(
                $customer_yyzzh,
                $customer_zzjgdmzh,
                $customer_swdjzh,
                $t_flag_radio.val(),
                varObj,
                "threeInOne"
              );
            }
            //$('[name="' + $t_flag.prop('name') + '"][value="' + window.changeApply_[$t_flag.prop('name')][varObj.beforeValue] + '"]').prop('checked', 'checked').trigger('input'); //回显改前值
            /**
             * RM 2018-10-7
             * 页面数据填充进来，当radio 没有选中项。修改完后回显，选中项需要清空
             */
            if (window.changeApply_[$t_flag.prop("name")][varObj.beforeValue]) {
              $(
                '[name="' +
                  $t_flag.prop("name") +
                  '"][value="' +
                  window.changeApply_[$t_flag.prop("name")][
                    varObj.beforeValue
                  ] +
                  '"]'
              )
                .prop("checked", "checked")
                .trigger("input"); //回显改前值
            } else {
              $('[name="' + $t_flag.prop("name") + '"]').prop("checked", false);
            }

            //$.extend(selObj, window.changeApply_[$t_flag.prop('name')]);
            selObj[varObj.beforeValue] =
              window.changeApply_[$t_flag.prop("name")][varObj.beforeValue];
            window.changeApply[$t_flag.prop("name")] = selObj;
            /**
             * // 写在这儿是因为上面开票信息 发票类型当改为电子时，下面电话等修改的时候就不用必填，发票类型修改为增值税时下面电话等需要必填
             * 但是当发票类型由增值税改为电子又改回增值税时保存的时候没有对电话等添加校验规则，导致编辑完发票类型再去修改电话等时，就没有必填校验了
             * 写在这儿的代码是当保存操作快结束，初始值都回显了的之后再通过获取值来设置是否必填的规则
             */
            if ($t_flag.parents("form").attr("id") == "billInfo_form") {
              if ($("[name=invoiceType]:checked").val() == "2") {
                //。
                // 这个时候 需要回显 营业执照地址和电话为必填
                $("#billInfo_form [name=businessLicenseAddress]")["addClass"](
                  "{validate:{ required :true}}"
                );
                $("#billInfo_form [name=businessLicenseAddress]")
                  .prev()
                  .html('<i class="text-require">*  </i>营业执照地址');
                $("#billInfo_form [name=billingPhone]")
                  .prev()
                  .html('<i class="text-require">*  </i>电话');
                $("#billInfo_form [name=billingPhone]")
                  .removeClass()
                  .addClass(
                    "form-control {validate:{required :true, isTel :true}}"
                  );
              } else if ($("[name=invoiceType]:checked").val() == "1" ||
                $("[name=invoiceType]:checked").val() == "3" || 
                $("[name=invoiceType]:checked").val() == "5" ) {
                $("#billInfo_form [name=businessLicenseAddress]")[
                  "removeClass"
                ]("{validate:{ required :true}}");
                $("#billInfo_form [name=businessLicenseAddress]")
                  .prev()
                  .html("营业执照地址");
                $("#billInfo_form [name=billingPhone]").prev().html("电话");
                $("#billInfo_form [name=billingPhone]")
                  .removeClass()
                  .addClass("form-control {validate:{isTel :true}}");
              }
            }
            break;
          case "checkbox": //为复选
            var $t_flag_checkbox = $t.siblings("input[type=checkbox]:checked"),
              $t_checkedText_ary = [],
              $t_checked_ary = [];
            $t_flag_checkbox.each(function (i, v) {
              if (v.checked) {
                $t_checked_ary.push($(v).val());
                $t_checkedText_ary.push($(v).nextAll("label").eq(0).html());
              }
            });
            /**
             * 2019-02-28 RM
             *  经营类别为必填项，不能为空
             */
            // if($t.parents('form').attr('id') == 'customerApplVo'){
            //     if($t_checkedText_ary.length == 0 ){
            //         var title = $t.parents('.input-group').find('.require').text().substring(1).trim();
            //         utils.dialog({
            //             content:  title + '保存值不能为空',
            //             timeout: 2000,
            //             quickClose: true,
            //         }).show();
            //         return false;
            //     }
            // };
            $t.siblings("input[type=checkbox]").attr("readonly", "readonly"); //禁用
            $t.siblings("input[type=checkbox]").attr("disabled", "disabled"); //禁用
            selObj = {
              [varObj.name]: $t_flag.prop("name"), //字段名
              [varObj.afterValue]: $t_checked_ary.join(","), //改后值
              afterText: $t_checkedText_ary.join(","),
              [varObj.status]: "1",
            };

            $('[name="' + $t_flag.prop("name") + '"]').prop("checked", false);
            var baseCheckedData =
              window.changeApply_[$t_flag.prop("name")][
                varObj.beforeValue
              ].split(",");
            for (var i = 0; i < baseCheckedData.length; i++) {
              $(
                '[name="' +
                  $t_flag.prop("name") +
                  '"][value="' +
                  baseCheckedData[i] +
                  '"]'
              ).prop("checked", "checked"); //回显改前值
            }

            //从 变更对象 添加该属性
            selObj[varObj.beforeValue] =
              window.changeApply_[$t_flag.prop("name")][varObj.beforeValue];
            window.changeApply[$t_flag.prop("name")] = selObj;
            break;
          case "textarea": //为文本域sitian
            $t_flag.attr("readonly", "readonly"); //解除禁用
            $t_flag.attr("disabled", "disabled"); //解除禁用
            selObj = {
              [varObj.name]: $t_flag.prop("name"), //字段名
              [varObj.afterValue]: $t_flag.val(), //改后值
              afterText: $t_flag.val(),
              [varObj.status]: "1",
            };

            //从 变更对象 添加该属性
            selObj[varObj.beforeValue] =
              window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
            window.changeApply[$t_flag.prop("name")] = selObj;
            var textareaVal =
              window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
            $t_flag.val(textareaVal);
            break;
          case "text": //为文本
            if($t_flag.attr("name")=="contactPerson"){
                window.myContactPerson = true;  //使用全局变量的js文件 supplierOrganBaseApprovalInsert.js
            }
            var $t_flag_hidden = $("#" + $t_flag.attr("changeapplyflag"));
            // 生产厂商-地址显示
            if ($t_flag.attr("changeapplyflag") == "manufacturer") {
              $("#manufacturerAddress").val(base_manufacturerAddress);
            }
            if ($t_flag.attr("changeapplyflag") == "entrustmentManufacturer") {
              $("#entrustmentManufacturerAddress").val(
                base_entrustmentManufacturerAddress
              );
            }
            //如果ID是隐藏域
            if ($t_flag.attr("changeapplyflag") && $t_flag_hidden.length) {
              //if (!$t_flag_hidden.val()) {
              //2018-09-12 RM : 资料变更保存时，如果不是必填项，不用做判断
              if (
                !$t_flag_hidden.val() &&
                $t_flag.parent().find(".input-group-addon").hasClass("require")
              ) {
                utils
                  .dialog({
                    content: "保存值不能为空",
                    timeout: 2000,
                    quickClose: true,
                  })
                  .show();
                return;
              }

              $t_flag.attr("disabled", "disabled"); //禁用
              selObj = {
                [varObj.name]: $t_flag_hidden.prop("name"),
                [varObj.afterValue]: $t_flag_hidden.val(),
                afterText:
                  $t_flag.prop("type") == "select-one"
                    ? $t_flag.find("option:checked").text()
                    : $t_flag.val(),
                [varObj.status]: "1",
              };

              $t_flag_hidden
                .val(
                  window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                )
                .trigger("input");
            } else if ($t_flag.attr("changeapplyflag") == "registerList") {
              //如果为注册列表
              var $t_flag_selects = $t_flag
                .parents("div.row")
                .eq(0)
                .find("select");

              //2018.9.5,RL,bug2415,begin
              if (!validform($t_flag.parents("form").attr("id")).form()) {
                return false;
              }
              //2018.9.5,RL,bug2415,end

              $t_flag_selects.attr("readonly", "readonly"); //地址下拉框禁用
              $t_flag_selects.attr("disabled", "disabled"); //禁用
              $t_flag.attr("disabled", "disabled"); // 地址   区 文本输入框  禁用
              $t_flag.attr("readonly", "readonly");

              //当前新地址保存
              if ($t_flag_selects.length > 3) {
                $t_flag_selects
                  .eq(3)
                  .attr(
                    "curStreetName",
                    $t_flag_selects.eq(3).find("option:selected").text()
                  );
              } else {
                $t_flag_selects
                  .eq(2)
                  .attr(
                    "curStreetName",
                    $t_flag_selects.eq(2).find("option:selected").text()
                  );
              }
              selObj = {
                [varObj.name]: "registerList",
                [varObj.afterValue]: {
                  registerProvince: $t_flag_selects.eq(0).val(),
                  registerCity: $t_flag_selects.eq(1).val(),
                  registerArea: $t_flag_selects.eq(2).val(),
                  registerStreet: $t_flag_selects.eq(3).val(), // 街道下拉框
                  registerDetail: $t_flag.val(),
                },
                [varObj.afterText]:
                  $t_flag_selects.length > 3
                    ? $t_flag_selects.eq(3).find("option:selected").text()
                    : $t_flag_selects.eq(2).find("option:selected").text(), // 街道下拉框,
                [varObj.status]: "1",
              };
              //當前街道 保存
              // $t_flag_selects.eq(3).attr('curStreetName',$t_flag_selects.eq(3).find("option:selected").text());
              //注册地址回显
              // $t_flag_selects.eq(0).val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].registerProvince);
              // $t_flag_selects.eq(1).val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].registerCity);
              // $t_flag_selects.eq(2).val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].registerArea);
              // $t_flag_selects.eq(3).val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].registerStreet); // 街道数据回显
              // $t_flag.val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].registerDetail);
              // initDistpicker();

              let addressSelIdObj = [
                {
                  nextNodeWrap: "#provinceSel_wrap",
                  nextNodeName: "registerProvince",
                  nextNodeId: "province1",
                },
                {
                  nextNodeWrap: "#citySel_wrap",
                  nextNodeName: "registerCity",
                  nextNodeId: "registerCity",
                },
                {
                  nextNodeWrap: "#districtSel_wrap",
                  nextNodeName: "registerArea",
                  nextNodeId: "district1",
                },
                {
                  nextNodeWrap: "#streetSel_wrap",
                  nextNodeName: "registerStreet",
                  nextNodeId: "street1",
                },
              ];
              let registerPromiseArray = [];
              utils.setAllProDom(
                "#provinceSel_wrap",
                addressSelIdObj,
                "#registerBox",
                true,
                function () {
                  // 注册地址有值回显
                  let _registerHiddenVal = [
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .registerProvince,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .registerCity,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .registerArea,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .registerStreet,
                  ];
                  $t_flag.val(
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .registerDetail
                  );
                  // if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
                  // _registerHiddenVal.splice(_registerHiddenVal.length - 1);
                  $("#" + addressSelIdObj[0]["nextNodeId"]).addClass(
                    "{validate:{ required :true}}"
                  );
                  $("#" + addressSelIdObj[0]["nextNodeId"]).val(
                    _registerHiddenVal[0]
                  );
                  $("#" + addressSelIdObj[0]["nextNodeId"]).attr(
                    "data-value",
                    _registerHiddenVal[0]
                  );
                  for (let i = 1; i < _registerHiddenVal.length; i++) {
                    registerPromiseArray.push(
                      utils.setAddressReturnVal(_registerHiddenVal[i - 1])
                    );
                  }
                  Promise.all(registerPromiseArray).then((data) => {
                    for (let i = 0; i < data.length; i++) {
                      $("#" + addressSelIdObj[i + 1]["nextNodeId"]).html(
                        data[i]
                      );
                      $("#" + addressSelIdObj[i + 1]["nextNodeId"]).attr(
                        "data-depth",
                        i + 2
                      );
                      $("#" + addressSelIdObj[i + 1]["nextNodeId"]).val(
                        _registerHiddenVal[i + 1]
                      );
                      $("#" + addressSelIdObj[i + 1]["nextNodeId"]).prop(
                        "disabled",
                        true
                      );
                      if (i == 2) {
                        $(
                          "#" + addressSelIdObj[i + 1]["nextNodeId"]
                        ).removeClass("{validate:{ required :true}}");
                      }
                    }
                  });
                  disable();
                }
              );
            } else if (
              $t_flag.attr("changeapplyflag") == "customerDeliveryAddressVoList"
            ) {
              //如果为收货地址
              var $t_flag_selects = $t_flag
                .parents("div.row")
                .eq(0)
                .find("select");

              //2018.9.5,RL,bug2415,begin
              if (!validform($t_flag.parents("form").attr("id")).form()) {
                return false;
              }
              //2018.9.5,RL,bug2415,end

              $t_flag_selects.attr("readonly", "readonly"); //地址下拉框禁用
              $t_flag_selects.attr("disabled", "disabled"); //禁用
              $t_flag.attr("disabled", "disabled"); // 地址   区 文本输入框  禁用
              $t_flag.attr("readonly", "readonly");

              //当前新地址保存
              if ($t_flag_selects.length > 3) {
                $t_flag_selects
                  .eq(3)
                  .attr(
                    "curStreetName",
                    $t_flag_selects.eq(3).find("option:selected").text()
                  );
              } else {
                $t_flag_selects
                  .eq(2)
                  .attr(
                    "curStreetName",
                    $t_flag_selects.eq(2).find("option:selected").text()
                  );
              }
              selObj = {
                [varObj.name]: "customerDeliveryAddressVoList",
                //WN: 20200827 保存时，使用编辑前的原街道地址作为 beforeText
                [varObj.beforeText]: window.deliveryAddressBeforeStreetText,
                [varObj.beforeValue]: {
                  province2:
                    window.changeApply_["customerDeliveryAddressVoList"][
                      varObj.beforeValue
                    ].province2,
                  shippingAddressCityId:
                    window.changeApply_["customerDeliveryAddressVoList"][
                      varObj.beforeValue
                    ].shippingAddressCityId,
                  shippingAddressDistrictId:
                    window.changeApply_["customerDeliveryAddressVoList"][
                      varObj.beforeValue
                    ].shippingAddressDistrictId,
                  shippingAddressStreetId:
                    window.changeApply_["customerDeliveryAddressVoList"][
                      varObj.beforeValue
                    ].shippingAddressStreetId,
                  shippingAddressInput:
                    window.changeApply_["customerDeliveryAddressVoList"][
                      varObj.beforeValue
                    ].shippingAddressInput,
                },
                [varObj.afterValue]: {
                  province2: $t_flag_selects.eq(0).val(),
                  shippingAddressCityId: $t_flag_selects.eq(1).val(),
                  shippingAddressDistrictId: $t_flag_selects.eq(2).val(),
                  shippingAddressStreetId: $t_flag_selects.eq(3).val(), // 街道下拉框
                  shippingAddressInput: $t_flag.val(),
                },
                [varObj.afterText]:
                  $t_flag_selects.length > 3
                    ? $t_flag_selects.eq(3).find("option:selected").text()
                    : $t_flag_selects.eq(2).find("option:selected").text(), // 街道下拉框,
                [varObj.status]: "1",
              };
              let shippingAddressObj = [
                {
                  nextNodeWrap: "#saProvinceSel_wrap",
                  nextNodeName: "province2",
                  nextNodeId: "province2",
                },
                {
                  nextNodeWrap: "#sacitySel_wrap",
                  nextNodeName: "shippingAddressCityId",
                  nextNodeId: "shippingAddressCityId",
                },
                {
                  nextNodeWrap: "#sadistrictSel_wrap",
                  nextNodeName: "shippingAddressDistrictId",
                  nextNodeId: "shippingAddressDistrictId",
                },
                {
                  nextNodeWrap: "#sastreetSel_wrap",
                  nextNodeName: "shippingAddressStreetId",
                  nextNodeId: "shippingAddressStreetId",
                },
              ];
              let registerPromiseArray = [];
              utils.setAllProDom(
                "#saProvinceSel_wrap",
                shippingAddressObj,
                "#registerBox",
                true,
                function () {
                  // 收货地址有值回显
                  let _registerHiddenVal = [
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .province2,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .shippingAddressCityId,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .shippingAddressDistrictId,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .shippingAddressStreetId,
                  ];
                  $t_flag.val(
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .shippingAddressInput
                  );
                  // if (!_registerHiddenVal) _registerHiddenVal = ['','','',''];
                  // _registerHiddenVal.splice(_registerHiddenVal.length - 1);
                  $("#" + shippingAddressObj[0]["nextNodeId"]).addClass(
                    "{validate:{ required :true}}"
                  );
                  $("#" + shippingAddressObj[0]["nextNodeId"]).val(
                    _registerHiddenVal[0]
                  );
                  $("#" + shippingAddressObj[0]["nextNodeId"]).attr(
                    "data-value",
                    _registerHiddenVal[0]
                  );
                  for (let i = 1; i < _registerHiddenVal.length; i++) {
                    registerPromiseArray.push(
                      utils.setAddressReturnVal(_registerHiddenVal[i - 1])
                    );
                  }
                  Promise.all(registerPromiseArray).then((data) => {
                    for (let i = 0; i < data.length; i++) {
                      $("#" + shippingAddressObj[i + 1]["nextNodeId"]).html(
                        data[i]
                      );
                      $("#" + shippingAddressObj[i + 1]["nextNodeId"]).attr(
                        "data-depth",
                        i + 2
                      );
                      $("#" + shippingAddressObj[i + 1]["nextNodeId"]).val(
                        _registerHiddenVal[i + 1]
                      );
                      $("#" + shippingAddressObj[i + 1]["nextNodeId"]).prop(
                        "disabled",
                        true
                      );
                      if (i == 2) {
                        $(
                          "#" + shippingAddressObj[i + 1]["nextNodeId"]
                        ).removeClass("{validate:{ required :true}}");
                      }
                    }
                  });
                  disable();
                }
              );
            } else if (
              $t_flag.attr("changeapplyflag") == "customerBillingAddressVoList"
            ) {
              //如果账单 地址列表
              var $t_flag_selects = $t_flag
                .parents("div.row")
                .eq(0)
                .find("select");
              var arr = [],
                streetArr = [];
              //组合账单数据
              /**
               * RM 2018-10-06
               * 在有多条账单地址的前提下，当编辑完某一条地址保存后查看，会把所有的账单地址的数据都显示出来，因为 上面那个changeapplyflag 有多个，
               * .billList 也会有多个全部取出，
               * 改完后只取当前修改项
               */
              //var billLists = $t_flag.parents('div.row').eq(0).parents('.billList');
              var billLists = $t_flag.parents("#billAddress").find(".billList");
              if (billLists.length > 0) {
                $(billLists).each(function (index) {
                  var se = $(this).find("select");
                  var inp = $(this).find(".text-inp");
                  var repertoryProvince = se
                    .eq(0)
                    .find("option:selected")
                    .val();
                  if (repertoryProvince && repertoryProvince != "") {
                    //
                    var repertoryCity = se.eq(1).find("option:selected").val();
                    var repertoryArea = se.eq(2).find("option:selected").val();
                    var registerStreet = se.eq(3).find("option:selected").val();
                    var repertoryDetail = inp.val();
                    //当前新地址保存
                    se.eq(3).attr(
                      "curStreetName",
                      se.eq(3).find("option:selected").text()
                    );
                    arr[index] = {
                      repertoryProvince: repertoryProvince,
                      repertoryCity: repertoryCity,
                      repertoryArea: repertoryArea,
                      registerStreet: registerStreet,
                      repertoryDetail: repertoryDetail,
                    };
                    streetArr[index] = se.eq(3).find("option:selected").text();
                  }
                });
              }

              //2018.9.5,RL,bug2415,begin
              if (!validform($t_flag.parents("form").attr("id")).form()) {
                return false;
              }
              //2018.9.5,RL,bug2415,end
              $t_flag_selects.attr("readonly", "readonly"); //地址下拉框禁用
              $t_flag_selects.attr("disabled", "disabled"); //禁用
              $t_flag.attr("disabled", "disabled"); // 地址   区 文本输入框  禁用
              $t_flag.attr("readonly", "readonly");
              selObj = {
                [varObj.name]: "customerBillingAddressVoList",
                [varObj.afterValue]: arr,
                [varObj.afterText]: streetArr,
                [varObj.status]: "1",
              };

              //账单地址保存回显
              var AddressVOList =
                window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
              var html = "";
              if (null != AddressVOList) {
                var len = AddressVOList.length;
                $("#billAddress").find(".billList").remove();
                var obj = distpickerHTML(len);
                let billAddressSelIdObj = [];
                let _billHiddenValArr = AddressVOList.map((item) => {
                  return [
                    item.repertoryProvince,
                    item.repertoryCity,
                    item.repertoryArea,
                    item.registerStreet,
                  ];
                });
                let _storgeHiddenVal = AddressVOList.map((item) => {
                  return [
                    item.repertoryProvince,
                    item.repertoryCity,
                    item.repertoryArea,
                    item.registerStreet,
                    item.repertoryDetail,
                  ];
                });
                $(obj.radomInit).each((index, item) => {
                  let _arr = [
                    {
                      nextNodeWrap: "#billProvinceSel_wrap_" + item,
                      nextNodeName: "billingProvinceId_" + item,
                      nextNodeId: "billingProvinceId_" + item,
                    },
                    {
                      nextNodeWrap: "#billCitySel_wrap_" + item,
                      nextNodeName: "billingCityId_" + item,
                      nextNodeId: "billingCityId_" + item,
                    },
                    {
                      nextNodeWrap: "#billDistrictSel_wrap_" + item,
                      nextNodeName: "billingDistrictId_" + item,
                      nextNodeId: "billingDistrictId_" + item,
                    },
                    {
                      nextNodeWrap: "#billStreetSel_wrap_" + item,
                      nextNodeName: "billingStreetId_" + item,
                      nextNodeId: "billingStreetId_" + item,
                    },
                  ];
                  billAddressSelIdObj.push(_arr);
                });
                $("#billAddress").find(".billList").remove();
                $("#billAddress").append(obj.html);

                $(obj.radomInit).each((index, item) => {
                  let billPromiseArray = [];
                  utils.setAllProDom(
                    "#billProvinceSel_wrap_" + obj.radomInit[index],
                    billAddressSelIdObj[index],
                    "#billingBox_" + obj.radomInit[index],
                    true,
                    function () {
                      $("#" + billAddressSelIdObj[index][0]["nextNodeId"]).val(
                        _billHiddenValArr[index][0]
                      );
                      $("#" + billAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find("[name=billingAddress]")
                        .val(_storgeHiddenVal[index][4]);
                      $("#" + billAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find("[name=billingAddress]")
                        .attr(
                          "changeApplyFlag",
                          "customerBillingAddressVoList"
                        );
                      $("#" + billAddressSelIdObj[index][0]["nextNodeId"]).prop(
                        "disabled",
                        true
                      );
                      $("#" + billAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find("[name=billingAddress]")
                        .prop("disabled", true);
                      $("#" + billAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find(".btn")
                        .css("display", "none");
                      disable();
                      $("#auditOpinion").removeAttr("disabled");
                      for (
                        let ind = 1;
                        ind < _billHiddenValArr[index].length;
                        ind++
                      ) {
                        billPromiseArray.push(
                          utils.setAddressReturnVal(
                            _billHiddenValArr[index][ind - 1]
                          )
                        );
                      }
                      let allSelArr = billAddressSelIdObj[index]
                        .flat()
                        .map((item, index) => {
                          if (index != 0) {
                            return item["nextNodeId"];
                          }
                        })
                        .filter((item) => {
                          return item;
                        });
                      Promise.all(billPromiseArray).then((data) => {
                        for (let i = 0; i < data.length; i++) {
                          $("#" + allSelArr[i]).html(data[i]);
                          $("#" + allSelArr[i]).val(
                            _billHiddenValArr[index][i + 1]
                          );
                          $("#" + allSelArr[i]).prop("disabled", true);
                        }
                      });
                    }
                  );
                  // 设置点点点
                  if (index == 0) {
                    var _htm =
                      '<i class="yulan yulanInput yulanInput_after" style="position:absolute;top: 8px;right:-17px;display: inline;"></i><i changeApplyBtn class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>';
                    $("#" + billAddressSelIdObj[index][0]["nextNodeId"])
                      .parents(".distpicker")
                      .find("[name=billingAddress]")
                      .after(_htm);
                  }
                });
              } else {
                let obj = distpickerHTML();
                $("#billAddress").html(obj.html);
                let billAddressSelIdObj = [
                  {
                    nextNodeWrap: "#billProvinceSel_wrap_" + obj.radomInit[0],
                    nextNodeName: "billingProvinceId_" + obj.radomInit[0],
                    nextNodeId: "billingProvinceId_" + obj.radomInit[0],
                  },
                  {
                    nextNodeWrap: "#billCitySel_wrap_" + obj.radomInit[0],
                    nextNodeName: "billingCityId_" + obj.radomInit[0],
                    nextNodeId: "billingCityId_" + obj.radomInit[0],
                  },
                  {
                    nextNodeWrap: "#billDistrictSel_wrap_" + obj.radomInit[0],
                    nextNodeName: "billingDistrictId_" + obj.radomInit[0],
                    nextNodeId: "billingDistrictId_" + obj.radomInit[0],
                  },
                  {
                    nextNodeWrap: "#billStreetSel_wrap_" + obj.radomInit[0],
                    nextNodeName: "billingStreetId_" + obj.radomInit[0],
                    nextNodeId: "billingStreetId_" + obj.radomInit[0],
                  },
                ];
                utils.setAllProDom(
                  "#billProvinceSel_wrap_" + obj.radomInit[0],
                  billAddressSelIdObj,
                  "#billingBox",
                  true,
                  function () {
                    $("#" + billAddressSelIdObj[index][0]["nextNodeId"])
                      .parents(".distpicker")
                      .find("[name=billingAddress]")
                      .attr("changeApplyFlag", "customerBillingAddressVoList");
                    disable();
                  }
                );
              }
              /**
               *  RM 2018-10-07
               *  下面取oldBillList 是因为在每个街道下拉框都有存值，后面回显的时候会把之前的html 清空，清空之后再去回显的话回显不出来，
               *  因为 这个属性oldstreetname 都已经没有了，所以先提前存起来，后面重新放置html 之后再追加进去，
               *  这样调用initDistpicker 方法的时候就能拿到oldstreetname属性值
               */
              // var oldBillList =  $("#billAddress").find('.billList'), oldStreetName = [], oldStreetValArr = [];
              // if(oldBillList.length > 0){
              //     $(oldBillList).each(function(){
              //         oldStreetName.push($(this).find('select').eq(3).attr('oldstreetname'));
              //         var thisSels =  $(this).find('select');
              //         var that = this;
              //         $(thisSels).each(function(i,v){
              //             oldStreetValArr.push($(that).find('select').eq(i).attr('oldstreetVal'));
              //         })
              //     })
              // }
              // $("#billAddress").html(html);
              // var _htm = '<i class="yulan yulanInput yulanInput_after" style="position:absolute;top: 8px;right:-5px; display: block;"></i><i changeapplybtn="" class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>'
              // $("#billAddress").find('.billList').eq(0).append(_htm)
              /**
               *
               */
              // var billLists = $('#billAddress').find('.billList');//$t_flag.parents('div.row').eq(0).parents('.billList');
              // if(billLists.length > 0){
              //     var allSels = $(billLists).find('select');
              //     $(allSels).each(function(index,item){
              //         $(this).attr('oldstreetVal',oldStreetValArr[index]);
              //     })
              //     $(billLists).each(function (index) {
              //         initCitys($(this),'billingProvinceId','billingCityId','billingDistrictId');
              //         $(this).find("select[name='billingStreetId']").attr('oldstreetname',oldStreetName[index]); // 在新的html中给街道下拉框赋值进去之前存的属性值
              //         if(AddressVOList[index]){
              //             $(this).find("select[name='billingProvinceId']").attr("data-value", AddressVOList[index].repertoryProvince).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("select[name='billingCityId']").attr("data-value", AddressVOList[index].repertoryCity).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("select[name='billingDistrictId']").attr("data-value", AddressVOList[index].repertoryArea).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("select[name='billingStreetId']").attr("data-value", AddressVOList[index].registerStreet).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("input[name='billingAddress']").val(AddressVOList[index].repertoryDetail).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("input[name='billingAddress']").removeClass('disNone');
              //         }
              //     });
              // }
              $(".addbill").hide();
              $(".removeDepot").hide();
              // $('[data-toggle="distpicker"]').distpicker();
              // initDistpicker();
              //$('[changeapplyflag="customerBillingAddressVoList"]').after('<i class="yulan yulanInput yulanInput_after" style="position: absolute;right: -18px; display: inherit;"></i><i changeApplyBtn class="changeApplyBtn"></i>');
              $('[changeapplyflag="customerBillingAddressVoList"]')
                .parent(".form-group")
                .css("position", "initial");
            } else if (
              $t_flag.attr("changeapplyflag") == "customerStorageAddressVOList"
            ) {
              //如果为客户资料仓库的地址
              var $t_flag_selects = $t_flag
                .parents("div.row")
                .eq(0)
                .find("select");

              //2018.9.5,RL,bug2415,begin
              if (!validform($t_flag.parents("form").attr("id")).form()) {
                return false;
              }
              //2018.9.5,RL,bug2415,end

              $t_flag_selects.attr("readonly", "readonly"); //地址下拉框禁用
              $t_flag_selects.attr("disabled", "disabled"); //禁用
              $t_flag.attr("disabled", "disabled"); // 地址   区 文本输入框  禁用
              $t_flag.attr("readonly", "readonly");

              selObj = {
                [varObj.name]: "customerStorageAddressVOList", ///customerStorageAddressList
                [varObj.afterValue]: {
                  repertoryProvince: $t_flag_selects.eq(0).val(),
                  repertoryCity: $t_flag_selects.eq(1).val(),
                  repertoryArea: $t_flag_selects.eq(2).val(),
                  registerStreet: $t_flag_selects.eq(3).val(), // 街道下拉框
                  repertoryDetail: $t_flag.val(),
                },
                [varObj.afterText]: $t_flag_selects
                  .eq(3)
                  .find("option:selected")
                  .text(), // 街道下拉框,
                [varObj.status]: "1",
              };
              //當前街道 保存
              $t_flag_selects
                .eq(3)
                .attr(
                  "curStreetName",
                  $t_flag_selects.eq(3).find("option:selected").text()
                );
              //仓库库地址回显
              let storgeAddressSelIdObj = [
                {
                  nextNodeWrap: "#stoProvinceSel_wrap",
                  nextNodeName: "repertoryProvince",
                  nextNodeId: "repertoryProvince",
                },
                {
                  nextNodeWrap: "#stoCitySel_wrap",
                  nextNodeName: "repertoryCity",
                  nextNodeId: "repertoryCity",
                },
                {
                  nextNodeWrap: "#stoDistrictSel_wrap",
                  nextNodeName: "repertoryArea",
                  nextNodeId: "repertoryArea",
                },
                {
                  nextNodeWrap: "#stoStreetSel_wrap",
                  nextNodeName: "repertoryStreet",
                  nextNodeId: "repertoryStreet",
                },
              ];
              let storgePromiseArray = [];
              utils.setAllProDom(
                "#stoProvinceSel_wrap",
                storgeAddressSelIdObj,
                "#storageBox",
                true,
                function () {
                  // 仓库地址有值回显
                  let _storgeHiddenVal = [
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .repertoryProvince,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .repertoryCity,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .repertoryArea,
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .registerStreet,
                  ];
                  $t_flag.val(
                    window.changeApply_[selObj[varObj.name]][varObj.beforeValue]
                      .repertoryDetail
                  );
                  // if (!_storgeHiddenVal) _storgeHiddenVal = ['','','',''];
                  // _storgeHiddenVal.splice(_storgeHiddenVal.length - 1);
                  $("#" + storgeAddressSelIdObj[0]["nextNodeId"]).addClass(
                    "{validate:{ required :true}}"
                  );
                  $("#" + storgeAddressSelIdObj[0]["nextNodeId"]).val(
                    _storgeHiddenVal[0]
                  );
                  $("#" + storgeAddressSelIdObj[0]["nextNodeId"]).attr(
                    "data-value",
                    _storgeHiddenVal[0]
                  );
                  for (let i = 1; i < _storgeHiddenVal.length; i++) {
                    storgePromiseArray.push(
                      utils.setAddressReturnVal(_storgeHiddenVal[i - 1])
                    );
                  }
                  let allSelArr = storgeAddressSelIdObj
                    .flat()
                    .map((item, index) => {
                      if (index % 4 != 0) {
                        return item["nextNodeId"];
                      }
                    })
                    .filter((item) => {
                      return item;
                    });
                  Promise.all(storgePromiseArray).then((data) => {
                    for (let i = 0; i < data.length; i++) {
                      $("#" + allSelArr[i]).html(data[i]);
                      $("#" + allSelArr[i]).attr("data-depth", i + 2);
                      $("#" + allSelArr[i]).val(_storgeHiddenVal[i + 1]);
                      $("#" + allSelArr[i]).prop("disabled", true);
                      if (i == 2) {
                        $("#" + allSelArr[i]).removeClass(
                          "{validate:{ required :true}}"
                        );
                      }
                    }
                  });
                  disable();
                }
              );
              // $t_flag_selects.eq(0).val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].repertoryProvince);
              // $t_flag_selects.eq(1).val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].repertoryCity);
              // $t_flag_selects.eq(2).val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].repertoryArea);
              // $t_flag_selects.eq(3).val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].registerStreet); // 街道数据回显
              // $t_flag.val(window.changeApply_[selObj[varObj.name]][varObj.beforeValue].repertoryDetail);
              // initDistpicker();
            } else if (
              $t_flag.attr("changeapplyflag") ==
              "supplierRepertoryAddressVOList"
            ) {
              var $t_flag_selects = $t_flag
                .parents("div.row")
                .eq(0)
                .find("select");
              //如果供应商仓库地址列表
              var arr = [];
              //组合仓库数据
              $(".depotList").each(function (index) {
                var se = $(this).find("select");
                var inp = $(this).find(".text-inp");
                var repertoryProvince = se.eq(0).find("option:selected").val();
                var repertoryCity = se.eq(1).find("option:selected").val();
                var repertoryArea = se.eq(2).find("option:selected").val();
                var registerStreet = se.eq(3).find("option:selected").val();
                var repertoryDetail = inp.val();
                //当前新地址保存
                se.eq(3).attr(
                  "curStreetName",
                  se.eq(3).find("option:selected").text()
                );
                arr[index] = {
                  repertoryProvince: repertoryProvince,
                  repertoryCity: repertoryCity,
                  repertoryArea: repertoryArea,
                  registerStreet: registerStreet,
                  repertoryDetail: repertoryDetail,
                };
              });

              //2018.9.5,RL,bug2415,begin
              if (!validform($t_flag.parents("form").attr("id")).form()) {
                return false;
              }
              //2018.9.5,RL,bug2415,end
              $t_flag_selects.attr("readonly", "readonly"); //地址下拉框禁用
              $t_flag_selects.attr("disabled", "disabled"); //禁用
              $t_flag.attr("disabled", "disabled"); // 地址   区 文本输入框  禁用
              $t_flag.attr("readonly", "readonly");
              selObj = {
                [varObj.name]: "supplierRepertoryAddressVOList",
                [varObj.afterValue]: arr,
                [varObj.status]: "1",
              };

              //仓库地址保存回显
              var AddressVOList =
                window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
              var html = "";
              if (null != AddressVOList) {
                // var len = AddressVOList.length;
                // html = returnHTML(len);
                var len = AddressVOList.length;
                $("#depotAddress").find(".depotList").remove();
                var obj = distpickerHTML(len);
                let storgeAddressSelIdObj = [];
                let _storgeHiddenValArr = AddressVOList.map((item) => {
                  return [
                    item.repertoryProvince,
                    item.repertoryCity,
                    item.repertoryArea,
                  ];
                });
                let _storgeHiddenVal = AddressVOList.map((item) => {
                  return [
                    item.repertoryProvince,
                    item.repertoryCity,
                    item.repertoryArea,
                    item.repertoryDetail,
                  ];
                });
                $(obj.radomInit).each((index, item) => {
                  let _arr = [
                    {
                      nextNodeWrap: "#stoProvinceSel_wrap_" + item,
                      nextNodeName: "repertoryProvince_" + item,
                      nextNodeId: "repertoryProvince_" + item,
                    },
                    {
                      nextNodeWrap: "#stoCitySel_wrap_" + item,
                      nextNodeName: "repertoryCity_" + item,
                      nextNodeId: "repertoryCity_" + item,
                    },
                    {
                      nextNodeWrap: "#stoDistrictSel_wrap_" + item,
                      nextNodeName: "repertoryArea_" + item,
                      nextNodeId: "repertoryArea_" + item,
                    },
                  ];
                  storgeAddressSelIdObj.push(_arr);
                });
                $("#depotAddress").html(obj.html);
                $(obj.radomInit).each((index, item) => {
                  let storagePromiseArray = [];
                  // for (let ind = 1; ind < _storgeHiddenValArr[index].length; ind++) {
                  utils.setAllProDom(
                    "#stoProvinceSel_wrap_" + obj.radomInit[index],
                    storgeAddressSelIdObj[index],
                    "#storageBox_" + obj.radomInit[index],
                    true,
                    function () {
                      $(
                        "#" + storgeAddressSelIdObj[index][0]["nextNodeId"]
                      ).val(_storgeHiddenValArr[index][0]);
                      $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find("[name=repertoryDetail]")
                        .val(_storgeHiddenVal[index][3]);
                      $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find("[name=repertoryDetail]")
                        .attr(
                          "changeApplyFlag",
                          "supplierRepertoryAddressVOList"
                        );
                      $(
                        "#" + storgeAddressSelIdObj[index][0]["nextNodeId"]
                      ).prop("disabled", true);
                      $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find("[name=repertoryDetail]")
                        .prop("disabled", true);
                      $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
                        .parents(".distpicker")
                        .find(".btn")
                        .css("display", "none");
                      disable();
                      $("#auditOpinion").removeAttr("disabled");
                      for (
                        let ind = 1;
                        ind < _storgeHiddenValArr[index].length;
                        ind++
                      ) {
                        storagePromiseArray.push(
                          utils.setAddressReturnVal(
                            _storgeHiddenValArr[index][ind - 1]
                          )
                        );
                      }
                      let allSelArr = storgeAddressSelIdObj[index]
                        .flat()
                        .map((item, index) => {
                          if (index != 0) {
                            return item["nextNodeId"];
                          }
                        })
                        .filter((item) => {
                          return item;
                        });
                      Promise.all(storagePromiseArray).then((data) => {
                        for (let i = 0; i < data.length; i++) {
                          $("#" + allSelArr[i]).html(data[i]);
                          $("#" + allSelArr[i]).val(
                            _storgeHiddenValArr[index][i + 1]
                          );
                          $("#" + allSelArr[i]).prop("disabled", true);
                        }
                      });
                      // utils.setAddressReturnVal(_storgeHiddenValArr[index][ind-1]).then(str => {
                      //     $('#' + storgeAddressSelIdObj[index][ind]['nextNodeId']).html(str);
                      //     $('#' + storgeAddressSelIdObj[index][ind]['nextNodeId']).val(_storgeHiddenValArr[index][ind]);
                      //     $('#' + storgeAddressSelIdObj[index][ind]['nextNodeId']).prop('disabled', true);
                      // }).catch(err => {
                      //     console.log(err)
                      // });
                    }
                  );
                  // }
                  // 设置点点点
                  if (index == 0) {
                    var _htm =
                      '<i class="yulan yulanInput yulanInput_after" style="position:absolute;top: 8px;right:-17px;display: inline;"></i><i changeApplyBtn class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>';
                    $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
                      .parents(".distpicker")
                      .find("[name=repertoryDetail]")
                      .after(_htm);
                  }
                });
              } else {
                let obj = distpickerHTML();
                $("#depotAddress").html(obj.html);
                let storgeAddressSelIdObj = [
                  {
                    nextNodeWrap: "#stoProvinceSel_wrap_" + obj.radomInit[0],
                    nextNodeName: "repertoryProvince_" + obj.radomInit[0],
                    nextNodeId: "repertoryProvince_" + obj.radomInit[0],
                  },
                  {
                    nextNodeWrap: "#stoCitySel_wrap_" + obj.radomInit[0],
                    nextNodeName: "repertoryCity_" + obj.radomInit[0],
                    nextNodeId: "repertoryCity_" + obj.radomInit[0],
                  },
                  {
                    nextNodeWrap: "#stoDistrictSel_wrap_" + obj.radomInit[0],
                    nextNodeName: "repertoryArea_" + obj.radomInit[0],
                    nextNodeId: "repertoryArea_" + obj.radomInit[0],
                  },
                ];
                utils.setAllProDom(
                  "#stoProvinceSel_wrap_" + obj.radomInit[0],
                  storgeAddressSelIdObj,
                  "#storageBox_" + obj.radomInit[0],
                  true,
                  function () {
                    $("#" + storgeAddressSelIdObj[index][0]["nextNodeId"])
                      .parents(".distpicker")
                      .find("[name=repertoryDetail]")
                      .attr(
                        "changeApplyFlag",
                        "supplierRepertoryAddressVOList"
                      );
                    disable();
                  }
                );
              }
              // var oldDepotList =  $("#depotAddress").find('.depotList'), oldStreetName = [];
              // if(oldDepotList.length > 0){
              //     $(oldDepotList).each(function(){
              //         oldStreetName.push($(this).find('select').eq(2).attr('oldstreetname'));
              //     })
              // }
              // //$("#depotAddress").html(html);
              // /**
              //  * RM 2018-11-13
              //  * 页面结构调整之后代码修改：
              //  * 保存时数据回显的时候，先判断旧数据有几条仓库地址，当大于或者等于一条的时候，需要对下面一行的追加仓库地址的dom 结构
              //  * 如果旧数据只有一条仓库地址的话，把数据回填至
              //  */
              // if(oldDepotList.length >= 1){
              //     $("#depotAddress").parents('.row').next().find('.depotList').remove().append(html);
              // }
              // var _htm = '<i class="yulan yulanInput yulanInput_after" style="position:absolute;top: 8px;right:-5px; display: block;"></i><i changeapplybtn="" class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>'
              // //$("#depotAddress").find('.depotList').eq(0).append(_htm)
              // $(".depotList").each(function (index) {
              //     if(AddressVOList[index]){
              //         if($(this).find("select[name='storageProvinceId']").length > 0){
              //             $(this).find("select[name='storageProvinceId']").attr("data-value", AddressVOList[index].repertoryProvince).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("select[name='storageCityId']").attr("data-value", AddressVOList[index].repertoryCity).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("select[name='storageDistrictId']").attr("data-value", AddressVOList[index].repertoryArea).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("select[name='storageStreetId']").attr("data-value", AddressVOList[index].registerStreet).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("input[name='repertoryDetail']").val(AddressVOList[index].repertoryDetail).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;  供应商资料变更的仓库地址name 这个
              //             $(this).find("input[name='storageAddress']").val(AddressVOList[index].repertoryDetail).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;  客户资料变更的仓库地址name叫这个
              //         }else if($(this).find("select[name='repertoryProvince']").length > 0){
              //             //  供应商资料变更申请 仓库地址回显。
              //             $(this).find("select[name='repertoryProvince']").attr("data-value", AddressVOList[index].repertoryProvince).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("select[name='repertoryCity']").attr("data-value", AddressVOList[index].repertoryCity).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("select[name='repertoryArea']").attr("data-value", AddressVOList[index].repertoryArea).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;
              //             $(this).find("input[name='repertoryDetail']").val(AddressVOList[index].repertoryDetail).attr('readonly', 'readonly').attr('disabled', 'disabled'); //禁用;  供应商资料变更的仓库地址name 这个
              //         }
              //
              //     }
              // });
              // $('#addDepot').hide();
              // $('.removeDepot').hide();
              // $('[data-toggle="distpicker"]').distpicker();
              // initDistpicker();
              //$('[changeapplyflag="supplierRepertoryAddressVOList"]').after('<i class="yulan yulanInput yulanInput_after" style="position: absolute;right: -18px; display: inherit;"></i><i changeApplyBtn class="changeApplyBtn"></i>');
              //$t.hide().siblings('.yulan').show().addClass('yulanInput_after');
            } else if (
              $t_flag.attr("changeapplyflag") == "supplierManufactoryVOList"
            ) {
              // 供应商对应生产厂商
              if (!validform($t_flag.parents("form").attr("id")).form()) {
                return false;
              }
              //组合应生产厂商数据
              let ManufactoryListArr = [];
              $(".ManufactoryList").each((index, item) => {
                let hiddenInpVal = $(item)
                  .find("input[name^=Manufactory]")
                  .val();
                let inpVal = $(item).find("input[type=text]").val();
                ManufactoryListArr[index] = {
                  manufactoryId: hiddenInpVal,
                  manufactoryName: inpVal,
                };
              });
              let $t_flag_inps = $t_flag
                .parents("#manufacturer_row")
                .find("input[type=text]");
              $t_flag_inps.attr({ disabled: "disabled", readonly: "readonly" }); //禁用
              selObj = {
                [varObj.name]: "supplierManufactoryVOList",
                [varObj.afterValue]: ManufactoryListArr,
                [varObj.status]: "1",
              };
              //生产厂家保存回显
              var ManufactoryVOList =
                window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
              // todo
              var _htm =
                '<i class="yulan yulanInput yulanInput_after" style="position:absolute;top: 8px;right:-18px; display: block;"></i><i changeapplybtn="" class="changeApplyBtn" style="position:absolute;top: 8px;right:-17px"></i>';
              var ManufactoryHtml = "";
              let isReuire = false;
              isReuire = $("#supplierTypeId").val() == "55";
              if (null != ManufactoryVOList) {
                var len = ManufactoryVOList.length;
                for (let i = 0; i < len; i++) {
                  ManufactoryHtml += `<div class="col-md-6 ManufactoryList">
                                                        <div class="input-group">
                                                            <div class="input-group-addon ">${
                                                              isReuire
                                                                ? '<i class="text-require">*  </i>'
                                                                : ""
                                                            }对应生产厂商</div>
                                                            <div class="form-control form-inline distpicker">
                                                                <div>
                                                                    <div class="form-group col-md-11">
                                                                        <input type="hidden" id="Manufactory${i}" value="${
                    ManufactoryVOList[i]["manufactoryId"]
                  }"  name="Manufactory${i}"/>
                                                                        <input type="text" class="form-control  text-inp Filter_SpaceAndFiveStrLen_Class ${
                                                                          $(
                                                                            "#supplierTypeId"
                                                                          ).val() ==
                                                                          "55"
                                                                            ? ""
                                                                            : ""
                                                                        }" disabled readonly value="${
                    ManufactoryVOList[i]["manufactoryName"]
                  }"   id="Manufactory${i}Val" name="Manufactory${i}Val" ${
                    i == 0 ? 'changeApplyFlag="supplierManufactoryVOList"' : ""
                  }  />
                                                                        ${
                                                                          i == 0
                                                                            ? _htm
                                                                            : ""
                                                                        }
                                                                    </div>
                                                                    <div class="form-group btn-box col-md-1">
                                                                        <button type="button" class="btn ${
                                                                          i == 0
                                                                            ? "btn_addManufacturer"
                                                                            : "btn_removeManufacturer"
                                                                        } ">
                                                                            <span class="glyphicon ${
                                                                              i ==
                                                                              0
                                                                                ? "glyphicon-plus"
                                                                                : "glyphicon-minus"
                                                                            }" aria-hidden="true" ></span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>`; // {validate:{ required :true}}
                }
                $("#manufacturer_row").html(ManufactoryHtml);
                ManufactoryVOList.forEach(function (item, index) {
                  var _id = "Manufactory" + index;
                  utils.valAutocomplete(
                    "/dict/querymanufactorynotpage",
                    { paramName: "keyWord", params: { isStop: 0 } },
                    _id,
                    {
                      data: "manufactoryId",
                      value: "manufactoryName",
                      brandfactoryName: "brandfactoryName",
                      brandfactoryId: "brandfactoryId",
                      address: "address",
                    }
                  );
                });
              } else {
                //html = distpickerHTML();
              }

              $(".btn_addManufacturer").hide();
              $(".btn_removeManufacturer").hide();
              $('[changeapplyflag="supplierManufactoryVOList"]')
                .parent(".form-group")
                .css("position", "initial");
            } else if (
              $t_flag.attr("changeapplyflag") == "supplierExtendItemVOList"
            ) {
              //如果付款方式

              //付款、结算部分
              var supplierExtendItemVoList = [];
              $(".paymentSettlement").each(function () {
                //var parentInp = $(this).find(".parentCode");
                var parentInp =
                  $(this).find(".parentCode").length > 0
                    ? $(this).find(".parentCode")
                    : $(this).find("input[type=checkbox]");
                if (parentInp.is(":checked")) {
                  var parentCode = parentInp.attr("name");
                  var parentCodeVal = parentInp.val();
                  if ($(this).find(".childCode").length < 1) {
                    supplierExtendItemVoList.push({
                      [parentCode]: parentCodeVal,
                    });
                  }
                  $(this)
                    .find(".childCode")
                    .each(function (index) {
                      var json = {};
                      var cCode = $(this).find(".cCode");
                      var cCodeName = cCode.attr("name");
                      var cCodeValue = cCode.val();
                      var cValue = $(this).find(".cValue");
                      var cValueName = cValue.attr("name");
                      var cValueValue = cValue.val();
                      if ($.trim(cValueValue) != "") {
                        json[parentCode] = parentCodeVal;
                        json[cCodeName] = cCodeValue;
                        json[cValueName] = cValueValue;
                        supplierExtendItemVoList.push(json);
                      }
                    });
                }
              });
              selObj = {
                [varObj.name]: "supplierExtendItemVOList",
                [varObj.afterValue]: supplierExtendItemVoList,
                afterText: "",
                [varObj.status]: "1",
              };

              //付款、结算部分 --回显
              $(".parentCode").prop("checked", false);
              $(".childCode").find(".cValue").val("");
              var extendItemList =
                window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
              if (extendItemList.length > 0) {
                for (var i = 0; i < extendItemList.length; i++) {
                  var parentCode = extendItemList[i].parentCode;
                  var parent = $('.parentCode[value="' + parentCode + '"]');
                  if (!parent.is(":checked")) {
                    parent.prop("checked", true);
                  }
                  var code = extendItemList[i].code;
                  if (code && code != "") {
                    var value = extendItemList[i].value;
                    var cCode = parent
                      .parents(".paymentSettlement")
                      .find('.cCode[value="' + code + '"]');
                    var cValue = cCode.parent(".childCode").find(".cValue");
                    cValue.val(value);
                  }
                }
                const baseVal = ["1010", "1003", "1004"];
                const checkedVal = extendItemList.map(
                  (item) => item.parentCode
                );
                let b = checkedVal.filter((item) => baseVal.indexOf(item) > -1);
                $(".paymentNode").css(
                  "display",
                  b.length > 0 ? "block" : "none"
                );
              }

              //付款，结算  初始化
              initPaymentSettlement();
              $(".parentCode").attr("disabled", "disabled");
              $(".childCode").find(".cValue").attr("disabled", "disabled");

              setOldDetail();
            } else if (
              $t_flag.attr("changeapplyflag") == "customerExtendItemVoList"
            ) {
              //如果付款方式  客户资料变更
              var customerExtendItemVoList = [];
              $(".paymentSettlement").each(function () {
                //var parentInp = $(this).find(".parentCode");
                var parentInp =
                  $(this).find(".parentCode").length > 0
                    ? $(this).find(".parentCode")
                    : $(this).find("input[type=checkbox]");
                if (parentInp.is(":checked")) {
                  var parentCode = parentInp.attr("name");
                  var parentCodeVal = parentInp.val();
                  if ($(this).find(".childCode").length < 1) {
                    customerExtendItemVoList.push({
                      [parentCode]: parentCodeVal,
                    });
                  }
                  $(this)
                    .find(".childCode")
                    .each(function (index) {
                      var json = {};
                      var cCode = $(this).find(".cCode");
                      var cCodeName = cCode.attr("name");
                      var cCodeValue = cCode.val();
                      var cValue = $(this).find(".cValue");
                      var cValueName = cValue.attr("name");
                      var cValueValue = cValue.val();
                      if ($.trim(cValueValue) != "") {
                        json[parentCode] = parentCodeVal;
                        json[cCodeName] = cCodeValue;
                        json[cValueName] = cValueValue;
                        customerExtendItemVoList.push(json);
                      }
                    });
                }
              });

              selObj = {
                [varObj.name]: "customerExtendItemVoList",
                [varObj.afterValue]: customerExtendItemVoList,
                afterText: "",
                [varObj.status]: "1",
              };

              //付款、结算部分 --回显
              $(".parentCode").prop("checked", false);
              $(".childCode").find(".cValue").val("");
              var extendItemList =
                window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
              if (extendItemList.length > 0) {
                for (var i = 0; i < extendItemList.length; i++) {
                  var parentCode = extendItemList[i].parentCode;
                  var parent = $('.parentCode[value="' + parentCode + '"]');
                  if (!parent.is(":checked")) {
                    parent.prop("checked", true);
                  }
                  var code = extendItemList[i].code;
                  if (code && code != "") {
                    var value = extendItemList[i].value;
                    var cCode = parent
                      .parents(".paymentSettlement")
                      .find('.cCode[value="' + code + '"]');
                    var cValue = cCode.parent(".childCode").find(".cValue");
                    cValue.val(value);
                  }
                }
              }

              //付款，结算  初始化
              initPaymentSettlement();
              $(".parentCode").attr("disabled", "disabled");
              $(".childCode").find(".cValue").attr("disabled", "disabled");
              addDisabled("2002");
              addDisabled("2003");
            } else {
              if (
                $t_flag.prop("name") == "billingPhone" ||
                $t_flag.prop("name") == "businessLicenseAddress"
              ) {
                if (
                  window.changeApply &&
                  window.changeApply["invoiceType"] &&
                  window.changeApply["invoiceType"]["valueAfter"] == "1"
                ) {
                  $t_flag.addClass("ignore");
                } else {
                  $t_flag.removeClass("ignore");
                }
              }
              //判断不等于标签 并且 校验不通过
              if (
                !$t_flag.attr("data-role") &&
                !validform($t_flag.parents("form").attr("id")).form()
              ) {
                return;
              }
              /**
               *RM 2018-10-22
               * 小包装条码 件包装数 * 中包装数 删除后保存，不能为空
               */
              if ($t_flag.parent().hasClass("for_js_empty_test")) {
                if ($t_flag.parent().find("span").length < 1) {
                  var text = $t_flag
                    .parents(".for_js_empty_test")
                    .prev()
                    .text()
                    .trim();
                  text = text.replace(/\*/g, "").trim();
                  $t.parent()
                  .find("div")
                  .find("button")
                  .removeClass("disNone")
                  .addClass("disBlock");
                  utils
                    .dialog({
                      title: "提示",
                      content: text + "不能为空",
                      width: 300,
                      height: 30,
                      ok: function () {},
                    })
                    .showModal();
                  return false;
                }
              }
              /**
               * RM 2018-09-25
               * 供应商运营资料变更 到货周期，最小供货量限制
               */
              if (
                $t_flag.parents("form").attr("id") ==
                "supplierOrganBaseApprovalRecordVO"
              ) {
                var val = $t_flag.val();
                if (
                  $t_flag.prev().text().indexOf("最小供货量") > 0 ||
                  $t_flag.prev().text().indexOf("到货周期") > 0
                ) {
                  if (val && val > 99999999.99) {
                    if ($t_flag.prev().text().indexOf("最小供货量") > 0) {
                      utils
                        .dialog({
                          content: "最小供货量最大为99999999.99！",
                          quickClose: true,
                          timeout: 2000,
                        })
                        .showModal();
                    } else {
                      utils
                        .dialog({
                          content: "到货周期最大为99999999.99！",
                          quickClose: true,
                          timeout: 2000,
                        })
                        .showModal();
                    }
                    return false;
                  }
                }
              }
              //针对多个输入框的情况
              let ModifyVal = [];
              if (
                $t_flag.prev().is("input[type=number]") ||
                $t_flag.prev().is("input[type=text]")
              ) {
                let inputNodes =
                  $t_flag.parent().find("input[type=number]").length > 0
                    ? $t_flag.parent().find("input[type=number]")
                    : $t_flag.parent().find("input[type=text]");
                ModifyVal = Array.from(inputNodes).map(function (item, index) {
                  return $(item).val();
                });
              }
              if (ModifyVal.length > 0) {
                let bool_everyNull = ModifyVal.every(function (item, index) {
                  return item != "";
                });
                if (bool_everyNull) {
                  if (Number(ModifyVal[1]) <= Number(ModifyVal[0])) {
                    utils
                      .dialog({
                        title: "提示",
                        content: "温度范围后面的值必须大于前面的值.",
                        okValue: "确定",
                        ok: function () {},
                      })
                      .showModal();
                    return false;
                  }
                }
                let bool_someNull = false;
                if (ModifyVal[0] != "") {
                  if (ModifyVal[1] == "") {
                    bool_someNull = true;
                  }
                } else if (ModifyVal[1] != "") {
                  if (ModifyVal[0] == "") {
                    bool_someNull = true;
                  }
                }
                if (bool_someNull) {
                  utils
                    .dialog({
                      title: "提示",
                      content: "请将温度范围的值填写完整.",
                      okValue: "确定",
                      ok: function () {},
                    })
                    .showModal();
                  return false;
                }
              }
              let ModifyValNull = ModifyVal.every(function (item, index) {
                return item == "";
              });
              selObj = {
                [varObj.name]: $t_flag.prop("name"),
                [varObj.afterValue]:
                  ModifyVal.length > 0
                    ? ModifyValNull
                      ? ""
                      : ModifyVal.join()
                    : $t_flag.val(),
                afterText:
                  $t_flag.prop("type") == "select-one"
                    ? $t_flag.find("option:checked").text()
                    : ModifyVal.length > 0
                    ? ModifyValNull
                      ? ""
                      : ModifyVal.join()
                    : $t_flag.val(),
                [varObj.status]: "1",
              };

              let tpobj = {};

              if($t_flag.prop("name") === 'contactMessage1'){    
                tpobj = {
                [varObj.name]: 'contactType1',
                [varObj.afterValue]:$("select[name='contactType1']").val(),
                afterText:$("select[name='contactType1']")[0].selectedOptions[0].label,
                [varObj.beforeValue]:window.changeApply_['contactType1']?window.changeApply_['contactType1'][varObj.afterValue]:'',
                [varObj.beforeText]:window.changeApply_['contactType1']?window.changeApply_['contactType1'][varObj.afterText]:'',
                [varObj.status]: "1",
              };
                window.changeApply[tpobj[varObj.name]] = tpobj
                window.changeApply_[tpobj[varObj.name]] = tpobj
                window.myContactMessage1 = true
              }

              let bottombj = {};
              if($t_flag.prop("name") === 'contactMessage2'){    
                bottombj = {
                [varObj.name]: 'contactType2',
                [varObj.afterValue]:$("select[name='contactType2']").val(),
                afterText:$("select[name='contactType2']")[0].selectedOptions[0].label,
                [varObj.beforeValue]:window.changeApply_['contactType2']?window.changeApply_['contactType2'][varObj.afterValue]:'',
                [varObj.beforeText]:window.changeApply_['contactType2']?window.changeApply_['contactType2'][varObj.afterText]:'',
                [varObj.status]: "1",
              };
                window.changeApply[bottombj[varObj.name]] = bottombj
                window.changeApply_[bottombj[varObj.name]] = bottombj
                window.myContactMessage2 = true
              }

              if ($t_flag.parents("form").attr("id") == "billInfo_form") {
                if ($t_flag.attr("name") == "billingCompanyName") {
                  $t_flag.parent().find("#bill_customerBtn").remove();
                }
              }
              $t_flag.attr("disabled", "disabled"); //禁用
              $t_flag
                .siblings("input[type=text],input[type=number]")
                .attr("readonly", "readonly");
              $t_flag
                .siblings("input[type=text],input[type=number]")
                .attr("disabled", "disabled");
            }
            if (
              selObj[varObj.name] == "temperatureRange" &&
              window.changeApply_[selObj[varObj.name]][
                varObj.beforeValue
              ].split(",").length > 0
            ) {
              // 温度范围
              let reviewData =
                window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
              $t_flag.prev().val(reviewData.split(",")[0]);
              $t_flag.val(reviewData.split(",")[1]);
            } else {
              // 普通文本框，（一个文本框）
              window.changeApply_[selObj[varObj.name]].afterText != undefined
                ? $t_flag.prop("type") == "select-one"
                  ? $t_flag.val(
                      window.changeApply_[selObj[varObj.name]].changeBefore
                    )
                  : $t_flag.val(
                      window.changeApply_[selObj[varObj.name]].afterText
                    )
                : ""; //回显改前值
            }
            if ($t_flag.attr("id") == "customerNature") {
              let baseNatureVal = changeApply_["nature"]["valueBefore"];
              $("#customerNature").val(baseNatureVal);
            } else {
              window.changeApply_[selObj[varObj.name]].afterText != undefined
                ? $t_flag.prop("type") == "select-one"
                  ? $t_flag.val(
                      window.changeApply_[selObj[varObj.name]].changeBefore
                    )
                  : $t_flag.val(
                      window.changeApply_[selObj[varObj.name]].afterText
                    )
                : ""; //回显改前值
            }
            selObj[varObj.beforeValue] =
              window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
            if (
              selObj[varObj.name] == "organizationCode" ||
              selObj[varObj.name] == "taxRegistrationCode"
            ) {
              //如果是 组织机构代码证 和 税务登记证号
              window[selObj[varObj.name]] = selObj;
            }

            window.changeApply[selObj[varObj.name]] = selObj;

            if (
              $t_flag.attr("data-role") &&
              window.changeApply[$t_flag.prop("name")]
            ) {
              //如果是标签文本，保存编辑需加控制
              $t_flag.tagsinput("removeAll");
              $.each(
                window.changeApply[$t_flag.prop("name")].changeBefore.split(
                  ","
                ),
                function (i, v) {
                  $("[name=" + $t_flag.prop("name") + "]").tagsinput("add", v);
                }
              );
              $t_flag.tagsinput("notOperable");
              $t_flag
                .parent()
                .find("span[data-role=remove]")
                .removeClass("disBlock")
                .addClass("disNone");
            }

            break;
          case "": //为tabs切换
            var $t_ul =
              $t.parents("ul.nav").length > 0
                ? $t.parents("ul.nav")
                : $t.parents("div.panel-heading");
            var $t_ul_nav_content =
              $t_ul.next(".nav-content").length > 0
                ? $t_ul.next(".nav-content")
                : $t_ul.next().find(".nav-content");
            // var $t_ul = $t.parents('div.panel-heading');
            // var $t_ul_nav_content = $t_ul.next().find('.nav-content');
            var $t_table = $t_ul_nav_content.find("table.XGridUI:visible");

            if ($t_ul.length) {
              $t_flag.css("padding-right", "25px");
            } else {
              $t_ul_nav_content = $t_flag
                .parents(".panel-heading")
                .next(".panel-body")
                .find(".nav-content");
              $t_table = $t_ul_nav_content.find("table.XGridUI:visible");
            }
            if ($t_table.length) {
              if ($t_table.attr("id") == "X_Table_Channel") {
                let rowdata = getPharmacyTypeCheck();
                let resAry = rowdata.filter(
                  (item) => item["dimensionSalesPriceYn"] == "1"
                );
                let res = resAry.filter(
                  (item) => item["pharmacyTypeArr"].length == 0
                );
                if (resAry.length != 0) {
                  if (res.length != 0) {
                    // && item['pharmacyTypeArr'].length == 0
                    utils
                      .dialog({
                        content: "出库价维价关联客户类型不能为空",
                        quickClose: true,
                        timeout: 2000,
                      })
                      .showModal();
                    return false;
                  }
                }
              }
              selObj = {
                [varObj.afterValue]:
                  $t_table.attr("id") == "X_Table_Channel"
                    ? getPharmacyTypeCheck()
                    : $("#" + $t_table.prop("id")).XGrid("getRowData"),
                [varObj.status]: "1",
                [varObj.name]: $t_flag.prop("name"),
              };
              if (selObj["columnValue"] == "supplierApprovalFileList") {
                // || selObj['columnValue'] == "supplierClientProxyOrderList"
                let msg = approvalInputValidation(
                  $("#table3").XGrid("getRowData"),
                  $("#supplierTypeId").val(),
                  "changepage"
                );
                if (msg != "") {
                  utils
                    .dialog({
                      title: "提示",
                      width: 350,
                      content: msg,
                      okValue: "确定",
                      ok: function () {},
                    })
                    .showModal();
                  return false;
                }
              }
              if (
                window.changeApplyList_[$t_flag.prop("name")][
                  varObj.beforeValue
                ] &&
                window.changeApplyList_[$t_flag.prop("name")][
                  varObj.beforeValue
                ].length > 0
              ) {
                if (
                  $("#shippingSource").length &&
                  $("#shippingSource").val() != 3 &&
                  $t_flag.prop("name") == "customerApprovalFileVoList"
                ) {
                  $t_table.XGrid("clearGridData").trigger("reloadGrid");
                  var fileData = {
                    result: {
                      list: JSON.parse(
                        JSON.stringify(
                          window.changeApplyList_[$t_flag.prop("name")][
                            varObj.beforeValue
                          ]
                        )
                      ),
                    },
                  };
                  handleData(fileData, $("#table3"));
                } else {
                  const temp = JSON.parse(
                    JSON.stringify(
                      window.changeApplyList_[$t_flag.prop("name")][
                        varObj.beforeValue
                      ]
                    )
                  );
                  // WN: 20200901 恢复到变更前数据时，删除此前保存经营范围字段，避免 UI 错乱
                  temp.forEach((item) => {
                    if (item.customerBusinessScopeVoList) {
                      item.customerBusinessScopeVoList.length = 0;
                    }
                  });
                  // WN: 20201013 仅在客户变更页面 - 资料存档模块刷新数据
                  if (
                    $t_table.attr("id") == "table2" &&
                    $("#customerOtherInfo")
                  ) {
                    $t_table.XGrid("clearGridData");
                  }
                  $t_table
                    .XGrid("setGridParam", {
                      data: temp,
                    })
                    .trigger("reloadGrid");
                }
              } else {
                $t_table.XGrid("clearGridData").trigger("reloadGrid");
              }
              $t_ul
                .next(".nav-content")
                .find("button")
                .attr("disabled", "disabled");
              $t_ul_nav_content.find("button").attr("disabled", "disabled");
              $t_table.find("input,select").attr("disabled", "disabled");
              if ($t_flag.prop("name") == "supplierClientProxyOrderList") {
                $("#isEdit").val(0);
              } else if (
                $t_flag.prop("name") == "supplierApprovalFileList" ||
                $t_flag.prop("name") == "customerApprovalFileVoList"
              ) {
                setDisabledZtree(false);
              }
              if ($("#toggleSuppleentalEditing").length) {
                $("#toggleSuppleentalEditing").removeAttr("disabled");
              }
            } else {
              //tabs中上传附件
              var otherFilesArr = [];
              var $t_checkbox = $t_ul
                .next(".nav-content")
                .find('input[type="checkbox"]:visible');
              if ($("#otherFileBox").length > 0) {
                //其他附件
                $("#otherFileBox input[type='checkbox']").each(function () {
                  var checked = this.checked;
                  if (checked) {
                    var v = $(this).val();
                    var json = {};
                    var imgList = [];
                    var newOther = $("input[id='newother" + v + "']");
                    var fLen = $("input[id='newother" + v + "']").length;
                    json.certificateType = v;
                    if (fLen > 0) {
                      for (let i = 0; i < fLen; i++) {
                        var str = $(newOther[i]).val();
                        str = JSON.parse(str);
                        imgList = imgList.concat(str);
                      }
                      //imgList = JSON.parse($("input[id='newother" + v + "']").val())
                      json.enclosureList = imgList;
                      for (var j = 0; j < json.enclosureList.length; j++) {
                        delete json.enclosureList[j].type;
                      }
                    } else {
                      json.enclosureList = [];
                    }
                    otherFilesArr.push(json);
                  }
                });
              }
              if ($("#contractType").length > 0) {
                // 客户变更的合同附件
                $("#contractType input[type='checkbox']").each(function () {
                  var checked = this.checked;
                  if (checked) {
                    var v = $(this).val();
                    var json = {};
                    var imgList = [];
                    var newOther = $("input[id='newcontractType" + v + "']");
                    var fLen = $("input[id='newcontractType" + v + "']").length;
                    json.certificateType = v;
                    if (fLen > 0) {
                      for (let i = 0; i < fLen; i++) {
                        var str = $(newOther[i]).val();
                        str = JSON.parse(str);
                        imgList = imgList.concat(str);
                      }
                      //imgList = JSON.parse($("input[id='newother" + v + "']").val())
                      json.enclosureList = imgList;
                      for (var j = 0; j < json.enclosureList.length; j++) {
                        delete json.enclosureList[j].type;
                      }
                    } else {
                      json.enclosureList = [];
                    }
                    otherFilesArr.push(json);
                  }
                });
              }
              selObj = {
                [varObj.name]: $t_flag.prop("name"),
                [varObj.status]: "1",
                [varObj.afterValue]: otherFilesArr,
              };
              //回显
              //$t_checkbox.removeAttr("checked");
              $t_checkbox.prop("checked", false);
              otherFilesArr =
                window.changeApplyList_[$t_flag.prop("name")][
                  varObj.beforeValue
                ];
              $(otherFilesArr).each(function (fileIndex, fileItem) {
                var enclosureList = fileItem.enclosureList;
                if (enclosureList && enclosureList.length > 0) {
                  var imgArr = [];
                  $(enclosureList).each(function (imgIndex, imgItem) {
                    var imgObj = {};
                    if ($("#otherFileBox").length > 0) {
                      imgObj.fileName = imgItem.fileName;
                      imgObj.filePath = imgItem.filePath;
                    }

                    if ($("#contractType").length > 0) {
                      imgObj.fileName = imgItem.enclosureName;
                      imgObj.filePath = imgItem.url;
                    }
                    imgArr.push(imgObj);
                  });
                  //var html = '<input type="hidden" data-type="' + fileItem.certificateType + '" id="other' + fileItem.certificateType + '" value=\'' + JSON.stringify(imgArr) + '\' />';
                  var html = "";
                  if ($("#otherFileBox").length > 0) {
                    //其他附件
                    html =
                      '<input type="hidden" data-type="' +
                      fileItem.certificateType +
                      '" id="other' +
                      fileItem.certificateType +
                      "\" value='" +
                      JSON.stringify(imgArr) +
                      "' />";
                  }
                  if ($("#contractType").length > 0) {
                    // 客户变更的客户合同
                    //html = '<input type="hidden" data-type="' + fileItem.certificateType + '" id="contractType' + fileItem.certificateType + '" value=\'' + JSON.stringify(imgArr) + '\' />';
                  }
                  $("body").append(html);
                }
                //var $t_checkbox = $('.otherFile').find('input[type=checkbox]');
                $($t_checkbox).each(function (index, item) {
                  if (fileItem.certificateType == $(this).val()) {
                    $(this).prop("checked", true);
                  }
                });
              });

              $t_checkbox.attr("disabled", "disabled");
              $("#qtfjUpload").attr("disabled", "disabled");
            }

            selObj[varObj.beforeValue] =
              window.changeApplyList_[$t_flag.prop("name")][varObj.beforeValue];
            // 仅针对神农发起的客户资料变更申请以及后续审核环节发生的变更进行标记
            let addFlag =
              window.location.href.indexOf("approval/toBaseApprovalDetail") > 0;
            let auditFlag =
              window.location.href.indexOf("approval/toCustomerBaseChange") > 0;
            if (addFlag || auditFlag) {
              // 临时 Map ，空间换时间，快速对比
              const beforeMap = new Map();
              selObj[varObj.beforeValue].forEach((item) => {
                // 删除 UploadeImg 属性，避免因该属性不一致造成的 JSON 判断失效
                delete item.UploadeImg;
                // 收集 beforeMap
                beforeMap.set(item.id, item);
              });
              selObj[varObj.afterValue].forEach((item) => {
                // 删除 UploadeImg 属性，避免因该属性不一致造成的 JSON 判断失效
                delete item.UploadeImg;
                if (beforeMap.get(item.id)) {
                  // 若 item 在 beforeMap 中存在，则判断其值是否有变更。
                  // 如果其值有变更，则将其 changeStatus 修改为 1，
                  item.changeStatus = equals(beforeMap.get(item.id), item)
                    ? item.changeStatus
                    : 1;
                } else {
                  // 若 afterItem 在 beforeMap 中不存在，则将其 changeStatus 设置为 1
                  item.changeStatus = 1;
                }
              });
            }
            window.changeApplyList[$t_flag.prop("name")] = selObj;

            //
            if (
              selObj["columnValue"] == "supplierApprovalFileList" ||
              selObj["columnValue"] == "supplierClientProxyOrderList"
            ) {
              let _data =
                window.changeApplyList &&
                window.changeApplyList.supplierApprovalFileList &&
                window.changeApplyList.supplierApprovalFileList.valueAfter
                  ? window.changeApplyList.supplierApprovalFileList.valueAfter
                  : $("#table3").XGrid("getRowData");
              let BuseScopes = utils.operateRange(
                _data,
                "supplierApprovalFileBusinessScopeVOList",
                "validityDate"
              );
              let BuseScopeAry = [];
              $(BuseScopes).each((index, item) => {
                BuseScopeAry.push({
                  businessScopeCode: item["id"],
                  businessScopeName: item["name"],
                });
              });
              let table2Data = $("#table2").XGrid("getRowData");
              let table2Obj = {
                valueBefore: table2Data,
                changeStatus: "1",
                columnValue: "supplierClientProxyOrderList",
              };
              let table2DataCopy =
                window.changeApplyList &&
                window.changeApplyList.supplierClientProxyOrderList &&
                window.changeApplyList.supplierClientProxyOrderList.valueAfter
                  ? window.changeApplyList.supplierClientProxyOrderList
                      .valueAfter
                  : $("#table2").XGrid("getRowData");
              if (table2DataCopy.length > 0) {
                $(table2DataCopy).each((index, item) => {
                  if (item["authorityType"] == "1") {
                    item["supplierClientProxyBusinessScopeVOList"] =
                      BuseScopeAry;
                  }
                });
                if (table2DataCopy[0]["authorityType"] == "1") {
                  // 当批准文件变化时，只有当 客户委托书的授权类型是经营范围授权的时候，客户委托书才会有变更记录。
                  $("[name=supplierClientProxyOrderList]")
                    .parent("li")
                    .find(".yulan")
                    .addClass("yulanInput_after");
                  table2Obj["valueAfter"] = table2DataCopy;
                  console.log("table2Data", table2Data);
                  window.changeApplyList["supplierClientProxyOrderList"] =
                    table2Obj;
                }
              }
            }
            if ($t_table.find(".ApprovalUploadeImg").length) {
              $t_table.find(".ApprovalUploadeImg").attr("disabled", "disabled");
            }
            break;
          case "button": //
            // 开票信息
            if ($t_flag.parents("form").attr("id") == "billInfo_form") {
              let tags = $t_flag.parents(".row").find(".fileIcon_p");
              let kaipiaoPicArr = [];
              $(tags).each(function (index, item) {
                let kaipiaoPicObj = {};
                kaipiaoPicObj.enclosureName = "非独立经营证明" + (index + 1);
                kaipiaoPicObj.url = $(item).attr("data-imgurl");
                kaipiaoPicArr.push(kaipiaoPicObj);
              });

              selObj = {
                [varObj.name]: "customerEnclosureVoList", //字段名
                [varObj.afterValue]: JSON.stringify(kaipiaoPicArr), //改后值
                afterText: JSON.stringify(kaipiaoPicArr),
                [varObj.status]: "1",
              };

              //从 变更对象 添加该属性
              selObj[varObj.beforeValue] =
                window.changeApply_[selObj[varObj.name]][varObj.beforeValue];
              window.changeApply["customerEnclosureVoList"] = selObj;
              $t_flag.prop("disabled", "disabled"); //禁用
              //window.changeApply['customerEnclosureVoList'] = kaipiaoPicArr;
            }
            break;
        }
        if ($t_flag.attr("changeapplyflag") == "largeCategory") {
          delete changeApply["entryTaxRate"]; // 当商品大类 内容切换保存时，清空遍历里的进项税率 销项税率的值
          delete changeApply["salesRate"];
        }

        //-1：不变，0：撤销，1：新增，2：变更又变更
        if (
          window.changeApplyBak &&
          window.changeApplyBak[selObj[varObj.name]]
        ) {
          //selObj[varObj.status] = 2;
          //window.changeApplyBak[selObj[varObj.name]][varObj.status] = 2;
          //0919 邓涛改，变更状态不应变更Bak中的状态
          window.changeApply[selObj[varObj.name]][varObj.status] = 2;
        }

        //select置灰
        $t.parents(".tel-wrap").find(".tel-label").attr("disabled", true);
        //删除placeholder
        $t.siblings(".tel-part").removeAttr("placeholder");
        //变更后更 展示并改图标颜色
        $t.hide().siblings(".yulan").show().addClass("yulanInput_after");
      })
      .on("click", ".cSelect", function () {
        //展示变更
        $.fn_select_changeApply(varObj);
      })
      .on("click", ".cDelete", function () {  //删除
        /*数据删除*/
        var $t = $(window.changeApply_e);
        /**
         * 开票信息
         */
        if ($("#shippingSource").length && $("#shippingSource").val() != 3) {
          if (
            $t.prev("input").attr("name") == "customerTypeName" ||
            $t.prev("input").attr("name") == "invoiceType" ||
            $t.parents("form").attr("id") == "customerApprovalFileVoList" ||
            $t.prev("a").attr("name") == "customerQualityAgreementVoList" ||
            $t.prev("a").attr("name") == "customerDelegationFileVoList" ||
            $t.prev("a").attr("name") == "customerBeProxyIdentityVoList"
          ) {
            utils
              .dialog({
                title: "提示",
                content: "此项不支持删除",
                okValue: "确定",
                ok: function () {},
              })
              .showModal();
            return false;
          }
        }
        if ($t.parents("form").attr("id") == "billInfo_form") {
          // 开票信息
          let prevNameArr = [
              "taxpayerNum",
              "billingOpeningBank",
              "billingBankAccount",
            ],
            thisTagName = $t.prev("input").attr("name");
          if (prevNameArr.indexOf(thisTagName) >= 0) {
            utils
              .dialog({
                title: "提示",
                content: "不支持删除",
                okVal: "确定",
                ok: function () {},
              })
              .showModal();
            return false;
          }
          // 如果  是否独立核算的值有修改，并且修改后的值为 是， 公司名称等不支持编辑
          // 如果，是否独立核算的值没有修改, 并且初始值 当前为是是， 公司名称等不支持编辑
          if (
            window.changeApply["isIndependent"] &&
            window.changeApply["isIndependent"]["valueAfter"] == "1"
          ) {
            if (thisTagName == "billingCompanyName") {
              // 公司名称
              utils
                .dialog({
                  title: "提示",
                  content: "不支持删除",
                  okVal: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
          } else {
            let _val = $("[name=isIndependent]:checked").val();
            if (_val == "1" && thisTagName == "billingCompanyName") {
              utils
                .dialog({
                  title: "提示",
                  content: "不支持删除",
                  okVal: "确定",
                  ok: function () {},
                })
                .showModal();
              return false;
            }
          }
        }

        //var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
        var $t_flag =
          $t.siblings("[changeapplyflag]").length > 0
            ? $t.siblings("[changeapplyflag]")
            : $t.parent("div").find("[changeapplyflag]"); //标记元素
        $t_flag.attr("data-value", "");
        var $t_flag_hidden = $("#" + $t_flag.attr("changeapplyflag"));
        var threeAValue = window.changeApply[$t_flag.prop("name")]
          ? window.changeApply[$t_flag.prop("name")][varObj.afterValue]
          : $t_flag.val();
        var threeBValue = window.changeApply["threeEvidenceAll"]
          ? window.changeApply["threeEvidenceAll"][varObj.beforeValue]
          : $('[name="threeEvidenceAll"]').filter(":checked").val();
        window.changeApply_d.close(); //关闭对应dialog

        //如果bak中有上次更改的值，则为撤销，否则为取消变更直接删除

        if (window.changeApplyBak && changeApplyBak[$t_flag.prop("name")]) {
          if (window.changeApply[$t_flag.prop("name")]) {
            window.changeApply[$t_flag.prop("name")] = {};
            window.changeApply[$t_flag.prop("name")][varObj.status] = 0;
            window.changeApply[$t_flag.prop("name")][varObj.name] =
              $t_flag.prop("name");
          }
          if (window.changeApplyList[$t_flag.prop("name")]) {
            window.changeApplyList[$t_flag.prop("name")] = {};
            window.changeApplyList[$t_flag.prop("name")][varObj.status] = 0;
            window.changeApplyList[$t_flag.prop("name")][varObj.name] =
              $t_flag.prop("name");
          }
        } else if (
          window.changeApplyBak &&
          changeApplyBak[$t_flag.attr("changeapplyflag")]
        ) {
          if (window.changeApply[$t_flag.attr("changeapplyflag")]) {
            window.changeApply[$t_flag.attr("changeapplyflag")] = {};
            window.changeApply[$t_flag.attr("changeapplyflag")][
              varObj.status
            ] = 0;
            window.changeApply[$t_flag.attr("changeapplyflag")][varObj.name] =
              $t_flag.attr("changeapplyflag");
          }
        } else {
          delete window.changeApply[$t_flag.prop("name")];
          delete window.changeApply[$t_flag_hidden.prop("name")];
          delete window.changeApplyList[$t_flag.prop("name")];
          delete window.changeApply[$t_flag.attr("changeapplyflag")];
          if ($t_flag.prop("name") == "businessLicenseNum") {
            delete window.changeApply["organizationCodeNumber"];
            delete window.changeApply["organizationCodeNumber"];
            delete window.changeApplyList["organizationCodeNumber"];
            delete window.changeApply["taxRegistryNumber"];
            delete window.changeApply["taxRegistryNumber"];
            delete window.changeApplyList["taxRegistryNumber"];
          }
          //开票信息。客户名称修改记录删除，相应的删除后面三项的修改记录
          if (
            $t_flag.parents("form").attr("id") == "billInfo_form" ||
            $t_flag.prop("name") == "isIndependent"
          ) {
            delete window.changeApply["billingBankAccount"];
            delete window.changeApply["billingOpeningBank"];
            delete window.changeApply["taxpayerNum"];
            $(
              "[name=taxpayerNum], [name=billingOpeningBank], [name=billingBankAccount]"
            )
              .next(".yulan")
              .removeClass("yulanInput_after");
          }
          if ($t_flag.prop("name") == "isIndependent") {
            delete window.changeApply["billingCompanyName"];
            $("[name=billingCompanyName]")
              .next(".yulan")
              .removeClass("yulanInput_after");
          }
        }

        if (
          $t_flag.attr("changeapplyflag") == "supplierRepertoryAddressVOList"
        ) {
          delete window.changeApply.supplierRepertoryAddressVOList;
        }

        if($t_flag.prop("name") === 'contactMessage1'){
          window.changeApply['contactType1'] = {};
          window.changeApply['contactType1'][varObj.status] = 0
          window.changeApply['contactType1'][varObj.name] = 'contactType1'
          $("select[name='contactType1']").val('')
        }

        if($t_flag.prop("name") === 'contactMessage2'){
          window.changeApply['contactType2'] = {};
          window.changeApply['contactType2'][varObj.status] = 0
          window.changeApply['contactType2'][varObj.name] = 'contactType2'
          $("select[name='contactType2']").val('')
        }

        //三证合一触发 删除--需更新关联属性
        if ($t_flag.attr("name") == "threeEvidenceAll") {
          if (threeBValue == "1") {
            //撤销为是
            //将 组织机构代码证 税务登记证号 变更为 页面 营业执照号
            //将 组织机构代码证 税务登记证号 变更为 营业执照号有变更
            var supplierBusinessNumValue_now = window.changeApply
              .supplierBusinessNum
              ? window.changeApply.supplierBusinessNum[varObj.afterValue]
              : $('[name="supplierBusinessNum"]').val();
            var selObj = {};
            if (window.changeApplyBak && changeApplyBak.organizationCode) {
              if (window.changeApply && changeApply.organizationCode) {
                delete window.changeApply.organizationCode;
              }
            } else {
              selObj = {
                [varObj.name]: "organizationCode",
                [varObj.afterValue]: supplierBusinessNumValue_now,
                [varObj.beforeValue]: $('[name="organizationCode"]').val(),
                afterText: supplierBusinessNumValue_now,
                [varObj.status]: "1",
              };
              window.changeApply[selObj[varObj.name]] = selObj;
            }

            if (window.changeApplyBak && changeApplyBak.taxRegistrationCode) {
              if (window.changeApply && changeApply.taxRegistrationCode) {
                delete window.changeApply.taxRegistrationCode;
              }
            } else {
              selObj = {
                [varObj.name]: "taxRegistrationCode",
                [varObj.afterValue]: supplierBusinessNumValue_now,
                [varObj.beforeValue]: $('[name="taxRegistrationCode"]').val(),
                afterText: supplierBusinessNumValue_now,
                [varObj.status]: "1",
              };
              window.changeApply[selObj[varObj.name]] = selObj;
            }
          } else if (threeBValue == "0") {
            var selObj_ = {};
            if (window.organizationCode) {
              selObj_ = window.organizationCode;
              delete window.organizationCode;
            }
            //撤销为否
            var selObj = {
              [varObj.name]: "organizationCode",
              [varObj.afterValue]: selObj_[[varObj.afterValue]],
              [varObj.beforeValue]: $('[name="organizationCode"]').val(),
              afterText: selObj_[[varObj.afterText]],
              [varObj.status]: "1",
            };
            window.changeApply[selObj[varObj.name]] = selObj;

            if (window.taxRegistrationCode) {
              selObj_ = window.taxRegistrationCode;
              delete window.taxRegistrationCode;
            }
            selObj = {
              [varObj.name]: "taxRegistrationCode",
              [varObj.afterValue]: selObj_[[varObj.afterValue]],
              [varObj.beforeValue]: $('[name="taxRegistrationCode"]').val(),
              afterText: selObj_[[varObj.afterText]],
              [varObj.status]: "1",
            };
            window.changeApply[selObj[varObj.name]] = selObj;
          }
        }
        //删除变更后，恢复蓝色图标
        $t.removeClass("yulanInput_after");

        /*else {
                if ($('[name="threeEvidenceAll"]').filter(':checked').val() == '1' || (window.changeApply && window.changeApply.threeEvidenceAll == '1')) {

                }
            }*/
      });

    var $yyzzh = $('input[name="supplierBusinessNum"]'), //营业执照号
      $zzjgdmzh = $('input[name="organizationCode"]'), //组织机构代码证号
      $swdjzh = $('input[name="taxRegistrationCode"]'), //税务登记证号
      $sfszhy = $('input[name="threeEvidenceAll"]'); //是否三证合一

    $sfszhy.on("input", function () {
      if ($sfszhy.filter(":checked").val() == "1") {
        //隐藏图标
        $zzjgdmzh.nextAll(".yulanInput").hide();
        $swdjzh.nextAll(".yulanInput").hide();
      } else {
        //隐藏图标
        $zzjgdmzh.nextAll(".yulanInput").show();
        $swdjzh.nextAll(".yulanInput").show();
      }
    });
    $sfszhy.parent("div").on("click", ".changeApplyBtn", function () {
      $.changeApply_ThreeInone(
        $yyzzh,
        $zzjgdmzh,
        $swdjzh,
        $sfszhy.filter(":checked").val(),
        varObj,
        "threeEvidenceAll"
      );
    });

    $yyzzh.parent("div").on("click", ".changeApplyBtn", function () {
      $.changeApply_ThreeInone(
        $yyzzh,
        $zzjgdmzh,
        $swdjzh,
        changeApply[$sfszhy.prop("name")]
          ? changeApply[$sfszhy.prop("name")][varObj.afterValue]
          : $sfszhy.val(),
        varObj,
        "supplierBusinessNum"
      );
    });

    // 客户资料变更
    var $customer_yyzzh = $('input[name="businessLicenseNum"]'), //营业执照号
      $customer_zzjgdmzh = $('input[name="organizationCodeNumber"]'), //组织机构代码证号
      $customer_swdjzh = $('input[name="taxRegistryNumber"]'), //税务登记证号
      $customer_sfszhy = $('input[name="threeInOne"]:checked'); //是否三证合一， 获取当前选中的项的值

    $('input[name="threeInOne"]:checked').on("input", function () {
      if (
        $('input[name="threeInOne"]:checked').filter(":checked").val() == "1"
      ) {
        //隐藏图标
        $customer_zzjgdmzh.nextAll(".yulanInput").hide();
        $customer_swdjzh.nextAll(".yulanInput").hide();
      } else {
        //隐藏图标
        $customer_zzjgdmzh.nextAll(".yulanInput").show();
        $customer_swdjzh.nextAll(".yulanInput").show();
      }
    });
    $('input[name="threeInOne"]:checked')
      .parent("div")
      .on("click", ".changeApplyBtn", function () {
        $.changeApply_ThreeInone(
          $customer_yyzzh,
          $customer_zzjgdmzh,
          $customer_swdjzh,
          $('input[name="threeInOne"]:checked').filter(":checked").val(),
          varObj,
          "threeInOne"
        );
      });

    $customer_yyzzh.parent("div").on("click", ".changeApplyBtn", function () {
      $.changeApply_ThreeInone(
        $customer_yyzzh,
        $customer_zzjgdmzh,
        $customer_swdjzh,
        changeApply[$('input[name="threeInOne"]:checked').prop("name")]
          ? changeApply[$('input[name="threeInOne"]:checked').prop("name")][
              varObj.afterValue
            ]
          : $('input[name="threeInOne"]:checked').val(),
        varObj,
        "businessLicenseNum"
      );
    });
  },
  //回显变更，将已变更改为黄色图标
  changeApply_updateIcon: function (obj, varObj_) {
    var varObj = {
      name: "columnValue",
      beforeValue: "beforeValue",
      afterValue: "valueAfter",
      afterText: "afterText",
      status: "changeStatus",
    };

    $.extend(varObj, varObj_);

    window.changeApply = window.changeApply || {};
    var $d = $("[changeApplyFlag]");
    $.each(window.changeApply, function (i, v) {
      var $v = $('[name="' + v[varObj.name] + '"]').length
        ? $('[name="' + v[varObj.name] + '"]')
        : $('[changeApplyFlag="' + v[varObj.name] + '"]');
      $v =
        $v.nextAll("i.yulan").length != 0
          ? $v.nextAll("i.yulan")
          : $v.parent().nextAll("i.yulan");
      $v.addClass("yulanInput_after");
    });
    $.each(window.changeApplyList, function (i, v) {
      var $v = $('[name="' + v[varObj.name] + '"]').length
        ? $('[name="' + v[varObj.name] + '"]')
        : $('[changeApplyFlag="' + v[varObj.name] + '"]');
      $v =
        $v.nextAll("i.yulan").length != 0
          ? $v.nextAll("i.yulan")
          : $v.parent().nextAll("i.yulan");
      $v.addClass("yulanInput_after");
    });
  },
  //变更申请，查阅变更
  changeApply_selectData: function (data, obj) {
    var varObj = {
      name: "columnValue",
      beforeValue: "beforeValue",
      afterValue: "valueAfter",
      afterText: "afterText",
      status: "changeStatus",
    };

    $.extend(varObj, obj);

    window.changeApply = window.changeApply || {};
    var $d = $("[changeApplyFlag]");
    $.each(data, function (i, v) {
      if(i !== 'contactType2' && i !== 'contactType1'){
        var $v = $('[name="' + v[varObj.name] + '"]').length
        ? $('[name="' + v[varObj.name] + '"]')
        : $('[changeApplyFlag="' + v[varObj.name] + '"]');
        if ($v.is("a")) {
        $v.css("padding-right", "32px");
        $v.after(
          '<i class="changeApplyB_See_icon" style="top:13px;right: 12px"></i>'
        );
      } else {
        if ($v.prop("type") == "radio") {
          $v.parent(".checkbox").css("position", "initial");
          $v.after(
            '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
          );
        } else if ($v.prop("type") == "checkbox") {
          $v.parent(".checkbox").css("position", "initial");
          $v.length > 2
            ? $v
                .eq($v.length - 1)
                .after(
                  '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
                )
            : $v.after(
                '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
              );
        } else if ($v.prop("type") == "hidden") {
          //
          // RM  DOM 结构问题，有的隐藏域父级没有form-control  所以当有修改值的时候，没法写入放大镜的dom
          if ($v.parents(".form-control").length > 0) {
            $v.parents(".form-control").css("position", "initial");
            $v.parents(".form-control")
              .find("button")
              .after(
                '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
              );
          } else {
            $v.next().after(
              '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
            );
          }
        } else {
          //仓库地址
          if ($v.attr("changeapplyflag") == "supplierRepertoryAddressVOList") {
            $v.parent(".form-group").css("position", "initial");
            $v.after(
              '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
            );
          } else if (
            $v.attr("changeapplyflag") == "customerStorageAddressVOList"
          ) {
            //仓库地址
            $v.parent(".form-group").css("position", "initial");
            $v.after(
              '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
            );
          } else if (
            $v.attr("changeapplyflag") == "customerBillingAddressVoList"
          ) {
            // 发票邮寄地址
            $v.parent(".form-group").css("position", "initial");
            $v.after(
              '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
            );
          } else if (
            $v.attr("changeapplyflag") == "customerDeliveryAddressVoList"
          ) {
            $v.parent(".form-group").css("position", "initial");
            $v.after(
              '<i class="changeApplyB_See_icon" style="top:9px;right:  -1.8rem;"></i>'
            );
          } else {
            //普通文本
            if ($v.prop("name") != "validityDate") {
              $v.after('<i class="changeApplyB_See_icon"></i>');
            } else {
              /**
               * 比如说 批准文件 中 放大镜查看修改记录。在弹窗中 也会显示放大镜， 由于日期节点 的name在页面中会有多个 存在，所以只要有修改，就会在日期的后面加放大镜，实际上在弹窗中是不需要的导致的
               */
              $($v).each(function (index, item) {
                if (
                  $(item).prop("name") == "validityDate" &&
                  $(item).parent().hasClass("input-group")
                ) {
                  $(item).after('<i class="changeApplyB_See_icon"></i>');
                }
              });
            }
          }
        }

        $v.parents(".panel-body").find(".row").css("width", "100%");
      }
      }
    });

    $(data).map(function () {
      window.changeApply[this[varObj.name]] = this;
    });

    $("body").on("click", ".changeApplyB_See_icon", function () {
      //展示变更
      window.changeApply_e = this;
      $.fn_select_changeApply(varObj);
    });
  },
  //变更申请，查阅变更公用fn
  fn_select_changeApply: function (varObj) {
    var $t = $(window.changeApply_e); //找到对应节点
    //var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
    var $t_flag =
      $t.siblings("[changeapplyflag]").length > 0
        ? $t.siblings("[changeapplyflag]")
        : $t.parent("div").find("[changeapplyflag]"); //标记元素
    var content = "",
      width = 400;
    if (window.changeApply_d) window.changeApply_d.close(); //关闭对应dialog
    var $t_type =
      $t_flag.prop("type") == "number" || $t_flag.prop("type") == "select-one"
        ? "text"
        : $t_flag.prop("type");
    switch ($t_type) {
      case "radio": //为单选
        //var $t_flag_radio = $t.siblings('input[type=radio]:checked');
        if (window.changeApply[$t_flag.prop("name")] != undefined) {
          content =
            $t_flag.parents(".input-group").find(".input-group-addon").html() +
            "：" +
            window.changeApply[$t_flag.prop("name")].afterText;
          if ($t_flag.prop("name") == "invoiceType") {
            var text = window.changeApply[$t_flag.prop("name")].afterText;
            var text = window.changeApply[$t_flag.prop("name")].afterText;
            if (text == "1") {
              content =
                $t_flag
                  .parents(".input-group")
                  .find(".input-group-addon")
                  .html() +
                "：" +
                "电子普通发票";
            } else if (text == "2") {
              content =
                $t_flag
                  .parents(".input-group")
                  .find(".input-group-addon")
                  .html() +
                "：" +
                "增值税专用发票";
            } else if (text == "3") {
              content =
                $t_flag
                  .parents(".input-group")
                  .find(".input-group-addon")
                  .html() +
                "：" +
                "纸质普通发票";
            } else if (text == "4") {
              content =
                $t_flag
                  .parents(".input-group")
                  .find(".input-group-addon")
                  .html() +
                "：" +
                "增值税电子专用发票";
            } else if (text == "5") {
				content =
                $t_flag
                  .parents(".input-group")
                  .find(".input-group-addon")
                  .html() +
                "：" +
                "全电普票";
			} else if (text == "6") {
				content =
                $t_flag
                  .parents(".input-group")
                  .find(".input-group-addon")
                  .html() +
                "：" +
                "全电专票";
			}
          }
        }
        break;
      case "checkbox": //为复选
        if (window.changeApply[$t_flag.prop("name")] != undefined) {
          content =
            $t_flag.parents(".input-group").find(".input-group-addon").html() +
            "：" +
            window.changeApply[$t_flag.prop("name")].afterText;
          if ($t_flag.prop("name") == "specialBusinessScope") {
            var textFinal = "";
            var text = window.changeApply[$t_flag.prop("name")].valueAfter;
            if (text.indexOf(",") != -1) {
              var texts = text.split(",");
            } else {
              var texts = new Array();
              texts[0] = text;
            }
            for (var i = 0; i < texts.length; i++) {
              if (texts[i] == "1") {
                textFinal += "一般纳税人资格证明,";
              } else if (texts[i] == "2") {
                textFinal += "可经营抗生素,";
              } else if (texts[i] == "3") {
                textFinal += "可经营注射剂,";
              } else if (texts[i] == "4") {
                textFinal += "可经营终止妊娠,";
              } else if (texts[i] == "5") {
                textFinal += "可经营特殊管理药品,";
              } else if (texts[i] == "6") {
                textFinal += "可经营含兴奋剂,";
              } else if (texts[i] == "7") {
                textFinal += "可经营抗肿瘤治疗药商品,";
              } else if (texts[i] == "8") {
                textFinal += "麻醉药品,";
              } else if (texts[i] == "9") {
                textFinal += "精神药品,";
              } else if (texts[i] == "10") {
                textFinal += "医疗用毒性药品,";
              } else if (texts[i] == "12") {
                textFinal += "无特殊属性,";
              }
            }
            textFinal = textFinal.substring(0, textFinal.length - 1);
            content =
              $t_flag
                .parents(".input-group")
                .find(".input-group-addon")
                .html() +
              "：" +
              textFinal;
          }
        }
        break;
      case "textarea": //为文本域
        if (
          window.changeApply[$t_flag.prop("name")] != undefined &&
          window.changeApply[$t_flag.prop("name")][varObj.status] != 0
        ) {
          if (window.changeApply[$t_flag.prop("name")].afterText != "") {
            if (window.changeApply[$t_flag.prop("name")].afterText == null) {
              window.changeApply[$t_flag.prop("name")].afterText = "";
            }
            content =
              $t_flag.prevAll(".input-group-addon").eq(0).html() +
              "：" +
              window.changeApply[$t_flag.prop("name")].afterText;
          }
        }
        break;
      case "text": //为文本
        var $t_flag_hidden = $("#" + $t_flag.attr("changeapplyflag"));
        if ($t_flag.attr("changeapplyflag") && $t_flag_hidden.length) {
          //if(window.changeApply[$t_flag.prop('name')]){
          if (
            window.changeApply[$t_flag_hidden.prop("name")] != undefined &&
            window.changeApply[$t_flag_hidden.prop("name")][varObj.status] != 0
          ) {
            //content = $t_flag.prevAll('.input-group-addon').eq(0).html() + '：' + window.changeApply[$t_flag_hidden.prop('name')].afterText;
            if (
              window.changeApply[$t_flag_hidden.prop("name")].afterText != ""
            ) {
              if (
                window.changeApply[$t_flag_hidden.prop("name")].afterText ==
                null
              ) {
                window.changeApply[$t_flag_hidden.prop("name")].afterText = "";
              }
              content =
                $t_flag.prevAll(".input-group-addon").eq(0).html() +
                "：" +
                window.changeApply[$t_flag_hidden.prop("name")].afterText;
            }
          }
        } else if ($t_flag.attr("changeapplyflag") == "registerList") {
          if (changeApply.registerList) {
            var cityAry = [],
              v = changeApply.registerList[varObj.afterValue];
            cityAry.push(ChineseDistricts[86][v.registerProvince]);
            cityAry.push(" ");
            cityAry.push(ChineseDistricts[v.registerProvince][v.registerCity]);
            cityAry.push(" ");
            cityAry.push(
              ChineseDistricts[v.registerCity][v.registerArea]
                ? ChineseDistricts[v.registerCity][v.registerArea]
                : ChineseDistricts[v.registerProvince][v.registerArea]
            );
            cityAry.push(" ");
            if (v.registerStreet) {
              cityAry.push(changeApply.registerList.afterText); //$t_flag.parent().prev().find('select').attr('curStreetName')
              cityAry.push(" ");
            }
            cityAry.push(v.registerDetail);
            cityAry.push(" <br>");

            content =
              $t_flag
                .parents(".input-group")
                .find(".input-group-addon")
                .html() +
              ':<br><div style="margin-left: 72px;margin-top: -20px;">' +
              cityAry.join("") +
              "</div>";
          }
        } else if (
          $t_flag.attr("changeapplyflag") == "customerDeliveryAddressVoList"
        ) {
          //收货地址
          if (changeApply.customerDeliveryAddressVoList) {
            var cityAry = [],
              v = changeApply.customerDeliveryAddressVoList[varObj.afterValue];
            if (Array.isArray(v)) {
              v = v[0];
            }
            if (!v) {
              return false;
            }
            cityAry.push(ChineseDistricts[86][v.province2]);
            cityAry.push(" ");
            cityAry.push(
              ChineseDistricts[v.province2][v.shippingAddressCityId]
            );
            cityAry.push(" ");
            cityAry.push(
              ChineseDistricts[v.shippingAddressCityId][
                v.shippingAddressDistrictId
              ]
                ? ChineseDistricts[v.shippingAddressCityId][
                    v.shippingAddressDistrictId
                  ]
                : ChineseDistricts[v.shippingAddressCityId][
                    v.shippingAddressDistrictId
                  ]
            );
            cityAry.push(" ");
            if (v.shippingAddressStreetId) {
              var obj_afterText =
                changeApply.customerDeliveryAddressVoList.afterText;
              var afterTextArr = "";
              if (
                obj_afterText.indexOf("[") == -1 ||
                Array.isArray(obj_afterText)
              ) {
                afterTextArr = Array.isArray(obj_afterText)
                  ? obj_afterText[0]
                  : obj_afterText;
              } else {
                afterTextArr = JSON.parse(
                  changeApply.customerDeliveryAddressVoList.afterText
                );
                afterTextArr = afterTextArr[0].街道地址;
              }

              cityAry.push(afterTextArr); //$t_flag.parent().prev().find('select').attr('curStreetName')
              // cityAry.push(' ');
              // cityAry.push(changeApply.customerDeliveryAddressVoList.afterText);//$t_flag.parent().prev().find('select').attr('curStreetName')
              cityAry.push(" ");
            }
            cityAry.push(v.shippingAddressInput);
            cityAry.push(" <br>");

            content =
              $t_flag
                .parents(".input-group")
                .find(".input-group-addon")
                .html() +
              ':<br><div style="margin-left: 72px;margin-top: -20px;">' +
              cityAry.join("") +
              "</div>";
          }
        } else if (
          $t_flag.attr("changeapplyflag") == "customerStorageAddressVOList"
        ) {
          //客户资料变更  仓库地址
          if (changeApply.customerStorageAddressVOList) {
            var cityAry = [],
              v = changeApply.customerStorageAddressVOList[varObj.afterValue];
            if (v && v.repertoryCity) {
              cityAry.push(ChineseDistricts[86][v.repertoryProvince]);
              cityAry.push(" ");
              cityAry.push(
                ChineseDistricts[v.repertoryProvince][v.repertoryCity]
              );
              cityAry.push(" ");
              cityAry.push(ChineseDistricts[v.repertoryCity][v.repertoryArea]);
              cityAry.push(" ");
              if (v.registerStreet) {
                cityAry.push(
                  changeApply.customerStorageAddressVOList.afterText
                ); //$t_flag.parent().prev().find('select').attr('curStreetName')
                cityAry.push(" ");
              }
              cityAry.push(v.repertoryDetail);
              cityAry.push(" <br>");

              content =
                $t_flag
                  .parents(".input-group")
                  .find(".input-group-addon")
                  .html() +
                ':<br><div style="margin-left: 72px;margin-top: -20px;">' +
                cityAry.join("") +
                "</div>";
            }
          }
        } else if (
          $t_flag.attr("changeapplyflag") == "customerBillingAddressVoList"
        ) {
          //账单  地址
          if (changeApply.customerBillingAddressVoList) {
            var cityAry = [];
            $.each(
              changeApply.customerBillingAddressVoList.valueAfter,
              function (i, v) {
                cityAry.push(ChineseDistricts[86][v.repertoryProvince]);
                cityAry.push(" ");
                cityAry.push(
                  ChineseDistricts[v.repertoryProvince][v.repertoryCity]
                );
                cityAry.push(" ");
                cityAry.push(
                  ChineseDistricts[v.repertoryCity][v.repertoryArea]
                );
                cityAry.push(" ");
                if (v.registerStreet) {
                  /**
                   * RM 2018-10-24
                   * 当资料变更申请的时候，账单地址。保存的的街道信息，保存在afterText中是数组的形式，
                   * 但是，当查看录入中状态的详情时，后端返回的afterText是 字符串类型的对象，
                   * 所以下面根据afterText 的类型判断应该怎样取值，然后回显、
                   */
                  var obj_afterText =
                    changeApply.customerBillingAddressVoList.afterText;
                  var afterTextArr = Array.isArray(obj_afterText)
                    ? obj_afterText
                    : JSON.parse(
                        changeApply.customerBillingAddressVoList.afterText
                      );
                  cityAry.push(
                    Array.isArray(obj_afterText)
                      ? afterTextArr[i]
                      : afterTextArr[i].街道地址
                  ); //$t_flag.parent().prev().find('select').attr('curStreetName')
                  cityAry.push(" ");
                }
                cityAry.push(v.repertoryDetail);
                cityAry.push(" <br>");
              }
            );
            content =
              $t_flag
                .parents(".input-group")
                .find(".input-group-addon")
                .html() +
              ':<br><div style="margin-left: 104px;margin-top: -19px;">' +
              cityAry.join("") +
              "</div>";
          }
        } else if (
          $t_flag.attr("changeapplyflag") == "supplierRepertoryAddressVOList"
        ) {
          //仓库地址
          if (changeApply.supplierRepertoryAddressVOList) {
            var cityAry = [];
            $.each(
              changeApply.supplierRepertoryAddressVOList[varObj.afterValue],
              function (i, v) {
                cityAry.push(ChineseDistricts[86][v.repertoryProvince]);
                cityAry.push(" ");
                cityAry.push(
                  ChineseDistricts[v.repertoryProvince][v.repertoryCity]
                );
                cityAry.push(" ");
                cityAry.push(
                  ChineseDistricts[v.repertoryCity][v.repertoryArea]
                );
                cityAry.push(" ");
                if (v.registerStreet) {
                  cityAry.push(
                    $t_flag.parent().prev().find("select").attr("curStreetName")
                  );
                  cityAry.push(" ");
                }
                cityAry.push(v.repertoryDetail);
                cityAry.push(" <br>");
              }
            );
            content =
              $t_flag
                .parents(".input-group")
                .find(".input-group-addon")
                .html() +
              ':<br><div style="margin-left: 72px;margin-top: -19px;">' +
              cityAry.join("") +
              "</div>";
          }
        } else if (
          $t_flag.attr("changeapplyflag") == "supplierManufactoryVOList"
        ) {
          //对应生产厂商
          if (changeApply.supplierManufactoryVOList) {
            var manufactoryNameAry = [];
            $.each(
              changeApply.supplierManufactoryVOList[varObj.afterValue],
              function (i, v) {
                manufactoryNameAry.push(v["manufactoryName"]);
              }
            );
            content =
              $t_flag
                .parents(".input-group")
                .find(".input-group-addon")
                .html() +
              ':<br><div style="margin-left: 100px;margin-top: -20px;">' +
              manufactoryNameAry.join(",") +
              "</div>";
          }
        } else if (
          $t_flag.attr("changeapplyflag") == "supplierExtendItemVOList"
        ) {
          //如果付款方式
          var parentCodeAry = [],
            parentCodeObj = {};
          if (!$.isEmptyObject(changeApply)) {
            // 如果付款方式结算方式有修改。不加if会报错
            if (
              window.changeApply.supplierExtendItemVOList &&
              window.changeApply.supplierExtendItemVOList[varObj.afterValue]
            ) {
              // 如果有修改
              $(
                window.changeApply.supplierExtendItemVOList[varObj.afterValue]
              ).each(function (i, v) {
				const status = ["2001", "2004"].includes(v.code);
				if (status) {
					v.parentCode = v.code
				}
                if (v.code && !status) {
                  var $t_hidden_val = $(
                    '.paymentSettlement .childCode input[value="' +
                      v.code +
                      '"]'
                  );
                  parentCodeObj[v.parentCode]
                    ? ""
                    : (parentCodeObj[v.parentCode] = "");

                  if ($t_hidden_val.length <= 0) return;
                  if ($.inArray(v.parentCode, parentCodeAry) < 0) {
                    parentCodeAry.push(v.parentCode);
                    parentCodeObj[v.parentCode] += $(
                      '.paymentSettlement input[type="checkbox"][value="' +
                        v.parentCode +
                        '"]'
                    )
                      .next("span")
                      .html(); //标题
                  }
                  parentCodeObj[v.parentCode] +=
                    "," +
                    $t_hidden_val
                      .parent(".childCode")
                      .find(".childName")
                      .html() +
                    v.value +
                    $t_hidden_val.nextAll("span").html(); //子集内容
                } else {
                  //parentCodeObj[v.parentCode] += $('.paymentSettlement input[type="checkbox"][value="' + v.parentCode + '"]').next('span').html() //标题
                  // 客戶资料变更  结算方式 第一项没有 子项，然后数据拼接时 threeEvidenceAll 等于undefined
                  if (JSON.stringify(parentCodeObj) == "{}") {
                    parentCodeObj[v.parentCode] = $(
                      '.paymentSettlement input[type="checkbox"][value="' +
                        v.parentCode +
                        '"]'
                    )
                      .next("span")
                      .html(); //标题
                  } else {
                    parentCodeObj[v.parentCode] += $(
                      '.paymentSettlement input[type="checkbox"][value="' +
                        v.parentCode +
                        '"]'
                    )
                      .next("span")
                      .html(); //标题
                  }
                }
              });
            }
          }

          $.each(parentCodeObj, function (i, v) {
            if (
              $('.parentCode[data-type][value="' + i + '"]').attr(
                "data-type"
              ) == "1" &&
              content.indexOf("付款方式") < 0
            ) {
              content +=
                '&nbsp;&nbsp;&nbsp;<i style="color:red;">*</i>付款方式：<br>';
            } else if (
              $('.parentCode[data-type][value="' + i + '"]').attr(
                "data-type"
              ) == "2" &&
              content.indexOf("结算方式") < 0
            ) {
              content +=
                '&nbsp;&nbsp;&nbsp;<i style="color:red;">*</i>结算方式：<br>';
            }
            if (v == "") {
              // 如果结算方式 的选项没有可输入框，但是也被checked ，在查看的时候依然需要显示，所以得拿到当前被选中项的名称
              v = $('.parentCode[data-type][value="' + i + '"]')
                .next("span")
                .text();
            }

            var reg = new RegExp("undefined", "g");
            content +=
              "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
              v.replace(reg, "") +
              "<br>";
            if (i === "2002" || i === "2003") {
              let str = "";
              if (settleMultipleDateListObj && settleMultipleDateListObj[i]) {
                settleMultipleDateListObj[i].forEach((item) => {
                  let tishiStr = i === "2002" ? "对账账期" : "实销实结";
                  let cirStr = item.settlementCycle;
                  let dateStr = item.settlementDate;
                  let dayStr = item.paymentDay;
                  let allstr =
                    tishiStr +
                    ",结算周期" +
                    cirStr +
                    "天,结算日" +
                    dateStr +
                    "日,支付日" +
                    dayStr +
                    "日";
                  str +=
                    "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
                    allstr +
                    "<br>";
                });
              }
              content += str;
            }
          });
        } else if (
          $t_flag.attr("changeapplyflag") == "customerExtendItemVoList" &&
          window.changeApply.customerExtendItemVoList
        ) {
          //如果付款方式 客户资料变更
          var parentCodeAry = [],
            parentCodeObj = {};
          if (changeApply) {
            if (!$.isEmptyObject(changeApply)) {
              $(
                window.changeApply.customerExtendItemVoList[varObj.afterValue]
              ).each(function (i, v) {
                if (v.code) {
                  var $t_hidden_val = $(
                    '.paymentSettlement .childCode input[value="' +
                      v.code +
                      '"]'
                  );
                  parentCodeObj[v.parentCode]
                    ? ""
                    : (parentCodeObj[v.parentCode] = "");
                  if ($t_hidden_val.length <= 0) return;
                  if ($.inArray(v.parentCode, parentCodeAry) < 0) {
                    parentCodeAry.push(v.parentCode);
                    parentCodeObj[v.parentCode] += $(
                      '.paymentSettlement input[type="checkbox"][value="' +
                        v.parentCode +
                        '"]'
                    )
                      .next("span")
                      .html(); //标题
                  }
                  parentCodeObj[v.parentCode] +=
                    "," +
                    $t_hidden_val
                      .parent(".childCode")
                      .find(".childName")
                      .html() +
                    v.value +
                    $t_hidden_val.nextAll("span").html(); //子集内容
                } else {
                  //parentCodeObj[v.parentCode] += $('.paymentSettlement input[type="checkbox"][value="' + v.parentCode + '"]').next('span').html() //标题
                  // 客戶资料变更  结算方式 第一项没有 子项，然后数据拼接时 threeEvidenceAll 等于undefined
                  if (JSON.stringify(parentCodeObj) == "{}") {
                    parentCodeObj[v.parentCode] = $(
                      '.paymentSettlement input[type="checkbox"][value="' +
                        v.parentCode +
                        '"]'
                    )
                      .next("span")
                      .html(); //标题
                  } else {
                    parentCodeObj[v.parentCode] += $(
                      '.paymentSettlement input[type="checkbox"][value="' +
                        v.parentCode +
                        '"]'
                    )
                      .next("span")
                      .html(); //标题
                  }
                }
              });
            }
          }

          $.each(parentCodeObj, function (i, v) {
            if (
              $('.parentCode[data-type][value="' + i + '"]').attr(
                "data-type"
              ) == "1" &&
              content.indexOf("付款方式") < 0
            ) {
              content +=
                '&nbsp;&nbsp;&nbsp;<i style="color:red;">*</i>付款方式：<br>';
            } else if (
              $('.parentCode[data-type][value="' + i + '"]').attr(
                "data-type"
              ) == "2" &&
              content.indexOf("结算方式") < 0
            ) {
              content +=
                '&nbsp;&nbsp;&nbsp;<i style="color:red;">*</i>结算方式：<br>';
            }
            if (v == "") {
              // 如果结算方式 的选项没有可输入框，但是也被checked ，在查看的时候依然需要显示，所以得拿到当前被选中项的名称
              v = $('.parentCode[data-type][value="' + i + '"]')
                .next("span")
                .text();
            }

            content +=
              "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
              v +
              "<br>";
            if (i === "2002" || i === "2003") {
              let str = "";
              if (settleMultipleDateListObj && settleMultipleDateListObj[i]) {
                settleMultipleDateListObj[i].forEach((item) => {
                  let tishiStr = i === "2002" ? "对账账期" : "实销实结";
                  let cirStr = item.settlementCycle;
                  let dateStr = item.settlementDate;
                  let dayStr = item.paymentDay;
                  let allstr =
                    tishiStr +
                    ",结算周期" +
                    cirStr +
                    "天,结算日" +
                    dateStr +
                    "日,支付日" +
                    dayStr +
                    "日";
                  str +=
                    "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
                    allstr +
                    "<br>";
                });
              }
              content += str;
            }
          });
        } else {
          if ($t_flag.filter('[data-role="tagsinput"]').length) {
            if (window.changeApply[$t_flag.attr("name")]) {
              content =
                '<div style="word-break: break-all;">' +
                $t_flag
                  .parent("div.tagsinput")
                  .prev(".input-group-addon")
                  .html() +
                "：" +
                window.changeApply[$t_flag.attr("name")].afterText +
                "</div>";
            }
          } else if (
            window.changeApply[$t_flag.attr("name")] != undefined &&
            window.changeApply[$t_flag.attr("name")][varObj.status] != 0
          ) {
            content =
              '<div style="word-break: break-all;">' +
              $t_flag
                .parents(".input-group")
                .find(".input-group-addon")
                .html() +
              "：" +
              window.changeApply[$t_flag.attr("name")].afterText +
              "</div>";
          }
        }

        break;
      case "": //为tabs切换
        if ($t_flag.is("a")) {
          var tableData = window.changeApplyList[$t_flag.prop("name")];

          var $t_ul = $t.parents("ul.nav");
          var $t_ul_nav_content = $t_ul.next(".nav-content");
          var $t_table = $t_ul_nav_content.find("table.XGridUI:visible");

          if ($t_ul.length) {
            $t_flag.css("padding-right", "25px");
          } else {
            $t_ul_nav_content = $t_flag
              .parents(".panel-heading")
              .next(".panel-body")
              .find(".nav-content");
            $t_table = $t_ul_nav_content.find("table.XGridUI:visible");
          }

          var $t_table_param = $t_table.XGrid("getGridParam");

          if ($t_table.length) {
            width = $t_table.width() > width ? $t_table.width() * 0.75 : width;

            if (tableData) {
              // tableData  == undefined  时报错，所以加if
              content =
                '<div style="width: 100%;overflow-x: scroll;"><table class="changeApplyTableHideBlock" id="changeApplyTable"></table></div>'; //$('#changeApplyTable').clone();
              setTimeout(function () {
                var gridObj = {
                  data: [],
                  colNames: $t_table_param.colNames,
                  colModel: $t_table_param.colModel,
                };
                //如果列表包含 gridComplete回调
                if ($t_table_param.gridComplete) {
                  gridObj.gridComplete = $t_table_param.gridComplete;
                }
                //如果列表中包含tree
                if (
                  JSON.stringify($t_table_param.colModel).indexOf("Ztree") > 0
                ) {
                  //initTable3($("#supplierBaseId").val(), 0, $('#changeApplyTable'));
                  $("#changeApplyTable").XGrid(gridObj);
                  var data = {
                    result: {
                      list: tableData[varObj.afterValue],
                    },
                  };
                  handleData(data, $("#changeApplyTable"));
                } else {
                  gridObj.data = tableData[varObj.afterValue];
                  let _data = gridObj.data;

                  gridObj.gridComplete = function () {
                    _data.forEach(function (item) {
                      setPharmacyTypeCheck(item, "changeApplyTable");
                    });
                  };
                  $("#changeApplyTable").XGrid(gridObj);
                }
              }, 0);
            }
          } else {
            //tabs中附件上传
            var $t_content = $t_ul.next(".nav-content"),
              htmlAry = [];
            // if (tableData) {  // tableData  == undefined  时报错，所以加if
            //     tableData = $.isArray(tableData) ? tableData : tableData[varObj.afterValue];
            //     $(tableData).each(function (i, v) {
            //         var a = $t_content.find('input[type="checkbox"][value="' + v.certificateType + '"]').parent('label');
            //         a.find('input').attr('checked', 'checked');
            //         htmlAry.push(a.html() + '<br>');
            //     });
            //     content = htmlAry.join('');
            // }
            if (tableData) {
              // tableData  == undefined  时报错，所以加if
              var dataArr = [];
              if (tableData.columnValue == "customerContractVos") {
                tableData = tableData.valueAfter;
                for (var i = 0; i < tableData.length; i++) {
                  if (tableData[i].length != 0) {
                    var a = null;
                    if (
                      $t_content.parents("form").attr("id") ==
                      "customerOtherInfo"
                    ) {
                      if (tableData[i].certificateType) {
                        a = $t_content
                          .find(
                            'input[type="checkbox"][value="' +
                              tableData[i].certificateType +
                              '"]'
                          )
                          .next("label");
                        htmlAry.push(
                          '<span data-val="' +
                            tableData[i].certificateType +
                            '">' +
                            a.html() +
                            "</span>" +
                            "<br>"
                        );
                      } else {
                        a = $t_content
                          .find(
                            'input[type="checkbox"][value="' +
                              tableData[i].contractType +
                              '"]'
                          )
                          .next("label");
                        htmlAry.push(
                          '<span data-val="' +
                            tableData[i].contractType +
                            '">' +
                            a.html() +
                            "</span>" +
                            "<br>"
                        );
                      }
                      //$t_content.find('input[type="checkbox"][value="' + v.certificateType + '"]').prop('checked', true);
                    } else {
                      a = $t_content
                        .find(
                          'input[type="checkbox"][value="' +
                            tableData[i].certificateType +
                            '"]'
                        )
                        .parent("label");
                      a.find("input").prop("checked", true);
                    }
                    dataArr.push(tableData[i].enclosureList);
                  }
                }
              } else {
                tableData = $.isArray(tableData)
                  ? tableData
                  : tableData[varObj.afterValue];
                $(tableData).each(function (i, v) {
                  if (v.enclosureList.length != 0) {
                    var a = null;
                    if (
                      $t_content.parents("form").attr("id") ==
                      "customerOtherInfo"
                    ) {
                      a = $t_content
                        .find(
                          'input[type="checkbox"][value="' +
                            v.certificateType +
                            '"]'
                        )
                        .next("label");
                      //$t_content.find('input[type="checkbox"][value="' + v.certificateType + '"]').prop('checked', true);
                    } else {
                      a = $t_content
                        .find(
                          'input[type="checkbox"][value="' +
                            v.certificateType +
                            '"]'
                        )
                        .parent("label");
                      a.find("input").prop("checked", true);
                    }
                    htmlAry.push(
                      '<span data-val="' +
                        v.certificateType +
                        '">' +
                        a.html() +
                        "</span>" +
                        "<br>"
                    );
                    dataArr.push(v.enclosureList);
                  }
                });
              }
              content = htmlAry.join("");
              if (dataArr.length == 0) {
                utils
                  .dialog({
                    title: "提示",
                    content: "没有可以显示的内容。",
                    okValue: "确定",
                    ok: function () {},
                  })
                  .showModal();
                return false;
              }
            }
            content = htmlAry.join("");
            if (content != "") {
              content = '<div id="id_dataChangeDiv">' + content + "</div>";
            }
          }
        }
        break;
      case "button":
        if ($t_flag.parents("form").attr("id") == "billInfo_form") {
          if (
            window.changeApply["customerEnclosureVoList"] &&
            window.changeApply["customerEnclosureVoList"]["valueAfter"] != "" &&
            window.changeApply["customerEnclosureVoList"]["valueAfter"] != "[]"
          ) {
            utils
              .dialog({
                title: "提示",
                content: "请点击图片进行预览查看",
                okVal: "确定",
                ok: function () {},
              })
              .showModal();
            return false;
          } else {
            utils
              .dialog({
                title: "提示",
                content: "数据被清空",
                okVal: "确定",
                ok: function () {},
              })
              .showModal();
            return false;
          }
        }
        break;
    }

    /*展示变更*/ //content: content.find('i.text-require').hide(),
    if (content == "") {
      utils
        .dialog({
          title: "　提示",
          content: "没有变更数据",
          quickClose: true,
          timeout: 2500,
        })
        .showModal();
    } else {
      /*            var a = '';
                        $(content).clone().each(function (i, v) {
                            $(v).find('input,textarea').attr('disabled', 'disabled');
                            a += $(v).prop('outerHTML');
                        });*/
      utils
        .dialog({
          align: "top",
          title: "　变更数据",
          width: width,
          content: content,
          quickClose: true,
          okValue: "确定",
          ok: function () {
            if (tableData && tableData.columnValue == "customerContractVos") {
              tableData = tableData.valueAfter;
              for (var i = 0; i < tableData.length; i++) {
                if (tableData[i].length != 0) {
                  var a = $t_content
                    .find(
                      'input[type="checkbox"][value="' +
                        tableData[i].certificateType +
                        '"]'
                    )
                    .parent("label");
                  a.find("input").prop("checked", false);
                }
              }
            } else {
              $(tableData).each(function (i, v) {
                /**
                 * 2018-09-18 rm
                 *  没有enclosureList 属性的时候点确定按钮弹窗，报错，无法关闭
                 */
                if (v.enclosureList) {
                  if (v.enclosureList.length != 0) {
                    var a = $t_content
                      .find(
                        'input[type="checkbox"][value="' +
                          v.certificateType +
                          '"]'
                      )
                      .parent("label");
                    //a.find('input').prop('checked',false)
                  }
                }
              });
            }
          },
        })
        .showModal();

      setTimeout(function () {
        $(".ui-dialog")
          .find("input,select,textarea")
          .attr("disabled", "disabled");
      }, 200);
    }
  },
  //三证合一FN
  changeApply_ThreeInone: function (
    $yyzzh,
    $zzjgdmzh,
    $swdjzh,
    v,
    varObj,
    eventTarget
  ) {
    if ($yyzzh && $zzjgdmzh && $swdjzh) {
      if (v == "1") {
        //如果更改为是

        //缓存数据
        $zzjgdmzh.attr("data-cache", $zzjgdmzh.val());
        $swdjzh.attr("data-cache", $swdjzh.val());

        //加载缓存数据
        /*$zzjgdmzh.val($yyzzh.val());
                $swdjzh.val($yyzzh.val());*/

        var $yyzzh_val =
          eventTarget == "supplierBusinessNum"
            ? $yyzzh.val()
            : changeApply[$yyzzh.prop("name")] &&
              changeApply[$yyzzh.prop("name")][varObj.afterValue]
            ? changeApply[$yyzzh.prop("name")][varObj.afterValue]
            : $yyzzh.val();

        var selObj = {
          [varObj.name]: $zzjgdmzh.prop("name"),
          [varObj.afterValue]: $yyzzh_val,
          [varObj.beforeValue]: $zzjgdmzh.val(),
          afterText: $yyzzh_val,
          [varObj.status]: "1",
        };
        //-1：不变，0：撤销，1：新增，2：变更又变更
        if (
          window.changeApplyBak &&
          window.changeApplyBak[selObj[varObj.name]]
        ) {
          selObj[varObj.status] = 2;
        }
        window.changeApply[selObj[varObj.name]] = selObj;
        selObj = {
          [varObj.name]: $swdjzh.prop("name"),
          [varObj.afterValue]: $yyzzh_val,
          [varObj.beforeValue]: $swdjzh.val(),
          afterText: $yyzzh_val,
          [varObj.status]: "1",
        };
        //-1：不变，0：撤销，1：新增，2：变更又变更
        if (
          window.changeApplyBak &&
          window.changeApplyBak[selObj[varObj.name]]
        ) {
          selObj[varObj.status] = 2;
        }
        window.changeApply[selObj[varObj.name]] = selObj;

        //隐藏图标
        $zzjgdmzh.nextAll(".yulanInput").hide();
        $swdjzh.nextAll(".yulanInput").hide();
      } else {
        //如果更改为否

        //获取缓存数据
        /*$zzjgdmzh.val($zzjgdmzh.attr('data-cache'));
                $swdjzh.val($swdjzh.attr('data-cache'));
    */
        var selObj_ = {};
        if (window[$zzjgdmzh.prop("name")]) {
          selObj_ = window[$zzjgdmzh.prop("name")];
          delete window[$zzjgdmzh.prop("name")];
        }

        //如果是 三证合一 触发，新增空变更
        if (eventTarget == "threeEvidenceAll") {
          var selObj = {
            [varObj.name]: $zzjgdmzh.prop("name"),
            [varObj.afterValue]: selObj_[[varObj.afterValue]] || "",
            [varObj.beforeValue]: $zzjgdmzh.val(),
            afterText: selObj_[[varObj.afterText]] || "",
            [varObj.status]: "1",
          };
          //-1：不变，0：撤销，1：新增，2：变更又变更
          if (
            window.changeApplyBak &&
            window.changeApplyBak[selObj[varObj.name]]
          ) {
            //selObj[varObj.status] = 2;
            // window.changeApplyBak[selObj[varObj.name]][varObj.status] = 2;
            //0919 邓涛改，变更状态不应变更Bak中的状态
            window.changeApply[selObj[varObj.name]][varObj.status] = 2;
          }
          window.changeApply[selObj[varObj.name]] = selObj;

          if (window[$swdjzh.prop("name")]) {
            selObj_ = window[$swdjzh.prop("name")];
            delete window[$swdjzh.prop("name")];
          }
          selObj = {
            [varObj.name]: $swdjzh.prop("name"),
            [varObj.beforeValue]: $swdjzh.val(),
            [varObj.afterValue]: selObj_[[varObj.afterValue]] || "",
            afterText: selObj_[[varObj.afterText]] || "",
            [varObj.status]: "1",
          };
          //-1：不变，0：撤销，1：新增，2：变更又变更
          if (
            window.changeApplyBak &&
            window.changeApplyBak[selObj[varObj.name]]
          ) {
            //selObj[varObj.status] = 2;
            //window.changeApplyBak[selObj[varObj.name]][varObj.status] = 2;
            //0919 邓涛改，变更状态不应变更Bak中的状态
            window.changeApply[selObj[varObj.name]][varObj.status] = 2;
          }
          window.changeApply[selObj[varObj.name]] = selObj;
        } else if (eventTarget == "threeInOne") {
          //
          var selObj = {
            [varObj.name]: $zzjgdmzh.prop("name"),
            [varObj.afterValue]: "",
            [varObj.beforeValue]: $zzjgdmzh.val(),
            afterText: "",
            [varObj.status]: "0",
          };
          //-1：不变，0：撤销，1：新增，2：变更又变更
          if (
            window.changeApplyBak &&
            window.changeApplyBak[selObj[varObj.name]]
          ) {
            //selObj[varObj.status] = 2;
            // window.changeApplyBak[selObj[varObj.name]][varObj.status] = 2;
            //0919 邓涛改，变更状态不应变更Bak中的状态
            window.changeApply[selObj[varObj.name]][varObj.status] = 2;
          }
          window.changeApply[selObj[varObj.name]] = selObj;

          selObj = {
            [varObj.name]: $swdjzh.prop("name"),
            [varObj.beforeValue]: $swdjzh.val(),
            [varObj.afterValue]: "",
            afterText: "",
            [varObj.status]: "0",
          };
          //-1：不变，0：撤销，1：新增，2：变更又变更
          if (
            window.changeApplyBak &&
            window.changeApplyBak[selObj[varObj.name]]
          ) {
            //selObj[varObj.status] = 2;
            // window.changeApplyBak[selObj[varObj.name]][varObj.status] = 2;
            //0919 邓涛改，变更状态不应变更Bak中的状态
            window.changeApply[selObj[varObj.name]][varObj.status] = 2;
          }
          window.changeApply[selObj[varObj.name]] = selObj;
        }

        //展示图标
        $zzjgdmzh.nextAll(".yulanInput").show();
        $swdjzh.nextAll(".yulanInput").show();
      }
      if (
        $("#customerApplVo") &&
        $("#customerApplVo").length &&
        $('[name="businessLicenseNum"]') &&
        $('[name="businessLicenseNum"]').length
      ) {
        window.changeApply["taxpayerNum"] = {};
        window.changeApply["taxpayerNum"]["columnValue"] = "taxpayerNum";
        window.changeApply["taxpayerNum"]["afterText"] = $(
          '[name="businessLicenseNum"]'
        ).val();
        window.changeApply["taxpayerNum"]["valueAfter"] = $(
          '[name="businessLicenseNum"]'
        ).val();
        window.changeApply["taxpayerNum"]["valueBefore"] = $(
          '[name="taxpayerNum"]'
        ).val();
        window.changeApply["taxpayerNum"]["changeStatus"] = "1";
        window.changeApply["organizationCodeNumber"] = {};
        window.changeApply["organizationCodeNumber"]["columnValue"] =
          "organizationCodeNumber";
        window.changeApply["organizationCodeNumber"]["afterText"] = $(
          '[name="businessLicenseNum"]'
        ).val();
        window.changeApply["organizationCodeNumber"]["valueAfter"] = $(
          '[name="businessLicenseNum"]'
        ).val();
        window.changeApply["organizationCodeNumber"]["valueBefore"] = $(
          '[name="taxpayerNum"]'
        ).val();
        window.changeApply["organizationCodeNumber"]["changeStatus"] = "1";
        window.changeApply["taxRegistryNumber"] = {};
        window.changeApply["taxRegistryNumber"]["columnValue"] =
          "taxRegistryNumber";
        window.changeApply["taxRegistryNumber"]["afterText"] = $(
          '[name="businessLicenseNum"]'
        ).val();
        window.changeApply["taxRegistryNumber"]["valueAfter"] = $(
          '[name="businessLicenseNum"]'
        ).val();
        window.changeApply["taxRegistryNumber"]["valueBefore"] = $(
          '[name="taxpayerNum"]'
        ).val();
        window.changeApply["taxRegistryNumber"]["changeStatus"] = "1";
        var _varObj = {
          name: "columnValue",
          beforeValue: "valueBefore",
          afterValue: "valueAfter",
          afterText: "afterText",
          status: "changeStatus",
        };
        $.changeApply_updateIcon(window.changeApply, _varObj);
        // window.changeApply['organizationCodeNumber'] = $('businessLicenseNum').val();
      }
    }
  },
  //清除所有变更状态
  clear_changeApply: function () {
    $("i.yulan").show().removeClass("yulanInput_after");
    $("[changeapplybtn]").hide();
  },

  addTelListener: function(){
    if($(".tel-label")){
      //失去焦点绑定tel检查
      $('[name="contactMessage1"]').focus(function(){
        var _input = $(this);
        //手机号
        if(_input.parents(".tel-wrap").find("select").val()==1){
          _input.attr("type", "number");
          _input.removeClass().addClass("form-control Filter_SpaceAndStrLen_Class tel-part {validate:{ required :true,isTel:true}}");
          $.addRules(_input);
        }
      });
      $(".tel-label").on("change", function () {
        var _this = $(this);
        var _input = _this.parents(".row").find('.tel-part');
        _input.val(""); 
        switch (_this.val()) {
          case "":
            _input.removeAttr("placeholder"); 
            _input.attr("type", "text");
            _input.attr("disabled", "true");
            if(_this.hasClass("require")){
              _input.removeClass().addClass("form-control Filter_SpaceAndStrLen_Class tel-part {validate:{ required :true}}");
            } else {
              _input.removeClass().addClass("form-control Filter_SpaceAndStrLen_Class tel-part");
            }
            $.delRules(_input)
            break;
          case "1":
            _input.attr("placeholder", "请输入手机号");
            _input.attr("type", "number");
            _input.removeAttr("disabled");
            _input.removeClass().addClass("form-control Filter_SpaceAndStrLen_Class tel-part {validate:{ required :true,isTel:true}}");
            $.addRules(_input)
            break;
          case "2":
            _input.attr("placeholder", "XXXX-XXXXXXX");
            _input.attr("type", "text");
            _input.removeAttr("disabled");
            _input.removeClass().addClass("form-control Filter_SpaceAndStrLen_Class tel-part {validate:{ required :true}}");
            $.delRules(_input)
            break;
          default:
            _input.removeAttr("placeholder"); 
            _input.attr("type", "text");
            _input.removeAttr("disabled");
            _input.removeClass().addClass("form-control Filter_SpaceAndStrLen_Class tel-part {validate:{ required :true}}");
            $.delRules(_input)
        }
      });
    }
  },
  delRules: function (_input) {
    try {
      _input.rules("add", {isTel:false})
    } catch (error) {
      
    }
  },
  addRules: function (_input) {
    try {
      _input.rules("add", {isTel:true})
    } catch (error) {
      
    }
  }
});

/*
 *tableID:
 * valTitle:
 *
 *
 * */
function checkValDiff() {
  var valDiffOpt = ["签订日期", "委托书编号", "证书类型", "报告年份"];
  var signDateArr = [],
    proxyOderNoArr = [],
    certificateIdArr = [],
    reportDateArr = [];
  var bigArr = [],
    intFlag = 0;
  var table1RowData = $("#table1").XGrid("getRowData");
  var table2RowData = $("#table2").XGrid("getRowData");
  var table3RowData = $("#table3").XGrid("getRowData");
  var table4RowData = $("#table4").XGrid("getRowData");
  if (table1RowData.length > 0) {
    for (var item of table1RowData) {
      signDateArr.push(item.signDate);
    }
    bigArr.push(signDateArr);
  }
  if (table2RowData.length > 0) {
    for (var item of table2RowData) {
      proxyOderNoArr.push(item.proxyOderNo);
    }
    bigArr.push(proxyOderNoArr);
  }
  if (table3RowData.length > 0) {
    for (var item of table3RowData) {
      certificateIdArr.push(item.certificateId);
    }
    bigArr.push(certificateIdArr);
  }
  if (table4RowData.length > 0) {
    for (var item of table4RowData) {
      reportDateArr.push(item.reportDate);
    }
    bigArr.push(reportDateArr);
  }
  //开始验证每一项都不能有重复项
  var flag = false;
  if (bigArr.length > 0) {
    for (var i = 0; i < bigArr.length; i++) {
      for (var j = 0; j < bigArr[i].length; j++) {
        var temp = bigArr[i][j];
        var count = 0;
        if (bigArr[i].length > 1) {
          //判斷当前数据有几条，如果只有一条就没啥可比较的，肯定不会有重复项。直接返回 就行
          for (var k = 0; k < bigArr[i].length; k++) {
            var temp2 = bigArr[i][k];
            if (temp == temp2) {
              count++;
              intFlag = i;
            }
          }
          //由于中间又一次会跟自己本身比较所有这里要判断count>=2
          if (count >= 2) {
            utils
              .dialog({
                title: "提示",
                content: valDiffOpt[intFlag] + "有重复值存在！！！",
                okValue: "确定",
                ok: function () {},
              })
              .showModal();

            return flag;
          } else {
            flag = true;
            return flag;
          }
        }
      }
    }
  } else {
    return false;
  }
}