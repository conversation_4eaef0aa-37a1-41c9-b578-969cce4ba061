$(function() {	
	var dataCenter = $("#dataCenter").val();
	var appCode = $("#appCode").val();	
	var isDataCenter = 0;
	if(dataCenter == 1 && "dataCenter" == appCode) {
		isDataCenter = 1;
	}
	queryRoleListByOrgCode(isDataCenter, appCode);
	
	 
})

//查询条件 选择机构联动角色
function queryRoleListByOrgCode(isDataCenter, appCode){
//	var isDataCenter = 0;
    $.ajax({
        url:"/proxy-sysmanage/sysmanage/resource/treeNodeList",
        data:{isDataCenter: isDataCenter,
        	appCode: appCode},
        type:"get",        
        success:function(data){
            var leftoptions = "";
            if(data.code==0){
                if(data.result){
                     $.each(data.result, function(i, val){  
                    	var icon = val.icon;
                     	if(icon == null) {
                     		icon = '/proxy-sysmanage/static/images/masterdata.png';
                     	}
                     	var title = val.title;
                     	if(title == null) {
                     		title = '';
                     	}
                    	leftoptions += "<li class=''>" +
                    			"<a href='#' class='dropdown-toggle'>" +
                    			"<img class='font_family' src='" + icon + "' alt=''>" +
                    			"<span class='menu-text'>" + title + "</span>" +
                    			"<b class='arrow fa fa-angle-down'></b>" +
                    			"</a>";
                     	
                     	leftoptions += treeNodeUrl(val.children);
                     	leftoptions += "</li>"; 
                     });
                }               
            }else{
                console.log("treeNodeList-->获取数据失败！"); 
            }
//            console.log(leftoptions);  
            $("#menuSideBar").html(leftoptions);
           
            /*$('body').on('mouseenter','#menuSideBar li .btabs', function(){
             	$(this).find('button').css('display','block')
             }).on('mouseleave','#menuSideBar li .btabs',function(){
             	$(this).find('button').css('display','none')
             })*/
        }
    });
}

//菜单层级
function treeNodeUrl(childrenSet) {
	var treeNodeUrl = "";
	if(childrenSet != null && childrenSet.length > 0) {
		treeNodeUrl += "<ul class='submenu'>";
		$.each(childrenSet, function(j, item) { 
			var id = item.id;
	     	if(id == null) {
	     		id = '';
	     	}
	     	var url = item.url;
         	if(url == null) {
         		url = '';
         	}
	     	var title = item.title;
	     	if(title == null) {
	     		title = '';
	     	}
			if(item.children != null && item.children.length > 0) {
//				console.log("title1=" + title + ",url=" + url);
				if(url == '' || url == '/' || url == '/#' || url.indexOf("#") > -1) {
					treeNodeUrl += "<li mid='" + id + "' data-href='' style='padding-left:20px;background: #102442'>" +
										"<a class='btabs dropdown-toggle second' tabindex='-1' href='javascript:void(0);'>" +
											"<span class='menu-text'>" + title + "</span>" +
											"<b class='arrow fa fa-angle-down'></b>" +
										"</a>" +
										"<ul class='submenu'>";	
					$.each(item.children, function(k, item2) {						
						var id = item2.id;
		             	if(id == null) {
		             		id = '';
		             	}
		             	var url = item2.url;
		             	if(url == null) {
		             		url = '';
		             	}
		             	var title = item2.title;
		             	if(title == null) {
		             		title = '';
		             	}	             	
		             	var btnfast = item2.btnfast;
		             	if(btnfast == null) {
		             		btnfast = '';
		             	}
//		             	console.log("title2=" + title + ",url=" + url);
						if(url == '' || url == '/' || url == '/#' || url.indexOf("#") > -1) {			             	
							treeNodeUrl += "<li class='' data-href=''><a href='#' tabindex='-1' class='btabs dropdown-toggle third opentab'>";                           
                                 
							treeNodeUrl += "<span class='menu-text'>" + title + "</span>" +
											"<b class='arrow fa fa-angle-down'></b>" +
											"</a>" +
											"<ul class='submenu'>";
                                 
							if(item2.children != null && item2.children.length > 0) {	
								$.each(item2.children, function(aa, item3) {	
									var id = item3.id;
					             	if(id == null) {
					             		id = '';
					             	}
					             	var url = item3.url;
					             	if(url == null) {
					             		url = '';
					             	}
					             	var title = item3.title;
					             	if(title == null) {
					             		title = '';
					             	}	             
									treeNodeUrl += "<li class='fours' mid='" + id + "' data-href='" + url + "'>" +
														"<a class='btabs third' tabindex='-1' href='javascript:void(0);'>" +
																"<span class='menu-text' title='" + title + "'>" + title + "</span>" +
														"</a>" +
													"</li>";                                  
                                         
								})
							}     
                           treeNodeUrl += "</ul></li>";                         
//							
						} else {
							treeNodeUrl += "<li mid='" + id + "' data-href='" + url + "'>" +
												"<a class='btabs third' tabindex='-1' href='javascript:void(0);'>" +
													"<span title='" + title + "' class='menu-text'>" + title + "</span>";
							 	if(item2.children != null && item2.children.length > 0) {		             		
							 		treeNodeUrl += treeNodeBtn(item2.children);		             		
							 	}
								treeNodeUrl += 			"</a>";
							treeNodeUrl += 	"</li>";	
						}
						
							
//						console.log("treeNodeUrl1111111111=" + treeNodeUrl);
					});				
					treeNodeUrl += "</ul></li>";				
				} else {
					treeNodeUrl += "<li mid='" + id + "' data-href='" + url + "' style='padding-left:20px;background: #102442'>" +
					"<a class='btabs second' tabindex='-1' href='javascript:void(0);'>" +
					"<span class='menu-text'>" + title + "</span>";				
		            					
					if(item.children != null && item.children.length > 0) {						
						treeNodeUrl += treeNodeBtn(item.children);
//						console.log("treeNodeUrl3333333=" + treeNodeUrl);
					}																			
					
					treeNodeUrl += 		"</a>";
					treeNodeUrl += 	"</li>";
				}				
			} else {	
//				console.log("url2=" + url);
				treeNodeUrl += "<li mid='" + id + "' data-href='" + url + "' style='padding-left:20px;background: #102442' >" +
									"<a class='btabs second' tabindex='-1' href='javascript:void(0);'>" +
									"<span class='menu-text'>" + title + "</span>" +
									"</a>" +
								"</li>";
			}			
		});
		treeNodeUrl += "</ul>";
	}	
	return treeNodeUrl;
}
function treeNodeBtn(childrenSet) {
	var treeNodeBtn = "";
	$.each(childrenSet, function(k, item) { 
		var id = item.id;
     	if(id == null) {
     		id = '';
     	}
     	var url = item.url;
     	if(url == null) {
     		url = '';
     	}
     	var title = item.title;
     	if(title == null) {
     		title = '';
     	}	             	
     	var btnfast = item.btnfast;
     	if(btnfast == null) {
     		btnfast = '';
     	}
     	var dataTitle = item.dataTitle;
     	if(dataTitle == null) {
     		dataTitle = '';
     	}
     	
     	if('1' == btnfast) {
     		treeNodeBtn += "<button class='apply' mid='" + id + "' data-href='" + url + "' data-title='" + dataTitle + "'>" + title + "</button>";
     	}
//     	console.log("treeNodeBtn=" + treeNodeBtn);
    });
	return treeNodeBtn;
}
