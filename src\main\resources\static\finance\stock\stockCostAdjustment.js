$(function () {

    /* 合计计算 */
    var totalTable = z_utils.totalTable;
    //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    });
    var sysOrgCode = $("#sysOrgCode").val();
    //商品名称 双击查询
    $('#productDesc').dblclick(function () {
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#productDesc').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#productDesc').val(data.productName);
                    $('#drugCode').val(data.productCode)
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    });


    //商品名称 搜索
    $('#productDesc').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode=' + sysOrgCode, //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data);

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            // console.log(query, suggestions);

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#productDesc").val("");
        }
    });


    $('input[name="postDate"]').attr('value', getNowFormatDate());
    //渲染仓库下拉列表
    initStorageTypeSelect();

    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/initBalance/findStockCostAdjustmentList',
        colNames: ['商品编号', '商品名称', '商品规格', '单位','库房名称', '<span class="text-danger">结存数量</span>', '<span class="text-danger">结存金额</span>', '<span class="text-danger">调账金额</span>', '<span class="text-danger">移动类型</span>', '<span class="text-danger">过账日期</span>', '机构编号','业务类型', '库编号','有效期'],
        colModel: [{
            name: 'drugCode'
        }, {
            name: 'productName'
        }, {
            name: 'specifications'
        }, {
            name: 'packingUnit'
        }, {
            name: 'storageTypeName'
        }, {
            name: 'amount'
        }, {
            name: 'notaxSum',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'adjustmentAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'movementTypeName',
            formatter: function (rowId, cellval, colpos, rwdat, _act) {
                var adjustmentAmount = colpos.adjustmentAmount; //调账金额
                if (adjustmentAmount > 0) {
                    return "调账入库"
                } else if (adjustmentAmount < 0) {
                    return "调账出库";
                }

                return "-";

            }
        }, {
            name: 'postDate',
            rowtype: '#time_black'
        }, {
            name: 'orgCode',
            hidden: true
        }, {
            name: 'channelId',
            hidden: true
        }, {
            name: 'storageType',
            hidden: true
        }, {
            name: 'validateDate',
            hidden: true
        }],
        key: 'drugCode',
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        rownumbers: true,
        multiselect: true,
        altRows: true, //设置为交替行表格,默认为false
        attachRow:true,
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
        gridComplete: function () {
            if (!$(this).XGrid('getRowData').length) {
                utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
            }
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['amount','notaxSum','adjustmentAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                //lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                lastRowEle.find("td[row-describedby="+item+"]").text((item == 'amount'?totalTable(data,item).substring(0,totalTable(data,item).indexOf('.')):parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.')))
            });
        }
    });

    //总计
    totalSum();

    //查询数据，重置data
    $('#searchBtn').on('click', function () {
        search();
    })

    //过账
    $('#passBillBtn').bind('click', function () {
        var obj = $('#X_Table').XGrid('getSeleRow');
        if (!obj||obj.length<1) {
            utils.dialog({content: '请选择要过账的数据！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        utils.dialog({

            title: '提示',
            content: '确认要过账吗？',
            width: '300',
            okValue: '确认',
            ok: function () {

                var data;
                if (obj instanceof Array) {
                    data = obj.map(function (val, index, arr) {
                        return {
                            drugCode: val.drugCode,
                            orgCode: val.orgCode,
                            storageType: val.storageType,
                            postDate: val.postDate,
                            notaxSum:val.notaxSum,
                            channelId:val.channelId
                        };
                    })
                } else if (obj instanceof Object) {
                    data = new Array(obj);
                }
                $.ajax({
                    method: "POST",
                    url: "/proxy-finance/finance/initBalance/checkCostAdjustment",
                    data: {data:JSON.stringify(data)},
                    dataType: 'json',
                    cache: false
                }).done(function (result) {
                    if (result.code == 1) {
                        utils.dialog({
                            title: "提示",
                            content: result.msg + "已不满足成本调整条件，请重新查询后再点击过账",
                            okValue:"确定",
                            ok:function(){
                                search()
                            },

                        }).show();

                    } else {
                        adjustment(data);
                    }
                });
            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).show();


    })

})

/**
 * 搜索
 */
function search() {
    var param = $('#myform').serializeToJSON();
    //总计
    totalSum(param);
    $('#X_Table').setGridParam({
        url: '/proxy-finance/finance/initBalance/findStockCostAdjustmentList',
        postData: param
    }).trigger('reloadGrid');
}
/**
 * 总计
 */
function totalSum(param) {
    $.ajax({
        url: '/proxy-finance/finance/initBalance/getAdjustmentTotal',
        type: 'POST',
        dataType: 'json',
        data: param,
        success: function (data) {
            if(data.code==0){
                var  result = data.result;
                var sum_ele = $('.X_Table_sum .sum');
                if(result==null){
                    $(sum_ele[0]).text(0.00);
                    $(sum_ele[1]).text(0.00);
                    $(sum_ele[2]).text(0.00);
                }else {
                    $(sum_ele[0]).text(result.amount);
                    $(sum_ele[1]).text(parseFloat(result.notaxSum).formatMoney('2', '', ',', '.'));
                    $(sum_ele[2]).text(parseFloat(result.adjustmentAmount).formatMoney('2', '', ',', '.'));
                }
            }
        }
    });

    /*    utils.ajax(param,
            "/proxy-finance/finance/initBalance/getAdjustmentTotal", "POST",
            function (data) {
                if(data.code==0){
                    var  result = data.result;
                    var sum_ele = $('.X_Table_sum .sum');
                    if(result==null){
                        $(sum_ele[0]).text(0.00);
                        $(sum_ele[1]).text(0.00);
                        $(sum_ele[2]).text(0.00);
                    }else {
                        $(sum_ele[0]).text(result.amount);
                        $(sum_ele[1]).text(result.notaxSum);
                        $(sum_ele[2]).text(result.adjustmentAmount);
                    }
                }
            });*/

}

/**
 * 调账
 */
function adjustment(data) {
    utils.ajax({data: JSON.stringify(data)},
        "/proxy-finance/finance/initBalance/updateCostAdjustment", "POST",
        function (data) {
            utils.dialog({
                content: "调账成功",
                timeout: 2000,
                quickClose: true
            }).show();
            if (data.code != 0) {
                return false;
            }
            setTimeout(function () {
                search();
            }, 500);

        });
}


/**
 * 渲染仓库下拉列表
 */
function initStorageTypeSelectTemplate(data) {
    var html = template('storageType-tmp', {list: data});
    document.getElementById('storageType-div').innerHTML = html;
}

/**
 * 获取仓库
 */
function initStorageTypeSelect() {
    $.post("/proxy-sysmanage/sysmanage/dict/querycommonnotpage", {type: 10},
        function (data) {
            console.log(data.result);
            if (data.code == 0) {
                var _data = data.result;
                initStorageTypeSelectTemplate(_data);
            }
        }, "json");
}

/**
 * 获取当前时间，格式YYYY-MM-DD
 * @returns {string}
 */
function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}
