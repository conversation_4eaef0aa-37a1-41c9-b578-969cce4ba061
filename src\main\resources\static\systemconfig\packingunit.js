$(function () {


    $('#Packingunit_Table').XGrid({
        url:'/proxy-sysmanage/sysmanage/dict/querypackingunit',
        colNames: ['包装单位id', '包装单位名称', '助记码', '是否停用', '关键词',  '创建人', '创建日期'/*,'操作'*/],
        colModel: [
            {
                name: 'packingId',
                index: 'packingId',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'packingName',
                index: 'packingName',
                width: 200,//宽度
                editable: true,//是否可编辑
                edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

            }, {
                name: 'packingNumber',
                index: 'packingNumber',
                width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'packingIsstop',
                index: 'packingIsstop',
                width: 150,
                editable: true,
                formatter:isShop,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'packingKeyword',
                index: 'packingKeyword',
                width: 250,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'createUser',
                index: 'createUser',
                width: 250,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'createTime',
                index: 'createTime',
                width: 250,
                sortable: false,
                editable: true,
                formatter:datetimeFormatter,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }/*, {

                width: 60,
                editable: true,
                formatter:operation
            }*/
        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        key:'packingId',
        ondblClickRow: function (id, dom, obj, index, event) {
            //双击事件回调函数
            var el = document.querySelector('#dialog_Block');//html元素
            $('#packingunitform')[0].reset();
            var selRow = obj;
            if(selRow){
                $("[name='packingName']").val(selRow.packingName)
                $("[name='packingKeyword']").val(selRow.packingKeyword)
                $("[name='packingId']").val(selRow.packingId)
                var val=$('input[name="packingKeyword"]').val();
                $("[name='packingNumber']").val(selRow.packingNumber)
                $('input[data-role="tagsinput"]').tagsinput('removeAll');
                $('input[data-role="tagsinput"]').tagsinput('add',val);
                if(selRow.packingIsstop=="是"){
                    $(":radio[name='packingIsstop'][value='1']").prop("checked", "checked");
                }else{
                    $(":radio[name='packingIsstop'][value='0']").prop("checked", "checked");
                }
                $("[name='packingName']").attr("disabled",true);
                $(".tagsinput input[type='text']").prop("disabled",true);
                $(":radio[name='packingIsstop']").attr("disabled",true);
                utils.dialog({
                    title: '查看详情',
                    content: el
                }).showModal();
                $(".tag").dblclick(function (ev) {
                    return false;
                })
            }else{
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
        }
    });
    //新增一行
    $('#addRowData').on('click', function () {
        var el = document.querySelector('#dialog_Block');//html元素
        $('#packingunitform')[0].reset();
        $("[name='packingId']").val(null)
        $("[name='packingName']").attr("disabled",false);
        $(":radio[name='packingIsstop']").attr("disabled",false);
        $(".tagsinput input[type='text']").prop("disabled",false);
        $('input[data-role="tagsinput"]').each(function () {
            $(this).tagsinput('removeAll');
        })
        utils.dialog({
            title: '新增',
            content: el,
            width:'630px',
            okValue: '确定',
            ok: function () {
                $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                data= decodeURIComponent($("#packingunitform").serialize(),true);
                if (validform("packingunitform").form()) {//验证通过 "myform"为需要验证的form的ID
                    $.ajax({
                        url: "addorupdatepackingunit",
                        type: "post",
                        data: data,
                        success: function (result) {
                            utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                            setTimeout("location.reload();",1000);
                        }
                    })
                } else {//验证不通过
                    utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).showModal();

    })
    //删除选中行
    $('#delpackunit').on('click', function () {

        var selRow = $('#Packingunit_Table').XGrid('getSeleRow');
        if (selRow) {
            $.ajax({
                url:"delatepackingunit",
                data:{packingId:selRow.packingId},
                type:"POST",
                success:function(result){
                    utils.dialog({content:  result.result, quickClose: true, timeout: 2000}).showModal();
                    setTimeout("location.reload();",1000);
                }
            })
        } else {
            utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
        }


    })

    $("#querybut").click(function () {
        $('#Packingunit_Table').setGridParam({
            url:"/proxy-sysmanage/sysmanage/dict/querypackingunit",
            postData:{
                "packingName":$('#packingUnit').val().replace(/\s+/g,""),
                "isStop":$('#isStop').val()
            }
        }).trigger('reloadGrid');

    });
    function isShop(val){
        if(val==0){
            return "否"
        }else{
            return  "是"
        }
    }


    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })

    function datetimeFormatter(val) {
        if (val != null && val !="") {
            return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
        } else {
            return "";
        }
    };
})
function operation(){

    return  "<a  href='javascript:;' onclick='editpackingunit(this)'>编辑</a>"
}

function editpackingunit(obj){
    var el = document.querySelector('#dialog_Block');//html元素
    $('#packingunitform')[0].reset();
    var id=$(obj).parents('tr').attr('id');
    var selRow = $('#Packingunit_Table').XGrid('getRowData',id);
    $("[name='packingName']").attr("disabled",false);
    $(".tagsinput input[type='text']").prop("disabled",false);
    $(":radio[name='packingIsstop']").attr("disabled",false);
    if (selRow) {
        $("[name='packingName']").val(selRow.packingName)
        $("[name='packingKeyword']").val(selRow.packingKeyword)
        $("[name='packingId']").val(selRow.packingId)
        $("[name='packingNumber']").val(selRow.packingNumber)
        var val=$('input[name="packingKeyword"]').val();
        $('input[data-role="tagsinput"]').tagsinput('removeAll');
        $('input[data-role="tagsinput"]').tagsinput('add',val);
        if(selRow.packingIsstop=="是"){
            $(":radio[name='packingIsstop'][value='1']").prop("checked", "checked");
        }else{
            $(":radio[name='packingIsstop'][value='0']").prop("checked", "checked");
        }
        utils.dialog({
            title: '编辑',
            content: el,
            width:'630px',
            okValue: '确定',
            ok: function () {
                $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                data = decodeURIComponent($("#packingunitform").serialize(), true);
                if (validform("packingunitform").form()) {//验证通过 "myform"为需要验证的form的ID
                    $.ajax({
                        url: "addorupdatepackingunit",
                        type: "post",
                        data: data,
                        success: function (result) {
                            utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                            setTimeout("location.reload();",1000);
                        }
                    })

                } else {//验证不通过
                    utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }
            },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal();
    }else {
        utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
    }

}
