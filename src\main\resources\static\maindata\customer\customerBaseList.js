    var urlObjectList = [];
    var dueDayMax = $('#dueDayMax').val();
    if(dueDayMax){
        dueDayMax = "?dueDayMax="+dueDayMax;
    }


    //创建供应商列表
    $('#baseTable').XGrid({
        url:'/proxy-customer/customer/customerBaseAppl/customerBaseList'+dueDayMax,
        // 业务类型修改 这里也要跟着改 , 为了存储方便 ，channel = 前缀@+ channel + ， + 后缀#
        postData: $("#searchForm").serializeToJSON(),

    colNames: ['', '机构','客户编码', '客户名称', '客户类别', '联系人', '联系电话', '营业执照号', '是否停用', '剩余效期'
        ],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            hidden: true,
        }, {
            name: 'orgName',
            index: 'orgName'
        }
        // ,{
        //     name: 'channel',
        //     index: 'channel'
        // }
        ,{
            name: 'customerCode',
            index: 'customerCode'
        }, {
            name: 'customerName',
            index: 'customerName'
        }, {
            name: 'customerTypeName',
            index: 'customerTypeName'
        }, {
            name: 'salesman',
            index: 'salesman'
        }, {
            name: 'salesmanTel',
            index: 'salesmanTel'
        }, {
            name: 'businessLicenseNum',
            index: 'businessLicenseNum'
        }, {
            name: 'disabledYn',
            index: 'disabledYn',
            formatter: function (e) {
                if (e == '1') {
                    return '已停用'
                } else if (e == '0') {
                    return '未停用'
                }
            }
        }, {
            name: 'dueDay',
            index: 'dueDay',
            formatter:function (value,a,rowData) {
                if(value || value == 0)
                {
                    var id=rowData.id;
                    //console.log(id);

                    setTimeout(function () {
                        if(0 < value && value < 180){
                            $("#"+id).css({
                                "background":"#FFF9DD",
                            });
                        }else if(value <= 0){
                            $("#"+id).css({
                                "background":"#FDE5E5",
                            });
                        }
                    },0);

                    return value.toString();
                }else{
                    return '';
                }
                setTimeout(function () {

                },2)
            }
        },{
            name: 'snapshootUrl',
            hidden: true
        }],
        rowNum: 20,
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        rowList: [20,50,100],//分页条数下拉选择
        multiselect: true,//是否多选
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件', id, dom, obj, index, event);
            utils.openTabs("customerBaseDetail", "机构客户详情", "/proxy-customer/customer/customerBaseAppl/detail?id="+id);
            // location.href='/proxy-customer/customer/customerBaseAppl/detail?id='+id
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //console.log(id, dom, obj, index, event)
            setUrlObjectList(dom,id,obj);
        },
        pager: '#grid-pager'
    });


    function  btn_output_list(){
        utils.dialog({
            title: '导出列表',
            url: '/proxy-customer/customer/customerBaseAppl/toExportList?moduleName=customer&taskCode=1062',
            width: $(window).width() * 0.8,
            height: 600,
            // data: , // 给modal 要传递的 的数据
            onclose: function () {
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    }

    $("#searchBtn").on("click", function () {
        urlObjectList=[];
        // 业务类型修改 这里也要跟着改 , 为了存储方便 ，channel = 前缀@+ channel + ， + 后缀#
        var channelIds = $('#channel').val()
        console.log(channelIds);
        $('#baseTable').XGrid('setGridParam', {
            postData: $("#searchForm").serializeToJSON(),page:1
        }).trigger('reloadGrid');
    });
    $("#exportBtn").on("click", function () {
        const startTime = $("#startTime").val()
        const endTime = $("#endTime").val()
        if (!startTime || startTime.length === 0) {
            utils.dialog({content: '请输入开始日期', quickClose: true, timeout: 2000}).showModal();
            return
        }
        if (!endTime || endTime.length === 0) {
            utils.dialog({content: '请输入截止日期', quickClose: true, timeout: 2000}).showModal();
            return;
        }
        // IMPORTANT 当前页面内部的导出数量限制，与全局限制无关。
        const totalPageNum = Number($('#totalPageNum').text());
        const limitNum = Number($('#exportLimit').val());
        if (totalPageNum > limitNum) {
            const tips = "您本次导出的数据量过大（已超过"+limitNum+"条），不允许导出，请缩小导出范围。";
            utils.dialog({content: tips,quickClose: true, timeout: 2000}).showModal()
            return
        }
        //wgf判断条数wgf20200225
//         utils.exportAstrictHandle('baseTable', Number($('#totalPageNum').text()) , 1).then( () => {
//             return false;
//         }).catch( () => {
//             //判断 条数
//               //原始处理逻辑代码
//             //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
//             var ck = true;
//             // copy this parameter and the below buttons
//             var html = $('#exportHtml').html();
//             var d = dialog({
//                 title: '请选择导出字段',
//                 width:820,
//                 height:400,
//                 content: html,
//                 okValue: '确定',
//                 ok: function () {
//                     this.title('提交中…');
//                     var arr=[];
//                     for(var i=0;i<$(".exportItem").length;i++)
//                     {
//                         $(".exportItem").eq(i).find('dd input[type="checkbox"]').each(function () {
//                             var checked=this.checked;
//                             if(checked){
//                                 arr.push($.trim($(this).val()))
//                             }
//                         })
//                     }
//                     if(arr.length == 0){
//                         utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
//                         return false;
//                     }
//                     var exportAttribute= arr.join(',');
//                     // $("#exportAttribute").val(exportAttribute);
//                     // $("#searchForm").attr("action","/proxy-customer/customer/customerBaseAppl/exportExcel");
//                     // $("#searchForm").submit();
//                     // $('#orgCode').removeAttr('readonly').prop('disabled',true);
                    const params = $("#searchForm").serializeToJSON();
//                     // if(params.startTime){
//                     //     params.startTime = new Date(params.startTime).getTime();
//                     // }
//                     // if(params.endTime){
//                     //     params.endTime = new Date(params.endTime).getTime();
//                     // }
//                     params.bigCustomer = 0;//非大客户
                    var formData2 = {
                        moduleName: 'customer',
                        taskCode: '1062',
                        colName: '101,102,103,104,106,107,111,114,115,116,117,118,125,126,127,129,130,134,135,142,143,150,153,159',//bd姓名：153   文本收货地址：  159
                        colNameDesc: '',
                        fileName: '机构客户',
                        exportParams:params
                    };
                    utils.dialog({
                        title: '温馨提示',
                        content: '导出任务提交成功后页面将关闭，是否确认导出？',
                        okValue: '确定',
                        ok: function () {
                            $.ajax({
                                url: "/proxy-customer/customer/customerBaseAppl/commonCommitExportTask",
                                type: 'post',
                                dataType: 'json',
                                data: {
                                    "data":JSON.stringify(formData2)
                                },
                                success: function (res) {
                                    if (res) {
                                        if (res.code === 0) {
                                            utils.dialog({
                                                title: '温馨提示',
                                                content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                                okValue: '确定',
                                                ok: function () {
    
                                                }
                                            }).showModal()
                                        } else {
                                            utils.dialog({
                                                title: '温馨提示',
                                                content: data.msg,
                                                okValue: '确定',
                                                ok: function () {
    
                                                }
                                            }).showModal()
                                        }
                                    }
                                }
                            });
                        }
                    }).showModal()
//                     // httpPost("/proxy-customer/customer/customerBaseAppl/exportExcel", params)
//                 },
//                 cancelValue: '取消',
//                 cancel: function () {
//                     $('#orgCode').removeAttr('readonly').prop('disabled',true);
//                 },
// // copy button to other dialogues
//                 button: [
//                     {
//                         id: 'chooseAll',
//                         value: '全选',
//                         callback: function () {
//                             //debugger;
//                             if(!ck){
//                                 $(".exportItem input").removeAttr("checked");
//                                 ck = true;
//                             }else if(ck){
//                                 $(".exportItem input").prop("checked","checked");
//                                 ck = false;
//                             }else{
//                                 return false;
//                             };
//                             return false;
//                         }
//                     }
//                 ]
//                 //copy ends here
//             });
//             d.showModal();
//         });

        $('#orgCode').prop('disabled',false).attr('readonly','readonly');
        $('.ui-dialog-content').css({
            'overflow':'hidden',
            'overflow-y': 'auto',
        })
    });
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
    $(document).on('change','#grid_checked input',function () {
        var $tr=$(this).parents('tr');
        var rowData=$("#baseTable").getRowData($tr.attr('id'));
        var id=$tr.attr('id');
        setUrlObjectList($tr,id,rowData);
        //var checked=this.checked;
        // urlObjectList=[];
        // if(checked){
        //     var selRow=$('#baseTable').XGrid('getSeleRow');
        //     if(selRow && selRow.length > 0){
        //
        //         for(var i=0;i<selRow.length;i++){
        //             if(selRow[i].snapshootUrl != ''){
        //                 var fileParam={};
        //                 fileParam.id = selRow[i].id;
        //                 fileParam.name = selRow[i].customerCode;
        //                 fileParam.url = selRow[i].snapshootUrl;
        //                 urlObjectList.push(fileParam);
        //             }
        //         }
        //     }
        // }else{
        //     urlObjectList=[];
        // }
    })
    $(document).on("change",".exportItem dt input",function () {
        var checked=this.checked;
        if(checked)
        {
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',false);
        }
    });
    $(document).on("change",".exportItem dd input[type='checkbox']",function () {
        var inpLen=$(this).parents("dd").find("input[type='checkbox']").length;
        var checkLen=$(this).parents("dd").find("input[type='checkbox']:checked").length;
        if(inpLen == checkLen)
        {
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',false);
        }
    });
    //首营审批快照
    $('#snapshootBtn').on('click', function () {
        var len=$("#baseTable").XGrid('getSeleRow');
        if(!len || len.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'请先从列表中选择一条数据',
                okValue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        utils.dialog({
            align: 'top',
            width: 90,
            height: 50,
            padding: 8,
            content:'<div class="changeApplyItem formApprovalBlock"><div class="cSelect">预览</div><div class="cDown">下载</div></div>',
            quickClose: true
        }).show(this);
    });
    // $("#baseTable").on('change','td[row-describedby="ck"] input',function(){
    //     var $tr=$(this).parents('tr');
    //     var rowData=$("#baseTable").getRowData($tr.attr('id'));
    //     var id=$tr.attr('id');
    //     setUrlObjectList($tr,id,rowData);
    // })

    $('body').on('click', '.cSelect', function () {
        if(urlObjectList.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'没有可预览附件',
                okVlue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        $.viewImg({
            fileParam:{
                name:'name',
                url:'url'
            },
            list:urlObjectList
        })
    })

    $('body').on('click', '.cDown', function () {
        if(urlObjectList.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'没有可下载的附件',
                okVlue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        //批量下载
        var a=[];
        a.push('<form style="display: none" method="post">');
        urlObjectList.forEach(function(item) {
            console.log(item);
            a.push('<input name="file" value = ' + item.url + '>');
            a.push('<input name="name" value = '+ item.name + '>');
        });
        a.push('</form>');

        var $eleForm = $(a.join(''));
        $eleForm.attr("action", "/proxy-sysmanage/upload/downloadZip");
        //$eleForm.attr("action", "http://**************:8080/proxy-customer/customer/upload/download?filepath=G2/M00/00/01/Cgo001tPH3OAXX8_AABEWCsL8kc354.png&name=''");
        $(document.body).append($eleForm);
        //提交表单，实现下载
        $eleForm.submit();
    })
    function setUrlObjectList($tr,id,rowData){
        var a=rowData;
        var fileParam = {};
        if($tr.hasClass('selRow') && a.snapshootUrl){
            fileParam.id = a.id;
            fileParam.name = a.customerCode;
            fileParam.url = a.snapshootUrl;
            urlObjectList.push(fileParam);
        }else if(!$tr.hasClass('selRow')){
            $(urlObjectList).map(function (i,v) {
                if(v.id==a.id){
                    urlObjectList.splice(i,1);
                }
            })
        }
    }

    $(function () {
        //商品搜索图标
        $(document).on("click", ".glyphicon-search", function () {
            $(this).siblings("input").trigger("dblclick")
        })
        //业务类型 输入框双击 弹出业务类型列表
        $("#channelId_inp").dblclick(function () {
            utils.channelDialog('1').then( res => {
                console.log(res)
                let _str_name = '', _str_code = '';
                // if(Array.isArray(res)){
                    let _str_arr = res.map(item => {
                        return item.channelName;
                    })
                    _str_name = _str_arr.join(',');

                    let _str_code_arr = res.map(item => {
                        return "@" + item.channelCode + ",";
                    })
                    _str_code = _str_code_arr.join('#')
                // }else{
                //     _str_name = res.channelName;
                //     _str_code = "@" + res.channelCode + "," ;
                // }
                // _str_code = _str_code + "#";
                $('#channelId_inp').val(_str_name)
                $('#channel').val(_str_code)
            })
        });
    })
