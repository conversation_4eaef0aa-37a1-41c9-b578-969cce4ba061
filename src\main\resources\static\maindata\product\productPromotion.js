//改动点 3
$('#activeGoods_table').XGrid({
    colNames: ['机构','商品ID','商品编码','商品名称','活动时段及内容','活动条件','调价规则','调价规则Code', 'APP继续调价(包括分省价格)','连锁APP继续调价','活动开始时间','活动结束时间','活动类型','活动名称', 'promoInfoVOList'],
    colModel: [
        {
            name: 'orgCodeValue',
            index: 'orgCodeValue'
        },
        {
            name: 'productId',
            index: 'productId',
            hidden:true
        },
        {
            name: 'productCode',
            index: 'productCode'
        },
        {
            name: 'productName',
            index: 'productName'
        },
        {
            name: 'activeContent',
            index: 'activeContent',
        },
        {
            name: 'promoCondition',
            index: 'promoCondition',
        },
        {
            name: 'adjustRule',
            index: 'adjustRule',
        },
        {
            name: 'adjustRuleCode',
            index: 'adjustRuleCode',
            hidden: true
        },
        {
            name: 'appControl',
            index: 'appControl',
            rowtype: '#activeGoods_check',
        },
        {
            name: 'chainControl',
            index: 'chainControl',
            rowtype: '#chainControl_check'
        },
        {
            name: 'promoStartTime',
            index: 'promoStartTime',
            hidden: true
        },
        {
            name: 'promoEndTime',
            index: 'promoEndTime',
            hidden: true
        },
        {
            name: 'promoType',
            index: 'promoType',
            hidden: true
        },
        {
            name: 'promoName',
            index: 'promoName',
            hidden: true
        },
        {
            name: 'promoInfoVOList',
            index: 'promoInfoVOList',
            formatter:function(v){
                return JSON.stringify(v)
            },
            hidden: true
        }
    ],
    key: 'productId',
    rowNum: 0,
    rownumbers: true,
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index, event) {},
    onSelectRow: function (id, dom, obj, index, event) {
        event.stopPropagation()
    },
})
function activeGoodsFun(rowData){
    console.log(rowData,'rowDatarowData')
    return new Promise((resolve, reject) => {
        console.log(resolve,'reslosacve111')
        //APP售价>申请APP售价 商品ID集合
        let productIdAry = rowData.filter(item => Number(item['appPrice'] > Number(item['applicationAppPrice']))).map(item =>  item['productId']);
        //药帮忙渠道商品ID集合
        let proIdAry = rowData.filter(item=>item.channelId  == '1').map(item => {
            return item.productId
        });
        console.log($('#X_Table'),'$(\'#X_Table\')')
        console.log($('#activeGoods_table'),'$(\'#activeGoods_table\')')
        $('#activeGoods_table').setGridParam({
            url:"/proxy-product/product/adjustPrice/getProPromotion",
            postData: {
                productIds: proIdAry.toString(),
            },
            page:1,
            gridComplete: function () {
                let rowdata   =  $('#activeGoods_table').XGrid('getRowData');
                let errData  =  rowdata.filter(item  => productIdAry.indexOf(item['productId']) > -1)
                $(rowdata).each((index, item) => {
                    let _id  = item['id']
                    let valist = JSON.parse(item['promoInfoVOList'])
                    let _activeContentstr = '', _adjustRuleStr  = '',  _promoConditionStr  = ''
                    $(valist).each((ind, ite) =>{
                        _activeContentstr +=  `<p>${ite['activeContent']}</p>`
                        _adjustRuleStr +=  `<p>${ite['adjustRule']}</p>`
                        _promoConditionStr +=  `<p>${ite['promoCondition']}</p>`
                        if (ite['adjustRuleCode'] == 3 ) {//不可调价
                            $('#activeGoods_table #' + _id).find('[row-describedby="appControl"] input').prop('disabled', true)
                        }
                    })

                    $('#activeGoods_table #'  + item.id).find('[row-describedby="activeContent"]').html(_activeContentstr)
                    $('#activeGoods_table #'  + item.id).find('[row-describedby="adjustRule"]').html(_adjustRuleStr)
                    $('#activeGoods_table #'  + item.id).find('[row-describedby="promoCondition"]').html(_promoConditionStr)
                })
                $(errData).each((index, item) => {
                    let _id  = item['id']
                    if (item['adjustRuleCode'] == 1) {//可调高
                        $('#activeGoods_table #' + _id).find('[row-describedby="appControl"] input').prop('disabled', true)
                    }
                })
                resolve()
            }
        }).trigger('reloadGrid');

    })
}

function btn_check(status) {
    let allCheck = $('#activeGoods_table').find('input[type=checkbox]');
    $(allCheck).each((index, item) => {
        if(!$(item).prop('disabled')){
            $(item).prop('checked', status == 1 ? true : false)
        }
    })
}
