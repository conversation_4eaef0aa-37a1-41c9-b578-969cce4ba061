var customerApplType = 0;
$(function () {
    if ($('#edit').val() && $('#edit').val() == 1) {
        $('[name=businessLicenseNum]').prop('readonly', false);
    }
    customerApplType = $('#customerApplType').val();
    if ($('[name=tag_forPage]').val() != 'tag_change_page') {
        $('#customerNature').on('change', function () {
            let val = $(this).val();
            if (val == 3) {
                // $('[name=businessLicenseNum], [name=taxRegistryNumber]').val('');
                // $('[name=businessLicenseNum], [name=taxRegistryNumber]').addClass('ignore');
                $('[name=taxRegistryNumber]').addClass('ignore');
                $('[name=businessLicenseNum]').attr("class", "form-control {validate:{ num_word_or_blank : true}} businessLicenseNum")
                $('[name=businessLicenseNum]').metadata()["validate"] = { num_word_or_blank: true }
                $('[name=businessLicenseNum]').prev().html('营业执照号'); // <i class="text-require">*  </i>
            } else {
                $('[name=businessLicenseNum], [name=taxRegistryNumber]').removeClass('ignore');
                $('[name=businessLicenseNum], [name=taxRegistryNumber]').prop('readonly', false);
                $('[name=businessLicenseNum]').prev().html('<i class="text-require">*  </i>营业执照号'); //
                $('[name=businessLicenseNum]').attr("class", "form-control {validate:{ required :true, num_word_or_blank : true}} businessLicenseNum")
                $('[name=businessLicenseNum]').metadata()["validate"] = { required: true, num_word_or_blank: true }
            }
        })
    }
    if ($('#customerNature') && $('#customerNature').val() == 3) {
        // $('[name=businessLicenseNum], [name=taxRegistryNumber]').val('');
        // $('[name=businessLicenseNum], [name=taxRegistryNumber]').addClass('ignore');
        $('[name=taxRegistryNumber]').addClass('ignore');
        $('[name=businessLicenseNum]').attr("class", "form-control {validate:{ num_word_or_blank : true}} businessLicenseNum")
        $('[name=businessLicenseNum]').metadata()["validate"] = { num_word_or_blank: true }
        $('[name=businessLicenseNum]').prev().html('营业执照号'); // <i class="text-require">*  </i>
    } else {
        $('[name=businessLicenseNum], [name=taxRegistryNumber]').removeClass('ignore');
        $('[name=businessLicenseNum]').prev().html('<i class="text-require">*  </i>营业执照号'); //
        $('[name=businessLicenseNum]').attr("class", "form-control {validate:{ required :true, num_word_or_blank : true}} businessLicenseNum")
        $('[name=businessLicenseNum]').metadata()["validate"] = { required: true, num_word_or_blank: true }
    }
    if ($("#source").length && $("#source").val() != 3 && $("#source").length && $("#source").val() != 5) {
        $('#customerTypeVal').prop('readonly', true);
        $("#customerTypeVal").attr("disabled", "disabled");
        setTimeoutDis();
        function setTimeoutDis() {
            setTimeout(function () {
                if ($("#customerApprovalFileVoList").find("[name='credentialTypeId']").length < 0 || !$("#customerApprovalFileVoList").find("[name='credentialTypeId']").attr("disabled")) {
                    setTimeoutDis();
                }
                $("#customerApprovalFileVoList").find("[name='credentialTypeId']").attr("disabled", "disabled");
            }, 1000)
        }
    }
    if ($('#edit').val() && $('#edit').val() == 1) {
        setTimeout(function () {
            $(".ApprovalUploadeImg").removeAttr("disabled", "disabled");
        }, 2000);
    }
    $('[name=billingCompanyName]').on('blur', function () {
        parent.showLoading({ hideTime: 60000 });
        $.ajax({
            type: 'post',
            url: '/proxy-customer/customer/customerBaseAppl/getCustomerBaseByParameter  ',
            data: {
                orgCode: $('#orgCode').val(),
                customerName: $('[name=billingCompanyName]').val()
            },
            success: function (res) {
                // console.log(res)
                if (!res.result) { //  bu cunzai
                    // console.log('-----------')
                    utils.dialog({
                        title: '提示',
                        content: '填写的公司名称系统中不存在.',
                        okValue: '确定',
                        ok: function () {
                            $('#billInfo_form').attr('supportEdit', 'true')
                        }
                    }).showModal()
                } else {
                    $('#billInfo_form').attr('supportEdit', 'false')
                }
            },
            error: function (err) {

            },
            complete: function () {
                parent.hideLoading()
            }
        })
    })

})
/**
 * 流程图显示
 */

function initApprovalFlowChart(key, processInstaId) {
    if ($('#customerApplType').val() == 6 || ($('#applicationCode').val() && $('#applicationCode').val().search("SNKH") != -1)) {
        key = 'customerFirstErp';
    }
    var url = "/proxy-product/product/purchaseLimit/queryTotle?key=" + key;
    if (processInstaId) {
        url = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId=" + processInstaId;
    }
    $("#workProcessKey").val(key);
    parent.showLoading({ hideTime: 15000 });
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code == 0 && data.result != null) {
                // console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () { },
        complete: function () {
            parent.hideLoading();
        }
    });
}
//判断批准文件附件
function tableImgDataChek(tableId) {
    var _rowData;
    if ($("#" + tableId).length) {
        _rowData = $("#" + tableId).XGrid('getRowData');
        if (_rowData.length > 0) {
            for (var i = 0; i < _rowData.length; i++) {
                if (!_rowData[i].enclosureCount || _rowData[i].enclosureCount == 0) {
                    return false;
                }
            }
            return true;
        }
    }
    return false;
}
//审核按钮
$('.audiPass').on('click', function () {
    // 校验客户名称与公司名称是否一致
    const _this = this;
    // !!! ATTENTION PLEASE !!!
    // !!! ATTENTION PLEASE !!!
    // !!! ATTENTION PLEASE !!!
    // 因此 JS 在多处调用，为避免对其他无关页面造成负面影响，此处将其作用域限定到客户首营页面
    if ((_this.getAttribute("status") == 2 || _this.getAttribute("status") == 9) && window.location.href.indexOf("customerFirst") && !validateCustomerName()) {
        utils.dialog({
            title: '提示',
            content: '客户名称与发票信息中的公司名称不一致，确认要提交吗？',
            okValue: '确定',
            ok: function () {
                audiPassAction(_this.getAttribute("status"));
            },
            cancelValue: '取消',
            cancel: function () { }
        }).showModal();
    } else {
        audiPassAction(_this.getAttribute("status"));
    }
});
function audiPassAction(status) {
    $('#auditOpinion').val('');
    var _contentTitle = "";

    var title = "审核通过";
    $('#auditRequired').css('display', 'none');
    if (status == 4) {
        title = "审核不通过";
        $('#auditRequired').css('display', 'inline');
    } else if (status == 3) {
        title = "关闭审核";
        $('#auditRequired').css('display', 'inline');
    }
    if (status == 1) {
        title = "保存草稿";
        submitAuditInfo(status, "", true);
        return false;
    }
    // 客户首营申请审核过程中，点击「审核通过」按钮时，进行数据校验
    if (status == 2 && $('#customerApplType').val() == 2 && $('#edit').val() == 1) {
        if (!$("#customerName").val()) {
            utils.dialog({
                title: '提示',
                content: '客户名称为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if (!$("#customerTypeVal").val()) {
            utils.dialog({
                title: '提示',
                content: '客户类别为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if ($("#district1").length && !$("#district1").val() || !$("#input[name=registerAddress]")) {
            utils.dialog({
                title: '提示',
                content: '注册地址为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if ($("#shippingAddressDistrictId").length && !$("#shippingAddressDistrictId").val() || $("#input[name=shippingAddressInput]").length && $("#input[name=shippingAddressInput]").val()) {
            utils.dialog({
                title: '提示',
                content: '收货地址为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if ($('#baseDataBuseScope').find('font').length < 1) {
            utils.dialog({
                title: '提示',
                content: '经营范围不能为空.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if (!validform("customerApprovalFileVoList").form()) {
            utils.dialog({
                title: '提示',
                content: '批准文件资质为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if ($('[name=tag_forPage]').val() != "tag_detail_page") {
            let len1 = $('#certTable').XGrid('getRowData').length,
                len2 = $('#table2').XGrid('getRowData').length;
            if (len2 == 0) {
                utils.dialog({
                    title: '提示',
                    content: '客户委托书信息不能为空.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal()
                return false;
            }
            if (len1 == 0) {
                utils.dialog({
                    title: '提示',
                    content: '被委托人身份证信息不能为空.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal()
                return false;
            };
        }
        if (!tableImgDataChek("table3")) {
            utils.dialog({
                title: '提示',
                content: '资质附件不能为空，请上传附件.',
                okValue: '确定',
                ok: function () { }
            }).showModal()
            return false;
        }
    }
    if ($("#auditStatus").length && ($("#auditStatus").val() == 2 || $("#auditStatus").val() == 20) && $("#source").length && $("#source").val() != 3 && status != 4 && status != 3) {
        if (!$("#customerName").val()) {
            utils.dialog({
                title: '提示',
                content: '客户名称为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if (!$("#customerTypeVal").val()) {
            utils.dialog({
                title: '提示',
                content: '客户类别为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if ($("#district1").length && !$("#district1").val() || !$("#input[name=registerAddress]")) {
            utils.dialog({
                title: '提示',
                content: '基础属性中注册地址为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if ($("#shippingAddressDistrictId").length && !$("#shippingAddressDistrictId").val() || $("#input[name=shippingAddressInput]").length && $("#input[name=shippingAddressInput]").val()) {
            utils.dialog({
                title: '提示',
                content: '基础属性中收货地址为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if ($('#baseDataBuseScope').find('font').length < 1) {
            utils.dialog({
                title: '提示',
                content: '经营范围不能为空.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if (!validform("customerApprovalFileVoList").form()) {
            utils.dialog({
                title: '提示',
                content: '批准文件资质为必填项.',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }
        if ($('[name=tag_forPage]').val() != "tag_detail_page") {
            let len1 = $('#certTable').XGrid('getRowData').length,
                len2 = $('#table2').XGrid('getRowData').length;
            if (len2 == 0) {
                utils.dialog({
                    title: '提示',
                    content: '客户委托书信息不能为空.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal()
                return false;
            }
            if (len1 == 0) {
                utils.dialog({
                    title: '提示',
                    content: '被委托人身份证信息不能为空.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal()
                return false;
            };
        }
        if (!tableImgDataChek("table3")) {
            utils.dialog({
                title: '提示',
                content: '资质附件不能为空，请上传附件.',
                okValue: '确定',
                ok: function () { }
            }).showModal()
            return false;
        }
        submitAuditInfo(status, "", true);
        return false;
    }
    if (status != 3) {
        let natureVal = $('#customerNature').val(), // 客户性质
            threeInOneVal = $('[name=threeInOne]:checked').val(), // 是否三证合一
            taxRegistryNumberVal = $('[name=taxRegistryNumber]').val();
        /**
         * 1）客户首营页面增加字段：【客户性质】：下拉选择，字典值为：“一般纳税人/小规模纳税人/其他”共三项。
         * 由质管审核时填写，默认值为空，控制必填。
         *（2）控制逻辑修改：当【客户性质】=“一般纳税人”、“小规模纳税人”
         * 并且 【是否三证合一】=“否”时，控制【税务登记证号】必填。提交时提示并禁止提交。
         * 不满足这一条件时，不控制必填。
         */
        // ERP2018-5455 质量主管节点不需要对必填项校验，因为在这个节点 页面是不可编辑状态
        if (status != 4) {
            if (!tableImgDataChek("table3")) {
                utils.dialog({
                    title: '提示',
                    content: '资质附件不能为空，请上传附件.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal()
                return false;
            }
            if (natureVal == 0 && !$('#customerNature').prop('disabled')) {
                utils.dialog({
                    title: '提示',
                    content: '客户性质为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return false;
            }
            /**
             *
             */
            if ($('[name=tag_forPage]').val() != "tag_detail_page") {
                let len1 = $('#certTable').XGrid('getRowData').length,
                    len2 = $('#table2').XGrid('getRowData').length;
                if (len2 == 0) {
                    utils.dialog({
                        title: '提示',
                        content: '客户委托书信息不能为空.',
                        okValue: '确定',
                        ok: function () { }
                    }).showModal()
                    return false;
                }
                if (len1 == 0) {
                    utils.dialog({
                        title: '提示',
                        content: '被委托人身份证信息不能为空.',
                        okValue: '确定',
                        ok: function () { }
                    }).showModal()
                    return false;
                };
            }
        }
        if (natureVal != 3 && threeInOneVal == 0 && status != 4) {
            $('[name=businessLicenseNum], [name=taxRegistryNumber]').prop('readonly', false);
            if (taxRegistryNumberVal == '') {
                utils.dialog({
                    title: '提示',
                    content: '当【客户性质】=“一般纳税人”、“小规模纳税人”，【是否三证合一】=“否”时，税务登记证号必填.',
                    okValue: '确定',
                    ok: function () {
                        $('[name=taxRegistryNumber]').focus();
                    }
                }).showModal();
                return false;
            }
        }
        /**
         *  客户性质为其他的时候。必填逻辑更改
         */
        if (natureVal == 3) {
            // $('[name=businessLicenseNum], [name=taxRegistryNumber]').val('');
            // $('[name=businessLicenseNum], [name=taxRegistryNumber]').addClass('ignore');
            $('[name=taxRegistryNumber]').addClass('ignore');
            $('[name=businessLicenseNum]').prev().html('营业执照号'); // <i class="text-require">*  </i>
        } else {
            $('[name=businessLicenseNum], [name=taxRegistryNumber]').removeClass('ignore');
            $('[name=businessLicenseNum], [name=taxRegistryNumber]').prop('readonly', false);
            $('[name=businessLicenseNum]').prev().html('<i class="text-require">*  </i>营业执照号'); //
        }


        if ($('[name=isIndependent]:checked').val() == '1') { // 是否独立核算 选中值为是时。判断上下文对应的值是否相等
            if (status != 4) {
                let returnBool = checkChangeSyncDataSame();
                if (!returnBool) {
                    return false;
                };
            }
        }
        // 客户首营申请，客户首营详情页 的审核通过按钮掉的是同一个方法
        //当页面为详情页的时候，不用去判断空值
        if (status != 4) {
            if ($('[name=tag_forPage]').val() != 'tag_detail_page') {
                if (validBillInfoForm_fun()) {
                    return false;
                };
            }
        }

        if (status == 9) { // 重新提交按钮
            utils.dialog({
                title: '提交审核',
                content: '确定提交申请?',
                width: 300,
                height: 30,
                okValue: '确定',
                ok: function () {
                    submitAuditInfo(status, $("#auditOpinion").val());
                },
                cancelValue: '取消',
                cancel: function () { }
            }).showModal()
        } else { // 提交审核按钮
            var a = utils.dialog({
                title: (customerApplType == 6) ? "提交审核" : title,
                content: (customerApplType == 6) ? $('#container_onlySub') : $('#container'),
                okValue: '确定',
                ok: function () {
                    //审核意见不能为空
                    if ($("#auditOpinion").val() == "" && (status == 3 || status == 4)) {
                        utils.dialog({ content: '审批意见不能为空!', quickClose: true, timeout: 2000 }).showModal();
                        return false;
                    } else {
                        a.close().remove();
                        submitAuditInfo(status, $("#auditOpinion").val());
                    }

                },
                cancelValue: '取消',
                cancel: function () { }
            }).showModal();
        }
    } else {
        utils.dialog({
            title: title,
            width: 300,
            height: 30,
            okValue: '确定',
            content: "确定关闭此申请？",
            ok: function () {
                submitAuditInfo(status, $("#auditOpinion").val());
            },
            cancelValue: '取消',
            cancel: function () { }
        }).showModal();
    }
}
// 联系人电话校验
function checkSalesmanTel(telList, auditStatus) {
    let finalTelAry = telList.map(item => item.tels)
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '/proxy-customer/customer/customerBaseAppl/validateUserMobile?mobiles=' + finalTelAry.toString(),
            type: 'get',
            success: function (res) {
                if (res.code == 0) {
                    // 对返回数据操作判断是否有不满足数据，如果有 ，返回false
                    let list = res.result;
                    let checkres = list.filter(item => item.flag)
                    if (auditStatus == 2 || auditStatus == 9) {
                        // 大客户的 不用判断
                        if (($('[name=customerCode]').val() || $('#customerCode').val()) && $('#bigcusType').val() == '1') {
                            reject()
                        } else {
                            if (checkres.length == 0) {
                                reject()
                            } else {
                                //  筛出 不允许的手机号
                                let tels = checkres.map(item => item.mobile);
                                let tag = ''
                                for (let i = 0; i < tels.length; i++) {
                                    for (let j = 0; j < telList.length; j++) {
                                        if (tels[i] == telList[j]['tels']) {
                                            tag = telList[j]['tag'];
                                            break;
                                        }
                                    }
                                }
                                resolve(tag);
                            }
                        }
                    } else {
                        reject()
                    }
                } else {
                    if ($('#bigcusType').val() == '1') {
                        reject()
                    } else {
                        if (res.result) {
                            resolve(res.msg)
                        } else {
                            reject()
                        }
                    }
                }
            }
        })
    })
}
function submitAuditInfo(auditStatus, auditOpinion, booles) {
    parent.showLoading({ hideTime: 60000 });
    var customer = {};
    if (auditStatus == 2 && !booles || auditStatus == 9 && !booles) { // 提交审核、重新提交
        if ($('#edit').val() && $('#edit').val() == 1) {
            //结算方式
            var noCheck = 0;
            var itemValueFlag = true;
            $(".settlement").each(function () {
                var parentInp = $(this).find("input[name='itemValue']");
                if (parentInp.is(":checked")) {
                    $(this).find(".childCode").each(function (index) {
                        var cValue = $(this).find("input[name='itemValue1']").val();
                        if ($.trim(cValue) == '') {
                            itemValueFlag = false;
                        }
                    });
                } else {
                    noCheck += 1;
                }
            });
            if (noCheck == $(".settlement").length) {
                itemValueFlag = false;
            }
            //结算方式结束
            // 去除校验 20200319
            // if($('input[name=businessCategoryName]:checked').length == 0){ // 经营类别 没有选中的数据
            //     parent.hideLoading();
            //     utils.dialog({
            //         content: '经营类别的保存值不能为空',
            //         timeout: 2000,
            //         quickClose: true,
            //     }).show();
            //     return false;
            // }
            if ($('input[name=isIndependent]:checked').length == 0) { // 是否独立核算 没有选中的数据
                parent.hideLoading();
                utils.dialog({
                    content: '是否独立核算的保存值不能为空',
                    timeout: 2000,
                    quickClose: true,
                }).show();
                return false;
            }
            if ($('input[name=invoiceType]:checked').length == 0) { // 发票类型 没有选中的数据
                parent.hideLoading();
                utils.dialog({
                    content: '发票类型的保存值不能为空',
                    timeout: 2000,
                    quickClose: true,
                }).show();
                return false;
            }
            if ($('#customerApplType').val() == 2 || $('#customerApplType').val() == 6) {
                // 提交审核时当 是否独立核算为否时，判断非独立经营证明中是否有数据
                if ($('[name=isIndependent]:checked').val() == '0') {
                    if ($('.fileIcon_p').length < 1) {
                        parent.hideLoading();
                        utils.dialog({
                            title: '提示',
                            content: '非独立经营证明附件未上传，不允许审核通过！',
                            okValue: '确定',
                            ok: function () { }
                        }).showModal();
                        return false;
                    }
                }
            }
            /**
             * 20200114
             * 线上紧急需求，药品经营许可证和GSP
             */
            // 批准文件-证书类型绑定校验
            // var rowData3 = $('#table3').XGrid('getRowData');
            // var certificateOption = [
            //     {
            //         credentialTypeId:[21,22],
            //         msg:'《药品经营许可证》与《GSP证书》必须同时录入'
            //     }
            // ];
            // var certificateMsg = '';
            // certificateOption.forEach(function (item) {
            //     if(rowData3.some(function (row) {
            //         return item.credentialTypeId[0] == row.credentialTypeId;
            //     }) !== rowData3.some(function (row) {
            //         return item.credentialTypeId[1] == row.credentialTypeId;
            //     })){
            //         certificateMsg = item.msg;
            //     }
            // });
            // if(certificateMsg){
            //     parent.hideLoading();
            //     utils.dialog({
            //         title: '提示',
            //         content: certificateMsg,
            //         okValue: "确定",
            //         ok: function () {}
            //     }).showModal();
            //     return false;
            // }
            // || !validform("customerBillingAddressVoList").form()
            if (($('#customerApplType').val() == 2 || $('#customerApplType').val() == 6) && (!validform("customerApplVo").form()
                || !validform("customerApprovalFileVoList").form() || !validform("customerOtherInfo").form() || !validform("billInfo_form").form())) {
                // 提交审核时当 是否独立核算为否时，判断非独立经营证明中是否有数据
                if ($('[name=isIndependent]:checked').val() == '0') {
                    if ($('.fileIcon_p').length < 1) {
                        parent.hideLoading();
                        utils.dialog({
                            title: '提示',
                            content: '非独立经营证明附件未上传，不允许审核通过！',
                            okValue: '确定',
                            ok: function () { }
                        }).showModal();
                        return false;
                    }
                }
                if (!validform("customerApplVo").form()) {
                    utils.dialog({ content: '校验不通过！基础属性请正确填写内容。', quickClose: true, timeout: 2000 }).showModal();
                    parent.hideLoading();
                    //表单验证不通过
                    return false;
                }
                if (!validform("customerApprovalFileVoList").form()) {
                    utils.dialog({ content: '校验不通过！批准文件请正确填写内容。', quickClose: true, timeout: 2000 }).showModal();
                    parent.hideLoading();
                    //表单验证不通过
                    return false;
                }
                if (!validform("customerOtherInfo").form()) {
                    utils.dialog({ content: '请填写客户委托书,被委托人身份证信息再提交。', quickClose: true, timeout: 2000 }).showModal();
                    parent.hideLoading();
                    //表单验证不通过
                    return false;
                }
                if (!validform("billInfo_form").form()) {
                    utils.dialog({ content: '校验不通过！开票信息请正确填写内容。', quickClose: true, timeout: 2000 }).showModal();
                    parent.hideLoading();
                    //表单验证不通过
                    return false;
                }
                // utils.dialog({content: '校验不通过！请正确填写内容。', quickClose: true, timeout: 2000}).showModal();
                // parent.hideLoading();
                // //表单验证不通过
                // return false;
            } else if ($('#customerApplType').val() == 4 && (!validform("customerApplVo").form()
                || !validform("customerApprovalFileVoList").form() || !validform("customerOtherInfo").form() || !validform("billInfo_form").form())) {
                utils.dialog({ content: '校验不通过！请正确填写内容。', quickClose: true, timeout: 2000 }).showModal();
                parent.hideLoading();
                //表单验证不通过
                return false;
            }
            // else if(!itemValueFlag){
            //     //表单验证不通过
            //     utils.dialog({content: '校验不通过！结算方式不能为空。', quickClose: true, timeout: 2000}).showModal();
            //     parent.hideLoading();
            //     return false;
            // }
            // 特殊属性去除 20200319
            // else if( $("input[name='specialBusinessScope']:checked").length == 0){
            //     //表单验证不通过
            //     utils.dialog({content: '校验不通过！特殊属性不能为空。', quickClose: true, timeout: 2000}).showModal();
            //     parent.hideLoading();
            //     return false;
            // }
            else if ($('#baseDataBuseScope').find('font').length < 1) {
                //表单验证不通过
                utils.dialog({ content: '校验不通过！经营范围不能为空。', quickClose: true, timeout: 2000 }).showModal();
                parent.hideLoading();
                return false;
            } else {
                // 获取 开票信息，非独立经营证明中 图片的值
                let fileIcon_p = $('.fileIcon_p');
                let val = [];
                $(fileIcon_p).each(function () {
                    val.push($(this).attr('data-imgUrl'))
                })
                $('[name=customerEnclosureVoList]').val(val.toString()); // 非独立经营证明  隐藏域赋值
                customer = getSavedData();//表单数据
            }
        }
    }

    let salesmanTelList = []; // 所有联系人的电话
    if (window.changeApply && changeApply['salesmanTel'] && changeApply['salesmanTel']['afterText']) {
        salesmanTelList.push({ 'tag': 'salesmanTel', 'tels': changeApply['salesmanTel']['afterText'] })
    } else {
        salesmanTelList.push({ 'tag': 'salesmanTel', 'tels': $('[name=salesmanTel]').val() })
    }
    let allTable2Tel = [];
    if (window.changeApplyList && changeApplyList['customerDelegationFileVoList'] && changeApplyList['customerDelegationFileVoList']['valueAfter']) {
        let ary = changeApplyList['customerDelegationFileVoList']['valueAfter'];
        allTable2Tel = ary.map(item => {
            return { 'tag': 'table', 'tels': item.delegationTel }
        });
    } else {
        let allTable2Data = $('#table2').XGrid('getRowData');
        allTable2Tel = allTable2Data.map(item => {
            return { 'tag': 'table', 'tels': item.delegationTel }
        });
    }
    salesmanTelList = salesmanTelList.concat(allTable2Tel);
    var dialogContent = "";
    if (auditStatus != 1 && !booles) {
        // checkSalesmanTel(salesmanTelList, auditStatus).then((msg) => {
        //     if (msg){
        //         parent.hideLoading();
        //        if(msg == 'table'){
        //             utils.dialog({content: '客户委托书中的电话不允许录入我司员工手机号', quickClose: true, timeout: 2000}).showModal();
        //             return false;
        //         }else{
        //             utils.dialog({content: msg, quickClose: true, timeout: 2000}).showModal();
        //             return false;
        //         }
        //     }
        // }).catch( () => {
        // console.log('手机号验证通过 ')
        // return false
        if (customerApplType == 6) {
            if (auditStatus == 2 || auditStatus == 9) {
                dialogContent = "恭喜提交审核成功！";
            } else if (auditStatus == 4) {
                dialogContent = "驳回成功！";
            } else if (auditStatus == 3) {
                dialogContent = "流程已关闭！";
            }
        } else {
            if (auditStatus == 2) {
                dialogContent = "恭喜审核通过！";
            } else if (auditStatus == 9) {
                dialogContent = "恭喜提交审核成功！";
            } else if (auditStatus == 4) {
                dialogContent = "驳回成功！";
            } else if (auditStatus == 3) {
                dialogContent = "流程已关闭！";
            } else if (auditStatus == 1) {
                dialogContent = "保存草稿成功！";
            }
        }
        nextContentSend();
        // })
    } else {
        customer = getSavedData();
        if (auditStatus == 2) {
            dialogContent = "恭喜审核通过！";
        } else if (auditStatus == 9) {
            dialogContent = "恭喜提交审核成功！";
        } else if (auditStatus == 4) {
            dialogContent = "驳回成功！";
        } else if (auditStatus == 3) {
            dialogContent = "流程已关闭！";
        } else if (auditStatus == 1) {
            dialogContent = "保存草稿成功！";
        } else {
            dialogContent = "恭喜审核通过！";
        }
        nextContentSend();
    }
    function nextContentSend() {
        var applicationCode = $("#applicationCode").val();
        var taskId = $("#taskId").val();
        customer.applicationCode = applicationCode;
        customer.taskId = taskId;
        if (auditStatus == 9) {
            customer.auditStatus = 2;
        } else {
            customer.auditStatus = auditStatus;
        }
        customer.customerApplStatus = $('#auditStatus').val();
        customer.auditOpinion = auditOpinion;
        customer.customerCode = $('[name=customerCode]').val();
        customer.edit = $('#edit').val();
        customer.approvalProcessId = $("#processId").length ? $("#processId").val() : "";
        customer.id = $('#customerApplId').val();
        var baseDataBuseScope = "";
        if ($('#edit').val() == 0) {
            $('#baseDataBuseScope').find('font').each(function () {
                baseDataBuseScope += $(this).html() + ",";
            });
            if (baseDataBuseScope.length > 0) {
                baseDataBuseScope = baseDataBuseScope.substr(0, baseDataBuseScope.length - 1);
            }
        }
        customer.baseDataBuseScope = baseDataBuseScope;
        if (customer.customerFirstApplVo) {
            if (Array.isArray(customer.customerFirstApplVo.businessCategoryName)) {
                customer.customerFirstApplVo.businessCategoryName = customer.customerFirstApplVo.businessCategoryName.join(",");
            }
            customer.customerFirstApplVo.businessCategory = customer.customerFirstApplVo.businessCategoryName;
        }
        if ($("#source").length) {
            if (!customer.customerFirstApplVo) {
                customer.customerFirstApplVo = {};
            }
            customer.customerFirstApplVo.source = $("#source").val();
        }
        // 不可经营类别 wgf
        if (customer.customerFirstApplVo) {
            if (Array.isArray(customer.customerFirstApplVo.cannotBusinessCategoryName)) {
                customer.customerFirstApplVo.cannotBusinessCategoryName = customer.customerFirstApplVo.cannotBusinessCategoryName.join(",");
            }
            customer.customerFirstApplVo.cannotBusinessCategory = customer.customerFirstApplVo.cannotBusinessCategoryName;
        }
        // console.log(customer);
        var data = JSON.stringify(customer);
        var url = "";
        if (customerApplType == 2) {
            url = "/proxy-customer/customer/customerFirstAppl/audit";
        }
        if (customerApplType == 4) {
            url = "/proxy-customer/customer/change/approval/editor";
        }
        if (customerApplType == 6) {
            url = "/proxy-customer/customer/customerFirstAppl/add";
            data = JSON.stringify(getSavedData());
        }
        if (auditStatus == 1) {
            url = "/proxy-customer/customer/customerFirstAppl/saveDrafts";
        }
        // console.log(data);
        parent.showLoading({ hideTime: 15000 });
        $.ajax({
            type: "post",
            url: url,
            async: false,
            data: data,
            dataType: "json",
            contentType: "application/json",
            success: function (data) {
                if (customerApplType == 6) {
                    if (data == '1') {
                        dialogContent = '提交审核成功！';
                    } else if (data == '4') {
                        dialogContent = '提交审核失败！原因是客户重复提交';
                    } else if (data == '2') {
                        dialogContent = '客户首营申请审核流审核失败! 审核没通过！';
                    } else if (data == '3') {
                        dialogContent = '客户首营申请审核流审核失败!';
                    } else if (data == '5') {
                        dialogContent = '首营申请添加客户数据失败!';
                    } else if (data == '6') {
                        dialogContent = '首营申请添加客户数据失败! 营业执照号重复！';
                    } else if (data == "101") {
                        dialogContent = '保存草稿成功！';
                    }
                    else {
                        dialogContent = auditStatus == 1 ? dialogContent = '保存草稿失败！' : '提交审核失败！';
                    }
                    utils.dialog({
                        title: "提示",
                        content: dialogContent,
                        width: 300,
                        height: 30,
                        okValue: '确定',
                        ok: function () {
                            // window.location.href="/process/gTaskList";
                            // var  mid = parent.$('#nav-tab li.active a').attr('href').replace('#', '');
                            // parent.$('#mainFrameTabs').bTabsClose(mid);
                            if (data == '1') {
                                setTimeout(function () {
                                    utils.closeTab();
                                }, 2000)
                            }
                        }
                    }).showModal();

                    $(".ui-dialog-close").hide();
                } else {
                    utils.dialog({
                        title: "提示",
                        content: dialogContent ? dialogContent : "驳回成功",
                        width: 300,
                        height: 30,
                        okValue: '确定',
                        ok: function () {
                            // window.location.href="/process/gTaskList";
                            // var  mid = parent.$('#nav-tab li.active a').attr('href').replace('#', '');
                            // parent.$('#mainFrameTabs').bTabsClose(mid);
                            setTimeout(function () {
                                utils.closeTab();
                            }, 2000)
                        }
                    }).showModal();
                    $(".ui-dialog-close").hide();
                }
                return false;
            },
            error: function () {
                if (customerApplType == 6) {
                    utils.dialog({ content: '提交审核失败', quickClose: true, timeout: 3000 }).showModal();
                } else {
                    utils.dialog({ content: '审核失败', quickClose: true, timeout: 3000 }).showModal();
                }
            },
            complete: function () {
                parent.hideLoading();
            }
        });
    }

}

function checkDate(name, obj, code) {
    var type = "^[1-9][0-9]{0,2}$";
    var r = new RegExp(type);
    var flag = r.test(obj.value);
    if (!flag || (code == 2 && obj.value > 31)) {
        if (code == 2) {
            utils.dialog({ content: name + '须为正整数，且最大为31日。', quickClose: true, timeout: 2000 }).showModal();
        } else {
            utils.dialog({ content: name + '须为正整数，且在适当范围内。', quickClose: true, timeout: 2000 }).showModal();
        }

        obj.value = "";
        return false;
    }
}
//签订人不能重复
function checkRepeat(obj, id, name, id1) {
    var i = id1 ? $(obj).parents('td').prev().find('select').val() + $(obj).val() : $(obj).val();
    $("input[name=" + id + "],select[name=" + id + "]").not($(obj)).each(function () {
        if ((id1 ? $(this).parents('td').prev().find('select').val() + $(this).val() : $(this).val()) == i && $(obj).val() != "") {
            utils.dialog({ content: name + '不能重复。', quickClose: true, timeout: 2000 }).showModal();
            $(obj).val("");
        }
    });
}

/**
 * 当三证合一为是且经营方式≠非营利性时，组织机构代码证号和税务登记证号自动带出营业执照号；
 * 当三证合一为是且经营方式=非营利性时，营业执照号=无且不可编辑；
 * 当三证合一为否时，组织机构代码证号和税务登记证号自行填写
 */
function checkBusinessLicenseNum() {

}

function loadBusinessLicenseNum() {

}

// 是否独立核算
let SyncDataSstaus = false;
$('[name=isIndependent]').on('change', function () {
    // console.log($(this).val())
    let checkedCVal = $(this).val();
    switch (checkedCVal) {
        case '1':
            SyncDataSstaus = true;
            SyncData();
            $('.fileIcon_div').remove();
            break;
        case '0':
            SyncDataSstaus = false;
            AsyncData();
            break;
    }
    //}
})
// 同步数据
function SyncData() {
    let base_billingCompanyName = $('[name=billingCompanyName]').val();
    /**
     * RM 2019-07-19
     *  邮件:  海波好：此需求因切仓给耽误了，还请重新启动处理
     经财务赵总、质管袁总及严老师、卢经理共同确认，决定将开票信息中的 公司名称 与 客户名称 的强制关联关系去掉。具体修改方法如下，请安排开发。
     （1）是否独立核算=“是”时，公司名称默认仍等于公司名称，只需要改成允许编辑即可。（本条需要修改）
     （2）是否独立核算=“否”时，公司名称默认仍等于母公司的开票地址，并且不允许编辑。（本条无需修改，目前系统就是这样的逻辑，只是明确以下）
     非独立核算的客户如果开票信息公司名称不对，还是如以前一样需要修改母公司开票信息的公司地址即可。

        所以 公司名称的值也不用再与上面客户名称的值保持一致
     */
    $('#bill_customerBtn').css('display', 'none');
    if ($('[name=tag_forPage]').val() != 'tag_change_page') { // 客户首营申请页面。。。
        if (SyncDataSstaus) {
            $('[name=billingCompanyName]').val($('.baseInfo_form [name=customerName]').val());
            $('[name=billingCompanyName]').attr('title', $('.baseInfo_form  [name=customerName]').val());
        }
        if ($('[name=tag_forPage]').val() != 'tag_detail_page') {
            $('[name=billingCompanyName]').removeAttr('readonly disabled');
            /**
             *  RM 2019-10-30
             */
            $('[name=taxpayerNum], [name=billingOpeningBank], [name=billingBankAccount]').prop('disabled', true);
        }
        $('[name=taxpayerNum]').val($('[name=taxRegistryNumber]').val());
        $('[name=billingOpeningBank]').val($('[name=openingBank]').val());
        $('[name=billingBankAccount]').val($('[name=bankAccount]').val());
    }

    $('.bill_upfile, .fileIcon_p').prop('disabled', true);
    $('.bill_upfile').next('button').prop('disabled', true);
    let sub_customerNameVal = (window.changeApply && window.changeApply['customerName']) ? window.changeApply['customerName']['valueAfter'] : $('#customerApplVo [name=customerName]').val();
    let billingCompanyName_obj = {
        afterText: sub_customerNameVal,
        changeStatus: "1",
        columnValue: "billingCompanyName",
        valueAfter: sub_customerNameVal,
        valueBefore: base_billingCompanyName,
    }
    $('[name=billingCompanyName]').next('.yulan').addClass('yulanInput_after');
    if (window.changeApply) {
        window.changeApply['billingCompanyName'] = billingCompanyName_obj;
    }
    let registerBoxSel = $('#registerBox').find('select'), registerBoxInp = $('#registerBox').find('input').val(), registerBoxStr = '';
    $(registerBoxSel).each(function (index, item) {
        registerBoxStr += ($(item).find('option:selected').text() == '请选择') ? '' : $(item).find('option:selected').text();;
    })
    registerBoxStr += registerBoxInp;
    //$('[name=businessLicenseAddress]').val(registerBoxStr);
    // 非独立 经营证明 附件
    let fileIcons = $('.fileIcon_p'),
        base_FileIconsVal = [];
    if (fileIcons.length > 0) {
        $(fileIcons).each(function (index, item) {
            let _obj = {}
            _obj.enclosureName = '非独立经营证明' + (index + 1)
            _obj.url = $(item).attr('data-imgurl');
            base_FileIconsVal.push(_obj)
        });
        if (window.changeApply && !window.changeApply['customerEnclosureVoList']) {
            window.changeApply.customerEnclosureVoList = {};

        }
        if (window.changeApply) {
            window.changeApply.customerEnclosureVoList.valueBefore = JSON.stringify(base_FileIconsVal);
        }
        $('#billInfo_form [name=customerEnclosureVoList]').parents('.form-control').find('.yulan').addClass('yulanInput_after');
    }

    if ($('[name=tag_forPage]').val() != 'tag_change_page') { // 客户首营申请页面。。。
        $('.fileIcon_div').remove();
    }

    let sub_taxRegistryNumberVal = (window.changeApply && window.changeApply['taxRegistryNumber']) ? window.changeApply['taxRegistryNumber']['valueAfter'] : $('#customerApplVo [name=taxRegistryNumber]').val();
    let sub_openingBankVal = (window.changeApply && window.changeApply['openingBank']) ? window.changeApply['openingBank']['valueAfter'] : $('#customerApplVo [name=openingBank]').val();
    let sub_bankAccountVal = (window.changeApply && window.changeApply['bankAccount']) ? window.changeApply['bankAccount']['valueAfter'] : $('#customerApplVo [name=bankAccount]').val();
    let sub_salesmanTelVal = (window.changeApply && window.changeApply['salesmanTel']) ? window.changeApply['salesmanTel']['valueAfter'] : $('#customerApplVo [name=salesmanTel]').val();
    customerDataBill(sub_taxRegistryNumberVal, sub_openingBankVal, sub_bankAccountVal) //, registerBoxStr, sub_salesmanTelVal
}
// 手动录入数据
function AsyncData() {
    $('#bill_customerBtn').css('display', 'block');
    if ($('[name=tag_forPage]').val() != 'tag_change_page') { //
        $('[name=billingCompanyName], [name=taxpayerNum], [name=billingOpeningBank], [name=billingBankAccount]').prop('disabled', false);
        $('[name=billingCompanyName]').val('');
        $('[name=billingCompanyName]').attr('title', '');
        $('[name=taxpayerNum]').val('');
        $('[name=billingOpeningBank]').val('');
        $('[name=billingBankAccount]').val('');
        $('.bill_upfile, .fileIcon_p').prop('disabled', false);
    }
    /**
     * 新需求，要求切换为否时清空所有的开票信息的内容
     * 又新需求，不要了
     */
    if ($('[name=tag_forPage]').val() != 'tag_change_page') { //
        $('#billInfo_form [name^=billingProvinceId],#billInfo_form  [name^=billingCityId],#billInfo_form [name^=billingDistrictId], #billInfo_form [name^=billingStreetId]').val('');
        $('#billInfo_form [name^=billingAddress]').val('');
        $('#billInfo_form [name=billingPhone],#billInfo_form [name=businessLicenseAddress]').val('');
    }
    if (window.changeApply && window.changeApply['customerBillingAddressVoList']) {
        delete window.changeApply['customerBillingAddressVoList'];
        $('[name^=billingAddress]').parents('.billList').find('i.yulan').removeClass('yulanInput_after');
    }
    if (window.changeApply && window.changeApply['billingCompanyName']) {
        delete window.changeApply['billingCompanyName'];
        $('[name=billingCompanyName]').next('i').removeClass('yulanInput_after');
    }
    if (window.changeApply && window.changeApply['taxpayerNum']) {
        delete window.changeApply['taxpayerNum']
        $('[name=taxpayerNum]').next('i').removeClass('yulanInput_after');
    }
    if (window.changeApply && window.changeApply['billingOpeningBank']) {
        delete window.changeApply['billingOpeningBank'];
        $('[name=billingOpeningBank]').next('i').removeClass('yulanInput_after');
    }
    if (window.changeApply && window.changeApply['billingBankAccount']) {
        delete window.changeApply['billingBankAccount'];
        $('[name=billingBankAccount]').next('i').removeClass('yulanInput_after');
    }
    if (window.changeApply && window.changeApply['businessLicenseAddress']) {
        delete window.changeApply['businessLicenseAddress'];
        $('[name=businessLicenseAddress]').next('i').removeClass('yulanInput_after');
    }
    if (window.changeApply && window.changeApply['billingPhone']) {
        delete window.changeApply['billingPhone'];
        $('[name=billingPhone]').next('i').removeClass('yulanInput_after');
    }
    if (window.changeApply && window.changeApply['customerEnclosureVoList']) {
        if (JSON.parse(window.changeApply['customerEnclosureVoList']['valueBefore']).length > 0) {
            window.changeApply['customerEnclosureVoList']['afterText'] = '[]';
            window.changeApply['customerEnclosureVoList']['valueAfter'] = '[]';
            let before_fileIcons = window.changeApply['customerEnclosureVoList']['valueBefore'];
            let _arr = JSON.parse(before_fileIcons);
            let ss = '';
            for (let i = 0; i < _arr.length; i++) {
                ss += `
                        <div class="fileIcon_div" data-index="`+ (i + 1) + `">
                            <p class="btn btn-info btn-xs fileIcon_p" data-imgurl="`+ _arr[i].url + `" style="position: relative; margin-right: 10px;">
                                <span class="glyphicon glyphicon-picture"></span>
                            </p>
                            <div class="btn-group fileIcon_showTag" style="position: absolute; top:-23px ; left: -27px; width: 72px; height: 22px;  display: none;">
                                <button type="button" class="btn btn-default btn-xs btn_fileView" onclick="btn_fileView(this)">预览</button>
                                <button type="button" class="btn btn-default btn-xs btn_fileClean" disabled>清空</button>
                            </div>
                        </div>
                    `
            }
            $('.btn_customerFile').prev().before(ss)
        } else {
            delete window.changeApply['customerEnclosureVoList'];
        }
        $('#billInfo_form [name=customerEnclosureVoList]').parents('.form-control').find('.yulan').removeClass('yulanInput_after');
    }
    if ($('[name=tag_forPage]').val() != 'tag_detail_page') {//  && $('[name=tag_forPage]').val() != 'tag_change_page'
        $('.bill_upfile, .fileIcon_p').prop('disabled', false);
        $('.bill_upfile').next('button').prop('disabled', false);
        $('.fileIcon_div').remove();
    }
}
// 提交审核时，当是否独立核算的值为是时，判断上下同步的值是否相等
function checkSyncDataSame() {
    let bool = true;
    let base_customerName = $('.baseInfo_form [name=customerName]').val(),
        bill_customerName = $('[name=billingCompanyName]').val(),
        base_taxRegistryNumber = $('[name=taxRegistryNumber]').val(),
        bill_taxRegistryNumber = $('[name=taxpayerNum]').val(),
        base_openingBank = $('[name=openingBank]').val(),
        bill_openingBank = $('[name=billingOpeningBank]').val(),
        base_bankAccount = $('[name=bankAccount]').val(),
        bill_bankAccount = $('[name=billingBankAccount]').val();
    /**
     * RM 2019-07-19
     * 上面需求有写 为什么修改
     * @type {string[]}
     */
    let checkTagTitle = ['纳税人识别号', '开户银行', '银行账号'], // '公司名称',
        baseInfoTagTitle = ['税务登记账号', '开户银行', '银行账号']; // '客户名称',
    let checkBaseVal = [base_taxRegistryNumber, base_openingBank, base_bankAccount], // base_customerName,
        checkBillVal = [bill_taxRegistryNumber, bill_openingBank, bill_bankAccount]; // bill_customerName,
    for (let i = 0, leng = checkBaseVal.length; i < leng; i++) {
        if (checkBaseVal[i] != checkBillVal[i]) {
            if ($('#edit').val() != '0') {
                bool = false;
                utils.dialog({
                    title: '提示',
                    content: '开票信息中' + checkTagTitle[i] + '字段值与基础属性中的' + baseInfoTagTitle[i] + '字段值不一致,请将【是否独立核算】先选为"否"再选为"是"确保数据一致.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal()
                return false;
            }
        }
    }
    return bool;
}
// 资料变更的时候对上下值做判断
function checkChangeSyncDataSame() {
    let bool = true;
    let change_customerName = '', // 变更后 基础属性 的值 获取
        change_taxRegistryNumber = '',
        change_openingBank = '',
        change_bankAccount = '';
    let change_bill_customerName = '', // 变更后 开票信息 的值 获取
        change_bill_taxRegistryNumber = '',
        change_bill_openingBank = '',
        change_bill_bankAccount = '';

    change_customerName = (window.changeApply && window.changeApply['customerName']) ? window.changeApply['customerName']['valueAfter'] : $('.baseInfo_form [name=customerName]').val();
    change_taxRegistryNumber = (window.changeApply && window.changeApply['taxRegistryNumber']) ? window.changeApply['taxRegistryNumber']['valueAfter'] : $('.baseInfo_form [name=taxRegistryNumber]').val();
    change_openingBank = (window.changeApply && window.changeApply['openingBank']) ? window.changeApply['openingBank']['valueAfter'] : $('.baseInfo_form [name=openingBank]').val();
    change_bankAccount = (window.changeApply && window.changeApply['bankAccount']) ? window.changeApply['bankAccount']['valueAfter'] : $('.baseInfo_form [name=bankAccount]').val();

    change_bill_customerName = (window.changeApply && window.changeApply['billingCompanyName']) ? window.changeApply['billingCompanyName']['valueAfter'] : $('#billInfo_form [name=billingCompanyName]').val();
    change_bill_taxRegistryNumber = (window.changeApply && window.changeApply['taxpayerNum']) ? window.changeApply['taxpayerNum']['valueAfter'] : $('#billInfo_form [name=taxpayerNum]').val();
    change_bill_openingBank = (window.changeApply && window.changeApply['billingOpeningBank']) ? window.changeApply['billingOpeningBank']['valueAfter'] : $('#billInfo_form [name=billingOpeningBank]').val();
    change_bill_bankAccount = (window.changeApply && window.changeApply['billingBankAccount']) ? window.changeApply['billingBankAccount']['valueAfter'] : $('#billInfo_form [name=billingBankAccount]').val();

    let checkTagTitle = ['纳税人识别号', '开户银行', '银行账号'], // '公司名称',
        baseInfoTagTitle = ['税务登记账号', '开户银行', '银行账号']; // '客户名称',
    let checkBaseVal = [change_taxRegistryNumber, change_openingBank, change_bankAccount], // change_customerName,
        checkBillVal = [change_bill_taxRegistryNumber, change_bill_openingBank, change_bill_bankAccount]; // change_bill_customerName,
    for (let i = 0, leng = checkBaseVal.length; i < leng; i++) {
        if (checkBaseVal[i] != checkBillVal[i]) {
            if ($('#edit').val() != '0') {
                bool = false;
                utils.dialog({
                    title: '提示',
                    content: '开票信息中' + checkTagTitle[i] + '字段值与基础属性中的' + baseInfoTagTitle[i] + '字段值不一致,请将【是否独立核算】先选为"否"再选为"是"确保数据一致.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal()
                return false;
            }
        }
    }
    return bool;
}
//点击放大镜触发
$(document).on("click", "#bill_customerBtn", function (ev) {
    bill_search(ev);
});
//回车触发
$(document).on("keydown", "input[name=billingCompanyName]", function (ev) {
    if (ev.keyCode == 13) {
        bill_search(ev);
    }
});
function bill_search(ev) {
    var supplierName = $("[name=billingCompanyName]").val();
    utils.dialog({
        title: '客户列表',
        url: '/proxy-customer/customer/customerBaseAppl/toCustomerList',
        width: $(window).width() * 0.8,
        height: 600,
        data: supplierName, // 给modal 要传递的 的数据
        onclose: function () {
            if (this.returnValue) {
                var data = this.returnValue;
                //// console.log(data)
                $.ajax({
                    url: '/proxy-customer/customer/customerBaseAppl/queryOrganBaseBillingById',
                    data: { "id": data.id },
                    type: "post",
                    dataType: 'json',
                    success: function (data) {
                        // console.log(data)
                        if (data.code == 0) {
                            $('[name=billingCompanyName]').val(data.result.baseVo.customerName);
                            if ($('[name=tag_forPage]').val() != 'tag_change_page') {
                                $('[name=taxpayerNum]').val(data.result.baseVo.taxRegistryNumber);
                                $('[name=billingOpeningBank]').val(data.result.baseVo.bankName);
                                $('[name=billingBankAccount]').val(data.result.baseVo.bankAccount);
                                $('[name=businessLicenseAddress]').val(data.result.baseVo.businessLicenseAddress);
                                $('[name=billingPhone]').val(data.result.baseVo.billingPhone)
                            }
                            /**
                             *  客户资料变更页面 对修改后的数据记录在  changeApply  里面
                             *  纳税人识别号、 开户银行 、 银行账号
                             *  taxpayerNum  、 billingOpeningBank 、 billingBankAccount
                             */
                            customerDataBill(data.result.baseVo.taxRegistryNumber, data.result.baseVo.bankName, data.result.baseVo.bankAccount, data.result.baseVo.businessLicenseAddress, data.result.baseVo.billingPhone)
                        }

                    }
                })
            }
        }
    }).showModal();
    ev.stopPropagation();
}
//  客户资料变更 开票信息 变更 数据保存 changeApply
function customerDataBill(taxRegistryNumber, bankName, bankAccount, businessLicenseAddress, billingPhone) {
    let base_taxpayerNum = $('[name=taxpayerNum]').val(),
        base_billingOpeningBank = $('[name=billingOpeningBank]').val(),
        base_billingBankAccount = $('[name=billingBankAccount]').val(),
        base_businessLicenseAddress = $('[name=businessLicenseAddress]').val(),
        base_billingPhone = $('[name=billingPhone]').val();
    //if(window.changeApply){
    let taxpayerNum_obj = {
        afterText: (taxRegistryNumber ? taxRegistryNumber : ''),
        changeStatus: "1",
        columnValue: "taxpayerNum",
        valueAfter: (taxRegistryNumber ? taxRegistryNumber : ''),
        valueBefore: base_taxpayerNum,
    };
    let billingOpeningBank_obj = {
        afterText: (bankName ? bankName : ''),
        changeStatus: "1",
        columnValue: "billingOpeningBank",
        valueAfter: (bankName ? bankName : ''),
        valueBefore: base_billingOpeningBank,
    };
    let billingBankAccount_obj = {
        afterText: (bankAccount ? bankAccount : ''),
        changeStatus: "1",
        columnValue: "billingBankAccount",
        valueAfter: (bankAccount ? bankAccount : ''),
        valueBefore: base_billingBankAccount,
    }
    // 当是否独立经营核算切换为是的时候， 不需要给营业执照地址和电话同步数据，所以同步数据方法就不给这个传参了
    let businessLicenseAddress_obj = {}, billingPhone_obj = {};
    if (businessLicenseAddress != undefined) {
        businessLicenseAddress_obj = {
            afterText: (businessLicenseAddress ? businessLicenseAddress : ''),
            changeStatus: "1",
            columnValue: "businessLicenseAddress",
            valueAfter: (businessLicenseAddress ? businessLicenseAddress : ''),
            valueBefore: base_businessLicenseAddress,
        }
        $('[name=businessLicenseAddress]').next('.yulan').addClass('yulanInput_after');
    }
    if (billingPhone != undefined) {
        billingPhone_obj = {
            afterText: (billingPhone ? billingPhone : ''),
            changeStatus: "1",
            columnValue: "billingPhone",
            valueAfter: (billingPhone ? billingPhone : ''),
            valueBefore: base_billingPhone,
        }
        $('[name=billingPhone]').next('.yulan').addClass('yulanInput_after');
    }

    $('[name=taxpayerNum], [name=billingOpeningBank], [name=billingBankAccount]').next('.yulan').addClass('yulanInput_after');
    if (window.changeApply) {
        window.changeApply['taxpayerNum'] = taxpayerNum_obj;
        window.changeApply['billingOpeningBank'] = billingOpeningBank_obj;
        window.changeApply['billingBankAccount'] = billingBankAccount_obj;
        if (businessLicenseAddress_obj.columnValue) {
            window.changeApply['businessLicenseAddress'] = businessLicenseAddress_obj;
        }
        if (billingPhone_obj.columnValue) {
            window.changeApply['billingPhone'] = billingPhone_obj;
        }
        if (window.changeApply['customerEnclosureVoList']) {
            $('[name=customerEnclosureVoList]').parents('.input-group').find('.yulan').addClass('yulanInput_after');
            let customerEnclosureVoList_obj = {
                afterText: window.changeApply['customerEnclosureVoList']['afterText'],
                changeStatus: "1",
                columnValue: "customerEnclosureVoList",
                valueAfter: window.changeApply['customerEnclosureVoList']['valueAfter'],
                valueBefore: window.changeApply['customerEnclosureVoList']['valueBefore'],
            };
            window.changeApply['customerEnclosureVoList'] = customerEnclosureVoList_obj;
        }
    }

    //}
}
$(function () {
    // 非独立经营证明 图片上传
    $('.bill_upfile').change(function () {
        let fileIconLen = $('.fileIcon_p').length;
        if (fileIconLen < 2) {
            var that = this;
            var file = this.files;
            var maxsize = 2 * 1024 * 1024; //2M
            for (var i = 0; i < file.length; i++) {
                var size = file[i].size; //图片大小
                if (size > maxsize) {
                    alert('图片过大，单张图片大小不超过2M');
                    that.value = '';
                    return false;
                }
            }
            sendFile(file);
        } else {
            utils.dialog({
                title: '提示',
                content: '最多上传两张',
                okValue: '确定',
                ok: function () { }
            }).showModal();
            return false;
        }

    })
    //当 页面加载进来发票类型 为增值税专用发票的时候 需要给营业执照地址和电话添加必填校验
    if ($('#billInfo_form [name=invoiceType]:checked').val() == '2') {
        $('[name=businessLicenseAddress]')['addClass']('{validate:{ required :true}}');
        $('[name=businessLicenseAddress]').prev().html('<i class="text-require">*  </i>营业执照地址');
        $('[name=billingPhone]').prev().html('<i class="text-require">*  </i>电话');
        $('[name=billingPhone]').removeClass().addClass('form-control {validate:{required :true, isTel :true}}');
    }
    //当 页面加载进来
    //当发票类型为 【增值税专用发票】或 【增值税电子专用发票时】或【专票】时，基础属性中的【开户银行】，【开户户名】，【银行账号】为必填项，同时 开票信息中的 【开户银行】【银行账号】也为必填项。
    //当发票类型为 非【增值税专用发票】或 【增值税电子专用发票时】时，基础属性中的【开户银行】，【开户户名】，【银行账号】为非必填项，同时 开票信息中的 【开户银行】【银行账号】也为非必填项。
    if ($('#billInfo_form [name=invoiceType]:checked').val() == '1' || $('#billInfo_form [name=invoiceType]:checked').val() == '3' || $('#billInfo_form [name=invoiceType]:checked').val() == '5' ) {
        $('[name=openingBank]').attr("class", "form-control {validate:{ }}")
        $('[name=openingBank]').prev().html('开户银行')
        $('[name=bankAccountName]').attr("class", "form-control {validate:{ }}")
        $('[name=bankAccountName]').prev().html('开户户名')
        $('[name=bankAccount]').attr("class", "form-control {validate:{ }}")
        $('[name=bankAccount]').prev().html('银行账号')
        $('[name=billingOpeningBank]').attr("class", "form-control {validate:{ }}")
        $('[name=billingOpeningBank]').prev().html('开户银行')
        $('[name=billingBankAccount]').attr("class", "form-control {validate:{ }}")
        $('[name=billingBankAccount]').prev().html('银行账号')
    } else {
        $('[name=openingBank]').attr("class", "form-control {validate:{ required :true}}")
        $('[name=openingBank]').prev().html('<i class="text-require">*  </i>开户银行')
        $('[name=bankAccountName]').attr("class", "form-control {validate:{ required :true}}")
        $('[name=bankAccountName]').prev().html('<i class="text-require">*  </i>开户户名')
        $('[name=bankAccount]').attr("class", "form-control {validate:{ required :true}}")
        $('[name=bankAccount]').prev().html('<i class="text-require">*  </i>银行账号')
        $('[name=billingOpeningBank]').attr("class", "form-control {validate:{ required :true}}")
        $('[name=billingOpeningBank]').prev().html('<i class="text-require">*  </i>开户银行')
        $('[name=billingBankAccount]').attr("class", "form-control {validate:{ required :true}}")
        $('[name=billingBankAccount]').prev().html('<i class="text-require">*  </i>银行账号')
    }
    // 是否独立核算  数据加载回来有值的时候 ，根据值来设置公司名称 是否可编辑
    setTimeout(function () {
        let pageType = $('[name=tag_forPage]').val();
        let isIndependentVal = $('[name=isIndependent]:checked').val();
        if (pageType != 'tag_change_page') {
            (isIndependentVal == '1') ? SyncData() : null;
        }
    }, 200)
})

//往服务器发送图片1
function sendFile(files) {
    var formData = new FormData();
    for (var i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }
    let curimgLen = $('.fileIcon_div')
    var d = utils.dialog({
        content: '正在上传..'
    }).showModal();
    $.ajax({
        url: '/proxy-sysmanage/upload/upload',
        data: formData,
        type: 'post',
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function (data) {
            // console.log(data)
            $('.bill_upfile').val(''); //清空按钮value
            d.close().remove();
            if (data.code == 0) {
                utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '上传成功',
                    okValue: '确定',
                    ok: function () {
                    }
                }).showModal();
                var imgArr = data.result;
                $('.bill_upfile').parent().before(`
                    <div class="fileIcon_div" data-index="`+ ((curimgLen.length == 0) ? '0' : '1') + `">
                        <p class="btn btn-info btn-xs fileIcon_p" data-imgUrl="`+ imgArr[0] + `" style="position: relative; margin-right: 10px;">
                            <span class="glyphicon glyphicon-picture"></span>
                        </p>
                        <div class="btn-group fileIcon_showTag" style="position: absolute; top:-23px ; left: -27px;width: 72px; height: 22px; display: none;">
                            <button type="button" class="btn btn-default btn-xs btn_fileView" onclick="btn_fileView(this)">预览</button>
                            <button type="button" class="btn btn-default btn-xs btn_fileClean" onclick="btn_fileClean(this)">清空</button>
                        </div>
                    </div>
                `)
            } else {
                utils.dialog({
                    title: '提示',
                    width: '200px',
                    content: '上传失败',
                    okValue: '确定',
                    ok: function () {
                    }
                }).showModal();
            }
        },
        error: function () {
            $('.bill_upfile').val('');
            d.close().remove();
        }
    })
}
// 非独立经营证明 ，点击图片 图标，
$('body').on('click', '.fileIcon_p', function (e) {
    let all_fileIcon_p = $('.fileIcon_p');
    let thisIndex = $(this).parents('.fileIcon_div').attr('data-index');
    let that = $(this);
    $(all_fileIcon_p).each(function (index, item) {
        if (thisIndex == index) {
            if (that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display') != 'none') {
                that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display', 'none')
            } else {
                that.parents('.fileIcon_div').find('.fileIcon_showTag').find('button').removeClass('disNone');
                that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display', 'block')
                that.parents('.fileIcon_div').siblings().find('.fileIcon_showTag').css('display', 'none')
            }
            return false;
        } else {
            if (that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display') != 'none') {
                that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display', 'none')
            } else {
                that.parents('.fileIcon_div').find('.fileIcon_showTag').find('button').removeClass('disNone');
                that.parents('.fileIcon_div').find('.fileIcon_showTag').css('display', 'block')
                that.parents('.fileIcon_div').siblings().find('.fileIcon_showTag').css('display', 'none')
            }
            return false;
        }
    })
    let pageType = $('[name=tag_forPage]').val();
    if (pageType == 'tag_edit_page' || !$('.btn_customerFile').prop('disabled')) { // 编辑页面
        $('.btn_fileClean').prop('disabled', false)
    } else {
        $('.btn_fileClean').prop('disabled', true)
    }
})
// 非独立经营证明  预览
function btn_fileView(that) {
    let urlList = [$(that).parents('.fileIcon_div').find('.fileIcon_p').attr('data-imgUrl')];
    $.viewImg({
        fileParam: {
            name: 'fileName',
            url: 'url'
        },
        list: urlList
    })
}
// 非独立经营证明 清空
function btn_fileClean(that) {
    $(that).parents('.fileIcon_div').remove();
}

// 提交审核的时候 是否独立经营为否，发票类型为增值税发票，当母公司数据中纳税人登记号、卡户银行、银行账号营业执照地址 电话如果为空则不允许提交
function checkValEmpty() {
    let isIndependentVal = '',
        invoiceTypeVal = '',
        taxpayerNumVal = '',
        billingOpeningBankVal = '',
        billingBankAccountVal = '',
        businessLicenseAddressVal = '',
        billingPhoneVal = '';
    isIndependentVal = (window.changeApply && window.changeApply['isIndependent']) ? window.changeApply['isIndependent']['valueAfter'] : $('#billInfo_form [name=isIndependent]:checked').val();
    invoiceTypeVal = (window.changeApply && window.changeApply['invoiceType']) ? window.changeApply['invoiceType']['valueAfter'] : $('#billInfo_form [name=invoiceType]:checked').val();
    taxpayerNumVal = (window.changeApply && window.changeApply['taxpayerNum']) ? window.changeApply['taxpayerNum']['valueAfter'] : $('#billInfo_form [name=taxpayerNum]').val();
    billingOpeningBankVal = (window.changeApply && window.changeApply['billingOpeningBank']) ? window.changeApply['billingOpeningBank']['valueAfter'] : $('#billInfo_form [name=billingOpeningBank]').val();
    billingBankAccountVal = (window.changeApply && window.changeApply['billingBankAccount']) ? window.changeApply['billingBankAccount']['valueAfter'] : $('#billInfo_form [name=billingBankAccount]').val();
    businessLicenseAddressVal = (window.changeApply && window.changeApply['businessLicenseAddress']) ? window.changeApply['businessLicenseAddress']['valueAfter'] : $('#billInfo_form [name=businessLicenseAddress]').val();
    billingPhoneVal = (window.changeApply && window.changeApply['billingPhone']) ? window.changeApply['billingPhone']['valueAfter'] : $('#billInfo_form [name=billingPhone]').val();

    let checkValArr = [taxpayerNumVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal];
    let bool = false;
    if (isIndependentVal == '0' && invoiceTypeVal == '2') {
        for (let i = 0; i < checkValArr.length; i++) {
            if (checkValArr[i] == '') {
                bool = true;
                return bool;
            }
        }
    }
    return bool;
}
// 提交审核的时候 是否独立经营为否，发票类型为增值税发票，当母公司数据中纳税人登记号、卡户银行、银行账号 如果为空则不允许提交
function anotherCheckValEmpty() {
    let isIndependentVal = '',
        invoiceTypeVal = '',
        taxpayerNumVal = '',
        billingOpeningBankVal = '',
        billingBankAccountVal = '';
    isIndependentVal = (window.changeApply && window.changeApply['isIndependent']) ? window.changeApply['isIndependent']['valueAfter'] : $('#billInfo_form [name=isIndependent]:checked').val();
    invoiceTypeVal = (window.changeApply && window.changeApply['invoiceType']) ? window.changeApply['invoiceType']['valueAfter'] : $('#billInfo_form [name=invoiceType]:checked').val();
    taxpayerNumVal = (window.changeApply && window.changeApply['taxpayerNum']) ? window.changeApply['taxpayerNum']['valueAfter'] : $('#billInfo_form [name=taxpayerNum]').val();
    billingOpeningBankVal = (window.changeApply && window.changeApply['billingOpeningBank']) ? window.changeApply['billingOpeningBank']['valueAfter'] : $('#billInfo_form [name=billingOpeningBank]').val();
    billingBankAccountVal = (window.changeApply && window.changeApply['billingBankAccount']) ? window.changeApply['billingBankAccount']['valueAfter'] : $('#billInfo_form [name=billingBankAccount]').val();

    let checkValArr = [taxpayerNumVal, billingOpeningBankVal, billingBankAccountVal];
    let bool = false;
    if (isIndependentVal == '1' && invoiceTypeVal == '2') {
        for (let i = 0; i < checkValArr.length; i++) {
            if (checkValArr[i] == '') {
                bool = true;
                return bool;
            }
        }
    }
    return bool;
}

/**
 *  发票单选框绑定change事件
 *  --  当发票类型为 【增值税专用发票】或 【增值税电子专用发票时】或【全电普票】时，基础属性中的【开户银行】，【开户户名】，【银行账号】为必填项，同时 开票信息中的 【开户银行】【银行账号】也为必填项。
 *  --  当发票类型为 非【增值税专用发票】或 【增值税电子专用发票时】时，基础属性中的【开户银行】，【开户户名】，【银行账号】为非必填项，同时 开票信息中的 【开户银行】【银行账号】也为非必填项。
 */

$(document).ready(function () {
    $('input[type=radio][name=invoiceType]').change(function () {
        if (this.value == '1' || this.value == '3' || this.value == '5' ) {
            $('[name=openingBank]').attr("class", "form-control {validate:{ }}")
            $('[name=openingBank]').prev().html('开户银行')
            $('[name=bankAccountName]').attr("class", "form-control {validate:{ }}")
            $('[name=bankAccountName]').prev().html('开户户名')
            $('[name=bankAccount]').attr("class", "form-control {validate:{ }}")
            $('[name=bankAccount]').prev().html('银行账号')
            $('[name=billingOpeningBank]').attr("class", "form-control {validate:{ }}")
            $('[name=billingOpeningBank]').prev().html('开户银行')
            $('[name=billingBankAccount]').attr("class", "form-control {validate:{ }}")
            $('[name=billingBankAccount]').prev().html('银行账号')
        } else {
            $('[name=openingBank]').attr("class", "form-control {validate:{ required :true}}")
            $('[name=openingBank]').prev().html('<i class="text-require">*  </i>开户银行')
            $('[name=bankAccountName]').attr("class", "form-control {validate:{ required :true}}")
            $('[name=bankAccountName]').prev().html('<i class="text-require">*  </i>开户户名')
            $('[name=bankAccount]').attr("class", "form-control {validate:{ required :true}}")
            $('[name=bankAccount]').prev().html('<i class="text-require">*  </i>银行账号')
            $('[name=billingOpeningBank]').attr("class", "form-control {validate:{ required :true}}")
            $('[name=billingOpeningBank]').prev().html('<i class="text-require">*  </i>开户银行')
            $('[name=billingBankAccount]').attr("class", "form-control {validate:{ required :true}}")
            $('[name=billingBankAccount]').prev().html('<i class="text-require">*  </i>银行账号')
        }
    });
});

/**
 *  客户首营、变更
 *  --  是否独立经营为否，发票类型为增值税发票时，当所选公司名称返回的值（纳税人识别号、开户银行、银行账号） 数据不全时，给出修改提示
 *  --  是否独立经营为否，发票类型为电子普通发票时，当所选公司名称返回的值（纳税人识别号） 数据不全时，给出修改提示
 */
function validBillInfoForm_fun() {
    let bool = false;
    let isIndependentValSub = (window.changeApply && window.changeApply['isIndependent']) ? window.changeApply['isIndependent']['valueAfter'] : $('#billInfo_form [name=isIndependent]:checked').val();
    let invoiceTypeValSub = (window.changeApply && window.changeApply['invoiceType']) ? window.changeApply['invoiceType']['valueAfter'] : $('#billInfo_form [name=invoiceType]:checked').val();
    let billingCompanyNameVal = (window.changeApply && window.changeApply['billingCompanyName']) ? window.changeApply['billingCompanyName']['valueAfter'] : $('#billInfo_form [name=billingCompanyName]').val(),
        taxpayerNumVal = (window.changeApply && window.changeApply['taxpayerNum']) ? window.changeApply['taxpayerNum']['valueAfter'] : $('#billInfo_form [name=taxpayerNum]').val();
    let openingBankVal = (window.changeApply && window.changeApply['openingBank']) ? window.changeApply['openingBank']['valueAfter'] : $('#customerApplVo [name=openingBank]').val(),
        bankAccountNameVal = (window.changeApply && window.changeApply['bankAccountName']) ? window.changeApply['bankAccountName']['valueAfter'] : $('#customerApplVo [name=bankAccountName]').val(),
        bankAccountVal = (window.changeApply && window.changeApply['bankAccount']) ? window.changeApply['bankAccount']['valueAfter'] : $('#customerApplVo [name=bankAccount]').val(),
        billingOpeningBankVal = (window.changeApply && window.changeApply['billingOpeningBank']) ? window.changeApply['billingOpeningBank']['valueAfter'] : $('#billInfo_form [name=billingOpeningBank]').val(),
        billingBankAccountVal = (window.changeApply && window.changeApply['billingBankAccount']) ? window.changeApply['billingBankAccount']['valueAfter'] : $('#billInfo_form [name=billingBankAccount]').val(),
        businessLicenseAddressVal = (window.changeApply && window.changeApply['businessLicenseAddress']) ? window.changeApply['businessLicenseAddress']['valueAfter'] : $('#billInfo_form [name=businessLicenseAddress]').val(),
        billingPhoneVal = (window.changeApply && window.changeApply['billingPhone']) ? window.changeApply['billingPhone']['valueAfter'] : $('#billInfo_form [name=billingPhone]').val(),
        billCustomerBillingAddressVoList = (window.changeApply && window.changeApply['customerBillingAddressVoList']) ? window.changeApply['customerBillingAddressVoList']['valueAfter'] : getBillCustomerBillingAddressVoList();
    let customerNatureVal = (window.changeApply && window.changeApply['nature']) ? window.changeApply['nature']['valueAfter'] : $('[name=nature]').val();
    if (invoiceTypeValSub == undefined) {
        bool = true;
        utils.dialog({
            title: '提示',
            content: '发票类型为空，不允许审核通过，请到豆芽系统填写发票类型！',
            okValue: '确定',
            ok: function () { }
        }).showModal();
        return bool;
    }
    if (isIndependentValSub == '0') {
        if (invoiceTypeValSub == '1' || invoiceTypeValSub == '5') { //  电子普通发票的时候或者全电专票
            let _Arr = [],
                titleArr = [],
                titleTipsArr = [];
            _Arr = (customerNatureVal != '3' && customerNatureVal != '0') ? [billingCompanyNameVal, taxpayerNumVal] : [billingCompanyNameVal];
            titleArr = (customerNatureVal != '3' && customerNatureVal != '0') ? ['公司名称', '纳税人识别号'] : ['公司名称'];

            for (let i = 0; i < titleArr.length; i++) {
                if (_Arr[i] == '') {
                    titleTipsArr.push(titleArr[i])
                }
            }
            if (titleTipsArr.length != 0) {
                bool = true;
                utils.dialog({
                    title: '提示',
                    content: '当开票信息独立核算为否，发票类型为电子发票时，' + titleTipsArr.toString() + '的值为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return bool;
            }
        } else if (invoiceTypeValSub == '2') { // 增值税专用发票
            let checkValArr = [],
                checkValTitleArr = [],
                titleTipsArr = [];
            checkValArr = (customerNatureVal != '3' && customerNatureVal != '0') ? [openingBankVal, bankAccountNameVal, bankAccountVal, taxpayerNumVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal] : [openingBankVal, bankAccountNameVal, bankAccountVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal]
            checkValTitleArr = (customerNatureVal != '3' && customerNatureVal != '0') ? ['开户银行(基础属性)', '开户户名(基础属性)', '银行账号(基础属性)', '纳税人识别号', '开户银行(开票信息)', '银行账号(开票信息)', '营业执照地址', '电话'] : ['开户银行(基础属性)', '开户户名(基础属性)', '银行账号(基础属性)', '开户银行(开票信息)', '银行账号(开票信息)', '营业执照地址', '电话'];
            for (let i = 0; i < checkValArr.length; i++) {
                if (checkValArr[i] == '') {
                    titleTipsArr.push(checkValTitleArr[i])
                }
            }
            if (titleTipsArr.length != 0) {
                bool = true;
                utils.dialog({
                    title: '提示',
                    content: '当开票信息独立核算为否，发票类型为增值税发票时，公司名称为【' + billingCompanyNameVal + '】的' + titleTipsArr.toString() + '的值为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return bool;
            }
        } else if (invoiceTypeValSub == '3') { // 纸质普通发票

            let checkValArr = [],
                checkValTitleArr = [],
                titleTipsArr = [];
            _Arr = (customerNatureVal != '3' && customerNatureVal != '0') ? [billingCompanyNameVal, taxpayerNumVal] : [billingCompanyNameVal];
            titleArr = (customerNatureVal != '3' && customerNatureVal != '0') ? ['公司名称', '纳税人识别号'] : ['公司名称'];
            for (let i = 0; i < checkValArr.length; i++) {
                if (checkValArr[i] == '') {
                    titleTipsArr.push(checkValTitleArr[i])
                }
            }
            if (titleTipsArr.length != 0) {
                bool = true;
                utils.dialog({
                    title: '提示',
                    content: '当开票信息独立核算为否，发票类型为纸质普通发票时，公司名称为【' + billingCompanyNameVal + '】的' + titleTipsArr.toString() + '的值为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return bool;
            }
        } else { // 增值税专用电子发票
            let checkValArr = [],
                checkValTitleArr = [],
                titleTipsArr = [];
            checkValArr = (customerNatureVal != '3' && customerNatureVal != '0') ? [openingBankVal, bankAccountNameVal, bankAccountVal, taxpayerNumVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal] : [openingBankVal, bankAccountNameVal, bankAccountVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal]
            checkValTitleArr = (customerNatureVal != '3' && customerNatureVal != '0') ? ['开户银行(基础属性)', '开户户名(基础属性)', '银行账号(基础属性)', '纳税人识别号', '开户银行(开票信息)', '银行账号(开票信息)', '营业执照地址', '电话'] : ['开户银行(基础属性)', '开户户名(基础属性)', '银行账号(基础属性)', '开户银行(开票信息)', '银行账号(开票信息)', '营业执照地址', '电话'];
            for (let i = 0; i < checkValArr.length; i++) {
                if (checkValArr[i] == '') {
                    titleTipsArr.push(checkValTitleArr[i])
                }
            }
            if (titleTipsArr.length != 0) {
                bool = true;
                utils.dialog({
                    title: '提示',
                    content: '当开票信息独立核算为否，发票类型为增值税专用电子发票时，公司名称为【' + billingCompanyNameVal + '】的' + titleTipsArr.toString() + '的值为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return bool;
            }
        }
    } else if (isIndependentValSub == '1') { // 是否独立核算 提交的值为是
        if (invoiceTypeValSub == '1' || invoiceTypeValSub == '5') { //  电子普通发票的时候或者全电专票
            let _Arr = [],
                titleArr = [],
                titleTipsArr = [];
            _Arr = (customerNatureVal != '3' && customerNatureVal != '0') ? [billingCompanyNameVal, taxpayerNumVal] : [billingCompanyNameVal];
            titleArr = (customerNatureVal != '3' && customerNatureVal != '0') ? ['公司名称', '纳税人识别号'] : ['公司名称'];

            for (let i = 0; i < titleArr.length; i++) {
                if (_Arr[i] == '') {
                    titleTipsArr.push(titleArr[i])
                }
            }
            if (titleTipsArr.length != 0) {
                bool = true;
                utils.dialog({
                    title: '提示',
                    content: '当开票信息独立核算为是，发票类型为电子发票时，' + titleTipsArr.toString() + '的值为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return bool;
            }
        } else if (invoiceTypeValSub == '2') { // 增值税专用发票
            let checkValArr = [],
                checkValTitleArr = [],
                titleTipsArr = [];
            checkValArr = (customerNatureVal != '3' && customerNatureVal != '0') ? [openingBankVal, bankAccountNameVal, bankAccountVal, taxpayerNumVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal] : [openingBankVal, bankAccountNameVal, bankAccountVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal]
            checkValTitleArr = (customerNatureVal != '3' && customerNatureVal != '0') ? ['开户银行(基础属性)', '开户户名(基础属性)', '银行账号(基础属性)', '纳税人识别号', '开户银行(开票信息)', '银行账号(开票信息)', '营业执照地址', '电话'] : ['开户银行(基础属性)', '开户户名(基础属性)', '银行账号(基础属性)', '开户银行(开票信息)', '银行账号(开票信息)', '营业执照地址', '电话'];
            for (let i = 0; i < checkValArr.length; i++) {
                if (checkValArr[i] == '') {
                    titleTipsArr.push(checkValTitleArr[i])
                }
            }
            if (titleTipsArr.length != 0) {
                bool = true;
                utils.dialog({
                    title: '提示',
                    content: '当开票信息独立核算为是，发票类型为增值税发票时，公司名称为【' + billingCompanyNameVal + '】的' + titleTipsArr.toString() + '的值为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return bool;
            }
        } else if (invoiceTypeValSub == '3') { // 纸质普通发票

            let checkValArr = [],
                checkValTitleArr = [],
                titleTipsArr = [];
            _Arr = (customerNatureVal != '3' && customerNatureVal != '0') ? [billingCompanyNameVal, taxpayerNumVal] : [billingCompanyNameVal];
            titleArr = (customerNatureVal != '3' && customerNatureVal != '0') ? ['公司名称', '纳税人识别号'] : ['公司名称'];
            for (let i = 0; i < checkValArr.length; i++) {
                if (checkValArr[i] == '') {
                    titleTipsArr.push(checkValTitleArr[i])
                }
            }
            if (titleTipsArr.length != 0) {
                bool = true;
                utils.dialog({
                    title: '提示',
                    content: '当开票信息独立核算为是，发票类型为纸质普通发票时，公司名称为【' + billingCompanyNameVal + '】的' + titleTipsArr.toString() + '的值为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return bool;
            }
        } else { // 增值税专用电子发票
            let checkValArr = [],
                checkValTitleArr = [],
                titleTipsArr = [];
            checkValArr = (customerNatureVal != '3' && customerNatureVal != '0') ? [openingBankVal, bankAccountNameVal, bankAccountVal, taxpayerNumVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal] : [openingBankVal, bankAccountNameVal, bankAccountVal, billingOpeningBankVal, billingBankAccountVal, businessLicenseAddressVal, billingPhoneVal]
            checkValTitleArr = (customerNatureVal != '3' && customerNatureVal != '0') ? ['开户银行(基础属性)', '开户户名(基础属性)', '银行账号(基础属性)', '纳税人识别号', '开户银行(开票信息)', '银行账号(开票信息)', '营业执照地址', '电话'] : ['开户银行(基础属性)', '开户户名(基础属性)', '银行账号(基础属性)', '开户银行(开票信息)', '银行账号(开票信息)', '营业执照地址', '电话'];
            for (let i = 0; i < checkValArr.length; i++) {
                if (checkValArr[i] == '') {
                    titleTipsArr.push(checkValTitleArr[i])
                }
            }
            if (titleTipsArr.length != 0) {
                bool = true;
                utils.dialog({
                    title: '提示',
                    content: '当开票信息独立核算为是，发票类型为增值税专用电子发票时，公司名称为【' + billingCompanyNameVal + '】的' + titleTipsArr.toString() + '的值为必填项.',
                    okValue: '确定',
                    ok: function () { }
                }).showModal();
                return bool;
            }
        }
    }
    // if(billCustomerBillingAddressVoList.length == 0){
    //     bool = true;
    //     utils.dialog({
    //         title: '提示',
    //         content: '发票邮寄地址不能为空.',
    //         okValue: '确定',
    //         ok: function () {}
    //     }).showModal();
    //     return bool;
    // }
    return bool;
}

// 获取 发票邮寄地址 输入框中的 选中值
function getBillCustomerBillingAddressVoList() {
    let arr = [];
    let proId = $('[name^=billingProvinceId]').find('option:selected').val(),
        cityId = $('[name^=billingCityId]').find('option:selected').val(),
        distId = $('[name^=billingDistrictId]').find('option:selected').val();
    // 对于第四级得下拉框不需要校验
    arr = [proId, cityId, distId];
    let bool = arr.some(function (item, index) {
        return item == ''
    })
    if (bool) {
        arr = []
    }
    return arr;
}

// 校验基础属性中的客户名称 与 开票信息中的公司名称是否一致
function validateCustomerName() {
    const customerName = $("[name=customerName]").val()
    const billingCompanyName = $("[name=billingCompanyName]").val()
    return customerName == billingCompanyName
}
