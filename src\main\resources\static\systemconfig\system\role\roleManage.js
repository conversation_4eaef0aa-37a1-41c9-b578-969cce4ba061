 var orgCode = "${orgCode!''}";
   console.log("roleManage-->load start:orgCode="+orgCode);
   function showDialog(titleText,contentText){
        //提示框
        utils.dialog({
            width: 180,
            height: 30,
            title: titleText,
            content: contentText,
            quickClose: false,
            okValue: '确定',
            ok: function () {},
        }).showModal();
    }
    
    function showDialogWithCallback(titleText,contentText,callback){
        //提示框
        utils.dialog({
            width: 180,
            height: 30,
            title: titleText,
            content: contentText,
            quickClose: false,
            okValue: '确定',
            ok: callback,
        }).showModal();
    }
    
        
    //弹框提示
    function showTips(contentText){
        utils.dialog({
            content: contentText,
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
    
    //时间转换
    function datetimeFormatter(val) {
        if (val != null && val !="") {
            return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
        } else {
            return "";
        }
    };
    
    function searchData(){
        var result = searchCheck();
        console.log("seachData-->click--data:"+JSON.stringify(result));
        //console.log("seachData-->click--data:"+JSON.stringify(result));
        if(!result.passed){
            return;
        }

        $("#X_Table").setGridParam({
            url: '/proxy-sysmanage/sysmanage/role/queryRoleListByOrgCode',
            postData: result.data
        }).trigger('reloadGrid');
    }
    
    $(function () {
        //角色表
        $('#X_Table').XGrid({
            url: '/proxy-sysmanage/sysmanage/role/queryRoleListByOrgCode',
            //postData:{orgCode:orgCode},
            postData:{orgCode:"-1"},//查询所有角色
            colNames: ['id','角色编号', '角色名', '操作','机构编码'],
            colModel: [{
                name: 'id',
                index: 'id', 
                key: true, 
                hidden:true
            }, {
                name: 'roleCode',
                index: 'roleCode',
            }, {
                name: 'name',
                index: 'name'
            }, {
                name: 'Operation',
                index: 'Operation',
                rowtype: '#Operation'
            },
            {
                name: 'orgCode',
                index: 'orgCode',
                hidden:true
            }
            ],
            rowNum: 20,
            rowList:[20,50,100],
            altRows: true, //设置为交替行表格,默认为false
            rownumbers: true,
            ondblClickRow: function (e, c, a, b) {
                console.log('双击行事件', e, c, a, b);
            }, 
             onSelectRow: function (e, c, a, b) {
                console.log('单机行事件', e, c, a, b);
            }, 
            pager: '#grid-pager'
        });
        
        //查询数据，重置data
        $('#searchData').on('click', function () {
            searchData();
        });       
        
    });    
    
    function setPermission(obj){
       //var _this = $(obj);
       //var rowId = _this.closest('tr').attr('id');
       //console.log("-----setPermission--rowId="+rowId);
       var rowId=$(obj).parents('tr').attr('id');
       var selRow = $('#X_Table').XGrid('getRowData',rowId);
       //console.log("-----setPermission--selRow="+JSON.stringify(selRow));
       console.log("-----setPermission--rowId="+rowId+",orgCode="+selRow.orgCode);
       window.location.href="/proxy-sysmanage/sysmanage/role/roleSet?orgCode="+selRow.orgCode+"&roleId="+rowId;
    }
    
    //验证查询参数
    function searchCheck(){
        var result={passed:false};
        var name = $("#name").val();
        var startTime = $("#startTime").val();
        var endTime = $("#endTime").val();
        
        name=$.trim(name);

        var orgCodeVal = $("#orgList").children('option:selected').val();

        result.passed=true;
        //result.data={orgCode:orgCode,roleName:name,startTime:startTime,endTime:endTime};
        result.data={orgCode:orgCodeVal,roleName:name,startTime:startTime,endTime:endTime};
        return result;
     }
        
  