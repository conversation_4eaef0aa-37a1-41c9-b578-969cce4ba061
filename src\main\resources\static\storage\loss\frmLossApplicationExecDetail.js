/**
 * Created by 沙漠里的红裤头 on 2019/6/27
 */
/* 审批流 */
/**
 * 流程图显示
 */
    //根据流程实例ID加载流程图
var processInstaId=$("#processId").val();
initApprovalFlowChart(processInstaId);
function  initApprovalFlowChart(processInstaId) {
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: "/proxy-storage/storage/loss/getProcessListView?processInstanceId="+processInstaId,
        async: false,
        success: function (data) {
            console.log(data);
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}

// 时间格式化
function formatDateTime(inputTime) {
    if(inputTime == null || inputTime == "")
        return "";
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    var minute = date.getMinutes();
    var second = date.getSeconds();
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d;
    // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
}

$(function () {
    var colNames = ['商品编码','原商品编码','商品大类', '商品名称', '商品规格','产地', '生产厂家',
            '单位', '库房名称', '业务类型','批号', '生产日期', '有效期至','报损数量', '不含税成本单价','不含税成本金额'],
        colModel =   [
            {name: 'productCode',index: 'productCode'},
            {name: 'oldProductCode', index: 'oldProductCode'},
            {name: 'drugClass',index: 'drugClass'},
            {name: 'productName',index: 'productName'},
            {name: 'specifications',index: 'specifications'},
            {name:'producingArea', index:'producingArea' },
            {name: 'manufacturerName',index: 'manufacturerName'},
            {name: 'productUnit', index: 'productUnit'},
            {name: 'storeHouseName',index: 'storeHouseName'
                ,formatter:function () {
                    return "不合格库";
                }},
            { name: 'channelId',index: 'channelId'},
            { name: 'batch',index: 'batch'},
            {name: 'produceDate',index: 'produceDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }},
            {name: 'validDate',index: 'validDate',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                } },
            {name: 'lossNum',index: 'lossNum'},
            {name: 'noTaxPrice', index: 'noTaxPrice'},
            {name: 'noTaxCostNum',index: 'noTaxCostNum' },
        ];

    $('#X_Table').XGrid({
        url:'/proxy-storage/storage/loss/getLossRequestDetailExecList?lossOrderNo='+$("#lossOrderNo").val(),
        colNames: colNames,
        colModel:colModel,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        gridComplete: function () {
            /* 合计写入 */
            var data = $(this).XGrid('getRowData');
            data.forEach(function (item, index) {
                if (item.abnormalCause) {
                    $('#X_Table #' + item['id']).addClass('warnning')
                }
            });
        },
        pager: '#grid-pager',
    });
})