$(function () {

    //初始化移动类型
    //initMovementTypeSelect();
    //初始化仓库
    //initStorageTypeSelect();

    var sysOrgCode = $("#sysOrgCode").val();
    var totalTable = z_utils.totalTable;
    var totalTablea = z_utils.totalTablea;

    //设置日期默认值
    $("#startOrderDate,#startPostDate").val(getMonthStartDate());
    $("#endOrderDate,#endPostDate").val(getNowFormatDate());
    var param = $('#myform').serializeToJSON();
    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/initBalance/findOutAndInStorage',
        postData: param,
        colNames: ['库房', '单据号', '单据行号', '移动类型编码', '移动类型名称','采购订单号','销售订单号', '业务类型','单据日期',
            '过账日期', '商品大类编码', '商品大类名称', '商品编码', '商品名称', '数量',
            '金额（不含税）', '货币',  '供应商编码', '供应商名称',  '客户编码','客户名称','摘要'
        ],
        colModel: [{
            name: 'storageTypeName'
        }, {
            name: 'orderCode'
        }, {
            name: 'seqNum'
        }, {
            name: 'moveType'
        }, {
            name: 'movementTypeName'
        },{
            name: 'purchaseOrder'
        },{
            name: 'salesOrder'
        }, {
            name: 'channelName'
        }, {
            name: 'orderDate'
        }, {
            name: 'postDate'
        }, {
            name: 'drugClass'
        }, {
            name: 'largeCategoryName'
        }, {
            name: 'drugCode'
        }, {
            name: 'productName'
        },{
            name: 'quantity'
        },{
            name: 'notaxSum',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        },{
            name: 'currency'
        },{
            name: 'supplierCode'
        },{
            name: 'supplierName'
        },{
            name: 'customerCode'
        },{
            name: 'customerName'
        },{
        	name:'movementTypeName'
        }],
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        //shrinkToFit:false,  //设置为true时，列数充满表格，当列数较多时，只会缩小列宽，并不会出现滚动条，需要将属性设置为false
        //autoScroll: true,    //设置滚动条
        rownumbers: true,
        attachRow:true,//合计行
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
        	var data = $(this).XGrid("getRowData");
        	if(!data.length){
        		utils.dialog({
        			content:"查询无数据",
        			quickClose: true,
                    timeout: 2000
        		}).show();
        	}
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['quantity','notaxSum'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                if(item == 'quantity'){
                    lastRowEle.find("td[row-describedby="+item+"]").text(totalTablea(data,item));
                }else{

                    lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
                }
                lastRowEle.find("td[row-describedby="+item+"]").prop("title","");
            });
        },
        pager: '#grid-pager'
    });
    //统计方法
    totalSum();

    // 查询数据
    $('#searchBtn').bind('click', function () {

        var param = $('#myform').serializeToJSON();
        $('#X_Table').setGridParam({
            url: '/proxy-finance/finance/initBalance/findOutAndInStorage',
            postData: param
        }).trigger('reloadGrid');
        //统计方法
        totalSum();
    })

    // 导出
    $('#exportBtn').bind('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            parent.showLoading({hideTime: 999999999});
            var obj = {
                startOrderDate :$("#startOrderDate").val(),
                endOrderDate : $("#endOrderDate").val(),
                startPostDate : $("#startPostDate").val(),
                endPostDate :$("#endPostDate").val(),
                drugCode : $("#drugCode").val(),
                orderCode : $("#orderCode").val(),
                moveType :$("#moveType").val(),
                purchaseOrder : $("#purchaseOrder").val(),
                storageType : $("#storageType").val(),
                salesOrder : $("#salesOrder").val(),
                channelId : $("#channelId").val()
            };
            httpPost("/proxy-finance/finance/initBalance/exportExcelFinanceStockInitialBalance", obj);
            parent.hideLoading();
            //原始处理逻辑代码
            // parent.showLoading({hideTime: 999999999});
            // $.ajax({
            //     url : "/proxy-finance/finance/initBalance/exportExcelFinanceStockInitialBalance",
            //     data:{
            //         startOrderDate :$("#startOrderDate").val(),
            //         endOrderDate : $("#endOrderDate").val(),
            //         startPostDate : $("#startPostDate").val(),
            //         endPostDate :$("#endPostDate").val(),
            //         drugCode : $("#drugCode").val(),
            //         orderCode : $("#orderCode").val(),
            //         moveType :$("#moveType").val(),
            //         purchaseOrder : $("#purchaseOrder").val(),
            //         storageType : $("#storageType").val(),
            //         salesOrder : $("#salesOrder").val(),
            //         channelId : $("#channelId").val()
            //     },
            //     type: "post",
            //     success:function(result){
            //         //校验成功
            //         if(result.code==0){
            //             console.log(result.result);
            //             var url = result.result.filePath;
            //             var extfilename = result.result.extfilename;
            //             var parames = [];
            //             parames.push({ name: "filePath", value: url});
            //             parames.push({ name: "extfilename", value: extfilename});
            //             Post("/proxy-finance/finance/purchase/AccountPayableReports/downLoadExcel", parames);
            //             parent.hideLoading();
            //         }else{
            //             parent.hideLoading();
            //             utils.dialog({content:result.msg, quickClose: true,
            //                 timeout: 3000}).showModal();
            //         }
            //     }
            // })
        })
    });

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }


    // // 导出
    // $('#exportBtn').bind('click', function () {
    //   console.log($("#startOrderDate").val());
    //   var parames = [];
	 //  parames.push({ name: "startOrderDate", value: $("#startOrderDate").val()});
    //   parames.push({ name: "endOrderDate", value: $("#endOrderDate").val()});
    //   parames.push({ name: "startPostDate", value: $("#startPostDate").val()});
    //   parames.push({ name: "endPostDate", value: $("#endPostDate").val()});
    //   parames.push({ name: "drugCode", value: $("#drugCode").val()});
    //   parames.push({ name: "orderCode", value: $("#orderCode").val()});
    //   parames.push({ name: "moveType", value: $("#moveType").val()});
    //   parames.push({ name: "purchaseOrder", value: $("#purchaseOrder").val()});
    //   parames.push({ name: "storageType", value: $("#storageType").val()});
    //   parames.push({ name: "salesOrder", value: $("#salesOrder").val()});
		// Post("/proxy-finance/finance/initBalance/exportExcelFinanceStockInitialBalance", parames);
    // });

    function Post(URL, PARAMTERS) {
        //创建form表单
        var temp_form = document.createElement("form");
        temp_form.action = URL;
        //如需打开新窗口，form的target属性要设置为'_blank'
        temp_form.target = "_self";
        temp_form.method = "post";
        temp_form.style.display = "none";
        //添加参数
        for (var item in PARAMTERS) {
            var opt = document.createElement("textarea");
            opt.name = PARAMTERS[item].name;
            opt.value = PARAMTERS[item].value;
            temp_form.appendChild(opt);
        }
        document.body.appendChild(temp_form);
        //提交数据
        temp_form.submit();
    }




    //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    });

	//商品名称 双击查询
    $('#productDesc').dblclick(function () {
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#productDesc').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#productDesc').val(data.productName);
                    $('#drugCode').val(data.productCode)
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    });


        //商品名称 搜索
        $('#productDesc').Autocomplete({
            serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+sysOrgCode, //异步请求
            paramName: 'param',//查询参数，默认
            dataType: 'json',
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            transformResult: function (response) {
                return {
                    suggestions: $.map(response.result.list, function (dataItem) {
                        return {value: dataItem.productName, data: dataItem.productCode};
                    })
                };
            },
            triggerSelectOnValidInput: false, // 必选
            // multi: true, //多选要和delimiter一起使用
            // delimiter: ',',
            // showNoSuggestionNotice: true, //显示查无结果的container
            // noSuggestionNotice: '查询无结果',//查无结果的提示语
            // tabDisabled: true,
            onSelect: function (result) {
                //选中回调
                $("#drugCode").val(result.data);

            },
            onSearchStart: function (params) {
                // console.log('检索开始回调', params)
            },
            onSearchComplete: function (query, suggestions) {
                //匹配结果后回调
               // console.log(query, suggestions);

            },
            onSearchError: function (query, jqXHR, textStatus, errorThrown) {
                //查询失败回调
                console.log(query, jqXHR, textStatus, errorThrown)
            },
            onHide: function (container) {
                // console.log('container隐藏前回调', container)
            },
            onNoneSelect: function (params, suggestions) {
                $("#drugCode").val("");
                $("#productDesc").val("");
            }
        });


    //统计方法
    function totalSum() {
        var storageTypeLists = $("#storageTypeLists").val();
        if(!$.isArray (storageTypeLists) && storageTypeLists != undefined){
            var tem = [];
            tem.push(storageTypeLists);
            storageTypeLists = tem;
        }
        storageTypeLists = JSON.stringify(storageTypeLists);
        console.log(storageTypeLists);
        var productCode = $("#drugCode").val();
        var oldProductCode = $("#oldProductCode").val();

        $.ajax({
            url:'/proxy-finance/finance/initBalance/totalSumOutAndInStorage',
            type:'post',
            data:{
                startOrderDate :$("#startOrderDate").val(),
                endOrderDate : $("#endOrderDate").val(),
                startPostDate : $("#startPostDate").val(),
                endPostDate :$("#endPostDate").val(),
                drugCode : $("#drugCode").val(),
                orderCode : $("#orderCode").val(),
                moveType :$("#moveType").val(),
                purchaseOrder : $("#purchaseOrder").val(),
                storageType : $("#storageType").val(),
                salesOrder : $("#salesOrder").val(),
                channelId : $("#channelId").val()
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;
                    var totalAmount = data.totalAmount;
                    var totalNotaxSum = data.totalNotaxSum;
                    if(totalAmount){
                        $("#totalAmount").text(totalAmount);
                    }else {
                        $("#totalAmount").text(0);
                    }
                    if(totalNotaxSum){
                        $("#totalNotaxSum").text(parseFloat(totalNotaxSum).formatMoney('2', '', ',', '.'));
                    }else {
                        $("#totalNotaxSum").text(0);
                    }
                }
            }
        })
    }

    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('1').then( res => {
            console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });

})


/**
 * 渲染移动类型下拉列表
 */
function initMovementTypeSelectTemplate(data) {
    var html = template('movementType-tmp', {list: data});
    document.getElementById('movementType-div').innerHTML = html;
}

/**
 * 获取移动类型
 */
function initMovementTypeSelect() {
    $.post("/proxy-finance/finance/initBalance/findMovementTypeList", { },
        function(data){
            if (data.code == 0) {
                var _data = data.result;
                initMovementTypeSelectTemplate(_data);
            }
        }, "json");
}


/**
 * 渲染仓库下拉列表
 */
function initStorageTypeSelectTemplate(data) {
    var html = template('storageType-tmp', {list: data});
    document.getElementById('storageType-div').innerHTML = html;
}

/**
 * 获取仓库
 */
function initStorageTypeSelect() {
    $.post("/proxy-sysmanage/sysmanage/dict/querycommonnotpage", {type: 10},
        function(data){
            if (data.code == 0) {
                var _data = data.result;
                initStorageTypeSelectTemplate(_data);
            }
        }, "json");
}

/**
 * 单据日期 开始
 */
function startOrderDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        maxDate:'#F{$dp.$D(\'endOrderDate\')}'
    });
    $("#endOrderDate").val("");
}

/**
 * 单据日期 结束
 */
function endOrderDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        minDate:'#F{$dp.$D(\'startOrderDate\')}',
        maxDate: getMaxDate('#startOrderDate')
    });
    //$("#startOrderDate").val("");
}

function getMaxDate(id){
	 return $(id).val().split('-')[0]+'-12-31';
}
/**
 * 记账日期 开始
 */
function startPostDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        maxDate:'#F{$dp.$D(\'endPostDate\')}'
    });
    $("#endPostDate").val("");
}

/**
 * 记账日期 结束
 */
function endPostDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        minDate:'#F{$dp.$D(\'startPostDate\')}',
        maxDate: getMaxDate('#startPostDate')
    });
   // $("#startPostDate").val("");
}

/**
 * 获取当前时间，格式YYYY-MM-DD
 * @returns {string}
 */
function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}

//获得本月的开始日期
function getMonthStartDate(){
    var now = new Date();                    //当前日期
    var nowDayOfWeek = now.getDay();         //今天本周的第几天
    var nowDay = now.getDate();              //当前日
    var nowMonth = now.getMonth();           //当前月
    var nowYear = now.getYear();             //当前年
    nowYear += (nowYear < 2000) ? 1900 : 0;  //
    var monthStartDate = new Date(nowYear, nowMonth, 1);
    return formatDate(monthStartDate);
}

//格式化日期：yyyy-MM-dd
function formatDate(date) {
    var myyear = date.getFullYear();
    var mymonth = date.getMonth() + 1;
    var myweekday = date.getDate();

    if (mymonth < 10) {
        mymonth = "0" + mymonth;
    }
    if (myweekday < 10) {
        myweekday = "0" + myweekday;
    }
    return (myyear + "-" + mymonth + "-" + myweekday);
}
