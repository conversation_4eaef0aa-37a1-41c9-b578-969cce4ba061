var getData;
$(function () {
    // 模拟数据
    /* 日期格式化 */
    //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
    function dateFormat(time, format) {
        var t = new Date(parseInt(time,10));
        var tf = function (i) {
            return (i < 10 ? '0' : '') + i;
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        });
    }

    /* 获取数据 */
    getData = function (printType,outOrderCode) {
        var outOrderCodes = outOrderCode.join(',');
        var urlpath="/proxy-order/order/orderOut/orderOut/findZlOutOrderPrintList";
        if(printType==2){
            urlpath="/proxy-order/order/orderOut/orderOut/findZlEmptyOutOrderPrintList";
        }
        $.ajax({
            url:urlpath,
            data:{
                outOrderCodes:outOrderCodes,
                printType: printType
            },
            success:function(res){
                let resultList = res.result;  //真实数据
                // let resultList = resss.result; // 使用模拟数据
                if(resultList && $.isArray(resultList.printList)){
                    if($.isArray(resultList.priceOutOrderVoList)){
                        webRender(resultList.printList, printType, resultList.zlprintList,resultList.priceOutOrderVoList);
                    }else{
                        webRender(resultList.printList, printType, resultList.zlprintList);
                    }
                }else {
                    utils.dialog({
                        title:'提示',
                        content:"数据为空或格式不正确",
                        okValue:'确定',
                        ok:function () {}
                    }).showModal();
                }
            },
        })
    };

    function orderCodeFormat(str) {
        if (str.length>4){
            var front=str.substr(0,str.length-4);
            var after=str.substr(str.length-4);
            return '<i class="val" style="font-size: 16px;">'+front+'</i><i class="val" style="font-size: 18px;">'+after+'</i>';
        }
        return '';
    }
    /* 数据渲染 */
    function webRender(data,printType, zlprintList,temList) {
        var box_html = '';
        /* 基本结构拼装 */
        var number = 0;
        data.forEach(function (item,index) {
            /* 销售出库复核单 */
            box_html +=`
                   <div class="content indent1" style="font-family:KaiTi">
                    <div class="header">
                        <!--双行-->
                        <div class="title">${item.orgName}</div>
                        <div class="title" style="font-size: 23px;">${item.printTemplateType && item.printTemplateType == '4'?'(赠品)销售出库复核单(随货同行)':'销售出库复核单(随货同行)'}</div>
                    </div>
                    <div class="top">
                        <ul class="info_list" style="position: relative;">
                            <!--<li style="position: absolute;width: 24%;left: 76%;">
                                <span class="col_2_5" style="margin-left:-26px;"> ${item.supervisionCode!=""?'流通监管码：':''}</span>
                            </li>-->
                            <li>
                                <span class="col_10">客户编号：<i class="val">${item.customerCode ? item.customerCode : ""}</i></span>
                                <!--<span class="col_2" style="margin-left:-26px;overflow: initial;"> 药品属于特殊商品，非质量问题一律不予退货！</span>-->
                            </li>
                            <li>
                                <span class="col_10">客户名称：<i class="val">${item.customerName ? item.customerName : ""}</i></span>
                                <!--<span class="col_2_4" style="margin-left: -16px;"><i class="val"  style="font-size: 14px;">${item.supervisionCode?item.supervisionCode:''}</i></span>-->  
                                <!--<span class="col_2_4" style="margin-left:-26px;"> 售后服务：<i class="val">400-0505-111</i></span>-->
                            </li>
                            <li>
                                <span class="col_7_8">客户电话：<i class="val">${item.receiverPhone ? item.receiverPhone : ""}</i></span>
                                <span class="col_2_3" style="margin-left:-90px;">单据编号：<i class="val" style="font-size: 16px;">${item.salesOrderCode ? orderCodeFormat(item.salesOrderCode) : ""}</i></span>
                            </li>
                            <li>
                                <span class="col_4_8">收货地址：<i class="val">${item.address?item.address:""}</i></span>
                                <span class="col_1_5" style="margin-left:-37px;">开票日期：<i class="val">${item.slaseOrderCreateTime ? dateFormat(item.slaseOrderCreateTime, 'yyyy-MM-dd') : ""}</i></span>
                                <span class="col_1_5" style="margin-left:-27px;">发货日期：<i class="val">${item.createTime ? dateFormat(item.createTime, 'yyyy-MM-dd') : ""}</i></span>
                                <span class="col_2_3" style="margin-left:-26px;">订单编号：${item.ecOrderCode ? item.ecOrderCode : ""}</span>
                            </li>
                        </ul>
                        <span class="inden_type">销</span>
                        <div style="position: absolute;right: 0;bottom: 43px">
                            ${item.manualOrderScope == 1 ? '<span style="line-height:35px;top:-10px;right:110px;bottom:33px;font-size: 25px;border:1px solid black;color:black;width:70px;height: 35px;">二精</span>':
                            item.manualOrderScope == 2 ? '<span style="line-height:35px;top:-10px;right:110px;bottom:33px;font-size: 25px;border:1px solid black;color:black;width:70px;height: 35px;">蛋肽</span>' : ''}
                            ${item.ephedrineType == 1 ? '<span style="line-height:35px;top:-10px;right:110px;bottom:33px;font-size: 25px;border:1px solid black;color:black;width:70px;height: 35px;">含特</span>':''}
                            ${item.coldType&&item.coldType=='1'? '<span class="inden_tag2">冷</span>':''} 
                            ${item.firstOrderFlag==1?'<span style="top:-10px;right:80px;bottom:33px;font-size: 30px;">※</span> ':''} 
                            ${item.reprint&&item.outType!=2?'<span style="position: initial;right: 0;bottom: 43px;width: 70px;height: 35px;line-height: 35px; text-align: center;border: 1px solid #000000;border-radius: 1px;font-size: 25px;font-weight: bold;">重打</span> ':''}
                        </div>
                     </div>
                    <table id="table_a_${index}"></table>
                    <div class="bottom">
                        <ul class="info_list">
                            <li>
                                <span class="col_10">发货地址：<i class="val">${item.shipAddress?item.shipAddress:""}</i></span>
                                
                            </li>
                            <li>
                                <!--<span class="col_4"><span class="col_5">销售电话：0731-********</span>-->
                                <span class="col_2">售后电话：400-0505-111</span></span>
                                <span class="col_2">复核员：<i class="val">${item.officeAccountant?item.officeAccountant:""}</i></span>
                                <span class="col_2">发货人：</span>
                                <span class="col_1">共<i class="val">${item.pageTotal}</i>页，第<i class="val">${item.pageNumber}</i>页</span>
                                <span class="col_2">白联：公司留存 红/黄联：随货同行</span>
                            </li>
                                <br/>
                            <li>
                                <span class="col_4">${item.printTemplateType && item.printTemplateType == '1'?'':'药品属于特殊商品，非质量问题一律不予退货！'}</span>
                            </li>
                        </ul>
                    </div>
                    <div style="page-break-after:always"></div>
                </div>
                `;
            /* 销售汇总单 */
            if(item.pageNumber === item.pageTotal){
                box_html +=`
                        <div class="content indent2" style="margin-bottom: 25px; font-family:KaiTi">
                        <div class="header">
                            <!--双行-->
                            <div class="title">${item.orgName}</div>
                            <div class="title" style="font-size: 23px;">${item.printTemplateType && item.printTemplateType == '4'?'(赠品)销售汇总单(货物交接单)':'销售汇总单(货物交接单)'}</div>
                            <!--二维码-->
                            <div class="qrcode" id="qrcode${index}" style="width: 96px;height: 96px;position: absolute;top: 15px;left: 815px; z-index: 99;">
                            <!--<div class="qrcode" id="qrcode" style="width: 110px;height: 110px;position: absolute;top: 4px;left: 825px; z-index: 99;">-->
                                <!--<img src=${item.qrcodeUrl} style="width: 100%; height: 100%;" />-->
                            </div>
                        </div>
                        <div class="top">
                            <ul class="info_list">
                                 
                                <li>
                                    <span class="col_8">客户编号：${item.customerCode ? item.customerCode : ""}</span>
                                    <span class="col_2" style="margin-left:-26px;"></span>
                                </li>
                                <li>
                                    <span class="col_10">客户名称：${item.customerName ? item.customerName : ""}</span>
                                </li>
                                <li>
                                    <span class="col_8">客户电话：<i class="val">${item.receiverPhone ? item.receiverPhone : ""}</i></span>
                                    <span class="col_2" style="margin-left:-26px;">${item.printTemplateType && item.printTemplateType == '1'?'':'如含有特殊药品，概不接受现金结算'}</span>
                                </li>
                                <li>
                                    <span class="col_4_8">收货地址：${item.address ? item.address : ""}</span>
                                    <span class="col_3_2">发货日期：${item.createTime ? dateFormat(item.createTime, 'yyyy-MM-dd') : ""}</span>
                                    <span class="col_2" style="margin-left:-26px;">车牌号码：</span>
                                </li>
                            </ul>
                        </div>
                        <table id="table_b_${index}"></table>
                        <table id="table_c_${index}" class="table_c"></table>
                        <div class="bottom">
                            <ul class="info_list">
                                <li>
                                    <span class="col_3_8">${item.printTemplateType && item.printTemplateType == '1'?'':'药品属于特殊商品，非质量问题一律不予退货！'}</span>
                                    <span class="col_2">运输员：</span>
                                    <span class="col_2">客户签名：</span>
                                    <span class="col_2">送达时间：</span>
                                </li>
                                <!--<li>
                                    <span class="col_4">${item.pcs ? item.pcs : "整件数：0 — 拼箱数：0 — 总箱数：0"}</span>
                                    <span class="col_4">药品属于特殊商品，非质量问题一律不予退货！</span>
                                </li>-->
                            </ul>
                        </div>
                        <div style="page-break-after:always"></div>
                    </div>
                    `;
                /* 委托配送单 */
                //当前单子item共打印到第几张了
                number = number + item.pageTotal;
                console.log(index+":number======"+number);
                //detailTotal代表同一个出库单的随货同行单张数
                if(temList&&temList.length>0){
                    if (item.detailTotal === number && temList && temList.length && temList.length > 0) {
                        number = 0;
                        let resultList = [];
                        temList.forEach(function (ress, k) {
                            if (item.outOrderCode === ress.outOrderCode) {
                                ress.adds = k;
                                resultList.push(ress)
                            }
                        });
                        console.log(1, resultList);
                        if (resultList && resultList.length > 0) {
                            resultList.forEach(function (res_item, res_index) {
                                box_html += `
                           <div class="content indent1" style="font-family:KaiTi">
                            <div class="header">
                                <!--双行-->
                                <div class="title">${res_item.delegationDeliveryCompany ? res_item.delegationDeliveryCompany : ""}委托配送单</div>
                            </div>
                            <div class="top">
                                <ul class="info_list" style="padding-top: 35px;margin-top: -17px;">
                                    <li>
                                        <span class="col_3_2">发货日期：<i class="val">${res_item.createTime ? dateFormat(res_item.createTime, 'yyyy-MM-dd') : ""}</i></span>
                                        <span class="col_3_2">单据编号：<i class="val">${res_item.salesOrderCode ? res_item.salesOrderCode : ""}</i></span>
                                        <span class="col_3_2">收货人：<i class="val">${res_item.receiverName ? res_item.receiverName : ""}</i></span>
                                    </li>
                                    <li>
                                        <span class="col_3_2">承运单位：<i class="val">${res_item.orgName}</i></span>
                                        <span class="col_3_2">收货单位：<i class="val">${res_item.storeName ? res_item.storeName : ""}</i></span>
                                        <span class="col_3_2" style="height: 34px;">收货地址：<i class="val" style="height: 34px; line-height: 17px; width: 320px;word-wrap: break-word;white-space: initial;">${res_item.ecZlAddress ? res_item.ecZlAddress : ""}</i></span>
                                    </li>
                                </ul>
                            </div>
                            `;
                                if (res_item.isInstrument == 1) {
                                    box_html += `
                                <table id="table_e_${res_item.adds}"></table>`;
                                } else {
                                    box_html += `
                                <table id="table_d_${res_item.adds}"></table>`;
                                }
                                box_html += `
                            <div class="bottom">
                                <ul class="info_list">
                                    <li>
                                       
                                        <span class="col_10">发货地址：<i class="val">${res_item.orgCode == "002" ? "武汉市东西湖区走马岭食品三路1#厂房一层、二层" : res_item.address}</i></span>
                                    </li>
                                    <li>
                                        <!--<span class="col_4"><span class="col_5">销售电话：0731-********</span>-->
                                        <!--  <span class="col_5">售后电话：400-0505-111</span></span>-->
                                        <span class="col_1" style="margin-left: 600px;">复核员：<i class="val">${res_item.officeAccountant ? res_item.officeAccountant : ""}</i></span>
                                        <span class="col_1">共<i class="val">${res_item.pageTotal}</i>页，第<i class="val">${res_item.pageNumber}</i>页</span>
                                        <span class="col_3">白联：财务 红联：门店 黄联：仓库</span>
                                    </li>
                                        <br/>
                                    <!-- <li>
                                        <span class="col_4">${res_item.printTemplateType && res_item.printTemplateType == '1' ? '' : '药品属于特殊商品，非质量问题一律不予退货！'}</span> -->
                                    </li>
                                </ul>
                            </div>
                            <div style="page-break-after:always"></div>
                        </div>
                        `;
                            })
                        }
                    }
                }else {
                    if (item.detailTotal === number && zlprintList && zlprintList.length && zlprintList.length > 0) {
                        number = 0;
                        let resultList = [];
                        zlprintList.forEach(function (ress, k) {
                            if (item.outOrderCode === ress.outOrderCode) {
                                ress.adds = k;
                                resultList.push(ress)
                            }
                        });
                        if (resultList && resultList.length > 0) {
                            resultList.forEach(function (res_item, res_index) {
                                box_html += `
                           <div class="content indent1" style="font-family:KaiTi">
                            <div class="header">
                                <!--双行-->
                                <div class="title">${res_item.delegationDeliveryCompany ? res_item.delegationDeliveryCompany : ""}委托配送单</div>
                            </div>
                            <div class="top">
                                <ul class="info_list" style="padding-top: 35px;margin-top: -17px;">
                                    <li>
                                        <span class="col_3_2">发货日期：<i class="val">${res_item.createTime ? dateFormat(res_item.createTime, 'yyyy-MM-dd') : ""}</i></span>
                                        <span class="col_3_2">单据编号：<i class="val">${res_item.salesOrderCode ? res_item.salesOrderCode : ""}</i></span>
                                        <span class="col_3_2">收货人：<i class="val">${res_item.receiverName ? res_item.receiverName : ""}</i></span>
                                    </li>
                                    <li>
                                        <span class="col_3_2">承运单位：<i class="val">${res_item.orgName}</i></span>
                                        <span class="col_3_2">收货单位：<i class="val">${res_item.storeName ? res_item.storeName : ""}</i></span>
                                        <span class="col_3_2" style="height: 34px;">收货地址：<i class="val" style="height: 34px; line-height: 17px; width: 320px;word-wrap: break-word;white-space: initial;">${res_item.ecZlAddress ? res_item.ecZlAddress : ""}</i></span>
                                    </li>
                                </ul>
                            </div>
                            `;
                                if (res_item.isInstrument == 1) {
                                    box_html += `
                                <table id="table_e_${res_item.adds}"></table>`;
                                } else {
                                    box_html += `
                                <table id="table_d_${res_item.adds}"></table>`;
                                }
                                box_html += `
                            <div class="bottom">
                                <ul class="info_list">
                                    <li>
                                       
                                        <span class="col_10">发货地址：<i class="val">${res_item.orgCode == "002" ? "武汉市东西湖区走马岭食品三路1#厂房一层、二层" : res_item.address}</i></span>
                                    </li>
                                    <li>
                                        <!--<span class="col_4"><span class="col_5">销售电话：0731-********</span>-->
                                        <!--  <span class="col_5">售后电话：400-0505-111</span></span>-->
                                        <span class="col_1" style="margin-left: 600px;">复核员：<i class="val">${res_item.officeAccountant ? res_item.officeAccountant : ""}</i></span>
                                        <span class="col_1">共<i class="val">${res_item.pageTotal}</i>页，第<i class="val">${res_item.pageNumber}</i>页</span>
                                        <span class="col_3">白联：财务 红联：门店 黄联：仓库</span>
                                    </li>
                                        <br/>
                                    <!-- <li>
                                        <span class="col_4">${res_item.printTemplateType && res_item.printTemplateType == '1' ? '' : '药品属于特殊商品，非质量问题一律不予退货！'}</span> -->
                                    </li>
                                </ul>
                            </div>
                            <div style="page-break-after:always"></div>
                        </div>
                        `;
                            })
                        }
                    }
                }
            }
        });

        $("#box").html(box_html);

        /* 表格初始化 */
        data.forEach(function (item,index) {
            item.detailVoList = item.detailVoList.map(function (val,key) {
                delete val.id;
                if(val.productCode){
                    val.sort = key + 1;
                }
                return val
            });

            var merge=6;
            var style=7;
            let priceStr = ''
            if (item.orderType) {
                priceStr = item.orderType == '1' ? '单价</br>(折后单价)' : '单价'
            } else {
                priceStr = '单价</br>(折后单价)'
            }
            // const priceStr = item.orderType == '1' ? '单价</br>(折后单价)' : '单价'

            const taxPrice = item.orderType == '1' ? {
                index: 'realPayPrice',
                name: 'realPayPrice',
                formatter: function (val, rowType, rowData) {
                    var taxPrice = rowData.productTaxPrice && !isNaN(rowData.productTaxPrice) ? parseFloat(rowData.productTaxPrice).toFixed(2) : '' + '</p>';
                    var realPrice = rowData.realPayPrice &&  !isNaN(rowData.realPayPrice) ? '('+parseFloat(rowData.realPayPrice).toFixed(2)+')' : '' + '</p>';
                    return  taxPrice+'</br>'+realPrice
                },
                width: 80,
                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                    if (rowId == style) {
                        return 'style="display:none"'
                    }
                }
            } : {
                index: 'productTaxPrice',
                    name: 'productTaxPrice',
                    formatter: function (e) {
                    return e && !isNaN(e) ? parseFloat(e).toFixed(2) : ''
                },
                width: 80,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                    if (rowId == style) {
                        return 'style="display:none"'
                    }
                }
            }

            function ShipmentOrderTemplate() {
                //药品类 智鹿单bool
                if (item.storeOrder) {
                    $("#table_a_" + index).jqGrid({
                        data: item.detailVoList,
                        datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                        //width: "1322.83",
                        colNames: ['序号', '商品编号</br>通用名称</br>（商品名/品名）', '规格', '单位', '生产厂商', '产地', '数量', priceStr, '销售金额', '优惠金额', '实付金额', '批号', '生产日期/<br />有效期至',
                            '批准文号', '上市许可<br/>持有人', '剂型', '包装<br />数量', '件数', '质量<br />状况'],
                        colModel: [{
                            index: 'sort',
                            name: 'sort',
                            width: 30,
                        }, {
                            index: 'productCode',
                            name: 'productCode',
                            width: 110,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var code = rowData.productCode == null ? '' : '<p>' + rowData.productCode + '</p>';
                                    var prdName = rowData.productName == null ? '' : '<p>' + rowData.productName + '</p>';
                                    return code + prdName;
                                } else {
                                    return val;
                                }
                            }
                        }, {
                            index: 'productSpec',
                            name: 'productSpec',
                            width: 80,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'colspan=4'
                                } else if (rowId == style) {
                                    //金额合计(大写) value
                                    return 'colspan=3 style="text-align: left;padding-left:5px;"'
                                }
                            },
                            formatter: function (val, rowType, rowData) {
                                if (val){
                                    if (val.length > 10){
                                        return `<p>${val.substring(0,9)}</p><p>${val.substring(9)}</p>`
                                    } else{
                                        return val
                                    }
                                } else{
                                    return ''
                                }
                            }
                        }, {
                            index: 'producingPackingUnit',
                            name: 'producingPackingUnit',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'productmManufacturer',
                            name: 'productmManufacturer',
                            width: 110,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'producingArea',
                            name: 'producingArea',
                            width: 55,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 7 || rowId == 8) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'outStoreNumber',
                            name: 'outStoreNumber',
                            width: 45,
                            summaryType: function (value, name, record) {
                                return value
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, taxPrice, {
                            index: 'taxAmount',
                            name: 'taxAmount',
                            formatter: function (e, a, b) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : ''
                            },
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'activityPreferentialAmount',
                            name: 'activityPreferentialAmount',
                            formatter: function (e, a, b) {
                                if(b.isKangPuYP === '1'){
                                    return '(---)';
                                }
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : ''
                            },
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayAmount',
                            name: 'realPayAmount',
                            formatter: function (e, a, b) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : ''
                            },
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'batchCode', // 批号
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'colspan=7 style="text-align: left;padding-left:5px;"'
                                }
                                return 'style="word-wrap:break-word"'
                            }
                        }
                        , {
                            index: 'manufactureTime',
                            name: 'manufactureTime',
                            width: 95,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var mTime = rowData.manufactureTime == null ? '' : dateFormat(rowData.manufactureTime, 'yyyy-MM-dd') + '/';
                                    var eTime = rowData.expiryTime == null ? '' : rowData.expiryTime;
                                    return mTime + '</br>' + eTime;
                                } else {
                                    return val == null ? '' : val;
                                }
                            }
                        }
                            // ,{
                            //     index: 'expiryTimeActual',
                            //     name: 'expiryTimeActual',
                            //     width: 85  ,
                            //     cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            //         if (rowId == 8) {
                            //             return 'colspan=4 style="word-wrap:break-word;text-align: left;padding-left:5px;"'
                            //         }
                            //     }
                            // }
                        , {
                            index: 'approvalNumber',
                            name: 'approvalNumber',
                            width: 120,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    //金额合计 value
                                    return 'colspan=5 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'marketAuthor',
                            name: 'marketAuthor',
                            width: 60
                        }, {
                            index: 'dosageForm',
                            name: 'dosageForm',
                            width: 55
                        }, {
                            index: 'piecePackageNumber',
                            name: 'piecePackageNumber',
                            width: 40,
                        }, {
                            index: 'pieceNum',
                            name: 'pieceNum',
                            width: 40,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    //金额合计 value
                                    return 'colspan=5 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'quality',
                            name: 'quality',
                            width: 35,
                            formatter: function (val, rowType, rowData) {
                                if (rowData.productCode && rowData.productCode != "小计" && rowData.productCode != "金额合计(大写)：") {
                                    return "合格"
                                } else {
                                    return ""
                                }
                            }
                        }],
                        shrinkToFit: false,
                        rowNum: merge,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_taxAmount = $(this).getCol('taxAmount', false, 'sum');
                            var sum_activityPreferentialAmount = $(this).getCol('activityPreferentialAmount', false, 'sum');
                            var sum_realPayAmount = $(this).getCol('realPayAmount', false, 'sum');
                            var data = $(this).getRowData();
                            if (data.length < merge-1) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == merge-1) {
                                $(this).addRowData(merge, {
                                    productCode: "小计",
                                    outStoreNumber: sum_number,
                                    taxAmount: sum_taxAmount.toFixed(2),
                                    activityPreferentialAmount: sum_activityPreferentialAmount.toFixed(2),
                                    realPayAmount: sum_realPayAmount.toFixed(2),
                                }, "last");
                            } else if (data.length == merge) {
                                $(this).addRowData(style, {
                                    productCode: "金额合计(大写)：",
                                    productSpec: item.taxAmountDesc,
                                    manufactureTime: "金额合计：",
                                    approvalNumber: "￥：" + item.taxAmount,
                                }, "last");
                            }
                            //$(this).footerData("set",{"productCode":"合计","number":sum_number,"sum":sum_sum});
                        }
                    });
                } else {
                    $("#table_a_" + index).jqGrid({
                        data: item.detailVoList,
                        datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                        //width: "1322.83",
                        colNames: ['序号', '商品编号<br>通用名称</br>（商品名/品名）', '规格', '单位', '生产厂商', '产地', '数量', priceStr, '金额', '销售金额</br>(优惠金额)', '实付金额', '批号',
                            '生产日期/<br />有效期至', '批准文号', '上市许可<br/>持有人', '剂型', '包装<br />数量', '件数', '质量<br />状况'],
                        colModel: [{
                            index: 'sort',
                            name: 'sort',
                            width: 30,
                        }, {
                            index: 'productCode',
                            name: 'productCode',
                            width: 120,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var code = rowData.productCode == null ? '' : '<p>' + rowData.productCode + '</p>';
                                    var prdName = rowData.productName == null ? '' : '<p>' + rowData.productName + '</p>';
                                    return code + prdName;
                                } else {
                                    return val;
                                }
                            },
                        }, {
                            index: 'productSpec',
                            name: 'productSpec',
                            width: 90,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                //console.log(rowId, tv, rawObject, cm, rdata);
                                if (rowId == merge) {
                                    return 'colspan=4'
                                } else if (rowId == style) {
                                    //金额合计(大写) value
                                    return 'colspan=8 style="text-align: left;padding-left:5px;"'
                                }
                            },
                            formatter: function (val, rowType, rowData) {
                                if (val){
                                    if (val.length > 10){
                                        return `<p>${val.substring(0,9)}</p><p>${val.substring(9)}</p>`
                                    } else{
                                        return val
                                    }
                                } else{
                                    return ''
                                }
                            }
                        }, {
                            index: 'producingPackingUnit',
                            name: 'producingPackingUnit',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'productmManufacturer',
                            name: 'productmManufacturer',
                            width: 120,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'producingArea',
                            name: 'producingArea',
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'outStoreNumber',
                            name: 'outStoreNumber',
                            width: 50,
                            summaryType: function (value, name, record) {
                                return value
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, taxPrice, {
                            index: 'taxAmount',
                            name: 'taxAmount',
                            hidden: true,
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : ''
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'taxAmountAndPreferentialAmount',
                            name: 'taxAmountAndPreferentialAmount',
                            formatter: function (val, rowType, rowData) {
                                var preferentialAmount;
                                if(rowData.isKangPuYP === '1'){
                                    preferentialAmount = '(---)';
                                }else{
                                    preferentialAmount = rowData.activityPreferentialAmount && !isNaN(rowData.activityPreferentialAmount) ? '<p>(' + parseFloat(rowData.activityPreferentialAmount).toFixed(2) + ')</p>' : '';
                                }
                                var taxAmount = rowData.taxAmount && !isNaN(rowData.taxAmount) ? '<p>' + parseFloat(rowData.taxAmount).toFixed(2) + '</p>' : '';
                                return taxAmount + preferentialAmount;
                            },
                            width: 85,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayAmount',
                            name: 'realPayAmount',
                            width: 75,
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '';
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'batchCode',
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'colspan=7 style="text-align: left;padding-left:5px;"'
                                }
                                return 'style="word-wrap:break-word"'
                            }, formatter: function (val, rowType, rowData) {
                                if (val){
                                    return '<span style="font-size: 16px">'+val+ '</span>'
                                }else {
                                    return "";
                                }
                            }
                        }

                         ,{
                                index: 'expiryTimeActual',
                                name: 'expiryTimeActual',
                                width: 85
                            }
                        , {
                            index: 'approvalNumber',
                            name: 'approvalNumber',
                            width: 110,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    //金额合计 value
                                    return 'colspan=6 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'marketAuthor',
                            name: 'marketAuthor',
                            width: 60
                        }, {
                            index: 'dosageForm',
                            name: 'dosageForm',
                            width: 55
                        }, {
                            index: 'piecePackageNumber',
                            name: 'piecePackageNumber',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'pieceNum',
                            name: 'pieceNum',
                            width: 40,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    //金额合计 value
                                    return 'colspan=5 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'quality',
                            name: 'quality',
                            width: 35,
                            formatter: function (val, rowType, rowData) {
                                if (rowData.productCode && rowData.productCode != "小计" && rowData.productCode != "金额合计(大写)：") {
                                    return "合格"
                                } else {
                                    return ""
                                }
                            }
                        },/* {
                         index: 'status',
                         name: 'status',
                         hidden: true,
                         hidegrid: true
                         },*/],
                        shrinkToFit: false,
                        rowNum: merge,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_sum = $(this).getCol('taxAmount', false, 'sum');
                            var data = $(this).getRowData();
                            if (data.length < merge-1) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == merge-1) {
                                $(this).addRowData(merge, {
                                    productCode: "小计",
                                    outStoreNumber: sum_number,
                                    taxAmount: sum_sum.toFixed(2),
                                }, "last");
                            } else if (data.length == merge) {
                                $(this).addRowData(style, {
                                    productCode: "金额合计(大写)：",
                                    productSpec: item.taxAmountDesc,
                                    expiryTimeActual: "金额合计：",
                                    approvalNumber: "￥：" + item.taxAmount,
                                }, "last");
                            }
                            //$(this).footerData("set",{"productCode":"合计","number":sum_number,"sum":sum_sum});
                        }
                    });
                }

                /* 销售汇总单 */
                var data_b = [{
                    ecOrderCode: item.ecOrderCode,
                    salesOrderCode: item.salesOrderCode,
                    expressFee: item.expressFee,
                    taxAmount: item.taxAmount,
                    activityPreferentialAmount: item.activityPreferentialAmount,
                    totalRealPayAmount: item.totalRealPayAmount,
                    totalRealPayAmountDesc: item.totalRealPayAmountDesc,
                    isOnlinePay: item.payType ? "已支付" : "未支付",
                    payType: item.payType ? item.payType : ""
                }, {
                    expressFee: "整件数：",
                    taxAmount: item.fullPcs ? item.fullPcs : 0,
                    activityPreferentialAmount: '拼箱数：',
                    totalRealPayAmount: item.spellPcs ? item.spellPcs : 0,
                    isOnlinePay: '总箱数：',
                    payType: item.totalPcs ? item.totalPcs : 0,
                }];
                $("#table_b_" + index).jqGrid({
                    data: data_b,
                    datatype: "local",
                    height: "auto",
                    width: "1218",
                    colNames: ["订单编号", "运费", "销售金额", "优惠金额", "实付金额(小写)", "实付金额(大写)", "是否在线支付", "支付方式"],
                    colModel: [{
                        index: "ecOrderCode",
                        name: "ecOrderCode",
                        width: "193",
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 1) {
                                return 'rowspan=2'
                            } else {
                                return 'style="display:none"'
                            }
                        },
                    },
                        {
                            index: "expressFee",
                            name: "expressFee",
                            formatter: function (e) {
                                if (e == '整件数：') {
                                    return e
                                } else {
                                    return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                }
                            }
                        },
                        {
                            index: "taxAmount",
                            name: "taxAmount",
                            formatter: function (e, key) {
                                if (key.rowId == 1) {
                                    return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                } else {
                                    return e
                                }
                            }
                        },
                        {
                            index: "activityPreferentialAmount",
                            name: "activityPreferentialAmount",
                            formatter: function (e) {
                                if (e == '拼箱数：') {
                                    return e
                                } else {
                                    return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                }
                            }
                        },
                        {
                            index: "totalRealPayAmount",
                            name: "totalRealPayAmount",
                            width: "120",
                            formatter: function (e, key) {
                                if (key.rowId == 1) {
                                    return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                } else {
                                    return e
                                }
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 2) {
                                    return 'colspan=2'
                                }
                            },
                        },
                        {
                            index: "totalRealPayAmountDesc",
                            name: "totalRealPayAmountDesc",
                            width: "200",
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 2) {
                                    return 'style="display:none"'
                                }
                            },
                        },
                        {
                            index: "isOnlinePay",
                            name: "isOnlinePay",
                        },
                        {
                            index: "payType",
                            name: "payType",
                        },
                    ],
                    shrinkToFit: true,
                    rowNum: 2,
                    gridview: true,
                });
                var ordersRemarks = item.salesOrdersRemarks ? item.salesOrdersRemarks : "";
                var peerTypeDesc = item.peerTypeDesc ? item.peerTypeDesc : "";
                var billTypeDesc = item.billTypeDesc ? item.billTypeDesc : "";
                var data_c = [
                    {
                        key: "报告单情况",
                        value: [],
                        other: "",
                    },
                    {
                        key: "发票情况",
                        value: [],
                        other: item.temporaryAreaName,
                    },
                    {
                        key: "售后服务电话",
                        value: [],
                        other: "",
                    },
                    {
                        key: "备注",
                        value: "发票类型：" + billTypeDesc + peerTypeDesc + "  " + "客户备注：" + ordersRemarks,
                        other: "",
                    },
                ];
                $("#table_c_" + index).jqGrid({
                    data: data_c,
                    datatype: "local",
                    height: "auto",
                    width: "1215",
                    colNames: ["", "", ""],
                    colModel: [{
                        index: "key",
                        name: "key",
                        width: "200",
                    },
                        {
                            index: "value",
                            name: "value",
                            width: "320",
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 4 || rowId == 1) {
                                    return 'colspan=2'
                                }
                            },
                            formatter: function (e, row, rowData, fromType) {
                                if (e instanceof Array) {
                                    if (row.rowId == 1) {
                                        return '<span class="col_de"><input type="checkbox" disabled>电子</span><span class="col_de"><input type="checkbox" disabled>纸质</span>'
                                    } else if (row.rowId == 2) {
                                        return '<span class="col_de"><input type="checkbox" disabled>电子</span><span class="col_de"><input type="checkbox" disabled>纸质</span>'
                                    } else if (row.rowId == 3) {
                                        return '<span class="col_de">400-0505-111</span>'
                                    }
                                } else {
                                    return e
                                }
                            }
                        },
                        {
                            index: "other",
                            name: "other",
                            width: "360",
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 2) {
                                    return 'rowspan=2 style="text-align: left;padding-left:5px;'
                                } else {
                                    return 'style="display:none"'
                                }
                            },
                            formatter: function (e, row) {
                                return '暂存区名称：' + e
                            }
                        },
                    ],
                    shrinkToFit: true,
                    rowNum: 4,
                    gridview: true,
                    gridComplete: function () {
                        //隐藏表头
                        $(this).closest('.ui-jqgrid-view').find('div.ui-jqgrid-hdiv').hide();
                    }
                });

                //药品的货物交接单添加二维码
                if (item.pageNumber == item.pageTotal) {
                    //生成二维码
                    new QRCode(document.getElementById("qrcode" + index), {
                        text: item.qrcodeUrl,
                        width: 96,//设置宽高
                        height: 96,
                        colorDark: "#000000",
                        colorLight: "#ffffff",
                        correctLevel: QRCode.CorrectLevel.L
                    });
                }
            }

            function qxShipmentOrderTemplate() {
                //医疗器械类,智鹿出库单
                if (item.storeOrder) {
                    $("#table_a_" + index).jqGrid({
                        data: item.detailVoList,
                        datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                        //width: "1322.83",
                        colNames: ['序号', '商品编号</br>医疗器械名称', '规格</br>（型号）', '生产厂商', '包装数量', '单位', '数量', '件数', priceStr, '销售金额', '优惠金额', '实付金额', '生产企业许可证号<br />（备案凭证编号）', '生产批号<br />（序列号）', '生产日期/<br />有效期至', '储运条件', '注册证号</br>（备案凭证编号）', '质量<br />状况'],
                        colModel: [{
                            index: 'sort',
                            name: 'sort',
                            width: 30,
                        }, {
                            index: 'productCode',
                            name: 'productCode',
                            width: 135,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var code = rowData.productCode == null ? '' : rowData.productCode;
                                    var prdName = rowData.productName == null ? '' : rowData.productName;
                                    return '<p>' + code + '</p><p>' + prdName + '</p>';
                                } else {
                                    return val;
                                }
                            }
                        }, {
                            index: 'productSpec',
                            name: 'productSpec',
                            width: 95,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'colspan=4'
                                } else if (rowId == style) {
                                    //金额合计(大写) value
                                    return 'colspan=9 style="text-align: left;padding-left:5px;"'
                                }
                            },
                            formatter: function (val, rowType, rowData) {
                                if (val){
                                    if (val.length > 10){
                                        return `<p>${val.substring(0,9)}</p><p>${val.substring(9)}</p>`
                                    } else{
                                        return val
                                    }
                                } else{
                                    return ''
                                }
                            }
                        }, {
                            index: 'productmManufacturer',
                            name: 'productmManufacturer',
                            width: 90,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'piecePackageNumber',
                            name: 'piecePackageNumber',
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'producingPackingUnit',
                            name: 'producingPackingUnit',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'outStoreNumber',
                            name: 'outStoreNumber',
                            width: 40,
                            summaryType: function (value, name, record) {
                                return value
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'pieceNum',
                            name: 'pieceNum',
                            width: 40,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, taxPrice, {
                            index: 'taxAmount',
                            name: 'taxAmount',
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : ''
                            },
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'activityPreferentialAmount',
                            name: 'activityPreferentialAmount',
                            formatter: function (e, a, b) {
                                if(b.isKangPuYP === '1'){
                                    return '(---)';
                                }else{
                                    return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : '';
                                }
                            },
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayAmount',
                            name: 'realPayAmount',
                            formatter: function (e, a, b) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : ''
                            },
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'approvalFileBatchCode',
                            name: 'approvalFileBatchCode',
                            width: 100,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                } else {
                                    return 'style="word-wrap:break-word"'
                                }
                            }
                        }, {
                            index: 'batchCode',
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        }
                        ,{
                            index: 'expiryTimeActual',
                            name: 'expiryTimeActual',
                            width: 85,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'colspan=4 style="word-wrap:break-word;text-align: left;padding-left:5px;"'
                                }
                            }

                        }
                        , {
                            //储运条件
                            index: 'storageConditions',
                            name: 'storageConditions',
                            width: 60,
                        }, {
                            index: 'approvalNumber',
                            name: 'approvalNumber',
                            width: 110,
                        }, {
                            index: 'quality',
                            name: 'quality',
                            width: 35,
                            formatter: function (val, rowType, rowData) {
                                if (rowData.productCode && rowData.productCode != "小计" && rowData.productCode != "金额合计(大写)：") {
                                    return "合格"
                                } else {
                                    return ""
                                }
                            }
                        },/* {
                         index: 'status',
                         name: 'status',
                         hidden: true,
                         hidegrid: true
                         },*/],
                        shrinkToFit: false,
                        rowNum: merge,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_sum = $(this).getCol('taxAmount', false, 'sum');
                            var data = $(this).getRowData();
                            if (data.length < (merge-1)) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == (merge-1)) {
                                $(this).addRowData(merge, {
                                    productCode: "小计",
                                    outStoreNumber: sum_number,
                                    taxAmount: sum_sum.toFixed(2),
                                }, "last");
                            } else if (data.length == merge) {
                                $(this).addRowData(style, {
                                    productCode: "金额合计(大写)：",
                                    productSpec: item.taxAmountDesc,
                                    batchCode: "金额合计：",
                                    manufactureTime: "￥：" + item.taxAmount,
                                }, "last");
                            }
                            //$(this).footerData("set",{"productCode":"合计","number":sum_number,"sum":sum_sum});
                        }
                    });

                    /* 销售汇总单 */
                    var data_b_app = [{
                        ecOrderCode: item.ecOrderCode,
                        salesOrderCode: item.salesOrderCode,
                        expressFee: item.expressFee,
                        taxAmount: item.taxAmount,
                        activityPreferentialAmount: item.activityPreferentialAmount,
                        totalRealPayAmount: item.totalRealPayAmount,
                        totalRealPayAmountDesc: item.totalRealPayAmountDesc,
                        isOnlinePay: item.payType ? "已支付" : "未支付",
                        payType: item.payType ? item.payType : ""
                    }, {
                        expressFee: "整件数：",
                        taxAmount: item.fullPcs ? item.fullPcs : 0,
                        activityPreferentialAmount: '拼箱数：',
                        totalRealPayAmount: item.spellPcs ? item.spellPcs : 0,
                        isOnlinePay: '总箱数：',
                        payType: item.totalPcs ? item.totalPcs : 0,
                    }];
                    $("#table_b_" + index).jqGrid({
                        data: data_b_app,
                        datatype: "local",
                        height: "auto",
                        width: "1218",
                        colNames: ["订单编号", "运费", "销售金额", "优惠金额", "实付金额(小写)", "实付金额(大写)", "是否在线支付", "支付方式"],
                        colModel: [{
                            index: "ecOrderCode",
                            name: "ecOrderCode",
                            width: "193",
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 1) {
                                    return 'rowspan=2'
                                } else {
                                    return 'style="display:none"'
                                }
                            },
                        },
                            {
                                index: "expressFee",
                                name: "expressFee",
                                formatter: function (e) {
                                    if (e == '整件数：') {
                                        return e
                                    } else {
                                        return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                    }
                                }
                            },
                            {
                                index: "taxAmount",
                                name: "taxAmount",
                                formatter: function (e, key) {
                                    if (key.rowId == 1) {
                                        return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                    } else {
                                        return e
                                    }
                                }
                            },
                            {
                                index: "activityPreferentialAmount",
                                name: "activityPreferentialAmount",
                                formatter: function (e) {
                                    if (e == '拼箱数：') {
                                        return e
                                    } else {
                                        return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                    }
                                }
                            },
                            {
                                index: "totalRealPayAmount",
                                name: "totalRealPayAmount",
                                width: "120",
                                formatter: function (e, key) {
                                    if (key.rowId == 1) {
                                        return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                    } else {
                                        return e
                                    }
                                },
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == 2) {
                                        return 'colspan=2'
                                    }
                                },
                            },
                            {
                                index: "totalRealPayAmountDesc",
                                name: "totalRealPayAmountDesc",
                                width: "200",
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == 2) {
                                        return 'style="display:none"'
                                    }
                                },
                            },
                            {
                                index: "isOnlinePay",
                                name: "isOnlinePay",
                            },
                            {
                                index: "payType",
                                name: "payType",
                            },
                        ],
                        shrinkToFit: true,
                        rowNum: 2,
                        gridview: true,
                    });
                    var ordersRemarks_app = item.salesOrdersRemarks ? item.salesOrdersRemarks : "";
                    var peerTypeDesc_app = item.peerTypeDesc ? item.peerTypeDesc : "";
                    var billTypeDesc_app = item.billTypeDesc ? item.billTypeDesc : "";
                    var data_c_app = [
                        {
                            key: "报告单情况",
                            value: [],
                            other: "",
                        },
                        {
                            key: "发票情况",
                            value: [],
                            other: item.temporaryAreaName,
                        },
                        {
                            key: "售后服务电话",
                            value: [],
                            other: "",
                        },
                        {
                            key: "备注",
                            value: "发票类型：" + billTypeDesc_app + peerTypeDesc_app + "  " + "客户备注：" + ordersRemarks_app,
                            other: "",
                        },
                    ];
                    $("#table_c_" + index).jqGrid({
                        data: data_c_app,
                        datatype: "local",
                        height: "auto",
                        width: "1215",
                        colNames: ["", "", ""],
                        colModel: [{
                            index: "key",
                            name: "key",
                            width: "200",
                        },
                            {
                                index: "value",
                                name: "value",
                                width: "320",
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == 4 || rowId == 1) {
                                        return 'colspan=2'
                                    }
                                },
                                formatter: function (e, row, rowData, fromType) {
                                    if (e instanceof Array) {
                                        if (row.rowId == 1) {
                                            return '<span class="col_de"><input type="checkbox" disabled>电子</span><span class="col_de"><input type="checkbox" disabled>纸质</span>'
                                        } else if (row.rowId == 2) {
                                            return '<span class="col_de"><input type="checkbox" disabled>电子</span><span class="col_de"><input type="checkbox" disabled>纸质</span>'
                                        } else if (row.rowId == 3) {
                                            return '<span class="col_de">400-0505-111</span>'
                                        }
                                    } else {
                                        return e
                                    }
                                }
                            },
                            {
                                index: "other",
                                name: "other",
                                width: "360",
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == 2) {
                                        return 'rowspan=2 style="text-align: left;padding-left:5px;'
                                    } else {
                                        return 'style="display:none"'
                                    }
                                },
                                formatter: function (e, row) {
                                    return '暂存区名称：' + e
                                }
                            },
                        ],
                        shrinkToFit: true,
                        rowNum: 4,
                        gridview: true,
                        gridComplete: function () {
                            //隐藏表头
                            $(this).closest('.ui-jqgrid-view').find('div.ui-jqgrid-hdiv').hide();
                        }
                    });

                    //货物交接单添加二维码
                    if (item.pageNumber == item.pageTotal) {
                        //生成二维码
                        new QRCode(document.getElementById("qrcode" + index), {
                            text: item.qrcodeUrl,
                            width: 96,//设置宽高
                            height: 96,
                            colorDark: "#000000",
                            colorLight: "#ffffff",
                            correctLevel: QRCode.CorrectLevel.L
                        });
                    }

                } else {
                    //医疗器械类,其他出库单
                    $("#table_a_" + index).jqGrid({
                        data: item.detailVoList,
                        datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                        //width: "1322.83",
                        colNames: ['序号', '商品编号</br>医疗器械名称', '规格</br>（型号）', '生产厂商', '包装</br>数量', '单位', '数量', '件数', priceStr, '金额', '销售金额</br>(优惠金额)', '实付金额', '生产企业许可证号<br />（备案凭证编号）', '生产批号<br />（序列号）', '生产日期/<br />有效期至', '储运</br>条件', '注册证号</br>（备案凭证编号）', '质量<br />状况'],
                        colModel: [{
                            index: 'sort',
                            name: 'sort',
                            width: 30,
                        }, {
                            index: 'productCode',
                            name: 'productCode',
                            width: 120,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var code = rowData.productCode == null ? '' : rowData.productCode;
                                    var prdName = rowData.productName == null ? '' : rowData.productName;
                                    return '<p>' + code + '</p><p>' + prdName + '</p>';
                                } else {
                                    return val;
                                }
                            }
                        }, {
                            index: 'productSpec',
                            name: 'productSpec',
                            width: 103,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'colspan=4'
                                } else if (rowId == style) {
                                    //金额合计(大写) value
                                    return 'colspan=9 style="text-align: left;padding-left:5px;"'
                                }
                            },
                            formatter: function (val, rowType, rowData) {
                                if (val){
                                    if (val.length > 10){
                                        return `<p>${val.substring(0,9)}</p><p>${val.substring(9)}</p>`
                                    } else{
                                        return val
                                    }
                                } else{
                                    return ''
                                }
                            }
                        }, {
                            index: 'productmManufacturer',
                            name: 'productmManufacturer',
                            width: 140,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'piecePackageNumber',
                            name: 'piecePackageNumber',
                            width: 40,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'producingPackingUnit',
                            name: 'producingPackingUnit',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'outStoreNumber',
                            name: 'outStoreNumber',
                            width: 40,
                            summaryType: function (value, name, record) {
                                return value
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'pieceNum',
                            name: 'pieceNum',
                            width: 40,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, taxPrice, {
                            index: 'taxAmount',
                            name: 'taxAmount',
                            hidden: true,
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : ''
                            },
                            width: 70,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'taxAmountAndPreferentialAmount',
                            name: 'taxAmountAndPreferentialAmount',
                            formatter: function (val, rowType, rowData) {
                                var preferentialAmount;
                                if(rowData.isKangPuYP === '1'){
                                    preferentialAmount = '(---)';
                                }else{
                                    preferentialAmount = rowData.activityPreferentialAmount && !isNaN(rowData.activityPreferentialAmount) ? '<p>(' + parseFloat(rowData.activityPreferentialAmount).toFixed(2) + ')</p>' : '';
                                }
                                var taxAmount = rowData.taxAmount && !isNaN(rowData.taxAmount) ? '<p>' + parseFloat(rowData.taxAmount).toFixed(2) + '</p>' : '';
                                return taxAmount + preferentialAmount;
                            },
                            width: 85,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayAmount',
                            name: 'realPayAmount',
                            width: 75,
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '';
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'approvalFileBatchCode',
                            name: 'approvalFileBatchCode',
                            width: 110,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                } else {
                                    return 'style="word-wrap:break-word"'
                                }
                            }
                        }, {
                            index: 'batchCode',
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }, formatter: function (val, rowType, rowData) {
                                if (val){
                                    return '<span style="font-size: 16px">'+val+ '</span>'
                                }else {
                                    return "";
                                }
                            }
                        }
                        , {
                            index: 'manufactureTime',
                            name: 'manufactureTime',
                            width: 85,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var mTime = rowData.manufactureTime == null ? '' : dateFormat(rowData.manufactureTime, 'yyyy-MM-dd') + '/';
                                    var eTime = rowData.expiryTime == null ? '' : rowData.expiryTime;
                                    return mTime + '</br>' + eTime;
                                } else {
                                    return val == null ? '' : val;
                                }
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'colspan=4 style="word-wrap:break-word;text-align: left;padding-left:5px;"'
                                }
                            }
                        }
                            // ,{
                            //     index: 'expiryTimeActual',
                            //     name: 'expiryTimeActual',
                            //     width: 85  ,
                            //     cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            //         if (rowId == 8) {
                            //             return 'colspan=4 style="word-wrap:break-word;text-align: left;padding-left:5px;"'
                            //         }
                            //     }
                            // }
                        , {
                            //储运条件
                            index: 'storageConditions',
                            name: 'storageConditions',
                            width: 35,
                        }, {
                            index: 'approvalNumber',
                            name: 'approvalNumber',
                            width: 110,
                        }, {
                            index: 'quality',
                            name: 'quality',
                            width: 35,
                            formatter: function (val, rowType, rowData) {
                                if (rowData.productCode && rowData.productCode != "小计" && rowData.productCode != "金额合计(大写)：") {
                                    return "合格"
                                } else {
                                    return ""
                                }
                            }
                        },/* {
                         index: 'status',
                         name: 'status',
                         hidden: true,
                         hidegrid: true
                         },*/],
                        shrinkToFit: false,
                        rowNum: merge,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_sum = $(this).getCol('taxAmount', false, 'sum');
                            var data = $(this).getRowData();
                            if (data.length < merge-1) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == merge-1) {
                                $(this).addRowData(merge, {
                                    productCode: "小计",
                                    outStoreNumber: sum_number,
                                    taxAmount: sum_sum.toFixed(2),
                                }, "last");
                            } else if (data.length == merge) {
                                $(this).addRowData(style, {
                                    productCode: "金额合计(大写)：",
                                    productSpec: item.taxAmountDesc,
                                    batchCode: "金额合计：",
                                    manufactureTime: "￥：" + item.taxAmount,
                                }, "last");
                            }
                            //$(this).footerData("set",{"productCode":"合计","number":sum_number,"sum":sum_sum});
                        }
                    });

                    /* 销售汇总单 */
                    var data_b_app = [{
                        ecOrderCode: item.ecOrderCode,
                        salesOrderCode: item.salesOrderCode,
                        expressFee: item.expressFee,
                        taxAmount: item.taxAmount,
                        activityPreferentialAmount: item.activityPreferentialAmount,
                        totalRealPayAmount: item.totalRealPayAmount,
                        totalRealPayAmountDesc: item.totalRealPayAmountDesc,
                        isOnlinePay: item.payType ? "已支付" : "未支付",
                        payType: item.payType ? item.payType : ""
                    }, {
                        expressFee: "整件数：",
                        taxAmount: item.fullPcs ? item.fullPcs : 0,
                        activityPreferentialAmount: '拼箱数：',
                        totalRealPayAmount: item.spellPcs ? item.spellPcs : 0,
                        isOnlinePay: '总箱数：',
                        payType: item.totalPcs ? item.totalPcs : 0,
                    }];
                    $("#table_b_" + index).jqGrid({
                        data: data_b_app,
                        datatype: "local",
                        height: "auto",
                        width: "1218",
                        colNames: ["订单编号", "运费", "销售金额", "优惠金额", "实付金额(小写)", "实付金额(大写)", "是否在线支付", "支付方式"],
                        colModel: [{
                            index: "ecOrderCode",
                            name: "ecOrderCode",
                            width: "193",
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 1) {
                                    return 'rowspan=2'
                                } else {
                                    return 'style="display:none"'
                                }
                            },
                        },
                            {
                                index: "expressFee",
                                name: "expressFee",
                                formatter: function (e) {
                                    if (e == '整件数：') {
                                        return e
                                    } else {
                                        return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                    }
                                }
                            },
                            {
                                index: "taxAmount",
                                name: "taxAmount",
                                formatter: function (e, key) {
                                    if (key.rowId == 1) {
                                        return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                    } else {
                                        return e
                                    }
                                }
                            },
                            {
                                index: "activityPreferentialAmount",
                                name: "activityPreferentialAmount",
                                formatter: function (e) {
                                    if (e == '拼箱数：') {
                                        return e
                                    } else {
                                        return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                    }
                                }
                            },
                            {
                                index: "totalRealPayAmount",
                                name: "totalRealPayAmount",
                                width: "120",
                                formatter: function (e, key) {
                                    if (key.rowId == 1) {
                                        return e && !isNaN(e) ? parseFloat(e).toFixed(2) : '0.00'
                                    } else {
                                        return e
                                    }
                                },
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == 2) {
                                        return 'colspan=2'
                                    }
                                },
                            },
                            {
                                index: "totalRealPayAmountDesc",
                                name: "totalRealPayAmountDesc",
                                width: "200",
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == 2) {
                                        return 'style="display:none"'
                                    }
                                },
                            },
                            {
                                index: "isOnlinePay",
                                name: "isOnlinePay",
                            },
                            {
                                index: "payType",
                                name: "payType",
                            },
                        ],
                        shrinkToFit: true,
                        rowNum: 2,
                        gridview: true,
                    });
                    var ordersRemarks_app = item.salesOrdersRemarks ? item.salesOrdersRemarks : "";
                    var peerTypeDesc_app = item.peerTypeDesc ? item.peerTypeDesc : "";
                    var billTypeDesc_app = item.billTypeDesc ? item.billTypeDesc : "";
                    var data_c_app = [
                        {
                            key: "报告单情况",
                            value: [],
                            other: "",
                        },
                        {
                            key: "发票情况",
                            value: [],
                            other: item.temporaryAreaName,
                        },
                        {
                            key: "售后服务电话",
                            value: [],
                            other: "",
                        },
                        {
                            key: "备注",
                            value: "发票类型：" + billTypeDesc_app + peerTypeDesc_app + "  " + "客户备注：" + ordersRemarks_app,
                            other: "",
                        },
                    ];
                    $("#table_c_" + index).jqGrid({
                        data: data_c_app,
                        datatype: "local",
                        height: "auto",
                        width: "1215",
                        colNames: ["", "", ""],
                        colModel: [{
                            index: "key",
                            name: "key",
                            width: "200",
                        },
                            {
                                index: "value",
                                name: "value",
                                width: "320",
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == 4 || rowId == 1) {
                                        return 'colspan=2'
                                    }
                                },
                                formatter: function (e, row, rowData, fromType) {
                                    if (e instanceof Array) {
                                        if (row.rowId == 1) {
                                            return '<span class="col_de"><input type="checkbox" disabled>电子</span><span class="col_de"><input type="checkbox" disabled>纸质</span>'
                                        } else if (row.rowId == 2) {
                                            return '<span class="col_de"><input type="checkbox" disabled>电子</span><span class="col_de"><input type="checkbox" disabled>纸质</span>'
                                        } else if (row.rowId == 3) {
                                            return '<span class="col_de">400-0505-111</span>'
                                        }
                                    } else {
                                        return e
                                    }
                                }
                            },
                            {
                                index: "other",
                                name: "other",
                                width: "360",
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == 2) {
                                        return 'rowspan=2 style="text-align: left;padding-left:5px;'
                                    } else {
                                        return 'style="display:none"'
                                    }
                                },
                                formatter: function (e, row) {
                                    return '暂存区名称：' + e
                                }
                            },
                        ],
                        shrinkToFit: true,
                        rowNum: 4,
                        gridview: true,
                        gridComplete: function () {
                            //隐藏表头
                            $(this).closest('.ui-jqgrid-view').find('div.ui-jqgrid-hdiv').hide();
                        }
                    });

                    //货物交接单添加二维码
                    if (item.pageNumber == item.pageTotal) {
                        //生成二维码
                        new QRCode(document.getElementById("qrcode" + index), {
                            text: item.qrcodeUrl,
                            width: 96,//设置宽高
                            height: 96,
                            colorDark: "#000000",
                            colorLight: "#ffffff",
                            correctLevel: QRCode.CorrectLevel.L
                        });
                    }
                }
            }
            /* 销售出库复核单 */
            if(item.printTemplateType && item.printTemplateType == '1'){
                qxShipmentOrderTemplate();
            }else if(item.printTemplateType && item.printTemplateType == '3'){
                ShipmentOrderTemplate();
            }else if(item.printTemplateType && item.printTemplateType == '4'){
                ShipmentOrderTemplate();
            }else {
                ShipmentOrderTemplate();
            }});


            if(temList&&temList.length>0){
                temList.forEach(function (item, index) {
                    var keysList = item.detailVoList.map(function (val, key) {
                        delete val.id;
                        if (val.salesOutOrderCode) {
                            val.sort = key + 1;
                        }
                        return val
                    });
                    var merge = 6;
                    var style = 7;
                    $("#table_d_" + index).jqGrid({
                        data: keysList,
                        datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                        //width: "1322.83",
                        colNames: ['序号', '商品编号</br>通用名称</br>（商品名/品名）', '规格', '上市许可<br/>持有人', '剂型', '单位', '生产厂商', '产地', '数量', '实付单价', '实付金额', '批号',
                            '生产日期/<br />有效期至', '批准文号/<br />注册证号', '件数', '质量<br />状况', '备注'],
                        colModel: [{
                            index: 'sort',
                            name: 'sort',
                            width: 30,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId == merge) {
                                    return ''
                                } else {
                                    return val ? val : '';
                                }
                            }
                        }, {
                            index: 'productCode',
                            name: 'productCode',
                            width: 140,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var code = rowData.productCode == null ? '' : '<p>' + rowData.productCode + '</p>';
                                    var prdName = rowData.productName == null ? '' : '<p>' + rowData.productName + '</p>';
                                    return code + prdName;
                                } else {
                                    return val;
                                }
                            },

                        }, {
                            index: 'productSpec',
                            name: 'productSpec',
                            width: 100,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                //console.log(rowId, tv, rawObject, cm, rdata);
                                if (rowId == merge) {
                                    return 'colspan=5'
                                } else if (rowId == style) {
                                    //金额合计(大写) value
                                    return 'colspan=8 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'marketAuthor',
                            name: 'marketAuthor',
                            width: 60,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId == 5 || rowType.rowId == 6 || rowType.rowId == 4 || rowType.rowId == 3) {
                                    return "";
                                } else {
                                    return val == null ? "" : val;
                                }
                            }
                        }, {
                            index: 'dosageForm',
                            name: 'dosageForm',
                            width: 55,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'producingPackingUnit',
                            name: 'producingPackingUnit',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'productmManufacturer',
                            name: 'productmManufacturer',
                            width: 140,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'producingArea',
                            name: 'producingArea',
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'outStoreNumber',
                            name: 'outStoreNumber',
                            width: 40,
                            summaryType: function (value, name, record) {
                                return value
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayPrice',
                            name: 'realPayPrice',
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : ''
                            },
                            width: 65,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayAmount',
                            name: 'realPayAmount',
                            formatter: function (e, a, b) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : ''
                            },
                            width: 70,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'batchCode',
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'colspan=6 style="text-align: left;padding-left:5px;"'
                                }
                                return 'style="word-wrap:break-word"'
                            }
                        }
                            , {
                                index: 'manufactureTime',
                                name: 'manufactureTime',
                                width: 85,
                                formatter: function (val, rowType, rowData) {
                                    if (rowType.rowId < merge) {
                                        var mTime = rowData.manufactureTime == null ? '' : dateFormat(rowData.manufactureTime, 'yyyy-MM-dd') + '/';
                                        var eTime = rowData.expiryTime == null ? '' : rowData.expiryTime;
                                        return mTime + '</br>' + eTime;
                                    } else {
                                        return val == null ? '' : val;
                                    }
                                },
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == style) {
                                        return 'colspan=5 style="text-align: left;padding-left:5px;"'
                                    }
                                }
                            }
                            // ,{
                            //     index: 'expiryTimeActual',
                            //     name: 'expiryTimeActual',
                            //     width: 85  ,
                            //     cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            //         if (rowId == 8) {
                            //             return 'colspan=4 style="word-wrap:break-word;text-align: left;padding-left:5px;"'
                            //         }
                            //     }
                            // }
                            , {
                                index: 'approvalNumber',
                                name: 'approvalNumber',
                                width: 130
                            }, {
                                index: 'pieceNum',
                                name: 'pieceNum',
                                width: 40,
                            }, {
                                index: 'quality',
                                name: 'quality',
                                width: 35,
                                formatter: function (val, rowType, rowData) {
                                    if (rowData.productCode && rowData.productCode != "小计" && rowData.productCode != "金额合计(大写)：") {
                                        return "合格"
                                    } else {
                                        return ""
                                    }
                                }
                            }, {
                                index: 'quality',
                                name: 'quality',
                                width: 35
                            }],
                        shrinkToFit: false,
                        rowNum: merge,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_sum = $(this).getCol('realPayAmount', false, 'sum');
                            var data = $(this).getRowData();
                            if (data.length < merge - 1) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == merge - 1) {
                                $(this).addRowData(merge, {
                                    productCode: "小计",
                                    outStoreNumber: sum_number,
                                    realPayAmount: sum_sum.toFixed(2),
                                }, "last");
                            } else if (data.length == merge) {
                                $(this).addRowData(style, {
                                    productCode: "金额合计(大写)：",
                                    productSpec: item.totalRealPayAmountDesc,
                                    batchCode: "金额合计：",
                                    manufactureTime: "￥：" + item.totalRealPayAmount ? item.totalRealPayAmount : 0,
                                }, "last");
                            }
                        }
                    });

                    $("#table_e_" + index).jqGrid({
                        data: keysList,
                        datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                        //width: "1322.83",
                        colNames: ['序号', '商品编号</br>医疗器械名称', '规格</br>(型号)', '生产厂商', '生产企业许可证号</br>(备案凭证号编号)', '单位', '数量', '件数', '单价', '实付金额', '生产批号</br>(序列号)',
                            '生产日期/<br />有效期至', '储运条件', '注册证号/</br>(备案凭证编号)', '质量</br>状况', '备注'],
                        colModel: [{
                            index: 'sort',
                            name: 'sort',
                            width: 30,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId == merge) {
                                    return ''
                                } else {
                                    return val ? val : '';
                                }
                            }
                        }, {
                            index: 'productCode',
                            name: 'productCode',
                            width: 140,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var code = rowData.productCode == null ? '' : '<p>' + rowData.productCode + '</p>';
                                    var prdName = rowData.productName == null ? '' : '<p>' + rowData.productName + '</p>';
                                    return code + prdName;
                                } else {
                                    return val;
                                }
                            },

                        }, {
                            index: 'productSpec',
                            name: 'productSpec',
                            width: 100,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                //console.log(rowId, tv, rawObject, cm, rdata);
                                if (rowId == merge) {
                                    return 'colspan=3'
                                } else if (rowId == style) {
                                    //金额合计(大写) value
                                    return 'colspan=6 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'productmManufacturer',
                            name: 'productmManufacturer',
                            width: 140,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'approvalFileBatchCode',
                            name: 'approvalFileBatchCode',
                            width: 150,
                        }, {
                            index: 'producingPackingUnit',
                            name: 'producingPackingUnit',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'outStoreNumber',
                            name: 'outStoreNumber',
                            width: 40,
                            summaryType: function (value, name, record) {
                                return value
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'pieceNum',
                            name: 'pieceNum',
                            width: 40,
                        }, {
                            index: 'realPayPrice',
                            name: 'realPayPrice',
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : ''
                            },
                            width: 65,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayAmount',
                            name: 'realPayAmount',
                            formatter: function (e, a, b) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : ''
                            },
                            width: 70,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'batchCode',
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'colspan=6 style="text-align: left;padding-left:5px;"'
                                }
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'manufactureTime',
                            name: 'manufactureTime',
                            width: 85,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var mTime = rowData.manufactureTime == null ? '' : dateFormat(rowData.manufactureTime, 'yyyy-MM-dd') + '/';
                                    var eTime = rowData.expiryTime == null ? '' : rowData.expiryTime;
                                    return mTime + '</br>' + eTime;
                                } else {
                                    return val == null ? '' : val;
                                }
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'colspan=5 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'storageConditions',
                            name: 'storageConditions',
                            width: 70
                        }, {
                            index: 'approvalNumber',
                            name: 'approvalNumber',
                            width: 100
                        }, {
                            index: 'quality',
                            name: 'quality',
                            width: 35,
                            formatter: function (val, rowType, rowData) {
                                if (rowData.productCode && rowData.productCode != "小计" && rowData.productCode != "金额合计(大写)：") {
                                    return "合格"
                                } else {
                                    return ""
                                }
                            }
                        }, {
                            index: 'quality',
                            name: 'quality',
                            width: 35
                        }],
                        shrinkToFit: false,
                        rowNum: merge,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_sum = $(this).getCol('realPayAmount', false, 'sum');
                            var data = $(this).getRowData();
                            if (data.length < merge - 1) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == merge - 1) {
                                $(this).addRowData(merge, {
                                    productCode: "小计",
                                    outStoreNumber: sum_number,
                                    realPayAmount: sum_sum.toFixed(2),
                                }, "last");
                            } else if (data.length == merge) {
                                $(this).addRowData(style, {
                                    productCode: "金额合计(大写)：",
                                    productSpec: item.totalRealPayAmountDesc,
                                    batchCode: "金额合计：",
                                    manufactureTime: "￥：" + item.totalRealPayAmount ? item.totalRealPayAmount : 0,
                                }, "last");
                            }
                        }
                    });
                });
            }
            else if (zlprintList && zlprintList.length > 0){
                zlprintList.forEach(function (item, index) {
                    var keysList = item.detailVoList.map(function (val, key) {
                        delete val.id;
                        if (val.salesOutOrderCode) {
                            val.sort = key + 1;
                        }
                        return val
                    });
                    var merge = 6;
                    var style = 7;
                    $("#table_d_" + index).jqGrid({
                        data: keysList,
                        datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                        //width: "1322.83",
                        colNames: ['序号', '商品编号</br>通用名称</br>（商品名/品名）', '规格', '上市许可<br/>持有人', '剂型', '单位', '生产厂商', '产地', '数量', '实付单价', '实付金额', '批号',
                            '生产日期/<br />有效期至', '批准文号/<br />注册证号', '件数', '质量<br />状况', '备注'],
                        colModel: [{
                            index: 'sort',
                            name: 'sort',
                            width: 30,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId == merge) {
                                    return ''
                                } else {
                                    return val ? val : '';
                                }
                            }
                        }, {
                            index: 'productCode',
                            name: 'productCode',
                            width: 140,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var code = rowData.productCode == null ? '' : '<p>' + rowData.productCode + '</p>';
                                    var prdName = rowData.productName == null ? '' : '<p>' + rowData.productName + '</p>';
                                    return code + prdName;
                                } else {
                                    return val;
                                }
                            },

                        }, {
                            index: 'productSpec',
                            name: 'productSpec',
                            width: 100,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                //console.log(rowId, tv, rawObject, cm, rdata);
                                if (rowId == merge) {
                                    return 'colspan=5'
                                } else if (rowId == style) {
                                    //金额合计(大写) value
                                    return 'colspan=8 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'marketAuthor',
                            name: 'marketAuthor',
                            width: 60,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId == 5 || rowType.rowId == 6 || rowType.rowId == 4 || rowType.rowId == 3) {
                                    return "";
                                } else {
                                    return val == null ? "" : val;
                                }
                            }
                        }, {
                            index: 'dosageForm',
                            name: 'dosageForm',
                            width: 55,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'producingPackingUnit',
                            name: 'producingPackingUnit',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'productmManufacturer',
                            name: 'productmManufacturer',
                            width: 140,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'producingArea',
                            name: 'producingArea',
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'outStoreNumber',
                            name: 'outStoreNumber',
                            width: 40,
                            summaryType: function (value, name, record) {
                                return value
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayPrice',
                            name: 'realPayPrice',
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : ''
                            },
                            width: 65,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayAmount',
                            name: 'realPayAmount',
                            formatter: function (e, a, b) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : ''
                            },
                            width: 70,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'batchCode',
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'colspan=6 style="text-align: left;padding-left:5px;"'
                                }
                                return 'style="word-wrap:break-word"'
                            }
                        }
                            , {
                                index: 'manufactureTime',
                                name: 'manufactureTime',
                                width: 85,
                                formatter: function (val, rowType, rowData) {
                                    if (rowType.rowId < merge) {
                                        var mTime = rowData.manufactureTime == null ? '' : dateFormat(rowData.manufactureTime, 'yyyy-MM-dd') + '/';
                                        var eTime = rowData.expiryTime == null ? '' : rowData.expiryTime;
                                        return mTime + '</br>' + eTime;
                                    } else {
                                        return val == null ? '' : val;
                                    }
                                },
                                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                    if (rowId == style) {
                                        return 'colspan=5 style="text-align: left;padding-left:5px;"'
                                    }
                                }
                            }
                            // ,{
                            //     index: 'expiryTimeActual',
                            //     name: 'expiryTimeActual',
                            //     width: 85  ,
                            //     cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            //         if (rowId == 8) {
                            //             return 'colspan=4 style="word-wrap:break-word;text-align: left;padding-left:5px;"'
                            //         }
                            //     }
                            // }
                            , {
                                index: 'approvalNumber',
                                name: 'approvalNumber',
                                width: 130
                            }, {
                                index: 'pieceNum',
                                name: 'pieceNum',
                                width: 40,
                            }, {
                                index: 'quality',
                                name: 'quality',
                                width: 35,
                                formatter: function (val, rowType, rowData) {
                                    if (rowData.productCode && rowData.productCode != "小计" && rowData.productCode != "金额合计(大写)：") {
                                        return "合格"
                                    } else {
                                        return ""
                                    }
                                }
                            }, {
                                index: 'quality',
                                name: 'quality',
                                width: 35
                            }],
                        shrinkToFit: false,
                        rowNum: merge,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_sum = $(this).getCol('realPayAmount', false, 'sum');
                            var data = $(this).getRowData();
                            if (data.length < merge - 1) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == merge - 1) {
                                $(this).addRowData(merge, {
                                    productCode: "小计",
                                    outStoreNumber: sum_number,
                                    realPayAmount: sum_sum.toFixed(2),
                                }, "last");
                            } else if (data.length == merge) {
                                $(this).addRowData(style, {
                                    productCode: "金额合计(大写)：",
                                    productSpec: item.totalRealPayAmountDesc,
                                    batchCode: "金额合计：",
                                    manufactureTime: "￥：" + item.totalRealPayAmount ? item.totalRealPayAmount : 0,
                                }, "last");
                            }
                        }
                    });

                    $("#table_e_" + index).jqGrid({
                        data: keysList,
                        datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                        //width: "1322.83",
                        colNames: ['序号', '商品编号</br>医疗器械名称', '规格</br>(型号)', '生产厂商', '生产企业许可证号</br>(备案凭证号编号)', '单位', '数量', '件数', '单价', '实付金额', '生产批号</br>(序列号)',
                            '生产日期/<br />有效期至', '储运条件', '注册证号/</br>(备案凭证编号)', '质量</br>状况', '备注'],
                        colModel: [{
                            index: 'sort',
                            name: 'sort',
                            width: 30,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId == merge) {
                                    return ''
                                } else {
                                    return val ? val : '';
                                }
                            }
                        }, {
                            index: 'productCode',
                            name: 'productCode',
                            width: 140,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var code = rowData.productCode == null ? '' : '<p>' + rowData.productCode + '</p>';
                                    var prdName = rowData.productName == null ? '' : '<p>' + rowData.productName + '</p>';
                                    return code + prdName;
                                } else {
                                    return val;
                                }
                            },

                        }, {
                            index: 'productSpec',
                            name: 'productSpec',
                            width: 100,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                //console.log(rowId, tv, rawObject, cm, rdata);
                                if (rowId == merge) {
                                    return 'colspan=3'
                                } else if (rowId == style) {
                                    //金额合计(大写) value
                                    return 'colspan=6 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'productmManufacturer',
                            name: 'productmManufacturer',
                            width: 140,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'approvalFileBatchCode',
                            name: 'approvalFileBatchCode',
                            width: 150,
                        }, {
                            index: 'producingPackingUnit',
                            name: 'producingPackingUnit',
                            width: 35,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge || rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'outStoreNumber',
                            name: 'outStoreNumber',
                            width: 40,
                            summaryType: function (value, name, record) {
                                return value
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'pieceNum',
                            name: 'pieceNum',
                            width: 40,
                        }, {
                            index: 'realPayPrice',
                            name: 'realPayPrice',
                            formatter: function (e) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : ''
                            },
                            width: 65,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'realPayAmount',
                            name: 'realPayAmount',
                            formatter: function (e, a, b) {
                                return e && !isNaN(e) ? parseFloat(e).toFixed(2) : b.salesOutOrderCode ? '0.00' : ''
                            },
                            width: 70,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'batchCode',
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == merge) {
                                    return 'colspan=6 style="text-align: left;padding-left:5px;"'
                                }
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'manufactureTime',
                            name: 'manufactureTime',
                            width: 85,
                            formatter: function (val, rowType, rowData) {
                                if (rowType.rowId < merge) {
                                    var mTime = rowData.manufactureTime == null ? '' : dateFormat(rowData.manufactureTime, 'yyyy-MM-dd') + '/';
                                    var eTime = rowData.expiryTime == null ? '' : rowData.expiryTime;
                                    return mTime + '</br>' + eTime;
                                } else {
                                    return val == null ? '' : val;
                                }
                            },
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == style) {
                                    return 'colspan=5 style="text-align: left;padding-left:5px;"'
                                }
                            }
                        }, {
                            index: 'storageConditions',
                            name: 'storageConditions',
                            width: 70
                        }, {
                            index: 'approvalNumber',
                            name: 'approvalNumber',
                            width: 100
                        }, {
                            index: 'quality',
                            name: 'quality',
                            width: 35,
                            formatter: function (val, rowType, rowData) {
                                if (rowData.productCode && rowData.productCode != "小计" && rowData.productCode != "金额合计(大写)：") {
                                    return "合格"
                                } else {
                                    return ""
                                }
                            }
                        }, {
                            index: 'quality',
                            name: 'quality',
                            width: 35
                        }],
                        shrinkToFit: false,
                        rowNum: merge,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_sum = $(this).getCol('realPayAmount', false, 'sum');
                            var data = $(this).getRowData();
                            if (data.length < merge - 1) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == merge - 1) {
                                $(this).addRowData(merge, {
                                    productCode: "小计",
                                    outStoreNumber: sum_number,
                                    realPayAmount: sum_sum.toFixed(2),
                                }, "last");
                            } else if (data.length == merge) {
                                $(this).addRowData(style, {
                                    productCode: "金额合计(大写)：",
                                    productSpec: item.totalRealPayAmountDesc,
                                    batchCode: "金额合计：",
                                    manufactureTime: "￥：" + item.totalRealPayAmount ? item.totalRealPayAmount : 0,
                                }, "last");
                            }
                        }
                    });
                });
            }
        
        setTimeout(function () {
            if(printType==0){
                /* 打印预览 */
                utils.dialog({
                    title:'预览',
                    //width:$(parent.window).width()-100,
                    content:$('#big_box').html(),
                    okValue:'确定',
                    ok:function () {},
                    button: [
                        {
                            value: '下载',
                            callback: function () {
                                utils.htmlToCanvas('销售出库单')
                                return false;
                            }
                        },
                    ]
                }).showModal();
                //$("#print_box").contents().find(".ui-dialog-content").css("overflow","auto");
                window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
            }else if(printType==1){
                /* 打印 */
                $("#box").jqprint({
                    globalStyles: true, //是否包含父文档的样式，默认为true
                    mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                    stylesheet: null, //外部样式表的URL地址，默认为null
                    noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                    iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                    append: null, //将内容添加到打印内容的后面
                    prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                    deferred: $.Deferred() //回调函数
                });
            }else if(printType==2){
                /* 打印 */
                $("#box").jqprint({
                    globalStyles: true, //是否包含父文档的样式，默认为true
                    mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                    stylesheet: null, //外部样式表的URL地址，默认为null
                    noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                    iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                    append: null, //将内容添加到打印内容的后面
                    prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                    deferred: $.Deferred() //回调函数
                });
            }
        },0)
    }
});
