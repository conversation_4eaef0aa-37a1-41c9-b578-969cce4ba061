$(function () {
    $('#X_Tableb').XGrid({
        url:"/proxy-product/product/adjustPrice/getList",
        colNames: ['id', '活动类型','活动类型code', '活动条件','活动条件code','调价规则','操作'],
        colModel: [
            {
                name: 'id',
                index: 'id',
                width: 100,
                hidden: true,
                key: true
            },
            {
                name: 'promoType',
                index: 'promoType',
                width: 80
            },
            {
                name: 'promoTypeCode',
                index: 'promoTypeCode',
                hidden: true,
                width: 80
            },
            {
                name: 'promoCondition',
                index: 'promoCondition',
                width: 120
            },
            {
                name: 'promoConditionCode',
                index: 'promoConditionCode',
                hidden: true,
                width: 120
            },
            {
                name: 'adjustRuleCode',
                index: 'adjustRuleCode',
                width: 150,
                rowtype: '#adjustRule_div',
            },
            {
                name: 'btns',
                index: 'btns',
                width: 60,
                rowtype: '#editBtn_div',
            }
        ],
        rowNum: 100,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        ondblClickRow: function (id,dom,obj,index,event) {
        },
    });

    // 保存
    /*$('#SaveBtn').on('click', function () {
        let  tableData= $('#X_Tableb').XGrid('getRowData')
        $.ajax({
            url: "/proxy-product/product/adjustPrice/updateRule",
            type:'post',
            data: JSON.stringify(tableData),
            contentType : 'application/json;charset=utf-8',
            dataType:"json",
            success: function (data) {
                console.log('success',data);
                utils.dialog({content:  '修改成功', quickClose: true, timeout:  2000}).showModal()
            },
            error:function () {}
        });
    })*/

    //  编辑保存
    $('body').on('click','.edit_save_btn', function () {
        let _this = $(this).val()
        $(this).parents('tr').find('[row-describedby="adjustRuleCode"] select').prop('disabled',   (_this == '编辑') ? false : true)
        $(this).val($(this).val()  == '编辑' ?  '保存' : '编辑')
        if ($(this).val() == '编辑') {
            let rowId = $(this).parents('tr').attr('id');
            let rowData = $("#X_Tableb").getRowData(rowId);
            $.ajax({
                url: "/proxy-product/product/adjustPrice/updateRule",
                type:'post',
                data: JSON.stringify(rowData),
                contentType : 'application/json;charset=utf-8',
                dataType:"json",
                success: function (data) {
                    console.log('success',data);
                    utils.dialog({content:  '修改成功', quickClose: true, timeout:  2000}).showModal()
                },
                error:function () {}
            });
        }
    })
});

