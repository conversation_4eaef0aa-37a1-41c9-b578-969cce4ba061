/**
 * Created by 沙漠里的红裤头 on 2019/6/27
 */
/* 审批流 */
/**
 * 流程图显示
 */
    //根据流程实例ID加载流程图
var processInstaId=$("#processId").val();
initApprovalFlowChart(processInstaId);
function  initApprovalFlowChart(processInstaId) {
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: "/proxy-storage/storage/loss/getProcessListView?processInstanceId="+processInstaId,
        async: false,
        success: function (data) {
            console.log(data);
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}

$(function () {
    var colNames = ['商品编码','原商品编码','商品大类', '商品名称', '商品规格','产地', '生产厂家',
            '单位', '库房名称', '业务类型', '批号', '生产日期', '有效期至','报损数量', '不含税成本单价','不含税成本金额','移库原因'],
        colModel =   [
            {name: 'productCode',index: 'productCode'},
            {name: 'oldProductCode', index: 'oldProductCode'},
            {name: 'drugClass',index: 'drugClass'},
            {name: 'productName',index: 'productName'},
            {name: 'specifications',index: 'specifications'},
            {name:'producingArea', index:'producingArea' },
            {name: 'manufacturerName',index: 'manufacturerName'},
            {name: 'unit', index: 'unit'},
            {name: 'storageName',index: 'storeHouseName'
                ,formatter:function () {
                    return "不合格库";
                }},
            { name: 'channelId',index: 'channelId'},
            { name: 'batchNum',index: 'batchNum'},
            {name: 'productDate',index: 'productDate',
                /*formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }*/
                },
            {name: 'validateDate',index: 'validateDate',
                /*formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }*/
                },
            {name: 'lossNum',index: 'lossNum'},
            {   name: 'noTaxPrice',
                index: 'noTaxPrice',
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }
            },
            {   name: 'noTaxCostNum',
                index: 'noTaxCostNum' ,
                formatter: function (e) {
                    if (e != null && e != '') {
                        var array = e.toString().split(".");
                        if (array.length == 1){
                            return e.toString() + ".00"
                        }else if (array.length ==2){
                            var elngt2 = array[1].length;
                            if (elngt2 == 1){
                                return e.toString()+"0"
                            }else {
                                return e;
                            }
                        }else {
                            return e;
                        }
                    }else {
                        return e;
                    }
                }
            },

            {name: 'wmsTransferReason',index: 'wmsTransferReason'}
        ];

    $('#X_Table').XGrid({
        url:'/proxy-storage/storage/loss/getLossRequestOrderDetailData?lossOrderNo='+$("#lossOrderNo").val(),
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        gridComplete: function () {
            /* 合计写入 */
            var data = $(this).XGrid('getRowData');
            data.forEach(function (item, index) {
                if (item.abnormalCause) {
                    $('#X_Table #' + item['id']).addClass('warnning')
                }
            });
        },
        pager: '#grid-pager',
    });

    //编辑
    /*$("#edit").on('click',function () {
        // 获取时间+单号
        // window.location.href="/proxy-storage/storage/loss/toEditLossOrder?currentDate="+$("#requestDate").val()+"&lossRequestNo="+$("#lossOrderNo").val()+"&taskId="+$("#taskId").val()+"&processId="+$("#processId").val()
        utils.openTabs('toEditLossOrder','编辑不合格品报损申请单', "/proxy-storage/storage/loss/toEditLossOrder?currentDate="+$("#requestDate").val()+"&lossRequestNo="+$("#lossOrderNo").val()+"&taskId="+$("#taskId").val()+"&processId="+$("#processId").val())
    });

    //取消
    $("#cancel").on('click',function () {
        //TODO 需要加弹框，确认是否取消

        // 页面上需要tackId值
        $.ajax({
            type: "POST",
            url: "/proxy-storage/storage/loss/requestLossClose?taskId="+$("#taskId").val()+"&lossOrderNo="+$("#lossOrderNo").val(),
            async: false,
            success: function (data) {
                console.log(data);

            },
            error: function () {}
        });


    })*/

    /* 导出 */
    $('#exportRowData').on('click', function () {
        var tableId = $('#X_Table').attr('id');

        z_utils.exportTable(tableId, function (that) {
            //需要导出项
            var colName = [];
            var colNameDesc = [];
            $(that.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                if ($(this).prop("checked")) {
                    colName.push($(this).attr('name'))
                    colNameDesc.push($(this).parent().text());
                }
            });
            //获取当前选中项
            var data = $('#X_Table').XGrid('getSeleRow');
            console.log(colName);

            //获取form数据
            var formData = {
                colName: colName,
                colNameDesc: colNameDesc,
                selectData: data,
                lossRequestNo: $("#lossOrderNo").val()
            }
            httpPost("/proxy-storage/storage/loss/exportLossDetailList", formData);
        });
    });


    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }


})