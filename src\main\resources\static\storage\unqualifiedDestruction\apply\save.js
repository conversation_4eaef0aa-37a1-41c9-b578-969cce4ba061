/**
 * Created by 沙漠里的红裤头 on 2019/6/28
 */

var path = '/proxy-storage/storage/unqualifiedDestructionApply/'
function initDate() {
    var oldTimes = new Date();
    var newTimes = new Date(oldTimes - 6*24*3600*1000);
    // 开始时间为当前时间减6天
    var yearN = newTimes.getFullYear();
    var monthN = newTimes.getMonth() + 1;
    var dateN = newTimes.getDate();
    // 结束时间为当前时间
    var yearO = oldTimes.getFullYear();
    var monthO = oldTimes.getMonth() + 1;
    var dateO = oldTimes.getDate();
    //1位数加0
    monthN = monthN.toString().length <= 1 ? '0' + monthN : monthN;
    monthO = monthO.toString().length <= 1 ? '0' + monthO : monthO;
    dateN = dateN.toString().length <= 1 ? '0' + dateN : dateN;
    dateO = dateO.toString().length <= 1 ? '0' + dateO : dateO;
    //设置开始时间为当前时间减6天，结束时间为当天23:59:59
//    $('#applyTime').val(yearN + '-' + monthN + '-' + dateN);
    $('#applyTime').val(yearO + '-' + monthO + '-' + dateO);
}
// 返回按钮
function btn_cancel(){
    utils.closeTab();
}
// 删除行
$('#detelRow').on('click',function(){
    var selectRow = $('#X_Table').XGrid('getSeleRow');
    var allData = $('#X_Table').XGrid('getRowData');
    console.log(selectRow);
    if (selectRow.length == 0) {
        utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
    } else {
    	let delIdStr = '', delIdMap = {} ,delIdList = [];
    	$(selectRow).each(function(index,item){
    		if(!(item.lossOrderExec in delIdMap)){
    			delIdMap[item.lossOrderExec] = 0; 
    		}
    	});
    	$(allData).each(function(index, item){
    		if(item.lossOrderExec in delIdMap){
    			delIdMap[item.lossOrderExec] = delIdMap[item.lossOrderExec] + 1; 
    			delIdList.push(item.id); 
    		}
    	});
    	for(var a in delIdMap){
    		delIdStr += '<br>执行单号：' + a + ' 商品数量：'+ delIdMap[a];
    	}
    	utils.dialog({
            title: '温馨提示',
            content: '是否确认删除？' +delIdStr,
            okValue: '确定',
            ok: function () {
            	$(delIdList).each(function(){
                	$('#X_Table').XGrid('delRowData',this);
            	});
            	for(var i = lossOrderExecs.length; i >= 0 ; i--){
            		if(lossOrderExecs[i] in delIdMap){
            			lossOrderExecs.splice(i,1)
            		}
            	}
                utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
            	var _a = 0;
                var rowdata = $('#X_Table').getRowData();
                rowdata.forEach(function (item,index) {
                    _a += Number(item.lossNum);
                })

                $('.span_total').text(_a.toFixed(2));
            },
            cancelValue: '取消',
            cancel: function f() {}
        }).showModal()
        
        for(var i=0;i<selectRow.length;i++){
            var id= selectRow[i].id,
            	lossOrderExecVal = selectRow[i]['lossOrderExec'];
        }
    }
})

var lossOrderExecs = new Array();
$("#addRowData").on("click", function () {
//	lossOrderExecs = $('#X_Table').XGrid('getRowData');
    utils.dialog({
        url: path + 'toLossRequestList',
        title: '报损执行单列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        onclose: function () {
        	var data = this.returnValue;
        	if(!(data === "")){
	            if (data.length != 0) {
	                $('#X_Table').setGridParam({
	               		url: path + 'findLossRequestDetailList',
		           	    postData: {
		           	    	lossOrderIds: data.join(',')
		           	    },
	               }).trigger('reloadGrid');
	            }
        	}
        }
    }).showModal();
});
var sumPrice = 0;
$('#X_Table').XGrid({
    url:path + "findLossRequestDetailList",
    postData:{"lossOrderIds":JSON.stringify(lossOrderExecs)},
    colNames: ['销毁原因','销毁地点','销毁方式','报损执行单号','商品编码', '原商品编码','商品大类','商品名称',
                    '商品规格', '商品产地', '生产厂家', '单位',  '库房名称','业务类型' ,'业务类型', '批号', '生产日期', '有效期至','销毁数量','不含税成本单价','不含税成本金额'],
    colModel: [
        {name: 'destructionReason',index: 'destructionReason' ,rowtype: '#destroy_Reson'},
        {name: 'destructionPlace',index: 'destructionPlace' ,rowtype: '#destroy_Address'},
        {name: 'destructionMethod',index: 'destructionMethod' ,rowtype: '#destroy_Type'},
        {name: 'lossOrderExec',index: 'lossOrderExec',width:200},
        {name: 'productCode',index: 'productCode' },
        {name: 'oldProductCode',index: 'oldProductCode'},
        {name: 'drugClass',index: 'drugClass'},
        {name: 'productName',index: 'productName'},
        {name: 'specifications',index: 'specifications'},
        {name: 'producingArea',index: 'producingArea',},
        {name: 'manufacturerName',index: 'manufacturerName',},
        {name: 'productUnit',index: 'productUnit'},
        {name: 'storageName',index: 'storageName',
            formatter: function (e) {
                return '不合格库'
            }
        },
        {name: 'channelId',index: 'channelId'},
        {name: 'channelName',index: 'channelName',hidden:true},
        {name: 'batch',index: 'batch' },
        {name: 'produceDate',index: 'produceDate' ,
            formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }
        },
        {name: 'validDate',index: 'validDate',
            formatter:function (e){
                if (e != null && e !="") {
                    return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                } else {
                    return "";
                }
            }
        },
        {name: 'lossNum',index: 'lossNum',
            formatter: function (e) {
            	console.log("e : ", e );
                return Number(e).toFixed(2);
            }
        },
        {name: 'noTaxPrice',index: 'noTaxPrice',hidden:true,
            formatter: function (e) {
            	console.log("e : ", e );
                return Number(e).toFixed(2);
            }
        },
        {name: 'noTaxCostNum',index: 'noTaxCostNum',hidden:true,
            formatter: function (e) {
                return Number(e).toFixed(2);
            }
        }
    ],
    altRows: true, //设置为交替行表格,默认为false
    rowNum: 500,
    key:"id",
    rowList:[20,50,100,500],
    selectandorder: true,//是否展示序号，多选
    //attachRow:true,
    ondblClickRow: function (id, dom, obj, index, event) {},
    onSelectRow: function (id, dom, obj, index, event) {},
    gridComplete: function () {
        var _a = 0;
        var rowdata = $(this).getRowData();
        rowdata.forEach(function (item,index) {
            _a += Number(item.noTaxCostNum);
        })

        $('.span_total').text(_a.toFixed(2));
    },
    //pager: '#grid_page'
});

$(function (){
    initDate();
})

function timeStamp2String(time){
    var datetime = new Date();
    datetime.setTime(time);
    var year = datetime.getFullYear();
    var month = datetime.getMonth() + 1;
    var date = datetime.getDate();
    date = date.toString().length <= 1 ? '0' + date : date;
    month = month.toString().length <= 1 ? '0' + month : month;
    return year + "-" + month + "-" + date;
};

//保存
function btn_save() {
    //var rowdata = $('#X_Table').XGrid('getSeleRow');
    var allData = $('#X_Table').XGrid('getRowData');
    /*if(rowdata.length == 0){
        utils.dialog({
            title:'温馨提示',
            content:'请选择数据！',
            okValue:'确定',
            ok:function () {}
        }).showModal();
        return false;
    }*/
    var arr = [];
    allData.forEach(function (item,index) {
        arr.push({
    		"storageLossExecutionCode": item.lossOrderExec,
    		"storageLossExecutionDetailId": item.id,
    		"destructionReason": item.destructionReason,
    		"destructionPlace": item.destructionPlace,
    		"destructionMethod": item.destructionMethod,
            "channelId":item.channelId,
            "channelName":item.channelName
    	})
    })
    utils.dialog({
        title: '温馨提示',
        content: '提交审核后不合格品销毁申请单将无法进行关闭，是否确认提交审核？',
        okValue: '确定',
        ok: function () {
            //下面保存
            $.ajax({
                url: path + "save",
                type:'post',
                dataType:'json',
//                data: {applyExplain:$("#applyExplain").val(), rowdata:JSON.stringify(rowdata)},
                data: {
                	applyTime:$("#applyTime").val(), 
                	applyExplain:$("#applyExplain").val(), 
                	unqualifiedDestructionApplyCode:$("#unqualifiedDestructionApplyCode").val(), 
                	rowData:JSON.stringify(arr)
            	},
                success: function(data){
                    if(data.code == 1000000){
                        utils.dialog({
                            title: '温馨提示',
                            content: '保存成功',
                            okValue: '确定',
                            ok: function () {
                            	utils.closeTab();
                            }

                        }).showModal()
                    }else{
                        utils.dialog({
                            title: '温馨提示',
                            content: data.msg,
                            okValue: '确定',
                            ok: function () {
                                return;
                            }

                        }).showModal()
                    }
                }
            });
        }

    }).showModal()
}
function fidInArr(id){
    let arr = $("#table").getRowData()
    for(var i=0;i<arr.length;i++){
        if(arr[i].baseId == id){
            return true;
        }
    }
    return false;
}
