var req_url_prefix = "/proxy-sysmanage/sysmanage/common/dict";
var common_dict_type = 17;
$(function () {
	renderData();
});

/** 渲染数据 */
function renderData() {
    $('#X_Table').XGrid({
		url : req_url_prefix + "/commonDictListPage",
		mtype : "POST",
		postData : {
			type: common_dict_type
		},
        colNames: ['名称', '状态', '创建人', '最后一次修改人', '创建时间', '最后一次修改时间'],
        colModel: [
        {
            name: 'name'
        }, {
            name: 'statesName',
            formatter: function(e, d, rData) {
            	var val = rData.states;
                if(0 == val) {
                    return "停用";
                } else if(1 == val){
                    return "启用";
                }else {
                    return "";
                }
			}
        }, {
            name: 'createUser',
        }, {
            name: 'updateUser',
        }, {
            name: 'createTime',
            formatter : dateFormatter
        }, {
            name: 'updateTime',
            formatter : dateFormatter
        }, {
            name: 'states',
            hidden: true
        }, ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        altRows: true,
        pager: '#grid-pager',
        ondblClickRow: function (id, dom, obj, index, event) {
        },
        onSelectRow: function (id, dom, obj, index, event) {
        }
    });
};

/** 停用/启用 */
function chanageStatus(id, states) {
	if (states == 1) {
		states = 0;
	} else {
		states = 1;
	}
	$.ajax({
		url: req_url_prefix + "/chanageStatus",
		type: "POST",
		dataType: "json",
		async: false,
		data: {
			id:id,
			states:states
		},
		success: function(res) {
			if (res.code == 0 && "true" == res.result) {
				showTips("保存成功");
			} else {
				showTips("保存失败");
			}
			renderData();
		}
	});
};

/** 停用/启用 */
$('#chanage_status_btn').bind('click', function () {
    var selRow = $('#X_Table').XGrid('getSeleRow');
    if (selRow.length > 0) {
    	chanageStatus(selRow[0].id, selRow[0].states);
    } else {
        utils.dialog({
            content: '没有选中任何行！',
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
});

/** 编辑 */
$('#edit_btn').bind('click', function () {
    var selRow = $('#X_Table').XGrid('getSeleRow');
    if (selRow.length > 0) {
    	queryById(selRow[0].id);
    } else {
        utils.dialog({
            content: '没有选中任何行！',
            quickClose: true,
            timeout: 2000
        }).showModal();
    }
});

/** 新增 */
$('#add_btn').bind('click', function () {
	resetForm();
	showModel("add");
});

/** 查询信息 */
function queryById(id) {
	$.ajax({
		url: req_url_prefix + "/queryById",
		type: "POST",
		dataType: "json",
		async: false,
		data: {
			id: id,
			type: common_dict_type
		},
		success: function(res) {
			if (res.code == 0) {
				$("#add_common_id").val(res.result.id);
				$("#add_common_name").val(res.result.name);
				showModel("edit");
			} else {
				showTips(res.msg);
			}
		}
	});
};

/** 保存信息 */
function save(obj) {
	$.ajax({
		url: req_url_prefix + "/save",
		type: "POST",
		dataType: "json",
		async: false,
		contentType: "application/json;charset=UTF-8",
		data: JSON.stringify(obj),
		success: function(res) {
			if (res.code == 0 && "true" == res.result) {
				showTips("保存成功");
			} else {
				showTips(res.msg);
			}
			renderData();
		}
	});
	resetForm();
};

/** 重置表单 */
function resetForm() {
	$("#add_common_id").val(""),
	$("#add_common_name").val("")	
}

/** 保存信息 */
function saveCheck() {
	var result={passed:false};
	var name = $("#add_common_name").val();
	var regexp = /^[\u4e00-\u9fa5a-zA-Z]+$/;
    if (!regexp.test(name)){
        showTips("格式不正确，只能输入中文或英文字母");
        return result;
    }
    result.passed = true;
    result.id = $("#add_common_id").val();
    result.name = name;
    result.type = common_dict_type;
	return result;
};

/** 展示弹板信息 */
function showModel(flag) {
	var str = "新建";
	if ("edit" == flag) {
		str = "编辑";
	}
    utils.dialog({
        title: str + '销售分类属性',
        content: $('#modal'),
        width: 450,
        okValue: '确认',
        cancelValue: '取消',
        cancel: true,
        ok: function () {
        	var params = saveCheck();
            if(!params.passed){
                return false;
            }
        	save(params);
        }
    }).show();	
}

/** 时间格式化 */
function dateFormatter(val) {
    if (val != null && val !="") {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
    } else {
        return "";
    }
};

/** 状态格式化 */
function statesFormatter(val){
    if(0 == val) {
        return "停用";
    } else if(1 == val){
        return "启用";
    }else {
        return "";
    }
};

/** 弹框提示 */
function showTips(content){
    utils.dialog({
        content: content,
        quickClose: true,
        timeout: 2000
    }).showModal();
};
