$(function () {
    var id = $("#customerApplId").val();
    $('div[fold=head]').fold({
        sub: 'sub'
    });
//显示流程图
    var processInstaId=$("#processId").val();
    if(!processInstaId || processInstaId == ""){
        processInstaId=$("#approvalProcessId").val();
    }
    initApprovalFlowChart("customerFirstCampApply", processInstaId);

    var rowNumber=1;
    /*$("#addRowData").on("click",function () {
     rowNumber++;
     $('#table3').addRowData({id:rowNumber});
     initFileSelected();
     });
     $("#deleRow").on("click",function () {
     var selectRow = $('#table3').XGrid('getSeleRow');
     if (!selectRow) {
     utils.dialog({content: '请选择删除行！', quickClose: true, timeout: 2000}).showModal();
     } else {
     $('#table3').XGrid('delRowData', selectRow.id);
     utils.dialog({content: '删除成功！', quickClose: true, timeout: 2000}).showModal();
     }

     });*/

    //助记码
    $("#customerNm").on("keyup",function (ev) {
        if(ev.keyCode != 13)
        {
            var value=this.value;
            getMnemonicCode(value,'customerMnemonicCode');
        }
    });

    //结算方式
    $("input[name='itemValue']:not(:checked)").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').hide();
    });
    $("input[name='itemValue']:checked").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').show();
    });

    //tab切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    })
    //切换的质量保证协议
    // $('#table1').XGrid({
    //     url:"/proxy-customer/customer/customerCommonData/getCustomerQualityAgreementAll?correlationId="+id+"&&type=4",
    //     colNames: ['', '签订日期', '有效期至', '签订人', '附件','附件数据'],
    //     colModel: [{
    //         name: 'id', //与反回的json数据中key值对应
    //         index: 'id', //索引。其和后台交互的参数为sidx
    //         key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    //         hidden: true
    //     }, {
    //         name: 'signedDate',
    //         index: 'signedDate',
    //         rowtype: '#signedDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'validDate',
    //         index: 'validDate',
    //         rowtype: '#validDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'signer',
    //         index: 'signer',
    //         rowtype: '#signer'
    //     }, {
    //         name: 'enclosureCount',
    //         index: 'enclosureCount',
    //         formatter:function (value) {
    //             var str='无';
    //             if(value)
    //             {
    //                 var tableId = "#table1"
    //                 str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
    //             }
    //             return str;
    //         },
    //         unformat: function (e) {
    //             e=e.replace(/<[^>]+>/g,'');
    //             if(e == '无'){
    //                 e = 0;
    //             }
    //             return e;
    //         }
    //     },{
    //         name:'customerEnclosureVoList',
    //         index:'customerEnclosureVoList',
    //         hidden:true,
    //         formatter:function (value) {
    //             if(value)
    //             {
    //                 return JSON.stringify(value);
    //             }
    //             return JSON.stringify([]);
    //         }
    //     }],
    //     altRows: true,
    //     rownumbers: true/*, //设置为交替行表格,默认为false
    //      ondblClickRow: function (id,dom,obj,index,event) {
    //      console.log('双击行事件', id,dom,obj,index,event);
    //      },
    //      onSelectRow: function (id,dom,obj,index,event) {
    //      console.log('单机行事件',id,dom,obj,index,event);
    //      },*/
    //     //pager:'#table1_page'
    // });
    // //批量上传 质量保证协议
    // $("#zlbzUpload").on("click", function () {
    //     //获取type类型
    //     var typeList=[];
    //     var eChoImgList=[];
    //     var $table=$('#table1');
    //     var rowData=$table.getRowData();
    //     var $tr=$table.find("tr").not(":first");
    //     for(var i=0;i<$tr.length;i++)
    //     {
    //         var sel=$tr.eq(i).find("input[name='signer']");
    //         typeList.push({
    //             text:sel.val(),
    //             value:sel.val()
    //         });
    //         console.log(sel.val())
    //         //添加已存在附件
    //         if(rowData[i].customerEnclosureVoList.length > 0)
    //         {
    //             rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
    //             for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
    //             {
    //                 rowData[i].customerEnclosureVoList[j].type=sel.val();
    //             }
    //             eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
    //         }
    //     }
    //     $(this).upLoad({
    //         typeList:typeList,//格式[{text:xxx,value:xxx}]
    //         eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
    //         fileParam:{
    //             name:'enclosureName',
    //             url:'url'
    //         },
    //         urlBack: function (data) {
    //             //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
    //             console.log(data)
    //             if($.isEmptyObject(data)){
    //                 $table.find("tr").each(function () {
    //                     var id=$(this).attr('id');
    //                     if(id){
    //                         $table.setRowData(id,{'customerEnclosureVoList':[]});
    //                         $table.setRowData(id,{'enclosureCount':''});
    //                     }
    //                 })
    //                 return false;
    //             }
    //             for(var name in data)
    //             {
    //                 var list=data[name];
    //                 for(var i=0;i<rowData.length;i++)
    //                 {
    //                     if(rowData[i].signer == name)
    //                     {
    //                         var trId=$table.find("tr").eq(i+1).attr('id');
    //                         $table.setRowData(trId,{'customerEnclosureVoList':list});//存储时只能存字符串，取值时用JSON.parse转换
    //                         $table.setRowData(trId,{'enclosureCount':(list.length == 0?'':list.length)});
    //                         break;
    //                     }
    //                 }
    //             }
    //         }
    //     });
    // });

    //客户委托书
    $('#table2').XGrid({
        url:"/proxy-customer/customer/customerCommonData/getCustomerDelegationFileAll?correlationId="+id+"&&type=4",
        colNames: ['', '姓名', '电话', '委托书有效期至', '附件','附件数据'
        ],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'delegationName',
            index: 'delegationName',
            rowtype: '#delegationName'
        },
       // {
        //     name: 'delegationSex',
        //     index: 'delegationSex',
        //     rowtype: '#entrustSex'
        // },
            {
            name: 'delegationTel',
            index: 'delegationTel',
            rowtype: '#delegationTel'
        },
        //     {
        //     name: 'delegationNum',
        //     index: 'delegationNum',
        //     rowtype: '#delegationNum'
        // },
        // {
        //     name: 'delegationAddr',
        //     index: 'delegationAddr',
        //     rowtype: '#delegationAddr'
        // },
        //     {
        //     name: 'delegationIdentityDate',
        //     index: 'delegationIdentityDate',
        //     rowtype: '#delegationIdentityDate',
        //     formatter:function(value){
        //         var date=value;
        //         if(!value)return false;
        //         date=format(value);
        //         return date.split(' ')[0];
        //     }
        // },
            {
            name: 'validityDelegationCredential',
            index: 'validityDelegationCredential',
            rowtype: '#validityDelegationCredential',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        },{
            name: 'enclosureCount',
            index: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#table2";
                    str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        },{
            name:'customerEnclosureVoList',
            index:'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],
        altRows: true,
        rownumbers: true/*, //设置为交替行表格,默认为false
         ondblClickRow: function (id,dom,obj,index,event) {
         console.log('双击行事件', id,dom,obj,index,event);
         },
         onSelectRow: function (id,dom,obj,index,event) {
         console.log('单机行事件', id,dom,obj,index,event);
         },*/
        //pager:'#table2_page'
    });
    //批量上传 客户委托书
    $("#knwtUplode").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#table2');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("input[name='delegationName']");
            typeList.push({
                text:sel.val(),
                value:sel.val()
            });
            console.log(sel.val())
            //添加已存在附件
            if(rowData[i].customerEnclosureVoList.length > 0)
            {
                rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
                for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
                {
                    rowData[i].customerEnclosureVoList[j].type=sel.val();
                }
                eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
            }
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'customerEnclosureVoList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                for(var name in data)
                {
                    var list=data[name];
                    for(var i=0;i<rowData.length;i++)
                    {
                        if(rowData[i].delegationName == name)
                        {
                            var trId=$table.find("tr").eq(i+1).attr('id');
                            $table.setRowData(trId,{'customerEnclosureVoList':list});//存储时只能存字符串，取值时用JSON.parse转换
                            $table.setRowData(trId,{'enclosureCount':(list.length == 0?'':list.length)});
                            break;
                        }
                    }
                }
            }
        });
    });

    //被委托人身份证
    $('#certTable').XGrid({
        url:"/proxy-customer/customer/customerCommonData/getCustomerBeProxyIdentityAll?correlationId="+id+"&&type=4",
        colNames: ['', '姓名', '证件号码', '身份证有效期至', '附件','附件数据'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'customerName',
            index: 'customerName',
            rowtype :'#customerName'
        }, {
            name: 'identityNumber',
            index: 'identityNumber',
            rowtype :'#identityNumber'
        }, {
            name: 'identityValidityDate',
            index: 'identityValidityDate',
            rowtype :'#identityValidityDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'enclosureCount',
            index: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#certTable";
                    str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        },{
            name:'customerEnclosureVoList',
            index:'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],
        altRows: true,
        rownumbers: true/*, //设置为交替行表格,默认为false
         ondblClickRow: function (id,dom,obj,index,event) {
         console.log('双击行事件', id,dom,obj,index,event);
         },
         onSelectRow: function (id,dom,obj,index,event) {
         console.log('单机行事件', id,dom,obj,index,event);
         },*/
        //pager:'#certTable_page'
    });
    //批量上传 委托人身份证
    $("#bwtrUplode").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#certTable');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("input[name='customerName']");
            typeList.push({
                text:sel.val(),
                value:sel.val()
            });
            console.log(sel.val())
            //添加已存在附件
            if(rowData[i].customerEnclosureVoList.length > 0)
            {
                rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
                for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
                {
                    rowData[i].customerEnclosureVoList[j].type=sel.val();
                }
                eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
            }
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'customerEnclosureVoList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                for(var name in data)
                {
                    var list=data[name];
                    for(var i=0;i<rowData.length;i++)
                    {
                        if(rowData[i].customerName == name)
                        {
                            var trId=$table.find("tr").eq(i+1).attr('id');
                            $table.setRowData(trId,{'customerEnclosureVoList':list});//存储时只能存字符串，取值时用JSON.parse转换
                            $table.setRowData(trId,{'enclosureCount':(list.length == 0?'':list.length)});
                            break;
                        }
                    }
                }
            }
        });
    });
    initTable3(id,4);
    //批准文件
    // $('#table3').XGrid({
    //     url: "/proxy-customer/customer/customerCommonData/getCustomerApprovalFileAll?correlationId="+id+"&&type=4",
    //     colNames: ['', '证书类型', '证书编号', '经营范围', '发证机关', '发证日期', '有效期至', '附件','附件数据'],
    //     colModel: [{
    //         name: 'id', //与反回的json数据中key值对应
    //         index: 'id', //索引。其和后台交互的参数为sidx
    //         key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    //     }, {
    //         name: 'credentialTypeId',
    //         index: 'credentialTypeId',
    //         rowtype: '#credentialTypeId'
    //     }, {
    //         name: 'credentialCode',
    //         index: 'credentialCode',
    //         rowtype: '#credentialCode'
    //     }, {
    //         name: 'businessScope',
    //         index: 'businessScope',
    //         rowtype: '#businessScope'
    //     }, {
    //         name: 'certificationOffice',
    //         index: 'certificationOffice',
    //         rowtype: '#certificationOffice'
    //     }, {
    //         name: 'openingDate',
    //         index: 'openingDate',
    //         rowtype: '#openingDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'validUntil',
    //         index: 'validUntil',
    //         rowtype: '#zcertDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'enclosureCount',
    //         index: 'enclosureCount',
    //         formatter:function (value) {
    //             var str='';
    //             if(value)
    //             {
    //                 var tableId = "#table3"
    //                 str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
    //             }
    //             return str;
    //         },
    //         unformat: function (e) {
    //             e=e.replace(/<[^>]+>/g,'');
    //             return e;
    //         }
    //     },{
    //         name:'customerEnclosureVoList',
    //         index:'customerEnclosureVoList',
    //         hidden:true,
    //         formatter:function (value) {
    //             if(value)
    //             {
    //                 return JSON.stringify(value);
    //             }
    //             return JSON.stringify([]);
    //         }
    //     }],
    //     rowNum: 10,
    //     altRows: true/*, //设置为交替行表格,默认为false
    //     ondblClickRow: function (id,dom,obj,index,event) {
    //         console.log('双击行事件',id,dom,obj,index,event);
    //     },
    //     onSelectRow: function (id,dom,obj,index,event) {
    //         console.log('单机行事件', id,dom,obj,index,event);
    //     },*/
    //     //pager:'#table3_page'
    // });
    //批量上传  批准文件
    $("#pzwjUplode").on("click", function () {
        //获取type类型
        var typeList=[];
        var eChoImgList=[];
        var $table=$('#table3');
        var rowData=$table.getRowData();
        var $tr=$table.find("tr").not(":first");
        for(var i=0;i<$tr.length;i++)
        {
            var sel=$tr.eq(i).find("select[name='credentialTypeId'] option:selected");
            typeList.push({
                text:sel.text(),
                value:sel.val()
            });
            console.log(sel.val())
            //添加已存在附件
            if(rowData[i].customerEnclosureVoList.length > 0)
            {
                rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
                for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
                {
                    rowData[i].customerEnclosureVoList[j].type=sel.val();
                }
                eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
            }
        }
        $(this).upLoad({
            typeList:typeList,//格式[{text:xxx,value:xxx}]
            eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            urlBack: function (data) {
                //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
                console.log(data)
                if($.isEmptyObject(data)){
                    $table.find("tr").each(function () {
                        var id=$(this).attr('id');
                        if(id){
                            $table.setRowData(id,{'customerEnclosureVoList':[]});
                            $table.setRowData(id,{'enclosureCount':''});
                        }
                    })
                    return false;
                }
                for(var name in data)
                {
                    var list=data[name];
                    for(var i=0;i<rowData.length;i++)
                    {
                        if(rowData[i].credentialTypeId == name)
                        {
                            var trId=$table.find("tr").eq(i+1).attr('id');
                            $table.setRowData(trId,{'customerEnclosureVoList':list});//存储时只能存字符串，取值时用JSON.parse转换
                            $table.setRowData(trId,{'enclosureCount':(list.length == 0?'':list.length)});
                            break;
                        }
                    }
                }
            }
        });
    });

    //客户合同回显
    // $.ajax({
    //     url:'/proxy-customer/customer/customerCommonData/getCustomerContractAll?correlationId='+id+'&type=4',
    //     dataType:'json',
    //     //async:false,
    //     success:function(data){
    //         if(!data.result) return false;
    //         if(data.result.length){
    //             $(data.result).each(function(contractIndex,ontractItem){
    //                 var contractTypes = $('#contractType').find('input[type=checkbox]');
    //                 $(contractTypes).each(function(index,item){
    //                     if(ontractItem.contractType==$(this).val()){
    //                         $(this).attr("checked",'true');
    //                         if(ontractItem.customerEnclosureVoList && ontractItem.customerEnclosureVoList.length > 0){
    //                             for(var j=0;j<ontractItem.customerEnclosureVoList.length;j++)
    //                             {
    //                                 ontractItem.customerEnclosureVoList[j].type = ontractItem.contractType;
    //                             }
    //                             var arr=JSON.stringify(ontractItem.customerEnclosureVoList);
    //                             var html='<input type="hidden" data-type="'+ontractItem.contractType+'" id="contractType'+ontractItem.contractType+'" value=\''+arr+'\' />';
    //                             $("body").append(html);
    //                         }
    //                     }
    //                 });
    //             });
    //         }
    //     },
    //     error:function(){
    //
    //     }
    // });
    //批量上传 客户合同
    // $("#qtfjUpload").on("click", function() {
    //     //获取type类型
    //     var typeList=[];
    //     var eChoImgList=[];
    //     var checkLen=$("#contractType input[type='checkbox']:checked").length;
    //     if(checkLen < 1){
    //         alert('请选择客户合同类型！');
    //         return false;
    //     }
    //     $("#contractType label").each(function(){
    //         var checked=$(this).find("input[type='checkbox']").get(0).checked;
    //         if(checked)
    //         {
    //             var text=$.trim($(this).text());
    //             var v=$(this).find("input").val();
    //             typeList.push({
    //                 text:text,
    //                 value:v
    //             })
    //             if($("input[id='contractType"+v+"']").length > 0)
    //             {
    //                 var imgArr=$("input[id='contractType"+v+"']").val();
    //                 console.log(imgArr,JSON.parse(imgArr))
    //                 eChoImgList = eChoImgList.concat(JSON.parse(imgArr));
    //             }
    //         }
    //     });
    //     $(this).upLoad({
    //         typeList:typeList,//格式[{text:xxx,value:xxx}]
    //         eChoImgList:eChoImgList,//回显图片集合 eg:[{name:xx,type:xxx,url:xxxx}]
    //         fileParam:{
    //             name:'enclosureName',
    //             url:'url'
    //         },
    //         urlBack: function (data) {
    //             //data  json对象  eg:{1:[{name:xx,type:xxx,url:xxxx}]}
    //             //存放数据
    //             //files
    //             console.log(data)
    //             var html='';
    //             $('input[id^=contractType]').remove();
    //             for(var name in data)
    //             {
    //                 var list=data[name];
    //                 if(list.length > 0){
    //                     var arr=JSON.stringify(list);
    //                     html='<input type="hidden" data-type="'+name+'" id="contractType'+name+'" value=\''+arr+'\' />';
    //                     $("body").append(html);
    //                 }
    //             }
    //         }
    //     });
    // });
    //客户合同 查看附件
    // $("#qtfjView").on("click",function(){
    //     //获取type类型
    //     var imgList=[];
    //     var checkInp=$("#contractType input[type='checkbox']:checked");
    //     if(checkInp.length)
    //     {
    //         for(var i=0;i<checkInp.length;i++)
    //         {
    //             var type=checkInp.eq(i).val();
    //             var inp=$("#contractType"+type);
    //             if(inp.length)
    //             {
    //                 var imgArr=JSON.parse(inp.val());
    //                 imgList=imgList.concat(imgArr);
    //             }
    //         }
    //     }
    //     $.viewImg({
    //         fileParam:{
    //             name:'enclosureName',
    //             url:'url'
    //         },
    //         list:imgList
    //     })
    // });

    $("#busineessScopeAll").attr("disabled","disabled");

    //批准文件新增行 删除行
    // addRow('#pzwjAddRow','#table3');
    // deleRow('#pzwjDelRow','#table3');
    //被委托人新增行 删除行
    /*addRow('#bwtrAddRow','#certTable');
    deleRow('#bwtrDelRow','#certTable');

    //客户委托书新增行 删除行
    addRow('#khwtAddRow','#table2');
    deleRow('#khwtDelRow','#table2');

    //质量保证协议新增行 删除行
    addRow('#zlbzAddRow','#table1');
    deleRow('#zlbzDelRow','#table1');*/

    var townFormat = function(obj,info){
        $(obj).find("select:last").html('');
        if(info['code']%1e4&&info['code']<7e5){	//是否为“区”且不是港澳台地区
            $.ajax({
                url:'/proxy-sysmanage/sysmanage/area/getNexAreaListByPcode?pCode='+info['code'],
                dataType:'json',
                //async:false,
                success:function(town){
                    $(obj).find("select:last").html('');
                    $(obj).find("select:last").append('<option value="">请选择</option>');
                    var arr=town.result;
                    if(arr && arr.length>0)
                    {
                        for(var i=0;i<arr.length;i++)
                        {
                            var code='';
                            var text='';
                            for(name in arr[i]){
                                if(name == 'code')
                                {
                                    code=arr[i][name];
                                }
                                if(name == 'name')
                                {
                                    text=arr[i][name];
                                }
                            }
                            $(obj).find("select:last").append('<option value="'+code+'">'+text+'</option>');
                        }
                        var flag=$(obj).attr("data-flag");
                        if(flag == "true")
                        {
                            var val3=$.trim($(obj).find("select:last").attr("data-value"));
                            if(val3 && val3 != '')
                            {
                                $(obj).find("select:last").val(val3);
                            }
                        }
                        $(obj).attr("data-flag","false");
                    }
                }
            });
        }
    };
    initCitys($('#registerBox'),'registerProvinceId','registerCityId','registerDistrictId');
    initCitys($('#storageBox'),'storageProvinceId','storageCityId','storageDistrictId');
    initCitys($('#billingBox'),'billingProvinceId','billingCityId','billingDistrictId');
    initCitys($('.depotList'),'billingProvinceId','billingCityId','billingDistrictId');
    //初始化四级联动
    function initCitys(obj,provinceField,cityField,areaField){
        obj.attr("data-flag","true").citys({
            dataUrl:'/proxy-sysmanage/sysmanage/area/getAreaExtStreet',
            provinceField:provinceField, //省份字段名
            cityField:cityField,         //城市字段名
            areaField:areaField,         //地区字段名
            onChange:function(obj,info){
                townFormat(obj,info);
            }
        },function(obj){
            $(obj).find("select:last").html('<option value="">请选择</option>');
            var flag=$(obj).attr("data-flag");
            if(flag == "true")
            {
                var dataVal=$.trim($(obj).find("select:first").attr("data-value"));
                if(!dataVal || dataVal == '')return;

                $(obj).find("select:first").val(dataVal);
                $(obj).find("select:first").change();
                var dataVal1=$.trim($(obj).find("select").eq(1).attr("data-value"));
                $(obj).find("select").eq(1).val(dataVal1);
                var val1=$(obj).find("select").eq(1).val();
                if(!val1 || val1 == '')return;

                $(obj).find("select").eq(1).change();
                var dataVal2=$.trim($(obj).find("select").eq(2).attr("data-value"));
                if(!dataVal2 || dataVal2 == '')return;

                $(obj).find("select").eq(2).val(dataVal2);
                var val2=$(obj).find("select").eq(2).val();

                if(!val2 || val2 == '')return;
                $(obj).find("select").eq(2).change();
            }
        });
    }
    //添加发票邮寄地址
    // $("#billAddress").on("click",".addbill", function () {
    //     var html = getHTML({
    //         title:'发票邮寄地址'
    //     });
    //     $("#billAddress").append(html);
    //     var num=$('.depotList').length-1;
    //     initCitys($('.depotList').eq(num),'billingProvinceId','billingCityId','billingDistrictId');
    // });
    // //删除发票邮寄地址
    // $("#billAddress").on("click", ".removeDepot", function () {
    //     $(this).parents(".depotList").remove();
    // });

    function getHTML(option){
        var html='<div class="col-md-6 depotList">\
                    <div class="input-group">\
                        <div class="input-group-addon require">'+option.title+'</div>\
                        <div data-toggle="distpicker" class="form-control form-inline distpicker">\
                            <div class="row">\
                                <div class="form-group col-md-2">\
                                    <select class="form-control {validate:{ required :true}}" name="billingProvinceId"></select>\
                                </div>\
                                <div class="form-group col-md-2">\
                                    <select class="form-control {validate:{ required :true}}" name="billingCityId"></select>\
                                </div>\
                                <div class="form-group col-md-2">\
                                    <select class="form-control {validate:{ required :true}}" name="billingDistrictId"></select>\
                                </div>\
                                <div class="form-group col-md-2">\
                                    <select class="form-control" name="billingStreetId"></select>\
                                </div>\
                                <div class="form-group col-md-3">\
                                    <input type="text" class="form-control text-inp {validate:{ required :true}}" name="billingAddress"/>\
                                </div>\
                                <div class="form-group btn-box col-md-1">\
                                    <button type="button" class="btn removeDepot">\
                                        <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>\
                                    </button>\
                                </div>\
                            </div>\
                        </div>\
                    </div>\
                </div>';
        return html;
    }
    //$("input[name='disabledYn']").attr("disabled","disabled");
});


function getMnemonicCode(str,id){
    $.ajax({
        url:'/proxy-product/product/productFirst/getMnemonicCode?name='+str,
        type:'get',
        dataType:'json',
        async : false,
        success:function(data){
            if(data.code == 0)
            {
                $("#"+id).val(data.result);
            }
        }
    })
}

/**
 * 客户首营 customerFirstApplVo;
 * 批准文件 customerApprovalFileVoList;
 * 质量保证协议 customerQualityAgreementVoList;
 * 客户委托书 customerDelegationFileVoList;
 * 被委托人身份证 customerBeProxyIdentityVoList;
 * 客户合同 customerContractVos;
 * 发票邮寄地址 customerBillingAddressVoList;
 * 结算方式 customerExtendItemVoList;
 */
function getSavedData() {
    //变更申请基础属性
    var customerChangeApprovalRecordVo = $("#customerApplVo").serializeToJSON();
    //数组变成字符串

    if (customerChangeApprovalRecordVo.specialBusinessScope == undefined) {
        customerChangeApprovalRecordVo.specialBusinessScope = "";
    }
    var specialBusinessScope = customerChangeApprovalRecordVo.specialBusinessScope.toString();
    //重新赋值
    customerChangeApprovalRecordVo.specialBusinessScope = specialBusinessScope;
    //切换的质量保证协议
    // var table1 = $("#table1").getRowData();
    // for(var i=0;i<table1.length;i++)
    // {
    //     if(table1[i].customerEnclosureVoList)
    //     {
    //         var customerEnclosureVoList = JSON.parse(table1[i].customerEnclosureVoList);
    //         for(var j=0;j<customerEnclosureVoList.length;j++)
    //         {
    //             customerEnclosureVoList[j].type = 0;
    //         }
    //         table1[i].customerEnclosureVoList=customerEnclosureVoList;
    //     }
    // }
    //被委托人身份证
    var certTable = $("#certTable").getRowData();
    for(var i=0;i<certTable.length;i++)
    {
        if(certTable[i].customerEnclosureVoList)
        {
            var customerEnclosureVoList = JSON.parse(certTable[i].customerEnclosureVoList);
            for(var j=0;j<customerEnclosureVoList.length;j++)
            {
                customerEnclosureVoList[j].type = 0;
            }
            certTable[i].customerEnclosureVoList=customerEnclosureVoList;
        }
    }
    //批准文件
    var table3 = $("#table3").getRowData();
    for(var i=0;i<table3.length;i++){
        if(table3[i].customerEnclosureVoList){
            var customerEnclosureVoList = JSON.parse(table3[i].customerEnclosureVoList);
            for(var j=0;j<customerEnclosureVoList.length;j++){
                customerEnclosureVoList[j].type = 0;
            }
            table3[i].customerEnclosureVoList=customerEnclosureVoList;
        }
        if (table3[i].customerBusinessScopeVoList){
            var selectBusinessScopeVoList=[];
            for (var j=0; j<table3[i].customerBusinessScopeVoList.length;j++){
                var customerBusinessScopeVo = {};
                if (0 != table3[i].customerBusinessScopeVoList[j].id){
                    customerBusinessScopeVo.businessScopeCode = table3[i].customerBusinessScopeVoList[j].id;
                    selectBusinessScopeVoList.push(customerBusinessScopeVo);
                }
            }
            table3[i].customerBusinessScopeVoList = selectBusinessScopeVoList;
        }
    }
    //客户委托书
    var table2 = $("#table2").getRowData();
    for(var i=0;i<table2.length;i++)
    {
        if(table2[i].customerEnclosureVoList)
        {
            var customerEnclosureVoList = JSON.parse(table2[i].customerEnclosureVoList);
            for(var j=0;j<customerEnclosureVoList.length;j++)
            {
                customerEnclosureVoList[j].type = 0;
            }
            table2[i].customerEnclosureVoList=customerEnclosureVoList;
        }
    }
    //客户合同 customerContractVos
    // var customerContractVos=[];
    // $("#contractType input[type='checkbox']").each(function(){
    //     var checked=this.checked;
    //     if(checked)
    //     {
    //         var v=$(this).val();
    //         var json={};
    //         var imgList=[];
    //         var fLen=$("input[id='contractType"+v+"']").length;
    //         json.contractType=v;
    //         if(fLen > 0){
    //             imgList=JSON.parse($("input[id='contractType"+v+"']").val())
    //             json.customerEnclosureVoList=imgList;
    //             for(var j=0;j<json.customerEnclosureVoList.length;j++)
    //             {
    //                 delete json.customerEnclosureVoList[j].type
    //             }
    //         }else{
    //             json.customerEnclosureVoList=[];
    //         }
    //         customerContractVos.push(json);
    //     }
    // });
    //结算方式 customerExtendItemVoList
    var customerExtendItemVoList=[];
    $(".settlement").each(function(){
        var parentInp=$(this).find("input[name='itemValue']");
        if(parentInp.is(":checked"))
        {
            //var parentCode=parentInp.attr('code');
            var json={};
            var parentCodeValue=parentInp.val();
            json['parentCode']=0;
            json['code']=parentCodeValue;
            json['value']=1;
            customerExtendItemVoList.push(json);
            $(this).find(".childCode").each(function(index){
                var json1={};
                var cCode=$(this).find("input[name='code']");
                //var cCodeName=cCode.attr('name');
                var cCodeValue=cCode.val();
                var cValue=$(this).find("input[name='itemValue1']").val();
                //var cValueName=cValue.attr('name');
                //var cValueValue=cValue.val();
                if($.trim(cValue) != '')
                {
                    json1['parentCode']=parentCodeValue;
                    json1['code']=cCodeValue;
                    json1['value']=cValue;
                    customerExtendItemVoList.push(json1);
                }
            });
        }
    });
    var customerBillingAddressVoList = [];
    $('.depotList').each(function(){
        var provinceId = $(this).find('select[name=billingProvinceId] option:selected').val();
        var cityId = $(this).find('select[name=billingCityId] option:selected').val();
        var districtId = $(this).find('select[name=billingDistrictId] option:selected').val();
        var streetId = $(this).find('select[name=billingStreetId] option:selected').val();
        var address = $(this).find('input[name=billingAddress]').val();
        var customerBillingAddressVo = {"billingProvinceId":""+provinceId+"","billingCityId":""+cityId +"","billingDistrictId":""+districtId+"","billingStreetId":""+streetId+"","billingAddress":""+address+""};
        customerBillingAddressVoList.push(customerBillingAddressVo)
    });

    var customerChangeApprovalRecordInfoVo = {
        customerChangeApprovalRecordVo: customerChangeApprovalRecordVo,
        customerApprovalFileVoList: table3,
        // customerQualityAgreementVoList: table1,
        customerDelegationFileVoList: table2,
        customerBeProxyIdentityVoList: certTable,
        customerBillingAddressVoList:customerBillingAddressVoList,
        // customerContractVos:customerContractVos,
        customerExtendItemVoList:customerExtendItemVoList
    }
    return customerChangeApprovalRecordInfoVo;
    /*$.ajax({
        url: "/proxy-customer/customer/change/approval/editor",
        data: JSON.stringify(customerChangeApprovalRecordInfoVo),
        type: "post",
        dataType: 'json',
        contentType: "application/json",

        success: function (data) {
            if(data == 1){
                alert("变更成功！");
            }else{
                alert("变更失败！");
            }
        },
        error: function () {
            alert("内部出错！");
        }
    });*/
}

function initFileSelected() {

}
//经营方式
$("#businessPatternName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:3},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    onSelect: function (result) {
        $('#businessPattern').val(result.data);
    },
    onNoneSelect: function (params, suggestions) {
        console.log(params, suggestions);
        $('#businessPattern').val("");
        $('#businessPatternName').val("");
    }
});
//经营类别
$("#businessCategoryName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:1},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    onSelect: function (result) {
        $('#businessCategory').val(result.data);
    },
    onNoneSelect: function (params, suggestions) {
        console.log(params, suggestions);
        $('#businessCategory').val("");
        $('#businessCategoryName').val("");
    }
});

//客户类别
$("#customerTypeName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:2},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    transformResult: function (response) {
        response.result = response.result.filter(item => item.clientName != '基层医疗机构')
        return response
    },
    onSelect: function (result) {
        $('#customerType').val(result.data);
    },
    onNoneSelect: function (params, suggestions) {
        console.log(params, suggestions);
        $('#customerType').val("");
        $('#customerTypeName').val("");
    }
});

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }

//结算方式
function balanceTypeCheck(){
    $("input[name='itemValue']:not(:checked)").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').hide();
        $(this).parent().parent().parent().children().not('.checkbox').find('input[name="itemValue1"]').val('');
    });
    $("input[name='itemValue']:checked").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').show();
    });
}
/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj, tableId) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(tableId).getRowData(parentId);
    if(data.customerEnclosureVoList)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            list:JSON.parse(data.customerEnclosureVoList)
        })
    }
}

//经营类别回显
if ($('#businessCategory').val()) {
    var businessCategory = $('#businessCategory').val().split(',');
    $("input[name='businessCategoryName']").each(function () {
        for(var x = 0; x < businessCategory.length; x ++){
            if($(this).val() == businessCategory[x]){
                $(this).attr("checked","checked");
            }
        }
    });
}


//不可经营类别回显
if ($('#cannotBusinessCategory').val()) {
    var cannotBusinessCategory = $('#cannotBusinessCategory').val().split(',');
    $("input[name='cannotBusinessCategoryName']").each(function () {
        for(var x = 0; x < cannotBusinessCategory.length; x ++){
            if($(this).val() == cannotBusinessCategory[x]){
                $(this).attr("checked","checked");
            }
        }
    });
}


//批准文件,质量保障协议,客户委托书,被委托人 一键下载
function patchDownload(tableId){
    downloadTableAttachFiles(tableId)
}
function downloadTableAttachFiles(tableId){
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$(tableId);
    var rowData=$table.getRowData();
    console.log(rowData)
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++)
    {
        var sel=$tr.eq(i).find("select[name='credentialTypeId'] option:selected");
        typeList.push({
            text:sel.text(),
            value:sel.val()
        });
        console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0)
        {
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
            {
                rowData[i].customerEnclosureVoList[j].type=sel.val();
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    console.log(eChoImgList)
    // downloadImg("/static/MobileToken.png") //同源图片，下载有效
    const fileUrls = []
    const fileNames = []
    eChoImgList.forEach((item,index)=>{
        if (item.url && item.url.length) {
            fileUrls.push(item.url)
            let fileName = item.enclosureName
            if (!fileName){
                fileName = index + ''
            }
            fileNames.push(fileName)
        }
    })
    downloadImg(fileUrls,fileNames)
}
//客户合同 一键下载
// TODO 待验证
// function qtfjPatchDownload() {
//     let eChoImgList = []
//     var checkInp=$("#contractType input[type='checkbox']:checked");
//     if(checkInp.length)
//     {
//         for(var i=0;i<checkInp.length;i++)
//         {
//             var type=checkInp.eq(i).val();
//             var inp=$("#contractType"+type);
//             if(inp.length)
//             {
//                 var imgArr=JSON.parse(inp.val());
//                 eChoImgList=eChoImgList.concat(imgArr);
//             }
//         }
//     }
//     const fileUrls = []
//     const fileNames = []
//     eChoImgList.forEach((item,index)=>{
//         if (item.url && item.url.length) {
//             fileUrls.push(item.url)
//             let fileName = item.enclosureName
//             if (!fileName){
//                 fileName = index + ''
//             }
//             fileNames.push(fileName)
//         }
//     })
//     downloadImg(fileUrls,fileNames)
// }
function downloadImg(fileUrls,fileNames){
    if (!(fileUrls.length * fileNames.length)){
        utils.dialog({content: '暂无附件下载', quickClose: true, timeout: 2000}).showModal();
        return
    }
    window.open("/proxy-customer/customer/customerFirstAppl/downloadZip?fileUrls="+fileUrls+"&fileNames="+fileNames+"&zipFileName=导出")
}

function oneKeyCopyCerts() {
    const delegationFiles = $('#table2').getRowData()
    // 如果存在客户委托书，
    if (delegationFiles.length){
        const certTable = $('#certTable')
        // 遍历客户委托书，依次复制到被委托人身份证
        delegationFiles.forEach(lastDelegationFile=>{
            const certName = certTable.find('tr:last').find('input[name=customerName]').val()
            const certIdNo =  certTable.find('tr:last').find('input[name=identityNumber]').val()
            const certDate = certTable.find('tr:last').find('input[name=identityValidityDate]').val()
            // 若最后一行中 姓名，证件号码，身份证有效期 均有值，则新增一行进行插入。
            // 若最后一行中 姓名，证件好吗，身份证有效期 的值均为空，则不新增行，直接在最后一行的基础上修改。
            if (certName &&  certIdNo && certDate){
                addRow('#certTable')
            }
            certTable.find('tr:last').find('input[name=customerName]').val(lastDelegationFile.delegationName)
            certTable.find('tr:last').find('input[name=identityNumber]').val(lastDelegationFile.delegationNum)
            certTable.find('tr:last').find('input[name=identityValidityDate]').val(lastDelegationFile.delegationIdentityDate)
        })
    }else {
        utils.dialog({content: '暂无可用证件信息', quickClose: true, timeout: 2000}).showModal();
    }
}
