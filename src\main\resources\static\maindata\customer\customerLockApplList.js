$(function () {
    /*var dialog = parent.dialog.get(window);
    var keyword=dialog.data;*/
    // $("#search_vl").val(keyword);
    $('#X_Tableb').XGrid({
        url:"/proxy-customer/customer/customerLock/queryCustomerLockApplList",
        colNames: ['', '申请日期', '机构', '申请人', '单据编号', '客户编码', '客户名称', '客户类型','业务类型', '申请原因','审核状态','审核完成时间',''],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                hidden:true
            }, {
                name: 'createTime',
                index: 'createTime',
                width:100,
                formatter:function(value){
                    var date=value;
                    if(!value)return false;
                    date=format(value);
                    return date.split(' ')[0];
                }
            }, {
                name: 'orgCode',
                index: 'orgCode',
                width: 250
            }, {
                name: 'applicantName',
                index: 'applicantName',
                width: 150
            }, {
                name: 'applicantNum',
                index: 'applicantNum',
                width: 150
            }, {
                name: 'customerCode',
                index: 'customerCode',
                width: 150
            }, {
                name: 'customerName',
                index: 'customerName',
                width: 250
            }, {
                name: 'customerType',
                index: 'customerType',
                width: 150
            }, {
                name: 'businessType',
                index: 'businessType',
                formatter:function(value){
                    if(value=='0'){
                        return '解锁'
                    }else if(value=='1'){
                        return '锁定'
                    }
                }
            }, {
                name: 'applReason',
                index: 'applReason',
                width: 150
            }, {
                name: 'auditStatus',
                index: 'auditStatus',
                width:120,
                formatter: function (e) {
                    if (e == '1') {
                        return '录入中'
                    } else if (e == '2') {
                        return '审核中'
                    }else if (e == '3') {
                        return '审核通过'
                    }else if (e == '4') {
                        return '审核不通过'
                    }
                }
            }, {
                name: 'auditTime',
                index: 'auditTime',
                formatter: function (e) {
                    if(e){
                        return format(e);
                    }
                },
                width:200
            }, {
                name: 'correlationId',
                index: 'correlationId',
                width: 50,
                hidden:true
            },{
                name: 'applicant',
                index: 'applicant',
                width: 50,
                hidden:true
            },{
                name: 'auditStatus',
                index: 'auditStatus',
                hidden: true
            }
        ],
        rownumbers:true,
        rowNum: 20,
        rowList:[20,50,100],
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (correlationId, dom, obj, index, event) {
            console.log("obj.auditStatus");
            if(obj.auditStatus==1){
                var loginUserId = $("#loginUserId").val();
                if(obj.applicant==loginUserId){
                    var url = '/proxy-customer/customer/customerLock/customerLockDetail?id='+obj.correlationId ;
                    utils.openTabs("customerLockDetail", "客户锁定 ", url)
                }else{
                    utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                }
            }else {
                var url = '/proxy-customer/customer/customerLock/customerLockDetail?id='+obj.correlationId ;
                utils.openTabs("customerLockDetail", "客户锁定详情 ", url)
            }
        },
        onSelectRow: function (id, dom, obj, index, event, obj) {
            console.log('单机行事件', id, dom, obj, index, event, obj);
        },
        pager: '#grid-pager'
    });


    //删除
    $('#delete').on('click', function () {
        var selData = $('#X_Tableb').XGrid("getSeleRow");
        if(!selData.length){
            utils.dialog({title: '提示',content:'请至少选择一行！',timeout:3000}).show();
            return false;
        }
        $.each(selData, function (index, item) {
            if ($('#loginUserId').val() != item.applicant) {
                utils.dialog({title:'温馨提示:',content:'只允许删除自己录入的单据！',timeout: 3000}).show();
                return false;
            }
            if (item.auditStatus == 1) { // 审核中
                utils.dialog({
                    title: '温馨提示:',
                    content: '确定要删除选中的单据吗？',
                    width: 300,
                    height: 50,
                    okValue: '【是】',
                    ok: function () {
                        $.ajax({
                            type: "GET",
                            url: "/proxy-customer/customer/customerLock/updateCustomerLock?applicationNumber="+item.applicantNum ,
                            async: false,
                            success: function (data) {
                                if (data.code==0&&data.result!=null){
                                    console.log(data.result);
                                    console.log("更新 删除成功");
                                    // window.location.reload();
                                    //刷新列表
                                    $('#X_Tableb').XGrid('setGridParam', {
                                        postData: {
                                            "orgCode": $("#orgCode").val(),
                                            "customerCode":$("#customerCode").val(),
                                            "auditStatus":$("#auditStatus").val(),
                                            "applicantNum":$("#applicantNum").val(),
                                            "businessType":$("#businessType").val()
                                        },page:1
                                    }).trigger('reloadGrid');

                                }
                            },
                            error: function (data) {
                                if (data.code==1&&data.result!=null){
                                    utils.dialog({title:'温馨提示:',content:'只允许删除自己录入的单据！',timeout: 3000}).show();
                                    return false;
                                }
                            }
                        });
                    },
                    cancelValue: '【否】',
                    cancel: function () {
                    }
                }).showModal();

            } else {
                utils.dialog({title:'温馨提示:',content:'只允许删除录入中的单据！',timeout: 3000}).show();
                return false;
            }
        });
    });


    /* 查询按钮事件 */
    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "orgCode": $("#orgCode option:selected").val(),
                "customerCode":$("#customerCode").val(),
                "auditStatus":$("#auditStatus").val(),
                "applicantNum":$("#applicantNum").val(),
                "businessType":$("#businessType").val()
            },page:1
        }).trigger('reloadGrid');
    });
});

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }


var orgCode=$("#loginOrgCode").val();
if(orgCode=='001'){
    $.ajax({
        url: "/system/querySubOrgListByOrgCode?orgCode="+orgCode,
        async : false,
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option>';
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].orgCode+'">'+arr[i].orgName+'</option>';
                    }
                }
            }
            $("#orgCode").html(html);
        },
        error:function () {
        }
    });


}
