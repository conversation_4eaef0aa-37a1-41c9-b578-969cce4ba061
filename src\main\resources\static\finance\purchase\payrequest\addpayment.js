$(function () {
    if($('#supplementOrderYn').val() == 1){
        $('#KingdeeBtn').hide()
    }
    //查询所有款项类型
    $('.content').css('height', $(window).height());
    /**
     * 获取款项类型
     */
    var fundTypes = JSON.parse($('#fundTypes').val());
    var ammount = parseFloat($("#applayAmount").val());
    var actualPaymentAmount = parseFloat($("#actualPaymentAmount").val());
    var actualPayAmount = parseFloat($("#actualPayAmount").val());
    console.log(ammount + "amount");
    console.log(actualPaymentAmount + "amount");
    $('#applayAmount').val(ammount ? ammount.formatMoney('2', '', ',', '.') : '0.00');
    $('#actualPaymentAmount').val(actualPaymentAmount ? actualPaymentAmount.formatMoney('2', '', ',', '.') : '0.00');
    $('#actualPayAmount').val(actualPayAmount ? actualPayAmount.formatMoney('2', '', ',', '.') : '0.00');

    function getFundName(code) {
        var result = '';
        if (fundTypes) {
            $.each(fundTypes, function (index, item) {
                if (item.code == code) {
                    result = item.name;
                }
            })
        }
        return result;
    }
    var paymentTypes = JSON.parse($('#paymentTypes').val());
    function getPayName(code) {
        var result = '';
        if (paymentTypes) {
            $.each(paymentTypes, function (index, item) {
                if (item.code == code) {
                    result = item.name;
                }
            })
        }
        return result;
    }

    $('#paymentType').bind('change', function () {
        var text = $(this).find('option:selected').text();
        if (/银行转账/.test(text)) {
            $('.hide-input').show();
        } else {
            $('.hide-input').hide();
        }
    })

    var isPrepayList = [];
    $.each($("#isPrepay option"), function (index, item) {
        isPrepayList.push({value: $(item).val(), name: $(item).text()})
    })

    isPrepayList = isPrepayList.splice(0, isPrepayList.length / 2);

    $('#X_Tablea').XGrid({
        data: [],
        colNames: ['序号', '<span class="text-danger">*</span> 实际付款日期', '<span class="text-danger">*</span> 过账日期', '款项类型', '<span class="text-danger">*</span> 支付方式', '银行名称', '银行账号', '<span class="text-danger">*</span> 付款金额', '备注', '承兑类型', '承兑编号', '承兑名称',"承兑开户行号"],
        colModel: [{
            name: 'id'
        }
            , {
                name: 'realPaymentTime',
                rowtype: '#accountingDate_black'
            }, {
                name: 'accountTime',
                rowtype: '#actualPaymentDate_black'
            }, {
                name: 'isPrepayStr',
                formatter: function (val) {
                    var result;
                    if (!val) result = '';
                    result = getFundName(val);
                    return result;

                },
                unformat: function (val) {
                    var result;
                    if (!val) result = '';
                    $.each(isPrepayList, function (index, item) {
                        if (val == item.name) {
                            result = item.value;
                        }
                    })

                    return result;
                }
            }, {
                name: 'paymentType',
                rowtype: '#modePayment_black',
                rowEvent: function (etype) {
                    var name = $(etype.e.target).find('option:selected').text();
                    var value=$(etype.e.target).find('option:selected').val();
                    if(value=='114'||value=='115'){
                        var sopenBankNum = $("#openBankNum").val();  //获取文本对应账号
                        $(etype.e.target).closest('tr').find('td[row-describedby="sopenBankNum"] input').val(sopenBankNum).attr('title', sopenBankNum);
                    }else{
                        $(etype.e.target).closest('tr').find('td[row-describedby="sopenBankNum"] input').val('').attr('title', '');
                    }
                    if (!name.includes('银行转账')) {
                        $(etype.e.target).closest('tr').find('td[row-describedby="bankName"] select').val('').attr('disabled', '');
                        $(etype.e.target).closest('tr').find('td[row-describedby="bankAccount"] input').val('');
                    } else {
                        let paymentDetail = $('#X_Tablea').XGrid('getRowData');
                        paymentDetail.pop();
                        if(paymentDetail.length > 1){
                            utils.dialog({content: '不允许混合支付', quickClose: true, timeout: 2000}).show();
                            $(etype.e.target).val('');
                            return false;
                        };
                        $(etype.e.target).closest('tr').find('td[row-describedby="bankName"] select').removeAttr('disabled');
                    }
                    if (name.indexOf('承兑') === -1) {
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptType"] select').val('').attr('disabled', '');
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptName"] input').val('').attr('disabled', '');
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptCode"] input').val('').attr('disabled', '');
                    } else {
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptType"] select').removeAttr('disabled');
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptName"] input').removeAttr('disabled');
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptCode"] input').removeAttr('disabled');
                    }
                }
            }, {
                name: 'bankName',
                rowtype: '#bankName_black',
                rowEvent: function (etype) {
                    var name = $(etype.e.target).find('option:selected').text(); //获取选择文本
                    var bankAccount = $(etype.e.target.selectedOptions[0]).data('backaccount'); //获取文本对应账号
                    $(etype.e.target).closest('tr').find('td[row-describedby="bankAccount"] input').val(bankAccount).attr('title', bankAccount);

                }
            }, {
                name: 'bankAccount',
                editable: true
            }, {
                name: 'paymentAccount',
                rowtype: '#money_black',
                rowEvent: function (etype) {
                    totalTable()
                }
                // formatter: function (val) {
                //     return parseFloat(val).formatMoney('2', '', ',' ,'.');
                // },
                // unformat: function (val) {
                //     return val.replace(/,/g ,'');
                // }
            },{
                name: 'remark',
                formatter: function (val) {
                    return transEntity(val, true);
                },
                rowtype: '#text_black'
            },
            {
                name: 'acceptType',
                rowtype: '#modeAcceptType_black',
            },
            {
                name: 'acceptCode',
                rowtype: '#acceptCode_black',

            },
            {
                name: 'acceptName',
                rowtype: '#acceptName_black',

            },
            {
                name: 'sopenBankNum',
                rowtype: '#openBankNum_black',

            }

        ],
        key: 'id',
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        // multiselect: true
        gridComplete: function () {
            setTimeout(function () {
                $("#addBtn").trigger("click");
            }, 200);
        }

    });

    window.isFirst = true;

    //合计
    function totalTable() {

        if ($('#X_Tablea tr:last-child td:first-child').html() == '合计') {
            $('#X_Tablea tr:last-child').remove();
        }
        var data = $('#X_Tablea').XGrid('getRowData');
        var payment = $("#applayAmount").val();
        $('#X_Tablea').XGrid('addRowData', {
            id: '999',
            paymentAccount: totalSum(data, 'paymentAccount')
        });
        $('#X_Tablea tr:last-child td:first-child').html('合计');
        $('#X_Tablea tr:last-child').find('td:eq(1)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(2)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(4)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(5)').html('');
        $('#X_Tablea tr:last-child').find('td:last-child').html('');
        $('#X_Tablea tr:last-child').find('td:eq(7)').html(totalSum(data, 'paymentAccount'));
        $('#X_Tablea tr:last-child').find('td:eq(8)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(9)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(10)').html('');
        window.isFirst = false;

    }

    //合计计算方法
    function totalSum(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat($.trim(item[colName]).replace(/,/, ''));
        })
        return count.toFixed(2) === "NaN" ? 0.00 : count.formatMoney('2', '', ',', '.');
    }


    // 删除
    $('#deleteBtn').bind('click', function () {
        var seleRow = $('#X_Tablea').XGrid('getSeleRow');
        console.log(seleRow)
        if (seleRow) {
            //删除二次确认
            utils.dialog({
                title: "提示",
                content: "确认删除当前选中行？",
                okValue: '确定',
                ok: function () {
                    $('#X_Tablea').XGrid('delRowData', seleRow.id);
                    refreshIndex($('#X_Tablea'));
                    totalTable();
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                timeout: 2000
            }).show()
        }
    })

    //新增
    $('#addBtn').bind('click', function (param) {
        var index = $('#X_Tablea tr').length - 1;
        if (index > 20) {
            utils.dialog({content: '最多添加20行', quickClose: true, timeout: 2000}).show();
            return false;
        }
        let paymentDetail = $('#X_Tablea').XGrid('getRowData');
        paymentDetail.pop();
        let flag = false;
        paymentDetail.forEach(item=>{
            if(item.paymentType == '111'){
                flag = true;
            }
        })
        if(flag){
            utils.dialog({content: '存在支付方式为银行转账，无法新增行', quickClose: true, timeout: 2000}).show();
            return false;
        }
        var data = $('#X_Tablea').XGrid('getRowData');
        var tatalCountStr =  $('#X_Tablea tr:last-child').find('td:eq(20)').html();
        if(!tatalCountStr){
            tatalCountStr ='0.00';
        }
        var tatalCount =tatalCountStr.replace(/,/g, '');
        console.log("tatalCountStr="+tatalCountStr);
        console.log("tatalCount="+tatalCount);
        var temp =0;
        if((actualPayAmount-tatalCount)>0){ 
           temp = actualPayAmount-tatalCount
        }else{
            temp =0;
        }
        $('#X_Tablea').XGrid('addRowData', {
            id: index,
            paymentType: $('#payableType').val() != '111' ? $('#payableType').val() : '',
            isPrepayStr: $('#isPrepay').val(),
            sopenBankNum:$('#openBankNum').val(),
            paymentAccount: temp ? temp : '0.00'
        }, 'before', '999');
        $('#X_Tablea').XGrid('editRow', index);
        var $select = $('#X_Tablea tr:last-child').prev('tr').find('td[row-describedby="paymentType"] select');
        var name = $select.find('option:selected').text();
        if (!name.includes('银行转账')) {
            $select.closest('tr').find('td[row-describedby="bankName"] select').val('').attr('disabled', '');
        } else {
            $select.closest('tr').find('td[row-describedby="bankName"] select').removeAttr('disabled');
        }
        if (name.indexOf('承兑') === -1) {
            $select.closest('tr').find('td[row-describedby="acceptType"] select').val('').attr('disabled', '');
            $select.closest('tr').find('td[row-describedby="acceptName"] input').val('').attr('disabled', '');
            $select.closest('tr').find('td[row-describedby="acceptCode"] input').val('').attr('disabled', '');
        } else {
            $select.closest('tr').find('td[row-describedby="acceptType"] select').removeAttr('disabled');
            $select.closest('tr').find('td[row-describedby="acceptName"] input').removeAttr('disabled');
            $select.closest('tr').find('td[row-describedby="acceptCode"] input').removeAttr('disabled');
        }
        $select.closest('tr').find('td[row-describedby="bankAccount"] input').val('').attr('readonly', '');

        totalTable();

    })

    $('#addBtn').trigger('click');

    //更新序号
    function refreshIndex($Table) {
        var rn = $Table.find('td[row-describedby="id"]');
        $.each(rn, function (index, item) {
            if ($(item).html() !== '合计') {
                $(item).html(index + 1);
            }
        })
    }

    //新增
    $('#saveBtn').bind('click', function () {
        var selRow = $('#X_Tablea').XGrid('getSeleRow');
        $('#X_Tablea').XGrid('saveRow', '3', selRow);
    })
    /**金蝶-付款 */
    $('#KingdeeBtn').bind('click', function () {
        
        $('#KingdeeBtn').attr('disabled',"true");
        let paymentDetail = $('#X_Tablea').XGrid('getRowData');
        paymentDetail.pop();
        var ttx = $("#myform").serializeToJSON();
        ttx.applayAmount = ttx.applayAmount.replace(/,/g, '');
        ttx.actualPayAmount = ttx.actualPayAmount.replace(/,/g, '');
        ttx.actualPaymentAmount = ttx.actualPaymentAmount.replace(/,/g, '');
        ttx.outerBillNo = $("#outerBillNo").val() ? $("#outerBillNo").val() : '';
        let flag = false;
        paymentDetail.forEach(item=>{
            if(item.paymentType == '113' || item.paymentType == '114' || item.paymentType == '115'){
                flag = true;
            }
        })
        if(flag){
            utils.dialog({content: '支付类型为电子承兑不能金蝶付款', quickClose: true, timeout: 2000}).show();
            $('#KingdeeBtn').removeAttr("disabled");
            return false;
        }
        // console.log(paymentDetail);
        if (!validPay(paymentDetail, ttx)){
            $('#KingdeeBtn').removeAttr("disabled");
            return false;
        }
        $('#KingdeeBtn').attr('disabled', '');
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/paymentKingdee",
            data: {
                "paymentDetailVo": JSON.stringify(paymentDetail),
                "paymentVo": JSON.stringify(ttx)
            },
            dataType: 'json',
            cache: false
        }).done(function (data) {
            if (data.code == 0) {
                utils.dialog({
                    content: data.result,
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                setTimeout(function () {
                    var dialog = parent.dialog.get(window);
                    dialog.close();
                }, 500);
            } else {
                $('#KingdeeBtn').removeAttr('disabled');
                utils.dialog({
                    content: data.result, quickClose: true,
                    timeout: 3000
                }).showModal();
            }
        });
    })
    // 确认付款
    var dialog = parent.dialog.get(window);
    $('#submitBtn').bind('click', function () {
        $('#submitBtn').attr('disabled',"true");
        var tempArr = [],
            paymentDetail = $('#X_Tablea').XGrid('getRowData');
        paymentDetail.pop();
        var ttx = $("#myform").serializeToJSON();
        ttx.applayAmount = ttx.applayAmount.replace(/,/g, '');
        ttx.actualPayAmount = ttx.actualPayAmount.replace(/,/g, '');
        ttx.actualPaymentAmount = ttx.actualPaymentAmount.replace(/,/g, '');
        ttx.outerBillNo = $("#outerBillNo").val() ? $("#outerBillNo").val() : '';
        // console.log(paymentDetail);
        if (!validPay(paymentDetail, ttx)){
            $('#submitBtn').removeAttr("disabled");
            return false;
        }

        $('submitBtn').attr('disabled', '');
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/savePayment",
            data: {
                "paymentDetailVo": JSON.stringify(paymentDetail),
                "paymentVo": JSON.stringify(ttx)
            },
            dataType: 'json',
            cache: false
        }).done(function (data) {
            if (data.code == 0) {
                utils.dialog({
                    content: data.result,
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                setTimeout(function () {
                    var dialog = parent.dialog.get(window);
                    dialog.close();
                }, 500);
            } else {
                $('submitBtn').removeAttr('disabled');
                utils.dialog({
                    content: data.result, quickClose: true,
                    timeout: 3000
                }).showModal();
            }
        });

    })

    //校验数据
    function validPay(paymentDetail, ttx) {
        if (!paymentDetail.length) {
            utils.dialog({content: '请添加付款明细行', quickClose: true, timeout: 2000}).show();
            return false;
        }

        if ($('#actualPayAmount').val() !== $('#X_Tablea tr:last-child td:eq(7)').html()) {
            utils.dialog({content: '本次实付金额不等于合计付款金额！', quickClose: true, timeout: 2000}).show();
            return false;
        }

        var flag = true;
        $.each(paymentDetail, function (index, item) {

            if (item.realPaymentTime == '') {
                utils.dialog({content: '实际付款日期必选！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }

            if (item.accountTime == '') {
                utils.dialog({content: '过账日期必选！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }
            if (!moment(item.accountTime).isAfter($('#period').val())) {
                if(item.accountTime == $('#period').val()){
                    flag = true;
                }else{
                    utils.dialog({
                        content: '过账日期不能小于当前会计期间',
                        quickClose: true,
                        timeout: 2000
                    }).show();
                    flag = false;
                    return false;
                }

            }


            if (item.paymentType == '') {
                utils.dialog({content: '支付方式必选！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }else{
                var payName = getPayName(item.paymentType);
                if (payName.includes('银行转账')){
                    if (item.bankName == '') {
                        utils.dialog({content: '银行名称必选！', quickClose: true, timeout: 2000}).show();
                        flag = false;
                        return false;
                    }
                }
            }

            let payTypeName = getPayName(item.paymentType);
            if(payTypeName.indexOf('承兑') !== -1) {
                if(item.acceptType === ''||item.acceptName === ''||item.acceptCode === ''){
                    utils.dialog({content: '如果支付方式为承兑，承兑类型、承兑编号和承兑名称不可为空！', quickClose: true, timeout: 2000}).show();
                        flag = false;
                        return false;
                }
            }

            // if (item.paymentAccount == '' || item.paymentAccount == 0) {
            if (item.paymentAccount == '' || item.paymentAccount < 0) {
                utils.dialog({content: '付款金额必填！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }



        })


        return flag;
    }

    // getPayableType($('[name=supplierNo]').val(), '.payableType')

})
// 支付方式 查詢回显
function getPayableType(supplierCode,tagNode) {
    $.ajax({
        url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierPayType?supplierCode='+ supplierCode,
        type: 'get',
        success: (res) => {
            console.log('res', res);
            let _Str = '<option value="">请选择</option>'
            if (res.code == 0) {
                res.result.payTypeVOList.forEach(item => _Str += `<option value="${item.code}">${item.name}</option>`)
            }
            $(tagNode).empty().html(_Str)
        },
        error: (err) => {
            console.log('err', err);
        }
    })
}
//  支付方式切换
function payableTypeChange(el) {
    $('#accept_transfer_Rebate').css('display', ($(el).val() == '112' || $(el).val() == '114' || $(el).val() == '115') ? 'block' : 'none')
}

// 承兑类型切换
function acceptTypeChange(el) {
    let thisVal = $(el).val()
    if(thisVal != '0'){
        let supplierName = $('input[name="supplierName"]').val();
        if(supplierName === ''){
            utils.dialog({content: '请先选择供应商', quickClose: true, timeout: 2000}).show();
            $(el).val('');
            return
        }
        $(el).parents('tr').find('[row-describedby="acceptName"] input').val(supplierName);
        $(el).parents('tr').find('[row-describedby="acceptName"] input').prop('disabled',true);
    }else{
        $(el).parents('tr').find('[row-describedby="acceptName"] input').val('');
        $(el).parents('tr').find('[row-describedby="acceptName"] input').prop('disabled',false);
    }
}