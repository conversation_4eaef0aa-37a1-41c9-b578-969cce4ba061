.bootstrap-tagsinput {
  background-color: #fff;
  display: inline-block;
  color: #555;
  vertical-align: middle;
  border-radius: 4px;
  max-width: 100%;
  line-height: 22px;
  cursor: text;
  margin-top:-4px;
  padding-right:12px;
}
.bootstrap-tagsinput .addTagBtn{
  outline:none;
  background:#fff;
  border:none;
  position: absolute;
  height:20px;
  width: 20px;
  right:4px;top:6px;
  transition:.5s all;
  color:#666;
}
.bootstrap-tagsinput .addTagBtn:hover{
  color:#333;
}
.bootstrap-tagsinput input {
  border:none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
  padding: 0 6px;
  margin: 0;
  margin-top:3px;
  /*width: auto;*/
  max-width: inherit;
  position: relative;
  z-index:2;
  width: 90px;
}
.bootstrap-tagsinput.form-control input::-moz-placeholder {
  color: #777;
  opacity: 1;
}
.bootstrap-tagsinput.form-control input:-ms-input-placeholder {
  color: #777;
}
.bootstrap-tagsinput.form-control input::-webkit-input-placeholder {
  color: #777;
}
.bootstrap-tagsinput input:focus {
  box-shadow: none;
}
.bootstrap-tagsinput .tag {
  margin-right: 2px;
  color: #999999;
}
.bootstrap-tagsinput .tag [data-role="remove"] {
  margin-left: 8px;
  cursor: pointer;
}
.bootstrap-tagsinput .tag [data-role="remove"]:after {
  content: "x";
  padding: 0px 2px;
}
.bootstrap-tagsinput .tag [data-role="remove"]:hover {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05);
}
.bootstrap-tagsinput .tag [data-role="remove"]:hover:active {
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}


.bootstrap-tagsinput .label-info{
  background-color: #fff !important;
  border: 1px solid #e2e2e2;
  border-radius: 20px;
  white-space: nowrap;
  display:inline-block;
  text-overflow:ellipsis;
  overflow:hidden;
  max-width:100%;
  margin-bottom:-5px;
}