function initDate(param) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    var month = new_date.getMonth() + 1;
    var date = new_date.getDate();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    //console.log(new_date, year, month, date);
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#uringTime').val(year + '-' + month +'-'+ date);
    $('#endTime').val(year + '-' + month + '-' + date);
}


$(function () {
    initDate();
  var colName = ['养护记录单据编号','养护计划单类型', '日期', '机构名称', '部门名称', '养护员', '养护类别', '在库养护状态', '备注'];
  var colModel = [{
    name: 'checkPlanCode',
    index: 'checkPlanCode',
    width:'180'
  },{
      name: 'isZyypName',
      index: 'isZyypName'
  }, {
    name: 'checkTimes',
    index: 'checkTimes',
      formatter:function (e){
        console.log('ee',e)
          if (e != null && e !="") {
              return ToolUtil.dateFormat(e, 'yyyy-MM-dd ');
          } else {
              return e;
          }
      }
  }, {
    name: 'orgName',
    index: 'orgName'
  }, {
    name: 'depaName',
    index: 'depaName'
  }, {
    name: 'userName',
    index: 'userName'
  }, {
    name: 'checkType',
    index: 'checkType',
    formatter: function (e) {
      if (e == '1') {
          return '重点养护'
      } else if (e == '2') {
          return '普通养护'
      }else if (e == '3') {
          return '临时养护'
      }
    },
    unformat:function (e) {
      var val = ''
      if (e == '重点养护') {
          val =  '1'
      } else if (e == '普通养护') {
          val =  '2'
      }else if (e == '临时养护') {
          val =  '3'
      }
      return val
    }
  }, {
    name: 'status',
    index: 'status',
      formatter: function (e) {
          if (e == '1') {
              return '完成'
          } else if (e == '2') {
              return '编辑中'
          }
      },
      unformat:function (e) {
        var val = ''
          if (e == '完成') {
              val =  '1'
          } else if (e == '编辑中') {
              val =  '2'
          }
          return val
      }
  }, {
    name: 'rate',
    index: 'rate'
  }, {
      name: 'isZyyp',
      index: 'isZyyp',
      hidden:true,
      higrid:true
  }];
  $('#table_a').XGrid({
      //data: grid_dataY,
    url:'/proxy-gsp/gsp/stockInCheck/getstockInCheck',
      postData:{
        orgCode:$("#val_orgCode_hidden").val(),
          startTime:$("#uringTime").val(),
      },
    colNames: colName,
    colModel: colModel,
    rownumbers: true,
    key: 'checkPlanCode',
    rowNum: 20,
    rowList:[20,50,100],
    altRows: true, //设置为交替行表格,默认为false
    ondblClickRow: function (id, dom, obj, index ) {
      //跳转详情页
        // 状态
        //养护单据编号
        data(obj.status,obj.checkPlanCode,obj.checkType,obj.isZyyp,obj.checkTimes);
    },
    gridComplete: function () {},
    onSelectRow: function (id, dom, obj, index, event) {
      //选中事件
      //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
      //console.log(id, dom, obj, index, event)
    },
      pager: '#grid_pager_a'
  });
    $.ajax({
        url: "/proxy-gsp/gsp/stockInCheck/queryOrg",
        type: "get",
        success: function (result) {
            $("#orgCodeName").val(JSON.stringify(result.result));
            var  options  =""
            for (var i =0;i<result.result.length;i++) {
                options+=" <option value="+result.result[i].orgCode+" >"+result.result[i].orgName+"</option>"
            }
            $("#orgCode").html(options);
        }

    })

    if($("#orgvalue").val()!=null){
        console.log($("#orgvalue").val())
    }



    // 筛选列
  $("#set_tb_rows").click(function () {
    //获取当前显示表格
    var tableId = $('#nav_content .active .XGridBody table').attr('id');
    $('#' + tableId).XGrid('filterTableHead');
  })

  /* 查询 */
  $('#searchBtn').on('click', function (e) {
    //获取form数据
    var data = $('#form_a').serializeToJSON();
    //console.log(data);
    //更新表格数据
    $('#table_a').XGrid('setGridParam', {
        url:'/proxy-gsp/gsp/stockInCheck/getstockInCheck',
        postData: {
            "orgCode": $("#val_orgCode_hidden").val(),
            "startTime":$("#uringTime").val(),
            "endTime":$("#endTime").val(),
            "status":$("#type").val()
        },
      colNames: colName,
      colModel: colModel,
        rowNum: 20,
        rowList:[20,50,100],
      altRows: true, //设置为交替行表格,默认为false
      pager: '#grid_pager_a',


    }).trigger("reloadGrid");
  });

  /* 新增 */
  $('#addRowData').on('click', function () {
      data();
  //&+'orgcode='+orgcode
  });


  function  data(status,checkPlanCode,checkType,isZyyp,checkTimes){
      var time = $('#uringTime').val();
      var departName=$('#departName').val();
      //var orgcode=$('#orgCode option:selected').text();
      var orgcode = $('#val_orgCode_hidden').val();
      var orgName = '';
      var orgList = JSON.parse($("#orgCodeName").val());
      orgList.forEach(function (item,index) {
          if(item.orgCode==orgcode){
              orgName = item.orgName
          }
      });
      var url = '/proxy-gsp/gsp/stockInCheck/toAdd?time='+time+'&orgcode='+encodeURIComponent(orgcode)+'&departName='+encodeURIComponent(departName)+'&checkTimes='+encodeURIComponent(checkTimes);
      if(status && checkPlanCode && checkType){
          url += '&status='+encodeURIComponent(status) +'&checkPlanCode='+encodeURIComponent(checkPlanCode) +'&checkType='+encodeURIComponent(checkType)+'&isZyyp='+encodeURIComponent(isZyyp)
      }
      window.location = url;
  }



    /* 导出当前机构的 */

    $('#exportRowData').on('click', function () {
    	utils.exportAstrictHandle('table_a', Number($('#totalPageNum').text()), 1).then( () => {
            return false;
        }).catch( () => {
        	window.location = '/proxy-gsp/gsp/stockInCheck/getstocklist?orgCode='+$("#val_orgCode_hidden").val()+'&startTime='+$("#uringTime").val()
        	+'&endTime='+$("#endTime").val()+'&status='+$("#type").val();
        }); 

    });
})