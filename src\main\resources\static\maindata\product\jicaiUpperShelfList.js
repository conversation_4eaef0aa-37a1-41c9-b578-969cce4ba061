////其他相关功能修改 3. 集团集采上架管理
$(function () {
    $('#X_Tableb').XGrid({
        url: "/proxy-product/product/upperShelf/queryProductUpperShelf",
        postData: {"applicationType": 1},
        colNames: ['单据编号', '申请日期', '申请人', '机构', '提交日期', '审核日期', '审核状态', 'EC审核状态', 'EC上架时间', 'EC未上架原因', '业务类型', '商品编号', '原商品编码', '小包装条码', '商品名称', '通用名', '商品规格', '生产厂家', '单位', '申请商品定位', '品类策略审核商品定位',
            '是否专供', 'APP销售价是否维价', '终端零售价是否维价', '应季类型', '建议终端售价', '品类策略审核建议终端售价', '建议APP售价', '底价', '建议智鹿总部采购价', '价格管理部审核智鹿总部采购价', '建议连锁APP售价', '价格管理部审核连锁APP售价','建议荷叶大药房采购价','价格管理部审核荷叶大药房采购价', 
            '连锁票面毛利率','价格管理部最终定价', '票面毛利率', '终端毛利率', '备注', '采购员', '销售状态', '库存数量', '最后含税进价', '最后入库时间', '最后供应商', '商品产地', '剂型', '处方分类', '批准文号',
            '存储条件', '一级分类', '二级分类', '三级分类', '四级分类', '申请人ID', '记录明细ID'],
        colModel: [
            {
                name: 'applicationCode',
                index: 'applicationCode',
                width: 150
            },
            {
                name: 'applicationTime',
                index: 'applicationTime',
                width: 180,
                formatter: function (value) {
                    return new Date(value).Format('yyyy-MM-dd hh:mm:ss');
                },
            },
            {
                name: 'applicantName',
                index: 'applicantName',
                width: 80
            },
            {
                name: 'productOrgName',
                index: 'productOrgName',
                width: 210
            },
            {
                name: 'submitTime',
                index: 'submitTime',
                width: 180,
                formatter: function (value) {
                    if (value != null) {
                        return new Date(value).Format('yyyy-MM-dd hh:mm:ss');
                    }
                },
            },
            {
                name: 'auditTime',
                index: 'auditTime',
                width: 180,
                formatter: function (value) {
                    if (value != null) {
                        return new Date(value).Format('yyyy-MM-dd hh:mm:ss');
                    }
                },
            },
            {
                name: 'statuesStr',
                index: 'statuesStr',
                width: 100,
            }, {
                name: 'ecAuditStatusStr',
                index: 'ecAuditStatusStr',
                width: 120,
            }, {
                name: 'ecUpperShelfTime',
                index: 'ecUpperShelfTime',
                width: 180,
                formatter: function (value) {
                    if (value != null) {
                        return new Date(value).Format('yyyy-MM-dd hh:mm:ss');
                    }
                },
            }, {
                name: 'ecReason',
                index: 'ecReason',
                width: 200,
            }, {
                name: 'channelId',
                index: 'channelId',
                rowType: 'channelId',
                width: 100
            }, {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'oldProductCode',
                index: 'oldProductCode',
                width: 140
            }, {
                name: 'smallPackageBarCode',
                index: 'smallPackageBarCode',
                width: 130
            }, {
                name: 'productName',
                index: 'productName',
                width: 110
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 110
            }, {
                name: 'specifications',
                index: 'specifications',
                width: 140
            }, {
                name: 'manufacturerVal',
                index: 'manufacturerVal',
                width: 240
            }, {
                name: 'packingUnitVal',
                index: 'packingUnitVal',
                width: 80
            }, {
                name: 'commodityPositionVal',
                index: 'commodityPositionVal',
                width: 120
            }, {
                name: 'auditCommodityPositionVal',
                index: 'auditCommodityPositionVal',
                width: 170
            }, {
                name: 'exclusiveYn',
                index: 'exclusiveYn',
                hidden: true,
                hidegrid: true,
                width: 80
            }, {
                name: 'dimensionSalesPriceYnVal',
                index: 'dimensionSalesPriceYnVal',
                width: 180
            }, {
                name: 'dimensionTerminalPriceYnVal',
                index: 'dimensionTerminalPriceYnVal',
                width: 150
            }, {
                name: 'seasonalVarietiesVal',
                index: 'seasonalVarietiesVal',
                width: 100
            }, {
                name: 'terminalPrice',
                index: 'terminalPrice',
                width: 130
            }, {
                name: 'auditTerminalPrice',
                index: 'auditTerminalPrice',
                width: 220
            }, {
                name: 'appPrice',
                index: 'appPrice',
                width: 110
            }, {
                name: 'floorPrice',
                index: 'floorPrice',
                width: 80
            }, {
                name: 'zhiluPrice',
                index: 'zhiluPrice',
                width: 150
            }, {
                name: 'auditZhiluPrice',
                index: 'auditZhiluPrice',
                width: 220
            }, {
                name: 'chainGuidePrice',
                index: 'chainGuidePrice',
                width: 140
            }, {
                name: 'auditChainGuidePrice',
                index: 'auditChainGuidePrice',
                width: 220
            },{
                name: 'heyePrice',
                index: 'heyePrice',
                width:180
            }, {
                name: 'auditHeyePrice',
                index: 'auditHeyePrice',
                width:240
            }, {
                name: 'chainParGrossMargin',
                index: 'chainParGrossMargin',
                width: 130
            }, {
                name: 'auditAppPrice',
                index: 'auditAppPrice',
                width: 150
            }, {
                name: 'parGrossMargin',
                index: 'parGrossMargin',
                width: 120
            }, {
                name: 'terminalGrossMargin',
                index: 'terminalGrossMargin',
                width: 120
            }, {
                name: 'remarks',
                index: 'remarks',
                width: 180
            }, {
                name: 'buyerVal',
                index: 'buyerVal',
                width: 80
            }, {
                name: 'ecProductStatus',
                index: 'ecProductStatus',
                width: 80
            }, {
                name: 'inventoryQuantity',
                index: 'inventoryQuantity',
                width: 80
            }, {
                name: 'lastIncludingTaxPurchasePrice',
                index: 'lastIncludingTaxPurchasePrice',
                width: 110
            }, {
                name: 'lastStorageTime',
                index: 'lastStorageTime',
                width: 200,
                formatter: function (value) {
                    if (value) {
                        return new Date(value).Format('yyyy-MM-dd');
                    }
                }
            }, {
                name: 'lastSupplier',
                index: 'lastSupplier',
                width: 220
            }, {
                name: 'producingArea',
                index: 'producingArea',
                width: 80
            }, {
                name: 'dosageFormVal',
                index: 'dosageFormVal',
                width: 100
            }, {
                name: 'prescriptionClassificationVal',
                index: 'prescriptionClassificationVal',
                width: 100
            }, {
                name: 'approvalNumber',
                index: 'approvalNumber',
                width: 160
            }, {
                name: 'storageConditionsVal',
                index: 'storageConditionsVal',
                width: 80
            }, {
                name: 'firstCategoryVal',
                index: 'firstCategoryVal',
                width: 100
            }, {
                name: 'secondCategoryVal',
                index: 'secondCategoryVal',
                width: 120
            }, {
                name: 'thirdCategoryVal',
                index: 'thirdCategoryVal',
                width: 120
            }, {
                name: 'fourCategoryVal',
                index: 'fourCategoryVal',
                width: 100
            }, {
                name: 'applicantId',
                index: 'applicantId',
                hidegrid: true,
                hidden: true
            }, {
                name: 'detailId',
                index: 'detailId',
                hidegrid: true,
                hidden: true
            },
        ],
        rowNum: 20,
        rowList: [20, 50, 100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function (id, dom, obj, index, event) {
            utils.openTabs("productUpperShelfDetail", "集采商品上架申请详情", "/proxy-product/product/upperShelf/toUpperShelfDetail?id=" + id);
        },
        rownumbers: true,
    });

    //设置显示列
    $("#setRow").click(function () {
        $('#X_Tableb').XGrid('filterTableHead', 1000);
    });

    $("#SearchBtn").on("click", function () {
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "productOrgCode":$("#productOrgCode").val(),
                "applicantId": $("#id").val(),
                "statues": $("#statues").val(),
                "flag": $("#flag").val(),
                "beginTime": $("#beginTime").val(),
                "endTime": $("#endTime").val(),
                "channelIdList": $("#channelId").val(),
                "productCode": $("#productCode").val(),
                "ecAuditStatus": $("#ecAuditStatus").val(),
                "applicationType": 1
            }, page: 1
        }).trigger('reloadGrid');
    });
    // 导出
    $('#ExportBtn').on('click', function () {
        utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text())).then(() => {
            return false;
        }).catch(() => {
            //原始处理逻辑代码
            utils.exportTableData('X_Tableb', 'searchForm', '/proxy-product/product/upperShelf/exportUpperList')
        });
    })
    //搜索申请人
    valAutocomplete("/proxy-product/product/productFirst/queryBuyerList", {paramName: 'userNames'}, "id", {
        data: "id",
        value: "userName"
    });

    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('0').then(res => {
            console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });

});

Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url, param, obj, resParam, select, noneSelect) {
    var resParam = Object.assign({'list': 'result'}, resParam);
    $("#" + obj + "Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params: param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader: resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            $("#" + obj).val(result.data);
            select && select(result)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            $("#" + obj).val("");
            $("#" + obj + "Val").val("");
            noneSelect && noneSelect();
        }
    });
}

function addHtmlA(arry) {
    if (!$('#setCol')[0]) {
        var s = '<div id="setCol" style="display: none;">' +
            '    <div class="row" id="checkRow">';

        for (var i = 0; i < arry.length; i++) {
            //   alert(arry[i]);
            s += '<div class="col-md-3">' +
                '            <div class="checkbox">' +
                '                <label>' +
                '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                '                </label>' +
                '            </div>' +
                '        </div>';

        }

        s += '</div></div>';
        $("body").append(s);
    }
}

//发送POST请求跳转到指定页面
function httpPost(URL, PARAMS) {
    var temp = document.createElement("form");
    temp.action = URL;
    temp.method = "post";
    temp.style.display = "none";

    for (var x in PARAMS) {
        var opt = document.createElement("textarea");
        opt.name = x;
        opt.value = PARAMS[x];
        temp.appendChild(opt);
    }
    document.body.appendChild(temp);
    temp.submit();

    return temp;
}