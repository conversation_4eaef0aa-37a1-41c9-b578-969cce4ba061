<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <link rel="stylesheet" href="css/process.css">
    <script src="../jquery/3.3.1/jquery.js"></script>
</head>

<body>
    <div id="box"></div>
</body>
<script src="js/process-jquery.js"></script>
<script>
    $('#box').process({
        "node1": {
            "id": 1,
            "nodeName": "【采购员】提交申请",
            "status":0,
            "records": [{
                "taskId": "123",
                "handler": "张三",
                "userId": 2222,
                "detail": "审核通过",
                "rejectTo": "",
                "status": 0,
                "createTime": "2018-01-01 18:00:00"
            }]
        },
        "node2": {
            "id": 2,
            "nodeName": "【商品中心】审批",
            "status":1,
            "records": [{
                "taskId": "123",
                "handler": "张三",
                "userId": 2222,
                "detail": "审核通过",
                "rejectTo": "",
                "status": 0,
                "createTime": "2018-01-01 18:00:00"
            }, {
                "taskId": "124",
                "handler": "张三",
                "userId": 2222,
                "detail": "驳回",
                "rejectTo": "",
                "status": 1,
                "createTime": "2018-01-01 19:00:00"
            },{
                "taskId": "123",
                "handler": "张三",
                "userId": 2222,
                "detail": "审核通过",
                "rejectTo": "",
                "status": 0,
                "createTime": "2018-01-01 18:00:00"
            }, {
                "taskId": "124",
                "handler": "张三",
                "userId": 2222,
                "detail": "驳回",
                "rejectTo": "",
                "status": 1,
                "createTime": "2018-01-01 19:00:00"
            }]
        },
        "node3": {
            "id": 1,
            "nodeName": "【总经办】审批",
            "status":2,
            "records": [{
                "taskId": "123",
                "handler": "张三",
                "userId": 2222,
                "detail": "审核通过",
                "rejectTo": "",
                "status": 0,
                "createTime": "2018-01-01 18:00:00"
            }]
        },
        "node4": {
            "id": 1,
            "nodeName": "【采购员】提交申请",
            "status":3,
            "records": [{
                "taskId": "123",
                "handler": "张三",
                "userId": 2222,
                "detail": "审核通过",
                "rejectTo": "",
                "status": 0,
                "createTime": "2018-01-01 18:00:00"
            }, {
                "taskId": "124",
                "handler": "张三",
                "userId": 2222,
                "detail": "驳回",
                "rejectTo": 1,
                "status": 1,
                "createTime": "2018-01-01 19:00:00"
            }]
        },
        "node5": {
            "id": 2,
            "nodeName": "【商品中心】审批",
            "status":1,
            "records": [{
                "taskId": "123",
                "handler": "张三",
                "userId": 2222,
                "detail": "审核通过",
                "rejectTo": "",
                "status": 0,
                "createTime": "2018-01-01 18:00:00"
            }, {
                "taskId": "124",
                "handler": "张三",
                "userId": 2222,
                "detail": "驳回",
                "rejectTo": "",
                "status": 1,
                "createTime": "2018-01-01 19:00:00"
            }]
        },
        "node6": {
            "id": 1,
            "nodeName": "【总经办】审批",
            "status":0,
            "records": [{
                "taskId": "123",
                "handler": "张三",
                "userId": 2222,
                "detail": "审核通过",
                "rejectTo": "",
                "status": 0,
                "createTime": "2018-01-01 18:00:00"
            }, {
                "taskId": "124",
                "userId": 2222,
                "handler": "王五",
                "detail": "驳回",
                "rejectTo": "2",
                "status": 1,
                "createTime": "2018-01-01 19:00:00"
            }]
        },
        "node7": {
            "id": 1,
            "nodeName": "【总经办】审批",
            "status":0,
            "records": [{
                "taskId": "123",
                "handler": "张三",
                "userId": 2222,
                "detail": "审核通过",
                "rejectTo": "",
                "status": 0,
                "createTime": "2018-01-01 18:00:00"
            }, {
                "taskId": "124",
                "userId": 2222,
                "handler": "王五",
                "detail": "驳回",
                "rejectTo": "2",
                "status": 1,
                "createTime": "2018-01-01 19:00:00"
            }]
        }
    })
</script>

</html>