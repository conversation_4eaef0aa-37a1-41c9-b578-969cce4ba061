/**
 * Created by 沙漠里的红裤头 on 2019/6/24
 */
$('div[fold=head]').fold({
    sub: 'sub'
});
//显示流程图
var key = $("#key").val();
initApprovalFlowChart(key);
/* 表格加载 */
$('#table').XGrid({
    colNames: ['', '供应商编码', '供应商名称', '供应商类别', '是否锁定', '业务类型', '<i style="color:red;margin-right:4px;">*</i>申请原因'],
    colModel: [
        {
            name: 'id', //与反回的json数据中key值对应
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden:true
        }, {
            name: 'supplierCode',
            editable:''
        }, {
            name: 'supplierName',
        }, {
            name: 'supplierTypeName',
        }, {
            name: 'isLock',
            formatter: function (e) {
                if (e == '0') {
                    return '否';
                } else if (e == '1') {
                    return '是';
                } else {
                    return "";
                }
            },unformat: function (e) {
                if (e == '否') {
                    return '0';
                } else if (e == '是') {
                    return '1';
                } else {
                    return "";
                }
            }
        }, {
            name: 'isLock_a',
            formatter: function (e) {
                if (e == '0') {
                    return '锁定';
                } else if (e == '1') {
                    return '解锁';
                } else {
                    return "";
                }
            },unformat: function (e) {
                if (e == '锁定') {
                    return '0';
                } else if (e == '解锁') {
                    return '1';
                } else {
                    return "";
                }
            }
        },{
            name: 'appReason',
            rowtype: '#appContentT'
        }/*,{
            name:'supplierTypeId',
            hidden:true
        }*/,{
            name:'supplierType',
            hidden:true
        },{
            name: 'orgCode',
            index: 'orgCode',
            hidden:true
        },{
            name: 'supplierOrganId',
            hidden:true
        },{
            name: 'keyId',
            hidden:true
        }
    ],
    rowNum: 999,
    rownumbers: true,//是否展示序号
    altRows: true
});

/* 新增行 事件*/
var selArr = [];
$("#addRow").on("click",function(){
	var lockPageFlag = $("#lockPageFlag").val();
    selArr = $("#table").getRowData();
    utils.dialog({
        url: '/proxy-supplier/supplier/supplierLockApprovalRecord/toSearchList/'+lockPageFlag,//弹框页面请求地址
        title: '搜索供应商',
        width: 1000,
        data:{
            initDataArr:selArr
        },
        height: 600,
        onclose:function(){
//            if(this.returnValue){
//                var data=this.returnValue;
//                $("#table").XGrid('clearGridData');
//                for(var i = 0 ; i < data.length; i++ ){
//                    $('#table').XGrid("addRowData",data[i]);
//                }
//            }
            if(this.returnValue){
                var data=this.returnValue;
                $("#table").XGrid('clearGridData');
                for(var i = 0 ; i < data.length; i++ ){
                    if(!fidInArr(data[i].keyId)){
                        $('#table').XGrid("addRowData",data[i]);
                    }
                }
            }

        }
    }).showModal();
});
//数组中查找id
//function fidInArr(arr,supplierOrganId){
//    for(var i=0;i<arr.length;i++){
//        if(arr[i].supplierOrganId == supplierOrganId){
//            return true;
//        }
//    }
//    return false;
//}
//数组中查找id
function fidInArr(id){
    let arr = $("#table").getRowData()
    for(var i=0;i<arr.length;i++){
        if(arr[i].id == id){
            return true;
        }
    }
    return false;
}
/* 删除行 事件*/
$("#delRow").on("click",function(){
    var selObj = $('#table').XGrid("getSeleRow");
    if(selObj){
        $('#table').XGrid("delRowData",selObj.id);
    }else{
        utils.dialog({"content":"请选择删除行！","timeout":2000}).show();
    }
});


function save(auditStatus) {
	var lockPageFlag = $("#lockPageFlag").val();
    var sexamine = $("#examine").val()
    var supplierLockApprovalRecordVO = $("#baseApproval").serializeToJSON()
    var table = $("#table").getRowData();
    if(table == false){
    	if("lock"==lockPageFlag){
    		utils.dialog({content: '请添加供应商锁定申请！', quickClose: true, timeout: 2000}).showModal();
    	}else{
    		utils.dialog({content: '请添加供应商解除锁定申请！', quickClose: true, timeout: 2000}).showModal();
    	}
        return;
    }
    for(var i = 0 ; i < table.length; i++){
        if(table[i].supplierCode==""||table[i].supplierCode==null){
        	if("lock"==lockPageFlag){
        		utils.dialog({content: '请选择锁定运营商或删除空行！', quickClose: true, timeout: 2000}).showModal();
        	}else{
        		utils.dialog({content: '请选择解除锁定运营商或删除空行！', quickClose: true, timeout: 2000}).showModal();
        	}
            return;
        }
    }
    if(auditStatus == 2){//提交审核
        for(var i = 0 ; i < table.length; i++  ){
            if(table[i].appReason==""||table[i].appReason==null){
                utils.dialog({content: '请填写申请原因！', quickClose: true, timeout: 2000}).showModal();
                return;
            }
        }
    }
    var supplierDisableApprovalRecordDetailVOList = {}
    for(var i = 0 ; i < table.length; i++ ){
        table[i].supplierBusiType=table[i].isLock;
    }
    supplierLockApprovalRecordVO.supplierDisableApprovalRecordDetailVOList = table;
    if ( sexamine!=""){
        supplierLockApprovalRecordVO.id = sexamine;
    }
    supplierLockApprovalRecordVO.auditStatus = auditStatus;
    //加载页面
    parent.showLoading({hideTime: 60000});
    $.ajax({
        url: "/proxy-supplier/supplier/supplierLockApprovalRecord/addSupplierLockApprovalRecord",
        data: JSON.stringify(supplierLockApprovalRecordVO),
        type: "post",
        dataType: 'json',
        contentType: "application/json",
        success: function (data) {
            parent.hideLoading();
            if(data.staus == 1){
                if (sexamine==""){
                    $("#examine").val(data.keyId)
                }
                var msg = "";
                if(data.message){
                    msg = data.message;
                }else{
                    msg="保存成功";
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                //$(".ui-dialog-close").hide();
                return false;
            }else{
                var msg = "";
                if(data.message){
                    msg = data.message;
                }else{
                    msg="保存失败";
                }
                utils.dialog({
                    // title: "提示",
                    content: msg,
                    // width:300,
                    // height:30,
                    /*okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }*/
                    quickClose:true
                }).showModal();
                //$(".ui-dialog-close").hide();
                return false;
            }
        },
        error: function () {
            utils.dialog({content: '内部出错！', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
        },
        complete:function(){
            parent.hideLoading();
        }
    });
}
/**
 * 流程图显示
 */
function  initApprovalFlowChart(key) {
    //获取审核流程数据
    var key = $("#key").val();
    url = "/proxy-product/product/purchaseLimit/queryTotle?key=" + key ,
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}

//关闭按钮
$("#closeAssert").on("click", function () {
    utils.dialog({
        title: "提示",
        content: "是否保存草稿？",
        width:300,
        height:30,
        button:[
            {
                value:'关闭',
                callback:function(){
                    utils.closeTab();
                }
            }
        ],
        okValue: '保存草稿',
        ok: function () {
            $("#auditStatus").val("1");// 1:录入中
            save(1);
            return false;
        }
    }).showModal();
});
