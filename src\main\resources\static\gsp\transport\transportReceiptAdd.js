$(function () {
    var sysOrgCode = $("#sysOrgCode").val();
    var totalTable = z_utils.totalTable;
    var totalTablea = z_utils.totalTablea;
    function getNowFormatDateTime() {
        var date = new Date();
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        var hour = date.getHours();
        var mini = date.getMinutes();
        var sec = date.getSeconds();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        if (hour >= 1 && hour <= 9) {
            hour = "0" + hour;
        }
        if (mini >= 0 && mini <= 9) {
            mini = "0" + mini;
        }
        if (sec >= 0 && sec <= 9) {
            sec = "0" + sec;
        }
        var currentdate = year + seperator1 + month + seperator1 + strDate +  ' ' + hour+ ':'+ mini + ':'+sec;
        return currentdate;
    }
    //设置日期默认值
    $("#receiveTime").val(getNowFormatDate());

    //设置table高度
    utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: [],
        colNames: ['id','送达情况', '运输登记单号', '销售订单号', '收货单位', '收货地址','整件件数','发车时间', '收货时间',
            '收货人', '收货人联系方式', '到货温度（℃）'
        ],
        colModel: [{
            name: 'id',
            hidden: true
        }, {
            name: 'receiveState',
            rowtype: '#receiveState'
        }, {
            name: 'transportRegisterCode'
        }, {
            name: 'salesOrderCode'
        }, {
            name: 'customerName'
        }, {
            name: 'address'
        },{
            name: 'fullPcs'
        },{
            name: 'departureTime',
            formatter: function (val) {
                if(val){
                    return formatDate(val);
                }else {
                    return '';
                }
            }
        },{
            name: 'receiveTime',
            rowtype: '#receiveTime1',
            width: 200
        },{
            name: 'receiver',
            rowtype: '#receiver'
        }, {
            name: 'receiverPhone',
            rowtype: '#receiverPhone'
        }, {
            name: 'endShipmentTemperature',
            rowtype: '#endShipmentTemperature'
        },
            {
                name: 'outOrderCode',
                hidden:true
            }],
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        //shrinkToFit:false,  //设置为true时，列数充满表格，当列数较多时，只会缩小列宽，并不会出现滚动条，需要将属性设置为false
        //autoScroll: true,    //设置滚动条
        // rownumbers: true,
        selectandorder: true,
        // attachRow:true,//合计行
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {

        },
        pager: '#grid-pager'
    });
    //统计方法

    // 查询数据
    $('#searchBtn').bind('click', function () {

        var param = $('#myform').serializeToJSON();
        $('#X_Table').setGridParam({
            url: '/proxy-finance/finance/initBalance/findOutAndInStorage',
            postData: param
        }).trigger('reloadGrid');
        //统计方法
    })


    //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    });

    //运输单号 双击查询
    $('#transportRegisterCode').dblclick(function () {
        utils.dialog({
            title: '运输登记单列表',
            url: '/proxy-gsp/gsp/transport/toTransportReceiptOrderList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#productDesc').val(), // 给modal 要传递的 的数据
            onclose: function () {

                if (this.returnValue) {
                    // 回显信息
                    var data = this.returnValue;
                    console.log(data)
                    $('#transportRegisterCode').val(data.transportRegisterCode);
                    $('#orgName').val(data.orgName);
                    $('#driverName').val(data.driverName);
                    $('#truckName').val(data.truckName);
                    $('#agentor').val(data.agentor);
                    $('#deliverAddressName').val(data.deliverAddressName);
                    $('#companyName').val(data.companyName);
                    $('#outOrderCode').val(data.outOrderCode);
                    if(data){
                        //获取明细
                        $.ajax({
                            method: "POST",
                            url: "/proxy-gsp/gsp/transport/transportRecordDetail",
                            dataType: 'json',
                            data: {transportRegisterCode: data.transportRegisterCode},
                            cache: false,
                            success: function(data){
                                console.log(data);
                                // $("#deliverAddressName").val(data.result);
                                $('#X_Table').XGrid('clearGridData');
                                if(data&& data.result&&data.result.length){

                                    $.each(data.result,function (index, item) {
                                        $('#X_Table').XGrid('addRowData',item);
                                    })
                                 /*   $('input[name="receiveTime"]').removeAttr('readonly');*/
                                }
                            },
                            error: function (message) {

                            }
                        });



                    }

                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    });

    //格式化日期：yyyy-MM-dd
    function formatDate(date) {
        var date = new Date(parseInt(date));
        var myyear = date.getFullYear();
        var mymonth = date.getMonth() + 1;
        var myweekday = date.getDate();

        if (mymonth < 10) {
            mymonth = "0" + mymonth;
        }
        if (myweekday < 10) {
            myweekday = "0" + myweekday;
        }
        return (myyear + "-" + mymonth + "-" + myweekday);
    }

    //取消
    $('#cancelBtn').on('click', function () {
        utils.dialog({
            title: '温馨提示:',
            content: '取消后页面信息将丢失，是否确认取消？',
            width: 300,
            height: 50,
            okValue: '确认',
            ok: function () {
                $('#myform')[0].reset();
                $('#X_Table').XGrid('clearGridData');
                //设置日期默认值
                $("#receiveTime,#startPostDate").val(getMonthStartDate());
                },
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal();
    })

    //统一收货确认
    $('#allConfirm').on('click', function () {
        var selData = $('#X_Table').XGrid("getSeleRow");
        if(!selData.length){
            utils.dialog({title: '提示',content:'请至少选择一行！',timeout:3000}).show();
            return false;
        }
        utils.dialog({
            title: '统一收货确认',
            content: $('#receiverModal'),
            width: 400,
            height: 100,
            onshow: function(){
                $(this.node).find('#receiveTime').val(getNowFormatDateTime())
            },
            okValue: '统一收货确认',
            ok: function () {
                console.log(this.node)
                var formdata = $(this.node).find('form').serializeToJSON();
                var selRow =  $('#X_Table').XGrid('getSeleRow');
                if(selRow.length){
                    $.each(selRow, function (index, item) {
                        /*item.receiveState = formdata.receiveState;
                        item.receiveTime = formdata.receiveTime;*/
                        $('#X_Table').XGrid('setRowData',item.id, {receiveState:formdata.receiveState,receiveTime:formdata.receiveTime});
                    })
                }
            }
            ,
            cancelValue: '取消',
            cancel: function () {

            }
        }).showModal();
    })



    //确认保存
    $('#confirmBtn').on('click', function () {
        var rowdata = $('#X_Table').getRowData();
        if(!rowdata.length){
            utils.dialog({title:'提示',content:'请先选择需要回执的登记单！',timeout: 3000}).show();
            return false;
        }

        $.each(rowdata, function (index, item) {
            delete item.id;
            delete item.rowid;
        })

        if(rowdata.length == 0){
            utils.dialog({
                title:'温馨提示',
                content:'请选择数据！',
                okValue:'确定',
                ok:function () {}
            }).showModal();
            return false;
        }
        var arr = [];
        utils.dialog({
            title: '温馨提示',
            content: '保存成功后该页面将关闭，是否确认保存？',
            okValue: '确定',
            ok: function () {
                //下面保存
                var formData = $('#myform').serializeToJSON();
                $.ajax({
                    url: "/proxy-gsp/gsp/transport/toUpdateTransportRegisterRecord",
                    type:'post',
                    dataType:'json',
                    data: {formData:JSON.stringify(formData), rowdata:JSON.stringify(rowdata)},
                    success: function(res){
                        if(res){
                            if(res.code === 0){
                                utils.dialog({content: '确认成功！', quickClose: true, timeout: 1000}).show();
                                setTimeout(function () {
                                    utils.closeTab();
                                },1000);
                            }else{
                                utils.dialog({
                                    title: '温馨提示',
                                    content: data.msg,
                                    okValue: '确定',
                                    ok: function () {

                                    }
                                }).showModal()
                            }
                        }
                    }
                });
            }
        }).showModal()
    })

})










/**
 * 单据日期 开始
 */
function startOrderDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        maxDate:'#F{$dp.$D(\'endOrderDate\')}'
    });
    $("#endOrderDate").val("");
}

/**
 * 单据日期 结束
 */
function endOrderDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        minDate:'#F{$dp.$D(\'startOrderDate\')}',
        maxDate: getMaxDate('#startOrderDate')
    });
}

function getMaxDate(id){
    return $(id).val().split('-')[0]+'-12-31';
}

/**
 * 获取当前时间，格式YYYY-MM-DD
 * @returns {string}
 */
function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}

//获得本月的开始日期
function getMonthStartDate(){
    var now = new Date();                    //当前日期
    var nowDayOfWeek = now.getDay();         //今天本周的第几天
    var nowDay = now.getDate();              //当前日
    var nowMonth = now.getMonth();           //当前月
    var nowYear = now.getYear();             //当前年
    nowYear += (nowYear < 2000) ? 1900 : 0;  //
    var monthStartDate = new Date(nowYear, nowMonth, 1);
    return formatDate(monthStartDate);
}

//格式化日期：yyyy-MM-dd
function formatDate(date) {
    var myyear = date.getFullYear();
    var mymonth = date.getMonth() + 1;
    var myweekday = date.getDate();

    if (mymonth < 10) {
        mymonth = "0" + mymonth;
    }
    if (myweekday < 10) {
        myweekday = "0" + myweekday;
    }
    return (myyear + "-" + mymonth + "-" + myweekday);
}
