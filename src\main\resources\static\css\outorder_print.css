/* 默认属性 */
ul,li{
    list-style: none;
}
#box{
    color: #000 !important;
    font-family: 宋体;
    font-weight: 600 !important;
}
#box table tr td,#box table th{
    font-weight: 600 !important;
}
#box input[type=checkbox]{
    position: relative;
    background-color: #fff;
    -webkit-appearance:none;
    border: 1px solid #000;
    outline: none;
    width: 10px;
    height: 10px;
    margin: 0 4px;
}
#box input[type=checkbox]:checked::before{
    content: "V";
    position: absolute;
    top: 0;
    left: 0;
    color: #000;
}
#box table{
    border-color: #000 !important;
    font-size: 14px;
    border-width: 0.5px;
    border-style: solid;
    border-top:none;
}
#box .ui-jqgrid tr.jqgrow td {
    white-space: normal !important;
    height: 40px;
    vertical-align:center;
    text-align: center;
}
#box .ui-jqgrid tr.ui-jqgrid-labels th{
    height: 40px;
    font-weight: normal;
    vertical-align:center;
    text-align: center;
}

#big_box {
    width: 0;
    height: 0;
    overflow: hidden;
}
#box {
    box-sizing: border-box;
}
#box .content {
    position: relative;
    width: 1215px;
    font-size: 14px;
    padding: 20px 10px 0;
}
.box1 .content{
    width: 1215px !important;
}
#box .content .info_list{
    padding-left: 0;
}
#box .content .info_list li i.val{
    font-style: normal;
    height: 20px;
    line-height: 20px;
}
#box .content .ui-jqgrid {
    font-size: 14px;
    box-sizing: border-box;
}
#box .content .header {
    position: absolute;
    top: 20px;
    left: 0;
    width: 100%;
    text-align: center;
    font-size: 25px;
}
#box .content .header .title_num {
    position: absolute;
    bottom: -12px;
    left: 40%;
    font-size: 12px;
}
#box .content .top {
    position: relative;
    height: 80px;
}
#box .content .top .inden_type {
    position: absolute;
    right: 20px;
    bottom: 3px;
    display: block;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    border: 1px solid grey;
    border-radius: 1px;
    font-size: 25px;
}
.box1 .content .top .inden_type{
    right: 0 !important;
}
#box .content .info_list li {
    clear: both;
}
#box .content .info_list li span {
    float: left;
    display: block;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    white-space: nowrap;
}
#box .content .info_list li .col_1 {
    width: 10%;
}
#box .content .info_list li .col_1_8 {
    width: 18%;
}
#box .content .info_list li .col_2 {
    width: 20%;
}
#box .content .info_list li .col_2_4 {
    width: 24%;
}
#box .content .info_list li .col_3 {
    width: 30%;
}
#box .content .info_list li .col_4 {
    width: 40%;
}
#box .content .info_list li .col_5 {
    width: 50%;
}
#box .content .info_list li .col_5_8 {
    width: 58%;
}
#box .content .info_list li .col_6 {
    width: 60%;
}
#box .content .info_list li .col_7 {
    width: 70%;
}
#box .content .info_list li .col_8 {
    width: 80%;
}
#box .content .info_list li .col_8_2 {
    width: 82%;
}
#box .content .info_list li .col_9 {
    width: 90%;
}
#box .content .info_list li .col_10 {
    width: 100%;
}
#box .indent2 {
    /*margin-top: 40px;*/
}
#box .indent2 .table_c {
    margin-top: 10px;
}
#box .indent2 .table_c tr.jqgrow td {
    clear: both;
}
#box .indent2 .table_c tr.jqgrow td span {
    float: left;
    display: block;
    height: 20px;
    overflow: hidden;
    white-space: nowrap;
}
#box .indent2 .table_c tr.jqgrow td .col_de {
    width: 140px;
    text-align: left;
    padding: 0 10px;
    line-height: 20px;
}
#box .indent2 .table_c tr.jqgrow td .col_de input{
    vertical-align: middle;
}
.ui-th-column th.ui-th-column,
.ui-jqgrid .ui-jqgrid-htable th.ui-th-column {
    border-width: 1px 1px 1px 0;
    border-color: inherit;
    border-style: solid;
}
.ui-jqgrid .ui-jqgrid-htable .ui-th-div{
    height: 40px !important;
    align-items: center;
    display: flex;
    justify-content: center;
}
.ui-th-column th.ui-th-column:first-child,
.ui-jqgrid .ui-jqgrid-htable th.ui-th-column:first-child {
    border-left-width: 1px;
}
.ui-jqgrid tr.jqgrow td:first-child,
.ui-jqgrid tr.jqgroup td:first-child {
    border-left-color: inherit;
    border-left-style: solid;
    border-left-width: 1px;
}
.table_c tr.jqgrow:nth-child(2) td {
    border-top-color: inherit;
    border-top-style: solid;
    border-top-width: 1px;
}

/* 表头多余部分隐藏，表头对齐 */
.ui-jqgrid .ui-jqgrid-resize-ltr{
    display: none;
}
/* 分页下一页顶部边框 */
.page_border_top .ui-jqgrid  tr.jqgrow:nth-child(47) td{
    border-top-color: #000;
    border-top-style: solid;
    border-top-width: 1px;
}
.page_border_top .ui-jqgrid  tr.jqgrow:nth-child(96) td{
    border-top-color: #000;
    border-top-style: solid;
    border-top-width: 1px;
}

/* 长沙打印专配 */
/*.box1 .indent1{
  border-top: 1px solid #000;
}*/
.box1 .header .title{
    font-size: 30px;
}
.box1 .no_border th,.box1 .no_border td{
    border-style: none !important;
}
.box1 .name{
    width: 80px;
    margin-left: 7px !important;
}
.box1 th{
    height: 35px !important;
    box-sizing: border-box;
}
/*.box1 tr.jqgrow td{
    height: 26px !important;
    box-sizing: border-box;
    !*size: 24px !important;*!
    !*font-size: 24px !important;*!
}*/
.box1 .ui-jqgrid tr{
    height: 26px!important;
    line-height: 13px !important;
    padding: 0!important;
}
.box1 .ui-jqgrid tr.jqgrow td{
    height: 26px!important;
    line-height: 13px !important;
    padding: 0!important;
}
.box1 .ui-jqgrid tr.jqgrow td:last-child{
    height: 25px!important;
}
.box1 .indent1{
    margin-left: 25px !important;
    margin-top: -22px !important;

}
.box1 .bottom{
    margin-top: 1px;
}
.box1 .ui-jqgrid tr.jqgfirstrow {
    height:0!important;
}