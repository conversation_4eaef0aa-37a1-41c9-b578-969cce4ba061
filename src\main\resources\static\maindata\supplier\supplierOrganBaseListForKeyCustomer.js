
    var urlObjectList = [];
    $('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierOrganBase/supplierOrganBase/ajaxSupplierOrganBaseListForKeyCustomer",
        colNames: ['', '机构','供应商编码', '供应商名称', '供应商类型', '业务员', '业务员电话', '营业执照号','付款方式','结算方式','到货周期','注册地址','是否停用'/*,'剩余效期'*/],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                hidden:true
            },  {
                name: 'orgName',
                index: 'orgName',
                width: 350
            },  {
                name: 'supplierCode',
                index: 'supplierCode',
                width: 200

            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 250
            }, {
                name: 'supplierTypeName',
                index: 'supplierTypeName',
                width: 100
            }, {
                name: 'delivery',
                index: 'delivery',
                width: 150
            }, {
                name: 'deliveryPhone',
                index: 'deliveryPhone',
                width: 130
            }, {
                name: 'supplierBusinessNum',
                index: 'supplierBusinessNum',
                width: 200
            }, {
                name: 'paymentMethod',
                index: 'paymentMethod',
                width: 200
            },{
                name: 'settlementModes',
                index: 'settlementModes',
                width: 200
            },{
                name: 'arrivalPeriod',
                index: 'arrivalPeriod',
                width: 200
            },{
                name: 'registerAddress',
                index: 'registerAddress',
                width: 200
            },{
                name: 'disableState',
                index: 'disableState',
                width: 120,
                formatter:function(value){
                	if(value=='0'){
                		return "否"
                	}else{
                		return "是"
                	}
                	
                }
            },{
                name: 'snapImageUrl',
                hidden: true
            }
           /* , 
            {
                name: 'remainDays',
                index: 'remainDays',
                width: 250
            }*/

        ],
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        multiselect: true,//是否多选
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
//            window.location="/supplierOrganBase/firstSupplierOrganBase/detail/organ?businessId="+id;
            var url = "/proxy-supplier/supplier/supplierOrganBase/firstSupplierOrganBase/detail/organ?businessId="+id;
    		utils.openTabs("firstSupplierOrganBaseDetailOrgan", "机构供应商详情 ", url)
        },
        onSelectRow: function (id, dom, obj, index, event) {
            console.log(id, dom, obj, index, event)
            setUrlObjectList(dom,id,obj);
        }
    });

    $("#SearchBtn").on("click", function () {
        urlObjectList=[];
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "queryFields": $("#queryFields").val(),
                "supplierTypeId":$("#supplierTypeId").val(),
                "disableState":$("#disableState").val(),
                "orgCode":$("#orgCode").val(),
               /* "auditStatus":$("#auditStatus").val(),
                "maxRemainDays":$("#maxRemainDays").val(),
                "minRemainDays":$("#minRemainDays").val()*/
            },page:1
        }).trigger('reloadGrid');
    });

 
    
    $.ajax({
       	url:'/proxy-sysmanage/sysmanage/dict/querycommonnotpage?type=8',
       	type:"post",
       	async:false,
       	dataType:'json',
       	success:function(data){
       		console.log("供应商类别："+data.result);
       		var html = '';
	      $(data.result).each(function(index,item){
	    	  html +=  '<option value="'+item.id+'">&nbsp;'+item.name+'</option>'
	      });
       	   $(".supplierTypeClass").append(html);
       		
       	},
       	error:function(){
       		 utils.dialog({content: '请求失败', quickClose: true, timeout: 2000}).showModal();
       	}
       });
    $(document).on('change','#grid_checked input',function () {
        if($(this).prop('checked')){
            var $tr=$(this).parents('tr');
            var rowData=$("#X_Tableb").getRowData($tr.attr('id'));
            var id=$tr.attr('id');
            setUrlObjectList($tr,id,rowData);
        }else{
            urlObjectList = [];
        }
        // urlObjectList=[];
        // if(checked){
        //     var selRow=$('#X_Tableb').XGrid('getSeleRow');
        //     if(selRow && selRow.length > 0){
        //         for(var i=0;i<selRow.length;i++){
        //             if(selRow[i].snapImageUrl != ''){
        //                 var fileParam={};
        //                 fileParam.id = selRow[i].id;
        //                 fileParam.name = selRow[i].supplierCode;
        //                 fileParam.url = selRow[i].snapImageUrl;
        //                 urlObjectList.push(fileParam);
        //             }
        //         }
        //     }
        // }else{
        //     urlObjectList=[];
        // }
    })
    //首营审批快照
    $('#snapshootBtn').on('click', function () {
        var len=$("#X_Tableb").XGrid('getSeleRow');
        if(!len || len.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'请先从列表中选择一条数据',
                okValue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        utils.dialog({
            align: 'top',
            width: 90,
            height: 50,
            padding: 8,
            content:'<div class="changeApplyItem formApprovalBlock"><div class="cSelect">预览</div><div class="cDown">下载</div></div>',
            quickClose: true
        }).show(this);
    });
    // $("#X_Tableb").on('change','td[row-describedby="ck"] input',function(){
    //     var $tr=$(this).parents('tr');
    //     var rowData=$("#X_Tableb").getRowData($tr.attr('id'));
    //     var id=$tr.attr('id');
    //     setUrlObjectList($tr,id,rowData);
    // });
    $('body').on('click', '.cSelect', function () {
        if(urlObjectList.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'没有可预览附件',
                okVlue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        $.viewImg({
            fileParam:{
                name:'name',
                url:'url'
            },
            list:urlObjectList
        })
    })

    $('body').on('click', '.cDown', function () {
        if(urlObjectList.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'没有可下载的附件',
                okVlue: '确定',
                ok:function(){}
            }).showModal();
            return ;
        }
        //批量下载
        var a=[];
        a.push('<form style="display: none" method="post">');
        urlObjectList.forEach(function(item) {
            console.log(item);
            a.push('<input name="file" value = ' + item.url + '>');
            a.push('<input name="name" value = '+ item.name + '>');
        });
        a.push('</form>');

        var $eleForm = $(a.join(''));
        $eleForm.attr("action", "/proxy-sysmanage/upload/downloadZip");
        $(document.body).append($eleForm);
        //提交表单，实现下载
        $eleForm.submit();
    })

function setUrlObjectList($tr,id,rowData){
    var a=rowData;
    var fileParam = {};
    if(!id){
        //  点击的是全选按钮。，所以拿全部数据
        urlObjectList = a.map(function (item,index) {
            let _obj = {}
            _obj.id = item.id;
            _obj.name = item.supplierCode;
            _obj.url = item.snapImageUrl;
            return _obj;
        })
    }else{
        if($tr.hasClass('selRow') && a.snapImageUrl){
            fileParam.id = a.id;
            fileParam.name = a.supplierCode;
            fileParam.url = a.snapImageUrl;
            urlObjectList.push(fileParam);
        }else if(!$tr.hasClass('selRow')){
            $(urlObjectList).map(function (i,v) {
                if(v.id==a.id){
                    urlObjectList.splice(i,1);
                }
            })
        }
    }


}