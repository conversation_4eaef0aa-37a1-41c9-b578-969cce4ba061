let payableTypeHtml = ''
$(function () {

    //查询所有款项类型
    $('.content').css('height', $(window).height());

    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '1', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            //选中了
            window.isSelect = true;
            $("#supplierNo").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);;
            // getPayableType(result.data, '#payableType');
            console.log(result)
            $("#openBankNum").val(result.openBankNum)
            $("#supplierProfitRate").val(result.supplierProfitRate)
                    $ (".table-box input[name='sopenBankNum']").each(function(){
                        if($(this).closest('tr').find('option:selected').val()==114||$(this).closest('tr').find('option:selected').val()==115){
                            $(this).val($("#openBankNum").val())
                        }
                    })
            $ (".table-box input[name='supplierProfitRate']").each(function(){
                $(this).val($("#supplierProfitRate").val())
            })
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName',
            delivery: 'delivery',
            deliveryPhone: 'deliveryPhone',
            openBankNum:"openBankNum",
            supplierProfitRate:'supplierProfitRate',
        },
        onNoneSelect: function (params, suggestions) {
            //没选中
            $("#supplierNo").val("");
            $("#supplierName").val("");
            // getPayableType('', '#payableType')
            // payableTypeHtml = `<option value="">请选择</option>`
            window.isSelect = false;
        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    //放大镜查询
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });

    //供应商双击查询
    function supplierdDalog(val) {

        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=2',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                $('#supplierName').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    $("#supplierNo").val(data.supplierCode);
                    $("#supplierName").val(data.supplierName);
                    // getPayableType(data.supplierCode, '#payableType')
                    //$("#input_custorm").val(data.customerName)
                    //赋值给3月或者六月
                    $("#openBankNum").val(data.openBankNum)
                    $("#supplierProfitRate").val(data.supplierProfitRate)
                    $ (".table-box input[name='sopenBankNum']").each(function(){
                        if($(this).closest('tr').find('option:selected').val()==114||$(this).closest('tr').find('option:selected').val()==115){
                            $(this).val($("#openBankNum").val())
                        }
                    })
                    $ (".table-box input[name='supplierProfitRate']").each(function(){
                        $(this).val($("#supplierProfitRate").val())
                    })
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }


    $('#paymentType').bind('change', function () {
        var text = $(this).find('option:selected').text();
        if (/银行转账/.test(text)) {
            $('.hide-input').show();
        } else {
            $('.hide-input').hide();
        }
    })

    var isPrepayList = [];
    $.each($("#isPrepay option"), function (index, item) {
        isPrepayList.push({value: $(item).val(), name: $(item).text()})
    })

    isPrepayList = isPrepayList.splice(0, isPrepayList.length / 2);

    var paymentTypes = JSON.parse($('#paymentTypes').val());
    function getPayName(code) {
        var result = '';
        if (paymentTypes) {
            $.each(paymentTypes, function (index, item) {
                if (item.code == code) {
                    result = item.name;
                }
            })
        }
        return result;
    }

    $('#X_Tablea').XGrid({
        data: [],
        colNames: ['序号', '<span class="text-danger">*</span> 实际付款日期', '<span class="text-danger">*</span> 过账日期','款项类型编码', '款项类型','供应商财务毛利率 <span class="questa" style="left: 124px;"></span>', '<span class="text-danger">*</span> 支付方式', '银行名称', '银行账号', '<span class="text-danger">*</span> 付款金额',  '<span class="text-danger">*</span> 付款原因','关联采购订单号','备注', '承兑类型', '承兑编号', '承兑名称',"承兑开户行号"],
        colModel: [{
            name: 'id'
        }
            , {
                name: 'realPaymentTime',
                rowtype: '#accountingDate_black'
            }, {
                name: 'accountTime',
                rowtype: '#actualPaymentDate_black'
            }, {
                name: 'isPrepay',
                hidden: true,
                formatter: function (val) {
                    var result;
                    if (!val) result = '';
                    result = $("#isPrepay").val();
                    return result;

                }

            }, {
                name: 'isPrepayStr',
                formatter: function (val) {
                    var result;
                    if (!val) result = '';
                    result = $("#isPrepay").find("option:selected").text();
                    return result;

                }

            }, {
                name: 'supplierProfitRate',
                width:200,
                rowtype:'#supplierProfitRate_table'
            },{
                name: 'paymentType',
                rowtype: '#modePayment_black',
                rowEvent: function (etype) {
                    var name = $(etype.e.target).find('option:selected').text();
                    var value=$(etype.e.target).find('option:selected').val();
                    if(value=='114'||value=='115'){
                        var sopenBankNum = $("#openBankNum").val(); //获取文本对应账号
                        $(etype.e.target).closest('tr').find('td[row-describedby="sopenBankNum"] input').val(sopenBankNum).attr('title', sopenBankNum);
                    }else{
                        $(etype.e.target).closest('tr').find('td[row-describedby="sopenBankNum"] input').val('').attr('title', '');
                    }
                    if (!name.includes('银行转账')) {
                        $(etype.e.target).closest('tr').find('td[row-describedby="bankName"] select').val('').attr('disabled', '');
                        $(etype.e.target).closest('tr').find('td[row-describedby="bankAccount"] input').val('');
                    } else {
                        $(etype.e.target).closest('tr').find('td[row-describedby="bankName"] select').removeAttr('disabled');
                    }
                    if (name.indexOf('承兑') === -1) {
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptType"] select').val('').attr('disabled', '');
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptName"] input').val('').attr('disabled', '');
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptCode"] input').val('').attr('disabled', '');
                    } else {
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptType"] select').removeAttr('disabled');
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptName"] input').removeAttr('disabled');
                        $(etype.e.target).closest('tr').find('td[row-describedby="acceptCode"] input').removeAttr('disabled');
                    }
          
                  
                }
            }, {
                name: 'bankName',
                rowtype: '#bankName_black',
                rowEvent: function (etype) {
                    var name = $(etype.e.target).find('option:selected').text(); //获取选择文本
                    var bankAccount = $(etype.e.target.selectedOptions[0]).data('backaccount'); //获取文本对应账号
                    $(etype.e.target).closest('tr').find('td[row-describedby="bankAccount"] input').val(bankAccount).attr('title', bankAccount);

                }
            }, {
                name: 'bankAccount',
                editable: true
            }, {
                name: 'paymentAccount',
                rowtype: '#money_black',
                rowEvent: function (etype) {
                    totalTable()
                }
                // formatter: function (val) {
                //     return parseFloat(val).formatMoney('2', '', ',' ,'.');
                // },
                // unformat: function (val) {
                //     return val.replace(/,/g ,'');
                // }
            }, {
                name: 'paymentReason',
                rowtype: '#pay_reason_black',
            }, {
                name: 'orderNo',
                rowtype: '#orderNo_black',
            },{
                name: 'remark',
                formatter: function (val) {
                    return transEntity(val, true);
                },
                rowtype: '#text_black',
            },
            {
                name: 'acceptType',
                rowtype: '#modeAcceptType_black',
            },
            {
                name: 'acceptCode',
                rowtype: '#acceptCode_black',

            },
            {
                name: 'acceptName',
                rowtype: '#acceptName_black',

            }, {
                name: 'sopenBankNum',
                rowtype: '#openBankNum_black',

            }

        ],
        key: 'id',
        rowNum: 10,
        altRows: true, //设置为交替行表格,默认为false
        // multiselect: true,
        gridComplete: function () {
            setTimeout(function () {
                $("#addBtn").trigger("click");
            }, 100);
            //初始化table头部hover提示
            setTimeout(() => {
                initQuesta();
            }, 100);
        }

    });
     //初始化table头部hover提示
     function initQuesta(result) {
        var string = result;

        const questaOption = [{
            th: 'supplierProfitRate', //供应商财务毛利率
            title: `取自新建无申请付款单建立时，计提口径供应商维度财务毛利率`,
            width: 460,
            height: 80,
        },
        ];
        eachTipView(questaOption);
    }

    function eachTipView(arr) {
        $.each(arr, function (index, item) {
            $('.table_wrap1').delegate('th[row-describedby=' + item.th + '] .questa', {
                mouseover: function (e) {
                    $('body').append(`
                        <div id='div_tooltips'>
                            <style>
                                #div_tooltips:after{
                                    content: "";
                                    width: 0;
                                    height: 0;
                                    position: absolute;
                                    left: ${item.width / 2 - 10}px;
                                    bottom: -10px;
                                    border-left: solid 10px transparent;
                                    border-top: solid 10px white;
                                    border-right: solid 10px transparent;
                                }
                            </style>
                            <div id='inner_tooltips'>${item.title}</div>
                        </div>
                    `);
                    $('#div_tooltips')
                        .css({
                            boxSizing: 'border-box',
                            width: item.width + 'px',
                            height: item.height + 'px',
                            padding: '10px',
                            zIndex: 9999,
                            backgroundColor: '#ffffff',
                            border: '1px solid #c4c4c4',
                            position: 'absolute',
                            top: $(e.target).offset().top - item.height - 10 + 'px',
                            left: $(e.target).offset().left + 5 - item.width / 2 + 'px',
                        })
                        .show('fast');
                },
                mouseout: function () {
                    $('#div_tooltips').remove();
                },
                click: function () { },
            });
        });
    }
    window.isFirst = true;

    //合计
    function totalTable() {

        if ($('#X_Tablea tr:last-child td:first-child').html() == '合计') {
            $('#X_Tablea tr:last-child').remove();
        }
        var data = $('#X_Tablea').XGrid('getRowData');
        $('#X_Tablea').XGrid('addRowData', {
            id: '999',
            paymentAccount: totalSum(data, 'paymentAccount')
        });
        $('#X_Tablea tr:last-child td:first-child').html('合计');
        $('#X_Tablea tr:last-child').find('td:eq(1)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(2)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(4)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(5)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(6)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(7)').html('');
        $('#X_Tablea tr:last-child').find('td:last-child').html('');
        $('#X_Tablea tr:last-child').find('td:eq(8)').html(totalSum(data, 'paymentAccount'));
        $('#X_Tablea tr:last-child').find('td:eq(9)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(10)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(11)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(12)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(13)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(14)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(15)').html('');
        $('#X_Tablea tr:last-child').find('td:eq(16)').html('');
        window.isFirst = false;

    }

    //合计计算方法
    function totalSum(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat($.trim(item[colName]).replace(/,/, ''));
        })
        return count.toFixed(2) === "NaN" ? 0.00 : count.formatMoney('2', '', ',', '.');
    }


    // 删除
    $('#deleteBtn').bind('click', function () {
        var seleRow = $('#X_Tablea').XGrid('getSeleRow');
        if (seleRow) {
            //删除二次确认
            utils.dialog({
                title: "提示",
                content: "确认删除当前选中行？",
                okValue: '确定',
                ok: function () {
                    $('#X_Tablea').XGrid('delRowData', seleRow.id);
                    refreshIndex($('#X_Tablea'));
                    totalTable();
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                timeout: 2000
            }).show()
        }
    })

    //新增
    $('#addBtn').bind('click', function (param) {
        var index = $('#X_Tablea tr').length - 1;
        if (index > 20) {
            utils.dialog({content: '最多添加20行', quickClose: true, timeout: 2000}).show();
            return false;
        }
        payableTypeHtml = $('#X_Tablea').XGrid('getRowData').length == 1 ? payableTypeHtml : $('#X_Tablea tr').eq(1).find('#payableType').html()
        $('#X_Tablea').XGrid('addRowData', {
            id: index,
            paymentType: $('#payableType').val(),
            isPrepay: $('#isPrepay').val(),
            paymentAccount: '0.00',
            supplierProfitRate:$('#supplierProfitRate').val()
        }, 'before', '999');
        $('#X_Tablea').XGrid('editRow', index);
        var $select = $('#X_Tablea tr:last-child').prev('tr').find('td[row-describedby="paymentType"] select');
        var name = $select.find('option:selected').text();
        if (!name.includes('银行转账')) {
            $select.closest('tr').find('td[row-describedby="bankName"] select').val('').attr('disabled', '');
        } else {
            $select.closest('tr').find('td[row-describedby="bankName"] select').removeAttr('disabled');
        }
        $select.closest('tr').find('td[row-describedby="bankAccount"] input').val('').attr('readonly', '');

        $('#X_Tablea tr').eq($('#X_Tablea tr').length - 2).find('#payableType').html(payableTypeHtml)
        totalTable();

    })

    $('#addBtn').trigger('click');

    //更新序号
    function refreshIndex($Table) {
        var rn = $Table.find('td[row-describedby="id"]');
        $.each(rn, function (index, item) {
            if ($(item).html() !== '合计') {
                $(item).html(index + 1);
            }
        })
    }

    //新增
    $('#saveBtn').bind('click', function () {
        var selRow = $('#X_Tablea').XGrid('getSeleRow');

        $('#X_Tablea').XGrid('saveRow', '3', selRow);

    })

    // 确认付款
    var dialog = parent.dialog.get(window);
    $('#submitBtn').bind('click', function () {
        $('#submitBtn').attr('disabled',"true");
        var tempArr = [],
            paymentDetail = $('#X_Tablea').XGrid('getRowData');
        paymentDetail.pop();

        //
        let someRowData = paymentDetail.filter(item => {
            return item['paymentReason'] == '1' || item['paymentReason'] == '2'
        }).filter(item => item['orderNo']).map(item => {
            return {
                id: item['id'],
                paymentReason: item['paymentReason']
            }
        })
        var ttx = $("#myform").serializeToJSON();
        if (!validPay(paymentDetail, ttx)){
            $('#submitBtn').removeAttr("disabled");
            return false;
        }
        ttx.supplierProfitRate = $('#supplierProfitRate').val();
        $.ajax({
            method: "POST",
            url: "/proxy-finance/finance/purchase/payrequestinfo/savePayment",
            data: {
                "paymentDetailVo": JSON.stringify(paymentDetail),
                "paymentVo": JSON.stringify(ttx)
            },
            dataType: 'json',
            cache: false
        }).done(function (data) {
            if (data.code == 0) {
                utils.dialog({
                    content: "付款成功",
                    quickClose: true,
                    timeout: 2000
                }).showModal();
                setTimeout(function () {
                    var dialog = parent.dialog.get(window);
                    dialog.close();
                }, 500);
            } else {
                $('submitBtn').removeAttr('disabled');
                utils.dialog({
                    content: data.result,
                    quickClose: true,
                    timeout: 3000
                }).showModal();
                $('#submitBtn').removeAttr('disabled');
            }
        });
    })

    //校验数据
    function validPay(paymentDetail, ttx) {
        if (!paymentDetail.length) {
            utils.dialog({content: '请添加付款明细行', quickClose: true, timeout: 2000}).show();
            return false;
        }

        if ($('#supplierNo').val() == '') {
            utils.dialog({content: '供应商不能为空！', quickClose: true, timeout: 2000}).show();
            return false;
        }

        /* if ($('#applayAmount').val() !== $('#X_Tablea tr:last-child td:eq(7)').html()) {
             utils.dialog({content: '申请金额不等于合计付款金额！', quickClose: true, timeout: 2000}).show();
             return false;
         }*/

        var flag = true;
        $.each(paymentDetail, function (index, item) {

            if (item.realPaymentTime == '') {
                utils.dialog({content: '实际付款日期必选！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }

            if (item.accountTime == '') {
                utils.dialog({content: '过账日期必选！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }
            if (!moment(item.accountTime).isAfter($('#period').val())) {
                if(item.accountTime == $('#period').val()){
                    flag = true;
                }else{
                    utils.dialog({
                        content: '过账日期不能小于当前会计期间',
                        quickClose: true,
                        timeout: 2000
                    }).show();
                    flag = false;
                    return false;
                }

            }
            if (item.paymentType == '') {
                utils.dialog({content: '支付方式必选！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }else {
                var payName = getPayName(item.paymentType);
                if (payName.includes('银行转账')){
                    if (item.bankName == '') {
                        utils.dialog({content: '银行名称必选！', quickClose: true, timeout: 2000}).show();
                        flag = false;
                        return false;
                    }
                }
            }

            let payTypeName = getPayName(item.paymentType);
            if(payTypeName.indexOf('承兑') !== -1) {
                if(item.acceptType === ''||item.acceptName === ''||item.acceptCode === ''){
                    utils.dialog({content: '如果支付方式为承兑，承兑类型、承兑编号和承兑名称不可为空！', quickClose: true, timeout: 2000}).show();
                        flag = false;
                        return false;
                }
            }

            if (item.paymentAccount == '' || item.paymentAccount == 0) {
                utils.dialog({content: '付款金额必填！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }else if(parseFloat(item.paymentAccount)< -*********.00){
                utils.dialog({content: '付款金额不能小于-*********.00！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }

            if (parseFloat(item.paymentAccount) > *********.00) {
                utils.dialog({content: '金额不得大于*********.00！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }
            if (item.paymentReason == '') {
                utils.dialog({content: '请选择付款原因！', quickClose: true, timeout: 2000}).show();
                flag = false;
                return false;
            }
        })


        return flag;
    }

})
// 支付方式 查詢回显
function getPayableType(supplierCode,tagNode) {
    $.ajax({
        url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierPayType?supplierCode='+ supplierCode,
        type: 'get',
        success: (res) => {
            console.log('res', res);
            let _Str = '<option value="">请选择</option>'
            if (res.code == 0) {
                res.result.payTypeVOList.forEach(item => _Str += `<option value="${item.code}">${item.name}</option>`)
            }
            payableTypeHtml = _Str
            $(tagNode).empty().html(_Str)
        },
        error: (err) => {
            console.log('err', err);
        }
    })
}
//  支付方式切换
function payableTypeChange(el) {
    $('#accept_transfer_Rebate').css('display', ($(el).val() == '112' || $(el).val() == '114' || $(el).val() == '115') ? 'block' : 'none')
}
// 款项类型切换
function change_prePay(el) {
    let text = utils.getOptText(el, $(el).val())
    $('#X_Tablea').XGrid('getRowData').filter(item => item.id != 999).forEach((item, index) => {
        $('#' + item.id).find('[row-describedby="isPrepayStr"]').text(text)
        $('#' + item.id).find('[row-describedby="isPrepay"]').text($(el).val())
    })
}
// 付款原因切换
function payReasonChange(el) {
    let thisVal = $(el).val()
    if(thisVal != '1' && thisVal != '2'){
        $(el).parents('tr').find('[row-describedby="orderNo"] input').val('');
    }
    $(el).parents('tr').find('[row-describedby="orderNo"] input').prop('disabled',(thisVal == '1' || thisVal == '2' ? false : true))
}

// 承兑类型切换
function acceptTypeChange(el) {
    let thisVal = $(el).val()
    if(thisVal != '0'){
        let supplierName = $("#supplierName").val();
        if(supplierName === ''){
            utils.dialog({content: '请先选择供应商', quickClose: true, timeout: 2000}).show();
            $(el).val('');
            return
        }
        $(el).parents('tr').find('[row-describedby="acceptName"] input').val(supplierName);
        $(el).parents('tr').find('[row-describedby="acceptName"] input').prop('disabled',true);
    }else{
        $(el).parents('tr').find('[row-describedby="acceptName"] input').val('');
        $(el).parents('tr').find('[row-describedby="acceptName"] input').prop('disabled',false);
    }
}