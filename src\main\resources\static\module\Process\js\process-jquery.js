(function(){
	const origin = location.origin;
	if (origin.indexOf('test') > -1) {
		document.domain = 'test.ybm100.com'
	} else if (origin.indexOf('stage') > -1) {
		document.domain = 'stage.ybm100.com'
	} else {
		document.domain = 'ybm100.com'
	}
})();

(function ($) {
  $.fn.process = function (opt) {
    var settings = {
      steps: [],
      newsteps: []
    };
    $.each(opt, function (index, item) {
      settings.steps.push(item);
    });
    for (var i = 1; i < settings.steps.length + 1; i++) {
      var item = opt["node" + i];
      settings.newsteps.push(item);
    }
    function buildReadMoreElements(item,nodeIndex) {
      const $more = $(
        `<div class="tips-more"><span class="tips-more-span">查看详情</span><div class="tips-modal"><div class="main-tips"></div><i class="close">&times;</i></div></div>`
      );
      //节点详情
      $.each(item.records, function (i, t) {
        //判断详情信息状态
        let status = t.status;
        let hide = ""
        if (status == 0) {
          status = "驳回";
        } else if (status == 1) {
          if (nodeIndex == 0) {
            status = "提交审核";
          } else {
            status = "审核通过";
          }
        } else if (status == 2) {
          status = "审核中";
          hide = "child";
        } else if (status == 4) {
          status = "关闭";
        } else {  //status 为0 1 2 4 之外的情况直接隐藏
          hide = "child";
        }

        //判断处理人
        if (t.handler != null) {
          t.handler = t.handler;
        } else {
          t.handler = "";
        }
        const handler = t.company ? t.company : "处理人:" + t.handler;

        const detail_new = t.detail ? t.detail : "";
        let $more_cont
        // 首个节点即申请人节点
        if (nodeIndex == 0) {
          $more_cont = $(
            `<div class="more_cont ${hide}"><span class="node-line"></span><p class="handler">${handler}</p><p class="handTime">处理时间:${
              t.handTime
            }</p><p class="detail">状态:${status}</p></div>`
          );
        } else {
          $more_cont = $(
            `<div class="more_cont ${hide}"><span class="node-line"></span><p class="handler">${handler}</p><p class="handTime">处理时间:${
              t.handTime
            }</p><p class="detail">审核状态:${status}</p><p class="detail">处理意见:${detail_new}</p></div>`
          );
        }
        if (i > 2) {
          $more.find(".main-tips").css({
            overflow: "auto",
            "max-height": "322px"
          });
        } else if (i < 2) {
          $more.find(".main-tips").removeAttr("overflow");
        }
        $more.find(".main-tips").append($more_cont);
      });
      return $more
    }
    //创建结构
    var $container = $('<ul class="process-container">');
    $.each(settings.newsteps, function (index, item) {
      var name = item.iscompany ? item.iscompany : item.name;
      var $step = $(
        `<li class="process-step"><span>${index +
            1}</span><div class="step-content">  </div><div class="line"></div></li>`
      );

      var tempArr = [];
      if (item.records.length > 0) {
        tempArr = item.records.sort((a, b) => b.handTime - a.handTime);
        //判断时间类型
        var timeType = item.id.indexOf('purchaseOrderAdvance') > -1 || item.id.indexOf('purchaseOrderUnadvance') > -1 
        				|| item.id.indexOf('majorCustomerPurchaseOrderAdvance') > -1 || item.id.indexOf('majorCustomerPurchaseOrderUnadvance') > -1
            ||item.id.indexOf('orderInternalTransaction') > -1 ;

        $.each(tempArr, function (e, it) {
          if (it.handTime != null && it.handTime != "") {
            it.handTime = moment(it.handTime).format(true ? "YYYY-MM-DD" : "YYYY-MM-DD");
          } else {
            it.handTime = "";
          }
        });
      }
      item.records = tempArr;
      //tips位置
      if (item.records) {
        // var handler = item.records[0].company?item.records[0].company:item.records[0].handler?item.records[0].handler:item.executor?item.executor:"";
        let handler
        if (item.records[0].status == 0) {
          handler = item.executor || "";
        } else {
          handler = item.records[0].company || item.records[0].handler || item.executor || "";
        }
        var $tips;
        if(item.Taskstatus == 2&&item.linecount == 0){
          if(index!=0){
            $tips = $(
                '<div class="step-tips"><p title='+handler+' style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">' +handler +"</p></div>"
            );
            /** 若 records 数组内任意元素的 handler 字段不为空，则展示「查看详情」按钮；
             * 否则，隐藏「查看详情按钮」
             */
            if (item.records.some(recordItem => recordItem.handler)) {
              // 添加「查看详情」按钮
              $tips.append(buildReadMoreElements(item, index));
            }
            $step.append($tips);
          }
        } else if (item.Taskstatus == 0||item.Taskstatus == 1||item.Taskstatus == 2||item.Taskstatus == 4) {
          $tips = $(
            '<div class="step-tips"><p title='+handler+' style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">' +handler +"</p><p>" +item.records[0].handTime +"</p><p>" +item.records[0].detail +"</p></div>"
          );
          $tips.append(buildReadMoreElements(item,index));
          $step.append($tips);
        }else{
            //Taskstatus==3  显示需要审批人名字
            $tips = $(
                '<div class="step-tips"><p title='+handler+' style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">' +handler +"</p></div>"
            );
            $step.append($tips);
        }
      }
      //判断第一个节点的状态
      if (index == 0) {
        // $(this).addClass('step-frist');
      }
      //判断节点状态
      switch (parseInt(item.Taskstatus)) {
        case 0:
          $step.addClass("status-return");
          break;
        case 1:
          $step.addClass("status-success");
          break;
        case 2:
          $step.addClass("status-pending");
          break;
        case 3:
          $step.addClass("status-notStart");
          break;
        case 4:
          $step.addClass("status-close");
          break;
      }
      //判断线的状态
      if (item.linecount == 0) {
        $step.find(".line").addClass("track-1");
      } else if (item.linecount == 1) {
        $step.find(".line").addClass("track-2");
      } else if (item.linecount == 2) {
        $step.find(".line").addClass("track-3");
      } else if (item.linecount == 3 || item.linecount > 3) {
        $step.find(".line").addClass("track-4");
      }
      //判断是否为最后一步
      if (index == settings.steps.length - 1) {
        $step.addClass("step-last");
      }
      $container.append($step);
    });
    $(this).append($container);
    //判断节点长度
    /* if (settings.steps.length <= 11) {
      $(this).css({
        height: "141px",
        width: "100%"
      });
    } else {
      $(this).css({
        height: "141px",
        width: "100%",
        "overflow-x": "scroll",
        "overflow-y": "hidden",
        "z-index": 1
      });
    } */
    $(this).css({
      width: "100%",
      "overflow-x": "auto",
      "z-index": 1
    });
    //查看详情模态框
    $(this).on("click", ".tips-more-span", function (e) {
      e.stopPropagation();

      var $this = $(this).closest('.tips-more');
      var $tipsModal = $this.find(".tips-modal");

      //判断提示语位置
      var sit = '';
      if ($tipsModal.closest('li.step-frist').length) {
        sit = ' left'
      } else if ($tipsModal.closest('li.step-last').length) {
        sit = ' right'
      }
      window.tipsModal = dialog({
        align: 'bottom' + sit,
        content: $tipsModal,
        quickClose: true,
        skin: 'tipsModal',
        padding: 10,
        onshow: function () {
          $tipsModal.clone().hide().appendTo($this);
          $(this.node).find('.close').on("click", function (e) {
            window.tipsModal.close().remove();
          });
        }
      });
      window.tipsModal.show($this[0])
    });
    $(this).find('li:eq(0)').addClass('step-frist');
    return this;
  };
})(jQuery);