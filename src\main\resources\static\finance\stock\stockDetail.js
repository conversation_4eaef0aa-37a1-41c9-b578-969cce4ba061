$(function () {

    //放大镜查询
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    });
    var sysOrgCode = $("#sysOrgCode").val();
    //商品名称 双击查询
    $('#productDesc').dblclick(function () {
        utils.dialog({
            title: '商品列表',
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            width: $(window).width() * 0.8,
            height: 500,
            data: $('#productDesc').val(), // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#productDesc').val(data.productName);
                    $('#drugCode').val(data.productCode)
                }
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
        return false;
    });


    //商品名称 搜索
    $('#productDesc').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+sysOrgCode, //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#drugCode").val(result.data);

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            // console.log(query, suggestions);

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            $("#drugCode").val("");
            $("#productDesc").val("");
        }
    });


    //初始化移动类型
  //  initMovementTypeSelect();
    //设置日期默认值
    $("#startOrderDate,#startPostDate").val(getMonthStartDate());
    $("#endOrderDate,#endPostDate").val(getNowFormatDate());

    //设置table高度
    utils.setTableHeight('X_Table');
    var colNames = [ '单据日期', '过账日期','单据编号','摘要', '移动类型', '商品编码', '商品名称', '入库数量',
        '入库单价', '入库金额', '出库数量', '出库单价', '出库金额','结存数量', '结存单价',  '结存金额'
    ];
    var colModel =[ {
        name: 'orderDate',
        index: 'orderDate',
        width: 100, //宽度

    }, {
        name: 'postDate',
        // index: 'postDate',
        width: 100
    }, {
        name: 'orderCode',
        index: 'orderCode',
        width: 260
    }, {
        name: 'orderType',
        index: 'orderType',
        width: 100
    }, {
        name: 'movementTypeName',
        index: 'movementTypeName',
        width: 100,
        sortable: false,
        editable: true
    }, {
        name: 'drugCode',
        index: 'drugCode',
        width: 100,
        sortable: false
    }, {
        name: 'productName',
        index: 'productName',
        width: 300,
        sortable: false,
        editable: true
    }, {
        name: 'amountIn',
        index: 'amountIn',
        width: 100,
        sortable: false,
        editable: true
    }, {
        name: 'priceIn',
        index: 'priceIn',
        width: 100,
        sortable: false,
        editable: true,
        formatter: function (val) {
            return parseFloat(val).formatMoney('2', '', ',' ,'.');
        },
        unformat: function (val) {
            return val.replace(/,/g ,'');
        }
    }, {
        name: 'sumIn',
        index: 'sumIn',
        width: 100,
        sortable: false,
        editable: true,
        formatter: function (val) {
            return parseFloat(val).formatMoney('2', '', ',' ,'.');
        },
        unformat: function (val) {
            return val.replace(/,/g ,'');
        }
    }, {
        name: 'amountOut',
        index: 'amountOut',
        width: 100,
        sortable: false,
        editable: true

    }, {
        name: 'priceOut',
        index: 'priceOut',
        width: 100,
        sortable: false,
        editable: true,
        formatter: function (val) {
            return parseFloat(val).formatMoney('2', '', ',' ,'.');
        },
        unformat: function (val) {
            return val.replace(/,/g ,'');
        }
    }, {
        name: 'sumOut',
        index: 'sumOut',
        width: 100,
        sortable: false,
        editable: true,
        formatter: function (val) {
            return parseFloat(val).formatMoney('2', '', ',' ,'.');
        },
        unformat: function (val) {
            return val.replace(/,/g ,'');
        }
    }, {
        name: 'inventoryAmount',
        index: 'inventoryAmount',
        width: 100,
        sortable: false,
        editable: true
    }, {
        name: 'inventoryPrice',
        index: 'inventoryPrice',
        width: 100,
        sortable: false,
        editable: true,
        formatter: function (val) {
            return parseFloat(val).formatMoney('2', '', ',' ,'.');
        },
        unformat: function (val) {
            return val.replace(/,/g ,'');
        }
    }, {
        name: 'inventorySum',
        index: 'inventorySum',
        width: 100,
        sortable: false,
        editable: true,
        formatter: function (val) {
            return parseFloat(val).formatMoney('2', '', ',' ,'.');
        },
        unformat: function (val) {
            return val.replace(/,/g ,'');
        }
    }
    ];
    window.isFirst = true;
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/initBalance/findStockDetailList',
        postData : $('#myform').serializeToJSON(),
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        attachRow:true,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        shrinkToFit:false,  //设置为true时，列数充满表格，当列数较多时，只会缩小列宽，并不会出现滚动条，需要将属性设置为false
        autoScroll: true,    //设置滚动条
        ondblClickRow: function (e, c, a, b) {
            console.log('双击行事件', e, c, a, b);
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
        rownumbers: true,
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['amountIn','sumIn','amountOut','sumOut','inventoryAmount','inventorySum'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item));
            });
        }
    });
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    // 查询数据
    $('#searchBtn').bind('click', function () {
        window.isFirst = false;
        if ($('#startPostDate').val() == '' || $('#endPostDate').val() == '') {
            utils.dialog({content: '请选择过账日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#drugCode').val() == '') {
            utils.dialog({content: '请选择商品！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if (validform("myform").form()) {
            var param = $('#myform').serializeToJSON();
            console.log(param)
            $('#X_Table').setGridParam({
                url: '/proxy-finance/finance/initBalance/findStockDetailList',
                postData: param
            }).trigger('reloadGrid');
            calculatedTotal();
        }

    })

    // 导出
  /*  $('#exportBtn').bind('click', function () {
        // var storeNome=$("#storeNome").val();
        location.href="/proxy-finance/finance/initBalance/exportExcelStockDetailList";
    });*/

    //导出
    $('#exportBtn').on('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            if ($('#startPostDate').val() == '' || $('#endPostDate').val() == '') {
            utils.dialog({content: '请选择过账日期！', quickClose: true, timeout: 2000}).show();
            return false;
            }
            if ($('#drugCode').val() == '') {
                utils.dialog({content: '请选择商品！', quickClose: true, timeout: 2000}).show();
                return false;
            }

    //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            addHtmlA(colNames);
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    // var obj = $("#searchForm").serializeToJSON();
                    // obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    var obj = $('#myform').serializeToJSON();
                    obj["nameModel"] = nameModel;
                    httpPost("/proxy-finance/finance/initBalance/exportExcelStockDetailList", obj);
                },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })
    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }


    // 筛选列，集成到 xgrid.js 里了
    $("#set_tb_rows").click(function () {
        $('#X_Table').XGrid('filterTableHead');
    });
    calculatedTotal();

})

/**
 * 渲染移动类型下拉列表
 */
function initMovementTypeSelectTemplate(data) {
    var html = template('movementType-tmp', {list: data});
    document.getElementById('movementType-div').innerHTML = html;
}

/**
 * 获取移动类型
 */
/*function initMovementTypeSelect() {
    $.post("/proxy-finance/finance/initBalance/findMovementTypeList", { },
        function(data){
            console.log(data.result);
            if (data.code == 0) {
                var _data = data.result;
                initMovementTypeSelectTemplate(_data);
            }
        }, "json");
}*/

/**
 * 单据日期 开始
 */
function startOrderDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        maxDate:'#F{$dp.$D(\'endOrderDate\')}'
    });
}

/**
 * 单据日期 结束
 */
function endOrderDateFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        minDate:'#F{$dp.$D(\'startOrderDate\')}'
    });
}

/**
 * 记账日期 开始
 */
function startPostDateeFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        maxDate:'#F{$dp.$D(\'endPostDate\')}'
    });
}

/**
 * 记账日期 结束
 */
function endPostDateeFun() {
    WdatePicker({
        dateFmt: 'yyyy-MM-dd',
        startDate: getMonthStartDate(),
        isShowToday: false,
        isShowClear: false,
        minDate:'#F{$dp.$D(\'startPostDate\')}'
    });
}

/**
 * 获取当前时间，格式YYYY-MM-DD
 * @returns {string}
 */
function getNowFormatDate() {
    var date = new Date();
    var seperator1 = "-";
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var currentdate = year + seperator1 + month + seperator1 + strDate;
    return currentdate;
}

//获得本月的开始日期
function getMonthStartDate(){
    var now = new Date();                    //当前日期
    var nowDayOfWeek = now.getDay();         //今天本周的第几天
    var nowDay = now.getDate();              //当前日
    var nowMonth = now.getMonth();           //当前月
    var nowYear = now.getYear();             //当前年
    nowYear += (nowYear < 2000) ? 1900 : 0;  //
    var monthStartDate = new Date(nowYear, nowMonth, 1);
    return formatDate(monthStartDate);
}

//格式化日期：yyyy-MM-dd
function formatDate(date) {
    var myyear = date.getFullYear();
    var mymonth = date.getMonth() + 1;
    var myweekday = date.getDate();

    if (mymonth < 10) {
        mymonth = "0" + mymonth;
    }
    if (myweekday < 10) {
        myweekday = "0" + myweekday;
    }
    return (myyear + "-" + mymonth + "-" + myweekday);
}

function calculatedTotal() {
   var da = $('#myform').serializeToJSON();
    $.ajax({
        url: '/proxy-finance/finance/initBalance/calculatedTotal',
        data: $('#myform').serializeToJSON(),
        type: "POST",
        dataType: "JSON",
        success: function (data) {
            $("#totalInventorySum").text(Number(data['totalInventorySum']).toFixed(2));
            $("#totalSumIn").text(Number(data['totalSumIn']).toFixed(2));
            $("#totalSumOut").text(Number(data['totalSumOut']).toFixed(2));
        }
    });
}

function totalTable(data, colName) {
    //初始值
    var count = 0;
    //精确到小数点8位
    var n = 8;
    $.each(data, function (index, item) {
        var item_num = item[colName];
        if (!item_num) item_num = 0;
        try {
            var baseNum = item_num.toString().split('.')[1].length;
            count += Number(item_num) * Math.pow(10, baseNum) * Math.pow(10, n - baseNum);
        } catch (e) {
            count += Number(item_num) * Math.pow(10, n)
        }
    });
    if(colName=='amountIn' || colName=='amountOut' || colName=='inventoryAmount'){
        return parseInt(count / Math.pow(10, n));
    } else {
        return (count / Math.pow(10, n)).toFixed(2);
    }
}