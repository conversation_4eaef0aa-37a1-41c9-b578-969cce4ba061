$(function () {
    /* 参数,页面传递的数据 */
    /* 接收数据要求：
     type:来源类型(0:出库异常 1:损益异常 2:移库异常);
     id:唯一标识;
     data:其他数据;
     */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);
    var paramId = -1;
    var orderType = $('#orderType').val();
    //var orderType = param.orderType;
    if (orderType == '') {
        paramId = 3;
    } else if (orderType == 1) {
        paramId = 0;
    } else if (orderType == 3) {
        paramId = 1;
    }
    //var paramId = 0;
    //设置显示详情类别
    $('.content .abnormal-info').eq(paramId).addClass('abnormal-info-active');

    /* table_a 商品明细*/
    //data
    var grid_dataY = [{
        id: 0,
        productCode: "",
        productName: "",
        specifications: "",
        manufacturerValue: "",
        producingArea: "",
        packingUnitValue: "",
        storeName: "",
        batchCode: "",
        manufactureTime: "",
        expiryTime: "",
        adjustmentNumber: "",
        costPrice: "",
        costAmount: "",
        canEdit: 'true'
    }];
    var colName = ['id', '商品编码','原商品编码', '商品名称', '商品规格', '生产厂家', '产地', '单位', '库房类型', '库房名称', '批号', '生产日期', '有效期至', '可用库存数量',
        '调账数量', '调账后数量', '不含税成本价', '不含税成本金额','业务类型','业务类型', 'canEdit'
    ];
    var colModel = [{
        name: 'id',
        index: 'id',
        hidden: true,
        hideGrid: true
    }, {
        name: 'productCode',
        index: 'productCode'
    }, 	{
        name: 'oldProductCode',
        index: 'oldProductCode'
    }, {
        name: 'productName',
        index: 'productName',
        rowtype: '#text2_e'
    }, {
        name: 'specifications',
        index: 'specifications'
    }, {
        name: 'manufacturerValue',
        index: 'manufacturerValue'
    }, {
        name: 'producingArea',
        index: 'producingArea'
    }, {
        name: 'packingUnitValue',
        index: 'packingUnitvalue'
    }, {
        name: 'storageType',
        index: 'storageType',
        hidden: true,
        hideGrid: true,


    },


        {
            name: 'storeName',
            index: 'storeName',
            rowtype: "#sel_storeName",
            formatter: function (e, id, data) {
                if (data.canEdit == 'false' || data.canEdit == false) {
                    return {rowdata: data.storageType, rowtype: "#sel_storeName_dis"};

                } else {
                    if (e != undefined && e != null) {
                        return e;
                    } else {
                        return ""
                    }


                }

            },
            rowEvent: function (e) {
                var rowData = e.rowData;
                var val = rowData.storeName;
                $('#table_a').XGrid('setRowData', rowData.id, {storageType: val});
            }
        }, {
            name: 'batchCode',
            index: 'batchCode',
            formatter:function (e,id,data) {
                if (e == undefined || e == null) {
                    e = "";
                }
                if(data.canEdit == 'false' || data.canEdit == false){
                    return "<div class='search-box'><input   class= \"form-control\"    style=\"min-width: 110px \"   disabled = \"disabled\"   value="+e+">" +"<i class=\"glyphicon glyphicon-search commodity_batch \" id=\"commodity_batch\"></i></div>"
                }else {
                    return "<div class='search-box'><input   class= \"form-control\"   style=\"min-width: 110px \"    value="+e+">" +"<i class=\"glyphicon glyphicon-search commodity_batch \" id=\"commodity_batch\"></i></div>"
                }

            },
            unformat:function (e,id,dom) {
                return $(dom).find("input").val()
            }

        }, {
            name: 'productDateStr',
            index: 'productDateStr',
            //rowtype: '#text9_e',
            formatter: function (e, id, data) {
                if (data.canEdit == 'false' || data.canEdit == false) {
                    return "<input class=\"Wdate form-control grid_date\"    name=\"text9_e\"   style=\"min-width: 110px\" onclick=\"WdatePicker({startDate:'%y-%M-01',dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true})\" disabled=\"disabled\" value=" + e + ">"
                } else {
                    if (e == undefined || e == null) {
                        e = ""
                    }
                    return "<input class=\"Wdate form-control grid_date\"    name=\"text9_e\"   style=\"min-width: 110px\"  onclick=\"WdatePicker({startDate:'%y-%M-01',dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true})\" value=" + e + ">"
                }

            },
            unformat: function (e, id, dom) {
                return $(dom).children("input").val()
            }
        }, {
            name: 'validateDateStr',
            index: 'validateDateStr',
            //rowtype: '#text10_e'
            formatter: function (e, id, data) {
                if (data.canEdit == 'false' || data.canEdit == false) {
                    return "<input class=\"Wdate form-control grid_date\"    name=\"text10_e\"  style=\"min-width: 110px\"   onclick=\"WdatePicker({startDate:'%y-%M-%d',dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true})\" disabled=\"disabled\" value=" + e + ">"
                } else {
                    if (e == undefined || e == null) {
                        e = ""
                    }

                    return "<input class=\"Wdate form-control grid_date\"    name=\"text10_e\"  style=\"min-width: 110px\"  onclick=\"WdatePicker({startDate:'%y-%M-%d',dateFmt:'yyyy-MM-dd',alwaysUseStartDate:true})\"  value=" + e + ">"
                }

            },
            unformat: function (e, id, dom) {
                return $(dom).children("input").val()
            }
        }, {
            name: 'batchStockNum',
            index: 'batchStockNum',
            rowtype: '#text13_e'
        }, {
            name: 'adjustmentNumber',
            index: 'adjustmentNumber',
            rowtype: '#text12_e',
            rowEvent: function (row) {
                //var rowData = $('#table_a').XGrid('getSeleRow');
                var input_id = $(row.e.target).parent().attr('id');
                var $input = $(row.e.target);
                var rowData = row.rowData;
                if (rowData.productCode == null || rowData.productCode == "") {
                    utils.dialog({
                        title: '提示',
                        content: '请选择商品',
                        width: '300',
                        cancel: false,
                        okValue: '确认',
                        ok: function () {

                        }
                    }).showModal();
                    $input.val("");
                    return
                }

                if (rowData.productCode != null || rowData.productCode != "") {
                    var adjustmentNumber = rowData.adjustmentNumber;
                    var adjustmentPrice = rowData.costPrice
                    if (adjustmentNumber == '' || adjustmentNumber == null) {
                        adjustmentNumber = 0;
                    }

                    if (adjustmentPrice == null || adjustmentPrice == '') {
                        adjustmentPrice = 0;
                    }
                    rowData.costAmount = adjustmentNumber * adjustmentPrice;
                    var batchStockNum = rowData.batchStockNum;
                    var afterAdjustNum = 0;
                    if (batchStockNum == null || batchStockNum == '') {
                        batchStockNum = 0;
                    }
                    adjustmentNumber = adjustmentNumber.replace(/\-+/g, function (e) {
                        console.log(e);
                        if (adjustmentNumber.lastIndexOf(e) == 0) {
                            return '-'
                        } else {
                            return ''
                        }
                    });
                    adjustmentNumber = adjustmentNumber.replace(/0+/g, function (e) {
                        console.log(e);
                        if (adjustmentNumber.indexOf(e) == 0) {
                            return ''
                        } else {
                            return e
                        }
                    });
                    adjustmentNumber = adjustmentNumber ? adjustmentNumber.replace(/[^-1234567890]/g, '') : 0;
                    $input.val(adjustmentNumber);
                    if (!isNaN(rowData.costAmount)) {
                        afterAdjustNum = Number(batchStockNum) + Number(adjustmentNumber);
                        $('#table_a').XGrid('setRowData', rowData.id, {
                            costAmount: rowData.costAmount.toFixed(2),
                            afterAdjustNum: afterAdjustNum
                        });
                    }
                }

            },
        }, {
            name: 'afterAdjustNum',
            index: 'afterAdjustNum'
        }, {
            name: 'costPrice',
            index: 'costPrice',
            rowtype: '#text15_e',
            rowEvent:function(row){
                //var rowData = $('#table_a').XGrid('getSeleRow');
                var input_id = $(row.e.target).parent().attr('id');
                var $input = $(row.e.target);
                var rowData = row.rowData;
                if (rowData.productCode == null || rowData.productCode == "" ){
                    utils.dialog({
                        title: '提示',
                        content: '请选择商品',
                        width: '300',
                        cancel: false,
                        okValue: '确认',
                        ok: function () {

                        }
                    }).showModal();
                    $input.val("");
                    return
                }

                if(rowData.productCode != null || rowData.productCode != ""){
                    var adjustmentNumber = rowData.adjustmentNumber;
                    var adjustmentPrice = rowData.costPrice
                    if ( adjustmentNumber == '' ||  adjustmentNumber  == null){
                        adjustmentNumber = 0;
                    }

                    if(adjustmentPrice == null || adjustmentPrice == ''){
                        adjustmentPrice = 0;
                    }
                    rowData.costAmount =  adjustmentNumber * adjustmentPrice;
                    /*adjustmentPrice =adjustmentPrice?adjustmentPrice.replace(/[^-1234567890]/g,''):0;
                    $input.val(adjustmentPrice);*/
                    if(!isNaN(rowData.costAmount)){
                        $('#table_a').XGrid('setRowData',rowData.id,{costAmount:rowData.costAmount.toFixed(2)});
                    }
                }

            }
        },{
            name: 'costAmount',
            index: 'costAmount'
        },{
            name: 'channelId',
            index: 'channelId',
        },{
            name: 'channelName',
            index: 'channelName',
            hidden: true,
            hidegrid: true
        },{
            name: 'canEdit',
            index: 'canEdit',
            hidden: true,
            hidegrid: true,
        },{
            name: 'oldCostPrice',
            index: 'oldCostPrice',
            hidden:true,
            hidegrid:true
        }


        ];
    $('#table_a').XGrid({
        url: "/proxy-storage/storage/adjustment/findDetailList",
        postData: {adjustmentApplyCode: $("#adjustmentApplyCode").val()},
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'id',
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function () {

        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event);
            console.log("id========" + id);
            var ele = $(event.target);
            if (ele.hasClass('commodity_name')) {
                //选择商品放大镜
                select_goods(id)
            } else if (ele.hasClass('commodity_batch')) {
                var commodity_id = id;
                var rowData =  $('#table_a').XGrid('getRowData',commodity_id);
                var productCode = rowData.productCode;
                var channelId = rowData.channelId;
                if(productCode == undefined || productCode == ''){
                    select_goods(id);
                    return;
                }

                /* 批号放大镜 */
                utils.dialog({
                    url: '/proxy-storage/storage/adjustment/toBatchProductList',
                    title: '批号库存列表',
                    width: $(window).width() * 0.9,
                    height: $(window).height() * 0.9,
                    data: {productCode:productCode,channelId:channelId},
                    onclose: function () {
                        if (this.returnValue) {
                            var returnData = this.returnValue;
                            rowData.batchCode =  returnData.batchCode;
                            rowData.productDateStr =  returnData.productDateStr;
                            rowData.validateDateStr =  returnData.validateDateStr;
                            rowData.batchStockNum= returnData.batchStockNum;
                            var costPrice = returnData.costPrice
                            if (costPrice == null || costPrice == undefined || costPrice == "") {
                                costPrice = 0
                            }
                            rowData.costPrice = Number(costPrice.toFixed(6) );
                            rowData.costAmount =  returnData.costAmount;
                            rowData.canEdit =  returnData.canEdit;
                            rowData.manufacturerValue =  returnData.manufacturerVal;
                            rowData.storageType = returnData.storageType;
                            rowData.adjustmentNumber = 0;
                            rowData.afterAdjustNum = 0;
                            rowData.oldCostPrice = Number(costPrice) ;

                            $('#table_a').XGrid('setRowData',commodity_id,{batchCode:returnData.batchCode, productDateStr:returnData.productDateStr,validateDateStr:returnData.validateDateStr,costPrice:returnData.costPrice,canEdit:false,storageType:returnData.storageType,storeName:returnData.storeName,batchStockNum:returnData.batchStockNum,channelId:returnData.channelId,channelName:returnData.channelName});

                        }
                    },
                    oniframeload: function () {

                    }
                }).showModal();
            }
        }
    });


    /* $("input[name=text12_e]").change(function(){
     var rowData = $('#table_a').XGrid('getSeleRow');
     if (rowData.productCode == null || rowData.productCode == ""){
     utils.dialog({
     title: '提示',
     content: '请选择商品',
     width: '300',
     cancel: false,
     okValue: '确认',
     ok: function () {
     $(this).val("");
     }
     }).showModal();
     }

     return false;


     var value = $(this).val();
     if(value == "" || value == null){
     return false;
     }

     if(isNaN(value)){
     utils.dialog({
     title: '提示',
     content: '请输入数字',
     width: '300',
     cancel: false,
     okValue: '确认',
     ok: function () {
     $(this).val("");
     }
     }).showModal();
     }

     return false;

     })*/


    /* 商品名称查询 */
    /*$('#s_commodity').on('click', function (e) {
     var commodity_val = $(e.target).prev('input').val();
     var commodity_id = $(e.target).parents('tr').attr('id');
     utils.dialog({
     title: '商品列表',
     url: '/proxy-storage/storage/adjustment/toCommodityList',
     width: $(window).width() * 0.8,
     height: 500,

     onclose: function () {
     if (this.returnValue) {
     var data = this.returnValue;
     var productData = data.product;
     var batchData = data.batchData;
     $('#commodity').val(productData.productName)
     $("#productCode").val(productData.productCode);
     var rowData =  $('#table_a').XGrid('getRowData',commodity_id);
     rowData.productCode = productData.productCode;
     rowData.productName = productData.productName;
     rowData.specifications = productData.specifications;
     rowData.producingArea = productData.producingArea;
     rowData.packingUnit = productData.packingUnit;
     rowData.storeName = batchData.storeName;
     rowData.batchCode = batchData.batchCode;
     rowData.manufactureTime = batchData.manufactureTime;
     rowData.expiryTime = batchData.expiryTime;
     rowData.batchStockNum=batchData.batchStockNum;
     rowData.costPrice = batchData.costPrice;
     rowData.costAmount = batchData.costAmount;
     $('#table_a').XGrid('setRowData',commodity_id,rowData);


     }
     $('iframe').remove();
     },
     oniframeload: function () {
     // console.log('iframe ready')
     }
     }).showModal();
     //alert('查询商品名称')
     })*/


    /* 商品名称 搜索提示（只显示5条） */


    /* table_b 出库异常详情*/

    /*function output_abnormal() {
      var grid_dataY = [];
      for (var i = 0; i < 20; i++) {
        grid_dataY.push({
          sort: i,
          text1: "",
          text2: "",
          text3: "",
          text4: "",
          text5: "",
          text6: "",
          text7: "",
          text8: "",
          text9: "",
          text10: "",
          text11: "",
          text12: i == 1 ? "" : "2"
        });
      }
      var colName = ['商品编码', '商品名称', '商品规格', '生产厂家', '单位', '商品产地', '批准文号', '出库批号', '生产日期', '有效期至', 'ERP账面数量',
        '出库数量', '异常原因'
      ];
      var colModel = [{
        name: 'productCode',
        index: 'productCode'
      }, {
        name: 'productName',
        index: 'productName'
      }, {
        name: 'specifications',
        index: 'specifications'
      }, {
        name: 'manufacturerName',
        index: 'manufacturerName'
      },  {
        name: 'packingUnitValue',
        index: 'packingUnitValue'
      }, {
        name: 'producingArea',
        index: 'producingArea'
      },{
        name: 'approvalNumber',
        index: 'approvalNumber'
      }, {
        name: 'batchCode',
        index: 'batchCode'

      },  {
        name: 'manufactureTime',
        index: 'manufactureTime',
        formatter: dateFormatter

      }, {
        name: 'expiryTime',
        index: 'expiryTime'

      }, {
        name: 'storageAmount',
        index: 'storageAmount'
      }, {
        name: 'outNum',
        index: 'outNum'

      }, {
        name: 'exceptionRemarks',
        index: 'exceptionRemarks'
      }

      ];
      $('#table_b').XGrid({
        url: "/proxy-storage/storage/adjustment/findExceptionDetailList",
        postData:{orderType:$("#orderType").val(),orderCode:$("#orderCode").val()},
        colNames: colName,
        colModel: colModel,
        rownumbers: true,
        key: 'sort',
        rowNum: 0,
        //pager: '#grid_pager_a',
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function () {
          setTimeout(function name() {
            var data = $('#table_b').XGrid('getRowData');
            data.forEach(function (item, index) {
              if (item.text12) {
                $('#table_b #' + item['id']).addClass('warnning')
              }
            });
          }, 200);
        },
        onSelectRow: function (id, dom, obj, index, event) {
          //选中事件
          //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
          console.log(id, dom, obj, index, event)
        }
      });
    }*/


    function dateFormatter(val) {
        if (val != null && val != undefined) {
            var date = new Date(val);
            return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
        } else {
            return '';
        }
    }

//  output_abnormal();


    /* table_c 损溢异常详情*/
    /*  function GorL_abnormal() {

        var colName = ['商品编码', '商品名称', '商品规格', '产地', '生产厂家', '单位', '库房名称', '损溢批号', '生产日期', '有效期至', '库存数量',
          '占用库存', '损溢数量', '不含税成本价', '不含税成本金额', '含税价', '含税金额', '异常原因'
        ];
        var colModel = [{
          name: 'productCode',
          index: 'productCode'
        }, {
          name: 'productName',
          index: 'productName'
        }, {
          name: 'specifications',
          index: 'specifications'
        }, {
          name: 'producingArea',
          index: 'producingArea'
        },{
          name: 'manufacturerName',
          index: 'manufacturerName'
        },  {
          name: 'packingUnitValue',
          index: 'packingUnitValue'
        },{
          name: 'storeName',
          index: 'storeName',
            formatter: function (e) {
                if (e == '1') {
                    return '合格库'
                } else if (e == '2') {
                    return '不合格库'
                }else if (e == '3') {
                    return '暂存库'
                }
            }
        },

          {
            name: 'batchCode',
            index: 'batchCode'
          },   {
            name: 'manufactureTime',
            index: 'manufactureTime',
            formatter: dateFormatter

          }, {
            name: 'expiryTime',
            index: 'expiryTime'

          }, {
            name: 'storageAmount',
            index: 'storageAmount'
          }, {
            name: 'newamount',
            index: 'newamount'
          }, {
            name: 'causticExcessiveNumber',
            index: 'causticExcessiveNumber'
          }, {
            name: 'costPrice',
            index: 'costPrice'
          }, {
            name: 'costAmount',
            index: 'costAmount'
          }, {
            name: 'productTaxPrice',
            index: 'productTaxPrice'
          }, {
            name: 'taxAmount',
            index: 'taxAmount'
          }, {
            name: 'exceptionRemarks',
            index: 'exceptionRemarks'
          }];
        $('#table_c').XGrid({
          url: "/proxy-storage/storage/adjustment/findExceptionDetailList",
          postData:{orderType:$("#orderType").val(),orderCode:$("#orderCode").val()},
          colNames: colName,
          colModel: colModel,
          rownumbers: true,
          key: 'sort',
          rowNum: 0,
          altRows: true, //设置为交替行表格,默认为false
          pager: '#grid_pager_a',
          ondblClickRow: function (id, dom, obj, index, event) {

          },
          gridComplete: function () {

          },
          onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
            console.log(id, dom, obj, index, event)
          }
        });
      }*/
    //GorL_abnormal();

    /* table_d 移库异常详情*/
    function Mobile_abnormal() {
        var grid_dataY = [{
            sort: "1",
            text1: "",
            text2: "",
            text3: "",
            text4: "",
            text5: "",
            text6: "",
            text7: "",
            text8: "",
            text9: "",
            text10: "",
            text11: "",
            text12: "2",
            text13: "",
            text14: "",
            text15: "4",
            text16: "6",
            text17: "",
            text18: "",
            text19: "",
            text20: ""
        }];
        for (var i = 0; i < 20; i++) {
            grid_dataY.push(grid_dataY[0]);
        }
        var colName = ['商品编码', '商品名称', '商品规格', '产地', '生产厂家', '单位', '移出库房名称', '移入库房名称', '批号', '生产日期', '有效期至', '移出库房账面数量',
            '移动数量', '不含税成本价', '不含税成本金额', '异常原因'
        ];
        var colModel = [{
            name: 'sort',
            index: 'sort'
        }, {
            name: 'text1',
            index: 'text1'
        }, {
            name: 'text2',
            index: 'text2'
        }, {
            name: 'text3',
            index: 'text3'
        }, {
            name: 'text4',
            index: 'text4'
        }, {
            name: 'text5',
            index: 'text5'
        }, {
            name: 'text6',
            index: 'text6'
        }, {
            name: 'text7',
            index: 'text7'
        }, {
            name: 'text8',
            index: 'text8'
        }, {
            name: 'text9',
            index: 'text9'
        }, {
            name: 'text10',
            index: 'text10'
        }, {
            name: 'text11',
            index: 'text11'
        }, {
            name: 'text12',
            index: 'text12'
        }, {
            name: 'text11',
            index: 'text11'
        }, {
            name: 'text11',
            index: 'text11'
        }, {
            name: 'text11',
            index: 'text11'
        }];
        $('#table_d').XGrid({
            data: grid_dataY,
            colNames: colName,
            colModel: colModel,
            rownumbers: true,
            key: 'sort',
            rowNum: 20,
            rowList: [20, 50, 100],
            altRows: true, //设置为交替行表格,默认为false
            pager: '#grid_pager_a',
            ondblClickRow: function (id, dom, obj, index, event) {

            },
            gridComplete: function () {

            },
            onSelectRow: function (id, dom, obj, index, event) {
                //选中事件
                //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
                console.log(id, dom, obj, index, event)
            }
        });
    }

    Mobile_abnormal();

    /* 提交审核 */
    $('#sub_check').on('click', function () {
        //输入内容校验
        if(!validform('form_a').form()) return;
        if(!validform('table_form').form()) return;
        var form_data = $('#form_a').serializeToJSON();
        var table_data = $('#table_a').XGrid('getRowData');
        if (table_data.length == 0) {
            utils.dialog({
                title: '提示',
                content: '调账明细不能为空',
                width: '300',
                cancel: false,
                okValue: '确认',
                ok: function () {
                }
            }).showModal();

            return false;
        }

        var detailArray = JSON.stringify(table_data);
        form_data.detailArray = detailArray;

        $.ajax({
            url: "/proxy-storage/storage/adjustment/updateAndApprove",
            type: "post",
            dataType: "json",
            data: form_data,
            success: function (result) {
                if (result.code == 1) {
                    utils.dialog({
                        title: '提示',
                        content: result.msg,
                        width: '300',
                        cancel: false,
                        okValue: '确认',
                        ok: function () {
                        }
                    }).showModal();
                } else if (result.code == 0) {
                    utils.closeTab({reload:true});
                }
            }
        })

        console.log(form_data, table_data);
    });

    /* 返回 */
    $('#goback').on('click', function () {
        utils.dialog({
            title: '提示',
            content: '返回后当前页面数据将丢失，是否继续',
            okValue: '确定',
            ok: function () {
                utils.closeTab();
            },
            cancelValue: '取消',
            cancel: function () {
            }
        }).showModal()
    });

    /* 批号放大镜弹窗 */
    /* $('.commodity_batch').on('click', function () {
     utils.dialog({
     url: '/proxy-storage/storage/adjustment/toBatchProductList',
     title: '批号库存列表',
     width: $(window).width() * 0.9,
     height: $(window).height() * 0.7,
     data: {productCode:$('#productCode').val()},
     onclose: function () {
     if (this.returnValue) {
     var data = this.returnValue;
     console.log(data);
     }
     },
     oniframeload: function () {

     }
     }).showModal();
     });*/

    /* 新增 */
    $('#addRoW').on('click', function () {
        var data = $('#table_a').XGrid('getRowData');

        var item_data = {
            id: data?(data.length ? Number(data[data.length-1].id)+1 : 1):0,
            text2: "",
            text3: "",
            text4: "",
            text5: "",
            text6: "",
            text7: "",
            text8: "",
            text9: "",
            text10: "",
            text11: "2",
            text12: "",
            text13: "1",
            canEdit: 'true'
        };
        $('#table_a').XGrid('addRowData', item_data, 'last');
    });

    /* 删除 */
    $('#removeRoW').on('click', function () {
        var data = $('#table_a').XGrid('getSeleRow');
        if (!data) {
            utils.dialog({
                title: "提示",
                width: 200,
                height: 80,
                content: "请先选中一行数据",
                timeout: 2000,
            }).showModal();
            return
        }
        //删除二次确认
        utils.dialog({
            title: "提示",
            content: "确认删除当前选中行？",
            okValue: '确定',
            ok: function () {
                if (data.length) {
                    $.each(data, function (index, item) {
                        $('#table_a').XGrid('delRowData', item.id);
                    })
                } else {
                    $('#table_a').XGrid('delRowData', data.id);
                }
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    });

    /*    //付款、结算只可输入数字
        $(document).on('input','.cValue',function(){
            var v = $(this).val();
            if(v.indexOf('.')>0){
                v = v.replace('.','');
                $(this).val(v);
            }
            if(v.indexOf('-')>0){
                v = v.replace('-','-');
                $(this).val(v);
            }


            if(v.indexOf('.') == 0){
                v = v.replace('.','');
                $(this).val(v);
            }

            // var max=$.trim($(this).attr("data-max"));
            // if(max && max != ''){
            //     $(this).val($(this).val().replace(/[^-123456789]/,''));
            //     var value=$.trim($(this).val());
            //     if(Number(value) >=  Number(max))
            //     {
            //         $(this).val(max);
            //         return ;
            //     }
            // }else{
            //     $(this).val($.trim($(this).val()).replace(/[^-123456789]/,''));
            // }
        });*/


    function  select_goods(id) {
        /* 商品名称放大镜 */
        var commodity_id = id;
        var rowData =  $('#table_a').XGrid('getRowData',commodity_id);
        utils.dialog({
            title: '商品列表',
            url: '/proxy-storage/storage/adjustment/toCommodityList',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.85,
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    var productData = data.product;
                    var batchDataArray = data.batchData;
                    $('#table_a').XGrid('delRowData', commodity_id);
                    if (batchDataArray.length != undefined ){
                        var all_data = $('#table_a').XGrid('getRowData');
                        for (var i = 0 ; i< batchDataArray.length; i++){
                            var rowData1 =  {};
                            rowData1.productCode = productData.productCode;
                            rowData1.productName = productData.productName;
                            rowData1.specifications = productData.specifications;
                            rowData1.producingArea = productData.producingArea;
                            rowData1.packingUnitValue = productData.packingUnitVal;
                            rowData1.storeName = batchDataArray[i].storeName;
                            rowData1.batchCode =  batchDataArray[i].batchCode;
                            rowData1.productDateStr =  batchDataArray[i].productDateStr;
                            rowData1.validateDateStr =  batchDataArray[i].validateDateStr;
                            rowData1.costPrice = Number(batchDataArray[i].costPrice).toFixed(6) ;
                            rowData1.batchStockNum = batchDataArray[i].batchStockNum ;
                            rowData1.costAmount =  batchDataArray[i].costAmount;
                            rowData1.canEdit =  batchDataArray[i].canEdit;
                            rowData1.manufacturerValue =  productData.manufacturerVal;
                            rowData1.storageType = batchDataArray[i].storageType;
                            rowData1.channelId =  batchDataArray[i].channelId;
                            rowData1.channelName =  batchDataArray[i].channelName;
                            rowData1.adjustmentNumber = 0;
                            rowData1.afterAdjustNum = 0;
                            rowData1.oldCostPrice = Number(batchDataArray[i].costPrice).toFixed(6) ;
                            rowData1.id = Number(commodity_id)+Number(i)+1;

                            if(all_data&&all_data.length>0){
                                var flag = all_data.some(function (item,index) {
                                    return item.productCode==rowData1.productCode&&item.batchCode==rowData1.batchCode;
                                });
                                if(!flag) $('#table_a').XGrid('addRowData', rowData1, 'last');
                            }else {
                                $('#table_a').XGrid('addRowData', rowData1, 'last');
                            }
                        }
                    }else {

                        rowData.productCode = productData.productCode;
                        rowData.productName = productData.productName;
                        rowData.specifications = productData.specifications;
                        rowData.producingArea = productData.producingArea;
                        rowData.packingUnitValue = productData.packingUnitVal;
                        rowData.storeName = batchDataArray.storeName;
                        rowData.batchCode =  batchDataArray.batchCode;
                        rowData.productDateStr =  batchDataArray.productDateStr;
                        rowData.validateDateStr =  batchDataArray.validateDateStr;
                        rowData.costPrice = Number(batchDataArray.costPrice).toFixed(6) ;
                        rowData.batchStockNum = batchDataArray.batchStockNum ;
                        rowData.costAmount =  batchDataArray.costAmount;
                        rowData.canEdit =  batchDataArray.canEdit;
                        rowData.manufacturerValue =  productData.manufacturerVal;
                        rowData.storageType = batchDataArray.storageType;
                        rowData.channelId =  batchDataArray.channelId;
                        rowData.channelName =  batchDataArray.channelName;
                        rowData.id = commodity_id;
                        rowData.adjustmentNumber = 0;
                        rowData.afterAdjustNum = 0;
                        rowData.oldCostPrice = Number(batchDataArray.costPrice).toFixed(6) ;
                        var all_data = $('#table_a').XGrid('getRowData');
                        if(all_data&&all_data.length>0){
                            var flag = all_data.some(function (item,index) {
                                return item.productCode==rowData.productCode&&item.batchCode==rowData.batchCode;
                            });
                            if(!flag) $('#table_a').XGrid('addRowData', rowData, 'last');
                        }else {
                            $('#table_a').XGrid('addRowData', rowData, 'last');
                        }


                    }
                }
                //$('iframe').remove();
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();
    }

})

//商品名称 自动补全查找
var _objArr = [];
/*function fun_autoCom(el) {
 console.log(el)
 var elId = el.getAttribute('id');
 $('#'+elId).find('input').attr('id','inp_'+elId);
 $('#'+elId).parents('td').nextAll().find('select').attr('id','sel_'+elId);
 $('#inp_'+elId).Autocomplete({
 serviceUrl: '/orderReturn/orderReturnController/getAllProductList?orgCode=002', //异步请求
 paramName: 'param',//查询参数，默认
 dataType: 'json',
 minChars: '0', //触发自动匹配的最小字符数
 maxHeight: '300', //默认300高度
 showNoSuggestionNotice: true, //显示查无结果的container
 noSuggestionNotice: '查询无结果',//查无结果的提示语
 transformResult: function (response) {
 return {
 suggestions: $.map(response.result.list, function (dataItem) {
 return {value: dataItem.productName, data: dataItem};
 })
 };
 },
 triggerSelectOnValidInput: false, // 必选
 // multi: true, //多选要和delimiter一起使用
 // delimiter: ',',
 // showNoSuggestionNotice: true, //显示查无结果的container
 // noSuggestionNotice: '查询无结果',//查无结果的提示语
 // tabDisabled: true,
 onSelect: function (_select) {
 console.log(_select);
 //选中回调
 var oldProductCode = $("#productCode").val()
 var productCode =  _select.value;
 if (oldProductCode != productCode){

 $.ajax({
 url:"/proxy-storage/storage/adjustment/findBatchProductList",
 type:"post",
 dataType:"json",
 data:{productCode:productCode},
 success:function(data){
 console.log(data.result)

 var _html  = '';
 if(data.code == 0){
 for(var i= 0; i<data.result.length; i++){

 }
 $('#sel_'+elId).html(_html);
 }else{
 console.log('出错')
 }

 }
 })

 }

 $("#productCode").val(productCode)




 },
 onSearchStart: function (params) {
 // console.log('检索开始回调', params)
 },
 onSearchComplete: function (query, suggestions) {
 //匹配结果后回调
 console.log(query, suggestions);
 },
 onSearchError: function (query, jqXHR, textStatus, errorThrown) {
 //查询失败回调
 console.log(query, jqXHR, textStatus, errorThrown)
 },
 onHide: function (container) {
 // console.log('container隐藏前回调', container)
 },
 onNoneSelect: function (params, suggestions) {
 console.log(params, suggestions);
 console.log('没选中回调函数');
 $("#commodity").val('');
 $("#productCode").val('');

 }
 });
 }*/

//批号 change  切换
function fun_pihaoChange(el) {
    $.each(_objArr, function (ind, item) {
        if (el.value == item.batchCode) {
            var elId = el.getAttribute('id');
            $('#' + elId).find('input').attr('id', 'inp_' + elId);
            $('#' + elId).parents('td').nextAll().find('input[name=text9_e]').val(item.productDateStr);
            $('#' + elId).parents('td').nextAll().find('input[name=text10_e]').val(item.validateDateStr);
            $('#' + elId).parents('td').nextAll().find('input[name=text13_e]').val(item.batchStockNum);
        }
    })

}


//调账数量为负时，禁用不含税成本价的输入框
$("#table_a").on("blur","input[name=adjustmentNumber]",function (e) {
    var ele = $(e.target);
    var val = ele.val()?ele.val():0;
    var tr_ele = ele.parents("tr");
    if(Number(val)<0){
        var row_data = $("#table_a").XGrid("getRowData",tr_ele.attr('id'));
        tr_ele.find("input[name=costPrice]").val(row_data.oldCostPrice).attr('disabled',true)
    }else {
        tr_ele.find("input[name=costPrice]").attr('disabled',false)
    }
});

