// var parent_dialog = parent.dialog.get(window);

var grid_data = [];
var tableIndexs=1;
var medicalType = "1"; // 普药:1 特药：2
var tab = 1; // 1:普通药品;2:中药;3:医疗药品;4:非药品;5:冷藏药品;6:特殊管理类药品 后台使用
$('#storageConditionsFrist').hide();
$('#tableBoxFSecond').hide();
$('#tableBoxFThird').hide();
$('#tableBoxFFour').hide();
$('#tableBoxFFive').hide();
$('#tableBoxFSix').hide();

$("#seleteTips>li").on("click",function () {
    if(tableIndexs==$(this).data("index")){
        return false;
    }
    $("#seleteTips>li").removeClass("active");
    $(this).addClass("active");
    tableIndexs=$(this).data("index");
    $('#X_Table').XGrid("clearGridData");
    $('#X_Table2').XGrid("clearGridData");
    $('#X_Table3').XGrid("clearGridData");
    $('#X_Table4').XGrid("clearGridData");
    $('#X_Table5').XGrid("clearGridData");
    $('#X_Table6').XGrid("clearGridData");
    if (tableIndexs==1) { // 普通药品
        medicalType = "1"
        tab = 1
    }else if(tableIndexs==2){
        tab = 5
        medicalType = "1"
    }else if(tableIndexs==3){
        medicalType = "2"
        tab = 6
    }else if(tableIndexs==4){
        medicalType = "1"
        tab = 2
    }else if(tableIndexs==5){
        medicalType = "1"
        tab = 3
    }else if(tableIndexs==6){
        medicalType = "1"
        tab = 4
    }
    btn_search();
})
//查询
function btn_search(){
    var formData;
    if(tableIndexs==1){
        $('#tableBoxFrist').show();
        $('#storageConditions').attr("disabled",false);
        $('#storageConditions').show();
        $('#tableBoxFSecond').hide();
        $('#tableBoxFThird').hide();
        $('#tableBoxFFour').hide();
        $('#tableBoxFFive').hide();
        $('#tableBoxFSix').hide();
        $('#storageConditionsFrist').attr("disabled",true);
        $('#storageConditionsFrist').hide();
        formData = $("#saleOrderForm").serializeToJSON();
        $('#X_Table').setGridParam({
            url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList",
            postData: {
                "pageFlag": "1",
                "startTime":$("#startTime").val(),
                "endTime":$("#endTime").val(),
                "productCodeOrName":$("#productCodeOrName").val(),
                "scopeOfOperation":$("#scopeOfOperation").val(),
                "largeCategory":$("#largeCategory").val(),
                "storageConditions":$("#storageConditions").val(),
                "productCode":$("#productCode").val(),
                "supplierCode":$("#supplierCode").val(),
                "type":medicalType,
                "tab": tab
            },page:1
        }).trigger('reloadGrid');
    }else if (tableIndexs==2){
        $('#tableBoxFSecond').show();
        $('#storageConditionsFrist').attr("disabled",false);
        $('#storageConditionsFrist').show();
        $('#tableBoxFrist').hide();
        $('#tableBoxFThird').hide();
        $('#tableBoxFFour').hide();
        $('#tableBoxFFive').hide();
        $('#tableBoxFSix').hide();
        $('#storageConditions').attr("disabled",true);
        $('#storageConditions').hide();
        formData = $("#saleOrderForm").serializeToJSON();
        $('#X_Table2').setGridParam({
            url: "/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodColdRecordList",
            postData: {
                "pageFlag": "1",
                "startTime":$("#startTime").val(),
                "endTime":$("#endTime").val(),
                "productCodeOrName":$("#productCodeOrName").val(),
                "scopeOfOperation":$("#scopeOfOperation").val(),
                "largeCategory":$("#largeCategory").val(),
                "storageConditions":$("#storageConditionsFrist").val(),
                "productCode":$("#productCode").val(),
                "supplierCode":$("#supplierCode").val(),
                "type":medicalType,
                "tab": tab
            },page:1
        }).trigger('reloadGrid');
    }else if (tableIndexs==3){
        $('#tableBoxFThird').show();
        $('#storageConditions').attr("disabled",false);
        $('#storageConditions').show();
        $('#tableBoxFrist').hide();
        $('#tableBoxFSecond').hide();
        $('#tableBoxFFour').hide();
        $('#tableBoxFFive').hide();
        $('#tableBoxFSix').hide();
        $('#storageConditionsFrist').attr("disabled",true);
        $('#storageConditionsFrist').hide();
        formData = $("#saleOrderForm").serializeToJSON();
        $('#X_Table3').setGridParam({
            url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList",
            postData: {
                "pageFlag": "1",
                "startTime":$("#startTime").val(),
                "endTime":$("#endTime").val(),
                "productCodeOrName":$("#productCodeOrName").val(),
                "scopeOfOperation":$("#scopeOfOperation").val(),
                "largeCategory":$("#largeCategory").val(),
                "storageConditions":$("#storageConditions").val(),
                "productCode":$("#productCode").val(),
                "supplierCode":$("#supplierCode").val(),
                "type":medicalType,
                "tab": tab
            },page:1
        }).trigger('reloadGrid');
    }else if (tableIndexs==4){
        $('#tableBoxFFour').show();
        $('#storageConditions').attr("disabled",false);
        $('#storageConditions').show();
        $('#tableBoxFSecond').hide();
        $('#tableBoxFrist').hide();
        $('#tableBoxFThird').hide();
        $('#tableBoxFFive').hide();
        $('#tableBoxFSix').hide();
        $('#storageConditionsFrist').attr("disabled",true);
        $('#storageConditionsFrist').hide();
        formData = $("#saleOrderForm").serializeToJSON();
        formData.type = medicalType
        $('#X_Table4').setGridParam({
            url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList",
            postData: {
                "pageFlag": "1",
                "startTime":$("#startTime").val(),
                "endTime":$("#endTime").val(),
                "productCodeOrName":$("#productCodeOrName").val(),
                "scopeOfOperation":$("#scopeOfOperation").val(),
                "largeCategory":$("#largeCategory").val(),
                "storageConditions":$("#storageConditions").val(),
                "productCode":$("#productCode").val(),
                "supplierCode":$("#supplierCode").val(),
                "type":medicalType,
                "tab": tab
            },page:1
        }).trigger('reloadGrid');
    }else if (tableIndexs==5){
        $('#tableBoxFFive').show();
        $('#storageConditions').attr("disabled",false);
        $('#storageConditions').show();
        $('#tableBoxFrist').hide();
        $('#tableBoxFSecond').hide();
        $('#tableBoxFThird').hide();
        $('#tableBoxFFour').hide();
        $('#tableBoxFSix').hide();
        $('#storageConditionsFrist').attr("disabled",true);
        $('#storageConditionsFrist').hide();
        formData = $("#saleOrderForm").serializeToJSON();
        formData.type = medicalType
        $('#X_Table5').setGridParam({
            url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList",
            postData: {
                "pageFlag": "1",
                "startTime":$("#startTime").val(),
                "endTime":$("#endTime").val(),
                "productCodeOrName":$("#productCodeOrName").val(),
                "scopeOfOperation":$("#scopeOfOperation").val(),
                "largeCategory":$("#largeCategory").val(),
                "storageConditions":$("#storageConditions").val(),
                "productCode":$("#productCode").val(),
                "supplierCode":$("#supplierCode").val(),
                "type":medicalType,
                "tab": tab
            },page:1
        }).trigger('reloadGrid');
    }else  {
        $('#tableBoxFSix').show();
        $('#storageConditions').attr("disabled",false);
        $('#storageConditions').show();
        $('#tableBoxFrist').hide();
        $('#tableBoxFSecond').hide();
        $('#tableBoxFThird').hide();
        $('#tableBoxFFour').hide();
        $('#tableBoxFFive').hide();
        $('#storageConditionsFrist').attr("disabled",true);
        $('#storageConditionsFrist').hide();
        formData = $("#saleOrderForm").serializeToJSON();
        formData.type = medicalType
        $('#X_Table6').setGridParam({
            url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList",
            postData: {
                "pageFlag": "1",
                "startTime":$("#startTime").val(),
                "endTime":$("#endTime").val(),
                "productCodeOrName":$("#productCodeOrName").val(),
                "scopeOfOperation":$("#scopeOfOperation").val(),
                "largeCategory":$("#largeCategory").val(),
                "storageConditions":$("#storageConditions").val(),
                "productCode":$("#productCode").val(),
                "supplierCode":$("#supplierCode").val(),
                "type":medicalType,
                "tab": tab
            },page:1
        }).trigger('reloadGrid');
    }
}
$("#SearchBtn").on("click", function () {
    btn_search();
});
//重置
$("#resetBtn").on("click", function () {
    initDate();
    btn_search();
    // $('#X_Table').XGrid('setGridParam', {
    // url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList",
    // postData: {
    //         "pageFlag": "1",
    //         "startTime":$("#startTime").val(),
    //         "endTime":$("#endTime").val()
    //     },page:1
    // }).trigger('reloadGrid');
});


/* 打印预览 */
$("#reviewBtn").on("click",function () {
    var currentPageData;
    if (tableIndexs==1) {
        currentPageData = $('#X_Table').getRowData();
    }else if (tableIndexs == 2) {
        currentPageData = $('#X_Table2').getRowData();
    }else if (tableIndexs == 3){
        currentPageData = $('#X_Table3').getRowData();
    }else if (tableIndexs == 4) {
        currentPageData = $('#X_Table4').getRowData();
    }else if (tableIndexs == 5) {
        currentPageData = $('#X_Table5').getRowData();
    }else {
        currentPageData = $('#X_Table6').getRowData();
    }

    if(currentPageData.length==0){
        utils.dialog({content: '无打印数据', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    $("#print_box")[0].contentWindow.getData(0,currentPageData);
});
$("#printBtn").on("click",function () {
    var currentPageData;
    if (tableIndexs==1) {
        currentPageData = $('#X_Table').getRowData();
    }else if (tableIndexs == 2) {
        currentPageData = $('#X_Table2').getRowData();
    }else if (tableIndexs == 3){
        currentPageData = $('#X_Table3').getRowData();
    }else if (tableIndexs == 4){
        currentPageData = $('#X_Table4').getRowData();
    }else if (tableIndexs == 5){
        currentPageData = $('#X_Table5').getRowData();
    }else{
        currentPageData = $('#X_Table6').getRowData();
    }
    if(currentPageData.length==0){
        utils.dialog({content: '无打印数据', quickClose: true, timeout: 2000}).showModal();
        return false;
    }
    utils.dialog({
        content:"正在打印...",
        timeout:1000
    }).showModal();
    $("#print_box")[0].contentWindow.getData(1,currentPageData);
});



function initDate(param) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    var month = new_date.getMonth() + 1;
    var date = new_date.getDate();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    //console.log(new_date, year, month, date);
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#startTime').val(year + '-' + month + '-'+'01');
    $('#endTime').val(year + '-' + month + '-' + date);
}
    var fristColNames = ['','商品编码', '通用名称', '商品大类','所属经营范围', '规格', '剂型', '生产厂家', '产地', '供货单位名称','供货单位编码','数量','单价', '金额 ',
        '退货日期',/* '灭菌批号',*/ '批号', '生产日期', '有效期至', '采购员','退货原因','批准文号','包装单位','上市许可持有人','存储条件'
    ];
    var secondColNames = ['','商品编码', '通用名称', '商品大类','所属经营范围', '规格', '剂型', '生产厂家', '供货单位名称','供货单位编码','数量','单价', '金额 ',
        '退货日期',/* '灭菌批号',*/ '批号', '生产日期', '有效期至', '采购员','退货原因','批准文号','包装单位','上市许可持有人','存储条件'
    ];
    var thirdColNames=  ['','商品编码', '通用名称', '商品大类','所属经营范围', '规格', '剂型', '生产厂家', '产地', '供货单位名称', '供货单位编码','数量','单价', '金额 ',
        '退货日期', /*'灭菌批号', */'批号', '生产日期', '有效期至', '采购员','退货原因','批准文号','包装单位','上市许可持有人','存储条件'
    ];
    var fourColNames= ['','商品编码', '通用名称', '商品大类','所属经营范围', '规格', '剂型', '生产厂家', '产地', '供货单位名称', '供货单位编码','数量','单价', '金额 ',
        '退货日期', /*'灭菌批号',*/ '批号', '生产日期', '有效期至', '采购员','退货原因','批准文号','包装单位','上市许可持有人','存储条件'
    ];
    var fiveColNames=  ['','商品编码', '通用名称', '商品大类','所属经营范围', '规格/型号', '剂型', '生产厂家', '产地', '供货单位名称', '供货单位编码','数量','单价', '金额 ',
        '退货日期', /*'灭菌批号', */'批号/序列号', '生产日期', '有效期至/失效期', '采购员','退货原因','注册证号/备案凭证号','包装单位','上市许可持有人','存储条件'
    ];
    var sixColNames=   ['','商品编码', '通用名称', '商品大类','所属经营范围', '规格', '剂型', '生产厂家', '产地', '供货单位名称', '供货单位编码','数量','单价', '金额 ',
        '退货日期', /*'灭菌批号', */'批号', '生产日期', '有效期至', '采购员','退货原因','批准文号','包装单位','上市许可持有人','存储条件'
    ];

    var fristcolModel=[
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },{
            name: 'productCode',
            index: 'productCode',
        }, {
            name: 'productName',
            index: 'productName',
            width: 200

        },{
            name: 'largeCategory',
            index: 'largeCategory',
        },{
            name: 'scopeOfOperation',
            index: 'scopeOfOperation',
        }, {
            name: 'productSpecification',
            index: 'productSpecification',
            width: 200
        }, {
            name: 'productPreparation',
            index: 'productPreparation',
        }, {
            name: 'productProduceFactory',
            index: 'productProduceFactory',
            width: 250
        }, {
            name: 'productOriginPlace',
            index: 'productOriginPlace',
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        }, {
            name: 'supplierCode', // 供应单位编码
            index: 'supplierCode',
            width: 250
        }, {
            name: 'productPackCount',
            index: 'productPackCount',
        }, {
            name: 'productContainTaxPrice',// 单价
            index: 'productContainTaxPrice'
        },
        {
            name: 'realRefundContainTax',// 金额
            index: 'realRefundContainTax'
        },{
            name: 'createTime',
            index: 'createTime',
            formatter:function(value){
                return new Date(value).Format('yyyy-MM-dd');
            }

        }
        , {
            name: 'productApprovalNumber',
            index: 'productApprovalNumber',
            width: 200

        }, {
            name: 'productProduceDate',
            index: 'productProduceDate',

        }, {
            name: 'productExpireDate',
            index: 'productExpireDate',

        }, {
            name: 'createUserName',
            index: 'createUserName',
        }, {
            name: 'rejectTypeName',
            index: 'rejectTypeName',
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width:240
        }, {
            name: 'productPackUnitSmall',
            index: 'productPackUnitSmall',
        }, {
            name: 'marketAuthor',
            index: 'marketAuthor'
        }, {
            name: 'storageConditionsName',
            index: 'storageConditionsName'
        }
    ],allColModelA = JSON.parse(JSON.stringify(fristcolModel));
    var secondcolModel= [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },{
            name: 'productCode',
            index: 'productCode',
        }, {
            name: 'productName',
            index: 'productName',
            width: 200

        },{
            name: 'largeCategory',
            index: 'largeCategory',
        },{
            name: 'scopeOfOperation',
            index: 'scopeOfOperation',
        }, {
            name: 'productSpecification',
            index: 'productSpecification',
            width: 200
        }, {
            name: 'productPreparation',
            index: 'productPreparation',
        }, {
            name: 'productProduceFactory',
            index: 'productProduceFactory',
            width: 250
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        }, {
            name: 'supplierCode', // 供应单位编码
            index: 'supplierCode',
            width: 250
        }, {
            name: 'productPackCount',
            index: 'productPackCount',
        },{
            name: 'productContainTaxPrice',// 单价
            index: 'productContainTaxPrice'
        },
        {
            name: 'realRefundContainTax',// 金额
            index: 'realRefundContainTax'
        }, {
            name: 'createTime',
            index: 'createTime',
            formatter:function(value){
                return new Date(value).Format('yyyy-MM-dd');
            }

        }/*, {
        }*/
        , {
            name: 'productApprovalNumber',
            index: 'productApprovalNumber',
            width: 200

        }, {
            name: 'productProduceDate',
            index: 'productProduceDate',

        }, {
            name: 'productExpireDate',
            index: 'productExpireDate',

        }, {
            name: 'createUserName',
            index: 'createUserName',
        }, {
            name: 'rejectTypeName',
            index: 'rejectTypeName',
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width:240
        }, {
            name: 'productPackUnitSmall',
            index: 'productPackUnitSmall',
        }, {
            name: 'marketAuthor',
            index: 'marketAuthor'
        },
        {
            name: 'storageConditionsName',
            index: 'storageConditionsName'
        }
    ],allColModelB = JSON.parse(JSON.stringify(secondcolModel));
    var thirdcolModel=  [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },{
            name: 'productCode',
            index: 'productCode',
        }, {
            name: 'productName',
            index: 'productName',
            width: 200

        },{
            name: 'largeCategory',
            index: 'largeCategory',
        },{
            name: 'scopeOfOperation',
            index: 'scopeOfOperation',
        }, {
            name: 'productSpecification',
            index: 'productSpecification',
            width: 200
        }, {
            name: 'productPreparation',
            index: 'productPreparation',
        }, {
            name: 'productProduceFactory',
            index: 'productProduceFactory',
            width: 250
        }, {
            name: 'productOriginPlace',
            index: 'productOriginPlace',
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        },{
            name: 'supplierCode', // 供应单位编码
            index: 'supplierCode',
            width: 250
        }, {
            name: 'productPackCount',
            index: 'productPackCount',
        }, {
            name: 'productContainTaxPrice',// 单价
            index: 'productContainTaxPrice'
        },
        {
            name: 'realRefundContainTax',// 金额
            index: 'realRefundContainTax'
        },{
            name: 'createTime',
            index: 'createTime',
            formatter:function(value){
                return new Date(value).Format('yyyy-MM-dd');
            }

        }/*, {
        }*/
        , {
            name: 'productApprovalNumber',
            index: 'productApprovalNumber',
            width: 200

        }, {
            name: 'productProduceDate',
            index: 'productProduceDate',

        }, {
            name: 'productExpireDate',
            index: 'productExpireDate',

        }, {
            name: 'createUserName',
            index: 'createUserName',
        }, {
            name: 'rejectTypeName',
            index: 'rejectTypeName',
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width:240
        }, {
            name: 'productPackUnitSmall',
            index: 'productPackUnitSmall',
        }, {
            name: 'marketAuthor',
            index: 'marketAuthor'
        }, {
            name: 'storageConditionsName',
            index: 'storageConditionsName'
        }
    ],allColModelC = JSON.parse(JSON.stringify(thirdcolModel));
    var fourcolModel=  [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },{
            name: 'productCode',
            index: 'productCode',
        }, {
            name: 'productName',
            index: 'productName',
            width: 200

        },{
            name: 'largeCategory',
            index: 'largeCategory',
        },{
            name: 'scopeOfOperation',
            index: 'scopeOfOperation',
        }, {
            name: 'productSpecification',
            index: 'productSpecification',
            width: 200
        }, {
            name: 'productPreparation',
            index: 'productPreparation',
        }, {
            name: 'productProduceFactory',
            index: 'productProduceFactory',
            width: 250
        }, {
            name: 'productOriginPlace',
            index: 'productOriginPlace',
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        },{
            name: 'supplierCode', // 供应单位编码
            index: 'supplierCode',
            width: 250
        }, {
            name: 'productPackCount',
            index: 'productPackCount',
        }, {
            name: 'productContainTaxPrice',// 单价
            index: 'productContainTaxPrice'
        },
        {
            name: 'realRefundContainTax',// 金额
            index: 'realRefundContainTax'
        },{
            name: 'createTime',
            index: 'createTime',
            formatter:function(value){
                return new Date(value).Format('yyyy-MM-dd');
            }

        }/*, {
        }*/
        , {
            name: 'productApprovalNumber',
            index: 'productApprovalNumber',
            width: 200

        }, {
            name: 'productProduceDate',
            index: 'productProduceDate',

        }, {
            name: 'productExpireDate',
            index: 'productExpireDate',

        }, {
            name: 'createUserName',
            index: 'createUserName',
        }, {
            name: 'rejectTypeName',
            index: 'rejectTypeName',
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width:240
        }, {
            name: 'productPackUnitSmall',
            index: 'productPackUnitSmall',
        }, {
            name: 'marketAuthor',
            index: 'marketAuthor'
        }, {
            name: 'storageConditionsName',
            index: 'storageConditionsName'
        }
    ],allColModelD = JSON.parse(JSON.stringify(fourcolModel));
    var fivecolModel=  [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },{
            name: 'productCode',
            index: 'productCode',
        }, {
            name: 'productName',
            index: 'productName',
            width: 200

        },{
            name: 'largeCategory',
            index: 'largeCategory',
        },{
            name: 'scopeOfOperation',
            index: 'scopeOfOperation',
        }, {
            name: 'productSpecification',
            index: 'productSpecification',
            width: 200
        }, {
            name: 'productPreparation',
            index: 'productPreparation',
        }, {
            name: 'productProduceFactory',
            index: 'productProduceFactory',
            width: 250
        }, {
            name: 'productOriginPlace',
            index: 'productOriginPlace',
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        },{
            name: 'supplierCode', // 供应单位编码
            index: 'supplierCode',
            width: 250
        }, {
            name: 'productPackCount',
            index: 'productPackCount',
        }, {
            name: 'productContainTaxPrice',// 单价
            index: 'productContainTaxPrice'
        },
        {
            name: 'realRefundContainTax',// 金额
            index: 'realRefundContainTax'
        },{
            name: 'createTime',
            index: 'createTime',
            formatter:function(value){
                return new Date(value).Format('yyyy-MM-dd');
            }

        }/*, {
        }*/
        , {
            name: 'productApprovalNumber',
            index: 'productApprovalNumber',
            width: 200

        }, {
            name: 'productProduceDate',
            index: 'productProduceDate',

        }, {
            name: 'productExpireDate',
            index: 'productExpireDate',

        }, {
            name: 'createUserName',
            index: 'createUserName',
        }, {
            name: 'rejectTypeName',
            index: 'rejectTypeName',
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width:240
        }, {
            name: 'productPackUnitSmall',
            index: 'productPackUnitSmall',
        }, {
            name: 'marketAuthor',
            index: 'marketAuthor'
        }, {
            name: 'storageConditionsName',
            index: 'storageConditionsName'
        }
    ],allColModelE = JSON.parse(JSON.stringify(fivecolModel));
    var sixcolModel=  [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },{
            name: 'productCode',
            index: 'productCode',
        }, {
            name: 'productName',
            index: 'productName',
            width: 200

        },{
            name: 'largeCategory',
            index: 'largeCategory',
        },{
            name: 'scopeOfOperation',
            index: 'scopeOfOperation',
        }, {
            name: 'productSpecification',
            index: 'productSpecification',
            width: 200
        }, {
            name: 'productPreparation',
            index: 'productPreparation',
        }, {
            name: 'productProduceFactory',
            index: 'productProduceFactory',
            width: 250
        }, {
            name: 'productOriginPlace',
            index: 'productOriginPlace',
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        },{
            name: 'supplierCode', // 供应单位编码
            index: 'supplierCode',
            width: 250
        }, {
            name: 'productPackCount',
            index: 'productPackCount',
        }, {
            name: 'productContainTaxPrice',// 单价
            index: 'productContainTaxPrice'
        },
        {
            name: 'realRefundContainTax',// 金额
            index: 'realRefundContainTax'
        },{
            name: 'createTime',
            index: 'createTime',
            formatter:function(value){
                return new Date(value).Format('yyyy-MM-dd');
            }

        }/*, {
        }*/
        , {
            name: 'productApprovalNumber',
            index: 'productApprovalNumber',
            width: 200

        }, {
            name: 'productProduceDate',
            index: 'productProduceDate',

        }, {
            name: 'productExpireDate',
            index: 'productExpireDate',

        }, {
            name: 'createUserName',
            index: 'createUserName',
        }, {
            name: 'rejectTypeName',
            index: 'rejectTypeName',
        }, {
            name: 'approvalNumber',
            index: 'approvalNumber',
            width:240
        }, {
            name: 'productPackUnitSmall',
            index: 'productPackUnitSmall',
        }, {
            name: 'marketAuthor',
            index: 'marketAuthor'
        }, {
            name: 'storageConditionsName',
            index: 'storageConditionsName'
        }
    ],allColModelF = JSON.parse(JSON.stringify(sixcolModel));


$(function () {
    initDate()
    $('#categoryKeyword').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/purchaseOrderProduct/queryCommonPage', //异步请求
        paramName: 'keyword',//查询参数，默认 query
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        maxWidth: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.name, data: dataItem.id};
                })
            };
        },
        onSelect: function (result) {

            $("#largeCategory").val(result.data);
            //$("#createUser").val(result.data);
            // console.log('选中回调')
        },
        onNoneSelect: function (params, suggestions) {
            $("#categoryKeyword").val("");
            $("#largeCategory").val("");
        }
    });


    $('#scopeKeyword').Autocomplete({
        serviceUrl:'/proxy-purchase/purchase/purchaseOrderProduct/queryScopePage', //异步请求
        paramName: 'keyword',//查询参数，默认 query
        params: {'type':function(){
                return medicalType;
            }},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        maxWidth: '300', //默认300高度
        showNoSuggestionNotice: true,
        noSuggestionNotice: '查询无结果',
        triggerSelectOnValidInput: false, // 必选
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.scopeName, data: dataItem.simpleCode};
                })
            };
        },
        onSelect: function (result) {

            $("#scopeOfOperation").val(result.data);
            //$("#createUser").val(result.data);
            // console.log('选中回调')
        },
        onNoneSelect: function (params, suggestions) {
            $("#scopeKeyword").val("");
            $("#scopeOfOperation").val("");
        }
    });

    // 商品搜索框点击
    $('#productCode').on({
        dblclick: function (e) {
            commodity_search_Product($("#productCode").attr('oldvalue'))
        },
        keyup: function (e) {
            if (e.keyCode === 13 && !$("#productCode").attr('oldvalue')) {
                commodity_search_Product()
            } else if (e.keyCode === 13) {
                $(this).blur();
            }
        }
    }).siblings('.glyphicon-search').on("click", function (e) {
        commodity_search_Product($("#productCode").attr('oldvalue'))
        e.stopPropagation();
    });

    // 商品查询
    function commodity_search_Product(val) {
        var input_goodName = $("#productCode").val();
        utils.dialog({
            url: '/proxy-order/order/orderReturn/orderReturnController/toCommodityList',
            title: '商品列表',
            width: $(window).width() * 0.9,
            height: $(window).height() * 0.7,
            data: input_goodName, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                    $('#productCode').val(data.productName);
                    $('#productCodeOrName').val(data.productCode);
                }
            }
        }).showModal();
    };


    //商品模糊查询
    $('#productCode').Autocomplete({
        // serviceUrl: '/proxy-purchase/purchase/supplier/loadSupplierData4PurchaseOrder',
        serviceUrl: '/proxy-purchase/purchase/supplier/findPageInfoByMnemonInfo?orgCode=' + $("#orgCode").val(),
        paramName: 'keyword',
        zIndex: 9,
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (obj) {
            $("#productCodeOrName").val(obj.data);
            $("#productCode").val(obj.value);
        },
        // dataReader: {
        //     list: 'result', //结果集，不写返回结果为数组
        //     listChild: 'list',
        //     data: 'productCode',
        //     value: 'productName',
        //     id: 'id'
        // },
        transformResult: function (response) {
            return {
                suggestions: $.map(response, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        onNoneSelect: function (params, suggestions) {
            $("#productCode").val("");
            $("#productCodeOrName").val("");
        },
        onSearchComplete: function (query, suggestions) {
            // console.log("---onSearchComplete:"+query);
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });

    // 供应商搜索框点击
    $('#find_supplierc').on({
        dblclick: function (e) {
            commodity_search_dia($("#find_supplierc").attr('oldvalue'))
        },
        keyup: function (e) {
            if (e.keyCode === 13 && !$("#find_supplierc").attr('oldvalue')) {
                commodity_search_dia()
            } else if (e.keyCode === 13) {
                $(this).blur();
            }
        }
    }).siblings('.glyphicon-search').on("click", function (e) {
        commodity_search_dia($("#find_supplierc").attr('oldvalue'))
        e.stopPropagation();
    });

    //供应商模糊查询
    $('#find_supplierc').Autocomplete({
        // serviceUrl: '/proxy-purchase/purchase/supplier/loadSupplierData4PurchaseOrder',
        serviceUrl: '/proxy-purchase/purchase/supplier/loadSupplierData',
        paramName: 'keyword',
        params: {
            pageNum: 1,
            pageSize: 5
        },
        zIndex: 9,
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (obj) {
            $("#supplierCode").val(obj.data);
            $("#find_supplierc").val(obj.value);
        },
        dataReader: {
            list: 'result', //结果集，不写返回结果为数组
            listChild: 'list',
            data: 'supplierCode',
            value: 'supplierName',
            id: 'id'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierCode").val("");
            $("#find_supplierc").val("");
        },
        onSearchComplete: function (query, suggestions) {
            // console.log("---onSearchComplete:"+query);
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });

    // 供应商
    function commodity_search_dia(val) {
        utils.dialog({
            url: '/proxy-purchase/purchase/purchaseRefundProductOrder/toSupplier',
            title: '供应商列表',
            width: $(window).width() * 0.9,
            height: '550px',
            data: val, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    debugger;
                    //  console.log(data)
                    $("#supplierCode").val(data.supplierCode);
                    $("#find_supplierc").val(data.supplierName);
                } else {
                    $("#supplierCode").val("");
                    $("#find_supplierc").val("");
                }
            }
        }).showModal();
    };

    $('#X_Table').XGrid({
        //data: grid_data,
        url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList?pageFlag=1",
        colNames: fristColNames,
        colModel: fristcolModel ,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid_page',
        ondblClickRow: function (id,dom,obj,index,event) {

        },
    });
    $('#X_Table2').XGrid({
        data:[],
        //url: "/orderOut/orderNotOut/findList",
        colNames: secondColNames,
        colModel: secondcolModel,
        name: 'id', //与反回的json数据中key值对应productCodeOrName
        altRows: true, //设置为交替行表格,默认为false
        rowNum: 19,
        rowList:[20,50,100],
        rownumbers: true,
        //selectandorder: true,//是否展示序号，多选
        pager: '#grid_pageTwo'
    });
    $('#X_Table3').XGrid({
        //data: grid_data,
        url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList?pageFlag=1",
        colNames: thirdColNames,
        colModel:thirdcolModel ,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid_pageThree',
        ondblClickRow: function (id,dom,obj,index,event) {

        },
    });
    $('#X_Table4').XGrid({
        //data: grid_data,
        url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList?pageFlag=1",
        colNames: fourColNames ,
        colModel: fourcolModel,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid_pageThree',
        ondblClickRow: function (id,dom,obj,index,event) {

        },
    });
    $('#X_Table5').XGrid({
        //data: grid_data,
        url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList?pageFlag=1",
        colNames: fiveColNames,
        colModel: fivecolModel,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid_pageThree',
        ondblClickRow: function (id,dom,obj,index,event) {

        },
    });
    $('#X_Table6').XGrid({
        //data: grid_data,
        url:"/proxy-purchase/purchase/purchaseRefundProductOrderProduc/ajaxReturnGoodRecordList?pageFlag=1",
        colNames:sixColNames,
        colModel:sixcolModel ,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid_pageThree',
        ondblClickRow: function (id,dom,obj,index,event) {

        },
    });
    btn_search();
})
// 时间格式化
Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

$('#exportRowData').on('click', function () {

    var cs = $("#seleteTips .active").attr("id");
    var X_Table = X_Table;
    var allColModel = allColModelA;
    var grid = '';
    var colname  ;
    switch (cs) {
        case "normal":
            X_Table = 'X_Table';
            allColModel = allColModelA;
            grid = '#grid_page #totalPageNum';
            colname = fristColNames;
            break;
        case "colddrug":
            X_Table = 'X_Table2';
            allColModel = allColModelB;
            grid = '#grid_pageTwo #totalPageNum';
            colname = secondColNames;
            break;
        case "sprcial":
            X_Table = 'X_Table3';
            allColModel = allColModelC;
            grid = '#grid_pageThree #totalPageNum';
            colname = thirdColNames;
            break;
        case "chinese":
            X_Table = 'X_Table4';
            allColModel = allColModelD;
            grid = '#grid_pageFour #totalPageNum';
            colname = fourColNames;
            break;
        case "medical":
            X_Table = 'X_Table5';
            allColModel = allColModelE;
            grid = '#grid_pageFive #totalPageNum';
            colname = fiveColNames;
            break;
        case "notdrug":
            X_Table = 'X_Table6';
            allColModel = allColModelF;
            grid = '#grid_pageSix #totalPageNum';
            colname = sixColNames;

            break;
            return;
    }

    utils.exportAstrictHandle(X_Table,Number($(grid).text())).then(()=>{
        return false;
    }).catch(()=> {
        // alert("左边的");
        var ck = false;
        var nameModel = "";
        addHtmlA(colname);
        dialog({
            content: $("#setColA"),
            title: '筛选列',
            width: 706,
            data: 'val值',
            cancelValue: '取消',
            cancel: true,
            okValue: '导出',
            ok: function () {
                //     nameModel += "orgName:所属分公司,";
                var newColName = [], newColModel = [];
                var colName = [];
                var colNameDesc = [];
                $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                    if ($(this).is(":checked")) {
                        // newColName.push($(this).attr('name'));
                        // newColModel.push($(this).parent().text());
                        colName.push(allColModel[index+1].name)
                        colNameDesc.push($(this).attr('name'));
                        nameModel += allColModel[index+1].name + ":"+$(this).attr('name') + ","
                    }
                });
                if(nameModel.length == 0){
                    utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }

                var orgCode = $("#orgCode").val();
                var startTime = $("#startTime").val();
                var endTime = $("#endTime").val();
                var productCodeOrName = $("#productCodeOrName").val();
                var productCode = $("#productCode").val();
                var largeCategory = $("#categoryKeyword").val();
                var scopeOfOperation = $("#scopeKeyword").val();
                var storageConditions =$('#storageConditions').val();
                var supplierId =$('#supplierId').val();
                var supplierCode =$('#supplierCode').val();
                var type = medicalType;
                var sel =  $('#'+X_Table+'').XGrid('getSeleRow');
                // console.log(sel);
                var selectId = [];
                if(sel == undefined || sel == null){

                }else if(sel.length == undefined ){
                    selectId = sel.id;
                }else{
                    $.each(sel,function(i,obj){
                        selectId.push(obj.id);
                    })
                }

                var colNames= colName.join(",")
                var colNameDescs= colNameDesc.join(",")
                var formData2 = {
                    moduleName: 'returnRecord',
                    taskCode: '1029',
                    colName: colNames,
                    colNameDesc: colNameDescs,
                    fileName: '采购退回记录',
                    exportParams: {
                        "startTime": startTime,
                        "endTime": endTime,
                        "productCodeOrName":productCodeOrName,
                        "productCode":productCode,
                        "largeCategory":largeCategory,
                        "storageConditions":storageConditions,
                        "scopeOfOperation":scopeOfOperation,
                        "supplierId":supplierId,
                        "supplierCode":supplierCode,
                        "supplierCode":supplierCode,
                        "tab":tab,
                        "type":type,
                        "orgCode":orgCode,
                        "pageFlag":"1"
                    }
                };


                if(typeof sel == "undefined" || sel == null || sel == "") {
                    utils.dialog({
                        title: '温馨提示',
                        content: '导出任务提交成功后页面将关闭，是否确认导出？',
                        okValue: '确定',
                        ok: function () {
                            $.ajax({
                                url: "/proxy-purchase/purchase/commonExport/commonCommitExportTask",
                                type: 'post',
                                dataType: 'json',
                                data: {
                                    "data":JSON.stringify(formData2)
                                },
                                success: function (res) {
                                    if (res) {
                                        if (res.code === 0) {
                                            utils.dialog({
                                                title: '温馨提示',
                                                content: '导出任务提交成功,稍后请点击导出列表进行下载...',
                                                okValue: '确定',
                                                ok: function () {

                                                }
                                            }).showModal()
                                        } else {
                                            utils.dialog({
                                                title: '温馨提示',
                                                content: res.msg,
                                                okValue: '确定',
                                                ok: function () {

                                                }
                                            }).showModal()
                                        }
                                    }
                                }
                            });
                        }
                    }).showModal()
                }
            },
            // copy button to other dialogues
            button: [
                {
                    id: 'chooseAll',
                    value: '全选',
                    callback: function () {
                        //debugger;
                        if(!ck){
                            $("#checkRow input").prop("checked",false);
                            ck = true;
                        }else if(ck){
                            $("#checkRow input").prop("checked","checked");
                            ck = false;
                        }else{
                            return false;
                        };
                        return false;
                    }
                }
            ]
            //copy ends here
        }).showModal();
    })




})

function  btn_output_list(){
    var cs = $("#seleteTips .active").attr("id");
    var taskCode = "";
    var moduleName = "";
    taskCode = "1029";
    moduleName = "returnRecord";

    utils.dialog({
        title: '导出列表',
        url: '/proxy-purchase/purchase/commonExport/toExportList?moduleName='+moduleName+'&taskCode='+taskCode,
        width: $(window).width() * 0.8,
        height: 600,
        // data: , // 给modal 要传递的 的数据
        onclose: function () {
        },
        oniframeload: function () {
            // console.log('iframe ready')
        }
    }).showModal();
    return false;
}
function addHtmlA(arry) {
    if (!$('#setColA')[0]) {
        var s = '<div id="setColA" style="display: none;">' +
            '    <div class="row" id="checkRow">';

        for (var i = 1; i < arry.length; i++) {
            s += '<div class="col-md-3">' +
                '            <div class="checkbox">' +
                '                <label>' +
                '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                '                </label>' +
                '            </div>' +
                '        </div>';

        }

        s += '</div></div>';
        $("body").append(s);
    }

}

