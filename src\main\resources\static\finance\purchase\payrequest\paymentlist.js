$(function () {
    var totalTable = z_utils.totalTable;
    var colNames = ['付款申请单号','供应商编号', '供应商名称', '款项类型','申请金额', '本次预付余额抵扣', '实付金额','本次实付金额','已付款金额','是否为补单','状态','付款次数','支付方式','开户银行','开户户名','银行账号','待付款编号',' 承兑开户行号 ','申请日期 ', '期望支付日期','申请人','发起部门','单据属性','审批完成时间','备注','付款类型','制单人','制单时间','实际付款日期','失败原因','下载凭证'];
    var colNamesb = ['付款申请单号','供应商编号', '供应商名称', '款项类型','申请金额', '本次预付余额抵扣', '实付金额','本次实付金额','已付款金额','是否为补单','状态','付款次数','支付方式','开户银行','开户户名','银行账号','待付款编号',' 承兑开户行号 ','申请日期 ', '期望支付日期','申请人','发起部门','单据属性','审批完成时间','备注','付款类型','制单人','制单时间','实际付款日期','失败原因','下载凭证'];
    var colModel = [
        {
            name: 'billNo',
            index: 'billNo',
            width: 150,//宽度

        }, {
            name: 'supplierNo',
            index: 'supplierNo',
            width: 150
        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 200
        }, {
            name: 'isPrepayStr',
            index: 'isPrepayStr',
            width: 100
        },{
            name: 'applayAmount',
            index: 'applayAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }, {
            name: 'prepaymentAmountDeduction',
            index: 'prepaymentAmountDeduction',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        }, {
            name: 'actualPaymentAmount',
            index: 'actualPaymentAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        },{
            name: 'actualPayAmount',
            index: 'actualPayAmount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        },{
            name: 'paymentAmount',
            index: 'paymentAmount',//已付款金额
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            },
            width: 150
        },{
            name: 'supplementOrderYn',//是否为补单
            index: 'supplementOrderYn',
            width: 100,
            formatter:function(val,a,b,c){
                if(val == 1){
                    return '是'
                }else if(val == 0){
                    return '否'
                }
            },
        },{
            name: 'statusStr',//状态
            index: 'statusStr',
            width: 100,
        },{
            name: 'paymentNum',
            index: 'paymentNum',
            width: 100,
            formatter:function(val,a,b,c){
                return Number(b.outerBillNo.slice(-4));
            },
        }, {
            name: 'payableTypeStr',
            index: 'payableTypeStr',
            width: 100,
        }, {
            name: 'bankName',
            index: 'bankName',
            width: 150,
        }, {
            name: 'accountName',
            index: 'accountName',
            width: 150,
        },{
            name: 'bankAccount',
            index: 'bankAccount',
            width: 200,
        },{
            name:'outerBillNo',//待付款编号
            index: 'outerBillNo',
            width: 200,
        },{
            name: 'openBankNum', //承兑开户行号 
            index: 'openBankNum',
            width: 150,
        },{
            name: 'createTime',
            index: 'createTime',
            width: 100,
            sortable: false,
            editable: true,
            formatter:function (value){
                if(value){
                    return moment(value).format('YYYY-MM-DD');
                }else{
                    return ''
                }
            }
        }, {
            name: 'expectPayTime',
            index: 'expectPayTime',
            width: 100,
            sortable: false,
            editable: true,
            formatter:function (value){
                if(value){
                    return moment(value).format('YYYY-MM-DD');
                }else{
                    return ''
                }
            }
        }, {
            name: 'createUser',
            index: 'createUser',
            width: 100,
        },  {
            name: 'sourceDeptStr',
            index: 'sourceDeptStr',
            width: 100,
        },{
            name: 'docAttrStr',
            index: 'docAttrStr',
            width: 100,
        },{
            name: 'finishTime',
            index: 'finishTime',
            width: 150,
            sortable: false,
            editable: true,
            formatter:function (value){
                if(value == null || value == "")
                    return "";
                var date = new Date(value);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? ('0' + m) : m;
                var d = date.getDate();
                d = d < 10 ? ('0' + d) : d;
                var h = date.getHours();
                h = h < 10 ? ('0' + h) : h;
                var minute = date.getMinutes();
                var second = date.getSeconds();
                minute = minute < 10 ? ('0' + minute) : minute;
                second = second < 10 ? ('0' + second) : second;
                return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
            }
        },{
            name: 'remarks',
            index: 'remarks',
            width: 200,
        },{
            name: 'paymentTypeStr',//付款类型
            index: 'paymentTypeStr',
            width: 150,
        },{
            name: 'preparedPeople',//制单人
            index: 'preparedPeople',
            width: 150,
        },{
            name: 'preparedCreateTime',//制单时间
            index: 'preparedCreateTime',
            width: 150,
            formatter:function (value){
                if(value == null || value == "")
                    return "";
                var date = new Date(value);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? ('0' + m) : m;
                var d = date.getDate();
                d = d < 10 ? ('0' + d) : d;
                var h = date.getHours();
                h = h < 10 ? ('0' + h) : h;
                var minute = date.getMinutes();
                var second = date.getSeconds();
                minute = minute < 10 ? ('0' + minute) : minute;
                second = second < 10 ? ('0' + second) : second;
                return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
            }
        },{
            name: 'realPaymentTime',//实际付款日期
            index: 'realPaymentTime',
            width: 150,
            formatter:function (value){
                if(value == null || value == "")
                    return "";
                var date = new Date(value);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? ('0' + m) : m;
                var d = date.getDate();
                d = d < 10 ? ('0' + d) : d;
                var h = date.getHours();
                h = h < 10 ? ('0' + h) : h;
                var minute = date.getMinutes();
                var second = date.getSeconds();
                minute = minute < 10 ? ('0' + minute) : minute;
                second = second < 10 ? ('0' + second) : second;
                return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
            }
        },{
            name: 'rejectReason',//失败原因
            index: 'rejectReason',
            width: 200,
        },{
            name: 'receiptFile',//下载凭证
            index: 'receiptFile',
            formatter:function(val,a,b,c){
                if(val){
                    return  `
                    <div onclick="clickReceiptFile('${val}','${b.billNo}',1)">
                        <a style="cursor: pointer;">下载凭证</a>
                    </div>`
                }
            },
            width: 150,
        },{
            name:'id',
            index:'id',
            hidden: 'true'
        }
    ];
    
    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/payrequestinfo/findPayrequestPageV2',
        // url: 'http://localhost:8080/account/find',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        attachRow:true,
        ondblClickRow: function (id, dom, obj, index, event) {
            this.returnValue = obj;
            //  window.location.href = "/proxy-finance/finance/purchase/payrequestinfo/detailPayrequestPage?billNo="+obj.billNo+"&status=1";
            showDetail(obj);
            return obj;
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            var questaId;
            var _this = $(this);
            setTimeout(function () {
                if(_this.XGrid('getRowData').length === 0){
                    utils.dialog({content:'查询无数据',quickClose:true,timeout:2000}).show();
                }
            },200);
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['applayAmount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sum_prepaymentAmountDeduction = ['prepaymentAmountDeduction'];
            sum_prepaymentAmountDeduction.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sum_actualPaymentAmount = ['actualPaymentAmount'];
            sum_actualPaymentAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sum_actualPayAmount = ['actualPayAmount'];
            sum_actualPayAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            var sum_paymentAmount = ['paymentAmount'];
            sum_paymentAmount.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
            });
            //初始化table头部hover提示
            // setTimeout(() => {
            //     initQuesta();
            // }, 100);
            $('.questa').mouseenter(function(e){
                questaId = utils.dialog({
                    content: '计提口径月至今供应商维度财务毛利率，T-1数据',
                    // quickClose: true// 点击空白处快速关闭
                });
                questaId.show($('.questa')[0]);
            })
            $('.questa').mouseleave(function () {
                questaId.close().remove();
              })
        },
        pager: '#grid-pager'
    });
     //初始化table头部hover提示
     function initQuesta(result) {
        var string = result;

        const questaOption = [{
            th: 'supplierProfitRate', //供应商财务毛利率
            title: `计提口径月至今供应商维度财务毛利率，T-1数据`,
            width: 460,
            height: 1000,
        },
        ];
        eachTipView(questaOption);
    }

    function eachTipView(arr) {
        $.each(arr, function (index, item) {
            $('.table_wrap').delegate('th[row-describedby=' + item.th + '] .questa', {
                mouseover: function (e) {
                    $('body').append(`
                        <div id='div_tooltips'>
                            <style>
                                #div_tooltips:after{
                                    content: "";
                                    width: 0;
                                    height: 0;
                                    position: absolute;
                                    left: ${item.width / 2 - 10}px;
                                    bottom: -10px;
                                    border-left: solid 10px transparent;
                                    border-top: solid 10px white;
                                    border-right: solid 10px transparent;
                                }
                            </style>
                            <div id='inner_tooltips'>${item.title}</div>
                        </div>
                    `);
                    $('#div_tooltips')
                        .css({
                            boxSizing: 'border-box',
                            width: item.width + 'px',
                            height: item.height + 'px',
                            padding: '10px',
                            zIndex: 9999,
                            backgroundColor: '#ffffff',
                            border: '1px solid #c4c4c4',
                            position: 'absolute',
                            top: $(e.target).offset().top - item.height - 10 + 'px',
                            left: $(e.target).offset().left + 5 - item.width / 2 + 'px',
                        })
                        .show('fast');
                },
                mouseout: function () {
                    $('#div_tooltips').remove();
                },
                click: function () { },
            });
        });
    }
    function showDetail(a) {
        let c = '';
        if(a.isPrepayStr == '应付款'){
            c = 'C02';
        }else{
            c = 'C01';
        }
        utils.openTabs('payrequestinfo','采购付款申请单详情','/#/finance/purchase/newrequestV3?billNo=' + a.billNo + "&type=2&isPrepay=" + c);

        /* utils.dialog({
             title: '采购付款申请单详情',
             url: '/proxy-finance/finance/purchase/payrequestinfo/detailPayrequestPage?billNo=' + billNo + '&status=' + 1,
             width: $(window).width() * 0.8,
             height: $(window).height() * 0.8,
             data: 'val值', // 给modal 要传递的 的数据
             onclose: function () {
                 if (this.returnValue) {
                     var data = this.returnValue;
                     console.log(data);
                     $('searchBtn').triangle('click');
                 }
                 $('iframe').remove();
             }
         }).showModal();*/
    }


    $("#searchBtn").on("click", function () {
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/payrequestinfo/findPayrequestPageV2',
            postData: {
                keyWord: $("#keyWord").val(),
                billNo:$("#billNo").val(),
                preparedCreateTimeSt:$("#preparedCreateTimeSt").val(),
                preparedCreateTimeEt:$("#preparedCreateTimeEt").val(),
                status:$("#status").val(),
                supplementOrderYn:$("#supplementOrderYn").val(),
                paymentType:$("#paymentType").val(),
            }
        }).trigger('reloadGrid');
        totalPayrequestSum();
    });

    function loadPaymentDetail(){
        $('#X_Table1').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/payrequestinfo/findPaymnetDetailPage',
        }).trigger('reloadGrid');
        totalSum();
    }
    // 付款
    $('#payBtn').bind('click', function () {
        var seleRow = $('#X_Table').XGrid('getSeleRow');
        console.log(seleRow,$.isArray(seleRow));
        if(seleRow.length===0){
            utils.dialog({content:'请选择一条采购付款申请单明细！',timeout:2000,quickClose:true}).show();
            return false;
        }else if( seleRow[0].statusStr != '待付款' && seleRow[0].statusStr != '部分付款'){
            utils.dialog({content:'请选择采购付款申请单明细为待付款的订单！',timeout:2000,quickClose:true}).show();
            return false;

        }
        let supplementOrderYn = seleRow[0].supplementOrderYn == "是" ? 1 :0
        utils.dialog({
            url: '/proxy-finance/finance/purchase/payrequestinfo/toPayment?billNo=' + seleRow[0].billNo + '&outerBillNo=' + seleRow[0].outerBillNo + '&supplementOrderYn=' + supplementOrderYn,
            title: '采购付款',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.75,
            cancelValue:'',
            // cancel: false,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                //启用外部滚动条
                $(document).unbind("scroll");
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                }
                $('iframe').remove();
                $("#searchBtn").trigger('click');
                loadPaymentDetail();
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();

        //弹窗时禁用滚动条
        var tops = $(document).scrollTop();
        $(document).bind("scroll",function (){$(document).scrollTop(tops); });

        return false;

    })
    // 驳回付款
    $('#rejectyBtn').bind('click', function () {
        
        var seleRow = $('#X_Table').XGrid('getSeleRow');
        console.log(seleRow,$.isArray(seleRow));
        if(seleRow.length===0){
            utils.dialog({content:'请选择一条采购付款申请单明细！',timeout:2000,quickClose:true}).show();
            return false;
        }else if( seleRow[0].statusStr != '待付款' &&  seleRow[0].statusStr != '付款失败' && seleRow.length===1){
            utils.dialog({content:'请选择采购付款申请单明细为待付款或付款失败的订单！',timeout:2000,quickClose:true}).show();
            return false;
        }
        utils.dialog({
            title: '请选择驳回原因',
            width: 700,
            height:200,
            content: $("#rejectyBox"),
            okValue: '确认',
            cancelValue: '取消',
            onshow: function () {
            },
            ok: function () {
                if ($("#auditOpinion").val()==""){
                    utils.dialog({content:'审批意见不能为空！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }else if(!document.querySelector('input[name="rejectReason"]:checked')){
                    utils.dialog({content:'请选择驳回原因！', quickClose: true, timeout: 2000}).showModal();
                    return false;
                }else{  
                    let paramstemp = {
                        outerBillNo:seleRow[0].outerBillNo,
                        billNo:seleRow[0].billNo,
                        option:document.querySelector('input[name="rejectReason"]:checked').value,
                        rejectReason:$("#auditOpinion").val(),
                    }
                    $.ajax({
                        url:'/proxy-finance/finance/purchase/payrequestinfo/paymentReject',
                        type:'post',
                        data: JSON.stringify(paramstemp),
                        contentType: "application/json", // 明确告诉服务器发送的数据是 JSON 格式
                        dataType: "json",
                        success:function (result) {
                            console.log(result)
                            if(result.code == 0){
                                utils.dialog({content:'驳回成功！',timeout:2000,quickClose:true}).show();
                                $("#searchBtn").trigger('click');
                                // 获取所有驳回原因的单选按钮
                                const rejectReasons = document.querySelectorAll('input[name="rejectReason"]');
                                // 设置第一个单选按钮为选中状态
                                if (rejectReasons.length > 0) {
                                    rejectReasons[0].checked = true;
                                }
                                $("#auditOpinion").val('');                                   
                            }else{
                                utils.dialog({content:result.msg,timeout:2000,quickClose:true}).show();
                            }
                        }
                    });
                }
            },
            cancel: function () {
                // 获取所有驳回原因的单选按钮
                const rejectReasons = document.querySelectorAll('input[name="rejectReason"]');
                // 设置第一个单选按钮为选中状态
                if (rejectReasons.length > 0) {
                    rejectReasons[0].checked = true;
                }
                $("#auditOpinion").val('');
            },
            onclose: function () {
                // 获取所有驳回原因的单选按钮
                const rejectReasons = document.querySelectorAll('input[name="rejectReason"]');
                // 设置第一个单选按钮为选中状态
                if (rejectReasons.length > 0) {
                    rejectReasons[0].checked = true;
                }
                $("#auditOpinion").val('');
            },
        }).showModal();

        //弹窗时禁用滚动条
        // var tops = $(document).scrollTop();
        // $(document).bind("scroll",function (){$(document).scrollTop(tops); });

        return false;

    })



    var detailNames = ['付款单号','付款金额', '供应商编号','供应商名称','款项类型' ,'供应商财务毛利率<i class="questb" ></i>','付款次数','支付方式','银行名称','银行帐号','制单人','制单日期','实际付款日期','付款原因','备注'];
    var detailModel = [
        {
            name: 'billNo'

        }, {
            name: 'paymentAccount',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',' ,'.');
            },
            unformat: function (val) {
                return val.replace(/,/g ,'');
            }
        }, {
            name: 'supplierNo'
        }, {
            name: 'supplierName'
        }, {
            name: 'isPrepayStr'
        },{
            name:'supplierProfitRate',
            width:200,
        }, {
            name: 'paymentNum'
        }, 
        {
            name: 'paymentTypeStr'
        },  {
            name: 'bankName'
        }, {
            name: 'bankAccount'
        }, {
            name: 'createUser'
        }, {
            name: 'createTime',
            formatter:function (value){
                if(value){
                    return moment(value).format('YYYY-MM-DD');
                }else{
                    return ''
                }
            }
        }, {
            name: 'realPaymentTime',
            formatter:function (value){
                if(value == null || value == "")
                    return "";
                var date = new Date(value);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? ('0' + m) : m;
                var d = date.getDate();
                d = d < 10 ? ('0' + d) : d;
                var h = date.getHours();
                h = h < 10 ? ('0' + h) : h;
                var minute = date.getMinutes();
                var second = date.getSeconds();
                minute = minute < 10 ? ('0' + minute) : minute;
                second = second < 10 ? ('0' + second) : second;
                return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
            }
        },{
            name: 'paymentReasonStr'
        },{
            name: 'remark'
        },{
            name:'id',
            hidden: 'true'
        }
    ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));
    // $('#X_Table1').XGrid({
    //     //data: grid_data,
    //     url: '/proxy-finance/finance/purchase/payrequestinfo/findPaymnetDetailPage',
    //     // url: 'http://localhost:8080/account/find',
    //     colNames: detailNames,
    //     colModel: detailModel,
    //     rowNum: 20,
    //     rowList: [20,50,100],//分页条数下拉选择
    //     altRows: true,//设置为交替行表格,默认为false
    //     rownumbers:true,
    //     attachRow:true,
    //     ondblClickRow: function (id, dom, obj, index, event) {
    //         console.log('双击行事件', e, c, a, b);
    //         this.returnValue = obj;
    //         // console.log(obj)
    //         //window.location.href = "/proxy-finance/finance/purchase/payrequestinfo/toDetail?payReqNO=" + obj.billNo;
    //         return obj;
    //     },
    //     onSelectRow: function (e, c, a, b) {
    //         console.log('单机行事件', e, c, a, b);
    //     },
    //     pager: '#grid-pager1',
    //     gridComplete: function () {
    //         /* 合计行 */
    //         var questbId;
    //         var data = $(this).XGrid('getRowData');
    //         var sum_models = ['paymentAccount'];
    //         var lastRowEle = $(this).find("tr[nosele=true]");
    //         lastRowEle.find("td:first-child").text('合计');
    //         sum_models.forEach(function (item,index) {
    //             lastRowEle.find("td[row-describedby="+item+"]").text(parseFloat(totalTable(data,item)).formatMoney('2', '', ',', '.'))
    //         });
    //         var _this = $(this);
    //         setTimeout(function () {
    //             if(_this.XGrid('getRowData').length === 0){
    //                 utils.dialog({content:'查询无数据',quickClose:true,timeout:2000}).show();
    //             }
    //         },200);
            
    //         $('.questb').mouseenter(function(e){
    //             questbId = utils.dialog({
    //                 content: '计提口径月至今供应商维度财务毛利率，T-1数据',
    //                 // quickClose: true// 点击空白处快速关闭
    //             });
    //             questbId.show($('.questb')[0]);
    //         })
    //         $('.questb').mouseleave(function () {
    //             questbId.close().remove();
    //           })
    //         //初始化table头部hover提示
    //         // setTimeout(() => {
    //         //     initQuestb();
    //         // }, 100)
    //     },
    // });
      //初始化table头部hover提示
      function initQuestb(result) {
        var string = result;

        const questaOption = [{
            th: 'supplierProfitRate', //供应商财务毛利率
            title: `计提口径月至今供应商维度财务毛利率，T-1数据`,
            width: 460,
            height: 800,
        },
        ];
        eachTipView1(questaOption);
    }

    function eachTipView1(arr) {
        $.each(arr, function (index, item) {
            $('.table_wrapb').delegate('th[row-describedby=' + item.th + '] .questb', {
                mouseover: function (e) {
                    $('body').append(`
                        <div id='div_tooltips'>
                            <style>
                                #div_tooltips:after{
                                    content: "";
                                    width: 0;
                                    height: 0;
                                    position: absolute;
                                    left: ${item.width / 2 - 10}px;
                                    bottom: -10px;
                                    border-left: solid 10px transparent;
                                    border-top: solid 10px white;
                                    border-right: solid 10px transparent;
                                }
                            </style>
                            <div id='inner_tooltips'>${item.title}</div>
                        </div>
                    `);
                    $('#div_tooltips')
                        .css({
                            boxSizing: 'border-box',
                            width: item.width + 'px',
                            height: item.height + 'px',
                            padding: '10px',
                            zIndex: 9999,
                            backgroundColor: '#ffffff',
                            border: '1px solid #c4c4c4',
                            position: 'absolute',
                            top: $(e.target).offset().top - item.height - 10 + 'px',
                            left: $(e.target).offset().left + 5 - item.width / 2 + 'px',
                        })
                        .show('fast');
                },
                mouseout: function () {
                    $('#div_tooltips').remove();
                },
                click: function () { },
            });
        });
    }
    //总记
    totalSum();
    totalPayrequestSum();

    //导出
    $('#exportBtn').on('click', function () {

        utils.exportAstrictHandle('X_Table1', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            addHtmlA(colNamesb);
            //colNames:colNamesb
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    var obj ={
                		 keyWord: $("#keyWord").val(),
                         billNo:$("#billNo").val(),
                         preparedCreateTimeSt:$("#preparedCreateTimeSt").val(),
                         preparedCreateTimeEt:$("#preparedCreateTimeEt").val(),
                         status:$("#status").val(),
                         supplementOrderYn:$("#supplementOrderYn").val(),
                         paymentType:$("#paymentType").val(),
                    };

                    //obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    obj["nameModel"] = nameModel;
                    httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportPayrequestPage", obj);
                },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })
    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none;     padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }

    //关键字模糊查询
    $('#keyWord').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result)
            $("#keyWord").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {

            $("#keyWord").val("");

        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });


    //放大镜查询
    $('#keyWord').on({
        dblclick: function (e) {
            supplierdDalog($("#keyWord").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#keyWord").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#keyWord").attr('oldvalue'))
    });
    //供应商双击查询
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '',  // 给modal 要传递的 的数据
            onclose: function () {
                $('#keyWord').removeAttr('save');
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#keyWord").val(data.supplierName);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }


    // 付款
    $('#refundBtn').bind('click', function () {

        utils.dialog({
            url: '/proxy-finance/finance/purchase/payrequestinfo/toRefund',
            title: '采购付款',
            width: $(window).width() * 0.8,
            height: $(window).height() * 0.75,
            cancelValue:'',
            // cancel: false,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                //启用外部滚动条
                $(document).unbind("scroll");
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data)
                }
                $('iframe').remove();
                $("#searchBtn").trigger('click');
                loadPaymentDetail();
            },
            oniframeload: function () {
                // console.log('iframe ready')
            }
        }).showModal();

        //弹窗时禁用滚动条
        var tops = $(document).scrollTop();
        $(document).bind("scroll",function (){$(document).scrollTop(tops); });

        return false;

    })

    function totalSum() {
        $.ajax({
            url:'/proxy-finance/finance/purchase/payrequestinfo/sumTotalPaymentRecords',
            type:'post',
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;
                    $("#paymentTotalCount").text(data.totalCount);
                    if (data.sumPaymentAccount) {
                        $("#paymentSumPaymentAccount").text(parseFloat(data.sumPaymentAccount).formatMoney('2', '', ',' ,'.'));
                        // $("#paymentSumPaymentAccount").text(data.sumPaymentAccount);
                        // $("#paymentSumPaymentAccount").text(data.sumPaymentAccount.toFixed(2));
                    } else {
                        $("#paymentSumPaymentAccount").text(0);
                    }

                }
            }
        });
    }

    function totalPayrequestSum() {
        $.ajax({
            url:'/proxy-finance/finance/purchase/payrequestinfo/sumTotalPaymentPayrequestInfos',
            type:'post',
            data:{
                keyWord: $("#keyWord").val(),
                billNo:$("#billNo").val(),
                preparedCreateTimeSt:$("#preparedCreateTimeSt").val(),
                preparedCreateTimeEt:$("#preparedCreateTimeEt").val(),
                status:$("#status").val(),
                supplementOrderYn:$("#supplementOrderYn").val(),
                paymentType:$("#paymentType").val(),
            },
            dataType:'json',
            success:function (result) {
                console.log(result)
                if(result.code == 0){
                    var data = result.result;
                    $("#totalCount").text(data.totalCount);
                    if (data.sumApplayAmount) {
                        $("#sumApplayAmount").text(parseFloat(data.sumApplayAmount).formatMoney('2', '', ',' ,'.'));
                        // $("#sumApplayAmount").text(data.sumApplayAmount.toFixed(2));
                    } else {
                        $("#sumApplayAmount").text(0);
                    }
                    if (data.sumPrepaymentAmountDeduction) {
                        $("#sumPrepaymentAmountDeduction").text(parseFloat(data.sumPrepaymentAmountDeduction).formatMoney('2', '', ',' ,'.'));
                    } else {
                        $("#sumPrepaymentAmountDeduction").text(0);
                    }
                    if (data.sumActualPaymentAmount) {
                        $("#sumActualPaymentAmount").text(parseFloat(data.sumActualPaymentAmount).formatMoney('2', '', ',' ,'.'));
                    } else {
                        $("#sumActualPaymentAmount").text(0);
                    }
                    if (data.sumActualPayAmount) {
                        $("#sumActualPayAmount").text(parseFloat(data.sumActualPayAmount).formatMoney('2', '', ',' ,'.'));
                    } else {
                        $("#sumActualPayAmount").text(0);
                    }
                    if (data.sumPaymentAmount) {
                        $("#sumPaymentAmount").text(parseFloat(data.sumPaymentAmount).formatMoney('2', '', ',' ,'.'));
                    } else {
                        $("#sumPaymentAmount").text(0);
                    }
                }
            }
        });
    }

    function  flushTable() {

        $("#searchBtn").trigger('click');
        loadPaymentDetail();
    }

    $("#batchImportBtn").on('click', function () {

        utils.dialog({
            title: '无申请批量导入',
            width: 700,
            height:150,
            content: $("#batchImportBox"),
            okValue: '开始导入',
            cancelValue: '关闭',
            onshow: function () {

                $("").appendTo($("#previewBox").show().find("ul"));
                $("#previewBox").find("li").remove();
                $("#btnUpload").removeAttr("disabled");
            },
            ok: function () {

                UploadFileFunction(function (list, filename) {
                    //previewUpload(list, filename);
                });
                //保存按钮回调
                //   return submitDrugTestReport();
            },
            cancel: function () {
            },
            onclose: function () {

            },
        }).showModal();

    });

    var prevUploadFileName = "";//上一次上传的文件名
    $("#btnUpload").on("change", function () {
        $('#filePath').val($(this).val())
        var fileName = this.files[0].name;
        //是否选择了文件
        if (!fileName) {
            utils.dialog({
                title: "提示",
                width: 200,
                content: "请选择文件",
                okValue: "确定",
                ok: function () {
                }
            }).showModal();
            return false;
        }
    });

    $("#previewBox").on("click", '.close-btn', function () {
        $(this).closest("li").remove();
        $("#btnUpload").removeAttr("disabled");
    })

    var imageType = ['xls','xlsx'];

    function UploadFileFunction(cb) {
        var file = $("#btnUpload")[0].files[0];
        if (!file) {
            utils.dialog({
                title: "提示",
                width: 200,
                content: "请选择文件",
                okValue: "确定",
                ok: function () {
                }
            }).showModal();
            return false;
        }

        var fileName = file.name;
        var fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLocaleLowerCase();
        if(imageType.indexOf(fileType) == -1){
            utils.dialog({content: "请上传正确格式的文件！", quickClose: false, timeout: 2000}).showModal();
            return false;
        }
        var maxsize = 10 * 1024 * 1024; //5M
        var size = file.size; //图片大小
        if (size > maxsize) {
//                alert('图片过大，单张图片大小不超过2M');
            utils.dialog({content: "文件过大，文件大小不能超过10M", quickClose: false, timeout: 2000}).showModal();

            return false;
        }

        var formData = new FormData();
        formData.append("files", file);
        formData.append("name", file.name);
        // formData.files = file
        // formData.name = file.name

        var loading = utils.dialog({content: "上传中", quickClose: false}).showModal();
        /*var loading = dialog({
            title: '上传中',
            fixed: true,
            width: 200,
            quickClose: false,
            cancel: false
        }).showModal();*/

        $.ajax({
            url: '/proxy-finance/finance/purchase/payrequestinfo/batchUploadExcel',
            type: 'POST',
            sync: true,
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function () {
                console.log("正在上传");
            },
            success: function (data) {
                loading.close();
                if (data.code == 0) {
                    //prevUploadFileName = file.name;
                    //cb(data.result[0], prevUploadFileName);
                    var resu = data.result;

                    var errorTotal = resu.errorTotal;
                    var status = resu.status;
                    console.log("1111111122222222" + resu)
                    //result.
                    //utils.dialog({content: "上传成功", quickClose: false, timeout: 2000}).showModal();
                    if (status == 0) { //成功
                        utils.dialog({
                            title: '成功导入',
                            width: 700,
                            height:150,
                            content: resu.msg,
                            okValue: '确认',
                            cancelValue: '关闭',
                            onshow: function () {
                            },
                            ok: function () {
                                flushTable();
                            },
                            cancel: function () {
                                flushTable();
                            },
                            onclose: function () {
                                flushTable();
                            },
                        }).showModal();
                    } else {
                        utils.dialog({
                            title: '是否下载导入失败数据',
                            width: 700,
                            height:150,
                            content: resu.msg,
                            okValue: '确认下载',
                            cancelValue: '关闭',
                            onshow: function () {

                            },
                            ok: function () {
                                if (errorTotal> 0) {
                                    flushTable();
                                    downloadErrorExcel(resu.path,"无申请导入错误详情.xlsx");
                                    // location.href = '/proxy-finance/finance/purchase/payrequestinfo/downErrorData?filePath=' + resu.path;
                                } else {
                                    utils.dialog({content: "没有失败的", quickClose: true, timeout: 2000}).showModal();
                                }

                            },
                            cancel: function () {
                                flushTable();
                            },
                            onclose: function () {
                                flushTable();
                            },
                        }).showModal();
                    }


                } else if (data.code == 1) {
                    var res = data.result;
                    utils.dialog({content: "发生异常", quickClose: true, timeout: 2000}).showModal();
                } else {
                    utils.dialog({content: "上传失败", quickClose: true, timeout: 2000}).showModal();
                }
                // 展示附件
                /*let fileList = data.result;
                if (fileList && fileList.length != 0){
                    let str = '';
                    $(fileList).each( (index,item) => {
                        str += `<div data-url="`+item.enclosureUrl+`" style="display: inline-block; padding: 10px 20px 10px 10px; position: relative;">
                                        <span style=" position: absolute;width: 15px;height: 15px;top: 0;right: 0;z-index: 1;cursor: pointer;" onclick="btn_delFile(this)"><img src="/proxy-sysmanage/static/images/close.png"></span>
                                        <a href='javascript:;' onclick='uploadFile(this)' data-url="`+item.enclosureUrl+`" data-name="`+item.enclosureName+`">`+item.enclosureName+`</a></div>`
                        // <a href='`+item.enclosureUrl+`' target="_blank" download="`+item.enclosureName+`">`+item.enclosureName+`</a></div>`
                    });
                    $('.uploadFiles_div').append(str);
                    $('.uploadFiles_div').css('display','block')
                }*/
            }, error: function () {
                loading.close();
                utils.dialog({content: "上传失败", quickClose: true, timeout: 2000}).showModal();
            }, complete: function () {
                $("#btnUpload").val("");
                $('#filePath').val("");
            }
        });
    }
    function downloadErrorExcel(src,name){
        console.log("进入新下载方法:"+src+name);
        var x=new XMLHttpRequest();
        //禁止浏览器缓存；否则会报跨域的错误
        x.open("GET", src, true);
        x.responseType = 'blob';
        x.onload=function(e){
            var url = window.URL.createObjectURL(x.response)
            var a = document.createElement('a');
            a.href = url;
            a.download = name;
            a.click();
            $.ajax({ 
                url:'/proxy-finance/finance/sale/financeSaleController/deleteFailExcelOnFast',
                method: "POST",
                datatype:'json',
                data:{"filePath":src},
                success:function(result){
    
                },
                error:function(error){
                }
            });
        }
        x.send();
    }

})
/**下载凭证 */
function clickReceiptFile(val,billNo,type){
    downloadImg(val,billNo + '.pdf')
}
function downloadImg(src,name){
    let param = [];
    param.push({
        url:src,
        fileName:name
    })
    $.ajax({
        method: "POST",
        async:true,
        contentType: "application/json", // 明确告诉服务器发送的数据是 JSON 格式
        url: "/proxy-finance/finance/purchase/payrequestinfo/downloadZip",
        data: JSON.stringify(param),
        xhrFields: {
            responseType: 'blob' // 设置响应类型为 blob，用于处理二进制数据
        },
        // dataType: 'json',
        cache: false,
    }).done(function (blob) {
        let aaa = new Blob([blob])
        var url = window.URL.createObjectURL(aaa)
        var a = document.createElement('a');
        a.href = url;
         a.download = name;
        a.click()
        window.URL.revokeObjectURL(url);
    });
}