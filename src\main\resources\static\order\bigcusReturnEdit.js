var selEl = null;
$(function () {
    /* 参数,页面传递的数据 */
    var url = location.search;
    var param = z_utils.parseParams(url);
    console.log(url, param);

    /* 合计计算 */
    var totalTable = z_utils.totalTable;

    /* table_ a */
    //data
    var colName = ['id','退货原因', '销售退回单号','EC退回单号','商品编码' ,'原商品编码','商品名称', '商品规格', '生产厂家', '产地', '单位', '库房名称','batchBaseVoList', '批号', '生产日期', '有效期至', '本次退回数量',
        '实际退货数量', '含税价', '实付价', '价税合计', '实付金额', '活动明细优惠金额', '余额抵扣明细优惠金额','退回返利金额', '税率', '税额','原活动优惠金额','原余额优惠金额','原返利优惠金额',
    ];
    var colModel = [{ name: 'id',    index: 'id', hidden:true,hidegrid:true   },
        { name: 'returnReason',    index: 'returnReason',    rowtype: '#sort_e',
            formatter:function (e) {

                if(e==1){
                    return '整单拒收';
                }else if(e==2){
                    return '药品漏发';
                }else if(e==3){
                    return '药品错发';
                }else if(e==4){
                    return '药品破损';
                }else if(e==5){
                    return '效期不好';
                }else if(e==6){
                    return '批号不符';
                } else if(e==7){
                    return '税票有误';
                }else if(e==8){
                    return '无检验报告单';
                }else if(e==9){
                    return '采购价偏高';
                } else if(e==10){
                    return '实物与图片不符';
                }else if(e==11){
                    return '采购单重复';
                }else if(e==12){
                    return '商品其他质量问题';
                }else if(e==13){
                    return '拦截';
                }else if(e==14){
                    return '缺货未发';
                }else if(e==15){
                    return '召回';
                }else if(e==16){
                    return '水剂不发货';
                }else {
                    return e;
                }
            },
            rowEvent:function(row){
                var $input = $(row.e.target);
                var rowData = row.rowData;
                if ((rowData.returnReason != null || rowData.returnReason != "" )&&rowData.returnReason.length>=20){
                    utils.dialog({
                        title: '提示',
                        content: '退货原因不能超过20个字符',
                        width: '300',
                        cancel: false,
                        okValue: '确认',
                        ok: function () {

                        }
                    }).showModal();
                    $('#table_a').XGrid('setRowData',rowData.id,{ returnReason: rowData.returnReason.substring(0,20)});
                    return
                }
            }
        },
        {    name: 'salesReturnCode',    index: 'salesReturnCode', hidden:true ,hidegrid:true  },
        {    name: 'refundApplyCode',    index: 'refundApplyCode', hidden:true ,hidegrid:true  },
        {    name: 'productCode',    index: 'productCode'  },
        {    name: 'oldProductCode',    index: 'oldProductCode'  },
        {    name: 'productName',    index: 'productName'  },
        {    name: 'specifications',    index: 'specifications'  },
        {    name: 'manufacturer',    index: 'manufacturer'  },
        {    name: 'productOrigin',    index: 'productOrigin'  },
        {    name: 'productUnit',    index: 'productUnit'  },
        {    name: 'warehouseName',    index: 'warehouseName',formatter:function (e) {
            if(e==1){
                return '合格库';
            }else if(e==2){
                return '不合格库';
            }else if(e==3){
                return '暂存库';
            }else if(e==10){
                return '临时库';
            }else{
                return e;
            }
        },unformat:function (e) {
            if(e=='合格库'){
                return 1;
            }else if(e=='不合格库'){
                return 2;
            }else if(e=='暂存库'){
                return 3;
            }else if(e=='临时库'){
                return 10;
            }else{
                return '';
            }
        } },
        {    name: 'batchBaseVoList',    index: 'batchBaseVoList',hidden:true,hidegrid:true ,  formatter:function (result) {
            if(typeof result == 'string'){
                return result;
            }else {
                return JSON.stringify(result);
            }

        } },
        {    name: 'batchCode',    index: 'batchCode',    rowtype: '#text8_e',
            formatter:function (result,id,rowData) {
                var batchBaseVoList = rowData.batchBaseVoList;
                setTimeout(function () {
                    console.log(typeof batchBaseVoList);
                    var ary = [];
                    if($(id+'_'+rowData.id+' select>option').length>1)return;
                    if(typeof batchBaseVoList == 'string'){
                        ary = JSON.parse(batchBaseVoList);
                    }else {
                        ary = batchBaseVoList;
                    }
                    ary.forEach(function (item,index) {
                        $(id+'_'+rowData.id+' select').append(`<option value="${item.batchCode}">${item.batchCode}</option>`);
                    });
                },200)

                return result
            },
            rowEvent:function (event) {
                var rowData = event.rowData;
                var batchCode = rowData.batchCode;
                var batchBaseVoList = JSON.parse(rowData.batchBaseVoList);
                batchBaseVoList.forEach(function (item,index) {
                    if(batchCode==item.batchCode){
                        $('#table_a').XGrid('setRowData',rowData.id,{ productionTime: item.productionTime, periodValidity: item.periodValidity,oldactivityDiscountAmount:item.activityDiscountAmount,oldbalanceDiscountAmount:item.balanceDiscountAmount,oldamounOfRebate:item.amounOfRebate,actualReturnNumber:item.outStoreNumber});
                        console.log(item)
                    }
                });

            }},
        {    name: 'productionTime',    index: 'productionTime'  ,formatter:dateFormatter},
        {    name: 'periodValidity',    index: 'periodValidity' ,formatter:dateFormatter },
        {    name: 'returnsNumber',    index: 'returnsNumber'  },
        {    name: 'actualReturnNumber',
            index: 'actualReturnNumber',
            rowtype: '#text12_e',
            rowEvent:function(row){
                var input_id = $(row.e.target).parent().attr('id');
                var $input = $(row.e.target);
                var rowData = row.rowData;
                if (rowData.batchCode == null || rowData.batchCode == "" ){
                    utils.dialog({
                        title: '提示',
                        content: '请选择批号',
                        width: '300',
                        cancel: false,
                        okValue: '确认',
                        ok: function () {

                        }
                    }).showModal();
                    $input.val(0);
                    return
                }

                if(rowData.batchCode != null || rowData.batchCode != ""){
                    var actualReturnNumber = rowData.actualReturnNumber;
                    var returnNumber = rowData.returnsNumber;
                    var taxPrice = rowData.taxPrice;
                    var activityDiscountAmount = rowData.oldactivityDiscountAmount;
                    var balanceDiscountAmount = rowData.oldbalanceDiscountAmount;
                    var amounOfRebate = rowData.oldamounOfRebate;
                    var payPrice  = rowData.paymentPrice;
                    if ( actualReturnNumber == '' ||  actualReturnNumber  == null){
                        actualReturnNumber = 0;
                    }
                    if ( activityDiscountAmount == '' ||  activityDiscountAmount  == null){
                        activityDiscountAmount = 0;
                    }
                    if ( balanceDiscountAmount == '' ||  balanceDiscountAmount  == null){
                        balanceDiscountAmount = 0;
                    }
                    if ( amounOfRebate == '' ||  amounOfRebate  == null){
                        amounOfRebate = 0;
                    }
                    rowData.taxAmount =  (actualReturnNumber * taxPrice).toFixed(2);
                    rowData.paymentAmount = (actualReturnNumber * payPrice).toFixed(2);
                    rowData.activityDiscountAmount = (activityDiscountAmount*(actualReturnNumber/returnNumber)).toFixed(2);
                    rowData.balanceDiscountAmount = (balanceDiscountAmount*(actualReturnNumber/returnNumber)).toFixed(2);
                    rowData.amounOfRebate = (amounOfRebate*(actualReturnNumber/returnNumber)).toFixed(2);
                    $input.val(actualReturnNumber);
                    var rate = rowData.rate;
                    var tax = 0;
                    if (rate != '' && rate != null && rate != undefined){
                        rate = rate.replace("%" , "")
                        var notaxPrice = (taxPrice/(1+ rate/100)).toFixed(2)
                        tax = (rowData.taxAmount - notaxPrice* actualReturnNumber).toFixed(2);
                    }
                    $('#table_a').XGrid('setRowData',rowData.id,{taxAmount:rowData.taxAmount,paymentAmount:rowData.paymentAmount, activityDiscountAmount:rowData.activityDiscountAmount,balanceDiscountAmount:rowData.balanceDiscountAmount,amounOfRebate:rowData.amounOfRebate,rate:rate,tax:tax,ecOrderDetailId:0});

                    var data = $('#table_a').XGrid('getRowData');
                    var sum_ele = $('#table_a_sum .sum');
                    //console.log(sum_ele);
                    $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                    $(sum_ele[1]).text(totalTable(data, 'tax'));
                    $(sum_ele[2]).text(totalTable(data, 'paymentAmount'));

                }

            }

            },
        {    name: 'taxPrice',    index: 'taxPrice'  , formatter: priceFormatter},
        {    name: 'paymentPrice',    index: 'paymentPrice'  ,formatter: priceFormatter},
        {    name: 'taxAmount',    index: 'taxAmount' ,formatter: priceFormatter},
        {    name: 'paymentAmount',    index: 'paymentAmount' , formatter: priceFormatter },
        {    name: 'activityDiscountAmount',    index: 'activityDiscountAmount',formatter: priceFormatter},
        {    name: 'balanceDiscountAmount',    index: 'balanceDiscountAmount' ,formatter: priceFormatter},
        {    name: 'amounOfRebate',      index: 'amounOfRebate', formatter: priceFormatter},
        {    name: 'rate',    index: 'rate',  formatter: function (e) {
                if ( e != undefined && e != null ) {
                    return e+ "%"
                } else {
                    return ''
                }
            }   },
        {    name: 'tax',    index: 'tax'  },

        {    name: 'oldactivityDiscountAmount',    index: 'oldactivityDiscountAmount'},
        {    name: 'oldbalanceDiscountAmount',    index: 'oldbalanceDiscountAmount' },
        {    name: 'oldamounOfRebate',      index: 'oldamounOfRebate'},
        {    name: 'ecOrderDetailId',      index: 'ecOrderDetailId' ,hidden:true,hidegrid:true}
        ];

    function dateFormatter(inputTime) {
        if(inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }


    function priceFormatter(e){
        if (e != null && e != '') {
            var array = e.toString().split(".");
            if (array.length == 1){
                return e.toString() + ".00"
            }else if (array.length ==2){
                var elngt2 = array[1].length;
                if (elngt2 == 1){
                    return e.toString()+"0"
                }else {
                    return e;
                }
            }else {
                return e;
            }
        }else {
            return e;
        }


    }




    $('#table_a').XGrid({
        url:"/proxy-order/order/orderReturn/orderReturnController/getProductList?salesReturnCode=" + $("#salesReturnCode").val()+"&orderType=2",
        colNames: colName,
        colModel: colModel,
        selectandorder: false,
        key: 'id',
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {

        },
        gridComplete: function () {
            setTimeout(function (param) {
                /* 合计写入 */
                var data = $('#table_a').XGrid('getRowData');
                var sum_ele = $('#table_a_sum .sum');
                //console.log(sum_ele);
                $(sum_ele[0]).text(totalTable(data, 'taxAmount'));
                $(sum_ele[1]).text(totalTable(data, 'tax'));
                $(sum_ele[2]).text(totalTable(data, 'paymentAmount'));
            }, 200)
        },
        onSelectRow: function (id, dom, obj, index, event) {

        }
    });

    //
    $(selEl).on('change',function () {
        console.log('1111111111')
    })

    /* 提交审核 */
    $('#sub_check').on('click', function () {
        var form_data = $('#form_a').serializeToJSON();
        var table_data = $('#table_a').XGrid('getRowData');
        var formData = JSON.stringify(form_data);
        table_data = table_data.map(function (item,index) {
            delete item.batchBaseVoList;
            return item
        });
        var tableData = JSON.stringify(table_data);
        var  checkDate = true;
        var sum_data = [];
        for(var i=0;i<table_data.length;i++){
            var flag = false;
            sum_data = sum_data.map(function (item,index) {
                if(item.productCode == table_data[i].productCode){
                    item.actualReturnNumber = Number(item.actualReturnNumber)+Number(table_data[i].actualReturnNumber);
                    flag = true;
                }
                return item
            });
            if(!flag){
                sum_data.push({productCode:table_data[i].productCode,actualReturnNumber:table_data[i].actualReturnNumber,returnsNumber:table_data[i].returnsNumber});
            }
            if(table_data[i].batchCode==''){
                utils.dialog({
                    title: '温馨提示',
                    content: '存在未选择的批号',
                    okValue: '确定',
                    ok: function () {},
                    cancelValue: '取消',
                    cancel: function () {}
                }).showModal()
                checkDate = false;
                return;
            }



        }
        sum_data.forEach(function (item,index) {
            if (!item.actualReturnNumber) {
                utils.dialog({
                    title: '温馨提示',
                    content: '请输入商品编号（'+item.productCode+'）的实际退货数量',
                    okValue: '确定',
                    ok: function () {},
                    cancelValue: '取消',
                    cancel: function () {}
                }).showModal()
                checkDate = false;
                return;
            }

        });
        if(checkDate){
            console.log(form_data);
            console.log(table_data);
            for(var i=0;i<table_data.length;i++){
                var rate = table_data[i].rate
                if (rate){
                    table_data[i].rate =  rate.replace("%","");
                }else {
                    table_data[i].rate = 0;
                }
            }

            var detailArray = JSON.stringify(table_data);
            parent.showLoading();
            $.ajax({
                url: "/proxy-order/order/orderBigCusReturn/bigCusOrderReturnController/updateBigCusReturn",
                type:'post',
                dataType:'json',
                data: {tableData:detailArray, formData:formData},
                beforeSend: function(){
                    parent.showLoading({hideTime: 60000})
                },
                success: function(data){
                    parent.hideLoading();
                    if(data.code == 0){
                        utils.dialog({
                            title: '温馨提示',
                            content: '  保存成功   ',
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }

                        }).showModal()
                    }else{
                        utils.dialog({
                            title: '温馨提示',
                            content: data.msg,
                            okValue: '确定',
                            ok: function () {
                                return;
                            }

                        }).showModal()
                    }
                },
                complete: function () {
                    parent.hideLoading()
                }
            });
        }
    });

    /* 返回 */
    $('#goback').on('click', function () {
        utils.dialog({
            title: '温馨提示',
            content: '返回后当前页面数据将丢失，是否继续',
            okValue: '确定',
            ok: function () {
                utils.closeTab();
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal()
    });

    /* 退货原因下拉框同步 */
    $('#returnReason').on('change',function () {
        var val = $(this).val();
        var data = $('#table_a').XGrid('getRowData');
        console.log(data);
        data = data.forEach(function (item,index) {
            $('#table_a').XGrid('setRowData', item.id,{returnReason:val});
        });
        console.log(val,data);
    });

    /* 复制行 */
    $('#cloneRoW').on('click', function () {
        var id = 1;
        var data = $('#table_a').XGrid('getSeleRow');
        if (!data) {
            utils.dialog({
                title: "提示",
                content: "请先选中一行数据"
            }).showModal();
            return
        }
        if (data.length&&data.length>1) {
            utils.dialog({
                title: "提示",
                content: "只能单行复制"
            }).showModal();
            return

        } else {
            var all_data = $('#table_a').XGrid('getRowData');
            var detail = data[0];
            var batchBaseVoList = JSON.parse(detail.batchBaseVoList);
            var this_data = [];
            all_data.forEach(function (item,index) {
                if(item.productCode==detail.productCode){
                    this_data.push(item);
                }
            });
            //判断同一商品行数是否小于该商品批号数量
            if(this_data.length<batchBaseVoList.length){
                detail.id = Number(all_data[all_data.length-1].id)+1;
                var rate = detail.rate;
                if (rate != undefined && rate != null && rate != ''){
                    detail.rate = rate.replace("%","");
                }

                $('#table_a').XGrid('addRowData', detail, "last");
            }else {
                utils.dialog({
                    title: "提示",
                    content: "该商品已经达到批号数量限制"
                }).showModal();
            }
        }
    });

    /* 删除行 */
    $('#removeRoW').on('click', function () {
        var data = $('#table_a').XGrid('getSeleRow');
        if (!data) {
            utils.dialog({
                title: "提示",
                content: "请先选中一行数据"
            }).showModal();
            return
        }
        //删除二次确认
        utils.dialog({
            title: "提示",
            content: "确认删除当前选中行？",
            okValue: '确定',
            ok: function () {
                if (data.length) {
                    $.each(data, function (index, item) {
                        $('#table_a').XGrid('delRowData', item.id);
                    })
                } else {
                    $('#table_a').XGrid('delRowData', data.id);
                }
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    });




})
