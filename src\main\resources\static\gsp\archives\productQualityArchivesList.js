var req_url_pre = "/proxy-gsp/gsp/product/quality/archives";
let getRowData = [];
$(function() {
	productQualityArchivesList();
	//标签切换
	$('#toggle_wrap>li').on('click', function () {
		var $this = $(this), $nav_content = $this.parent().next();
		if($this.index() == 1){
			if(getRowData.length == 0){
                num = 0;
				utils.dialog({
					title: '提示',
					content: '请先选择一条需要查看的商品数据，或直接双击某行商品数据',
					okValue: '确定',
					ok: function () {}
				}).showModal();
				return false;
			}else{
				productPurchaseInStorageDetailList(getRowData[0].orgCode, getRowData[0].productCode);
                getRowData = $('#product_quality_archives_list_table').XGrid('getSeleRow');
                num = 0;
            }
		} else {
			//getRowData = [];
		}

		$this.addClass('active').siblings().removeClass('active');
		$nav_content.children('div').eq($this.index()).css('display', 'flex').siblings().hide();
		$nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass('active');

	});
});

//打印事件
$("#print_btn").click(function() {
	var rData = $('#product_quality_archives_list_table').XGrid('getSeleRow');
	if (rData.length == 0) {
		utils.dialog({
			content: "请选择一条商品质量档案",
			quickClose: true,
			timeout: 2000
		}).showModal();
		return;
	}
	productQualityArchivesPrint("1", rData[0].productCode);
});

//打印预览事件
$("#print_preview_btn").click(function() {
	var rData = $('#product_quality_archives_list_table').XGrid('getSeleRow');
	if (rData.length == 0) {
		utils.dialog({
			content: "请选择一条商品质量档案",
			quickClose: true,
			timeout: 2000
		}).showModal();
		return;
	}
	productQualityArchivesPrint("0", rData[0].productCode);
});

//打印获取数据
function productQualityArchivesPrint(type, productCode) {
	$.ajax({
		url: req_url_pre + "/productQualityArchivesPrint",
		data: {
			type: type,
			productCode:productCode
		},
		type: "POST",
		success: function(result) {
			if (result.code == "0") {
                $("#print_box")[0].contentWindow.getData(type,result.result);
			} else {
				utils.dialog({
					content: result.msg,
					quickClose: true,
					timeout: 2000
				}).showModal();
			}
		}
	});
}

//查询按钮事件
$("#query_btn").click(function() {
	$('#product_quality_archives_list_table').XGrid('setGridParam', {
		url: req_url_pre + "/productQualityArchivesListPage",
		mtype: "POST",
		postData: {
			archiveTime: $("#product_archives_time").val(),
			largeCategory: $("#product_category").val(),
			productCondition: $("#product_condition").val()
		}
	}).trigger('reloadGrid');
	$('#toggle_wrap>li').eq(0).click();
});

// 商品质量档案列表展示
let num= 0;
function productQualityArchivesList() {
	$('#product_quality_archives_list_table').XGrid({
		url: req_url_pre + "/productQualityArchivesListPage",
		mtype: "POST",
		postData: {
			archiveTime: $("#product_archives_time").val(),
			largeCategory: $("#product_category").val(),
			productCondition: $("#product_condition").val()
		},
		colNames: ['建档日期', '首批进货日期', '商品编号', '商品名称', '通用名', '规格', '生产厂家', '产地', '剂型', '有效期', '质量标准', '批准文号', '存储条件'],
        colModel: [
			{
				name: 'auditTime',
				formatter:dateFormatter
			}, {
				name: 'firstPurchaseInStorageTime',
				formatter: function (e, d, data) {
					if (!data.firstPurchaseInStorageTime) {
						return "无进货";
					} else {
						return data.firstPurchaseInStorageTime;
					}
				}
			}, {
				name: 'productCode'
			}, {
				name: 'productName'
			}, {
				name: 'commonName'
			}, {
				name: 'specifications'
			}, {
				name: 'manufacturerName'
			}, {
				name: 'producingArea'
			}, {
				name: 'dosageFormName'
			}, {
				name: 'indateTime',
				formatter: function(e, d, data) {
					return data.indate + data.indateType;
				}
			}, {
				name: 'qualityStandard'
			}, {
				name: 'approvalNumber'
			}, {
				name: 'storageConditionsName'
			}, {
				name: 'orgCode',
				hidden:true
			}
		],
		rowNum: 20,
		rowList: [20, 50, 100],
		altRows: true,
		rownumbers: true,
		pager: '#product_quality_archives_list_table_page',
		ondblClickRow: function(id, dom, obj, index, event) {
			productPurchaseInStorageDetailList(obj.orgCode, obj.productCode);
            $('#toggle_wrap>li').eq(1).click();
		},
		onSelectRow: function(id, dom, obj, index, event) {
            num++;
            if(getRowData.length != 0){
            	if(getRowData[0].id != id){
					getRowData = $('#product_quality_archives_list_table').XGrid('getSeleRow');
				}else{
					getRowData = getRowData
				}
			}else{
				getRowData = $('#product_quality_archives_list_table').XGrid('getSeleRow');
			}
            //console.log(getRowData)
			//getRowData = (getRowData.length != 0) ?getRowData : $('#product_quality_archives_list_table').XGrid('getSeleRow');
		}
	});
};

// 商品采购入库明细列表展示
function productPurchaseInStorageDetailList(orgCode, productCode) {
	$('#product_purchase_in_storage_detail_list_table').XGrid({
		url: req_url_pre + "/queryOrgProductPurchaseInStorageListPage",
		mtype: "POST",
		postData: {
			orgCode:orgCode,
			productCode:productCode
		},
		colNames: ['单据编号', '单据日期', '供应商名称', '商品名称', '通用名', '规格', '生产厂家', '产地', '数量', '灭菌批号', '批号', '生产日期', '有效期至', '质量状况'],
		colModel: [{
			name: 'stockOrderNo'
		}, {
			name: 'createTime',
            formatter:dateFormatter
		}, {
			name: 'supplierName'
		}, {
			name: 'productName'
		}, {
			name: 'productCommonName'
		}, {
			name: 'productSpecification'
		}, {
			name: 'productProduceFactory'
		}, {
			name: 'productOriginPlace'
		}, {
			name: 'productPackInStoreCount'
		}, {
			name: 'sterilizationBatchNum'
		}, {
			name: 'productBatchNo'
		}, {
			name: 'productProduceDate'
		}, {
			name: 'productExpireDate'
		}, {
			name: 'productQualityStatus',
            formatter: function(e, d, data) {
            	if (1 == data.productQualityStatus) {
					return "合格";
				} else {
					return "";
				}
            }
		}],
		rowNum: 20,
		rowList: [20, 50, 100],
		altRows: true,
		rownumbers: true,
		pager: '#product_purchase_in_storage_detail_list_table_pager',
		ondblClickRow: function(id, dom, obj, index, event) {
		},
		onSelectRow: function(id, dom, obj, index, event) {
		}
	});
};

//时间格式化
function dateFormatter(val) {
	if (val != null && val != "") {
		return ToolUtil.dateFormat(val, 'yyyy-MM-dd');
	} else {
		return "";
	}
};
