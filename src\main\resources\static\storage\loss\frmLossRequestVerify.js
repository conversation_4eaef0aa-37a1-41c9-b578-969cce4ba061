/**
 * Created by 沙漠里的红裤头 on 2019/6/28
 */
/* 审批流 */
/**
 * 流程图显示
 */
    //根据流程实例ID加载流程图
var processInstaId=$("#processId").val();
initApprovalFlowChart(processInstaId);
function  initApprovalFlowChart(processInstaId) {
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: "/proxy-storage/storage/loss/getProcessListView?processInstanceId="+processInstaId,
        async: false,
        success: function (data) {
            console.log(data);
            if (data.code==0&&data.result!=null){
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {}
    });
}

$(function () {
    var colNames = ['商品编码','原商品编码','商品大类', '商品名称', '商品规格','产地', '生产厂家',
            '单位', '库房名称', '业务类型','批号', '生产日期', '有效期至','报损数量', '不含税成本单价','不含税成本金额','移库原因'],
        colModel =   [
            {name: 'productCode',index: 'productCode'},
            {name: 'oldProductCode', index: 'oldProductCode'},
            {name: 'drugClass',index: 'drugClass'},
            {name: 'productName',index: 'productName'},
            {name: 'specifications',index: 'specifications'},
            {name:'producingArea', index:'producingArea' },
            {name: 'manufacturerName',index: 'manufacturerName'},
            {name: 'unit', index: 'unit'},
            {name: 'storeHouseName',index: 'storeHouseName'
                ,formatter:function () {
                    return "不合格库";
                }},
            { name: 'channelId',index: 'channelId'},
            { name: 'batchNum',index: 'batchNum'},
            {name: 'productDate',index: 'productDate'},
            {name: 'validateDate',index: 'validateDate'},
            {name: 'lossNum',index: 'lossNum'},
            {name: 'noTaxPrice', index: 'noTaxPrice'},
            {name: 'noTaxCostNum',index: 'noTaxCostNum' },
            {name: 'wmsTransferReason',index: 'wmsTransferReason'}
        ];

    $('#X_Table').XGrid({
        url:'/proxy-storage/storage/loss/getLossRequestOrderDetailData?lossOrderNo='+$("#lossOrderNo").val(),
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        gridComplete: function () {
            /* 合计写入 */
            var data = $(this).XGrid('getRowData');
            data.forEach(function (item, index) {
                if (item.abnormalCause) {
                    $('#X_Table #' + item['id']).addClass('warnning')
                }
            });
        },
        pager: '#grid-pager',
    });

    //编辑
    $("#edit").on('click',function () {
        // 获取时间+单号
        // window.location.href="/proxy-storage/storage/loss/toEditLossOrder?currentDate="+$("#requestDate").val()+"&lossRequestNo="+$("#lossOrderNo").val()+"&taskId="+$("#taskId").val()+"&processId="+$("#processId").val()
        utils.openTabs('toEditLossOrder','编辑不合格品报损申请单', "/proxy-storage/storage/loss/toEditLossOrder?requestDate="+$("#requestDate").val()+"&businessId="+$("#lossOrderNo").val()+"&taskId="+$("#taskId").val()+"&processId="+$("#processId").val())
    });

})

// 审核通过
function btn_pass() {
    utils.dialog({
        title: '审核通过',
        content: $('#check_pass_msg'),
        okValue: '确定',
        ok: function () {
            if($("#remark").val() != null && $("#remark").val() != ''){
                $.ajax({
                    type: "POST",
                    url: "/proxy-storage/storage/loss/requestLossVerify?&taskId="+$("#taskId").val()+"&comment="+$("#remark").val()+"&lossOrderNo="+$("#lossOrderNo").val(),
                    async: false,
                    success: function (data) {
                        if(data.code == 0){
                            utils.dialog({
                                title: '温馨提示',
                                content: '审核成功',
                                okValue: '确定',
                                ok: function () {
                                    utils.closeTab();
                                },
                                cancelValue: '取消',
                                cancel: function () {}
                            }).show()
                        }else{
                            utils.dialog({
                                title: '温馨提示',
                                content: data.msg,
                                okValue: '确定',
                                ok: function () {
                                    return;
                                }

                            }).show()
                        }
                    },
                    error: function () {}
                });
            }else {
                utils.dialog({content: '请填写通过原因', quickClose: true, timeout: 2000}).show();
                return false;
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal()
}

/* 审核驳回 */
function btn_unpass(){
    utils.dialog({
        title: '审核驳回',
        content: $('#check_nopass_msg'),
        okValue: '确定',
        ok: function () {
            if($("#remarks").val() != null && $("#remarks").val() != ''){
                $.ajax({
                    type: "POST",
                    url: "/proxy-storage/storage/loss/requestLossUnVerify?taskId="+$("#taskId").val()+"&comment="+$("#remarks").val()+"&lossOrderNo="+$("#lossOrderNo").val(),
                    async: false,
                    success: function (data) {
                        if(data=='false'){
                            /*utils.dialog({
                                title: '温馨提示',
                                content: '驳回失败',
                                okValue: '确定',
                                ok: function () {
                                    return ;
                                },
                                cancelValue: '取消',
                                cancel: function () {}
                            }).show()*/
                            utils.dialog({content: '驳回失败', quickClose: true, timeout: 2000}).show();
                        }else{
                            /*utils.dialog({
                                title: '温馨提示',
                                content: '驳回成功',
                                okValue: '确定',
                                ok: function () {
                                    // window.location="/proxy-storage/storage/loss/toOrderRequestList";
                                },
                                cancelValue: '取消',
                                cancel: function () {}
                            }).show();*/
                            utils.dialog({content: '驳回成功', quickClose: true, timeout: 2000}).show();
                            setTimeout(function () {
                                utils.closeTab()
                            },2000);
                        }
                    },
                    error: function () {}
                });
            }else{
                utils.dialog({content: '请填写驳回原因', quickClose: true, timeout: 2000}).show();
                return false;
            }
        },
        cancelValue: '取消',
        cancel: function () {}
    }).showModal();






}
