$(function () {
    /* 日期初始化 */
    z_utils.initDateNoZero('beginTime', 'endTime');
    // z_utils.initDate('beginTime', 'endTime');

    /* 合计计算  */
    var totalTable = z_utils.totalTable;

    /*查询页面总计*/
    getTotalNum();


    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result);
            $("#supplierNo").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue', result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierName").val("");
            $("#supplierNo").val("");

        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if (suggestions && suggestions.length === 0) {
                $ele.attr('oldvalue', '');
            } else {
                $ele.attr('oldvalue', $ele.val())
            }
            if (!$ele.is(":focus")) {
                $ele.Autocomplete('hide');
            }
        }
    });
    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if (e.keyCode === 13 && !$("#supplierName").attr('oldvalue')) {
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });


    //供应商双击查询
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    var data = this.returnValue;
                    console.log(data);
                    $("#supplierName").val(data.supplierName);
                    $("#supplierNo").val(data.supplierCode);
                } else {
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();

    }

    //var colNames = ['付款单号','付款申请单号', '付款金额', '供应商编号', '供应商名称', '款项类型', '支付方式', '银行名称', '银行账号','制单人','制单日期','实际付款日期','备注','申请单支付方式','申请单承兑返点','申请单银行转账返点','付款原因','关联采购订单号'],
    var colNames = ['付款单号', '付款申请单号', '唯一值','付款金额', '供应商编号', '供应商名称', '款项类型','付款次数', '支付方式', '银行名称', '银行账号', '制单人', '制单日期', '实际付款日期', '过账日期', '备注', '申请单支付方式', '申请单承兑返点', '申请单银行转账返点', '付款原因', '关联采购订单号', '承兑类型', '承兑编号', '承兑名称','银行回执单'],
        colModel = [
            {
                name: 'billNo',
                index: 'billNo',
                width: 150
            }, {
                name: 'paymentApplayNo',
                index: 'paymentApplayNo',
                width: 150
            },  {
                name: 'outerBillNo',//唯一值
                index: 'outerBillNo',
                width: 180
            },{
                name: 'paymentAccount',
                index: 'paymentAccount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                },
                width: 150
            },
            {
                name: 'supplierNo',
                index: 'supplierNo',
                width: 200,//宽度
            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 300
            }, {
                name: 'isPrepayStr',
                index: 'isPrepayStr',
                width: 100,
                sortable: false,
                editable: true
            },{
                name: 'paymentNum',
                index: 'paymentNum',
                width: 100
            }, {
                name: 'paymentTypeStr',
                index: 'paymentTypeStr',
                width: 100,
                sortable: false,
                editable: true,
            }, {
                name: 'bankName',
                index: 'bankName',
                width: 300
            }, {
                name: 'bankAccount',
                index: 'bankAccount',
                width: 200
            }, {
                name: 'createUser',
                index: 'createUser',
                width: 100
            }, {
                name: 'createTime',
                index: 'createTime',
                width: 100,
                sortable: false,
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            }, {
                name: 'realPaymentTime',
                index: 'realPaymentTime',
                width: 150,
                sortable: false,
                editable: true,
                formatter: function (value) {
                    if (value == null || value == "")
                        return "";
                    var date = new Date(value);
                    var y = date.getFullYear();
                    var m = date.getMonth() + 1;
                    m = m < 10 ? ('0' + m) : m;
                    var d = date.getDate();
                    d = d < 10 ? ('0' + d) : d;
                    var h = date.getHours();
                    h = h < 10 ? ('0' + h) : h;
                    var minute = date.getMinutes();
                    var second = date.getSeconds();
                    minute = minute < 10 ? ('0' + minute) : minute;
                    second = second < 10 ? ('0' + second) : second;
                    return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second;
                },
            },
            {
                name: 'accountTime',
                index: 'accountTime',
                width: 100,
                sortable: false,
                editable: true,
                formatter: function (value) {
                    if (value) {
                        return moment(value).format('YYYY-MM-DD');
                    } else {
                        return ''
                    }
                },
            },
            {
                name: 'remark',
                index: 'remark',
                width: 150,

            },
            {
                name: 'requestPayTypeStr',
                index: 'requestPayTypeStr',
                width: 150,

            },
            {
                name: 'acceptRebate',
                index: 'acceptRebate',
                width: 150,
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },

            },
            {
                name: 'transferRebate',
                index: 'transferRebate',
                width: 150,
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },

            }, {
                name: 'paymentReasonStr',
                index: 'paymentReasonStr',
                width: 150,

            },
            {
                name: 'orderNo',
                index: 'orderNo',
                width: 150,

            },
            {
                name: 'acceptTypeName',
                index: 'acceptTypeName',
                width: 150,

            },
            {
                name: 'acceptCode',
                index: 'acceptCode',
                width: 150,

            },
            {
                name: 'acceptName',
                index: 'acceptName',
                width: 150,

            },{
                name: 'receiptFile',//银行回执单
                index: 'receiptFile',
                formatter:function(val,a,b,c){
                    if(val){
                        return  `
                        <div onclick="clickReceiptFile('${val}','${b.billNo}',1)">
                            <a style="cursor: pointer;">下载凭证</a>
                        </div>`
                    }
                },
                width: 150,
            },

        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    //设置table高度
    //utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        url: '/proxy-finance/finance/purchase/payrequestinfo/findPaymnetDetailPage',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true,
        attachRow: true,
        postData: {
            supplierNo: $("#supplierNo").val(),
            supplierName: $("#supplierName").val(),
            billNo: $("#billNo").val(),
            paymentApplayNo: $("#payRequestNo").val(),
            startDate: $("#beginTime").val(),
            endDate: $("#endTime").val(),
            realPayStartDate: $("#realPayStarttime").val(),
            realPayEndDate: $("#realPayEndtime").val(),
            paymentReason: $("#paymentReason").val(),
            hasPaymentNo: $("#hasPaymentNo").val(),
            accountStartDate: $("#accountStartDate").val(),
            accountEndDate: $("#accountEndDate").val(),
            outerBillNo: $("#outerBillNo").val().trim(),

        },
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件！', id);
            var billNo = obj.paymentApplayNo;
            if (billNo != undefined && billNo != '') {
                showDetail(billNo);
            }
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['paymentAccount'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item, index) {
                // lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
                lastRowEle.find("td[row-describedby=" + item + "]").text(parseFloat(totalTable(data, item)).formatMoney('2', '', ',', '.'))
            });
            $('#searchBtn').removeAttr('disabled');
            var _this = $(this);
            if (!_this.XGrid('getRowData').length) {
                utils.dialog({content: '查询无数据', quickClose: true, timeout: 2000}).show();
                $("#paymentAccountSum").text("0.00");
            }

        }
    });


    $("#searchBtn").on("click", function () {
        $(this).attr('disabled', '');
        $('#X_Table').XGrid('clearGridData');
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/payrequestinfo/findPaymnetDetailPage',
            postData: {
                page: 1,
                supplierNo: $("#supplierNo").val(),
                supplierName: $("#supplierName").val(),
                billNo: $("#billNo").val(),
                paymentApplayNo: $("#payRequestNo").val(),
                startDate: $("#beginTime").val(),
                endDate: $("#endTime").val(),
                realPayStartDate: $("#realPayStarttime").val(),
                realPayEndDate: $("#realPayEndtime").val(),
                paymentReason: $("#paymentReason").val(),
                hasPaymentNo: $("#hasPaymentNo").val(),
                accountStartDate: $("#accountStartDate").val(),
                accountEndDate: $("#accountEndDate").val(),
                outerBillNo: $("#outerBillNo").val().trim(),
            }
        }).trigger('reloadGrid');

        getTotalNum();
    });

    //设置显示列
    $("#setBtn").on("click", function () {
        $('#X_Table').XGrid('filterTableHead');
    });


    //导出
    $('#exportBtn').on('click', function () {

        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()), 1).then(() => {
            return false;
        }).catch(() => {
            //原始处理逻辑代码
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            addHtmlA(colNames);
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                        }
                    });
                    if (nameModel.length == 0) {
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    // var obj = $("#searchForm").serializeToJSON();
                    var obj = {
                        supplierNo: $("#supplierNo").val(),
                        supplierName: $("#supplierName").val(),
                        billNo: $("#billNo").val(),
                        paymentApplayNo: $("#payRequestNo").val(),
                        startDate: $("#beginTime").val(),
                        endDate: $("#endTime").val(),
                        realPayStartDate: $("#realPayStarttime").val(),
                        realPayEndDate: $("#realPayEndtime").val(),
                        paymentReason: $("#paymentReason").val()
                    }
                    // obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    obj["nameModel"] = nameModel;
                    httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportExcelPaymentList", obj);
                },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if (!ck) {
                                $("#checkRow input").prop("checked", false);
                                ck = true;
                            } else if (ck) {
                                $("#checkRow input").prop("checked", "checked");
                                ck = false;
                            } else {
                                return false;
                            }
                            ;
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })
    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none; padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    function showDetail(billNo) {
        utils.openTabs('payrequestinfo','采购付款申请单详情','/#/finance/purchase/newrequestV3?billNo=' + billNo + "&type=2");
        // utils.openTabs('payrequestinfo', '采购付款申请单详情', '/proxy-finance/finance/purchase/payrequestinfo/detailPayrequestPage?billNo=' + billNo + '&status=1');
        return false;
    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
})
function clickReceiptFile(data,lable,type){
    let param = [];
    let urlList = data.split(',')
    let name = ''
    if(urlList.length  > 1){
        name = lable + '.zip'
        urlList.forEach((item,index)=>{
            let it = item.slice(item.lastIndexOf('.'))
            param.push({
                url:item,
                fileName:lable + '_' + (index +1) + it
            })
        })
    }else if (urlList.length  ==  1){
        name = lable + urlList[0].slice(urlList[0].lastIndexOf('.'))
        param.push({
            url:urlList[0],
            fileName:lable + urlList[0].slice(urlList[0].lastIndexOf('.'))
        })
    }
    $.ajax({
        method: "POST",
        async:true,
        contentType: "application/json", // 明确告诉服务器发送的数据是 JSON 格式
        url: "/proxy-finance/finance/purchase/payrequestinfo/downloadZip",
        data: JSON.stringify(param),
        xhrFields: {
            responseType: 'blob' // 设置响应类型为 blob，用于处理二进制数据
        },
        // dataType: 'json',
        cache: false,
    }).done(function (responseType) {
        let aaa = new Blob([responseType])
        var url = window.URL.createObjectURL(aaa)
        var a = document.createElement('a');
        a.href = url;
        a.download = name
        a.click()
        window.URL.revokeObjectURL(url);
    });
}
//查询总合计数目
function getTotalNum() {
    var formData = $("#searchForm").serializeToJSON();
    var exceptionTypeArray = formData.exceptionType;
    var exceptionTypeStr = "";
    if (exceptionTypeArray != undefined && exceptionTypeArray != '') {
        for (var i = 0; i < exceptionTypeArray.length; i++) {
            exceptionTypeStr = exceptionTypeStr + exceptionTypeArray[i] + ",";
        }
    }

    formData.exceptionType = exceptionTypeStr;
    if (!formData.supplierName) {
        formData.supplierNo = ''
    }
    formData.realPayStartDate = $("#realPayStarttime").val();
    formData.realPayEndDate = $("#realPayEndtime").val();
    formData.paymentReason = $("#paymentReason").val();
    //加载总数量
    $.ajax({
        url: '/proxy-finance/finance/purchase/payrequestinfo/selectPaymentedInfoStatisticsTotal',
        dataType: 'json',
        timeout: 8000, //6000
        data: formData,
        success: function (data) {
            // alert(data.code);
            if (data.code == 0) {
                var static = data.result;
                if (static) {
                    if (static.paymentAccountSum) {
                        // $("#paymentAccountSum").text(Number(static.paymentAccountSum).toFixed(2));
                        $("#paymentAccountSum").text(parseFloat(static.paymentAccountSum).formatMoney('2', '', ',', '.'));
                    }
                } else {
                    $("#paymentAccountSum").text("0.00");
                }


            }
        },
        error: function () {
            $("#paymentAccountSum").text("0.00");
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}