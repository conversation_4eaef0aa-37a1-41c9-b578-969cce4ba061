/**
 * Created by 沙漠里的红裤头 on 2019/6/28
 */
/* 审批流 */
/**
 * 流程图显示
 */
    //根据流程实例ID加载流程图

var path = '/proxy-storage/storage/unqualifiedDestructionApply/';
var dialog = parent.dialog.get(window);


$('#X_Table').XGrid({
    url: path + 'findLossRequestList',
    postData: {
        "lossOrderId": $("#lossOrderId").val()
    },
    colNames: ['ID','机构名称', '报损执行单号','报损申请单号', '部门名称', '申请人', '执行日期', '不含税成本金'],
    colModel: [
        {
            name: 'id',
            index: 'id',
            hidden: true
        }, {
            name: 'orgCode',
            index: 'orgCode',//索引。其和后台交互的参数为sidx
        }, {
            name: 'lossOrderExec',
            index: 'lossOrderExec',
        }, {
            name: 'lossOrder',
            index: 'lossOrder',
        }, {
            name: 'departmentName',
            index: 'departmentName',
        }, {
            name: 'applicant',
            index: 'applicant'
        },{
            name: 'createDate',
            index: 'createDate',
            hidden: true
        },{
            name: 'noTaxPriceSum',
            index: 'noTaxPriceSum',
            formatter: function (e) {
            	console.log("e : ", e );
                return Number(e).toFixed(2);
            }
        }
    ],
    key: 'lossOrderExec',
    rowNum: 20,
    altRows: true,//设置为交替行表格,默认为false
    //rownumbers: true,
    rowList: [20, 50,100], //分页条数下拉选择
    //multiselect: true,
    selectandorder:true,
    /*disableRow:resultArr.map(function (item) {
        return item.id
    }),*/
    onSelectRow: function (id, dom, obj, index, event) {
     /*   //添加已选商品
        var $tr = $("#X_Table tr#"+id);
        var check=$tr.find('[row-describedby="ck"] input').prop("checked");
        setSelectData(check,obj);*/
    },
    pager: '#grid-pager',
    gridComplete: function () {
        /*setTimeout(function () {
            $("#X_Table th:first input").hide();
            if(resultArr.length>0)
            {
                for(var i=0;i<resultArr.length;i++)
                {
                    var lossOrderExec = resultArr[i];
                    $("#X_Table tr").not('first').each(function(){
                        var ID=$(this).attr('id');
                        if(ID == lossOrderExec)
                        {
                            $(this).find('td[row-describedby="ck"] input').prop('checked',true).trigger('input');
                        }
                    });
                }
            }
        }, 1)*/
    }
});
/*//添加已选商品
$('#X_Table').on("change","td[row-describedby='ck'] input",function(ev){
    var check=this.checked;
    var $tr = $(this).parents('tr');
    var id=$tr.attr('id');
    var data=$('#X_Table').XGrid('getRowData', id);
    setSelectData(check,data);
    ev.stopPropagation();
})*/
//查询数据，重置data
$('#search_list').on('click', function () {
    $('#X_Table').setGridParam({
    	url: path + 'findLossRequestList',
        postData: {
            "lossOrderId": $("#lossOrderId").val()
        }
    }).trigger('reloadGrid');
})

$("#selectOne").on("click", function () {
    var seleData = $("#X_Table").XGrid('getSeleRow');
    var lossOrderArrray = new Array();
    for(var i= seleData.length-1 ;i >= 0 ; i--){
        lossOrderArrray.push(seleData[i].lossOrderExec)
    }
    dialog.close(lossOrderArrray)
});
/*//设置选中、删除数据
function setSelectData(check,data){
    //console.log(data)
    if(check)
    {
        resultArr.push(data.lossOrderExec);
    }else{
        var lossOrderExec = data.lossOrderExec;
        for(var i= resultArr.length -1 ;i >= 0 ; i--){
            if(resultArr[i] == lossOrderExec)
            {
                resultArr.splice(i,1);
                break;
            }
        }
    }
}*/
