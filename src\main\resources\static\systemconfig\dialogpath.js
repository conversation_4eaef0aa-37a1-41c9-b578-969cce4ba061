var dialog = parent.dialog.get(window);
var param=dialog.data;
var resultArr=[];
if(param.arr && param.arr.length>0)
{
    resultArr=param.arr;
}
console.log(param);

$('#Attribute_Dialog').XGrid({
    url:'/proxy-sysmanage/sysmanage/dict/queryexplainattribute?isStop=0',
    colNames: ['说明书属性id', '说明书属性名称','助记码', '是否必填','控件类型', '是否停用',  '创建人', '创建日期'],
    colModel: [
        {
            name: 'attributeId',
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            editable: true,
            edittype: "input",
            editoptions: {
                size: "20",
                maxlength: "30"
            }
        }, {
            name: 'attributeName',
            width: 200,//宽度
            editable: true,//是否可编辑
            edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

        }, {
            name: 'attributeNumber',
            width: 200,//宽度
            editable: true,//是否可编辑
            edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

        }, {
            name: 'attributeIsrequired',
            formatter:isRequired,
            unformat:function(val){
                if(val!=null ){
                    if(val=="否") {
                        return 0;
                    }else{
                        return 1;
                    }
                }
            },
            width: 60,
            sorttype: "double",
            editable: true
        }, {
            name: 'attributeType',
            formatter:attribute,
            unformat:function(val){
                if(val!=null){
                    if(val!="复选框"){
                        return  0;
                    }else{
                        return  1;
                    }
                }
            },
            width: 60,
            editable: true
        },
        {
            name: 'attributeIsstop',
            width: 150,
            editable: true,
            formatter:isShop,
            unformat:function(val){
                if(val=="否"){
                    return 0;
                }else{
                    return  1;
                }
            },
            editoptions: {
                size: "20",
                maxlength: "30"
            }
        }, {
            name: 'createUser',
            width: 250,
            sortable: false,
            editable: true,
            edittype: "textarea",
            editoptions: {
                rows: "2",
                cols: "10"
            }
        }, {
            name: 'createTime',
            width: 250,
            sortable: false,
            editable: true,
            formatter: datetimeFormatter,
            edittype: "textarea",
            editoptions: {
                rows: "2",
                cols: "10"
            }
        }
    ],
    rowNum: 20,
    altRows: true,//设置为交替行表格,默认为false
    pager: '#grid-pager',
    multiselect: true,
    key:'attributeId',
    ondblClickRow: function (id, dom, obj, index, event) {
    },
    onSelectRow: function (id, dom, obj, index, event) {
        //添加已选商品
        var $tr = $("#Attribute_Dialog tr#"+id);
        var check=$tr.find('[row-describedby="ck"] input').prop("checked");
        getCheckData(check,id,obj);
    },
    gridComplete: function () {
        $("#Attribute_Dialog th:first input").hide();
        if(param.attr && param.attr != '')
        {
            var arr=param.attr.split(',');
            for(var i=0;i<arr.length;i++)
            {
                $('#Attribute_Dialog tr:not("first")').each(function () {
                    var id=$(this).attr("id");
                    if(id == arr[i])
                    {
                        $(this).find("td[row-describedby='ck'] input").prop("checked",true).trigger('input');
                    }
                })
            }
        }
//        翻页回选
        var seleData = $('#selected_Tableb').XGrid('getRowData');
        $.each(seleData, function(){
            var _id  = this.id;
            $('#Attribute_Dialog tr:not("first")').each(function () {
                    var id=$(this).attr("id");
                    if(id == _id)
                    {
                        $(this).find("td[row-describedby='ck'] input").prop("checked",true).trigger('input');
                    }
             })
            
        })
    }
});
var xGridParam={};
if(!param.value || param.value == ''){
    xGridParam.data=[];
}else{
    xGridParam.url='/proxy-sysmanage/sysmanage/dict/querytemplatebyid?templateId='+param.value;
}
var options={
    colNames: ['说明书属性id', '说明书属性名称','助记码', '是否必填','控件类型', '是否停用',  '创建人', '创建日期','操作'],
    colModel: [
        {
            name: 'attributeId',
            index: 'attributeId',//索引。其和后台交互的参数为sidx
        }, {
            name: 'attributeName',
            index: 'attributeName',
        }, {
            name: 'attributeNumber',
            index: 'attributeNumber'
        }, {
            name: 'attributeIsrequired',
            index: 'attributeIsrequired',
            formatter:isRequired
        }, {
            name: 'attributeType',
            index: 'attributeType',
            formatter:attribute
        },
        {
            name: 'attributeIsstop',
            index: 'attributeIsstop',
            width: 150,
            editable: true,
            formatter:isShop
        }, {
            name: 'createUser',
            index: 'createUser'
        }, {
            name: 'createTime',
            index: 'createTime',
            formatter: datetimeFormatter,
        },{
            name:'Operation',
            rowtype: '#Operation'
        }
    ],
    rowNum: 20,
    altRows: true,//设置为交替行表格,默认为false
    key:'attributeId',
    gridComplete: function () {

        setTimeout(function () {
            if(param.arr && param.arr.length>0)
            {
                var arr=param.arr;
                for(var i=0;i<arr.length;i++)
                {
                    var d=$('#selected_Tableb').getRowData();
                    if(!fidInArr(d,arr[i].id))
                    {
                        var json=arr[i];
                        delete json.ck;
                        $('#selected_Tableb').addRowData(json);
                    }
                }
            }
            if(param.attr && param.attr != '')
            {
                var data=$('#selected_Tableb').getRowData();
                var attrIdArr=param.attr.split(',');
                for(var i=0;i<data.length;i++)
                {
                    var id=data[i].id;
                    if(!fidInArr(attrIdArr,id))
                    {
                        $('#selected_Tableb').XGrid('delRowData',id);
                    }
                }
            }
        },1)
    }
};
//数组中查找id
function fidInArr(arr,id){
    for(var i=0;i<arr.length;i++)
    {
        if(typeof arr[i] == 'object')
        {
            if(arr[i].id == id)
            {
                return true;
            }
        }else{
            if(arr[i] == id)
            {
                return true;
            }
        }
    }
    return false;
}
options=Object.assign(xGridParam,options);
$('#selected_Tableb').XGrid(options);

//修改属性初始化完成后判断是否选中
function initChecked(){
    var arr=$('#selected_Tableb').getRowData();
    if(arr.length>0)
    {
        for(var i=0;i<arr.length;i++)
        {
            $('#Attribute_Dialog tr:not("first")').each(function () {
                var id=$(this).attr("id");
                if(id == arr[i].attributeId)
                {
                    $(this).find("td[row-describedby='ck'] input").prop("checked",true);
                }
            })
        }
    }
}

$("#dialogBut").click(function () {

    $('#Attribute_Dialog').setGridParam({
        url:"queryexplainattribute",
        postData:{
            "attributeName":$('#attributeName').val().replace(/\s+/g,""),
            "isStop":0
        }
    }).trigger('reloadGrid');
    $('#selected_Tableb').setGridParam({
        url:'/proxy-sysmanage/sysmanage/dict/querytemplatebyid',
        postData:{
            "templateId":param.value
        }
    }).trigger('reloadGrid');

});

//添加已选商品
$('#Attribute_Dialog').on("change","td[row-describedby='ck'] input",function(ev){
    var check=this.checked;
    var $tr = $(this).parents('tr');
    var id=$tr.attr('id');
    var data=$('#Attribute_Dialog').XGrid('getRowData', id);
    getCheckData(check,id,data);
    ev.stopPropagation();
})
function getCheckData(check,id,data){
    if(check)
    {
        $('#selected_Tableb').XGrid('addRowData', data);
        resultArr.push(data);
    }else{
        $('#selected_Tableb').XGrid('delRowData', id);
        initData(id);
    }
}
function initData(id){
    for(var i=0;i<resultArr.length;i++)
    {
        if(resultArr[i].id == id)
        {
            resultArr.splice(i,1);
        }
    }
    console.log(resultArr)
}
//删除按钮事件
$('#selected_Tableb').on('click', '.btnDelete', function (e) {
    var $tr = $(this).parents('tr');
    if ($tr.length && $tr.attr('id')) {
        $('#Attribute_Dialog #'+$tr.attr('id')).find("td[row-describedby='ck'] input").prop('checked',false).trigger('input');
        $('#selected_Tableb').XGrid('delRowData', $tr.attr('id'));
        initData($tr.attr('id'));
    }
    e.stopPropagation();
})
$("#checkData").on("click",function () {
    var arr=$('#selected_Tableb').getRowData();
    if(arr.length < 1)
    {
        alert('请选择说明书属性');
        return false;
    }
    var idArr=[];
    for(var i=0;i<arr.length;i++){
        idArr.push(arr[i].attributeId);
    }
    console.log(resultArr)
    var str=idArr.join(',');
    dialog.close({
        str:str,
        arr:resultArr.length>0?resultArr:[]
    });
})
function isShop(val){
    if(val==0){
        return "否"
    }else{
        return  "是"
    }
}


function datetimeFormatter(val) {
    if (val != null && val !="") {
        if(val.toString().indexOf('-') == -1)
        {
            return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
        }else{
            return val;
        }
    } else {
        return "";
    }
};

function isRequired(val){
    if(val!=null ){
        if(val==0) {
            return "否"
        }else{
            return "是"
        }
    }
}

function attribute(val){
    if(val!=null){
        if(val!=0){
            return  "复选框"
        }else{
            return  "文本框"
        }
    }
}

function  checked(){

    return  "<input type='checkbox'>"
}




