$('div[fold=head]').fold({
    sub: 'sub'
});

$(function (){
    //显示流程图
    var processInstaId = $("#processInstanceId").val();
    initApprovalFlowChart(processInstaId);
})


// var textobj=document.getElementById('auditOpinion1');
// textobj.innerHTML=$("#audit").val();


var id = Number($("#appId").val());

$('#table1').XGrid({
    url: "/proxy-customer/customer/disuse/toDetailed?id=" + id,
    colNames: ['', '客户编码', '客户名称', '客户类别', '目前状态', '业务类型', '<i style="color:red;margin-right:4px;">*</i>申请原因'],
    colModel: [{
        name: 'id', //与反回的json数据中key值对应
        key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
        hidden: true
    }, {
        name: 'customerCode',
        editable: ''
    }, {
        name: 'customerName',
    }, {
        name: 'customerType',
    },  {
        name: 'serviceType',
        formatter: function (e) {
            if (e == '0') {
                return '启用状态';
            } else if (e == '1') {
                return '停用状态';
            } else {
                return "";
            }
        },unformat: function (e) {
            if (e == '启用状态') {
                return '0';
            } else if (e == '停用状态') {
                return '1';
            } else {
                return "";
            }
        }
    }, {
        name: 'serviceType',
        formatter: function (e) {
            if (e == '0') {
                return '停用';
            } else if (e == '1') {
                return '解除停用';
            } else {
                return "";
            }
        },unformat: function (e) {
            if (e == '停用') {
                return '0';
            } else if (e == '解除停用') {
                return '1';
            } else {
                return "";
            }
        }
    }, {
        name: 'pursueReason',
    }

        , {
            name: 'customerType',
            hidden: true
        }, {
            name: 'orgCode',
            index: 'orgCode',
            hidden: true
        }, {
            name: 'customerOrganId',
            hidden: true
        }, {
            name: 'keyId',
            hidden: true
        }, {
            name: 'baseId',
            hidden: true
        }],
    key: 'baseId',
    rowNum: 999,
    altRows: true, //设置为交替行表格,默认为false
    rownumbers: true,//是否展示序号
    ondblClickRow: function (id, dom, obj, index, event) {
        //console.log('双击行事件', id,dom,obj,index,event);
    },
    onSelectRow: function (id, dom, obj, index, event, obj) {
        ///console.log('单机行事件', id,dom,obj,index,event,obj);
    },

});






/**
 * 流程图显示
 */
function initApprovalFlowChart(processInstaId) {
    var key = $("#key").val();
    const url = "/proxy-customer/customer/disuse/queryTotle?processInstaId=" + processInstaId + "&key=" +key;
    //获取审核流程数据
    $.ajax({
        type: "POST",
        url: url,
        async: false,
        success: function (data) {
            if (data.code == 0 && data.result != null) {
                console.log(data.result);
                $('.flow').html("")
                $('.flow').process(data.result);
            }
        },
        error: function () {
        }
    });
}

