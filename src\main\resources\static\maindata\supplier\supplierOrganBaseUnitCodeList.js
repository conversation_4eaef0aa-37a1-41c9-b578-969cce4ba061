    var urlObjectList = [];

    //创建列表
    $('#baseTable').XGrid({
        url:"/proxy-supplier/supplier/supplierOrganBaseUnitCode/supplierOrganBase/ajaxSupplierOrganBaseUnitCodeList",
        // colNames: ['', '机构','供应商编码', '供应商名称', '供应商类别', '业务员', '业务员电话', '营业执照号','付款方式','结算方式','到货周期','注册地址','是否停用'/*,'剩余效期'*/],
        colNames: ['', '供应商编码', '供应商类别', '企业主键码', '供应商名称', '营业执照号', '药品经营许可证号','经营范围','注册地址','药品经营许可证有效期'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                width: 100,
                hidden:true
            },  {
                name: 'supplierCode',
                index: 'supplierCode',
                width: 200

            }, {
                name: 'supplierTypeName',
                index: 'supplierTypeName',
                width: 100
            }, {
                name: 'unitCode', //与反回的json数据中key值对应
                index: 'unitCode',
                rowtype: '#unitCode_div'
            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 250
            }, {
                name: 'supplierBusinessNum',
                index: 'supplierBusinessNum',
                width: 200
            }, {
                name: 'certificateNum',
                index: 'certificateNum',
                width: 200
            },{
                name: 'scopeOfBusiness',
                index: 'scopeOfBusiness',
                width: 200
            },{
                name: 'registerAddress',
                index: 'registerAddress',
                width: 200
            },{
                name: 'validityDate',
                index: 'validityDate',
                width: 200
            }

        ],
        rowNum: 20,
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        rowList: [20,50,100],//分页条数下拉选择
        multiselect: true,//是否多选
        ondblClickRow: function (id, dom, obj, index, event) {
            //console.log('双击行事件', id, dom, obj, index, event);
            //utils.openTabs("customerBaseDetail", "机构客户详情", "/customerBaseAppl/detail?id="+id);
            // location.href='/customerBaseAppl/detail?id='+id
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //console.log(id, dom, obj, index, event)
            setUrlObjectList(dom,id,obj);
        },
        pager: '#grid-pager'
    });
    $("#searchBtn").on("click", function () {
        urlObjectList=[];
        $('#baseTable').XGrid('setGridParam', {
            postData: {
                "queryFields":$("#queryFields").val(),
                "supplierTypeId":$("#supplierTypeId").val(),
                "unitCode":$("#unitCode").val(),
                "unitStatus":$("#unitStatus").val()
            },page:1
        }).trigger('reloadGrid');
    });
    $("#exportBtn").on("click", function () {
        utils.exportAstrictHandle('baseTable',
            Number($('#totalPageNum').text())).then(()=>{
            return false;
        }).catch(()=>{
            utils.dialog({
                title: '提示',
                content:"数据量大的时候耗时较长，请耐心等待。",
                okValue: '确定',
                ok: function () {
                    parent.showLoading({hideTime: 90000})
                    let selRowIdArr = [],
                        selRowData = $('#baseTable').XGrid('getSeleRow');
                    selRowIdArr = selRowData.map((item,index,)=>{
                        return item.id
                    })

                    if(selRowIdArr.length>0){
                        console.log(selRowIdArr)
                        let data ={
                            supplierOrgIds:selRowIdArr
                        }
                        httpPost("/proxy-supplier/supplier/supplierOrganBaseUnitCode/exportSupplierOrgBaseUnitCode",data);

                    }else{
                        var body = document.body;
                        var form = $(body).find('form#searchForm');
                        $(form).attr("action","/proxy-supplier/supplier/supplierOrganBaseUnitCode/exportSupplierOrgBaseUnitCode");
                        $(form).submit();
                        setTimeout(function () {
                            parent.hideLoading()
                        },2000)
                    }

                },
                cancelValue: '取消',
                cancel: function () { },
            }).showModal();
        })
    });



    $.ajax({
        url:'/proxy-sysmanage/sysmanage/dict/querycommonnotpage?type=8',
        type:"post",
        async:false,
        dataType:'json',
        success:function(data){
            console.log("供应商类别："+data.result);
            var html = '';
            $(data.result).each(function(index,item){
                html +=  '<option value="'+item.id+'">&nbsp;'+item.name+'</option>'
            });
            $(".supplierTypeClass").append(html);

        },
        error:function(){
            utils.dialog({content: '请求失败', quickClose: true, timeout: 2000}).showModal();
        }
    });

    $(document).on('change','#grid_checked input',function () {
        var $tr=$(this).parents('tr');
        var rowData=$("#baseTable").getRowData($tr.attr('id'));
        var id=$tr.attr('id');
        setUrlObjectList($tr,id,rowData);
        //var checked=this.checked;
        // urlObjectList=[];
        // if(checked){
        //     var selRow=$('#baseTable').XGrid('getSeleRow');
        //     if(selRow && selRow.length > 0){
        //
        //         for(var i=0;i<selRow.length;i++){
        //             if(selRow[i].snapshootUrl != ''){
        //                 var fileParam={};
        //                 fileParam.id = selRow[i].id;
        //                 fileParam.name = selRow[i].customerCode;
        //                 fileParam.url = selRow[i].snapshootUrl;
        //                 urlObjectList.push(fileParam);
        //             }
        //         }
        //     }
        // }else{
        //     urlObjectList=[];
        // }
    })
    $(document).on("change",".exportItem dt input",function () {
        var checked=this.checked;
        if(checked)
        {
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',false);
        }
    });
    $(document).on("change",".exportItem dd input[type='checkbox']",function () {
        var inpLen=$(this).parents("dd").find("input[type='checkbox']").length;
        var checkLen=$(this).parents("dd").find("input[type='checkbox']:checked").length;
        if(inpLen == checkLen)
        {
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',false);
        }
    });
    //首营审批快照
    $('#snapshootBtn').on('click', function () {
        var len=$("#baseTable").XGrid('getSeleRow');
        if(!len || len.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'请先从列表中选择一条数据',
                okValue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        utils.dialog({
            align: 'top',
            width: 90,
            height: 50,
            padding: 8,
            content:'<div class="changeApplyItem formApprovalBlock"><div class="cSelect">预览</div><div class="cDown">下载</div></div>',
            quickClose: true
        }).show(this);
    });
    // $("#baseTable").on('change','td[row-describedby="ck"] input',function(){
    //     var $tr=$(this).parents('tr');
    //     var rowData=$("#baseTable").getRowData($tr.attr('id'));
    //     var id=$tr.attr('id');
    //     setUrlObjectList($tr,id,rowData);
    // })

    $('body').on('click', '.cSelect', function () {
        if(urlObjectList.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'没有可预览附件',
                okVlue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        $.viewImg({
            fileParam:{
                name:'name',
                url:'url'
            },
            list:urlObjectList
        })
    })

    $('body').on('click', '.cDown', function () {
        if(urlObjectList.length < 1){
            utils.dialog({
                title:'提示',
                width: 200,
                content:'没有可下载的附件',
                okVlue: '确定',
                ok:function(){}
            }).show();
            return ;
        }
        //批量下载
        var a=[];
        a.push('<form style="display: none" method="post">');
        urlObjectList.forEach(function(item) {
            console.log(item);
            a.push('<input name="file" value = ' + item.url + '>');
            a.push('<input name="name" value = '+ item.name + '>');
        });
        a.push('</form>');

        var $eleForm = $(a.join(''));
        $eleForm.attr("action", "/proxy-sysmanage/upload/downloadZip");
        //$eleForm.attr("action", "http://**************:8080/upload/download?filepath=G2/M00/00/01/Cgo001tPH3OAXX8_AABEWCsL8kc354.png&name=''");
        $(document.body).append($eleForm);
        //提交表单，实现下载
        $eleForm.submit();
    })
    function setUrlObjectList($tr,id,rowData){
        var a=rowData;
        var fileParam = {};
        if($tr.hasClass('selRow') && a.snapshootUrl){
            fileParam.id = a.id;
            fileParam.name = a.customerCode;
            fileParam.url = a.snapshootUrl;
            urlObjectList.push(fileParam);
        }else if(!$tr.hasClass('selRow')){
            $(urlObjectList).map(function (i,v) {
                if(v.id==a.id){
                    urlObjectList.splice(i,1);
                }
            })
        }
    }
    function format(shijianchuo)
    {
//shijianchuo是整数，否则要parseInt转换
        var time = new Date(shijianchuo);
        var y = time.getFullYear();
        var m = time.getMonth()+1;
        var d = time.getDate();
        var h = time.getHours();
        var mm = time.getMinutes();
        var s = time.getSeconds();
        return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
    }
    function add0(m){return m<10?'0'+m:m }

    // 变更企业主键码
    function btn_modify(el) {
        let _htm = `<div class="pager_wrap" style="position: absolute; width: 100%; height: 100%; top: 0; z-index: 1; background-color: 
rgba(228, 234, 236,0.7)"></div>`
        if($(el).text() == '修改'){
            $('#baseTable').find('input[name=unitCode]').prop('disabled',false);
            $('#grid-pager').append(_htm);
        }else{
            // ajax
            $('#baseTable').find('input[name=unitCode]').prop('disabled',true);
            $('.pager_wrap').remove();
            let allRowData =  $('#baseTable').XGrid('getRowData');
            let filteArr = allRowData.map( item => {
                return {
                    id:  item.id,
                    unitCode: item.unitCode
                }
            });

            console.log(filteArr )
            $.ajax({
                url:'/proxy-supplier/supplier/supplierOrganBaseUnitCode/updateUnitCodeBatch',
                data:JSON.stringify(filteArr),
                type:"post",
                dataType:'json',
                contentType: "application/json",
                success:function(data){
                    if(data.code == 0){
                        utils.dialog({
                            title: '提示',
                            content: '保存成功',
                            okValue: '确定',
                            ok: function () {}
                        }).showModal();
                    }else{
                        utils.dialog({
                            title: '提示',
                            content: '保存失败，刷新后再操作',
                            okValue: '确定',
                            ok: function () {}
                        }).showModal();
                    }
                },
                error: function (err) {
                    console.log(err)
                }

            });

        }
        $(el).text($(el).text() == '修改'? '保存': '修改');


    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("input");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();
        document.body.removeChild(temp);
        return temp;
    }
