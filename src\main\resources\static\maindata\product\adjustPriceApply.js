var pageType = $("#pageType").val();
if ($("#pageType").val() == 0) { //修改
    $("#purchaseContractMode").attr('disabled', 'disabled');
}
if ($("#pageType").val() == 1) { //修改
    disSeleteHander(true);
}
if (processTypeFlag != 0) {
    //初始化表格数据
    initTable(newcolNames, newcolModel)
} else {
    initTable(colNames, colModel)
}

//新增行
$("#addRow").on("click", function() {
    if ($("#centralizedPurchaseType").val() == 1 && !$("#purchaseContractMode").val()) {
        utils.dialog({ content: '请先选择集采签约方式！', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }

    var resultArr = $('#X_Table').getRowData();

    // 点新增行的时候需要传递当前表格中的业务类型的值
    let channelId = resultArr.length > 0 ? resultArr[0].channelId : '',
        channelName = resultArr.length > 0 ? resultArr[0].channelVal : '',
        orgCode = resultArr.length > 0 ? resultArr[0].orgCode : '',
        buyer = resultArr.length > 0 ? resultArr[0].buyer : '',
        buyerVal = resultArr.length > 0 ? resultArr[0].buyerVal : '',
        upperEcAuditStatus = resultArr.length > 0 ? resultArr[0].upperEcAuditStatus : '',
        upperStatues = resultArr.length > 0 ? resultArr[0].upperStatues : '';

    dialog({
        url: '/proxy-product/product/adjustPrice/searchProduct',
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            "typeBywindow": 1,
            "orgCode": orgCode,
            "disableState": 0,
            processType: processTypeFlag,
            resultArr: resultArr,
            channelId: channelId, //channelId
            channelName: channelName,
            buyer: buyer,
            buyerVal: buyerVal,
            upperEcAuditStatus: upperEcAuditStatus,
            upperStatues: upperStatues,
            centralizedPurchaseType: $("#centralizedPurchaseType option:selected").val(),
            purchaseContractMode: $("#purchaseContractMode option:selected").val()
        }, // 给modal 要传递的 的数据
        onclose: function(data) {
            var data = this.returnValue;
            console.log(data)
            if (data) {
                var temp = [];
                var rows = data.resultArr;
                for (var i = 0; i < rows.length; i++) {
                    var orgCode = rows[i].orgCode;
                    temp.push(orgCode)
                }
                temp = unique(temp);
                if (temp.length > 1) {
                    utils.dialog({ content: '请选择同一机构商品', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                for (var i = 0; i < rows.length; i++) {
                    var id = rows[i].productId;
                    let node = rows[i];
                    if (!findInArr(id)) {
                        // 申请原因类型判断
                        if ($('#taskKey').find('option:selected').attr('data-val') === '0') {
                            node.processFlag = 1;
                        }
                        node['productId'] = node['productId']
                        node['standardId'] = node['standardId']
                        node['isPromo'] = "";
                        node['applicationZhiluPrice'] = node['zhiluPrice']
                        node['auditZhiluPrice'] = node['zhiluPrice']
                        node['applicationHeyePrice'] = node['heyePrice']
                        node['auditHeyePrice'] = node['heyePrice']
                        $('#X_Table').XGrid('addRowData', node);
                    } else { //编辑加载采购员
                        var object = $('#' + rows[i].productIdChannel);
                        var buyer = object.find('td[row-describedby=buyer]').text();
                        if (buyer == '') {
                            object.find('td[row-describedby=buyer]').text(rows[i].buyer);
                            object.find('td[row-describedby=buyerVal]').text(rows[i].buyerVal);
                        }
                    }

                }
                let rowData = $('#X_Table').XGrid('getRowData');
                if (rowData.length > 0) {
                    $("#productOrgCode").val(rows[0].orgCode);
                    // getSubsidiariesClassificationByOrgCode();
                    disSeleteHander(true)
                } else {
                    disSeleteHander()
                }
            }
        }
    }).showModal();
});
//关闭按钮
$("#closePage").on("click", function() {
    dialog({
        title: "提示",
        content: "是否保存草稿？",
        width: 300,
        height: 30,
        okValue: '保存',
        button: [{
            value: '关闭',
            callback: function() {
                utils.closeTab();
            }
        }],
        ok: function() {
            var rowData = $('#X_Table').getRowData();
            if (rowData.length == 0) {
                utils.dialog({ content: '至少添加一种商品', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
            $("#saveCaoGao").click()
            setTimeout(function() {
                utils.closeTab();
            }, 2000)
        }
    }).showModal();
});
//提交校验页面必填项
function submitCheck(rowData) {
    console.log(rowData, 'pppsssss')
    if (rowData.length == 0) {
        utils.dialog({ content: '至少添加一种商品', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    if (rowData.length > 200) {
        utils.dialog({ content: '最多添加200条商品', quickClose: true, timeout: 2000 }).showModal();
        return false;
    }
    var dataVal = $('#taskKey').find('option:selected').attr('data-val');
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        //区域调价
        if (processTypeFlag != 0) {
            if (selectRow.applicationAppPrice == "" || selectRow.applicationAppPrice <= 0) {
                utils.dialog({ content: '申请APP售价不能为空或0', quickClose: true, timeout: 2000 }).showModal();
                return false;
            }
        } else { //特殊调价校验
            let productChannelAreaList = getProductChannelAreaList(selectRow.productChannelAreaList)
            for (let i = 0; i < productChannelAreaList.length; i++) {
                let item = productChannelAreaList[i]
                if (item.areaCode === "" || item.areaCode == null) {
                    utils.dialog({ content: '区域类型不能为空', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }
                if (!item.applicationAppPrice || item.applicationAppPrice <= 0) {
                    utils.dialog({ content: '申请APP售价不能为空或0', quickClose: true, timeout: 2000 }).showModal();
                    return false;
                }

            }
        }

        // if (selectRow.applicationZhiluPrice == "") {
        //     utils.dialog({content: '申请智鹿总部采购价不能为空', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }

        if (selectRow.applicationChainGuidePrice == "" || selectRow.applicationChainGuidePrice < 0) {
            utils.dialog({ content: '申请连锁APP售价不能为空', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }

        // if (selectRow.applicationHeyePrice == "") {
        //     utils.dialog({content: '申请荷叶大药房采购价不能为空', quickClose: true, timeout: 2000}).showModal();
        //     return false;
        // }

        if (selectRow.applicationReasonsType == "") {
            utils.dialog({ content: '请填写申请原因', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        //特殊调价流程
        if (dataVal == "0" && selectRow.detailRemark == "") {
            utils.dialog({ content: '请填写提交备注', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
        //特殊调价流程
        if (dataVal == "0" && selectRow.treatmentPlan == "") {
            utils.dialog({ content: '请填写客诉处理方案', quickClose: true, timeout: 2000 }).showModal();
            return false;
        }
    }
    return true;
}

/**
 * 校验商品负毛利
 * @param rowData
 * @returns {boolean}
 */
function checkSubmitPrice(rowData, _this) {
    //提交校验
    // var message = "";
    var message1 = "";
    var zhilumessage = "";
    var chainGuideMessage = "";
    var heyemessage = "";
    var dataVal = $('#taskKey').find('option:selected').attr('data-val');
    for (var i = 0; i < rowData.length; i++) {
        var selectRow = rowData[i];
        //申请连锁APP售价>申请APP售价 拦截(爆款商品不用校验)
        // if (selectRow.inVogue === '0' && (parseFloat(selectRow.applicationChainGuidePrice) > parseFloat(selectRow.applicationAppPrice)) ){
        //     message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
        // }
        // if(processTypeFlag!=0){//正常调价流程
        //     if (selectRow.inVogue === '0' && (parseFloat(selectRow.applicationChainGuidePrice) > parseFloat(selectRow.applicationAppPrice)) ){
        //         message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
        //     }
        // }else{//特殊调价流程
        //     let productChannelAreaList=getProductChannelAreaList(selectRow.productChannelAreaList)
        //     for(let j = 0; j < productChannelAreaList.length; j++){
        //         let item=productChannelAreaList[j]
        //         if (parseFloat(selectRow.applicationChainGuidePrice) > parseFloat(item.applicationAppPrice)&&item.areaCode==0){
        //             message = message + selectRow.productCode + "：" + selectRow.productName + "</br>";
        //             break;
        //         }
        //     }
        // }
        //申请APP售价< 最后含税入库价  负毛利
        if (parseFloat(selectRow.applicationAppPrice) < parseFloat(selectRow.lastPurchasePrice)) {
            message1 = message1 + selectRow.productCode + "：" + selectRow.productName + "</br>";
        }
        //申请连锁APP售价< 底价        负毛利
        console.log("申请连锁APP售价< 底价 APP售价< 底价",
            parseFloat(selectRow.applicationChainGuidePrice),
            parseFloat(selectRow.applicationAppPrice),
            parseFloat(selectRow.floorPrice))
        if (parseFloat(selectRow.applicationChainGuidePrice) < parseFloat(selectRow.floorPrice) || parseFloat(selectRow.applicationAppPrice) < parseFloat(selectRow.floorPrice)) {
            chainGuideMessage = chainGuideMessage + selectRow.productCode + "：" + selectRow.productName + "</br>";
        }
        //智鹿采购价负毛利 拦截
        // if (parseFloat(selectRow.applicationZhiluPrice) < parseFloat(selectRow.lastPurchasePrice)) {
        //     zhilumessage = zhilumessage + selectRow.productCode + "：" + selectRow.productName + "</br>";
        // }
        //荷叶大药房采购价负毛利 拦截
        // if (parseFloat(selectRow.applicationHeyePrice) < parseFloat(selectRow.lastPurchasePrice)) {
        //     heyemessage = heyemessage + selectRow.productCode + "：" + selectRow.productName + "</br>";
        // }
    }
    // if (message != '') {
    //     checkPrice("以下商品申请/审核连锁APP售价＞申请/审核APP售价，请重新录入价格或删除商品！", message);
    //     return false;
    // }
    if (dataVal != "0" && message1 != '') { //特殊调价流程不校验负毛利
        checkPrice("以下商品申请APP售价出现负毛利，请重新录入价格或删除商品！", message1);
        return false;
    }
    // if (dataVal != "0" && chainGuideMessage != '') {//特殊调价流程不校验负毛利
    //     checkPrice("以下商品申请连锁APP价格出现负毛利，请重新录入价格或删除商品！", chainGuideMessage);
    //     return false;
    // }
    if (chainGuideMessage != '') { // 所有调价流程校验负毛利
        checkPriceSubmite("以下商品申请连锁APP价格出现负毛利，确认要继续操作调价吗？", chainGuideMessage, _this);
        return false;
    }
    // if (dataVal != "0" && zhilumessage != '') {//特殊调价流程不校验负毛利
    //     checkPrice("以下商品申请智鹿总部采购价<最后含税单价，只能走特殊调价流程！", zhilumessage);
    //     return false;
    // }
    // if (dataVal != "0" && heyemessage != '') {//特殊调价流程不校验负毛利
    //     checkPrice("以下商品申请荷叶大药房采购价<最后含税单价，只能走特殊调价流程！", heyemessage);
    //     return false;
    // }
    return true;
}

function submitAdjust(el) {
    let rowData = $('#X_Table').getRowData();
    //默认设置APP售价和连锁APP售价均继续调价
    for (let i = 0; i < rowData.length; i++) {
        rowData[i]['appControl'] = false; //继续调APP售价
        rowData[i]['chainControl'] = false; //继续调连锁APP售价
    }
    $('#activeGoods_table tr').not(':eq(0)').each((index, item) => {
        let el = $(item).find(' input[type=checkbox]');

        //有活动的商品ID
        var theProductId = $(item).find('[row-describedby="productId"]').text();
        for (let i = 0; i < rowData.length; i++) {
            var channelId = rowData[i]['channelId'];
            var productId = rowData[i]['productId'];
            if (channelId == '1' && productId == theProductId) { //药帮忙业务类型，未选中活动商品设置标识1
                //未选中商品编码 渠道为药帮忙  APP售价和连锁APP售价都不调
                var rowId = rowData[i]["productIdChannel"];
                if (!$(el[0]).prop('checked') && !$(el[1]).prop('checked')) {
                    rowData[i]['isPromo'] = "1";
                    //商品有活动不继续调价删除该商品
                    deleteRow(rowId);
                } else {
                    rowData[i]['appControl'] = !$(el[0]).prop('checked');
                    if (!$(el[0]).prop('checked')) { //APP售价不继续调价
                        var trId = rowData[i]['id'];
                        let apprice = rowData[i]['appPrice']
                            //特殊调价流程
                        if (processTypeFlag == 0) {
                            let productChannelAreaList = []
                            productChannelAreaList = getProductChannelAreaList(rowData[i].productChannelAreaList);
                            productChannelAreaList.map((item, index) => {
                                // item.appPrice=apprice
                                appPriceGrossMargin(index, trId, apprice);
                            })
                            $(`.applyAppPrice_${rowData[i].productId}`).val(apprice)
                                // $('#X_Table').XGrid('setRowData', rowId, { applicationAppPrice: apprice,auditAppPrice:  apprice,appPrice: apprice,productChannelAreaList:setProductChannelAreaList(productChannelAreaList)});

                        } else { //正常调价流程
                            rowData[i]['applicationAppPrice'] = apprice,
                                rowData[i]['auditAppPrice'] = apprice
                            $(`.applyAppPrice_${rowData[i].productId}`).val(apprice)
                            $('#X_Table').XGrid('setRowData', rowId, {
                                applicationAppPrice: apprice,
                                auditAppPrice: apprice
                            });
                            appPriceGrossMargin(i, trId, apprice);
                        }
                    }
                    rowData[i]['chainControl'] = !$(el[1]).prop('checked');
                    if (!$(el[1]).prop('checked')) { //连锁APP售价不继续调价
                        rowData[i]['applicationChainGuidePrice'] = rowData[i]['chainGuidePrice'],
                            rowData[i]['auditChainGuidePrice'] = rowData[i]['chainGuidePrice']
                        $('#X_Table').XGrid('setRowData', rowId, {
                            applicationChainGuidePrice: rowData[i]['chainGuidePrice'],
                            auditChainGuidePrice: rowData[i]['chainGuidePrice']
                        });
                    }
                }
            }
        }
    });
    let finalRowData = rowData.filter(item => item.isPromo == '') //未参加活动商品集合
        // 需求修改：所有商品都要校验负毛
        // checkSubmitPrice(rowData)
        //校验负毛利
        // if(!checkSubmitPrice(finalRowData)){
        //     return false;
        // }
    var taskKey = $("#taskKey").val();
    var dataRange = $('#taskKey').find('option:selected').attr('data-range');
    var dataMoney = $('#taskKey').find('option:selected').attr('data-money');
    var orgCategory = $('#taskKey').find('option:selected').attr('data-orgCategory');
    //新流程-机构调价，降价 10%-50% 标记
    var flag = $('#taskKey').find('option:selected').attr('data-flag');
    dataRange = dataRange ? dataRange : '';
    dataMoney = dataMoney ? dataMoney : '';
    if ((dataRange != '' && dataMoney != '')) { //新数据
        let changeState = getChangePrice();
        console.log('changeState: ' + changeState);
        if (!changeState) {
            // 拦截
            console.log('拦截')
            return;
        }
        taskKey = taskKey + orgCategory + dataRange + dataMoney + flag;
    }
    var status = el.getAttribute("status");

    //拼接工作流key值,新加字段“备注”,状态,调价类型
    var formData = $("#applyForm").serializeToJSON();
    formData = $.extend(formData, { "workProcessKey": $("#workProcessKey").val() }, { "remark": $("#remark").val() }, { "statues": status }, { "applyType": 1 }, { "taskKey": taskKey }, { "range": dataRange }, { "money": dataMoney }, { "subsidiariesClassification": $("#subsidiariesClassification").val() }, { "centralizedPurchaseType": $("#centralizedPurchaseType").val() }, { "purchaseContractMode": $("#purchaseContractMode").val() });
    var adjustPriceApprovalRecordVo = { "adjustPriceApprovalRecordVo": formData };
    var adjustPriceApprovalRecordDetailVos = { "adjustPriceApprovalRecordDetailVos": finalRowData.length == 0 ? rowData : finalRowData };
    //处理申请原因字段
    adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos = adjustPriceApprovalRecordDetailVos.adjustPriceApprovalRecordDetailVos.map(function(item) {
        if (item.processFlag == 1) {
            item.specialReason = item.applicationReasonsType;
            item.applicationReasonsType = null;
        }
        if (item['promoTypeCode'] != '') {
            item['promoType'] = item['promoTypeCode']
        }
        if (processTypeFlag == 0) {
            item.productChannelAreaList = $.parseJSON(item.productChannelAreaList)
            item.appPrice = null
            item.appPriceIncreaseRise = null
            item.grossMarginStr = null
        } else {
            item.productChannelAreaList = []
        }
        delete item.newAppPrice
        delete item.applyAppPrice //删除html展示列 20210708 by linl
        return item;
    });
    //拼装链接两个json对象
    var places = (JSON.stringify(adjustPriceApprovalRecordVo) + JSON.stringify(adjustPriceApprovalRecordDetailVos)).replace(/}{/, ',');
    parent.showLoading();
    var saveUrl = "";
    if ($("#pageType").val() == 0) { //新增
        saveUrl = "/proxy-product/product/adjustPrice/saveAdjustPrice";
    }
    if ($("#pageType").val() == 1) { //修改
        saveUrl = "/proxy-product/product/adjustPrice/adjustPriceApplyEditSave";
    }
    $.ajax({
        type: "POST",
        url: saveUrl,
        data: places,
        async: false,
        dataType: 'json',
        contentType: "application/json",
        error: function() {
            utils.dialog({ content: '提交失败', quickClose: true, timeout: 2000 }).showModal();
            return false;
        },
        success: function(data) {
            if (data.code == 0) {
                var msg = "";
                if (status == 0) {
                    msg = '保存成功';
                } else if (status == 1) {
                    msg = '提交审核成功';
                } else {
                    msg = '操作成功';
                }
                if (data.result.code == 1) {
                    msg = data.result.msg;
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function() {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            } else {
                utils.dialog({
                    title: "提示",
                    content: data.result.message,
                    width: 300,
                    height: 30,
                    okValue: '确定',
                    ok: function() {

                    }
                }).showModal();
            }
        },
        complete: function() {
            parent.hideLoading();
        }
    });
}
window.onload = function() {
    $('#range').val($('#taskKey').find('option:selected').attr('data-range'))
    $('#money').val($('#taskKey').find('option:selected').attr('data-money'))
    $('#orgCategory').val($('#taskKey').find('option:selected').attr('data-orgCategory'));
}

// 申请流程切换
function processTypeChange(el) {
    // 特殊调价流程单独逻辑
    var rowData = $('#X_Table').XGrid('getRowData');
    var processType = $(el).find('option:selected').attr('data-val');
    if (rowData.length > 0) {
        if ((processType != 0 && rowData[0].processFlag == 1) || (processType == 0 && !rowData[0].processFlag)) {
            if ($(el).find('option:selected').attr('data-val') == 0) {
                rowData.forEach(function(item) {
                    $('#X_Table').XGrid('setRowData', item.productIdChannel, {
                        processFlag: 1,
                        applicationReasonsType: ''
                    });
                });
            } else {
                rowData.forEach(function(item) {
                    $('#X_Table').XGrid('setRowData', item.productIdChannel, {
                        processFlag: '',
                        applicationReasonsType: ''
                    });
                });
            }
        }
    }
    processTypeFlag = processType
    console.log($(el).val());
    $("#range").val($(el).find('option:selected').attr('data-range'));
    $("#money").val($(el).find('option:selected').attr('data-money'));
    $('#orgCategory').val($(el).find('option:selected').attr('data-orgCategory'));
    if (processType != 0) {
        //去除添加的列
        initTable(newcolNames, newcolModel)
    } else {
        initTable(colNames, colModel)
    }
}
//检查调价选择的流程和输入的金额不匹配
function chackChangePrice(row) {
    // 校验规则类型
    var type = $('#taskKey').find('option:selected').attr('data-val');
    // 校验规则
    var option = [
        function(changeRange, changeNum) { //特殊调价流程
            return true;
        },
        function(changeRange, changeNum) { //降价＜10%
            return changeRange == 0 && 0 <= changeNum && changeNum < 10;
        },
        function(changeRange, changeNum) { //10%≤降价＜50%
            return changeRange == 0 && 10 <= changeNum && changeNum < 50;
        },
        function(changeRange, changeNum) { //降价≥50%
            return changeRange == 0 && changeNum >= 50;
        },
        function(changeRange, changeNum) { //涨价＜50%
            return changeRange == 1 && 0 < changeNum && changeNum < 50;
        },
        function(changeRange, changeNum) { //涨价≥50%
            return changeRange == 1 && changeNum >= 50;
        }
    ];
    let applicationAppPrice = row.applicationAppPrice
    if (isNaN(applicationAppPrice) || !applicationAppPrice) {
        return true;
    } else {
        var changeRange = 1;
        var changeNum = (Number(applicationAppPrice) - Number(row.appPrice)).toFixed(2);
        if (changeNum <= 0) { //降价
            changeRange = 0
        }
        changeNum = Math.abs(changeNum);
        var change = 100 * changeNum / Number(row.appPrice);
        if (row.appPrice == "") { //app 售价为空
            change = 100;
        }
        return !option[type](changeRange, change);
    }
}

function getChangePrice() {
    // 校验提示
    var allRowdata = $('#X_Table').XGrid('getRowData');
    var errorRow = allRowdata.filter(function(row) {
        if (processTypeFlag != 0) {
            return chackChangePrice(row)
        } else { //特殊调价校验
            let productChannelAreaList = getProductChannelAreaList(row.productChannelAreaList)
            for (let i = 0; i < productChannelAreaList.length; i++) {
                let item = productChannelAreaList[i]
                return chackChangePrice(item)
            }
        }
    });
    if (errorRow.length) {
        var msg = errorRow.map(function(item) {
            return item.productCode + '-' + item.productName;
        });
        utils.dialog({
            title: '提示',
            content: `<div  style="width: 470px;max-height: 300px;overflow-y: auto">以下商品价格调整范围不满足选定的流程条件，请重新录入或删除商品！<textarea id="copyprice2" readonly style="border:none;line-height: 25px;width: 100%;outline:none;resize:none;">` + msg.join().replace(/,/g, "\n") + `</textarea></div>`,
            okValue: '确定',
            ok: function() {},
            button: [{
                value: '复制信息',
                callback: function() {
                    $("#copyprice2").select(); // 选择对象
                    var flag = document.execCommand("Copy", "false", null); // 执行浏览器复制命令
                    if (flag) utils.dialog({ content: '复制成功！', quickClose: true, timeout: 2000 }).show();
                    return false;
                }
            }],
        }).showModal();
        return false
    } else {
        return true;
    }
}

function getSubsidiariesClassificationByOrgCode() {
    var productOrgCode = $("#productOrgCode").val();
    $.ajax({
        url: '/proxy-product/product/adjustPrice/getSubsidiariesClassificationByOrgCode',
        data: {
            orgCode: productOrgCode
        },
        success: function(data) {
            $("#subsidiariesClassification").val(data.result);
        },
        error: function(err) {

        }
    });
}