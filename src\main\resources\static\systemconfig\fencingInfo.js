/*
围栏编辑用
*/
//初始化map
var map = new AMap.Map("container", {
    center: [116.434381, 39.898515],
    resizeEnable: true,
    zoom: 14
});
//覆盖区域数据
var all_data = [];
var hasOverlayFlag = false;
var overAcreageFlag = false;
$(function () {
    $.ajax({
        url: "/fencing/queryFeningInfos?shippingMethodCode=" + $("#shippingMethodCode").val() + "&storageCode=" + $("#storageCode").val(),
        type: 'get',
        dataType: 'json',
        success: function (data) {
            console.log(data)
            var viewPointsArr = [];
            if (data.code == 0) {
                if (data.result.currentFencingInfo.length > 0) {
                    arr = data.result.currentFencingInfo.map(function (item, index) {
                        return item.coordinates
                    })
                    console.log(arr)
                    //var newArr = JSON.parse(arr);

                    arr.forEach(function (ite,ind) {
                        var path = [];
                        JSON.parse(ite).forEach(function (item, key) {
                            path.push(new AMap.LngLat(item.lng, item.lat))

                        })
                        console.log(path)
                        var polygon = new AMap.Polygon({
                            path: path,
                            fillColor: '#fff', // 多边形填充颜色
                            borderWeight: 2, // 线条宽度，默认为 1
                            strokeColor: 'red', // 线条颜色
                        });
                        map.add(polygon);

                        polygon.on('rightclick',function (e) {
                            polygon.setMap();
                        })
                    })
                }
            }

        }
    })
})
//返回
function btn_back() {
    utils.dialog({
        title: '温馨提示',
        content: '返回后当前修改数据不会被保存，请确认',
        okValue: '确认',
        ok: function () {
            utils.closeTab();
        },
        cancelValue: '取消',
        cancel: function () {
        }
    }).showModal();
}

//保存
function btn_save() {
    $("#sub").click(); // 调用覆盖物保存方法。
    //判断是否有重叠区域
    if(!hasOverlayFlag&&!overAcreageFlag){
        var shipingMethodInfo = $.extend({"id": $("#shippingMethodId").val()}, {"shippingMethodName": $("#shippingMethodName").val()}, {"status": $("input[name='status']:checked").val()});
        var paramArray = [];
        for (var x = 0; x < all_data.length; x++) {
            paramArray.push(JSON.stringify(all_data[x]));
        }
        //获取所有的点的数组
        var locations = {"locations": paramArray};
        var param = (JSON.stringify(shipingMethodInfo) + JSON.stringify(locations)).replace(/}{/, ',');
        parent.showLoading();
        $.ajax({
            type: "POST",
            url: "/fencing/saveFencingInfo",
            data: param,
            async: false,
            dataType: 'json',
            contentType: "application/json",
            error: function () {
                alert("提交失败！");
            },
            success: function (data) {
                if (data.code == 0) {
                    var msg = "保存成功";
                    if (data.result.code == 1) {
                        msg = data.result.message;
                        utils.dialog({content: msg+",请重试或重新选择区域", quickClose: true, timeout: 2000}).showModal();
                    }else {
                        utils.dialog({
                            title: "提示",
                            content: msg,
                            width:300,
                            height:30,
                            okValue: '确定',
                            ok: function () {
                                utils.closeTab();
                            }
                        }).showModal();
                    }
                } else {
                    utils.dialog({content: data.result, quickClose: true, timeout: 2000}).showModal();
                }
            },
            complete: function () {
                parent.hideLoading();
            }
        });
    }
}


//保存所有覆盖物
$("#sub").on("click", function (e) {
    var overlaysList = map.getAllOverlays('polygon');
    all_data = overlaysList.map(function (item, key) {
        return item.getPath();
    });
    if(overlaysList.length > 1){
        for (let i = 0; i < overlaysList.length; i++) {
            const a_path = overlaysList[i].getPath();
            for (let j = 0; j < i; j++) {
                const b_path = overlaysList[j].getPath();
                var flag = computePath(a_path, b_path);
                if (flag) {
                    overlaysList[i].setOptions({
                        fillColor: "red"
                    });
                    overlaysList[j].setOptions({
                        fillColor: "red"
                    });
                    hasOverlayFlag = true;
                    utils.dialog({
                        title:'提示',
                        content:'存在重叠区域',
                        timeout:3000
                    }).showModal();
                    break;
                }else{
                    hasOverlayFlag = false;
                }
            }
        }
    }else{
        hasOverlayFlag = false;
    }

    overlaysList.forEach(function (item, key) {
        var a_path = item.getPath();
    });
    console.log(all_data)

})
//清除所有覆盖物
$("#clear").on("click", function (e) {
    map.clearMap();
})
//复现覆盖物
$("#set").on("click", function (e) {
    all_data.forEach(function (item, key) {
        var name = 'polygon_' + key;
        new AMap.Polygon({
            extData: {
                id: key
            },
            map: map,
            strokeWeight: 1,
            strokeColor: '#0091ea',
            fillColor: '#80d8ff',
            fillOpacity: 0.2,
            path: item
        });
    })
})

//判断两个区域是否重叠
function computePath(a_path, b_path) {
    // 小圈是否在大圈内
    var isRingInRing = AMap.GeometryUtil.isRingInRing(a_path, b_path);
    // 两圈是否交叉
    var doesRingRingIntersect = AMap.GeometryUtil.doesRingRingIntersect(a_path, b_path);
    return isRingInRing || doesRingRingIntersect
}

//绘制
var mouseTool = new AMap.MouseTool(map);
//绘制完成事件
mouseTool.on('draw', function (e) {
    var tp = e.obj.CLASS_NAME;
    var position = e.obj.B.position;
    console.log(e);
    if (tp == "AMap.Marker") {
        //点
        var overlaysList = map.getAllOverlays('polygon');
        var flag = overlaysList.some(function (item, index) {
            return computeMarker(position, item.getPath());
        })
        if (!flag) {
            if (confirm("该点不在配送区域内")) {
                /* setTimeout(() => {
                  map.remove(e)
                }, 2000); */
            }
        }
    } else if (tp == "AMap.Polygon") {
        //多边形
        console.log(e)
        var _obj = e.obj;
        _obj.on('rightclick',function (e) {
            _obj.setMap();
           hasOverlayFlag = false;
           overAcreageFlag = false;
        });
        var getPathArr = e.obj.getPath();
        var pointsArr = [];
        for (var i of getPathArr){
            var arr = [];
            arr.push(i.lng);
            arr.push(i.lat)
            pointsArr.push(arr)
        }
        //计算封闭区域
        var area = AMap.GeometryUtil.ringArea(pointsArr);
        //console.log('区域面积')
        //console.log(area)
        //限制区域大小
        if (area > 28260000) {
            overAcreageFlag = true;
            utils.dialog({
                title:'提示',
                content:'绘制区域过大,请重新绘制',
                timeout:3000
            }).showModal();
        }else {
            overAcreageFlag = false;
        }
    }
})

//判断点是否在覆盖区域
function computeMarker(marker, path) {
    return AMap.GeometryUtil.isPointInRing(marker, path);
}

//点
function drawMarker() {
    mouseTool.marker({
        extData: {
            shape: 0
        },
        strokeColor: "#000",
    })
}

//多边形
function drawPolygon() {
    mouseTool.polygon({
        extData: {
            shape: 3
        },
        strokeColor: "#FF33FF",
        strokeOpacity: 1,
        strokeWeight: 6,
        strokeOpacity: 0.2,
        fillColor: '#1791fc',
        fillOpacity: 0.4,
        // 线样式还支持 'dashed'
        strokeStyle: "solid",
        // strokeStyle是dashed时有效
        // strokeDasharray: [30,10],
    })
}

//矩形
function drawRectangle() {
    mouseTool.rectangle({
        extData: {
            shape: 2
        },
        strokeColor: 'red',
        strokeOpacity: 0.5,
        strokeWeight: 6,
        fillColor: 'blue',
        fillOpacity: 0.5,
        // strokeStyle还支持 solid
        strokeStyle: 'solid',
        // strokeDasharray: [30,10],
    })
}

/*//接收的数据
var old_data = [{
   shape: 3,
   path: [
     [116.376907, 39.910967],
     [116.384911, 39.919505],
     [116.40109, 39.919728],
     [116.411132, 39.911408],
   ]
 },
 {
   shape: 3,
   path: [
     [116.390233, 39.906602],
     [116.395254, 39.908668],
     [116.400661, 39.906667],
     [116.400962, 39.898589],
     [116.395769, 39.894855],
     [116.390147, 39.898049],
   ]
 }
];
//图形实例集合
var obj_shape = [];
//接受数据实例化
old_data.forEach(function (item, key) {
 obj_shape[key] = new AMap.Polygon({
   map: map,
   fillColor: 'blue',
   fillOpacity: 0.3,
   path: item.path,
   //draggable: true
 });
}) */


var all_shape = [];

// mouseTool.on('draw', function (event) {
//   // event.obj 为绘制出来的覆盖物对象
//   //log.info('覆盖物对象绘制完成')

//   var map_data = event.obj.B;
//   /*  var all_path = old_data.map(function (item, key) {
//      return item.path.map(function (val, index) {
//        return [val.lng, val.lat]
//      })
//    }) */
//   console.log(map_data.path);
//   if (map_data.extData.shape == 0) {
//     //点
//     compute2([map_data.position.lng, map_data.position.lat], all_path)
//   } else if (map_data.extData.shape == 1) {
//     //圆形
//   } else if (map_data.extData.shape == 2) {
//     //矩形
//   } else if (map_data.extData.shape == 3) {
//     //多边形
//     /* var path = map_data.path.map(function (item, index) {
//       return [item.lng, item.lat]
//     });
//     console.log(map_data.path); */

//     //清除原图形
//     //map.remove(event)
//     //创建新的图形
//     /*
//     var polygon = new AMap.Polygon({
//       map: map,
//       fillColor: 'blue',
//       fillOpacity: 0.3,
//       path: path,
//       draggable: true
//     }); */
//     //compute1(path, all_path);
//     /* polygon.on('dragging', function (e) {

//      }) */
//   }
// })

function compute1(a_shape, all_shape) {
    var flag = 0;
    for (let i = 0; i < all_shape.length; i++) {
        const item = all_shape[i];
        // 小圈是否在大圈内
        var isRingInRing = AMap.GeometryUtil.isRingInRing(a_shape, item);
        // 两圈是否交叉
        var doesRingRingIntersect = AMap.GeometryUtil.doesRingRingIntersect(a_shape, item.path);
        if (isRingInRing || doesRingRingIntersect) {
            flag = 1;
            alert("存在互斥区域");
            break;
        }
    }
    //textBox.setText("存在互斥区域")
}

function compute2(a_dot, all_path) {
    var flag = all_path.some(function (item, index) {
        return AMap.GeometryUtil.isPointInRing(a_dot, item);
    })
    if (!flag) {
        alert("该点不在配送区域内");
    }
}

//自动选区行政区域
var district, polygons = [], citycode;
var citySelect = document.getElementById('city');
var districtSelect = document.getElementById('district');
var areaSelect = document.getElementById('street');

/* map = new AMap.Map('container', {
    resizeEnable: true,
    center: [116.30946, 39.937629],
    zoom: 3
}); */
//行政区划查询
var opts = {
    subdistrict: 1, //返回下一级行政区
    showbiz: false //最后一级返回街道信息
};
district = new AMap.DistrictSearch(opts); //注意：需要使用插件同步下发功能才能这样直接使用
district.search('中国', function (status, result) {
    if (status == 'complete') {
        getData(result.districtList[0]);
    }
});

function getData(data, level) {
    var bounds = data.boundaries;
    if (bounds) {
        for (var i = 0, l = bounds.length; i < l; i++) {
            var polygon = new AMap.Polygon({
                map: map,
                strokeWeight: 1,
                strokeColor: '#0091ea',
                fillColor: '#80d8ff',
                fillOpacity: 0.2,
                path: bounds[i]
            });
            polygons.push(polygon);
        }
        map.setFitView(); //地图自适应
    }

    //清空下一级别的下拉列表
    if (level === 'province') {
        citySelect.innerHTML = '';
        districtSelect.innerHTML = '';
    } else if (level === 'city') {
        districtSelect.innerHTML = '';
    } else if (level === 'district') {
    }

    var subList = data.districtList;
    if (subList) {
        var contentSub = new Option('--请选择--');
        var curlevel = subList[0].level;
        if (curlevel == "street") return;
        var curList = document.querySelector('#' + curlevel);
        curList.add(contentSub);
        for (var i = 0, l = subList.length; i < l; i++) {
            var name = subList[i].name;
            var levelSub = subList[i].level;
            var cityCode = subList[i].citycode;
            contentSub = new Option(name);
            contentSub.setAttribute("value", levelSub);
            contentSub.center = subList[i].center;
            contentSub.adcode = subList[i].adcode;
            curList.add(contentSub);
        }
    }
}

function search(obj) {
    //清除地图上所有覆盖物
    for (var i = 0, l = polygons.length; i < l; i++) {
        polygons[i].setMap(null);
    }
    var option = obj[obj.options.selectedIndex];
    var keyword = option.text; //关键字
    var adcode = option.adcode;
    district.setLevel(option.value); //行政区级别
    district.setExtensions('all');
    //行政区查询
    //按照adcode进行查询可以保证数据返回的唯一性
    district.search(adcode, function (status, result) {
        if (status === 'complete') {
            getData(result.districtList[0], obj.id);
        }
    });
}

/* function setCenter(obj){
    map.setCenter(obj[obj.options.selectedIndex].center)
} */

//地图区域自适应
map.setFitView();