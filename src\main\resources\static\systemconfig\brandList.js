$(function () {
    $('#brand_Table').XGrid({
        url:'/proxy-sysmanage/sysmanage/brand/listPage',
		mtype : "POST",
		postData : {
			uniteField : $("#brand_uniteField").val(),
			status : $("#brand_status").val(),
		},
        colNames: ['品牌id', '品牌名称', '助记码', '是否停用', '关键词',  '创建人', '创建日期', '操作'],
        colModel: [
            {
                name: 'id'
            }, {
                name: 'brandName'
            }, {
                name: 'brandCode'
            }, {
                name: 'status_format',
                formatter: function (e, d, rData) {
                	if ("true" == String(rData.status)) {
                		return "是";
					} else {
						return "否";
					}
                }
            }, {
                name: 'brandKeyword_format',
                formatter: function (e, d, rData) {
                	if (String(rData.brandKeyword).length > 70) {
                		return String(rData.brandKeyword).substring(0, 70);
					} else {
						return String(rData.brandKeyword);
					}
                }
            }, {
                name: 'createUser'
            }, {
                name: 'createTime',
                formatter:datetimeFormatter
            }, {
				name : 'operation',
				width : 250,
				formatter: operation
			}, {
				name : 'status',
				hidden: true
			}, {
				name : 'brandKeyword',
				hidden: true
			} ],
        rowNum: 20,
        altRows: true,
        pager: '#grid-pager',
        ondblClickRow: function (id, dom, obj, index, event) {
            //双击事件回调函数
            var el = document.querySelector('#dialog_Block');//html元素
//            $('#brandForm')[0].reset();
            var selRow = obj;
            if (selRow) {
            	$('#hidebrandId').show();
            	$('#hidebrandName').show();
            	$("#brand_id").val(selRow.id);
                $("#brand_name").val(selRow.brandName);
                $("#brand_code").val(selRow.brandCode);
                var val=selRow.brandKeyword;
                $('input[data-role="tagsinput"]').tagsinput('removeAll');
                $('input[data-role="tagsinput"]').tagsinput('add',val);
                if(selRow.status=="true"){
                    $(":radio[name='brand_status'][value='true']").prop("checked", "checked");
                }else{
                    $(":radio[name='brand_status'][value='false']").prop("checked", "checked");
                }
                
                $("#brand_name").attr("disabled",true);
                $(".tagsinput input[type='text']").prop("disabled",true);
                $(":radio[name='brand_status']").attr("disabled",true);

                utils.dialog({
                    title: '查看明细',
                    content: el,
                }).showModal();
                $(".tag").dblclick(function (ev) {
                    return false;
                })

            }else {
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }
        },
        onSelectRow: function (id, dom, obj, index, event) {
            //选中事件
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
        }
    });
})

//新增
$('#addBtn').on('click', function () {
	$('#hidebrandId').hide();
	$('#hidebrandName').hide();
	$("#brand_id").val("");
    $("#brand_name").val("");
    $("#brand_code").val("");
    $('input[data-role="tagsinput"]').tagsinput('removeAll');
    $(":radio[name='brand_status'][value='false']").prop("checked", "checked");
    
    $("#brand_name").attr("disabled", false);
    $(".tagsinput input[type='text']").prop("disabled", false);
    $(":radio[name='brand_status']").attr("disabled", false);
    
    var el = document.querySelector('#dialog_Block');//html元素
    
    utils.dialog({
        title: '新增',
        content: el,
        width:'630px',
        okValue: '确定',
        ok: function () {
            if (validform("brandForm").form()) {
            	var brand_keyword_val = $('input[name="brandKeyword"]').val();
                $.ajax({
                    url: "/proxy-sysmanage/sysmanage/brand/saveBrand",
                    type: "POST",
                    data: {
                    	brandName: $("#brand_name").val(),
                    	brandKeyword: brand_keyword_val,
                    	status: $("input[name='brand_status']:checked").val()
                    },
                    success: function (result) {
	                	if (result.result) {
							// 刷新当前列表
	                		$('#brand_Table').trigger('reloadGrid');
						} else {
							utils.dialog({content: result.msg, quickClose: true, timeout: 2000}).showModal();
						}
                	}
                })
            } else {//验证不通过
                utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        },
        cancelValue: '取消',
        cancel: function () {
        }
    }).showModal();

});

// 查询
$("#queryBrandbut").click(function () {
    $('#brand_Table').setGridParam({
        url:'/proxy-sysmanage/sysmanage/brand/listPage',
		mtype : "POST",
		postData : {
			uniteField : $("#brand_uniteField").val(),
			status : $("#brand_status").val(),
		},
    }).trigger('reloadGrid');

});

// 时间格式化
function datetimeFormatter(val) {
    if (val != null && val !="") {
        return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
    } else {
        return "";
    }
};

// 编辑
function operation(){
    return  "<a href='javascript:modify()'>编辑</a>"
}

//修改
function modify() {
	var selRow = $('#brand_Table').XGrid('getSeleRow');
	$('#hidebrandId').show();
	$('#hidebrandName').show();
	$("#brand_id").val(selRow.id);
    $("#brand_name").val(selRow.brandName);
    $("#brand_code").val(selRow.brandCode);
    var val=selRow.brandKeyword;
    $('input[data-role="tagsinput"]').tagsinput('removeAll');
    $('input[data-role="tagsinput"]').tagsinput('add',val);
    if(selRow.status=="true"){
        $(":radio[name='brand_status'][value='true']").prop("checked", "checked");
    }else{
        $(":radio[name='brand_status'][value='false']").prop("checked", "checked");
    }
    
    $("#brand_name").attr("disabled", false);
    $(".tagsinput input[type='text']").prop("disabled", false);
    $(":radio[name='brand_status']").attr("disabled", false);
    
    var el = document.querySelector('#dialog_Block');//html元素
    
    utils.dialog({
        title: '编辑',
        content: el,
        width:'630px',
        okValue: '确定',
        ok: function () {
            if (validform("brandForm").form()) {
            	var brand_keyword_val = $('input[name="brandKeyword"]').val();
                $.ajax({
                    url: "/proxy-sysmanage/sysmanage/brand/saveBrand",
                    type: "POST",
                    data: {
                    	id: $("#brand_id").val(),
                    	brandName: $("#brand_name").val(),
                    	brandKeyword: brand_keyword_val,
                    	status: $("input[name='brand_status']:checked").val()
                    },
                    success: function (result) {
                    	if (result.result) {
    						// 刷新当前列表
                    		$('#brand_Table').trigger('reloadGrid');
    					} else {
    						utils.dialog({content: result.msg, quickClose: true, timeout: 2000}).showModal();
    					}
                    }
                })
            } else {//验证不通过
                utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                return false;
            }
        },
        cancelValue: '取消',
        cancel: function () {
        }
    }).showModal();
}