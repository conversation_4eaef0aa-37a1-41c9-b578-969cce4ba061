var dialog = parent.dialog.get(window);
var initDataArr=dialog.data.initDataArr;
console.log(initDataArr)
var selArr=[];
$('#X_Tableb').XGrid({
    url:"/proxy-supplier/supplier/supplierOrganBase/supplierBase/ajaxSupplierBaseList?queryFields="+""+"&auditStatus=2",
    colNames: ['', '供应商编码', '供应商名称', '供应商类型', '业务员', '业务员电话', '营业执照号','','',''],
    colModel: [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        }, {
            name: 'supplierCode',
            index: 'supplierCode',
            width: 200

        }, {
            name: 'supplierName',
            index: 'supplierName',
            width: 60
        }, {
            name: 'supplierTypeName',
            index: 'supplierTypeName',
            width: 150
        }, {
            name: 'delivery',
            index: 'delivery',
            width: 250
        }, {
            name: 'deliveryPhone',
            index: 'deliveryPhone',
            width: 250
        }, {
            name: 'supplierBusinessNum',
            index: 'supplierBusinessNum',
            width: 250
        }, {
            name: 'disableStateBase',
            index: 'disableStateBase',
            width: 250,
            hidden: true
        },{
            name: 'supplierTypeId',
            index: 'supplierTypeId',
            width: 250,
            hidden: true
        },{
            name: 'orgCode',
            index: 'orgCode',
            hidden:true
        }
    ],
    
    rowNum: 20,
    rowList:[10,20,50],
    rownumbers:true,
    altRows: true,//设置为交替行表格,默认为false
    pager: '#grid-pager',
    multiselect: true,
    onSelectRow: function (id, dom, obj, index, event) {
        //添加已选商品
        var $tr = $(dom);
        var check=$tr.find('[row-describedby="ck"] input').prop("checked");
        obj.disableState = obj.disableStateBase;
        getCheckData(check,obj);
    },
    gridComplete: function () {
        setTimeout(function () {
            $("#X_Tableb th:first input").hide();
            if(initDataArr.length > 0)
            {
                var rowData=$('#X_Tableb').getRowData();
                var arr=initDataArr;
                console.log(arr,rowData);
                for(var i=0;i<arr.length;i++)
                {
                    console.log(arr[i].supplierCode)
                    for(var j=0;j<rowData.length;j++){
                        var supplierCode=rowData[j].supplierCode;
                        console.log(supplierCode)
                        if(supplierCode == arr[i].supplierCode)
                        {
                            $('#X_Tableb tr').eq(j+1).find("td[row-describedby='ck'] input").prop("checked",true).trigger('input');
                        }
                    }
                }
            }
        },3)
    }
});
//添加已选商品
$('#X_Tableb').on("change","td[row-describedby='ck'] input",function(ev){
    var check=this.checked;
    var $tr = $(this).parents('tr');
    var id=$tr.attr('id');
    var data=$('#X_Tableb').XGrid('getRowData', id);
    data.disableState = data.disableStateBase;
    getCheckData(check,data);
    ev.stopPropagation();
})
//查询
$("#SearchBtn").on("click", function () {
    $('#X_Tableb').XGrid('setGridParam', {
        url:"/proxy-supplier/supplier/supplierOrganBase/supplierBase/ajaxSupplierBaseList",
        postData: {
            "queryFields": $("#queryFields").val(),
            "supplierBusinessNum":$("#queryFields").val(),
            "auditStatus":"2"
        },page:1

    }).trigger('reloadGrid');
});
//选择
$("#SelectBtn").on("click", function () {
	 if(selArr.length > 0){
	        for(var i=0;i<selArr.length;i++){
	            selArr[i].disableState_a=selArr[i].disableStateBase;
	            selArr[i].keyId=selArr[i].id;
	            selArr[i].supplierType=selArr[i].supplierTypeId;
	            selArr[i].supplierOrganId=selArr[i].id;
	        }
	    }
    dialog.close(selArr);
});
//组装已选数据
function getCheckData(check,data){
    if(check){
        if(!fidInArr(data.supplierCode)){
            selArr.push(data);
        }
    }else{
        for(var i=0;i<selArr.length;i++){
            if(selArr[i].supplierCode == data.supplierCode){
                selArr.splice(i,1);
                break;
            }
        }
    }
}
//数组中查找id
function fidInArr(supplierCode){
    for(var i=0;i<selArr.length;i++)
    {
        if(selArr[i].supplierCode == supplierCode)
        {
            return true;
        }
    }
    return false;
}

    
    

