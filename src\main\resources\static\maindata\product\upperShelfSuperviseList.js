$('div[fold=head]').fold({
    sub: 'sub'
});
var colNames=['阶段状态','机构', '业务类型','商品编号','原商品编码','小包装条码', '商品名','通用名', '商品规格', '生产厂家','单位', '商品定位',
    '是否专供','APP销售价是否维价', '应季类型', '建议终端售价', '建议APP售价', '采购员', '销售状态', '库存数量', '最后含税进价','最后入库时间', '最后供应商',
    '商品产地', '剂型', '处方分类', '批准文号', '存储条件', '一级分类', '二级分类', '三级分类', '四级分类'];
var colModel= [
    {
        name: 'stageState',
        index: 'stageState',
        width:80
    },  {
        name: 'orgName',
        index: 'orgName',
        width:220
    }, {
        name: 'channelId',
        index: 'channelId',
        width:80
    }, {
        name: 'productCode',
        index: 'productCode',
        width:110
    }, {
        name: 'oldProductCode',
        index: 'oldProductCode',
        width:140
    }, {
        name: 'smallPackageBarCode',
        index: 'smallPackageBarCode',
        width:140
    }, {
        name: 'productName',
        index: 'productName',
        width:110
    }, {
        name: 'commonName',
        index: 'commonName',
        width:110
    }, {
        name: 'specifications',
        index: 'specifications',
        width:140
    }, {
        name: 'manufacturerVal',
        index: 'manufacturerVal',
        width:240
    },{
        name: 'packingUnitVal',
        index: 'packingUnitVal',
        width:80
    }, {
        name: 'commodityPositionVal',
        index: 'commodityPositionVal',
        width:110
    },{
        name: 'exclusiveYn',
        index: 'exclusiveYn',
        hidden:true,
        hidegrid:true,
        width:80
    }, {
        name: 'dimensionSalesPriceYnVal',
        index: 'dimensionSalesPriceYnVal',
        width:160
    }, {
        name: 'seasonalVarietiesVal',
        index: 'seasonalVarietiesVal',
        width:100
    },{
        name: 'terminalPrice',
        index: 'terminalPrice',
        formatter: function (e) {
            return Number(e).toFixed(2);
        },
        width:140
    },{
        name: 'appPrice',
        index: 'appPrice',
        formatter: function (e) {
            return Number(e).toFixed(2);
        },
        width:110
    },{
        name: 'buyerVal',
        index: 'buyerVal',
        width:80
    }, {
        name: 'ecProductStatus',
        index: 'ecProductStatus',
        width:80
    }, {
        name: 'inventoryQuantity',
        index: 'inventoryQuantity',
        width:80
    }, {
        name: 'lastIncludingTaxPurchasePrice',
        index: 'lastIncludingTaxPurchasePrice',
        width:110
    }, {
        name: 'lastStorageTime',
        index: 'lastStorageTime',
        formatter: function (e) {
            if(e){
                return dateFormatter(e);
            }
        },
        width:110
    }, {
        name: 'lastSupplier',
        index: 'lastSupplier',
        width:220
    }, {
        name: 'producingArea',
        index: 'producingArea',
        width:100
    }, {
        name: 'dosageFormVal',
        index: 'dosageFormVal',
        width:100
    }, {
        name: 'prescriptionClassificationVal',
        index: 'prescriptionClassificationVal',
        width:100
    }, {
        name: 'approvalNumber',
        index: 'approvalNumber',
        width:160
    }, {
        name: 'storageConditionsVal',
        index: 'storageConditionsVal',
        width:80
    }, {
        name: 'firstCategoryVal',
        index: 'firstCategoryVal',
        width:100
    }, {
        name: 'secondCategoryVal',
        index: 'secondCategoryVal',
        width:120
    }, {
        name: 'thirdCategoryVal',
        index: 'thirdCategoryVal',
        width:120
    }, {
        name: 'fourCategoryVal',
        index: 'fourCategoryVal',
        width:100
    }];

//机构-商品-上下架管理-待上架商品督办，显示"操作列"
if($('#getSysOrgCode').val() !== '001'){
    colNames.unshift('操作列')
    colModel.unshift({
        name: 'handleColumn',
        index: 'handleColumn',
        formatter: function (val,rowType,rowData) {
            if(rowData.stageState === '未提申请' || rowData.stageState === '4') {
                return `<a href="javascript:;" data-orgcode="${rowData.orgCode}" data-productcode="${rowData.productCode}" class="handleColumn">发起上架申请</a>`;
            }else {
                return '';
            }
        },
        width: 120
    })
}

$('#X_Table').XGrid({
    url:"/proxy-product/product/upperShelf/querySuperviseProduct",
    colNames: colNames,
    colModel: colModel,
    key: 'oldProductCode',
    datasouce: 'json',
    rowNum: 20,
    altRows: true,//设置为交替行表格,默认为false
    rownumbers: true,
    rowList: [20, 50,100], //分页条数下拉选择
    pager: '#grid-pager',
    key: 'productId',
    ondblClickRow: function (id, dom, obj, index, event) {
        //console.log('双击行事件', id, dom, obj, index, event);
    },
    onSelectRow: function (id, dom, obj, index, event) {
        //console.log('单机行事件', id, dom, obj, index, event);
    }
});
//查询数据，重置data
$('#SearchBtn').on('click', function () {
    $('#X_Table').setGridParam({
        url: '/proxy-product/product/upperShelf/querySuperviseProduct',
        postData: {
            "orgCode": $("#orgCode").find("option:selected").val(),
            "stageState": $("#stageState").find("option:selected").val(),
            "channelId":$("#channelId").val(),
            "productName":$("#productName").val(),
            "userId":$("#userId").val()
        }
    }).trigger('reloadGrid');
})
// 导出
$("#exportBtn").on("click", function () {
    utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text())).then( () => {
        return false;
    }).catch( () => {
    //原始处理逻辑代码
        utils.dialog({
        title: '提示',
        content:"数据量大的时候耗时较长，请耐心等待。",
        okValue: '确定',
        ok: function () {
            //parent.showLoading()
            var body = document.body;
            var form = $(body).find('form#searchForm');
            $(form).attr("action","/proxy-product/product/upperShelf/exportSuperviseProduct");
            $(form).submit();
            // setTimeout(function () {
            //     parent.hideLoading()
            // },2000)
        },
        cancelValue: '取消',
        cancel: function () { },
    }).showModal();
    });
});


//时间格式化
function dateFormatter(inputTime) {
    if (inputTime == null || inputTime == "")
        return "";
    var date = new Date(inputTime);
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    var h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    var minute = date.getMinutes();
    var second = date.getSeconds();
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d;
    // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
}
//设置显示列
$("#setRow").click(function () {
    $('#X_Table').XGrid('filterTableHead',800);
});

$(function () {
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('0').then( res => {
            console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });

    //初始化采购员
    initPurchaser()

    //机构select的change
    $("#orgCode").on('change', function () {
        $("#userId").val("");
        $("#userIdVal").val("");
        initPurchaser();
    });

    //采购员查询
    function initPurchaser(){
        $("#userIdVal").Autocomplete({
            serviceUrl: '/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode',
            paramName: 'userName',//查询参数，默认 query
            params: { "orgCode": $("#orgCode").val() },
            dataType: 'json',
            minChars: '0', //触发自动匹配的最小字符数
            maxHeight: '300', //默认300高度
            dataReader: { list: "result", data: "id", value: "userName" },
            triggerSelectOnValidInput: false, // 必选
            showNoSuggestionNotice: true, //显示查无结果的container
            noSuggestionNotice: '查询无结果',//查无结果的提示语
            onSelect: function (result) {
                $("#userId").val(result.data);
                $("#userIdVal").attr("data-value", result.value);
            },
            onNoneSelect: function (params, suggestions) {
                if ($("#userIdVal").val() !== $("#userIdVal").attr("data-value")) {
                    $("#userId").val("");
                    $("#userIdVal").val("");
                }
            }
        });
    }

    //发起上架申请
    $('#X_Table').on('click','.handleColumn', (e)=>{
        const orgCode = e.target.getAttribute('data-orgcode')
        const productCode = e.target.getAttribute('data-productcode')
        utils.openTabs('productUpperShelfApply','商品上架申请',`/proxy-product/product/upperShelf/toUpperShelfApply?orgCode=${orgCode}&productCode=${productCode}`)
    })
})
