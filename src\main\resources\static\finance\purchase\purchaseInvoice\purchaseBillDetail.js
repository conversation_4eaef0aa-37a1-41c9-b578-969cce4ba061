$(function () {
    jQuery.validator.addMethod("eightLh", function (value, element) {
        return this.optional(element) || value.length == 8;
    }, "发票号为8位数字");

    jQuery.validator.addMethod("isEqZone", function (value, element) {
        return this.optional(element) || value != 0;
    }, "金额不能为零");

    jQuery.validator.addMethod("isLg", function (value, element) {
        value = Math.abs(parseFloat(value));
        var preValue = $(element).closest('tr').find('td[row-describedby="noInvoiceTaxAmount"] input').val();
        return this.optional(element) || value >= Math.abs(parseFloat(preValue));
    }, "不得小于发票不含税金额");



    //重新计算金额合计
    resetStoreInList();

    function resetStoreInList() {
        $.each(storeInList, function (index, item) {
            var stockOrderNo = item.stockOrderNo;
            item.priceTaxSum = 0;
            item.sumTaxMoney = 0;
            item.sumNoTaxMoney = 0;
            $.each(storeInDetailList, function (index, obj) {
                if (stockOrderNo === obj.stockOrderNo) {
                    item.priceTaxSum += parseFloat(obj.productContainTaxMoney);
                    item.sumTaxMoney += parseFloat(obj.productTaxMoney);
                    item.sumNoTaxMoney += parseFloat(obj.procutNoTaxMoney);
                }
            })
            storeInList[index].priceTaxSum = item.priceTaxSum.toFixed(2);
            storeInList[index].sumTaxMoney = item.sumTaxMoney.toFixed(2);
            storeInList[index].sumNoTaxMoney = item.sumNoTaxMoney.toFixed(2);

        })
    }

    //入库单详情
    function initDialogTable(data) {
        data = $.map(data,function (item,index) {
            item.id = item.productCode;
            return item
        });
        $('#X_Table1').XGrid({
            data: data,
            colNames: ['id', '单据日期', '采购单据号', '采购单据行号','采购单据行状态', '商品编码', '商品名称', '规格', '小包装单位', '含税单价',
                '小包装数量','本次开票数量','已开票数量',
                '金额合计', '税额合计', '价税合计'
            ],
            colModel: [{
                name: 'id',
                hidden: true,
                hidegrid: true
            },
                {
                    name: 'storeTime'
                }, {
                    name: 'stockOrderNo'
                }, {
                    name: 'billsSort'
                }, {
                    name: 'ticketStatus',
                    formatter: function (value) {
                        if (value != '') {
                            if (value == '0') {
                                return '未开发票';
                            } else if (value == '1') {
                                return '部分开票';
                            } else if (value == '2') {
                                return '已开票';
                            } else {
                                return '';
                            }
                        } else {
                            return '';
                        }
                    },
                    unformat: function (value) {
                        if (value != '') {
                            if (value == '未开发票') {
                                return '0';
                            } else if (value == '部分开票') {
                                return '1';
                            } else if (value == '已开票') {
                                return '2';
                            }
                        } else {
                            return '';
                        }
                    }
                }, {
                    name: 'productCode'
                }, {
                    name: 'productName'
                }, {
                    name: 'productSpecification'
                }, {
                    name: 'productPackUnitSmall'
                }, {
                    name: 'productContainTaxPrice',
                    formatter: function (val) {
                        if(val||val === 0){
                            return parseFloat(val);
                        }else {
                            return '';
                        }

                    },
                    unformat: function (val) {
                        return val.replace(/,/g, '');
                    }
                }, {
                    name: 'productPackInStoreCount'
                },
                {
                    name: 'thisInvoicedCount',
                    formatter: function (val) {
                        if(val){
                            return parseFloat(val).toFixed(0);
                        }else {
                            return 0;
                        }
                    }
                },{
                    name: 'invoicedCount',
                    formatter: function (val) {
                        if(val){
                            return parseFloat(val).toFixed(0);
                        }else {
                            return 0;
                        }
                    }
                },{
                    name: 'procutNoTaxMoney',
                    formatter: function (val) {
                        return parseFloat(val).formatMoney('2', '', ',', '.');
                    },
                    unformat: function (val) {
                        return val.replace(/,/g, '');
                    }
                }, {
                    name: 'productTaxMoney',
                    formatter: function (val) {
                        return parseFloat(val).formatMoney('2', '', ',', '.');
                    },
                    unformat: function (val) {
                        return val.replace(/,/g, '');
                    }
                }, {
                    name: 'productContainTaxMoney',
                    formatter: function (val) {
                        return parseFloat(val).formatMoney('2', '', ',', '.');
                    },
                    unformat: function (val) {
                        return val.replace(/,/g, '');
                    }
                }],
            rowNum: 1000,
            rownumbers: true,//是否展示序号
            altRows: true, //设置为交替行表格,默认为false
            gridComplete: function () {
                var _this = this;
                setTimeout(function () {
                    var data = $('#X_Table1').XGrid('getRowData');
                    $('#X_Table1').XGrid('addRowData', {
                        id: "合计",
                        thisInvoicedCount: totalTableX(data, "thisInvoicedCount"),
                        invoicedCount: totalTableX(data, "invoicedCount"),
                        procutNoTaxMoney: totalTableY(data, "procutNoTaxMoney"),
                        productTaxMoney: totalTableY(data, "productTaxMoney"),
                        productContainTaxMoney: totalTableY(data, "productContainTaxMoney")
                    })
                    $('#X_Table1').find('tr:last-child').attr('id', '').find('td:first-child').html('合计');
                    $('#X_Table1').unbind('click');
                }, 200)
            }
            // pager: '#grid-pager',
        });
    }

    //已选择入库单
    $('#X_Tablea').XGrid(
        {
            data: storeInList,
            colNames: ['供应商', '供应商编码', '采购单据单号','业务类型', '单据日期',
                '制单人', '金额合计', '税额合计', '税率', '价税合计', 'id'],
            colModel: [{
                name: 'supplierName'

            }, {
                name: 'supplierCode',
                hidden: true
            }, {
                name: 'stockOrderNo'
            }, {
                name: 'channelName'
            }, {
                name: 'storeTime'
            }, {
                name: 'createUser'
            }, {
                name: 'sumNoTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }

            }, {
                name: 'sumTaxMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }

            }, {
                name: 'productEntryTax',
                formatter: function (val, a, b, c) {
                    console.log(val, a, b, c);
                    if (!val) return '';
                    var sumTaxMoney = b.sumTaxMoney;
                    var sumNoTaxMoney = b.sumNoTaxMoney;
                    if(!parseFloat(sumNoTaxMoney)) return val;
                    val = sumTaxMoney / sumNoTaxMoney;
                    val = (val * 100).toFixed(0) + '%';
                    return val;
                }
            }, {
                name: 'priceTaxSum',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'id',
                hidden: true

            }, {
                name: 'channelId',
                hidden: true,
                hidegrid: true
            }],
            rowNum: 1000,
            key: 'id',
            rownumbers: true,//是否展示序号
            altRows: true, //设置为交替行表格,默认为false
            ondblClickRow: function (id, dom, obj, index, event) {
                //双击事件回调函数
                var arr = [];
                $.each(storeInDetailList, function (index, item) {
                    if (item.stockOrderNo == obj.stockOrderNo) {
                        arr.push(item);
                    }
                })

                initDialogTable(arr);
                $("#input_goodName").val('');
                utils.dialog({
                    title: '已选采购单据明细行',
                    width: 1000,
                    content: $('#modal')
                }).showModal();

            },
            gridComplete: function () {
                setTimeout(function () {
                    totalStoreInSum();
                }, 200)
            },

        });

    //合计采购单据
    function totalStoreInSum() {

        addtotalRow($('#X_Tablea'), {
            id: "合计",
            sumNoTaxMoney: "sumNoTaxMoney",
            sumTaxMoney: "sumTaxMoney",
            priceTaxSum: "priceTaxSum"
        })
    }


    // 增加合计行
    function addtotalRow($X_Table, rowData) {

        var tem = [],
            data = $X_Table.XGrid('getRowData');

        $.each(data, function (idnex, item) {
            if (item.id) {
                tem.push(item)
            }
        })

        data = tem;

        $.each($X_Table.find('tr'), function (index, item) {
            if ($(item).find('td:first-child').html() === '合计') {
                $(item).remove();
            }
        })
        for (var key in rowData) {
            if (rowData[key] && (key !== 'id')) {
                rowData[key] = totalTableY(data, key);
            }
        }
        $X_Table.XGrid('addRowData', rowData);
        $X_Table.find('tr:last-child').attr('id', '').find('td:first-child').html('合计');
    }

    //合计采购单据明细
    function totalTableY(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            if(item[colName]) {
                count += parseFloat(item[colName]);
            }
        })
        return count.toFixed(2);
    }
    function totalTableX(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            if(item[colName]) {
                count += parseFloat(item[colName]);
            }
        })
        return count.toFixed(0);
    }

    //发票明细
    $('#Y_Tablea').XGrid({
        data: [],
        colNames: ['序号', '<span class="danger">*</span> 供应商发票号',
            '<span class="danger">*</span> 发票日期', '<span class="danger">*</span> 发票不含税金额',
            '<span class="danger">*</span> 发票含税金额', '发票税额', '税率', '备注'
        ],
        colModel: [{
            name: 'id'
        }, {
            name: 'supplierInvoiceNumber',
            rowtype: '#number_black'
        }, {
            name: 'invoiceDate',
            rowtype: '#bill_black'
        }, {
            name: 'noInvoiceTaxAmount',
            rowtype: '#money1_black',
            rowEvent: rowEvent
        }, {
            name: 'invoiceTaxAmount',
            rowtype: '#money_black',
            rowEvent: rowEvent
        }, {
            name: 'tax'
        }, {
            name: 'taxRate'
        }, {
            name: 'rates',
            rowtype: '#text_black'
        }],
        key: 'id',
        rowNum: 1000,
        // rownumbers: true,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {
            //双击事件回调函数
            if (id == '999') return false;
            //$('#Y_Tablea').XGrid('editRow', id);

        },
        gridComplete: function () {
            setTimeout(function () {
                totalTable();
                $("#addRow").trigger('click');
            }, 200);

            hotKey({
                tableId: 'Y_Tablea',
                keyFn: {
                    supplierInvoiceNumber: 'supplierInvoiceNumber',
                    invoiceDate: 'invoiceDate',
                    noInvoiceTaxAmount: 'noInvoiceTaxAmount',
                    invoiceTaxAmount:'invoiceTaxAmount',
                    rates: 'rates'
                },
                endFn:function (tr,enterFocus) {
                    if(validform("formTable").form()){
                        $("#addRow").trigger('click');
                        enterFocus()
                    }
                }
            });

            function hotKey(option) {
                var $table = $('#' + option.tableId),
                    keyFn = option.keyFn,
                    keyAry = Object.keys(keyFn),
                    colInput = '';
                colInput = $.map(keyAry,function(item){
                    return 'td[row-describedby='+item+'] input'
                }).join(',');
                setTimeout(function () {
                    $table.delegate(colInput,{
                        keyup:function (e) {
                            if(e.keyCode === 13){
                                var $ele = $(e.target),
                                    colName = $ele.parents('td').attr('row-describedby'),
                                    rowId = $ele.parents('tr').attr('id');
                                if($.isFunction(keyFn[colName])){
                                    keyFn[colName].call(this,rowId,colName,enterFocus)
                                }else {
                                    enterFocus(rowId,colName)
                                }
                            }else if(e.keyCode === 37){
                                //左
                            }else if(e.keyCode === 39){
                                //右
                            }else if(e.keyCode === 38){
                                //上
                            }else if(e.keyCode === 40){
                                //下
                            }
                        }
                    })
                });
                function enterFocus(rowId,oldCol) {
                    var col = keyAry[keyAry.indexOf(oldCol)+1],$tr = $table.find('tr#'+rowId);
                    if(col){
                        $tr.find('td[row-describedby='+col+'] input').focus();
                    }else {
                        //需判断是需要切换下一行还是执行结束回调
                        if($tr.next().length > 0 && $tr.next().attr('id') != '999'){
                            $tr.next().find('td[row-describedby='+keyAry[0]+'] input').focus();
                        }else {
                            option.endFn($tr[0],function () {
                                setTimeout(function () {
                                    enterFocus(rowId,oldCol)
                                })
                            })
                        }
                    }
                }
            }
        }

    });

    // 行内计算发票税额和税率
    function rowEvent(etype) {
        var $target = $(etype.e.target);
        var tax, taxRate, data = etype.rowData;
        //发票含税金额
        var invoiceTaxAmount = parseFloat($.trim(data.invoiceTaxAmount).replace(/,/g, ''));
        //发票不含税金额
        var noInvoiceTaxAmount = parseFloat($.trim(data.noInvoiceTaxAmount.replace(/,/g, '')));
        //税额
        tax = invoiceTaxAmount - noInvoiceTaxAmount;
        //税率 = 税额/发票不含税金额
        taxRate = Math.abs(tax / noInvoiceTaxAmount);
        taxRate = (taxRate * 100).toFixed(0) + '%';
        tax = tax.toFixed(2);
        if (tax == 'NaN') {
            tax = '';
        }
        if (taxRate.includes('-') || taxRate.includes('NaN') || taxRate.includes('Infinity')) {
            taxRate = '';
        }
        $target.closest('tr').find('td[row-describedby="tax"]').html(tax);
        $target.closest('tr').find('td[row-describedby="taxRate"]').html(taxRate);
        totalTable();
    }

    //合计
    function totalTable() {
        var data = $('#Y_Tablea').XGrid('getRowData');
        data.pop();
        var obj = {
            id: '999',
            noInvoiceTaxAmount: totalSum(data, 'noInvoiceTaxAmount'),
            invoiceTaxAmount: totalSum(data, 'invoiceTaxAmount'),
            tax: totalSum(data, 'tax')
        };

        $('#Y_Tablea').XGrid('addRowData', obj);
        //$('#Y_Tablea tr:last-child').attr('id','');
        $('#Y_Tablea tr:last-child td:first-child').html('合计');
        $('#Y_Tablea tr:last-child').find('td:eq(1)').html('');
        $('#Y_Tablea tr:last-child').find('td:eq(2)').html('');
        $('#Y_Tablea tr:last-child').find('td:eq(3)').html(obj.noInvoiceTaxAmount);
        $('#Y_Tablea tr:last-child').find('td:eq(4)').html(obj.invoiceTaxAmount);
        $('#Y_Tablea tr:last-child').find('td:last-child').html('');

        //获取发票合计金额
        var totalInvoiceData = $('#Y_Tablea').XGrid('getRowData').pop();
        var totalStoreInData = $('#X_Tablea').XGrid('getRowData').pop();

        ///赋值
        //发票税额合计(发票汇总税额)
        $("#totalInvoiceValue").val(parseFloat(totalInvoiceData.tax).formatMoney('2', '', ',', '.'));
        //发票价税合计(发票汇总含税总额)
        $("#totalInvoiceTax").val(parseFloat(totalInvoiceData.invoiceTaxAmount).formatMoney('2', '', ',', '.'));
        //发票不含税金额
        $("#noTotalTaxAmount").val(parseFloat(totalInvoiceData.noInvoiceTaxAmount).formatMoney('2', '', ',', '.'));
        //货值差 发票不含税金额合计-入库单金额合计
        var valueDifference = totalInvoiceData.noInvoiceTaxAmount - totalStoreInData.sumNoTaxMoney;
        $("#valueDifference").val(parseFloat(valueDifference).formatMoney('2', '', ',', '.'));
        //货值差 发票含税金额合计-入库单金额合计
        var taxAmountDiff = totalInvoiceData.invoiceTaxAmount - totalStoreInData.priceTaxSum;
        $("#taxAmountDiff").val(parseFloat(taxAmountDiff).formatMoney('2', '', ',', '.'));
    }

    //发票明细合计
    function totalSum(data, colName) {
        if ($('#Y_Tablea tr:last-child td:first-child').html() == '合计') {
            $('#Y_Tablea tr:last-child').remove();
        }
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat($.trim(item[colName]).replace(/,/g, ''));
        })
        return Number.isNaN(count) ? '0.00' : count.toFixed(2);
    }


    //删除选中行
    $('#deleRow').click(function (param) {

        var selRow = $('#Y_Tablea').XGrid('getSeleRow');
        if (selRow) {
            //删除二次确认
            utils.dialog({
                title: "提示",
                content: "确认删除当前选中行？",
                okValue: '确定',
                ok: function () {
                    $('#Y_Tablea').XGrid('delRowData', selRow.id);
                    refreshIndex($('#Y_Tablea'));
                    totalTable();
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        } else {
            utils.dialog({
                content: '没有选中任何行！',
                quickClose: true,
                timeout: 2000
            }).showModal();
        }
    })

    //新增一行
    $('#addRow').on('click', function () {

        id = $('#Y_Tablea tr').length - 1;
        if (id > 200) {
            utils.dialog({content: '最多可添加200行!', quickClose: true, timeout: 2000}).show();
            return false;
        }

        $('#Y_Tablea').XGrid('addRowData', {
            id: id
        }, 'before', '999');

        $('#Y_Tablea').XGrid('editRow', id);

    })


    //返回
    $('#backInvoice').bind('click', function () {

        if($("#exportBatchNo").val()){
           if($("#invoiceNo").val()){
               parent.financePurchaseInvoiceToAdd = null;
               window.location.href = "/proxy-finance/finance/purchase/invoice/toEdit?invoiceNo="+$('#invoiceNo').val();
           }else{
               window.location.href = "/proxy-finance/finance/purchase/invoice/toImportStoreinDetail";
           }
        }else{
            if($("#invoiceNo").val()){
                parent.financePurchaseInvoiceToAdd = null;
            }
            window.location.href = "/proxy-finance/finance/purchase/invoice/toAdd";
        }
        //弹框
       /* utils.dialog({
            title: '提示',
            content: '确定返回？',
            width: 200,
            okValue: '确定',
            cancelValue: '取消',
            ok: function () {
                if($("#invoiceNo").val()){
                    parent.financePurchaseInvoiceToAdd = null;
                }
                window.location.href = "/proxy-finance/finance/purchase/invoice/toAdd";
            },
            cancel: true
        }).showModal();*/
    })

    //更新序号
    function refreshIndex($Table) {
        var rn = $Table.find('td[row-describedby="id"]');
        $.each(rn, function (index, item) {
            if ($(item).html() !== '合计') {
                $(item).html(index + 1);
            }

        })
    }

    //保存
    $('#saveInvoice').bind('click', function () {
        if (validform("formTable").form() && validform("invoiceForm").form()) {
            //发票明细校验
            var flag = true;
            if ($('#Y_Tablea tr').length == 2) {
                utils.dialog({content: '无发票明细,请添加', quickClose: true, timeout: 2000}).show();
                flag = false;
            }
            if (!flag) return false;
            //汇总不含税金额差额，校验 小于10，小于发票不含税金额的1%
            var taxMoney = parseFloat($('#totalInvoiceTax').val().replace(/,/g, ''));
            var totalStoreInData = $('#X_Tablea').XGrid('getRowData').pop();
            var storeInMoney =totalStoreInData.priceTaxSum;
           var  difference = taxMoney -storeInMoney;
            difference = difference.toFixed(2);
            var taxAmountDiff =  parseFloat($('#taxAmountDiff').val().replace(/,/g, ''));
            //汇总不含税金额差额，校验 小于10，小于发票不含税金额的1%
            var valueDifference = parseFloat($('#valueDifference').val().replace(/,/g, ''));
            if (valueDifference == "0.00" && taxAmountDiff== 0.00) {
                var invoiceNo = $("#invoiceNo").val();
                var invoiceStatus = 1;
                saveInvoice(invoiceNo, invoiceStatus);
            }else{
                 utils.dialog({
                    title: '发票金额校验',
                    content: '发票明细含税金额合计与已选采购单据含税金额存在差额'+difference+'，请确认是否保存？',
                    width: 330,
                    height: 100,
                    okValue: '确定',
                    cancelValue: '取消',
                    ok: function () {
                        var invoiceNo = $("#invoiceNo").val();
                        var invoiceStatus = 1;
                        saveInvoice(invoiceNo, invoiceStatus);
                    },
                    cancel: true
                }).showModal();
            }

        }

    })

    //过账，过账时校验汇总不含税金额差额
    $('#outInvoice').bind('click', function () {
        //汇总不含税金额差额，校验 小于10，小于发票不含税金额的1%
        var valueDifference = parseFloat($('#valueDifference').val().replace(/,/g, ''));
        var noTotalTaxAmount = parseFloat($("#noTotalTaxAmount").val().replace(/,/g, ''));
        var bills = valueDifference / noTotalTaxAmount;
        var taxAmountDiff =  parseFloat($('#taxAmountDiff').val().replace(/,/g, ''));
        //校验
        if (validform("formTable").form() && validform("invoiceForm").form()) {
            //发票明细校验
            var flag = true;
            if ($('#Y_Tablea tr').length == 2) {
                utils.dialog({content: '无发票明细,请添加', quickClose: true, timeout: 2000}).show();
                flag = false;
            }
            if (!flag) return false;

            if (Math.abs(valueDifference) == 0 && Math.abs(taxAmountDiff) == 0) {
                var invoiceNo = $("#invoiceNo").val();
                var invoiceStatus = 2;
                saveInvoice(invoiceNo, invoiceStatus);
            } else {
                utils.dialog({
                    title: '提示',
                    content: '含税金额差额'+taxAmountDiff+'不含税金额差额'+valueDifference+'，请确认是否继续？',
                    width: 200,
                    okValue: '确定',
                    cancelValue: '取消',
                    ok: function () {
                        var invoiceNo = $("#invoiceNo").val();
                        var invoiceStatus = 2;
                        $.ajax({
                            url: '/proxy-finance/finance/purchase/invoice/toValidatePassMoney',
                            type: 'post',
                            data:{valueDifference: taxAmountDiff},
                            success: function (result) {
                                console.log(result);
                                if(result.code ==0){
                                    saveInvoice(invoiceNo, invoiceStatus);
                                }else{
                                    utils.dialog({content: '含税金额差异超过限定范围['+result.result.minDiffMoney+','+result.result.maxDiffMoney+']!', quickClose: true, timeout: 2000}).show();
                                    return false;
                                }
                            },
                            error: function (error) {
                                console.log(error)
                            }
                        });

                    },
                    cancel: true
                }).showModal();
                // utils.dialog({content: '过账差额超过范围，请使用财务高级权限用户进行“超级过账”', quickClose: true, timeout: 2000}).show();
                return false;
            }
        }
        return false;

    });
    //超级过账，不校验，直接过账
    $('#superOutInvoice').bind('click', function () {
        if (validform("formTable").form() && validform("invoiceForm").form()) {
            //发票明细校验
            var flag = true;
            if ($('#Y_Tablea tr').length == 2) {
                utils.dialog({content: '无发票明细,请添加', quickClose: true, timeout: 2000}).show();
                flag = false;
            }
            if (!flag) return false;

            var invoiceNo = $("#invoiceNo").val();
            var invoiceStatus = 2;
            saveInvoice(invoiceNo, invoiceStatus);
        }
    });

    //作废
    $('#feiInvoice').bind('click', function () {
        //校验
        if (validform("formTable").form() && validform("invoiceForm").form()) {
            //发票明细校验
            var flag = true;
            if ($('#Y_Tablea tr').length == 2) {
                utils.dialog({content: '无发票明细,请添加', quickClose: true, timeout: 2000}).show();
                flag = false;
            }
            if (!flag) return false;

            var invoiceNo = $("#invoiceNo").val();
            var invoiceStatus = 3;
            saveInvoice(invoiceNo, invoiceStatus);
        }
        return false;

    });

    function saveInvoice(invoiceNo, invoiceStatus) {
    	$('#saveInvoice').attr('disabled','');
    	$('#outInvoice').attr('disabled','');
    	$('#superOutInvoice').attr('disabled','');
        //发票明细表单数据
        var invoiceDetailFormdata = $('#Y_Tablea').XGrid('getRowData');
        //如果表格下边有合计行
        invoiceDetailFormdata.pop();
        $.each(invoiceDetailFormdata, function (index, item) {
            delete item.id;
            item.taxRate = item.taxRate.split('%').join('');
            //处理特殊字符
            item.rates = transEntity(item.rates, false);
        })
        console.log("入库单列表", storeInList);
        console.log("入库单详情", storeInDetailList);

        if (storeInList.length == 0 ) {
            utils.dialog({content: '请重新选择入库单！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if(storeInDetailList.length ==0){
            utils.dialog({content: '请重新选择入库单！', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        //校验供应商发票编码
        $.ajax({
            url: '/proxy-finance/finance/purchase/invoice/toValidateInvoice',
            type: 'post',
            data: {invoiceDetail: JSON.stringify(invoiceDetailFormdata),
                   invoiceNo: invoiceNo
            },
            success: function (result) {
                console.log(result);
                if(result.code ==0){
                    validatedSave(invoiceNo, invoiceStatus);
                }else{
                    utils.dialog({
                        title: '供应商发票号校验',
                        content: '供应商发票号'+result.result.supplierInvoiceNo+'重复，是否继续？',
                        width: '300',
                        okValue: '确认',
                        ok: function () {
                            validatedSave(invoiceNo, invoiceStatus);
                        },
                        cancelValue: '取消',
                        cancel: function () {
                            $('#saveInvoice').removeAttr('disabled');
                            $('#outInvoice').removeAttr('disabled','');
                            $('#superOutInvoice').removeAttr('disabled','');
                        }
                    }).show();
                }
            },
            error: function (error) {
                console.log(error)
            }
        });


    }

    function validatedSave(invoiceNo, invoiceStatus){

        //发票表单数据
        var invoiceFormdata = $('#invoiceForm').serializeToJSON();
        //发票税额合计(发票汇总税额)
        invoiceFormdata.totalInvoiceValue = invoiceFormdata.totalInvoiceValue.replace(/,/g, '');
        //发票价税合计(发票汇总含税总额)
        invoiceFormdata.totalInvoiceTax = invoiceFormdata.totalInvoiceTax.replace(/,/g, '');
        //不含税金额差
        invoiceFormdata.valueDifference = invoiceFormdata.valueDifference.replace(/,/g, '');
        //发票不含税金额
        invoiceFormdata.noTotalTaxAmount = invoiceFormdata.noTotalTaxAmount.replace(/,/g, '');
        invoiceFormdata.taxAmountDiff = invoiceFormdata.taxAmountDiff.replace(/,/g, '');


        //发票明细表单数据
        var invoiceDetailFormdata = $('#Y_Tablea').XGrid('getRowData');
        //如果表格下边有合计行
        invoiceDetailFormdata.pop();

        $.each(storeInList, function (index, item) {
            delete item.id;
        });
        $.each(storeInDetailList, function (index, item) {
            delete item.id;
        });
        $.each(invoiceDetailFormdata, function (index, item) {
            delete item.id;
            item.taxRate = item.taxRate.split('%').join('');
            //处理特殊字符
            item.rates = transEntity(item.rates, false);
        })

        //处理特殊字符
        invoiceFormdata.certificateRate = transEntity(invoiceFormdata.certificateRate, false);

        var taxAmountDiff =  parseFloat($('#taxAmountDiff').val().replace(/,/g, ''));
        var finalData = {
            storeInList: JSON.stringify(storeInList),
            storeInDetailList: JSON.stringify(storeInDetailList),
            invoiceInfo: JSON.stringify(invoiceFormdata),
            invoiceDetail: JSON.stringify(invoiceDetailFormdata),
            invoiceNo: invoiceNo,
            invoiceStatus: invoiceStatus
        }
        if(invoiceStatus === 2 && Math.abs(taxAmountDiff) !== 0){
            showTaxAmountDiffModal(finalData,taxAmountDiff)
        }else {
            finalData.boolAdjustInvoice = false
            finalSave(finalData)
        }
    }

    function showTaxAmountDiffModal(finalData,taxAmountDiff){
            utils.dialog({
                title: '提示',
                content: '采购发票存在含税金额差额'+taxAmountDiff+'，发票调整单金额'+(-1 * taxAmountDiff)+'，是否生成发票调整单？',
                width: 380,
                cancel: false,
                button:[
                    {
                        id:'no',
                        value:'否',
                        callback:function(){
                            finalData.boolAdjustInvoice = false
                            finalSave(finalData)
                        }
                    },
                    {
                        id:'yes',
                        value:'是',
                        callback:function(){
                            finalData.boolAdjustInvoice = true
                            finalSave(finalData)
                        }
                    }
                ]
            }).showModal();
    }
    // 最终保存、过账、超级过账、作废
    function finalSave(data){
        //保存
        $.ajax({
            url: '/proxy-finance/finance/purchase/invoice/tosaveAndPassInvoice',
            type: 'post',
            data: data,
            success: function (result) {
                utils.dialog({
                    content: result.msg, quickClose: true,
                    timeout: 3000
                }).showModal();
                if (result.result.invoiceNo != null) {
                    $("#invoiceNo").val(result.result.invoiceNo);
                    $("#commitUser").val(result.result.commitUser);

                }
            },
            error: function (error) {
                console.log(error)
            }
        }).done(function(msg){
            $('#saveInvoice').removeAttr('disabled');
            $('#outInvoice').removeAttr('disabled');
            $('#superOutInvoice').removeAttr('disabled');
        })
    }


    function batchSearch (id) {
        var idName = id+'_batchSearch',$ele = $('#'+id),eleBox;
        if($("#"+idName).length>0){
            eleBox = $("#"+idName);
        }else {
            $('body').append('<div class="autocomplete-suggestions" id="'+idName+'"></div>');
            eleBox = $("#"+idName);
            eleBox.css({
                'position': 'absolute',
                'max-height': '300px',
                'z-index': '9999',
                'box-sizing':'border-box',
                'display': 'block',
                'fout-size': '16px'
            })
        }
        $ele.on({
            input:function () {
                fixPosition();
                var val = $(this).val(),
                    valAry = val.split(';'),
                    eleList = '',
                    seleLength = $(this)[0].selectionStart;
                //console.log(seleLength);
                $.each(valAry,function (index,item) {
                    if(item){
                        var bg = '#fff';
                        if(seleLength === +seleLength){
                            if(seleLength > 0){
                                seleLength = seleLength - item.length - 1;
                            }
                            if(seleLength <= 0){
                                bg = '#66AFE9';
                                seleLength = false;
                            }
                        }
                        eleList += '<div class="autocomplete-suggestion" data-index="'+index+'" style="padding-right: 22px;background:'+bg+'">'+item+'<span class="cancel" style="float: right;margin-right: -10px;cursor: pointer;" data-value="'+item+'">X</span></span></div>'
                    }
                });
                eleList ? eleBox.html(eleList).show() : eleBox.hide();
            },
            focus:function () {
                $(this).trigger('input')
            },
            click:function () {
                $(this).trigger('input')
            },
            keyup:function (e) {
                //console.log(e.keyCode);
                if(e.keyCode>=37 && e.keyCode<=40){
                    $(this).trigger('input')
                }
            },
            blur:function () {
                window[idName] = setTimeout(function () {
                    eleBox.hide();
                },200)
            }
        });
        eleBox.on('click',function (e) {
            window[idName] && clearTimeout(window[idName]);
            var $this = $(e.target);
            if($this.hasClass('cancel')){
                var valAry = $ele.val().split(';');
                valAry.splice(eleBox.find('.cancel').index($this),1);
                $ele.val(valAry.join(';')).trigger('input');
            }
        });
        function fixPosition() {
            eleBox.css({
                minWidth: $ele.outerWidth(),
                top: $ele.outerHeight() + $ele.offset().top + 'px',
                left: $ele.offset().left+ 'px',
            });
        }
    }
    batchSearch('input_goodName');

    $('#l_product_btn').bind('click', function () {
        var valAry = $("#input_goodName").val().split(';');
        var seleRow =$("#X_Table1").XGrid('getRowData');

        $.each(seleRow ,function(idx,it){
            if(it.id){
                $("#X_Table1").find("tr#"+it.id).removeClass('selRow');
                $.each(valAry,function (index,item) {
                    if(it.productCode == item){
                        $("#X_Table1").find("tr#"+it.id).addClass('selRow');
                    }
                });
            }
        })

    })


})
