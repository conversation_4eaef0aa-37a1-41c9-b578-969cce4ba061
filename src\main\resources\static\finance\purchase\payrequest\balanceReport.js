$(function () {
    /* 日期初始化 */
    z_utils.initDate('beginTime', 'endTime');

    /* 合计计算  */
    var totalTable = z_utils.totalTable;

    /*查询页面总计*/
    getTotalNum ();

    //关键字模糊查询
    $('#supplierName').Autocomplete({
        serviceUrl: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierVo?flag=1', //异步请求
        paramName: 'name',//查询参数，默认 query
        dataType: 'json',
        zIndex: 9,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // querydelimiter: '-', //查询参数分隔符
        // delimiter: ',', //多选时输入逗号触发匹配操作
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        autoSelectFirst: true,
        onSelect: function (result) {
            console.log(result);
            $("#supplierNo").val(result.data);
            $("#supplierName").val(result.value).attr('oldvalue',result.value);
        },
        dataReader: {
            list: "result", //结果集，不写返回结果为数组
            data: 'supplierCode',
            value: 'supplierName'
        },
        onNoneSelect: function (params, suggestions) {
            $("#supplierNo").val("");
            $("#supplierName").val("");

        },
        onSearchComplete: function (query, suggestions) {
            var $ele = $(this);
            if(suggestions && suggestions.length === 0){
                $ele.attr('oldvalue','');
            }else {
                $ele.attr('oldvalue',$ele.val())
            }
            if(!$ele.is(":focus")){
                $ele.Autocomplete('hide');
            }
        }
    });

    $('#supplierName').on({
        dblclick: function (e) {
            supplierdDalog($("#supplierName").attr('oldvalue'))
        },
        keyup: function (e) {
            if(e.keyCode === 13 && !$("#supplierName").attr('oldvalue')){
                supplierdDalog()
            }
        }
    }).siblings('.glyphicon-search').on("click", function () {
        supplierdDalog($("#supplierName").attr('oldvalue'))
    });
//供应商列表弹窗
    function supplierdDalog(val) {
        utils.dialog({
            title: '供应商列表',
            url: '/proxy-finance/finance/purchase/payrequestinfo/toSupplierList?flag=1',
            width: $(window).width() * 0.8,
            height: 600,
            data: val ? val : '', // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    $('#supplierName').removeAttr('save');
                    var data = this.returnValue;
                    console.log(data);
                    $("#supplierName").val(data.supplierName);
                    $("#supplierNo").val(data.supplierCode);
                }else{
                    $("#keyWord").val('');
                    $("#supplierName").val('');
                }
            }
        }).showModal();
    }

    var colNames = ['供应商编码', '供应商名称', '采购员', '期初金额','采购金额','付款金额','应付余额'],
        colModel = [
            {
                name: 'supplierNo',
                index: 'supplierNo',
                width: 100,//宽度
            }, {
                name: 'supplierName',
                index: 'supplierName',
                width: 200
            }, {
                name: 'buyerName',
                index: 'buyerName',
                width: 80
            }, {
                name: 'initialAmount',
                index: 'initialAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                },
                width: 100,
            },
            {
                name: 'creditAmount',
                index: 'creditAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                },
                width: 100,
            }, {
                name: 'debteAmount',
                index: 'debteAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                },
                width: 100
            }, {
                name: 'balance',
                index: 'balance',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                },
                width: 100
            }
        ];
    var allColModelA = JSON.parse(JSON.stringify(colModel));

    //设置table高度
    //utils.setTableHeight('X_Table');
    $('#X_Table').XGrid({
        //data: grid_data,
        url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierBalanceReport',
        // url: 'http://localhost:8080/account/find',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        rownumbers: true,
        altRows: true,//设置为交替行表格,默认为false
        attachRow:true,
        postData: {
            supplierNo: $("#supplierNo").val(),
            supplierName:$("#supplierName").val(),
            startDate: $("#beginTime").val(),
            endDate: $("#endTime").val(),
            isBalane: $("#isBalance:checked").val()
        },
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', e, c, a, b);
            this.returnValue = obj;
            // console.log(obj)
            //window.location.href = "/proxy-finance/finance/purchase/payrequestinfo/toDetail?payReqNO=" + obj.billNo;
            return obj;
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        gridComplete: function () {
            /* 合计行 */
            var data = $(this).XGrid('getRowData');
            var sum_models = ['initialAmount','creditAmount','debteAmount','balance'];
            var lastRowEle = $(this).find("tr[nosele=true]");
            lastRowEle.find("td:first-child").text('合计');
            sum_models.forEach(function (item,index) {
                lastRowEle.find("td[row-describedby="+item+"]").text(totalTable(data,item))
            });
            var _this = $(this);
            if(!_this.XGrid('getRowData').length){
                utils.dialog({content:'查询无数据',quickClose:true,timeout:2000}).show();
            }
        },
        pager: '#grid-pager'
    });

    $("#searchBtn").on("click", function () {
        console.log($("#supplierNo").val());
        console.log($("#supplierName").val());
        if ($('#beginTime').val() == '') {
            utils.dialog({content: '请选择开始日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        if ($('#endTime').val() == '') {
            utils.dialog({content: '请选择结束日期！', quickClose: true, timeout: 2000}).show();
            return false;
        }
        $('#X_Table').XGrid('setGridParam', {
            url: '/proxy-finance/finance/purchase/payrequestinfo/findSupplierBalanceReport',
            postData: {
                supplierNo: $("#supplierNo").val(),
                supplierName:$("#supplierName").val(),
                keyWord : $("#keyWord").val(),
                startDate: $("#beginTime").val(),
                endDate: $("#endTime").val(),
                isBalane: $("#isBalance:checked").val(),
                buyer : $("#buyer").val(),
                caiGouYuanName : $("#buyer option:checked").text(),
                page: 1
            }
        }).trigger('reloadGrid');
        getTotalNum();
    });

    //设置显示列
    $("#setBtn").on("click", function () {
        $('#X_Table').XGrid('filterTableHead');
    });


    //导出
    $('#exportBtn').on('click', function () {
        utils.exportAstrictHandle('X_Table', Number($('#totalPageNum').text()),1).then( () => {
            return false;
        }).catch( () => {
            //原始处理逻辑代码
            if ($('#beginTime').val() == '') {
            utils.dialog({content: '请选择开始日期！', quickClose: true, timeout: 2000}).show();
            return false;
            }
            if ($('#endTime').val() == '') {
                utils.dialog({content: '请选择结束日期！', quickClose: true, timeout: 2000}).show();
                return false;
            }

    //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = false;
            // copy this parameter and the below buttons
            var nameModel = "";
            addHtmlA(colNames);
            dialog({
                content: $("#setCol"),
                title: '筛选列',
                width: $(window).width() * 0.4,
                data: 'val值',
                cancelValue: '取消',
                cancel: true,
                okValue: '导出',
                ok: function () {
                    var newColName = [], newColModel = [];
                    $(this.node).find('#checkRow input[type="checkbox"]').each(function (index) {
                        if ($(this).is(":checked")) {
                            nameModel += allColModelA[index].name + ":" + $(this).attr('name') + ","
                        }
                    });
                    if(nameModel.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    // var keyword = $("#keyword").val();
                    // var createTimeStart = $("#createTimeStart").val();
                    // var createTimeEnd = $("#createTimeEnd").val();
                    // var obj = $("#searchForm").serializeToJSON();
                    console.log($("#supplierNo").val());
                    console.log($("#supplierName").val());
                    var obj = {
                        supplierNo: $("#supplierNo").val(),
                        supplierName:$("#supplierName").val(),
                        billNo : $("#billNo").val(),
                        startDate: $("#beginTime").val(),
                        endDate: $("#endTime").val(),
                        isBalane: $("#isBalance:checked").val(),
                        buyer : $("#buyer").val(),
                        caiGouYuanName : $("#buyer option:checked").text()
                    }
                    // obj["pageNum"] = "1";
                    // obj["pageSize"] = "1000000";
                    obj["nameModel"] = nameModel;
                    httpPost("/proxy-finance/finance/purchase/payrequestinfo/exportSupplierBalanceReport", obj);
                },
    // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $("#checkRow input").prop("checked",false);
                                ck = true;
                            }else if(ck){
                                $("#checkRow input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            }).showModal();
        })
    });

    function addHtmlA(arry) {
        if (!$('#setCol')[0]) {
            var s = '<div id="setCol" style="display: none; padding-left: 2.4rem;">' +
                '    <div class="row" id="checkRow">';

            for (var i = 0; i < arry.length; i++) {

                s += '<div class="col-md-3">' +
                    '            <div class="checkbox">' +
                    '                <label>' +
                    '                    <input style="margin-right: 10px" checked type="checkbox" name="' + arry[i] + '">' + arry[i] +
                    '                </label>' +
                    '            </div>' +
                    '        </div>';

            }

            s += '</div></div>';
            $("body").append(s);
        }

    }

    //发送POST请求跳转到指定页面
    function httpPost(URL, PARAMS) {
        var temp = document.createElement("form");
        temp.action = URL;
        temp.method = "post";
        temp.style.display = "none";

        for (var x in PARAMS) {
            var opt = document.createElement("textarea");
            opt.name = x;
            opt.value = PARAMS[x];
            temp.appendChild(opt);
        }
        document.body.appendChild(temp);
        temp.submit();

        return temp;
    }
})
//查询总合计数目
function getTotalNum () {
    var formData = $("#form_a").serializeToJSON();
    var exceptionTypeArray = formData.exceptionType;
    var exceptionTypeStr = "";
    if (exceptionTypeArray != undefined && exceptionTypeArray != '') {
        for (var i = 0; i < exceptionTypeArray.length; i++) {
            exceptionTypeStr = exceptionTypeStr + exceptionTypeArray[i] + ",";
        }
    }

    formData.exceptionType = exceptionTypeStr;
    if(!formData.supplierName){
        formData.supplierNo = ''
    }
    //加载总数量
    $.ajax({
        url: '/proxy-finance/finance/purchase/payrequestinfo/selectSupplierBalanceStatisticsTotal',
        dataType: 'json',
        timeout: 80000, //6000
        data:formData,
        success: function (data) {
            // alert(data.code);
            if (data.code==0){
                var static = data.result;
                $("#initialAmountSum").text(Number(static.initialAmountSum).toFixed(2));
                $("#creditAmountSum").text(Number(static.creditAmountSum).toFixed(2));
                $("#debteAmountSum").text(Number(static.debteAmountSum).toFixed(2));
                $("#balanceSum").text(Number(static.balanceSum).toFixed(2));
            }
        },
        error: function () {
            $("#initialAmountSum").text('0.00');
            $("#creditAmountSum").text("0.00");
            $("#debteAmountSum").text("0.00");
            $("#balanceSum").text("0.00");
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });
}