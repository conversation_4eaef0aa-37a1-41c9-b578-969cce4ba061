
  var getData;
  var billNo = '';

$(function () {
   getData = function (printType,outOrderCode) {
     // var ts = "CGD1812006000";

      billNo = outOrderCode;
        //动态设置页面高度
      if(!billNo){
            billNo = "CGD1812007001";
            return false;
      };
    $('.content').css('height',$(window).height());




    $.ajax({
        method: "POST",
        url: "/proxy-finance/finance/purchase/payrequestinfo/getPayrequestInfo",
        data: {
            "billNo" :  billNo,
        },
        dataType: 'json',
        cache: false,
    }) .done(function(data ) {
        data.result.createDate = moment(data.result.createDateFormat);
        data.result.expectPayTime = moment(data.result.expectPayTime).format('YYYY-MM-DD');

        $('#myform').JSONToform(data.result);
        $('#basicInfo').JSONToform(data.result);
        $("#remarks").text(data.result.remarks);
        var applayAmount = data.result.applayAmount;
            applayAmount = (parseFloat(applayAmount)).toFixed(2);
             $('#applayAmount').html(applayAmount);
        var prepaymentAmountDeduction = data.result.prepaymentAmountDeduction;
        prepaymentAmountDeduction = (parseFloat(prepaymentAmountDeduction)).toFixed(2);
        $('#prepaymentAmountDeduction').html(prepaymentAmountDeduction);

        var actualPaymentAmount = data.result.actualPaymentAmount;
        actualPaymentAmount = (parseFloat(actualPaymentAmount)).toFixed(2);
        $('#actualPaymentAmount').html(actualPaymentAmount);
       // $('#applayAmount').val(data.result.applayAmount?data.result.applayAmount.formatMoney('2', '', ',', '.') : '0.00');
        $('#payableBalance').html(data.result.payableBalance?data.result.payableBalance.formatMoney('2', '', ',', '.') : '0.00');
        console.log(data.result)
        $('#ticketDeduction').html(data.result.ticketDeduction?data.result.ticketDeduction.formatMoney('2', '', ',', '.') : '0.00');
        $('#otherDeductions').html(data.result.otherDeductions?data.result.otherDeductions.formatMoney('2', '', ',', '.') : '0.00');

        if(data.result.approvalProcessId){
            loadFolow(data.result.approvalProcessId);
        }

        console.log(data);
        console.log("approvalProcessId"+data.result.approvalProcessId);
      //  loadFolow(data.result.approvalProcessId);
        if(data.result.isPrepay == 'C02'){
            //应付
            $('.nav-tabs>li:eq(0)').removeClass('active');
            $('.nav-tabs>li:eq(1)').addClass('active');
            $('.nav-content>.panel-body:eq(0)').hide();
            $('.nav-content>.panel-body:eq(1)').show();
            loadInvoiceTab(data.result.billNo);
        }else {
            loadOrderTab(data.result.billNo);
        }

    });


    window.isFirst = true;
    /**
     * 加载订单数据开始
     * am billNo
     */
    function loadOrderTab(billNo){
        var colNames = ['隱藏列','单据类型', '单据编号','业务类型', '供应商编号', '供应商名称', '送货方式', '是否预付', '送货人', '送货人电话', '供应商仓库地址',
            '预计到货时间', '金额合计',
            '税额合计', '税价合计','本次申请金额','累计申请金额'
        ];
        var colModel = [
            {
                name: 'id',
                hidden:true
            },
            {
                name: 'billType',
                index: 'billType'
            },
            {
                name: 'billNo',
                index: 'billNo'
            }, {
                name: 'channelName'
            },
            {
                name: 'supplierNo',
                index: 'supplierNo'
            },
            {
                name: 'supplierName',
                index: 'supplierName'
            },
            {
                name: 'deliveryMethod',
                index: 'deliveryMethod',
                formatter:function (val) {
                    if (val =="0"){
                        return "厢式送货";
                    }else if(val =="1"){
                        return "冷藏车";
                    }else if(val =="2"){
                        return "保温车";
                    }else if(val =="3") {
                        return "冷藏箱";
                    }else if(val =="4"){
                        return "其他封闭式车辆"
                    }else{
                        return ""
                    }
                },
                unformat: function (val){
                    if (val =="厢式送货"){
                        return "0";
                    }else if(val =="冷藏车"){
                        return "1";
                    }else if(val =="保温车"){
                        return "2";
                    }else if(val =="冷藏箱") {
                        return "3";
                    }else if(val =="其他封闭式车辆"){
                        return "4"
                    }

                }
            }, {
                name: 'isPrepay',
                index: 'isPrepay',
                width: 250,
                sortable: false,
                editable: true,
                formatter:function (val) {
                    if (val =="1"){
                        return "是"
                    }else if(val =="0"){
                        return "否"
                    }else {
                        return "";
                    }
                },
                unformat: function (val){
                    if (val =="是"){
                        return "1"
                    }else if(val =="否"){
                        return "0"
                    }else {
                        return "";
                    }
                }
            },
            {
                name: 'deliveryman',
                index: 'deliveryman'
            },
            {
                name: 'deliveryTel',
                index: 'deliveryTel'
            },{
                name: 'storeAddress',
                index: 'storeAddress'
            },{
                name: 'expectArrivalTime',
                index: 'expectArrivalTime',
                formatter: function (value) {
                    if(value){
                        return moment(value).format('YYYY-MM-DD');
                    }else{
                        return '';
                    }
                },
            },{
                name: 'amount',
                index: 'amount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            },{
                name: 'taxAmount',
                index: 'taxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            },{
                name: 'taxLimitAmount',
                index: 'taxLimitAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'thisTimeApplayAmount',
                index: 'thisTimeApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }

            }, {
                name: 'totalApplayAmount',
                index: 'totalApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }
        ];

        var url;
        url = '/proxy-finance/finance/purchase/payrequestinfo/selectOrderByPayReqNo?reqNo='+billNo

        $('#X_Tablea').XGrid({
            //data: grid_data,
            url: url,
            // url: 'http://localhost:8080/account/find',
            colNames: colNames,
            colModel: colModel,
            rownumbers: true,
            rowNum: 20,
            rowList: [20,50,100],//分页条数下拉选择
            altRows: true,//设置为交替行表格,默认为false
            gridComplete: function () {
                setTimeout(function (param) {
                    var data = $('#X_Tablea').XGrid('getRowData');
                    $('#X_Tablea').XGrid('addRowData', {
                        id: 11111,
                        amount: totalTable(data, 'amount'),
                        taxAmount: totalTable(data, 'taxAmount'),
                        taxLimitAmount: totalTable(data, 'taxLimitAmount'),
                        thisTimeApplayAmount: totalTable(data, 'thisTimeApplayAmount'),
                        totalApplayAmount: totalTable(data, 'totalApplayAmount')
                    }, 'last');
                    $('#X_Tablea tr:last-child td:eq(1)').html('');
                    $('#X_Tablea tr:last-child td:first-child').html('合计');
                    if(window.isFirst){
                        $('#X_Tablea input[type=checkbox]').prop('checked', true).trigger('input');
                        window.isFirst = false;
                    };

                }, 200)

                setTimeout(function(){


                     $("#print_divsa").jqprint({
                          globalStyles: true, //是否包含父文档的样式，默认为true
                          mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                          stylesheet: null, //外部样式表的URL地址，默认为null
                          noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                          iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                          append: null, //将内容添加到打印内容的后面
                          prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                          deferred: $.Deferred() //回调函数

                         });

                 }, 1500);
            },
            onSelectRow: function (id, dom, obj, index, event) {
                //选中事件
                //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件
                $('#basicInfo').JSONToform(obj);

                // console.log(id, dom, obj, index, event)
            },
            ondblClickRow: function (e, c, a, b) {
                console.log('双击行事件', e, c, a, b);
            },
            pager: '#grid_pager1',
        });

        // 查询
        $('#searchBtn').bind('click', function (param) {
            $('#X_Tablea').XGrid('setGridParam', {
                url: '/proxy-finance/finance/purchase/payrequestinfo/loadPurchaseOrder',
                postData: {
                    page:1
                }
            }).trigger('reloadGrid');
        })



        $('#X_Tablea').on('click','input[type="checkbox"]',function () {
            console.log('sss')
        })
    }
    ////////////////////////////加载订单数据结束////////////////////////////////////////////////


/////////////////////////////////加载发票数据开始/////////////////////////////////////////////////////////////////////
    function loadInvoiceTab(billNo){
        var colNames = ['隱藏列','ERP发票编号', '开票日期', '供应商编号', '供应商名称', '发票不含税金额', '是否预付', '送货人', '送货人电话', '供应商仓库地址',
            '预计到货时间', '金额合计',
            '税额合计', '税价合计', '状态','本次申请金额','累计申请金额'
        ];
        var colNames1 = ['隐藏列','隐藏列','ERP发票编码', '供应商发票号', '开票日期', '供应商编号', '供应商名称', '发票不含税金额合计', '发票税额合计', '发票价税合计', '未核销金额','本次申请金额','累计申请金额'];
        var colModel1 =[
            {
                name: 'id',
                hidden:true
            },{
                name: 'isInit',
                hidden:true
            },
            {
                name: 'invoiceNo',
                index: 'invoiceNo'
            }, {
				name: 'supplierInvoiceNumber',
				index: 'supplierInvoiceNumber'
			}, {
                name: 'invoiceCreateDate',
                index: 'invoiceCreateDate',
                formatter: function (value) {
                    if(value){
                        return moment(value).format('YYYY-MM-DD');
                    }else{
                        return '';
                    }
                }
            }, {
                name: 'supplierNo',
                index: 'supplierNo'
            }, {
                name: 'supplierName',
                index: 'supplierName'
            }, {
                name: 'noTotalTaxAmount',
                index: 'noTotalTaxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'totalInvoiceValue',
                index: 'totalInvoiceValue',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'totalInvoiceTax',
                index: 'totalInvoiceTax',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
            name: 'unwriteoffMoney',
            index: 'unwriteoffMoney',
            formatter: function (val) {
                return parseFloat(val).formatMoney('2', '', ',', '.');
            },
            unformat: function (val) {
                return val.replace(/,/g, '');
            }
        }, {
                name: 'thisTimeApplayAmount',
                index: 'thisTimeApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }

            }, {
                name: 'totalApplayAmount',
                index: 'totalApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }];

        var url;
        url = '/proxy-finance/finance/purchase/payrequestinfo/selectInvoiceByPayReqNo?billNo='+billNo;


        $('#Y_Tablea').XGrid({
            url: url,
            colNames: colNames1,
            colModel: colModel1,
            rowNum: 20,
            rownumbers: true,
            altRows: true, //设置为交替行表格,默认为false

            ondblClickRow: function (e, c, a, b) {
                console.log('双击行事件', e, c, a, b);
                var billNo = a.invoiceNo;
				var isInit = a.isInit;
                showDetail(billNo, isInit);
            },
            gridComplete: function () {
                setTimeout(function (param) {
                    var data = $('#Y_Tablea').XGrid('getRowData');
                    $('#Y_Tablea').XGrid('addRowData', {
                        id: 222222,
                        noTotalTaxAmount: totalTable(data, 'noTotalTaxAmount'),
                        totalInvoiceValue: totalTable(data, 'totalInvoiceValue'),
                        totalInvoiceTax: totalTable(data, 'totalInvoiceTax'),
 						unwriteoffMoney: totalTable(data, 'unwriteoffMoney'),
                        thisTimeApplayAmount: totalTable(data, 'thisTimeApplayAmount'),
                        totalApplayAmount: totalTable(data, 'totalApplayAmount')
                    }, 'last');
                    $('#Y_Tablea tr:last-child td:eq(1)').html('');
                    $('#Y_Tablea tr:last-child td:first-child').html('合计');
                    if(window.isFirst){
                        $('#Y_Tablea input[type=checkbox]').prop('checked', true).trigger('input');
                        window.isFirst = false;
                    };


                }, 200)

                 setTimeout(function(){


                         $("#print_divsa").jqprint({
                              globalStyles: true, //是否包含父文档的样式，默认为true
                              mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                              stylesheet: null, //外部样式表的URL地址，默认为null
                              noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                              iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                              append: null, //将内容添加到打印内容的后面
                              prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                              deferred: $.Deferred() //回调函数

                             });

                     }, 1500);

            },
        });

        // 查询
        $('#searchInvoiceBtn').bind('click', function (param) {
            $('#X_Tablea').XGrid('setGridParam', {
                url: '/proxy-finance/finance/purchase/payrequestinfo/getPayreqInvoice',
                postData: {
                    page:1
                }
            }).trigger('reloadGrid');
        })

    }
 /////////////////////////////////加载发票数据结束/////////////////////////////////////////////////////////////////////

    ////////////////////////////////公共方法开始//////////////////////////////////////////////////////////
    //合计
    function totalTable(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat(item[colName]);
        })
        return count.toFixed(2);

    }

    function loadFolow(processId){

        //132501   90047 purchaseReturn
        // var processId="${processId!}";
        // var taskId="${taskId!}"; //后续需修改，默认展示90047
        $.ajax({
            type: "POST",
            // url: "/proxy-purchase/purchase/purchaseRefundProductOrder/queryTotle?key=purchaseReturn",
            url: "/proxy-finance/finance/purchase/payrequestinfo/queryTotle?processInstaId="+processId,
            //async: false,
            error: function () {
                alert("审批意见不能为空！");
            },
            success: function (data) {
                if (data.code==0){
                    if(data.result!=null){
                        $('.flow').empty().process(data.result);

                    }
                }else {
                    utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
                }
            }
        });
    }


    function showDetail(billNo, isInit) {
        var title = "";
        var url = "";
        var height =0;
        title = "采购发票详情";
        url = '/proxy-finance/finance/purchase/payrequestinfo/detailInvoiceInfo?invoiceNo=' + billNo+'&isInit='+isInit;
        height = $(window).height() * 0.8;
        utils.dialog({
            title: title,
            url: url,
            width: $(window).width() * 0.8,
            height: height,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                $('iframe').remove();
            }
        }).show();
    }



     }

    payableTypeChange('#payableType')
    //  支付方式切换
    function payableTypeChange(el) {
        $('#accept_transfer_Rebate').css('display', ($(el).val() == '112' || $(el).val() == '114' || $(el).val() == '115') ? 'block' : 'none')
    }
})
