$(function () {
  /* 获取dialog上层实例 */
  var dialog = parent.dialog.get(window);
  if (dialog) {
    var dialog_data = dialog.data;
    $('#search_vl').val(dialog_data)
  }

  /* table_a */
  var grid_data = [{
    id: "00001",
    type: "退货出库",
    pay: "1000",
    name: "abc",
    text: "ccc",
    test: "嗯嗯嗯",
    test1: "嗯嗯嗯",
  }];
  for (let i = 0; i < 20; i++) {
    grid_data.push(grid_data[0]);
  }
  $('#table_a').XGrid({
    data: grid_data,
    // url: '/',
    /* postData:{
      name:dialog_data
    }, */
    colNames: ['商品编码', '商品名称', '商品规格', '生产厂家', '商品产地', '单位', '批准文号'],
    colModel: [{
      name: 'id',
      index: 'id',
      key: true,

    }, {
      name: 'type',
      index: 'type',

    }, {
      name: 'pay',
      index: 'pay',

    }, {
      name: 'name',
      index: 'name',

    }, {
      name: 'text',
      index: 'text',

    }, {
      name: 'test',
      index: 'test',

    }, {
      name: 'test1',
      index: 'test1',

    }],
    rowNum: 10,
    rownumbers: true,
    altRows: true, //设置为交替行表格,默认为false
    onSelectRow: function (id, dom, obj, index, event) {
      var data = [{
        id: "00001",
        type: "退货出库",
        pay: "1000",
        name: "abc",
        text: "ccc",
        test: "嗯嗯嗯",
        test1: "嗯嗯嗯",
      }];
      $('#table_b').XGrid('clearGridData').XGrid('setGridParam', {
        //$('#table_b').XGrid('setGridParam', {
            data: data,
        }).trigger("reloadGrid");
    },
    pager: '#grid_pager_a',
  });

  /* table_b */
  $('#table_b').XGrid({
    data: grid_data,
    // url: '/',
    /* postData:{
      name:dialog_data
    }, */
    colNames: ['仓库名称', '批号', '生产日期', '有效期至', '库存数量'],
    colModel: [{
      name: 'id',
      index: 'id',
    }, {
      name: 'type',
      index: 'type',

    }, {
      name: 'pay',
      index: 'pay',

    }, {
      name: 'name',
      index: 'name',

    }, {
      name: 'text',
      index: 'text',

    }],
    rowNum: 999,
    selectandorder: true,
    altRows: true, //设置为交替行表格,默认为false
  });

  /* 查询 */
  $('#search').on('click', function () {
    $('#table_a').setGridParam({
      url: 'http://www.baidu.com?name=' + $('#a').val(),
      postData: {
        a: 1
      }
    }).trigger('reloadGrid');
  })

  /* 确定 */
  $('#sub').on('click', function () {
    var data_a = $('#table_a').XGrid('getSeleRow');
    var data_b = $('#table_b').XGrid('getSeleRow');
    if(data_a&&data_b){
      var obj = {
        data_a:data_a,
        data_b:data_b
      }
      dialog.close(obj);
    }
    console.log(data_a,data_b);
  })



  // 去掉所有input的autocomplete, 显示指定的除外
  /* $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete',
    'off'); */

  /* 模糊搜索 */
  var countries = [{
      value: 'Andorra',
      data: 'AD'
    },
    {
      value: 'Zimbabwe',
      data: 'ZZ'
    }
  ];

  $('#search_vl').Autocomplete({
    serviceUrl: 'http://localhost/x.json', //异步请求
    // paramName: 'query111',//查询参数，默认 query
    dataType: 'json',
    //lookup: countries, //监听数据 value显示文本，data为option的值
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    /*  dataReader:{
          'value':'manufactoryName',
          'data':'manufactoryId',
           'xxt':"name"
      },*/
    triggerSelectOnValidInput: false, // 必选
    transformResult: function (response) {
      return {
        suggestions: $.each(response, function (idnex, dataItem) {
          return {
            value: dataItem,
            data: dataItem
          };
        })
      };
    },
    // showNoSuggestionNotice: true, //显示查无结果的container
    // noSuggestionNotice: '查询无结果',//查无结果的提示语
    // tabDisabled: true,
    onSelect: function (result) {
      //选中回调
      alert('You selected: ' + result.value + ', ' + result.data + ',' + result.xxt);
      // console.log('选中回调')
    },
    onSearchStart: function (params) {
      // console.log('检索开始回调', params)
    },
    onSearchComplete: function (query, suggestions) {
      //匹配结果后回调
      // console.log(query, suggestions);
      // if (suggestions.length < 1) {
      //     utils.dialog({
      //         title: '查询无结果',
      //         content: '是否新增生产厂家？',
      //         width: '300',
      //         okValue: '确认',
      //         ok: function () {
      //             this.title('提交中…');
      //             return false;
      //         },
      //         cancelValue: '取消',
      //         cancel: function () {
      //             $('input').val('')
      //         }
      //     }).show();
      // }
    },
    onSearchError: function (query, jqXHR, textStatus, errorThrown) {
      //查询失败回调
      console.log(query, jqXHR, textStatus, errorThrown)
    },
    onHide: function (container) {
      // console.log('container隐藏前回调', container)
    },
    onNoneSelect: function (params, suggestions) {
      console.log(params, suggestions);
      console.log('没选中回调函数');
    }
  });


})