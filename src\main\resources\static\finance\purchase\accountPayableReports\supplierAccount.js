// 去掉所有input的autocomplete, 显示指定的除外

$(function () {
    $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete',
        'off');

//字典查询
    var countries = [{
        value: 'Andorra',
        data: 'AD'
    },
        // ...
        {
            value: 'Zimbabwe',
            data: 'ZZ'
        }
    ];

    $('.selector').Autocomplete({
        serviceUrl: 'data.json', //异步请求
        paramName: 'query111', //查询参数，默认 query
        dataType: 'json',
        noCache: true,
        // lookup: countries, //监听数据 value显示文本，data为option的值
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        triggerSelectOnValidInput: false, // 必选
        onSelect: function (result) {
            //选中回调
            alert('You selected: ' + result.value + ', ' + result.data);
            // console.log('选中回调')
        }
    });

//调用validate 初始化 自定义
// $("#submitBtn").click(function () {
//     //提交前验证
//     //console.log(validform("myform").form());
//     if (validform("myform").form()) {//验证通过 "myform"为需要验证的form的ID
//         utils.dialog({content: '校验通过！', quickClose: true, timeout: 2000}).showModal();
//     } else {//验证不通过
//         utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
//         return false;
//     }
// })

    var grid_data = [{
        id: "1",
        text1: "189000001",
        text2: "湖北同济堂",
        text3: "0.00",
        text4: "1000.00",
        text5: "1000.00",
        text6: "0.00"
    }, {
        id: "2",
        text1: "189000001",
        text2: "武汉保安堂",
        text3: "2000.00",
        text4: "3000.00",
        text5: "4000.00",
        text6: "1000.00"
    }];


    var colNames = ['序号', '客户编号', '客户名称', '期初金额', '销售金额', '回款金额', '余额'],
        colModel = [{
            name: 'id',
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 300,
            editable: true
        }, {
            name: 'text1',
            index: 'text1',
            width: 200 //宽度

        }, {
            name: 'text2',
            index: 'text2',
            width: 200 //宽度

        }, {
            name: 'text3',
            index: 'text3',
            width: 200 //宽度

        }, {
            name: 'text4',
            index: 'text4',
            width: 200 //宽度

        }, {
            name: 'text5',
            index: 'text5',
            width: 200 //宽度

        }, {
            name: 'text6',
            index: 'text6',
            width: 200 //宽度

        }];

    $('#X_Table').XGrid({
        data: grid_data,
        // url: 'http://www.baidu.com',
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList: [20,50,100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        mutltiselect: true,
        ondblClickRow: function (rowid, usedata) {
            var rowData = $("#X_Table").XGrid('getRowData', rowid);
            $.extend(rowData, {
                id: rowid
            });
            dialog.close(rowData);
            dialog.remove();
        },
        onSelectRow: function (e, c, a, b) {
            console.log('单机行事件', e, c, a, b);
        },
        pager: '#grid-pager',
    });

    var dialog = parent.dialog.get(window);
//取消按钮
    $('#cancelBtn').click(function () {
        dialog.close('返回值');
        dialog.remove();
    })

//确定按钮
    $('#submitBtn').click(function () {
        var rowData = $("#X_Table").XGrid('getSeleRow');
        dialog.close(rowData);
        dialog.remove();
    })

    $("#filterBtn").bind('click',function () {
        $("#X_Table").XGrid("filterTableHead");
    })
})