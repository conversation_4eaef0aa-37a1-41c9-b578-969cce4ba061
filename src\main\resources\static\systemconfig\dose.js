
$(function () {
    $('#X_Table').XGrid({
        url:"/proxy-sysmanage/sysmanage/dict/querydose",
        colNames: ['剂型id ', '剂型名称', '助记码', '是否停用', '关键词', '创建人', '创建日期'/*,'操作'*/],
        colModel: [
            {
                name: 'doseid',
                index: 'doseid',//索引。其和后台交互的参数为sidx
                width: 100,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            },  {
                name: 'dosename',
                index: 'dosename',//索引。其和后台交互的参数为sidx
                width: 100,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'dosenumber',
                index: 'dosenumber',
                width: 200,//宽度
                editable: true,//是否可编辑
                edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

            }, {
                name: 'doseisstop',
                index: 'doseisstop',
                width: 60,
                formatter:isShop,
                editable: true
            }, {
                name: 'dosekeyword',
                index: 'dosekeyword',
                width: 150,
                editable: true,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'createuser',
                index: 'createuser',
                width: 250,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'createtime',
                index: 'createtime',
                width: 250,
                sortable: false,
                editable: true,
                formatter:datetimeFormatter,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }/*, {

                formatter:tablediv,
                editable: true
            }*/
        ],
        key:'doseid',
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function (id, dom, obj, index, event) {

            //双击事件回调函数
            //获取本行数据
            var selRow = obj;
            if(selRow){
                var el = document.querySelector('#dialog_Block');
                $('#addform')[0].reset();
                $("[name='doseid']").val(selRow.doseid)
                $("[name='dosenumber']").val(selRow.dosenumber)
                $("[name='dosename']").val(selRow.dosename)
                $("[name='dosekeyword']").val(selRow.dosekeyword)
                $("[name='createuser']").val(selRow.createuser)
                if(selRow.doseisstop=="是"){
                    $(":radio[name='doseisstop'][value='1']").prop("checked", "checked");
                }else{
                    $(":radio[name='doseisstop'][value='0']").prop("checked", "checked");
                }
                var val=$('input[name="dosekeyword"]').val();
                $('input[data-role="tagsinput"]').tagsinput('removeAll');
                $('input[data-role="tagsinput"]').tagsinput('add',val);
                $('input[name="dosekeyword"]').attr("disabled",true);;
                $("[name='dosename']").attr("disabled",true);
                $(":radio[name='doseisstop']").attr("disabled",true);
                utils.dialog({
                    title: '查看明细',
                    content: el,
                }).showModal();
                $(".tagsinput input[type='text']").prop("disabled",true);
                $(".tag").dblclick(function (ev) {
                    return false;
                })
            }else{
                utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
            }

        },
        onSelectRow: function (id, dom, obj, index, event) {
            //回调参数：id：本行id,dom:本行DOM元素,index:下标,event:原事件

        }
    });

})

$("#querybut").click(function () {

            $('#X_Table').setGridParam({
                url:"/proxy-sysmanage/sysmanage/dict/querydose",
                postData:{
                    "doseName":$('#doseName').val().replace(/\s+/g,""),
                    "isStop":$('#isStop').val()
                        }
            }).trigger('reloadGrid');

                });
        //新增一行
        $('#addRowData').on('click', function () {
            $('input[name="dosekeyword"]').attr("disabled",false);
            $("[name='dosename']").attr("disabled",false);
            $(":radio[name='doseisstop']").attr("disabled",false);
            $('#addform')[0].reset();
            $('input[data-role="tagsinput"]').each(function () {
                $(this).tagsinput('removeAll');
            })
            $(".tagsinput input[type='text']").prop("disabled",false);
            $("[name='doseid']").val(null)
            var el = document.querySelector('#dialog_Block');//html元素
            utils.dialog({
                title: '新增',
                content: el,
                width:'630px',
                okValue: '确定',
                ok: function () {
                    $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                    data= decodeURIComponent($("#addform").serialize(),true);
                    if (validform("addform").form()) {//验证通过 "myform"为需要验证的form的ID
                        $.ajax({
                            url:"addDoseOrUpdate",
                            type:"post",
                            data:data,
                            success:function(result){
                                utils.dialog({content:  result.result, quickClose: true, timeout: 2000}).showModal();
                                setTimeout("location.reload();",1000);
                            }
                        })

                    } else {//验证不通过
                        utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }


                },
                cancelValue: '取消',
                cancel: function () {

                }
            }).showModal();

            })

        function datetimeFormatter(val) {
            if (val != null && val !="") {
                return ToolUtil.dateFormat(val, 'yyyy-MM-dd HH:mm:ss');
            } else {
                return "";
            }
        };

        function isShop(val){
            if(val==0){
                return "否"
            }else{
                return  "是"
                }
                            }

        $("#resetbut").click(function(){
            $('#doseName').val(null)
        })

        //删除选中行
        $('#deldose').on('click', function () {
         var selRow = $('#X_Table').XGrid('getSeleRow');
         if (selRow) {
                 $.ajax({
                        url:"delatedose",
                        data:{doseId:selRow.doseid},
                        type:"POST",
                        success:function(result){
                            utils.dialog({content:  result.result, quickClose: true, timeout: 2000}).showModal();
                            setTimeout("location.reload();",1000);
                            }
                    })
                         } else {
                    utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
                 }
                                    });
                    //input小标签回填
                    $('input[data-role="tagsinput"]').each(function () {
                    $(this).tagsinput('add', $(this).val());
                    })
            function  tablediv(){
                        return  "<a  href='javascript:;' onclick='editdose(this)'>编辑</a>";
            }

            //修改选中行
            function  editdose (obj){
                    //获取本行数据
                    $('input[name="dosekeyword"]').attr("disabled",false);
                    $("[name='dosename']").attr("disabled",false);
                    $(":radio[name='doseisstop']").attr("disabled",false);
                    $(".tagsinput input[type='text']").prop("disabled",false);
                    var id=$(obj).parents('tr').attr('id');
                    var selRow = $('#X_Table').XGrid('getRowData',id);
                    var el = document.querySelector('#dialog_Block');
                    $('#addform')[0].reset();
                    console.log(selRow.doseid)
                    $("[name='doseid']").val(selRow.doseid)
                    $("[name='dosenumber']").val(selRow.dosenumber)
                    $("[name='dosename']").val(selRow.dosename)
                    $("[name='dosekeyword']").val(selRow.dosekeyword)
                    $("[name='createuser']").val(selRow.createuser)
                    if(selRow.doseisstop=="是"){
                        $(":radio[name='doseisstop'][value='1']").prop("checked", "checked");
                    }else{
                        $(":radio[name='doseisstop'][value='0']").prop("checked", "checked");
                    }
                    var val=$('input[name="dosekeyword"]').val();
                    $('input[data-role="tagsinput"]').tagsinput('removeAll');
                    $('input[data-role="tagsinput"]').tagsinput('add',val);

                    if (selRow) {

                        utils.dialog({
                            title: '编辑',
                            content: el,
                            width:'630px',
                            okValue: '确定',
                            ok: function () {
                                $(':disabled').attr('readonly','readonly').removeAttr('disabled');
                                data= decodeURIComponent($("#addform").serialize(),true);
                                if (validform("addform").form()) {//验证通过 "myform"为需要验证的form的ID
                                    $.ajax({
                                        url: "addDoseOrUpdate",
                                        type: "post",
                                        data: data,
                                        success: function (result) {
                                            utils.dialog({content: result.result, quickClose: true, timeout: 2000}).showModal();
                                            setTimeout("location.reload();",1000);

                                        }
                                    })
                                }else {//验证不通过
                                    utils.dialog({content: '校验不通过！', quickClose: true, timeout: 2000}).showModal();
                                    return false;
                                }
                            },
                            cancelValue: '取消',
                            cancel: function () {
                            }
                        }).showModal();
                    } else {
                        utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
                    }

            }