var urlObjectList = [];
let baseName = ['','目录id','业务类型id','业务类型','约定供货价','APP售价','负毛利','采购员', '机构','list'],
    baseModel = [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },{
            name: 'fid',
            index: 'fid',
            hidden:true
        },{
            name: 'channelId',
            index: 'channelId',
            hidden:true
        },{
            name: 'channelVal',//业务类型名称
            index: 'channelVal',
            width: 100
        },{
            name: 'promisePrice',//约定供货价
            index: 'promisePrice',
            width: 100
        },{
            name: 'appPrice',//app售价
            index: 'appPrice',
            width: 100
        },{
            name: 'grossProfit',//负毛利
            width: 100,
            formatter: function(val,a,obj,c){
                if (obj.appPrice &&  obj.promisePrice && Number(obj.promisePrice) > Number(obj.appPrice)) {
                    return  '是'
                }else{
                    return  '否'
                }

            }
        },{
            name: 'buyer',
            index: 'buyer',
            width: 80
        },
        {
            name: 'orgName',
            index: 'orgName',
            width: 230
        },
        {
            name: 'scopeCodeList',//供应商经营范围
            index: 'scopeCodeList',
            hidden:true
        }
    ];
let supplierName = ['供应商编码', '供应商名称'],
    supplierModel = [
        {
            name: 'supplierCode',
            index: 'supplierCode',
            width: 150

        },
        {
            name: 'supplierName',
            index: 'supplierName',
            width: 250
        }
    ];
let productName = ['商品编码', '商品名称', '通用名', '规格/型号','生产厂家'],
    productModel = [
        {
            name: 'productCode',
            index: 'productCode',
            width: 120
        }, {
            name: 'productName',
            index: 'productName',
            width: 180
        }, {
            name: 'commonName',
            index: 'commonName',
            width: 150
        }, {
            name: 'specifications',
            index: 'specifications',
            width: 150
        }, {
            name: 'manufacturerName',
            index: 'manufacturerName',
            width: 210
        },
    ];
let defaultName = supplierName.concat(productName),
    defaultModel = supplierModel.concat(productModel);
let colNames = baseName.splice(1,0,defaultName),
    colModel = baseModel.splice(1,0,defaultModel);
colNames = Array.prototype.concat.apply([],baseName);
colModel = Array.prototype.concat.apply([],baseModel);
$('#X_Tableb').XGrid({
    url:"/proxy-supplier/supplier/supplierGoodsPrice/querySupplierGoodsPriceList",
    colNames: colNames,
    colModel: colModel,
    rowNum: 20,
    rowList:[20,50,100],
    rownumbers:true,
    altRows: true,//设置为交替行表格,默认为false
    multiselect: true,//是否多选
    key: 'id',
    pager: '#grid-pager',
    onSelectRow: function (id, dom, obj, index, event) {
        console.log(id, dom, obj, index, event)
        setUrlObjectList(dom,id,obj);
    }
});


//查看方式   viewType
$('#viewType').on('change', function () {
    let thisVal = $(this).val();
    baseName = ['','业务类型','业务类型','约定供货价','APP售价','负毛利','采购员', '机构','list'],
    baseModel = [
        {
            name: 'id',
            index: 'id',//索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            width: 100,
            hidden:true
        },{
            name: 'channelId',
            index: 'channelId',
            hidden:true
        },{
            name: 'channelVal',//业务类型
            index: 'channelVal',
            width: 100
        },{
            name: 'promisePrice',//约定供货价
            index: 'promisePrice',
            width: 100
        },{
            name: 'appPrice',//app售价
            index: 'appPrice',
            width: 100
        },{
            name: 'grossProfit',//负毛利
            index: 'grossProfit',
            width: 100,
            formatter: function(val,a,obj,c){
                if (obj.appPrice &&  obj.promisePrice && Number(obj.promisePrice) > Number(obj.appPrice)) {
                    return  '是'
                }else{
                    return  '否'
                }

            }
        },{
            name: 'buyer',
            index: 'buyer',
            width: 100
        },
        {
            name: 'orgName',
            index: 'orgName',
            width: 230
        },
        {
            name: 'scopeCodeList',//供应商经营范围
            index: 'scopeCodeList',
            hidden:true
        }
    ];
    if (thisVal == '2') {
        defaultName = productName.concat(supplierName);
        defaultModel = productModel.concat(supplierModel);
    }else{
        defaultName = supplierName.concat(productName);
        defaultModel = supplierModel.concat(productModel);
    }
    baseName.splice(1,0,defaultName);
    baseModel.splice(1,0,defaultModel);
    colNames = Array.prototype.concat.apply([],baseName);
    colModel = Array.prototype.concat.apply([],baseModel);
    var promisePrice = '';
    $("input[name='promisePrice']:checkbox").each(function () {
        if ($(this).prop("checked")) {
            promisePrice = $(this).attr('value');
        }
    });
    $('.hidden-x-scroll').html('<table id="X_Tableb"> </table>')
    $('#X_Tableb').XGrid({
        url:"/proxy-supplier/supplier/supplierGoodsPrice/querySupplierGoodsPriceList",
        colNames: colNames,
        colModel: colModel,
        rowNum: 20,
        rowList:[20,50,100],
        rownumbers:true,
        altRows: true,//设置为交替行表格,默认为false
        multiselect: true,//是否多选
        pager: '#grid-pager',
        postData: {
            "selectType": $("#viewType").val(),
            "supplierField": $("#supplierField").val(),
            "productCode":$("#productCode").val(),
            "productCode1":$("#productCode1").val(),
            "channelId":$("#channelId").val(),
            "promisePrice":promisePrice,
            "orgCode":$("#orgCode").val(),
        },
    });
})

// 调价
$('#updatePrice').on('click', function () {
    let selData  = $('#X_Tableb').XGrid('getSeleRow');
    if (selData.length == 0 ){
        utils.dialog({
            title: '提示',
            content: '请选择需要调价的数据',
            okValue: '确定',
            ok: function () {}
        }).showModal()
        return false
    }
    utils.dialog({
        url: '/proxy-supplier/supplier/supplierGoodsPrice/toPrice',
        title: '供货价格调整',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {
            selData,
            viewType: $('#viewType').val()
        },
        onclose: function () {
            var promisePrice = '';
            $("input[name='promisePrice']:checkbox").each(function () {
                if ($(this).prop("checked")) {
                    promisePrice = $(this).attr('value');
                }
            });
            $('#X_Tableb').XGrid('setGridParam', {
                postData: {
                    "selectType": $("#viewType").val(),
                    "supplierField": $("#supplierField").val(),
                    "productCode":$("#productCode").val(),
                    "productCode1":$("#productCode1").val(),
                    "channelId":$("#channelId").val(),
                    "promisePrice":promisePrice,
                    "orgCode":$("#orgCode").val(),
                },page:1
            }).trigger('reloadGrid');
            // if (this.returnValue) {
            //
            // }
        },
        oniframeload: function () {
        }
    }).showModal();
})

//搜索按钮
$("#searchBtn").on("click", function () {
    urlObjectList=[];
    var promisePrice = '';
    $("input[name='promisePrice']:checkbox").each(function () {
        if ($(this).prop("checked")) {
            promisePrice = $(this).attr('value');
        }
    });
    $('#X_Tableb').XGrid('setGridParam', {
        postData: {
            "selectType": $("#viewType").val(),
            "supplierField": $("#supplierField").val(),
            "productCode":$("#productCode").val(),
            "productCode1":$("#productCode1").val(),
            "channelId":$("#channelId").val(),
            "promisePrice":promisePrice,
            "orgCode":$("#orgCode").val(),
        },page:1
    }).trigger('reloadGrid');
});

$(function () {
    //业务类型搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('0').then( res => {
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        }).catch(err => {
            console.log(err);
        })
    })
})

$(document).on('change','#grid_checked input',function () {
    if ($(this).prop('checked')) {
        var $tr = $(this).parents('tr');
        var rowData = $("#X_Tableb").getRowData($tr.attr('id'));
        var id = $tr.attr('id');
        setUrlObjectList($tr, id, rowData);
    } else {
        urlObjectList = [];
    }
})



function setUrlObjectList($tr,id,rowData){
    var a=rowData;
    var fileParam = {};
    if(!id){
        //  点击的是全选按钮。，所以拿全部数据
        urlObjectList = a.map(function (item,index) {
            let _obj = {}
            _obj.id = item.id;
            return _obj;
        })
    }else{
        if($tr.hasClass('selRow') && a.snapImageUrl){
            fileParam.id = a.id;
            urlObjectList.push(fileParam);
        }else if(!$tr.hasClass('selRow')){
            $(urlObjectList).map(function (i,v) {
                if(v.id==a.id){
                    urlObjectList.splice(i,1);
                }
            })
        }
    }


}


//商品 点击放大镜触发
$(document).on("click", "#searchProductBtn", function (ev) {
    searchPdroduct(ev);
});
// 商品 回车触发
$(document).on("keydown", "#productCode1", function (ev) {
    if(ev.keyCode==13){
        searchPdroduct(ev);
    }
});
// 搜索商品
function searchPdroduct(ev){
    var orgCode = $("#orgCode").val();
    dialog({
        url: '/proxy-supplier/supplier/supplierGoodsCatalogue/toProductCatalogueList',//弹框页面请求地址
        title: '搜索商品',
        width: 1000,
        height: 650,
        data: {
            orgCode: orgCode
        },
        onclose:function(){
            if(this.returnValue){
                var data=this.returnValue;
                $('#productCode1').val(data.productCode);
            }
        }

    }).showModal();
    ev.stopPropagation();
}

