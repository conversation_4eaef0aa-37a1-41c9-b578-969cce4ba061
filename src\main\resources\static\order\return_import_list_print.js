var getData;
$(function () {
    /* 日期格式化 */
    //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
    function dateFormat(time, format) {
        var t = new Date(parseInt(time,10));
        var tf = function (i) {
            return (i < 10 ? '0' : '') + i;
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        });
    }



    /* 获取数据 */
    getData = function (printType,orderReturnStorageCodes) {
        var orderReturnStorageCodes = orderReturnStorageCodes.join(',');
        $.ajax({
            url:"/proxy-order/order/orderReturnStorage/orderReturnStorageController/findOrderReturnStoragePrintList",
            data:{
                orderReturnStorageCodes:orderReturnStorageCodes,
                printType: printType
            },
            success:function(res){
                let resultList = res.result;
                if(resultList && $.isArray(resultList.printList)){
                    if($.isArray(resultList.priceOutOrderVoList)){
                        webRender(resultList.printList, printType, resultList.zlprintList,resultList.priceOutOrderVoList);
                    }else{
                        webRender(resultList.printList, printType, resultList.zlprintList);
                    }
                }else {
                    utils.dialog({
                        title:'提示',
                        content:"数据为空或格式不正确",
                        okValue:'确定',
                        ok:function () {}
                    }).showModal();
                }
            },
        })
    };
    /* 数据渲染 */
    function webRender(data,printType,zlPrintList,temList) {
        var box_html = '';
        /* 基本结构拼装 */
        data.forEach(function (item,index) {
            console.log(item,item.orgCode);
            /* 销售出库复核单 */
            box_html +=`
                   <div class="content indent1">
                    <div class="header">
                        <div class="title">${item.institutionName}</div>
                        <div class="title" style="font-size: 23px;">销售退回入库单</div>
                    </div>
                    <div class="top">
                        <ul class="info_list">
                            <li>
                                <span class="col_8"></span>                    
                                <!--<span class="col_2" style="margin-left:-20px;"> </span>-->
                                ${item.isSpecialMedicine=='1' ? ' <span class="col_1" style="margin-left:5px;">特殊药品</span>':'<span class="col_2" style="margin-left:-20px;"> </span>'}
                            </li>
                            <li>
                                <span class="col_8_2">客户编号：<i class="val">${item.customerCode?item.customerCode:''}</i></span>
                                <span class="col_1_8" style="margin-left:-20px;"> 单据编号：<i class="val">${item.refundRequestStorageCode}</i></span>
                            </li>
                            <li>
                                <span class="col_5_8">客户名称：<i class="val">${item.customerName?item.customerName:''}</i></span>
                                <span class="col_2_4">销售单位：<i class="val">${item.institutionName}</i></span>
                                <span class="col_1_8" style="margin-left:-20px;">销售员：<i class="val">${item.salesPersonName?item.salesPersonName:''}</i></span>
                            </li>
                            <li>
                                <span class="col_5_8">发货方式：<i class="val">${item.deleveryTypeDesc?item.deleveryTypeDesc:''}</i></span>
                                <span class="col_2_4">退货说明：<i class="val">${item.returnExplain?item.returnExplain:''}</i></span>
                                <span class="col_1_8" style="margin-left:-20px;">开票日期：<i class="val">${dateFormat(item.createTime,'yyyy-MM-dd')}</i></span>
                            </li>
                        </ul>
                    </div>
                    <table id="table_a_${index}"></table>
                    <div class="bottom">
                        <ul class="info_list">
                            <li>
                                <span class="col_3">合计金额（大写）：<i class="val">${item.taxAmountDesc}</i></span>
                                <span class="col_3">合计金额（小写）：<i class="val">${item.taxAmount}</i></span>
                                <span class="col_2">优惠金额：<i class="val">${item.discountAmount}</i></span>
                                <span class="col_2">实付金额：<i class="val">${item.paymentAmount}</i></span>
                            </li>
                            <li>
                                <span class="col_2">验收员：<i class="val">${item.checker?item.checker:''} </i></span>
                                <span class="col_2">保管员：<i class="val">${item.saver?item.saver:''} </i></span>
                                <span class="col_2">上架员：<i class="val">${item.putawayer?item.putawayer:''} </i></span>
                                <span class="col_2">共<i class="val">${item.pageTotal}</i>页，第<i class="val">${item.pageNumber}</i>页</span>
                                <span class="col_4">白联:财务部 红联:储运部 黄联:存档</span>
                            </li>
                        </ul>
                    </div>
                    <div style="page-break-after:always"></div>
                </div>
                `;
            /* 委托配送单 */
            if(temList&&temList.length>0){
                if (item.pageNumber == item.pageTotal && item.storeOrder == 1 && temList && temList.length > 0) {
                    let resultList = [];
                    temList.forEach(function (res, key) {
                        if (item.refundRequestStorageCode === res.refundRequestStorageCode) {
                            res.adds = key;
                            resultList.push(res);
                        }
                    });
                    console.log(item.refundRequestStorageCode, resultList);
                    if (resultList && resultList.length > 0) {
                        resultList.forEach(function (res_item, index) {
                            box_html += `
                        <div class="content indent1">
                            <div class="header">
                                <!--双行-->
                                <div class="title">${res_item.delegationDeliveryCompany ? res_item.delegationDeliveryCompany : ""}委托配送退货单</div>
                            </div>
                            <div class="top">
                                <ul class="info_list" style="padding-top: 75px;margin-top: -17px;">
                                    <li>
                                        <span class="col_3_2" style="width: 35%;">退货单位：<i class="val">${res_item.storeName ? res_item.storeName : ""}</i></span>
                                        <span class="col_3_2" style="width: 35%;">单据编号：<i class="val">${res_item.refundRequestStorageCode ? res_item.refundRequestStorageCode : ""}</i></span>
                                        <span class="col_3_2" style="width: 30%;">开票日期：<i class="val">${dateFormat(res_item.createTime, 'yyyy-MM-dd')}</i></span>
                                    </li>
                                </ul>
                            </div>
                            <table id="table_b_${res_item.adds}"></table>
                            <div class="bottom">
                                <ul class="info_list">
                                    <li>
                                        <span class="col_3">合计金额（大写）：<i class="val">${res_item.taxAmountDesc}</i></span>
                                        <span class="col_3">合计金额（小写）：<i class="val">${res_item.taxAmount}</i></span>
                                    </li>
                                    <li>
                                        <span class="col_4">白联:财务 红联:门店 黄联:仓库</span>
                                        <span class="col_2">共<i class="val">${res_item.pageTotal}</i>页，第<i class="val">${res_item.pageNumber}</i>页</span>
                                    </li>
                                </ul>
                            </div>
                            <div style="page-break-after:always"></div>
                        </div>
                    `
                        });
                    }
                }
            }else {
                if (item.pageNumber == item.pageTotal && item.storeOrder == 1 && zlPrintList && zlPrintList.length > 0) {
                    let resultList = [];
                    zlPrintList.forEach(function (res, key) {
                        if (item.refundRequestStorageCode === res.refundRequestStorageCode) {
                            res.adds = key;
                            resultList.push(res);
                        }
                    });
                    console.log(item.refundRequestStorageCode, resultList);
                    if (resultList && resultList.length > 0) {
                        resultList.forEach(function (res_item, index) {
                            box_html += `
                        <div class="content indent1">
                            <div class="header">
                                <!--双行-->
                                <div class="title">${res_item.delegationDeliveryCompany ? res_item.delegationDeliveryCompany : ""}委托配送退货单</div>
                            </div>
                            <div class="top">
                                <ul class="info_list" style="padding-top: 75px;margin-top: -17px;">
                                    <li>
                                        <span class="col_3_2" style="width: 35%;">退货单位：<i class="val">${res_item.storeName ? res_item.storeName : ""}</i></span>
                                        <span class="col_3_2" style="width: 35%;">单据编号：<i class="val">${res_item.refundRequestStorageCode ? res_item.refundRequestStorageCode : ""}</i></span>
                                        <span class="col_3_2" style="width: 30%;">开票日期：<i class="val">${dateFormat(res_item.createTime, 'yyyy-MM-dd')}</i></span>
                                    </li>
                                </ul>
                            </div>
                            <table id="table_b_${res_item.adds}"></table>
                            <div class="bottom">
                                <ul class="info_list">
                                    <li>
                                        <span class="col_3">合计金额（大写）：<i class="val">${res_item.taxAmountDesc}</i></span>
                                        <span class="col_3">合计金额（小写）：<i class="val">${res_item.taxAmount}</i></span>
                                    </li>
                                    <li>
                                        <span class="col_4">白联:财务 红联:门店 黄联:仓库</span>
                                        <span class="col_2">共<i class="val">${res_item.pageTotal}</i>页，第<i class="val">${res_item.pageNumber}</i>页</span>
                                    </li>
                                </ul>
                            </div>
                            <div style="page-break-after:always"></div>
                        </div>
                    `
                        });
                    }
                }
            }
        });

        $("#box").html(box_html);

        /* 表格初始化 */
        data.forEach(function (item,index) {
            item.detailVoList = item.detailVoList.map(function (val,key) {
                delete val.id;
                val.sortNo = key+1;
                return val
            });
            /* 销售出库复核单 */
            $("#table_a_"+index).jqGrid({
                data: item.detailVoList,
                datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                //width: "1322.83",
                colNames: ['序号', '商品编号<br/>药品通用名称（商品名）', '规格/<br/>型号', '剂型', '单位', '生产厂商','货位', '批号', '生产日期', '有效期至', '到货<br/>数量', '单价', '金额', '批准文号/注册证号<br/>/备案证号', '验收合<br/>格数量','质量'],
                colModel: [{
                    index: 'sortNo',
                    name: 'sortNo',
                    width: 30,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 8) {
                            //金额合计(大写) name
                            return 'colspan=3'
                        }
                    }
                },
                {
                    index: 'productName',
                    name: 'productName',
                    width: 165,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 8) {
                            //金额合计 name
                            return 'style="display:none"'
                        }
                    }
                }, {
                    index: 'specifications',
                    name: 'specifications',
                    width: 90,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 8) {
                            //金额合计 name
                            return 'style="display:none"'
                        }
                    }
                }, {
                    index: 'metrologicalSpecification',
                    name: 'metrologicalSpecification',
                    width: 55,
                }, {
                    index: 'productUnit',
                    name: 'productUnit',
                    width: 35,
                }, {
                    index: 'manufacturer',
                    name: 'manufacturer',
                    width: 180,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="word-wrap:break-word"'
                    }
                }, {
                    index: 'warehouseNature',
                    name: 'warehouseNature',
                    width: 50,
                }, {
                    index: 'batchCode',
                    name: 'batchCode',
                    width: 75,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="word-wrap:break-word"'
                    }
                }, {
                    index: 'productionTime',
                    name: 'productionTime',
                    width: 80,
                    formatter:function (e) {
                        if(e){
                            return dateFormat(e, 'yyyy-MM-dd')
                        }else{
                            return ""
                        }
                    }
                }, {
                    index: 'periodValidity',
                    name: 'periodValidity',
                    width: 80,
                    formatter:function (e) {
                        if(e){
                            return dateFormat(e, 'yyyy-MM-dd')
                        }else{
                            return ""
                        }
                    }
                }, {
                    index: 'storageNumber',
                    name: 'storageNumber',
                    width: 45,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="word-wrap:break-word"'
                    }
                }, {
                    index: 'taxPrice',
                    name: 'taxPrice',
                    width: 55,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="word-wrap:break-word"'
                    }
                }, {
                    index: 'taxAmount',
                    name: 'taxAmount',
                    width: 60,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="word-wrap:break-word"'
                    }
                }, {
                    index: 'approvalNumber',
                    name: 'approvalNumber',
                    width: 130,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="word-wrap:break-word"'
                    }
                }, {
                    index: 'storageNumber',
                    name: 'storageNumber',
                    width: 50,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        return 'style="word-wrap:break-word"'
                    }
                }, {
                    index: 'quality',
                    name: 'quality',
                    width: 35,
                }],
                shrinkToFit:false,
                rowNum: 7,
                gridview: true,
                gridComplete: function () {
                    var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                    var sum_sum = $(this).getCol('taxAmount', false, 'sum');
                    var data = $(this).getRowData();
                    //console.log(data);
                    if (data.length < 6) {
                        $(this).addRowData(data.length + 1, {}, "last");
                    } else if (data.length == 7) {
                        $(this).addRowData(8, {
                             sortNo: "本页小计" ,
                             taxAmount:item.amountSum ,
                             storageNumber: item.totalCount,
                        }, "last");
                    }
                    //$(this).footerData("set",{"productCode":"合计","number":sum_number,"sum":sum_sum});
                }
            });
        });
        if(temList&&temList.length>0){
            temList.forEach(function (item, index) {
                var keysList  = item.detailVoList.map(function (val,key) {
                    delete val.id;
                    if(val.salesOutOrderCode){
                        val.sort = key + 1;
                    }
                    return val
                });
                $("#table_b_"+index).jqGrid({
                    data: keysList,
                    datatype: "local",
                    height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                    colNames: ['序号','商品编号</br>药品通用名称（商品名）','规格/</br>型号','剂型','单位','生产厂商', '产地','批号','生产日期','有效期至','数量','单价', '金额',
                        '批准文号/注册证号<br />备案证号','上市许可<br/>持有人','退货</br>原因'],
                    colModel: [{
                        index: 'sortNo',
                        name: 'sortNo',
                        width: 30,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 8) {
                                //金额合计(大写) name
                                return 'colspan=3'
                            }
                        }
                    }, {
                        index: 'productName',
                        name: 'productName',
                        width: 165,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 8) {
                                //金额合计 name
                                return 'style="display:none"'
                            }
                        }
                    }, {
                        index: 'specifications',
                        name: 'specifications',
                        width: 90,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 8) {
                                //金额合计 name
                                return 'style="display:none"'
                            }
                        }
                    }, {
                        index: 'metrologicalSpecification',
                        name: 'metrologicalSpecification',
                        width: 55,
                    }, {
                        index: 'productUnit',
                        name: 'productUnit',
                        width: 35,
                    }, {
                        index: 'manufacturer',
                        name: 'manufacturer',
                        width: 180,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="word-wrap:break-word"'
                        }
                    }, {
                        index: 'productOrigin',
                        name: 'productOrigin',
                        width: 65,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="word-wrap:break-word"'
                        }
                    },{
                        index: 'batchCode',
                        name: 'batchCode',
                        width: 75,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="word-wrap:break-word"'
                        }
                    }, {
                        index: 'productionTime',
                        name: 'productionTime',
                        width: 80,
                        formatter:function (e) {
                            if(e){
                                return dateFormat(e, 'yyyy-MM-dd')
                            }else{
                                return ""
                            }
                        }
                    }, {
                        index: 'periodValidity',
                        name: 'periodValidity',
                        width: 80,
                        formatter:function (e) {
                            if(e){
                                return dateFormat(e, 'yyyy-MM-dd')
                            }else{
                                return ""
                            }
                        }
                    }, {
                        index: 'storageNumber',
                        name: 'storageNumber',
                        width: 40,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="word-wrap:break-word"'
                        }
                    }, {
                        index: 'taxPrice',
                        name: 'taxPrice',
                        width: 55,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="word-wrap:break-word"'
                        }
                    }, {
                        index: 'taxAmount',
                        name: 'taxAmount',
                        width: 60,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="word-wrap:break-word"'
                        }
                    }, {
                        index: 'approvalNumber',
                        name: 'approvalNumber',
                        width: 130,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="word-wrap:break-word"'
                        }
                    }, {
                        index: 'marketAuthor',
                        name: 'marketAuthor',
                        width: 70,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="word-wrap:break-word"'
                        }
                    }, {
                        index: 'returnReason',
                        name: 'returnReason',
                        width: 35,
                    }],
                    shrinkToFit:false,
                    rowNum: 7,
                    gridview: true,
                    gridComplete: function () {
                        var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                        var sum_sum = $(this).getCol('taxAmount', false, 'sum');
                        var data = $(this).getRowData();
                        //console.log(data);
                        if (data.length < 6) {
                            $(this).addRowData(data.length + 1, {}, "last");
                        } else if (data.length == 7) {
                            $(this).addRowData(8, {
                                sortNo: "本页小计" ,
                                taxAmount:item.amountSum ,
                                storageNumber: item.totalCount,
                            }, "last");
                        }
                    }
                });
            });
        }else if (zlPrintList && zlPrintList.length > 0) {
            zlPrintList.forEach(function (item, index) {
                var keysList  = item.detailVoList.map(function (val,key) {
                    delete val.id;
                    if(val.salesOutOrderCode){
                        val.sort = key + 1;
                    }
                    return val
                });
                $("#table_b_"+index).jqGrid({
                    data: keysList,
                    datatype: "local",
                    height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                    colNames: ['序号','商品编号</br>药品通用名称（商品名）','规格/</br>型号','剂型','单位','生产厂商', '产地','批号','生产日期','有效期至','数量','单价', '金额',
                         '批准文号/注册证号<br />备案证号','上市许可<br/>持有人','退货</br>原因'],
                    colModel: [{
                        index: 'sortNo',
                        name: 'sortNo',
                        width: 30,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 8) {
                                //金额合计(大写) name
                                return 'colspan=3'
                            }
                        }
                        }, {
                            index: 'productName',
                            name: 'productName',
                            width: 165,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 8) {
                                    //金额合计 name
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'specifications',
                            name: 'specifications',
                            width: 90,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                if (rowId == 8) {
                                    //金额合计 name
                                    return 'style="display:none"'
                                }
                            }
                        }, {
                            index: 'metrologicalSpecification',
                            name: 'metrologicalSpecification',
                            width: 55,
                        }, {
                            index: 'productUnit',
                            name: 'productUnit',
                            width: 35,
                        }, {
                            index: 'manufacturer',
                            name: 'manufacturer',
                            width: 180,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'productOrigin',
                            name: 'productOrigin',
                            width: 65,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        },{
                            index: 'batchCode',
                            name: 'batchCode',
                            width: 75,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'productionTime',
                            name: 'productionTime',
                            width: 80,
                            formatter:function (e) {
                                if(e){
                                    return dateFormat(e, 'yyyy-MM-dd')
                                }else{
                                    return ""
                                }
                            }
                        }, {
                            index: 'periodValidity',
                            name: 'periodValidity',
                            width: 80,
                            formatter:function (e) {
                                if(e){
                                    return dateFormat(e, 'yyyy-MM-dd')
                                }else{
                                    return ""
                                }
                            }
                        }, {
                            index: 'storageNumber',
                            name: 'storageNumber',
                            width: 40,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'taxPrice',
                            name: 'taxPrice',
                            width: 55,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'taxAmount',
                            name: 'taxAmount',
                            width: 60,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'approvalNumber',
                            name: 'approvalNumber',
                            width: 130,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'marketAuthor',
                            name: 'marketAuthor',
                            width: 70,
                            cellattr: function (rowId, tv, rawObject, cm, rdata) {
                                return 'style="word-wrap:break-word"'
                            }
                        }, {
                            index: 'returnReason',
                            name: 'returnReason',
                            width: 35,
                        }],
                        shrinkToFit:false,
                        rowNum: 7,
                        gridview: true,
                        gridComplete: function () {
                            var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                            var sum_sum = $(this).getCol('taxAmount', false, 'sum');
                            var data = $(this).getRowData();
                            //console.log(data);
                            if (data.length < 6) {
                                $(this).addRowData(data.length + 1, {}, "last");
                            } else if (data.length == 7) {
                                $(this).addRowData(8, {
                                    sortNo: "本页小计" ,
                                    taxAmount:item.amountSum ,
                                    storageNumber: item.totalCount,
                                }, "last");
                            }
                        }
                });
            });
        }


        if(printType==0){
            /* 打印预览 */
            utils.dialog({
                title:'预览',
                width:$(parent.window).width()-100,
                height:$(parent.window).height() * 0.7,
                content:$('#big_box').html(),
                okValue:'确定',
                ok:function () {}
            }).showModal();
            window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
        }else if(printType==1){
            /* 打印 */
            $("#box").jqprint({
                globalStyles: true, //是否包含父文档的样式，默认为true
                mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                stylesheet: null, //外部样式表的URL地址，默认为null
                noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                append: null, //将内容添加到打印内容的后面
                prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                deferred: $.Deferred() //回调函数
            });
        }
    }
});