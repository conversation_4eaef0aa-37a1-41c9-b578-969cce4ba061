var saveUrl="/proxy-product/product/baseApproval/saveBaseChangeApply";
var editUrl="/proxy-product/product/baseApproval/editBaseApprovalDo";
var auditUrl="/proxy-product/product/baseApproval/auditBaseApprovalDo";
var submitAuditAgainUrl="/proxy-product/product/baseApproval/submitAuditAgain";
//商品助记码
var productNameMnemonicCodeBefore="";
//通用名助记码
var commonNameMnemonicCodeBefore="";

var instructions = [];
var instructionsChangeBeforeHtml="";//修改前的说明书
var instructionsChangeAfterHtml="";//修改后的说明书
var largeCategorySubmit=false;//修改后保存商品大类
var firstCategoryBefore = "";
var firstCategoryBeforeVal = "";
var secondCategoryBefore = "";
var secondCategoryBeforeVal = "";
var thirdCategoryBefore = "";
var thirdCategoryBeforeVal = "";
var entryTaxRateBefore = "";
var salesRateBefore = "";
var largeCategoryBeforeVal="";
var largeCategoryAfterVal="";

var pageType=$("#pageType").val();
var largeCategoryArray = new Array();
largeCategoryArray[0] = "中药饮片";
largeCategoryArray[1] = "食品";
largeCategoryArray[2] = "保健食品";
largeCategoryArray[3] = "中药材";
largeCategoryArray[4] = "药食同源";
$(function () {
    $('div[fold=head]').fold({
        sub: 'sub'
    });

    //养护类型
    $('input[name=keyConservationCategories]').click(function () {
        ($(this).val() == '1')?$('#maintenancePeriod').val('30'):$('#maintenancePeriod').val('90')
    })
    //tabs输入框只能输入数字限制
    $(document).on("input",".tagdiv .bootstrap-tagsinput input",function(){
        this.value=this.value.replace(/\D/g,'');
        $(this).attr("maxlength",15);
    });
    $(document).on("input","#inputdia input",function(){
        if($(this).hasClass('NAN_TYPE_CLASS')){ // 允许汉字类型

        }else{
            this.value=this.value.replace(/\D/g,'');
        }
        $(this).attr("maxlength",15);
    });
    $('body').on('click', '.cEdit', function (){
        var $t = $(window.changeApply_e); //找到对应节点
        var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
        console.log($t_flag.attr('changeapplyflag'));
        var name = $t_flag.attr('name');
        if("productName"==name) {//商品名
            //保存商品名助记码
            var btn_save = $('#id_productName').find('.changeApplyBtn');
            $(btn_save).unbind("click"); //移除click
            $(btn_save).on('click',function() {
                delete window.changeApply["productNameMnemonicCode"];
                var productName = $("#productName").val();
                var productNameMnemonicCode = getMnemonicCode(productName);
                if(productNameMnemonicCode!=""){
                    var productNameMnemonicCodeObj = {
                        attribute:"productNameMnemonicCode",
                        changeBefore:productNameMnemonicCodeBefore,
                        changeAfter: productNameMnemonicCode,
                        afterText:productNameMnemonicCode,
                        changeType: '1'
                    }
                    window.changeApply["productNameMnemonicCode"] = productNameMnemonicCodeObj;
                }
            })
        }else if("commonName"==name) {//保存通用名
            var btn_save = $('#id_commonName').find('.changeApplyBtn');
            $(btn_save).unbind("click"); //移除click
            $(btn_save).on('click',function() {
                //保存通用名助记码
                delete window.changeApply["commonNameMnemonicCode"];
                var commonName = $("#search_commodity").val();
                var commonNameMnemonicCode = getMnemonicCode(commonName);
                if(commonNameMnemonicCode!=""){
                    var commonNameMnemonicCodeObj = {
                        attribute:"commonNameMnemonicCode",
                        changeBefore:commonNameMnemonicCodeBefore,
                        changeAfter: commonNameMnemonicCode,
                        afterText:commonNameMnemonicCode,
                        changeType: '1'
                    }
                    window.changeApply["commonNameMnemonicCode"] = commonNameMnemonicCodeObj;
                }
            })
        }else if("entryTaxRate"==name){//进项税率
            $("div#id_entryTaxRate .productrate").hide();
            // // 进项税率是否正在编辑状态
            let editState_entryTaxRate = $('#id_entryTaxRate').find('.changeApplyBtn').css('display');
            // // let hasFourRadioLab = ['药食同源', '食品', '中药饮片', '中药材', '保健食品'];
            let bool = largeCategoryArray.some((item) => {
                return item == (changeApply['largeCategory']?changeApply['largeCategory']['afterText']:(largeCategoryAfterVal?largeCategoryAfterVal:$('#largeCategoryVal').attr('data-value')))  //
            });
            if(bool){ // 如果当前商品大类选中项为这几项 需要显示 9% 的选项
               // if(editState_entryTaxRate != 'none'){ // 进项税率 编辑状态
                    $('#id_entryTaxRate').find('.productrate').show()
                //}
            }else{ // 如果当前商品大类选中项不为这几项，需要隐藏掉9% 的选项
                // if(editState_entryTaxRate != 'none'){ //当前进项税率 又处于编辑状态 设置13%项为默认选中项
                //     $('#id_entryTaxRate').find('[name=entryTaxRate]').eq(-1).prop('checked',true);
                // }
                $('#id_entryTaxRate').find('.productrate').hide()
            }
        }else if("salesRate"==name){//销项税率
            $("div#id_salesRate .productrate").hide();
            // 销项税率是否正在编辑状态
            let editState_salesRate = $('#id_salesRate').find('.changeApplyBtn').css('display');
            let bool = largeCategoryArray.some((item) => {
                return item == (changeApply['largeCategory']?changeApply['largeCategory']['afterText']:(largeCategoryAfterVal?largeCategoryAfterVal:$('#largeCategoryVal').attr('data-value')))  //
            });
            if(bool){ // 如果当前商品大类选中项为这几项 需要显示 9% 的选项
                //if(editState_salesRate != 'none'){ // 销项税率 编辑状态
                    $('#id_salesRate').find('.productrate').show()
                //}
            }else{ // 如果当前商品大类选中项不为这几项，需要隐藏掉9% 的选项
                // if(editState_salesRate != 'none'){ // 销项税率 编辑状态 设置13%项为默认选中项
                //     $('#id_salesRate').find('[name=entryTaxRate]').eq(-1).prop('checked',true);
                // }
                $('#id_salesRate').find('.productrate').hide()
            }
        }else if('keyConservationCategories' === name){ // 养护类型
            var oldVal =  $('#maintenancePeriod').val();
            var btn_save = $('input[name=keyConservationCategories]').parent().find('.changeApplyBtn');
            $(btn_save).unbind("click");
            $(btn_save).on('click',function() {
                $('#maintenancePeriod').val(oldVal)
            });
        }


        var changeapplyflag = $t_flag.attr('changeapplyflag');
        if("largeCategory"==changeapplyflag){//商品大类
            //说明书可编辑
            $('#insForm textarea').removeAttr('disabled');
            $('#insForm input').removeAttr('disabled');
            largeCategorySubmit=false;
            var btn_save_largeCategory = $('#id_largeCategory').find('.changeApplyBtn');
            $(btn_save_largeCategory).unbind("click"); //移除click
            $(btn_save_largeCategory).on('click',function() {
                delChangeApply("salesRate");
                delChangeApply("entryTaxRate");
                $('input[name="salesRate"]').nextAll('.yulanInput').removeClass('yulanInput_after');
                $('input[name="entryTaxRate"]').nextAll('.yulanInput').removeClass('yulanInput_after');
                saveInstructions();
            })
        }else  if("firstCategory"==changeapplyflag) {//一级分类
            saveFirstCategory();
        }else  if("secondCategory"==changeapplyflag) {//二级分类
            saveSecondCategory();
        }else if("thirdCategory"==changeapplyflag){//三级分类
            var btn_save_largeCategory = $('#div_thirdCategory').find('.changeApplyBtn');
            $(btn_save_largeCategory).unbind("click"); //移除click
            $(btn_save_largeCategory).on('click',function() {
                //删除已修改的一级分类和二级分类
                delChangeApply("firstCategory");
                delChangeApply("secondCategory");
            })
        }
    })
    //删除按钮
    $('body').on('click', '.cDelete', function (){
        var $t = $(window.changeApply_e); //找到对应节点
        var $t_flag = $t.siblings('[changeapplyflag]'); //标记元素
        console.log($t_flag.attr('changeapplyflag'));
        var name = $t_flag.attr('name');
        if("productName"==name) {//商品名
            //删除商品名助记码
            delChangeApply("productNameMnemonicCode");
            return;
        }else if("commonName"==name) {//通用名
            //删除通用名助记码
            delChangeApply("commonNameMnemonicCode");
            return;
        }
        var changeapplyflag = $t_flag.attr('changeapplyflag');
        if("firstCategory"==changeapplyflag||"secondCategory"==changeapplyflag||"thirdCategory"==changeapplyflag){//二级分类
            delChangeApply("firstCategory");
            delChangeApply("secondCategory");
            delChangeApply("thirdCategory");
        }
    })
    //查看修改后的说明书
    $("#showInstructions").on('click',function (){
        var content=instructionsChangeAfterHtml;
        var width = 1000;
        //展示变更
        if (content == '') {
            utils.dialog({
                title: '　提示',
                content: '没有变更数据',
                quickClose: true,
                timeout: 2500
            }).show();
        } else {
            var a='';
            $(content).clone().each(function(i,v){
                $(v).find('input,textarea').attr('disabled','disabled');
                a+=$(v).prop('outerHTML');
            });
            utils.dialog({
                align: 'top',
                title: '　变更数据',
                width: width,
                content: a,
                quickClose: true
            }).show();
        }
    });
    var baseProductId=$("#baseProductId").val();//主数据主键ID
    readOnly();
    $("#changeApplyBtn").hide();
    $(".rowBtn").attr("disabled","disabled");
    //非申请页，回显商品数据
    if (pageType != 0){
        if (baseProductId!=""){
            obtainProductBaseInfo(baseProductId);
        }
        //隐藏搜索图标及不可搜索
        $(".glyphicon-search").hide();
        $("#search_commodity").attr('disabled','disabled');
    }
        //工作流
        var workUrl="";
        if (pageType == 0 || pageType == 1){
            workUrl="/proxy-product/product/purchaseLimit/queryTotle?key="+$("#workProcessKey").val();
        }
        if (pageType==2||pageType==3||pageType==4){
            workUrl = "/proxy-product/product/purchaseLimit/queryTotle?processInstaId="+$("#processId").val();
        }
        //获取审核流程数据
        $.ajax({
            type: "POST",
            url: workUrl,
            async: false,
            success: function (data) {
                if (data.code==0){
                    $('.flow').process(data.result);
                }else {
                    utils.dialog({content: '流程图加载失败', quickClose: true, timeout: 2000}).showModal();
                }
            }
        });

    //首营新增商品名可输入
    if(baseProductId==""){
        //首营新增可输入商品名查询
        $("#search_commodity").removeAttr('disabled');
    }
    //首营申请默认值
    radioDefaultChecked();
    //根据是否委托判断委托厂家是否显示
    $("input[name='entrustmentProduction']").click(function(){
        if ($(this).val()==0){
            $("#entrustmentManufacturer").val("");
            $("#entrustmentManufacturerVal").val("");
            $("#entManufacturerDiv").hide();
        }else {
            $("#entManufacturerDiv").show();
        }
    })

    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //多选框选中(重点养护类别、特殊属性)
    loadData("keyConservationCategories");
    loadData("specialAttributes");
    loadData("storageAttribute");
    function loadData(key) {
        var tagName, type, arr, thisVal;
        var value = $("#"+key).val();
        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (type == 'checkbox') {
                if ($.type(value) == 'array') {
                    arr = value;
                } else if ($.type(value) == 'string') {
                    arr = value.split(',');
                }
                for (var i = 0; i < arr.length; i++) {
                    if (thisVal == arr[i]) {
                        $(this).attr('checked', true);
                        break;
                    }
                }
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //保存草稿
    $("#saveRowData").on("click", function () {
        $("#statues").val(0);
        if (pageType==0){
            butSubmint(saveUrl);
        }
        if (pageType==1){
            butSubmint(editUrl);
        }
    });
    //提交审核
    $("#submitAssert").on("click", function () {
            //提交审核
                $("#statues").val(1);
                if (pageType==0){
                    butSubmint(saveUrl);
                }
                if (pageType==1){
                    butSubmint(editUrl);
                }
    });
    //关闭按钮
    $("#closePage").on("click", function () {
        var d =  dialog({
            title: "提示",
            content: "是否保存草稿？",
            width:300,
            height:30,
            okValue: '保存草稿',
            button:[
                {
                    value:'关闭',
                    callback:function(){
                        utils.closeTab();
                    }
                }
            ],
            ok: function () {
                $("#statues").val(0);
                if(pageType==0){
                    butSubmint(saveUrl);
                }
                if (pageType==1){
                    butSubmint(editUrl);
                }
                utils.closeTab();
            }
        }).showModal();
    });
    function getMnemonicCode(str){
        var mnemonicCode = "";
        $.ajax({
            url:'/proxy-product/product/productFirst/getMnemonicCode?name='+str,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                if(data.code == 0)
                {
                    mnemonicCode = data.result;
                }
            }
        })
        return mnemonicCode;
    }

    //包装单位
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querypackingunitnotpage",{paramName:"packingName"},"packingUnit",{data:"packingId",value:"packingName"});
    //剂型
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querydosenotpage",{paramName:"doseName"},"dosageForm",{data:"doseid",value:"dosename"});

    //存储条件
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":2}},"storageConditions",{data:"id",value:"name"},"",
        function (result) {});
    //处方分类
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":6}},"prescriptionClassification",{data:"id",value:"name",numCode:"numCode"},""
        ,function (result) {
            $("#prescriptionClassificationCode").val(result.numCode);
        },function () {
            $("#prescriptionClassificationCode").val("");
        });
    //所属经营范围
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommodityscope",{paramName:'scopeName', params:{"orgCode":"001"}},"scopeOfOperation",{data:"simpleCode",value:"scopeName"},""
        ,function (result) {
            localBatchName(result.data);
        });
    //商品大类
    //商品大类
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querycommonnotpage",{paramName:'CommonName', params:{"type":7}},"largeCategory",{data:"id",value:"name"},""
        ,function (result) {
                if(largeCategorySubmit){
                    $("#insForm").html(instructionsChangeBeforeHtml);
                    //加载说明书
                    $('#insForm textarea').attr('disabled','disabled');
                    $('#insForm input').attr('disabled','disabled');
                    return false;
                }
                largeCategorySubmit=false;
                $('#insForm textarea').removeAttr('disabled');
                $('#insForm input').removeAttr('disabled');
                largeCategoryAfterVal=result.value;//修改后的值
                $.ajax({
                    type:"post",
                    url: "/proxy-sysmanage/sysmanage/dict/queryexplaintemplatenotpage",
                    async : false,
                    data:{"commodityType":result.data,"isStop":0},
                    dataType:"json",
                    success: function (data) {
                        console.log(data)
                        if(data.code == 0)
                        {
                            var result = data.result;
                            if(result==null){
                                $("#insForm").html("");
                            }else {
                                var html = getHTML(result[0].explainAttribute);
                                $("#insForm").html(html);
                            }
                        }else{
                            $("#insForm").html('');
                        }
                    },
                    error:function () {
                    }
                });
        },function () {
            $("#insForm").html("");
        });
    // 一级分类 firstCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/queryonecategory",{paramName:'categoryName'},"firstCategory",{data:"id",value:"categoryName",simpleCode:"simpleCode"},""
        ,function (result) {
            $("#firstCategoryCode").val(result.simpleCode);
            var value=$("#firstCategoryVal").val();
            if(value != $("#firstCategoryVal").attr("data-value"))
            {
                $("#secondCategory").val('');
                $("#secondCategoryVal").val('');
                $("#secondCategoryVal").attr('data-value','');
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
            $("#secondCategoryVal").focus(function () {
                if($.trim($("#firstCategoryVal").val()) == '')
                {
                    $(this).blur();
                }
            })
        },function () {
            var value=$("#firstCategoryVal").val();
            if(value != $("#firstCategoryVal").attr("data-value"))
            {
                $("#firstCategoryCode").val("");
                $("#secondCategory").val('');
                $("#secondCategoryVal").val('');
                $("#secondCategoryVal").attr('data-value','');
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
        });
    //二级分类
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querytwocategory",{paramName:'categoryName',params:{"pid":$("#firstCategory").val()}
            ,isSearch:function(params){
                var code=$("#firstCategory").val();
                console.log(code);
                if(code != '')
                {
                    params.pid=code;
                    return params;
                }
                return false;
            }},"secondCategory",{data:"id",value:"categoryName"},""
        ,function (result2) {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
            $("#thirdCategoryVal").focus(function () {
                if($.trim($("#secondCategoryVal").val()) == '')
                {
                    $(this).blur();
                }
            })
        },function () {
            var value=$("#secondCategoryVal").val();
            if(value != $("#secondCategoryVal").attr("data-value"))
            {
                $("#thirdCategory").val('');
                $("#thirdCategoryVal").val('');
                $("#thirdCategoryVal").attr('data-value','');
            }
        });
    //三级分类 thirdCategory
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querythreecategory",{paramName:'categoryName',params:{"pid":$("#secondCategory").val()}
        ,isSearch:function(params){
            var code=$("#secondCategory").val();
            if(code != '')
            {
                params.pid=code;
                return params;
            }
            return false;
        }},"thirdCategory",{data:"id",value:"categoryName"});
    //加载字典值
    showDictValue();
    //审核 是否显示搜索图标
    // var applyType=$("#applyType").val();
    // if(applyType==""){
        $("#search_commodity").dblclick(function () {
            commodity_search_dia();
        });
    // }else if(applyType=="apply"){//审核页面隐藏商品搜索图标
    //     $(".glyphicon-search").hide();
    // }
    //审核按钮
    $('.auditPass').on('click', function () {
        $('#auditOpinion').val('');
        //操作类型（0通过，1 驳回，2 关闭）
        var status=this.getAttribute("status");
        var title="审核通过";
        let statusTitle = new Map([
            [1,'审核不通过'],
            [2,'关闭审核'],
            ['default','审核通过']
        ]);
        title = statusTitle.get(Number(status)) || statusTitle.get('default');
        (status==1)?$('#opinion').show():$('#opinion').hide();

        if (status!=2){
            utils.dialog({
                title:title,
                content: $('#container'),
                okValue: '确定',
                ok: function () {
                    //审核不通过，意见不能为空
                    if (status==1){
                        if ($("#auditOpinion").val()=="") {
                            utils.dialog({content: '审批意见不能为空!', quickClose: true, timeout: 2000}).showModal();
                            return false;
                        }
                    }
                    submitAuditInfo(status,$("#auditOpinion").val());
                },
                cancelValue: '取消',
                cancel: function () {
                    $("#auditOpinion").val("");
                }
            }).showModal();
        }else {
            title="关闭审核";
            utils.dialog({
                title:title,
                width:300,
                height:30,
                okValue: '确定',
                content: "确定关闭此申请？",
                ok: function () {
                    submitAuditInfo(status,"");
                },
                cancelValue: '取消',
                cancel: function () {}
            }).showModal();
        }
    });
    $('#submitAuditAgain').on('click', function () {
        utils.dialog({
            title:"提交审核",
            width:300,
            height:30,
            okValue: '确定',
            content: "确定提交申请？",
            ok: function () {
                $("#statues").val(1);
                butSubmint(submitAuditAgainUrl+"?taskId="+$("#taskId").val())
            },
            cancelValue: '取消',
            cancel: function () {}
        }).showModal();
    })

    if (pageType==0){
        //开启变更事件
        $.changApply_insertData({
            name: 'attribute',
            status: 'changeType',
            afterValue: 'changeAfter',
            beforeValue: 'changeBefore'
        });  //按钮id：changeApplyBtn
    }else {
        $.ajax({
            url:'/proxy-product/product/orgApproval/getPropertyChangeDetail?correlationId='+$('#approvalRecordId').val()+"&type=0",
            type:'get',
            dataType:'json',
            success:function(data){
                if(data!=null&&data!=undefined){
                    var obj={};
                    $.each(data.result,function (i, v) {
                        v.changeType=-1;
                        obj[v.attribute]=v;
                        if(v.attribute=="largeCategory"){
                            largeCategoryAfterVal=v.afterText;
                        }
                    });
                    //编辑页面
                    if (pageType==1||pageType==4){
                        //开启变更事件
                        $.changApply_insertData({
                            name: 'attribute',
                            status: 'changeType',
                            afterValue: 'changeAfter',
                            beforeValue: 'changeBefore'
                        });  //按钮id：changeApplyBtn
                        window.changeApply=obj;
                        window.changeApplyBak=obj;
                    }
                    //审核页面
                    if (pageType==2||pageType==3){
                        window.changeApply=obj;
                        $.changeApply_selectData(obj, {
                            name: 'attribute',
                            status: 'changeType',
                            afterValue: 'changeAfter',
                            beforeValue: 'changeBefore'
                        });
                    }
                    if(pageType!=0){
                        //加载修改后的说明书
                        $.ajax({
                            type:"post",
                            url: "/proxy-product/product/productInstruction/toList",
                            async : false,
                            data:{"type": 1,"correlationId": $('#approvalRecordId').val()},
                            success: function (data) {
                                var list = data.result;
                                if(list!=undefined&&list.length>0){
                                    instructionsChangeAfterHtml=getProductHTML(data.result);
                                    $("#showInstructions").show();
                                    for (var i = 0; i < list.length; i++) {
                                        var javaObj = {};
                                        javaObj["id"] = list[i].id;
                                        javaObj["attribute"] = list[i].attribute;
                                        javaObj["attributeValue"] = list[i].attributeValue;
                                        javaObj["checked"] = list[i].checked;
                                        javaObj["controlTypes"] = list[i].controlTypes;
                                        javaObj["attributeRequired"] = list[i].attributeRequired;
                                        instructions.push(javaObj);
                                    }
                                }
                            },
                            error:function () {}
                        });
                    }
                }
            }
        })
    }
    // 温度范围 onblur校验
    $('[name=temperatureRange]').each(function (index) {
        $(this).on('blur',function () {
            if($(this).val() != ''){
                var that = $(this)
                if(Number($(this).val()) <= 0 || $(this).val().indexOf('.') > 0){
                    utils.dialog({
                        title:'提示',
                        content: '温度范围的值必须为大于0的正整数.',
                        okValue: '确定',
                        ok: function () {
                            setTimeout(function () {
                                that.focus();
                            },200)
                        }
                    }).showModal()
                    return false;
                };
                if($(this).val().indexOf('0') == 0){
                    utils.dialog({
                        title:'提示',
                        content: '温度值的起始值不能为0.',
                        okValue: '确定',
                        ok: function () {
                            setTimeout(function () {
                                that.focus();
                            },200)
                        }
                    }).showModal()
                    return false;
                }
            }
        })
    })
})
//商品大类  保存按钮
function saveInstructions() {
    //清空说明书数组
    largeCategorySubmit=true;//点击提价保存按钮
    $("#showInstructions").show();//显示说名查看图标
    instructions.splice(0,instructions.length);
    $("#insForm .attributeList").each(function () {
            var instructionsId=$(this).find(".instructionsId").val();
            var attributeName=$(this).find(".attributeName").val();
            var checkval=$(this).find(".checkval").val();
            var controlTypes=$(this).find(".controlTypes").val();
            var attributeRequired=$(this).find(".attributeRequired").val();
            var attributeValue='';
            if(controlTypes == '1')
            {
                // 复选框
                var arr=[];
                $(this).find("[name='attributeValue']").each(function(){
                    var checked=this.checked;
                    if(checked)
                    {
                        var value=$.trim($(this).val());
                        arr.push(value);
                        $(this).attr("checked","checked");
                    }else{
                        $(this).removeAttr("checked","checked");
                    }
                })
                attributeValue=arr.join(',');
            }else if(controlTypes == '0'){
                //文本框
                attributeValue=$(this).find("[name='attributeValue']").val();
                $(this).find("[name='attributeValue']").html(attributeValue);
            }

            var javaObj = {};
            javaObj["id"] = instructionsId;
            javaObj["attribute"] = attributeName;
            javaObj["attributeValue"] = attributeValue;
            javaObj["checked"] = checkval;
            javaObj["controlTypes"] = controlTypes;
            javaObj["attributeRequired"] = attributeRequired;
            instructions.push(javaObj);
        })
    $('#insForm textarea').attr('disabled','disabled');
    $('#insForm input').attr('disabled','disabled');
    //记录修改后的数据
    instructionsChangeAfterHtml=$("#insForm").html();
    $("#insForm").html(instructionsChangeBeforeHtml);
    //加载说明书
    $('#insForm textarea').attr('disabled','disabled');
    $('#insForm input').attr('disabled','disabled');
}

function initFileSelected() {
}

/**
 * 加载所属经营范围对应的批准文件
 * @param simpleCode
 */
function localBatchName(simpleCode) {
    $.ajax({
        type:"post",
        url: "/proxy-sysmanage/sysmanage/dict/querycommoditybatchnamebycode",
        async : false,
        data:{"simpleCode":simpleCode},
        dataType:"json",
        success: function (data) {
            var html='<option value="">请选择</option><option value="0">商品附件</option>';//-999
            if(data.code == 0)
            {
                var arr =data.result;
                if(arr!=null){
                    for(var i=0;i<arr.length;i++)
                    {
                        html+='<option value="'+arr[i].batchId+'">'+arr[i].batchName+'</option>';
                    }
                }
            }
            $("select[name='batchName']").html(html);
        },
        error:function () {
            utils.dialog({content: '加载失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}


/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
var Is_ZYYP_flag = false; // 商品大类是否为中药饮片
// function forEachIngFun(obj,s) {
//     $(obj).each(function (index,item) {
//         $('input[name'+item.name+']').parents('.input-group').find('div').eq(0).html(s=='R'?item.title:('<i class="text-require">*  </i>'+item.title));
//     })
// }
// function forEachTestFun(obj,s) {
//     $(obj).each(function (index,item) {
//         $('input[name'+item.name+']').parents('.input-group').find('div').eq(0).html(s=='R'?('<i class="text-require">*  </i>'+item.title):item.title);
//     })
// }
function valAutocomplete(url,param,obj,resParam,querydelimiter,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    var options={
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        querydelimiter:querydelimiter,
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
            $("#"+obj+"Val").attr("title",result.value);
            var ingObjArr = [
                {name:'indate',title:'有效期'},
                {name:'maintenancePeriod',title:'养护周期'},
                {name:'approvalNumber',title:'批准文号'},
                {name:'standardProductId',title:'标准库ID'},
                {name:'entrustmentProduction',title:'是否委托生产'}
            ];
            var testObjArr = [
                {name:'producingArea',title:'产地'},
                {name:'qualityStandard',title:'质量标准'}
            ];
            if(obj == 'largeCategory'){
                if(result.value == '中药饮片'){
                    Is_ZYYP_flag = true;
                    // forEachIngFun(ingObjArr,'R')
                    // forEachTestFun(testObjArr,'R');

                    $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                    $('#maintenancePeriod').parents('.input-group').find('div').eq(0).find('i').remove(); // 养护周期
                    $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                    $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                    $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                    $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                    $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                }else{
                    if(Is_ZYYP_flag){
                        // forEachIngFun(ingObjArr,'A')
                        // forEachTestFun(testObjArr,'A');

                        $('input[name=indate]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>有效期') // 有效期
                        $('#maintenancePeriod').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>养护周期'); // 养护周期
                        $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>批准文号') // 批准文号
                        $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>标准库ID') // 标准库ID
                        $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>是否委托生产')//是否委托生产
                        $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('产地') // 产地
                        $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('质量标准') // 质量标准
                    }
                }
            }

        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    }
    if(param.isSearch)
    {
        options.isSearch=param.isSearch;
    }
    $("#"+obj+"Val").Autocomplete(options);
}

//制保留2位小数，如：2，会在2后面补上00.即2.00
function toDecimal2(x) {
    var f = parseFloat(x);
    if (isNaN(f)) {
        return false;
    }
    var f = Math.round(x*100)/100;
    var s = f.toString();
    var rs = s.indexOf('.');
    if (rs < 0) {
        rs = s.length;
        s += '.';
    }
    while (s.length <= rs + 2) {
        s += '0';
    }
    return s;
}
/**
 * 设置输入两位小数 并且控制可输入的最大值的位数
 *
 * obj this对象
 * size 最大值位数 可不传
 * */
function setFixedTwo(obj,size) {
    var value=obj.value;
    var n='';
    if(size)
    {
        for(var i=0;i<size;i++)
        {
            n+='9';
        }
        n+='.99';
        if(Number(value) > Number(n))
        {
            value=n;
        }
    }
    obj.value=value.replace(/[^\d.]/g,'').replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');
}
/**
 *首营默认选中值设置
 */
function radioDefaultChecked() {
    //基本属性默认值
    //养护周期默认值
    if($("#maintenancePeriod").val()==''){
        $("#maintenancePeriod").val(90);
    }
    //有效期
    if(!$("#indateType").is(":checked")){
        $("#indateType").val("月");
    }
    //是否显示委托厂家处理
    setRadioChecked('entrustmentProduction','0');
    initChecked();
    // 是否税务优惠
    setRadioChecked('taxIncentives','0');
    //进项税率
    setRadioChecked('entryTaxRate','1');
    //销项税率
    setRadioChecked('salesRate','1');
    //限销状态
    setRadioChecked('baseLimitedPinState','0');
    //限采状态
    setRadioChecked('baseLimitedProductionState','0');
    //停用状态
    setRadioChecked('baseDisableState','0');
}
function setRadioChecked(name,value){
    var obj=$("input[name="+name+"]");
    if($("input[name="+name+"]:checked").length < 1)
    {
        obj.each(function () {
            var val=this.value;
            if(val == value)
            {
                $(this).prop("checked",true);
            }
        })
    }
}
function  readOnly() {
    //按钮置灰
   $('.only-read').attr('disabled','disabled');
}
function  unReadOnly() {
    $(".only-read").each(function(){
        $(this).removeAttr('disabled');
    });
}
/**
 * 搜索商品主数据
 * @returns {boolean}
 */
function commodity_search_dia() {
    //搜索商品置空变更数据
    window.changeApply={};
    var keyword=$("#search_commodity").val();
    var applicationCode=$("#applicationCode").val();
    dialog({
        url: '/proxy-product/product/baseApproval/toSearchList',
        title: '商品列表',
        width: $(window).width() * 0.8,
        height: $(window).height() * 0.8,
        data: {"productName":keyword,"disableState":0}, // 给modal 要传递的 的数据
        onclose: function () {
            if (this.returnValue) {
                var data = this.returnValue;
                //加载主数据信息
                var productId=data.productBaseId;
                $("#baseProductId").val(productId);
                obtainProductBaseInfo(productId)
            }
            //申请编号不能修改
            $("#applicationCode").val(applicationCode);
            $("#search_commodity").attr('disabled','disabled');
        },
        oniframeload: function () {
        }
    }).showModal();
    return false;
};

/**
 * 主数据内容回显
 * @param json
 */
function loadProductData(json) {
    var obj = json;
    var key, value, tagName, type, arr, thisVal;
    for (x in obj) {
        key = x;
        value = obj[x];

        $("[name='" + key + "']").each(function () {
            tagName = $(this)[0].tagName;
            type = $(this).attr('type');
            thisVal = $(this).val();
            if (tagName == 'INPUT') {
                if (type == 'radio') {
                    $(this).prop('checked', thisVal == value);
                } else if (type == 'checkbox') {
                    if ($.type(value) == 'array') {
                        arr = value;
                    } else if ($.type(value) == 'string') {
                        arr = value.split(',');
                    }
                    if(arr!=null){
                        for (var i = 0; i < arr.length; i++) {
                            if (thisVal == arr[i]) {
                                $(this).prop('checked', true);
                                break;
                            }
                        }
                    }
                } else {
                    $(this).val(value);
                    $(this).attr('title',value);
                }
            } else if (tagName == 'SELECT' || tagName == 'TEXTAREA') {
                $(this).val(value);
            }
        });
    }
    //input小标签回填
    $('input[data-role="tagsinput"]').each(function () {
        $(this).tagsinput('add', $(this).val());
    })
    //生产厂家名称
    $("#manufacturerVal").val("");
    //加载字典值
    showDictValue();
    //受托厂家是否显示
    initChecked();
}
//判断委托产家是否显示
function initChecked(){
    var type=$("input[name='entrustmentProduction']:checked").val();
    if(type == 1)
    {
        $("#entManufacturerDiv").show();
    }else{
        $("#entManufacturerDiv").hide();
    }

};


/**
 * 商品主数据带回说明书
 * @param arr
 * @returns {string}
 */
function getProductHTML(arr) {
    arr = arr == null ? [] : arr;
    var left = [], right = [];
    var html = '';
    for (var i = 0; i < arr.length; i++) {
        var type = arr[i].controlTypes;
        if (type == 1) {
            //复选框
            left.push(arr[i]);
        } else {
            //文本框/
            right.push(arr[i]);
        }
    }
    var list = left.concat(right);
    for (var i = 0; i < list.length; i++) {
        var cType = list[i].controlTypes;
        var attr=list[i].checked;
        var attributeRequired = list[i].attributeRequired;
        if(attributeRequired==null||attributeRequired=="null"){
            attributeRequired="";
        }
        if (cType == 0) {
            //文本框
            html += '<div class="row attributeList">\n' +
                '        <div class="input-group">\n' +
                '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="'+attributeRequired +'">' +
                '           <input type="hidden" class="checkval" name="checked" value="'+attr+'">' +
                '           <input type="hidden" class="controlTypes" name="controlTypes" value="0">' +
                '           <input type="hidden" class="instructionsId" name="id" value="'+list[i].id+'">' +
                '           <label class="input-group-addon">';
            if(Number(attributeRequired) && Number(attributeRequired) == 1)
            {
                //必填
                html+='<i class="text-require" style="color:red;display: contents;">*  </i>';
            }
            html+= list[i].attribute + ':<input type="hidden" class="attributeName" name="attribute" value="' + list[i].attribute + '" /></label>' +
                '           <textarea class="form-control attributeValue only-read';
            if(Number(attributeRequired) && Number(attributeRequired) == 1)
            {
                //必填
                html+=' {validate:{ required :true}}';
            }
            html+='" name="attributeValue" >' + list[i].attributeValue + '</textarea>\n' +
                '       </div>\n' +
                '</div>';

        } else if (cType == 1) {
            //复选框
            html += '<div class="row attributeList">\n' +
                '        <div class="input-group">\n' +
                '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="'+attributeRequired +'">' +
                '           <input type="hidden" class="checkval" name="checked" value="'+attr+'">' +
                '           <input type="hidden" class="controlTypes" name="controlTypes" value="1">' +
                '           <input type="hidden" class="instructionsId" name="id" value="'+list[i].id+'">' +
                '           <label class="input-group-addon">';
            if(Number(attributeRequired) && Number(attributeRequired) == 1)
            {
                //必填
                html+='<i class="text-require" style="color:red;display: contents;">*  </i>';
            }
            html+= list[i].attribute + ':<input type="hidden" class="attributeName" name="attribute" value="' + list[i].attribute + '" /></label>' +
                '               <div class="form-control" style="height:auto;min-height:34px;">\n' +
                '                       <div class="checkbox" style="margin:0;">';
            var attrVal = list[i].checked;
            if (attrVal && attrVal != '') {
                var item = attrVal.split(',');//把复选框属性转换为数组
                var attributeValue=list[i].attributeValue;
                var checkList=attributeValue.split(',');
                for (var j = 0; j < item.length; j++) {

                    html += '<label style="margin-right: 14px;"><input type="checkbox" ';
                    if(checkList && checkList.length > 0)
                    {
                        for(var x=0;x<checkList.length;x++)
                        {
                            if(checkList[x] == item[j])
                            {
                                html+='checked="checked"';
                                break;
                            }
                        }
                    }
                    html+=' name="attributeValue" value="' + item[j] + '" class="only-read">' +
                        '                  ' + item[j] + '</label>';

                }
            }
            html += '              </div>\n' +
                '               </div>' +
                '           </div>\n' +
                '       </div>';
        }
    }
    return html;
}

/**
 * 根据说明书模板显示
 * @returns {string}
 */
function getHTML(arr) {
    arr = arr == null ? [] : arr;
    var left = [], right = [];
    var html = '';
    for (var i = 0; i < arr.length; i++) {
        var type = arr[i].attributeType;
        if (type == 1) {
            //复选框
            left.push(arr[i]);
        } else {
            //文本框
            right.push(arr[i]);
        }
    }
    var list = left.concat(right);
    for (var i = 0; i < list.length; i++) {
        var cType = list[i].attributeType;
        var attr=list[i].attributeName;
        if(attr && attr != '')
        {
            var require=list[i].attributeIsrequired;
            if (cType == 0) {
                //文本框
                html += '<div class="row attributeList">\n' +
                    '        <div class="input-group">\n' +
                    '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="'+require+'">' +
                    '           <input type="hidden" class="checkval" name="checked" value="">' +
                    '           <input type="hidden" class="controlTypes" name="controlTypes" value="0">' +
                    '           <input type="hidden" class="instructionsId" name="id" value="">' +
                    '           <label class="input-group-addon">';
                if(Number(require) == 1)
                {
                    //必填
                    html+='<i class="text-require" style="color:red;display: contents;">*  </i>';
                }
                html+= attr + ':<input type="hidden" class="attributeName" name="attribute" value="' + attr + '" /></label>' +
                    '           <textarea class="form-control attributeValue';
                if(Number(require) == 1)
                {
                    //必填
                    html+=' {validate:{ required :true}}';
                }
                html+='" name="attributeValue">' + list[i].attributeDefaultvalue + '</textarea>\n' +
                    '       </div>\n' +
                    '</div>';
            } else if (cType == 1) {
                //复选框
                var attrVal = list[i].attributeDefaultvalue;
                html += '<div class="row attributeList">\n' +
                    '        <div class="input-group">\n' +
                    '           <input type="hidden" class="attributeRequired" name="attributeRequired" value="'+list[i].attributeIsrequired +'">' +
                    '           <input type="hidden" class="checkval"  name="checked" value="'+attrVal+'">' +
                    '           <input type="hidden" class="controlTypes" name="controlTypes" value="1">' +
                    '           <input type="hidden" class="instructionsId" name="id" value="">' +
                    '           <label class="input-group-addon">';
                if(Number(require) == 1)
                {
                    //必填
                    html+='<i class="text-require" style="color:red;display: contents;">*  </i>';
                }
                html+= attr + ':<input type="hidden" class="attributeName" name="attribute" value="' + attr+ '" /></label>' +
                    '               <div class="form-control" style="height:auto;min-height:34px;">\n' +
                    '                       <div class="checkbox" style="margin:0;">';
                if (attrVal && attrVal != '') {
                    var item = attrVal.split(',');//把复选框属性转换为数组
                    for (var j = 0; j < item.length; j++) {
                        html += '<label style="margin-right: 14px;"><input type="checkbox" name="attributeValue" value="' + item[j] + '" class="only-read">' + item[j] + '</label>';
                    }
                }
                html += '              </div>\n' +
                    '               </div>' +
                    '           </div>\n' +
                    '       </div>';
            }
        }
    }
    return html;
}
var checkSubmitFlag= false;
function checkSubmit(){
    if (checkSubmitFlag==true){
        return false;
    }
    checkSubmitFlag=true;
    return true;
}

function  valTest_area_Stand(name,title) {
    var flag = true;
    if(changeApply[name]){ // 先判断name是否有修改
        if(changeApply[name]['afterText'] == undefined){
            diaFun(title)
            flag = false;
        }else if(changeApply[name]['afterText'] == ''){
            diaFun(title)
            flag = false;
        }
    }else{
        if(name == 'producingArea'){ // 产地
            if($('input[name='+name+']').prev().find('span').length < 1){ // 如果没有修改，那name的原始数据也应该有值，才可以提交审核
                diaFun(title)
                flag = false;
            }
        }
        if(name == 'qualityStandard'){ // 质量标准
            if($('input[name='+name+']').val() == ''){
                diaFun(title)
                flag = false;
            }
        }
    }
    return flag;
}

function valMust_area_stand(name,title) {
    var flag = true;
    if(window.changeApply[name]){
        if(window.changeApply[name]['afterText'] == undefined){
            diaFun(title)
            flag = false;
        }else if(window.changeApply[name]['afterText'] == ''){
            diaFun(title)
            flag = false;
        }
    }else{
        if($('input[name='+name+']').val() == ''){
            diaFun(title)
            flag = false;
        }
    }
    return flag;
}
function  diaFun(title) {
    parent.hideLoading();
    utils.dialog({
        title:'提示',
        content: title+'信息不能为空。',
        okValue:'确定',
        ok:function () {}
    }).showModal();
    return false;
}
/**
 * 保存数据
 */
function  butSubmint(url) {
    /*if (!checkSubmit()){
        return false;
    }*/
    //商品编码为空，未选择商品验证
    if ($("#commodity_code").val()==""){
        utils.dialog({content: '请先选择商品', quickClose: true, timeout: 2000}).showModal();
        parent.hideLoading();
        return false;
    }
    var productDataVo=getSavedData();
    //涉税字段变更
    productDataVo.baseApprovalRecordVo.applyType=2;
    // 提交审核 判断是否有修改
    if ($("#statues").val()==1){
        if(productDataVo.propertyApprovalDetails&&productDataVo.propertyApprovalDetails.length<1
            &&productDataVo.instructionsList&&productDataVo.instructionsList.length<1){
            utils.dialog({content: '请在进行过修改后，再提交审核', quickClose: true, timeout: 2000}).showModal();
            parent.hideLoading();
            return false;
        }
        //商品大类修改，判断税率是否必须修改
        if(window.changeApply['largeCategory'] != undefined&&largeCategoryArray.indexOf(window.changeApply['largeCategory']['changeAfter'])==-1){
            if(entryTaxRateBefore == "3"&&(window.changeApply['entryTaxRate'] == undefined
                    ||(window.changeApply['entryTaxRate'] != undefined&&window.changeApply['entryTaxRate']['changeAfter']==undefined))){
                utils.dialog({content: '请在修改进项税率后，再提交审核', quickClose: true, timeout: 2000}).showModal();
                parent.hideLoading();
                return false;
            }
            if(salesRateBefore == "3"&&(window.changeApply['salesRate'] == undefined
                    ||(window.changeApply['salesRate'] != undefined&&window.changeApply['salesRate']['changeAfter']==undefined))){
                utils.dialog({content: '请在修改销项项税率后，再提交审核', quickClose: true, timeout: 2000}).showModal();
                parent.hideLoading();
                return false;
            }
        }
    }
    /**
     *重新提交的时候判断商品大类的值
     * 如果修改后的值为中药饮片，判断 产地和质量标准的值是否为空，
     * 为空时不能提交
     */
    if(window.changeApply && window.changeApply.largeCategory){
        if(window.changeApply.largeCategory.afterText == '中药饮片'){
            if(!valTest_area_Stand('producingArea','产地')){ return false;}
            if(!valTest_area_Stand('qualityStandard','质量标准')){ return false;}
        }
    }
    if(window.changeApply && window.changeApply.scopeOfOperation){
        if(window.changeApply.scopeOfOperation.afterText == '中药饮片'){
            if(!valTest_area_Stand('producingArea','产地')){ return false;}
        }
    }
    /**
     * RM 2018-10-30
     * 当提交审核的时候需要再次校验。因为用户数据的修改顺序hi导致产地为空的时候修改了商品大类
     * 导致，此时产地，质量标准应该必填了，却没有数据，去提交导致流程错误
     */
    if($('#largeCategoryVal').attr('data-value') == '中药饮片'){
        if(!valTest_area_Stand('producingArea','产地')){ return false;}
        if(!valTest_area_Stand('qualityStandard','质量标准')){ return false;}
    }else{
        //非中药饮片时，下面都为必填项，提交时需要做为空判断
        if(!valMust_area_stand('approvalNumber','批准文号')){ return false;}
        if(!valMust_area_stand('standardProductId','标准库ID')){ return false;}
        //if(!valMust_area_stand('indateValue','有效期')){ return false;}
        if(window.changeApply.indateValue){
            if(window.changeApply['indateValue']['afterText'] == undefined){
                diaFun('有效期')
                return false
            }else if(window.changeApply['indateValue']['afterText'] == '月'){
                diaFun('有效期')
                return false
            }
        }else{
            if($('input[name=indateValue]').val() == ''){
                diaFun('有效期')
               return false
            }
        }
        if(!valMust_area_stand('maintenancePeriod','养护周期')){ return false;}
    }
    /**
     * RM 2019-04-25
     * 新需求。所属经营范围的值为中药饮片时，产地的值为必填项
     * @type {string}
     */
    if($('#scopeOfOperationVal').attr('data-value') == '中药饮片'){
        if(!valTest_area_Stand('producingArea','产地')){ return false;}
    }
    var data=JSON.stringify(productDataVo);
    parent.showLoading();
    //console.log(data);
    $.ajax({
        type:"post",
        url: url,
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            if (data.code==0){
                var msg="";
                // if ($("#statues").val()==0){
                //     msg = '保存成功';
                // }else if ($("#statues").val()==1){
                //     msg = '提交审核成功';
                // }else {
                //     msg = '操作成功';
                // }
                let statusMsg = new Map([
                    ['0','保存成功'],
                    ['1','提交审核成功'],
                    ['default','操作成功']
                ])
                msg = statusMsg.get($("#statues").val()) || statusMsg.get('default');

                if (data.result.code==1) {
                    msg = data.result.msg;
                }
                utils.dialog({
                    title: "提示",
                    content: msg,
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        utils.closeTab();
                    }
                }).showModal();
                $(".ui-dialog-close").hide();
                return false;
            }else {
                utils.dialog({content: data.result, timeout: 2000}).showModal();
            }
        },
        error:function () {
            utils.dialog({content: '保存失败', quickClose: true, timeout: 2000}).showModal();
        },complete:function(){
            parent.hideLoading();
        }
    });
}

/**
 * 获取提交数据
 * @returns {string}
 */
function getSavedData() {
    /**
     * 申请属性数据 applyFormVo
     * 基础数据 productBaseInfoVo
     */
    var productDataVo = {};
    //机构信息
    unReadOnly();
    var propertyApprovalDetails=$("#applicationAttributeVo").serializeToJSON();
    //重新赋值申请时间
    propertyApprovalDetails.recordApplicationTime = $("#applicationTimeRetrun").val();
    var productBaseInfoVo=$("#productBaseInfoVo").serializeToJSON();
    //重点养护  养护周期30天，非重点养护 养护周期90天，不管输入框输入什么值，只有这两个数据

    // if(window.changeApply.maintenancePeriod){
    //     //养护类型没有变化的情况时，判断养护周期是否有变化
    //     productBaseInfoVo.maintenancePeriod = window.changeApply.maintenancePeriod.afterText;
    // }
    if(window.changeApply.keyConservationCategories){
        //养护类型有变化的话，养护周期就肯定有变化，所以给变量赋值，如果养护类型没有变化的话，养护周期也不会有变化，提交的时候就从输入框中取默认值就行
        if(window.changeApply.keyConservationCategories.afterText == '非重点养护'){
            window.changeApply.maintenancePeriod ={
                afterText: "90",
                attribute:"maintenancePeriod",
                changeAfter:"90",
                changeBefore:$('#maintenancePeriod').val(),
                changeType:"1"
            }
        }else if(window.changeApply.keyConservationCategories.afterText == '重点养护'){
            window.changeApply.maintenancePeriod ={
                afterText: "30",
                attribute:"maintenancePeriod",
                changeAfter:"30",
                changeBefore:$('#maintenancePeriod').val(),
                changeType:"1"
            }
        }
        //养护类型有变化的话，养护周期就肯定有变化，所以给变量赋值，如果养护类型没有变化的话，养护周期也不会有变化，提交的时候就从输入框中取默认值就行
        // let boolFlag= (window.changeApply.keyConservationCategories && window.changeApply.keyConservationCategories.afterText == '非重点养护');
        // window.changeApply.maintenancePeriod ={
        //     afterText: boolFlag?"90":'30',
        //     attribute:"maintenancePeriod",
        //     changeAfter:boolFlag?"90":'30',
        //     changeBefore:$('#maintenancePeriod').val(),
        //     changeType:"1"
        // }
    }/*else{
        //如果没有变化时，查看此时页面养护类型选中值是什么： 1为重点养护，2位非重点养护
        if(productBaseInfoVo.keyConservationCategories == '1'){
            productBaseInfoVo.maintenancePeriod = '30';
            window.changeApply.maintenancePeriod ={
                afterText: "30",
                attribute:"maintenancePeriod",
                changeAfter:"30",
                changeBefore:$('#maintenancePeriod').val(),
                changeType:"1"
            }
        }else if(productBaseInfoVo.keyConservationCategories == '2'){
            productBaseInfoVo.maintenancePeriod = '90';
            window.changeApply.maintenancePeriod ={
                afterText: "90",
                attribute:"maintenancePeriod",
                changeAfter:"90",
                changeBefore:$('#maintenancePeriod').val(),
                changeType:"1"
            }
        }
    }*/
    productDataVo.baseApprovalRecordVo=$.extend({}, propertyApprovalDetails,productBaseInfoVo);
    if($.isArray(productDataVo.baseApprovalRecordVo.keyConservationCategories))
    {
        productDataVo.baseApprovalRecordVo.keyConservationCategories=productDataVo.baseApprovalRecordVo.keyConservationCategories.join(',');
    }
    if($.isArray(productDataVo.baseApprovalRecordVo.specialAttributes))
    {
        productDataVo.baseApprovalRecordVo.specialAttributes=productDataVo.baseApprovalRecordVo.specialAttributes.join(',');
    }
    if($.isArray(productDataVo.baseApprovalRecordVo.storageAttribute)){
        productDataVo.baseApprovalRecordVo.storageAttribute=productDataVo.baseApprovalRecordVo.storageAttribute.join(',');
    }
    if($.isArray(productDataVo.baseApprovalRecordVo.temperatureRange)){
        productDataVo.baseApprovalRecordVo.temperatureRange=productDataVo.baseApprovalRecordVo.temperatureRange.join(',');
    }

    productDataVo.instructionsList=instructions;

    var changeApply = window.changeApply;
    //console.log(changeApply);
    var changeApplyList = window.changeApplyList;
    if(changeApplyList.approvalFile){
        var approvalFileDtos=changeApplyList.approvalFile.changeAfter;
        if(approvalFileDtos!=undefined){
            for(var i=0;i<approvalFileDtos.length;i++)
            {
                if(approvalFileDtos[i].enclosureList)
                {
                    if (typeof approvalFileDtos[i].enclosureList == 'string') {
                        approvalFileDtos[i].enclosureList=JSON.parse(approvalFileDtos[i].enclosureList);
                    }
                }
            }
            productDataVo.approvalFileDtos=approvalFileDtos;
        }
    }
    var propertyArray=[];
    $.each(changeApply,function (c, v) {
         propertyArray.push(v);
     })
    //console.log(propertyArray);
    //组装关联属性变更
    //如果是否委托为否，删除委托商家
   /* propertyArray.forEach(item=>{
        if (item.attribute=="entrustmentProduction"&&item.changeAfter==0) {
            for (var i = 0; i < propertyArray.length; i++) {
                if (propertyArray[i].attribute=="entrustmentManufacturer"){
                        propertyArray.splice(i,1);
                }
            }
        }
    })*/
    //清除变更前后未变的数据
    for (var i = propertyArray.length-1; i >= 0; i--) {
        if (propertyArray[i].changeType!=0&&propertyArray[i].changeBefore==propertyArray[i].changeAfter&&propertyArray[i].attribute!='approvalFile'){
            propertyArray[i].changeType = 0;
        }
    }
    $.each(changeApplyList,function (c, v) {
        v.changeAfter="";
        v.changeBefore="";
        propertyArray.push(v);
    })
    productDataVo.propertyApprovalDetails=propertyArray;
    //console.log(productDataVo);
    readOnly();
    return  productDataVo;
}
function propertyIsExist(array,property) {
    var flag = false;
    array.forEach(item=>{
        if(item.attribute==property){
            flag = true;
        }
    })
   return flag;
}
/**
 * 字典值回显
 */
function  showDictValue() {
    //商品大类
    showComValue("largeCategory","1017");
    //生产厂家
    //showComValue("manufacturer","1003");
    //包装单位
    showComValue("packingUnit","1002");
    //剂型
    showComValue("dosageForm","1001");
    // 委托厂家
    //showComValue("entrustmentManufacturer","1003");
    //存储条件
    showComValue("storageConditions","1019");
    //处方分类
    showComValue("prescriptionClassification","1016");
    //所属经营范围
    //showComValue("scopeOfOperation","1006");
    var simpleCode =$("#scopeOfOperation").val();
    var orgCode = $("#applicantOrgCode").val();
    if(simpleCode!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/queryscopebycode?orgCode='+orgCode+"&simpleCode="+simpleCode,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
               //console.log(data);
                if(data.code == 0)
                {
                    var simpleCodeName = "";
                    for (var i = 0;i<data.result.length;i++){
                        if (i!=data.result.length-1){
                            simpleCodeName = simpleCodeName + data.result[i].name+",";
                        } else {
                            simpleCodeName = simpleCodeName + data.result[i].name;
                        }
                    }
                    $("#scopeOfOperationVal").val(simpleCodeName);
                    $("#scopeOfOperationVal").attr("data-value",simpleCodeName);
                    $("#scopeOfOperationVal").attr("title",simpleCodeName);
                }
            }
        })
    }


    /*// 一级分类 firstCategory
    showComValue("firstCategory","1025");
    //二级分类 secondCategory
    showComValue("secondCategory","1026");
    //三级分类 thirdCategory
    showComValue("thirdCategory","1027");*/
    //品牌
   /* var brand=$("#brand").val();
    if(brand!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/brand/queryById?id='+brand,
            type:'get',
            dataType:'json',
            success:function(data){
                console.log(data);
                if(data!=null&&data!=undefined){
                    $("#brandVal").val(data.result.brandName);
                    $("#brandVal").attr("data-value",data.result.brandName);
                }
            }
        })
    }*/
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showComValue(obj,type) {
    var id =$("#"+obj).val();
    if(id!=""){
        $.ajax({
            url:'/proxy-sysmanage/sysmanage/dict/querybeanbytype?type='+type+"&id="+id,
            type:'get',
            dataType:'json',
            async : false,
            success:function(data){
                //console.log(data);
                if(data.code == 0)
                {
                    $("#"+obj+"Val").val(data.result);
                    $("#"+obj+"Val").attr("data-value",data.result);
                    $("#"+obj+"Val").attr("title",data.result);
                    if(obj=="largeCategory"){
                        if(data.result=="中药饮片") {
                            $(".productrate").show();
                            if(data.result == '中药饮片'){
                                //Is_ZYYP_flag = true
                                $('input[name=indate]').parents('.input-group').find('div').eq(0).find('i').remove() // 有效期
                                $('#maintenancePeriod').parents('.input-group').find('div').eq(0).find('i').remove(); // 养护周期
                                $('input[name=approvalNumber]').parents('.input-group').find('div').eq(0).find('i').remove() // 批准文号
                                $('input[name=standardProductId]').parents('.input-group').find('div').eq(0).find('i').remove() // 标准库ID
                                $('input[name=entrustmentProduction]').parents('.input-group').find('div').eq(0).find('i').remove()//是否委托生产
                                $('input[name=producingArea]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>产地') // 产地
                                $('input[name=qualityStandard]').parents('.input-group').find('div').eq(0).html('<i class="text-require">*  </i>质量标准') // 质量标准
                            }
                        }else{
                            $(".productrate").hide();
                        }
                        if(largeCategoryArray.indexOf(data.result)>-1) {
                            $(".productrate").show();
                        }else{
                            $(".productrate").hide();
                        }
                    }
                }
            }
        })
    }
}
/**
 * 显示字典值
 * @param obj
 * @param data
 */
function  showValue(obj,data) {
    var key =$("#"+obj).val();
    for(var i=0;i<data.length;i++){
        if(data[i].data==key){
            $("#"+obj+"Val").val(data[i].value);
        }
    }
}

/**
 * 审核按钮操作
 */
function submitAuditInfo(status,auditOpinion) {
    var productData={
        'recordId':$('#approvalRecordId').val(),
        'statues':status,
        'auditOpinion':auditOpinion,
        "taskId":$("#taskId").val(),
        "productCode":$("#commodity_code").val(),
        "recordApplicationTime":$("#applicationTimeRetrun").val()
    };
    var data=JSON.stringify(productData);
    //console.log(data);
    $.ajax({
        type:"post",
        url: auditUrl,
        async : false,
        data:data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {
            var msg="";
            let statusMsg = new Map([
                [0,'恭喜审核通过'],
                [1,'驳回成功'],
                [2,'流程已关闭'],
            ])
            msg = statusMsg.get(Number(status));
            utils.dialog({
                title: "提示",
                content: msg,
                width:300,
                height:30,
                okValue: '确定',
                ok: function () {
                    utils.closeTab();
                }
            }).showModal();
            $(".ui-dialog-close").hide();
            return false;
        },
        error:function () {
            utils.dialog({content: '审核失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

//根据商品ID，加载页面数据
function obtainProductBaseInfo(productId){
    $.ajax({
        type:"post",
        url: "/proxy-product/product/productBase/getProductBaseById",
        async : true,
        data:{"productId":productId},
        dataType:"json",
        success: function (data) {
            var result=data.result;
            //console.log(data);
            $('input[data-role="tagsinput"]').tagsinput('removeAll');
           productNameMnemonicCodeBefore=result.productBaseInfoVo.productNameMnemonicCode;
           commonNameMnemonicCodeBefore=result.productBaseInfoVo.commonNameMnemonicCode;
            entryTaxRateBefore = result.productBaseInfoVo.entryTaxRate;
            salesRateBefore = result.productBaseInfoVo.salesRate;
            loadProductData(result.productBaseInfoVo);
            var temperatureRange = result.productBaseInfoVo.temperatureRange
             if(temperatureRange!=null){
                 var tempArr = temperatureRange .split(',');
                 for(var i=0;i<tempArr.length;i++){
                    if(i==0){
                        $('#temperatureRange1').val(tempArr[0]);
                    }
                    if(i==1){
                        $('#temperatureRange2').val(tempArr[1]);
                    }
                 }
             }
            $("#changeApplyBtn").show();
            firstCategoryBefore = $("#firstCategory").val();
            firstCategoryBeforeVal = $("#firstCategoryVal").val();
            secondCategoryBefore = $("#secondCategory").val();
            secondCategoryBeforeVal = $("#secondCategoryVal").val();
            thirdCategoryBefore=$("#thirdCategory").val();
            thirdCategoryBeforeVal=$("#thirdCategoryVal").val();
            largeCategoryBeforeVal = $("#largeCategoryVal").val();

            //加载说明书
            // if (pageType==0||pageType==1||pageType==4){ 8.22  审核页说明书显示修改前
                var html = getProductHTML(result.instructionsList);
                instructionsChangeBeforeHtml=html;//记录修改前的说明书数据
                $("#insForm").html(html);
            // }
            $('.only-read').prev('div').find('input').removeClass('disBlock').addClass('disNone');

        }, error:function (XMLHttpRequest, textStatus, errorThrown) {
            utils.dialog({content: '加载数据失败', quickClose: true, timeout: 2000}).showModal();
        }
    });
}

function saveFirstCategory(){
    //二级和三级分类可编辑
    $('#secondCategoryVal').removeAttr('disabled');
    $('#thirdCategoryVal').removeAttr('disabled');
    $('#secondCategoryVal').nextAll('.yulanInput').hide();
    $('#thirdCategoryVal').nextAll('.yulanInput').hide();
    //保存一级分类
    var btn_save_largeCategory = $('#div_firstCategory').find('.changeApplyBtn');
    $(btn_save_largeCategory).unbind("click"); //移除click
    $(btn_save_largeCategory).on('click',function() {
        if($('#firstCategory').val()==""){//判断是否选择一级分类
            utils.dialog({content: '请选择一级分类', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if($('#secondCategory').val()==""){//判断是否选择二级分类
            utils.dialog({content: '请选择二级分类', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if($('#thirdCategory').val()==""){//判断是否选择三级分类
            utils.dialog({content: '请选择三级分类', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        //保存关联的二级分类
        delete window.changeApply["secondCategory"];
        var thirdCategoryBeforeObj = {
            attribute:"secondCategory",
            changeBefore:secondCategoryBefore,
            changeAfter: $('#secondCategory').val(),
            afterText:$('#secondCategoryVal').val(),
            changeType: '1'
        }
        window.changeApply["secondCategory"] = thirdCategoryBeforeObj;
        //三级分类 回显后不可编辑
        $('#secondCategory').val(secondCategoryBefore);
        $('#secondCategoryVal').val(secondCategoryBeforeVal);
        $("#secondCategoryVal").attr('data-value',secondCategoryBeforeVal);
        $('#secondCategoryVal').attr('disabled','disabled');

        //保存关联的三级分类
        delete window.changeApply["thirdCategory"];
        var thirdCategoryBeforeObj = {
            attribute:"thirdCategory",
            changeBefore:thirdCategoryBefore,
            changeAfter: $('#thirdCategory').val(),
            afterText:$('#thirdCategoryVal').val(),
            changeType: '1'
        }
        window.changeApply["thirdCategory"] = thirdCategoryBeforeObj;
        //三级分类 回显后不可编辑
        $('#thirdCategory').val(thirdCategoryBefore);
        $('#thirdCategoryVal').val(thirdCategoryBeforeVal);
        $("#thirdCategoryVal").attr('data-value',thirdCategoryBeforeVal);
        $('#thirdCategoryVal').attr('disabled','disabled');
        $('#secondCategoryVal').nextAll('.yulanInput').show().addClass('yulanInput_after');
        $('#thirdCategoryVal').nextAll('.yulanInput').show().addClass('yulanInput_after');
    });
}
function saveSecondCategory(){
    //删除一级分类变更数据
    delChangeApply("firstCategory");
    //三级分类可编辑
    $('#thirdCategoryVal').removeAttr('disabled');
    $('#thirdCategoryVal').nextAll('.yulanInput').hide();
    //保存二级分类
    var btn_save_largeCategory = $('#div_secondCategory').find('.changeApplyBtn');
    $(btn_save_largeCategory).unbind("click"); //移除click
    $(btn_save_largeCategory).on('click',function() {
        if($('#secondCategory').val()==""){//判断是否选择二级分类
            utils.dialog({content: '请选择二级分类', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        if($('#thirdCategory').val()==""){//判断是否选择三级分类
            utils.dialog({content: '请选择三级分类', quickClose: true, timeout: 2000}).showModal();
            return false;
        }
        //保存关联的三级分类
        delete window.changeApply["thirdCategory"];
        //保存三级的修改
        var thirdCategoryBeforeObj = {
            attribute:"thirdCategory",
            changeBefore:thirdCategoryBefore,
            changeAfter: $('#thirdCategory').val(),
            afterText:$('#thirdCategoryVal').val(),
            changeType: '1'
        }
        window.changeApply["thirdCategory"] = thirdCategoryBeforeObj;
        //三级分类 回显后不可编辑
        $('#thirdCategory').val(thirdCategoryBefore);
        $('#thirdCategoryVal').val(thirdCategoryBeforeVal);
        $("#thirdCategoryVal").attr('data-value',thirdCategoryBeforeVal);
        $('#thirdCategoryVal').attr('disabled','disabled');
        $('#thirdCategoryVal').nextAll('.yulanInput').show().addClass('yulanInput_after');
    });
}

/**
 * 删除已修改字段
 */
function delChangeApply(name) {
    if (window.changeApplyBak && changeApplyBak[name]) {
        if (window.changeApply[name]) {
            window.changeApply[name] = {};
            window.changeApply[name]["changeType"] = 0;
            window.changeApply[name]["attribute"] = name;
        }
    } else {
        delete window.changeApply[name];
    }
    $('#'+name+'Val').nextAll('.yulanInput').removeClass('yulanInput_after');
}

//判断委托产家是否显示
function initChecked(){
    var type=$("input[name='entrustmentProduction']:checked").val();
    if(type == 1)
    {
        $("#entManufacturerDiv").show();
    }else{
        $("#entManufacturerDiv").hide();
    }

};
