$(function () {

    function dateFormatter(inputTime) {
        if (inputTime == null || inputTime == "")
            return "";
        var date = new Date(inputTime);
        var y = date.getFullYear();
        var m = date.getMonth() + 1;
        m = m < 10 ? ('0' + m) : m;
        var d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        var h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        var minute = date.getMinutes();
        var second = date.getSeconds();
        minute = minute < 10 ? ('0' + minute) : minute;
        second = second < 10 ? ('0' + second) : second;
        return y + '-' + m + '-' + d;
        // return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
    }
    var detailNames = ['发票调整单号', '供应商编码', '供应商名称', '调整金额', '发票含税金额', '采购单含税金额', 'ERP采购发票号', '供应商发票号', '制单人', '单据日期'];
    var detailModel = [
            {
                name: 'billNo'
            }, {
                name: 'supplierNo'
            }, {
                name: 'supplierName'
            }, {
                name: 'money',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'taxInvoiceMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'taxBillMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            },
            {
                name: 'invoiceNo'
            }, {
                name: 'supplierInvoiceNo'
            }, {
                name: 'createUser'
            }, {
                name: 'createTime',
                formatter:dateFormatter
            }
        ]
    ;
    $('#X_Table1').XGrid({
        colNames: detailNames,
        colModel: detailModel,
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        rownumbers: true
    });

    // 发票详情
    $.ajax({
        url: '/proxy-finance/finance/purchase/invoice/getAdjustInvoiceDetail',
        type: "POST",
        dataType: "json",
        contentType:"application/json",
        timeout: 8000, //6000
        data: JSON.stringify({
            billNo: $("#billNo").val(),
        }),
        success: function (data) {
            if (data.code==0){
                $('#X_Table1').XGrid({
                    data: [data.result],
                    colNames: detailNames,
                    colModel: detailModel,
                    rowNum: 20,
                    rowList: [20, 50, 100],//分页条数下拉选择
                    altRows: true,//设置为交替行表格,默认为false
                    rownumbers: true
                });
            }
        },
        error: function () {
            utils.dialog({content: '网络异常！', quickClose: true, timeout: 2000}).showModal();
        }
    });





})