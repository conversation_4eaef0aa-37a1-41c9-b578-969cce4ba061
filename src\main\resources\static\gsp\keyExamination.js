function initDate(param) {
    var new_date = new Date();
    var year = new_date.getFullYear();
    var month = new_date.getMonth() + 1;
    var date = new_date.getDate();
    //1位数加0
    month = month.toString().length <= 1 ? '0' + month : month;
    date = date.toString().length <= 1 ? '0' + date : date;
    //console.log(new_date, year, month, date);
    //设置开始时间为当月第一天00:00:00，结束时间为当天23:59:59
    $('#beginTime').val(year + '-' + month + '-01');
    $('#endTime').val(year + '-' + month + '-' + date);
}

$(function () {

    $('#Point_Table').XGrid({
        url:'/proxy-gsp/gsp/checkPlan/queryKeyExaminationPage',
        colNames: ['养护计划单据编号', '制单日期', '流程名称', '任务名称', '发起部门', '发起机构', '养护类别', '状态','备注'],
        postData:{
            type:$("#type").val(),
            orgCode:$("#orgvalue").val(),
        },
        colModel: [
            {
                name: 'checkPlanCode',
                index: 'checkPlanCode',//索引。其和后台交互的参数为sidx
            }, {
                name: 'checkTimes',
                index: 'checkTimes',
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }
            }, {
                name: 'processName',
                index: 'processName'

            }, {
                name: 'taskName',
                index: 'taskName'
            }, {
                name: 'depaName',
                index: 'depaName'
            }, {
                name: 'orgName',
                index: 'orgName'
            }, {
                name: 'checkType',
                index: 'checkType',
                formatter: function (e) {
                    if (e == '1') {
                        return '重点养护'
                    } else if (e == '2') {
                        return '普通养护'
                    }
                }
            }, {
                name: 'type',
                index: 'type',
                formatter: function (e) {
                    if (e == 0) {
                        $("#divs").show();
                        return '未审批'
                    } else if (e == 1) {
                        $("#divs").hide();

                        return '已审批'
                    }
                }
            }, {
                name: 'remark',
                index: 'remark',
            },{
                name: 'orgCode',
                index: 'orgCode',
                hidden:true
            }
        ],
        rowNum: 20,
        altRows: true,//设置为交替行表格,默认为false
        rowList:[20,50,100],
        rownumbers: true,//是否展示序号，多选
        ondblClickRow: function (e, c, obj, b) {
            $("#flag").val(obj.checkPlanCode)
            var el = document.querySelector('#PointInfo');//html元素
            utils.dialog({
                title:'重点养护药品信息',
                content: el,
                width:$(window).width() * 0.8,
                height:$(window).height()*0.8
            }).showModal();
            $('#PointInfo_Table').XGrid({
                url: '/proxy-gsp/gsp/checkPlan/queryCheckPlanInfo',
                postData:{
                    checkPlanCode:obj.checkPlanCode,
                    orgCode:obj.orgCode,

                },
                colNames: ['日期', '商品编号', '商品名称', '商品规格', '生产厂家', '商品产地', '单位',
                    '剂型','库房名称','养护原因'
                ],
                colModel: [
                    {
                        name: 'checkTime',
                        index: 'checkTime',
                        formatter:function (e){
                            if (e != null && e !="") {
                                return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                            } else {
                                return "";
                            }
                        }
                        //索引。其和后台交互的参数为sidx
                    }, {
                        name: 'productCode',
                        index: 'productCode'
                    }, {
                        name: 'productName',
                        index: 'productName'

                    }, {
                        name: 'specifications',
                        index: 'specifications'
                    }, {
                        name: 'manufacturerName',
                        index: 'manufacturerName'
                    }, {
                        name: 'producingArea',
                        index: 'producingArea'
                    }, {
                        name: 'packingUnit',
                        index: 'packingUnit'
                    }, {
                        name: 'dosageForm',
                        index: 'dosageForm',
                    },{
                        name: 'wareHouse',
                        index: 'wareHouse',
                        formatter:function (e){
                            if (e != null) {
                                if(e==1){
                                    return  "合格库"
                                } if(e==2){
                                    return  "不合格库"
                                } if(e==3){
                                    return  "暂存库"
                                }

                            } else {
                                return "";
                            }
                        }
                    }, {
                        name: 'checkCause',
                        index: 'checkCause',
                        formatter: function (e, c, rowData) {
                            var  checkCause  = "";
                            var count = 0;
                            var count1 = 0;
                            if (e) {
                                var  str =   e.split(",");
                                for (var i = 0; i < str.length; i++) {
                                    var s = str[i];
                                    if(s.indexOf("1")>=0){
                                        if(count1>0){
                                            checkCause+=",近效期";
                                        }else{
                                            checkCause+="近效期";
                                        }

                                        count++;
                                    }if(s.indexOf("2")>=0){
                                        if(count>0){
                                            checkCause+=",短效期";
                                            count1++;
                                        }else{
                                            checkCause+="短效期";
                                            count1++;
                                        }

                                    }if(s.indexOf("3")>=0){
                                        if(count1>0){
                                            checkCause+=",特殊存储要求";
                                        }else if(count>0){
                                            checkCause+=",特殊存储要求";
                                        }else{
                                            checkCause+="特殊存储要求";
                                        }

                                    }

                                }
                                return  checkCause;
                            } else {
                                var id = rowData.id;


                                return  "空";
                                //$("#" + id).find('td').eq(0).css('display','none');
                            }

                        },

                    }, {
                        name: 'id',
                        index: 'id',
                        hidden:true
                    }
                ],
                rowNum: 20,
                rowList:[20,50,100],
                rownumbers:true,
                //selectandorder: true,//是否展示序号，多选
                altRows: true,//设置为交替行表格,默认为false
                ondblClickRow: function (e, c, a, b) {
                    //console.log('双击行事件',a);
                },
                onSelectRow: function (e, c, a, b) {

                },

                pager: '#PointInfo_Table_pager',
            });
        },
        onSelectRow: function (e, c, a, b) {

        },
        pager: '#grid_page',
    });



})
        //查询
        function btn_search() {

            $('#Point_Table').setGridParam({
                url: '/proxy-gsp/gsp/checkPlan/queryKeyExaminationPage',
                postData: {
                    startTime:$("#beginTime").val(),
                    endTime:$("#endTime").val(),
                    checkPlanCode:$("#checkPlanCode").val(),
                    type:$("#type").val(),
                    orgCode:$("#orgvalue").val()
                }
            }).trigger('reloadGrid');
        }

        function check_seccsee(){
            $.ajax({
                url:'/proxy-gsp/gsp/checkPlan/CheckSuccess',
                data:{checkPlanCode:$("#flag").val()},
                type:"post",
                success:function(result){
                    if(result){
                        utils.dialog({content: "审批成功", quickClose: true, timeout: 2000}).showModal();
                    }else{
                        utils.dialog({content: "审批未成功", quickClose: true, timeout: 2000}).showModal();
                    }
                    setTimeout("location.reload();",500);
                }
            })
        }

        function btn_output(){
        	utils.exportAstrictHandle('Point_Table', Number($('#totalPageNum').text()), 1).then( () => {
                return false;
            }).catch( () => {
            	window.location = '/proxy-gsp/gsp/checkPlan/excelKeyExamination?checkPlanCode='+$("#checkPlanCode").val()+'' +
                '&orgCode='+$("#orgvalue").val()+'&type='+$("#type").val()+'&startTime='+$("#beginTime").val()+'&endTime='+$("#endTime").val();
            });    
            
        }