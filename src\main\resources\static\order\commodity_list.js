$(function () {
    /* 获取dialog上层实例  */
    var dialog = parent.dialog.get(window);
    if (dialog) {
        var dialog_data = dialog.data;
        $('#search_vl').val(dialog_data)
    }

    $('#X_Table').XGrid({
        url:'/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$('#orgCode').val()+"&param="+$('#search_vl').val(),
        colNames: ['商品编码', '商品名称','通用名', '商品规格', '生产厂家', '产地', '单位', '批准文号'],
        colModel: [{ name: 'productCode',      index: 'productCode',    },
            {name: 'productName',      index: 'productName',    },
            {name: 'commonName',      index: 'commonName',    },
            {      name: 'specifications',      index: 'specifications',    },
            {     name: 'manufacturerVal',      index: 'manufacturerVal',    },
            {      name: 'producingArea',    index: 'producingArea',   },
            {    name: 'packingUnitVal',    index: 'packingUnitVal',    },
            {    name: 'approvalNumber',     index: 'approvalNumber',   }],
        rowNum: 20,
        rowList:[20,50,100],
        key:'productCode',
        rownumbers: true,
        altRows: true, //设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {
            console.log('双击行事件', obj);
            dialog.close(obj);
        },
        pager: '#grid-pager',
    });


    //商品名称 搜索
    $('#search_vl').Autocomplete({
        serviceUrl: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList?orgCode='+$("#orgCode").val(), //异步请求
        paramName: 'param',//查询参数，默认
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        transformResult: function (response) {
            return {
                suggestions: $.map(response.result.list, function (dataItem) {
                    return {value: dataItem.productName, data: dataItem.productCode};
                })
            };
        },
        triggerSelectOnValidInput: false, // 必选
        // multi: true, //多选要和delimiter一起使用
        // delimiter: ',',
        // showNoSuggestionNotice: true, //显示查无结果的container
        // noSuggestionNotice: '查询无结果',//查无结果的提示语
        // tabDisabled: true,
        onSelect: function (result) {
            //选中回调
            $("#search_vl").val(result.value)

        },
        onSearchStart: function (params) {
            // console.log('检索开始回调', params)
        },
        onSearchComplete: function (query, suggestions) {
            //匹配结果后回调
            console.log(query, suggestions);

        },
        onSearchError: function (query, jqXHR, textStatus, errorThrown) {
            //查询失败回调
            console.log(query, jqXHR, textStatus, errorThrown)
        },
        onHide: function (container) {
            // console.log('container隐藏前回调', container)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            console.log('没选中回调函数');
            $("#search_vl").val('');

        }
    });






    /* 查询 */
    $('#search_list').on('click', function () {
        $('#X_Table').setGridParam({
            url: '/proxy-order/order/orderReturn/orderReturnController/getAllProductList',
            postData: {
                orgCode:$('#orgCode').val(),
                param:$('#search_vl').val(),
            }
        }).trigger('reloadGrid');
    })


})