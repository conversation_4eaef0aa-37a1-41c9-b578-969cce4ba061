var imageType = ['png','jpg','pdf'];
var listDownloadId = []
var listDownload = []
$(function () {
    $("#info_table_goods").XGrid({
        url:"/proxy-gsp/gsp/drugReport/queryStoreProductList",
        colNames: ["序号","商品编码","原商品编码","商品名","通用名","规格","生产厂家","批准文号","包装单位","剂型","小包装条码","药检报告应传数量","未传药检报告数量","采购员"],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: "productCode"
        }, {
            name: "oldProductCode"
        }, {
            name: "productName"
        }, {
            name: "commonName"
        }, {
            name: "productSpecification"
        }, {
            name: "manufacturerName"
        }, {
            name: "productApprovalNumber"
        }, {
            name: "productPackUnitSmall"
        }, {
            name: "productPreparation"
        }, {
            name: "smallPackageBarCode"
        }, {
            name: "drugReportsNum",
            formatter:function (e){
                if (e != null && e !="") {
                    return e;
                } else {
                    return "0";
                }
            }
        }, {
            name: "notDrugReportsNum",
            formatter:function (e){
                if (e != null && e !="") {
                    return e;
                } else {
                    return "0";
                }
            }
        }, {
            name: "purchaseUser"
        }],
        altRows: true,
        // rownumbers: true,
        key: "id",
        rowNum: 20,
        selectandorder: true,//是否展示序号，多选
        rowList: [20, 50, 100],
        pager: "#grid_pager_goods",
        onSelectRow: function (id, dom, obj) {
            var selectid=null;
            /*if (dom.hasClass("selRow")) {
                $("#info_table_report").XGrid("clearGridData");
                loadDetail(obj);
            } else {
                $("#info_table_report").XGrid("clearGridData");
            }*/
            listDownloadId= [];
            showTableSiteData(id, dom, obj);
            showYtableSiteData(id,dom,obj);
            $("#Y_Tablea").XGrid("clearGridData");
        },
        onPaging: function (page) {
            $("#info_table_report").XGrid("clearGridData");
            $("#Y_Tablea").XGrid("clearGridData");
        },
    });

    // tabs 切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $('.nav-content');
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
        $nav_content.children('div').eq($this.index()).addClass('active').siblings().removeClass(
            'active');
        if ($(this).index() == '0') {

        } else {

        }
    })

    /* 查询 */
    $('#search_btn').on('click', function () {
        $('#info_table_goods').XGrid('setGridParam', {
            url: '/proxy-gsp/gsp/drugReport/queryStoreProductList',
            postData: {
                startDate: $("#beginTime").val(),
                endDate: $("#endTime").val(),
                imageCount: $("#imageCount").val(),
                supplierName: $("#supplierName").val(),
                productBatchNo: $("#productBatchNo").val(),
                productName: $("#productName").val(),
                oldProductCode: $("#oldProductCode").val(),
                notDrugReportsNum : $("#notDrugReportsNum").val()
            }
        }).trigger('reloadGrid');
        $("#info_table_report").XGrid("clearGridData");
        $("#Y_Tablea").XGrid("clearGridData");
    });

    $("#add_btn").on('click', function () {
        $("#supplierName_new").val('')
        $("#batch").val('')
        var selectedRowData = $("#info_table_goods").XGrid("getSeleRow");
        if (selectedRowData.length < 1) {
            utils.dialog({
                content: '请选择商品列表编辑行！',
                quickClose: true,
                timeout: 3000
            }).showModal();
            return false;
        }else {
            utils.dialog({
                title: '新增',
                width: 700,
                height:550,
                content: $("#add_edit_dialog"),
                okValue: '保存',
                cancelValue: '取消',
                onshow: function () {
                    $("#add_edit_dialog").find("input[name='id']").val("");
                    $("#add_edit_dialog").find("input[name='orgCode']").val(selectedRowData[0].orgCode);
                    $("#add_edit_dialog").find("input[name='productCode']").val(selectedRowData[0].productCode);
                    $("#add_edit_dialog").find("input[name='commonName']").val(selectedRowData[0].commonName);
                    $("#add_edit_dialog").find("input[name='productName']").val(selectedRowData[0].productName);
                    $("#add_edit_dialog").find("input[name='manufacturerName']").val(selectedRowData[0].manufacturerName);
                    $("#add_edit_dialog").find("input[name='productSpecification']").val(selectedRowData[0].productSpecification);
                    $("#add_edit_dialog").find("input[name='supplierName']").attr("disabled",false);
                    $("#add_edit_dialog").find("input[name='productBatchNo']").attr("disabled",false);
                    $("#batch").css('display','block');
                    $("#no_batch").css('display','none');
                    $("#supplierName_new").css('display','block');
                    $("#supplierName_no").css('display','none');

                    $('#editFlag').val("1");
                    $("").appendTo($("#previewBox").show().find("ul"));

                    $("#previewBox").find("li").remove();
                    $("#btnUpload").removeAttr("disabled");
                    valAutocomplete("/proxy-gsp/gsp/drugReportDetatil/querySupplierByCode",{paramName:'productBatchNo',params:{"productCode":$("input[name='productCode']").val(),"startStoreTime":$("input[name='startDate']").val(),"endStoreTime":$("input[name='endDate']").val()}},"supplierName_new",{data:"supplierCode",value:"supplierName"});



                    //  valAutocomplete("/drug/Report/Detatil/queryBatchNumByCode",{paramName:'productBatchNo',params:{"productCode":$("input[name='productCode']").val(),"startTime":$("input[name='startDate']").val(),"endTime":$("input[name='endDate']").val()}},"batch",{data:"batchNum",value:"batchNum"});

                },
                ok: function () {
                    //保存按钮回调
                    return submitDrugTestReport();
                },
                cancel: function () {
                },
                onclose: function () {

                },
            }).showModal();
        }
    });

    $("#edit_btn").on('click', function () {
        var selectedRowData = $("#info_table_report").XGrid("getSeleRow");

        let info_table_goods_selObj = {};
        if (selectedRowData.length < 1) {
            utils.dialog({
                content: '请选择药检报告编辑行！',
                quickClose: true,
                timeout: 3000
            }).showModal();
            return false;
        }else {
            info_table_goods_selObj =  {
                id: $('#info_table_goods').XGrid('getSeleRow')[0]['id'],
                dom: $('#' + $('#info_table_goods').XGrid('getSeleRow')[0]['id']),
                obj: $('#info_table_goods').XGrid('getSeleRow')[0]
            }
            utils.dialog({
                title: '修改',
                width: 700,
                content: $("#add_edit_dialog"),
                okValue: '保存',
                cancelValue: '取消',
                onshow: function () {
                    $("#add_edit_dialog").find("input[name='orgCode']").val(selectedRowData[0].orgCode);
                    $("#add_edit_dialog").find("input[name='id']").val(selectedRowData[0].id);
                    $("#add_edit_dialog").find("input[name='productCode']").val(selectedRowData[0].productCode);
                    $("#add_edit_dialog").find("input[name='commonName']").val(selectedRowData[0].commonName);
                    $("#add_edit_dialog").find("input[name='productName']").val(selectedRowData[0].productName);
                    $("#add_edit_dialog").find("input[name='manufacturerName']").val(selectedRowData[0].manufacturerName);
                    $("#add_edit_dialog").find("input[name='productSpecification']").val(selectedRowData[0].productSpecification);
                    $("#add_edit_dialog").find("input[name='supplierName']").val(selectedRowData[0].supplierName);
                    $("#add_edit_dialog").find("input[name='supplierCode']").val(selectedRowData[0].supplierCode);
                    $("#add_edit_dialog").find("input[name='supplierName']").attr("disabled",true);
                    $("#add_edit_dialog").find("input[name='productBatchNo']").val(selectedRowData[0].productBatchNo);
                    $("#add_edit_dialog").find("input[name='productBatchNo']").attr("disabled",true);
                    $("#batch").css('display','block');
                    $("#no_batch").css('display','none');
                    $("#supplierName_new").css('display','block');
                    $("#supplierName_no").css('display','none');
                    $('#editFlag').val("2");

                    $("").appendTo($("#previewBox").show().find("ul"));

                    $("#previewBox").find("li").remove();
                    var htmlArr = [];

                    var detailsVOList = JSON.parse(selectedRowData[0].detailsVOList);

                    for(var x = 0; x < detailsVOList.length; x ++){
                        var detailsVO = detailsVOList[x];
                        htmlArr.push("<li>");
                        htmlArr.push("<span class='close-btn'>×</span>");

                        if (detailsVO.enclosureUrl.lastIndexOf('.pdf') > -1) {
                            htmlArr.push("<embed src='" + detailsVO.enclosureUrl + "' type='application/pdf' width='100%' height='100%' class='pre-img'>")
                        } else {
                            htmlArr.push("<img src='" + detailsVO.enclosureUrl + "' class='pre-img'>");
                        }

                        htmlArr.push("");
                        htmlArr.push("<p>" + detailsVO.enclosureName + "</p>");
                        htmlArr.push("<input type='hidden' name='enclosureUrl' value='" + detailsVO.enclosureUrl + "'>");
                        htmlArr.push("<input type='hidden' name='enclosureName' value='" + detailsVO.enclosureName + "'>");
                        htmlArr.push("</li>");

                        htmlArr.push("");
                    }

                    $(htmlArr.join("")).appendTo($("#previewBox").show().find("ul"));

                },
                ok: function () {

                    //保存按钮回调
                    return submitDrugTestReport(info_table_goods_selObj);
                },
                cancel: function () {
                },
                onclose: function () {

                },
            }).showModal();
        }
    });
    //打印药检报告
    $('#print_btn').on('click', function () {



        var selectedRowData = $("#info_table_report").XGrid("getSeleRow");
        if (selectedRowData.length < 1) {
            utils.dialog({
                content: '请选择药检报告编辑行！',
                quickClose: true,
                timeout: 3000
            }).showModal();
            return false;
        }else {
            var correlationId = JSON.parse(selectedRowData[0].id);
            printPdf(correlationId);
            // printBarcode(detailsVOList);
        }
    });

    var prevUploadFileName = "";//上一次上传的文件名
    $("#btnUpload").on("change", function () {

        var fileName = this.files[0].name;
        var fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLocaleLowerCase();
        if(imageType.indexOf(fileType) == -1){
            utils.dialog({content: "请上传正确格式的文件！如图片：jpg、png、以及pdf。", quickClose: false, timeout: 2000}).showModal();
            return false;
        }

        if($(".upload-file-box").find("li").length >= 30){
            utils.dialog({content: "最多只能上传30张附件", quickClose: false, timeout: 2000}).showModal();
            return false;
        }

        //是否选择了文件
        if (!fileName) {
            utils.dialog({
                title: "提示",
                width: 200,
                content: "请选择文件",
                okValue: "确定",
                ok: function () {
                }
            }).showModal();
            return false;
        }

        //重复文件
        // if (prevUploadFileName != "" && prevUploadFileName == fileName) {
        //     utils.dialog({
        //         title: "提示",
        //         width: 300,
        //         content: "此文件已经上传过，请确认是否再次上传？",
        //         okValue: "再次上传",
        //         cancelValue: "取消上传",
        //         ok: function () {
        //             UploadFileFn(function (list, filename) {
        //                 previewUpload(list, filename);
        //             });
        //         },
        //         cancel: function () {
        //
        //         }
        //     }).showModal();
        // } else {
        UploadFileFn(function (list, filename) {
            previewUpload(list, filename);
        });
        // }
    });

    $("#previewBox").on("click", '.close-btn', function () {
        $(this).closest("li").remove();
        $("#btnUpload").removeAttr("disabled");
    });

    // 当点击未上传药检查询的时候下载药检报告按钮置灰
    $("#nuPullDrugReport").on("click", function () {
        $("#download_btn").attr("disabled","disabled");
    });

    $("#pullDrugReport").on("click", function () {
        $("#download_btn").removeAttr("disabled");
    });

    //下载
    $('#download_btn').on('click',function () {
        var a=[];
        a.push('<form method="post" style="display: none;">');
        if(listDownloadId.length > 0){
            listDownloadId.map(function (x) {
                var obj = $('#info_table_report').XGrid('getRowData',x)
                if(obj.detailsVOList){
                    var dp = JSON.parse(obj.detailsVOList)
                    $.each(dp, function (i,v) {
                        a.push('<input name="file" value = ' + v.enclosureUrl + '>');
                    })
                    a.push('<input name="name" value = '+ obj.productCode + '>');

                }
                listDownload.push(obj);
            })
        }else{
            let rowdata = $('#info_table_report').XGrid('getRowData');
            if(rowdata.length == 0){
                utils.dialog({
                    title: '提示',
                    content: '请先选择商品，再进行下载。',
                    okValue: '确定',
                    ok: function () {}
                }).showModal();
                return false;
            }

            var b = rowdata.map(function(i,v){
                return i.id
            })
            b.map(function (x) {
                var obj = $('#info_table_report').XGrid('getRowData',x)
                if(obj.detailsVOList){
                    var dp = JSON.parse(obj.detailsVOList)
                    $.each(dp, function (i,v) {
                        a.push('<input name="file" value = ' + v.enclosureUrl + '>');
                    })
                    a.push('<input name="name" value = '+ obj.productCode + '>');
                    listDownload.push(obj);
                }
                listDownload.push(obj);
            })

        }

        a.push('</form>');
        var $eleForm = $(a.join(''));
        $eleForm.attr("action", "/proxy-gsp/gsp/drugReportDetatil/downloadZip");
        $(document.body).append($eleForm);
        // 提交表单，实现下载
        $eleForm.submit();

    })
});

function printPdf(correlationId){

    $.ajax({
        url: '/proxy-gsp/gsp/drugReport/getStoreProductPdf?correlationId='+correlationId,
        type: 'POST',
        async: false,
        processData: false,
        contentType: false,

        success: function (data) {
            printBarcode(data.result.files)

        }, error: function () {


        }
    });
}

function printBarcode(list) {
    var d = utils.dialog({
        title: '请稍候，生成中...',
        width: 300
    }).showModal();
    $("#printTagModule").html("");
    if (!(list && list.length)) {
        return false;
    }
    var img = [],
        flag = 0,
        mulitImg = list;
    var imgTotal = mulitImg.length;
    var htmlStr=[];
    // $("#printIframe")[0].contentWindow.print();
    for (var i = 0; i < imgTotal; i++) {//embed pdf

        if(mulitImg[i].lastIndexOf('.pdf')>-1 || mulitImg[i].lastIndexOf('.PDF')>-1){
            // htmlStr.push('<embed src="'+mulitImg[i].enclosureUrl+'" type="application/pdf" width="100%" height="1180" class="imgView" internalinstanceid="54" style="opacity: 1;" />');
            // htmlStr.push('<div style="page-break-after: always;"></div>');
            // htmlStr.push('<iframe style="" id="printIframe" src="'+mulitImg[i].enclosureUrl+'"></iframe>');
            // htmlStr.push('<div style="page-break-after: always;"></div>');
        }else{
            htmlStr.push('<img src="'+mulitImg[i]+'" />');
            htmlStr.push('<div style="page-break-after: always;"></div>');
        }
    }
    $("#printTagModule").append($(htmlStr.join('')));

    d.close().remove();
    printFn();

}
//打印方法
function printFn() {
    $("#printTagModule").show();
    var printStatus = $.Deferred();
    $("#printTagModule").print({
        globalStyles: true, //是否包含父文档的样式，默认为true
        mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
        stylesheet: null, //外部样式表的URL地址，默认为null
        noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
        iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
        append: null, //将内容添加到打印内容的后面
        prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
        deferred: printStatus  //回调函数
    });
    $("#printTagModule").hide();
}
function UploadFileFn(cb) {
    var file = $("#btnUpload")[0].files[0];
    var formData = new FormData();
    formData.append("files", file);
    formData.append("name", file.name);

    var loading = dialog({
        title: '上传中',
        fixed: true,
        width: 200,
        quickClose: false,
        cancel: false
    }).showModal();

    $.ajax({
        url: '/proxy-gsp/gsp/upload/upload',
        type: 'POST',
        async: false,
        data: formData,
        processData: false,
        contentType: false,
        beforeSend: function () {
            console.log("正在上传");
        },
        success: function (data) {
            loading.close();
            if (data.code == 0) {
                prevUploadFileName = file.name;
                cb(data.result[0], prevUploadFileName);
                utils.dialog({content: "上传成功", quickClose: false, timeout: 2000}).showModal();

            } else if (data.code == 1) {
                utils.dialog({content: "上传成功", quickClose: false, timeout: 2000}).showModal();
            } else {
                utils.dialog({content: "上传失败", quickClose: true, timeout: 2000}).showModal();
            }
        }, error: function () {
            loading.close();
            utils.dialog({content: "上传失败", quickClose: true, timeout: 2000}).showModal();
        }, complete: function () {
            $("#btnUpload").val("");
        }
    });
}

function previewUpload(src, filename) {
    if (src) {
        var htmlArr = [];
        htmlArr.push("<li>");
        htmlArr.push("<span class='close-btn'>×</span>");

        if (src.lastIndexOf('.pdf') > -1) {
            htmlArr.push("<embed src='" + src + "' type='application/pdf' width='100%' height='100%' class='pre-img'>")
        } else {
            htmlArr.push("<img src='" + src + "' class='pre-img'>");
        }

        htmlArr.push("");
        htmlArr.push("<p>" + filename + "</p>");
        htmlArr.push("<input type='hidden' name='enclosureUrl' value='" + src + "'>");
        htmlArr.push("<input type='hidden' name='enclosureName' value='" + filename + "'>");
        htmlArr.push("</li>");

        $(htmlArr.join("")).appendTo($("#previewBox").show().find("ul"));
    }
}
function showYtableSiteData(id, dom, obj) {
    if (dom.hasClass("selRow")) {
        $("#Y_Tablea").XGrid({
            url:"/proxy-gsp/gsp/drugReportDetatil/queryNoUploadStoreProductDetail?productCode="+obj.productCode+"&supplierCode="+obj.productCode,
            colNames: ["序号", "供应商", "供应商编号", "批号","商品编码", "商品名", "通用名", "生产厂家", "规格", "最后入库时间",'操作'],
            colModel: [{
                name: 'id', //与反回的json数据中key值对应
                index: 'id', //索引。其和后台交互的参数为sidx
                key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden: true
            }, {
                name: "supplierName"
            }, {
                name: "supplierCode",
                hidden: true
            }, {
                name: "productBatchNo"
            }, {
                name: "productCode"
            }, {
                name: "productName"
            }, {
                name: "commonName"
            }, {
                name: "manufacturerName"
            }, {
                name: "productSpecification"
            }, {
                name: "storeTime",

            }, {
                sortable: false,
                editable: true,
                rowtype: '#buttn_black',
                // formatter:brandOperation
            }, {
                name: "orgCode",
                hidden: true
            }
            ],
            altRows: true,
            rownumbers: true,
            key: "id",
            rowNum: 20,
            rowList: [10, 20, 50],
            onSelectRow: function (id,dom,obj,index,event,obj) {
                if (dom.hasClass('selRow')) {
                    setIdList(id)
                }else{
                    listDownloadId.map(function (x,i) {
                        if(x == id){
                            listDownloadId.splice(i,1);
                        }
                    })
                }
                //选中事件
                //回调参数化：id：数据中id或者本行id,dom:本行DOM元素,obj:本行所有json值,index:下标,event:原事件
            },
            pager: "#grid_pager"
        });
    }
}

function  brandOperation(){
    return  "<button class='btn btn-info' onclick='upLoadBrand(this)'>上传</button>"
}
function showTableSiteData(id, dom, obj) {
    if (dom.hasClass("selRow")) {
        $("#info_table_report").XGrid({
            url:"/proxy-gsp/gsp/drugReportDetatil/queryStoreProductDetail?productCode="+obj.productCode+"&supplierName="+$("#supplierName").val()
            +"&productBatchNo="+$("#productBatchNo").val(),
            colNames: ["序号", "供应商", "供应商编号", "批号", "图片数量", "商品编码", "商品名", "通用名", "生产厂家", "规格", "操作人", "操作时间"],
            colModel: [{
                name: 'id', //与反回的json数据中key值对应
                index: 'id', //索引。其和后台交互的参数为sidx
                key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                hidden: true
            }, {
                name: "supplierName"
            }, {
                name: "supplierCode",
                hidden: true
            }, {
                name: "productBatchNo"
            }, {
                name: "imageCount",
                formatter: function (value,c,rowData) {
                    var rowDataId = rowData.id;
                    var str = '无';
                    if (value) {
                        var tableId = "#info_table_report"
                        str = '<a href="javascript:;" onclick="showImg(this, \'' + tableId + '\');">' + value + '</a>';
                    }else{

                        //var id = rowData.id;
                        setTimeout(function () {
                            $(" #info_table_report #"+rowDataId).css({
                                "background": "#ff0031",
                            });
                        },0)
                        // console.log('++++++  ' + e)
                        // if (str == '无') {
                        //     $('#info_table_report').css({
                        //         "background": "#ff0031"
                        //     });
                        //
                        // }
                    }

                    return str;
                },
                unformat: function (e) {
                    e = e.replace(/<[^>]+>/g, '');
                    if (e == '无') {
                        e = 0;
                    }
                    return e;
                }
            }, {
                name: "productCode"
            }, {
                name: "productName"
            }, {
                name: "commonName"
            }, {
                name: "manufacturerName"
            }, {
                name: "productSpecification"
            }, {
                name: "operateUserName"
            }, {
                name: "operateTime",
                formatter:function (e){
                    if (e != null && e !="") {
                        return ToolUtil.dateFormat(e, 'yyyy-MM-dd');
                    } else {
                        return "";
                    }
                }


            }, {
                name: "detailsVOList",
                hidden: true,
                formatter: function (value) {
                    if (value) {
                        return JSON.stringify(value);
                    }
                    return JSON.stringify([]);
                }
            }, {
                name: "file",
                hidden: true,
                hidegrid: true
            }],
            altRows: true,
            rownumbers: false,
            key: "id",
            rowNum: 20,
            selectandorder: true,
            rowList: [10, 20, 50],
            onSelectRow: function (id,dom,obj,index,event,obj) {
                if (dom.hasClass('selRow')) {
                    setIdList(id)
                }else{
                    listDownloadId.map(function (x,i) {
                        if(x == id){
                            listDownloadId.splice(i,1);
                        }
                    })
                }

                // 当选中药检报告大于1条后，修改按钮置灰
                $('#edit_btn, #print_btn').prop('disabled',listDownloadId.length > 1 ? true: false)
                //选中事件
                //回调参数化：id：数据中id或者本行id,dom:本行DOM元素,obj:本行所有json值,index:下标,event:原事件
            },
            pager: "#grid_pager_report"
        });
    }else{
        $("#info_table_report").XGrid("clearGridData");
    }
}
//第二个表选中ID放入数组
function setIdList(id){

    if(listDownloadId.indexOf(id) == "-1"){
        listDownloadId.push(id);
    }else{
        listDownloadId.map(function (x,i) {

            if(x == id){
                listDownloadId.splice(i,1);
            }
        })
    }
}

function reloadGridFunc(_obj) {
    //更新表格数据
    // $('#info_table_goods').XGrid('setGridParam', {
    //     url:"/drugReport/queryStoreProductList",
    //     postData: {
    //         beginTime:$("#beginTime").val(),
    //         endTime:$("#endTime").val(),
    //         productCode:$("#productCode").val(),
    //         productBatchNo:$("#productBatchNo").val(),
    //         supplierCode:$("#supplierCode").val()
    //     }
    // }).trigger("reloadGrid");
    if(_obj){
        // listDownloadId = [];
        showYtableSiteData(_obj.id, _obj.dom, _obj.obj);
        showTableSiteData(_obj.id, _obj.dom, _obj.obj);
    }

}

function getBatchNoAutocomplete(url,param,obj,resParam,select,noneSelect){
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj).Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params: param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            console.log("结果集"+result.data)
            if(obj=='supplierName_new'){
                $("#"+obj).next().val(result.data);
            }else{

                $("#"+obj).val(result.data);
            }

            var drugTestReportVo = $('#drugTestReportForm').serializeToJSON();
            var paraData=JSON.stringify(drugTestReportVo);
            $.ajax({
                type:"post",
                url: '/proxy-gsp/gsp/drugReportDetatil/checkStoreProductDetail',
                async : false,
                data: paraData,
                dataType:"json",
                contentType: "application/json",
                success: function (data) {
                    console.log(data);
                    if(data.code==1){
                        $("#"+obj).val("");
                        utils.dialog({content: '已有该供应商和该批号下的药检报告！', quickClose: true, timeout: 3000}).showModal();
                        return false;
                    }

                },
                error:function () {
                    //utils.dialog({content: '保存失败！', quickClose: true, timeout: 3000}).showModal();
                }
            });

            /*


                        var selectedRowData = $("#info_table_report").XGrid("getRowData");
                        var flag = 0;
                        if($('#supplierName_new').next().val() != "" && $('#batch').val() != "" && selectedRowData.length >= 1){
                            for(var x = 0; x < selectedRowData.length; x ++){
                                if(selectedRowData[x].supplierCode == $('#supplierName_new').next().val() && selectedRowData[x].productBatchNo == $('#batch').val()){
                                    flag = 1;
                                }
                            }
                        }

                        if(flag == 1){
                            $("#"+obj).val("");
                            if(obj=='supplierName'){
                                $("#"+obj).next().val("");
                            }
                            utils.dialog({content: '已有该供应商和该批号下的药检报告！', quickClose: true, timeout: 3000}).showModal();
                            return false;
                        }*/
            select && select(result)
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            $("#"+obj).val("");
            if(obj=='supplierName'){
                $("#"+obj).next().val("");
            }
            noneSelect && noneSelect();
        }
    });
}

/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj).Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params: param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            console.log("结果集"+result.data)
            if(obj=='supplierName_new'){
                $("#batch").val("");
                $("#"+obj).next().val(result.data);
                // valAutocomplete("/drug/Report/Detatil/queryBatchNumByCode",{paramName:'productBatchNo',params:{"productCode":$("input[name='productCode']").val(),"startTime":$("input[name='startDate']").val(),"endTime":$("input[name='endDate']").val()}},"batch",{data:"batchNum",value:"batchNum"});
                getBatchNoAutocomplete("/proxy-gsp/gsp/drugReportDetatil/queryBatchNumByCode",{paramName:'productBatchNo',params:{"productCode":$("input[name='productCode']").val(),"startTime":$("input[name='startDate']").val(),"endTime":$("input[name='endDate']").val(),"supplierCode":result.data}},"batch",{data:"productBatchNo",value:"productBatchNo"});
            }else{

                $("#"+obj).val(result.data);
            }

            /*  var selectedRowData = $("#info_table_report").XGrid("getRowData");
              var flag = 0;
              if($('#supplierName_new').next().val() != "" && $('#batch').val() != "" && selectedRowData.length >= 1){
                  for(var x = 0; x < selectedRowData.length; x ++){
                      if(selectedRowData[x].supplierCode == $('#supplierName_new').next().val() && selectedRowData[x].productBatchNo == $('#batch').val()){
                          flag = 1;
                      }
                  }
              }

              if(flag == 1){
                  $("#"+obj).val("");
                  if(obj=='supplierName'){
                      $("#"+obj).next().val("");
                  }
                  utils.dialog({content: '已有该供应商和该批号下的药检报告！', quickClose: true, timeout: 3000}).showModal();
                  return false;
              }
              select && select(result)*/
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            $("#"+obj).val("");
            if(obj=='supplierName'){
                $("#"+obj).next().val("");
            }
            noneSelect && noneSelect();
        }
    });
}

//时间转化
function format(shijianchuo){
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }

function submitDrugTestReport(_obj){
    //var supplierCode = $("input[name='supplierName']").val();
    var supplierCode = $("#supplierName_new").val();//$("input[name='supplierName']").val();
    var productBatchNo = $("#batch").val();

    if($('#drugTestReportForm').find('li').length > 0 && supplierCode != "" && productBatchNo != ""){
        var drugTestReportVo = $('#drugTestReportForm').serializeToJSON();
        var detailsVOList = [];
        $('#drugTestReportForm').find('li').each(function(){
            var enclosureUrl = $(this).find("input[name='enclosureUrl']").val();
            var enclosureName = $(this).find("input[name='enclosureName']").val();
            var detailsVO = {"enclosureUrl":""+enclosureUrl+"","enclosureName":""+enclosureName +""};
            detailsVOList.push(detailsVO);
        });
        drugTestReportVo.productBatchNo = productBatchNo;
        drugTestReportVo.supplierName =supplierCode;
        drugTestReportVo.detailsVOList = detailsVOList;
        var paraData=JSON.stringify(drugTestReportVo);
        console.log(paraData);
        $.ajax({
            type:"post",
            url: '/proxy-gsp/gsp/drugReportDetatil/addOrEditStoreProductDetail',
            async : false,
            data: paraData,
            dataType:"json",
            contentType: "application/json",
            success: function (data) {
                pdfToJpg(paraData);
                pushToEc(paraData);
                utils.dialog({
                    title: "提示",
                    content: "保存成功！",
                    width:300,
                    height:30,
                    okValue: '确定',
                    ok: function () {
                        //
                        $('.nav-tabs>li:eq(1)').removeClass('active');
                        $('.nav-tabs>li:eq(0)').addClass('active');
                        $('.nav-content>.panel-body:eq(1)').hide();
                        $('.nav-content>.panel-body:eq(0)').show();
                        $("#info_table_report").XGrid("clearGridData");
                        $("#Y_Tablea").XGrid("clearGridData");
                        listDownloadId = [];
                        reloadGridFunc(_obj);
                    }
                }).showModal();
            },
            error:function () {
                utils.dialog({content: '保存失败！', quickClose: true, timeout: 3000}).showModal();
            }
        });
        return true;
    }else if(supplierCode == ""){
        utils.dialog({content: '供应商不能为空！', quickClose: true, timeout: 3000}).showModal();
        return false;
    }else if(productBatchNo == ""){
        utils.dialog({content: '批号不能为空！', quickClose: true, timeout: 3000}).showModal();
        return false;
    }else{
        utils.dialog({content: '请上传药检报告附件！', quickClose: true, timeout: 3000}).showModal();
        return false;
    }
}

/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj, tableId) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(tableId).getRowData(parentId);
    if(data.detailsVOList)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'enclosureUrl'
            },
            list:JSON.parse(data.detailsVOList)
        })
    }
}

function pdfToJpg(data){
    $.ajax({
        type:"post",
        url: '/proxy-gsp/gsp/drugReport/pdfToJpg',
        async : false,
        data: data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {

        },
        error:function () {
            //utils.dialog({content: '保存失败！', quickClose: true, timeout: 3000}).showModal();
        }
    });
}

/**
 * 推送药检报告给ec
 * @param data
 */
function pushToEc(data){
    $.ajax({
        type:"post",
        url: '/proxy-gsp/gsp/drugReport/pushToEc',
        async : false,
        data: data,
        dataType:"json",
        contentType: "application/json",
        success: function (data) {

        },
        error:function () {
            //utils.dialog({content: '保存失败！', quickClose: true, timeout: 3000}).showModal();
        }
    });
}

function  upLoadBrand(obj){
    var id=$(obj).parents('tr').attr('id');

    var selRow = $('#Y_Tablea').XGrid('getRowData',id);
    if (selRow) {
        // var drugTestReportVo = $('#drugTestReportForm').serializeToJSON();
        var paraData=JSON.stringify(selRow);
        $.ajax({
            type:"post",
            url: '/proxy-gsp/gsp/drugReportDetatil/checkStoreProductDetail',
            async : false,
            data: paraData,
            dataType:"json",
            contentType: "application/json",
            success: function (data) {
                console.log(data);
                if(data.code==1){
                    utils.dialog({content: '已有该供应商和该批号下的药检报告！', quickClose: true, timeout: 3000}).showModal();
                    return false;
                }else{
                    utils.dialog({
                        title: '新增',
                        width: 700,
                        height:550,
                        content: $("#add_edit_dialog"),
                        okValue: '保存',
                        cancelValue: '取消',
                        onshow: function () {
                            $("#add_edit_dialog").find("input[name='id']").val("");
                            $("#add_edit_dialog").find("input[name='orgCode']").val(selRow.orgCode);
                            $("#add_edit_dialog").find("input[name='productCode']").val(selRow.productCode);
                            $("#add_edit_dialog").find("input[name='commonName']").val(selRow.commonName);
                            $("#add_edit_dialog").find("input[name='productName']").val(selRow.productName);
                            $("#add_edit_dialog").find("input[name='manufacturerName']").val(selRow.manufacturerName);
                            $("#add_edit_dialog").find("input[name='productSpecification']").val(selRow.productSpecification);
                            $("#add_edit_dialog").find("input[name='supplierCode']").val(selRow.supplierCode);
                            // $("#add_edit_dialog").find("input[name='supplierName']").val(selRow.supplierName);
                            // $("#add_edit_dialog").find("input[name='supplierName']").attr("readonly","readonly")
                            // $("#add_edit_dialog").find("input[name='productBatchNo']").attr("readonly","readonly")
                            // $("#add_edit_dialog").find("input[name='productBatchNo']").val(selRow.productBatchNo);
                            $("#supplierName_no").attr("readonly","readonly");
                            $("#no_batch").attr("readonly","readonly");
                            $("#batch").css('display','none');
                            $("#no_batch").css('display','block');
                            $("#no_batch").val(selRow.productBatchNo);
                            $("#batch").val(selRow.productBatchNo);
                            $("#supplierName_new").css('display','none');
                            $("#supplierName_new").val(selRow.supplierName);
                            $("#supplierName_no").css('display','block');
                            $("#supplierName_no").val(selRow.supplierName);

                            $('#editFlag').val("1");
                            $("").appendTo($("#previewBox").show().find("ul"));
                            //  $('#supplierName_new').unautocomplete();//清除上一次结果

                            // var disabled = $("#supplierName_new" ).Autocomplete().disable();
                            $("#previewBox").find("li").remove();
                            $("#btnUpload").removeAttr("disabled");
                            //  valAutocomplete("/drug/Report/Detatil/querySupplierByCode",{paramName:'productBatchNo',params:{"productCode":$("input[name='productCode']").val(),"startStoreTime":$("input[name='startDate']").val(),"endStoreTime":$("input[name='endDate']").val()}},"supplierName_new",{data:"supplierCode",value:"supplierName"});
                        },
                        ok: function () {
                            let o = {
                                id: selRow['id'],
                                dom: $('#' +selRow['id']),
                                obj: selRow
                            }
                            //保存按钮回调
                            return submitDrugTestReport(o);
                        },
                        cancel: function () {
                        },
                        onclose: function () {

                        },
                    }).showModal();
                }

            },
            error:function () {
                //utils.dialog({content: '保存失败！', quickClose: true, timeout: 3000}).showModal();
            }
        });


    }else {
        utils.dialog({content: '没有选中任何行！', quickClose: true, timeout: 2000}).showModal();
    }


}
