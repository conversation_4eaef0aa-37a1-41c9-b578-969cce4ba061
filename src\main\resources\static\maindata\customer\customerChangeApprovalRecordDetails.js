$(function () {
    var id = $("#customerApplId").val();
    $('div[fold=head]').fold({
        sub: 'sub'
    });
//显示流程图
    var processInstaId=$("#processId").val();
    if(!processInstaId || processInstaId == ""){
        processInstaId=$("#approvalProcessId").val();
    }
    initApprovalFlowChart("customerFirstCampApply", processInstaId);

    //结算方式
    $("input[name='itemValue']:not(:checked)").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').hide();
    });
    $("input[name='itemValue']:checked").each(function() {
        $(this).parent().parent().parent().children().not('.checkbox').show();
    });

    //tab切换
    $('.nav-tabs>li').on('click', function () {
        var $this = $(this),
            $nav_content = $this.parent().next();
        $this.addClass('active').siblings().removeClass('active');
        $nav_content.children('div').eq($this.index()).show().siblings().hide();
    })

    //切换的质量保证协议
    // $('#table1').XGrid({
    //     url:"/proxy-customer/customer/customerCommonData/getCustomerQualityAgreementAll?correlationId="+id+"&&type=4",
    //     colNames: ['', '签订日期', '有效期至', '签订人', '附件','附件数据'],
    //     colModel: [{
    //         name: 'id', //与反回的json数据中key值对应
    //         index: 'id', //索引。其和后台交互的参数为sidx
    //         key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    //         hidden: true
    //     }, {
    //         name: 'signedDate',
    //         index: 'signedDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'validDate',
    //         index: 'validDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'signer',
    //         index: 'signer'
    //     }, {
    //         name: 'enclosureCount',
    //         index: 'enclosureCount',
    //         formatter:function (value) {
    //             var str='无';
    //             if(value)
    //             {
    //                 var tableId = "#table1"
    //                 str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
    //             }
    //             return str;
    //         },
    //         unformat: function (e) {
    //             e=e.replace(/<[^>]+>/g,'');
    //             if(e == '无'){
    //                 e = 0;
    //             }
    //             return e;
    //         }
    //     },{
    //         name:'customerEnclosureVoList',
    //         index:'customerEnclosureVoList',
    //         hidden:true,
    //         formatter:function (value) {
    //             if(value)
    //             {
    //                 return JSON.stringify(value);
    //             }
    //             return JSON.stringify([]);
    //         }
    //     }],
    //     altRows: true,
    //     rownumbers: true/*, //设置为交替行表格,默认为false
    //      ondblClickRow: function (id,dom,obj,index,event) {
    //      console.log('双击行事件', id,dom,obj,index,event);
    //      },
    //      onSelectRow: function (id,dom,obj,index,event) {
    //      console.log('单机行事件',id,dom,obj,index,event);
    //      },*/
    //     //pager:'#table1_page'
    // });

    //客户委托书
    $('#table2').XGrid({
        url:"/proxy-customer/customer/customerCommonData/getCustomerDelegationFileAll?correlationId="+id+"&&type=4",
        // colNames: ['', '姓名', '性别', '电话', '证件号码',  '身份证有效期至', '委托书有效期至','附件','附件数据'
        // ],
        colNames: ['', '姓名',  '电话', '委托书有效期至','附件','附件数据'
        ],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'delegationName',
            index: 'delegationName'
        },
        //     {
        //     name: 'delegationSex',
        //     index: 'delegationSex',
        //     formatter: function (e) {
        //         if (e == '1') {
        //             return '男'
        //         } else if (e == '2') {
        //             return '女'
        //         } else {
        //             return '未知'
        //         }
        //     }
        // },
            {
            name: 'delegationTel',
            index: 'delegationTel'
        },
        //     {
        //     name: 'delegationNum',
        //     index: 'delegationNum'
        // },
        // {
        //     name: 'delegationAddr',
        //     index: 'delegationAddr'
        // },
        //     {
        //     name: 'delegationIdentityDate',
        //     index: 'delegationIdentityDate',
        //     formatter:function(value){
        //         var date=value;
        //         if(!value)return false;
        //         date=format(value);
        //         return date.split(' ')[0];
        //     }
        // },
        {
            name: 'validityDelegationCredential',
            index: 'validityDelegationCredential',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'enclosureCount',
            index: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#table2";
                    str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        },{
            name:'customerEnclosureVoList',
            index:'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],
        altRows: true,
        rownumbers: true/*, //设置为交替行表格,默认为false
         ondblClickRow: function (id,dom,obj,index,event) {
         console.log('双击行事件', id,dom,obj,index,event);
         },
         onSelectRow: function (id,dom,obj,index,event) {
         console.log('单机行事件', id,dom,obj,index,event);
         },*/
        //pager:'#table2_page'
    });

    //被委托人身份证
    $('#certTable').XGrid({
        url:"/proxy-customer/customer/customerCommonData/getCustomerBeProxyIdentityAll?correlationId="+id+"&&type=4",
        colNames: ['', '姓名', '证件号码', '身份证有效期至', '附件','附件数据'],
        colModel: [{
            name: 'id', //与反回的json数据中key值对应
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'customerName',
            index: 'customerName'
        }, {
            name: 'identityNumber',
            index: 'identityNumber'
        }, {
            name: 'identityValidityDate',
            index: 'identityValidityDate',
            formatter:function(value){
                var date=value;
                if(!value)return false;
                date=format(value);
                return date.split(' ')[0];
            }
        }, {
            name: 'enclosureCount',
            index: 'enclosureCount',
            formatter:function (value) {
                var str='无';
                if(value)
                {
                    var tableId = "#certTable";
                    str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
                }
                return str;
            },
            unformat: function (e) {
                e=e.replace(/<[^>]+>/g,'');
                if(e == '无'){
                    e = 0;
                }
                return e;
            }
        },{
            name:'customerEnclosureVoList',
            index:'customerEnclosureVoList',
            hidden:true,
            formatter:function (value) {
                if(value)
                {
                    return JSON.stringify(value);
                }
                return JSON.stringify([]);
            }
        }],
        altRows: true,
        rownumbers: true/*, //设置为交替行表格,默认为false
         ondblClickRow: function (id,dom,obj,index,event) {
         console.log('双击行事件', id,dom,obj,index,event);
         },
         onSelectRow: function (id,dom,obj,index,event) {
         console.log('单机行事件', id,dom,obj,index,event);
         },*/
        //pager:'#certTable_page'
    });

    //批准文件
    // $('#table3').XGrid({
    //     url: "/proxy-customer/customer/customerCommonData/getCustomerApprovalFileAll?correlationId="+id+"&&type=4",
    //     colNames: ['', '证书类型', '证书编号', '经营范围', '发证机关', '发证日期', '有效期至', '附件','附件数据'],
    //     colModel: [{
    //         name: 'id', //与反回的json数据中key值对应
    //         index: 'id', //索引。其和后台交互的参数为sidx
    //         key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
    //     }, {
    //         name: 'credentialTypeId',
    //         index: 'credentialTypeId',
    //         rowtype: '#credentialTypeId'
    //     }, {
    //         name: 'credentialCode',
    //         index: 'credentialCode'
    //     }, {
    //         name: 'businessScope',
    //         index: 'businessScope'
    //     }, {
    //         name: 'certificationOffice',
    //         index: 'certificationOffice'
    //     }, {
    //         name: 'openingDate',
    //         index: 'openingDate',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'validUntil',
    //         index: 'validUntil',
    //         formatter:function(value){
    //             var date=value;
    //             if(!value)return false;
    //             date=format(value);
    //             return date.split(' ')[0];
    //         }
    //     }, {
    //         name: 'enclosureCount',
    //         index: 'enclosureCount',
    //         formatter:function (value) {
    //             var str='';
    //             if(value)
    //             {
    //                 var tableId = "#table3"
    //                 str ='<a href="javascript:;" onclick="showImg(this, \''+tableId+'\');">'+value+'</a>';
    //             }
    //             return str;
    //         },
    //         unformat: function (e) {
    //             e=e.replace(/<[^>]+>/g,'');
    //             return e;
    //         }
    //     },{
    //         name:'customerEnclosureVoList',
    //         index:'customerEnclosureVoList',
    //         hidden:true,
    //         formatter:function (value) {
    //             if(value)
    //             {
    //                 return JSON.stringify(value);
    //             }
    //             return JSON.stringify([]);
    //         }
    //     }],
    //     rowNum: 10,
    //     altRows: true/*, //设置为交替行表格,默认为false
    //     ondblClickRow: function (id,dom,obj,index,event) {
    //         console.log('双击行事件',id,dom,obj,index,event);
    //     },
    //     onSelectRow: function (id,dom,obj,index,event) {
    //         console.log('单机行事件', id,dom,obj,index,event);
    //     },*/
    //     //pager:'#table3_page'
    // });
    initTable3(id,4);
    //客户合同回显
    // $.ajax({
    //     url:'/proxy-customer/customer/customerCommonData/getCustomerContractAll?correlationId='+id+'&type=4',
    //     dataType:'json',
    //     //async:false,
    //     success:function(data){
    //         if(!data.result) return false;
    //         if(data.result.length){
    //             $(data.result).each(function(contractIndex,ontractItem){
    //                 var contractTypes = $('#contractType').find('input[type=checkbox]');
    //                 $(contractTypes).each(function(index,item){
    //                     if(ontractItem.contractType==$(this).val()){
    //                         $(this).attr("checked",'true');
    //                         if(ontractItem.customerEnclosureVoList && ontractItem.customerEnclosureVoList.length > 0){
    //                             for(var j=0;j<ontractItem.customerEnclosureVoList.length;j++)
    //                             {
    //                                 ontractItem.customerEnclosureVoList[j].type = ontractItem.contractType;
    //                             }
    //                             var arr=JSON.stringify(ontractItem.customerEnclosureVoList);
    //                             var html='<input type="hidden" data-type="'+ontractItem.contractType+'" id="contractType'+ontractItem.contractType+'" value=\''+arr+'\' />';
    //                             $("body").append(html);
    //                         }
    //                     }
    //                 });
    //             });
    //         }
    //     },
    //     error:function(){
    //
    //     }
    // });
    // //客户合同 查看附件
    // $("#qtfjView").on("click",function(){
    //     //获取type类型
    //     var imgList=[];
    //     var checkInp=$("#contractType input[type='checkbox']:checked");
    //     if(checkInp.length)
    //     {
    //         for(var i=0;i<checkInp.length;i++)
    //         {
    //             var type=checkInp.eq(i).val();
    //             var inp=$("#contractType"+type);
    //             if(inp.length)
    //             {
    //                 var imgArr=JSON.parse(inp.val());
    //                 imgList=imgList.concat(imgArr);
    //             }
    //         }
    //     }
    //     $.viewImg({
    //         fileParam:{
    //             name:'enclosureName',
    //             url:'url'
    //         },
    //         list:imgList
    //     })
    // });

    // $('#qtfjUpload').hide();

    $("div[name='operateRows']").hide();

    //2018-07-23 13
    var townFormat = function(obj,info){
        $(obj).find("select:last").html('');
        if(info['code']%1e4&&info['code']<7e5){	//是否为“区”且不是港澳台地区
            $.ajax({
                url:'/proxy-sysmanage/sysmanage/area/getNexAreaListByPcode?pCode='+info['code'],
                dataType:'json',
                //async:false,
                success:function(town){
                    $(obj).find("select:last").html('');
                    $(obj).find("select:last").append('<option value="">请选择</option>');
                    var arr=town.result;
                    if(arr && arr.length>0)
                    {
                        for(var i=0;i<arr.length;i++)
                        {
                            var code='';
                            var text='';
                            for(name in arr[i]){
                                if(name == 'code')
                                {
                                    code=arr[i][name];
                                }
                                if(name == 'name')
                                {
                                    text=arr[i][name];
                                }
                            }
                            $(obj).find("select:last").append('<option value="'+code+'">'+text+'</option>');
                        }
                        var flag=$(obj).attr("data-flag");
                        if(flag == "true")
                        {
                            var val3=$.trim($(obj).find("select:last").attr("data-value"));
                            if(val3 && val3 != '')
                            {
                                $(obj).find("select:last").val(val3);
                            }
                        }
                        $(obj).attr("data-flag","false");
                    }
                }
            });
        }
    };
    initCitys($('#registerBox'),'registerProvinceId','registerCityId','registerDistrictId');
    initCitys($('#storageBox'),'storageProvinceId','storageCityId','storageDistrictId');
    initCitys($('#billingBox'),'billingProvinceId','billingCityId','billingDistrictId');
    initCitys($('.depotList'),'billingProvinceId','billingCityId','billingDistrictId');

    //初始化四级联动
    function initCitys(obj,provinceField,cityField,areaField){
        obj.attr("data-flag","true").citys({
            dataUrl:'/proxy-sysmanage/sysmanage/area/getAreaExtStreet',
            provinceField:provinceField, //省份字段名
            cityField:cityField,         //城市字段名
            areaField:areaField,         //地区字段名
            onChange:function(obj,info){
                townFormat(obj,info);
            }
        },function(obj){
            $(obj).find("select:last").html('<option value="">请选择</option>');
            var flag=$(obj).attr("data-flag");
            if(flag == "true")
            {
                var dataVal=$.trim($(obj).find("select:first").attr("data-value"));
                if(!dataVal || dataVal == '')return;

                $(obj).find("select:first").val(dataVal);
                $(obj).find("select:first").change();
                var dataVal1=$.trim($(obj).find("select").eq(1).attr("data-value"));
                $(obj).find("select").eq(1).val(dataVal1);
                var val1=$(obj).find("select").eq(1).val();
                if(!val1 || val1 == '')return;

                $(obj).find("select").eq(1).change();
                var dataVal2=$.trim($(obj).find("select").eq(2).attr("data-value"));
                if(!dataVal2 || dataVal2 == '')return;

                $(obj).find("select").eq(2).val(dataVal2);
                var val2=$(obj).find("select").eq(2).val();

                if(!val2 || val2 == '')return;
                $(obj).find("select").eq(2).change();
            }
        });
    }
    function getHTML(option){
        var html='<div class="col-md-6 depotList">\
                    <div class="input-group">\
                        <div class="input-group-addon">'+option.title+'</div>\
                        <div data-toggle="distpicker" class="form-control form-inline distpicker">\
                            <div class="row">\
                                <div class="form-group col-md-2">\
                                    <select class="form-control" name="billingProvinceId"></select>\
                                </div>\
                                <div class="form-group col-md-2">\
                                    <select class="form-control" name="billingCityId"></select>\
                                </div>\
                                <div class="form-group col-md-2">\
                                    <select class="form-control" name="billingDistrictId"></select>\
                                </div>\
                                <div class="form-group col-md-2">\
                                    <select class="form-control" name="billingStreetId"></select>\
                                </div>\
                                <div class="form-group col-md-4">\
                                    <input type="text" class="form-control text-inp" name="billingAddress"/>\
                                </div>\
                                <div class="form-group btn-box col-md-1">\
                                    <button type="button" class="btn removeDepot">\
                                        <span class="glyphicon glyphicon-minus" aria-hidden="true"></span>\
                                    </button>\
                                </div>\
                            </div>\
                        </div>\
                    </div>\
                </div>';
        return html;
    }
    $('[data-toggle="distpicker"]').distpicker();
    $("input[type='text']").attr("disabled","disabled");
    $("input[type='tel']").attr("disabled","disabled");
    $("input[type='number']").attr("disabled","disabled");
    $("input[type='checkbox']").attr("disabled","disabled");
    $("input[type='radio']").attr("disabled","disabled");
    $("textarea").attr("disabled","disabled");
    $("select").attr("disabled","disabled");
    $('#auditOpinion').removeAttr("disabled");
})

//经营方式
$("#businessPatternName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:3},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    onSelect: function (result) {
        $("#businessPattern").val(result.data);
        $("#businessPatternName").val(result.value);
    },
    onNoneSelect: function (params, suggestions) {
        console.log(params, suggestions);
        if(suggestions.length >0) {
            $("#businessPattern").val(suggestions[0].data);
            $("#businessPatternName").val(params.clientName);
        }
    }
});
//经营类别
$("#businessCategoryName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:1},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    onSelect: function (result) {
        $("#businessCategory").val(result.data);
        $("#businessCategoryName").val(result.value);
    },
    onNoneSelect: function (params, suggestions) {
        console.log(params, suggestions);
        if(suggestions.length >0) {
            $("#businessCategory").val(suggestions[0].data);
            $("#businessCategoryName").val(params.clientName);
        }
    }
});

//客户类别
$("#customerTypeName").Autocomplete({
    serviceUrl:"/proxy-sysmanage/sysmanage/dict/queryClient", //异步请求
    paramName: 'clientName',//查询参数，默认 query
    params:{type:2},//多参数
    dataType: 'json',
    minChars: '0', //触发自动匹配的最小字符数
    maxHeight: '300', //默认300高度
    dataReader:{
        'list':'result',
        'data':'clientId',
        'value':'clientName'
    },
    triggerSelectOnValidInput: false, // 必选
    showNoSuggestionNotice: true, //显示查无结果的container
    noSuggestionNotice: '查询无结果',//查无结果的提示语
    transformResult: function (response) {
        response.result = response.result.filter(item => item.clientName != '基层医疗机构')
        return response
    },
    onSelect: function (result) {
        $("#customerType").val(result.data);
        $("#customerTypeName").val(result.value);
    },
    onNoneSelect: function (params, suggestions) {
        if(suggestions.length >0) {
            $("#customerType").val(suggestions[0].data);
            $("#customerTypeName").val(params.clientName);
        }
    }
});

function format(shijianchuo)
{
//shijianchuo是整数，否则要parseInt转换
    var time = new Date(shijianchuo);
    var y = time.getFullYear();
    var m = time.getMonth()+1;
    var d = time.getDate();
    var h = time.getHours();
    var mm = time.getMinutes();
    var s = time.getSeconds();
    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}
function add0(m){return m<10?'0'+m:m }

/**
 * 附件图片查看
 * @param  this 当前点击对象 this eg:showImg(this)
 * */
function showImg(obj, tableId) {
    var parentId=$(obj).parents("tr").attr("id");
    var data=$(tableId).getRowData(parentId);
    if(data.customerEnclosureVoList)
    {
        $.viewImg({
            fileParam:{
                name:'enclosureName',
                url:'url'
            },
            list:JSON.parse(data.customerEnclosureVoList)
        })
    }
}

//经营类别回显
if ($('#businessCategory').val()) {
    var businessCategory = $('#businessCategory').val().split(',');
    $("input[name='businessCategoryName']").each(function () {
        for(var x = 0; x < businessCategory.length; x ++){
            if($(this).val() == businessCategory[x]){
                $(this).attr("checked","checked");
            }
        }
    });
}


//不可经营类别回显
if ($('#cannotBusinessCategory').val()) {
    var cannotBusinessCategory = $('#cannotBusinessCategory').val().split(',');
    $("input[name='cannotBusinessCategoryName']").each(function () {
        for(var x = 0; x < cannotBusinessCategory.length; x ++){
            if($(this).val() == cannotBusinessCategory[x]){
                $(this).attr("checked","checked");
            }
        }
    });
}


//批准文件,质量保障协议,客户委托书,被委托人 一键下载
function patchDownload(tableId){
    downloadTableAttachFiles(tableId)
}
function downloadTableAttachFiles(tableId){
    //获取type类型
    var typeList=[];
    var eChoImgList=[];
    var $table=$(tableId);
    var rowData=$table.getRowData();
    console.log(rowData)
    var $tr=$table.find("tr").not(":first");
    for(var i=0;i<$tr.length;i++)
    {
        var sel=$tr.eq(i).find("select[name='credentialTypeId'] option:selected");
        typeList.push({
            text:sel.text(),
            value:sel.val()
        });
        console.log(sel.val())
        //添加已存在附件
        if(rowData[i].customerEnclosureVoList.length > 0)
        {
            rowData[i].customerEnclosureVoList=JSON.parse(rowData[i].customerEnclosureVoList);
            for(var j=0;j<rowData[i].customerEnclosureVoList.length;j++)
            {
                rowData[i].customerEnclosureVoList[j].type=sel.val();
            }
            eChoImgList = eChoImgList.concat(rowData[i].customerEnclosureVoList);
        }
    }
    console.log(eChoImgList)
    // downloadImg("/static/MobileToken.png") //同源图片，下载有效
    const fileUrls = []
    const fileNames = []
    eChoImgList.forEach((item,index)=>{
        if (item.url && item.url.length) {
            fileUrls.push(item.url)
            let fileName = item.enclosureName
            if (!fileName){
                fileName = index + ''
            }
            fileNames.push(fileName)
        }
    })
    downloadImg(fileUrls,fileNames)
}
//客户合同 一键下载
// TODO 待验证
// function qtfjPatchDownload() {
//     let eChoImgList = []
//     var checkInp=$("#contractType input[type='checkbox']:checked");
//     if(checkInp.length)
//     {
//         for(var i=0;i<checkInp.length;i++)
//         {
//             var type=checkInp.eq(i).val();
//             var inp=$("#contractType"+type);
//             if(inp.length)
//             {
//                 var imgArr=JSON.parse(inp.val());
//                 eChoImgList=eChoImgList.concat(imgArr);
//             }
//         }
//     }
//     const fileUrls = []
//     const fileNames = []
//     eChoImgList.forEach((item,index)=>{
//         if (item.url && item.url.length) {
//             fileUrls.push(item.url)
//             let fileName = item.enclosureName
//             if (!fileName){
//                 fileName = index + ''
//             }
//             fileNames.push(fileName)
//         }
//     })
//     downloadImg(fileUrls,fileNames)
// }
function downloadImg(fileUrls,fileNames){
    if (!(fileUrls.length * fileNames.length)){
        utils.dialog({content: '暂无附件下载', quickClose: true, timeout: 2000}).showModal();
        return
    }
    window.open("/proxy-customer/customer/customerFirstAppl/downloadZip?fileUrls="+fileUrls+"&fileNames="+fileNames+"&zipFileName=导出")
}
