var getData;
$(function () {
    /* 模拟数据 */
    /*var data_a = [{
        productCode: "Y010101591",
        productName: "西地碘含片(华素片)",
        size: "1.5毫克*15",
        dosage: "片剂",
        enterprise: "北京华素制药股份有限公司",
        from: "北京",
        unit: "盒",
        number: "10",
        price: "7.300",
        sum: "73.00",
        sterilizationBatch: "",
        batch: "1805072",
        productionDate: "2018-04-30",
        effectiveDate: "2020-04-30",
        storageName: "合格库",
        locationName: "合格库",
        approvalNumber: "国药准字H10910012",
        quality: "合格",
    }, {
        productCode: "Y010101591",
        productName: "西地碘含片(华素片)",
        size: "1.5毫克*15",
        dosage: "片剂",
        enterprise: "北京华素制药股份有限公司",
        from: "北京",
        unit: "盒",
        number: "10",
        price: "7.300",
        sum: "73.00",
        sterilizationBatch: "",
        batch: "1805072",
        productionDate: "2018-04-30",
        effectiveDate: "2020-04-30",
        storageName: "合格库",
        locationName: "合格库",
        approvalNumber: "国药准字H10910012",
        quality: "合格",
    }];

    var data_b = [{
        indentCode: "YBM20180817102154100237",
        carriage: "0",
        salesSum: "1161.25",
        couponSum: "100",
        paySum: "1061.25",
        paySumC: "壹仟零陆拾壹元贰角伍分",
        isOnlinePay: "已支付",
        payType: "A"
    }];

    var data_c = [{
        key: "发票情况",
        value: [],
        other: "",
    },
        {
            key: "结款情况",
            value: [],
            other: "缓存区名称：Y21-56--Y21-56",
        },
        {
            key: "结款方式",
            value: [3],
            other: "",
        },
        {
            key: "备注",
            value: "急用",
            other: "",
        },
    ];*/
    /* 日期格式化 */
    //时间格式化例如：var date = format(val,'yyyy-MM-dd HH:mm:ss');return date;
    function dateFormat(time, format) {
        var t = new Date(parseInt(time,10));
        var tf = function (i) {
            return (i < 10 ? '0' : '') + i;
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        });
    }



    /* 获取数据 */
    getData = function (printType,outOrderCode) {
        var outOrderCodes = outOrderCode.join(',');
        $.ajax({
            url:"/proxy-order/order/orderOut/orderOut/findOutOrderPrintList",
            data:{
                outOrderCodes:outOrderCodes,
                printType: printType
            },
            success:function(res){
                if($.isArray(res.result)){
                    if (res.result.length>0){
                        webRender(res.result,printType);
                    }else {
                        utils.dialog({
                            title:'提示',
                            content:"该订单还没有生成出库详情",
                            okValue:'确定',
                            ok:function () {}
                        }).showModal();
                    }
                }else {
                    utils.dialog({
                        title:'提示',
                        content:"数据为空或格式不正确",
                        okValue:'确定',
                        ok:function () {}
                    }).showModal();
                }
            },
        })
    };
    /* 数据渲染 */
    function webRender(data,printType) {
        var box_html = '';
        /* 基本结构拼装 */
        data.forEach(function (item,index) {
            console.log(item,item.orgCode);

            /* 销售出库复核单 */
            box_html +=`
                   <div class="content indent1">
                    <div class="header">
                        <div class="title">合肥小药药医药科技有限公司销售出库单复核单(随货同行)</div>
                    </div>
                    <div class="top">
                        <ul class="info_list">
                            <li>
                                <span class="col_8">客户编号：<i class="val">${item.customerCode?item.customerCode:""}</i></span>
                                <span class="col_2" style="margin-left:-26px;overflow: initial;"> 药品属于特殊商品，非质量问题一律不予退货！</span>
                            </li>
                            <li>
                                <span class="col_8">客户名称：<i class="val">${item.customerName?item.customerName:""}</i></span>
                                <span class="col_2" style="margin-left:-26px;"> 售后服务：<i class="val">400-0505-111</i></span>
                            </li>
                            <li>
                                <span class="col_8">客户电话：<i class="val">${item.receiverPhone?item.receiverPhone:""}</i></span>
                                <span class="col_2" style="margin-left:-26px;">单据编号：<i class="val">${item.salesOrderCode?item.salesOrderCode:""}</i></span>
                            </li>
                            <li>
                                <span class="col_4">收货地址：<i class="val">${item.address}</i></span>
                                <span class="col_2">开票日期：<i class="val">${item.slaseOrderCreateTime?dateFormat(item.slaseOrderCreateTime, 'yyyy-MM-dd'):""}</i></span>
                                <span class="col_2">发货日期：<i class="val">${item.createTime?dateFormat(item.createTime, 'yyyy-MM-dd'):""}</i></span>
                                <span class="col_2" style="margin-left:-26px;">订单编号：<i class="val">${item.ecOrderCode?item.ecOrderCode:""}</i></span>
                            </li>
                        </ul>
                        <span class="inden_type">销</span>
                    </div>
                    <table id="table_a_${index}"></table>
                    <div class="bottom">
                        <ul class="info_list">
                            <li>
                                <span class="col_4">发货地址：<i class="val"></i></span>
                                <span class="col_2">复核员：<i class="val"> </i></span>
                                <span class="col_1">共<i class="val">${item.pageTotal}</i>页，第<i class="val">${item.pageNumber}</i>页</span>
                                <span class="col_3">白联：财务部 红联：随货同行 蓝联：储运部 绿联：回执</span>
                            </li>
                            <li>
                                <span class="col_4"><span class="col_5">销售电话：0731-86395690</span>
                                <span class="col_5">售后服务：0731-86368589</span></span>
                            </li>
                        </ul>
                    </div>
                    <div style="page-break-after:always"></div>
                </div>
                `;
            /* 销售汇总单 */
            if(item.pageNumber==item.pageTotal){
                box_html +=`
                        <div class="content indent2">
                        <div class="header">
                            <div class="title">合肥小药药医药科技有限公司销售汇总单(货物交接单)</div>
                        </div>
                        <div class="top">
                            <ul class="info_list">
                                <li>
                                    <span class="col_8"></span>
                                    <span class="col_2" style="margin-left:-26px;"></span>
                                </li>
                                <li>
                                    <span class="col_8">客户编号：${item.customerCode?item.customerCode:""}</span>
                                    <span class="col_2" style="margin-left:-26px;"></span>
                                </li>
                                <li>
                                    <span class="col_8">客户名称：${item.customerName?item.customerName:""}</span>
                                    <span class="col_2" style="margin-left:-26px;">如含有特殊药品，概不接受现金结算</span>
                                </li>
                                <li>
                                    <span class="col_6">收货地址：${item.address}</span>
                                    <span class="col_2">发货日期：${item.createTime?dateFormat(item.createTime, 'yyyy-MM-dd'):""}</span>
                                    <span class="col_2" style="margin-left:-26px;">车牌号码：</span>
                                </li>
                            </ul>
                        </div>
                        <table id="table_b_${index}"></table>
                        <table id="table_c_${index}" class="table_c"></table>
                        <div class="bottom">
                            <ul class="info_list">
                                <li>
                                    <span class="col_4">${item.pcs?item.pcs:"整件数：0 — 拼箱数：0 — 总箱数：0"}</span>
                                    <span class="col_2">运输员：</span>
                                    <span class="col_2">客户签名：</span>
                                    <span class="col_2">送达时间：</span>
                                </li>
                                <li>
                                    <span class="col_4">药品属于特殊商品，非质量问题一律不予退货！</span>
                                </li>
                            </ul>
                        </div>
                        <div style="page-break-after:always"></div>
                    </div>
                    `;
            }

        });

        $("#box").html(box_html);

        /* 表格初始化 */
        data.forEach(function (item,index) {

            item.detailVoList = item.detailVoList.map(function (val,key) {
                delete val.id;
                return val
            });
            /* 销售出库复核单 */
            $("#table_a_"+index).jqGrid({
                data: item.detailVoList,
                datatype: "local", //数据来源，本地数据（local，json,jsonp,xml等）
                height: "auto", //高度，表格高度。可为数值、百分比或'auto'
                //width: "1322.83",
                colNames: ['商品编号', '商品(药品)通用名称', '规格', '剂型', '生产企业', '产地', '单位', '数量', '单价', '金额', '灭菌批号', '批号', '生产日期', '有效期至', '批准文号', '质量状况'],
                colModel: [{
                    index: 'productCode',
                    name: 'productCode',
                    width: 70,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 6) {
                            //金额合计(大写) name
                            return 'colspan=2'
                        }
                    }
                }, {
                    index: 'productName',
                    name: 'productName',
                    width: 180,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        //console.log(rowId, tv, rawObject, cm, rdata);
                        if (rowId == 5) {
                            return 'colspan=6'
                        } else if (rowId == 6) {
                            //金额合计(大写) value
                            return 'colspan=5 style="text-align: left;padding-left:5px;"'
                        }
                    }
                }, {
                    index: 'productSpec',
                    name: 'productSpec',
                    width: 90,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 5) {
                            return 'style="display:none"'
                        } else if (rowId == 6) {
                            //金额合计 name
                            return 'colspan=2'
                        }
                    }
                }, {
                    index: 'dosageForm',
                    name: 'dosageForm',
                    width: 60,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 5) {
                            return 'style="display:none"'
                        } else if (rowId == 6) {
                            //金额合计 value
                            return 'colspan=7 style="text-align: left;padding-left:5px;"'
                        }
                    }
                }, {
                    index: 'productmManufacturer',
                    name: 'productmManufacturer',
                    width: 180,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 5) {
                            return 'style="display:none"'
                        }
                    }
                }, {
                    index: 'producingArea',
                    name: 'producingArea',
                    width: 60,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 5) {
                            return 'style="display:none"'
                        }
                    }
                }, {
                    index: 'producingPackingUnit',
                    name: 'producingPackingUnit',
                    width: 40,
                    cellattr: function (rowId, tv, rawObject, cm, rdata) {
                        if (rowId == 5) {
                            return 'style="display:none"'
                        }
                    }
                }, {
                    index: 'outStoreNumber',
                    name: 'outStoreNumber',
                    width: 40,
                    summaryType: function (value, name, record) {
                        console.log(value, name, record);

                        return value
                    }
                }, {
                    index: 'productTaxPrice',
                    name: 'productTaxPrice',
                    width: 60,
                }, {
                    index: 'taxAmount',
                    name: 'taxAmount',
                    width: 80,
                }, {
                    index: 'sterilizationCode',
                    name: 'sterilizationCode',
                    width: 80,
                }, {
                    index: 'batchCode',
                    name: 'batchCode',
                    width: 80,
                }, {
                    index: 'manufactureTime',
                    name: 'manufactureTime',
                    width: 100,
                    formatter:function (e) {
                        if(e){
                            return dateFormat(e, 'yyyy-MM-dd')
                        }else{
                            return ""
                        }
                    }
                }, {
                    index: 'expiryTime',
                    name: 'expiryTime',
                    width: 100,
                }, {
                    index: 'approvalNumber',
                    name: 'approvalNumber',
                    width: 160,
                }, {
                    index: 'quality',
                    name: 'quality',
                    width: 60,
                    formatter:function (val,rowType,rowData) {
                        if(rowData.productCode&&rowData.productCode!="小计"&&rowData.productCode!="金额合计(大写)："){
                            return "合格"
                        }else {
                            return ""
                        }
                    }
                },/* {
                    index: 'status',
                    name: 'status',
                    hidden: true,
                    hidegrid: true
                },*/ ],
                shrinkToFit:false,
                rowNum: 4,
                gridview: true,
                gridComplete: function () {
                    var sum_number = $(this).getCol('outStoreNumber', false, 'sum');
                    var sum_sum = $(this).getCol('taxAmount', false, 'sum');
                    var data = $(this).getRowData();
                    //console.log(data);
                    if (data.length < 4) {
                        $(this).addRowData(data.length + 1, {}, "last");
                    } else if (data.length == 4) {
                        $(this).addRowData(5, {
                            productCode: "小计",
                            outStoreNumber: sum_number,
                            taxAmount: sum_sum,
                        }, "last");
                    } else if (data.length == 5) {
                        $(this).addRowData(6, {
                            productCode: "金额合计(大写)：",
                            productName: item.taxAmountDesc,
                            productSpec: "金额合计：",
                            dosageForm: "￥："+item.taxAmount+"元",
                        }, "last");
                    }
                    //$(this).footerData("set",{"productCode":"合计","number":sum_number,"sum":sum_sum});
                }
            });

            /* 销售汇总单 */
            var data_b = [{
                ecOrderCode:item.ecOrderCode,
                salesOrderCode:item.salesOrderCode,
                expressFee:item.expressFee,
                taxAmount:item.taxAmount,
                activityPreferentialAmount:item.activityPreferentialAmount,
                totalRealPayAmount:item.totalRealPayAmount,
                totalRealPayAmountDesc:item.totalRealPayAmountDesc,
                isOnlinePay:item.payType?"已支付":"未支付",
                payType:item.payType?item.payType:""
            }];
            $("#table_b_"+index).jqGrid({
                data: data_b,
                datatype: "local",
                height: "auto",
                width: "1430",
                colNames: ["订单编号", "运费", "销售金额", "优惠金额", "实付金额(小写)", "实付金额(大写)", "是否在线支付", "支付方式"],
                colModel: [{
                    index: "ecOrderCode",
                    name: "ecOrderCode",
                },
                    {
                        index: "expressFee",
                        name: "expressFee",
                    },
                    {
                        index: "taxAmount",
                        name: "taxAmount",
                    },
                    {
                        index: "activityPreferentialAmount",
                        name: "activityPreferentialAmount",
                    },
                    {
                        index: "totalRealPayAmount",
                        name: "totalRealPayAmount",
                    },
                    {
                        index: "totalRealPayAmountDesc",
                        name: "totalRealPayAmountDesc",
                    },
                    {
                        index: "isOnlinePay",
                        name: "isOnlinePay",
                    },
                    {
                        index: "payType",
                        name: "payType",
                    },
                ],
                shrinkToFit: true,
                rowNum: 1,
                gridview: true,
            });

            var data_c = [
                {
                    key: "报告单情况",
                    value: [],
                    other: "",
                },
                {
                    key: "发票情况",
                    value: [],
                    other: item.temporaryAreaName,
                },
                {
                    key: "售后服务电话",
                    value: [],
                    other: "",
                },
                {
                    key: "备注",
                    value: item.salesOrdersRemarks?item.salesOrdersRemarks:"",
                    other: "",
                },
            ];
            $("#table_c_"+index).jqGrid({
                data: data_c,
                datatype: "local",
                height: "auto",
                width: "1430",
                colNames: ["", "", ""],
                colModel: [{
                    index: "key",
                    name: "key",
                    width:"200",
                },
                    {
                        index: "value",
                        name: "value",
                        width:"400",
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 4 || rowId == 1) {
                                return 'colspan=2'
                            }
                        },
                        formatter: function (e, row, rowData, fromType) {
                            //console.log(e, row, rowData, fromType);
                            if (e instanceof Array) {
                                if (row.rowId == 1) {
                                    return '<span class="col_de"><input type="checkbox" disabled>电子</span><span class="col_de"><input type="checkbox" disabled>纸质</span>'
                                } else if (row.rowId == 2) {
                                    return '<span class="col_de"><input type="checkbox" disabled>电子</span><span class="col_de"><input type="checkbox" disabled>纸质</span>'
                                } else if (row.rowId == 3) {
                                    return '<span class="col_de">400-0505-111</span>'
                                }
                            } else {
                                return e
                            }
                        }
                    },
                    {
                        index: "other",
                        name: "other",
                        width:"400",
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rowId == 2) {
                                return 'rowspan=2 style="text-align: left;padding-left:5px;'
                            }else {
                                return 'style="display:none"'
                            }
                        },
                        formatter:function (e, row) {
                            return '暂存区名称：'+ e
                        }
                    },
                ],
                shrinkToFit: true,
                rowNum: 4,
                gridview: true,
                gridComplete: function () {
                    //隐藏表头
                    $(this).closest('.ui-jqgrid-view').find('div.ui-jqgrid-hdiv').hide();
                }
            });

        });


        if(printType==0){
            /* 打印预览 */
            utils.dialog({
                title:'预览',
                width:$(parent.window).width()-100,
                height: $(parent.window).height() * 0.7,
                content:$('#big_box').html(),
                okValue:'确定',
                ok:function () {},
                button: [
                    {
                        value: '下载',
                        callback: function () {
                            utils.htmlToCanvas('销售出库单')
                            return false;
                        }
                    },
                ]
            }).showModal();
            //$("#print_box").contents().find(".ui-dialog-content").css("overflow","auto");
            window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
        }else if(printType==1){
            /* 打印 */
            $("#box").jqprint({
                globalStyles: true, //是否包含父文档的样式，默认为true
                mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                stylesheet: null, //外部样式表的URL地址，默认为null
                noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                append: null, //将内容添加到打印内容的后面
                prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                deferred: $.Deferred() //回调函数
            });
        }
    }
});
