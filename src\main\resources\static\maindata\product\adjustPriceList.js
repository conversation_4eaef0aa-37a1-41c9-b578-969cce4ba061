//改动点 4. 机构调价列表，集团调价列表
var resultArr = [];
$(function() {
    $('#X_Tableb').XGrid({
        url: "/proxy-product/product/adjustPrice/listData",
        colNames: ['ID', '单据ID', '状态', '申请日期', '机构', '申请人ID', '申请人', '采购员', '单据编号', '业务类型', '商品编码', '原编码', '标准库ID', '商品名称', '商品大类', '型号/规格', '生产厂家', '包装单位',
            'APP售价', '申请APP售价', '审核APP售价', 'APP售价涨幅/涨额', '终端售价', '申请终端售价', '审核终端售价', '终端售价涨幅/涨额', '智鹿总部采购价', '申请智鹿总部采购价', '审核智鹿总部采购价', '连锁APP售价', '申请连锁APP售价', '审核连锁APP售价',
            '荷叶大药房采购价', '申请荷叶大药房采购价', '审核荷叶大药房采购价', '在途价', '最后含税单价', '票面毛利率', '前30天销量', '库存数量', 'EC审核状态', 'EC调价时间', 'EC未调价原因', '活动类型', '申请类型', '审核状态', '审核时间', '商品中心审核原因', '单据状态', "EC状态编号", "神农状态编号"
        ],
        colModel: [{
            name: 'id',
            index: 'id', //索引。其和后台交互的参数为sidx
            key: true, //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            hidden: true
        }, {
            name: 'recordId',
            index: 'recordId',
            hidden: true
        }, {
            name: 'statues',
            index: 'statues',
            hidden: true
        }, {
            name: 'applicationTime',
            index: 'applicationTime',
            formatter: function(value) {
                return new Date(value).Format('yyyy-MM-dd');
            },
            width: 100
        }, {
            name: 'orgCodeValue',
            index: 'orgCodeValue',
            width: 210
        }, {
            name: 'applicantId',
            index: 'applicantId',
            hidden: true
        }, {
            name: 'applicantValue',
            index: 'applicantValue',
            width: 80
        }, {
            name: 'buyerVal',
            index: 'buyerVal',
            width: 80
        }, {
            name: 'applicationCode',
            index: 'applicationCode',
            width: 160
        }, {
            name: 'channelId',
            index: 'channelId',
            width: 100
        }, {
            name: 'productCode',
            index: 'productCode',
            width: 100
        }, {
            name: 'oldProductCode',
            index: 'oldProductCode',
            width: 160
        }, {
            name: 'standardId',
            index: 'standardId',
            width: 160
        }, {
            name: 'productName',
            index: 'productName',
            width: 200
        }, {
            name: 'largeCategoryVal',
            index: 'largeCategoryVal',
            width: 80
        }, {
            name: 'specifications',
            index: 'specifications',
            width: 200
        }, {
            name: 'manufacturerValue',
            index: 'manufacturerValue',
            width: 220
        }, {
            name: 'packingUnitValue',
            index: 'packingUnitValue',
            width: 80
        }, {
            name: 'appPrice',
            index: 'appPrice',
            width: 100
        }, {
            name: 'applicationAppPrice',
            index: 'applicationAppPrice',
            width: 120
        }, {
            name: 'auditAppPrice',
            index: 'auditAppPrice',
            width: 120
        }, {
            name: 'appPriceIncreaseRise',
            index: 'appPriceIncreaseRise',
            width: 160
        }, {
            name: 'terminalPrice',
            index: 'terminalPrice',
            width: 120
        }, {
            name: 'applicationTerminalPrice',
            index: 'applicationTerminalPrice',
            width: 120
        }, {
            name: 'auditTerminalPrice',
            index: 'auditTerminalPrice',
            width: 130
        }, {
            name: 'terminalPriceIncreaseRise',
            index: 'terminalPriceIncreaseRise',
            width: 180
        }, {
            name: 'zhiluPrice',
            index: 'zhiluPrice'
        }, {
            name: 'applicationZhiluPrice',
            index: 'applicationZhiluPrice',
        }, {
            name: 'auditZhiluPrice',
            index: 'auditZhiluPrice',
        }, {
            name: 'chainGuidePrice',
            index: 'chainGuidePrice'
        }, {
            name: 'applicationChainGuidePrice',
            index: 'applicationChainGuidePrice'
        }, {
            name: 'auditChainGuidePrice',
            index: 'auditChainGuidePrice'
        }, {
            name: 'heyePrice',
            index: 'heyePrice'
        }, {
            name: 'applicationHeyePrice',
            index: 'applicationHeyePrice',
            width: 180,
        }, {
            name: 'auditHeyePrice',
            index: 'auditHeyePrice',
            width: 180,
        }, {
            name: 'inTransitPrice',
            index: 'inTransitPrice'
        }, {
            name: 'lastPurchasePrice',
            index: 'lastPurchasePrice'
        }, {
            name: 'grossMarginStr',
            index: 'grossMarginStr'
        }, {
            name: 'salesVolume',
            index: 'salesVolume'
        }, {
            name: 'inventoryQuantity',
            index: 'inventoryQuantity'
        }, {
            name: 'ecAuditStatusStr', //EC审核状态
            index: 'ecAuditStatusStr',
            width: 120,
        }, {
            name: 'ecAuditTime',
            index: 'ecAuditTime',
            width: 120,
            formatter: function(value) {
                if (value != null) {
                    return new Date(value).Format('yyyy-MM-dd');
                }
            },
        }, {
            name: 'ecReason',
            index: 'ecReason',
            width: 200,
        }, {
            name: 'promoType',
            index: 'promoType'
        }, {
            name: 'applyType',
            index: 'applyType',
            formatter: function(value) {
                if (value == 1) {
                    return "售价调整"
                } else if (value == 2) {
                    return "标价调整"
                } else if (value == 3) {
                    return "终端售价调整"
                } else if (value == 4) {
                    return "OEM售价调整"
                } else {
                    return "未知类型"
                }

            },
            width: 130
        }, {
            name: 'statuesValue', //审核状态
            index: 'statuesValue',
            width: 100
        }, {
            name: 'auditTime',
            index: 'auditTime',
            formatter: function(val, rowType, rowData) {
                var status = rowData.statues;
                if (val != null && (status == 2 || status == 3)) {
                    return new Date(val).Format('yyyy-MM-dd');
                } else {
                    return '';
                }
            },
            width: 100
        }, {
            name: 'remarks',
            index: 'remarks',
            width: 150
        }, {
            name: 'newFlg',
            index: 'newFlg',
            hidden: true
        }, {
            name: 'upperEcAuditStatus',
            index: 'upperEcAuditStatus',
            hidden: true

        }, {
            name: 'upperStatues',
            index: 'upperStatues',
            hidden: true
        }],
        rowNum: 20,
        rowList: [20, 50, 100], //分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        multiselect: true,
        rownumbers: true,
        pager: '#grid-pager',
        ondblClickRow: function(id, dom, obj, index, event) {
            console.log(obj);
            if (obj.statues == 0) {
                var loginUserId = $("#loginUserId").val();
                if (obj.applicantId == loginUserId) {
                    if (obj.applyType == "售价调整" || obj.applyType == "OEM售价调整") {
                        if (obj.newFlg == 1) {
                            //新单据跳新页面
                            utils.openTabs("productPriceChangeEdit", "商品调价申请", "/#/supply/product/priceAdjustment/update?recordId=" + obj.recordId);
                            return;
                        }
                        utils.openTabs("productPriceChangeEdit", "商品调价申请", "/proxy-product/product/adjustPrice/adjustPriceApplyEdit?recordId=" + obj.recordId);
                    } else {
                        utils.openTabs("productTerminalPriceChangeEdit", "商品终端售价申请", "/proxy-product/product/adjustPrice/adjustPriceApplyEdit?recordId=" + obj.recordId);
                    }
                } else {
                    utils.dialog({ content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000 }).showModal();
                }
            } else {
                if (obj.applyType == "售价调整" || obj.applyType == "OEM售价调整") {
                    if (obj.newFlg == 1) {
                        utils.openTabs("productPriceChangeDetail", "商品调价申请详情", "/#/supply/product/priceAdjustment/detail?businessId=" + obj.recordId);
                        return
                    }
                    utils.openTabs("productPriceChangeDetail", "商品调价申请详情", "/proxy-product/product/adjustPrice/adjustPriceDetail?businessId=" + obj.recordId);
                } else {
                    utils.openTabs("productTerminalPriceChangeDetail", "商品终端售价申请详情", "/proxy-product/product/adjustPrice/adjustPriceDetail?businessId=" + obj.recordId);
                }
            }
        },
        onSelectRow: function(id, dom, obj, index, event) {
            //console.log('单机行事件', id, dom, obj, index, event);
            //console.log(obj)
            var $tr = $("#X_Tableb tr#" + id);
            var check = $tr.find('[row-describedby="ck"] input').prop("checked");
            setSelectData(check, obj);
        },
        gridComplete: function() {
            setTimeout(function() {
                //$("#X_Tableb th:first input").hide();
                if (resultArr.length > 0) {
                    for (var i = 0; i < resultArr.length; i++) {
                        var id = resultArr[i];
                        $("#X_Tableb tr").not('first').each(function() {
                            var ID = $(this).attr('id');
                            if (ID == id) {
                                $(this).find('td[row-describedby="ck"] input').prop('checked', true).trigger('input');
                            }
                        });
                    }
                }
            }, 1)
        }
    });
    //全选checkox
    $(".table").on('click', ' .XGridHead table tbody tr th:first input', function() {
            var rowData = $('#X_Tableb').getRowData();
            if ($(this).prop('checked')) {
                var curIdsArr = [];
                curIdsArr = rowData.map(function(ite) {
                    return ite.id;
                })
                resultArr = concat_(resultArr, curIdsArr)
            } else {
                $(rowData).each(function(ind, ite) {
                    for (var i = 0; i < resultArr.length; i++) {
                        if (resultArr[i] == ite.id) {
                            resultArr.splice(i, 1)
                        }
                    }
                })
            }
            //console.log(resultArr)
        })
        //合并两个数组，去重
    var concat_ = function(arr1, arr2) {
        var arr = arr1.concat();
        for (var i = 0; i < arr2.length; i++) {
            arr.indexOf(arr2[i]) === -1 ? arr.push(arr2[i]) : 0;
        }
        return arr;
    }


    // 删除草稿
    $("#deleteDraftBtn").on("click", function() {
        let selRow = $('#X_Tableb').XGrid('getSeleRow');
        var loginUserId = $("#loginUserId").val();
        var applicantId = selRow[0].applicantId;
        // 表格重新渲染 的参数
        let postData = {}
        let data = {
            adjustPriceDetailId: selRow[0].id,
            applicantId: selRow[0].applicantId,
            loginUserId: loginUserId,
            adjustPriceId: selRow[0].recordId
        }
        let params = {
            applicantId: selRow[0].applicantId,
            statusVal: selRow[0].statuesValue,
            statusName: '录入中',
            url: '/proxy-product/product/adjustPrice/delete',
            loginUserId: loginUserId
        }
        utils.deleteDraft('X_Tableb', params, data, postData);
    });

    $("#SearchBtn").on("click", function() {
        //查询清空选中数据
        resultArr = [];
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "orgCode": $("#orgCode").val(),
                "keyword": $("#keyword").val().trim(),
                "statues": $("#statues").val(),
                "applyType": $("#applyType").val(),
                "largeCategory": $("#largeCategory").val(),
                "applicationCode": $("#applicationCode").val().trim(),
                "startTime": $("#startTime").val(),
                "endTime": $("#endTime").val(),
                "auditStartTime": $("#auditStartTime").val(),
                "auditEndTime": $("#auditEndTime").val(),
                "channelId": $("#channelId").val(),
                "applicantId": $("#applicantId").val(),
                "buyer": $("#buyer").val(),
                "ecAuditStatus": $("#ecAuditStatus").val()
            },
            page: 1
        }).trigger('reloadGrid');
    });
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function() {
            $(this).siblings("input").trigger("dblclick")
        })
        //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function() {
        utils.channelDialog('0').then(res => {
            console.log(res)
            let _str_name = '',
                _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });
    //APP售价智能分单
    $('#applyProductPriceBtn').on('click', function() {
        utils.dialog({
            align: 'top',
            width: 130,
            height: 50,
            padding: 2,
            content: '<div class="changeApplyItem formApprovalBlock">' +
                '<div class="downTemplate"><a href="/proxy-product/product/adjustPrice/downTemplate?applyType=1"  class="download">下载模板</a></div>' +
                '<div class="import" onclick="intelligenceFileupload(1)">批量生单</div>' +
                '</div>',
            quickClose: true
        }).show(this);
    });
    //终端价智能分单
    $('#applyTerminalPriceBtn').on('click', function() {
        utils.dialog({
            align: 'top',
            width: 130,
            height: 50,
            padding: 2,
            content: `<div class="changeApplyItem formApprovalBlock">
                        <div class="downTemplate"><a href="/proxy-product/product/adjustPrice/downTemplate?applyType=3"  class="download">下载模板</a></div>
                        <div class="import" onclick="intelligenceFileupload(3)">批量生单</div>       
                    </div>`,
            quickClose: true
        }).show(this);
    });
})


//批量生单进行判断如果存在有未审核完成的上架申请，不允许上传
function intelligenceFileupload(applyType) {
    var rowData = $('#X_Tableb').getRowData();
    console.log(rowData)
        /*   let isNext = true
          rowData.forEach((item) => {
              //①神农审核状态为审核通过（2）且EC为2、3、5、4。②神农审核状态为审核不通过、录入中（0、3），这两种情况就可以申请
              //第一种情况
              if (item.upperStatues == "2") {
                  if (item.upperEcAuditStatus == "2" || item.upperEcAuditStatus == "3" || item.upperEcAuditStatus == "4" || item.upperEcAuditStatus == "5") {} else {
                      isNext = false
                      return false
                  }
              }
              //第二种情况
              else if (item.upperStatues == 0 || item.upperStatues == 3) {

              } else {
                  isNext = false
                  return false
              }
          })
          if (!isNext) {
              utils.dialog({
                  title: "提示",
                  content: "存在有未审核完成的上架申请，不允许上传",
              }).show()
              return
          } */


    $('#piliang_dialog').find('.btn_upload').attr('data-applyType', applyType)
    let _dialog = utils.dialog({
        // url: '/proxy-product/product/adjustPrice/toIntelligenceFileupload',
        title: '批量生单',
        content: $('#piliang_dialog'),
        width: 420,
        height: 300,
        data: '',
        onclose: function(data) {
            $("#SearchBtn").trigger("click");
            $('.fileName').attr('title', '').text('')
            document.getElementById('uploadForm') && document.getElementById('uploadForm').reset()
            _dialog.close().remove()
        }
    }).showModal();
    document.getElementById('uploadForm') && document.getElementById('uploadForm').reset()
}

var orgCode = $("#loginOrgCode").val();
if (orgCode == '001') {
    $.ajax({
        url: "/proxy-sysmanage/sysmanage/system/querySubOrgListByOrgCode?orgCode=" + orgCode,
        async: false,
        dataType: "json",
        success: function(data) {
            var html = '<option value="">请选择</option>';
            if (data.code == 0) {
                var arr = data.result;
                if (arr != null) {
                    for (var i = 0; i < arr.length; i++) {
                        html += '<option value="' + arr[i].orgCode + '">' + arr[i].orgName + '</option>';
                    }
                }
            }
            $("#orgCode").html(html);
        },
        error: function() {}
    });
    $("#orgCode").on('change', function() {
        $("#buyerVal").val("");
        $("#buyer").val("");
        $("#applicantIdVal").val("");
        $("#applicantId").val("");
        initBuyer();
    });
}
initBuyer();
Date.prototype.Format = function(fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}
var exportIdsArr = [];

function exportExcel() {
    utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text())).then(() => {
        return false;
    }).catch(() => {
        //原始处理逻辑代码
        var obj = {};
        if (resultArr.length > 0) {
            exportIdsArr = resultArr.map(function(ite) {
                return ite
            })
            obj["detailIdStr"] = exportIdsArr.toString();
        } else {
            var formData = $("#adjustPriceForm").serializeToJSON();
            obj = formData;
        }
        console.log(obj);
        httpPost("/proxy-product/product/adjustPrice/exportSelectRecord", obj);
    });
}
//发送POST请求跳转到指定页面
function httpPost(URL, PARAMS) {
    var temp = document.createElement("form");
    temp.action = URL;
    temp.method = "post";
    temp.style.display = "none";

    for (var x in PARAMS) {
        var opt = document.createElement("input");
        opt.name = x;
        opt.value = PARAMS[x];
        temp.appendChild(opt);
    }
    document.body.appendChild(temp);
    temp.submit();
    document.body.removeChild(temp);
    return temp;
}
//设置选中、删除数据
function setSelectData(check, data) {
    if (check) {
        resultArr.push(data.id);
    } else {
        var id = data.id;
        for (var i = 0; i < resultArr.length; i++) {
            if (resultArr[i] == id) {
                resultArr.splice(i, 1);
                break;
            }
        }
    }
};

function initBuyer() {
    valAutocomplete("/proxy-sysmanage/sysmanage/system/queryPurchaserListByOrgCode", { paramName: 'userName', params: { "orgCode": $("#orgCode").val() } }, "buyer", { data: "id", value: "userName" });
    valAutocomplete("/proxy-product/product/productFirst/queryBuyerList", { paramName: 'userNames' }, "applicantId", { data: "id", value: "userName" });


}
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url, param, obj, resParam, select, noneSelect) {
    var resParam = Object.assign({ 'list': 'result' }, resParam);
    $("#" + obj + "Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName, //查询参数，默认 query
        params: param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader: resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果', //查无结果的提示语
        onSelect: function(result) {
            select && select(result)
            $("#" + obj).val(result.data);
            $("#" + obj + "Val").attr("data-value", result.value);
        },
        onNoneSelect: function(params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value = $("#" + obj + "Val").val();
            if (value != $("#" + obj + "Val").attr("data-value")) {
                $("#" + obj).val("");
                $("#" + obj + "Val").val("");
            }
        }
    });
}