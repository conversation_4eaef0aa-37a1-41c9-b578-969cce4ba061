function GetQueryOrderNo(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return decodeURI(r[2]);
    return null;
}
$(function() {

    var dialog = parent.dialog.get(window);
    $('#X_Table').XGrid({
        url: '/proxy-purchase/purchase/purchaseOrderStoreAnd/selectPurchaseStoreCheckList',
        postData: {
            "purchaseUser": '',
            "supplierCode": $("#supplierCode").val(),
            "startDate":'',
            "endDate":'',
            "orderNoSearch":'',
            "storeOrderNo":$("#storeOrderNo").val()
        },
        colNames: ['id','供应商编号','供应商名称','采购订单号','收货单号','质检单号','质检单状态','采购员','验收员','验收单日期'],
        colModel: [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                hidden:true
            },
            {
                name: 'supplierCode',
            },
            {
                name: 'supplierName',
            },
            {
                name: 'purchaseOrderNo',
            },
            {
                name: 'orderReceiveNo',
            },
            {
                name: 'orderCheckNo',
            },
            {
                name: 'orderStautsFormat',
            },
            {
                name: 'purchaseUserName',
            },
            {
                name: 'createUserName',
            },
            {
                name: 'createTimeFormat',
            },
            {
                name: 'purchaseUser',
                hidden:true
            },
        ],
        key: 'id',
        rowNum: 0,
        rownumbers: true,//是否展示序号
        //disableRow: dialog.data,
        //rowList: [20, 50, 100],
        altRows: true,//设置为交替行表格,默认为false
        ondblClickRow: function (id, dom, obj, index, event) {
            dialog.close(obj);
        },
        //pager: '#grid-pager',
    });


    //查询按钮
    $('#search_list').on('click', function () {
        $('#X_Table').setGridParam({
            url:'/proxy-purchase/purchase/purchaseOrderStoreAnd/selectPurchaseStoreCheckList',
            postData: {
                "orderNoSearch": $("#orderNoSearch").val(),
                "supplierCode": $("#supplierCode").val(),
                "startDate":$("#startDate").val(),
                "endDate":$("#endDate").val(),
                "purchaseUser": $("#purchaseUser").val(),
                "storeOrderNo":$("#storeOrderNo").val()
            }
        }).trigger('reloadGrid');
    })

});