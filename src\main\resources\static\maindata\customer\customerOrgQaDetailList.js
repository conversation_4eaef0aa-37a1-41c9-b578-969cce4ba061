$(function () {
    let orgCode = $("#orgCode").val();
    console.log("orgCoe="+orgCode);
    $('#table').XGrid({
        url: "/proxy-customer/customer/customerWarning/customerOrgQaDetailList",
        postData: {
            orgCode
        },
        colNames: ['',  '机构名称', '质管姓名', '质管电话', '质管邮箱', '质管钉钉', '备注', '操作'],
        colModel: [
            {
                name: 'id', //与反回的json数据中key值对应
                hidden: true,
                key: true //当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
            },
            {
                name: 'orgCodeName',
                index: 'orgCodeName'
            },
            {
                name: 'qualityName',
                index: 'qualityName'
            },
            {
                name: 'qualityPhone',
                index: 'qualityPhone'
            },
            {
                name: 'qualityMail',
                index: 'qualityMail'
            },
            {
                name: 'qualityDing',
                index: 'qualityDing'
            },
            {
                name: 'remark',
                index: 'remark'
            },
            {
                name: 'control',
                index: 'control',
                rowtype: '#control_div'
            }

        ],
        rowNum: 20,
        rowList: [20, 50, 100],//分页条数下拉选择
        altRows: true, //设置为交替行表格,默认为false
        rownumbers: true,//是否展示序号
        ondblClickRow: function (id, c, a, b) {
            // utils.openTabs("purchaseRefundPriceOrderDetail", "资质预警明细", "/proxy-customer/customer/customerWarning/customerOrgQaDetailById?id="+id);
        },
        onSelectRow: function (e, c, a, b) {},
        gridComplete: function() {
            $('#addBtn').prop('disabled', $('#table').XGrid('getRowData').length >= 5 ? true : false)
        },
        pager: '#grid-pager'
    });

    $("#addBtn").on("click", function () {
        $('#qualityName').val('')
        $('#qualityPhone').val('')
        $('#qualityMail').val('')
        $('#qualityDing').val('')
        $('#remark').val('')
        // let form = $(body).find('form#searchForm');
        // $(form).attr("action","/proxy-customer/customer/customerDelegationFile/exportCustomerDelegationFileExcel");
        // $(form).submit();
        let _dia = utils.dialog({
            title: '添加质管',
            content: $('#addQa'),
            width: 550,
            button: [
                {
                    value: '确定',
                    callback: function () {
                        submit('customerOrgQaDetailInsert').then(res => {
                            utils.dialog({content: res.msg, quickClose: true, timeout: 3000}).showModal()
                            $('#table').XGrid('setGridParam', {
                                postData: {
                                    orgCode
                                },page:1
                            }).trigger('reloadGrid');
                        }).catch(() => {
                            return false;
                        })
                    }
                },
            ]
            // okValue: '确定',
            // ok: function () {
            //     submit('customerOrgQaDetailInsert').then(res => {
            //         utils.dialog({content: res.msg, quickClose: true, timeout: 3000}).showModal()
            //         $('#table').XGrid('setGridParam', {
            //             postData: {
            //                 orgCode
            //             },page:1
            //         }).trigger('reloadGrid');
            //     }).catch(() => {
            //         _dia.showModal()
            //         return false;
            //     })
            // }
        }).showModal()
    });

    $('body').on('click', '.btn_edit',function () {
        let _thisId = $(this).parents('tr').attr('id')
            // _thisName = $('#'+_thisId).find('[row-describedby="qualityName"]').text(),
            // _thisEmail = $('#'+_thisId).find('[row-describedby="qualityPhone"]').text(),
            // _thisPhone = $('#'+_thisId).find('[row-describedby="qualityMail"]').text(),
            // _thisDD = $('#'+_thisId).find('[row-describedby="qualityDing"]').text(),
            // _thisRemark = $('#'+_thisId).find('[row-describedby="remark"]').text();
        $.ajax({
            type: 'get',
            url: '/proxy-customer/customer/customerWarning/customerOrgQaDetailById?id='+ _thisId,
            success: function (res) {
                if (res.code == 0){
                    $('#qualityName').val(res.result.qualityName);  //
                    $('#qualityPhone').val(res.result.qualityPhone);
                    $('#qualityMail').val(res.result.qualityMail);
                    $('#qualityDing').val(res.result.qualityDing);
                    $('#remark').val(res.result.remark);
                    utils.dialog({
                        title: '编辑',
                        content: $('#addQa'),
                        width: 550,
                        okValue: '确定',
                        ok: function () {
                            submit('customerOrgQaDetailUpdate', _thisId).then(res => {
                                utils.dialog({content: res.msg, quickClose: true, timeout: 3000}).showModal()
                                $('#table').XGrid('setGridParam', {
                                    postData: {
                                        orgCode
                                    },page:1
                                }).trigger('reloadGrid');


                            }).catch(() => {

                                return false;
                            })
                        },
                        cancelValue: '取消'
                    }).showModal()
                }
            },

        })


    })

    $('body').on('click', '.btn_del',function () {
        let _thisId = $(this).parents('tr').attr('id')
        utils.dialog({
            title: '提示',
            content: '确定删除',
            okValue: '确定',
            ok: function () {
                $.ajax({
                    url:'/proxy-customer/customer/customerWarning/customerOrgQaDetailUpdate',
                    type: 'post',
                    data: {
                        id: _thisId,
                        orgCode: orgCode,
                        yn: 0,

                    },
                    success: function (res) {
                        if (res.code == 0){
                           utils.dialog({content: res.msg, quickClose: true, timeout: 3000}).showModal();
                            $('#table').XGrid('setGridParam', {
                                postData: {
                                    orgCode
                                },page:1
                            }).trigger('reloadGrid');
                        }else{
                            utils.dialog({content: res.msg, quickClose: true, timeout: 3000}).showModal();
                        }
                    }
                })
            }
        }).showModal()
    })


})

function submit(url, id) {
    let postData = {
        orgCode: $('#orgCode').val() ,
        qualityName: $('#qualityName').val() ,
        qualityPhone: $('#qualityPhone').val() ,
        qualityMail: $('#qualityMail').val(),
        qualityDing: $('#qualityDing').val(),
        remark: $('#remark').val()
    }
    if(id){
        postData['id'] = id
    }
    return new Promise((resolve, reject) => {
        $.ajax({
            url:'/proxy-customer/customer/customerWarning/' + url,
            type: 'post',
            data: postData,
            success: function (res) {
                if (res.code == 0){
                    resolve(res)
                }else{
                    utils.dialog({
                        title: '提示',
                        content: res.msg,
                        okValue: '确定',
                        ok: function () {}
                    }).showModal()
                    reject()
                }
            }
        })
    })
}
