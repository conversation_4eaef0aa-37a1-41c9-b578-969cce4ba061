$(function () {
    //商品搜索图标
    $(document).on("click", ".glyphicon-search", function () {
        $(this).siblings("input").trigger("dblclick")
    })
    //业务类型 输入框双击 弹出业务类型列表
    $("#channelId_inp").dblclick(function () {
        utils.channelDialog('1').then( res => {
            // console.log(res)
            let _str_name = '', _str_code = '';
            let _str_arr = res.map(item => {
                return item.channelName;
            })
            _str_name = _str_arr.join(',');

            let _str_code_arr = res.map(item => {
                return item.channelCode;
            })
            _str_code = _str_code_arr.join(',')
            $('#channelId_inp').val(_str_name)
            $('#channelId').val(_str_code)
        })
    });
    $('#X_Tableb').XGrid({
        url:"/proxy-product/product/productFirst/query",
        colNames: [ '申请日期', '机构','业务类型','申请人ID','申请人','单据编号','商品编码','原商品编码', '商品名', '通用名','商品大类', '型号/规格', '生产厂家', '批准文号', '包装单位', '剂型', '小包装条码', '审核状态','审核完成日期'],
        colModel: [
            {
                name: 'applicationTimeValue',
                index: 'applicationTimeValue',
                width: 100
            },  {
                name: 'orgCodeValue',
                index: 'orgCodeValue',
                width: 210
            },{
                name: 'channelId',
                index: 'channelId',
                width: 150
            },{
                name: 'applicantId',
                index: 'applicantId',
                hidden:true
            }, {
                name: 'applicantValue',
                index: 'applicantValue',
                width: 80
            },  {
                name: 'applicationCode',
                index: 'applicationCode',
                width: 160
            }, {
                name: 'productCode',
                index: 'productCode',
                width: 100
            }, {
                name: 'oldProductCode',
                index: 'oldProductCode',
                width: 160
            }, {
                name: 'productName',
                index: 'productName',
                width: 180
            }, {
                name: 'commonName',
                index: 'commonName',
                width: 180
            },{
                name: 'largeCategoryVal',
                index: 'largeCategoryVal',
                width: 80
            }, {
                name: 'specifications',
                index: 'specifications',
                width: 200
            }, {
                name: 'manufacturerValue',
                index: 'manufacturerValue',
                width: 220
            }, {
                name: 'approvalNumber',
                index: 'approvalNumber',
                width: 180
            }, {
                name: 'packingUnitValue',
                index: 'packingUnitValue',
                width: 80
            }, {
                name: 'dosageFormValue',
                index: 'dosageFormValue',
                width: 80
            }, {
                name: 'smallPackageBarCode',
                index: 'smallPackageBarCode',
                width: 160
            }, {
                name: 'statusValue',
                index: 'statusValue',
                width: 100
            },{
                name: 'auditTimeValue',
                index: 'auditTimeValue',
                width: 120
            }
        ],
        rowNum: 20,
        rowList: [20,50,100], //分页条数下拉选择
        altRows: true,//设置为交替行表格,默认为false
        pager: '#grid-pager',
        ondblClickRow: function (id,dom,obj,index,event) {
            if(obj.statusValue=="录入中"){
                var loginUserId = $("#loginUserId").val();
                if(obj.applicantId==loginUserId){
                    utils.openTabs("productEdit","商品首营申请", "/proxy-product/product/productFirst/toApplyUpdate?productId="+id);
                }else{
                    utils.dialog({content: '您无权限查看该单据信息!', quickClose: true, timeout: 2000}).showModal();
                }
            }else {
                utils.openTabs("productDetail", "商品首营申请详情", "/proxy-product/product/productFirst/toDetail?productId="+id);
            }
        },
        rownumbers: true,
    });

    // 生产厂家
    valAutocomplete("/proxy-sysmanage/sysmanage/dict/querymanufactorynotpage",{paramName:"keyWord"},"manufacturer",{data:"manufactoryId",value:"manufactoryName"});

    valAutocomplete("/proxy-product/product/productFirst/queryBuyerList",{paramName:'userNames'},"applicantId",{data:"id",value:"userName"});

    $("#SearchBtn").on("click", function () {
        var formData = $("#searchForm").serializeToJSON();
        $("#productCode").val($("#productCode").val().trim());
        $("#approvalNumber").val($("#approvalNumber").val().trim());
        $("#applicationCode").val($("#applicationCode").val().trim());
        $("#smallPackageBarCode").val($("#smallPackageBarCode").val().trim());
        $('#X_Tableb').XGrid('setGridParam', {
            postData: {
                "productCode": $("#productCode").val(),
                "approvalNumber":$("#approvalNumber").val(),
                "smallPackageBarCode":$("#smallPackageBarCode").val(),
                "manufacturer":$("#manufacturer").val(),
                "statues":$("#statues").val(),
                "largeCategory":$("#largeCategory").val(),
                "startTime":$("#startTime").val(),
                "endTime":$("#endTime").val(),
                "auditStartTime":$("#auditStartTime").val(),
                "auditEndTime":$("#auditEndTime").val(),
                "applicantId":$("#applicantId").val(),
                "applicationCode":$("#applicationCode").val(),
                "channelId":$("#channelId").val(),
            },page:1
        }).trigger('reloadGrid');
    });

    $("#exportBtn").on("click", function () {
        utils.exportAstrictHandle('X_Tableb', Number($('#totalPageNum').text())).then( () => {
            return false;
        }).catch( () => {
            //全选，取消，no delete, no comment out !!! 12.08, this function is required by 波波
            var ck = true;
            // copy this parameter and the below buttons
            var html = $('#exportHtml').html();
            var d = dialog({
                title: '请选择导出字段',
                width:820,
                height:400,
                content: html,
                okValue: '确定',
                ok: function () {
                    let that = this;
                    var arr=[];
                    for(var i=0;i< $(that.node).find('.exportItem').length;i++)
                    {
                        $(that.node).find('.exportItem').eq(i).find('dd input[type="checkbox"]').each(function (index,item) {
                            var checked=$(item).prop('checked');
                            if(checked){
                                arr.push($.trim($(item).val()))
                            }
                        })
                    }
                    if(arr.length == 0){
                        utils.dialog({content: '请选择后导出', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    var exportAttribute= arr.join(',');
                    if(exportAttribute==""){
                        utils.dialog({content: '请选择导出属性', quickClose: true, timeout: 2000}).showModal();
                        return false;
                    }
                    $("#exportAttribute").val(exportAttribute);
                    $("#searchForm").attr("action","/proxy-product/product/productFirst/exportExcel");
                    $("#searchForm").submit();
                },
                cancelValue: '取消',
                cancel: function () { },
                // copy button to other dialogues
                button: [
                    {
                        id: 'chooseAll',
                        value: '全选',
                        callback: function () {
                            //debugger;
                            if(!ck){
                                $(".exportItem input").removeAttr("checked");
                                ck = true;
                            }else if(ck){
                                $(".exportItem input").prop("checked","checked");
                                ck = false;
                            }else{
                                return false;
                            };
                            return false;
                        }
                    }
                ]
                //copy ends here
            });
            d.showModal();
        });
    });
    $(document).on("change",".exportItem dt input",function () {
        var checked=this.checked;
        if(checked)
        {
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dd input[type='checkbox']").prop('checked',false);
        }
    })
    $(document).on("change",".exportItem dd input[type='checkbox']",function () {
        var inpLen=$(this).parents("dd").find("input[type='checkbox']").length;
        var checkLen=$(this).parents("dd").find("input[type='checkbox']:checked").length;
        if(inpLen == checkLen)
        {
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',true);
        }else{
            $(this).parents("dl").find("dt input[type='checkbox']").prop('checked',false);
        }
    })
    //批量申请
    $('#batchApply').on('click', function () {
        utils.dialog({
            align: 'top',
            width: 130,
            height: 50,
            padding: 2,
            content:'<div class="changeApplyItem formApprovalBlock"><div class="import" onclick="utils.openTabs(\'productFileupload\',\'商品首营批量申请导入数据\',\'/proxy-product/product/productFirst/toFileupload\')">导入数据</div><div class="downTemplate"><a href="/proxy-product/product/productFirst/downTemplate"  class="download">下载模板</a></div></div>',
            quickClose: true
        }).show(this);
    });

    $('#importProductFirst').on('click', function () {
        utils.dialog({
            align: 'top',
            width: 130,
            height: 50,
            padding: 2,
            content:'<div class="changeApplyItem formApprovalBlock">' +
                '<div class="downTemplate"><a href="/proxy-product/product/tools/downloadProductFirstTemplate"  class="download">下载模板</a></div>' +
                '<div class="import" onclick="utils.openTabs(\'toProductChannelPage\',\'批量导入商品首营\',\'/proxy-product/product/tools/toProductFirstImportPage\')">批量导入</div>' +
                '</div>',
            quickClose: true
        }).show(this);
    });

});
/**
 * 字典值
 * @param isEmpty
 * @param obj
 * @param data
 */
function valAutocomplete(url,param,obj,resParam,select,noneSelect) {
    var resParam=Object.assign({'list':'result'},resParam);
    $("#"+obj+"Val").Autocomplete({
        serviceUrl: url, //异步请求
        paramName: param.paramName,//查询参数，默认 query
        params:param.params || {},
        dataType: 'json',
        minChars: '0', //触发自动匹配的最小字符数
        maxHeight: '300', //默认300高度
        dataReader:resParam,
        triggerSelectOnValidInput: false, // 必选
        showNoSuggestionNotice: true, //显示查无结果的container
        noSuggestionNotice: '查询无结果',//查无结果的提示语
        onSelect: function (result) {
            select && select(result)
            $("#"+obj).val(result.data);
            $("#"+obj+"Val").attr("data-value",result.value);
        },
        onNoneSelect: function (params, suggestions) {
            console.log(params, suggestions);
            noneSelect && noneSelect();
            var value=$("#"+obj+"Val").val();
            if(value != $("#"+obj+"Val").attr("data-value"))
            {
                $("#"+obj).val("");
                $("#"+obj+"Val").val("");
            }
        }
    });
}

Date.prototype.Format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}
