$(function () {
    initOtherFile($("#supplierOrganBaseId").val(), 0);//其他附件
})


//初始化其他附件为选中
function initOtherFile(supplierBaseId, type) {
    $.ajax({
        url: '/proxy-supplier/supplier/supplierOrganBaseFile/toOrganBaseFile/getOtherFileList',
        data: {"correlationId": supplierBaseId, "type": type},
        type: "post",
        dataType: 'json',
        success: function (data) {
            if (!data.otherFileList) return false;
            if (data.otherFileList.length) {
                $("#delOtherFile").val("Y");
                $(data.otherFileList).each(function (fileIndex, fileItem) {
                    var enclosureList = fileItem.enclosureList;
                    if (enclosureList && enclosureList.length > 0) {
                        var imgArr = [];
                        $(enclosureList).each(function (imgIndex, imgItem) {
                            var imgObj = {};
                            imgObj.fileName = imgItem.fileName;
                            imgObj.filePath = imgItem.filePath;
                            imgArr.push(imgObj);
                        });
                        var html = '<input type="hidden" data-type="' + fileItem.certificateType + '" id="other' + fileItem.certificateType + '" value=\'' + JSON.stringify(imgArr) + '\' />';
                        $("body").append(html);
                    }
                    var otherFiles = $('.otherFile').find('input[type=checkbox]');
                    $(otherFiles).each(function (index, item) {
                        if (fileItem.certificateType == $(this).val()) {
                            $(this).attr("checked", 'true')
                        }
                    });
                });
            }else{
                var otherFileInps = $('#otherFileBox input');
                $(otherFileInps).each(function(){
                    $(this).prop('checked',false)
                });
                $('input[id^=other]').remove()
            }
        },
        error: function () {
            utils.dialog({content: '请求失败', quickClose: true, timeout: 2000}).showModal();

        }
    });
}
