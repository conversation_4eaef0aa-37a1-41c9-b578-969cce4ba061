﻿var getData;
var outOrderCode ='CGD1910006004';
$(function () {

    // var outOrderCode = [];
    // outOrderCode.push("CGD1910006004");
     printType ="1";
     getData = function(printType,outOrderCode) {
         var temp = []; //一个新的临时数组
         for(var i = 0; i < outOrderCode.length; i++){
             if(temp.indexOf(outOrderCode[i]) == -1){
                 temp.push(outOrderCode[i]);
             }
         }

         var billNos = temp.join(',');
         $.ajax({
             url:"/proxy-finance/finance/purchase/payrequestinfo/selectPayRequestPrintData",
             data:{
                 billNos :  billNos
             },
             success:function(res){
                 if(!res.result || res.result.length === 0){
                     utils.dialog({
                         title:'提示',
                         content:"数据为空或格式不正确",

                    }).showModal();
                    return ;
                }
                webRender(res.result,printType);
            },
            error:function(){

            }
        })
    }





    /* 数据渲染 */
    function webRender(data,printType) {
        var box_html = '';
        /* 基本结构拼装 */
        data.forEach(function (item,index) {

            box_html +=`
             <div class="content"   id="print_divsa_${index}" STYLE="page-break-after:always;">
        <div style="text-align:center"><h3>采购付款申请单</h3></div>`
            if(item.status !=1){
                box_html +=`  <div class="headwraps">
                 <h3 class="panel-title">审批流程</h3>
                 <div class="pull-right" role="group">
                 </div>
                 </div>
                 <div class="bodywraps">
                 <div class="col-md-12">
                 <div class="flow" id="flow_${index}"></div>
                 </div>
                 </div>`
            }
            box_html +=` 
            <div class="headwraps">
                <h3 class="panel-title">单据信息</h3>
            </div>
            <div class="bodywraps">
                <form id="myform">
                        <div class="col-mdp-3">
                            <div class="halfs">
                                <div class="halfs-addon">创建时间</div>
                                <input type="text" id="d" name="createDateFormat" value="${item.createDateFormat}" readonly class="form-controls">
                            </div>
                            <div class="halfs">
                                <div class="halfs-addon">单据编号</div>
                                <input type="text" id="billNo" name="billNo" readonly value="${item.billNo}" class="form-controls">
                            </div>
                        </div>

                        <div class="col-mdp-3">
                            <div class="halfs">
                                <div class="halfs-addon">提交人</div>
                                <input type="text" id="f" name="createUser" value="${item.createUser}" readonly class="form-controls {validate:{ required :true, isDate :true,}}">
                            </div>
                            <div class="halfs">
                                <div class="halfs-addon">所属机构</div>
                                <input type="text" id="g" name="orgCode" value="${item.orgName}" readonly class="form-controls {validate:{ required :true, isDate :true,}}">
                            </div>
                        </div>
                </form>
            </div>

            <div class="headwraps">
                <h3 class="panel-title">
                    基本信息
                </h3>
            </div>
            <div class="bodywraps">
                    <form id="basicInfo">
                            <div class="col-mdp-3">
                                <div class="halfs">
                                    <div class="halfs-addon">供应商编号</div>
                                    <input type="text" name="supplierNo" id="supplierNo" value="${item.supplierNo}" class="form-controls " readonly>
                                </div>
                                <div class="halfs">
                                    <div class="halfs-addon">供应商名称</div>
                                    <input type="text" name="supplierName" id="supplierName" value="${item.supplierName}" class="form-controls " readonly>
                                </div>
                            </div>

                            <div class="col-mdp-3">
                                <div class="halfs">
                                    <div class="halfs-addon">供应商送货员</div>
                                    <input type="text" name="deliveryman" id="deliveryman" value="${item.deliveryman}" class="form-controls " readonly>
                                </div>

                                <div class="halfs">
                                    <div class="halfs-addon">联系电话</div>
                                    <input type="text" name="telno" id="telno" value="${item.telno}" class="form-controls " readonly>
                                </div>
                            </div>


                            <div class="col-mdp-3">
                                <div class="halfs">
                                    <div class="halfs-addon require">款项类型</div>
                                   <input type="text" value=" ${item.isPrepayStr}" >
                                </div>
                                 
                                <div class="halfs">
                                    <div class="halfs-addon">申请总额</div>
                                    <span  id="applayAmount_${index}" class="form-controls" ></span>
                                </div>
                            </div>

                        <div class="col-mdp-3">
                            <div class="halfs">
                                <div class="halfs-addon">本次预付金额抵扣</div>
                                <span   id="prepaymentAmountDeduction_${index}" class="form-controls"></span>
                            </div>
                            <div class="halfs">
                              <div class="halfs-addon">票折抵扣</div>
                              <span id="ticketDeduction_${index}" class="form-controls"></span>
                            </div>
                        </div>
                          <div class="col-mdp-3">
                            <div class="halfs">
                              <div class="halfs-addon">其他抵扣</div>
                              <span id="otherDeductions_${index}" class="form-controls"></span>
                            </div>
                            <div class="halfs">
                                <div class="halfs-addon">本次实付金额</div>
                                <span  id="actualPaymentAmount_${index}" class="form-controls"></span>
                            </div>
                          </div>

                            <div class="col-mdp-3">
                                <div class="halfs">
                                    <div class="halfs-addon">应付余额</div>
                                    <span id="payableBalance_${index}" class="form-controls" ></span>
                                
                                </div>
                                <div class="halfs">
                                    <div class="halfs-addon ">支付方式</div>
                                     <input type="text"  value="${item.payableTypeStr}" class="form-controls "
                                           readonly>
                                </div>
                            </div>

                            <div class="col-mdp-3">
                                <div class="halfs">
                                    <div class="halfs-addon">期望支付日期</div>
                                            <span id="expectPayTime_${index}" class="form-controls" ></span>                           
                                </div>
                                <div class="halfs">
                                    <div class="halfs-addon require">异常确认</div>
                                     <input type="text"  value="${item.exceptionConfirStr}" class="form-controls "
                                           readonly>
                                </div>
                            </div>

                            <div class="col-mdp-3">
                               

                                <div class="halfs">
                                    <div class="halfs-addon  require">开户银行</div>
                                    <input type="text" name="bankName" id="bankName"  value="${item.bankName}" readonly class="form-controls "/>
                                </div>
                               <div class="halfs">
                                    <div class="halfs-addon  require">开户户名</div>
                                    <input type="text" id="accountName" name="accountName"  value="${item.accountName}" class="form-controls "
                                           readonly>
                                 
                                </div> 
                            </div>
                            <div class="col-mdp-3">
                                <div class="halfs">
                                    <div class="halfs-addon  require">银行账号</div>
                                    <input type="text" id="bankAccount" name="bankAccount" value="${item.bankAccount}" class="form-controls "
                                           readonly>
                                
                                </div>
                            </div>
                             <div class="col-mdp-3">
                             <div class="halfs">
                                    <div class="halfs-addon">备注信息</div>
                                   <input type="text" class="form-controls" id="remarks" name="remark" readonly value="${item.remarks}"  maxlength="200" />
                                </div>
                          </div>       
                    </form>
            </div>
            <div class="bodywraps active">
                    <div class="table-box">
                        <table id="X_Tablea_${index}"></table>
                    </div>
            </div>
            <div class="bodywraps active">
                   <div class="table-box">
                        <table id="Y_Tablea_${index}"> </table>
                   </div>
             </div>
    </div>
            `;

        });

        $("#box").html(box_html);
        data.forEach(function (item,index) {
            if(item.approvalProcessId){
                loadFolow(item.approvalProcessId,index);
            }
            $('#expectPayTime_'+index).html(moment(item.expectPayTime).format('YYYY-MM-DD'));
            $('#payableBalance_'+index).html(item.payableBalance?item.payableBalance.formatMoney('2', '', ',', '.') : '0.00');
            $('#prepaymentAmountDeduction_'+index).html(item.prepaymentAmountDeduction?item.prepaymentAmountDeduction.formatMoney('2', '', ',', '.') : '0.00');
            $('#ticketDeduction_'+index).html(item.ticketDeduction?item.ticketDeduction.formatMoney('2', '', ',', '.') : '0.00');
            $('#otherDeductions_'+index).html(item.otherDeductions?item.otherDeductions.formatMoney('2', '', ',', '.') : '0.00');
            $('#actualPaymentAmount_'+index).html(item.actualPaymentAmount?item.actualPaymentAmount.formatMoney('2', '', ',', '.') : '0.00');
            $('#applayAmount_'+index).html(item.applayAmount?item.applayAmount.formatMoney('2', '', ',', '.') : '0.00');
            if(item.isPrepay == 'C02'){
                //应付
                $('.nav-tabs>li:eq(0)').removeClass('active');
                $('.nav-tabs>li:eq(1)').addClass('active');
                $('.nav-content>.panel-body:eq(0)').hide();
                $('.nav-content>.panel-body:eq(1)').show();
                loadInvoiceTab(item.billNo,index,item.payrequestInvoiceDetailVos);
            }else {
                loadOrderTab(item.billNo,index,item.payrequestOrderDetailVos);
            }
        });


        setTimeout(function () {
            if(printType==0){
                /* 打印预览 */
                utils.dialog({
                    title:'预览',
                    content:$('#big_box').html(),
                    //okValue:'确定',

                }).showModal();
                //$("#print_box").contents().find(".ui-dialog-content").css("overflow","auto");
                window.parent.$('.box').parent('.ui-dialog-content').css("overflow","auto");
            }else if(printType==1){
                /* 打印 */
                $("#box").jqprint({
                    globalStyles: true, //是否包含父文档的样式，默认为true
                    mediaPrint: false, //是否包含media='print'的链接标签。会被globalStyles选项覆盖，默认为false
                    stylesheet: null, //外部样式表的URL地址，默认为null
                    noPrintSelector: ".no-print", //不想打印的元素的jQuery选择器，默认为".no-print"
                    iframe: true, //是否使用一个iframe来替代打印表单的弹出窗口，true为在本页面进行打印，false就是说新开一个页面打印，默认为true
                    append: null, //将内容添加到打印内容的后面
                    prepend: null, //将内容添加到打印内容的前面，可以用来作为要打印内容
                    deferred: $.Deferred() //回调函数
                });
            }
        },1500);
    }

    //合计
    function totalTable(data, colName) {
        var count = 0;
        $.each(data, function (index, item) {
            count += parseFloat(item[colName]);
        })
        return count.toFixed(2);

    }

    function loadFolow(processId,index){

        //132501   90047 purchaseReturn
        // var processId="${processId!}";
        // var taskId="${taskId!}"; //后续需修改，默认展示90047
        $.ajax({
            type: "POST",
            // url: "/proxy-purchase/purchase/purchaseRefundProductOrder/queryTotle?key=purchaseReturn",
            url: "/proxy-finance/finance/purchase/payrequestinfo/queryTotle?processInstaId="+processId,
            //async: false,
            error: function () {
                alert("审批意见不能为空！");
            },
            success: function (data) {
                if (data.code==0){
                    if(data.result!=null){
                        $('#flow_'+index).empty().process(data.result);
                        $('#flow_'+index).css("width","800px");

                    }
                }else {
                    utils.dialog({content: '服务器错误', quickClose: true, timeout: 2000}).showModal();
                }
            }
        });
    }


    /**
     * 加载订单数据开始
     * am billNo
     */
    function loadOrderTab(billNo,index,data){
        var colNames = ['隱藏列','单据类型', '单据编号', '供应商编号', '供应商名称', '送货方式', '是否预付', '送货人', '送货人电话', '供应商仓库地址',
            '预计到货时间', '金额合计',
            '税额合计', '税价合计','本次申请金额','累计申请金额'
        ];
        var colModel = [
            {
                name: 'id',
                hidden:true
            },
            {
                name: 'billType',
                index: 'billType'
            },
            {
                name: 'billNo',
                index: 'billNo'
            },
            {
                name: 'supplierNo',
                index: 'supplierNo'
            },
            {
                name: 'supplierName',
                index: 'supplierName'
            },
            {
                name: 'deliveryMethod',
                index: 'deliveryMethod',
                formatter:function (val) {
                    if (val =="0"){
                        return "厢式送货";
                    }else if(val =="1"){
                        return "冷藏车";
                    }else if(val =="2"){
                        return "保温车";
                    }else if(val =="3") {
                        return "冷藏箱";
                    }else if(val =="4"){
                        return "其他封闭式车辆"
                    }else{
                        return ""
                    }
                },
                unformat: function (val){
                    if (val =="厢式送货"){
                        return "0";
                    }else if(val =="冷藏车"){
                        return "1";
                    }else if(val =="保温车"){
                        return "2";
                    }else if(val =="冷藏箱") {
                        return "3";
                    }else if(val =="其他封闭式车辆"){
                        return "4"
                    }

                }
            }, {
                name: 'isPrepay',
                index: 'isPrepay',
                width: 250,
                sortable: false,
                editable: true,
                formatter:function (val) {
                    if (val =="1"){
                        return "是"
                    }else if(val =="0"){
                        return "否"
                    }else {
                        return "";
                    }
                },
                unformat: function (val){
                    if (val =="是"){
                        return "1"
                    }else if(val =="否"){
                        return "0"
                    }else {
                        return "";
                    }
                }
            },
            {
                name: 'deliveryman',
                index: 'deliveryman'
            },
            {
                name: 'deliveryTel',
                index: 'deliveryTel'
            },{
                name: 'storeAddress',
                index: 'storeAddress'
            },{
                name: 'expectArrivalTime',
                index: 'expectArrivalTime',
                formatter: function (value) {
                    if(value){
                        return moment(value).format('YYYY-MM-DD');
                    }else{
                        return '';
                    }
                },
            },{
                name: 'amount',
                index: 'amount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            },{
                name: 'taxAmount',
                index: 'taxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            },{
                name: 'taxLimitAmount',
                index: 'taxLimitAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'thisTimeApplayAmount',
                index: 'thisTimeApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }

            }, {
                name: 'totalApplayAmount',
                index: 'totalApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }
        ];

        var url;
        url = '/proxy-finance/finance/purchase/payrequestinfo/selectOrderByPayReqNo?reqNo='+billNo

        $('#X_Tablea_'+index).XGrid({
            data: data,
            // url: url,
            // url: 'http://localhost:8080/account/find',
            colNames: colNames,
            colModel: colModel,
            rownumbers: true,
            rowNum: 20,
            rowList: [20,50,100],//分页条数下拉选择
            altRows: true,//设置为交替行表格,默认为false
            gridComplete: function () {
                setTimeout(function (param) {
                    var data = $('#X_Tablea_'+index).XGrid('getRowData');
                    $('#X_Tablea_'+index).XGrid('addRowData', {
                        id: 11111,
                        amount: totalTable(data, 'amount'),
                        taxAmount: totalTable(data, 'taxAmount'),
                        taxLimitAmount: totalTable(data, 'taxLimitAmount'),
                        thisTimeApplayAmount: totalTable(data, 'thisTimeApplayAmount'),
                        totalApplayAmount: totalTable(data, 'totalApplayAmount')
                    }, 'last');
                    $('#X_Tablea_'+index+' tr:last-child td:eq(1)').html('');
                    $('#X_Tablea_'+index+' tr:last-child td:first-child').html('合计');
                    if(window.isFirst){
                        $('#X_Tablea_'+index+' input[type=checkbox]').prop('checked', true).trigger('input');
                        window.isFirst = false;
                    };

                }, 200)
            },
            pager: '#grid_pager1'
        });




    }


/////////////////////////////////加载发票数据开始/////////////////////////////////////////////////////////////////////
    function loadInvoiceTab(billNo,index,data){
        var colNames = ['隱藏列','ERP发票编号', '开票日期', '供应商编号', '供应商名称', '发票不含税金额', '是否预付', '送货人', '送货人电话', '供应商仓库地址',
            '预计到货时间', '金额合计',
            '税额合计', '税价合计', '状态','本次申请金额','累计申请金额'
        ];
        var colNames1 = ['隐藏列','隐藏列','ERP发票编码', '开票日期', '供应商编号', '供应商名称', '发票不含税金额合计', '发票税额合计', '发票价税合计', '未核销金额','本次申请金额','累计申请金额'];
        var colModel1 =[
            {
                name: 'id',
                hidden:true
            },{
                name: 'isInit',
                hidden:true
            },
            {
                name: 'invoiceNo',
                index: 'invoiceNo'
            }, {
                name: 'invoiceCreateDate',
                index: 'invoiceCreateDate',
                formatter: function (value) {
                    if(value){
                        return moment(value).format('YYYY-MM-DD');
                    }else{
                        return '';
                    }
                }
            }, {
                name: 'supplierNo',
                index: 'supplierNo'
            }, {
                name: 'supplierName',
                index: 'supplierName'
            }, {
                name: 'noTotalTaxAmount',
                index: 'noTotalTaxAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'totalInvoiceValue',
                index: 'totalInvoiceValue',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'totalInvoiceTax',
                index: 'totalInvoiceTax',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',' ,'.');
                },
                unformat: function (val) {
                    return val.replace(/,/g ,'');
                }
            }, {
                name: 'unwriteoffMoney',
                index: 'unwriteoffMoney',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }, {
                name: 'thisTimeApplayAmount',
                index: 'thisTimeApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }

            }, {
                name: 'totalApplayAmount',
                index: 'totalApplayAmount',
                formatter: function (val) {
                    return parseFloat(val).formatMoney('2', '', ',', '.');
                },
                unformat: function (val) {
                    return val.replace(/,/g, '');
                }
            }];

        var url;
        url = '/proxy-finance/finance/purchase/payrequestinfo/selectInvoiceByPayReqNo?billNo='+billNo;
        $('#Y_Tablea_'+index).XGrid({
            //  url:url,
            data:data,
            colNames: colNames1,
            colModel: colModel1,
            rowNum: 20,
            rownumbers: true,
            altRows: true, //设置为交替行表格,默认为false
            gridComplete: function () {
                // var data = $('#Y_Tablea_'+index).XGrid('getRowData');
                setTimeout(function () {
                    $('#Y_Tablea_'+index).XGrid('addRowData', {
                        id: 222222,
                        noTotalTaxAmount: totalTable(data, 'noTotalTaxAmount'),
                        totalInvoiceValue: totalTable(data, 'totalInvoiceValue'),
                        totalInvoiceTax: totalTable(data, 'totalInvoiceTax'),
                        unwriteoffMoney: totalTable(data, 'unwriteoffMoney'),
                        thisTimeApplayAmount: totalTable(data, 'thisTimeApplayAmount'),
                        totalApplayAmount: totalTable(data, 'totalApplayAmount')
                    }, 'last');
                    $('#Y_Tablea_'+index+' tr:last-child td:eq(1)').html('');
                    $('#Y_Tablea_'+index+' tr:last-child td:first-child').html('合计');
                })
            },
        });


    }
    /////////////////////////////////加载发票数据结束/////////////////////////////////////////////////////////////////////


});
